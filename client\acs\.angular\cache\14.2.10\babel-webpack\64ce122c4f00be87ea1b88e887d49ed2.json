{"ast": null, "code": "'use strict';\n\nvar origSymbol = typeof Symbol !== 'undefined' && Symbol;\n\nvar hasSymbolSham = require('./shams');\n\nmodule.exports = function hasNativeSymbols() {\n  if (typeof origSymbol !== 'function') {\n    return false;\n  }\n\n  if (typeof Symbol !== 'function') {\n    return false;\n  }\n\n  if (typeof origSymbol('foo') !== 'symbol') {\n    return false;\n  }\n\n  if (typeof Symbol('bar') !== 'symbol') {\n    return false;\n  }\n\n  return hasSymbolSham();\n};", "map": {"version": 3, "names": ["origSymbol", "Symbol", "hasSymbolSham", "require", "module", "exports", "hasNativeSymbols"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/has-symbols/index.js"], "sourcesContent": ["'use strict';\n\nvar origSymbol = typeof Symbol !== 'undefined' && Symbol;\nvar hasSymbolSham = require('./shams');\n\nmodule.exports = function hasNativeSymbols() {\n\tif (typeof origSymbol !== 'function') { return false; }\n\tif (typeof Symbol !== 'function') { return false; }\n\tif (typeof origSymbol('foo') !== 'symbol') { return false; }\n\tif (typeof Symbol('bar') !== 'symbol') { return false; }\n\n\treturn hasSymbolSham();\n};\n"], "mappings": "AAAA;;AAEA,IAAIA,UAAU,GAAG,OAAOC,MAAP,KAAkB,WAAlB,IAAiCA,MAAlD;;AACA,IAAIC,aAAa,GAAGC,OAAO,CAAC,SAAD,CAA3B;;AAEAC,MAAM,CAACC,OAAP,GAAiB,SAASC,gBAAT,GAA4B;EAC5C,IAAI,OAAON,UAAP,KAAsB,UAA1B,EAAsC;IAAE,OAAO,KAAP;EAAe;;EACvD,IAAI,OAAOC,MAAP,KAAkB,UAAtB,EAAkC;IAAE,OAAO,KAAP;EAAe;;EACnD,IAAI,OAAOD,UAAU,CAAC,KAAD,CAAjB,KAA6B,QAAjC,EAA2C;IAAE,OAAO,KAAP;EAAe;;EAC5D,IAAI,OAAOC,MAAM,CAAC,KAAD,CAAb,KAAyB,QAA7B,EAAuC;IAAE,OAAO,KAAP;EAAe;;EAExD,OAAOC,aAAa,EAApB;AACA,CAPD"}, "metadata": {}, "sourceType": "script"}