{"ast": null, "code": "import { config } from './config';\nimport { hostReportError } from './util/hostReportError';\nexport const empty = {\n  closed: true,\n\n  next(value) {},\n\n  error(err) {\n    if (config.useDeprecatedSynchronousErrorHandling) {\n      throw err;\n    } else {\n      hostReportError(err);\n    }\n  },\n\n  complete() {}\n\n};", "map": {"version": 3, "names": ["config", "hostReportError", "empty", "closed", "next", "value", "error", "err", "useDeprecatedSynchronousErrorHandling", "complete"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/Observer.js"], "sourcesContent": ["import { config } from './config';\nimport { hostReportError } from './util/hostReportError';\nexport const empty = {\n    closed: true,\n    next(value) { },\n    error(err) {\n        if (config.useDeprecatedSynchronousErrorHandling) {\n            throw err;\n        }\n        else {\n            hostReportError(err);\n        }\n    },\n    complete() { }\n};\n"], "mappings": "AAAA,SAASA,MAAT,QAAuB,UAAvB;AACA,SAASC,eAAT,QAAgC,wBAAhC;AACA,OAAO,MAAMC,KAAK,GAAG;EACjBC,MAAM,EAAE,IADS;;EAEjBC,IAAI,CAACC,KAAD,EAAQ,CAAG,CAFE;;EAGjBC,KAAK,CAACC,GAAD,EAAM;IACP,IAAIP,MAAM,CAACQ,qCAAX,EAAkD;MAC9C,MAAMD,GAAN;IACH,CAFD,MAGK;MACDN,eAAe,CAACM,GAAD,CAAf;IACH;EACJ,CAVgB;;EAWjBE,QAAQ,GAAG,CAAG;;AAXG,CAAd"}, "metadata": {}, "sourceType": "module"}