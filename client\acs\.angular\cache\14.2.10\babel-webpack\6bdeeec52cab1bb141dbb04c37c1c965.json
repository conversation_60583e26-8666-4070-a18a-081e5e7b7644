{"ast": null, "code": "import format from \"./format.js\";\nimport { modifierPhases } from \"../enums.js\";\nvar INVALID_MODIFIER_ERROR = 'Popper: modifier \"%s\" provided an invalid %s property, expected %s but got %s';\nvar MISSING_DEPENDENCY_ERROR = 'Popper: modifier \"%s\" requires \"%s\", but \"%s\" modifier is not available';\nvar VALID_PROPERTIES = ['name', 'enabled', 'phase', 'fn', 'effect', 'requires', 'options'];\nexport default function validateModifiers(modifiers) {\n  modifiers.forEach(function (modifier) {\n    [].concat(Object.keys(modifier), VALID_PROPERTIES) // IE11-compatible replacement for `new Set(iterable)`\n    .filter(function (value, index, self) {\n      return self.indexOf(value) === index;\n    }).forEach(function (key) {\n      switch (key) {\n        case 'name':\n          if (typeof modifier.name !== 'string') {\n            console.error(format(INVALID_MODIFIER_ERROR, String(modifier.name), '\"name\"', '\"string\"', \"\\\"\" + String(modifier.name) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'enabled':\n          if (typeof modifier.enabled !== 'boolean') {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"enabled\"', '\"boolean\"', \"\\\"\" + String(modifier.enabled) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'phase':\n          if (modifierPhases.indexOf(modifier.phase) < 0) {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"phase\"', \"either \" + modifierPhases.join(', '), \"\\\"\" + String(modifier.phase) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'fn':\n          if (typeof modifier.fn !== 'function') {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"fn\"', '\"function\"', \"\\\"\" + String(modifier.fn) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'effect':\n          if (modifier.effect != null && typeof modifier.effect !== 'function') {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"effect\"', '\"function\"', \"\\\"\" + String(modifier.fn) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'requires':\n          if (modifier.requires != null && !Array.isArray(modifier.requires)) {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"requires\"', '\"array\"', \"\\\"\" + String(modifier.requires) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'requiresIfExists':\n          if (!Array.isArray(modifier.requiresIfExists)) {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"requiresIfExists\"', '\"array\"', \"\\\"\" + String(modifier.requiresIfExists) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'options':\n        case 'data':\n          break;\n\n        default:\n          console.error(\"PopperJS: an invalid property has been provided to the \\\"\" + modifier.name + \"\\\" modifier, valid properties are \" + VALID_PROPERTIES.map(function (s) {\n            return \"\\\"\" + s + \"\\\"\";\n          }).join(', ') + \"; but \\\"\" + key + \"\\\" was provided.\");\n      }\n\n      modifier.requires && modifier.requires.forEach(function (requirement) {\n        if (modifiers.find(function (mod) {\n          return mod.name === requirement;\n        }) == null) {\n          console.error(format(MISSING_DEPENDENCY_ERROR, String(modifier.name), requirement, requirement));\n        }\n      });\n    });\n  });\n}", "map": {"version": 3, "names": ["format", "modifierPhases", "INVALID_MODIFIER_ERROR", "MISSING_DEPENDENCY_ERROR", "VALID_PROPERTIES", "validateModifiers", "modifiers", "for<PERSON>ach", "modifier", "concat", "Object", "keys", "filter", "value", "index", "self", "indexOf", "key", "name", "console", "error", "String", "enabled", "phase", "join", "fn", "effect", "requires", "Array", "isArray", "requiresIfExists", "map", "s", "requirement", "find", "mod"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/utils/validateModifiers.js"], "sourcesContent": ["import format from \"./format.js\";\nimport { modifierPhases } from \"../enums.js\";\nvar INVALID_MODIFIER_ERROR = 'Popper: modifier \"%s\" provided an invalid %s property, expected %s but got %s';\nvar MISSING_DEPENDENCY_ERROR = 'Popper: modifier \"%s\" requires \"%s\", but \"%s\" modifier is not available';\nvar VALID_PROPERTIES = ['name', 'enabled', 'phase', 'fn', 'effect', 'requires', 'options'];\nexport default function validateModifiers(modifiers) {\n  modifiers.forEach(function (modifier) {\n    [].concat(Object.keys(modifier), VALID_PROPERTIES) // IE11-compatible replacement for `new Set(iterable)`\n    .filter(function (value, index, self) {\n      return self.indexOf(value) === index;\n    }).forEach(function (key) {\n      switch (key) {\n        case 'name':\n          if (typeof modifier.name !== 'string') {\n            console.error(format(INVALID_MODIFIER_ERROR, String(modifier.name), '\"name\"', '\"string\"', \"\\\"\" + String(modifier.name) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'enabled':\n          if (typeof modifier.enabled !== 'boolean') {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"enabled\"', '\"boolean\"', \"\\\"\" + String(modifier.enabled) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'phase':\n          if (modifierPhases.indexOf(modifier.phase) < 0) {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"phase\"', \"either \" + modifierPhases.join(', '), \"\\\"\" + String(modifier.phase) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'fn':\n          if (typeof modifier.fn !== 'function') {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"fn\"', '\"function\"', \"\\\"\" + String(modifier.fn) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'effect':\n          if (modifier.effect != null && typeof modifier.effect !== 'function') {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"effect\"', '\"function\"', \"\\\"\" + String(modifier.fn) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'requires':\n          if (modifier.requires != null && !Array.isArray(modifier.requires)) {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"requires\"', '\"array\"', \"\\\"\" + String(modifier.requires) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'requiresIfExists':\n          if (!Array.isArray(modifier.requiresIfExists)) {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"requiresIfExists\"', '\"array\"', \"\\\"\" + String(modifier.requiresIfExists) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'options':\n        case 'data':\n          break;\n\n        default:\n          console.error(\"PopperJS: an invalid property has been provided to the \\\"\" + modifier.name + \"\\\" modifier, valid properties are \" + VALID_PROPERTIES.map(function (s) {\n            return \"\\\"\" + s + \"\\\"\";\n          }).join(', ') + \"; but \\\"\" + key + \"\\\" was provided.\");\n      }\n\n      modifier.requires && modifier.requires.forEach(function (requirement) {\n        if (modifiers.find(function (mod) {\n          return mod.name === requirement;\n        }) == null) {\n          console.error(format(MISSING_DEPENDENCY_ERROR, String(modifier.name), requirement, requirement));\n        }\n      });\n    });\n  });\n}"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,aAAnB;AACA,SAASC,cAAT,QAA+B,aAA/B;AACA,IAAIC,sBAAsB,GAAG,+EAA7B;AACA,IAAIC,wBAAwB,GAAG,yEAA/B;AACA,IAAIC,gBAAgB,GAAG,CAAC,MAAD,EAAS,SAAT,EAAoB,OAApB,EAA6B,IAA7B,EAAmC,QAAnC,EAA6C,UAA7C,EAAyD,SAAzD,CAAvB;AACA,eAAe,SAASC,iBAAT,CAA2BC,SAA3B,EAAsC;EACnDA,SAAS,CAACC,OAAV,CAAkB,UAAUC,QAAV,EAAoB;IACpC,GAAGC,MAAH,CAAUC,MAAM,CAACC,IAAP,CAAYH,QAAZ,CAAV,EAAiCJ,gBAAjC,EAAmD;IAAnD,CACCQ,MADD,CACQ,UAAUC,KAAV,EAAiBC,KAAjB,EAAwBC,IAAxB,EAA8B;MACpC,OAAOA,IAAI,CAACC,OAAL,CAAaH,KAAb,MAAwBC,KAA/B;IACD,CAHD,EAGGP,OAHH,CAGW,UAAUU,GAAV,EAAe;MACxB,QAAQA,GAAR;QACE,KAAK,MAAL;UACE,IAAI,OAAOT,QAAQ,CAACU,IAAhB,KAAyB,QAA7B,EAAuC;YACrCC,OAAO,CAACC,KAAR,CAAcpB,MAAM,CAACE,sBAAD,EAAyBmB,MAAM,CAACb,QAAQ,CAACU,IAAV,CAA/B,EAAgD,QAAhD,EAA0D,UAA1D,EAAsE,OAAOG,MAAM,CAACb,QAAQ,CAACU,IAAV,CAAb,GAA+B,IAArG,CAApB;UACD;;UAED;;QAEF,KAAK,SAAL;UACE,IAAI,OAAOV,QAAQ,CAACc,OAAhB,KAA4B,SAAhC,EAA2C;YACzCH,OAAO,CAACC,KAAR,CAAcpB,MAAM,CAACE,sBAAD,EAAyBM,QAAQ,CAACU,IAAlC,EAAwC,WAAxC,EAAqD,WAArD,EAAkE,OAAOG,MAAM,CAACb,QAAQ,CAACc,OAAV,CAAb,GAAkC,IAApG,CAApB;UACD;;UAED;;QAEF,KAAK,OAAL;UACE,IAAIrB,cAAc,CAACe,OAAf,CAAuBR,QAAQ,CAACe,KAAhC,IAAyC,CAA7C,EAAgD;YAC9CJ,OAAO,CAACC,KAAR,CAAcpB,MAAM,CAACE,sBAAD,EAAyBM,QAAQ,CAACU,IAAlC,EAAwC,SAAxC,EAAmD,YAAYjB,cAAc,CAACuB,IAAf,CAAoB,IAApB,CAA/D,EAA0F,OAAOH,MAAM,CAACb,QAAQ,CAACe,KAAV,CAAb,GAAgC,IAA1H,CAApB;UACD;;UAED;;QAEF,KAAK,IAAL;UACE,IAAI,OAAOf,QAAQ,CAACiB,EAAhB,KAAuB,UAA3B,EAAuC;YACrCN,OAAO,CAACC,KAAR,CAAcpB,MAAM,CAACE,sBAAD,EAAyBM,QAAQ,CAACU,IAAlC,EAAwC,MAAxC,EAAgD,YAAhD,EAA8D,OAAOG,MAAM,CAACb,QAAQ,CAACiB,EAAV,CAAb,GAA6B,IAA3F,CAApB;UACD;;UAED;;QAEF,KAAK,QAAL;UACE,IAAIjB,QAAQ,CAACkB,MAAT,IAAmB,IAAnB,IAA2B,OAAOlB,QAAQ,CAACkB,MAAhB,KAA2B,UAA1D,EAAsE;YACpEP,OAAO,CAACC,KAAR,CAAcpB,MAAM,CAACE,sBAAD,EAAyBM,QAAQ,CAACU,IAAlC,EAAwC,UAAxC,EAAoD,YAApD,EAAkE,OAAOG,MAAM,CAACb,QAAQ,CAACiB,EAAV,CAAb,GAA6B,IAA/F,CAApB;UACD;;UAED;;QAEF,KAAK,UAAL;UACE,IAAIjB,QAAQ,CAACmB,QAAT,IAAqB,IAArB,IAA6B,CAACC,KAAK,CAACC,OAAN,CAAcrB,QAAQ,CAACmB,QAAvB,CAAlC,EAAoE;YAClER,OAAO,CAACC,KAAR,CAAcpB,MAAM,CAACE,sBAAD,EAAyBM,QAAQ,CAACU,IAAlC,EAAwC,YAAxC,EAAsD,SAAtD,EAAiE,OAAOG,MAAM,CAACb,QAAQ,CAACmB,QAAV,CAAb,GAAmC,IAApG,CAApB;UACD;;UAED;;QAEF,KAAK,kBAAL;UACE,IAAI,CAACC,KAAK,CAACC,OAAN,CAAcrB,QAAQ,CAACsB,gBAAvB,CAAL,EAA+C;YAC7CX,OAAO,CAACC,KAAR,CAAcpB,MAAM,CAACE,sBAAD,EAAyBM,QAAQ,CAACU,IAAlC,EAAwC,oBAAxC,EAA8D,SAA9D,EAAyE,OAAOG,MAAM,CAACb,QAAQ,CAACsB,gBAAV,CAAb,GAA2C,IAApH,CAApB;UACD;;UAED;;QAEF,KAAK,SAAL;QACA,KAAK,MAAL;UACE;;QAEF;UACEX,OAAO,CAACC,KAAR,CAAc,8DAA8DZ,QAAQ,CAACU,IAAvE,GAA8E,oCAA9E,GAAqHd,gBAAgB,CAAC2B,GAAjB,CAAqB,UAAUC,CAAV,EAAa;YACnK,OAAO,OAAOA,CAAP,GAAW,IAAlB;UACD,CAFkI,EAEhIR,IAFgI,CAE3H,IAF2H,CAArH,GAEE,UAFF,GAEeP,GAFf,GAEqB,kBAFnC;MAvDJ;;MA4DAT,QAAQ,CAACmB,QAAT,IAAqBnB,QAAQ,CAACmB,QAAT,CAAkBpB,OAAlB,CAA0B,UAAU0B,WAAV,EAAuB;QACpE,IAAI3B,SAAS,CAAC4B,IAAV,CAAe,UAAUC,GAAV,EAAe;UAChC,OAAOA,GAAG,CAACjB,IAAJ,KAAae,WAApB;QACD,CAFG,KAEE,IAFN,EAEY;UACVd,OAAO,CAACC,KAAR,CAAcpB,MAAM,CAACG,wBAAD,EAA2BkB,MAAM,CAACb,QAAQ,CAACU,IAAV,CAAjC,EAAkDe,WAAlD,EAA+DA,WAA/D,CAApB;QACD;MACF,CANoB,CAArB;IAOD,CAvED;EAwED,CAzED;AA0ED"}, "metadata": {}, "sourceType": "module"}