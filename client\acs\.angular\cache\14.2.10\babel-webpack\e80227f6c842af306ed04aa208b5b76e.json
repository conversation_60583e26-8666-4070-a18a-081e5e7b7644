{"ast": null, "code": "import { scheduleObservable } from './scheduleObservable';\nimport { schedulePromise } from './schedulePromise';\nimport { scheduleArray } from './scheduleArray';\nimport { scheduleIterable } from './scheduleIterable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isPromise } from '../util/isPromise';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isIterable } from '../util/isIterable';\nexport function scheduled(input, scheduler) {\n  if (input != null) {\n    if (isInteropObservable(input)) {\n      return scheduleObservable(input, scheduler);\n    } else if (isPromise(input)) {\n      return schedulePromise(input, scheduler);\n    } else if (isArrayLike(input)) {\n      return scheduleArray(input, scheduler);\n    } else if (isIterable(input) || typeof input === 'string') {\n      return scheduleIterable(input, scheduler);\n    }\n  }\n\n  throw new TypeError((input !== null && typeof input || input) + ' is not observable');\n}", "map": {"version": 3, "names": ["scheduleObservable", "schedulePromise", "scheduleArray", "scheduleIterable", "isInteropObservable", "isPromise", "isArrayLike", "isIterable", "scheduled", "input", "scheduler", "TypeError"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/scheduled/scheduled.js"], "sourcesContent": ["import { scheduleObservable } from './scheduleObservable';\nimport { schedulePromise } from './schedulePromise';\nimport { scheduleArray } from './scheduleArray';\nimport { scheduleIterable } from './scheduleIterable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isPromise } from '../util/isPromise';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isIterable } from '../util/isIterable';\nexport function scheduled(input, scheduler) {\n    if (input != null) {\n        if (isInteropObservable(input)) {\n            return scheduleObservable(input, scheduler);\n        }\n        else if (isPromise(input)) {\n            return schedulePromise(input, scheduler);\n        }\n        else if (isArrayLike(input)) {\n            return scheduleArray(input, scheduler);\n        }\n        else if (isIterable(input) || typeof input === 'string') {\n            return scheduleIterable(input, scheduler);\n        }\n    }\n    throw new TypeError((input !== null && typeof input || input) + ' is not observable');\n}\n"], "mappings": "AAAA,SAASA,kBAAT,QAAmC,sBAAnC;AACA,SAASC,eAAT,QAAgC,mBAAhC;AACA,SAASC,aAAT,QAA8B,iBAA9B;AACA,SAASC,gBAAT,QAAiC,oBAAjC;AACA,SAASC,mBAAT,QAAoC,6BAApC;AACA,SAASC,SAAT,QAA0B,mBAA1B;AACA,SAASC,WAAT,QAA4B,qBAA5B;AACA,SAASC,UAAT,QAA2B,oBAA3B;AACA,OAAO,SAASC,SAAT,CAAmBC,KAAnB,EAA0BC,SAA1B,EAAqC;EACxC,IAAID,KAAK,IAAI,IAAb,EAAmB;IACf,IAAIL,mBAAmB,CAACK,KAAD,CAAvB,EAAgC;MAC5B,OAAOT,kBAAkB,CAACS,KAAD,EAAQC,SAAR,CAAzB;IACH,CAFD,MAGK,IAAIL,SAAS,CAACI,KAAD,CAAb,EAAsB;MACvB,OAAOR,eAAe,CAACQ,KAAD,EAAQC,SAAR,CAAtB;IACH,CAFI,MAGA,IAAIJ,WAAW,CAACG,KAAD,CAAf,EAAwB;MACzB,OAAOP,aAAa,CAACO,KAAD,EAAQC,SAAR,CAApB;IACH,CAFI,MAGA,IAAIH,UAAU,CAACE,KAAD,CAAV,IAAqB,OAAOA,KAAP,KAAiB,QAA1C,EAAoD;MACrD,OAAON,gBAAgB,CAACM,KAAD,EAAQC,SAAR,CAAvB;IACH;EACJ;;EACD,MAAM,IAAIC,SAAJ,CAAc,CAACF,KAAK,KAAK,IAAV,IAAkB,OAAOA,KAAzB,IAAkCA,KAAnC,IAA4C,oBAA1D,CAAN;AACH"}, "metadata": {}, "sourceType": "module"}