{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { HttpService } from 'src/app/Modules/Core/Services/HttpService.Service';\nimport { of } from 'rxjs';\nimport { ComponentMessageModel } from '../../Models/component-message.model';\nimport { SubjectEnums } from '../../Enums/subject.enum';\nimport { ComponentMessageService } from 'src/app/Modules/Core/Services/Common/component-message.service';\nimport { map } from 'rxjs/operators';\nlet CartService = class CartService {\n  constructor(http, componentMessageService) {\n    this.http = http;\n    this.componentMessageService = componentMessageService; // this.envConfig = JSON.parse(localStorage.getItem('envConfig'));\n  } //get cart listing data\n\n\n  getCartListing(orderNumber, itemadd) {\n    this.envConfig = JSON.parse(localStorage.getItem('envConfig'));\n    let stringAppned = itemadd ? '/?__LS=0' : '';\n    return this.http.GetO(\"shoppingcart/cartdetails/\" + orderNumber + stringAppned).pipe(map(cartList => {\n      if (cartList.orderCartResponseVM.length > 0) {\n        cartList.orderCartResponseVM.forEach(data => {\n          data.image = this.envConfig.blobUrl + data.productCode + \"_\" + data.categoryCode + \"_\" + data.subCategoryCode + \".png\";\n          data.imageFallback = this.envConfig.blobUrl + data.productCode + \"_\" + data.categoryCode + \"_\" + data.subCategoryCode + \".jpg\";\n        });\n      }\n\n      return cartList;\n    }));\n  }\n\n  getCartOrderCount(orderNumber) {\n    return this.http.GetO(\"CheckOut/CartOrdersCount/\" + orderNumber).pipe(map(cartList => {\n      return cartList;\n    }));\n  } //set order number\n\n\n  setOrderNumber(orderNumber, IsDisableMessageService, loginflag) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      let savedOrderNo = yield _this.getOrderNumber().toPromise();\n      const currentUser = JSON.parse(localStorage.getItem('currentUser'));\n\n      if (currentUser && currentUser.token) {\n        if (loginflag) {\n          localStorage.setItem('ordernumber', JSON.stringify(orderNumber));\n          savedOrderNo = orderNumber;\n        }\n\n        if (!savedOrderNo) {\n          localStorage.setItem('ordernumber', JSON.stringify(orderNumber));\n          savedOrderNo = orderNumber;\n        }\n\n        if (!IsDisableMessageService) {\n          const activeSectionMessage = new ComponentMessageModel();\n          activeSectionMessage.subject = SubjectEnums.cartadd;\n          activeSectionMessage.payload = {\n            orderNumber: savedOrderNo\n          };\n\n          _this.componentMessageService.sendMessage(activeSectionMessage);\n        }\n      } else {\n        const activeSectionMessage = new ComponentMessageModel();\n        activeSectionMessage.subject = SubjectEnums.logout;\n        activeSectionMessage.payload = {};\n\n        _this.componentMessageService.sendMessage(activeSectionMessage);\n      }\n    })();\n  }\n\n  getOrderNumber() {\n    const ordernumber = JSON.parse(localStorage.getItem('ordernumber'));\n    return of(ordernumber);\n  }\n\n  deleteCartItem(ordernumber, kitlinenumber, productnumber) {\n    return this.http.DeleteData(\"shoppingcart/\" + ordernumber + \"/\" + kitlinenumber + \"/\" + productnumber);\n  }\n\n  getOrderNumberSynchronus() {\n    return JSON.parse(localStorage.getItem('ordernumber'));\n  } // clear order number\n\n\n  clearOrderNumber() {\n    localStorage.removeItem('ordernumber');\n  }\n\n  getAssignedOrderNumber(orderNumber) {\n    return this.http.GetO('shoppingcart/GetAssignedOrderNumber/' + orderNumber);\n  }\n\n  updateQuantity(payload) {\n    return this.http.PutO('blumberg/cart/updatequantity', payload, true);\n  }\n\n  deleteCartItems(ordernumber, queryString) {\n    return this.http.DeleteData(`shoppingcart/DeleteCartItems/${ordernumber}?${queryString}`);\n  }\n\n  viewImprintDetails(orderNumber, serviceLineNumber) {\n    return this.http.GetO(`indexAndTabDivider/view-form-controls?orderNumber=${orderNumber}&serviceLineNumber=${serviceLineNumber}`);\n  }\n\n};\n\nCartService.ctorParameters = () => [{\n  type: HttpService\n}, {\n  type: ComponentMessageService\n}];\n\nCartService = __decorate([Injectable({\n  providedIn: 'root'\n})], CartService);\nexport { CartService };", "map": {"version": 3, "mappings": ";;AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,WAAT,QAA4B,mDAA5B;AACA,SAAqBC,EAArB,QAA+B,MAA/B;AAEA,SAASC,qBAAT,QAAsC,sCAAtC;AACA,SAASC,YAAT,QAA6B,0BAA7B;AACA,SAASC,uBAAT,QAAwC,gEAAxC;AAGA,SAASC,GAAT,QAAoB,gBAApB;IAKaC,WAAW,SAAXA,WAAW;EAEtBC,YAAoBC,IAApB,EAA+CC,uBAA/C,EAA+F;IAA3E;IAA2B,uDAAgD,CAC7F;EACD,CAJqB,CAKtB;;;EACOC,cAAc,CAACC,WAAD,EAAsBC,OAAtB,EAAuC;IAC1D,KAAKC,SAAL,GAAiBC,IAAI,CAACC,KAAL,CAAWC,YAAY,CAACC,OAAb,CAAqB,WAArB,CAAX,CAAjB;IACA,IAAIC,YAAY,GAAGN,OAAO,GAAG,UAAH,GAAgB,EAA1C;IACA,OAAO,KAAKJ,IAAL,CAAUW,IAAV,CAAe,8BAA8BR,WAA9B,GAA4CO,YAA3D,EAAyEE,IAAzE,CAA8Ef,GAAG,CAACgB,QAAQ,IAAG;MAClG,IAAIA,QAAQ,CAACC,mBAAT,CAA6BC,MAA7B,GAAsC,CAA1C,EAA6C;QAC3CF,QAAQ,CAACC,mBAAT,CAA6BE,OAA7B,CAAqCC,IAAI,IAAG;UAC1CA,IAAI,CAACC,KAAL,GAAa,KAAKb,SAAL,CAAec,OAAf,GAAyBF,IAAI,CAACG,WAA9B,GAA4C,GAA5C,GAAkDH,IAAI,CAACI,YAAvD,GAAsE,GAAtE,GAA4EJ,IAAI,CAACK,eAAjF,GAAmG,MAAhH;UACAL,IAAI,CAACM,aAAL,GAAqB,KAAKlB,SAAL,CAAec,OAAf,GAAyBF,IAAI,CAACG,WAA9B,GAA4C,GAA5C,GAAkDH,IAAI,CAACI,YAAvD,GAAsE,GAAtE,GAA4EJ,IAAI,CAACK,eAAjF,GAAmG,MAAxH;QACD,CAHD;MAID;;MACD,OAAOT,QAAP;IACD,CARuF,CAAjF,CAAP;EASD;;EAEMW,iBAAiB,CAACrB,WAAD,EAAoB;IAC1C,OAAO,KAAKH,IAAL,CAAUW,IAAV,CAAe,8BAA8BR,WAA7C,EAA0DS,IAA1D,CAA+Df,GAAG,CAACgB,QAAQ,IAAG;MACnF,OAAOA,QAAP;IACD,CAFwE,CAAlE,CAAP;EAGD,CAxBqB,CA0BtB;;;EACMY,cAAc,CAACtB,WAAD,EAAsBuB,uBAAtB,EAAyDC,SAAzD,EAA4E;IAAA;;IAAA;MAC9F,IAAIC,YAAY,SAAS,KAAI,CAACC,cAAL,GAAsBC,SAAtB,EAAzB;MACA,MAAMC,WAAW,GAAqBzB,IAAI,CAACC,KAAL,CAAWC,YAAY,CAACC,OAAb,CAAqB,aAArB,CAAX,CAAtC;;MACA,IAAIsB,WAAW,IAAIA,WAAW,CAACC,KAA/B,EAAsC;QACpC,IAAIL,SAAJ,EAAe;UACbnB,YAAY,CAACyB,OAAb,CAAqB,aAArB,EAAoC3B,IAAI,CAAC4B,SAAL,CAAe/B,WAAf,CAApC;UACAyB,YAAY,GAAGzB,WAAf;QACD;;QACD,IAAI,CAACyB,YAAL,EAAmB;UACjBpB,YAAY,CAACyB,OAAb,CAAqB,aAArB,EAAoC3B,IAAI,CAAC4B,SAAL,CAAe/B,WAAf,CAApC;UACAyB,YAAY,GAAGzB,WAAf;QACD;;QAED,IAAI,CAACuB,uBAAL,EAA8B;UAC5B,MAAMS,oBAAoB,GAA0B,IAAIzC,qBAAJ,EAApD;UACAyC,oBAAoB,CAACC,OAArB,GAA+BzC,YAAY,CAAC0C,OAA5C;UACAF,oBAAoB,CAACG,OAArB,GAA+B;YAC7BnC,WAAW,EAAEyB;UADgB,CAA/B;;UAGA,KAAI,CAAC3B,uBAAL,CAA6BsC,WAA7B,CAAyCJ,oBAAzC;QACD;MACF,CAlBD,MAmBK;QACH,MAAMA,oBAAoB,GAA0B,IAAIzC,qBAAJ,EAApD;QACAyC,oBAAoB,CAACC,OAArB,GAA+BzC,YAAY,CAAC6C,MAA5C;QACAL,oBAAoB,CAACG,OAArB,GAA+B,EAA/B;;QACA,KAAI,CAACrC,uBAAL,CAA6BsC,WAA7B,CAAyCJ,oBAAzC;MACD;IA3B6F;EA4B/F;;EAEDN,cAAc;IACZ,MAAMY,WAAW,GAAWnC,IAAI,CAACC,KAAL,CAAWC,YAAY,CAACC,OAAb,CAAqB,aAArB,CAAX,CAA5B;IACA,OAAOhB,EAAE,CAACgD,WAAD,CAAT;EACD;;EAEDC,cAAc,CAACD,WAAD,EAAsBE,aAAtB,EAA6CC,aAA7C,EAAkE;IAC9E,OAAO,KAAK5C,IAAL,CAAU6C,UAAV,CAAqB,kBAAkBJ,WAAlB,GAAgC,GAAhC,GAAsCE,aAAtC,GAAsD,GAAtD,GAA4DC,aAAjF,CAAP;EACD;;EACDE,wBAAwB;IACtB,OAAOxC,IAAI,CAACC,KAAL,CAAWC,YAAY,CAACC,OAAb,CAAqB,aAArB,CAAX,CAAP;EACD,CAnEqB,CAqEtB;;;EACAsC,gBAAgB;IACdvC,YAAY,CAACwC,UAAb,CAAwB,aAAxB;EACD;;EAEDC,sBAAsB,CAAC9C,WAAD,EAAoB;IACxC,OAAO,KAAKH,IAAL,CAAUW,IAAV,CAAe,yCAAyCR,WAAxD,CAAP;EACD;;EAED+C,cAAc,CAACZ,OAAD,EAAQ;IACpB,OAAO,KAAKtC,IAAL,CAAUmD,IAAV,CAAe,8BAAf,EAA+Cb,OAA/C,EAAwD,IAAxD,CAAP;EACD;;EAEDc,eAAe,CAACX,WAAD,EAAsBY,WAAtB,EAAyC;IACtD,OAAO,KAAKrD,IAAL,CAAU6C,UAAV,CAAqB,gCAAgCJ,WAAW,IAAIY,WAAW,EAA/E,CAAP;EACD;;EAEDC,kBAAkB,CAACnD,WAAD,EAAsBoD,iBAAtB,EAA+C;IAC/D,OAAO,KAAKvD,IAAL,CAAUW,IAAV,CAAe,qDAAqDR,WAAW,sBAAsBoD,iBAAiB,EAAtH,CAAP;EACD;;AAxFqB;;;;;;;;AAAXzD,WAAW,eAHvBP,UAAU,CAAC;EACViE,UAAU,EAAE;AADF,CAAD,CAGa,GAAX1D,WAAW,CAAX;SAAAA", "names": ["Injectable", "HttpService", "of", "ComponentMessageModel", "SubjectEnums", "ComponentMessageService", "map", "CartService", "constructor", "http", "componentMessageService", "getCartListing", "orderNumber", "itemadd", "envConfig", "JSON", "parse", "localStorage", "getItem", "stringAppned", "GetO", "pipe", "cartList", "orderCartResponseVM", "length", "for<PERSON>ach", "data", "image", "blobUrl", "productCode", "categoryCode", "subCategoryCode", "imageFallback", "getCartOrderCount", "setOrderNumber", "IsDisableMessageService", "loginflag", "savedOrderNo", "getOrderNumber", "to<PERSON>romise", "currentUser", "token", "setItem", "stringify", "activeSectionMessage", "subject", "cartadd", "payload", "sendMessage", "logout", "ordernumber", "deleteCartItem", "kitlinenumber", "productnumber", "DeleteData", "getOrderNumberSynchronus", "clearOrderNumber", "removeItem", "getAssignedOrderNumber", "updateQuantity", "PutO", "deleteCartItems", "queryString", "viewImprintDetails", "serviceLineNumber", "providedIn"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\viatech\\Ecom\\client\\acs\\src\\app\\Modules\\Shared\\Services\\cart\\cart.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpService } from 'src/app/Modules/Core/Services/HttpService.Service';\r\nimport { Observable, of } from 'rxjs';\r\nimport { CartModel, ShoppingCartOrderRes } from '../../Models/Cart/cart.model';\r\nimport { ComponentMessageModel } from '../../Models/component-message.model';\r\nimport { SubjectEnums } from '../../Enums/subject.enum';\r\nimport { ComponentMessageService } from 'src/app/Modules/Core/Services/Common/component-message.service';\r\nimport { EnvConfigModel } from '../../Models/envConfig.model';\r\nimport { CurrentUserModel } from '../../Models/AuthModel/current-user.model';\r\nimport { map } from 'rxjs/operators';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class CartService {\r\n  envConfig: EnvConfigModel;\r\n  constructor(private http: HttpService, private componentMessageService: ComponentMessageService) {\r\n    // this.envConfig = JSON.parse(localStorage.getItem('envConfig'));\r\n  }\r\n  //get cart listing data\r\n  public getCartListing(orderNumber: number, itemadd?: boolean): Observable<ShoppingCartOrderRes> {\r\n    this.envConfig = JSON.parse(localStorage.getItem('envConfig'));\r\n    let stringAppned = itemadd ? '/?__LS=0' : '';\r\n    return this.http.GetO(\"shoppingcart/cartdetails/\" + orderNumber + stringAppned).pipe(map(cartList => {\r\n      if (cartList.orderCartResponseVM.length > 0) {\r\n        cartList.orderCartResponseVM.forEach(data => {\r\n          data.image = this.envConfig.blobUrl + data.productCode + \"_\" + data.categoryCode + \"_\" + data.subCategoryCode + \".png\";\r\n          data.imageFallback = this.envConfig.blobUrl + data.productCode + \"_\" + data.categoryCode + \"_\" + data.subCategoryCode + \".jpg\";\r\n        })\r\n      }\r\n      return cartList;\r\n    }));\r\n  }\r\n\r\n  public getCartOrderCount(orderNumber: number): Observable<number> {\r\n    return this.http.GetO(\"CheckOut/CartOrdersCount/\" + orderNumber).pipe(map(cartList => {\r\n      return cartList;\r\n    }));\r\n  }\r\n\r\n  //set order number\r\n  async setOrderNumber(orderNumber: number, IsDisableMessageService?: boolean, loginflag?: boolean) {\r\n    let savedOrderNo = await this.getOrderNumber().toPromise();\r\n    const currentUser: CurrentUserModel = JSON.parse(localStorage.getItem('currentUser'));\r\n    if (currentUser && currentUser.token) {\r\n      if (loginflag) {\r\n        localStorage.setItem('ordernumber', JSON.stringify(orderNumber));\r\n        savedOrderNo = orderNumber;\r\n      }\r\n      if (!savedOrderNo) {\r\n        localStorage.setItem('ordernumber', JSON.stringify(orderNumber));\r\n        savedOrderNo = orderNumber;\r\n      }\r\n\r\n      if (!IsDisableMessageService) {\r\n        const activeSectionMessage: ComponentMessageModel = new ComponentMessageModel();\r\n        activeSectionMessage.subject = SubjectEnums.cartadd;\r\n        activeSectionMessage.payload = {\r\n          orderNumber: savedOrderNo\r\n        }\r\n        this.componentMessageService.sendMessage(activeSectionMessage);\r\n      }\r\n    }\r\n    else {\r\n      const activeSectionMessage: ComponentMessageModel = new ComponentMessageModel();\r\n      activeSectionMessage.subject = SubjectEnums.logout;\r\n      activeSectionMessage.payload = {}\r\n      this.componentMessageService.sendMessage(activeSectionMessage);\r\n    }\r\n  }\r\n\r\n  getOrderNumber(): Observable<any> {\r\n    const ordernumber: number = JSON.parse(localStorage.getItem('ordernumber'));\r\n    return of(ordernumber);\r\n  }\r\n\r\n  deleteCartItem(ordernumber: number, kitlinenumber: number, productnumber: number): Observable<any> {\r\n    return this.http.DeleteData(\"shoppingcart/\" + ordernumber + \"/\" + kitlinenumber + \"/\" + productnumber);\r\n  }\r\n  getOrderNumberSynchronus() {\r\n    return JSON.parse(localStorage.getItem('ordernumber'));\r\n  }\r\n\r\n  // clear order number\r\n  clearOrderNumber(): void {\r\n    localStorage.removeItem('ordernumber');\r\n  }\r\n\r\n  getAssignedOrderNumber(orderNumber: number) {\r\n    return this.http.GetO('shoppingcart/GetAssignedOrderNumber/' + orderNumber)\r\n  }\r\n\r\n  updateQuantity(payload) {\r\n    return this.http.PutO('blumberg/cart/updatequantity', payload, true);\r\n  }\r\n\r\n  deleteCartItems(ordernumber: number, queryString: string): Observable<any> {\r\n    return this.http.DeleteData(`shoppingcart/DeleteCartItems/${ordernumber}?${queryString}`);\r\n  }\r\n\r\n  viewImprintDetails(orderNumber: number, serviceLineNumber: string) {\r\n    return this.http.GetO(`indexAndTabDivider/view-form-controls?orderNumber=${orderNumber}&serviceLineNumber=${serviceLineNumber}`)\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}