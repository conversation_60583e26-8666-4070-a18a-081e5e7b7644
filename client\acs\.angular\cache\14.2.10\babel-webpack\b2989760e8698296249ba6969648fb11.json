{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function mapTo(value) {\n  return source => source.lift(new MapToOperator(value));\n}\n\nclass MapToOperator {\n  constructor(value) {\n    this.value = value;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new MapToSubscriber(subscriber, this.value));\n  }\n\n}\n\nclass MapToSubscriber extends Subscriber {\n  constructor(destination, value) {\n    super(destination);\n    this.value = value;\n  }\n\n  _next(x) {\n    this.destination.next(this.value);\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "mapTo", "value", "source", "lift", "MapToOperator", "constructor", "call", "subscriber", "subscribe", "MapToSubscriber", "destination", "_next", "x", "next"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/mapTo.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function mapTo(value) {\n    return (source) => source.lift(new MapToOperator(value));\n}\nclass MapToOperator {\n    constructor(value) {\n        this.value = value;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new MapToSubscriber(subscriber, this.value));\n    }\n}\nclass MapToSubscriber extends Subscriber {\n    constructor(destination, value) {\n        super(destination);\n        this.value = value;\n    }\n    _next(x) {\n        this.destination.next(this.value);\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,KAAT,CAAeC,KAAf,EAAsB;EACzB,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,aAAJ,CAAkBH,KAAlB,CAAZ,CAAnB;AACH;;AACD,MAAMG,aAAN,CAAoB;EAChBC,WAAW,CAACJ,KAAD,EAAQ;IACf,KAAKA,KAAL,GAAaA,KAAb;EACH;;EACDK,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,eAAJ,CAAoBF,UAApB,EAAgC,KAAKN,KAArC,CAAjB,CAAP;EACH;;AANe;;AAQpB,MAAMQ,eAAN,SAA8BV,UAA9B,CAAyC;EACrCM,WAAW,CAACK,WAAD,EAAcT,KAAd,EAAqB;IAC5B,MAAMS,WAAN;IACA,KAAKT,KAAL,GAAaA,KAAb;EACH;;EACDU,KAAK,CAACC,CAAD,EAAI;IACL,KAAKF,WAAL,CAAiBG,IAAjB,CAAsB,KAAKZ,KAA3B;EACH;;AAPoC"}, "metadata": {}, "sourceType": "module"}