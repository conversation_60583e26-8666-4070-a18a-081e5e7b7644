{"ast": null, "code": "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "map": {"version": 3, "names": ["getFreshSideObject", "top", "right", "bottom", "left"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/utils/getFreshSideObject.js"], "sourcesContent": ["export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}"], "mappings": "AAAA,eAAe,SAASA,kBAAT,GAA8B;EAC3C,OAAO;IACLC,GAAG,EAAE,CADA;IAELC,KAAK,EAAE,CAFF;IAGLC,MAAM,EAAE,CAHH;IAILC,IAAI,EAAE;EAJD,CAAP;AAMD"}, "metadata": {}, "sourceType": "module"}