{"ast": null, "code": "export const observable = (() => typeof Symbol === 'function' && Symbol.observable || '@@observable')();", "map": {"version": 3, "names": ["observable", "Symbol"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/symbol/observable.js"], "sourcesContent": ["export const observable = (() => typeof Symbol === 'function' && Symbol.observable || '@@observable')();\n"], "mappings": "AAAA,OAAO,MAAMA,UAAU,GAAG,CAAC,MAAM,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACD,UAAvC,IAAqD,cAA5D,GAAnB"}, "metadata": {}, "sourceType": "module"}