{"ast": null, "code": "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element, strategy) {\n  var rect = getBoundingClientRect(element, false, strategy === 'fixed');\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent, strategy) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary, strategy) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "map": {"version": 3, "names": ["viewport", "getViewportRect", "getDocumentRect", "listScrollParents", "getOffsetParent", "getDocumentElement", "getComputedStyle", "isElement", "isHTMLElement", "getBoundingClientRect", "getParentNode", "contains", "getNodeName", "rectToClientRect", "max", "min", "getInnerBoundingClientRect", "element", "strategy", "rect", "top", "clientTop", "left", "clientLeft", "bottom", "clientHeight", "right", "clientWidth", "width", "height", "x", "y", "getClientRectFromMixedType", "clippingParent", "getClippingParents", "clippingParents", "canEscapeClipping", "indexOf", "position", "clipperElement", "filter", "getClippingRect", "boundary", "rootBoundary", "mainClippingParents", "concat", "firstClippingParent", "clippingRect", "reduce", "accRect"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js"], "sourcesContent": ["import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element, strategy) {\n  var rect = getBoundingClientRect(element, false, strategy === 'fixed');\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent, strategy) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary, strategy) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}"], "mappings": "AAAA,SAASA,QAAT,QAAyB,aAAzB;AACA,OAAOC,eAAP,MAA4B,sBAA5B;AACA,OAAOC,eAAP,MAA4B,sBAA5B;AACA,OAAOC,iBAAP,MAA8B,wBAA9B;AACA,OAAOC,eAAP,MAA4B,sBAA5B;AACA,OAAOC,kBAAP,MAA+B,yBAA/B;AACA,OAAOC,gBAAP,MAA6B,uBAA7B;AACA,SAASC,SAAT,EAAoBC,aAApB,QAAyC,iBAAzC;AACA,OAAOC,qBAAP,MAAkC,4BAAlC;AACA,OAAOC,aAAP,MAA0B,oBAA1B;AACA,OAAOC,QAAP,MAAqB,eAArB;AACA,OAAOC,WAAP,MAAwB,kBAAxB;AACA,OAAOC,gBAAP,MAA6B,8BAA7B;AACA,SAASC,GAAT,EAAcC,GAAd,QAAyB,kBAAzB;;AAEA,SAASC,0BAAT,CAAoCC,OAApC,EAA6CC,QAA7C,EAAuD;EACrD,IAAIC,IAAI,GAAGV,qBAAqB,CAACQ,OAAD,EAAU,KAAV,EAAiBC,QAAQ,KAAK,OAA9B,CAAhC;EACAC,IAAI,CAACC,GAAL,GAAWD,IAAI,CAACC,GAAL,GAAWH,OAAO,CAACI,SAA9B;EACAF,IAAI,CAACG,IAAL,GAAYH,IAAI,CAACG,IAAL,GAAYL,OAAO,CAACM,UAAhC;EACAJ,IAAI,CAACK,MAAL,GAAcL,IAAI,CAACC,GAAL,GAAWH,OAAO,CAACQ,YAAjC;EACAN,IAAI,CAACO,KAAL,GAAaP,IAAI,CAACG,IAAL,GAAYL,OAAO,CAACU,WAAjC;EACAR,IAAI,CAACS,KAAL,GAAaX,OAAO,CAACU,WAArB;EACAR,IAAI,CAACU,MAAL,GAAcZ,OAAO,CAACQ,YAAtB;EACAN,IAAI,CAACW,CAAL,GAASX,IAAI,CAACG,IAAd;EACAH,IAAI,CAACY,CAAL,GAASZ,IAAI,CAACC,GAAd;EACA,OAAOD,IAAP;AACD;;AAED,SAASa,0BAAT,CAAoCf,OAApC,EAA6CgB,cAA7C,EAA6Df,QAA7D,EAAuE;EACrE,OAAOe,cAAc,KAAKjC,QAAnB,GAA8Ba,gBAAgB,CAACZ,eAAe,CAACgB,OAAD,EAAUC,QAAV,CAAhB,CAA9C,GAAqFX,SAAS,CAAC0B,cAAD,CAAT,GAA4BjB,0BAA0B,CAACiB,cAAD,EAAiBf,QAAjB,CAAtD,GAAmFL,gBAAgB,CAACX,eAAe,CAACG,kBAAkB,CAACY,OAAD,CAAnB,CAAhB,CAA/L;AACD,C,CAAC;AACF;AACA;;;AAGA,SAASiB,kBAAT,CAA4BjB,OAA5B,EAAqC;EACnC,IAAIkB,eAAe,GAAGhC,iBAAiB,CAACO,aAAa,CAACO,OAAD,CAAd,CAAvC;EACA,IAAImB,iBAAiB,GAAG,CAAC,UAAD,EAAa,OAAb,EAAsBC,OAAtB,CAA8B/B,gBAAgB,CAACW,OAAD,CAAhB,CAA0BqB,QAAxD,KAAqE,CAA7F;EACA,IAAIC,cAAc,GAAGH,iBAAiB,IAAI5B,aAAa,CAACS,OAAD,CAAlC,GAA8Cb,eAAe,CAACa,OAAD,CAA7D,GAAyEA,OAA9F;;EAEA,IAAI,CAACV,SAAS,CAACgC,cAAD,CAAd,EAAgC;IAC9B,OAAO,EAAP;EACD,CAPkC,CAOjC;;;EAGF,OAAOJ,eAAe,CAACK,MAAhB,CAAuB,UAAUP,cAAV,EAA0B;IACtD,OAAO1B,SAAS,CAAC0B,cAAD,CAAT,IAA6BtB,QAAQ,CAACsB,cAAD,EAAiBM,cAAjB,CAArC,IAAyE3B,WAAW,CAACqB,cAAD,CAAX,KAAgC,MAAhH;EACD,CAFM,CAAP;AAGD,C,CAAC;AACF;;;AAGA,eAAe,SAASQ,eAAT,CAAyBxB,OAAzB,EAAkCyB,QAAlC,EAA4CC,YAA5C,EAA0DzB,QAA1D,EAAoE;EACjF,IAAI0B,mBAAmB,GAAGF,QAAQ,KAAK,iBAAb,GAAiCR,kBAAkB,CAACjB,OAAD,CAAnD,GAA+D,GAAG4B,MAAH,CAAUH,QAAV,CAAzF;EACA,IAAIP,eAAe,GAAG,GAAGU,MAAH,CAAUD,mBAAV,EAA+B,CAACD,YAAD,CAA/B,CAAtB;EACA,IAAIG,mBAAmB,GAAGX,eAAe,CAAC,CAAD,CAAzC;EACA,IAAIY,YAAY,GAAGZ,eAAe,CAACa,MAAhB,CAAuB,UAAUC,OAAV,EAAmBhB,cAAnB,EAAmC;IAC3E,IAAId,IAAI,GAAGa,0BAA0B,CAACf,OAAD,EAAUgB,cAAV,EAA0Bf,QAA1B,CAArC;IACA+B,OAAO,CAAC7B,GAAR,GAAcN,GAAG,CAACK,IAAI,CAACC,GAAN,EAAW6B,OAAO,CAAC7B,GAAnB,CAAjB;IACA6B,OAAO,CAACvB,KAAR,GAAgBX,GAAG,CAACI,IAAI,CAACO,KAAN,EAAauB,OAAO,CAACvB,KAArB,CAAnB;IACAuB,OAAO,CAACzB,MAAR,GAAiBT,GAAG,CAACI,IAAI,CAACK,MAAN,EAAcyB,OAAO,CAACzB,MAAtB,CAApB;IACAyB,OAAO,CAAC3B,IAAR,GAAeR,GAAG,CAACK,IAAI,CAACG,IAAN,EAAY2B,OAAO,CAAC3B,IAApB,CAAlB;IACA,OAAO2B,OAAP;EACD,CAPkB,EAOhBjB,0BAA0B,CAACf,OAAD,EAAU6B,mBAAV,EAA+B5B,QAA/B,CAPV,CAAnB;EAQA6B,YAAY,CAACnB,KAAb,GAAqBmB,YAAY,CAACrB,KAAb,GAAqBqB,YAAY,CAACzB,IAAvD;EACAyB,YAAY,CAAClB,MAAb,GAAsBkB,YAAY,CAACvB,MAAb,GAAsBuB,YAAY,CAAC3B,GAAzD;EACA2B,YAAY,CAACjB,CAAb,GAAiBiB,YAAY,CAACzB,IAA9B;EACAyB,YAAY,CAAChB,CAAb,GAAiBgB,YAAY,CAAC3B,GAA9B;EACA,OAAO2B,YAAP;AACD"}, "metadata": {}, "sourceType": "module"}