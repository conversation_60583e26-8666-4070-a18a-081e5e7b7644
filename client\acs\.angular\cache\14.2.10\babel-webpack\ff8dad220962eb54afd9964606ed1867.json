{"ast": null, "code": "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "map": {"version": 3, "names": ["getNodeName", "isTableElement", "element", "indexOf"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/dom-utils/isTableElement.js"], "sourcesContent": ["import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}"], "mappings": "AAAA,OAAOA,WAAP,MAAwB,kBAAxB;AACA,eAAe,SAASC,cAAT,CAAwBC,OAAxB,EAAiC;EAC9C,OAAO,CAAC,OAAD,EAAU,IAAV,EAAgB,IAAhB,EAAsBC,OAAtB,CAA8BH,WAAW,CAACE,OAAD,CAAzC,KAAuD,CAA9D;AACD"}, "metadata": {}, "sourceType": "module"}