{"ast": null, "code": "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name; // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "map": {"version": 3, "names": ["computeOffsets", "popperOffsets", "_ref", "state", "name", "modifiersData", "reference", "rects", "element", "popper", "strategy", "placement", "enabled", "phase", "fn", "data"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/modifiers/popperOffsets.js"], "sourcesContent": ["import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};"], "mappings": "AAAA,OAAOA,cAAP,MAA2B,4BAA3B;;AAEA,SAASC,aAAT,CAAuBC,IAAvB,EAA6B;EAC3B,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAjB;EAAA,IACIC,IAAI,GAAGF,IAAI,CAACE,IADhB,CAD2B,CAG3B;EACA;EACA;EACA;;EACAD,KAAK,CAACE,aAAN,CAAoBD,IAApB,IAA4BJ,cAAc,CAAC;IACzCM,SAAS,EAAEH,KAAK,CAACI,KAAN,CAAYD,SADkB;IAEzCE,OAAO,EAAEL,KAAK,CAACI,KAAN,CAAYE,MAFoB;IAGzCC,QAAQ,EAAE,UAH+B;IAIzCC,SAAS,EAAER,KAAK,CAACQ;EAJwB,CAAD,CAA1C;AAMD,C,CAAC;;;AAGF,eAAe;EACbP,IAAI,EAAE,eADO;EAEbQ,OAAO,EAAE,IAFI;EAGbC,KAAK,EAAE,MAHM;EAIbC,EAAE,EAAEb,aAJS;EAKbc,IAAI,EAAE;AALO,CAAf"}, "metadata": {}, "sourceType": "module"}