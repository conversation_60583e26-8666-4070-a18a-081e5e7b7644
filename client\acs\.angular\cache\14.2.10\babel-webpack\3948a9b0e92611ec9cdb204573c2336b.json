{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, Inject, NgModule } from '@angular/core';\nimport * as i2 from '@ngrx/store';\nimport { ActionsSubject, UPDATE, INIT, INITIAL_STATE, StateObservable, ReducerManagerDispatcher } from '@ngrx/store';\nimport { EMPTY, Observable, of, merge, queueScheduler, ReplaySubject } from 'rxjs';\nimport { share, filter, map, concatMap, timeout, debounceTime, catchError, take, takeUntil, switchMap, skip, observeOn, withLatestFrom, scan } from 'rxjs/operators';\nconst PERFORM_ACTION = 'PERFORM_ACTION';\nconst REFRESH = 'REFRESH';\nconst RESET = 'RESET';\nconst ROLLBACK = 'ROLLBACK';\nconst COMMIT = 'COMMIT';\nconst SWEEP = 'SWEEP';\nconst TOGGLE_ACTION = 'TOGGLE_ACTION';\nconst SET_ACTIONS_ACTIVE = 'SET_ACTIONS_ACTIVE';\nconst JUMP_TO_STATE = 'JUMP_TO_STATE';\nconst JUMP_TO_ACTION = 'JUMP_TO_ACTION';\nconst IMPORT_STATE = 'IMPORT_STATE';\nconst LOCK_CHANGES = 'LOCK_CHANGES';\nconst PAUSE_RECORDING = 'PAUSE_RECORDING';\n\nclass PerformAction {\n  constructor(action, timestamp) {\n    this.action = action;\n    this.timestamp = timestamp;\n    this.type = PERFORM_ACTION;\n\n    if (typeof action.type === 'undefined') {\n      throw new Error('Actions may not have an undefined \"type\" property. ' + 'Have you misspelled a constant?');\n    }\n  }\n\n}\n\nclass Refresh {\n  constructor() {\n    this.type = REFRESH;\n  }\n\n}\n\nclass Reset {\n  constructor(timestamp) {\n    this.timestamp = timestamp;\n    this.type = RESET;\n  }\n\n}\n\nclass Rollback {\n  constructor(timestamp) {\n    this.timestamp = timestamp;\n    this.type = ROLLBACK;\n  }\n\n}\n\nclass Commit {\n  constructor(timestamp) {\n    this.timestamp = timestamp;\n    this.type = COMMIT;\n  }\n\n}\n\nclass Sweep {\n  constructor() {\n    this.type = SWEEP;\n  }\n\n}\n\nclass ToggleAction {\n  constructor(id) {\n    this.id = id;\n    this.type = TOGGLE_ACTION;\n  }\n\n}\n\nclass SetActionsActive {\n  constructor(start, end, active = true) {\n    this.start = start;\n    this.end = end;\n    this.active = active;\n    this.type = SET_ACTIONS_ACTIVE;\n  }\n\n}\n\nclass JumpToState {\n  constructor(index) {\n    this.index = index;\n    this.type = JUMP_TO_STATE;\n  }\n\n}\n\nclass JumpToAction {\n  constructor(actionId) {\n    this.actionId = actionId;\n    this.type = JUMP_TO_ACTION;\n  }\n\n}\n\nclass ImportState {\n  constructor(nextLiftedState) {\n    this.nextLiftedState = nextLiftedState;\n    this.type = IMPORT_STATE;\n  }\n\n}\n\nclass LockChanges {\n  constructor(status) {\n    this.status = status;\n    this.type = LOCK_CHANGES;\n  }\n\n}\n\nclass PauseRecording {\n  constructor(status) {\n    this.status = status;\n    this.type = PAUSE_RECORDING;\n  }\n\n}\n/**\n * Chrome extension documentation\n * @see https://github.com/reduxjs/redux-devtools/blob/main/extension/docs/API/Arguments.md\n * Firefox extension documentation\n * @see https://github.com/zalmoxisus/redux-devtools-extension/blob/master/docs/API/Arguments.md\n */\n\n\nclass StoreDevtoolsConfig {\n  constructor() {\n    /**\n     * Maximum allowed actions to be stored in the history tree (default: `false`)\n     */\n    this.maxAge = false;\n  }\n\n}\n\nconst STORE_DEVTOOLS_CONFIG = new InjectionToken('@ngrx/store-devtools Options');\n/**\n * Used to provide a `StoreDevtoolsConfig` for the store-devtools.\n */\n\nconst INITIAL_OPTIONS = new InjectionToken('@ngrx/store-devtools Initial Config');\n\nfunction noMonitor() {\n  return null;\n}\n\nconst DEFAULT_NAME = 'NgRx Store DevTools';\n\nfunction createConfig(optionsInput) {\n  const DEFAULT_OPTIONS = {\n    maxAge: false,\n    monitor: noMonitor,\n    actionSanitizer: undefined,\n    stateSanitizer: undefined,\n    name: DEFAULT_NAME,\n    serialize: false,\n    logOnly: false,\n    autoPause: false,\n    // Add all features explicitly. This prevent buggy behavior for\n    // options like \"lock\" which might otherwise not show up.\n    features: {\n      pause: true,\n      lock: true,\n      persist: true,\n      export: true,\n      import: 'custom',\n      jump: true,\n      skip: true,\n      reorder: true,\n      dispatch: true,\n      test: true // Generate tests for the selected actions\n\n    }\n  };\n  const options = typeof optionsInput === 'function' ? optionsInput() : optionsInput;\n  const logOnly = options.logOnly ? {\n    pause: true,\n    export: true,\n    test: true\n  } : false;\n  const features = options.features || logOnly || DEFAULT_OPTIONS.features;\n  const config = Object.assign({}, DEFAULT_OPTIONS, {\n    features\n  }, options);\n\n  if (config.maxAge && config.maxAge < 2) {\n    throw new Error(`Devtools 'maxAge' cannot be less than 2, got ${config.maxAge}`);\n  }\n\n  return config;\n}\n\nfunction difference(first, second) {\n  return first.filter(item => second.indexOf(item) < 0);\n}\n/**\n * Provides an app's view into the state of the lifted store.\n */\n\n\nfunction unliftState(liftedState) {\n  const {\n    computedStates,\n    currentStateIndex\n  } = liftedState; // At start up NgRx dispatches init actions,\n  // When these init actions are being filtered out by the predicate or safe/block list options\n  // we don't have a complete computed states yet.\n  // At this point it could happen that we're out of bounds, when this happens we fall back to the last known state\n\n  if (currentStateIndex >= computedStates.length) {\n    const {\n      state\n    } = computedStates[computedStates.length - 1];\n    return state;\n  }\n\n  const {\n    state\n  } = computedStates[currentStateIndex];\n  return state;\n}\n\nfunction unliftAction(liftedState) {\n  return liftedState.actionsById[liftedState.nextActionId - 1];\n}\n/**\n * Lifts an app's action into an action on the lifted store.\n */\n\n\nfunction liftAction(action) {\n  return new PerformAction(action, +Date.now());\n}\n/**\n * Sanitizes given actions with given function.\n */\n\n\nfunction sanitizeActions(actionSanitizer, actions) {\n  return Object.keys(actions).reduce((sanitizedActions, actionIdx) => {\n    const idx = Number(actionIdx);\n    sanitizedActions[idx] = sanitizeAction(actionSanitizer, actions[idx], idx);\n    return sanitizedActions;\n  }, {});\n}\n/**\n * Sanitizes given action with given function.\n */\n\n\nfunction sanitizeAction(actionSanitizer, action, actionIdx) {\n  return { ...action,\n    action: actionSanitizer(action.action, actionIdx)\n  };\n}\n/**\n * Sanitizes given states with given function.\n */\n\n\nfunction sanitizeStates(stateSanitizer, states) {\n  return states.map((computedState, idx) => ({\n    state: sanitizeState(stateSanitizer, computedState.state, idx),\n    error: computedState.error\n  }));\n}\n/**\n * Sanitizes given state with given function.\n */\n\n\nfunction sanitizeState(stateSanitizer, state, stateIdx) {\n  return stateSanitizer(state, stateIdx);\n}\n/**\n * Read the config and tell if actions should be filtered\n */\n\n\nfunction shouldFilterActions(config) {\n  return config.predicate || config.actionsSafelist || config.actionsBlocklist;\n}\n/**\n * Return a full filtered lifted state\n */\n\n\nfunction filterLiftedState(liftedState, predicate, safelist, blocklist) {\n  const filteredStagedActionIds = [];\n  const filteredActionsById = {};\n  const filteredComputedStates = [];\n  liftedState.stagedActionIds.forEach((id, idx) => {\n    const liftedAction = liftedState.actionsById[id];\n    if (!liftedAction) return;\n\n    if (idx && isActionFiltered(liftedState.computedStates[idx], liftedAction, predicate, safelist, blocklist)) {\n      return;\n    }\n\n    filteredActionsById[id] = liftedAction;\n    filteredStagedActionIds.push(id);\n    filteredComputedStates.push(liftedState.computedStates[idx]);\n  });\n  return { ...liftedState,\n    stagedActionIds: filteredStagedActionIds,\n    actionsById: filteredActionsById,\n    computedStates: filteredComputedStates\n  };\n}\n/**\n * Return true is the action should be ignored\n */\n\n\nfunction isActionFiltered(state, action, predicate, safelist, blockedlist) {\n  const predicateMatch = predicate && !predicate(state, action.action);\n  const safelistMatch = safelist && !action.action.type.match(safelist.map(s => escapeRegExp(s)).join('|'));\n  const blocklistMatch = blockedlist && action.action.type.match(blockedlist.map(s => escapeRegExp(s)).join('|'));\n  return predicateMatch || safelistMatch || blocklistMatch;\n}\n/**\n * Return string with escaped RegExp special characters\n * https://stackoverflow.com/a/6969486/1337347\n */\n\n\nfunction escapeRegExp(s) {\n  return s.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n\nclass DevtoolsDispatcher extends ActionsSubject {}\n/** @nocollapse */\n\n\nDevtoolsDispatcher.ɵfac = /* @__PURE__ */function () {\n  let ɵDevtoolsDispatcher_BaseFactory;\n  return function DevtoolsDispatcher_Factory(t) {\n    return (ɵDevtoolsDispatcher_BaseFactory || (ɵDevtoolsDispatcher_BaseFactory = i0.ɵɵgetInheritedFactory(DevtoolsDispatcher)))(t || DevtoolsDispatcher);\n  };\n}();\n/** @nocollapse */\n\n\nDevtoolsDispatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DevtoolsDispatcher,\n  factory: DevtoolsDispatcher.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DevtoolsDispatcher, [{\n    type: Injectable\n  }], null, null);\n})();\n\nconst ExtensionActionTypes = {\n  START: 'START',\n  DISPATCH: 'DISPATCH',\n  STOP: 'STOP',\n  ACTION: 'ACTION'\n};\nconst REDUX_DEVTOOLS_EXTENSION = new InjectionToken('@ngrx/store-devtools Redux Devtools Extension');\n\nclass DevtoolsExtension {\n  constructor(devtoolsExtension, config, dispatcher) {\n    this.config = config;\n    this.dispatcher = dispatcher;\n    this.devtoolsExtension = devtoolsExtension;\n    this.createActionStreams();\n  }\n\n  notify(action, state) {\n    if (!this.devtoolsExtension) {\n      return;\n    } // Check to see if the action requires a full update of the liftedState.\n    // If it is a simple action generated by the user's app and the recording\n    // is not locked/paused, only send the action and the current state (fast).\n    //\n    // A full liftedState update (slow: serializes the entire liftedState) is\n    // only required when:\n    //   a) redux-devtools-extension fires the @@Init action (ignored by\n    //      @ngrx/store-devtools)\n    //   b) an action is generated by an @ngrx module (e.g. @ngrx/effects/init\n    //      or @ngrx/store/update-reducers)\n    //   c) the state has been recomputed due to time-traveling\n    //   d) any action that is not a PerformAction to err on the side of\n    //      caution.\n\n\n    if (action.type === PERFORM_ACTION) {\n      if (state.isLocked || state.isPaused) {\n        return;\n      }\n\n      const currentState = unliftState(state);\n\n      if (shouldFilterActions(this.config) && isActionFiltered(currentState, action, this.config.predicate, this.config.actionsSafelist, this.config.actionsBlocklist)) {\n        return;\n      }\n\n      const sanitizedState = this.config.stateSanitizer ? sanitizeState(this.config.stateSanitizer, currentState, state.currentStateIndex) : currentState;\n      const sanitizedAction = this.config.actionSanitizer ? sanitizeAction(this.config.actionSanitizer, action, state.nextActionId) : action;\n      this.sendToReduxDevtools(() => this.extensionConnection.send(sanitizedAction, sanitizedState));\n    } else {\n      // Requires full state update\n      const sanitizedLiftedState = { ...state,\n        stagedActionIds: state.stagedActionIds,\n        actionsById: this.config.actionSanitizer ? sanitizeActions(this.config.actionSanitizer, state.actionsById) : state.actionsById,\n        computedStates: this.config.stateSanitizer ? sanitizeStates(this.config.stateSanitizer, state.computedStates) : state.computedStates\n      };\n      this.sendToReduxDevtools(() => this.devtoolsExtension.send(null, sanitizedLiftedState, this.getExtensionConfig(this.config)));\n    }\n  }\n\n  createChangesObservable() {\n    if (!this.devtoolsExtension) {\n      return EMPTY;\n    }\n\n    return new Observable(subscriber => {\n      const connection = this.devtoolsExtension.connect(this.getExtensionConfig(this.config));\n      this.extensionConnection = connection;\n      connection.init();\n      connection.subscribe(change => subscriber.next(change));\n      return connection.unsubscribe;\n    });\n  }\n\n  createActionStreams() {\n    // Listens to all changes\n    const changes$ = this.createChangesObservable().pipe(share()); // Listen for the start action\n\n    const start$ = changes$.pipe(filter(change => change.type === ExtensionActionTypes.START)); // Listen for the stop action\n\n    const stop$ = changes$.pipe(filter(change => change.type === ExtensionActionTypes.STOP)); // Listen for lifted actions\n\n    const liftedActions$ = changes$.pipe(filter(change => change.type === ExtensionActionTypes.DISPATCH), map(change => this.unwrapAction(change.payload)), concatMap(action => {\n      if (action.type === IMPORT_STATE) {\n        // State imports may happen in two situations:\n        // 1. Explicitly by user\n        // 2. User activated the \"persist state accross reloads\" option\n        //    and now the state is imported during reload.\n        // Because of option 2, we need to give possible\n        // lazy loaded reducers time to instantiate.\n        // As soon as there is no UPDATE action within 1 second,\n        // it is assumed that all reducers are loaded.\n        return this.dispatcher.pipe(filter(action => action.type === UPDATE), timeout(1000), debounceTime(1000), map(() => action), catchError(() => of(action)), take(1));\n      } else {\n        return of(action);\n      }\n    })); // Listen for unlifted actions\n\n    const actions$ = changes$.pipe(filter(change => change.type === ExtensionActionTypes.ACTION), map(change => this.unwrapAction(change.payload)));\n    const actionsUntilStop$ = actions$.pipe(takeUntil(stop$));\n    const liftedUntilStop$ = liftedActions$.pipe(takeUntil(stop$));\n    this.start$ = start$.pipe(takeUntil(stop$)); // Only take the action sources between the start/stop events\n\n    this.actions$ = this.start$.pipe(switchMap(() => actionsUntilStop$));\n    this.liftedActions$ = this.start$.pipe(switchMap(() => liftedUntilStop$));\n  }\n\n  unwrapAction(action) {\n    return typeof action === 'string' ? eval(`(${action})`) : action;\n  }\n\n  getExtensionConfig(config) {\n    const extensionOptions = {\n      name: config.name,\n      features: config.features,\n      serialize: config.serialize,\n      autoPause: config.autoPause ?? false // The action/state sanitizers are not added to the config\n      // because sanitation is done in this class already.\n      // It is done before sending it to the devtools extension for consistency:\n      // - If we call extensionConnection.send(...),\n      //   the extension would call the sanitizers.\n      // - If we call devtoolsExtension.send(...) (aka full state update),\n      //   the extension would NOT call the sanitizers, so we have to do it ourselves.\n\n    };\n\n    if (config.maxAge !== false\n    /* support === 0 */\n    ) {\n      extensionOptions.maxAge = config.maxAge;\n    }\n\n    return extensionOptions;\n  }\n\n  sendToReduxDevtools(send) {\n    try {\n      send();\n    } catch (err) {\n      console.warn('@ngrx/store-devtools: something went wrong inside the redux devtools', err);\n    }\n  }\n\n}\n/** @nocollapse */\n\n\nDevtoolsExtension.ɵfac = function DevtoolsExtension_Factory(t) {\n  return new (t || DevtoolsExtension)(i0.ɵɵinject(REDUX_DEVTOOLS_EXTENSION), i0.ɵɵinject(STORE_DEVTOOLS_CONFIG), i0.ɵɵinject(DevtoolsDispatcher));\n};\n/** @nocollapse */\n\n\nDevtoolsExtension.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DevtoolsExtension,\n  factory: DevtoolsExtension.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DevtoolsExtension, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [REDUX_DEVTOOLS_EXTENSION]\n      }]\n    }, {\n      type: StoreDevtoolsConfig,\n      decorators: [{\n        type: Inject,\n        args: [STORE_DEVTOOLS_CONFIG]\n      }]\n    }, {\n      type: DevtoolsDispatcher\n    }];\n  }, null);\n})();\n\nconst INIT_ACTION = {\n  type: INIT\n};\nconst RECOMPUTE = '@ngrx/store-devtools/recompute';\nconst RECOMPUTE_ACTION = {\n  type: RECOMPUTE\n};\n/**\n * Computes the next entry in the log by applying an action.\n */\n\nfunction computeNextEntry(reducer, action, state, error, errorHandler) {\n  if (error) {\n    return {\n      state,\n      error: 'Interrupted by an error up the chain'\n    };\n  }\n\n  let nextState = state;\n  let nextError;\n\n  try {\n    nextState = reducer(state, action);\n  } catch (err) {\n    nextError = err.toString();\n    errorHandler.handleError(err);\n  }\n\n  return {\n    state: nextState,\n    error: nextError\n  };\n}\n/**\n * Runs the reducer on invalidated actions to get a fresh computation log.\n */\n\n\nfunction recomputeStates(computedStates, minInvalidatedStateIndex, reducer, committedState, actionsById, stagedActionIds, skippedActionIds, errorHandler, isPaused) {\n  // Optimization: exit early and return the same reference\n  // if we know nothing could have changed.\n  if (minInvalidatedStateIndex >= computedStates.length && computedStates.length === stagedActionIds.length) {\n    return computedStates;\n  }\n\n  const nextComputedStates = computedStates.slice(0, minInvalidatedStateIndex); // If the recording is paused, recompute all states up until the pause state,\n  // else recompute all states.\n\n  const lastIncludedActionId = stagedActionIds.length - (isPaused ? 1 : 0);\n\n  for (let i = minInvalidatedStateIndex; i < lastIncludedActionId; i++) {\n    const actionId = stagedActionIds[i];\n    const action = actionsById[actionId].action;\n    const previousEntry = nextComputedStates[i - 1];\n    const previousState = previousEntry ? previousEntry.state : committedState;\n    const previousError = previousEntry ? previousEntry.error : undefined;\n    const shouldSkip = skippedActionIds.indexOf(actionId) > -1;\n    const entry = shouldSkip ? previousEntry : computeNextEntry(reducer, action, previousState, previousError, errorHandler);\n    nextComputedStates.push(entry);\n  } // If the recording is paused, the last state will not be recomputed,\n  // because it's essentially not part of the state history.\n\n\n  if (isPaused) {\n    nextComputedStates.push(computedStates[computedStates.length - 1]);\n  }\n\n  return nextComputedStates;\n}\n\nfunction liftInitialState(initialCommittedState, monitorReducer) {\n  return {\n    monitorState: monitorReducer(undefined, {}),\n    nextActionId: 1,\n    actionsById: {\n      0: liftAction(INIT_ACTION)\n    },\n    stagedActionIds: [0],\n    skippedActionIds: [],\n    committedState: initialCommittedState,\n    currentStateIndex: 0,\n    computedStates: [],\n    isLocked: false,\n    isPaused: false\n  };\n}\n/**\n * Creates a history state reducer from an app's reducer.\n */\n\n\nfunction liftReducerWith(initialCommittedState, initialLiftedState, errorHandler, monitorReducer, options = {}) {\n  /**\n   * Manages how the history actions modify the history state.\n   */\n  return reducer => (liftedState, liftedAction) => {\n    let {\n      monitorState,\n      actionsById,\n      nextActionId,\n      stagedActionIds,\n      skippedActionIds,\n      committedState,\n      currentStateIndex,\n      computedStates,\n      isLocked,\n      isPaused\n    } = liftedState || initialLiftedState;\n\n    if (!liftedState) {\n      // Prevent mutating initialLiftedState\n      actionsById = Object.create(actionsById);\n    }\n\n    function commitExcessActions(n) {\n      // Auto-commits n-number of excess actions.\n      let excess = n;\n      let idsToDelete = stagedActionIds.slice(1, excess + 1);\n\n      for (let i = 0; i < idsToDelete.length; i++) {\n        if (computedStates[i + 1].error) {\n          // Stop if error is found. Commit actions up to error.\n          excess = i;\n          idsToDelete = stagedActionIds.slice(1, excess + 1);\n          break;\n        } else {\n          delete actionsById[idsToDelete[i]];\n        }\n      }\n\n      skippedActionIds = skippedActionIds.filter(id => idsToDelete.indexOf(id) === -1);\n      stagedActionIds = [0, ...stagedActionIds.slice(excess + 1)];\n      committedState = computedStates[excess].state;\n      computedStates = computedStates.slice(excess);\n      currentStateIndex = currentStateIndex > excess ? currentStateIndex - excess : 0;\n    }\n\n    function commitChanges() {\n      // Consider the last committed state the new starting point.\n      // Squash any staged actions into a single committed state.\n      actionsById = {\n        0: liftAction(INIT_ACTION)\n      };\n      nextActionId = 1;\n      stagedActionIds = [0];\n      skippedActionIds = [];\n      committedState = computedStates[currentStateIndex].state;\n      currentStateIndex = 0;\n      computedStates = [];\n    } // By default, aggressively recompute every state whatever happens.\n    // This has O(n) performance, so we'll override this to a sensible\n    // value whenever we feel like we don't have to recompute the states.\n\n\n    let minInvalidatedStateIndex = 0;\n\n    switch (liftedAction.type) {\n      case LOCK_CHANGES:\n        {\n          isLocked = liftedAction.status;\n          minInvalidatedStateIndex = Infinity;\n          break;\n        }\n\n      case PAUSE_RECORDING:\n        {\n          isPaused = liftedAction.status;\n\n          if (isPaused) {\n            // Add a pause action to signal the devtools-user the recording is paused.\n            // The corresponding state will be overwritten on each update to always contain\n            // the latest state (see Actions.PERFORM_ACTION).\n            stagedActionIds = [...stagedActionIds, nextActionId];\n            actionsById[nextActionId] = new PerformAction({\n              type: '@ngrx/devtools/pause'\n            }, +Date.now());\n            nextActionId++;\n            minInvalidatedStateIndex = stagedActionIds.length - 1;\n            computedStates = computedStates.concat(computedStates[computedStates.length - 1]);\n\n            if (currentStateIndex === stagedActionIds.length - 2) {\n              currentStateIndex++;\n            }\n\n            minInvalidatedStateIndex = Infinity;\n          } else {\n            commitChanges();\n          }\n\n          break;\n        }\n\n      case RESET:\n        {\n          // Get back to the state the store was created with.\n          actionsById = {\n            0: liftAction(INIT_ACTION)\n          };\n          nextActionId = 1;\n          stagedActionIds = [0];\n          skippedActionIds = [];\n          committedState = initialCommittedState;\n          currentStateIndex = 0;\n          computedStates = [];\n          break;\n        }\n\n      case COMMIT:\n        {\n          commitChanges();\n          break;\n        }\n\n      case ROLLBACK:\n        {\n          // Forget about any staged actions.\n          // Start again from the last committed state.\n          actionsById = {\n            0: liftAction(INIT_ACTION)\n          };\n          nextActionId = 1;\n          stagedActionIds = [0];\n          skippedActionIds = [];\n          currentStateIndex = 0;\n          computedStates = [];\n          break;\n        }\n\n      case TOGGLE_ACTION:\n        {\n          // Toggle whether an action with given ID is skipped.\n          // Being skipped means it is a no-op during the computation.\n          const {\n            id: actionId\n          } = liftedAction;\n          const index = skippedActionIds.indexOf(actionId);\n\n          if (index === -1) {\n            skippedActionIds = [actionId, ...skippedActionIds];\n          } else {\n            skippedActionIds = skippedActionIds.filter(id => id !== actionId);\n          } // Optimization: we know history before this action hasn't changed\n\n\n          minInvalidatedStateIndex = stagedActionIds.indexOf(actionId);\n          break;\n        }\n\n      case SET_ACTIONS_ACTIVE:\n        {\n          // Toggle whether an action with given ID is skipped.\n          // Being skipped means it is a no-op during the computation.\n          const {\n            start,\n            end,\n            active\n          } = liftedAction;\n          const actionIds = [];\n\n          for (let i = start; i < end; i++) actionIds.push(i);\n\n          if (active) {\n            skippedActionIds = difference(skippedActionIds, actionIds);\n          } else {\n            skippedActionIds = [...skippedActionIds, ...actionIds];\n          } // Optimization: we know history before this action hasn't changed\n\n\n          minInvalidatedStateIndex = stagedActionIds.indexOf(start);\n          break;\n        }\n\n      case JUMP_TO_STATE:\n        {\n          // Without recomputing anything, move the pointer that tell us\n          // which state is considered the current one. Useful for sliders.\n          currentStateIndex = liftedAction.index; // Optimization: we know the history has not changed.\n\n          minInvalidatedStateIndex = Infinity;\n          break;\n        }\n\n      case JUMP_TO_ACTION:\n        {\n          // Jumps to a corresponding state to a specific action.\n          // Useful when filtering actions.\n          const index = stagedActionIds.indexOf(liftedAction.actionId);\n          if (index !== -1) currentStateIndex = index;\n          minInvalidatedStateIndex = Infinity;\n          break;\n        }\n\n      case SWEEP:\n        {\n          // Forget any actions that are currently being skipped.\n          stagedActionIds = difference(stagedActionIds, skippedActionIds);\n          skippedActionIds = [];\n          currentStateIndex = Math.min(currentStateIndex, stagedActionIds.length - 1);\n          break;\n        }\n\n      case PERFORM_ACTION:\n        {\n          // Ignore action and return state as is if recording is locked\n          if (isLocked) {\n            return liftedState || initialLiftedState;\n          }\n\n          if (isPaused || liftedState && isActionFiltered(liftedState.computedStates[currentStateIndex], liftedAction, options.predicate, options.actionsSafelist, options.actionsBlocklist)) {\n            // If recording is paused or if the action should be ignored, overwrite the last state\n            // (corresponds to the pause action) and keep everything else as is.\n            // This way, the app gets the new current state while the devtools\n            // do not record another action.\n            const lastState = computedStates[computedStates.length - 1];\n            computedStates = [...computedStates.slice(0, -1), computeNextEntry(reducer, liftedAction.action, lastState.state, lastState.error, errorHandler)];\n            minInvalidatedStateIndex = Infinity;\n            break;\n          } // Auto-commit as new actions come in.\n\n\n          if (options.maxAge && stagedActionIds.length === options.maxAge) {\n            commitExcessActions(1);\n          }\n\n          if (currentStateIndex === stagedActionIds.length - 1) {\n            currentStateIndex++;\n          }\n\n          const actionId = nextActionId++; // Mutation! This is the hottest path, and we optimize on purpose.\n          // It is safe because we set a new key in a cache dictionary.\n\n          actionsById[actionId] = liftedAction;\n          stagedActionIds = [...stagedActionIds, actionId]; // Optimization: we know that only the new action needs computing.\n\n          minInvalidatedStateIndex = stagedActionIds.length - 1;\n          break;\n        }\n\n      case IMPORT_STATE:\n        {\n          // Completely replace everything.\n          ({\n            monitorState,\n            actionsById,\n            nextActionId,\n            stagedActionIds,\n            skippedActionIds,\n            committedState,\n            currentStateIndex,\n            computedStates,\n            isLocked,\n            isPaused\n          } = liftedAction.nextLiftedState);\n          break;\n        }\n\n      case INIT:\n        {\n          // Always recompute states on hot reload and init.\n          minInvalidatedStateIndex = 0;\n\n          if (options.maxAge && stagedActionIds.length > options.maxAge) {\n            // States must be recomputed before committing excess.\n            computedStates = recomputeStates(computedStates, minInvalidatedStateIndex, reducer, committedState, actionsById, stagedActionIds, skippedActionIds, errorHandler, isPaused);\n            commitExcessActions(stagedActionIds.length - options.maxAge); // Avoid double computation.\n\n            minInvalidatedStateIndex = Infinity;\n          }\n\n          break;\n        }\n\n      case UPDATE:\n        {\n          const stateHasErrors = computedStates.filter(state => state.error).length > 0;\n\n          if (stateHasErrors) {\n            // Recompute all states\n            minInvalidatedStateIndex = 0;\n\n            if (options.maxAge && stagedActionIds.length > options.maxAge) {\n              // States must be recomputed before committing excess.\n              computedStates = recomputeStates(computedStates, minInvalidatedStateIndex, reducer, committedState, actionsById, stagedActionIds, skippedActionIds, errorHandler, isPaused);\n              commitExcessActions(stagedActionIds.length - options.maxAge); // Avoid double computation.\n\n              minInvalidatedStateIndex = Infinity;\n            }\n          } else {\n            // If not paused/locked, add a new action to signal devtools-user\n            // that there was a reducer update.\n            if (!isPaused && !isLocked) {\n              if (currentStateIndex === stagedActionIds.length - 1) {\n                currentStateIndex++;\n              } // Add a new action to only recompute state\n\n\n              const actionId = nextActionId++;\n              actionsById[actionId] = new PerformAction(liftedAction, +Date.now());\n              stagedActionIds = [...stagedActionIds, actionId];\n              minInvalidatedStateIndex = stagedActionIds.length - 1;\n              computedStates = recomputeStates(computedStates, minInvalidatedStateIndex, reducer, committedState, actionsById, stagedActionIds, skippedActionIds, errorHandler, isPaused);\n            } // Recompute state history with latest reducer and update action\n\n\n            computedStates = computedStates.map(cmp => ({ ...cmp,\n              state: reducer(cmp.state, RECOMPUTE_ACTION)\n            }));\n            currentStateIndex = stagedActionIds.length - 1;\n\n            if (options.maxAge && stagedActionIds.length > options.maxAge) {\n              commitExcessActions(stagedActionIds.length - options.maxAge);\n            } // Avoid double computation.\n\n\n            minInvalidatedStateIndex = Infinity;\n          }\n\n          break;\n        }\n\n      default:\n        {\n          // If the action is not recognized, it's a monitor action.\n          // Optimization: a monitor action can't change history.\n          minInvalidatedStateIndex = Infinity;\n          break;\n        }\n    }\n\n    computedStates = recomputeStates(computedStates, minInvalidatedStateIndex, reducer, committedState, actionsById, stagedActionIds, skippedActionIds, errorHandler, isPaused);\n    monitorState = monitorReducer(monitorState, liftedAction);\n    return {\n      monitorState,\n      actionsById,\n      nextActionId,\n      stagedActionIds,\n      skippedActionIds,\n      committedState,\n      currentStateIndex,\n      computedStates,\n      isLocked,\n      isPaused\n    };\n  };\n}\n\nclass StoreDevtools {\n  constructor(dispatcher, actions$, reducers$, extension, scannedActions, errorHandler, initialState, config) {\n    const liftedInitialState = liftInitialState(initialState, config.monitor);\n    const liftReducer = liftReducerWith(initialState, liftedInitialState, errorHandler, config.monitor, config);\n    const liftedAction$ = merge(merge(actions$.asObservable().pipe(skip(1)), extension.actions$).pipe(map(liftAction)), dispatcher, extension.liftedActions$).pipe(observeOn(queueScheduler));\n    const liftedReducer$ = reducers$.pipe(map(liftReducer));\n    const liftedStateSubject = new ReplaySubject(1);\n    const liftedStateSubscription = liftedAction$.pipe(withLatestFrom(liftedReducer$), scan(({\n      state: liftedState\n    }, [action, reducer]) => {\n      let reducedLiftedState = reducer(liftedState, action); // On full state update\n      // If we have actions filters, we must filter completely our lifted state to be sync with the extension\n\n      if (action.type !== PERFORM_ACTION && shouldFilterActions(config)) {\n        reducedLiftedState = filterLiftedState(reducedLiftedState, config.predicate, config.actionsSafelist, config.actionsBlocklist);\n      } // Extension should be sent the sanitized lifted state\n\n\n      extension.notify(action, reducedLiftedState);\n      return {\n        state: reducedLiftedState,\n        action\n      };\n    }, {\n      state: liftedInitialState,\n      action: null\n    })).subscribe(({\n      state,\n      action\n    }) => {\n      liftedStateSubject.next(state);\n\n      if (action.type === PERFORM_ACTION) {\n        const unliftedAction = action.action;\n        scannedActions.next(unliftedAction);\n      }\n    });\n    const extensionStartSubscription = extension.start$.subscribe(() => {\n      this.refresh();\n    });\n    const liftedState$ = liftedStateSubject.asObservable();\n    const state$ = liftedState$.pipe(map(unliftState));\n    this.extensionStartSubscription = extensionStartSubscription;\n    this.stateSubscription = liftedStateSubscription;\n    this.dispatcher = dispatcher;\n    this.liftedState = liftedState$;\n    this.state = state$;\n  }\n\n  dispatch(action) {\n    this.dispatcher.next(action);\n  }\n\n  next(action) {\n    this.dispatcher.next(action);\n  }\n\n  error(error) {}\n\n  complete() {}\n\n  performAction(action) {\n    this.dispatch(new PerformAction(action, +Date.now()));\n  }\n\n  refresh() {\n    this.dispatch(new Refresh());\n  }\n\n  reset() {\n    this.dispatch(new Reset(+Date.now()));\n  }\n\n  rollback() {\n    this.dispatch(new Rollback(+Date.now()));\n  }\n\n  commit() {\n    this.dispatch(new Commit(+Date.now()));\n  }\n\n  sweep() {\n    this.dispatch(new Sweep());\n  }\n\n  toggleAction(id) {\n    this.dispatch(new ToggleAction(id));\n  }\n\n  jumpToAction(actionId) {\n    this.dispatch(new JumpToAction(actionId));\n  }\n\n  jumpToState(index) {\n    this.dispatch(new JumpToState(index));\n  }\n\n  importState(nextLiftedState) {\n    this.dispatch(new ImportState(nextLiftedState));\n  }\n\n  lockChanges(status) {\n    this.dispatch(new LockChanges(status));\n  }\n\n  pauseRecording(status) {\n    this.dispatch(new PauseRecording(status));\n  }\n\n}\n/** @nocollapse */\n\n\nStoreDevtools.ɵfac = function StoreDevtools_Factory(t) {\n  return new (t || StoreDevtools)(i0.ɵɵinject(DevtoolsDispatcher), i0.ɵɵinject(i2.ActionsSubject), i0.ɵɵinject(i2.ReducerObservable), i0.ɵɵinject(DevtoolsExtension), i0.ɵɵinject(i2.ScannedActionsSubject), i0.ɵɵinject(i0.ErrorHandler), i0.ɵɵinject(INITIAL_STATE), i0.ɵɵinject(STORE_DEVTOOLS_CONFIG));\n};\n/** @nocollapse */\n\n\nStoreDevtools.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: StoreDevtools,\n  factory: StoreDevtools.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StoreDevtools, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: DevtoolsDispatcher\n    }, {\n      type: i2.ActionsSubject\n    }, {\n      type: i2.ReducerObservable\n    }, {\n      type: DevtoolsExtension\n    }, {\n      type: i2.ScannedActionsSubject\n    }, {\n      type: i0.ErrorHandler\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [INITIAL_STATE]\n      }]\n    }, {\n      type: StoreDevtoolsConfig,\n      decorators: [{\n        type: Inject,\n        args: [STORE_DEVTOOLS_CONFIG]\n      }]\n    }];\n  }, null);\n})();\n\nconst IS_EXTENSION_OR_MONITOR_PRESENT = new InjectionToken('@ngrx/store-devtools Is Devtools Extension or Monitor Present');\n\nfunction createIsExtensionOrMonitorPresent(extension, config) {\n  return Boolean(extension) || config.monitor !== noMonitor;\n}\n\nfunction createReduxDevtoolsExtension() {\n  const extensionKey = '__REDUX_DEVTOOLS_EXTENSION__';\n\n  if (typeof window === 'object' && typeof window[extensionKey] !== 'undefined') {\n    return window[extensionKey];\n  } else {\n    return null;\n  }\n}\n/**\n * Provides developer tools and instrumentation for `Store`.\n *\n * @usageNotes\n *\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [\n *     provideStoreDevtools({\n *       maxAge: 25,\n *       logOnly: environment.production,\n *     }),\n *   ],\n * });\n * ```\n */\n\n\nfunction provideStoreDevtools(options = {}) {\n  return {\n    ɵproviders: [DevtoolsExtension, DevtoolsDispatcher, StoreDevtools, {\n      provide: INITIAL_OPTIONS,\n      useValue: options\n    }, {\n      provide: IS_EXTENSION_OR_MONITOR_PRESENT,\n      deps: [REDUX_DEVTOOLS_EXTENSION, STORE_DEVTOOLS_CONFIG],\n      useFactory: createIsExtensionOrMonitorPresent\n    }, {\n      provide: REDUX_DEVTOOLS_EXTENSION,\n      useFactory: createReduxDevtoolsExtension\n    }, {\n      provide: STORE_DEVTOOLS_CONFIG,\n      deps: [INITIAL_OPTIONS],\n      useFactory: createConfig\n    }, {\n      provide: StateObservable,\n      deps: [StoreDevtools],\n      useFactory: createStateObservable\n    }, {\n      provide: ReducerManagerDispatcher,\n      useExisting: DevtoolsDispatcher\n    }]\n  };\n}\n\nfunction createStateObservable(devtools) {\n  return devtools.state;\n}\n\nclass StoreDevtoolsModule {\n  static instrument(options = {}) {\n    return {\n      ngModule: StoreDevtoolsModule,\n      providers: [...provideStoreDevtools(options).ɵproviders]\n    };\n  }\n\n}\n/** @nocollapse */\n\n\nStoreDevtoolsModule.ɵfac = function StoreDevtoolsModule_Factory(t) {\n  return new (t || StoreDevtoolsModule)();\n};\n/** @nocollapse */\n\n\nStoreDevtoolsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: StoreDevtoolsModule\n});\n/** @nocollapse */\n\nStoreDevtoolsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StoreDevtoolsModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\n/**\n * DO NOT EDIT\n *\n * This file is automatically generated at build\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { INITIAL_OPTIONS, RECOMPUTE, REDUX_DEVTOOLS_EXTENSION, StoreDevtools, StoreDevtoolsConfig, StoreDevtoolsModule, provideStoreDevtools };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Injectable", "Inject", "NgModule", "i2", "ActionsSubject", "UPDATE", "INIT", "INITIAL_STATE", "StateObservable", "ReducerMana<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EMPTY", "Observable", "of", "merge", "queueScheduler", "ReplaySubject", "share", "filter", "map", "concatMap", "timeout", "debounceTime", "catchError", "take", "takeUntil", "switchMap", "skip", "observeOn", "withLatestFrom", "scan", "PERFORM_ACTION", "REFRESH", "RESET", "ROLLBACK", "COMMIT", "SWEEP", "TOGGLE_ACTION", "SET_ACTIONS_ACTIVE", "JUMP_TO_STATE", "JUMP_TO_ACTION", "IMPORT_STATE", "LOCK_CHANGES", "PAUSE_RECORDING", "PerformAction", "constructor", "action", "timestamp", "type", "Error", "Refresh", "Reset", "Rollback", "Commit", "Sweep", "ToggleAction", "id", "SetActionsActive", "start", "end", "active", "JumpToState", "index", "JumpToAction", "actionId", "ImportState", "nextLiftedState", "LockChanges", "status", "PauseRecording", "StoreDevtoolsConfig", "maxAge", "STORE_DEVTOOLS_CONFIG", "INITIAL_OPTIONS", "noMonitor", "DEFAULT_NAME", "createConfig", "optionsInput", "DEFAULT_OPTIONS", "monitor", "actionSanitizer", "undefined", "stateSanitizer", "name", "serialize", "logOnly", "autoPause", "features", "pause", "lock", "persist", "export", "import", "jump", "reorder", "dispatch", "test", "options", "config", "Object", "assign", "difference", "first", "second", "item", "indexOf", "unliftState", "liftedState", "computedStates", "currentStateIndex", "length", "state", "unliftAction", "actionsById", "nextActionId", "liftAction", "Date", "now", "sanitizeActions", "actions", "keys", "reduce", "sanitizedActions", "actionIdx", "idx", "Number", "sanitizeAction", "sanitizeStates", "states", "computedState", "sanitizeState", "error", "stateIdx", "shouldFilterActions", "predicate", "actions<PERSON><PERSON><PERSON>st", "actionsBlocklist", "filterLiftedState", "safelist", "blocklist", "filteredStagedActionIds", "filteredActionsById", "filteredComputedStates", "stagedActionIds", "for<PERSON>ach", "liftedAction", "isActionFiltered", "push", "blockedlist", "predicateMatch", "safelistMatch", "match", "s", "escapeRegExp", "join", "blocklistMatch", "replace", "Dev<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵfac", "ɵprov", "ExtensionActionTypes", "START", "DISPATCH", "STOP", "ACTION", "REDUX_DEVTOOLS_EXTENSION", "DevtoolsExtension", "devtoolsExtension", "dispatcher", "createActionStreams", "notify", "isLocked", "isPaused", "currentState", "sanitizedState", "sanitizedAction", "sendToReduxDevtools", "extensionConnection", "send", "sanitizedLiftedState", "getExtensionConfig", "createChangesObservable", "subscriber", "connection", "connect", "init", "subscribe", "change", "next", "unsubscribe", "changes$", "pipe", "start$", "stop$", "liftedActions$", "unwrapAction", "payload", "actions$", "actionsUntilStop$", "liftedUntilStop$", "eval", "extensionOptions", "err", "console", "warn", "decorators", "args", "INIT_ACTION", "RECOMPUTE", "RECOMPUTE_ACTION", "computeNextEntry", "reducer", "<PERSON><PERSON><PERSON><PERSON>", "nextState", "nextError", "toString", "handleError", "recomputeStates", "minInvalidatedStateIndex", "committedState", "skippedActionIds", "nextComputedStates", "slice", "lastIncludedActionId", "i", "previousEntry", "previousState", "previousError", "shouldSkip", "entry", "liftInitialState", "initialCommittedState", "monitorReducer", "monitorState", "liftReducerWith", "initialLiftedState", "create", "commitExcessActions", "n", "excess", "idsToDelete", "commitChanges", "Infinity", "concat", "actionIds", "Math", "min", "lastState", "stateHasErrors", "cmp", "StoreDevtools", "reducers$", "extension", "scannedActions", "initialState", "liftedInitialState", "liftReducer", "liftedAction$", "asObservable", "liftedReducer$", "liftedStateSubject", "liftedStateSubscription", "reducedLiftedState", "unliftedAction", "extensionStartSubscription", "refresh", "liftedState$", "state$", "stateSubscription", "complete", "performAction", "reset", "rollback", "commit", "sweep", "toggleAction", "jumpToAction", "jumpToState", "importState", "lockChanges", "pauseRecording", "ReducerObservable", "ScannedActionsSubject", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IS_EXTENSION_OR_MONITOR_PRESENT", "createIsExtensionOrMonitorPresent", "Boolean", "createReduxDevtoolsExtension", "extensionKey", "window", "provideStoreDevtools", "ɵproviders", "provide", "useValue", "deps", "useFactory", "createStateObservable", "useExisting", "devtools", "StoreDevtoolsModule", "instrument", "ngModule", "providers", "ɵmod", "ɵinj"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@ngrx/store-devtools/fesm2020/ngrx-store-devtools.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, Inject, NgModule } from '@angular/core';\nimport * as i2 from '@ngrx/store';\nimport { ActionsSubject, UPDATE, INIT, INITIAL_STATE, StateObservable, ReducerManagerDispatcher } from '@ngrx/store';\nimport { EMPTY, Observable, of, merge, queueScheduler, ReplaySubject } from 'rxjs';\nimport { share, filter, map, concatMap, timeout, debounceTime, catchError, take, takeUntil, switchMap, skip, observeOn, withLatestFrom, scan } from 'rxjs/operators';\n\nconst PERFORM_ACTION = 'PERFORM_ACTION';\nconst REFRESH = 'REFRESH';\nconst RESET = 'RESET';\nconst ROLLBACK = 'ROLLBACK';\nconst COMMIT = 'COMMIT';\nconst SWEEP = 'SWEEP';\nconst TOGGLE_ACTION = 'TOGGLE_ACTION';\nconst SET_ACTIONS_ACTIVE = 'SET_ACTIONS_ACTIVE';\nconst JUMP_TO_STATE = 'JUMP_TO_STATE';\nconst JUMP_TO_ACTION = 'JUMP_TO_ACTION';\nconst IMPORT_STATE = 'IMPORT_STATE';\nconst LOCK_CHANGES = 'LOCK_CHANGES';\nconst PAUSE_RECORDING = 'PAUSE_RECORDING';\nclass PerformAction {\n    constructor(action, timestamp) {\n        this.action = action;\n        this.timestamp = timestamp;\n        this.type = PERFORM_ACTION;\n        if (typeof action.type === 'undefined') {\n            throw new Error('Actions may not have an undefined \"type\" property. ' +\n                'Have you misspelled a constant?');\n        }\n    }\n}\nclass Refresh {\n    constructor() {\n        this.type = REFRESH;\n    }\n}\nclass Reset {\n    constructor(timestamp) {\n        this.timestamp = timestamp;\n        this.type = RESET;\n    }\n}\nclass Rollback {\n    constructor(timestamp) {\n        this.timestamp = timestamp;\n        this.type = ROLLBACK;\n    }\n}\nclass Commit {\n    constructor(timestamp) {\n        this.timestamp = timestamp;\n        this.type = COMMIT;\n    }\n}\nclass Sweep {\n    constructor() {\n        this.type = SWEEP;\n    }\n}\nclass ToggleAction {\n    constructor(id) {\n        this.id = id;\n        this.type = TOGGLE_ACTION;\n    }\n}\nclass SetActionsActive {\n    constructor(start, end, active = true) {\n        this.start = start;\n        this.end = end;\n        this.active = active;\n        this.type = SET_ACTIONS_ACTIVE;\n    }\n}\nclass JumpToState {\n    constructor(index) {\n        this.index = index;\n        this.type = JUMP_TO_STATE;\n    }\n}\nclass JumpToAction {\n    constructor(actionId) {\n        this.actionId = actionId;\n        this.type = JUMP_TO_ACTION;\n    }\n}\nclass ImportState {\n    constructor(nextLiftedState) {\n        this.nextLiftedState = nextLiftedState;\n        this.type = IMPORT_STATE;\n    }\n}\nclass LockChanges {\n    constructor(status) {\n        this.status = status;\n        this.type = LOCK_CHANGES;\n    }\n}\nclass PauseRecording {\n    constructor(status) {\n        this.status = status;\n        this.type = PAUSE_RECORDING;\n    }\n}\n\n/**\n * Chrome extension documentation\n * @see https://github.com/reduxjs/redux-devtools/blob/main/extension/docs/API/Arguments.md\n * Firefox extension documentation\n * @see https://github.com/zalmoxisus/redux-devtools-extension/blob/master/docs/API/Arguments.md\n */\nclass StoreDevtoolsConfig {\n    constructor() {\n        /**\n         * Maximum allowed actions to be stored in the history tree (default: `false`)\n         */\n        this.maxAge = false;\n    }\n}\nconst STORE_DEVTOOLS_CONFIG = new InjectionToken('@ngrx/store-devtools Options');\n/**\n * Used to provide a `StoreDevtoolsConfig` for the store-devtools.\n */\nconst INITIAL_OPTIONS = new InjectionToken('@ngrx/store-devtools Initial Config');\nfunction noMonitor() {\n    return null;\n}\nconst DEFAULT_NAME = 'NgRx Store DevTools';\nfunction createConfig(optionsInput) {\n    const DEFAULT_OPTIONS = {\n        maxAge: false,\n        monitor: noMonitor,\n        actionSanitizer: undefined,\n        stateSanitizer: undefined,\n        name: DEFAULT_NAME,\n        serialize: false,\n        logOnly: false,\n        autoPause: false,\n        // Add all features explicitly. This prevent buggy behavior for\n        // options like \"lock\" which might otherwise not show up.\n        features: {\n            pause: true,\n            lock: true,\n            persist: true,\n            export: true,\n            import: 'custom',\n            jump: true,\n            skip: true,\n            reorder: true,\n            dispatch: true,\n            test: true, // Generate tests for the selected actions\n        },\n    };\n    const options = typeof optionsInput === 'function' ? optionsInput() : optionsInput;\n    const logOnly = options.logOnly\n        ? { pause: true, export: true, test: true }\n        : false;\n    const features = options.features || logOnly || DEFAULT_OPTIONS.features;\n    const config = Object.assign({}, DEFAULT_OPTIONS, { features }, options);\n    if (config.maxAge && config.maxAge < 2) {\n        throw new Error(`Devtools 'maxAge' cannot be less than 2, got ${config.maxAge}`);\n    }\n    return config;\n}\n\nfunction difference(first, second) {\n    return first.filter((item) => second.indexOf(item) < 0);\n}\n/**\n * Provides an app's view into the state of the lifted store.\n */\nfunction unliftState(liftedState) {\n    const { computedStates, currentStateIndex } = liftedState;\n    // At start up NgRx dispatches init actions,\n    // When these init actions are being filtered out by the predicate or safe/block list options\n    // we don't have a complete computed states yet.\n    // At this point it could happen that we're out of bounds, when this happens we fall back to the last known state\n    if (currentStateIndex >= computedStates.length) {\n        const { state } = computedStates[computedStates.length - 1];\n        return state;\n    }\n    const { state } = computedStates[currentStateIndex];\n    return state;\n}\nfunction unliftAction(liftedState) {\n    return liftedState.actionsById[liftedState.nextActionId - 1];\n}\n/**\n * Lifts an app's action into an action on the lifted store.\n */\nfunction liftAction(action) {\n    return new PerformAction(action, +Date.now());\n}\n/**\n * Sanitizes given actions with given function.\n */\nfunction sanitizeActions(actionSanitizer, actions) {\n    return Object.keys(actions).reduce((sanitizedActions, actionIdx) => {\n        const idx = Number(actionIdx);\n        sanitizedActions[idx] = sanitizeAction(actionSanitizer, actions[idx], idx);\n        return sanitizedActions;\n    }, {});\n}\n/**\n * Sanitizes given action with given function.\n */\nfunction sanitizeAction(actionSanitizer, action, actionIdx) {\n    return {\n        ...action,\n        action: actionSanitizer(action.action, actionIdx),\n    };\n}\n/**\n * Sanitizes given states with given function.\n */\nfunction sanitizeStates(stateSanitizer, states) {\n    return states.map((computedState, idx) => ({\n        state: sanitizeState(stateSanitizer, computedState.state, idx),\n        error: computedState.error,\n    }));\n}\n/**\n * Sanitizes given state with given function.\n */\nfunction sanitizeState(stateSanitizer, state, stateIdx) {\n    return stateSanitizer(state, stateIdx);\n}\n/**\n * Read the config and tell if actions should be filtered\n */\nfunction shouldFilterActions(config) {\n    return config.predicate || config.actionsSafelist || config.actionsBlocklist;\n}\n/**\n * Return a full filtered lifted state\n */\nfunction filterLiftedState(liftedState, predicate, safelist, blocklist) {\n    const filteredStagedActionIds = [];\n    const filteredActionsById = {};\n    const filteredComputedStates = [];\n    liftedState.stagedActionIds.forEach((id, idx) => {\n        const liftedAction = liftedState.actionsById[id];\n        if (!liftedAction)\n            return;\n        if (idx &&\n            isActionFiltered(liftedState.computedStates[idx], liftedAction, predicate, safelist, blocklist)) {\n            return;\n        }\n        filteredActionsById[id] = liftedAction;\n        filteredStagedActionIds.push(id);\n        filteredComputedStates.push(liftedState.computedStates[idx]);\n    });\n    return {\n        ...liftedState,\n        stagedActionIds: filteredStagedActionIds,\n        actionsById: filteredActionsById,\n        computedStates: filteredComputedStates,\n    };\n}\n/**\n * Return true is the action should be ignored\n */\nfunction isActionFiltered(state, action, predicate, safelist, blockedlist) {\n    const predicateMatch = predicate && !predicate(state, action.action);\n    const safelistMatch = safelist &&\n        !action.action.type.match(safelist.map((s) => escapeRegExp(s)).join('|'));\n    const blocklistMatch = blockedlist &&\n        action.action.type.match(blockedlist.map((s) => escapeRegExp(s)).join('|'));\n    return predicateMatch || safelistMatch || blocklistMatch;\n}\n/**\n * Return string with escaped RegExp special characters\n * https://stackoverflow.com/a/6969486/1337347\n */\nfunction escapeRegExp(s) {\n    return s.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n\nclass DevtoolsDispatcher extends ActionsSubject {\n}\n/** @nocollapse */ DevtoolsDispatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: DevtoolsDispatcher, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ DevtoolsDispatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: DevtoolsDispatcher });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: DevtoolsDispatcher, decorators: [{\n            type: Injectable\n        }] });\n\nconst ExtensionActionTypes = {\n    START: 'START',\n    DISPATCH: 'DISPATCH',\n    STOP: 'STOP',\n    ACTION: 'ACTION',\n};\nconst REDUX_DEVTOOLS_EXTENSION = new InjectionToken('@ngrx/store-devtools Redux Devtools Extension');\nclass DevtoolsExtension {\n    constructor(devtoolsExtension, config, dispatcher) {\n        this.config = config;\n        this.dispatcher = dispatcher;\n        this.devtoolsExtension = devtoolsExtension;\n        this.createActionStreams();\n    }\n    notify(action, state) {\n        if (!this.devtoolsExtension) {\n            return;\n        }\n        // Check to see if the action requires a full update of the liftedState.\n        // If it is a simple action generated by the user's app and the recording\n        // is not locked/paused, only send the action and the current state (fast).\n        //\n        // A full liftedState update (slow: serializes the entire liftedState) is\n        // only required when:\n        //   a) redux-devtools-extension fires the @@Init action (ignored by\n        //      @ngrx/store-devtools)\n        //   b) an action is generated by an @ngrx module (e.g. @ngrx/effects/init\n        //      or @ngrx/store/update-reducers)\n        //   c) the state has been recomputed due to time-traveling\n        //   d) any action that is not a PerformAction to err on the side of\n        //      caution.\n        if (action.type === PERFORM_ACTION) {\n            if (state.isLocked || state.isPaused) {\n                return;\n            }\n            const currentState = unliftState(state);\n            if (shouldFilterActions(this.config) &&\n                isActionFiltered(currentState, action, this.config.predicate, this.config.actionsSafelist, this.config.actionsBlocklist)) {\n                return;\n            }\n            const sanitizedState = this.config.stateSanitizer\n                ? sanitizeState(this.config.stateSanitizer, currentState, state.currentStateIndex)\n                : currentState;\n            const sanitizedAction = this.config.actionSanitizer\n                ? sanitizeAction(this.config.actionSanitizer, action, state.nextActionId)\n                : action;\n            this.sendToReduxDevtools(() => this.extensionConnection.send(sanitizedAction, sanitizedState));\n        }\n        else {\n            // Requires full state update\n            const sanitizedLiftedState = {\n                ...state,\n                stagedActionIds: state.stagedActionIds,\n                actionsById: this.config.actionSanitizer\n                    ? sanitizeActions(this.config.actionSanitizer, state.actionsById)\n                    : state.actionsById,\n                computedStates: this.config.stateSanitizer\n                    ? sanitizeStates(this.config.stateSanitizer, state.computedStates)\n                    : state.computedStates,\n            };\n            this.sendToReduxDevtools(() => this.devtoolsExtension.send(null, sanitizedLiftedState, this.getExtensionConfig(this.config)));\n        }\n    }\n    createChangesObservable() {\n        if (!this.devtoolsExtension) {\n            return EMPTY;\n        }\n        return new Observable((subscriber) => {\n            const connection = this.devtoolsExtension.connect(this.getExtensionConfig(this.config));\n            this.extensionConnection = connection;\n            connection.init();\n            connection.subscribe((change) => subscriber.next(change));\n            return connection.unsubscribe;\n        });\n    }\n    createActionStreams() {\n        // Listens to all changes\n        const changes$ = this.createChangesObservable().pipe(share());\n        // Listen for the start action\n        const start$ = changes$.pipe(filter((change) => change.type === ExtensionActionTypes.START));\n        // Listen for the stop action\n        const stop$ = changes$.pipe(filter((change) => change.type === ExtensionActionTypes.STOP));\n        // Listen for lifted actions\n        const liftedActions$ = changes$.pipe(filter((change) => change.type === ExtensionActionTypes.DISPATCH), map((change) => this.unwrapAction(change.payload)), concatMap((action) => {\n            if (action.type === IMPORT_STATE) {\n                // State imports may happen in two situations:\n                // 1. Explicitly by user\n                // 2. User activated the \"persist state accross reloads\" option\n                //    and now the state is imported during reload.\n                // Because of option 2, we need to give possible\n                // lazy loaded reducers time to instantiate.\n                // As soon as there is no UPDATE action within 1 second,\n                // it is assumed that all reducers are loaded.\n                return this.dispatcher.pipe(filter((action) => action.type === UPDATE), timeout(1000), debounceTime(1000), map(() => action), catchError(() => of(action)), take(1));\n            }\n            else {\n                return of(action);\n            }\n        }));\n        // Listen for unlifted actions\n        const actions$ = changes$.pipe(filter((change) => change.type === ExtensionActionTypes.ACTION), map((change) => this.unwrapAction(change.payload)));\n        const actionsUntilStop$ = actions$.pipe(takeUntil(stop$));\n        const liftedUntilStop$ = liftedActions$.pipe(takeUntil(stop$));\n        this.start$ = start$.pipe(takeUntil(stop$));\n        // Only take the action sources between the start/stop events\n        this.actions$ = this.start$.pipe(switchMap(() => actionsUntilStop$));\n        this.liftedActions$ = this.start$.pipe(switchMap(() => liftedUntilStop$));\n    }\n    unwrapAction(action) {\n        return typeof action === 'string' ? eval(`(${action})`) : action;\n    }\n    getExtensionConfig(config) {\n        const extensionOptions = {\n            name: config.name,\n            features: config.features,\n            serialize: config.serialize,\n            autoPause: config.autoPause ?? false,\n            // The action/state sanitizers are not added to the config\n            // because sanitation is done in this class already.\n            // It is done before sending it to the devtools extension for consistency:\n            // - If we call extensionConnection.send(...),\n            //   the extension would call the sanitizers.\n            // - If we call devtoolsExtension.send(...) (aka full state update),\n            //   the extension would NOT call the sanitizers, so we have to do it ourselves.\n        };\n        if (config.maxAge !== false /* support === 0 */) {\n            extensionOptions.maxAge = config.maxAge;\n        }\n        return extensionOptions;\n    }\n    sendToReduxDevtools(send) {\n        try {\n            send();\n        }\n        catch (err) {\n            console.warn('@ngrx/store-devtools: something went wrong inside the redux devtools', err);\n        }\n    }\n}\n/** @nocollapse */ DevtoolsExtension.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: DevtoolsExtension, deps: [{ token: REDUX_DEVTOOLS_EXTENSION }, { token: STORE_DEVTOOLS_CONFIG }, { token: DevtoolsDispatcher }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ DevtoolsExtension.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: DevtoolsExtension });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: DevtoolsExtension, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [REDUX_DEVTOOLS_EXTENSION]\n                }] }, { type: StoreDevtoolsConfig, decorators: [{\n                    type: Inject,\n                    args: [STORE_DEVTOOLS_CONFIG]\n                }] }, { type: DevtoolsDispatcher }]; } });\n\nconst INIT_ACTION = { type: INIT };\nconst RECOMPUTE = '@ngrx/store-devtools/recompute';\nconst RECOMPUTE_ACTION = { type: RECOMPUTE };\n/**\n * Computes the next entry in the log by applying an action.\n */\nfunction computeNextEntry(reducer, action, state, error, errorHandler) {\n    if (error) {\n        return {\n            state,\n            error: 'Interrupted by an error up the chain',\n        };\n    }\n    let nextState = state;\n    let nextError;\n    try {\n        nextState = reducer(state, action);\n    }\n    catch (err) {\n        nextError = err.toString();\n        errorHandler.handleError(err);\n    }\n    return {\n        state: nextState,\n        error: nextError,\n    };\n}\n/**\n * Runs the reducer on invalidated actions to get a fresh computation log.\n */\nfunction recomputeStates(computedStates, minInvalidatedStateIndex, reducer, committedState, actionsById, stagedActionIds, skippedActionIds, errorHandler, isPaused) {\n    // Optimization: exit early and return the same reference\n    // if we know nothing could have changed.\n    if (minInvalidatedStateIndex >= computedStates.length &&\n        computedStates.length === stagedActionIds.length) {\n        return computedStates;\n    }\n    const nextComputedStates = computedStates.slice(0, minInvalidatedStateIndex);\n    // If the recording is paused, recompute all states up until the pause state,\n    // else recompute all states.\n    const lastIncludedActionId = stagedActionIds.length - (isPaused ? 1 : 0);\n    for (let i = minInvalidatedStateIndex; i < lastIncludedActionId; i++) {\n        const actionId = stagedActionIds[i];\n        const action = actionsById[actionId].action;\n        const previousEntry = nextComputedStates[i - 1];\n        const previousState = previousEntry ? previousEntry.state : committedState;\n        const previousError = previousEntry ? previousEntry.error : undefined;\n        const shouldSkip = skippedActionIds.indexOf(actionId) > -1;\n        const entry = shouldSkip\n            ? previousEntry\n            : computeNextEntry(reducer, action, previousState, previousError, errorHandler);\n        nextComputedStates.push(entry);\n    }\n    // If the recording is paused, the last state will not be recomputed,\n    // because it's essentially not part of the state history.\n    if (isPaused) {\n        nextComputedStates.push(computedStates[computedStates.length - 1]);\n    }\n    return nextComputedStates;\n}\nfunction liftInitialState(initialCommittedState, monitorReducer) {\n    return {\n        monitorState: monitorReducer(undefined, {}),\n        nextActionId: 1,\n        actionsById: { 0: liftAction(INIT_ACTION) },\n        stagedActionIds: [0],\n        skippedActionIds: [],\n        committedState: initialCommittedState,\n        currentStateIndex: 0,\n        computedStates: [],\n        isLocked: false,\n        isPaused: false,\n    };\n}\n/**\n * Creates a history state reducer from an app's reducer.\n */\nfunction liftReducerWith(initialCommittedState, initialLiftedState, errorHandler, monitorReducer, options = {}) {\n    /**\n     * Manages how the history actions modify the history state.\n     */\n    return (reducer) => (liftedState, liftedAction) => {\n        let { monitorState, actionsById, nextActionId, stagedActionIds, skippedActionIds, committedState, currentStateIndex, computedStates, isLocked, isPaused, } = liftedState || initialLiftedState;\n        if (!liftedState) {\n            // Prevent mutating initialLiftedState\n            actionsById = Object.create(actionsById);\n        }\n        function commitExcessActions(n) {\n            // Auto-commits n-number of excess actions.\n            let excess = n;\n            let idsToDelete = stagedActionIds.slice(1, excess + 1);\n            for (let i = 0; i < idsToDelete.length; i++) {\n                if (computedStates[i + 1].error) {\n                    // Stop if error is found. Commit actions up to error.\n                    excess = i;\n                    idsToDelete = stagedActionIds.slice(1, excess + 1);\n                    break;\n                }\n                else {\n                    delete actionsById[idsToDelete[i]];\n                }\n            }\n            skippedActionIds = skippedActionIds.filter((id) => idsToDelete.indexOf(id) === -1);\n            stagedActionIds = [0, ...stagedActionIds.slice(excess + 1)];\n            committedState = computedStates[excess].state;\n            computedStates = computedStates.slice(excess);\n            currentStateIndex =\n                currentStateIndex > excess ? currentStateIndex - excess : 0;\n        }\n        function commitChanges() {\n            // Consider the last committed state the new starting point.\n            // Squash any staged actions into a single committed state.\n            actionsById = { 0: liftAction(INIT_ACTION) };\n            nextActionId = 1;\n            stagedActionIds = [0];\n            skippedActionIds = [];\n            committedState = computedStates[currentStateIndex].state;\n            currentStateIndex = 0;\n            computedStates = [];\n        }\n        // By default, aggressively recompute every state whatever happens.\n        // This has O(n) performance, so we'll override this to a sensible\n        // value whenever we feel like we don't have to recompute the states.\n        let minInvalidatedStateIndex = 0;\n        switch (liftedAction.type) {\n            case LOCK_CHANGES: {\n                isLocked = liftedAction.status;\n                minInvalidatedStateIndex = Infinity;\n                break;\n            }\n            case PAUSE_RECORDING: {\n                isPaused = liftedAction.status;\n                if (isPaused) {\n                    // Add a pause action to signal the devtools-user the recording is paused.\n                    // The corresponding state will be overwritten on each update to always contain\n                    // the latest state (see Actions.PERFORM_ACTION).\n                    stagedActionIds = [...stagedActionIds, nextActionId];\n                    actionsById[nextActionId] = new PerformAction({\n                        type: '@ngrx/devtools/pause',\n                    }, +Date.now());\n                    nextActionId++;\n                    minInvalidatedStateIndex = stagedActionIds.length - 1;\n                    computedStates = computedStates.concat(computedStates[computedStates.length - 1]);\n                    if (currentStateIndex === stagedActionIds.length - 2) {\n                        currentStateIndex++;\n                    }\n                    minInvalidatedStateIndex = Infinity;\n                }\n                else {\n                    commitChanges();\n                }\n                break;\n            }\n            case RESET: {\n                // Get back to the state the store was created with.\n                actionsById = { 0: liftAction(INIT_ACTION) };\n                nextActionId = 1;\n                stagedActionIds = [0];\n                skippedActionIds = [];\n                committedState = initialCommittedState;\n                currentStateIndex = 0;\n                computedStates = [];\n                break;\n            }\n            case COMMIT: {\n                commitChanges();\n                break;\n            }\n            case ROLLBACK: {\n                // Forget about any staged actions.\n                // Start again from the last committed state.\n                actionsById = { 0: liftAction(INIT_ACTION) };\n                nextActionId = 1;\n                stagedActionIds = [0];\n                skippedActionIds = [];\n                currentStateIndex = 0;\n                computedStates = [];\n                break;\n            }\n            case TOGGLE_ACTION: {\n                // Toggle whether an action with given ID is skipped.\n                // Being skipped means it is a no-op during the computation.\n                const { id: actionId } = liftedAction;\n                const index = skippedActionIds.indexOf(actionId);\n                if (index === -1) {\n                    skippedActionIds = [actionId, ...skippedActionIds];\n                }\n                else {\n                    skippedActionIds = skippedActionIds.filter((id) => id !== actionId);\n                }\n                // Optimization: we know history before this action hasn't changed\n                minInvalidatedStateIndex = stagedActionIds.indexOf(actionId);\n                break;\n            }\n            case SET_ACTIONS_ACTIVE: {\n                // Toggle whether an action with given ID is skipped.\n                // Being skipped means it is a no-op during the computation.\n                const { start, end, active } = liftedAction;\n                const actionIds = [];\n                for (let i = start; i < end; i++)\n                    actionIds.push(i);\n                if (active) {\n                    skippedActionIds = difference(skippedActionIds, actionIds);\n                }\n                else {\n                    skippedActionIds = [...skippedActionIds, ...actionIds];\n                }\n                // Optimization: we know history before this action hasn't changed\n                minInvalidatedStateIndex = stagedActionIds.indexOf(start);\n                break;\n            }\n            case JUMP_TO_STATE: {\n                // Without recomputing anything, move the pointer that tell us\n                // which state is considered the current one. Useful for sliders.\n                currentStateIndex = liftedAction.index;\n                // Optimization: we know the history has not changed.\n                minInvalidatedStateIndex = Infinity;\n                break;\n            }\n            case JUMP_TO_ACTION: {\n                // Jumps to a corresponding state to a specific action.\n                // Useful when filtering actions.\n                const index = stagedActionIds.indexOf(liftedAction.actionId);\n                if (index !== -1)\n                    currentStateIndex = index;\n                minInvalidatedStateIndex = Infinity;\n                break;\n            }\n            case SWEEP: {\n                // Forget any actions that are currently being skipped.\n                stagedActionIds = difference(stagedActionIds, skippedActionIds);\n                skippedActionIds = [];\n                currentStateIndex = Math.min(currentStateIndex, stagedActionIds.length - 1);\n                break;\n            }\n            case PERFORM_ACTION: {\n                // Ignore action and return state as is if recording is locked\n                if (isLocked) {\n                    return liftedState || initialLiftedState;\n                }\n                if (isPaused ||\n                    (liftedState &&\n                        isActionFiltered(liftedState.computedStates[currentStateIndex], liftedAction, options.predicate, options.actionsSafelist, options.actionsBlocklist))) {\n                    // If recording is paused or if the action should be ignored, overwrite the last state\n                    // (corresponds to the pause action) and keep everything else as is.\n                    // This way, the app gets the new current state while the devtools\n                    // do not record another action.\n                    const lastState = computedStates[computedStates.length - 1];\n                    computedStates = [\n                        ...computedStates.slice(0, -1),\n                        computeNextEntry(reducer, liftedAction.action, lastState.state, lastState.error, errorHandler),\n                    ];\n                    minInvalidatedStateIndex = Infinity;\n                    break;\n                }\n                // Auto-commit as new actions come in.\n                if (options.maxAge && stagedActionIds.length === options.maxAge) {\n                    commitExcessActions(1);\n                }\n                if (currentStateIndex === stagedActionIds.length - 1) {\n                    currentStateIndex++;\n                }\n                const actionId = nextActionId++;\n                // Mutation! This is the hottest path, and we optimize on purpose.\n                // It is safe because we set a new key in a cache dictionary.\n                actionsById[actionId] = liftedAction;\n                stagedActionIds = [...stagedActionIds, actionId];\n                // Optimization: we know that only the new action needs computing.\n                minInvalidatedStateIndex = stagedActionIds.length - 1;\n                break;\n            }\n            case IMPORT_STATE: {\n                // Completely replace everything.\n                ({\n                    monitorState,\n                    actionsById,\n                    nextActionId,\n                    stagedActionIds,\n                    skippedActionIds,\n                    committedState,\n                    currentStateIndex,\n                    computedStates,\n                    isLocked,\n                    isPaused,\n                } = liftedAction.nextLiftedState);\n                break;\n            }\n            case INIT: {\n                // Always recompute states on hot reload and init.\n                minInvalidatedStateIndex = 0;\n                if (options.maxAge && stagedActionIds.length > options.maxAge) {\n                    // States must be recomputed before committing excess.\n                    computedStates = recomputeStates(computedStates, minInvalidatedStateIndex, reducer, committedState, actionsById, stagedActionIds, skippedActionIds, errorHandler, isPaused);\n                    commitExcessActions(stagedActionIds.length - options.maxAge);\n                    // Avoid double computation.\n                    minInvalidatedStateIndex = Infinity;\n                }\n                break;\n            }\n            case UPDATE: {\n                const stateHasErrors = computedStates.filter((state) => state.error).length > 0;\n                if (stateHasErrors) {\n                    // Recompute all states\n                    minInvalidatedStateIndex = 0;\n                    if (options.maxAge && stagedActionIds.length > options.maxAge) {\n                        // States must be recomputed before committing excess.\n                        computedStates = recomputeStates(computedStates, minInvalidatedStateIndex, reducer, committedState, actionsById, stagedActionIds, skippedActionIds, errorHandler, isPaused);\n                        commitExcessActions(stagedActionIds.length - options.maxAge);\n                        // Avoid double computation.\n                        minInvalidatedStateIndex = Infinity;\n                    }\n                }\n                else {\n                    // If not paused/locked, add a new action to signal devtools-user\n                    // that there was a reducer update.\n                    if (!isPaused && !isLocked) {\n                        if (currentStateIndex === stagedActionIds.length - 1) {\n                            currentStateIndex++;\n                        }\n                        // Add a new action to only recompute state\n                        const actionId = nextActionId++;\n                        actionsById[actionId] = new PerformAction(liftedAction, +Date.now());\n                        stagedActionIds = [...stagedActionIds, actionId];\n                        minInvalidatedStateIndex = stagedActionIds.length - 1;\n                        computedStates = recomputeStates(computedStates, minInvalidatedStateIndex, reducer, committedState, actionsById, stagedActionIds, skippedActionIds, errorHandler, isPaused);\n                    }\n                    // Recompute state history with latest reducer and update action\n                    computedStates = computedStates.map((cmp) => ({\n                        ...cmp,\n                        state: reducer(cmp.state, RECOMPUTE_ACTION),\n                    }));\n                    currentStateIndex = stagedActionIds.length - 1;\n                    if (options.maxAge && stagedActionIds.length > options.maxAge) {\n                        commitExcessActions(stagedActionIds.length - options.maxAge);\n                    }\n                    // Avoid double computation.\n                    minInvalidatedStateIndex = Infinity;\n                }\n                break;\n            }\n            default: {\n                // If the action is not recognized, it's a monitor action.\n                // Optimization: a monitor action can't change history.\n                minInvalidatedStateIndex = Infinity;\n                break;\n            }\n        }\n        computedStates = recomputeStates(computedStates, minInvalidatedStateIndex, reducer, committedState, actionsById, stagedActionIds, skippedActionIds, errorHandler, isPaused);\n        monitorState = monitorReducer(monitorState, liftedAction);\n        return {\n            monitorState,\n            actionsById,\n            nextActionId,\n            stagedActionIds,\n            skippedActionIds,\n            committedState,\n            currentStateIndex,\n            computedStates,\n            isLocked,\n            isPaused,\n        };\n    };\n}\n\nclass StoreDevtools {\n    constructor(dispatcher, actions$, reducers$, extension, scannedActions, errorHandler, initialState, config) {\n        const liftedInitialState = liftInitialState(initialState, config.monitor);\n        const liftReducer = liftReducerWith(initialState, liftedInitialState, errorHandler, config.monitor, config);\n        const liftedAction$ = merge(merge(actions$.asObservable().pipe(skip(1)), extension.actions$).pipe(map(liftAction)), dispatcher, extension.liftedActions$).pipe(observeOn(queueScheduler));\n        const liftedReducer$ = reducers$.pipe(map(liftReducer));\n        const liftedStateSubject = new ReplaySubject(1);\n        const liftedStateSubscription = liftedAction$\n            .pipe(withLatestFrom(liftedReducer$), scan(({ state: liftedState }, [action, reducer]) => {\n            let reducedLiftedState = reducer(liftedState, action);\n            // On full state update\n            // If we have actions filters, we must filter completely our lifted state to be sync with the extension\n            if (action.type !== PERFORM_ACTION && shouldFilterActions(config)) {\n                reducedLiftedState = filterLiftedState(reducedLiftedState, config.predicate, config.actionsSafelist, config.actionsBlocklist);\n            }\n            // Extension should be sent the sanitized lifted state\n            extension.notify(action, reducedLiftedState);\n            return { state: reducedLiftedState, action };\n        }, { state: liftedInitialState, action: null }))\n            .subscribe(({ state, action }) => {\n            liftedStateSubject.next(state);\n            if (action.type === PERFORM_ACTION) {\n                const unliftedAction = action.action;\n                scannedActions.next(unliftedAction);\n            }\n        });\n        const extensionStartSubscription = extension.start$.subscribe(() => {\n            this.refresh();\n        });\n        const liftedState$ = liftedStateSubject.asObservable();\n        const state$ = liftedState$.pipe(map(unliftState));\n        this.extensionStartSubscription = extensionStartSubscription;\n        this.stateSubscription = liftedStateSubscription;\n        this.dispatcher = dispatcher;\n        this.liftedState = liftedState$;\n        this.state = state$;\n    }\n    dispatch(action) {\n        this.dispatcher.next(action);\n    }\n    next(action) {\n        this.dispatcher.next(action);\n    }\n    error(error) { }\n    complete() { }\n    performAction(action) {\n        this.dispatch(new PerformAction(action, +Date.now()));\n    }\n    refresh() {\n        this.dispatch(new Refresh());\n    }\n    reset() {\n        this.dispatch(new Reset(+Date.now()));\n    }\n    rollback() {\n        this.dispatch(new Rollback(+Date.now()));\n    }\n    commit() {\n        this.dispatch(new Commit(+Date.now()));\n    }\n    sweep() {\n        this.dispatch(new Sweep());\n    }\n    toggleAction(id) {\n        this.dispatch(new ToggleAction(id));\n    }\n    jumpToAction(actionId) {\n        this.dispatch(new JumpToAction(actionId));\n    }\n    jumpToState(index) {\n        this.dispatch(new JumpToState(index));\n    }\n    importState(nextLiftedState) {\n        this.dispatch(new ImportState(nextLiftedState));\n    }\n    lockChanges(status) {\n        this.dispatch(new LockChanges(status));\n    }\n    pauseRecording(status) {\n        this.dispatch(new PauseRecording(status));\n    }\n}\n/** @nocollapse */ StoreDevtools.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: StoreDevtools, deps: [{ token: DevtoolsDispatcher }, { token: i2.ActionsSubject }, { token: i2.ReducerObservable }, { token: DevtoolsExtension }, { token: i2.ScannedActionsSubject }, { token: i0.ErrorHandler }, { token: INITIAL_STATE }, { token: STORE_DEVTOOLS_CONFIG }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ StoreDevtools.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: StoreDevtools });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: StoreDevtools, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: DevtoolsDispatcher }, { type: i2.ActionsSubject }, { type: i2.ReducerObservable }, { type: DevtoolsExtension }, { type: i2.ScannedActionsSubject }, { type: i0.ErrorHandler }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [INITIAL_STATE]\n                }] }, { type: StoreDevtoolsConfig, decorators: [{\n                    type: Inject,\n                    args: [STORE_DEVTOOLS_CONFIG]\n                }] }]; } });\n\nconst IS_EXTENSION_OR_MONITOR_PRESENT = new InjectionToken('@ngrx/store-devtools Is Devtools Extension or Monitor Present');\nfunction createIsExtensionOrMonitorPresent(extension, config) {\n    return Boolean(extension) || config.monitor !== noMonitor;\n}\nfunction createReduxDevtoolsExtension() {\n    const extensionKey = '__REDUX_DEVTOOLS_EXTENSION__';\n    if (typeof window === 'object' &&\n        typeof window[extensionKey] !== 'undefined') {\n        return window[extensionKey];\n    }\n    else {\n        return null;\n    }\n}\n/**\n * Provides developer tools and instrumentation for `Store`.\n *\n * @usageNotes\n *\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [\n *     provideStoreDevtools({\n *       maxAge: 25,\n *       logOnly: environment.production,\n *     }),\n *   ],\n * });\n * ```\n */\nfunction provideStoreDevtools(options = {}) {\n    return {\n        ɵproviders: [\n            DevtoolsExtension,\n            DevtoolsDispatcher,\n            StoreDevtools,\n            {\n                provide: INITIAL_OPTIONS,\n                useValue: options,\n            },\n            {\n                provide: IS_EXTENSION_OR_MONITOR_PRESENT,\n                deps: [REDUX_DEVTOOLS_EXTENSION, STORE_DEVTOOLS_CONFIG],\n                useFactory: createIsExtensionOrMonitorPresent,\n            },\n            {\n                provide: REDUX_DEVTOOLS_EXTENSION,\n                useFactory: createReduxDevtoolsExtension,\n            },\n            {\n                provide: STORE_DEVTOOLS_CONFIG,\n                deps: [INITIAL_OPTIONS],\n                useFactory: createConfig,\n            },\n            {\n                provide: StateObservable,\n                deps: [StoreDevtools],\n                useFactory: createStateObservable,\n            },\n            {\n                provide: ReducerManagerDispatcher,\n                useExisting: DevtoolsDispatcher,\n            },\n        ],\n    };\n}\n\nfunction createStateObservable(devtools) {\n    return devtools.state;\n}\nclass StoreDevtoolsModule {\n    static instrument(options = {}) {\n        return {\n            ngModule: StoreDevtoolsModule,\n            providers: [...provideStoreDevtools(options).ɵproviders],\n        };\n    }\n}\n/** @nocollapse */ StoreDevtoolsModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: StoreDevtoolsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n/** @nocollapse */ StoreDevtoolsModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.2\", ngImport: i0, type: StoreDevtoolsModule });\n/** @nocollapse */ StoreDevtoolsModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: StoreDevtoolsModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: StoreDevtoolsModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }] });\n\n/**\n * DO NOT EDIT\n *\n * This file is automatically generated at build\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INITIAL_OPTIONS, RECOMPUTE, REDUX_DEVTOOLS_EXTENSION, StoreDevtools, StoreDevtoolsConfig, StoreDevtoolsModule, provideStoreDevtools };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,UAAzB,EAAqCC,MAArC,EAA6CC,QAA7C,QAA6D,eAA7D;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,SAASC,cAAT,EAAyBC,MAAzB,EAAiCC,IAAjC,EAAuCC,aAAvC,EAAsDC,eAAtD,EAAuEC,wBAAvE,QAAuG,aAAvG;AACA,SAASC,KAAT,EAAgBC,UAAhB,EAA4BC,EAA5B,EAAgCC,KAAhC,EAAuCC,cAAvC,EAAuDC,aAAvD,QAA4E,MAA5E;AACA,SAASC,KAAT,EAAgBC,MAAhB,EAAwBC,GAAxB,EAA6BC,SAA7B,EAAwCC,OAAxC,EAAiDC,YAAjD,EAA+DC,UAA/D,EAA2EC,IAA3E,EAAiFC,SAAjF,EAA4FC,SAA5F,EAAuGC,IAAvG,EAA6GC,SAA7G,EAAwHC,cAAxH,EAAwIC,IAAxI,QAAoJ,gBAApJ;AAEA,MAAMC,cAAc,GAAG,gBAAvB;AACA,MAAMC,OAAO,GAAG,SAAhB;AACA,MAAMC,KAAK,GAAG,OAAd;AACA,MAAMC,QAAQ,GAAG,UAAjB;AACA,MAAMC,MAAM,GAAG,QAAf;AACA,MAAMC,KAAK,GAAG,OAAd;AACA,MAAMC,aAAa,GAAG,eAAtB;AACA,MAAMC,kBAAkB,GAAG,oBAA3B;AACA,MAAMC,aAAa,GAAG,eAAtB;AACA,MAAMC,cAAc,GAAG,gBAAvB;AACA,MAAMC,YAAY,GAAG,cAArB;AACA,MAAMC,YAAY,GAAG,cAArB;AACA,MAAMC,eAAe,GAAG,iBAAxB;;AACA,MAAMC,aAAN,CAAoB;EAChBC,WAAW,CAACC,MAAD,EAASC,SAAT,EAAoB;IAC3B,KAAKD,MAAL,GAAcA,MAAd;IACA,KAAKC,SAAL,GAAiBA,SAAjB;IACA,KAAKC,IAAL,GAAYjB,cAAZ;;IACA,IAAI,OAAOe,MAAM,CAACE,IAAd,KAAuB,WAA3B,EAAwC;MACpC,MAAM,IAAIC,KAAJ,CAAU,wDACZ,iCADE,CAAN;IAEH;EACJ;;AATe;;AAWpB,MAAMC,OAAN,CAAc;EACVL,WAAW,GAAG;IACV,KAAKG,IAAL,GAAYhB,OAAZ;EACH;;AAHS;;AAKd,MAAMmB,KAAN,CAAY;EACRN,WAAW,CAACE,SAAD,EAAY;IACnB,KAAKA,SAAL,GAAiBA,SAAjB;IACA,KAAKC,IAAL,GAAYf,KAAZ;EACH;;AAJO;;AAMZ,MAAMmB,QAAN,CAAe;EACXP,WAAW,CAACE,SAAD,EAAY;IACnB,KAAKA,SAAL,GAAiBA,SAAjB;IACA,KAAKC,IAAL,GAAYd,QAAZ;EACH;;AAJU;;AAMf,MAAMmB,MAAN,CAAa;EACTR,WAAW,CAACE,SAAD,EAAY;IACnB,KAAKA,SAAL,GAAiBA,SAAjB;IACA,KAAKC,IAAL,GAAYb,MAAZ;EACH;;AAJQ;;AAMb,MAAMmB,KAAN,CAAY;EACRT,WAAW,GAAG;IACV,KAAKG,IAAL,GAAYZ,KAAZ;EACH;;AAHO;;AAKZ,MAAMmB,YAAN,CAAmB;EACfV,WAAW,CAACW,EAAD,EAAK;IACZ,KAAKA,EAAL,GAAUA,EAAV;IACA,KAAKR,IAAL,GAAYX,aAAZ;EACH;;AAJc;;AAMnB,MAAMoB,gBAAN,CAAuB;EACnBZ,WAAW,CAACa,KAAD,EAAQC,GAAR,EAAaC,MAAM,GAAG,IAAtB,EAA4B;IACnC,KAAKF,KAAL,GAAaA,KAAb;IACA,KAAKC,GAAL,GAAWA,GAAX;IACA,KAAKC,MAAL,GAAcA,MAAd;IACA,KAAKZ,IAAL,GAAYV,kBAAZ;EACH;;AANkB;;AAQvB,MAAMuB,WAAN,CAAkB;EACdhB,WAAW,CAACiB,KAAD,EAAQ;IACf,KAAKA,KAAL,GAAaA,KAAb;IACA,KAAKd,IAAL,GAAYT,aAAZ;EACH;;AAJa;;AAMlB,MAAMwB,YAAN,CAAmB;EACflB,WAAW,CAACmB,QAAD,EAAW;IAClB,KAAKA,QAAL,GAAgBA,QAAhB;IACA,KAAKhB,IAAL,GAAYR,cAAZ;EACH;;AAJc;;AAMnB,MAAMyB,WAAN,CAAkB;EACdpB,WAAW,CAACqB,eAAD,EAAkB;IACzB,KAAKA,eAAL,GAAuBA,eAAvB;IACA,KAAKlB,IAAL,GAAYP,YAAZ;EACH;;AAJa;;AAMlB,MAAM0B,WAAN,CAAkB;EACdtB,WAAW,CAACuB,MAAD,EAAS;IAChB,KAAKA,MAAL,GAAcA,MAAd;IACA,KAAKpB,IAAL,GAAYN,YAAZ;EACH;;AAJa;;AAMlB,MAAM2B,cAAN,CAAqB;EACjBxB,WAAW,CAACuB,MAAD,EAAS;IAChB,KAAKA,MAAL,GAAcA,MAAd;IACA,KAAKpB,IAAL,GAAYL,eAAZ;EACH;;AAJgB;AAOrB;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM2B,mBAAN,CAA0B;EACtBzB,WAAW,GAAG;IACV;AACR;AACA;IACQ,KAAK0B,MAAL,GAAc,KAAd;EACH;;AANqB;;AAQ1B,MAAMC,qBAAqB,GAAG,IAAIxE,cAAJ,CAAmB,8BAAnB,CAA9B;AACA;AACA;AACA;;AACA,MAAMyE,eAAe,GAAG,IAAIzE,cAAJ,CAAmB,qCAAnB,CAAxB;;AACA,SAAS0E,SAAT,GAAqB;EACjB,OAAO,IAAP;AACH;;AACD,MAAMC,YAAY,GAAG,qBAArB;;AACA,SAASC,YAAT,CAAsBC,YAAtB,EAAoC;EAChC,MAAMC,eAAe,GAAG;IACpBP,MAAM,EAAE,KADY;IAEpBQ,OAAO,EAAEL,SAFW;IAGpBM,eAAe,EAAEC,SAHG;IAIpBC,cAAc,EAAED,SAJI;IAKpBE,IAAI,EAAER,YALc;IAMpBS,SAAS,EAAE,KANS;IAOpBC,OAAO,EAAE,KAPW;IAQpBC,SAAS,EAAE,KARS;IASpB;IACA;IACAC,QAAQ,EAAE;MACNC,KAAK,EAAE,IADD;MAENC,IAAI,EAAE,IAFA;MAGNC,OAAO,EAAE,IAHH;MAINC,MAAM,EAAE,IAJF;MAKNC,MAAM,EAAE,QALF;MAMNC,IAAI,EAAE,IANA;MAONlE,IAAI,EAAE,IAPA;MAQNmE,OAAO,EAAE,IARH;MASNC,QAAQ,EAAE,IATJ;MAUNC,IAAI,EAAE,IAVA,CAUM;;IAVN;EAXU,CAAxB;EAwBA,MAAMC,OAAO,GAAG,OAAOpB,YAAP,KAAwB,UAAxB,GAAqCA,YAAY,EAAjD,GAAsDA,YAAtE;EACA,MAAMQ,OAAO,GAAGY,OAAO,CAACZ,OAAR,GACV;IAAEG,KAAK,EAAE,IAAT;IAAeG,MAAM,EAAE,IAAvB;IAA6BK,IAAI,EAAE;EAAnC,CADU,GAEV,KAFN;EAGA,MAAMT,QAAQ,GAAGU,OAAO,CAACV,QAAR,IAAoBF,OAApB,IAA+BP,eAAe,CAACS,QAAhE;EACA,MAAMW,MAAM,GAAGC,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBtB,eAAlB,EAAmC;IAAES;EAAF,CAAnC,EAAiDU,OAAjD,CAAf;;EACA,IAAIC,MAAM,CAAC3B,MAAP,IAAiB2B,MAAM,CAAC3B,MAAP,GAAgB,CAArC,EAAwC;IACpC,MAAM,IAAItB,KAAJ,CAAW,gDAA+CiD,MAAM,CAAC3B,MAAO,EAAxE,CAAN;EACH;;EACD,OAAO2B,MAAP;AACH;;AAED,SAASG,UAAT,CAAoBC,KAApB,EAA2BC,MAA3B,EAAmC;EAC/B,OAAOD,KAAK,CAACpF,MAAN,CAAcsF,IAAD,IAAUD,MAAM,CAACE,OAAP,CAAeD,IAAf,IAAuB,CAA9C,CAAP;AACH;AACD;AACA;AACA;;;AACA,SAASE,WAAT,CAAqBC,WAArB,EAAkC;EAC9B,MAAM;IAAEC,cAAF;IAAkBC;EAAlB,IAAwCF,WAA9C,CAD8B,CAE9B;EACA;EACA;EACA;;EACA,IAAIE,iBAAiB,IAAID,cAAc,CAACE,MAAxC,EAAgD;IAC5C,MAAM;MAAEC;IAAF,IAAYH,cAAc,CAACA,cAAc,CAACE,MAAf,GAAwB,CAAzB,CAAhC;IACA,OAAOC,KAAP;EACH;;EACD,MAAM;IAAEA;EAAF,IAAYH,cAAc,CAACC,iBAAD,CAAhC;EACA,OAAOE,KAAP;AACH;;AACD,SAASC,YAAT,CAAsBL,WAAtB,EAAmC;EAC/B,OAAOA,WAAW,CAACM,WAAZ,CAAwBN,WAAW,CAACO,YAAZ,GAA2B,CAAnD,CAAP;AACH;AACD;AACA;AACA;;;AACA,SAASC,UAAT,CAAoBrE,MAApB,EAA4B;EACxB,OAAO,IAAIF,aAAJ,CAAkBE,MAAlB,EAA0B,CAACsE,IAAI,CAACC,GAAL,EAA3B,CAAP;AACH;AACD;AACA;AACA;;;AACA,SAASC,eAAT,CAAyBtC,eAAzB,EAA0CuC,OAA1C,EAAmD;EAC/C,OAAOpB,MAAM,CAACqB,IAAP,CAAYD,OAAZ,EAAqBE,MAArB,CAA4B,CAACC,gBAAD,EAAmBC,SAAnB,KAAiC;IAChE,MAAMC,GAAG,GAAGC,MAAM,CAACF,SAAD,CAAlB;IACAD,gBAAgB,CAACE,GAAD,CAAhB,GAAwBE,cAAc,CAAC9C,eAAD,EAAkBuC,OAAO,CAACK,GAAD,CAAzB,EAAgCA,GAAhC,CAAtC;IACA,OAAOF,gBAAP;EACH,CAJM,EAIJ,EAJI,CAAP;AAKH;AACD;AACA;AACA;;;AACA,SAASI,cAAT,CAAwB9C,eAAxB,EAAyClC,MAAzC,EAAiD6E,SAAjD,EAA4D;EACxD,OAAO,EACH,GAAG7E,MADA;IAEHA,MAAM,EAAEkC,eAAe,CAAClC,MAAM,CAACA,MAAR,EAAgB6E,SAAhB;EAFpB,CAAP;AAIH;AACD;AACA;AACA;;;AACA,SAASI,cAAT,CAAwB7C,cAAxB,EAAwC8C,MAAxC,EAAgD;EAC5C,OAAOA,MAAM,CAAC7G,GAAP,CAAW,CAAC8G,aAAD,EAAgBL,GAAhB,MAAyB;IACvCb,KAAK,EAAEmB,aAAa,CAAChD,cAAD,EAAiB+C,aAAa,CAAClB,KAA/B,EAAsCa,GAAtC,CADmB;IAEvCO,KAAK,EAAEF,aAAa,CAACE;EAFkB,CAAzB,CAAX,CAAP;AAIH;AACD;AACA;AACA;;;AACA,SAASD,aAAT,CAAuBhD,cAAvB,EAAuC6B,KAAvC,EAA8CqB,QAA9C,EAAwD;EACpD,OAAOlD,cAAc,CAAC6B,KAAD,EAAQqB,QAAR,CAArB;AACH;AACD;AACA;AACA;;;AACA,SAASC,mBAAT,CAA6BnC,MAA7B,EAAqC;EACjC,OAAOA,MAAM,CAACoC,SAAP,IAAoBpC,MAAM,CAACqC,eAA3B,IAA8CrC,MAAM,CAACsC,gBAA5D;AACH;AACD;AACA;AACA;;;AACA,SAASC,iBAAT,CAA2B9B,WAA3B,EAAwC2B,SAAxC,EAAmDI,QAAnD,EAA6DC,SAA7D,EAAwE;EACpE,MAAMC,uBAAuB,GAAG,EAAhC;EACA,MAAMC,mBAAmB,GAAG,EAA5B;EACA,MAAMC,sBAAsB,GAAG,EAA/B;EACAnC,WAAW,CAACoC,eAAZ,CAA4BC,OAA5B,CAAoC,CAACxF,EAAD,EAAKoE,GAAL,KAAa;IAC7C,MAAMqB,YAAY,GAAGtC,WAAW,CAACM,WAAZ,CAAwBzD,EAAxB,CAArB;IACA,IAAI,CAACyF,YAAL,EACI;;IACJ,IAAIrB,GAAG,IACHsB,gBAAgB,CAACvC,WAAW,CAACC,cAAZ,CAA2BgB,GAA3B,CAAD,EAAkCqB,YAAlC,EAAgDX,SAAhD,EAA2DI,QAA3D,EAAqEC,SAArE,CADpB,EACqG;MACjG;IACH;;IACDE,mBAAmB,CAACrF,EAAD,CAAnB,GAA0ByF,YAA1B;IACAL,uBAAuB,CAACO,IAAxB,CAA6B3F,EAA7B;IACAsF,sBAAsB,CAACK,IAAvB,CAA4BxC,WAAW,CAACC,cAAZ,CAA2BgB,GAA3B,CAA5B;EACH,CAXD;EAYA,OAAO,EACH,GAAGjB,WADA;IAEHoC,eAAe,EAAEH,uBAFd;IAGH3B,WAAW,EAAE4B,mBAHV;IAIHjC,cAAc,EAAEkC;EAJb,CAAP;AAMH;AACD;AACA;AACA;;;AACA,SAASI,gBAAT,CAA0BnC,KAA1B,EAAiCjE,MAAjC,EAAyCwF,SAAzC,EAAoDI,QAApD,EAA8DU,WAA9D,EAA2E;EACvE,MAAMC,cAAc,GAAGf,SAAS,IAAI,CAACA,SAAS,CAACvB,KAAD,EAAQjE,MAAM,CAACA,MAAf,CAA9C;EACA,MAAMwG,aAAa,GAAGZ,QAAQ,IAC1B,CAAC5F,MAAM,CAACA,MAAP,CAAcE,IAAd,CAAmBuG,KAAnB,CAAyBb,QAAQ,CAACvH,GAAT,CAAcqI,CAAD,IAAOC,YAAY,CAACD,CAAD,CAAhC,EAAqCE,IAArC,CAA0C,GAA1C,CAAzB,CADL;EAEA,MAAMC,cAAc,GAAGP,WAAW,IAC9BtG,MAAM,CAACA,MAAP,CAAcE,IAAd,CAAmBuG,KAAnB,CAAyBH,WAAW,CAACjI,GAAZ,CAAiBqI,CAAD,IAAOC,YAAY,CAACD,CAAD,CAAnC,EAAwCE,IAAxC,CAA6C,GAA7C,CAAzB,CADJ;EAEA,OAAOL,cAAc,IAAIC,aAAlB,IAAmCK,cAA1C;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASF,YAAT,CAAsBD,CAAtB,EAAyB;EACrB,OAAOA,CAAC,CAACI,OAAF,CAAU,qBAAV,EAAiC,MAAjC,CAAP;AACH;;AAED,MAAMC,kBAAN,SAAiCxJ,cAAjC,CAAgD;AAEhD;;;AAAmBwJ,kBAAkB,CAACC,IAAnB;EAAA;EAAA;IAAA,8EAAqG/J,EAArG,uBAA+G8J,kBAA/G,SAA+GA,kBAA/G;EAAA;AAAA;AACnB;;;AAAmBA,kBAAkB,CAACE,KAAnB,kBADqGhK,EACrG;EAAA,OAAmH8J,kBAAnH;EAAA,SAAmHA,kBAAnH;AAAA;;AACnB;EAAA,mDAFwH9J,EAExH,mBAA2F8J,kBAA3F,EAA2H,CAAC;IAChH7G,IAAI,EAAE/C;EAD0G,CAAD,CAA3H;AAAA;;AAIA,MAAM+J,oBAAoB,GAAG;EACzBC,KAAK,EAAE,OADkB;EAEzBC,QAAQ,EAAE,UAFe;EAGzBC,IAAI,EAAE,MAHmB;EAIzBC,MAAM,EAAE;AAJiB,CAA7B;AAMA,MAAMC,wBAAwB,GAAG,IAAIrK,cAAJ,CAAmB,+CAAnB,CAAjC;;AACA,MAAMsK,iBAAN,CAAwB;EACpBzH,WAAW,CAAC0H,iBAAD,EAAoBrE,MAApB,EAA4BsE,UAA5B,EAAwC;IAC/C,KAAKtE,MAAL,GAAcA,MAAd;IACA,KAAKsE,UAAL,GAAkBA,UAAlB;IACA,KAAKD,iBAAL,GAAyBA,iBAAzB;IACA,KAAKE,mBAAL;EACH;;EACDC,MAAM,CAAC5H,MAAD,EAASiE,KAAT,EAAgB;IAClB,IAAI,CAAC,KAAKwD,iBAAV,EAA6B;MACzB;IACH,CAHiB,CAIlB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,IAAIzH,MAAM,CAACE,IAAP,KAAgBjB,cAApB,EAAoC;MAChC,IAAIgF,KAAK,CAAC4D,QAAN,IAAkB5D,KAAK,CAAC6D,QAA5B,EAAsC;QAClC;MACH;;MACD,MAAMC,YAAY,GAAGnE,WAAW,CAACK,KAAD,CAAhC;;MACA,IAAIsB,mBAAmB,CAAC,KAAKnC,MAAN,CAAnB,IACAgD,gBAAgB,CAAC2B,YAAD,EAAe/H,MAAf,EAAuB,KAAKoD,MAAL,CAAYoC,SAAnC,EAA8C,KAAKpC,MAAL,CAAYqC,eAA1D,EAA2E,KAAKrC,MAAL,CAAYsC,gBAAvF,CADpB,EAC8H;QAC1H;MACH;;MACD,MAAMsC,cAAc,GAAG,KAAK5E,MAAL,CAAYhB,cAAZ,GACjBgD,aAAa,CAAC,KAAKhC,MAAL,CAAYhB,cAAb,EAA6B2F,YAA7B,EAA2C9D,KAAK,CAACF,iBAAjD,CADI,GAEjBgE,YAFN;MAGA,MAAME,eAAe,GAAG,KAAK7E,MAAL,CAAYlB,eAAZ,GAClB8C,cAAc,CAAC,KAAK5B,MAAL,CAAYlB,eAAb,EAA8BlC,MAA9B,EAAsCiE,KAAK,CAACG,YAA5C,CADI,GAElBpE,MAFN;MAGA,KAAKkI,mBAAL,CAAyB,MAAM,KAAKC,mBAAL,CAAyBC,IAAzB,CAA8BH,eAA9B,EAA+CD,cAA/C,CAA/B;IACH,CAhBD,MAiBK;MACD;MACA,MAAMK,oBAAoB,GAAG,EACzB,GAAGpE,KADsB;QAEzBgC,eAAe,EAAEhC,KAAK,CAACgC,eAFE;QAGzB9B,WAAW,EAAE,KAAKf,MAAL,CAAYlB,eAAZ,GACPsC,eAAe,CAAC,KAAKpB,MAAL,CAAYlB,eAAb,EAA8B+B,KAAK,CAACE,WAApC,CADR,GAEPF,KAAK,CAACE,WALa;QAMzBL,cAAc,EAAE,KAAKV,MAAL,CAAYhB,cAAZ,GACV6C,cAAc,CAAC,KAAK7B,MAAL,CAAYhB,cAAb,EAA6B6B,KAAK,CAACH,cAAnC,CADJ,GAEVG,KAAK,CAACH;MARa,CAA7B;MAUA,KAAKoE,mBAAL,CAAyB,MAAM,KAAKT,iBAAL,CAAuBW,IAAvB,CAA4B,IAA5B,EAAkCC,oBAAlC,EAAwD,KAAKC,kBAAL,CAAwB,KAAKlF,MAA7B,CAAxD,CAA/B;IACH;EACJ;;EACDmF,uBAAuB,GAAG;IACtB,IAAI,CAAC,KAAKd,iBAAV,EAA6B;MACzB,OAAO5J,KAAP;IACH;;IACD,OAAO,IAAIC,UAAJ,CAAgB0K,UAAD,IAAgB;MAClC,MAAMC,UAAU,GAAG,KAAKhB,iBAAL,CAAuBiB,OAAvB,CAA+B,KAAKJ,kBAAL,CAAwB,KAAKlF,MAA7B,CAA/B,CAAnB;MACA,KAAK+E,mBAAL,GAA2BM,UAA3B;MACAA,UAAU,CAACE,IAAX;MACAF,UAAU,CAACG,SAAX,CAAsBC,MAAD,IAAYL,UAAU,CAACM,IAAX,CAAgBD,MAAhB,CAAjC;MACA,OAAOJ,UAAU,CAACM,WAAlB;IACH,CANM,CAAP;EAOH;;EACDpB,mBAAmB,GAAG;IAClB;IACA,MAAMqB,QAAQ,GAAG,KAAKT,uBAAL,GAA+BU,IAA/B,CAAoC9K,KAAK,EAAzC,CAAjB,CAFkB,CAGlB;;IACA,MAAM+K,MAAM,GAAGF,QAAQ,CAACC,IAAT,CAAc7K,MAAM,CAAEyK,MAAD,IAAYA,MAAM,CAAC3I,IAAP,KAAgBgH,oBAAoB,CAACC,KAAlD,CAApB,CAAf,CAJkB,CAKlB;;IACA,MAAMgC,KAAK,GAAGH,QAAQ,CAACC,IAAT,CAAc7K,MAAM,CAAEyK,MAAD,IAAYA,MAAM,CAAC3I,IAAP,KAAgBgH,oBAAoB,CAACG,IAAlD,CAApB,CAAd,CANkB,CAOlB;;IACA,MAAM+B,cAAc,GAAGJ,QAAQ,CAACC,IAAT,CAAc7K,MAAM,CAAEyK,MAAD,IAAYA,MAAM,CAAC3I,IAAP,KAAgBgH,oBAAoB,CAACE,QAAlD,CAApB,EAAiF/I,GAAG,CAAEwK,MAAD,IAAY,KAAKQ,YAAL,CAAkBR,MAAM,CAACS,OAAzB,CAAb,CAApF,EAAqIhL,SAAS,CAAE0B,MAAD,IAAY;MAC9K,IAAIA,MAAM,CAACE,IAAP,KAAgBP,YAApB,EAAkC;QAC9B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,OAAO,KAAK+H,UAAL,CAAgBuB,IAAhB,CAAqB7K,MAAM,CAAE4B,MAAD,IAAYA,MAAM,CAACE,IAAP,KAAgB1C,MAA7B,CAA3B,EAAiEe,OAAO,CAAC,IAAD,CAAxE,EAAgFC,YAAY,CAAC,IAAD,CAA5F,EAAoGH,GAAG,CAAC,MAAM2B,MAAP,CAAvG,EAAuHvB,UAAU,CAAC,MAAMV,EAAE,CAACiC,MAAD,CAAT,CAAjI,EAAqJtB,IAAI,CAAC,CAAD,CAAzJ,CAAP;MACH,CAVD,MAWK;QACD,OAAOX,EAAE,CAACiC,MAAD,CAAT;MACH;IACJ,CAfoK,CAA9I,CAAvB,CARkB,CAwBlB;;IACA,MAAMuJ,QAAQ,GAAGP,QAAQ,CAACC,IAAT,CAAc7K,MAAM,CAAEyK,MAAD,IAAYA,MAAM,CAAC3I,IAAP,KAAgBgH,oBAAoB,CAACI,MAAlD,CAApB,EAA+EjJ,GAAG,CAAEwK,MAAD,IAAY,KAAKQ,YAAL,CAAkBR,MAAM,CAACS,OAAzB,CAAb,CAAlF,CAAjB;IACA,MAAME,iBAAiB,GAAGD,QAAQ,CAACN,IAAT,CAActK,SAAS,CAACwK,KAAD,CAAvB,CAA1B;IACA,MAAMM,gBAAgB,GAAGL,cAAc,CAACH,IAAf,CAAoBtK,SAAS,CAACwK,KAAD,CAA7B,CAAzB;IACA,KAAKD,MAAL,GAAcA,MAAM,CAACD,IAAP,CAAYtK,SAAS,CAACwK,KAAD,CAArB,CAAd,CA5BkB,CA6BlB;;IACA,KAAKI,QAAL,GAAgB,KAAKL,MAAL,CAAYD,IAAZ,CAAiBrK,SAAS,CAAC,MAAM4K,iBAAP,CAA1B,CAAhB;IACA,KAAKJ,cAAL,GAAsB,KAAKF,MAAL,CAAYD,IAAZ,CAAiBrK,SAAS,CAAC,MAAM6K,gBAAP,CAA1B,CAAtB;EACH;;EACDJ,YAAY,CAACrJ,MAAD,EAAS;IACjB,OAAO,OAAOA,MAAP,KAAkB,QAAlB,GAA6B0J,IAAI,CAAE,IAAG1J,MAAO,GAAZ,CAAjC,GAAmDA,MAA1D;EACH;;EACDsI,kBAAkB,CAAClF,MAAD,EAAS;IACvB,MAAMuG,gBAAgB,GAAG;MACrBtH,IAAI,EAAEe,MAAM,CAACf,IADQ;MAErBI,QAAQ,EAAEW,MAAM,CAACX,QAFI;MAGrBH,SAAS,EAAEc,MAAM,CAACd,SAHG;MAIrBE,SAAS,EAAEY,MAAM,CAACZ,SAAP,IAAoB,KAJV,CAKrB;MACA;MACA;MACA;MACA;MACA;MACA;;IAXqB,CAAzB;;IAaA,IAAIY,MAAM,CAAC3B,MAAP,KAAkB;IAAM;IAA5B,EAAiD;MAC7CkI,gBAAgB,CAAClI,MAAjB,GAA0B2B,MAAM,CAAC3B,MAAjC;IACH;;IACD,OAAOkI,gBAAP;EACH;;EACDzB,mBAAmB,CAACE,IAAD,EAAO;IACtB,IAAI;MACAA,IAAI;IACP,CAFD,CAGA,OAAOwB,GAAP,EAAY;MACRC,OAAO,CAACC,IAAR,CAAa,sEAAb,EAAqFF,GAArF;IACH;EACJ;;AAlImB;AAoIxB;;;AAAmBpC,iBAAiB,CAACR,IAAlB;EAAA,iBAA8GQ,iBAA9G,EAjJqGvK,EAiJrG,UAAiJsK,wBAAjJ,GAjJqGtK,EAiJrG,UAAsLyE,qBAAtL,GAjJqGzE,EAiJrG,UAAwN8J,kBAAxN;AAAA;AACnB;;;AAAmBS,iBAAiB,CAACP,KAAlB,kBAlJqGhK,EAkJrG;EAAA,OAAkHuK,iBAAlH;EAAA,SAAkHA,iBAAlH;AAAA;;AACnB;EAAA,mDAnJwHvK,EAmJxH,mBAA2FuK,iBAA3F,EAA0H,CAAC;IAC/GtH,IAAI,EAAE/C;EADyG,CAAD,CAA1H,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAE+C,IAAI,EAAEiC,SAAR;MAAmB4H,UAAU,EAAE,CAAC;QAC9D7J,IAAI,EAAE9C,MADwD;QAE9D4M,IAAI,EAAE,CAACzC,wBAAD;MAFwD,CAAD;IAA/B,CAAD,EAG3B;MAAErH,IAAI,EAAEsB,mBAAR;MAA6BuI,UAAU,EAAE,CAAC;QAC5C7J,IAAI,EAAE9C,MADsC;QAE5C4M,IAAI,EAAE,CAACtI,qBAAD;MAFsC,CAAD;IAAzC,CAH2B,EAM3B;MAAExB,IAAI,EAAE6G;IAAR,CAN2B,CAAP;EAMY,CARtD;AAAA;;AAUA,MAAMkD,WAAW,GAAG;EAAE/J,IAAI,EAAEzC;AAAR,CAApB;AACA,MAAMyM,SAAS,GAAG,gCAAlB;AACA,MAAMC,gBAAgB,GAAG;EAAEjK,IAAI,EAAEgK;AAAR,CAAzB;AACA;AACA;AACA;;AACA,SAASE,gBAAT,CAA0BC,OAA1B,EAAmCrK,MAAnC,EAA2CiE,KAA3C,EAAkDoB,KAAlD,EAAyDiF,YAAzD,EAAuE;EACnE,IAAIjF,KAAJ,EAAW;IACP,OAAO;MACHpB,KADG;MAEHoB,KAAK,EAAE;IAFJ,CAAP;EAIH;;EACD,IAAIkF,SAAS,GAAGtG,KAAhB;EACA,IAAIuG,SAAJ;;EACA,IAAI;IACAD,SAAS,GAAGF,OAAO,CAACpG,KAAD,EAAQjE,MAAR,CAAnB;EACH,CAFD,CAGA,OAAO4J,GAAP,EAAY;IACRY,SAAS,GAAGZ,GAAG,CAACa,QAAJ,EAAZ;IACAH,YAAY,CAACI,WAAb,CAAyBd,GAAzB;EACH;;EACD,OAAO;IACH3F,KAAK,EAAEsG,SADJ;IAEHlF,KAAK,EAAEmF;EAFJ,CAAP;AAIH;AACD;AACA;AACA;;;AACA,SAASG,eAAT,CAAyB7G,cAAzB,EAAyC8G,wBAAzC,EAAmEP,OAAnE,EAA4EQ,cAA5E,EAA4F1G,WAA5F,EAAyG8B,eAAzG,EAA0H6E,gBAA1H,EAA4IR,YAA5I,EAA0JxC,QAA1J,EAAoK;EAChK;EACA;EACA,IAAI8C,wBAAwB,IAAI9G,cAAc,CAACE,MAA3C,IACAF,cAAc,CAACE,MAAf,KAA0BiC,eAAe,CAACjC,MAD9C,EACsD;IAClD,OAAOF,cAAP;EACH;;EACD,MAAMiH,kBAAkB,GAAGjH,cAAc,CAACkH,KAAf,CAAqB,CAArB,EAAwBJ,wBAAxB,CAA3B,CAPgK,CAQhK;EACA;;EACA,MAAMK,oBAAoB,GAAGhF,eAAe,CAACjC,MAAhB,IAA0B8D,QAAQ,GAAG,CAAH,GAAO,CAAzC,CAA7B;;EACA,KAAK,IAAIoD,CAAC,GAAGN,wBAAb,EAAuCM,CAAC,GAAGD,oBAA3C,EAAiEC,CAAC,EAAlE,EAAsE;IAClE,MAAMhK,QAAQ,GAAG+E,eAAe,CAACiF,CAAD,CAAhC;IACA,MAAMlL,MAAM,GAAGmE,WAAW,CAACjD,QAAD,CAAX,CAAsBlB,MAArC;IACA,MAAMmL,aAAa,GAAGJ,kBAAkB,CAACG,CAAC,GAAG,CAAL,CAAxC;IACA,MAAME,aAAa,GAAGD,aAAa,GAAGA,aAAa,CAAClH,KAAjB,GAAyB4G,cAA5D;IACA,MAAMQ,aAAa,GAAGF,aAAa,GAAGA,aAAa,CAAC9F,KAAjB,GAAyBlD,SAA5D;IACA,MAAMmJ,UAAU,GAAGR,gBAAgB,CAACnH,OAAjB,CAAyBzC,QAAzB,IAAqC,CAAC,CAAzD;IACA,MAAMqK,KAAK,GAAGD,UAAU,GAClBH,aADkB,GAElBf,gBAAgB,CAACC,OAAD,EAAUrK,MAAV,EAAkBoL,aAAlB,EAAiCC,aAAjC,EAAgDf,YAAhD,CAFtB;IAGAS,kBAAkB,CAAC1E,IAAnB,CAAwBkF,KAAxB;EACH,CAtB+J,CAuBhK;EACA;;;EACA,IAAIzD,QAAJ,EAAc;IACViD,kBAAkB,CAAC1E,IAAnB,CAAwBvC,cAAc,CAACA,cAAc,CAACE,MAAf,GAAwB,CAAzB,CAAtC;EACH;;EACD,OAAO+G,kBAAP;AACH;;AACD,SAASS,gBAAT,CAA0BC,qBAA1B,EAAiDC,cAAjD,EAAiE;EAC7D,OAAO;IACHC,YAAY,EAAED,cAAc,CAACvJ,SAAD,EAAY,EAAZ,CADzB;IAEHiC,YAAY,EAAE,CAFX;IAGHD,WAAW,EAAE;MAAE,GAAGE,UAAU,CAAC4F,WAAD;IAAf,CAHV;IAIHhE,eAAe,EAAE,CAAC,CAAD,CAJd;IAKH6E,gBAAgB,EAAE,EALf;IAMHD,cAAc,EAAEY,qBANb;IAOH1H,iBAAiB,EAAE,CAPhB;IAQHD,cAAc,EAAE,EARb;IASH+D,QAAQ,EAAE,KATP;IAUHC,QAAQ,EAAE;EAVP,CAAP;AAYH;AACD;AACA;AACA;;;AACA,SAAS8D,eAAT,CAAyBH,qBAAzB,EAAgDI,kBAAhD,EAAoEvB,YAApE,EAAkFoB,cAAlF,EAAkGvI,OAAO,GAAG,EAA5G,EAAgH;EAC5G;AACJ;AACA;EACI,OAAQkH,OAAD,IAAa,CAACxG,WAAD,EAAcsC,YAAd,KAA+B;IAC/C,IAAI;MAAEwF,YAAF;MAAgBxH,WAAhB;MAA6BC,YAA7B;MAA2C6B,eAA3C;MAA4D6E,gBAA5D;MAA8ED,cAA9E;MAA8F9G,iBAA9F;MAAiHD,cAAjH;MAAiI+D,QAAjI;MAA2IC;IAA3I,IAAyJjE,WAAW,IAAIgI,kBAA5K;;IACA,IAAI,CAAChI,WAAL,EAAkB;MACd;MACAM,WAAW,GAAGd,MAAM,CAACyI,MAAP,CAAc3H,WAAd,CAAd;IACH;;IACD,SAAS4H,mBAAT,CAA6BC,CAA7B,EAAgC;MAC5B;MACA,IAAIC,MAAM,GAAGD,CAAb;MACA,IAAIE,WAAW,GAAGjG,eAAe,CAAC+E,KAAhB,CAAsB,CAAtB,EAAyBiB,MAAM,GAAG,CAAlC,CAAlB;;MACA,KAAK,IAAIf,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgB,WAAW,CAAClI,MAAhC,EAAwCkH,CAAC,EAAzC,EAA6C;QACzC,IAAIpH,cAAc,CAACoH,CAAC,GAAG,CAAL,CAAd,CAAsB7F,KAA1B,EAAiC;UAC7B;UACA4G,MAAM,GAAGf,CAAT;UACAgB,WAAW,GAAGjG,eAAe,CAAC+E,KAAhB,CAAsB,CAAtB,EAAyBiB,MAAM,GAAG,CAAlC,CAAd;UACA;QACH,CALD,MAMK;UACD,OAAO9H,WAAW,CAAC+H,WAAW,CAAChB,CAAD,CAAZ,CAAlB;QACH;MACJ;;MACDJ,gBAAgB,GAAGA,gBAAgB,CAAC1M,MAAjB,CAAyBsC,EAAD,IAAQwL,WAAW,CAACvI,OAAZ,CAAoBjD,EAApB,MAA4B,CAAC,CAA7D,CAAnB;MACAuF,eAAe,GAAG,CAAC,CAAD,EAAI,GAAGA,eAAe,CAAC+E,KAAhB,CAAsBiB,MAAM,GAAG,CAA/B,CAAP,CAAlB;MACApB,cAAc,GAAG/G,cAAc,CAACmI,MAAD,CAAd,CAAuBhI,KAAxC;MACAH,cAAc,GAAGA,cAAc,CAACkH,KAAf,CAAqBiB,MAArB,CAAjB;MACAlI,iBAAiB,GACbA,iBAAiB,GAAGkI,MAApB,GAA6BlI,iBAAiB,GAAGkI,MAAjD,GAA0D,CAD9D;IAEH;;IACD,SAASE,aAAT,GAAyB;MACrB;MACA;MACAhI,WAAW,GAAG;QAAE,GAAGE,UAAU,CAAC4F,WAAD;MAAf,CAAd;MACA7F,YAAY,GAAG,CAAf;MACA6B,eAAe,GAAG,CAAC,CAAD,CAAlB;MACA6E,gBAAgB,GAAG,EAAnB;MACAD,cAAc,GAAG/G,cAAc,CAACC,iBAAD,CAAd,CAAkCE,KAAnD;MACAF,iBAAiB,GAAG,CAApB;MACAD,cAAc,GAAG,EAAjB;IACH,CAtC8C,CAuC/C;IACA;IACA;;;IACA,IAAI8G,wBAAwB,GAAG,CAA/B;;IACA,QAAQzE,YAAY,CAACjG,IAArB;MACI,KAAKN,YAAL;QAAmB;UACfiI,QAAQ,GAAG1B,YAAY,CAAC7E,MAAxB;UACAsJ,wBAAwB,GAAGwB,QAA3B;UACA;QACH;;MACD,KAAKvM,eAAL;QAAsB;UAClBiI,QAAQ,GAAG3B,YAAY,CAAC7E,MAAxB;;UACA,IAAIwG,QAAJ,EAAc;YACV;YACA;YACA;YACA7B,eAAe,GAAG,CAAC,GAAGA,eAAJ,EAAqB7B,YAArB,CAAlB;YACAD,WAAW,CAACC,YAAD,CAAX,GAA4B,IAAItE,aAAJ,CAAkB;cAC1CI,IAAI,EAAE;YADoC,CAAlB,EAEzB,CAACoE,IAAI,CAACC,GAAL,EAFwB,CAA5B;YAGAH,YAAY;YACZwG,wBAAwB,GAAG3E,eAAe,CAACjC,MAAhB,GAAyB,CAApD;YACAF,cAAc,GAAGA,cAAc,CAACuI,MAAf,CAAsBvI,cAAc,CAACA,cAAc,CAACE,MAAf,GAAwB,CAAzB,CAApC,CAAjB;;YACA,IAAID,iBAAiB,KAAKkC,eAAe,CAACjC,MAAhB,GAAyB,CAAnD,EAAsD;cAClDD,iBAAiB;YACpB;;YACD6G,wBAAwB,GAAGwB,QAA3B;UACH,CAfD,MAgBK;YACDD,aAAa;UAChB;;UACD;QACH;;MACD,KAAKhN,KAAL;QAAY;UACR;UACAgF,WAAW,GAAG;YAAE,GAAGE,UAAU,CAAC4F,WAAD;UAAf,CAAd;UACA7F,YAAY,GAAG,CAAf;UACA6B,eAAe,GAAG,CAAC,CAAD,CAAlB;UACA6E,gBAAgB,GAAG,EAAnB;UACAD,cAAc,GAAGY,qBAAjB;UACA1H,iBAAiB,GAAG,CAApB;UACAD,cAAc,GAAG,EAAjB;UACA;QACH;;MACD,KAAKzE,MAAL;QAAa;UACT8M,aAAa;UACb;QACH;;MACD,KAAK/M,QAAL;QAAe;UACX;UACA;UACA+E,WAAW,GAAG;YAAE,GAAGE,UAAU,CAAC4F,WAAD;UAAf,CAAd;UACA7F,YAAY,GAAG,CAAf;UACA6B,eAAe,GAAG,CAAC,CAAD,CAAlB;UACA6E,gBAAgB,GAAG,EAAnB;UACA/G,iBAAiB,GAAG,CAApB;UACAD,cAAc,GAAG,EAAjB;UACA;QACH;;MACD,KAAKvE,aAAL;QAAoB;UAChB;UACA;UACA,MAAM;YAAEmB,EAAE,EAAEQ;UAAN,IAAmBiF,YAAzB;UACA,MAAMnF,KAAK,GAAG8J,gBAAgB,CAACnH,OAAjB,CAAyBzC,QAAzB,CAAd;;UACA,IAAIF,KAAK,KAAK,CAAC,CAAf,EAAkB;YACd8J,gBAAgB,GAAG,CAAC5J,QAAD,EAAW,GAAG4J,gBAAd,CAAnB;UACH,CAFD,MAGK;YACDA,gBAAgB,GAAGA,gBAAgB,CAAC1M,MAAjB,CAAyBsC,EAAD,IAAQA,EAAE,KAAKQ,QAAvC,CAAnB;UACH,CAVe,CAWhB;;;UACA0J,wBAAwB,GAAG3E,eAAe,CAACtC,OAAhB,CAAwBzC,QAAxB,CAA3B;UACA;QACH;;MACD,KAAK1B,kBAAL;QAAyB;UACrB;UACA;UACA,MAAM;YAAEoB,KAAF;YAASC,GAAT;YAAcC;UAAd,IAAyBqF,YAA/B;UACA,MAAMmG,SAAS,GAAG,EAAlB;;UACA,KAAK,IAAIpB,CAAC,GAAGtK,KAAb,EAAoBsK,CAAC,GAAGrK,GAAxB,EAA6BqK,CAAC,EAA9B,EACIoB,SAAS,CAACjG,IAAV,CAAe6E,CAAf;;UACJ,IAAIpK,MAAJ,EAAY;YACRgK,gBAAgB,GAAGvH,UAAU,CAACuH,gBAAD,EAAmBwB,SAAnB,CAA7B;UACH,CAFD,MAGK;YACDxB,gBAAgB,GAAG,CAAC,GAAGA,gBAAJ,EAAsB,GAAGwB,SAAzB,CAAnB;UACH,CAZoB,CAarB;;;UACA1B,wBAAwB,GAAG3E,eAAe,CAACtC,OAAhB,CAAwB/C,KAAxB,CAA3B;UACA;QACH;;MACD,KAAKnB,aAAL;QAAoB;UAChB;UACA;UACAsE,iBAAiB,GAAGoC,YAAY,CAACnF,KAAjC,CAHgB,CAIhB;;UACA4J,wBAAwB,GAAGwB,QAA3B;UACA;QACH;;MACD,KAAK1M,cAAL;QAAqB;UACjB;UACA;UACA,MAAMsB,KAAK,GAAGiF,eAAe,CAACtC,OAAhB,CAAwBwC,YAAY,CAACjF,QAArC,CAAd;UACA,IAAIF,KAAK,KAAK,CAAC,CAAf,EACI+C,iBAAiB,GAAG/C,KAApB;UACJ4J,wBAAwB,GAAGwB,QAA3B;UACA;QACH;;MACD,KAAK9M,KAAL;QAAY;UACR;UACA2G,eAAe,GAAG1C,UAAU,CAAC0C,eAAD,EAAkB6E,gBAAlB,CAA5B;UACAA,gBAAgB,GAAG,EAAnB;UACA/G,iBAAiB,GAAGwI,IAAI,CAACC,GAAL,CAASzI,iBAAT,EAA4BkC,eAAe,CAACjC,MAAhB,GAAyB,CAArD,CAApB;UACA;QACH;;MACD,KAAK/E,cAAL;QAAqB;UACjB;UACA,IAAI4I,QAAJ,EAAc;YACV,OAAOhE,WAAW,IAAIgI,kBAAtB;UACH;;UACD,IAAI/D,QAAQ,IACPjE,WAAW,IACRuC,gBAAgB,CAACvC,WAAW,CAACC,cAAZ,CAA2BC,iBAA3B,CAAD,EAAgDoC,YAAhD,EAA8DhD,OAAO,CAACqC,SAAtE,EAAiFrC,OAAO,CAACsC,eAAzF,EAA0GtC,OAAO,CAACuC,gBAAlH,CAFxB,EAE8J;YAC1J;YACA;YACA;YACA;YACA,MAAM+G,SAAS,GAAG3I,cAAc,CAACA,cAAc,CAACE,MAAf,GAAwB,CAAzB,CAAhC;YACAF,cAAc,GAAG,CACb,GAAGA,cAAc,CAACkH,KAAf,CAAqB,CAArB,EAAwB,CAAC,CAAzB,CADU,EAEbZ,gBAAgB,CAACC,OAAD,EAAUlE,YAAY,CAACnG,MAAvB,EAA+ByM,SAAS,CAACxI,KAAzC,EAAgDwI,SAAS,CAACpH,KAA1D,EAAiEiF,YAAjE,CAFH,CAAjB;YAIAM,wBAAwB,GAAGwB,QAA3B;YACA;UACH,CAnBgB,CAoBjB;;;UACA,IAAIjJ,OAAO,CAAC1B,MAAR,IAAkBwE,eAAe,CAACjC,MAAhB,KAA2Bb,OAAO,CAAC1B,MAAzD,EAAiE;YAC7DsK,mBAAmB,CAAC,CAAD,CAAnB;UACH;;UACD,IAAIhI,iBAAiB,KAAKkC,eAAe,CAACjC,MAAhB,GAAyB,CAAnD,EAAsD;YAClDD,iBAAiB;UACpB;;UACD,MAAM7C,QAAQ,GAAGkD,YAAY,EAA7B,CA3BiB,CA4BjB;UACA;;UACAD,WAAW,CAACjD,QAAD,CAAX,GAAwBiF,YAAxB;UACAF,eAAe,GAAG,CAAC,GAAGA,eAAJ,EAAqB/E,QAArB,CAAlB,CA/BiB,CAgCjB;;UACA0J,wBAAwB,GAAG3E,eAAe,CAACjC,MAAhB,GAAyB,CAApD;UACA;QACH;;MACD,KAAKrE,YAAL;QAAmB;UACf;UACA,CAAC;YACGgM,YADH;YAEGxH,WAFH;YAGGC,YAHH;YAIG6B,eAJH;YAKG6E,gBALH;YAMGD,cANH;YAOG9G,iBAPH;YAQGD,cARH;YASG+D,QATH;YAUGC;UAVH,IAWG3B,YAAY,CAAC/E,eAXjB;UAYA;QACH;;MACD,KAAK3D,IAAL;QAAW;UACP;UACAmN,wBAAwB,GAAG,CAA3B;;UACA,IAAIzH,OAAO,CAAC1B,MAAR,IAAkBwE,eAAe,CAACjC,MAAhB,GAAyBb,OAAO,CAAC1B,MAAvD,EAA+D;YAC3D;YACAqC,cAAc,GAAG6G,eAAe,CAAC7G,cAAD,EAAiB8G,wBAAjB,EAA2CP,OAA3C,EAAoDQ,cAApD,EAAoE1G,WAApE,EAAiF8B,eAAjF,EAAkG6E,gBAAlG,EAAoHR,YAApH,EAAkIxC,QAAlI,CAAhC;YACAiE,mBAAmB,CAAC9F,eAAe,CAACjC,MAAhB,GAAyBb,OAAO,CAAC1B,MAAlC,CAAnB,CAH2D,CAI3D;;YACAmJ,wBAAwB,GAAGwB,QAA3B;UACH;;UACD;QACH;;MACD,KAAK5O,MAAL;QAAa;UACT,MAAMkP,cAAc,GAAG5I,cAAc,CAAC1F,MAAf,CAAuB6F,KAAD,IAAWA,KAAK,CAACoB,KAAvC,EAA8CrB,MAA9C,GAAuD,CAA9E;;UACA,IAAI0I,cAAJ,EAAoB;YAChB;YACA9B,wBAAwB,GAAG,CAA3B;;YACA,IAAIzH,OAAO,CAAC1B,MAAR,IAAkBwE,eAAe,CAACjC,MAAhB,GAAyBb,OAAO,CAAC1B,MAAvD,EAA+D;cAC3D;cACAqC,cAAc,GAAG6G,eAAe,CAAC7G,cAAD,EAAiB8G,wBAAjB,EAA2CP,OAA3C,EAAoDQ,cAApD,EAAoE1G,WAApE,EAAiF8B,eAAjF,EAAkG6E,gBAAlG,EAAoHR,YAApH,EAAkIxC,QAAlI,CAAhC;cACAiE,mBAAmB,CAAC9F,eAAe,CAACjC,MAAhB,GAAyBb,OAAO,CAAC1B,MAAlC,CAAnB,CAH2D,CAI3D;;cACAmJ,wBAAwB,GAAGwB,QAA3B;YACH;UACJ,CAVD,MAWK;YACD;YACA;YACA,IAAI,CAACtE,QAAD,IAAa,CAACD,QAAlB,EAA4B;cACxB,IAAI9D,iBAAiB,KAAKkC,eAAe,CAACjC,MAAhB,GAAyB,CAAnD,EAAsD;gBAClDD,iBAAiB;cACpB,CAHuB,CAIxB;;;cACA,MAAM7C,QAAQ,GAAGkD,YAAY,EAA7B;cACAD,WAAW,CAACjD,QAAD,CAAX,GAAwB,IAAIpB,aAAJ,CAAkBqG,YAAlB,EAAgC,CAAC7B,IAAI,CAACC,GAAL,EAAjC,CAAxB;cACA0B,eAAe,GAAG,CAAC,GAAGA,eAAJ,EAAqB/E,QAArB,CAAlB;cACA0J,wBAAwB,GAAG3E,eAAe,CAACjC,MAAhB,GAAyB,CAApD;cACAF,cAAc,GAAG6G,eAAe,CAAC7G,cAAD,EAAiB8G,wBAAjB,EAA2CP,OAA3C,EAAoDQ,cAApD,EAAoE1G,WAApE,EAAiF8B,eAAjF,EAAkG6E,gBAAlG,EAAoHR,YAApH,EAAkIxC,QAAlI,CAAhC;YACH,CAbA,CAcD;;;YACAhE,cAAc,GAAGA,cAAc,CAACzF,GAAf,CAAoBsO,GAAD,KAAU,EAC1C,GAAGA,GADuC;cAE1C1I,KAAK,EAAEoG,OAAO,CAACsC,GAAG,CAAC1I,KAAL,EAAYkG,gBAAZ;YAF4B,CAAV,CAAnB,CAAjB;YAIApG,iBAAiB,GAAGkC,eAAe,CAACjC,MAAhB,GAAyB,CAA7C;;YACA,IAAIb,OAAO,CAAC1B,MAAR,IAAkBwE,eAAe,CAACjC,MAAhB,GAAyBb,OAAO,CAAC1B,MAAvD,EAA+D;cAC3DsK,mBAAmB,CAAC9F,eAAe,CAACjC,MAAhB,GAAyBb,OAAO,CAAC1B,MAAlC,CAAnB;YACH,CAtBA,CAuBD;;;YACAmJ,wBAAwB,GAAGwB,QAA3B;UACH;;UACD;QACH;;MACD;QAAS;UACL;UACA;UACAxB,wBAAwB,GAAGwB,QAA3B;UACA;QACH;IA7NL;;IA+NAtI,cAAc,GAAG6G,eAAe,CAAC7G,cAAD,EAAiB8G,wBAAjB,EAA2CP,OAA3C,EAAoDQ,cAApD,EAAoE1G,WAApE,EAAiF8B,eAAjF,EAAkG6E,gBAAlG,EAAoHR,YAApH,EAAkIxC,QAAlI,CAAhC;IACA6D,YAAY,GAAGD,cAAc,CAACC,YAAD,EAAexF,YAAf,CAA7B;IACA,OAAO;MACHwF,YADG;MAEHxH,WAFG;MAGHC,YAHG;MAIH6B,eAJG;MAKH6E,gBALG;MAMHD,cANG;MAOH9G,iBAPG;MAQHD,cARG;MASH+D,QATG;MAUHC;IAVG,CAAP;EAYH,CAxRD;AAyRH;;AAED,MAAM8E,aAAN,CAAoB;EAChB7M,WAAW,CAAC2H,UAAD,EAAa6B,QAAb,EAAuBsD,SAAvB,EAAkCC,SAAlC,EAA6CC,cAA7C,EAA6DzC,YAA7D,EAA2E0C,YAA3E,EAAyF5J,MAAzF,EAAiG;IACxG,MAAM6J,kBAAkB,GAAGzB,gBAAgB,CAACwB,YAAD,EAAe5J,MAAM,CAACnB,OAAtB,CAA3C;IACA,MAAMiL,WAAW,GAAGtB,eAAe,CAACoB,YAAD,EAAeC,kBAAf,EAAmC3C,YAAnC,EAAiDlH,MAAM,CAACnB,OAAxD,EAAiEmB,MAAjE,CAAnC;IACA,MAAM+J,aAAa,GAAGnP,KAAK,CAACA,KAAK,CAACuL,QAAQ,CAAC6D,YAAT,GAAwBnE,IAAxB,CAA6BpK,IAAI,CAAC,CAAD,CAAjC,CAAD,EAAwCiO,SAAS,CAACvD,QAAlD,CAAL,CAAiEN,IAAjE,CAAsE5K,GAAG,CAACgG,UAAD,CAAzE,CAAD,EAAyFqD,UAAzF,EAAqGoF,SAAS,CAAC1D,cAA/G,CAAL,CAAoIH,IAApI,CAAyInK,SAAS,CAACb,cAAD,CAAlJ,CAAtB;IACA,MAAMoP,cAAc,GAAGR,SAAS,CAAC5D,IAAV,CAAe5K,GAAG,CAAC6O,WAAD,CAAlB,CAAvB;IACA,MAAMI,kBAAkB,GAAG,IAAIpP,aAAJ,CAAkB,CAAlB,CAA3B;IACA,MAAMqP,uBAAuB,GAAGJ,aAAa,CACxClE,IAD2B,CACtBlK,cAAc,CAACsO,cAAD,CADQ,EACUrO,IAAI,CAAC,CAAC;MAAEiF,KAAK,EAAEJ;IAAT,CAAD,EAAyB,CAAC7D,MAAD,EAASqK,OAAT,CAAzB,KAA+C;MAC1F,IAAImD,kBAAkB,GAAGnD,OAAO,CAACxG,WAAD,EAAc7D,MAAd,CAAhC,CAD0F,CAE1F;MACA;;MACA,IAAIA,MAAM,CAACE,IAAP,KAAgBjB,cAAhB,IAAkCsG,mBAAmB,CAACnC,MAAD,CAAzD,EAAmE;QAC/DoK,kBAAkB,GAAG7H,iBAAiB,CAAC6H,kBAAD,EAAqBpK,MAAM,CAACoC,SAA5B,EAAuCpC,MAAM,CAACqC,eAA9C,EAA+DrC,MAAM,CAACsC,gBAAtE,CAAtC;MACH,CANyF,CAO1F;;;MACAoH,SAAS,CAAClF,MAAV,CAAiB5H,MAAjB,EAAyBwN,kBAAzB;MACA,OAAO;QAAEvJ,KAAK,EAAEuJ,kBAAT;QAA6BxN;MAA7B,CAAP;IACH,CAV6C,EAU3C;MAAEiE,KAAK,EAAEgJ,kBAAT;MAA6BjN,MAAM,EAAE;IAArC,CAV2C,CADd,EAY3B4I,SAZ2B,CAYjB,CAAC;MAAE3E,KAAF;MAASjE;IAAT,CAAD,KAAuB;MAClCsN,kBAAkB,CAACxE,IAAnB,CAAwB7E,KAAxB;;MACA,IAAIjE,MAAM,CAACE,IAAP,KAAgBjB,cAApB,EAAoC;QAChC,MAAMwO,cAAc,GAAGzN,MAAM,CAACA,MAA9B;QACA+M,cAAc,CAACjE,IAAf,CAAoB2E,cAApB;MACH;IACJ,CAlB+B,CAAhC;IAmBA,MAAMC,0BAA0B,GAAGZ,SAAS,CAAC5D,MAAV,CAAiBN,SAAjB,CAA2B,MAAM;MAChE,KAAK+E,OAAL;IACH,CAFkC,CAAnC;IAGA,MAAMC,YAAY,GAAGN,kBAAkB,CAACF,YAAnB,EAArB;IACA,MAAMS,MAAM,GAAGD,YAAY,CAAC3E,IAAb,CAAkB5K,GAAG,CAACuF,WAAD,CAArB,CAAf;IACA,KAAK8J,0BAAL,GAAkCA,0BAAlC;IACA,KAAKI,iBAAL,GAAyBP,uBAAzB;IACA,KAAK7F,UAAL,GAAkBA,UAAlB;IACA,KAAK7D,WAAL,GAAmB+J,YAAnB;IACA,KAAK3J,KAAL,GAAa4J,MAAb;EACH;;EACD5K,QAAQ,CAACjD,MAAD,EAAS;IACb,KAAK0H,UAAL,CAAgBoB,IAAhB,CAAqB9I,MAArB;EACH;;EACD8I,IAAI,CAAC9I,MAAD,EAAS;IACT,KAAK0H,UAAL,CAAgBoB,IAAhB,CAAqB9I,MAArB;EACH;;EACDqF,KAAK,CAACA,KAAD,EAAQ,CAAG;;EAChB0I,QAAQ,GAAG,CAAG;;EACdC,aAAa,CAAChO,MAAD,EAAS;IAClB,KAAKiD,QAAL,CAAc,IAAInD,aAAJ,CAAkBE,MAAlB,EAA0B,CAACsE,IAAI,CAACC,GAAL,EAA3B,CAAd;EACH;;EACDoJ,OAAO,GAAG;IACN,KAAK1K,QAAL,CAAc,IAAI7C,OAAJ,EAAd;EACH;;EACD6N,KAAK,GAAG;IACJ,KAAKhL,QAAL,CAAc,IAAI5C,KAAJ,CAAU,CAACiE,IAAI,CAACC,GAAL,EAAX,CAAd;EACH;;EACD2J,QAAQ,GAAG;IACP,KAAKjL,QAAL,CAAc,IAAI3C,QAAJ,CAAa,CAACgE,IAAI,CAACC,GAAL,EAAd,CAAd;EACH;;EACD4J,MAAM,GAAG;IACL,KAAKlL,QAAL,CAAc,IAAI1C,MAAJ,CAAW,CAAC+D,IAAI,CAACC,GAAL,EAAZ,CAAd;EACH;;EACD6J,KAAK,GAAG;IACJ,KAAKnL,QAAL,CAAc,IAAIzC,KAAJ,EAAd;EACH;;EACD6N,YAAY,CAAC3N,EAAD,EAAK;IACb,KAAKuC,QAAL,CAAc,IAAIxC,YAAJ,CAAiBC,EAAjB,CAAd;EACH;;EACD4N,YAAY,CAACpN,QAAD,EAAW;IACnB,KAAK+B,QAAL,CAAc,IAAIhC,YAAJ,CAAiBC,QAAjB,CAAd;EACH;;EACDqN,WAAW,CAACvN,KAAD,EAAQ;IACf,KAAKiC,QAAL,CAAc,IAAIlC,WAAJ,CAAgBC,KAAhB,CAAd;EACH;;EACDwN,WAAW,CAACpN,eAAD,EAAkB;IACzB,KAAK6B,QAAL,CAAc,IAAI9B,WAAJ,CAAgBC,eAAhB,CAAd;EACH;;EACDqN,WAAW,CAACnN,MAAD,EAAS;IAChB,KAAK2B,QAAL,CAAc,IAAI5B,WAAJ,CAAgBC,MAAhB,CAAd;EACH;;EACDoN,cAAc,CAACpN,MAAD,EAAS;IACnB,KAAK2B,QAAL,CAAc,IAAI1B,cAAJ,CAAmBD,MAAnB,CAAd;EACH;;AAhFe;AAkFpB;;;AAAmBsL,aAAa,CAAC5F,IAAd;EAAA,iBAA0G4F,aAA1G,EA3lBqG3P,EA2lBrG,UAAyI8J,kBAAzI,GA3lBqG9J,EA2lBrG,UAAwKK,EAAE,CAACC,cAA3K,GA3lBqGN,EA2lBrG,UAAsMK,EAAE,CAACqR,iBAAzM,GA3lBqG1R,EA2lBrG,UAAuOuK,iBAAvO,GA3lBqGvK,EA2lBrG,UAAqQK,EAAE,CAACsR,qBAAxQ,GA3lBqG3R,EA2lBrG,UAA0SA,EAAE,CAAC4R,YAA7S,GA3lBqG5R,EA2lBrG,UAAsUS,aAAtU,GA3lBqGT,EA2lBrG,UAAgWyE,qBAAhW;AAAA;AACnB;;;AAAmBkL,aAAa,CAAC3F,KAAd,kBA5lBqGhK,EA4lBrG;EAAA,OAA8G2P,aAA9G;EAAA,SAA8GA,aAA9G;AAAA;;AACnB;EAAA,mDA7lBwH3P,EA6lBxH,mBAA2F2P,aAA3F,EAAsH,CAAC;IAC3G1M,IAAI,EAAE/C;EADqG,CAAD,CAAtH,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAE+C,IAAI,EAAE6G;IAAR,CAAD,EAA+B;MAAE7G,IAAI,EAAE5C,EAAE,CAACC;IAAX,CAA/B,EAA4D;MAAE2C,IAAI,EAAE5C,EAAE,CAACqR;IAAX,CAA5D,EAA4F;MAAEzO,IAAI,EAAEsH;IAAR,CAA5F,EAAyH;MAAEtH,IAAI,EAAE5C,EAAE,CAACsR;IAAX,CAAzH,EAA6J;MAAE1O,IAAI,EAAEjD,EAAE,CAAC4R;IAAX,CAA7J,EAAwL;MAAE3O,IAAI,EAAEiC,SAAR;MAAmB4H,UAAU,EAAE,CAAC;QACrP7J,IAAI,EAAE9C,MAD+O;QAErP4M,IAAI,EAAE,CAACtM,aAAD;MAF+O,CAAD;IAA/B,CAAxL,EAG3B;MAAEwC,IAAI,EAAEsB,mBAAR;MAA6BuI,UAAU,EAAE,CAAC;QAC5C7J,IAAI,EAAE9C,MADsC;QAE5C4M,IAAI,EAAE,CAACtI,qBAAD;MAFsC,CAAD;IAAzC,CAH2B,CAAP;EAMlB,CARxB;AAAA;;AAUA,MAAMoN,+BAA+B,GAAG,IAAI5R,cAAJ,CAAmB,+DAAnB,CAAxC;;AACA,SAAS6R,iCAAT,CAA2CjC,SAA3C,EAAsD1J,MAAtD,EAA8D;EAC1D,OAAO4L,OAAO,CAAClC,SAAD,CAAP,IAAsB1J,MAAM,CAACnB,OAAP,KAAmBL,SAAhD;AACH;;AACD,SAASqN,4BAAT,GAAwC;EACpC,MAAMC,YAAY,GAAG,8BAArB;;EACA,IAAI,OAAOC,MAAP,KAAkB,QAAlB,IACA,OAAOA,MAAM,CAACD,YAAD,CAAb,KAAgC,WADpC,EACiD;IAC7C,OAAOC,MAAM,CAACD,YAAD,CAAb;EACH,CAHD,MAIK;IACD,OAAO,IAAP;EACH;AACJ;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASE,oBAAT,CAA8BjM,OAAO,GAAG,EAAxC,EAA4C;EACxC,OAAO;IACHkM,UAAU,EAAE,CACR7H,iBADQ,EAERT,kBAFQ,EAGR6F,aAHQ,EAIR;MACI0C,OAAO,EAAE3N,eADb;MAEI4N,QAAQ,EAAEpM;IAFd,CAJQ,EAQR;MACImM,OAAO,EAAER,+BADb;MAEIU,IAAI,EAAE,CAACjI,wBAAD,EAA2B7F,qBAA3B,CAFV;MAGI+N,UAAU,EAAEV;IAHhB,CARQ,EAaR;MACIO,OAAO,EAAE/H,wBADb;MAEIkI,UAAU,EAAER;IAFhB,CAbQ,EAiBR;MACIK,OAAO,EAAE5N,qBADb;MAEI8N,IAAI,EAAE,CAAC7N,eAAD,CAFV;MAGI8N,UAAU,EAAE3N;IAHhB,CAjBQ,EAsBR;MACIwN,OAAO,EAAE3R,eADb;MAEI6R,IAAI,EAAE,CAAC5C,aAAD,CAFV;MAGI6C,UAAU,EAAEC;IAHhB,CAtBQ,EA2BR;MACIJ,OAAO,EAAE1R,wBADb;MAEI+R,WAAW,EAAE5I;IAFjB,CA3BQ;EADT,CAAP;AAkCH;;AAED,SAAS2I,qBAAT,CAA+BE,QAA/B,EAAyC;EACrC,OAAOA,QAAQ,CAAC3L,KAAhB;AACH;;AACD,MAAM4L,mBAAN,CAA0B;EACL,OAAVC,UAAU,CAAC3M,OAAO,GAAG,EAAX,EAAe;IAC5B,OAAO;MACH4M,QAAQ,EAAEF,mBADP;MAEHG,SAAS,EAAE,CAAC,GAAGZ,oBAAoB,CAACjM,OAAD,CAApB,CAA8BkM,UAAlC;IAFR,CAAP;EAIH;;AANqB;AAQ1B;;;AAAmBQ,mBAAmB,CAAC7I,IAApB;EAAA,iBAAgH6I,mBAAhH;AAAA;AACnB;;;AAAmBA,mBAAmB,CAACI,IAApB,kBAtrBqGhT,EAsrBrG;EAAA,MAAiH4S;AAAjH;AACnB;;AAAmBA,mBAAmB,CAACK,IAApB,kBAvrBqGjT,EAurBrG;;AACnB;EAAA,mDAxrBwHA,EAwrBxH,mBAA2F4S,mBAA3F,EAA4H,CAAC;IACjH3P,IAAI,EAAE7C,QAD2G;IAEjH2M,IAAI,EAAE,CAAC,EAAD;EAF2G,CAAD,CAA5H;AAAA;AAKA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASrI,eAAT,EAA0BuI,SAA1B,EAAqC3C,wBAArC,EAA+DqF,aAA/D,EAA8EpL,mBAA9E,EAAmGqO,mBAAnG,EAAwHT,oBAAxH"}, "metadata": {}, "sourceType": "module"}