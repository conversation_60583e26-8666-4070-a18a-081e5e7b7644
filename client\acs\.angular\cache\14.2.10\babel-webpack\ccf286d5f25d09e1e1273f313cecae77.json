{"ast": null, "code": "'use strict';\n\nvar forEach = require('for-each');\n\nvar availableTypedArrays = require('available-typed-arrays');\n\nvar callBound = require('call-bind/callBound');\n\nvar $toString = callBound('Object.prototype.toString');\n\nvar hasToStringTag = require('has-tostringtag/shams')();\n\nvar gOPD = require('gopd');\n\nvar g = typeof globalThis === 'undefined' ? global : globalThis;\nvar typedArrays = availableTypedArrays();\n\nvar $indexOf = callBound('Array.prototype.indexOf', true) || function indexOf(array, value) {\n  for (var i = 0; i < array.length; i += 1) {\n    if (array[i] === value) {\n      return i;\n    }\n  }\n\n  return -1;\n};\n\nvar $slice = callBound('String.prototype.slice');\nvar toStrTags = {};\nvar getPrototypeOf = Object.getPrototypeOf; // require('getprototypeof');\n\nif (hasToStringTag && gOPD && getPrototypeOf) {\n  forEach(typedArrays, function (typedArray) {\n    var arr = new g[typedArray]();\n\n    if (Symbol.toStringTag in arr) {\n      var proto = getPrototypeOf(arr);\n      var descriptor = gOPD(proto, Symbol.toStringTag);\n\n      if (!descriptor) {\n        var superProto = getPrototypeOf(proto);\n        descriptor = gOPD(superProto, Symbol.toStringTag);\n      }\n\n      toStrTags[typedArray] = descriptor.get;\n    }\n  });\n}\n\nvar tryTypedArrays = function tryAllTypedArrays(value) {\n  var anyTrue = false;\n  forEach(toStrTags, function (getter, typedArray) {\n    if (!anyTrue) {\n      try {\n        anyTrue = getter.call(value) === typedArray;\n      } catch (e) {\n        /**/\n      }\n    }\n  });\n  return anyTrue;\n};\n\nmodule.exports = function isTypedArray(value) {\n  if (!value || typeof value !== 'object') {\n    return false;\n  }\n\n  if (!hasToStringTag || !(Symbol.toStringTag in value)) {\n    var tag = $slice($toString(value), 8, -1);\n    return $indexOf(typedArrays, tag) > -1;\n  }\n\n  if (!gOPD) {\n    return false;\n  }\n\n  return tryTypedArrays(value);\n};", "map": {"version": 3, "names": ["for<PERSON>ach", "require", "availableTypedArrays", "callBound", "$toString", "hasToStringTag", "gOPD", "g", "globalThis", "global", "typedArrays", "$indexOf", "indexOf", "array", "value", "i", "length", "$slice", "toStrTags", "getPrototypeOf", "Object", "typedArray", "arr", "Symbol", "toStringTag", "proto", "descriptor", "superProto", "get", "tryTypedArrays", "tryAllTypedArrays", "anyTrue", "getter", "call", "e", "module", "exports", "isTypedArray", "tag"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/is-typed-array/index.js"], "sourcesContent": ["'use strict';\n\nvar forEach = require('for-each');\nvar availableTypedArrays = require('available-typed-arrays');\nvar callBound = require('call-bind/callBound');\n\nvar $toString = callBound('Object.prototype.toString');\nvar hasToStringTag = require('has-tostringtag/shams')();\nvar gOPD = require('gopd');\n\nvar g = typeof globalThis === 'undefined' ? global : globalThis;\nvar typedArrays = availableTypedArrays();\n\nvar $indexOf = callBound('Array.prototype.indexOf', true) || function indexOf(array, value) {\n\tfor (var i = 0; i < array.length; i += 1) {\n\t\tif (array[i] === value) {\n\t\t\treturn i;\n\t\t}\n\t}\n\treturn -1;\n};\nvar $slice = callBound('String.prototype.slice');\nvar toStrTags = {};\nvar getPrototypeOf = Object.getPrototypeOf; // require('getprototypeof');\nif (hasToStringTag && gOPD && getPrototypeOf) {\n\tforEach(typedArrays, function (typedArray) {\n\t\tvar arr = new g[typedArray]();\n\t\tif (Symbol.toStringTag in arr) {\n\t\t\tvar proto = getPrototypeOf(arr);\n\t\t\tvar descriptor = gOPD(proto, Symbol.toStringTag);\n\t\t\tif (!descriptor) {\n\t\t\t\tvar superProto = getPrototypeOf(proto);\n\t\t\t\tdescriptor = gOPD(superProto, Symbol.toStringTag);\n\t\t\t}\n\t\t\ttoStrTags[typedArray] = descriptor.get;\n\t\t}\n\t});\n}\n\nvar tryTypedArrays = function tryAllTypedArrays(value) {\n\tvar anyTrue = false;\n\tforEach(toStrTags, function (getter, typedArray) {\n\t\tif (!anyTrue) {\n\t\t\ttry {\n\t\t\t\tanyTrue = getter.call(value) === typedArray;\n\t\t\t} catch (e) { /**/ }\n\t\t}\n\t});\n\treturn anyTrue;\n};\n\nmodule.exports = function isTypedArray(value) {\n\tif (!value || typeof value !== 'object') { return false; }\n\tif (!hasToStringTag || !(Symbol.toStringTag in value)) {\n\t\tvar tag = $slice($toString(value), 8, -1);\n\t\treturn $indexOf(typedArrays, tag) > -1;\n\t}\n\tif (!gOPD) { return false; }\n\treturn tryTypedArrays(value);\n};\n"], "mappings": "AAAA;;AAEA,IAAIA,OAAO,GAAGC,OAAO,CAAC,UAAD,CAArB;;AACA,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,wBAAD,CAAlC;;AACA,IAAIE,SAAS,GAAGF,OAAO,CAAC,qBAAD,CAAvB;;AAEA,IAAIG,SAAS,GAAGD,SAAS,CAAC,2BAAD,CAAzB;;AACA,IAAIE,cAAc,GAAGJ,OAAO,CAAC,uBAAD,CAAP,EAArB;;AACA,IAAIK,IAAI,GAAGL,OAAO,CAAC,MAAD,CAAlB;;AAEA,IAAIM,CAAC,GAAG,OAAOC,UAAP,KAAsB,WAAtB,GAAoCC,MAApC,GAA6CD,UAArD;AACA,IAAIE,WAAW,GAAGR,oBAAoB,EAAtC;;AAEA,IAAIS,QAAQ,GAAGR,SAAS,CAAC,yBAAD,EAA4B,IAA5B,CAAT,IAA8C,SAASS,OAAT,CAAiBC,KAAjB,EAAwBC,KAAxB,EAA+B;EAC3F,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,KAAK,CAACG,MAA1B,EAAkCD,CAAC,IAAI,CAAvC,EAA0C;IACzC,IAAIF,KAAK,CAACE,CAAD,CAAL,KAAaD,KAAjB,EAAwB;MACvB,OAAOC,CAAP;IACA;EACD;;EACD,OAAO,CAAC,CAAR;AACA,CAPD;;AAQA,IAAIE,MAAM,GAAGd,SAAS,CAAC,wBAAD,CAAtB;AACA,IAAIe,SAAS,GAAG,EAAhB;AACA,IAAIC,cAAc,GAAGC,MAAM,CAACD,cAA5B,C,CAA4C;;AAC5C,IAAId,cAAc,IAAIC,IAAlB,IAA0Ba,cAA9B,EAA8C;EAC7CnB,OAAO,CAACU,WAAD,EAAc,UAAUW,UAAV,EAAsB;IAC1C,IAAIC,GAAG,GAAG,IAAIf,CAAC,CAACc,UAAD,CAAL,EAAV;;IACA,IAAIE,MAAM,CAACC,WAAP,IAAsBF,GAA1B,EAA+B;MAC9B,IAAIG,KAAK,GAAGN,cAAc,CAACG,GAAD,CAA1B;MACA,IAAII,UAAU,GAAGpB,IAAI,CAACmB,KAAD,EAAQF,MAAM,CAACC,WAAf,CAArB;;MACA,IAAI,CAACE,UAAL,EAAiB;QAChB,IAAIC,UAAU,GAAGR,cAAc,CAACM,KAAD,CAA/B;QACAC,UAAU,GAAGpB,IAAI,CAACqB,UAAD,EAAaJ,MAAM,CAACC,WAApB,CAAjB;MACA;;MACDN,SAAS,CAACG,UAAD,CAAT,GAAwBK,UAAU,CAACE,GAAnC;IACA;EACD,CAXM,CAAP;AAYA;;AAED,IAAIC,cAAc,GAAG,SAASC,iBAAT,CAA2BhB,KAA3B,EAAkC;EACtD,IAAIiB,OAAO,GAAG,KAAd;EACA/B,OAAO,CAACkB,SAAD,EAAY,UAAUc,MAAV,EAAkBX,UAAlB,EAA8B;IAChD,IAAI,CAACU,OAAL,EAAc;MACb,IAAI;QACHA,OAAO,GAAGC,MAAM,CAACC,IAAP,CAAYnB,KAAZ,MAAuBO,UAAjC;MACA,CAFD,CAEE,OAAOa,CAAP,EAAU;QAAE;MAAM;IACpB;EACD,CANM,CAAP;EAOA,OAAOH,OAAP;AACA,CAVD;;AAYAI,MAAM,CAACC,OAAP,GAAiB,SAASC,YAAT,CAAsBvB,KAAtB,EAA6B;EAC7C,IAAI,CAACA,KAAD,IAAU,OAAOA,KAAP,KAAiB,QAA/B,EAAyC;IAAE,OAAO,KAAP;EAAe;;EAC1D,IAAI,CAACT,cAAD,IAAmB,EAAEkB,MAAM,CAACC,WAAP,IAAsBV,KAAxB,CAAvB,EAAuD;IACtD,IAAIwB,GAAG,GAAGrB,MAAM,CAACb,SAAS,CAACU,KAAD,CAAV,EAAmB,CAAnB,EAAsB,CAAC,CAAvB,CAAhB;IACA,OAAOH,QAAQ,CAACD,WAAD,EAAc4B,GAAd,CAAR,GAA6B,CAAC,CAArC;EACA;;EACD,IAAI,CAAChC,IAAL,EAAW;IAAE,OAAO,KAAP;EAAe;;EAC5B,OAAOuB,cAAc,CAACf,KAAD,CAArB;AACA,CARD"}, "metadata": {}, "sourceType": "script"}