{"ast": null, "code": "import { reduce } from './reduce';\nexport function max(comparer) {\n  const max = typeof comparer === 'function' ? (x, y) => comparer(x, y) > 0 ? x : y : (x, y) => x > y ? x : y;\n  return reduce(max);\n}", "map": {"version": 3, "names": ["reduce", "max", "comparer", "x", "y"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/max.js"], "sourcesContent": ["import { reduce } from './reduce';\nexport function max(comparer) {\n    const max = (typeof comparer === 'function')\n        ? (x, y) => comparer(x, y) > 0 ? x : y\n        : (x, y) => x > y ? x : y;\n    return reduce(max);\n}\n"], "mappings": "AAAA,SAASA,MAAT,QAAuB,UAAvB;AACA,OAAO,SAASC,GAAT,CAAaC,QAAb,EAAuB;EAC1B,MAAMD,GAAG,GAAI,OAAOC,QAAP,KAAoB,UAArB,GACN,CAACC,CAAD,EAAIC,CAAJ,KAAUF,QAAQ,CAACC,CAAD,EAAIC,CAAJ,CAAR,GAAiB,CAAjB,GAAqBD,CAArB,GAAyBC,CAD7B,GAEN,CAACD,CAAD,EAAIC,CAAJ,KAAUD,CAAC,GAAGC,CAAJ,GAAQD,CAAR,GAAYC,CAF5B;EAGA,OAAOJ,MAAM,CAACC,GAAD,CAAb;AACH"}, "metadata": {}, "sourceType": "module"}