{"ast": null, "code": "import * as i2$1 from '@angular/cdk/portal';\nimport { TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { CdkStepLabel, CdkStepHeader, CdkStep, STEPPER_GLOBAL_OPTIONS, CdkStepper, CdkStepperNext, CdkStepperPrevious, CdkStepperModule } from '@angular/cdk/stepper';\nimport * as i3 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, Injectable, Optional, SkipSelf, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, forwardRef, Inject, ContentChild, QueryList, EventEmitter, ViewChildren, ContentChildren, Output, NgModule } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport * as i1 from '@angular/material/core';\nimport { mixinColor, ErrorStateMatcher, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport * as i4 from '@angular/material/icon';\nimport { MatIconModule } from '@angular/material/icon';\nimport * as i2 from '@angular/cdk/a11y';\nimport { Subject, Subscription } from 'rxjs';\nimport * as i3$1 from '@angular/cdk/bidi';\nimport { switchMap, map, startWith, takeUntil, distinctUntilChanged } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nfunction MatStepHeader_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 8);\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.iconOverrides[ctx_r0.state])(\"ngTemplateOutletContext\", ctx_r0._getIconContext());\n  }\n}\n\nfunction MatStepHeader_ng_container_4_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r6._getDefaultTextForState(ctx_r6.state));\n  }\n}\n\nfunction MatStepHeader_ng_container_4_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7._intl.completedLabel);\n  }\n}\n\nfunction MatStepHeader_ng_container_4_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r8._intl.editableLabel);\n  }\n}\n\nfunction MatStepHeader_ng_container_4_mat_icon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r9._getDefaultTextForState(ctx_r9.state));\n  }\n}\n\nfunction MatStepHeader_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0, 9);\n    i0.ɵɵtemplate(1, MatStepHeader_ng_container_4_span_1_Template, 2, 1, \"span\", 10);\n    i0.ɵɵtemplate(2, MatStepHeader_ng_container_4_span_2_Template, 2, 1, \"span\", 11);\n    i0.ɵɵtemplate(3, MatStepHeader_ng_container_4_span_3_Template, 2, 1, \"span\", 11);\n    i0.ɵɵtemplate(4, MatStepHeader_ng_container_4_mat_icon_4_Template, 2, 1, \"mat-icon\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngSwitch\", ctx_r1.state);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"number\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.state === \"done\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.state === \"edit\");\n  }\n}\n\nfunction MatStepHeader_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelementContainer(1, 16);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2._templateLabel().template);\n  }\n}\n\nfunction MatStepHeader_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r3.label);\n  }\n}\n\nfunction MatStepHeader_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4._intl.optionalLabel);\n  }\n}\n\nfunction MatStepHeader_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r5.errorMessage);\n  }\n}\n\nfunction MatStep_ng_template_0_ng_template_1_Template(rf, ctx) {}\n\nfunction MatStep_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n    i0.ɵɵtemplate(1, MatStep_ng_template_0_ng_template_1_Template, 0, 0, \"ng-template\", 0);\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"cdkPortalOutlet\", ctx_r0._portal);\n  }\n}\n\nconst _c0 = [\"*\"];\n\nfunction MatStepper_div_1_ng_container_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 11);\n  }\n}\n\nconst _c1 = function (a0, a1) {\n  return {\n    step: a0,\n    i: a1\n  };\n};\n\nfunction MatStepper_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementContainer(1, 9);\n    i0.ɵɵtemplate(2, MatStepper_div_1_ng_container_2_div_2_Template, 1, 0, \"div\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const step_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const isLast_r8 = ctx.last;\n    i0.ɵɵnextContext(2);\n\n    const _r2 = i0.ɵɵreference(4);\n\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r2)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(3, _c1, step_r6, i_r7));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !isLast_r8);\n  }\n}\n\nconst _c2 = function (a0) {\n  return {\n    \"animationDuration\": a0\n  };\n};\n\nconst _c3 = function (a0, a1) {\n  return {\n    \"value\": a0,\n    \"params\": a1\n  };\n};\n\nfunction MatStepper_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵlistener(\"@horizontalStepTransition.done\", function MatStepper_div_1_div_4_Template_div_animation_horizontalStepTransition_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12._animationDone.next($event));\n    });\n    i0.ɵɵelementContainer(1, 13);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const step_r10 = ctx.$implicit;\n    const i_r11 = ctx.index;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"mat-horizontal-stepper-content-inactive\", ctx_r5.selectedIndex !== i_r11);\n    i0.ɵɵproperty(\"@horizontalStepTransition\", i0.ɵɵpureFunction2(8, _c3, ctx_r5._getAnimationDirection(i_r11), i0.ɵɵpureFunction1(6, _c2, ctx_r5._getAnimationDuration())))(\"id\", ctx_r5._getStepContentId(i_r11));\n    i0.ɵɵattribute(\"aria-labelledby\", ctx_r5._getStepLabelId(i_r11));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", step_r10.content);\n  }\n}\n\nfunction MatStepper_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5);\n    i0.ɵɵtemplate(2, MatStepper_div_1_ng_container_2_Template, 3, 6, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 7);\n    i0.ɵɵtemplate(4, MatStepper_div_1_div_4_Template, 2, 11, \"div\", 8);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.steps);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.steps);\n  }\n}\n\nfunction MatStepper_ng_container_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelementContainer(1, 9);\n    i0.ɵɵelementStart(2, \"div\", 16)(3, \"div\", 17);\n    i0.ɵɵlistener(\"@verticalStepTransition.done\", function MatStepper_ng_container_2_div_1_Template_div_animation_verticalStepTransition_done_3_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18._animationDone.next($event));\n    });\n    i0.ɵɵelementStart(4, \"div\", 18);\n    i0.ɵɵelementContainer(5, 13);\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const step_r15 = ctx.$implicit;\n    const i_r16 = ctx.index;\n    const isLast_r17 = ctx.last;\n    const ctx_r14 = i0.ɵɵnextContext(2);\n\n    const _r2 = i0.ɵɵreference(4);\n\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r2)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(10, _c1, step_r15, i_r16));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"mat-stepper-vertical-line\", !isLast_r17);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"mat-vertical-stepper-content-inactive\", ctx_r14.selectedIndex !== i_r16);\n    i0.ɵɵproperty(\"@verticalStepTransition\", i0.ɵɵpureFunction2(15, _c3, ctx_r14._getAnimationDirection(i_r16), i0.ɵɵpureFunction1(13, _c2, ctx_r14._getAnimationDuration())))(\"id\", ctx_r14._getStepContentId(i_r16));\n    i0.ɵɵattribute(\"aria-labelledby\", ctx_r14._getStepLabelId(i_r16));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", step_r15.content);\n  }\n}\n\nfunction MatStepper_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MatStepper_ng_container_2_div_1_Template, 6, 18, \"div\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.steps);\n  }\n}\n\nfunction MatStepper_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"mat-step-header\", 19);\n    i0.ɵɵlistener(\"click\", function MatStepper_ng_template_3_Template_mat_step_header_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r23);\n      const step_r20 = restoredCtx.step;\n      return i0.ɵɵresetView(step_r20.select());\n    })(\"keydown\", function MatStepper_ng_template_3_Template_mat_step_header_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24._onKeydown($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const step_r20 = ctx.step;\n    const i_r21 = ctx.i;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-horizontal-stepper-header\", ctx_r3.orientation === \"horizontal\")(\"mat-vertical-stepper-header\", ctx_r3.orientation === \"vertical\");\n    i0.ɵɵproperty(\"tabIndex\", ctx_r3._getFocusIndex() === i_r21 ? 0 : -1)(\"id\", ctx_r3._getStepLabelId(i_r21))(\"index\", i_r21)(\"state\", ctx_r3._getIndicatorType(i_r21, step_r20.state))(\"label\", step_r20.stepLabel || step_r20.label)(\"selected\", ctx_r3.selectedIndex === i_r21)(\"active\", ctx_r3._stepIsNavigable(i_r21, step_r20))(\"optional\", step_r20.optional)(\"errorMessage\", step_r20.errorMessage)(\"iconOverrides\", ctx_r3._iconOverrides)(\"disableRipple\", ctx_r3.disableRipple || !ctx_r3._stepIsNavigable(i_r21, step_r20))(\"color\", step_r20.color || ctx_r3.color);\n    i0.ɵɵattribute(\"aria-posinset\", i_r21 + 1)(\"aria-setsize\", ctx_r3.steps.length)(\"aria-controls\", ctx_r3._getStepContentId(i_r21))(\"aria-selected\", ctx_r3.selectedIndex == i_r21)(\"aria-label\", step_r20.ariaLabel || null)(\"aria-labelledby\", !step_r20.ariaLabel && step_r20.ariaLabelledby ? step_r20.ariaLabelledby : null)(\"aria-disabled\", ctx_r3._stepIsNavigable(i_r21, step_r20) ? null : true);\n  }\n}\n\nclass MatStepLabel extends CdkStepLabel {}\n\nMatStepLabel.ɵfac = /* @__PURE__ */function () {\n  let ɵMatStepLabel_BaseFactory;\n  return function MatStepLabel_Factory(t) {\n    return (ɵMatStepLabel_BaseFactory || (ɵMatStepLabel_BaseFactory = i0.ɵɵgetInheritedFactory(MatStepLabel)))(t || MatStepLabel);\n  };\n}();\n\nMatStepLabel.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatStepLabel,\n  selectors: [[\"\", \"matStepLabel\", \"\"]],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatStepLabel, [{\n    type: Directive,\n    args: [{\n      selector: '[matStepLabel]'\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Stepper data that is required for internationalization. */\n\n\nclass MatStepperIntl {\n  constructor() {\n    /**\n     * Stream that emits whenever the labels here are changed. Use this to notify\n     * components if the labels have changed after initialization.\n     */\n    this.changes = new Subject();\n    /** Label that is rendered below optional steps. */\n\n    this.optionalLabel = 'Optional';\n    /** Label that is used to indicate step as completed to screen readers. */\n\n    this.completedLabel = 'Completed';\n    /** Label that is used to indicate step as editable to screen readers. */\n\n    this.editableLabel = 'Editable';\n  }\n\n}\n\nMatStepperIntl.ɵfac = function MatStepperIntl_Factory(t) {\n  return new (t || MatStepperIntl)();\n};\n\nMatStepperIntl.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: MatStepperIntl,\n  factory: MatStepperIntl.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatStepperIntl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** @docs-private */\n\n\nfunction MAT_STEPPER_INTL_PROVIDER_FACTORY(parentIntl) {\n  return parentIntl || new MatStepperIntl();\n}\n/** @docs-private */\n\n\nconst MAT_STEPPER_INTL_PROVIDER = {\n  provide: MatStepperIntl,\n  deps: [[new Optional(), new SkipSelf(), MatStepperIntl]],\n  useFactory: MAT_STEPPER_INTL_PROVIDER_FACTORY\n};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatStepHeader.\n\n/** @docs-private */\n\nconst _MatStepHeaderBase = mixinColor(class MatStepHeaderBase extends CdkStepHeader {\n  constructor(elementRef) {\n    super(elementRef);\n  }\n\n}, 'primary');\n\nclass MatStepHeader extends _MatStepHeaderBase {\n  constructor(_intl, _focusMonitor, _elementRef, changeDetectorRef) {\n    super(_elementRef);\n    this._intl = _intl;\n    this._focusMonitor = _focusMonitor;\n    this._intlSubscription = _intl.changes.subscribe(() => changeDetectorRef.markForCheck());\n  }\n\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._elementRef, true);\n  }\n\n  ngOnDestroy() {\n    this._intlSubscription.unsubscribe();\n\n    this._focusMonitor.stopMonitoring(this._elementRef);\n  }\n  /** Focuses the step header. */\n\n\n  focus(origin, options) {\n    if (origin) {\n      this._focusMonitor.focusVia(this._elementRef, origin, options);\n    } else {\n      this._elementRef.nativeElement.focus(options);\n    }\n  }\n  /** Returns string label of given step if it is a text label. */\n\n\n  _stringLabel() {\n    return this.label instanceof MatStepLabel ? null : this.label;\n  }\n  /** Returns MatStepLabel if the label of given step is a template label. */\n\n\n  _templateLabel() {\n    return this.label instanceof MatStepLabel ? this.label : null;\n  }\n  /** Returns the host HTML element. */\n\n\n  _getHostElement() {\n    return this._elementRef.nativeElement;\n  }\n  /** Template context variables that are exposed to the `matStepperIcon` instances. */\n\n\n  _getIconContext() {\n    return {\n      index: this.index,\n      active: this.active,\n      optional: this.optional\n    };\n  }\n\n  _getDefaultTextForState(state) {\n    if (state == 'number') {\n      return `${this.index + 1}`;\n    }\n\n    if (state == 'edit') {\n      return 'create';\n    }\n\n    if (state == 'error') {\n      return 'warning';\n    }\n\n    return state;\n  }\n\n}\n\nMatStepHeader.ɵfac = function MatStepHeader_Factory(t) {\n  return new (t || MatStepHeader)(i0.ɵɵdirectiveInject(MatStepperIntl), i0.ɵɵdirectiveInject(i2.FocusMonitor), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nMatStepHeader.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatStepHeader,\n  selectors: [[\"mat-step-header\"]],\n  hostAttrs: [\"role\", \"tab\", 1, \"mat-step-header\"],\n  inputs: {\n    color: \"color\",\n    state: \"state\",\n    label: \"label\",\n    errorMessage: \"errorMessage\",\n    iconOverrides: \"iconOverrides\",\n    index: \"index\",\n    selected: \"selected\",\n    active: \"active\",\n    optional: \"optional\",\n    disableRipple: \"disableRipple\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 10,\n  vars: 19,\n  consts: [[\"matRipple\", \"\", 1, \"mat-step-header-ripple\", \"mat-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [1, \"mat-step-icon-content\", 3, \"ngSwitch\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\", 4, \"ngSwitchCase\"], [3, \"ngSwitch\", 4, \"ngSwitchDefault\"], [1, \"mat-step-label\"], [\"class\", \"mat-step-text-label\", 4, \"ngIf\"], [\"class\", \"mat-step-optional\", 4, \"ngIf\"], [\"class\", \"mat-step-sub-label-error\", 4, \"ngIf\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"ngSwitch\"], [\"aria-hidden\", \"true\", 4, \"ngSwitchCase\"], [\"class\", \"cdk-visually-hidden\", 4, \"ngIf\"], [\"aria-hidden\", \"true\", 4, \"ngSwitchDefault\"], [\"aria-hidden\", \"true\"], [1, \"cdk-visually-hidden\"], [1, \"mat-step-text-label\"], [3, \"ngTemplateOutlet\"], [1, \"mat-step-optional\"], [1, \"mat-step-sub-label-error\"]],\n  template: function MatStepHeader_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"div\", 0);\n      i0.ɵɵelementStart(1, \"div\")(2, \"div\", 1);\n      i0.ɵɵtemplate(3, MatStepHeader_ng_container_3_Template, 1, 2, \"ng-container\", 2);\n      i0.ɵɵtemplate(4, MatStepHeader_ng_container_4_Template, 5, 4, \"ng-container\", 3);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(5, \"div\", 4);\n      i0.ɵɵtemplate(6, MatStepHeader_div_6_Template, 2, 1, \"div\", 5);\n      i0.ɵɵtemplate(7, MatStepHeader_div_7_Template, 2, 1, \"div\", 5);\n      i0.ɵɵtemplate(8, MatStepHeader_div_8_Template, 2, 1, \"div\", 6);\n      i0.ɵɵtemplate(9, MatStepHeader_div_9_Template, 2, 1, \"div\", 7);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"matRippleTrigger\", ctx._getHostElement())(\"matRippleDisabled\", ctx.disableRipple);\n      i0.ɵɵadvance(1);\n      i0.ɵɵclassMapInterpolate1(\"mat-step-icon-state-\", ctx.state, \" mat-step-icon\");\n      i0.ɵɵclassProp(\"mat-step-icon-selected\", ctx.selected);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngSwitch\", !!(ctx.iconOverrides && ctx.iconOverrides[ctx.state]));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngSwitchCase\", true);\n      i0.ɵɵadvance(2);\n      i0.ɵɵclassProp(\"mat-step-label-active\", ctx.active)(\"mat-step-label-selected\", ctx.selected)(\"mat-step-label-error\", ctx.state == \"error\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx._templateLabel());\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx._stringLabel());\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.optional && ctx.state != \"error\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.state == \"error\");\n    }\n  },\n  dependencies: [i3.NgIf, i3.NgTemplateOutlet, i3.NgSwitch, i3.NgSwitchCase, i3.NgSwitchDefault, i4.MatIcon, i1.MatRipple],\n  styles: [\".mat-step-header{overflow:hidden;outline:none;cursor:pointer;position:relative;box-sizing:content-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-step-header:focus .mat-focus-indicator::before{content:\\\"\\\"}.cdk-high-contrast-active .mat-step-header{outline:solid 1px}.cdk-high-contrast-active .mat-step-header[aria-selected=true] .mat-step-label{text-decoration:underline}.cdk-high-contrast-active .mat-step-header[aria-disabled=true]{outline-color:GrayText}.cdk-high-contrast-active .mat-step-header[aria-disabled=true] .mat-step-label,.cdk-high-contrast-active .mat-step-header[aria-disabled=true] .mat-step-icon,.cdk-high-contrast-active .mat-step-header[aria-disabled=true] .mat-step-optional{color:GrayText}.mat-step-optional,.mat-step-sub-label-error{font-size:12px}.mat-step-icon{border-radius:50%;height:24px;width:24px;flex-shrink:0;position:relative}.mat-step-icon-content{position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);display:flex}.mat-step-icon .mat-icon{font-size:16px;height:16px;width:16px}.mat-step-icon-state-error .mat-icon{font-size:24px;height:24px;width:24px}.mat-step-label{display:inline-block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;min-width:50px;vertical-align:middle}.mat-step-text-label{text-overflow:ellipsis;overflow:hidden}.mat-step-header .mat-step-header-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatStepHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-step-header',\n      inputs: ['color'],\n      host: {\n        'class': 'mat-step-header',\n        'role': 'tab'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<div class=\\\"mat-step-header-ripple mat-focus-indicator\\\" matRipple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\"\\n     [matRippleDisabled]=\\\"disableRipple\\\"></div>\\n\\n<div class=\\\"mat-step-icon-state-{{state}} mat-step-icon\\\" [class.mat-step-icon-selected]=\\\"selected\\\">\\n  <div class=\\\"mat-step-icon-content\\\" [ngSwitch]=\\\"!!(iconOverrides && iconOverrides[state])\\\">\\n    <ng-container\\n      *ngSwitchCase=\\\"true\\\"\\n      [ngTemplateOutlet]=\\\"iconOverrides[state]\\\"\\n      [ngTemplateOutletContext]=\\\"_getIconContext()\\\"></ng-container>\\n    <ng-container *ngSwitchDefault [ngSwitch]=\\\"state\\\">\\n      <span aria-hidden=\\\"true\\\" *ngSwitchCase=\\\"'number'\\\">{{_getDefaultTextForState(state)}}</span>\\n      <span class=\\\"cdk-visually-hidden\\\" *ngIf=\\\"state === 'done'\\\">{{_intl.completedLabel}}</span>\\n      <span class=\\\"cdk-visually-hidden\\\" *ngIf=\\\"state === 'edit'\\\">{{_intl.editableLabel}}</span>\\n      <mat-icon aria-hidden=\\\"true\\\" *ngSwitchDefault>{{_getDefaultTextForState(state)}}</mat-icon>\\n    </ng-container>\\n  </div>\\n</div>\\n<div class=\\\"mat-step-label\\\"\\n     [class.mat-step-label-active]=\\\"active\\\"\\n     [class.mat-step-label-selected]=\\\"selected\\\"\\n     [class.mat-step-label-error]=\\\"state == 'error'\\\">\\n  <!-- If there is a label template, use it. -->\\n  <div class=\\\"mat-step-text-label\\\" *ngIf=\\\"_templateLabel()\\\">\\n    <ng-container [ngTemplateOutlet]=\\\"_templateLabel()!.template\\\"></ng-container>\\n  </div>\\n  <!-- If there is no label template, fall back to the text label. -->\\n  <div class=\\\"mat-step-text-label\\\" *ngIf=\\\"_stringLabel()\\\">{{label}}</div>\\n\\n  <div class=\\\"mat-step-optional\\\" *ngIf=\\\"optional && state != 'error'\\\">{{_intl.optionalLabel}}</div>\\n  <div class=\\\"mat-step-sub-label-error\\\" *ngIf=\\\"state == 'error'\\\">{{errorMessage}}</div>\\n</div>\\n\\n\",\n      styles: [\".mat-step-header{overflow:hidden;outline:none;cursor:pointer;position:relative;box-sizing:content-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-step-header:focus .mat-focus-indicator::before{content:\\\"\\\"}.cdk-high-contrast-active .mat-step-header{outline:solid 1px}.cdk-high-contrast-active .mat-step-header[aria-selected=true] .mat-step-label{text-decoration:underline}.cdk-high-contrast-active .mat-step-header[aria-disabled=true]{outline-color:GrayText}.cdk-high-contrast-active .mat-step-header[aria-disabled=true] .mat-step-label,.cdk-high-contrast-active .mat-step-header[aria-disabled=true] .mat-step-icon,.cdk-high-contrast-active .mat-step-header[aria-disabled=true] .mat-step-optional{color:GrayText}.mat-step-optional,.mat-step-sub-label-error{font-size:12px}.mat-step-icon{border-radius:50%;height:24px;width:24px;flex-shrink:0;position:relative}.mat-step-icon-content{position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);display:flex}.mat-step-icon .mat-icon{font-size:16px;height:16px;width:16px}.mat-step-icon-state-error .mat-icon{font-size:24px;height:24px;width:24px}.mat-step-label{display:inline-block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;min-width:50px;vertical-align:middle}.mat-step-text-label{text-overflow:ellipsis;overflow:hidden}.mat-step-header .mat-step-header-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"]\n    }]\n  }], function () {\n    return [{\n      type: MatStepperIntl\n    }, {\n      type: i2.FocusMonitor\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    state: [{\n      type: Input\n    }],\n    label: [{\n      type: Input\n    }],\n    errorMessage: [{\n      type: Input\n    }],\n    iconOverrides: [{\n      type: Input\n    }],\n    index: [{\n      type: Input\n    }],\n    selected: [{\n      type: Input\n    }],\n    active: [{\n      type: Input\n    }],\n    optional: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst DEFAULT_HORIZONTAL_ANIMATION_DURATION = '500ms';\nconst DEFAULT_VERTICAL_ANIMATION_DURATION = '225ms';\n/**\n * Animations used by the Material steppers.\n * @docs-private\n */\n\nconst matStepperAnimations = {\n  /** Animation that transitions the step along the X axis in a horizontal stepper. */\n  horizontalStepTransition: trigger('horizontalStepTransition', [state('previous', style({\n    transform: 'translate3d(-100%, 0, 0)',\n    visibility: 'hidden'\n  })), // Transition to `inherit`, rather than `visible`,\n  // because visibility on a child element the one from the parent,\n  // making this element focusable inside of a `hidden` element.\n  state('current', style({\n    transform: 'none',\n    visibility: 'inherit'\n  })), state('next', style({\n    transform: 'translate3d(100%, 0, 0)',\n    visibility: 'hidden'\n  })), transition('* => *', animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'), {\n    params: {\n      'animationDuration': DEFAULT_HORIZONTAL_ANIMATION_DURATION\n    }\n  })]),\n\n  /** Animation that transitions the step along the Y axis in a vertical stepper. */\n  verticalStepTransition: trigger('verticalStepTransition', [state('previous', style({\n    height: '0px',\n    visibility: 'hidden'\n  })), state('next', style({\n    height: '0px',\n    visibility: 'hidden'\n  })), // Transition to `inherit`, rather than `visible`,\n  // because visibility on a child element the one from the parent,\n  // making this element focusable inside of a `hidden` element.\n  state('current', style({\n    height: '*',\n    visibility: 'inherit'\n  })), transition('* <=> current', animate('{{animationDuration}} cubic-bezier(0.4, 0.0, 0.2, 1)'), {\n    params: {\n      'animationDuration': DEFAULT_VERTICAL_ANIMATION_DURATION\n    }\n  })])\n};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Template to be used to override the icons inside the step header.\n */\n\nclass MatStepperIcon {\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n  }\n\n}\n\nMatStepperIcon.ɵfac = function MatStepperIcon_Factory(t) {\n  return new (t || MatStepperIcon)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\n\nMatStepperIcon.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatStepperIcon,\n  selectors: [[\"ng-template\", \"matStepperIcon\", \"\"]],\n  inputs: {\n    name: [\"matStepperIcon\", \"name\"]\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatStepperIcon, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[matStepperIcon]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, {\n    name: [{\n      type: Input,\n      args: ['matStepperIcon']\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Content for a `mat-step` that will be rendered lazily.\n */\n\n\nclass MatStepContent {\n  constructor(_template) {\n    this._template = _template;\n  }\n\n}\n\nMatStepContent.ɵfac = function MatStepContent_Factory(t) {\n  return new (t || MatStepContent)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\n\nMatStepContent.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatStepContent,\n  selectors: [[\"ng-template\", \"matStepContent\", \"\"]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatStepContent, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[matStepContent]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatStep extends CdkStep {\n  constructor(stepper, _errorStateMatcher, _viewContainerRef, stepperOptions) {\n    super(stepper, stepperOptions);\n    this._errorStateMatcher = _errorStateMatcher;\n    this._viewContainerRef = _viewContainerRef;\n    this._isSelected = Subscription.EMPTY;\n  }\n\n  ngAfterContentInit() {\n    this._isSelected = this._stepper.steps.changes.pipe(switchMap(() => {\n      return this._stepper.selectionChange.pipe(map(event => event.selectedStep === this), startWith(this._stepper.selected === this));\n    })).subscribe(isSelected => {\n      if (isSelected && this._lazyContent && !this._portal) {\n        this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef);\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    this._isSelected.unsubscribe();\n  }\n  /** Custom error state matcher that additionally checks for validity of interacted form. */\n\n\n  isErrorState(control, form) {\n    const originalErrorState = this._errorStateMatcher.isErrorState(control, form); // Custom error state checks for the validity of form that is not submitted or touched\n    // since user can trigger a form change by calling for another step without directly\n    // interacting with the current form.\n\n\n    const customErrorState = !!(control && control.invalid && this.interacted);\n    return originalErrorState || customErrorState;\n  }\n\n}\n\nMatStep.ɵfac = function MatStep_Factory(t) {\n  return new (t || MatStep)(i0.ɵɵdirectiveInject(forwardRef(() => MatStepper)), i0.ɵɵdirectiveInject(i1.ErrorStateMatcher, 4), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(STEPPER_GLOBAL_OPTIONS, 8));\n};\n\nMatStep.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatStep,\n  selectors: [[\"mat-step\"]],\n  contentQueries: function MatStep_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatStepLabel, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatStepContent, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.stepLabel = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lazyContent = _t.first);\n    }\n  },\n  inputs: {\n    color: \"color\"\n  },\n  exportAs: [\"matStep\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: ErrorStateMatcher,\n    useExisting: MatStep\n  }, {\n    provide: CdkStep,\n    useExisting: MatStep\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  consts: [[3, \"cdkPortalOutlet\"]],\n  template: function MatStep_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵtemplate(0, MatStep_ng_template_0_Template, 2, 1, \"ng-template\");\n    }\n  },\n  dependencies: [i2$1.CdkPortalOutlet],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatStep, [{\n    type: Component,\n    args: [{\n      selector: 'mat-step',\n      providers: [{\n        provide: ErrorStateMatcher,\n        useExisting: MatStep\n      }, {\n        provide: CdkStep,\n        useExisting: MatStep\n      }],\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matStep',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<ng-template>\\n  <ng-content></ng-content>\\n  <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n</ng-template>\\n\"\n    }]\n  }], function () {\n    return [{\n      type: MatStepper,\n      decorators: [{\n        type: Inject,\n        args: [forwardRef(() => MatStepper)]\n      }]\n    }, {\n      type: i1.ErrorStateMatcher,\n      decorators: [{\n        type: SkipSelf\n      }]\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [STEPPER_GLOBAL_OPTIONS]\n      }]\n    }];\n  }, {\n    stepLabel: [{\n      type: ContentChild,\n      args: [MatStepLabel]\n    }],\n    color: [{\n      type: Input\n    }],\n    _lazyContent: [{\n      type: ContentChild,\n      args: [MatStepContent, {\n        static: false\n      }]\n    }]\n  });\n})();\n\nclass MatStepper extends CdkStepper {\n  constructor(dir, changeDetectorRef, elementRef) {\n    super(dir, changeDetectorRef, elementRef);\n    /** Steps that belong to the current stepper, excluding ones from nested steppers. */\n\n    this.steps = new QueryList();\n    /** Event emitted when the current step is done transitioning in. */\n\n    this.animationDone = new EventEmitter();\n    /**\n     * Whether the label should display in bottom or end position.\n     * Only applies in the `horizontal` orientation.\n     */\n\n    this.labelPosition = 'end';\n    /**\n     * Position of the stepper's header.\n     * Only applies in the `horizontal` orientation.\n     */\n\n    this.headerPosition = 'top';\n    /** Consumer-specified template-refs to be used to override the header icons. */\n\n    this._iconOverrides = {};\n    /** Stream of animation `done` events when the body expands/collapses. */\n\n    this._animationDone = new Subject();\n    this._animationDuration = '';\n    const nodeName = elementRef.nativeElement.nodeName.toLowerCase();\n    this.orientation = nodeName === 'mat-vertical-stepper' ? 'vertical' : 'horizontal';\n  }\n  /** Duration for the animation. Will be normalized to milliseconds if no units are set. */\n\n\n  get animationDuration() {\n    return this._animationDuration;\n  }\n\n  set animationDuration(value) {\n    this._animationDuration = /^\\d+$/.test(value) ? value + 'ms' : value;\n  }\n\n  ngAfterContentInit() {\n    super.ngAfterContentInit();\n\n    this._icons.forEach(({\n      name,\n      templateRef\n    }) => this._iconOverrides[name] = templateRef); // Mark the component for change detection whenever the content children query changes\n\n\n    this.steps.changes.pipe(takeUntil(this._destroyed)).subscribe(() => {\n      this._stateChanged();\n    });\n\n    this._animationDone.pipe( // This needs a `distinctUntilChanged` in order to avoid emitting the same event twice due\n    // to a bug in animations where the `.done` callback gets invoked twice on some browsers.\n    // See https://github.com/angular/angular/issues/24084\n    distinctUntilChanged((x, y) => x.fromState === y.fromState && x.toState === y.toState), takeUntil(this._destroyed)).subscribe(event => {\n      if (event.toState === 'current') {\n        this.animationDone.emit();\n      }\n    });\n  }\n\n  _stepIsNavigable(index, step) {\n    return step.completed || this.selectedIndex === index || !this.linear;\n  }\n\n  _getAnimationDuration() {\n    if (this.animationDuration) {\n      return this.animationDuration;\n    }\n\n    return this.orientation === 'horizontal' ? DEFAULT_HORIZONTAL_ANIMATION_DURATION : DEFAULT_VERTICAL_ANIMATION_DURATION;\n  }\n\n}\n\nMatStepper.ɵfac = function MatStepper_Factory(t) {\n  return new (t || MatStepper)(i0.ɵɵdirectiveInject(i3$1.Directionality, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\nMatStepper.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatStepper,\n  selectors: [[\"mat-stepper\"], [\"mat-vertical-stepper\"], [\"mat-horizontal-stepper\"], [\"\", \"matStepper\", \"\"]],\n  contentQueries: function MatStepper_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatStep, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatStepperIcon, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._steps = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._icons = _t);\n    }\n  },\n  viewQuery: function MatStepper_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(MatStepHeader, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._stepHeader = _t);\n    }\n  },\n  hostAttrs: [\"role\", \"tablist\"],\n  hostVars: 11,\n  hostBindings: function MatStepper_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-orientation\", ctx.orientation);\n      i0.ɵɵclassProp(\"mat-stepper-horizontal\", ctx.orientation === \"horizontal\")(\"mat-stepper-vertical\", ctx.orientation === \"vertical\")(\"mat-stepper-label-position-end\", ctx.orientation === \"horizontal\" && ctx.labelPosition == \"end\")(\"mat-stepper-label-position-bottom\", ctx.orientation === \"horizontal\" && ctx.labelPosition == \"bottom\")(\"mat-stepper-header-position-bottom\", ctx.headerPosition === \"bottom\");\n    }\n  },\n  inputs: {\n    selectedIndex: \"selectedIndex\",\n    disableRipple: \"disableRipple\",\n    color: \"color\",\n    labelPosition: \"labelPosition\",\n    headerPosition: \"headerPosition\",\n    animationDuration: \"animationDuration\"\n  },\n  outputs: {\n    animationDone: \"animationDone\"\n  },\n  exportAs: [\"matStepper\", \"matVerticalStepper\", \"matHorizontalStepper\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkStepper,\n    useExisting: MatStepper\n  }]), i0.ɵɵInheritDefinitionFeature],\n  decls: 5,\n  vars: 3,\n  consts: [[3, \"ngSwitch\"], [\"class\", \"mat-horizontal-stepper-wrapper\", 4, \"ngSwitchCase\"], [4, \"ngSwitchCase\"], [\"stepTemplate\", \"\"], [1, \"mat-horizontal-stepper-wrapper\"], [1, \"mat-horizontal-stepper-header-container\"], [4, \"ngFor\", \"ngForOf\"], [1, \"mat-horizontal-content-container\"], [\"class\", \"mat-horizontal-stepper-content\", \"role\", \"tabpanel\", 3, \"id\", \"mat-horizontal-stepper-content-inactive\", 4, \"ngFor\", \"ngForOf\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"mat-stepper-horizontal-line\", 4, \"ngIf\"], [1, \"mat-stepper-horizontal-line\"], [\"role\", \"tabpanel\", 1, \"mat-horizontal-stepper-content\", 3, \"id\"], [3, \"ngTemplateOutlet\"], [\"class\", \"mat-step\", 4, \"ngFor\", \"ngForOf\"], [1, \"mat-step\"], [1, \"mat-vertical-content-container\"], [\"role\", \"tabpanel\", 1, \"mat-vertical-stepper-content\", 3, \"id\"], [1, \"mat-vertical-content\"], [3, \"tabIndex\", \"id\", \"index\", \"state\", \"label\", \"selected\", \"active\", \"optional\", \"errorMessage\", \"iconOverrides\", \"disableRipple\", \"color\", \"click\", \"keydown\"]],\n  template: function MatStepper_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementContainerStart(0, 0);\n      i0.ɵɵtemplate(1, MatStepper_div_1_Template, 5, 2, \"div\", 1);\n      i0.ɵɵtemplate(2, MatStepper_ng_container_2_Template, 2, 1, \"ng-container\", 2);\n      i0.ɵɵelementContainerEnd();\n      i0.ɵɵtemplate(3, MatStepper_ng_template_3_Template, 1, 23, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngSwitch\", ctx.orientation);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngSwitchCase\", \"horizontal\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngSwitchCase\", \"vertical\");\n    }\n  },\n  dependencies: [i3.NgForOf, i3.NgIf, i3.NgTemplateOutlet, i3.NgSwitch, i3.NgSwitchCase, MatStepHeader],\n  styles: [\".mat-stepper-vertical,.mat-stepper-horizontal{display:block}.mat-horizontal-stepper-header-container{white-space:nowrap;display:flex;align-items:center}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header-container{align-items:flex-start}.mat-stepper-header-position-bottom .mat-horizontal-stepper-header-container{order:1}.mat-stepper-horizontal-line{border-top-width:1px;border-top-style:solid;flex:auto;height:0;margin:0 -16px;min-width:32px}.mat-stepper-label-position-bottom .mat-stepper-horizontal-line{margin:0;min-width:0;position:relative}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::before,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::before,.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::after,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::after{border-top-width:1px;border-top-style:solid;content:\\\"\\\";display:inline-block;height:0;position:absolute;width:calc(50% - 20px)}.mat-horizontal-stepper-header{display:flex;height:72px;overflow:hidden;align-items:center;padding:0 24px}.mat-horizontal-stepper-header .mat-step-icon{margin-right:8px;flex:none}[dir=rtl] .mat-horizontal-stepper-header .mat-step-icon{margin-right:0;margin-left:8px}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header{box-sizing:border-box;flex-direction:column;height:auto}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::after,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::after{right:0}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::before,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::before{left:0}[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:last-child::before,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:first-child::after{display:none}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header .mat-step-icon{margin-right:0;margin-left:0}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header .mat-step-label{padding:16px 0 0 0;text-align:center;width:100%}.mat-vertical-stepper-header{display:flex;align-items:center;height:24px}.mat-vertical-stepper-header .mat-step-icon{margin-right:12px}[dir=rtl] .mat-vertical-stepper-header .mat-step-icon{margin-right:0;margin-left:12px}.mat-horizontal-stepper-wrapper{display:flex;flex-direction:column}.mat-horizontal-stepper-content{outline:0}.mat-horizontal-stepper-content.mat-horizontal-stepper-content-inactive{height:0;overflow:hidden}.mat-horizontal-stepper-content:not(.mat-horizontal-stepper-content-inactive){visibility:inherit !important}.mat-horizontal-content-container{overflow:hidden;padding:0 24px 24px 24px}.cdk-high-contrast-active .mat-horizontal-content-container{outline:solid 1px}.mat-stepper-header-position-bottom .mat-horizontal-content-container{padding:24px 24px 0 24px}.mat-vertical-content-container{margin-left:36px;border:0;position:relative}.cdk-high-contrast-active .mat-vertical-content-container{outline:solid 1px}[dir=rtl] .mat-vertical-content-container{margin-left:0;margin-right:36px}.mat-stepper-vertical-line::before{content:\\\"\\\";position:absolute;left:0;border-left-width:1px;border-left-style:solid}[dir=rtl] .mat-stepper-vertical-line::before{left:auto;right:0}.mat-vertical-stepper-content{overflow:hidden;outline:0}.mat-vertical-stepper-content:not(.mat-vertical-stepper-content-inactive){visibility:inherit !important}.mat-vertical-content{padding:0 24px 24px 24px}.mat-step:last-child .mat-vertical-content-container{border:none}\"],\n  encapsulation: 2,\n  data: {\n    animation: [matStepperAnimations.horizontalStepTransition, matStepperAnimations.verticalStepTransition]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatStepper, [{\n    type: Component,\n    args: [{\n      selector: 'mat-stepper, mat-vertical-stepper, mat-horizontal-stepper, [matStepper]',\n      exportAs: 'matStepper, matVerticalStepper, matHorizontalStepper',\n      inputs: ['selectedIndex'],\n      host: {\n        '[class.mat-stepper-horizontal]': 'orientation === \"horizontal\"',\n        '[class.mat-stepper-vertical]': 'orientation === \"vertical\"',\n        '[class.mat-stepper-label-position-end]': 'orientation === \"horizontal\" && labelPosition == \"end\"',\n        '[class.mat-stepper-label-position-bottom]': 'orientation === \"horizontal\" && labelPosition == \"bottom\"',\n        '[class.mat-stepper-header-position-bottom]': 'headerPosition === \"bottom\"',\n        '[attr.aria-orientation]': 'orientation',\n        'role': 'tablist'\n      },\n      animations: [matStepperAnimations.horizontalStepTransition, matStepperAnimations.verticalStepTransition],\n      providers: [{\n        provide: CdkStepper,\n        useExisting: MatStepper\n      }],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<ng-container [ngSwitch]=\\\"orientation\\\">\\n  <!-- Horizontal stepper -->\\n  <div class=\\\"mat-horizontal-stepper-wrapper\\\" *ngSwitchCase=\\\"'horizontal'\\\">\\n    <div class=\\\"mat-horizontal-stepper-header-container\\\">\\n      <ng-container *ngFor=\\\"let step of steps; let i = index; let isLast = last\\\">\\n        <ng-container\\n          [ngTemplateOutlet]=\\\"stepTemplate\\\"\\n          [ngTemplateOutletContext]=\\\"{step: step, i: i}\\\"></ng-container>\\n        <div *ngIf=\\\"!isLast\\\" class=\\\"mat-stepper-horizontal-line\\\"></div>\\n      </ng-container>\\n    </div>\\n\\n    <div class=\\\"mat-horizontal-content-container\\\">\\n      <div *ngFor=\\\"let step of steps; let i = index\\\"\\n           class=\\\"mat-horizontal-stepper-content\\\" role=\\\"tabpanel\\\"\\n           [@horizontalStepTransition]=\\\"{\\n              'value': _getAnimationDirection(i),\\n              'params': {'animationDuration': _getAnimationDuration()}\\n            }\\\"\\n           (@horizontalStepTransition.done)=\\\"_animationDone.next($event)\\\"\\n           [id]=\\\"_getStepContentId(i)\\\"\\n           [attr.aria-labelledby]=\\\"_getStepLabelId(i)\\\"\\n           [class.mat-horizontal-stepper-content-inactive]=\\\"selectedIndex !== i\\\">\\n        <ng-container [ngTemplateOutlet]=\\\"step.content\\\"></ng-container>\\n      </div>\\n    </div>\\n  </div>\\n\\n  <!-- Vertical stepper -->\\n  <ng-container *ngSwitchCase=\\\"'vertical'\\\">\\n    <div class=\\\"mat-step\\\" *ngFor=\\\"let step of steps; let i = index; let isLast = last\\\">\\n      <ng-container\\n        [ngTemplateOutlet]=\\\"stepTemplate\\\"\\n        [ngTemplateOutletContext]=\\\"{step: step, i: i}\\\"></ng-container>\\n      <div class=\\\"mat-vertical-content-container\\\" [class.mat-stepper-vertical-line]=\\\"!isLast\\\">\\n        <div class=\\\"mat-vertical-stepper-content\\\" role=\\\"tabpanel\\\"\\n             [@verticalStepTransition]=\\\"{\\n                'value': _getAnimationDirection(i),\\n                'params': {'animationDuration': _getAnimationDuration()}\\n              }\\\"\\n             (@verticalStepTransition.done)=\\\"_animationDone.next($event)\\\"\\n             [id]=\\\"_getStepContentId(i)\\\"\\n             [attr.aria-labelledby]=\\\"_getStepLabelId(i)\\\"\\n             [class.mat-vertical-stepper-content-inactive]=\\\"selectedIndex !== i\\\">\\n          <div class=\\\"mat-vertical-content\\\">\\n            <ng-container [ngTemplateOutlet]=\\\"step.content\\\"></ng-container>\\n          </div>\\n        </div>\\n      </div>\\n    </div>\\n  </ng-container>\\n\\n</ng-container>\\n\\n<!-- Common step templating -->\\n<ng-template let-step=\\\"step\\\" let-i=\\\"i\\\" #stepTemplate>\\n  <mat-step-header\\n    [class.mat-horizontal-stepper-header]=\\\"orientation === 'horizontal'\\\"\\n    [class.mat-vertical-stepper-header]=\\\"orientation === 'vertical'\\\"\\n    (click)=\\\"step.select()\\\"\\n    (keydown)=\\\"_onKeydown($event)\\\"\\n    [tabIndex]=\\\"_getFocusIndex() === i ? 0 : -1\\\"\\n    [id]=\\\"_getStepLabelId(i)\\\"\\n    [attr.aria-posinset]=\\\"i + 1\\\"\\n    [attr.aria-setsize]=\\\"steps.length\\\"\\n    [attr.aria-controls]=\\\"_getStepContentId(i)\\\"\\n    [attr.aria-selected]=\\\"selectedIndex == i\\\"\\n    [attr.aria-label]=\\\"step.ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"(!step.ariaLabel && step.ariaLabelledby) ? step.ariaLabelledby : null\\\"\\n    [attr.aria-disabled]=\\\"_stepIsNavigable(i, step) ? null : true\\\"\\n    [index]=\\\"i\\\"\\n    [state]=\\\"_getIndicatorType(i, step.state)\\\"\\n    [label]=\\\"step.stepLabel || step.label\\\"\\n    [selected]=\\\"selectedIndex === i\\\"\\n    [active]=\\\"_stepIsNavigable(i, step)\\\"\\n    [optional]=\\\"step.optional\\\"\\n    [errorMessage]=\\\"step.errorMessage\\\"\\n    [iconOverrides]=\\\"_iconOverrides\\\"\\n    [disableRipple]=\\\"disableRipple || !_stepIsNavigable(i, step)\\\"\\n    [color]=\\\"step.color || color\\\"></mat-step-header>\\n</ng-template>\\n\",\n      styles: [\".mat-stepper-vertical,.mat-stepper-horizontal{display:block}.mat-horizontal-stepper-header-container{white-space:nowrap;display:flex;align-items:center}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header-container{align-items:flex-start}.mat-stepper-header-position-bottom .mat-horizontal-stepper-header-container{order:1}.mat-stepper-horizontal-line{border-top-width:1px;border-top-style:solid;flex:auto;height:0;margin:0 -16px;min-width:32px}.mat-stepper-label-position-bottom .mat-stepper-horizontal-line{margin:0;min-width:0;position:relative}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::before,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::before,.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::after,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::after{border-top-width:1px;border-top-style:solid;content:\\\"\\\";display:inline-block;height:0;position:absolute;width:calc(50% - 20px)}.mat-horizontal-stepper-header{display:flex;height:72px;overflow:hidden;align-items:center;padding:0 24px}.mat-horizontal-stepper-header .mat-step-icon{margin-right:8px;flex:none}[dir=rtl] .mat-horizontal-stepper-header .mat-step-icon{margin-right:0;margin-left:8px}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header{box-sizing:border-box;flex-direction:column;height:auto}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::after,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::after{right:0}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::before,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::before{left:0}[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:last-child::before,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:first-child::after{display:none}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header .mat-step-icon{margin-right:0;margin-left:0}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header .mat-step-label{padding:16px 0 0 0;text-align:center;width:100%}.mat-vertical-stepper-header{display:flex;align-items:center;height:24px}.mat-vertical-stepper-header .mat-step-icon{margin-right:12px}[dir=rtl] .mat-vertical-stepper-header .mat-step-icon{margin-right:0;margin-left:12px}.mat-horizontal-stepper-wrapper{display:flex;flex-direction:column}.mat-horizontal-stepper-content{outline:0}.mat-horizontal-stepper-content.mat-horizontal-stepper-content-inactive{height:0;overflow:hidden}.mat-horizontal-stepper-content:not(.mat-horizontal-stepper-content-inactive){visibility:inherit !important}.mat-horizontal-content-container{overflow:hidden;padding:0 24px 24px 24px}.cdk-high-contrast-active .mat-horizontal-content-container{outline:solid 1px}.mat-stepper-header-position-bottom .mat-horizontal-content-container{padding:24px 24px 0 24px}.mat-vertical-content-container{margin-left:36px;border:0;position:relative}.cdk-high-contrast-active .mat-vertical-content-container{outline:solid 1px}[dir=rtl] .mat-vertical-content-container{margin-left:0;margin-right:36px}.mat-stepper-vertical-line::before{content:\\\"\\\";position:absolute;left:0;border-left-width:1px;border-left-style:solid}[dir=rtl] .mat-stepper-vertical-line::before{left:auto;right:0}.mat-vertical-stepper-content{overflow:hidden;outline:0}.mat-vertical-stepper-content:not(.mat-vertical-stepper-content-inactive){visibility:inherit !important}.mat-vertical-content{padding:0 24px 24px 24px}.mat-step:last-child .mat-vertical-content-container{border:none}\"]\n    }]\n  }], function () {\n    return [{\n      type: i3$1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.ElementRef\n    }];\n  }, {\n    _stepHeader: [{\n      type: ViewChildren,\n      args: [MatStepHeader]\n    }],\n    _steps: [{\n      type: ContentChildren,\n      args: [MatStep, {\n        descendants: true\n      }]\n    }],\n    _icons: [{\n      type: ContentChildren,\n      args: [MatStepperIcon, {\n        descendants: true\n      }]\n    }],\n    animationDone: [{\n      type: Output\n    }],\n    disableRipple: [{\n      type: Input\n    }],\n    color: [{\n      type: Input\n    }],\n    labelPosition: [{\n      type: Input\n    }],\n    headerPosition: [{\n      type: Input\n    }],\n    animationDuration: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Button that moves to the next step in a stepper workflow. */\n\n\nclass MatStepperNext extends CdkStepperNext {}\n\nMatStepperNext.ɵfac = /* @__PURE__ */function () {\n  let ɵMatStepperNext_BaseFactory;\n  return function MatStepperNext_Factory(t) {\n    return (ɵMatStepperNext_BaseFactory || (ɵMatStepperNext_BaseFactory = i0.ɵɵgetInheritedFactory(MatStepperNext)))(t || MatStepperNext);\n  };\n}();\n\nMatStepperNext.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatStepperNext,\n  selectors: [[\"button\", \"matStepperNext\", \"\"]],\n  hostAttrs: [1, \"mat-stepper-next\"],\n  hostVars: 1,\n  hostBindings: function MatStepperNext_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵhostProperty(\"type\", ctx.type);\n    }\n  },\n  inputs: {\n    type: \"type\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatStepperNext, [{\n    type: Directive,\n    args: [{\n      selector: 'button[matStepperNext]',\n      host: {\n        'class': 'mat-stepper-next',\n        '[type]': 'type'\n      },\n      inputs: ['type']\n    }]\n  }], null, null);\n})();\n/** Button that moves to the previous step in a stepper workflow. */\n\n\nclass MatStepperPrevious extends CdkStepperPrevious {}\n\nMatStepperPrevious.ɵfac = /* @__PURE__ */function () {\n  let ɵMatStepperPrevious_BaseFactory;\n  return function MatStepperPrevious_Factory(t) {\n    return (ɵMatStepperPrevious_BaseFactory || (ɵMatStepperPrevious_BaseFactory = i0.ɵɵgetInheritedFactory(MatStepperPrevious)))(t || MatStepperPrevious);\n  };\n}();\n\nMatStepperPrevious.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatStepperPrevious,\n  selectors: [[\"button\", \"matStepperPrevious\", \"\"]],\n  hostAttrs: [1, \"mat-stepper-previous\"],\n  hostVars: 1,\n  hostBindings: function MatStepperPrevious_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵhostProperty(\"type\", ctx.type);\n    }\n  },\n  inputs: {\n    type: \"type\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatStepperPrevious, [{\n    type: Directive,\n    args: [{\n      selector: 'button[matStepperPrevious]',\n      host: {\n        'class': 'mat-stepper-previous',\n        '[type]': 'type'\n      },\n      inputs: ['type']\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatStepperModule {}\n\nMatStepperModule.ɵfac = function MatStepperModule_Factory(t) {\n  return new (t || MatStepperModule)();\n};\n\nMatStepperModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatStepperModule,\n  declarations: [MatStep, MatStepLabel, MatStepper, MatStepperNext, MatStepperPrevious, MatStepHeader, MatStepperIcon, MatStepContent],\n  imports: [MatCommonModule, CommonModule, PortalModule, MatButtonModule, CdkStepperModule, MatIconModule, MatRippleModule],\n  exports: [MatCommonModule, MatStep, MatStepLabel, MatStepper, MatStepperNext, MatStepperPrevious, MatStepHeader, MatStepperIcon, MatStepContent]\n});\nMatStepperModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MAT_STEPPER_INTL_PROVIDER, ErrorStateMatcher],\n  imports: [MatCommonModule, CommonModule, PortalModule, MatButtonModule, CdkStepperModule, MatIconModule, MatRippleModule, MatCommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatStepperModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CommonModule, PortalModule, MatButtonModule, CdkStepperModule, MatIconModule, MatRippleModule],\n      exports: [MatCommonModule, MatStep, MatStepLabel, MatStepper, MatStepperNext, MatStepperPrevious, MatStepHeader, MatStepperIcon, MatStepContent],\n      declarations: [MatStep, MatStepLabel, MatStepper, MatStepperNext, MatStepperPrevious, MatStepHeader, MatStepperIcon, MatStepContent],\n      providers: [MAT_STEPPER_INTL_PROVIDER, ErrorStateMatcher]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MAT_STEPPER_INTL_PROVIDER, MAT_STEPPER_INTL_PROVIDER_FACTORY, MatStep, MatStepContent, MatStepHeader, MatStepLabel, MatStepper, MatStepperIcon, MatStepperIntl, MatStepperModule, MatStepperNext, MatStepperPrevious, matStepperAnimations };", "map": {"version": 3, "names": ["i2$1", "TemplatePortal", "PortalModule", "CdkStepLabel", "CdkStepHeader", "CdkStep", "STEPPER_GLOBAL_OPTIONS", "CdkStepper", "CdkStepperNext", "CdkStepperPrevious", "CdkStepperModule", "i3", "CommonModule", "i0", "Directive", "Injectable", "Optional", "SkipSelf", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "forwardRef", "Inject", "ContentChild", "QueryList", "EventEmitter", "ViewChildren", "ContentChildren", "Output", "NgModule", "MatButtonModule", "i1", "mixinColor", "ErrorStateMatcher", "MatCommonModule", "MatRippleModule", "i4", "MatIconModule", "i2", "Subject", "Subscription", "i3$1", "switchMap", "map", "startWith", "takeUntil", "distinctUntilChanged", "trigger", "state", "style", "transition", "animate", "MatStepLabel", "ɵfac", "ɵdir", "type", "args", "selector", "MatStepperIntl", "constructor", "changes", "optionalLabel", "completedLabel", "editable<PERSON><PERSON><PERSON>", "ɵprov", "providedIn", "MAT_STEPPER_INTL_PROVIDER_FACTORY", "parentIntl", "MAT_STEPPER_INTL_PROVIDER", "provide", "deps", "useFactory", "_MatStepHeaderBase", "MatStepHeaderBase", "elementRef", "MatStepHeader", "_intl", "_focusMonitor", "_elementRef", "changeDetectorRef", "_intlSubscription", "subscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngAfterViewInit", "monitor", "ngOnDestroy", "unsubscribe", "stopMonitoring", "focus", "origin", "options", "focusVia", "nativeElement", "_stringLabel", "label", "_templateLabel", "_getHostElement", "_getIconContext", "index", "active", "optional", "_getDefaultTextForState", "FocusMonitor", "ElementRef", "ChangeDetectorRef", "ɵcmp", "NgIf", "NgTemplateOutlet", "NgSwitch", "NgSwitchCase", "NgSwitchDefault", "MatIcon", "<PERSON><PERSON><PERSON><PERSON>", "inputs", "host", "encapsulation", "None", "changeDetection", "OnPush", "template", "styles", "errorMessage", "iconOverrides", "selected", "disable<PERSON><PERSON><PERSON>", "DEFAULT_HORIZONTAL_ANIMATION_DURATION", "DEFAULT_VERTICAL_ANIMATION_DURATION", "matStepperAnimations", "horizontalStepTransition", "transform", "visibility", "params", "verticalStepTransition", "height", "MatStepperIcon", "templateRef", "TemplateRef", "name", "MatStepContent", "_template", "MatStep", "stepper", "_errorStateMatcher", "_viewContainerRef", "stepperOptions", "_isSelected", "EMPTY", "ngAfterContentInit", "_stepper", "steps", "pipe", "selectionChange", "event", "selectedStep", "isSelected", "_lazyContent", "_portal", "isErrorState", "control", "form", "originalErrorState", "customErrorState", "invalid", "interacted", "Mat<PERSON><PERSON><PERSON>", "ViewContainerRef", "useExisting", "CdkPortalOutlet", "providers", "exportAs", "decorators", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "color", "static", "dir", "animationDone", "labelPosition", "headerPosition", "_iconOverrides", "_animationDone", "_animationDuration", "nodeName", "toLowerCase", "orientation", "animationDuration", "value", "test", "_icons", "for<PERSON>ach", "_destroyed", "_stateChanged", "x", "y", "fromState", "toState", "emit", "_stepIsNavigable", "step", "completed", "selectedIndex", "linear", "_getAnimationDuration", "Directionality", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animations", "_step<PERSON><PERSON>er", "_steps", "descendants", "MatStepperNext", "MatStepperPrevious", "MatStepperModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/material/fesm2020/stepper.mjs"], "sourcesContent": ["import * as i2$1 from '@angular/cdk/portal';\nimport { TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { CdkStepLabel, CdkStepHeader, CdkStep, STEPPER_GLOBAL_OPTIONS, CdkStepper, CdkStepperNext, CdkStepperPrevious, CdkStepperModule } from '@angular/cdk/stepper';\nimport * as i3 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, Injectable, Optional, SkipSelf, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, forwardRef, Inject, ContentChild, QueryList, EventEmitter, ViewChildren, ContentChildren, Output, NgModule } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport * as i1 from '@angular/material/core';\nimport { mixinColor, ErrorStateMatcher, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport * as i4 from '@angular/material/icon';\nimport { MatIconModule } from '@angular/material/icon';\nimport * as i2 from '@angular/cdk/a11y';\nimport { Subject, Subscription } from 'rxjs';\nimport * as i3$1 from '@angular/cdk/bidi';\nimport { switchMap, map, startWith, takeUntil, distinctUntilChanged } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatStepLabel extends CdkStepLabel {\n}\nMatStepLabel.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepLabel, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatStepLabel.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatStepLabel, selector: \"[matStepLabel]\", usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matStepLabel]',\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Stepper data that is required for internationalization. */\nclass MatStepperIntl {\n    constructor() {\n        /**\n         * Stream that emits whenever the labels here are changed. Use this to notify\n         * components if the labels have changed after initialization.\n         */\n        this.changes = new Subject();\n        /** Label that is rendered below optional steps. */\n        this.optionalLabel = 'Optional';\n        /** Label that is used to indicate step as completed to screen readers. */\n        this.completedLabel = 'Completed';\n        /** Label that is used to indicate step as editable to screen readers. */\n        this.editableLabel = 'Editable';\n    }\n}\nMatStepperIntl.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepperIntl, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nMatStepperIntl.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepperIntl, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepperIntl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/** @docs-private */\nfunction MAT_STEPPER_INTL_PROVIDER_FACTORY(parentIntl) {\n    return parentIntl || new MatStepperIntl();\n}\n/** @docs-private */\nconst MAT_STEPPER_INTL_PROVIDER = {\n    provide: MatStepperIntl,\n    deps: [[new Optional(), new SkipSelf(), MatStepperIntl]],\n    useFactory: MAT_STEPPER_INTL_PROVIDER_FACTORY,\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatStepHeader.\n/** @docs-private */\nconst _MatStepHeaderBase = mixinColor(class MatStepHeaderBase extends CdkStepHeader {\n    constructor(elementRef) {\n        super(elementRef);\n    }\n}, 'primary');\nclass MatStepHeader extends _MatStepHeaderBase {\n    constructor(_intl, _focusMonitor, _elementRef, changeDetectorRef) {\n        super(_elementRef);\n        this._intl = _intl;\n        this._focusMonitor = _focusMonitor;\n        this._intlSubscription = _intl.changes.subscribe(() => changeDetectorRef.markForCheck());\n    }\n    ngAfterViewInit() {\n        this._focusMonitor.monitor(this._elementRef, true);\n    }\n    ngOnDestroy() {\n        this._intlSubscription.unsubscribe();\n        this._focusMonitor.stopMonitoring(this._elementRef);\n    }\n    /** Focuses the step header. */\n    focus(origin, options) {\n        if (origin) {\n            this._focusMonitor.focusVia(this._elementRef, origin, options);\n        }\n        else {\n            this._elementRef.nativeElement.focus(options);\n        }\n    }\n    /** Returns string label of given step if it is a text label. */\n    _stringLabel() {\n        return this.label instanceof MatStepLabel ? null : this.label;\n    }\n    /** Returns MatStepLabel if the label of given step is a template label. */\n    _templateLabel() {\n        return this.label instanceof MatStepLabel ? this.label : null;\n    }\n    /** Returns the host HTML element. */\n    _getHostElement() {\n        return this._elementRef.nativeElement;\n    }\n    /** Template context variables that are exposed to the `matStepperIcon` instances. */\n    _getIconContext() {\n        return {\n            index: this.index,\n            active: this.active,\n            optional: this.optional,\n        };\n    }\n    _getDefaultTextForState(state) {\n        if (state == 'number') {\n            return `${this.index + 1}`;\n        }\n        if (state == 'edit') {\n            return 'create';\n        }\n        if (state == 'error') {\n            return 'warning';\n        }\n        return state;\n    }\n}\nMatStepHeader.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepHeader, deps: [{ token: MatStepperIntl }, { token: i2.FocusMonitor }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nMatStepHeader.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatStepHeader, selector: \"mat-step-header\", inputs: { color: \"color\", state: \"state\", label: \"label\", errorMessage: \"errorMessage\", iconOverrides: \"iconOverrides\", index: \"index\", selected: \"selected\", active: \"active\", optional: \"optional\", disableRipple: \"disableRipple\" }, host: { attributes: { \"role\": \"tab\" }, classAttribute: \"mat-step-header\" }, usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mat-step-header-ripple mat-focus-indicator\\\" matRipple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\"\\n     [matRippleDisabled]=\\\"disableRipple\\\"></div>\\n\\n<div class=\\\"mat-step-icon-state-{{state}} mat-step-icon\\\" [class.mat-step-icon-selected]=\\\"selected\\\">\\n  <div class=\\\"mat-step-icon-content\\\" [ngSwitch]=\\\"!!(iconOverrides && iconOverrides[state])\\\">\\n    <ng-container\\n      *ngSwitchCase=\\\"true\\\"\\n      [ngTemplateOutlet]=\\\"iconOverrides[state]\\\"\\n      [ngTemplateOutletContext]=\\\"_getIconContext()\\\"></ng-container>\\n    <ng-container *ngSwitchDefault [ngSwitch]=\\\"state\\\">\\n      <span aria-hidden=\\\"true\\\" *ngSwitchCase=\\\"'number'\\\">{{_getDefaultTextForState(state)}}</span>\\n      <span class=\\\"cdk-visually-hidden\\\" *ngIf=\\\"state === 'done'\\\">{{_intl.completedLabel}}</span>\\n      <span class=\\\"cdk-visually-hidden\\\" *ngIf=\\\"state === 'edit'\\\">{{_intl.editableLabel}}</span>\\n      <mat-icon aria-hidden=\\\"true\\\" *ngSwitchDefault>{{_getDefaultTextForState(state)}}</mat-icon>\\n    </ng-container>\\n  </div>\\n</div>\\n<div class=\\\"mat-step-label\\\"\\n     [class.mat-step-label-active]=\\\"active\\\"\\n     [class.mat-step-label-selected]=\\\"selected\\\"\\n     [class.mat-step-label-error]=\\\"state == 'error'\\\">\\n  <!-- If there is a label template, use it. -->\\n  <div class=\\\"mat-step-text-label\\\" *ngIf=\\\"_templateLabel()\\\">\\n    <ng-container [ngTemplateOutlet]=\\\"_templateLabel()!.template\\\"></ng-container>\\n  </div>\\n  <!-- If there is no label template, fall back to the text label. -->\\n  <div class=\\\"mat-step-text-label\\\" *ngIf=\\\"_stringLabel()\\\">{{label}}</div>\\n\\n  <div class=\\\"mat-step-optional\\\" *ngIf=\\\"optional && state != 'error'\\\">{{_intl.optionalLabel}}</div>\\n  <div class=\\\"mat-step-sub-label-error\\\" *ngIf=\\\"state == 'error'\\\">{{errorMessage}}</div>\\n</div>\\n\\n\", styles: [\".mat-step-header{overflow:hidden;outline:none;cursor:pointer;position:relative;box-sizing:content-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-step-header:focus .mat-focus-indicator::before{content:\\\"\\\"}.cdk-high-contrast-active .mat-step-header{outline:solid 1px}.cdk-high-contrast-active .mat-step-header[aria-selected=true] .mat-step-label{text-decoration:underline}.cdk-high-contrast-active .mat-step-header[aria-disabled=true]{outline-color:GrayText}.cdk-high-contrast-active .mat-step-header[aria-disabled=true] .mat-step-label,.cdk-high-contrast-active .mat-step-header[aria-disabled=true] .mat-step-icon,.cdk-high-contrast-active .mat-step-header[aria-disabled=true] .mat-step-optional{color:GrayText}.mat-step-optional,.mat-step-sub-label-error{font-size:12px}.mat-step-icon{border-radius:50%;height:24px;width:24px;flex-shrink:0;position:relative}.mat-step-icon-content{position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);display:flex}.mat-step-icon .mat-icon{font-size:16px;height:16px;width:16px}.mat-step-icon-state-error .mat-icon{font-size:24px;height:24px;width:24px}.mat-step-label{display:inline-block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;min-width:50px;vertical-align:middle}.mat-step-text-label{text-overflow:ellipsis;overflow:hidden}.mat-step-header .mat-step-header-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"], dependencies: [{ kind: \"directive\", type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i3.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i3.NgSwitch, selector: \"[ngSwitch]\", inputs: [\"ngSwitch\"] }, { kind: \"directive\", type: i3.NgSwitchCase, selector: \"[ngSwitchCase]\", inputs: [\"ngSwitchCase\"] }, { kind: \"directive\", type: i3.NgSwitchDefault, selector: \"[ngSwitchDefault]\" }, { kind: \"component\", type: i4.MatIcon, selector: \"mat-icon\", inputs: [\"color\", \"inline\", \"svgIcon\", \"fontSet\", \"fontIcon\"], exportAs: [\"matIcon\"] }, { kind: \"directive\", type: i1.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepHeader, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-step-header', inputs: ['color'], host: {\n                        'class': 'mat-step-header',\n                        'role': 'tab',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<div class=\\\"mat-step-header-ripple mat-focus-indicator\\\" matRipple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\"\\n     [matRippleDisabled]=\\\"disableRipple\\\"></div>\\n\\n<div class=\\\"mat-step-icon-state-{{state}} mat-step-icon\\\" [class.mat-step-icon-selected]=\\\"selected\\\">\\n  <div class=\\\"mat-step-icon-content\\\" [ngSwitch]=\\\"!!(iconOverrides && iconOverrides[state])\\\">\\n    <ng-container\\n      *ngSwitchCase=\\\"true\\\"\\n      [ngTemplateOutlet]=\\\"iconOverrides[state]\\\"\\n      [ngTemplateOutletContext]=\\\"_getIconContext()\\\"></ng-container>\\n    <ng-container *ngSwitchDefault [ngSwitch]=\\\"state\\\">\\n      <span aria-hidden=\\\"true\\\" *ngSwitchCase=\\\"'number'\\\">{{_getDefaultTextForState(state)}}</span>\\n      <span class=\\\"cdk-visually-hidden\\\" *ngIf=\\\"state === 'done'\\\">{{_intl.completedLabel}}</span>\\n      <span class=\\\"cdk-visually-hidden\\\" *ngIf=\\\"state === 'edit'\\\">{{_intl.editableLabel}}</span>\\n      <mat-icon aria-hidden=\\\"true\\\" *ngSwitchDefault>{{_getDefaultTextForState(state)}}</mat-icon>\\n    </ng-container>\\n  </div>\\n</div>\\n<div class=\\\"mat-step-label\\\"\\n     [class.mat-step-label-active]=\\\"active\\\"\\n     [class.mat-step-label-selected]=\\\"selected\\\"\\n     [class.mat-step-label-error]=\\\"state == 'error'\\\">\\n  <!-- If there is a label template, use it. -->\\n  <div class=\\\"mat-step-text-label\\\" *ngIf=\\\"_templateLabel()\\\">\\n    <ng-container [ngTemplateOutlet]=\\\"_templateLabel()!.template\\\"></ng-container>\\n  </div>\\n  <!-- If there is no label template, fall back to the text label. -->\\n  <div class=\\\"mat-step-text-label\\\" *ngIf=\\\"_stringLabel()\\\">{{label}}</div>\\n\\n  <div class=\\\"mat-step-optional\\\" *ngIf=\\\"optional && state != 'error'\\\">{{_intl.optionalLabel}}</div>\\n  <div class=\\\"mat-step-sub-label-error\\\" *ngIf=\\\"state == 'error'\\\">{{errorMessage}}</div>\\n</div>\\n\\n\", styles: [\".mat-step-header{overflow:hidden;outline:none;cursor:pointer;position:relative;box-sizing:content-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-step-header:focus .mat-focus-indicator::before{content:\\\"\\\"}.cdk-high-contrast-active .mat-step-header{outline:solid 1px}.cdk-high-contrast-active .mat-step-header[aria-selected=true] .mat-step-label{text-decoration:underline}.cdk-high-contrast-active .mat-step-header[aria-disabled=true]{outline-color:GrayText}.cdk-high-contrast-active .mat-step-header[aria-disabled=true] .mat-step-label,.cdk-high-contrast-active .mat-step-header[aria-disabled=true] .mat-step-icon,.cdk-high-contrast-active .mat-step-header[aria-disabled=true] .mat-step-optional{color:GrayText}.mat-step-optional,.mat-step-sub-label-error{font-size:12px}.mat-step-icon{border-radius:50%;height:24px;width:24px;flex-shrink:0;position:relative}.mat-step-icon-content{position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);display:flex}.mat-step-icon .mat-icon{font-size:16px;height:16px;width:16px}.mat-step-icon-state-error .mat-icon{font-size:24px;height:24px;width:24px}.mat-step-label{display:inline-block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;min-width:50px;vertical-align:middle}.mat-step-text-label{text-overflow:ellipsis;overflow:hidden}.mat-step-header .mat-step-header-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"] }]\n        }], ctorParameters: function () { return [{ type: MatStepperIntl }, { type: i2.FocusMonitor }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { state: [{\n                type: Input\n            }], label: [{\n                type: Input\n            }], errorMessage: [{\n                type: Input\n            }], iconOverrides: [{\n                type: Input\n            }], index: [{\n                type: Input\n            }], selected: [{\n                type: Input\n            }], active: [{\n                type: Input\n            }], optional: [{\n                type: Input\n            }], disableRipple: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst DEFAULT_HORIZONTAL_ANIMATION_DURATION = '500ms';\nconst DEFAULT_VERTICAL_ANIMATION_DURATION = '225ms';\n/**\n * Animations used by the Material steppers.\n * @docs-private\n */\nconst matStepperAnimations = {\n    /** Animation that transitions the step along the X axis in a horizontal stepper. */\n    horizontalStepTransition: trigger('horizontalStepTransition', [\n        state('previous', style({ transform: 'translate3d(-100%, 0, 0)', visibility: 'hidden' })),\n        // Transition to `inherit`, rather than `visible`,\n        // because visibility on a child element the one from the parent,\n        // making this element focusable inside of a `hidden` element.\n        state('current', style({ transform: 'none', visibility: 'inherit' })),\n        state('next', style({ transform: 'translate3d(100%, 0, 0)', visibility: 'hidden' })),\n        transition('* => *', animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'), {\n            params: { 'animationDuration': DEFAULT_HORIZONTAL_ANIMATION_DURATION },\n        }),\n    ]),\n    /** Animation that transitions the step along the Y axis in a vertical stepper. */\n    verticalStepTransition: trigger('verticalStepTransition', [\n        state('previous', style({ height: '0px', visibility: 'hidden' })),\n        state('next', style({ height: '0px', visibility: 'hidden' })),\n        // Transition to `inherit`, rather than `visible`,\n        // because visibility on a child element the one from the parent,\n        // making this element focusable inside of a `hidden` element.\n        state('current', style({ height: '*', visibility: 'inherit' })),\n        transition('* <=> current', animate('{{animationDuration}} cubic-bezier(0.4, 0.0, 0.2, 1)'), {\n            params: { 'animationDuration': DEFAULT_VERTICAL_ANIMATION_DURATION },\n        }),\n    ]),\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Template to be used to override the icons inside the step header.\n */\nclass MatStepperIcon {\n    constructor(templateRef) {\n        this.templateRef = templateRef;\n    }\n}\nMatStepperIcon.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepperIcon, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nMatStepperIcon.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatStepperIcon, selector: \"ng-template[matStepperIcon]\", inputs: { name: [\"matStepperIcon\", \"name\"] }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepperIcon, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[matStepperIcon]',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; }, propDecorators: { name: [{\n                type: Input,\n                args: ['matStepperIcon']\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Content for a `mat-step` that will be rendered lazily.\n */\nclass MatStepContent {\n    constructor(_template) {\n        this._template = _template;\n    }\n}\nMatStepContent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepContent, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nMatStepContent.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatStepContent, selector: \"ng-template[matStepContent]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[matStepContent]',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatStep extends CdkStep {\n    constructor(stepper, _errorStateMatcher, _viewContainerRef, stepperOptions) {\n        super(stepper, stepperOptions);\n        this._errorStateMatcher = _errorStateMatcher;\n        this._viewContainerRef = _viewContainerRef;\n        this._isSelected = Subscription.EMPTY;\n    }\n    ngAfterContentInit() {\n        this._isSelected = this._stepper.steps.changes\n            .pipe(switchMap(() => {\n            return this._stepper.selectionChange.pipe(map(event => event.selectedStep === this), startWith(this._stepper.selected === this));\n        }))\n            .subscribe(isSelected => {\n            if (isSelected && this._lazyContent && !this._portal) {\n                this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef);\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._isSelected.unsubscribe();\n    }\n    /** Custom error state matcher that additionally checks for validity of interacted form. */\n    isErrorState(control, form) {\n        const originalErrorState = this._errorStateMatcher.isErrorState(control, form);\n        // Custom error state checks for the validity of form that is not submitted or touched\n        // since user can trigger a form change by calling for another step without directly\n        // interacting with the current form.\n        const customErrorState = !!(control && control.invalid && this.interacted);\n        return originalErrorState || customErrorState;\n    }\n}\nMatStep.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStep, deps: [{ token: forwardRef(() => MatStepper) }, { token: i1.ErrorStateMatcher, skipSelf: true }, { token: i0.ViewContainerRef }, { token: STEPPER_GLOBAL_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatStep.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatStep, selector: \"mat-step\", inputs: { color: \"color\" }, providers: [\n        { provide: ErrorStateMatcher, useExisting: MatStep },\n        { provide: CdkStep, useExisting: MatStep },\n    ], queries: [{ propertyName: \"stepLabel\", first: true, predicate: MatStepLabel, descendants: true }, { propertyName: \"_lazyContent\", first: true, predicate: MatStepContent, descendants: true }], exportAs: [\"matStep\"], usesInheritance: true, ngImport: i0, template: \"<ng-template>\\n  <ng-content></ng-content>\\n  <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n</ng-template>\\n\", dependencies: [{ kind: \"directive\", type: i2$1.CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStep, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-step', providers: [\n                        { provide: ErrorStateMatcher, useExisting: MatStep },\n                        { provide: CdkStep, useExisting: MatStep },\n                    ], encapsulation: ViewEncapsulation.None, exportAs: 'matStep', changeDetection: ChangeDetectionStrategy.OnPush, template: \"<ng-template>\\n  <ng-content></ng-content>\\n  <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n</ng-template>\\n\" }]\n        }], ctorParameters: function () { return [{ type: MatStepper, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => MatStepper)]\n                }] }, { type: i1.ErrorStateMatcher, decorators: [{\n                    type: SkipSelf\n                }] }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [STEPPER_GLOBAL_OPTIONS]\n                }] }]; }, propDecorators: { stepLabel: [{\n                type: ContentChild,\n                args: [MatStepLabel]\n            }], color: [{\n                type: Input\n            }], _lazyContent: [{\n                type: ContentChild,\n                args: [MatStepContent, { static: false }]\n            }] } });\nclass MatStepper extends CdkStepper {\n    constructor(dir, changeDetectorRef, elementRef) {\n        super(dir, changeDetectorRef, elementRef);\n        /** Steps that belong to the current stepper, excluding ones from nested steppers. */\n        this.steps = new QueryList();\n        /** Event emitted when the current step is done transitioning in. */\n        this.animationDone = new EventEmitter();\n        /**\n         * Whether the label should display in bottom or end position.\n         * Only applies in the `horizontal` orientation.\n         */\n        this.labelPosition = 'end';\n        /**\n         * Position of the stepper's header.\n         * Only applies in the `horizontal` orientation.\n         */\n        this.headerPosition = 'top';\n        /** Consumer-specified template-refs to be used to override the header icons. */\n        this._iconOverrides = {};\n        /** Stream of animation `done` events when the body expands/collapses. */\n        this._animationDone = new Subject();\n        this._animationDuration = '';\n        const nodeName = elementRef.nativeElement.nodeName.toLowerCase();\n        this.orientation = nodeName === 'mat-vertical-stepper' ? 'vertical' : 'horizontal';\n    }\n    /** Duration for the animation. Will be normalized to milliseconds if no units are set. */\n    get animationDuration() {\n        return this._animationDuration;\n    }\n    set animationDuration(value) {\n        this._animationDuration = /^\\d+$/.test(value) ? value + 'ms' : value;\n    }\n    ngAfterContentInit() {\n        super.ngAfterContentInit();\n        this._icons.forEach(({ name, templateRef }) => (this._iconOverrides[name] = templateRef));\n        // Mark the component for change detection whenever the content children query changes\n        this.steps.changes.pipe(takeUntil(this._destroyed)).subscribe(() => {\n            this._stateChanged();\n        });\n        this._animationDone\n            .pipe(\n        // This needs a `distinctUntilChanged` in order to avoid emitting the same event twice due\n        // to a bug in animations where the `.done` callback gets invoked twice on some browsers.\n        // See https://github.com/angular/angular/issues/24084\n        distinctUntilChanged((x, y) => x.fromState === y.fromState && x.toState === y.toState), takeUntil(this._destroyed))\n            .subscribe(event => {\n            if (event.toState === 'current') {\n                this.animationDone.emit();\n            }\n        });\n    }\n    _stepIsNavigable(index, step) {\n        return step.completed || this.selectedIndex === index || !this.linear;\n    }\n    _getAnimationDuration() {\n        if (this.animationDuration) {\n            return this.animationDuration;\n        }\n        return this.orientation === 'horizontal'\n            ? DEFAULT_HORIZONTAL_ANIMATION_DURATION\n            : DEFAULT_VERTICAL_ANIMATION_DURATION;\n    }\n}\nMatStepper.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepper, deps: [{ token: i3$1.Directionality, optional: true }, { token: i0.ChangeDetectorRef }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Component });\nMatStepper.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatStepper, selector: \"mat-stepper, mat-vertical-stepper, mat-horizontal-stepper, [matStepper]\", inputs: { selectedIndex: \"selectedIndex\", disableRipple: \"disableRipple\", color: \"color\", labelPosition: \"labelPosition\", headerPosition: \"headerPosition\", animationDuration: \"animationDuration\" }, outputs: { animationDone: \"animationDone\" }, host: { attributes: { \"role\": \"tablist\" }, properties: { \"class.mat-stepper-horizontal\": \"orientation === \\\"horizontal\\\"\", \"class.mat-stepper-vertical\": \"orientation === \\\"vertical\\\"\", \"class.mat-stepper-label-position-end\": \"orientation === \\\"horizontal\\\" && labelPosition == \\\"end\\\"\", \"class.mat-stepper-label-position-bottom\": \"orientation === \\\"horizontal\\\" && labelPosition == \\\"bottom\\\"\", \"class.mat-stepper-header-position-bottom\": \"headerPosition === \\\"bottom\\\"\", \"attr.aria-orientation\": \"orientation\" } }, providers: [{ provide: CdkStepper, useExisting: MatStepper }], queries: [{ propertyName: \"_steps\", predicate: MatStep, descendants: true }, { propertyName: \"_icons\", predicate: MatStepperIcon, descendants: true }], viewQueries: [{ propertyName: \"_stepHeader\", predicate: MatStepHeader, descendants: true }], exportAs: [\"matStepper\", \"matVerticalStepper\", \"matHorizontalStepper\"], usesInheritance: true, ngImport: i0, template: \"<ng-container [ngSwitch]=\\\"orientation\\\">\\n  <!-- Horizontal stepper -->\\n  <div class=\\\"mat-horizontal-stepper-wrapper\\\" *ngSwitchCase=\\\"'horizontal'\\\">\\n    <div class=\\\"mat-horizontal-stepper-header-container\\\">\\n      <ng-container *ngFor=\\\"let step of steps; let i = index; let isLast = last\\\">\\n        <ng-container\\n          [ngTemplateOutlet]=\\\"stepTemplate\\\"\\n          [ngTemplateOutletContext]=\\\"{step: step, i: i}\\\"></ng-container>\\n        <div *ngIf=\\\"!isLast\\\" class=\\\"mat-stepper-horizontal-line\\\"></div>\\n      </ng-container>\\n    </div>\\n\\n    <div class=\\\"mat-horizontal-content-container\\\">\\n      <div *ngFor=\\\"let step of steps; let i = index\\\"\\n           class=\\\"mat-horizontal-stepper-content\\\" role=\\\"tabpanel\\\"\\n           [@horizontalStepTransition]=\\\"{\\n              'value': _getAnimationDirection(i),\\n              'params': {'animationDuration': _getAnimationDuration()}\\n            }\\\"\\n           (@horizontalStepTransition.done)=\\\"_animationDone.next($event)\\\"\\n           [id]=\\\"_getStepContentId(i)\\\"\\n           [attr.aria-labelledby]=\\\"_getStepLabelId(i)\\\"\\n           [class.mat-horizontal-stepper-content-inactive]=\\\"selectedIndex !== i\\\">\\n        <ng-container [ngTemplateOutlet]=\\\"step.content\\\"></ng-container>\\n      </div>\\n    </div>\\n  </div>\\n\\n  <!-- Vertical stepper -->\\n  <ng-container *ngSwitchCase=\\\"'vertical'\\\">\\n    <div class=\\\"mat-step\\\" *ngFor=\\\"let step of steps; let i = index; let isLast = last\\\">\\n      <ng-container\\n        [ngTemplateOutlet]=\\\"stepTemplate\\\"\\n        [ngTemplateOutletContext]=\\\"{step: step, i: i}\\\"></ng-container>\\n      <div class=\\\"mat-vertical-content-container\\\" [class.mat-stepper-vertical-line]=\\\"!isLast\\\">\\n        <div class=\\\"mat-vertical-stepper-content\\\" role=\\\"tabpanel\\\"\\n             [@verticalStepTransition]=\\\"{\\n                'value': _getAnimationDirection(i),\\n                'params': {'animationDuration': _getAnimationDuration()}\\n              }\\\"\\n             (@verticalStepTransition.done)=\\\"_animationDone.next($event)\\\"\\n             [id]=\\\"_getStepContentId(i)\\\"\\n             [attr.aria-labelledby]=\\\"_getStepLabelId(i)\\\"\\n             [class.mat-vertical-stepper-content-inactive]=\\\"selectedIndex !== i\\\">\\n          <div class=\\\"mat-vertical-content\\\">\\n            <ng-container [ngTemplateOutlet]=\\\"step.content\\\"></ng-container>\\n          </div>\\n        </div>\\n      </div>\\n    </div>\\n  </ng-container>\\n\\n</ng-container>\\n\\n<!-- Common step templating -->\\n<ng-template let-step=\\\"step\\\" let-i=\\\"i\\\" #stepTemplate>\\n  <mat-step-header\\n    [class.mat-horizontal-stepper-header]=\\\"orientation === 'horizontal'\\\"\\n    [class.mat-vertical-stepper-header]=\\\"orientation === 'vertical'\\\"\\n    (click)=\\\"step.select()\\\"\\n    (keydown)=\\\"_onKeydown($event)\\\"\\n    [tabIndex]=\\\"_getFocusIndex() === i ? 0 : -1\\\"\\n    [id]=\\\"_getStepLabelId(i)\\\"\\n    [attr.aria-posinset]=\\\"i + 1\\\"\\n    [attr.aria-setsize]=\\\"steps.length\\\"\\n    [attr.aria-controls]=\\\"_getStepContentId(i)\\\"\\n    [attr.aria-selected]=\\\"selectedIndex == i\\\"\\n    [attr.aria-label]=\\\"step.ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"(!step.ariaLabel && step.ariaLabelledby) ? step.ariaLabelledby : null\\\"\\n    [attr.aria-disabled]=\\\"_stepIsNavigable(i, step) ? null : true\\\"\\n    [index]=\\\"i\\\"\\n    [state]=\\\"_getIndicatorType(i, step.state)\\\"\\n    [label]=\\\"step.stepLabel || step.label\\\"\\n    [selected]=\\\"selectedIndex === i\\\"\\n    [active]=\\\"_stepIsNavigable(i, step)\\\"\\n    [optional]=\\\"step.optional\\\"\\n    [errorMessage]=\\\"step.errorMessage\\\"\\n    [iconOverrides]=\\\"_iconOverrides\\\"\\n    [disableRipple]=\\\"disableRipple || !_stepIsNavigable(i, step)\\\"\\n    [color]=\\\"step.color || color\\\"></mat-step-header>\\n</ng-template>\\n\", styles: [\".mat-stepper-vertical,.mat-stepper-horizontal{display:block}.mat-horizontal-stepper-header-container{white-space:nowrap;display:flex;align-items:center}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header-container{align-items:flex-start}.mat-stepper-header-position-bottom .mat-horizontal-stepper-header-container{order:1}.mat-stepper-horizontal-line{border-top-width:1px;border-top-style:solid;flex:auto;height:0;margin:0 -16px;min-width:32px}.mat-stepper-label-position-bottom .mat-stepper-horizontal-line{margin:0;min-width:0;position:relative}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::before,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::before,.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::after,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::after{border-top-width:1px;border-top-style:solid;content:\\\"\\\";display:inline-block;height:0;position:absolute;width:calc(50% - 20px)}.mat-horizontal-stepper-header{display:flex;height:72px;overflow:hidden;align-items:center;padding:0 24px}.mat-horizontal-stepper-header .mat-step-icon{margin-right:8px;flex:none}[dir=rtl] .mat-horizontal-stepper-header .mat-step-icon{margin-right:0;margin-left:8px}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header{box-sizing:border-box;flex-direction:column;height:auto}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::after,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::after{right:0}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::before,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::before{left:0}[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:last-child::before,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:first-child::after{display:none}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header .mat-step-icon{margin-right:0;margin-left:0}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header .mat-step-label{padding:16px 0 0 0;text-align:center;width:100%}.mat-vertical-stepper-header{display:flex;align-items:center;height:24px}.mat-vertical-stepper-header .mat-step-icon{margin-right:12px}[dir=rtl] .mat-vertical-stepper-header .mat-step-icon{margin-right:0;margin-left:12px}.mat-horizontal-stepper-wrapper{display:flex;flex-direction:column}.mat-horizontal-stepper-content{outline:0}.mat-horizontal-stepper-content.mat-horizontal-stepper-content-inactive{height:0;overflow:hidden}.mat-horizontal-stepper-content:not(.mat-horizontal-stepper-content-inactive){visibility:inherit !important}.mat-horizontal-content-container{overflow:hidden;padding:0 24px 24px 24px}.cdk-high-contrast-active .mat-horizontal-content-container{outline:solid 1px}.mat-stepper-header-position-bottom .mat-horizontal-content-container{padding:24px 24px 0 24px}.mat-vertical-content-container{margin-left:36px;border:0;position:relative}.cdk-high-contrast-active .mat-vertical-content-container{outline:solid 1px}[dir=rtl] .mat-vertical-content-container{margin-left:0;margin-right:36px}.mat-stepper-vertical-line::before{content:\\\"\\\";position:absolute;left:0;border-left-width:1px;border-left-style:solid}[dir=rtl] .mat-stepper-vertical-line::before{left:auto;right:0}.mat-vertical-stepper-content{overflow:hidden;outline:0}.mat-vertical-stepper-content:not(.mat-vertical-stepper-content-inactive){visibility:inherit !important}.mat-vertical-content{padding:0 24px 24px 24px}.mat-step:last-child .mat-vertical-content-container{border:none}\"], dependencies: [{ kind: \"directive\", type: i3.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i3.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i3.NgSwitch, selector: \"[ngSwitch]\", inputs: [\"ngSwitch\"] }, { kind: \"directive\", type: i3.NgSwitchCase, selector: \"[ngSwitchCase]\", inputs: [\"ngSwitchCase\"] }, { kind: \"component\", type: MatStepHeader, selector: \"mat-step-header\", inputs: [\"color\", \"state\", \"label\", \"errorMessage\", \"iconOverrides\", \"index\", \"selected\", \"active\", \"optional\", \"disableRipple\"] }], animations: [\n        matStepperAnimations.horizontalStepTransition,\n        matStepperAnimations.verticalStepTransition,\n    ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepper, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-stepper, mat-vertical-stepper, mat-horizontal-stepper, [matStepper]', exportAs: 'matStepper, matVerticalStepper, matHorizontalStepper', inputs: ['selectedIndex'], host: {\n                        '[class.mat-stepper-horizontal]': 'orientation === \"horizontal\"',\n                        '[class.mat-stepper-vertical]': 'orientation === \"vertical\"',\n                        '[class.mat-stepper-label-position-end]': 'orientation === \"horizontal\" && labelPosition == \"end\"',\n                        '[class.mat-stepper-label-position-bottom]': 'orientation === \"horizontal\" && labelPosition == \"bottom\"',\n                        '[class.mat-stepper-header-position-bottom]': 'headerPosition === \"bottom\"',\n                        '[attr.aria-orientation]': 'orientation',\n                        'role': 'tablist',\n                    }, animations: [\n                        matStepperAnimations.horizontalStepTransition,\n                        matStepperAnimations.verticalStepTransition,\n                    ], providers: [{ provide: CdkStepper, useExisting: MatStepper }], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<ng-container [ngSwitch]=\\\"orientation\\\">\\n  <!-- Horizontal stepper -->\\n  <div class=\\\"mat-horizontal-stepper-wrapper\\\" *ngSwitchCase=\\\"'horizontal'\\\">\\n    <div class=\\\"mat-horizontal-stepper-header-container\\\">\\n      <ng-container *ngFor=\\\"let step of steps; let i = index; let isLast = last\\\">\\n        <ng-container\\n          [ngTemplateOutlet]=\\\"stepTemplate\\\"\\n          [ngTemplateOutletContext]=\\\"{step: step, i: i}\\\"></ng-container>\\n        <div *ngIf=\\\"!isLast\\\" class=\\\"mat-stepper-horizontal-line\\\"></div>\\n      </ng-container>\\n    </div>\\n\\n    <div class=\\\"mat-horizontal-content-container\\\">\\n      <div *ngFor=\\\"let step of steps; let i = index\\\"\\n           class=\\\"mat-horizontal-stepper-content\\\" role=\\\"tabpanel\\\"\\n           [@horizontalStepTransition]=\\\"{\\n              'value': _getAnimationDirection(i),\\n              'params': {'animationDuration': _getAnimationDuration()}\\n            }\\\"\\n           (@horizontalStepTransition.done)=\\\"_animationDone.next($event)\\\"\\n           [id]=\\\"_getStepContentId(i)\\\"\\n           [attr.aria-labelledby]=\\\"_getStepLabelId(i)\\\"\\n           [class.mat-horizontal-stepper-content-inactive]=\\\"selectedIndex !== i\\\">\\n        <ng-container [ngTemplateOutlet]=\\\"step.content\\\"></ng-container>\\n      </div>\\n    </div>\\n  </div>\\n\\n  <!-- Vertical stepper -->\\n  <ng-container *ngSwitchCase=\\\"'vertical'\\\">\\n    <div class=\\\"mat-step\\\" *ngFor=\\\"let step of steps; let i = index; let isLast = last\\\">\\n      <ng-container\\n        [ngTemplateOutlet]=\\\"stepTemplate\\\"\\n        [ngTemplateOutletContext]=\\\"{step: step, i: i}\\\"></ng-container>\\n      <div class=\\\"mat-vertical-content-container\\\" [class.mat-stepper-vertical-line]=\\\"!isLast\\\">\\n        <div class=\\\"mat-vertical-stepper-content\\\" role=\\\"tabpanel\\\"\\n             [@verticalStepTransition]=\\\"{\\n                'value': _getAnimationDirection(i),\\n                'params': {'animationDuration': _getAnimationDuration()}\\n              }\\\"\\n             (@verticalStepTransition.done)=\\\"_animationDone.next($event)\\\"\\n             [id]=\\\"_getStepContentId(i)\\\"\\n             [attr.aria-labelledby]=\\\"_getStepLabelId(i)\\\"\\n             [class.mat-vertical-stepper-content-inactive]=\\\"selectedIndex !== i\\\">\\n          <div class=\\\"mat-vertical-content\\\">\\n            <ng-container [ngTemplateOutlet]=\\\"step.content\\\"></ng-container>\\n          </div>\\n        </div>\\n      </div>\\n    </div>\\n  </ng-container>\\n\\n</ng-container>\\n\\n<!-- Common step templating -->\\n<ng-template let-step=\\\"step\\\" let-i=\\\"i\\\" #stepTemplate>\\n  <mat-step-header\\n    [class.mat-horizontal-stepper-header]=\\\"orientation === 'horizontal'\\\"\\n    [class.mat-vertical-stepper-header]=\\\"orientation === 'vertical'\\\"\\n    (click)=\\\"step.select()\\\"\\n    (keydown)=\\\"_onKeydown($event)\\\"\\n    [tabIndex]=\\\"_getFocusIndex() === i ? 0 : -1\\\"\\n    [id]=\\\"_getStepLabelId(i)\\\"\\n    [attr.aria-posinset]=\\\"i + 1\\\"\\n    [attr.aria-setsize]=\\\"steps.length\\\"\\n    [attr.aria-controls]=\\\"_getStepContentId(i)\\\"\\n    [attr.aria-selected]=\\\"selectedIndex == i\\\"\\n    [attr.aria-label]=\\\"step.ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"(!step.ariaLabel && step.ariaLabelledby) ? step.ariaLabelledby : null\\\"\\n    [attr.aria-disabled]=\\\"_stepIsNavigable(i, step) ? null : true\\\"\\n    [index]=\\\"i\\\"\\n    [state]=\\\"_getIndicatorType(i, step.state)\\\"\\n    [label]=\\\"step.stepLabel || step.label\\\"\\n    [selected]=\\\"selectedIndex === i\\\"\\n    [active]=\\\"_stepIsNavigable(i, step)\\\"\\n    [optional]=\\\"step.optional\\\"\\n    [errorMessage]=\\\"step.errorMessage\\\"\\n    [iconOverrides]=\\\"_iconOverrides\\\"\\n    [disableRipple]=\\\"disableRipple || !_stepIsNavigable(i, step)\\\"\\n    [color]=\\\"step.color || color\\\"></mat-step-header>\\n</ng-template>\\n\", styles: [\".mat-stepper-vertical,.mat-stepper-horizontal{display:block}.mat-horizontal-stepper-header-container{white-space:nowrap;display:flex;align-items:center}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header-container{align-items:flex-start}.mat-stepper-header-position-bottom .mat-horizontal-stepper-header-container{order:1}.mat-stepper-horizontal-line{border-top-width:1px;border-top-style:solid;flex:auto;height:0;margin:0 -16px;min-width:32px}.mat-stepper-label-position-bottom .mat-stepper-horizontal-line{margin:0;min-width:0;position:relative}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::before,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::before,.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::after,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::after{border-top-width:1px;border-top-style:solid;content:\\\"\\\";display:inline-block;height:0;position:absolute;width:calc(50% - 20px)}.mat-horizontal-stepper-header{display:flex;height:72px;overflow:hidden;align-items:center;padding:0 24px}.mat-horizontal-stepper-header .mat-step-icon{margin-right:8px;flex:none}[dir=rtl] .mat-horizontal-stepper-header .mat-step-icon{margin-right:0;margin-left:8px}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header{box-sizing:border-box;flex-direction:column;height:auto}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::after,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::after{right:0}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::before,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::before{left:0}[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:last-child::before,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:first-child::after{display:none}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header .mat-step-icon{margin-right:0;margin-left:0}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header .mat-step-label{padding:16px 0 0 0;text-align:center;width:100%}.mat-vertical-stepper-header{display:flex;align-items:center;height:24px}.mat-vertical-stepper-header .mat-step-icon{margin-right:12px}[dir=rtl] .mat-vertical-stepper-header .mat-step-icon{margin-right:0;margin-left:12px}.mat-horizontal-stepper-wrapper{display:flex;flex-direction:column}.mat-horizontal-stepper-content{outline:0}.mat-horizontal-stepper-content.mat-horizontal-stepper-content-inactive{height:0;overflow:hidden}.mat-horizontal-stepper-content:not(.mat-horizontal-stepper-content-inactive){visibility:inherit !important}.mat-horizontal-content-container{overflow:hidden;padding:0 24px 24px 24px}.cdk-high-contrast-active .mat-horizontal-content-container{outline:solid 1px}.mat-stepper-header-position-bottom .mat-horizontal-content-container{padding:24px 24px 0 24px}.mat-vertical-content-container{margin-left:36px;border:0;position:relative}.cdk-high-contrast-active .mat-vertical-content-container{outline:solid 1px}[dir=rtl] .mat-vertical-content-container{margin-left:0;margin-right:36px}.mat-stepper-vertical-line::before{content:\\\"\\\";position:absolute;left:0;border-left-width:1px;border-left-style:solid}[dir=rtl] .mat-stepper-vertical-line::before{left:auto;right:0}.mat-vertical-stepper-content{overflow:hidden;outline:0}.mat-vertical-stepper-content:not(.mat-vertical-stepper-content-inactive){visibility:inherit !important}.mat-vertical-content{padding:0 24px 24px 24px}.mat-step:last-child .mat-vertical-content-container{border:none}\"] }]\n        }], ctorParameters: function () { return [{ type: i3$1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ChangeDetectorRef }, { type: i0.ElementRef }]; }, propDecorators: { _stepHeader: [{\n                type: ViewChildren,\n                args: [MatStepHeader]\n            }], _steps: [{\n                type: ContentChildren,\n                args: [MatStep, { descendants: true }]\n            }], _icons: [{\n                type: ContentChildren,\n                args: [MatStepperIcon, { descendants: true }]\n            }], animationDone: [{\n                type: Output\n            }], disableRipple: [{\n                type: Input\n            }], color: [{\n                type: Input\n            }], labelPosition: [{\n                type: Input\n            }], headerPosition: [{\n                type: Input\n            }], animationDuration: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Button that moves to the next step in a stepper workflow. */\nclass MatStepperNext extends CdkStepperNext {\n}\nMatStepperNext.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepperNext, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatStepperNext.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatStepperNext, selector: \"button[matStepperNext]\", inputs: { type: \"type\" }, host: { properties: { \"type\": \"type\" }, classAttribute: \"mat-stepper-next\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepperNext, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'button[matStepperNext]',\n                    host: {\n                        'class': 'mat-stepper-next',\n                        '[type]': 'type',\n                    },\n                    inputs: ['type'],\n                }]\n        }] });\n/** Button that moves to the previous step in a stepper workflow. */\nclass MatStepperPrevious extends CdkStepperPrevious {\n}\nMatStepperPrevious.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepperPrevious, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatStepperPrevious.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatStepperPrevious, selector: \"button[matStepperPrevious]\", inputs: { type: \"type\" }, host: { properties: { \"type\": \"type\" }, classAttribute: \"mat-stepper-previous\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepperPrevious, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'button[matStepperPrevious]',\n                    host: {\n                        'class': 'mat-stepper-previous',\n                        '[type]': 'type',\n                    },\n                    inputs: ['type'],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatStepperModule {\n}\nMatStepperModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepperModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatStepperModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepperModule, declarations: [MatStep,\n        MatStepLabel,\n        MatStepper,\n        MatStepperNext,\n        MatStepperPrevious,\n        MatStepHeader,\n        MatStepperIcon,\n        MatStepContent], imports: [MatCommonModule,\n        CommonModule,\n        PortalModule,\n        MatButtonModule,\n        CdkStepperModule,\n        MatIconModule,\n        MatRippleModule], exports: [MatCommonModule,\n        MatStep,\n        MatStepLabel,\n        MatStepper,\n        MatStepperNext,\n        MatStepperPrevious,\n        MatStepHeader,\n        MatStepperIcon,\n        MatStepContent] });\nMatStepperModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepperModule, providers: [MAT_STEPPER_INTL_PROVIDER, ErrorStateMatcher], imports: [MatCommonModule,\n        CommonModule,\n        PortalModule,\n        MatButtonModule,\n        CdkStepperModule,\n        MatIconModule,\n        MatRippleModule, MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatStepperModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        MatCommonModule,\n                        CommonModule,\n                        PortalModule,\n                        MatButtonModule,\n                        CdkStepperModule,\n                        MatIconModule,\n                        MatRippleModule,\n                    ],\n                    exports: [\n                        MatCommonModule,\n                        MatStep,\n                        MatStepLabel,\n                        MatStepper,\n                        MatStepperNext,\n                        MatStepperPrevious,\n                        MatStepHeader,\n                        MatStepperIcon,\n                        MatStepContent,\n                    ],\n                    declarations: [\n                        MatStep,\n                        MatStepLabel,\n                        MatStepper,\n                        MatStepperNext,\n                        MatStepperPrevious,\n                        MatStepHeader,\n                        MatStepperIcon,\n                        MatStepContent,\n                    ],\n                    providers: [MAT_STEPPER_INTL_PROVIDER, ErrorStateMatcher],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_STEPPER_INTL_PROVIDER, MAT_STEPPER_INTL_PROVIDER_FACTORY, MatStep, MatStepContent, MatStepHeader, MatStepLabel, MatStepper, MatStepperIcon, MatStepperIntl, MatStepperModule, MatStepperNext, MatStepperPrevious, matStepperAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,IAAZ,MAAsB,qBAAtB;AACA,SAASC,cAAT,EAAyBC,YAAzB,QAA6C,qBAA7C;AACA,SAASC,YAAT,EAAuBC,aAAvB,EAAsCC,OAAtC,EAA+CC,sBAA/C,EAAuEC,UAAvE,EAAmFC,cAAnF,EAAmGC,kBAAnG,EAAuHC,gBAAvH,QAA+I,sBAA/I;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,SAAT,EAAoBC,UAApB,EAAgCC,QAAhC,EAA0CC,QAA1C,EAAoDC,SAApD,EAA+DC,iBAA/D,EAAkFC,uBAAlF,EAA2GC,KAA3G,EAAkHC,UAAlH,EAA8HC,MAA9H,EAAsIC,YAAtI,EAAoJC,SAApJ,EAA+JC,YAA/J,EAA6KC,YAA7K,EAA2LC,eAA3L,EAA4MC,MAA5M,EAAoNC,QAApN,QAAoO,eAApO;AACA,SAASC,eAAT,QAAgC,0BAAhC;AACA,OAAO,KAAKC,EAAZ,MAAoB,wBAApB;AACA,SAASC,UAAT,EAAqBC,iBAArB,EAAwCC,eAAxC,EAAyDC,eAAzD,QAAgF,wBAAhF;AACA,OAAO,KAAKC,EAAZ,MAAoB,wBAApB;AACA,SAASC,aAAT,QAA8B,wBAA9B;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,SAASC,OAAT,EAAkBC,YAAlB,QAAsC,MAAtC;AACA,OAAO,KAAKC,IAAZ,MAAsB,mBAAtB;AACA,SAASC,SAAT,EAAoBC,GAApB,EAAyBC,SAAzB,EAAoCC,SAApC,EAA+CC,oBAA/C,QAA2E,gBAA3E;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,KAAzB,EAAgCC,UAAhC,EAA4CC,OAA5C,QAA2D,qBAA3D;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;IAG+FvC,EAwHuwB,yB;;;;mBAxHvwBA,E;IAAAA,EAwH0zB,wH;;;;;;IAxH1zBA,EAwH8+B,8B;IAxH9+BA,EAwHoiC,U;IAxHpiCA,EAwHskC,e;;;;mBAxHtkCA,E;IAAAA,EAwHoiC,a;IAxHpiCA,EAwHoiC,gE;;;;;;IAxHpiCA,EAwHqlC,8B;IAxHrlCA,EAwHopC,U;IAxHppCA,EAwH4qC,e;;;;mBAxH5qCA,E;IAAAA,EAwHopC,a;IAxHppCA,EAwHopC,+C;;;;;;IAxHppCA,EAwH2rC,8B;IAxH3rCA,EAwH0vC,U;IAxH1vCA,EAwHixC,e;;;;mBAxHjxCA,E;IAAAA,EAwH0vC,a;IAxH1vCA,EAwH0vC,8C;;;;;;IAxH1vCA,EAwHgyC,kC;IAxHhyCA,EAwHg1C,U;IAxHh1CA,EAwHk3C,e;;;;mBAxHl3CA,E;IAAAA,EAwHg1C,a;IAxHh1CA,EAwHg1C,gE;;;;;;IAxHh1CA,EAwHk7B,8B;IAxHl7BA,EAwH8+B,8E;IAxH9+BA,EAwHqlC,8E;IAxHrlCA,EAwH2rC,8E;IAxH3rCA,EAwHgyC,sF;IAxHhyCA,EAwHm4C,wB;;;;mBAxHn4CA,E;IAAAA,EAwHi9B,qC;IAxHj9BA,EAwH0gC,a;IAxH1gCA,EAwH0gC,qC;IAxH1gCA,EAwH0nC,a;IAxH1nCA,EAwH0nC,4C;IAxH1nCA,EAwHguC,a;IAxHhuCA,EAwHguC,4C;;;;;;IAxHhuCA,EAwHopD,6B;IAxHppDA,EAwHwtD,0B;IAxHxtDA,EAwH2yD,e;;;;mBAxH3yDA,E;IAAAA,EAwHsuD,a;IAxHtuDA,EAwHsuD,iE;;;;;;IAxHtuDA,EAwH63D,6B;IAxH73DA,EAwHy7D,U;IAxHz7DA,EAwHk8D,e;;;;mBAxHl8DA,E;IAAAA,EAwHy7D,a;IAxHz7DA,EAwHy7D,gC;;;;;;IAxHz7DA,EAwH88D,6B;IAxH98DA,EAwHshE,U;IAxHthEA,EAwH6iE,e;;;;mBAxH7iEA,E;IAAAA,EAwHshE,a;IAxHthEA,EAwHshE,8C;;;;;;IAxHthEA,EAwHujE,6B;IAxHvjEA,EAwH0nE,U;IAxH1nEA,EAwH0oE,e;;;;mBAxH1oEA,E;IAAAA,EAwH0nE,a;IAxH1nEA,EAwH0nE,uC;;;;;;;;IAxH1nEA,EAwRgM,gB;IAxRhMA,EAwR6N,oF;;;;mBAxR7NA,E;IAAAA,EAwR0O,a;IAxR1OA,EAwR0O,8C;;;;;;;;IAxR1OA,EAkXusD,wB;;;;;;;;;;;;;IAlXvsDA,EAkX89C,2B;IAlX99CA,EAkXqjD,yB;IAlXrjDA,EAkXusD,+E;IAlXvsDA,EAkXkxD,wB;;;;;;;IAlXlxDA,E;;gBAAAA,E;;IAAAA,EAkX8kD,a;IAlX9kDA,EAkX8kD,gEAlX9kDA,EAkX8kD,wC;IAlX9kDA,EAkX6sD,a;IAlX7sDA,EAkX6sD,+B;;;;;;;;;;;;;;;;;;;iBAlX7sDA,E;;IAAAA,EAkX62D,6B;IAlX72DA,EAkXyqE;MAlXzqEA,EAkXyqE;MAAA,gBAlXzqEA,EAkXyqE;MAAA,OAlXzqEA,EAkX4sE,iDAAnC;IAAA,E;IAlXzqEA,EAkX46E,0B;IAlX56EA,EAkXq/E,e;;;;;;mBAlXr/EA,E;IAAAA,EAkX01E,uF;IAlX11EA,EAkXi/D,yCAlXj/DA,EAkXi/D,+DAlXj/DA,EAkXi/D,iG;IAlXj/DA,EAkXgyE,8D;IAlXhyEA,EAkX07E,a;IAlX17EA,EAkX07E,iD;;;;;;IAlX17EA,EAkX40C,yC;IAlX50CA,EAkX89C,iF;IAlX99CA,EAkXuyD,e;IAlXvyDA,EAkXqzD,4B;IAlXrzDA,EAkX62D,gE;IAlX72DA,EAkXigF,iB;;;;mBAlXjgFA,E;IAAAA,EAkX8/C,a;IAlX9/CA,EAkX8/C,oC;IAlX9/CA,EAkXo4D,a;IAlXp4DA,EAkXo4D,oC;;;;;;iBAlXp4DA,E;;IAAAA,EAkXqmF,6B;IAlXrmFA,EAkXosF,yB;IAlXpsFA,EAkXg1F,2C;IAlXh1FA,EAkXgsG;MAlXhsGA,EAkXgsG;MAAA,gBAlXhsGA,EAkXgsG;MAAA,OAlXhsGA,EAkXiuG,iDAAjC;IAAA,E;IAlXhsGA,EAkXu8G,6B;IAlXv8GA,EAkXy/G,0B;IAlXz/GA,EAkXskH,qB;;;;;;;oBAlXtkHA,E;;gBAAAA,E;;IAAAA,EAkX2tF,a;IAlX3tFA,EAkX2tF,gEAlX3tFA,EAkX2tF,2C;IAlX3tFA,EAkX83F,a;IAlX93FA,EAkX83F,sD;IAlX93FA,EAkXq3G,a;IAlXr3GA,EAkXq3G,sF;IAlXr3GA,EAkXkgG,uCAlXlgGA,EAkXkgG,iEAlXlgGA,EAkXkgG,oG;IAlXlgGA,EAkXyzG,+D;IAlXzzGA,EAkXugH,a;IAlXvgHA,EAkXugH,iD;;;;;;IAlXvgHA,EAkXojF,2B;IAlXpjFA,EAkXqmF,0E;IAlXrmFA,EAkX0nH,wB;;;;mBAlX1nHA,E;IAAAA,EAkX+oF,a;IAlX/oFA,EAkX+oF,oC;;;;;;iBAlX/oFA,E;;IAAAA,EAkX8vH,yC;IAlX9vHA,EAkXw6H;MAAA,oBAlXx6HA,EAkXw6H;MAAA;MAAA,OAlXx6HA,EAkXk7H,+BAAV;IAAA;MAlXx6HA,EAkXw6H;MAAA,gBAlXx6HA,EAkXw6H;MAAA,OAlXx6HA,EAkXm9H,wCAA3C;IAAA,E;IAlXx6HA,EAkXk3J,e;;;;;;mBAlXl3JA,E;IAAAA,EAkXoxH,oJ;IAlXpxHA,EAkX6+H,4iB;IAlX7+HA,EAkXkkI,sY;;;;AApXjqI,MAAMwC,YAAN,SAA2BlD,YAA3B,CAAwC;;AAExCkD,YAAY,CAACC,IAAb;EAAA;EAAA;IAAA,kEAA+FzC,EAA/F,uBAAyGwC,YAAzG,SAAyGA,YAAzG;EAAA;AAAA;;AACAA,YAAY,CAACE,IAAb,kBAD+F1C,EAC/F;EAAA,MAA6FwC,YAA7F;EAAA;EAAA,WAD+FxC,EAC/F;AAAA;;AACA;EAAA,mDAF+FA,EAE/F,mBAA2FwC,YAA3F,EAAqH,CAAC;IAC1GG,IAAI,EAAE1C,SADoG;IAE1G2C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IADX,CAAD;EAFoG,CAAD,CAArH;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMC,cAAN,CAAqB;EACjBC,WAAW,GAAG;IACV;AACR;AACA;AACA;IACQ,KAAKC,OAAL,GAAe,IAAIrB,OAAJ,EAAf;IACA;;IACA,KAAKsB,aAAL,GAAqB,UAArB;IACA;;IACA,KAAKC,cAAL,GAAsB,WAAtB;IACA;;IACA,KAAKC,aAAL,GAAqB,UAArB;EACH;;AAbgB;;AAerBL,cAAc,CAACL,IAAf;EAAA,iBAA2GK,cAA3G;AAAA;;AACAA,cAAc,CAACM,KAAf,kBAjC+FpD,EAiC/F;EAAA,OAA+G8C,cAA/G;EAAA,SAA+GA,cAA/G;EAAA,YAA2I;AAA3I;;AACA;EAAA,mDAlC+F9C,EAkC/F,mBAA2F8C,cAA3F,EAAuH,CAAC;IAC5GH,IAAI,EAAEzC,UADsG;IAE5G0C,IAAI,EAAE,CAAC;MAAES,UAAU,EAAE;IAAd,CAAD;EAFsG,CAAD,CAAvH;AAAA;AAIA;;;AACA,SAASC,iCAAT,CAA2CC,UAA3C,EAAuD;EACnD,OAAOA,UAAU,IAAI,IAAIT,cAAJ,EAArB;AACH;AACD;;;AACA,MAAMU,yBAAyB,GAAG;EAC9BC,OAAO,EAAEX,cADqB;EAE9BY,IAAI,EAAE,CAAC,CAAC,IAAIvD,QAAJ,EAAD,EAAiB,IAAIC,QAAJ,EAAjB,EAAiC0C,cAAjC,CAAD,CAFwB;EAG9Ba,UAAU,EAAEL;AAHkB,CAAlC;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;AACA,MAAMM,kBAAkB,GAAGxC,UAAU,CAAC,MAAMyC,iBAAN,SAAgCtE,aAAhC,CAA8C;EAChFwD,WAAW,CAACe,UAAD,EAAa;IACpB,MAAMA,UAAN;EACH;;AAH+E,CAA/C,EAIlC,SAJkC,CAArC;;AAKA,MAAMC,aAAN,SAA4BH,kBAA5B,CAA+C;EAC3Cb,WAAW,CAACiB,KAAD,EAAQC,aAAR,EAAuBC,WAAvB,EAAoCC,iBAApC,EAAuD;IAC9D,MAAMD,WAAN;IACA,KAAKF,KAAL,GAAaA,KAAb;IACA,KAAKC,aAAL,GAAqBA,aAArB;IACA,KAAKG,iBAAL,GAAyBJ,KAAK,CAAChB,OAAN,CAAcqB,SAAd,CAAwB,MAAMF,iBAAiB,CAACG,YAAlB,EAA9B,CAAzB;EACH;;EACDC,eAAe,GAAG;IACd,KAAKN,aAAL,CAAmBO,OAAnB,CAA2B,KAAKN,WAAhC,EAA6C,IAA7C;EACH;;EACDO,WAAW,GAAG;IACV,KAAKL,iBAAL,CAAuBM,WAAvB;;IACA,KAAKT,aAAL,CAAmBU,cAAnB,CAAkC,KAAKT,WAAvC;EACH;EACD;;;EACAU,KAAK,CAACC,MAAD,EAASC,OAAT,EAAkB;IACnB,IAAID,MAAJ,EAAY;MACR,KAAKZ,aAAL,CAAmBc,QAAnB,CAA4B,KAAKb,WAAjC,EAA8CW,MAA9C,EAAsDC,OAAtD;IACH,CAFD,MAGK;MACD,KAAKZ,WAAL,CAAiBc,aAAjB,CAA+BJ,KAA/B,CAAqCE,OAArC;IACH;EACJ;EACD;;;EACAG,YAAY,GAAG;IACX,OAAO,KAAKC,KAAL,YAAsB1C,YAAtB,GAAqC,IAArC,GAA4C,KAAK0C,KAAxD;EACH;EACD;;;EACAC,cAAc,GAAG;IACb,OAAO,KAAKD,KAAL,YAAsB1C,YAAtB,GAAqC,KAAK0C,KAA1C,GAAkD,IAAzD;EACH;EACD;;;EACAE,eAAe,GAAG;IACd,OAAO,KAAKlB,WAAL,CAAiBc,aAAxB;EACH;EACD;;;EACAK,eAAe,GAAG;IACd,OAAO;MACHC,KAAK,EAAE,KAAKA,KADT;MAEHC,MAAM,EAAE,KAAKA,MAFV;MAGHC,QAAQ,EAAE,KAAKA;IAHZ,CAAP;EAKH;;EACDC,uBAAuB,CAACrD,KAAD,EAAQ;IAC3B,IAAIA,KAAK,IAAI,QAAb,EAAuB;MACnB,OAAQ,GAAE,KAAKkD,KAAL,GAAa,CAAE,EAAzB;IACH;;IACD,IAAIlD,KAAK,IAAI,MAAb,EAAqB;MACjB,OAAO,QAAP;IACH;;IACD,IAAIA,KAAK,IAAI,OAAb,EAAsB;MAClB,OAAO,SAAP;IACH;;IACD,OAAOA,KAAP;EACH;;AAtD0C;;AAwD/C2B,aAAa,CAACtB,IAAd;EAAA,iBAA0GsB,aAA1G,EAvH+F/D,EAuH/F,mBAAyI8C,cAAzI,GAvH+F9C,EAuH/F,mBAAoK0B,EAAE,CAACgE,YAAvK,GAvH+F1F,EAuH/F,mBAAgMA,EAAE,CAAC2F,UAAnM,GAvH+F3F,EAuH/F,mBAA0NA,EAAE,CAAC4F,iBAA7N;AAAA;;AACA7B,aAAa,CAAC8B,IAAd,kBAxH+F7F,EAwH/F;EAAA,MAA8F+D,aAA9F;EAAA;EAAA,oBAAgZ,KAAhZ;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WAxH+F/D,EAwH/F;EAAA;EAAA;EAAA;EAAA;IAAA;MAxH+FA,EAwH+Y,uBAA9e;MAxH+FA,EAwHwjB,sCAAvpB;MAxH+FA,EAwHuwB,8EAAt2B;MAxH+FA,EAwHk7B,8EAAjhC;MAxH+FA,EAwHs5C,iBAAr/C;MAxH+FA,EAwHs6C,4BAArgD;MAxH+FA,EAwHopD,4DAAnvD;MAxH+FA,EAwH63D,4DAA59D;MAxH+FA,EAwH88D,4DAA7iE;MAxH+FA,EAwHujE,4DAAtpE;MAxH+FA,EAwHkpE,eAAjvE;IAAA;;IAAA;MAxH+FA,EAwHyd,8FAAxjB;MAxH+FA,EAwH6jB,aAA5pB;MAxH+FA,EAwH6jB,4EAA5pB;MAxH+FA,EAwHmnB,oDAAltB;MAxH+FA,EAwHwsB,aAAvyB;MAxH+FA,EAwHwsB,8EAAvyB;MAxH+FA,EAwH6xB,aAA53B;MAxH+FA,EAwH6xB,iCAA53B;MAxH+FA,EAwH08C,aAAziD;MAxH+FA,EAwH08C,wIAAziD;MAxH+FA,EAwHwrD,aAAvxD;MAxH+FA,EAwHwrD,yCAAvxD;MAxH+FA,EAwHi6D,aAAhgE;MAxH+FA,EAwHi6D,uCAAhgE;MAxH+FA,EAwHg/D,aAA/kE;MAxH+FA,EAwHg/D,yDAA/kE;MAxH+FA,EAwHgmE,aAA/rE;MAxH+FA,EAwHgmE,yCAA/rE;IAAA;EAAA;EAAA,eAAmrHF,EAAE,CAACgG,IAAtrH,EAAuxHhG,EAAE,CAACiG,gBAA1xH,EAA87HjG,EAAE,CAACkG,QAAj8H,EAAshIlG,EAAE,CAACmG,YAAzhI,EAA0nInG,EAAE,CAACoG,eAA7nI,EAA0sI1E,EAAE,CAAC2E,OAA7sI,EAA+1IhF,EAAE,CAACiF,SAAl2I;EAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDAzH+FpG,EAyH/F,mBAA2F+D,aAA3F,EAAsH,CAAC;IAC3GpB,IAAI,EAAEtC,SADqG;IAE3GuC,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,iBAAZ;MAA+BwD,MAAM,EAAE,CAAC,OAAD,CAAvC;MAAkDC,IAAI,EAAE;QACnD,SAAS,iBAD0C;QAEnD,QAAQ;MAF2C,CAAxD;MAGIC,aAAa,EAAEjG,iBAAiB,CAACkG,IAHrC;MAG2CC,eAAe,EAAElG,uBAAuB,CAACmG,MAHpF;MAG4FC,QAAQ,EAAE,+wDAHtG;MAGu3DC,MAAM,EAAE,CAAC,+3CAAD;IAH/3D,CAAD;EAFqG,CAAD,CAAtH,EAM4B,YAAY;IAAE,OAAO,CAAC;MAAEjE,IAAI,EAAEG;IAAR,CAAD,EAA2B;MAAEH,IAAI,EAAEjB,EAAE,CAACgE;IAAX,CAA3B,EAAsD;MAAE/C,IAAI,EAAE3C,EAAE,CAAC2F;IAAX,CAAtD,EAA+E;MAAEhD,IAAI,EAAE3C,EAAE,CAAC4F;IAAX,CAA/E,CAAP;EAAwH,CANlK,EAMoL;IAAExD,KAAK,EAAE,CAAC;MAC9KO,IAAI,EAAEnC;IADwK,CAAD,CAAT;IAEpK0E,KAAK,EAAE,CAAC;MACRvC,IAAI,EAAEnC;IADE,CAAD,CAF6J;IAIpKqG,YAAY,EAAE,CAAC;MACflE,IAAI,EAAEnC;IADS,CAAD,CAJsJ;IAMpKsG,aAAa,EAAE,CAAC;MAChBnE,IAAI,EAAEnC;IADU,CAAD,CANqJ;IAQpK8E,KAAK,EAAE,CAAC;MACR3C,IAAI,EAAEnC;IADE,CAAD,CAR6J;IAUpKuG,QAAQ,EAAE,CAAC;MACXpE,IAAI,EAAEnC;IADK,CAAD,CAV0J;IAYpK+E,MAAM,EAAE,CAAC;MACT5C,IAAI,EAAEnC;IADG,CAAD,CAZ4J;IAcpKgF,QAAQ,EAAE,CAAC;MACX7C,IAAI,EAAEnC;IADK,CAAD,CAd0J;IAgBpKwG,aAAa,EAAE,CAAC;MAChBrE,IAAI,EAAEnC;IADU,CAAD;EAhBqJ,CANpL;AAAA;AA0BA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMyG,qCAAqC,GAAG,OAA9C;AACA,MAAMC,mCAAmC,GAAG,OAA5C;AACA;AACA;AACA;AACA;;AACA,MAAMC,oBAAoB,GAAG;EACzB;EACAC,wBAAwB,EAAEjF,OAAO,CAAC,0BAAD,EAA6B,CAC1DC,KAAK,CAAC,UAAD,EAAaC,KAAK,CAAC;IAAEgF,SAAS,EAAE,0BAAb;IAAyCC,UAAU,EAAE;EAArD,CAAD,CAAlB,CADqD,EAE1D;EACA;EACA;EACAlF,KAAK,CAAC,SAAD,EAAYC,KAAK,CAAC;IAAEgF,SAAS,EAAE,MAAb;IAAqBC,UAAU,EAAE;EAAjC,CAAD,CAAjB,CALqD,EAM1DlF,KAAK,CAAC,MAAD,EAASC,KAAK,CAAC;IAAEgF,SAAS,EAAE,yBAAb;IAAwCC,UAAU,EAAE;EAApD,CAAD,CAAd,CANqD,EAO1DhF,UAAU,CAAC,QAAD,EAAWC,OAAO,CAAC,sDAAD,CAAlB,EAA4E;IAClFgF,MAAM,EAAE;MAAE,qBAAqBN;IAAvB;EAD0E,CAA5E,CAPgD,CAA7B,CAFR;;EAazB;EACAO,sBAAsB,EAAErF,OAAO,CAAC,wBAAD,EAA2B,CACtDC,KAAK,CAAC,UAAD,EAAaC,KAAK,CAAC;IAAEoF,MAAM,EAAE,KAAV;IAAiBH,UAAU,EAAE;EAA7B,CAAD,CAAlB,CADiD,EAEtDlF,KAAK,CAAC,MAAD,EAASC,KAAK,CAAC;IAAEoF,MAAM,EAAE,KAAV;IAAiBH,UAAU,EAAE;EAA7B,CAAD,CAAd,CAFiD,EAGtD;EACA;EACA;EACAlF,KAAK,CAAC,SAAD,EAAYC,KAAK,CAAC;IAAEoF,MAAM,EAAE,GAAV;IAAeH,UAAU,EAAE;EAA3B,CAAD,CAAjB,CANiD,EAOtDhF,UAAU,CAAC,eAAD,EAAkBC,OAAO,CAAC,sDAAD,CAAzB,EAAmF;IACzFgF,MAAM,EAAE;MAAE,qBAAqBL;IAAvB;EADiF,CAAnF,CAP4C,CAA3B;AAdN,CAA7B;AA2BA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;AACA,MAAMQ,cAAN,CAAqB;EACjB3E,WAAW,CAAC4E,WAAD,EAAc;IACrB,KAAKA,WAAL,GAAmBA,WAAnB;EACH;;AAHgB;;AAKrBD,cAAc,CAACjF,IAAf;EAAA,iBAA2GiF,cAA3G,EA1M+F1H,EA0M/F,mBAA2IA,EAAE,CAAC4H,WAA9I;AAAA;;AACAF,cAAc,CAAChF,IAAf,kBA3M+F1C,EA2M/F;EAAA,MAA+F0H,cAA/F;EAAA;EAAA;IAAA;EAAA;AAAA;;AACA;EAAA,mDA5M+F1H,EA4M/F,mBAA2F0H,cAA3F,EAAuH,CAAC;IAC5G/E,IAAI,EAAE1C,SADsG;IAE5G2C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IADX,CAAD;EAFsG,CAAD,CAAvH,EAK4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAE3C,EAAE,CAAC4H;IAAX,CAAD,CAAP;EAAoC,CAL9E,EAKgG;IAAEC,IAAI,EAAE,CAAC;MACzFlF,IAAI,EAAEnC,KADmF;MAEzFoC,IAAI,EAAE,CAAC,gBAAD;IAFmF,CAAD;EAAR,CALhG;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMkF,cAAN,CAAqB;EACjB/E,WAAW,CAACgF,SAAD,EAAY;IACnB,KAAKA,SAAL,GAAiBA,SAAjB;EACH;;AAHgB;;AAKrBD,cAAc,CAACrF,IAAf;EAAA,iBAA2GqF,cAA3G,EArO+F9H,EAqO/F,mBAA2IA,EAAE,CAAC4H,WAA9I;AAAA;;AACAE,cAAc,CAACpF,IAAf,kBAtO+F1C,EAsO/F;EAAA,MAA+F8H,cAA/F;EAAA;AAAA;;AACA;EAAA,mDAvO+F9H,EAuO/F,mBAA2F8H,cAA3F,EAAuH,CAAC;IAC5GnF,IAAI,EAAE1C,SADsG;IAE5G2C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IADX,CAAD;EAFsG,CAAD,CAAvH,EAK4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAE3C,EAAE,CAAC4H;IAAX,CAAD,CAAP;EAAoC,CAL9E;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMI,OAAN,SAAsBxI,OAAtB,CAA8B;EAC1BuD,WAAW,CAACkF,OAAD,EAAUC,kBAAV,EAA8BC,iBAA9B,EAAiDC,cAAjD,EAAiE;IACxE,MAAMH,OAAN,EAAeG,cAAf;IACA,KAAKF,kBAAL,GAA0BA,kBAA1B;IACA,KAAKC,iBAAL,GAAyBA,iBAAzB;IACA,KAAKE,WAAL,GAAmBzG,YAAY,CAAC0G,KAAhC;EACH;;EACDC,kBAAkB,GAAG;IACjB,KAAKF,WAAL,GAAmB,KAAKG,QAAL,CAAcC,KAAd,CAAoBzF,OAApB,CACd0F,IADc,CACT5G,SAAS,CAAC,MAAM;MACtB,OAAO,KAAK0G,QAAL,CAAcG,eAAd,CAA8BD,IAA9B,CAAmC3G,GAAG,CAAC6G,KAAK,IAAIA,KAAK,CAACC,YAAN,KAAuB,IAAjC,CAAtC,EAA8E7G,SAAS,CAAC,KAAKwG,QAAL,CAAczB,QAAd,KAA2B,IAA5B,CAAvF,CAAP;IACH,CAFkB,CADA,EAId1C,SAJc,CAIJyE,UAAU,IAAI;MACzB,IAAIA,UAAU,IAAI,KAAKC,YAAnB,IAAmC,CAAC,KAAKC,OAA7C,EAAsD;QAClD,KAAKA,OAAL,GAAe,IAAI5J,cAAJ,CAAmB,KAAK2J,YAAL,CAAkBhB,SAArC,EAAgD,KAAKI,iBAArD,CAAf;MACH;IACJ,CARkB,CAAnB;EASH;;EACD1D,WAAW,GAAG;IACV,KAAK4D,WAAL,CAAiB3D,WAAjB;EACH;EACD;;;EACAuE,YAAY,CAACC,OAAD,EAAUC,IAAV,EAAgB;IACxB,MAAMC,kBAAkB,GAAG,KAAKlB,kBAAL,CAAwBe,YAAxB,CAAqCC,OAArC,EAA8CC,IAA9C,CAA3B,CADwB,CAExB;IACA;IACA;;;IACA,MAAME,gBAAgB,GAAG,CAAC,EAAEH,OAAO,IAAIA,OAAO,CAACI,OAAnB,IAA8B,KAAKC,UAArC,CAA1B;IACA,OAAOH,kBAAkB,IAAIC,gBAA7B;EACH;;AA7ByB;;AA+B9BrB,OAAO,CAACvF,IAAR;EAAA,iBAAoGuF,OAApG,EApR+FhI,EAoR/F,mBAA6HS,UAAU,CAAC,MAAM+I,UAAP,CAAvI,GApR+FxJ,EAoR/F,mBAAsKmB,EAAE,CAACE,iBAAzK,MApR+FrB,EAoR/F,mBAAuNA,EAAE,CAACyJ,gBAA1N,GApR+FzJ,EAoR/F,mBAAuPP,sBAAvP;AAAA;;AACAuI,OAAO,CAACnC,IAAR,kBArR+F7F,EAqR/F;EAAA,MAAwFgI,OAAxF;EAAA;EAAA;IAAA;MArR+FhI,EAqR/F,0BAGsEwC,YAHtE;MArR+FxC,EAqR/F,0BAGiK8H,cAHjK;IAAA;;IAAA;MAAA;;MArR+F9H,EAqR/F,qBArR+FA,EAqR/F;MArR+FA,EAqR/F,qBArR+FA,EAqR/F;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA,WArR+FA,EAqR/F,oBAA8J,CACtJ;IAAEyD,OAAO,EAAEpC,iBAAX;IAA8BqI,WAAW,EAAE1B;EAA3C,CADsJ,EAEtJ;IAAEvE,OAAO,EAAEjE,OAAX;IAAoBkK,WAAW,EAAE1B;EAAjC,CAFsJ,CAA9J,GArR+FhI,EAqR/F;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MArR+FA,EAqR/F;MArR+FA,EAwR+K,mEAH9Q;IAAA;EAAA;EAAA,eAGobb,IAAI,CAACwK,eAHzb;EAAA;EAAA;AAAA;;AAIA;EAAA,mDAzR+F3J,EAyR/F,mBAA2FgI,OAA3F,EAAgH,CAAC;IACrGrF,IAAI,EAAEtC,SAD+F;IAErGuC,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,UAAZ;MAAwB+G,SAAS,EAAE,CAC9B;QAAEnG,OAAO,EAAEpC,iBAAX;QAA8BqI,WAAW,EAAE1B;MAA3C,CAD8B,EAE9B;QAAEvE,OAAO,EAAEjE,OAAX;QAAoBkK,WAAW,EAAE1B;MAAjC,CAF8B,CAAnC;MAGIzB,aAAa,EAAEjG,iBAAiB,CAACkG,IAHrC;MAG2CqD,QAAQ,EAAE,SAHrD;MAGgEpD,eAAe,EAAElG,uBAAuB,CAACmG,MAHzG;MAGiHC,QAAQ,EAAE;IAH3H,CAAD;EAF+F,CAAD,CAAhH,EAM4B,YAAY;IAAE,OAAO,CAAC;MAAEhE,IAAI,EAAE6G,UAAR;MAAoBM,UAAU,EAAE,CAAC;QAC/DnH,IAAI,EAAEjC,MADyD;QAE/DkC,IAAI,EAAE,CAACnC,UAAU,CAAC,MAAM+I,UAAP,CAAX;MAFyD,CAAD;IAAhC,CAAD,EAG3B;MAAE7G,IAAI,EAAExB,EAAE,CAACE,iBAAX;MAA8ByI,UAAU,EAAE,CAAC;QAC7CnH,IAAI,EAAEvC;MADuC,CAAD;IAA1C,CAH2B,EAK3B;MAAEuC,IAAI,EAAE3C,EAAE,CAACyJ;IAAX,CAL2B,EAKI;MAAE9G,IAAI,EAAEoH,SAAR;MAAmBD,UAAU,EAAE,CAAC;QACjEnH,IAAI,EAAExC;MAD2D,CAAD,EAEjE;QACCwC,IAAI,EAAEjC,MADP;QAECkC,IAAI,EAAE,CAACnD,sBAAD;MAFP,CAFiE;IAA/B,CALJ,CAAP;EAUlB,CAhBxB,EAgB0C;IAAEuK,SAAS,EAAE,CAAC;MACxCrH,IAAI,EAAEhC,YADkC;MAExCiC,IAAI,EAAE,CAACJ,YAAD;IAFkC,CAAD,CAAb;IAG1ByH,KAAK,EAAE,CAAC;MACRtH,IAAI,EAAEnC;IADE,CAAD,CAHmB;IAK1BuI,YAAY,EAAE,CAAC;MACfpG,IAAI,EAAEhC,YADS;MAEfiC,IAAI,EAAE,CAACkF,cAAD,EAAiB;QAAEoC,MAAM,EAAE;MAAV,CAAjB;IAFS,CAAD;EALY,CAhB1C;AAAA;;AAyBA,MAAMV,UAAN,SAAyB9J,UAAzB,CAAoC;EAChCqD,WAAW,CAACoH,GAAD,EAAMhG,iBAAN,EAAyBL,UAAzB,EAAqC;IAC5C,MAAMqG,GAAN,EAAWhG,iBAAX,EAA8BL,UAA9B;IACA;;IACA,KAAK2E,KAAL,GAAa,IAAI7H,SAAJ,EAAb;IACA;;IACA,KAAKwJ,aAAL,GAAqB,IAAIvJ,YAAJ,EAArB;IACA;AACR;AACA;AACA;;IACQ,KAAKwJ,aAAL,GAAqB,KAArB;IACA;AACR;AACA;AACA;;IACQ,KAAKC,cAAL,GAAsB,KAAtB;IACA;;IACA,KAAKC,cAAL,GAAsB,EAAtB;IACA;;IACA,KAAKC,cAAL,GAAsB,IAAI7I,OAAJ,EAAtB;IACA,KAAK8I,kBAAL,GAA0B,EAA1B;IACA,MAAMC,QAAQ,GAAG5G,UAAU,CAACkB,aAAX,CAAyB0F,QAAzB,CAAkCC,WAAlC,EAAjB;IACA,KAAKC,WAAL,GAAmBF,QAAQ,KAAK,sBAAb,GAAsC,UAAtC,GAAmD,YAAtE;EACH;EACD;;;EACqB,IAAjBG,iBAAiB,GAAG;IACpB,OAAO,KAAKJ,kBAAZ;EACH;;EACoB,IAAjBI,iBAAiB,CAACC,KAAD,EAAQ;IACzB,KAAKL,kBAAL,GAA0B,QAAQM,IAAR,CAAaD,KAAb,IAAsBA,KAAK,GAAG,IAA9B,GAAqCA,KAA/D;EACH;;EACDvC,kBAAkB,GAAG;IACjB,MAAMA,kBAAN;;IACA,KAAKyC,MAAL,CAAYC,OAAZ,CAAoB,CAAC;MAAEpD,IAAF;MAAQF;IAAR,CAAD,KAA4B,KAAK4C,cAAL,CAAoB1C,IAApB,IAA4BF,WAA5E,EAFiB,CAGjB;;;IACA,KAAKc,KAAL,CAAWzF,OAAX,CAAmB0F,IAAnB,CAAwBzG,SAAS,CAAC,KAAKiJ,UAAN,CAAjC,EAAoD7G,SAApD,CAA8D,MAAM;MAChE,KAAK8G,aAAL;IACH,CAFD;;IAGA,KAAKX,cAAL,CACK9B,IADL,EAEA;IACA;IACA;IACAxG,oBAAoB,CAAC,CAACkJ,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACE,SAAF,KAAgBD,CAAC,CAACC,SAAlB,IAA+BF,CAAC,CAACG,OAAF,KAAcF,CAAC,CAACE,OAA1D,CALpB,EAKwFtJ,SAAS,CAAC,KAAKiJ,UAAN,CALjG,EAMK7G,SANL,CAMeuE,KAAK,IAAI;MACpB,IAAIA,KAAK,CAAC2C,OAAN,KAAkB,SAAtB,EAAiC;QAC7B,KAAKnB,aAAL,CAAmBoB,IAAnB;MACH;IACJ,CAVD;EAWH;;EACDC,gBAAgB,CAACnG,KAAD,EAAQoG,IAAR,EAAc;IAC1B,OAAOA,IAAI,CAACC,SAAL,IAAkB,KAAKC,aAAL,KAAuBtG,KAAzC,IAAkD,CAAC,KAAKuG,MAA/D;EACH;;EACDC,qBAAqB,GAAG;IACpB,IAAI,KAAKjB,iBAAT,EAA4B;MACxB,OAAO,KAAKA,iBAAZ;IACH;;IACD,OAAO,KAAKD,WAAL,KAAqB,YAArB,GACD3D,qCADC,GAEDC,mCAFN;EAGH;;AA7D+B;;AA+DpCsC,UAAU,CAAC/G,IAAX;EAAA,iBAAuG+G,UAAvG,EAjX+FxJ,EAiX/F,mBAAmI6B,IAAI,CAACkK,cAAxI,MAjX+F/L,EAiX/F,mBAAmLA,EAAE,CAAC4F,iBAAtL,GAjX+F5F,EAiX/F,mBAAoNA,EAAE,CAAC2F,UAAvN;AAAA;;AACA6D,UAAU,CAAC3D,IAAX,kBAlX+F7F,EAkX/F;EAAA,MAA2FwJ,UAA3F;EAAA;EAAA;IAAA;MAlX+FxJ,EAkX/F,0BAAiiCgI,OAAjiC;MAlX+FhI,EAkX/F,0BAAomC0H,cAApmC;IAAA;;IAAA;MAAA;;MAlX+F1H,EAkX/F,qBAlX+FA,EAkX/F;MAlX+FA,EAkX/F,qBAlX+FA,EAkX/F;IAAA;EAAA;EAAA;IAAA;MAlX+FA,EAkX/F,aAAksC+D,aAAlsC;IAAA;;IAAA;MAAA;;MAlX+F/D,EAkX/F,qBAlX+FA,EAkX/F;IAAA;EAAA;EAAA,oBAA6c,SAA7c;EAAA;EAAA;IAAA;MAlX+FA,EAkX/F;MAlX+FA,EAkX/F;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA,WAlX+FA,EAkX/F,oBAA87B,CAAC;IAAEyD,OAAO,EAAE/D,UAAX;IAAuBgK,WAAW,EAAEF;EAApC,CAAD,CAA97B,GAlX+FxJ,EAkX/F;EAAA;EAAA;EAAA;EAAA;IAAA;MAlX+FA,EAkXgwC,8BAA/1C;MAlX+FA,EAkX40C,yDAA36C;MAlX+FA,EAkXojF,2EAAnpF;MAlX+FA,EAkX6oH,wBAA5uH;MAlX+FA,EAkXisH,iFAlXjsHA,EAkXisH,wBAAhyH;IAAA;;IAAA;MAlX+FA,EAkX8wC,wCAA72C;MAlX+FA,EAkX23C,aAA19C;MAlX+FA,EAkX23C,yCAA19C;MAlX+FA,EAkXmkF,aAAlqF;MAlX+FA,EAkXmkF,uCAAlqF;IAAA;EAAA;EAAA,eAAsuRF,EAAE,CAACkM,OAAzuR,EAAm2RlM,EAAE,CAACgG,IAAt2R,EAAu8RhG,EAAE,CAACiG,gBAA18R,EAA8mSjG,EAAE,CAACkG,QAAjnS,EAAssSlG,EAAE,CAACmG,YAAzsS,EAA0ySlC,aAA1yS;EAAA;EAAA;EAAA;IAAA,WAAu+S,CAC/9SoD,oBAAoB,CAACC,wBAD08S,EAE/9SD,oBAAoB,CAACK,sBAF08S;EAAv+S;EAAA;AAAA;;AAIA;EAAA,mDAtX+FxH,EAsX/F,mBAA2FwJ,UAA3F,EAAmH,CAAC;IACxG7G,IAAI,EAAEtC,SADkG;IAExGuC,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,yEAAZ;MAAuFgH,QAAQ,EAAE,sDAAjG;MAAyJxD,MAAM,EAAE,CAAC,eAAD,CAAjK;MAAoLC,IAAI,EAAE;QACrL,kCAAkC,8BADmJ;QAErL,gCAAgC,4BAFqJ;QAGrL,0CAA0C,wDAH2I;QAIrL,6CAA6C,2DAJwI;QAKrL,8CAA8C,6BALuI;QAMrL,2BAA2B,aAN0J;QAOrL,QAAQ;MAP6K,CAA1L;MAQI2F,UAAU,EAAE,CACX9E,oBAAoB,CAACC,wBADV,EAEXD,oBAAoB,CAACK,sBAFV,CARhB;MAWIoC,SAAS,EAAE,CAAC;QAAEnG,OAAO,EAAE/D,UAAX;QAAuBgK,WAAW,EAAEF;MAApC,CAAD,CAXf;MAWmEjD,aAAa,EAAEjG,iBAAiB,CAACkG,IAXpG;MAW0GC,eAAe,EAAElG,uBAAuB,CAACmG,MAXnJ;MAW2JC,QAAQ,EAAE,wpHAXrK;MAW+zHC,MAAM,EAAE,CAAC,wrHAAD;IAXv0H,CAAD;EAFkG,CAAD,CAAnH,EAc4B,YAAY;IAAE,OAAO,CAAC;MAAEjE,IAAI,EAAEd,IAAI,CAACkK,cAAb;MAA6BjC,UAAU,EAAE,CAAC;QACxEnH,IAAI,EAAExC;MADkE,CAAD;IAAzC,CAAD,EAE3B;MAAEwC,IAAI,EAAE3C,EAAE,CAAC4F;IAAX,CAF2B,EAEK;MAAEjD,IAAI,EAAE3C,EAAE,CAAC2F;IAAX,CAFL,CAAP;EAEuC,CAhBjF,EAgBmG;IAAEuG,WAAW,EAAE,CAAC;MACnGvJ,IAAI,EAAE7B,YAD6F;MAEnG8B,IAAI,EAAE,CAACmB,aAAD;IAF6F,CAAD,CAAf;IAGnFoI,MAAM,EAAE,CAAC;MACTxJ,IAAI,EAAE5B,eADG;MAET6B,IAAI,EAAE,CAACoF,OAAD,EAAU;QAAEoE,WAAW,EAAE;MAAf,CAAV;IAFG,CAAD,CAH2E;IAMnFpB,MAAM,EAAE,CAAC;MACTrI,IAAI,EAAE5B,eADG;MAET6B,IAAI,EAAE,CAAC8E,cAAD,EAAiB;QAAE0E,WAAW,EAAE;MAAf,CAAjB;IAFG,CAAD,CAN2E;IASnFhC,aAAa,EAAE,CAAC;MAChBzH,IAAI,EAAE3B;IADU,CAAD,CAToE;IAWnFgG,aAAa,EAAE,CAAC;MAChBrE,IAAI,EAAEnC;IADU,CAAD,CAXoE;IAanFyJ,KAAK,EAAE,CAAC;MACRtH,IAAI,EAAEnC;IADE,CAAD,CAb4E;IAenF6J,aAAa,EAAE,CAAC;MAChB1H,IAAI,EAAEnC;IADU,CAAD,CAfoE;IAiBnF8J,cAAc,EAAE,CAAC;MACjB3H,IAAI,EAAEnC;IADW,CAAD,CAjBmE;IAmBnFqK,iBAAiB,EAAE,CAAC;MACpBlI,IAAI,EAAEnC;IADc,CAAD;EAnBgE,CAhBnG;AAAA;AAuCA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAM6L,cAAN,SAA6B1M,cAA7B,CAA4C;;AAE5C0M,cAAc,CAAC5J,IAAf;EAAA;EAAA;IAAA,sEAva+FzC,EAua/F,uBAA2GqM,cAA3G,SAA2GA,cAA3G;EAAA;AAAA;;AACAA,cAAc,CAAC3J,IAAf,kBAxa+F1C,EAwa/F;EAAA,MAA+FqM,cAA/F;EAAA;EAAA;EAAA;EAAA;IAAA;MAxa+FrM,EAwa/F;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA,WAxa+FA,EAwa/F;AAAA;;AACA;EAAA,mDAza+FA,EAya/F,mBAA2FqM,cAA3F,EAAuH,CAAC;IAC5G1J,IAAI,EAAE1C,SADsG;IAE5G2C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wBADX;MAECyD,IAAI,EAAE;QACF,SAAS,kBADP;QAEF,UAAU;MAFR,CAFP;MAMCD,MAAM,EAAE,CAAC,MAAD;IANT,CAAD;EAFsG,CAAD,CAAvH;AAAA;AAWA;;;AACA,MAAMiG,kBAAN,SAAiC1M,kBAAjC,CAAoD;;AAEpD0M,kBAAkB,CAAC7J,IAAnB;EAAA;EAAA;IAAA,8EAvb+FzC,EAub/F,uBAA+GsM,kBAA/G,SAA+GA,kBAA/G;EAAA;AAAA;;AACAA,kBAAkB,CAAC5J,IAAnB,kBAxb+F1C,EAwb/F;EAAA,MAAmGsM,kBAAnG;EAAA;EAAA;EAAA;EAAA;IAAA;MAxb+FtM,EAwb/F;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA,WAxb+FA,EAwb/F;AAAA;;AACA;EAAA,mDAzb+FA,EAyb/F,mBAA2FsM,kBAA3F,EAA2H,CAAC;IAChH3J,IAAI,EAAE1C,SAD0G;IAEhH2C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,4BADX;MAECyD,IAAI,EAAE;QACF,SAAS,sBADP;QAEF,UAAU;MAFR,CAFP;MAMCD,MAAM,EAAE,CAAC,MAAD;IANT,CAAD;EAF0G,CAAD,CAA3H;AAAA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMkG,gBAAN,CAAuB;;AAEvBA,gBAAgB,CAAC9J,IAAjB;EAAA,iBAA6G8J,gBAA7G;AAAA;;AACAA,gBAAgB,CAACC,IAAjB,kBA/c+FxM,EA+c/F;EAAA,MAA8GuM,gBAA9G;EAAA,eAA+IvE,OAA/I,EACQxF,YADR,EAEQgH,UAFR,EAGQ6C,cAHR,EAIQC,kBAJR,EAKQvI,aALR,EAMQ2D,cANR,EAOQI,cAPR;EAAA,UAOmCxG,eAPnC,EAQQvB,YARR,EASQV,YATR,EAUQ6B,eAVR,EAWQrB,gBAXR,EAYQ4B,aAZR,EAaQF,eAbR;EAAA,UAaoCD,eAbpC,EAcQ0G,OAdR,EAeQxF,YAfR,EAgBQgH,UAhBR,EAiBQ6C,cAjBR,EAkBQC,kBAlBR,EAmBQvI,aAnBR,EAoBQ2D,cApBR,EAqBQI,cArBR;AAAA;AAsBAyE,gBAAgB,CAACE,IAAjB,kBAre+FzM,EAqe/F;EAAA,WAA2I,CAACwD,yBAAD,EAA4BnC,iBAA5B,CAA3I;EAAA,UAAqMC,eAArM,EACQvB,YADR,EAEQV,YAFR,EAGQ6B,eAHR,EAIQrB,gBAJR,EAKQ4B,aALR,EAMQF,eANR,EAMyBD,eANzB;AAAA;;AAOA;EAAA,mDA5e+FtB,EA4e/F,mBAA2FuM,gBAA3F,EAAyH,CAAC;IAC9G5J,IAAI,EAAE1B,QADwG;IAE9G2B,IAAI,EAAE,CAAC;MACC8J,OAAO,EAAE,CACLpL,eADK,EAELvB,YAFK,EAGLV,YAHK,EAIL6B,eAJK,EAKLrB,gBALK,EAML4B,aANK,EAOLF,eAPK,CADV;MAUCoL,OAAO,EAAE,CACLrL,eADK,EAEL0G,OAFK,EAGLxF,YAHK,EAILgH,UAJK,EAKL6C,cALK,EAMLC,kBANK,EAOLvI,aAPK,EAQL2D,cARK,EASLI,cATK,CAVV;MAqBC8E,YAAY,EAAE,CACV5E,OADU,EAEVxF,YAFU,EAGVgH,UAHU,EAIV6C,cAJU,EAKVC,kBALU,EAMVvI,aANU,EAOV2D,cAPU,EAQVI,cARU,CArBf;MA+BC8B,SAAS,EAAE,CAACpG,yBAAD,EAA4BnC,iBAA5B;IA/BZ,CAAD;EAFwG,CAAD,CAAzH;AAAA;AAqCA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASmC,yBAAT,EAAoCF,iCAApC,EAAuE0E,OAAvE,EAAgFF,cAAhF,EAAgG/D,aAAhG,EAA+GvB,YAA/G,EAA6HgH,UAA7H,EAAyI9B,cAAzI,EAAyJ5E,cAAzJ,EAAyKyJ,gBAAzK,EAA2LF,cAA3L,EAA2MC,kBAA3M,EAA+NnF,oBAA/N"}, "metadata": {}, "sourceType": "module"}