{"ast": null, "code": "import { Observable } from '../Observable';\nimport { async } from '../scheduler/async';\nimport { isNumeric } from '../util/isNumeric';\nexport function interval(period = 0, scheduler = async) {\n  if (!isNumeric(period) || period < 0) {\n    period = 0;\n  }\n\n  if (!scheduler || typeof scheduler.schedule !== 'function') {\n    scheduler = async;\n  }\n\n  return new Observable(subscriber => {\n    subscriber.add(scheduler.schedule(dispatch, period, {\n      subscriber,\n      counter: 0,\n      period\n    }));\n    return subscriber;\n  });\n}\n\nfunction dispatch(state) {\n  const {\n    subscriber,\n    counter,\n    period\n  } = state;\n  subscriber.next(counter);\n  this.schedule({\n    subscriber,\n    counter: counter + 1,\n    period\n  }, period);\n}", "map": {"version": 3, "names": ["Observable", "async", "isNumeric", "interval", "period", "scheduler", "schedule", "subscriber", "add", "dispatch", "counter", "state", "next"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/interval.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { async } from '../scheduler/async';\nimport { isNumeric } from '../util/isNumeric';\nexport function interval(period = 0, scheduler = async) {\n    if (!isNumeric(period) || period < 0) {\n        period = 0;\n    }\n    if (!scheduler || typeof scheduler.schedule !== 'function') {\n        scheduler = async;\n    }\n    return new Observable(subscriber => {\n        subscriber.add(scheduler.schedule(dispatch, period, { subscriber, counter: 0, period }));\n        return subscriber;\n    });\n}\nfunction dispatch(state) {\n    const { subscriber, counter, period } = state;\n    subscriber.next(counter);\n    this.schedule({ subscriber, counter: counter + 1, period }, period);\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,KAAT,QAAsB,oBAAtB;AACA,SAASC,SAAT,QAA0B,mBAA1B;AACA,OAAO,SAASC,QAAT,CAAkBC,MAAM,GAAG,CAA3B,EAA8BC,SAAS,GAAGJ,KAA1C,EAAiD;EACpD,IAAI,CAACC,SAAS,CAACE,MAAD,CAAV,IAAsBA,MAAM,GAAG,CAAnC,EAAsC;IAClCA,MAAM,GAAG,CAAT;EACH;;EACD,IAAI,CAACC,SAAD,IAAc,OAAOA,SAAS,CAACC,QAAjB,KAA8B,UAAhD,EAA4D;IACxDD,SAAS,GAAGJ,KAAZ;EACH;;EACD,OAAO,IAAID,UAAJ,CAAeO,UAAU,IAAI;IAChCA,UAAU,CAACC,GAAX,CAAeH,SAAS,CAACC,QAAV,CAAmBG,QAAnB,EAA6BL,MAA7B,EAAqC;MAAEG,UAAF;MAAcG,OAAO,EAAE,CAAvB;MAA0BN;IAA1B,CAArC,CAAf;IACA,OAAOG,UAAP;EACH,CAHM,CAAP;AAIH;;AACD,SAASE,QAAT,CAAkBE,KAAlB,EAAyB;EACrB,MAAM;IAAEJ,UAAF;IAAcG,OAAd;IAAuBN;EAAvB,IAAkCO,KAAxC;EACAJ,UAAU,CAACK,IAAX,CAAgBF,OAAhB;EACA,KAAKJ,QAAL,CAAc;IAAEC,UAAF;IAAcG,OAAO,EAAEA,OAAO,GAAG,CAAjC;IAAoCN;EAApC,CAAd,EAA4DA,MAA5D;AACH"}, "metadata": {}, "sourceType": "module"}