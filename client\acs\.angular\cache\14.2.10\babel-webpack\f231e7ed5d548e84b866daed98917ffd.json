{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./other-business-filing-services.component.html?ngResource\";\nimport { Component } from '@angular/core';\nimport { FilingApiService } from '../../Services/FilingApiService';\nimport { LoaderService } from 'src/app/Modules/Shared/Services/Common/loader.service';\nimport { SubCategoryCode } from 'src/app/Modules/Shared/Enums/sub-category-code.enum';\nimport { LandingPageServiceRouting } from 'src/app/Modules/Shared/Enums/landing-page-service-routing.enum';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { CategoryCode } from 'src/app/Modules/Shared/Enums/category-code.enum';\nimport { ViewportScroller } from '@angular/common';\nlet OtherBusinessFilingServicesComponent = class OtherBusinessFilingServicesComponent {\n  constructor(router, route, loader, otherBuisnessService, viewportScroller) {\n    this.router = router;\n    this.route = route;\n    this.loader = loader;\n    this.otherBuisnessService = otherBuisnessService;\n    this.viewportScroller = viewportScroller;\n    this.otherBuisnessFilingList = [];\n  }\n\n  ngOnInit() {\n    this.GetOtherBuisnessFilingData();\n  }\n\n  groupBy(inventory) {\n    return _asyncToGenerator(function* () {\n      const groupedInventory = {};\n      inventory.forEach(item => {\n        if (groupedInventory[item.group]) {\n          groupedInventory[item.group].push(item);\n        } else {\n          groupedInventory[item.group] = [item];\n        }\n      });\n      let transformedData = [groupedInventory].flatMap(entry => Object.keys(entry).map(key => ({\n        service: key,\n        id: entry[key][0].id,\n        items: entry[key]\n      })));\n      return transformedData;\n    })();\n  }\n\n  GetOtherBuisnessFilingData() {\n    var _this = this;\n\n    this.otherBuisnessService.GetOtherFilingServices().subscribe( /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (res) {\n        const products = res.map(product => {\n          if ([\"001\", \"002\", \"003\"].includes(product.categoryCode) && product.productName === \"\") {\n            return { ...product,\n              group: \"Formations\",\n              id: \"formations\"\n            };\n          } else if ([\"004\", \"FOT\"].includes(product.categoryCode) && [\"\", \"902\", \"903\", \"904\", \"905\"].includes(product.productName)) {\n            return { ...product,\n              group: \"Maintain the Entity\",\n              id: \"maintain-the-entity\"\n            };\n          } else {\n            return { ...product,\n              group: \"Stay in Compliance\",\n              id: \"stay-in-compliance\"\n            };\n          }\n        });\n        _this.otherBuisnessFilingList = yield _this.groupBy(products);\n        setTimeout(() => {\n          _this.route.fragment.subscribe(fragment => {\n            if (fragment) {\n              _this.scrollToCustomPosition(fragment);\n            }\n          });\n        }, 100);\n\n        _this.loader.hide();\n      });\n\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }()), error => {};\n  }\n\n  scrollToCustomPosition(fragment) {\n    const element = document.getElementById(fragment);\n\n    if (element) {\n      // Get the element's position and calculate a custom offset\n      const elementRect = element.getBoundingClientRect();\n      const absoluteElementTop = elementRect.top + window.scrollY; // Absolute top position of the element\n\n      const middleOffset = window.innerHeight / 2 - elementRect.height / 2; // Middle offset\n\n      const customOffset = middleOffset / 2; // Adjust the offset (closer to the top)\n      // Scroll the window to the custom position\n\n      window.scrollTo({\n        top: absoluteElementTop - customOffset,\n        behavior: 'smooth'\n      });\n    }\n  }\n\n  ListCss(otherBuisnessFiling) {\n    if (otherBuisnessFiling.productName == SubCategoryCode.annualreport) {\n      return \"icon-annual-report\";\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.conversion) {\n      return \"icon-conversion\";\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.dissolution) {\n      return \"icon-dissolution\";\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.employeeidntificationnumber) {\n      return \"icon-ein\";\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.fileamendment) {\n      return \"icon-amendment\";\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.beneficialownership) {\n      return \"icon-boi\";\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.filemerger) {\n      return \"icon-merger\";\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.namecheckreservation) {\n      return \"icon-name-check\";\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.registeredagent) {\n      return \"icon-registered-agent\";\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.retrievefielddocs) {\n      return \"icon-retrieve\";\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.uccfiling) {\n      return \"icon-ucc-filing\";\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.uccsearch) {\n      return \"icon-ucc-search\";\n    } else if (otherBuisnessFiling.categoryCode == CategoryCode.dba) {\n      return \"icon-dba\";\n    } else if (otherBuisnessFiling.categoryCode == CategoryCode.formcorporation) {\n      return \"icon-incorporation\";\n    } else if (otherBuisnessFiling.categoryCode == CategoryCode.formllc) {\n      return \"icon-llc\";\n    } else if (otherBuisnessFiling.categoryCode == CategoryCode.formpartnership) {\n      return \"icon-partnership\";\n    }\n  }\n\n  routerUrlService(otherBuisnessFiling) {\n    if (otherBuisnessFiling.productName == SubCategoryCode.annualreport) {\n      this.router.navigate([LandingPageServiceRouting.annualreport]);\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.conversion) {\n      this.router.navigate([LandingPageServiceRouting.conversion]);\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.dissolution) {\n      this.router.navigate([LandingPageServiceRouting.dissolution]);\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.employeeidntificationnumber) {\n      this.router.navigate([LandingPageServiceRouting.employeeidentification]);\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.fileamendment) {\n      this.router.navigate([LandingPageServiceRouting.fileAmendment]);\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.beneficialownership) {\n      this.router.navigate([LandingPageServiceRouting.beneficialOwnership]);\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.filemerger) {\n      this.router.navigate([LandingPageServiceRouting.fileMerger]);\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.namecheckreservation) {\n      this.router.navigate([LandingPageServiceRouting.namecheckreservation]);\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.registeredagent) {\n      this.router.navigate([LandingPageServiceRouting.registeredagent]);\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.retrievefielddocs) {\n      this.router.navigate([LandingPageServiceRouting.retrievefielddocs]);\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.uccfiling) {\n      this.router.navigate([LandingPageServiceRouting.uccfiling]);\n    } else if (otherBuisnessFiling.productName == SubCategoryCode.uccsearch) {\n      this.router.navigate([LandingPageServiceRouting.uccsearch]);\n    } else if (otherBuisnessFiling.categoryCode == CategoryCode.dba) {\n      this.router.navigate([LandingPageServiceRouting.DBA]);\n    } else if (otherBuisnessFiling.categoryCode == CategoryCode.formcorporation) {\n      this.router.navigate([LandingPageServiceRouting.formcorporation]);\n    } else if (otherBuisnessFiling.categoryCode == CategoryCode.formllc) {\n      this.router.navigate([LandingPageServiceRouting.formllc]);\n    } else if (otherBuisnessFiling.categoryCode == CategoryCode.formpartnership) {\n      this.router.navigate([LandingPageServiceRouting.formPartnership]);\n    } else {\n      return '#';\n    } //return \"/\"+otherBuisnessFiling.description.split(' ').join('-').toLowerCase()\n\n  }\n\n};\n\nOtherBusinessFilingServicesComponent.ctorParameters = () => [{\n  type: Router\n}, {\n  type: ActivatedRoute\n}, {\n  type: LoaderService\n}, {\n  type: FilingApiService\n}, {\n  type: ViewportScroller\n}];\n\nOtherBusinessFilingServicesComponent = __decorate([Component({\n  selector: 'app-other-business-filing-services',\n  template: __NG_CLI_RESOURCE__0\n})], OtherBusinessFilingServicesComponent);\nexport { OtherBusinessFilingServicesComponent };", "map": {"version": 3, "mappings": ";;;AAAA,SAASA,SAAT,QAAkC,eAAlC;AACA,SAASC,gBAAT,QAAiC,iCAAjC;AAEA,SAASC,aAAT,QAA8B,uDAA9B;AACA,SAASC,eAAT,QAAgC,qDAAhC;AACA,SAASC,yBAAT,QAA0C,gEAA1C;AACA,SAASC,cAAT,EAAyBC,MAAzB,QAAuC,iBAAvC;AACA,SAASC,YAAT,QAA6B,iDAA7B;AACA,SAASC,gBAAT,QAAiC,iBAAjC;IAMaC,oCAAoC,SAApCA,oCAAoC;EAG/CC,YACUC,MADV,EAEUC,KAFV,EAGUC,MAHV,EAIUC,oBAJV,EAKUC,gBALV,EAK4C;IAJlC;IACA;IACA;IACA;IACA;IAPV,+BAAiC,EAAjC;EAQK;;EAELC,QAAQ;IACN,KAAKC,0BAAL;EACD;;EAEKC,OAAO,CAACC,SAAD,EAAiB;IAAA;MAC5B,MAAMC,gBAAgB,GAAG,EAAzB;MACAD,SAAS,CAACE,OAAV,CAAmBC,IAAD,IAAS;QACzB,IAAIF,gBAAgB,CAACE,IAAI,CAACC,KAAN,CAApB,EAAkC;UAChCH,gBAAgB,CAACE,IAAI,CAACC,KAAN,CAAhB,CAA6BC,IAA7B,CAAkCF,IAAlC;QACD,CAFD,MAEO;UACLF,gBAAgB,CAACE,IAAI,CAACC,KAAN,CAAhB,GAA+B,CAACD,IAAD,CAA/B;QACD;MACF,CAND;MAQA,IAAIG,eAAe,GAAG,CAACL,gBAAD,EAAmBM,OAAnB,CAA4BC,KAAD,IAC/CC,MAAM,CAACC,IAAP,CAAYF,KAAZ,EAAmBG,GAAnB,CAAwBC,GAAD,KAAU;QAC/BC,OAAO,EAAED,GADsB;QAE/BE,EAAE,EAAEN,KAAK,CAACI,GAAD,CAAL,CAAW,CAAX,EAAcE,EAFa;QAG/BC,KAAK,EAAEP,KAAK,CAACI,GAAD;MAHmB,CAAV,CAAvB,CADoB,CAAtB;MAQA,OAAON,eAAP;IAlB4B;EAoB7B;;EAEDR,0BAA0B;IAAA;;IACxB,KAAKH,oBAAL,CAA0BqB,sBAA1B,GAAmDC,SAAnD;MAAA,6BAA6D,WAAOC,GAAP,EAAmC;QAE9F,MAAMC,QAAQ,GAAGD,GAAG,CAACP,GAAJ,CAAQS,OAAO,IAAG;UACjC,IACE,CAAC,KAAD,EAAQ,KAAR,EAAe,KAAf,EAAsBC,QAAtB,CAA+BD,OAAO,CAACE,YAAvC,KACAF,OAAO,CAACG,WAAR,KAAwB,EAF1B,EAGE;YACA,OAAO,EACL,GAAGH,OADE;cACOhB,KAAK,EAAE,YADd;cAC4BU,EAAE,EAAE;YADhC,CAAP;UAGD,CAPD,MAOO,IACL,CAAC,KAAD,EAAQ,KAAR,EAAeO,QAAf,CAAwBD,OAAO,CAACE,YAAhC,KACA,CAAC,EAAD,EAAK,KAAL,EAAY,KAAZ,EAAmB,KAAnB,EAA0B,KAA1B,EAAiCD,QAAjC,CAA0CD,OAAO,CAACG,WAAlD,CAFK,EAGL;YACA,OAAO,EAAE,GAAGH,OAAL;cAAchB,KAAK,EAAE,qBAArB;cAA4CU,EAAE,EAAE;YAAhD,CAAP;UACD,CALM,MAKA;YACL,OAAO,EAAE,GAAGM,OAAL;cAAchB,KAAK,EAAE,oBAArB;cAA2CU,EAAE,EAAE;YAA/C,CAAP;UACD;QACF,CAhBgB,CAAjB;QAkBA,KAAI,CAACU,uBAAL,SAAqC,KAAI,CAACzB,OAAL,CAAaoB,QAAb,CAArC;QAEAM,UAAU,CAAC,MAAK;UACd,KAAI,CAAChC,KAAL,CAAWiC,QAAX,CAAoBT,SAApB,CAA+BS,QAAD,IAAa;YACzC,IAAIA,QAAJ,EAAc;cACZ,KAAI,CAACC,sBAAL,CAA4BD,QAA5B;YACD;UACF,CAJD;QAKD,CANS,EAMP,GANO,CAAV;;QAQA,KAAI,CAAChC,MAAL,CAAYkC,IAAZ;MACD,CA/BD;;MAAA;QAAA;MAAA;IAAA,MAgCEC,KAAK,IAAG,CAEP,CAlCH;EAmCD;;EAEDF,sBAAsB,CAACD,QAAD,EAAiB;IACrC,MAAMI,OAAO,GAAGC,QAAQ,CAACC,cAAT,CAAwBN,QAAxB,CAAhB;;IACA,IAAII,OAAJ,EAAa;MACX;MACA,MAAMG,WAAW,GAAGH,OAAO,CAACI,qBAAR,EAApB;MACA,MAAMC,kBAAkB,GAAGF,WAAW,CAACG,GAAZ,GAAkBC,MAAM,CAACC,OAApD,CAHW,CAGkD;;MAE7D,MAAMC,YAAY,GAAGF,MAAM,CAACG,WAAP,GAAqB,CAArB,GAAyBP,WAAW,CAACQ,MAAZ,GAAqB,CAAnE,CALW,CAK2D;;MACtE,MAAMC,YAAY,GAAGH,YAAY,GAAG,CAApC,CANW,CAM4B;MAEvC;;MACAF,MAAM,CAACM,QAAP,CAAgB;QACdP,GAAG,EAAED,kBAAkB,GAAGO,YADZ;QAEdE,QAAQ,EAAE;MAFI,CAAhB;IAID;EACF;;EAGDC,OAAO,CAACC,mBAAD,EAAuC;IAC5C,IAAIA,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAAC+D,YAAvD,EAAqE;MACnE,OAAO,oBAAP;IACD,CAFD,MAGK,IAAID,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAACgE,UAAvD,EAAmE;MACtE,OAAO,iBAAP;IACD,CAFI,MAGA,IAAIF,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAACiE,WAAvD,EAAoE;MACvE,OAAO,kBAAP;IACD,CAFI,MAGA,IAAIH,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAACkE,2BAAvD,EAAoF;MACvF,OAAO,UAAP;IACD,CAFI,MAGA,IAAIJ,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAACmE,aAAvD,EAAsE;MACzE,OAAO,gBAAP;IACD,CAFI,MAGA,IAAIL,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAACoE,mBAAvD,EAA4E;MAC/E,OAAO,UAAP;IACD,CAFI,MAGA,IAAIN,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAACqE,UAAvD,EAAmE;MACtE,OAAO,aAAP;IACD,CAFI,MAGA,IAAIP,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAACsE,oBAAvD,EAA6E;MAChF,OAAO,iBAAP;IACD,CAFI,MAGA,IAAIR,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAACuE,eAAvD,EAAwE;MAC3E,OAAO,uBAAP;IACD,CAFI,MAGA,IAAIT,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAACwE,iBAAvD,EAA0E;MAC7E,OAAO,eAAP;IACD,CAFI,MAGA,IAAIV,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAACyE,SAAvD,EAAkE;MACrE,OAAO,iBAAP;IACD,CAFI,MAGA,IAAIX,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAAC0E,SAAvD,EAAkE;MACrE,OAAO,iBAAP;IACD,CAFI,MAGA,IAAIZ,mBAAmB,CAACxB,YAApB,IAAoClC,YAAY,CAACuE,GAArD,EAA0D;MAC7D,OAAO,UAAP;IACD,CAFI,MAGA,IAAIb,mBAAmB,CAACxB,YAApB,IAAoClC,YAAY,CAACwE,eAArD,EAAsE;MACzE,OAAO,oBAAP;IACD,CAFI,MAGA,IAAId,mBAAmB,CAACxB,YAApB,IAAoClC,YAAY,CAACyE,OAArD,EAA8D;MACjE,OAAO,UAAP;IACD,CAFI,MAGA,IAAIf,mBAAmB,CAACxB,YAApB,IAAoClC,YAAY,CAAC0E,eAArD,EAAsE;MACzE,OAAO,kBAAP;IACD;EAGF;;EAEDC,gBAAgB,CAACjB,mBAAD,EAAuC;IACrD,IAAIA,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAAC+D,YAAvD,EAAqE;MACnE,KAAKvD,MAAL,CAAYwE,QAAZ,CAAqB,CAAC/E,yBAAyB,CAAC8D,YAA3B,CAArB;IACD,CAFD,MAGK,IAAID,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAACgE,UAAvD,EAAmE;MACtE,KAAKxD,MAAL,CAAYwE,QAAZ,CAAqB,CAAC/E,yBAAyB,CAAC+D,UAA3B,CAArB;IACD,CAFI,MAGA,IAAIF,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAACiE,WAAvD,EAAoE;MACvE,KAAKzD,MAAL,CAAYwE,QAAZ,CAAqB,CAAC/E,yBAAyB,CAACgE,WAA3B,CAArB;IACD,CAFI,MAGA,IAAIH,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAACkE,2BAAvD,EAAoF;MACvF,KAAK1D,MAAL,CAAYwE,QAAZ,CAAqB,CAAC/E,yBAAyB,CAACgF,sBAA3B,CAArB;IACD,CAFI,MAGA,IAAInB,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAACmE,aAAvD,EAAsE;MACzE,KAAK3D,MAAL,CAAYwE,QAAZ,CAAqB,CAAC/E,yBAAyB,CAACiF,aAA3B,CAArB;IACD,CAFI,MAGA,IAAIpB,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAACoE,mBAAvD,EAA4E;MAC/E,KAAK5D,MAAL,CAAYwE,QAAZ,CAAqB,CAAC/E,yBAAyB,CAACkF,mBAA3B,CAArB;IACD,CAFI,MAGA,IAAIrB,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAACqE,UAAvD,EAAmE;MACtE,KAAK7D,MAAL,CAAYwE,QAAZ,CAAqB,CAAC/E,yBAAyB,CAACmF,UAA3B,CAArB;IACD,CAFI,MAGA,IAAItB,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAACsE,oBAAvD,EAA6E;MAChF,KAAK9D,MAAL,CAAYwE,QAAZ,CAAqB,CAAC/E,yBAAyB,CAACqE,oBAA3B,CAArB;IACD,CAFI,MAGA,IAAIR,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAACuE,eAAvD,EAAwE;MAC3E,KAAK/D,MAAL,CAAYwE,QAAZ,CAAqB,CAAC/E,yBAAyB,CAACsE,eAA3B,CAArB;IACD,CAFI,MAGA,IAAIT,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAACwE,iBAAvD,EAA0E;MAC7E,KAAKhE,MAAL,CAAYwE,QAAZ,CAAqB,CAAC/E,yBAAyB,CAACuE,iBAA3B,CAArB;IACD,CAFI,MAGA,IAAIV,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAACyE,SAAvD,EAAkE;MACrE,KAAKjE,MAAL,CAAYwE,QAAZ,CAAqB,CAAC/E,yBAAyB,CAACwE,SAA3B,CAArB;IACD,CAFI,MAGA,IAAIX,mBAAmB,CAACvB,WAApB,IAAmCvC,eAAe,CAAC0E,SAAvD,EAAkE;MACrE,KAAKlE,MAAL,CAAYwE,QAAZ,CAAqB,CAAC/E,yBAAyB,CAACyE,SAA3B,CAArB;IACD,CAFI,MAGA,IAAIZ,mBAAmB,CAACxB,YAApB,IAAoClC,YAAY,CAACuE,GAArD,EAA0D;MAC7D,KAAKnE,MAAL,CAAYwE,QAAZ,CAAqB,CAAC/E,yBAAyB,CAACoF,GAA3B,CAArB;IACD,CAFI,MAGA,IAAIvB,mBAAmB,CAACxB,YAApB,IAAoClC,YAAY,CAACwE,eAArD,EAAsE;MACzE,KAAKpE,MAAL,CAAYwE,QAAZ,CAAqB,CAAC/E,yBAAyB,CAAC2E,eAA3B,CAArB;IACD,CAFI,MAGA,IAAId,mBAAmB,CAACxB,YAApB,IAAoClC,YAAY,CAACyE,OAArD,EAA8D;MACjE,KAAKrE,MAAL,CAAYwE,QAAZ,CAAqB,CAAC/E,yBAAyB,CAAC4E,OAA3B,CAArB;IACD,CAFI,MAGA,IAAIf,mBAAmB,CAACxB,YAApB,IAAoClC,YAAY,CAAC0E,eAArD,EAAsE;MACzE,KAAKtE,MAAL,CAAYwE,QAAZ,CAAqB,CAAC/E,yBAAyB,CAACqF,eAA3B,CAArB;IACD,CAFI,MAGA;MACH,OAAO,GAAP;IACD,CAnDoD,CAoDrD;;EACD;;AAxM8C;;;;;;;;;;;;;;AAApChF,oCAAoC,eAJhDT,SAAS,CAAC;EACT0F,QAAQ,EAAE,oCADD;EAETC;AAFS,CAAD,CAIuC,GAApClF,oCAAoC,CAApC;SAAAA", "names": ["Component", "FilingApiService", "LoaderService", "SubCategoryCode", "LandingPageServiceRouting", "ActivatedRoute", "Router", "CategoryCode", "ViewportScroller", "OtherBusinessFilingServicesComponent", "constructor", "router", "route", "loader", "otherBuisnessService", "viewportScroller", "ngOnInit", "GetOtherBuisnessFilingData", "groupBy", "inventory", "groupedInventory", "for<PERSON>ach", "item", "group", "push", "transformedData", "flatMap", "entry", "Object", "keys", "map", "key", "service", "id", "items", "GetOtherFilingServices", "subscribe", "res", "products", "product", "includes", "categoryCode", "productName", "otherBuisnessFilingList", "setTimeout", "fragment", "scrollToCustomPosition", "hide", "error", "element", "document", "getElementById", "elementRect", "getBoundingClientRect", "absoluteElementTop", "top", "window", "scrollY", "middleOffset", "innerHeight", "height", "customOffset", "scrollTo", "behavior", "ListCss", "otherBuisnessFiling", "annualreport", "conversion", "dissolution", "employeeidntificationnumber", "fileamendment", "beneficialownership", "filemerger", "namecheckreservation", "registeredagent", "retrievefielddocs", "uccfiling", "uccsearch", "dba", "formcorporation", "formllc", "formpartnership", "routerUrlService", "navigate", "employeeidentification", "fileAmendment", "beneficialOwnership", "fileMerger", "DBA", "formPartnership", "selector", "template"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\viatech\\Ecom\\client\\acs\\src\\app\\Modules\\FilingService\\Pages\\other-business-filing-services\\other-business-filing-services.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FilingApiService } from '../../Services/FilingApiService'\r\nimport { OtherBuisnessList } from '../../Models/OtherBuisnessList';\r\nimport { LoaderService } from 'src/app/Modules/Shared/Services/Common/loader.service';\r\nimport { SubCategoryCode } from 'src/app/Modules/Shared/Enums/sub-category-code.enum';\r\nimport { LandingPageServiceRouting } from 'src/app/Modules/Shared/Enums/landing-page-service-routing.enum';\r\nimport { ActivatedRoute, Router } from '@angular/router'\r\nimport { CategoryCode } from 'src/app/Modules/Shared/Enums/category-code.enum';\r\nimport { ViewportScroller } from '@angular/common';\r\n\r\n@Component({\r\n  selector: 'app-other-business-filing-services',\r\n  templateUrl: './other-business-filing-services.component.html'\r\n})\r\nexport class OtherBusinessFilingServicesComponent implements OnInit {\r\n  otherBuisnessFilingList: any[] = []\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private loader: LoaderService,\r\n    private otherBuisnessService: FilingApiService,\r\n    private viewportScroller: ViewportScroller\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.GetOtherBuisnessFilingData();\r\n  }\r\n\r\n  async groupBy(inventory: any[]) {\r\n    const groupedInventory = {};\r\n    inventory.forEach((item) => {\r\n      if (groupedInventory[item.group]) {\r\n        groupedInventory[item.group].push(item);\r\n      } else {\r\n        groupedInventory[item.group] = [item];\r\n      }\r\n    });\r\n\r\n    let transformedData = [groupedInventory].flatMap((entry) =>\r\n      Object.keys(entry).map((key) => ({\r\n        service: key,\r\n        id: entry[key][0].id,\r\n        items: entry[key],\r\n      }))\r\n    );\r\n\r\n    return transformedData;\r\n\r\n  }\r\n\r\n  GetOtherBuisnessFilingData() {\r\n    this.otherBuisnessService.GetOtherFilingServices().subscribe(async (res: OtherBuisnessList[]) => {\r\n\r\n      const products = res.map(product => {\r\n        if (\r\n          [\"001\", \"002\", \"003\"].includes(product.categoryCode) &&\r\n          product.productName === \"\"\r\n        ) {\r\n          return {\r\n            ...product, group: \"Formations\", id: \"formations\"\r\n          };\r\n        } else if (\r\n          [\"004\", \"FOT\"].includes(product.categoryCode) &&\r\n          [\"\", \"902\", \"903\", \"904\", \"905\"].includes(product.productName)\r\n        ) {\r\n          return { ...product, group: \"Maintain the Entity\", id: \"maintain-the-entity\" };\r\n        } else {\r\n          return { ...product, group: \"Stay in Compliance\", id: \"stay-in-compliance\" };\r\n        }\r\n      });\r\n\r\n      this.otherBuisnessFilingList = await this.groupBy(products);\r\n\r\n      setTimeout(() => {\r\n        this.route.fragment.subscribe((fragment) => {\r\n          if (fragment) {\r\n            this.scrollToCustomPosition(fragment);\r\n          }\r\n        });\r\n      }, 100);\r\n\r\n      this.loader.hide();\r\n    }),\r\n      error => {\r\n\r\n      };\r\n  }\r\n\r\n  scrollToCustomPosition(fragment: string): void {\r\n    const element = document.getElementById(fragment);\r\n    if (element) {\r\n      // Get the element's position and calculate a custom offset\r\n      const elementRect = element.getBoundingClientRect();\r\n      const absoluteElementTop = elementRect.top + window.scrollY; // Absolute top position of the element\r\n\r\n      const middleOffset = window.innerHeight / 2 - elementRect.height / 2; // Middle offset\r\n      const customOffset = middleOffset / 2; // Adjust the offset (closer to the top)\r\n\r\n      // Scroll the window to the custom position\r\n      window.scrollTo({\r\n        top: absoluteElementTop - customOffset,\r\n        behavior: 'smooth'\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  ListCss(otherBuisnessFiling: OtherBuisnessList) {\r\n    if (otherBuisnessFiling.productName == SubCategoryCode.annualreport) {\r\n      return \"icon-annual-report\";\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.conversion) {\r\n      return \"icon-conversion\";\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.dissolution) {\r\n      return \"icon-dissolution\";\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.employeeidntificationnumber) {\r\n      return \"icon-ein\";\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.fileamendment) {\r\n      return \"icon-amendment\";\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.beneficialownership) {\r\n      return \"icon-boi\";\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.filemerger) {\r\n      return \"icon-merger\";\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.namecheckreservation) {\r\n      return \"icon-name-check\";\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.registeredagent) {\r\n      return \"icon-registered-agent\";\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.retrievefielddocs) {\r\n      return \"icon-retrieve\";\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.uccfiling) {\r\n      return \"icon-ucc-filing\";\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.uccsearch) {\r\n      return \"icon-ucc-search\";\r\n    }\r\n    else if (otherBuisnessFiling.categoryCode == CategoryCode.dba) {\r\n      return \"icon-dba\";\r\n    }\r\n    else if (otherBuisnessFiling.categoryCode == CategoryCode.formcorporation) {\r\n      return \"icon-incorporation\";\r\n    }\r\n    else if (otherBuisnessFiling.categoryCode == CategoryCode.formllc) {\r\n      return \"icon-llc\";\r\n    }\r\n    else if (otherBuisnessFiling.categoryCode == CategoryCode.formpartnership) {\r\n      return \"icon-partnership\";\r\n    }\r\n\r\n\r\n  }\r\n\r\n  routerUrlService(otherBuisnessFiling: OtherBuisnessList) {\r\n    if (otherBuisnessFiling.productName == SubCategoryCode.annualreport) {\r\n      this.router.navigate([LandingPageServiceRouting.annualreport])\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.conversion) {\r\n      this.router.navigate([LandingPageServiceRouting.conversion])\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.dissolution) {\r\n      this.router.navigate([LandingPageServiceRouting.dissolution])\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.employeeidntificationnumber) {\r\n      this.router.navigate([LandingPageServiceRouting.employeeidentification])\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.fileamendment) {\r\n      this.router.navigate([LandingPageServiceRouting.fileAmendment])\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.beneficialownership) {\r\n      this.router.navigate([LandingPageServiceRouting.beneficialOwnership])\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.filemerger) {\r\n      this.router.navigate([LandingPageServiceRouting.fileMerger])\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.namecheckreservation) {\r\n      this.router.navigate([LandingPageServiceRouting.namecheckreservation])\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.registeredagent) {\r\n      this.router.navigate([LandingPageServiceRouting.registeredagent])\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.retrievefielddocs) {\r\n      this.router.navigate([LandingPageServiceRouting.retrievefielddocs])\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.uccfiling) {\r\n      this.router.navigate([LandingPageServiceRouting.uccfiling])\r\n    }\r\n    else if (otherBuisnessFiling.productName == SubCategoryCode.uccsearch) {\r\n      this.router.navigate([LandingPageServiceRouting.uccsearch])\r\n    }\r\n    else if (otherBuisnessFiling.categoryCode == CategoryCode.dba) {\r\n      this.router.navigate([LandingPageServiceRouting.DBA])\r\n    }\r\n    else if (otherBuisnessFiling.categoryCode == CategoryCode.formcorporation) {\r\n      this.router.navigate([LandingPageServiceRouting.formcorporation])\r\n    }\r\n    else if (otherBuisnessFiling.categoryCode == CategoryCode.formllc) {\r\n      this.router.navigate([LandingPageServiceRouting.formllc])\r\n    }\r\n    else if (otherBuisnessFiling.categoryCode == CategoryCode.formpartnership) {\r\n      this.router.navigate([LandingPageServiceRouting.formPartnership])\r\n    }\r\n    else {\r\n      return '#'\r\n    }\r\n    //return \"/\"+otherBuisnessFiling.description.split(' ').join('-').toLowerCase()\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}