{"ast": null, "code": "import { Scheduler } from '../Scheduler';\nexport class AsyncScheduler extends Scheduler {\n  constructor(SchedulerAction, now = Scheduler.now) {\n    super(SchedulerAction, () => {\n      if (AsyncScheduler.delegate && AsyncScheduler.delegate !== this) {\n        return AsyncScheduler.delegate.now();\n      } else {\n        return now();\n      }\n    });\n    this.actions = [];\n    this.active = false;\n    this.scheduled = undefined;\n  }\n\n  schedule(work, delay = 0, state) {\n    if (AsyncScheduler.delegate && AsyncScheduler.delegate !== this) {\n      return AsyncScheduler.delegate.schedule(work, delay, state);\n    } else {\n      return super.schedule(work, delay, state);\n    }\n  }\n\n  flush(action) {\n    const {\n      actions\n    } = this;\n\n    if (this.active) {\n      actions.push(action);\n      return;\n    }\n\n    let error;\n    this.active = true;\n\n    do {\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    } while (action = actions.shift());\n\n    this.active = false;\n\n    if (error) {\n      while (action = actions.shift()) {\n        action.unsubscribe();\n      }\n\n      throw error;\n    }\n  }\n\n}", "map": {"version": 3, "names": ["Scheduler", "AsyncScheduler", "constructor", "SchedulerAction", "now", "delegate", "actions", "active", "scheduled", "undefined", "schedule", "work", "delay", "state", "flush", "action", "push", "error", "execute", "shift", "unsubscribe"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/scheduler/AsyncScheduler.js"], "sourcesContent": ["import { Scheduler } from '../Scheduler';\nexport class AsyncScheduler extends Scheduler {\n    constructor(SchedulerAction, now = Scheduler.now) {\n        super(SchedulerAction, () => {\n            if (AsyncScheduler.delegate && AsyncScheduler.delegate !== this) {\n                return AsyncScheduler.delegate.now();\n            }\n            else {\n                return now();\n            }\n        });\n        this.actions = [];\n        this.active = false;\n        this.scheduled = undefined;\n    }\n    schedule(work, delay = 0, state) {\n        if (AsyncScheduler.delegate && AsyncScheduler.delegate !== this) {\n            return AsyncScheduler.delegate.schedule(work, delay, state);\n        }\n        else {\n            return super.schedule(work, delay, state);\n        }\n    }\n    flush(action) {\n        const { actions } = this;\n        if (this.active) {\n            actions.push(action);\n            return;\n        }\n        let error;\n        this.active = true;\n        do {\n            if (error = action.execute(action.state, action.delay)) {\n                break;\n            }\n        } while (action = actions.shift());\n        this.active = false;\n        if (error) {\n            while (action = actions.shift()) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAT,QAA0B,cAA1B;AACA,OAAO,MAAMC,cAAN,SAA6BD,SAA7B,CAAuC;EAC1CE,WAAW,CAACC,eAAD,EAAkBC,GAAG,GAAGJ,SAAS,CAACI,GAAlC,EAAuC;IAC9C,MAAMD,eAAN,EAAuB,MAAM;MACzB,IAAIF,cAAc,CAACI,QAAf,IAA2BJ,cAAc,CAACI,QAAf,KAA4B,IAA3D,EAAiE;QAC7D,OAAOJ,cAAc,CAACI,QAAf,CAAwBD,GAAxB,EAAP;MACH,CAFD,MAGK;QACD,OAAOA,GAAG,EAAV;MACH;IACJ,CAPD;IAQA,KAAKE,OAAL,GAAe,EAAf;IACA,KAAKC,MAAL,GAAc,KAAd;IACA,KAAKC,SAAL,GAAiBC,SAAjB;EACH;;EACDC,QAAQ,CAACC,IAAD,EAAOC,KAAK,GAAG,CAAf,EAAkBC,KAAlB,EAAyB;IAC7B,IAAIZ,cAAc,CAACI,QAAf,IAA2BJ,cAAc,CAACI,QAAf,KAA4B,IAA3D,EAAiE;MAC7D,OAAOJ,cAAc,CAACI,QAAf,CAAwBK,QAAxB,CAAiCC,IAAjC,EAAuCC,KAAvC,EAA8CC,KAA9C,CAAP;IACH,CAFD,MAGK;MACD,OAAO,MAAMH,QAAN,CAAeC,IAAf,EAAqBC,KAArB,EAA4BC,KAA5B,CAAP;IACH;EACJ;;EACDC,KAAK,CAACC,MAAD,EAAS;IACV,MAAM;MAAET;IAAF,IAAc,IAApB;;IACA,IAAI,KAAKC,MAAT,EAAiB;MACbD,OAAO,CAACU,IAAR,CAAaD,MAAb;MACA;IACH;;IACD,IAAIE,KAAJ;IACA,KAAKV,MAAL,GAAc,IAAd;;IACA,GAAG;MACC,IAAIU,KAAK,GAAGF,MAAM,CAACG,OAAP,CAAeH,MAAM,CAACF,KAAtB,EAA6BE,MAAM,CAACH,KAApC,CAAZ,EAAwD;QACpD;MACH;IACJ,CAJD,QAISG,MAAM,GAAGT,OAAO,CAACa,KAAR,EAJlB;;IAKA,KAAKZ,MAAL,GAAc,KAAd;;IACA,IAAIU,KAAJ,EAAW;MACP,OAAOF,MAAM,GAAGT,OAAO,CAACa,KAAR,EAAhB,EAAiC;QAC7BJ,MAAM,CAACK,WAAP;MACH;;MACD,MAAMH,KAAN;IACH;EACJ;;AA1CyC"}, "metadata": {}, "sourceType": "module"}