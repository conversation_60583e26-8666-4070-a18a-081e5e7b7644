{"ast": null, "code": "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\nvar getOwnPropertyDescriptors = Object.getOwnPropertyDescriptors || function getOwnPropertyDescriptors(obj) {\n  var keys = Object.keys(obj);\n  var descriptors = {};\n\n  for (var i = 0; i < keys.length; i++) {\n    descriptors[keys[i]] = Object.getOwnPropertyDescriptor(obj, keys[i]);\n  }\n\n  return descriptors;\n};\n\nvar formatRegExp = /%[sdj%]/g;\n\nexports.format = function (f) {\n  if (!isString(f)) {\n    var objects = [];\n\n    for (var i = 0; i < arguments.length; i++) {\n      objects.push(inspect(arguments[i]));\n    }\n\n    return objects.join(' ');\n  }\n\n  var i = 1;\n  var args = arguments;\n  var len = args.length;\n  var str = String(f).replace(formatRegExp, function (x) {\n    if (x === '%%') return '%';\n    if (i >= len) return x;\n\n    switch (x) {\n      case '%s':\n        return String(args[i++]);\n\n      case '%d':\n        return Number(args[i++]);\n\n      case '%j':\n        try {\n          return JSON.stringify(args[i++]);\n        } catch (_) {\n          return '[Circular]';\n        }\n\n      default:\n        return x;\n    }\n  });\n\n  for (var x = args[i]; i < len; x = args[++i]) {\n    if (isNull(x) || !isObject(x)) {\n      str += ' ' + x;\n    } else {\n      str += ' ' + inspect(x);\n    }\n  }\n\n  return str;\n}; // Mark that a method should not be used.\n// Returns a modified function which warns once by default.\n// If --no-deprecation is set, then it is a no-op.\n\n\nexports.deprecate = function (fn, msg) {\n  if (typeof process !== 'undefined' && process.noDeprecation === true) {\n    return fn;\n  } // Allow for deprecating things in the process of starting up.\n\n\n  if (typeof process === 'undefined') {\n    return function () {\n      return exports.deprecate(fn, msg).apply(this, arguments);\n    };\n  }\n\n  var warned = false;\n\n  function deprecated() {\n    if (!warned) {\n      if (process.throwDeprecation) {\n        throw new Error(msg);\n      } else if (process.traceDeprecation) {\n        console.trace(msg);\n      } else {\n        console.error(msg);\n      }\n\n      warned = true;\n    }\n\n    return fn.apply(this, arguments);\n  }\n\n  return deprecated;\n};\n\nvar debugs = {};\nvar debugEnvRegex = /^$/;\n\nif (process.env.NODE_DEBUG) {\n  var debugEnv = process.env.NODE_DEBUG;\n  debugEnv = debugEnv.replace(/[|\\\\{}()[\\]^$+?.]/g, '\\\\$&').replace(/\\*/g, '.*').replace(/,/g, '$|^').toUpperCase();\n  debugEnvRegex = new RegExp('^' + debugEnv + '$', 'i');\n}\n\nexports.debuglog = function (set) {\n  set = set.toUpperCase();\n\n  if (!debugs[set]) {\n    if (debugEnvRegex.test(set)) {\n      var pid = process.pid;\n\n      debugs[set] = function () {\n        var msg = exports.format.apply(exports, arguments);\n        console.error('%s %d: %s', set, pid, msg);\n      };\n    } else {\n      debugs[set] = function () {};\n    }\n  }\n\n  return debugs[set];\n};\n/**\n * Echos the value of a value. Trys to print the value out\n * in the best way possible given the different types.\n *\n * @param {Object} obj The object to print out.\n * @param {Object} opts Optional options object that alters the output.\n */\n\n/* legacy: obj, showHidden, depth, colors*/\n\n\nfunction inspect(obj, opts) {\n  // default options\n  var ctx = {\n    seen: [],\n    stylize: stylizeNoColor\n  }; // legacy...\n\n  if (arguments.length >= 3) ctx.depth = arguments[2];\n  if (arguments.length >= 4) ctx.colors = arguments[3];\n\n  if (isBoolean(opts)) {\n    // legacy...\n    ctx.showHidden = opts;\n  } else if (opts) {\n    // got an \"options\" object\n    exports._extend(ctx, opts);\n  } // set default options\n\n\n  if (isUndefined(ctx.showHidden)) ctx.showHidden = false;\n  if (isUndefined(ctx.depth)) ctx.depth = 2;\n  if (isUndefined(ctx.colors)) ctx.colors = false;\n  if (isUndefined(ctx.customInspect)) ctx.customInspect = true;\n  if (ctx.colors) ctx.stylize = stylizeWithColor;\n  return formatValue(ctx, obj, ctx.depth);\n}\n\nexports.inspect = inspect; // http://en.wikipedia.org/wiki/ANSI_escape_code#graphics\n\ninspect.colors = {\n  'bold': [1, 22],\n  'italic': [3, 23],\n  'underline': [4, 24],\n  'inverse': [7, 27],\n  'white': [37, 39],\n  'grey': [90, 39],\n  'black': [30, 39],\n  'blue': [34, 39],\n  'cyan': [36, 39],\n  'green': [32, 39],\n  'magenta': [35, 39],\n  'red': [31, 39],\n  'yellow': [33, 39]\n}; // Don't use 'blue' not visible on cmd.exe\n\ninspect.styles = {\n  'special': 'cyan',\n  'number': 'yellow',\n  'boolean': 'yellow',\n  'undefined': 'grey',\n  'null': 'bold',\n  'string': 'green',\n  'date': 'magenta',\n  // \"name\": intentionally not styling\n  'regexp': 'red'\n};\n\nfunction stylizeWithColor(str, styleType) {\n  var style = inspect.styles[styleType];\n\n  if (style) {\n    return '\\u001b[' + inspect.colors[style][0] + 'm' + str + '\\u001b[' + inspect.colors[style][1] + 'm';\n  } else {\n    return str;\n  }\n}\n\nfunction stylizeNoColor(str, styleType) {\n  return str;\n}\n\nfunction arrayToHash(array) {\n  var hash = {};\n  array.forEach(function (val, idx) {\n    hash[val] = true;\n  });\n  return hash;\n}\n\nfunction formatValue(ctx, value, recurseTimes) {\n  // Provide a hook for user-specified inspect functions.\n  // Check that value is an object with an inspect function on it\n  if (ctx.customInspect && value && isFunction(value.inspect) && // Filter out the util module, it's inspect function is special\n  value.inspect !== exports.inspect && // Also filter out any prototype objects using the circular check.\n  !(value.constructor && value.constructor.prototype === value)) {\n    var ret = value.inspect(recurseTimes, ctx);\n\n    if (!isString(ret)) {\n      ret = formatValue(ctx, ret, recurseTimes);\n    }\n\n    return ret;\n  } // Primitive types cannot have properties\n\n\n  var primitive = formatPrimitive(ctx, value);\n\n  if (primitive) {\n    return primitive;\n  } // Look up the keys of the object.\n\n\n  var keys = Object.keys(value);\n  var visibleKeys = arrayToHash(keys);\n\n  if (ctx.showHidden) {\n    keys = Object.getOwnPropertyNames(value);\n  } // IE doesn't make error fields non-enumerable\n  // http://msdn.microsoft.com/en-us/library/ie/dww52sbt(v=vs.94).aspx\n\n\n  if (isError(value) && (keys.indexOf('message') >= 0 || keys.indexOf('description') >= 0)) {\n    return formatError(value);\n  } // Some type of object without properties can be shortcutted.\n\n\n  if (keys.length === 0) {\n    if (isFunction(value)) {\n      var name = value.name ? ': ' + value.name : '';\n      return ctx.stylize('[Function' + name + ']', 'special');\n    }\n\n    if (isRegExp(value)) {\n      return ctx.stylize(RegExp.prototype.toString.call(value), 'regexp');\n    }\n\n    if (isDate(value)) {\n      return ctx.stylize(Date.prototype.toString.call(value), 'date');\n    }\n\n    if (isError(value)) {\n      return formatError(value);\n    }\n  }\n\n  var base = '',\n      array = false,\n      braces = ['{', '}']; // Make Array say that they are Array\n\n  if (isArray(value)) {\n    array = true;\n    braces = ['[', ']'];\n  } // Make functions say that they are functions\n\n\n  if (isFunction(value)) {\n    var n = value.name ? ': ' + value.name : '';\n    base = ' [Function' + n + ']';\n  } // Make RegExps say that they are RegExps\n\n\n  if (isRegExp(value)) {\n    base = ' ' + RegExp.prototype.toString.call(value);\n  } // Make dates with properties first say the date\n\n\n  if (isDate(value)) {\n    base = ' ' + Date.prototype.toUTCString.call(value);\n  } // Make error with message first say the error\n\n\n  if (isError(value)) {\n    base = ' ' + formatError(value);\n  }\n\n  if (keys.length === 0 && (!array || value.length == 0)) {\n    return braces[0] + base + braces[1];\n  }\n\n  if (recurseTimes < 0) {\n    if (isRegExp(value)) {\n      return ctx.stylize(RegExp.prototype.toString.call(value), 'regexp');\n    } else {\n      return ctx.stylize('[Object]', 'special');\n    }\n  }\n\n  ctx.seen.push(value);\n  var output;\n\n  if (array) {\n    output = formatArray(ctx, value, recurseTimes, visibleKeys, keys);\n  } else {\n    output = keys.map(function (key) {\n      return formatProperty(ctx, value, recurseTimes, visibleKeys, key, array);\n    });\n  }\n\n  ctx.seen.pop();\n  return reduceToSingleString(output, base, braces);\n}\n\nfunction formatPrimitive(ctx, value) {\n  if (isUndefined(value)) return ctx.stylize('undefined', 'undefined');\n\n  if (isString(value)) {\n    var simple = '\\'' + JSON.stringify(value).replace(/^\"|\"$/g, '').replace(/'/g, \"\\\\'\").replace(/\\\\\"/g, '\"') + '\\'';\n    return ctx.stylize(simple, 'string');\n  }\n\n  if (isNumber(value)) return ctx.stylize('' + value, 'number');\n  if (isBoolean(value)) return ctx.stylize('' + value, 'boolean'); // For some reason typeof null is \"object\", so special case here.\n\n  if (isNull(value)) return ctx.stylize('null', 'null');\n}\n\nfunction formatError(value) {\n  return '[' + Error.prototype.toString.call(value) + ']';\n}\n\nfunction formatArray(ctx, value, recurseTimes, visibleKeys, keys) {\n  var output = [];\n\n  for (var i = 0, l = value.length; i < l; ++i) {\n    if (hasOwnProperty(value, String(i))) {\n      output.push(formatProperty(ctx, value, recurseTimes, visibleKeys, String(i), true));\n    } else {\n      output.push('');\n    }\n  }\n\n  keys.forEach(function (key) {\n    if (!key.match(/^\\d+$/)) {\n      output.push(formatProperty(ctx, value, recurseTimes, visibleKeys, key, true));\n    }\n  });\n  return output;\n}\n\nfunction formatProperty(ctx, value, recurseTimes, visibleKeys, key, array) {\n  var name, str, desc;\n  desc = Object.getOwnPropertyDescriptor(value, key) || {\n    value: value[key]\n  };\n\n  if (desc.get) {\n    if (desc.set) {\n      str = ctx.stylize('[Getter/Setter]', 'special');\n    } else {\n      str = ctx.stylize('[Getter]', 'special');\n    }\n  } else {\n    if (desc.set) {\n      str = ctx.stylize('[Setter]', 'special');\n    }\n  }\n\n  if (!hasOwnProperty(visibleKeys, key)) {\n    name = '[' + key + ']';\n  }\n\n  if (!str) {\n    if (ctx.seen.indexOf(desc.value) < 0) {\n      if (isNull(recurseTimes)) {\n        str = formatValue(ctx, desc.value, null);\n      } else {\n        str = formatValue(ctx, desc.value, recurseTimes - 1);\n      }\n\n      if (str.indexOf('\\n') > -1) {\n        if (array) {\n          str = str.split('\\n').map(function (line) {\n            return '  ' + line;\n          }).join('\\n').slice(2);\n        } else {\n          str = '\\n' + str.split('\\n').map(function (line) {\n            return '   ' + line;\n          }).join('\\n');\n        }\n      }\n    } else {\n      str = ctx.stylize('[Circular]', 'special');\n    }\n  }\n\n  if (isUndefined(name)) {\n    if (array && key.match(/^\\d+$/)) {\n      return str;\n    }\n\n    name = JSON.stringify('' + key);\n\n    if (name.match(/^\"([a-zA-Z_][a-zA-Z_0-9]*)\"$/)) {\n      name = name.slice(1, -1);\n      name = ctx.stylize(name, 'name');\n    } else {\n      name = name.replace(/'/g, \"\\\\'\").replace(/\\\\\"/g, '\"').replace(/(^\"|\"$)/g, \"'\");\n      name = ctx.stylize(name, 'string');\n    }\n  }\n\n  return name + ': ' + str;\n}\n\nfunction reduceToSingleString(output, base, braces) {\n  var numLinesEst = 0;\n  var length = output.reduce(function (prev, cur) {\n    numLinesEst++;\n    if (cur.indexOf('\\n') >= 0) numLinesEst++;\n    return prev + cur.replace(/\\u001b\\[\\d\\d?m/g, '').length + 1;\n  }, 0);\n\n  if (length > 60) {\n    return braces[0] + (base === '' ? '' : base + '\\n ') + ' ' + output.join(',\\n  ') + ' ' + braces[1];\n  }\n\n  return braces[0] + base + ' ' + output.join(', ') + ' ' + braces[1];\n} // NOTE: These type checking functions intentionally don't use `instanceof`\n// because it is fragile and can be easily faked with `Object.create()`.\n\n\nexports.types = require('./support/types');\n\nfunction isArray(ar) {\n  return Array.isArray(ar);\n}\n\nexports.isArray = isArray;\n\nfunction isBoolean(arg) {\n  return typeof arg === 'boolean';\n}\n\nexports.isBoolean = isBoolean;\n\nfunction isNull(arg) {\n  return arg === null;\n}\n\nexports.isNull = isNull;\n\nfunction isNullOrUndefined(arg) {\n  return arg == null;\n}\n\nexports.isNullOrUndefined = isNullOrUndefined;\n\nfunction isNumber(arg) {\n  return typeof arg === 'number';\n}\n\nexports.isNumber = isNumber;\n\nfunction isString(arg) {\n  return typeof arg === 'string';\n}\n\nexports.isString = isString;\n\nfunction isSymbol(arg) {\n  return typeof arg === 'symbol';\n}\n\nexports.isSymbol = isSymbol;\n\nfunction isUndefined(arg) {\n  return arg === void 0;\n}\n\nexports.isUndefined = isUndefined;\n\nfunction isRegExp(re) {\n  return isObject(re) && objectToString(re) === '[object RegExp]';\n}\n\nexports.isRegExp = isRegExp;\nexports.types.isRegExp = isRegExp;\n\nfunction isObject(arg) {\n  return typeof arg === 'object' && arg !== null;\n}\n\nexports.isObject = isObject;\n\nfunction isDate(d) {\n  return isObject(d) && objectToString(d) === '[object Date]';\n}\n\nexports.isDate = isDate;\nexports.types.isDate = isDate;\n\nfunction isError(e) {\n  return isObject(e) && (objectToString(e) === '[object Error]' || e instanceof Error);\n}\n\nexports.isError = isError;\nexports.types.isNativeError = isError;\n\nfunction isFunction(arg) {\n  return typeof arg === 'function';\n}\n\nexports.isFunction = isFunction;\n\nfunction isPrimitive(arg) {\n  return arg === null || typeof arg === 'boolean' || typeof arg === 'number' || typeof arg === 'string' || typeof arg === 'symbol' || // ES6 symbol\n  typeof arg === 'undefined';\n}\n\nexports.isPrimitive = isPrimitive;\nexports.isBuffer = require('./support/isBuffer');\n\nfunction objectToString(o) {\n  return Object.prototype.toString.call(o);\n}\n\nfunction pad(n) {\n  return n < 10 ? '0' + n.toString(10) : n.toString(10);\n}\n\nvar months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']; // 26 Feb 16:19:34\n\nfunction timestamp() {\n  var d = new Date();\n  var time = [pad(d.getHours()), pad(d.getMinutes()), pad(d.getSeconds())].join(':');\n  return [d.getDate(), months[d.getMonth()], time].join(' ');\n} // log is just a thin wrapper to console.log that prepends a timestamp\n\n\nexports.log = function () {\n  console.log('%s - %s', timestamp(), exports.format.apply(exports, arguments));\n};\n/**\n * Inherit the prototype methods from one constructor into another.\n *\n * The Function.prototype.inherits from lang.js rewritten as a standalone\n * function (not on Function.prototype). NOTE: If this file is to be loaded\n * during bootstrapping this function needs to be rewritten using some native\n * functions as prototype setup using normal JavaScript does not work as\n * expected during bootstrapping (see mirror.js in r114903).\n *\n * @param {function} ctor Constructor function which needs to inherit the\n *     prototype.\n * @param {function} superCtor Constructor function to inherit prototype from.\n */\n\n\nexports.inherits = require('inherits');\n\nexports._extend = function (origin, add) {\n  // Don't do anything if add isn't an object\n  if (!add || !isObject(add)) return origin;\n  var keys = Object.keys(add);\n  var i = keys.length;\n\n  while (i--) {\n    origin[keys[i]] = add[keys[i]];\n  }\n\n  return origin;\n};\n\nfunction hasOwnProperty(obj, prop) {\n  return Object.prototype.hasOwnProperty.call(obj, prop);\n}\n\nvar kCustomPromisifiedSymbol = typeof Symbol !== 'undefined' ? Symbol('util.promisify.custom') : undefined;\n\nexports.promisify = function promisify(original) {\n  if (typeof original !== 'function') throw new TypeError('The \"original\" argument must be of type Function');\n\n  if (kCustomPromisifiedSymbol && original[kCustomPromisifiedSymbol]) {\n    var fn = original[kCustomPromisifiedSymbol];\n\n    if (typeof fn !== 'function') {\n      throw new TypeError('The \"util.promisify.custom\" argument must be of type Function');\n    }\n\n    Object.defineProperty(fn, kCustomPromisifiedSymbol, {\n      value: fn,\n      enumerable: false,\n      writable: false,\n      configurable: true\n    });\n    return fn;\n  }\n\n  function fn() {\n    var promiseResolve, promiseReject;\n    var promise = new Promise(function (resolve, reject) {\n      promiseResolve = resolve;\n      promiseReject = reject;\n    });\n    var args = [];\n\n    for (var i = 0; i < arguments.length; i++) {\n      args.push(arguments[i]);\n    }\n\n    args.push(function (err, value) {\n      if (err) {\n        promiseReject(err);\n      } else {\n        promiseResolve(value);\n      }\n    });\n\n    try {\n      original.apply(this, args);\n    } catch (err) {\n      promiseReject(err);\n    }\n\n    return promise;\n  }\n\n  Object.setPrototypeOf(fn, Object.getPrototypeOf(original));\n  if (kCustomPromisifiedSymbol) Object.defineProperty(fn, kCustomPromisifiedSymbol, {\n    value: fn,\n    enumerable: false,\n    writable: false,\n    configurable: true\n  });\n  return Object.defineProperties(fn, getOwnPropertyDescriptors(original));\n};\n\nexports.promisify.custom = kCustomPromisifiedSymbol;\n\nfunction callbackifyOnRejected(reason, cb) {\n  // `!reason` guard inspired by bluebird (Ref: https://goo.gl/t5IS6M).\n  // Because `null` is a special error value in callbacks which means \"no error\n  // occurred\", we error-wrap so the callback consumer can distinguish between\n  // \"the promise rejected with null\" or \"the promise fulfilled with undefined\".\n  if (!reason) {\n    var newReason = new Error('Promise was rejected with a falsy value');\n    newReason.reason = reason;\n    reason = newReason;\n  }\n\n  return cb(reason);\n}\n\nfunction callbackify(original) {\n  if (typeof original !== 'function') {\n    throw new TypeError('The \"original\" argument must be of type Function');\n  } // We DO NOT return the promise as it gives the user a false sense that\n  // the promise is actually somehow related to the callback's execution\n  // and that the callback throwing will reject the promise.\n\n\n  function callbackified() {\n    var args = [];\n\n    for (var i = 0; i < arguments.length; i++) {\n      args.push(arguments[i]);\n    }\n\n    var maybeCb = args.pop();\n\n    if (typeof maybeCb !== 'function') {\n      throw new TypeError('The last argument must be of type Function');\n    }\n\n    var self = this;\n\n    var cb = function () {\n      return maybeCb.apply(self, arguments);\n    }; // In true node style we process the callback on `nextTick` with all the\n    // implications (stack, `uncaughtException`, `async_hooks`)\n\n\n    original.apply(this, args).then(function (ret) {\n      process.nextTick(cb.bind(null, null, ret));\n    }, function (rej) {\n      process.nextTick(callbackifyOnRejected.bind(null, rej, cb));\n    });\n  }\n\n  Object.setPrototypeOf(callbackified, Object.getPrototypeOf(original));\n  Object.defineProperties(callbackified, getOwnPropertyDescriptors(original));\n  return callbackified;\n}\n\nexports.callbackify = callbackify;", "map": {"version": 3, "names": ["getOwnPropertyDescriptors", "Object", "obj", "keys", "descriptors", "i", "length", "getOwnPropertyDescriptor", "formatRegExp", "exports", "format", "f", "isString", "objects", "arguments", "push", "inspect", "join", "args", "len", "str", "String", "replace", "x", "Number", "JSON", "stringify", "_", "isNull", "isObject", "deprecate", "fn", "msg", "process", "noDeprecation", "apply", "warned", "deprecated", "throwDeprecation", "Error", "traceDeprecation", "console", "trace", "error", "debugs", "debugEnvRegex", "env", "NODE_DEBUG", "debugEnv", "toUpperCase", "RegExp", "debuglog", "set", "test", "pid", "opts", "ctx", "seen", "stylize", "stylizeNoColor", "depth", "colors", "isBoolean", "showHidden", "_extend", "isUndefined", "customInspect", "stylizeWithColor", "formatValue", "styles", "styleType", "style", "arrayToHash", "array", "hash", "for<PERSON>ach", "val", "idx", "value", "recurseTimes", "isFunction", "constructor", "prototype", "ret", "primitive", "formatPrimitive", "visible<PERSON>eys", "getOwnPropertyNames", "isError", "indexOf", "formatError", "name", "isRegExp", "toString", "call", "isDate", "Date", "base", "braces", "isArray", "n", "toUTCString", "output", "formatArray", "map", "key", "formatProperty", "pop", "reduceToSingleString", "simple", "isNumber", "l", "hasOwnProperty", "match", "desc", "get", "split", "line", "slice", "numLinesEst", "reduce", "prev", "cur", "types", "require", "ar", "Array", "arg", "isNullOrUndefined", "isSymbol", "re", "objectToString", "d", "e", "isNativeError", "isPrimitive", "<PERSON><PERSON><PERSON><PERSON>", "o", "pad", "months", "timestamp", "time", "getHours", "getMinutes", "getSeconds", "getDate", "getMonth", "log", "inherits", "origin", "add", "prop", "kCustomPromisifiedSymbol", "Symbol", "undefined", "promisify", "original", "TypeError", "defineProperty", "enumerable", "writable", "configurable", "promiseResolve", "promiseReject", "promise", "Promise", "resolve", "reject", "err", "setPrototypeOf", "getPrototypeOf", "defineProperties", "custom", "callbackifyOnRejected", "reason", "cb", "newReason", "callbackify", "callbackified", "maybeCb", "self", "then", "nextTick", "bind", "rej"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/util/util.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nvar getOwnPropertyDescriptors = Object.getOwnPropertyDescriptors ||\n  function getOwnPropertyDescriptors(obj) {\n    var keys = Object.keys(obj);\n    var descriptors = {};\n    for (var i = 0; i < keys.length; i++) {\n      descriptors[keys[i]] = Object.getOwnPropertyDescriptor(obj, keys[i]);\n    }\n    return descriptors;\n  };\n\nvar formatRegExp = /%[sdj%]/g;\nexports.format = function(f) {\n  if (!isString(f)) {\n    var objects = [];\n    for (var i = 0; i < arguments.length; i++) {\n      objects.push(inspect(arguments[i]));\n    }\n    return objects.join(' ');\n  }\n\n  var i = 1;\n  var args = arguments;\n  var len = args.length;\n  var str = String(f).replace(formatRegExp, function(x) {\n    if (x === '%%') return '%';\n    if (i >= len) return x;\n    switch (x) {\n      case '%s': return String(args[i++]);\n      case '%d': return Number(args[i++]);\n      case '%j':\n        try {\n          return JSON.stringify(args[i++]);\n        } catch (_) {\n          return '[Circular]';\n        }\n      default:\n        return x;\n    }\n  });\n  for (var x = args[i]; i < len; x = args[++i]) {\n    if (isNull(x) || !isObject(x)) {\n      str += ' ' + x;\n    } else {\n      str += ' ' + inspect(x);\n    }\n  }\n  return str;\n};\n\n\n// Mark that a method should not be used.\n// Returns a modified function which warns once by default.\n// If --no-deprecation is set, then it is a no-op.\nexports.deprecate = function(fn, msg) {\n  if (typeof process !== 'undefined' && process.noDeprecation === true) {\n    return fn;\n  }\n\n  // Allow for deprecating things in the process of starting up.\n  if (typeof process === 'undefined') {\n    return function() {\n      return exports.deprecate(fn, msg).apply(this, arguments);\n    };\n  }\n\n  var warned = false;\n  function deprecated() {\n    if (!warned) {\n      if (process.throwDeprecation) {\n        throw new Error(msg);\n      } else if (process.traceDeprecation) {\n        console.trace(msg);\n      } else {\n        console.error(msg);\n      }\n      warned = true;\n    }\n    return fn.apply(this, arguments);\n  }\n\n  return deprecated;\n};\n\n\nvar debugs = {};\nvar debugEnvRegex = /^$/;\n\nif (process.env.NODE_DEBUG) {\n  var debugEnv = process.env.NODE_DEBUG;\n  debugEnv = debugEnv.replace(/[|\\\\{}()[\\]^$+?.]/g, '\\\\$&')\n    .replace(/\\*/g, '.*')\n    .replace(/,/g, '$|^')\n    .toUpperCase();\n  debugEnvRegex = new RegExp('^' + debugEnv + '$', 'i');\n}\nexports.debuglog = function(set) {\n  set = set.toUpperCase();\n  if (!debugs[set]) {\n    if (debugEnvRegex.test(set)) {\n      var pid = process.pid;\n      debugs[set] = function() {\n        var msg = exports.format.apply(exports, arguments);\n        console.error('%s %d: %s', set, pid, msg);\n      };\n    } else {\n      debugs[set] = function() {};\n    }\n  }\n  return debugs[set];\n};\n\n\n/**\n * Echos the value of a value. Trys to print the value out\n * in the best way possible given the different types.\n *\n * @param {Object} obj The object to print out.\n * @param {Object} opts Optional options object that alters the output.\n */\n/* legacy: obj, showHidden, depth, colors*/\nfunction inspect(obj, opts) {\n  // default options\n  var ctx = {\n    seen: [],\n    stylize: stylizeNoColor\n  };\n  // legacy...\n  if (arguments.length >= 3) ctx.depth = arguments[2];\n  if (arguments.length >= 4) ctx.colors = arguments[3];\n  if (isBoolean(opts)) {\n    // legacy...\n    ctx.showHidden = opts;\n  } else if (opts) {\n    // got an \"options\" object\n    exports._extend(ctx, opts);\n  }\n  // set default options\n  if (isUndefined(ctx.showHidden)) ctx.showHidden = false;\n  if (isUndefined(ctx.depth)) ctx.depth = 2;\n  if (isUndefined(ctx.colors)) ctx.colors = false;\n  if (isUndefined(ctx.customInspect)) ctx.customInspect = true;\n  if (ctx.colors) ctx.stylize = stylizeWithColor;\n  return formatValue(ctx, obj, ctx.depth);\n}\nexports.inspect = inspect;\n\n\n// http://en.wikipedia.org/wiki/ANSI_escape_code#graphics\ninspect.colors = {\n  'bold' : [1, 22],\n  'italic' : [3, 23],\n  'underline' : [4, 24],\n  'inverse' : [7, 27],\n  'white' : [37, 39],\n  'grey' : [90, 39],\n  'black' : [30, 39],\n  'blue' : [34, 39],\n  'cyan' : [36, 39],\n  'green' : [32, 39],\n  'magenta' : [35, 39],\n  'red' : [31, 39],\n  'yellow' : [33, 39]\n};\n\n// Don't use 'blue' not visible on cmd.exe\ninspect.styles = {\n  'special': 'cyan',\n  'number': 'yellow',\n  'boolean': 'yellow',\n  'undefined': 'grey',\n  'null': 'bold',\n  'string': 'green',\n  'date': 'magenta',\n  // \"name\": intentionally not styling\n  'regexp': 'red'\n};\n\n\nfunction stylizeWithColor(str, styleType) {\n  var style = inspect.styles[styleType];\n\n  if (style) {\n    return '\\u001b[' + inspect.colors[style][0] + 'm' + str +\n           '\\u001b[' + inspect.colors[style][1] + 'm';\n  } else {\n    return str;\n  }\n}\n\n\nfunction stylizeNoColor(str, styleType) {\n  return str;\n}\n\n\nfunction arrayToHash(array) {\n  var hash = {};\n\n  array.forEach(function(val, idx) {\n    hash[val] = true;\n  });\n\n  return hash;\n}\n\n\nfunction formatValue(ctx, value, recurseTimes) {\n  // Provide a hook for user-specified inspect functions.\n  // Check that value is an object with an inspect function on it\n  if (ctx.customInspect &&\n      value &&\n      isFunction(value.inspect) &&\n      // Filter out the util module, it's inspect function is special\n      value.inspect !== exports.inspect &&\n      // Also filter out any prototype objects using the circular check.\n      !(value.constructor && value.constructor.prototype === value)) {\n    var ret = value.inspect(recurseTimes, ctx);\n    if (!isString(ret)) {\n      ret = formatValue(ctx, ret, recurseTimes);\n    }\n    return ret;\n  }\n\n  // Primitive types cannot have properties\n  var primitive = formatPrimitive(ctx, value);\n  if (primitive) {\n    return primitive;\n  }\n\n  // Look up the keys of the object.\n  var keys = Object.keys(value);\n  var visibleKeys = arrayToHash(keys);\n\n  if (ctx.showHidden) {\n    keys = Object.getOwnPropertyNames(value);\n  }\n\n  // IE doesn't make error fields non-enumerable\n  // http://msdn.microsoft.com/en-us/library/ie/dww52sbt(v=vs.94).aspx\n  if (isError(value)\n      && (keys.indexOf('message') >= 0 || keys.indexOf('description') >= 0)) {\n    return formatError(value);\n  }\n\n  // Some type of object without properties can be shortcutted.\n  if (keys.length === 0) {\n    if (isFunction(value)) {\n      var name = value.name ? ': ' + value.name : '';\n      return ctx.stylize('[Function' + name + ']', 'special');\n    }\n    if (isRegExp(value)) {\n      return ctx.stylize(RegExp.prototype.toString.call(value), 'regexp');\n    }\n    if (isDate(value)) {\n      return ctx.stylize(Date.prototype.toString.call(value), 'date');\n    }\n    if (isError(value)) {\n      return formatError(value);\n    }\n  }\n\n  var base = '', array = false, braces = ['{', '}'];\n\n  // Make Array say that they are Array\n  if (isArray(value)) {\n    array = true;\n    braces = ['[', ']'];\n  }\n\n  // Make functions say that they are functions\n  if (isFunction(value)) {\n    var n = value.name ? ': ' + value.name : '';\n    base = ' [Function' + n + ']';\n  }\n\n  // Make RegExps say that they are RegExps\n  if (isRegExp(value)) {\n    base = ' ' + RegExp.prototype.toString.call(value);\n  }\n\n  // Make dates with properties first say the date\n  if (isDate(value)) {\n    base = ' ' + Date.prototype.toUTCString.call(value);\n  }\n\n  // Make error with message first say the error\n  if (isError(value)) {\n    base = ' ' + formatError(value);\n  }\n\n  if (keys.length === 0 && (!array || value.length == 0)) {\n    return braces[0] + base + braces[1];\n  }\n\n  if (recurseTimes < 0) {\n    if (isRegExp(value)) {\n      return ctx.stylize(RegExp.prototype.toString.call(value), 'regexp');\n    } else {\n      return ctx.stylize('[Object]', 'special');\n    }\n  }\n\n  ctx.seen.push(value);\n\n  var output;\n  if (array) {\n    output = formatArray(ctx, value, recurseTimes, visibleKeys, keys);\n  } else {\n    output = keys.map(function(key) {\n      return formatProperty(ctx, value, recurseTimes, visibleKeys, key, array);\n    });\n  }\n\n  ctx.seen.pop();\n\n  return reduceToSingleString(output, base, braces);\n}\n\n\nfunction formatPrimitive(ctx, value) {\n  if (isUndefined(value))\n    return ctx.stylize('undefined', 'undefined');\n  if (isString(value)) {\n    var simple = '\\'' + JSON.stringify(value).replace(/^\"|\"$/g, '')\n                                             .replace(/'/g, \"\\\\'\")\n                                             .replace(/\\\\\"/g, '\"') + '\\'';\n    return ctx.stylize(simple, 'string');\n  }\n  if (isNumber(value))\n    return ctx.stylize('' + value, 'number');\n  if (isBoolean(value))\n    return ctx.stylize('' + value, 'boolean');\n  // For some reason typeof null is \"object\", so special case here.\n  if (isNull(value))\n    return ctx.stylize('null', 'null');\n}\n\n\nfunction formatError(value) {\n  return '[' + Error.prototype.toString.call(value) + ']';\n}\n\n\nfunction formatArray(ctx, value, recurseTimes, visibleKeys, keys) {\n  var output = [];\n  for (var i = 0, l = value.length; i < l; ++i) {\n    if (hasOwnProperty(value, String(i))) {\n      output.push(formatProperty(ctx, value, recurseTimes, visibleKeys,\n          String(i), true));\n    } else {\n      output.push('');\n    }\n  }\n  keys.forEach(function(key) {\n    if (!key.match(/^\\d+$/)) {\n      output.push(formatProperty(ctx, value, recurseTimes, visibleKeys,\n          key, true));\n    }\n  });\n  return output;\n}\n\n\nfunction formatProperty(ctx, value, recurseTimes, visibleKeys, key, array) {\n  var name, str, desc;\n  desc = Object.getOwnPropertyDescriptor(value, key) || { value: value[key] };\n  if (desc.get) {\n    if (desc.set) {\n      str = ctx.stylize('[Getter/Setter]', 'special');\n    } else {\n      str = ctx.stylize('[Getter]', 'special');\n    }\n  } else {\n    if (desc.set) {\n      str = ctx.stylize('[Setter]', 'special');\n    }\n  }\n  if (!hasOwnProperty(visibleKeys, key)) {\n    name = '[' + key + ']';\n  }\n  if (!str) {\n    if (ctx.seen.indexOf(desc.value) < 0) {\n      if (isNull(recurseTimes)) {\n        str = formatValue(ctx, desc.value, null);\n      } else {\n        str = formatValue(ctx, desc.value, recurseTimes - 1);\n      }\n      if (str.indexOf('\\n') > -1) {\n        if (array) {\n          str = str.split('\\n').map(function(line) {\n            return '  ' + line;\n          }).join('\\n').slice(2);\n        } else {\n          str = '\\n' + str.split('\\n').map(function(line) {\n            return '   ' + line;\n          }).join('\\n');\n        }\n      }\n    } else {\n      str = ctx.stylize('[Circular]', 'special');\n    }\n  }\n  if (isUndefined(name)) {\n    if (array && key.match(/^\\d+$/)) {\n      return str;\n    }\n    name = JSON.stringify('' + key);\n    if (name.match(/^\"([a-zA-Z_][a-zA-Z_0-9]*)\"$/)) {\n      name = name.slice(1, -1);\n      name = ctx.stylize(name, 'name');\n    } else {\n      name = name.replace(/'/g, \"\\\\'\")\n                 .replace(/\\\\\"/g, '\"')\n                 .replace(/(^\"|\"$)/g, \"'\");\n      name = ctx.stylize(name, 'string');\n    }\n  }\n\n  return name + ': ' + str;\n}\n\n\nfunction reduceToSingleString(output, base, braces) {\n  var numLinesEst = 0;\n  var length = output.reduce(function(prev, cur) {\n    numLinesEst++;\n    if (cur.indexOf('\\n') >= 0) numLinesEst++;\n    return prev + cur.replace(/\\u001b\\[\\d\\d?m/g, '').length + 1;\n  }, 0);\n\n  if (length > 60) {\n    return braces[0] +\n           (base === '' ? '' : base + '\\n ') +\n           ' ' +\n           output.join(',\\n  ') +\n           ' ' +\n           braces[1];\n  }\n\n  return braces[0] + base + ' ' + output.join(', ') + ' ' + braces[1];\n}\n\n\n// NOTE: These type checking functions intentionally don't use `instanceof`\n// because it is fragile and can be easily faked with `Object.create()`.\nexports.types = require('./support/types');\n\nfunction isArray(ar) {\n  return Array.isArray(ar);\n}\nexports.isArray = isArray;\n\nfunction isBoolean(arg) {\n  return typeof arg === 'boolean';\n}\nexports.isBoolean = isBoolean;\n\nfunction isNull(arg) {\n  return arg === null;\n}\nexports.isNull = isNull;\n\nfunction isNullOrUndefined(arg) {\n  return arg == null;\n}\nexports.isNullOrUndefined = isNullOrUndefined;\n\nfunction isNumber(arg) {\n  return typeof arg === 'number';\n}\nexports.isNumber = isNumber;\n\nfunction isString(arg) {\n  return typeof arg === 'string';\n}\nexports.isString = isString;\n\nfunction isSymbol(arg) {\n  return typeof arg === 'symbol';\n}\nexports.isSymbol = isSymbol;\n\nfunction isUndefined(arg) {\n  return arg === void 0;\n}\nexports.isUndefined = isUndefined;\n\nfunction isRegExp(re) {\n  return isObject(re) && objectToString(re) === '[object RegExp]';\n}\nexports.isRegExp = isRegExp;\nexports.types.isRegExp = isRegExp;\n\nfunction isObject(arg) {\n  return typeof arg === 'object' && arg !== null;\n}\nexports.isObject = isObject;\n\nfunction isDate(d) {\n  return isObject(d) && objectToString(d) === '[object Date]';\n}\nexports.isDate = isDate;\nexports.types.isDate = isDate;\n\nfunction isError(e) {\n  return isObject(e) &&\n      (objectToString(e) === '[object Error]' || e instanceof Error);\n}\nexports.isError = isError;\nexports.types.isNativeError = isError;\n\nfunction isFunction(arg) {\n  return typeof arg === 'function';\n}\nexports.isFunction = isFunction;\n\nfunction isPrimitive(arg) {\n  return arg === null ||\n         typeof arg === 'boolean' ||\n         typeof arg === 'number' ||\n         typeof arg === 'string' ||\n         typeof arg === 'symbol' ||  // ES6 symbol\n         typeof arg === 'undefined';\n}\nexports.isPrimitive = isPrimitive;\n\nexports.isBuffer = require('./support/isBuffer');\n\nfunction objectToString(o) {\n  return Object.prototype.toString.call(o);\n}\n\n\nfunction pad(n) {\n  return n < 10 ? '0' + n.toString(10) : n.toString(10);\n}\n\n\nvar months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep',\n              'Oct', 'Nov', 'Dec'];\n\n// 26 Feb 16:19:34\nfunction timestamp() {\n  var d = new Date();\n  var time = [pad(d.getHours()),\n              pad(d.getMinutes()),\n              pad(d.getSeconds())].join(':');\n  return [d.getDate(), months[d.getMonth()], time].join(' ');\n}\n\n\n// log is just a thin wrapper to console.log that prepends a timestamp\nexports.log = function() {\n  console.log('%s - %s', timestamp(), exports.format.apply(exports, arguments));\n};\n\n\n/**\n * Inherit the prototype methods from one constructor into another.\n *\n * The Function.prototype.inherits from lang.js rewritten as a standalone\n * function (not on Function.prototype). NOTE: If this file is to be loaded\n * during bootstrapping this function needs to be rewritten using some native\n * functions as prototype setup using normal JavaScript does not work as\n * expected during bootstrapping (see mirror.js in r114903).\n *\n * @param {function} ctor Constructor function which needs to inherit the\n *     prototype.\n * @param {function} superCtor Constructor function to inherit prototype from.\n */\nexports.inherits = require('inherits');\n\nexports._extend = function(origin, add) {\n  // Don't do anything if add isn't an object\n  if (!add || !isObject(add)) return origin;\n\n  var keys = Object.keys(add);\n  var i = keys.length;\n  while (i--) {\n    origin[keys[i]] = add[keys[i]];\n  }\n  return origin;\n};\n\nfunction hasOwnProperty(obj, prop) {\n  return Object.prototype.hasOwnProperty.call(obj, prop);\n}\n\nvar kCustomPromisifiedSymbol = typeof Symbol !== 'undefined' ? Symbol('util.promisify.custom') : undefined;\n\nexports.promisify = function promisify(original) {\n  if (typeof original !== 'function')\n    throw new TypeError('The \"original\" argument must be of type Function');\n\n  if (kCustomPromisifiedSymbol && original[kCustomPromisifiedSymbol]) {\n    var fn = original[kCustomPromisifiedSymbol];\n    if (typeof fn !== 'function') {\n      throw new TypeError('The \"util.promisify.custom\" argument must be of type Function');\n    }\n    Object.defineProperty(fn, kCustomPromisifiedSymbol, {\n      value: fn, enumerable: false, writable: false, configurable: true\n    });\n    return fn;\n  }\n\n  function fn() {\n    var promiseResolve, promiseReject;\n    var promise = new Promise(function (resolve, reject) {\n      promiseResolve = resolve;\n      promiseReject = reject;\n    });\n\n    var args = [];\n    for (var i = 0; i < arguments.length; i++) {\n      args.push(arguments[i]);\n    }\n    args.push(function (err, value) {\n      if (err) {\n        promiseReject(err);\n      } else {\n        promiseResolve(value);\n      }\n    });\n\n    try {\n      original.apply(this, args);\n    } catch (err) {\n      promiseReject(err);\n    }\n\n    return promise;\n  }\n\n  Object.setPrototypeOf(fn, Object.getPrototypeOf(original));\n\n  if (kCustomPromisifiedSymbol) Object.defineProperty(fn, kCustomPromisifiedSymbol, {\n    value: fn, enumerable: false, writable: false, configurable: true\n  });\n  return Object.defineProperties(\n    fn,\n    getOwnPropertyDescriptors(original)\n  );\n}\n\nexports.promisify.custom = kCustomPromisifiedSymbol\n\nfunction callbackifyOnRejected(reason, cb) {\n  // `!reason` guard inspired by bluebird (Ref: https://goo.gl/t5IS6M).\n  // Because `null` is a special error value in callbacks which means \"no error\n  // occurred\", we error-wrap so the callback consumer can distinguish between\n  // \"the promise rejected with null\" or \"the promise fulfilled with undefined\".\n  if (!reason) {\n    var newReason = new Error('Promise was rejected with a falsy value');\n    newReason.reason = reason;\n    reason = newReason;\n  }\n  return cb(reason);\n}\n\nfunction callbackify(original) {\n  if (typeof original !== 'function') {\n    throw new TypeError('The \"original\" argument must be of type Function');\n  }\n\n  // We DO NOT return the promise as it gives the user a false sense that\n  // the promise is actually somehow related to the callback's execution\n  // and that the callback throwing will reject the promise.\n  function callbackified() {\n    var args = [];\n    for (var i = 0; i < arguments.length; i++) {\n      args.push(arguments[i]);\n    }\n\n    var maybeCb = args.pop();\n    if (typeof maybeCb !== 'function') {\n      throw new TypeError('The last argument must be of type Function');\n    }\n    var self = this;\n    var cb = function() {\n      return maybeCb.apply(self, arguments);\n    };\n    // In true node style we process the callback on `nextTick` with all the\n    // implications (stack, `uncaughtException`, `async_hooks`)\n    original.apply(this, args)\n      .then(function(ret) { process.nextTick(cb.bind(null, null, ret)) },\n            function(rej) { process.nextTick(callbackifyOnRejected.bind(null, rej, cb)) });\n  }\n\n  Object.setPrototypeOf(callbackified, Object.getPrototypeOf(original));\n  Object.defineProperties(callbackified,\n                          getOwnPropertyDescriptors(original));\n  return callbackified;\n}\nexports.callbackify = callbackify;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAIA,yBAAyB,GAAGC,MAAM,CAACD,yBAAP,IAC9B,SAASA,yBAAT,CAAmCE,GAAnC,EAAwC;EACtC,IAAIC,IAAI,GAAGF,MAAM,CAACE,IAAP,CAAYD,GAAZ,CAAX;EACA,IAAIE,WAAW,GAAG,EAAlB;;EACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,IAAI,CAACG,MAAzB,EAAiCD,CAAC,EAAlC,EAAsC;IACpCD,WAAW,CAACD,IAAI,CAACE,CAAD,CAAL,CAAX,GAAuBJ,MAAM,CAACM,wBAAP,CAAgCL,GAAhC,EAAqCC,IAAI,CAACE,CAAD,CAAzC,CAAvB;EACD;;EACD,OAAOD,WAAP;AACD,CARH;;AAUA,IAAII,YAAY,GAAG,UAAnB;;AACAC,OAAO,CAACC,MAAR,GAAiB,UAASC,CAAT,EAAY;EAC3B,IAAI,CAACC,QAAQ,CAACD,CAAD,CAAb,EAAkB;IAChB,IAAIE,OAAO,GAAG,EAAd;;IACA,KAAK,IAAIR,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGS,SAAS,CAACR,MAA9B,EAAsCD,CAAC,EAAvC,EAA2C;MACzCQ,OAAO,CAACE,IAAR,CAAaC,OAAO,CAACF,SAAS,CAACT,CAAD,CAAV,CAApB;IACD;;IACD,OAAOQ,OAAO,CAACI,IAAR,CAAa,GAAb,CAAP;EACD;;EAED,IAAIZ,CAAC,GAAG,CAAR;EACA,IAAIa,IAAI,GAAGJ,SAAX;EACA,IAAIK,GAAG,GAAGD,IAAI,CAACZ,MAAf;EACA,IAAIc,GAAG,GAAGC,MAAM,CAACV,CAAD,CAAN,CAAUW,OAAV,CAAkBd,YAAlB,EAAgC,UAASe,CAAT,EAAY;IACpD,IAAIA,CAAC,KAAK,IAAV,EAAgB,OAAO,GAAP;IAChB,IAAIlB,CAAC,IAAIc,GAAT,EAAc,OAAOI,CAAP;;IACd,QAAQA,CAAR;MACE,KAAK,IAAL;QAAW,OAAOF,MAAM,CAACH,IAAI,CAACb,CAAC,EAAF,CAAL,CAAb;;MACX,KAAK,IAAL;QAAW,OAAOmB,MAAM,CAACN,IAAI,CAACb,CAAC,EAAF,CAAL,CAAb;;MACX,KAAK,IAAL;QACE,IAAI;UACF,OAAOoB,IAAI,CAACC,SAAL,CAAeR,IAAI,CAACb,CAAC,EAAF,CAAnB,CAAP;QACD,CAFD,CAEE,OAAOsB,CAAP,EAAU;UACV,OAAO,YAAP;QACD;;MACH;QACE,OAAOJ,CAAP;IAVJ;EAYD,CAfS,CAAV;;EAgBA,KAAK,IAAIA,CAAC,GAAGL,IAAI,CAACb,CAAD,CAAjB,EAAsBA,CAAC,GAAGc,GAA1B,EAA+BI,CAAC,GAAGL,IAAI,CAAC,EAAEb,CAAH,CAAvC,EAA8C;IAC5C,IAAIuB,MAAM,CAACL,CAAD,CAAN,IAAa,CAACM,QAAQ,CAACN,CAAD,CAA1B,EAA+B;MAC7BH,GAAG,IAAI,MAAMG,CAAb;IACD,CAFD,MAEO;MACLH,GAAG,IAAI,MAAMJ,OAAO,CAACO,CAAD,CAApB;IACD;EACF;;EACD,OAAOH,GAAP;AACD,CApCD,C,CAuCA;AACA;AACA;;;AACAX,OAAO,CAACqB,SAAR,GAAoB,UAASC,EAAT,EAAaC,GAAb,EAAkB;EACpC,IAAI,OAAOC,OAAP,KAAmB,WAAnB,IAAkCA,OAAO,CAACC,aAAR,KAA0B,IAAhE,EAAsE;IACpE,OAAOH,EAAP;EACD,CAHmC,CAKpC;;;EACA,IAAI,OAAOE,OAAP,KAAmB,WAAvB,EAAoC;IAClC,OAAO,YAAW;MAChB,OAAOxB,OAAO,CAACqB,SAAR,CAAkBC,EAAlB,EAAsBC,GAAtB,EAA2BG,KAA3B,CAAiC,IAAjC,EAAuCrB,SAAvC,CAAP;IACD,CAFD;EAGD;;EAED,IAAIsB,MAAM,GAAG,KAAb;;EACA,SAASC,UAAT,GAAsB;IACpB,IAAI,CAACD,MAAL,EAAa;MACX,IAAIH,OAAO,CAACK,gBAAZ,EAA8B;QAC5B,MAAM,IAAIC,KAAJ,CAAUP,GAAV,CAAN;MACD,CAFD,MAEO,IAAIC,OAAO,CAACO,gBAAZ,EAA8B;QACnCC,OAAO,CAACC,KAAR,CAAcV,GAAd;MACD,CAFM,MAEA;QACLS,OAAO,CAACE,KAAR,CAAcX,GAAd;MACD;;MACDI,MAAM,GAAG,IAAT;IACD;;IACD,OAAOL,EAAE,CAACI,KAAH,CAAS,IAAT,EAAerB,SAAf,CAAP;EACD;;EAED,OAAOuB,UAAP;AACD,CA5BD;;AA+BA,IAAIO,MAAM,GAAG,EAAb;AACA,IAAIC,aAAa,GAAG,IAApB;;AAEA,IAAIZ,OAAO,CAACa,GAAR,CAAYC,UAAhB,EAA4B;EAC1B,IAAIC,QAAQ,GAAGf,OAAO,CAACa,GAAR,CAAYC,UAA3B;EACAC,QAAQ,GAAGA,QAAQ,CAAC1B,OAAT,CAAiB,oBAAjB,EAAuC,MAAvC,EACRA,OADQ,CACA,KADA,EACO,IADP,EAERA,OAFQ,CAEA,IAFA,EAEM,KAFN,EAGR2B,WAHQ,EAAX;EAIAJ,aAAa,GAAG,IAAIK,MAAJ,CAAW,MAAMF,QAAN,GAAiB,GAA5B,EAAiC,GAAjC,CAAhB;AACD;;AACDvC,OAAO,CAAC0C,QAAR,GAAmB,UAASC,GAAT,EAAc;EAC/BA,GAAG,GAAGA,GAAG,CAACH,WAAJ,EAAN;;EACA,IAAI,CAACL,MAAM,CAACQ,GAAD,CAAX,EAAkB;IAChB,IAAIP,aAAa,CAACQ,IAAd,CAAmBD,GAAnB,CAAJ,EAA6B;MAC3B,IAAIE,GAAG,GAAGrB,OAAO,CAACqB,GAAlB;;MACAV,MAAM,CAACQ,GAAD,CAAN,GAAc,YAAW;QACvB,IAAIpB,GAAG,GAAGvB,OAAO,CAACC,MAAR,CAAeyB,KAAf,CAAqB1B,OAArB,EAA8BK,SAA9B,CAAV;QACA2B,OAAO,CAACE,KAAR,CAAc,WAAd,EAA2BS,GAA3B,EAAgCE,GAAhC,EAAqCtB,GAArC;MACD,CAHD;IAID,CAND,MAMO;MACLY,MAAM,CAACQ,GAAD,CAAN,GAAc,YAAW,CAAE,CAA3B;IACD;EACF;;EACD,OAAOR,MAAM,CAACQ,GAAD,CAAb;AACD,CAdD;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,SAASpC,OAAT,CAAiBd,GAAjB,EAAsBqD,IAAtB,EAA4B;EAC1B;EACA,IAAIC,GAAG,GAAG;IACRC,IAAI,EAAE,EADE;IAERC,OAAO,EAAEC;EAFD,CAAV,CAF0B,CAM1B;;EACA,IAAI7C,SAAS,CAACR,MAAV,IAAoB,CAAxB,EAA2BkD,GAAG,CAACI,KAAJ,GAAY9C,SAAS,CAAC,CAAD,CAArB;EAC3B,IAAIA,SAAS,CAACR,MAAV,IAAoB,CAAxB,EAA2BkD,GAAG,CAACK,MAAJ,GAAa/C,SAAS,CAAC,CAAD,CAAtB;;EAC3B,IAAIgD,SAAS,CAACP,IAAD,CAAb,EAAqB;IACnB;IACAC,GAAG,CAACO,UAAJ,GAAiBR,IAAjB;EACD,CAHD,MAGO,IAAIA,IAAJ,EAAU;IACf;IACA9C,OAAO,CAACuD,OAAR,CAAgBR,GAAhB,EAAqBD,IAArB;EACD,CAfyB,CAgB1B;;;EACA,IAAIU,WAAW,CAACT,GAAG,CAACO,UAAL,CAAf,EAAiCP,GAAG,CAACO,UAAJ,GAAiB,KAAjB;EACjC,IAAIE,WAAW,CAACT,GAAG,CAACI,KAAL,CAAf,EAA4BJ,GAAG,CAACI,KAAJ,GAAY,CAAZ;EAC5B,IAAIK,WAAW,CAACT,GAAG,CAACK,MAAL,CAAf,EAA6BL,GAAG,CAACK,MAAJ,GAAa,KAAb;EAC7B,IAAII,WAAW,CAACT,GAAG,CAACU,aAAL,CAAf,EAAoCV,GAAG,CAACU,aAAJ,GAAoB,IAApB;EACpC,IAAIV,GAAG,CAACK,MAAR,EAAgBL,GAAG,CAACE,OAAJ,GAAcS,gBAAd;EAChB,OAAOC,WAAW,CAACZ,GAAD,EAAMtD,GAAN,EAAWsD,GAAG,CAACI,KAAf,CAAlB;AACD;;AACDnD,OAAO,CAACO,OAAR,GAAkBA,OAAlB,C,CAGA;;AACAA,OAAO,CAAC6C,MAAR,GAAiB;EACf,QAAS,CAAC,CAAD,EAAI,EAAJ,CADM;EAEf,UAAW,CAAC,CAAD,EAAI,EAAJ,CAFI;EAGf,aAAc,CAAC,CAAD,EAAI,EAAJ,CAHC;EAIf,WAAY,CAAC,CAAD,EAAI,EAAJ,CAJG;EAKf,SAAU,CAAC,EAAD,EAAK,EAAL,CALK;EAMf,QAAS,CAAC,EAAD,EAAK,EAAL,CANM;EAOf,SAAU,CAAC,EAAD,EAAK,EAAL,CAPK;EAQf,QAAS,CAAC,EAAD,EAAK,EAAL,CARM;EASf,QAAS,CAAC,EAAD,EAAK,EAAL,CATM;EAUf,SAAU,CAAC,EAAD,EAAK,EAAL,CAVK;EAWf,WAAY,CAAC,EAAD,EAAK,EAAL,CAXG;EAYf,OAAQ,CAAC,EAAD,EAAK,EAAL,CAZO;EAaf,UAAW,CAAC,EAAD,EAAK,EAAL;AAbI,CAAjB,C,CAgBA;;AACA7C,OAAO,CAACqD,MAAR,GAAiB;EACf,WAAW,MADI;EAEf,UAAU,QAFK;EAGf,WAAW,QAHI;EAIf,aAAa,MAJE;EAKf,QAAQ,MALO;EAMf,UAAU,OANK;EAOf,QAAQ,SAPO;EAQf;EACA,UAAU;AATK,CAAjB;;AAaA,SAASF,gBAAT,CAA0B/C,GAA1B,EAA+BkD,SAA/B,EAA0C;EACxC,IAAIC,KAAK,GAAGvD,OAAO,CAACqD,MAAR,CAAeC,SAAf,CAAZ;;EAEA,IAAIC,KAAJ,EAAW;IACT,OAAO,YAAYvD,OAAO,CAAC6C,MAAR,CAAeU,KAAf,EAAsB,CAAtB,CAAZ,GAAuC,GAAvC,GAA6CnD,GAA7C,GACA,SADA,GACYJ,OAAO,CAAC6C,MAAR,CAAeU,KAAf,EAAsB,CAAtB,CADZ,GACuC,GAD9C;EAED,CAHD,MAGO;IACL,OAAOnD,GAAP;EACD;AACF;;AAGD,SAASuC,cAAT,CAAwBvC,GAAxB,EAA6BkD,SAA7B,EAAwC;EACtC,OAAOlD,GAAP;AACD;;AAGD,SAASoD,WAAT,CAAqBC,KAArB,EAA4B;EAC1B,IAAIC,IAAI,GAAG,EAAX;EAEAD,KAAK,CAACE,OAAN,CAAc,UAASC,GAAT,EAAcC,GAAd,EAAmB;IAC/BH,IAAI,CAACE,GAAD,CAAJ,GAAY,IAAZ;EACD,CAFD;EAIA,OAAOF,IAAP;AACD;;AAGD,SAASN,WAAT,CAAqBZ,GAArB,EAA0BsB,KAA1B,EAAiCC,YAAjC,EAA+C;EAC7C;EACA;EACA,IAAIvB,GAAG,CAACU,aAAJ,IACAY,KADA,IAEAE,UAAU,CAACF,KAAK,CAAC9D,OAAP,CAFV,IAGA;EACA8D,KAAK,CAAC9D,OAAN,KAAkBP,OAAO,CAACO,OAJ1B,IAKA;EACA,EAAE8D,KAAK,CAACG,WAAN,IAAqBH,KAAK,CAACG,WAAN,CAAkBC,SAAlB,KAAgCJ,KAAvD,CANJ,EAMmE;IACjE,IAAIK,GAAG,GAAGL,KAAK,CAAC9D,OAAN,CAAc+D,YAAd,EAA4BvB,GAA5B,CAAV;;IACA,IAAI,CAAC5C,QAAQ,CAACuE,GAAD,CAAb,EAAoB;MAClBA,GAAG,GAAGf,WAAW,CAACZ,GAAD,EAAM2B,GAAN,EAAWJ,YAAX,CAAjB;IACD;;IACD,OAAOI,GAAP;EACD,CAf4C,CAiB7C;;;EACA,IAAIC,SAAS,GAAGC,eAAe,CAAC7B,GAAD,EAAMsB,KAAN,CAA/B;;EACA,IAAIM,SAAJ,EAAe;IACb,OAAOA,SAAP;EACD,CArB4C,CAuB7C;;;EACA,IAAIjF,IAAI,GAAGF,MAAM,CAACE,IAAP,CAAY2E,KAAZ,CAAX;EACA,IAAIQ,WAAW,GAAGd,WAAW,CAACrE,IAAD,CAA7B;;EAEA,IAAIqD,GAAG,CAACO,UAAR,EAAoB;IAClB5D,IAAI,GAAGF,MAAM,CAACsF,mBAAP,CAA2BT,KAA3B,CAAP;EACD,CA7B4C,CA+B7C;EACA;;;EACA,IAAIU,OAAO,CAACV,KAAD,CAAP,KACI3E,IAAI,CAACsF,OAAL,CAAa,SAAb,KAA2B,CAA3B,IAAgCtF,IAAI,CAACsF,OAAL,CAAa,aAAb,KAA+B,CADnE,CAAJ,EAC2E;IACzE,OAAOC,WAAW,CAACZ,KAAD,CAAlB;EACD,CApC4C,CAsC7C;;;EACA,IAAI3E,IAAI,CAACG,MAAL,KAAgB,CAApB,EAAuB;IACrB,IAAI0E,UAAU,CAACF,KAAD,CAAd,EAAuB;MACrB,IAAIa,IAAI,GAAGb,KAAK,CAACa,IAAN,GAAa,OAAOb,KAAK,CAACa,IAA1B,GAAiC,EAA5C;MACA,OAAOnC,GAAG,CAACE,OAAJ,CAAY,cAAciC,IAAd,GAAqB,GAAjC,EAAsC,SAAtC,CAAP;IACD;;IACD,IAAIC,QAAQ,CAACd,KAAD,CAAZ,EAAqB;MACnB,OAAOtB,GAAG,CAACE,OAAJ,CAAYR,MAAM,CAACgC,SAAP,CAAiBW,QAAjB,CAA0BC,IAA1B,CAA+BhB,KAA/B,CAAZ,EAAmD,QAAnD,CAAP;IACD;;IACD,IAAIiB,MAAM,CAACjB,KAAD,CAAV,EAAmB;MACjB,OAAOtB,GAAG,CAACE,OAAJ,CAAYsC,IAAI,CAACd,SAAL,CAAeW,QAAf,CAAwBC,IAAxB,CAA6BhB,KAA7B,CAAZ,EAAiD,MAAjD,CAAP;IACD;;IACD,IAAIU,OAAO,CAACV,KAAD,CAAX,EAAoB;MAClB,OAAOY,WAAW,CAACZ,KAAD,CAAlB;IACD;EACF;;EAED,IAAImB,IAAI,GAAG,EAAX;EAAA,IAAexB,KAAK,GAAG,KAAvB;EAAA,IAA8ByB,MAAM,GAAG,CAAC,GAAD,EAAM,GAAN,CAAvC,CAvD6C,CAyD7C;;EACA,IAAIC,OAAO,CAACrB,KAAD,CAAX,EAAoB;IAClBL,KAAK,GAAG,IAAR;IACAyB,MAAM,GAAG,CAAC,GAAD,EAAM,GAAN,CAAT;EACD,CA7D4C,CA+D7C;;;EACA,IAAIlB,UAAU,CAACF,KAAD,CAAd,EAAuB;IACrB,IAAIsB,CAAC,GAAGtB,KAAK,CAACa,IAAN,GAAa,OAAOb,KAAK,CAACa,IAA1B,GAAiC,EAAzC;IACAM,IAAI,GAAG,eAAeG,CAAf,GAAmB,GAA1B;EACD,CAnE4C,CAqE7C;;;EACA,IAAIR,QAAQ,CAACd,KAAD,CAAZ,EAAqB;IACnBmB,IAAI,GAAG,MAAM/C,MAAM,CAACgC,SAAP,CAAiBW,QAAjB,CAA0BC,IAA1B,CAA+BhB,KAA/B,CAAb;EACD,CAxE4C,CA0E7C;;;EACA,IAAIiB,MAAM,CAACjB,KAAD,CAAV,EAAmB;IACjBmB,IAAI,GAAG,MAAMD,IAAI,CAACd,SAAL,CAAemB,WAAf,CAA2BP,IAA3B,CAAgChB,KAAhC,CAAb;EACD,CA7E4C,CA+E7C;;;EACA,IAAIU,OAAO,CAACV,KAAD,CAAX,EAAoB;IAClBmB,IAAI,GAAG,MAAMP,WAAW,CAACZ,KAAD,CAAxB;EACD;;EAED,IAAI3E,IAAI,CAACG,MAAL,KAAgB,CAAhB,KAAsB,CAACmE,KAAD,IAAUK,KAAK,CAACxE,MAAN,IAAgB,CAAhD,CAAJ,EAAwD;IACtD,OAAO4F,MAAM,CAAC,CAAD,CAAN,GAAYD,IAAZ,GAAmBC,MAAM,CAAC,CAAD,CAAhC;EACD;;EAED,IAAInB,YAAY,GAAG,CAAnB,EAAsB;IACpB,IAAIa,QAAQ,CAACd,KAAD,CAAZ,EAAqB;MACnB,OAAOtB,GAAG,CAACE,OAAJ,CAAYR,MAAM,CAACgC,SAAP,CAAiBW,QAAjB,CAA0BC,IAA1B,CAA+BhB,KAA/B,CAAZ,EAAmD,QAAnD,CAAP;IACD,CAFD,MAEO;MACL,OAAOtB,GAAG,CAACE,OAAJ,CAAY,UAAZ,EAAwB,SAAxB,CAAP;IACD;EACF;;EAEDF,GAAG,CAACC,IAAJ,CAAS1C,IAAT,CAAc+D,KAAd;EAEA,IAAIwB,MAAJ;;EACA,IAAI7B,KAAJ,EAAW;IACT6B,MAAM,GAAGC,WAAW,CAAC/C,GAAD,EAAMsB,KAAN,EAAaC,YAAb,EAA2BO,WAA3B,EAAwCnF,IAAxC,CAApB;EACD,CAFD,MAEO;IACLmG,MAAM,GAAGnG,IAAI,CAACqG,GAAL,CAAS,UAASC,GAAT,EAAc;MAC9B,OAAOC,cAAc,CAAClD,GAAD,EAAMsB,KAAN,EAAaC,YAAb,EAA2BO,WAA3B,EAAwCmB,GAAxC,EAA6ChC,KAA7C,CAArB;IACD,CAFQ,CAAT;EAGD;;EAEDjB,GAAG,CAACC,IAAJ,CAASkD,GAAT;EAEA,OAAOC,oBAAoB,CAACN,MAAD,EAASL,IAAT,EAAeC,MAAf,CAA3B;AACD;;AAGD,SAASb,eAAT,CAAyB7B,GAAzB,EAA8BsB,KAA9B,EAAqC;EACnC,IAAIb,WAAW,CAACa,KAAD,CAAf,EACE,OAAOtB,GAAG,CAACE,OAAJ,CAAY,WAAZ,EAAyB,WAAzB,CAAP;;EACF,IAAI9C,QAAQ,CAACkE,KAAD,CAAZ,EAAqB;IACnB,IAAI+B,MAAM,GAAG,OAAOpF,IAAI,CAACC,SAAL,CAAeoD,KAAf,EAAsBxD,OAAtB,CAA8B,QAA9B,EAAwC,EAAxC,EACsBA,OADtB,CAC8B,IAD9B,EACoC,KADpC,EAEsBA,OAFtB,CAE8B,MAF9B,EAEsC,GAFtC,CAAP,GAEoD,IAFjE;IAGA,OAAOkC,GAAG,CAACE,OAAJ,CAAYmD,MAAZ,EAAoB,QAApB,CAAP;EACD;;EACD,IAAIC,QAAQ,CAAChC,KAAD,CAAZ,EACE,OAAOtB,GAAG,CAACE,OAAJ,CAAY,KAAKoB,KAAjB,EAAwB,QAAxB,CAAP;EACF,IAAIhB,SAAS,CAACgB,KAAD,CAAb,EACE,OAAOtB,GAAG,CAACE,OAAJ,CAAY,KAAKoB,KAAjB,EAAwB,SAAxB,CAAP,CAZiC,CAanC;;EACA,IAAIlD,MAAM,CAACkD,KAAD,CAAV,EACE,OAAOtB,GAAG,CAACE,OAAJ,CAAY,MAAZ,EAAoB,MAApB,CAAP;AACH;;AAGD,SAASgC,WAAT,CAAqBZ,KAArB,EAA4B;EAC1B,OAAO,MAAMvC,KAAK,CAAC2C,SAAN,CAAgBW,QAAhB,CAAyBC,IAAzB,CAA8BhB,KAA9B,CAAN,GAA6C,GAApD;AACD;;AAGD,SAASyB,WAAT,CAAqB/C,GAArB,EAA0BsB,KAA1B,EAAiCC,YAAjC,EAA+CO,WAA/C,EAA4DnF,IAA5D,EAAkE;EAChE,IAAImG,MAAM,GAAG,EAAb;;EACA,KAAK,IAAIjG,CAAC,GAAG,CAAR,EAAW0G,CAAC,GAAGjC,KAAK,CAACxE,MAA1B,EAAkCD,CAAC,GAAG0G,CAAtC,EAAyC,EAAE1G,CAA3C,EAA8C;IAC5C,IAAI2G,cAAc,CAAClC,KAAD,EAAQzD,MAAM,CAAChB,CAAD,CAAd,CAAlB,EAAsC;MACpCiG,MAAM,CAACvF,IAAP,CAAY2F,cAAc,CAAClD,GAAD,EAAMsB,KAAN,EAAaC,YAAb,EAA2BO,WAA3B,EACtBjE,MAAM,CAAChB,CAAD,CADgB,EACX,IADW,CAA1B;IAED,CAHD,MAGO;MACLiG,MAAM,CAACvF,IAAP,CAAY,EAAZ;IACD;EACF;;EACDZ,IAAI,CAACwE,OAAL,CAAa,UAAS8B,GAAT,EAAc;IACzB,IAAI,CAACA,GAAG,CAACQ,KAAJ,CAAU,OAAV,CAAL,EAAyB;MACvBX,MAAM,CAACvF,IAAP,CAAY2F,cAAc,CAAClD,GAAD,EAAMsB,KAAN,EAAaC,YAAb,EAA2BO,WAA3B,EACtBmB,GADsB,EACjB,IADiB,CAA1B;IAED;EACF,CALD;EAMA,OAAOH,MAAP;AACD;;AAGD,SAASI,cAAT,CAAwBlD,GAAxB,EAA6BsB,KAA7B,EAAoCC,YAApC,EAAkDO,WAAlD,EAA+DmB,GAA/D,EAAoEhC,KAApE,EAA2E;EACzE,IAAIkB,IAAJ,EAAUvE,GAAV,EAAe8F,IAAf;EACAA,IAAI,GAAGjH,MAAM,CAACM,wBAAP,CAAgCuE,KAAhC,EAAuC2B,GAAvC,KAA+C;IAAE3B,KAAK,EAAEA,KAAK,CAAC2B,GAAD;EAAd,CAAtD;;EACA,IAAIS,IAAI,CAACC,GAAT,EAAc;IACZ,IAAID,IAAI,CAAC9D,GAAT,EAAc;MACZhC,GAAG,GAAGoC,GAAG,CAACE,OAAJ,CAAY,iBAAZ,EAA+B,SAA/B,CAAN;IACD,CAFD,MAEO;MACLtC,GAAG,GAAGoC,GAAG,CAACE,OAAJ,CAAY,UAAZ,EAAwB,SAAxB,CAAN;IACD;EACF,CAND,MAMO;IACL,IAAIwD,IAAI,CAAC9D,GAAT,EAAc;MACZhC,GAAG,GAAGoC,GAAG,CAACE,OAAJ,CAAY,UAAZ,EAAwB,SAAxB,CAAN;IACD;EACF;;EACD,IAAI,CAACsD,cAAc,CAAC1B,WAAD,EAAcmB,GAAd,CAAnB,EAAuC;IACrCd,IAAI,GAAG,MAAMc,GAAN,GAAY,GAAnB;EACD;;EACD,IAAI,CAACrF,GAAL,EAAU;IACR,IAAIoC,GAAG,CAACC,IAAJ,CAASgC,OAAT,CAAiByB,IAAI,CAACpC,KAAtB,IAA+B,CAAnC,EAAsC;MACpC,IAAIlD,MAAM,CAACmD,YAAD,CAAV,EAA0B;QACxB3D,GAAG,GAAGgD,WAAW,CAACZ,GAAD,EAAM0D,IAAI,CAACpC,KAAX,EAAkB,IAAlB,CAAjB;MACD,CAFD,MAEO;QACL1D,GAAG,GAAGgD,WAAW,CAACZ,GAAD,EAAM0D,IAAI,CAACpC,KAAX,EAAkBC,YAAY,GAAG,CAAjC,CAAjB;MACD;;MACD,IAAI3D,GAAG,CAACqE,OAAJ,CAAY,IAAZ,IAAoB,CAAC,CAAzB,EAA4B;QAC1B,IAAIhB,KAAJ,EAAW;UACTrD,GAAG,GAAGA,GAAG,CAACgG,KAAJ,CAAU,IAAV,EAAgBZ,GAAhB,CAAoB,UAASa,IAAT,EAAe;YACvC,OAAO,OAAOA,IAAd;UACD,CAFK,EAEHpG,IAFG,CAEE,IAFF,EAEQqG,KAFR,CAEc,CAFd,CAAN;QAGD,CAJD,MAIO;UACLlG,GAAG,GAAG,OAAOA,GAAG,CAACgG,KAAJ,CAAU,IAAV,EAAgBZ,GAAhB,CAAoB,UAASa,IAAT,EAAe;YAC9C,OAAO,QAAQA,IAAf;UACD,CAFY,EAEVpG,IAFU,CAEL,IAFK,CAAb;QAGD;MACF;IACF,CAjBD,MAiBO;MACLG,GAAG,GAAGoC,GAAG,CAACE,OAAJ,CAAY,YAAZ,EAA0B,SAA1B,CAAN;IACD;EACF;;EACD,IAAIO,WAAW,CAAC0B,IAAD,CAAf,EAAuB;IACrB,IAAIlB,KAAK,IAAIgC,GAAG,CAACQ,KAAJ,CAAU,OAAV,CAAb,EAAiC;MAC/B,OAAO7F,GAAP;IACD;;IACDuE,IAAI,GAAGlE,IAAI,CAACC,SAAL,CAAe,KAAK+E,GAApB,CAAP;;IACA,IAAId,IAAI,CAACsB,KAAL,CAAW,8BAAX,CAAJ,EAAgD;MAC9CtB,IAAI,GAAGA,IAAI,CAAC2B,KAAL,CAAW,CAAX,EAAc,CAAC,CAAf,CAAP;MACA3B,IAAI,GAAGnC,GAAG,CAACE,OAAJ,CAAYiC,IAAZ,EAAkB,MAAlB,CAAP;IACD,CAHD,MAGO;MACLA,IAAI,GAAGA,IAAI,CAACrE,OAAL,CAAa,IAAb,EAAmB,KAAnB,EACKA,OADL,CACa,MADb,EACqB,GADrB,EAEKA,OAFL,CAEa,UAFb,EAEyB,GAFzB,CAAP;MAGAqE,IAAI,GAAGnC,GAAG,CAACE,OAAJ,CAAYiC,IAAZ,EAAkB,QAAlB,CAAP;IACD;EACF;;EAED,OAAOA,IAAI,GAAG,IAAP,GAAcvE,GAArB;AACD;;AAGD,SAASwF,oBAAT,CAA8BN,MAA9B,EAAsCL,IAAtC,EAA4CC,MAA5C,EAAoD;EAClD,IAAIqB,WAAW,GAAG,CAAlB;EACA,IAAIjH,MAAM,GAAGgG,MAAM,CAACkB,MAAP,CAAc,UAASC,IAAT,EAAeC,GAAf,EAAoB;IAC7CH,WAAW;IACX,IAAIG,GAAG,CAACjC,OAAJ,CAAY,IAAZ,KAAqB,CAAzB,EAA4B8B,WAAW;IACvC,OAAOE,IAAI,GAAGC,GAAG,CAACpG,OAAJ,CAAY,iBAAZ,EAA+B,EAA/B,EAAmChB,MAA1C,GAAmD,CAA1D;EACD,CAJY,EAIV,CAJU,CAAb;;EAMA,IAAIA,MAAM,GAAG,EAAb,EAAiB;IACf,OAAO4F,MAAM,CAAC,CAAD,CAAN,IACCD,IAAI,KAAK,EAAT,GAAc,EAAd,GAAmBA,IAAI,GAAG,KAD3B,IAEA,GAFA,GAGAK,MAAM,CAACrF,IAAP,CAAY,OAAZ,CAHA,GAIA,GAJA,GAKAiF,MAAM,CAAC,CAAD,CALb;EAMD;;EAED,OAAOA,MAAM,CAAC,CAAD,CAAN,GAAYD,IAAZ,GAAmB,GAAnB,GAAyBK,MAAM,CAACrF,IAAP,CAAY,IAAZ,CAAzB,GAA6C,GAA7C,GAAmDiF,MAAM,CAAC,CAAD,CAAhE;AACD,C,CAGD;AACA;;;AACAzF,OAAO,CAACkH,KAAR,GAAgBC,OAAO,CAAC,iBAAD,CAAvB;;AAEA,SAASzB,OAAT,CAAiB0B,EAAjB,EAAqB;EACnB,OAAOC,KAAK,CAAC3B,OAAN,CAAc0B,EAAd,CAAP;AACD;;AACDpH,OAAO,CAAC0F,OAAR,GAAkBA,OAAlB;;AAEA,SAASrC,SAAT,CAAmBiE,GAAnB,EAAwB;EACtB,OAAO,OAAOA,GAAP,KAAe,SAAtB;AACD;;AACDtH,OAAO,CAACqD,SAAR,GAAoBA,SAApB;;AAEA,SAASlC,MAAT,CAAgBmG,GAAhB,EAAqB;EACnB,OAAOA,GAAG,KAAK,IAAf;AACD;;AACDtH,OAAO,CAACmB,MAAR,GAAiBA,MAAjB;;AAEA,SAASoG,iBAAT,CAA2BD,GAA3B,EAAgC;EAC9B,OAAOA,GAAG,IAAI,IAAd;AACD;;AACDtH,OAAO,CAACuH,iBAAR,GAA4BA,iBAA5B;;AAEA,SAASlB,QAAT,CAAkBiB,GAAlB,EAAuB;EACrB,OAAO,OAAOA,GAAP,KAAe,QAAtB;AACD;;AACDtH,OAAO,CAACqG,QAAR,GAAmBA,QAAnB;;AAEA,SAASlG,QAAT,CAAkBmH,GAAlB,EAAuB;EACrB,OAAO,OAAOA,GAAP,KAAe,QAAtB;AACD;;AACDtH,OAAO,CAACG,QAAR,GAAmBA,QAAnB;;AAEA,SAASqH,QAAT,CAAkBF,GAAlB,EAAuB;EACrB,OAAO,OAAOA,GAAP,KAAe,QAAtB;AACD;;AACDtH,OAAO,CAACwH,QAAR,GAAmBA,QAAnB;;AAEA,SAAShE,WAAT,CAAqB8D,GAArB,EAA0B;EACxB,OAAOA,GAAG,KAAK,KAAK,CAApB;AACD;;AACDtH,OAAO,CAACwD,WAAR,GAAsBA,WAAtB;;AAEA,SAAS2B,QAAT,CAAkBsC,EAAlB,EAAsB;EACpB,OAAOrG,QAAQ,CAACqG,EAAD,CAAR,IAAgBC,cAAc,CAACD,EAAD,CAAd,KAAuB,iBAA9C;AACD;;AACDzH,OAAO,CAACmF,QAAR,GAAmBA,QAAnB;AACAnF,OAAO,CAACkH,KAAR,CAAc/B,QAAd,GAAyBA,QAAzB;;AAEA,SAAS/D,QAAT,CAAkBkG,GAAlB,EAAuB;EACrB,OAAO,OAAOA,GAAP,KAAe,QAAf,IAA2BA,GAAG,KAAK,IAA1C;AACD;;AACDtH,OAAO,CAACoB,QAAR,GAAmBA,QAAnB;;AAEA,SAASkE,MAAT,CAAgBqC,CAAhB,EAAmB;EACjB,OAAOvG,QAAQ,CAACuG,CAAD,CAAR,IAAeD,cAAc,CAACC,CAAD,CAAd,KAAsB,eAA5C;AACD;;AACD3H,OAAO,CAACsF,MAAR,GAAiBA,MAAjB;AACAtF,OAAO,CAACkH,KAAR,CAAc5B,MAAd,GAAuBA,MAAvB;;AAEA,SAASP,OAAT,CAAiB6C,CAAjB,EAAoB;EAClB,OAAOxG,QAAQ,CAACwG,CAAD,CAAR,KACFF,cAAc,CAACE,CAAD,CAAd,KAAsB,gBAAtB,IAA0CA,CAAC,YAAY9F,KADrD,CAAP;AAED;;AACD9B,OAAO,CAAC+E,OAAR,GAAkBA,OAAlB;AACA/E,OAAO,CAACkH,KAAR,CAAcW,aAAd,GAA8B9C,OAA9B;;AAEA,SAASR,UAAT,CAAoB+C,GAApB,EAAyB;EACvB,OAAO,OAAOA,GAAP,KAAe,UAAtB;AACD;;AACDtH,OAAO,CAACuE,UAAR,GAAqBA,UAArB;;AAEA,SAASuD,WAAT,CAAqBR,GAArB,EAA0B;EACxB,OAAOA,GAAG,KAAK,IAAR,IACA,OAAOA,GAAP,KAAe,SADf,IAEA,OAAOA,GAAP,KAAe,QAFf,IAGA,OAAOA,GAAP,KAAe,QAHf,IAIA,OAAOA,GAAP,KAAe,QAJf,IAI4B;EAC5B,OAAOA,GAAP,KAAe,WALtB;AAMD;;AACDtH,OAAO,CAAC8H,WAAR,GAAsBA,WAAtB;AAEA9H,OAAO,CAAC+H,QAAR,GAAmBZ,OAAO,CAAC,oBAAD,CAA1B;;AAEA,SAASO,cAAT,CAAwBM,CAAxB,EAA2B;EACzB,OAAOxI,MAAM,CAACiF,SAAP,CAAiBW,QAAjB,CAA0BC,IAA1B,CAA+B2C,CAA/B,CAAP;AACD;;AAGD,SAASC,GAAT,CAAatC,CAAb,EAAgB;EACd,OAAOA,CAAC,GAAG,EAAJ,GAAS,MAAMA,CAAC,CAACP,QAAF,CAAW,EAAX,CAAf,GAAgCO,CAAC,CAACP,QAAF,CAAW,EAAX,CAAvC;AACD;;AAGD,IAAI8C,MAAM,GAAG,CAAC,KAAD,EAAQ,KAAR,EAAe,KAAf,EAAsB,KAAtB,EAA6B,KAA7B,EAAoC,KAApC,EAA2C,KAA3C,EAAkD,KAAlD,EAAyD,KAAzD,EACC,KADD,EACQ,KADR,EACe,KADf,CAAb,C,CAGA;;AACA,SAASC,SAAT,GAAqB;EACnB,IAAIR,CAAC,GAAG,IAAIpC,IAAJ,EAAR;EACA,IAAI6C,IAAI,GAAG,CAACH,GAAG,CAACN,CAAC,CAACU,QAAF,EAAD,CAAJ,EACCJ,GAAG,CAACN,CAAC,CAACW,UAAF,EAAD,CADJ,EAECL,GAAG,CAACN,CAAC,CAACY,UAAF,EAAD,CAFJ,EAEsB/H,IAFtB,CAE2B,GAF3B,CAAX;EAGA,OAAO,CAACmH,CAAC,CAACa,OAAF,EAAD,EAAcN,MAAM,CAACP,CAAC,CAACc,QAAF,EAAD,CAApB,EAAoCL,IAApC,EAA0C5H,IAA1C,CAA+C,GAA/C,CAAP;AACD,C,CAGD;;;AACAR,OAAO,CAAC0I,GAAR,GAAc,YAAW;EACvB1G,OAAO,CAAC0G,GAAR,CAAY,SAAZ,EAAuBP,SAAS,EAAhC,EAAoCnI,OAAO,CAACC,MAAR,CAAeyB,KAAf,CAAqB1B,OAArB,EAA8BK,SAA9B,CAApC;AACD,CAFD;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAL,OAAO,CAAC2I,QAAR,GAAmBxB,OAAO,CAAC,UAAD,CAA1B;;AAEAnH,OAAO,CAACuD,OAAR,GAAkB,UAASqF,MAAT,EAAiBC,GAAjB,EAAsB;EACtC;EACA,IAAI,CAACA,GAAD,IAAQ,CAACzH,QAAQ,CAACyH,GAAD,CAArB,EAA4B,OAAOD,MAAP;EAE5B,IAAIlJ,IAAI,GAAGF,MAAM,CAACE,IAAP,CAAYmJ,GAAZ,CAAX;EACA,IAAIjJ,CAAC,GAAGF,IAAI,CAACG,MAAb;;EACA,OAAOD,CAAC,EAAR,EAAY;IACVgJ,MAAM,CAAClJ,IAAI,CAACE,CAAD,CAAL,CAAN,GAAkBiJ,GAAG,CAACnJ,IAAI,CAACE,CAAD,CAAL,CAArB;EACD;;EACD,OAAOgJ,MAAP;AACD,CAVD;;AAYA,SAASrC,cAAT,CAAwB9G,GAAxB,EAA6BqJ,IAA7B,EAAmC;EACjC,OAAOtJ,MAAM,CAACiF,SAAP,CAAiB8B,cAAjB,CAAgClB,IAAhC,CAAqC5F,GAArC,EAA0CqJ,IAA1C,CAAP;AACD;;AAED,IAAIC,wBAAwB,GAAG,OAAOC,MAAP,KAAkB,WAAlB,GAAgCA,MAAM,CAAC,uBAAD,CAAtC,GAAkEC,SAAjG;;AAEAjJ,OAAO,CAACkJ,SAAR,GAAoB,SAASA,SAAT,CAAmBC,QAAnB,EAA6B;EAC/C,IAAI,OAAOA,QAAP,KAAoB,UAAxB,EACE,MAAM,IAAIC,SAAJ,CAAc,kDAAd,CAAN;;EAEF,IAAIL,wBAAwB,IAAII,QAAQ,CAACJ,wBAAD,CAAxC,EAAoE;IAClE,IAAIzH,EAAE,GAAG6H,QAAQ,CAACJ,wBAAD,CAAjB;;IACA,IAAI,OAAOzH,EAAP,KAAc,UAAlB,EAA8B;MAC5B,MAAM,IAAI8H,SAAJ,CAAc,+DAAd,CAAN;IACD;;IACD5J,MAAM,CAAC6J,cAAP,CAAsB/H,EAAtB,EAA0ByH,wBAA1B,EAAoD;MAClD1E,KAAK,EAAE/C,EAD2C;MACvCgI,UAAU,EAAE,KAD2B;MACpBC,QAAQ,EAAE,KADU;MACHC,YAAY,EAAE;IADX,CAApD;IAGA,OAAOlI,EAAP;EACD;;EAED,SAASA,EAAT,GAAc;IACZ,IAAImI,cAAJ,EAAoBC,aAApB;IACA,IAAIC,OAAO,GAAG,IAAIC,OAAJ,CAAY,UAAUC,OAAV,EAAmBC,MAAnB,EAA2B;MACnDL,cAAc,GAAGI,OAAjB;MACAH,aAAa,GAAGI,MAAhB;IACD,CAHa,CAAd;IAKA,IAAIrJ,IAAI,GAAG,EAAX;;IACA,KAAK,IAAIb,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGS,SAAS,CAACR,MAA9B,EAAsCD,CAAC,EAAvC,EAA2C;MACzCa,IAAI,CAACH,IAAL,CAAUD,SAAS,CAACT,CAAD,CAAnB;IACD;;IACDa,IAAI,CAACH,IAAL,CAAU,UAAUyJ,GAAV,EAAe1F,KAAf,EAAsB;MAC9B,IAAI0F,GAAJ,EAAS;QACPL,aAAa,CAACK,GAAD,CAAb;MACD,CAFD,MAEO;QACLN,cAAc,CAACpF,KAAD,CAAd;MACD;IACF,CAND;;IAQA,IAAI;MACF8E,QAAQ,CAACzH,KAAT,CAAe,IAAf,EAAqBjB,IAArB;IACD,CAFD,CAEE,OAAOsJ,GAAP,EAAY;MACZL,aAAa,CAACK,GAAD,CAAb;IACD;;IAED,OAAOJ,OAAP;EACD;;EAEDnK,MAAM,CAACwK,cAAP,CAAsB1I,EAAtB,EAA0B9B,MAAM,CAACyK,cAAP,CAAsBd,QAAtB,CAA1B;EAEA,IAAIJ,wBAAJ,EAA8BvJ,MAAM,CAAC6J,cAAP,CAAsB/H,EAAtB,EAA0ByH,wBAA1B,EAAoD;IAChF1E,KAAK,EAAE/C,EADyE;IACrEgI,UAAU,EAAE,KADyD;IAClDC,QAAQ,EAAE,KADwC;IACjCC,YAAY,EAAE;EADmB,CAApD;EAG9B,OAAOhK,MAAM,CAAC0K,gBAAP,CACL5I,EADK,EAEL/B,yBAAyB,CAAC4J,QAAD,CAFpB,CAAP;AAID,CApDD;;AAsDAnJ,OAAO,CAACkJ,SAAR,CAAkBiB,MAAlB,GAA2BpB,wBAA3B;;AAEA,SAASqB,qBAAT,CAA+BC,MAA/B,EAAuCC,EAAvC,EAA2C;EACzC;EACA;EACA;EACA;EACA,IAAI,CAACD,MAAL,EAAa;IACX,IAAIE,SAAS,GAAG,IAAIzI,KAAJ,CAAU,yCAAV,CAAhB;IACAyI,SAAS,CAACF,MAAV,GAAmBA,MAAnB;IACAA,MAAM,GAAGE,SAAT;EACD;;EACD,OAAOD,EAAE,CAACD,MAAD,CAAT;AACD;;AAED,SAASG,WAAT,CAAqBrB,QAArB,EAA+B;EAC7B,IAAI,OAAOA,QAAP,KAAoB,UAAxB,EAAoC;IAClC,MAAM,IAAIC,SAAJ,CAAc,kDAAd,CAAN;EACD,CAH4B,CAK7B;EACA;EACA;;;EACA,SAASqB,aAAT,GAAyB;IACvB,IAAIhK,IAAI,GAAG,EAAX;;IACA,KAAK,IAAIb,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGS,SAAS,CAACR,MAA9B,EAAsCD,CAAC,EAAvC,EAA2C;MACzCa,IAAI,CAACH,IAAL,CAAUD,SAAS,CAACT,CAAD,CAAnB;IACD;;IAED,IAAI8K,OAAO,GAAGjK,IAAI,CAACyF,GAAL,EAAd;;IACA,IAAI,OAAOwE,OAAP,KAAmB,UAAvB,EAAmC;MACjC,MAAM,IAAItB,SAAJ,CAAc,4CAAd,CAAN;IACD;;IACD,IAAIuB,IAAI,GAAG,IAAX;;IACA,IAAIL,EAAE,GAAG,YAAW;MAClB,OAAOI,OAAO,CAAChJ,KAAR,CAAciJ,IAAd,EAAoBtK,SAApB,CAAP;IACD,CAFD,CAXuB,CAcvB;IACA;;;IACA8I,QAAQ,CAACzH,KAAT,CAAe,IAAf,EAAqBjB,IAArB,EACGmK,IADH,CACQ,UAASlG,GAAT,EAAc;MAAElD,OAAO,CAACqJ,QAAR,CAAiBP,EAAE,CAACQ,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoBpG,GAApB,CAAjB;IAA4C,CADpE,EAEQ,UAASqG,GAAT,EAAc;MAAEvJ,OAAO,CAACqJ,QAAR,CAAiBT,qBAAqB,CAACU,IAAtB,CAA2B,IAA3B,EAAiCC,GAAjC,EAAsCT,EAAtC,CAAjB;IAA6D,CAFrF;EAGD;;EAED9K,MAAM,CAACwK,cAAP,CAAsBS,aAAtB,EAAqCjL,MAAM,CAACyK,cAAP,CAAsBd,QAAtB,CAArC;EACA3J,MAAM,CAAC0K,gBAAP,CAAwBO,aAAxB,EACwBlL,yBAAyB,CAAC4J,QAAD,CADjD;EAEA,OAAOsB,aAAP;AACD;;AACDzK,OAAO,CAACwK,WAAR,GAAsBA,WAAtB"}, "metadata": {}, "sourceType": "script"}