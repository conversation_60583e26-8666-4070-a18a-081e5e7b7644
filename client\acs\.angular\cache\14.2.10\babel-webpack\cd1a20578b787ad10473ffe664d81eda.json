{"ast": null, "code": "import { isArray } from './isArray';\nexport function isNumeric(val) {\n  return !isArray(val) && val - parseFloat(val) + 1 >= 0;\n}", "map": {"version": 3, "names": ["isArray", "isNumeric", "val", "parseFloat"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/isNumeric.js"], "sourcesContent": ["import { isArray } from './isArray';\nexport function isNumeric(val) {\n    return !isArray(val) && (val - parseFloat(val) + 1) >= 0;\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,WAAxB;AACA,OAAO,SAASC,SAAT,CAAmBC,GAAnB,EAAwB;EAC3B,OAAO,CAACF,OAAO,CAACE,GAAD,CAAR,IAAkBA,GAAG,GAAGC,UAAU,CAACD,GAAD,CAAhB,GAAwB,CAAzB,IAA+B,CAAvD;AACH"}, "metadata": {}, "sourceType": "module"}