{"ast": null, "code": "/**\n * @license Angular v14.3.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\nimport * as i0 from '@angular/core';\nimport { ViewEncapsulation, Injectable, Inject, RendererFactory2, NgZone, ANIMATION_MODULE_TYPE, NgModule } from '@angular/core';\nexport { ANIMATION_MODULE_TYPE } from '@angular/core';\nimport { ɵDomRendererFactory2, BrowserModule } from '@angular/platform-browser';\nimport { AnimationBuilder, sequence, AnimationFactory } from '@angular/animations';\nimport * as i1 from '@angular/animations/browser';\nimport { ɵAnimationEngine, ɵWebAnimationsStyleNormalizer, ɵAnimationStyleNormalizer, AnimationDriver, ɵWebAnimationsDriver, ɵNoopAnimationDriver } from '@angular/animations/browser';\nimport { DOCUMENT } from '@angular/common';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nclass BrowserAnimationBuilder extends AnimationBuilder {\n  constructor(rootRenderer, doc) {\n    super();\n    this._nextAnimationId = 0;\n    const typeData = {\n      id: '0',\n      encapsulation: ViewEncapsulation.None,\n      styles: [],\n      data: {\n        animation: []\n      }\n    };\n    this._renderer = rootRenderer.createRenderer(doc.body, typeData);\n  }\n\n  build(animation) {\n    const id = this._nextAnimationId.toString();\n\n    this._nextAnimationId++;\n    const entry = Array.isArray(animation) ? sequence(animation) : animation;\n    issueAnimationCommand(this._renderer, null, id, 'register', [entry]);\n    return new BrowserAnimationFactory(id, this._renderer);\n  }\n\n}\n\nBrowserAnimationBuilder.ɵfac = function BrowserAnimationBuilder_Factory(t) {\n  return new (t || BrowserAnimationBuilder)(i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(DOCUMENT));\n};\n\nBrowserAnimationBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: BrowserAnimationBuilder,\n  factory: BrowserAnimationBuilder.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserAnimationBuilder, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i0.RendererFactory2\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\nclass BrowserAnimationFactory extends AnimationFactory {\n  constructor(_id, _renderer) {\n    super();\n    this._id = _id;\n    this._renderer = _renderer;\n  }\n\n  create(element, options) {\n    return new RendererAnimationPlayer(this._id, element, options || {}, this._renderer);\n  }\n\n}\n\nclass RendererAnimationPlayer {\n  constructor(id, element, options, _renderer) {\n    this.id = id;\n    this.element = element;\n    this._renderer = _renderer;\n    this.parentPlayer = null;\n    this._started = false;\n    this.totalTime = 0;\n\n    this._command('create', options);\n  }\n\n  _listen(eventName, callback) {\n    return this._renderer.listen(this.element, `@@${this.id}:${eventName}`, callback);\n  }\n\n  _command(command, ...args) {\n    return issueAnimationCommand(this._renderer, this.element, this.id, command, args);\n  }\n\n  onDone(fn) {\n    this._listen('done', fn);\n  }\n\n  onStart(fn) {\n    this._listen('start', fn);\n  }\n\n  onDestroy(fn) {\n    this._listen('destroy', fn);\n  }\n\n  init() {\n    this._command('init');\n  }\n\n  hasStarted() {\n    return this._started;\n  }\n\n  play() {\n    this._command('play');\n\n    this._started = true;\n  }\n\n  pause() {\n    this._command('pause');\n  }\n\n  restart() {\n    this._command('restart');\n  }\n\n  finish() {\n    this._command('finish');\n  }\n\n  destroy() {\n    this._command('destroy');\n  }\n\n  reset() {\n    this._command('reset');\n\n    this._started = false;\n  }\n\n  setPosition(p) {\n    this._command('setPosition', p);\n  }\n\n  getPosition() {\n    return this._renderer.engine.players[+this.id]?.getPosition() ?? 0;\n  }\n\n}\n\nfunction issueAnimationCommand(renderer, element, id, command, args) {\n  return renderer.setProperty(element, `@@${id}:${command}`, args);\n}\n\nconst ANIMATION_PREFIX = '@';\nconst DISABLE_ANIMATIONS_FLAG = '@.disabled';\n\nclass AnimationRendererFactory {\n  constructor(delegate, engine, _zone) {\n    this.delegate = delegate;\n    this.engine = engine;\n    this._zone = _zone;\n    this._currentId = 0;\n    this._microtaskId = 1;\n    this._animationCallbacksBuffer = [];\n    this._rendererCache = new Map();\n    this._cdRecurDepth = 0;\n    this.promise = Promise.resolve(0);\n\n    engine.onRemovalComplete = (element, delegate) => {\n      // Note: if a component element has a leave animation, and a host leave animation,\n      // the view engine will call `removeChild` for the parent\n      // component renderer as well as for the child component renderer.\n      // Therefore, we need to check if we already removed the element.\n      const parentNode = delegate?.parentNode(element);\n\n      if (parentNode) {\n        delegate.removeChild(parentNode, element);\n      }\n    };\n  }\n\n  createRenderer(hostElement, type) {\n    const EMPTY_NAMESPACE_ID = ''; // cache the delegates to find out which cached delegate can\n    // be used by which cached renderer\n\n    const delegate = this.delegate.createRenderer(hostElement, type);\n\n    if (!hostElement || !type || !type.data || !type.data['animation']) {\n      let renderer = this._rendererCache.get(delegate);\n\n      if (!renderer) {\n        // Ensure that the renderer is removed from the cache on destroy\n        // since it may contain references to detached DOM nodes.\n        const onRendererDestroy = () => this._rendererCache.delete(delegate);\n\n        renderer = new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine, onRendererDestroy); // only cache this result when the base renderer is used\n\n        this._rendererCache.set(delegate, renderer);\n      }\n\n      return renderer;\n    }\n\n    const componentId = type.id;\n    const namespaceId = type.id + '-' + this._currentId;\n    this._currentId++;\n    this.engine.register(namespaceId, hostElement);\n\n    const registerTrigger = trigger => {\n      if (Array.isArray(trigger)) {\n        trigger.forEach(registerTrigger);\n      } else {\n        this.engine.registerTrigger(componentId, namespaceId, hostElement, trigger.name, trigger);\n      }\n    };\n\n    const animationTriggers = type.data['animation'];\n    animationTriggers.forEach(registerTrigger);\n    return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n  }\n\n  begin() {\n    this._cdRecurDepth++;\n\n    if (this.delegate.begin) {\n      this.delegate.begin();\n    }\n  }\n\n  _scheduleCountTask() {\n    // always use promise to schedule microtask instead of use Zone\n    this.promise.then(() => {\n      this._microtaskId++;\n    });\n  }\n  /** @internal */\n\n\n  scheduleListenerCallback(count, fn, data) {\n    if (count >= 0 && count < this._microtaskId) {\n      this._zone.run(() => fn(data));\n\n      return;\n    }\n\n    if (this._animationCallbacksBuffer.length == 0) {\n      Promise.resolve(null).then(() => {\n        this._zone.run(() => {\n          this._animationCallbacksBuffer.forEach(tuple => {\n            const [fn, data] = tuple;\n            fn(data);\n          });\n\n          this._animationCallbacksBuffer = [];\n        });\n      });\n    }\n\n    this._animationCallbacksBuffer.push([fn, data]);\n  }\n\n  end() {\n    this._cdRecurDepth--; // this is to prevent animations from running twice when an inner\n    // component does CD when a parent component instead has inserted it\n\n    if (this._cdRecurDepth == 0) {\n      this._zone.runOutsideAngular(() => {\n        this._scheduleCountTask();\n\n        this.engine.flush(this._microtaskId);\n      });\n    }\n\n    if (this.delegate.end) {\n      this.delegate.end();\n    }\n  }\n\n  whenRenderingDone() {\n    return this.engine.whenRenderingDone();\n  }\n\n}\n\nAnimationRendererFactory.ɵfac = function AnimationRendererFactory_Factory(t) {\n  return new (t || AnimationRendererFactory)(i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(i1.ɵAnimationEngine), i0.ɵɵinject(i0.NgZone));\n};\n\nAnimationRendererFactory.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: AnimationRendererFactory,\n  factory: AnimationRendererFactory.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AnimationRendererFactory, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i0.RendererFactory2\n    }, {\n      type: i1.ɵAnimationEngine\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\n\nclass BaseAnimationRenderer {\n  constructor(namespaceId, delegate, engine, _onDestroy) {\n    this.namespaceId = namespaceId;\n    this.delegate = delegate;\n    this.engine = engine;\n    this._onDestroy = _onDestroy;\n    this.destroyNode = this.delegate.destroyNode ? n => delegate.destroyNode(n) : null;\n  }\n\n  get data() {\n    return this.delegate.data;\n  }\n\n  destroy() {\n    this.engine.destroy(this.namespaceId, this.delegate);\n    this.delegate.destroy();\n    this._onDestroy?.();\n  }\n\n  createElement(name, namespace) {\n    return this.delegate.createElement(name, namespace);\n  }\n\n  createComment(value) {\n    return this.delegate.createComment(value);\n  }\n\n  createText(value) {\n    return this.delegate.createText(value);\n  }\n\n  appendChild(parent, newChild) {\n    this.delegate.appendChild(parent, newChild);\n    this.engine.onInsert(this.namespaceId, newChild, parent, false);\n  }\n\n  insertBefore(parent, newChild, refChild, isMove = true) {\n    this.delegate.insertBefore(parent, newChild, refChild); // If `isMove` true than we should animate this insert.\n\n    this.engine.onInsert(this.namespaceId, newChild, parent, isMove);\n  }\n\n  removeChild(parent, oldChild, isHostElement) {\n    this.engine.onRemove(this.namespaceId, oldChild, this.delegate, isHostElement);\n  }\n\n  selectRootElement(selectorOrNode, preserveContent) {\n    return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n  }\n\n  parentNode(node) {\n    return this.delegate.parentNode(node);\n  }\n\n  nextSibling(node) {\n    return this.delegate.nextSibling(node);\n  }\n\n  setAttribute(el, name, value, namespace) {\n    this.delegate.setAttribute(el, name, value, namespace);\n  }\n\n  removeAttribute(el, name, namespace) {\n    this.delegate.removeAttribute(el, name, namespace);\n  }\n\n  addClass(el, name) {\n    this.delegate.addClass(el, name);\n  }\n\n  removeClass(el, name) {\n    this.delegate.removeClass(el, name);\n  }\n\n  setStyle(el, style, value, flags) {\n    this.delegate.setStyle(el, style, value, flags);\n  }\n\n  removeStyle(el, style, flags) {\n    this.delegate.removeStyle(el, style, flags);\n  }\n\n  setProperty(el, name, value) {\n    if (name.charAt(0) == ANIMATION_PREFIX && name == DISABLE_ANIMATIONS_FLAG) {\n      this.disableAnimations(el, !!value);\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n\n  setValue(node, value) {\n    this.delegate.setValue(node, value);\n  }\n\n  listen(target, eventName, callback) {\n    return this.delegate.listen(target, eventName, callback);\n  }\n\n  disableAnimations(element, value) {\n    this.engine.disableAnimations(element, value);\n  }\n\n}\n\nclass AnimationRenderer extends BaseAnimationRenderer {\n  constructor(factory, namespaceId, delegate, engine, onDestroy) {\n    super(namespaceId, delegate, engine, onDestroy);\n    this.factory = factory;\n    this.namespaceId = namespaceId;\n  }\n\n  setProperty(el, name, value) {\n    if (name.charAt(0) == ANIMATION_PREFIX) {\n      if (name.charAt(1) == '.' && name == DISABLE_ANIMATIONS_FLAG) {\n        value = value === undefined ? true : !!value;\n        this.disableAnimations(el, value);\n      } else {\n        this.engine.process(this.namespaceId, el, name.slice(1), value);\n      }\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n\n  listen(target, eventName, callback) {\n    if (eventName.charAt(0) == ANIMATION_PREFIX) {\n      const element = resolveElementFromTarget(target);\n      let name = eventName.slice(1);\n      let phase = ''; // @listener.phase is for trigger animation callbacks\n      // @@listener is for animation builder callbacks\n\n      if (name.charAt(0) != ANIMATION_PREFIX) {\n        [name, phase] = parseTriggerCallbackName(name);\n      }\n\n      return this.engine.listen(this.namespaceId, element, name, phase, event => {\n        const countId = event['_data'] || -1;\n        this.factory.scheduleListenerCallback(countId, callback, event);\n      });\n    }\n\n    return this.delegate.listen(target, eventName, callback);\n  }\n\n}\n\nfunction resolveElementFromTarget(target) {\n  switch (target) {\n    case 'body':\n      return document.body;\n\n    case 'document':\n      return document;\n\n    case 'window':\n      return window;\n\n    default:\n      return target;\n  }\n}\n\nfunction parseTriggerCallbackName(triggerName) {\n  const dotIndex = triggerName.indexOf('.');\n  const trigger = triggerName.substring(0, dotIndex);\n  const phase = triggerName.slice(dotIndex + 1);\n  return [trigger, phase];\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass InjectableAnimationEngine extends ɵAnimationEngine {\n  // The `ApplicationRef` is injected here explicitly to force the dependency ordering.\n  // Since the `ApplicationRef` should be created earlier before the `AnimationEngine`, they\n  // both have `ngOnDestroy` hooks and `flush()` must be called after all views are destroyed.\n  constructor(doc, driver, normalizer, appRef) {\n    super(doc.body, driver, normalizer);\n  }\n\n  ngOnDestroy() {\n    this.flush();\n  }\n\n}\n\nInjectableAnimationEngine.ɵfac = function InjectableAnimationEngine_Factory(t) {\n  return new (t || InjectableAnimationEngine)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1.AnimationDriver), i0.ɵɵinject(i1.ɵAnimationStyleNormalizer), i0.ɵɵinject(i0.ApplicationRef));\n};\n\nInjectableAnimationEngine.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: InjectableAnimationEngine,\n  factory: InjectableAnimationEngine.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InjectableAnimationEngine, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1.AnimationDriver\n    }, {\n      type: i1.ɵAnimationStyleNormalizer\n    }, {\n      type: i0.ApplicationRef\n    }];\n  }, null);\n})();\n\nfunction instantiateDefaultStyleNormalizer() {\n  return new ɵWebAnimationsStyleNormalizer();\n}\n\nfunction instantiateRendererFactory(renderer, engine, zone) {\n  return new AnimationRendererFactory(renderer, engine, zone);\n}\n\nconst SHARED_ANIMATION_PROVIDERS = [{\n  provide: AnimationBuilder,\n  useClass: BrowserAnimationBuilder\n}, {\n  provide: ɵAnimationStyleNormalizer,\n  useFactory: instantiateDefaultStyleNormalizer\n}, {\n  provide: ɵAnimationEngine,\n  useClass: InjectableAnimationEngine\n}, {\n  provide: RendererFactory2,\n  useFactory: instantiateRendererFactory,\n  deps: [ɵDomRendererFactory2, ɵAnimationEngine, NgZone]\n}];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserModule.\n */\n\nconst BROWSER_ANIMATIONS_PROVIDERS = [{\n  provide: AnimationDriver,\n  useFactory: () => new ɵWebAnimationsDriver()\n}, {\n  provide: ANIMATION_MODULE_TYPE,\n  useValue: 'BrowserAnimations'\n}, ...SHARED_ANIMATION_PROVIDERS];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserTestingModule.\n */\n\nconst BROWSER_NOOP_ANIMATIONS_PROVIDERS = [{\n  provide: AnimationDriver,\n  useClass: ɵNoopAnimationDriver\n}, {\n  provide: ANIMATION_MODULE_TYPE,\n  useValue: 'NoopAnimations'\n}, ...SHARED_ANIMATION_PROVIDERS];\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Exports `BrowserModule` with additional [dependency-injection providers](guide/glossary#provider)\n * for use with animations. See [Animations](guide/animations).\n * @publicApi\n */\n\nclass BrowserAnimationsModule {\n  /**\n   * Configures the module based on the specified object.\n   *\n   * @param config Object used to configure the behavior of the `BrowserAnimationsModule`.\n   * @see `BrowserAnimationsModuleConfig`\n   *\n   * @usageNotes\n   * When registering the `BrowserAnimationsModule`, you can use the `withConfig`\n   * function as follows:\n   * ```\n   * @NgModule({\n   *   imports: [BrowserAnimationsModule.withConfig(config)]\n   * })\n   * class MyNgModule {}\n   * ```\n   */\n  static withConfig(config) {\n    return {\n      ngModule: BrowserAnimationsModule,\n      providers: config.disableAnimations ? BROWSER_NOOP_ANIMATIONS_PROVIDERS : BROWSER_ANIMATIONS_PROVIDERS\n    };\n  }\n\n}\n\nBrowserAnimationsModule.ɵfac = function BrowserAnimationsModule_Factory(t) {\n  return new (t || BrowserAnimationsModule)();\n};\n\nBrowserAnimationsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BrowserAnimationsModule,\n  exports: [BrowserModule]\n});\nBrowserAnimationsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: BROWSER_ANIMATIONS_PROVIDERS,\n  imports: [BrowserModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserAnimationsModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserModule],\n      providers: BROWSER_ANIMATIONS_PROVIDERS\n    }]\n  }], null, null);\n})();\n/**\n * Returns the set of [dependency-injection providers](guide/glossary#provider)\n * to enable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to enable animations in an application\n * bootstrapped using the `bootstrapApplication` function. In this scenario there\n * is no need to import the `BrowserAnimationsModule` NgModule at all, just add\n * providers returned by this function to the `providers` list as show below.\n *\n * ```typescript\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n * @developerPreview\n */\n\n\nfunction provideAnimations() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideAnimations` call results in app code.\n  return [...BROWSER_ANIMATIONS_PROVIDERS];\n}\n/**\n * A null player that must be imported to allow disabling of animations.\n * @publicApi\n */\n\n\nclass NoopAnimationsModule {}\n\nNoopAnimationsModule.ɵfac = function NoopAnimationsModule_Factory(t) {\n  return new (t || NoopAnimationsModule)();\n};\n\nNoopAnimationsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NoopAnimationsModule,\n  exports: [BrowserModule]\n});\nNoopAnimationsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS,\n  imports: [BrowserModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NoopAnimationsModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserModule],\n      providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS\n    }]\n  }], null, null);\n})();\n/**\n * Returns the set of [dependency-injection providers](guide/glossary#provider)\n * to disable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to bootstrap an application using\n * the `bootstrapApplication` function, but you need to disable animations\n * (for example, when running tests).\n *\n * ```typescript\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideNoopAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n * @developerPreview\n */\n\n\nfunction provideNoopAnimations() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideNoopAnimations` call results in app code.\n  return [...BROWSER_NOOP_ANIMATIONS_PROVIDERS];\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { BrowserAnimationsModule, NoopAnimationsModule, provideAnimations, provideNoopAnimations, AnimationRenderer as ɵAnimationRenderer, AnimationRendererFactory as ɵAnimationRendererFactory, BrowserAnimationBuilder as ɵBrowserAnimationBuilder, BrowserAnimationFactory as ɵBrowserAnimationFactory, InjectableAnimationEngine as ɵInjectableAnimationEngine };", "map": {"version": 3, "names": ["i0", "ViewEncapsulation", "Injectable", "Inject", "RendererFactory2", "NgZone", "ANIMATION_MODULE_TYPE", "NgModule", "ɵDomRendererFactory2", "BrowserModule", "AnimationBuilder", "sequence", "AnimationFactory", "i1", "ɵAnimationEngine", "ɵWebAnimationsStyleNormalizer", "ɵAnimationStyleNormalizer", "AnimationDriver", "ɵWebAnimationsDriver", "ɵNoopAnimationDriver", "DOCUMENT", "BrowserAnimationBuilder", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "doc", "_nextAnimationId", "typeData", "id", "encapsulation", "None", "styles", "data", "animation", "_renderer", "<PERSON><PERSON><PERSON><PERSON>", "body", "build", "toString", "entry", "Array", "isArray", "issueAnimationCommand", "BrowserAnimationFactory", "ɵfac", "ɵprov", "type", "undefined", "decorators", "args", "_id", "create", "element", "options", "RendererAnimationPlayer", "parentPlayer", "_started", "totalTime", "_command", "_listen", "eventName", "callback", "listen", "command", "onDone", "fn", "onStart", "onDestroy", "init", "hasStarted", "play", "pause", "restart", "finish", "destroy", "reset", "setPosition", "p", "getPosition", "engine", "players", "renderer", "setProperty", "ANIMATION_PREFIX", "DISABLE_ANIMATIONS_FLAG", "AnimationRendererFactory", "delegate", "_zone", "_currentId", "_microtaskId", "_animationCallbacksBuffer", "_rendererCache", "Map", "_cdRecurDepth", "promise", "Promise", "resolve", "onRemovalComplete", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "hostElement", "EMPTY_NAMESPACE_ID", "get", "on<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delete", "BaseAnimationRenderer", "set", "componentId", "namespaceId", "register", "registerTrigger", "trigger", "for<PERSON>ach", "name", "animationTriggers", "<PERSON><PERSON><PERSON><PERSON>", "begin", "_scheduleCountTask", "then", "scheduleListenerCallback", "count", "run", "length", "tuple", "push", "end", "runOutsideAngular", "flush", "whenRenderingDone", "_onD<PERSON>roy", "destroyNode", "n", "createElement", "namespace", "createComment", "value", "createText", "append<PERSON><PERSON><PERSON>", "parent", "<PERSON><PERSON><PERSON><PERSON>", "onInsert", "insertBefore", "refChild", "isMove", "<PERSON><PERSON><PERSON><PERSON>", "isHostElement", "onRemove", "selectRootElement", "selectorOrNode", "preserve<PERSON><PERSON>nt", "node", "nextS<PERSON>ling", "setAttribute", "el", "removeAttribute", "addClass", "removeClass", "setStyle", "style", "flags", "removeStyle", "char<PERSON>t", "disableAnimations", "setValue", "target", "factory", "process", "slice", "resolveElementFromTarget", "phase", "parseTriggerCallbackName", "event", "countId", "document", "window", "triggerName", "dotIndex", "indexOf", "substring", "InjectableAnimationEngine", "driver", "normalizer", "appRef", "ngOnDestroy", "ApplicationRef", "instantiateDefaultStyleNormalizer", "instantiateRendererFactory", "zone", "SHARED_ANIMATION_PROVIDERS", "provide", "useClass", "useFactory", "deps", "BROWSER_ANIMATIONS_PROVIDERS", "useValue", "BROWSER_NOOP_ANIMATIONS_PROVIDERS", "BrowserAnimationsModule", "withConfig", "config", "ngModule", "providers", "ɵmod", "ɵinj", "exports", "provideAnimations", "NoopAnimationsModule", "provideNoopAnimations", "ɵAnimationRenderer", "ɵAnimationRendererFactory", "ɵBrowserAnimationBuilder", "ɵBrowserAnimationFactory", "ɵInjectableAnimationEngine"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/platform-browser/fesm2020/animations.mjs"], "sourcesContent": ["/**\n * @license Angular v14.3.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ViewEncapsulation, Injectable, Inject, RendererFactory2, NgZone, ANIMATION_MODULE_TYPE, NgModule } from '@angular/core';\nexport { ANIMATION_MODULE_TYPE } from '@angular/core';\nimport { ɵDomRendererFactory2, BrowserModule } from '@angular/platform-browser';\nimport { AnimationBuilder, sequence, AnimationFactory } from '@angular/animations';\nimport * as i1 from '@angular/animations/browser';\nimport { ɵAnimationEngine, ɵWebAnimationsStyleNormalizer, ɵAnimationStyleNormalizer, AnimationDriver, ɵWebAnimationsDriver, ɵNoopAnimationDriver } from '@angular/animations/browser';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass BrowserAnimationBuilder extends AnimationBuilder {\n    constructor(rootRenderer, doc) {\n        super();\n        this._nextAnimationId = 0;\n        const typeData = { id: '0', encapsulation: ViewEncapsulation.None, styles: [], data: { animation: [] } };\n        this._renderer = rootRenderer.createRenderer(doc.body, typeData);\n    }\n    build(animation) {\n        const id = this._nextAnimationId.toString();\n        this._nextAnimationId++;\n        const entry = Array.isArray(animation) ? sequence(animation) : animation;\n        issueAnimationCommand(this._renderer, null, id, 'register', [entry]);\n        return new BrowserAnimationFactory(id, this._renderer);\n    }\n}\nBrowserAnimationBuilder.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: BrowserAnimationBuilder, deps: [{ token: i0.RendererFactory2 }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nBrowserAnimationBuilder.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: BrowserAnimationBuilder });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: BrowserAnimationBuilder, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i0.RendererFactory2 }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\nclass BrowserAnimationFactory extends AnimationFactory {\n    constructor(_id, _renderer) {\n        super();\n        this._id = _id;\n        this._renderer = _renderer;\n    }\n    create(element, options) {\n        return new RendererAnimationPlayer(this._id, element, options || {}, this._renderer);\n    }\n}\nclass RendererAnimationPlayer {\n    constructor(id, element, options, _renderer) {\n        this.id = id;\n        this.element = element;\n        this._renderer = _renderer;\n        this.parentPlayer = null;\n        this._started = false;\n        this.totalTime = 0;\n        this._command('create', options);\n    }\n    _listen(eventName, callback) {\n        return this._renderer.listen(this.element, `@@${this.id}:${eventName}`, callback);\n    }\n    _command(command, ...args) {\n        return issueAnimationCommand(this._renderer, this.element, this.id, command, args);\n    }\n    onDone(fn) {\n        this._listen('done', fn);\n    }\n    onStart(fn) {\n        this._listen('start', fn);\n    }\n    onDestroy(fn) {\n        this._listen('destroy', fn);\n    }\n    init() {\n        this._command('init');\n    }\n    hasStarted() {\n        return this._started;\n    }\n    play() {\n        this._command('play');\n        this._started = true;\n    }\n    pause() {\n        this._command('pause');\n    }\n    restart() {\n        this._command('restart');\n    }\n    finish() {\n        this._command('finish');\n    }\n    destroy() {\n        this._command('destroy');\n    }\n    reset() {\n        this._command('reset');\n        this._started = false;\n    }\n    setPosition(p) {\n        this._command('setPosition', p);\n    }\n    getPosition() {\n        return this._renderer.engine.players[+this.id]?.getPosition() ?? 0;\n    }\n}\nfunction issueAnimationCommand(renderer, element, id, command, args) {\n    return renderer.setProperty(element, `@@${id}:${command}`, args);\n}\n\nconst ANIMATION_PREFIX = '@';\nconst DISABLE_ANIMATIONS_FLAG = '@.disabled';\nclass AnimationRendererFactory {\n    constructor(delegate, engine, _zone) {\n        this.delegate = delegate;\n        this.engine = engine;\n        this._zone = _zone;\n        this._currentId = 0;\n        this._microtaskId = 1;\n        this._animationCallbacksBuffer = [];\n        this._rendererCache = new Map();\n        this._cdRecurDepth = 0;\n        this.promise = Promise.resolve(0);\n        engine.onRemovalComplete = (element, delegate) => {\n            // Note: if a component element has a leave animation, and a host leave animation,\n            // the view engine will call `removeChild` for the parent\n            // component renderer as well as for the child component renderer.\n            // Therefore, we need to check if we already removed the element.\n            const parentNode = delegate?.parentNode(element);\n            if (parentNode) {\n                delegate.removeChild(parentNode, element);\n            }\n        };\n    }\n    createRenderer(hostElement, type) {\n        const EMPTY_NAMESPACE_ID = '';\n        // cache the delegates to find out which cached delegate can\n        // be used by which cached renderer\n        const delegate = this.delegate.createRenderer(hostElement, type);\n        if (!hostElement || !type || !type.data || !type.data['animation']) {\n            let renderer = this._rendererCache.get(delegate);\n            if (!renderer) {\n                // Ensure that the renderer is removed from the cache on destroy\n                // since it may contain references to detached DOM nodes.\n                const onRendererDestroy = () => this._rendererCache.delete(delegate);\n                renderer =\n                    new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine, onRendererDestroy);\n                // only cache this result when the base renderer is used\n                this._rendererCache.set(delegate, renderer);\n            }\n            return renderer;\n        }\n        const componentId = type.id;\n        const namespaceId = type.id + '-' + this._currentId;\n        this._currentId++;\n        this.engine.register(namespaceId, hostElement);\n        const registerTrigger = (trigger) => {\n            if (Array.isArray(trigger)) {\n                trigger.forEach(registerTrigger);\n            }\n            else {\n                this.engine.registerTrigger(componentId, namespaceId, hostElement, trigger.name, trigger);\n            }\n        };\n        const animationTriggers = type.data['animation'];\n        animationTriggers.forEach(registerTrigger);\n        return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n    }\n    begin() {\n        this._cdRecurDepth++;\n        if (this.delegate.begin) {\n            this.delegate.begin();\n        }\n    }\n    _scheduleCountTask() {\n        // always use promise to schedule microtask instead of use Zone\n        this.promise.then(() => {\n            this._microtaskId++;\n        });\n    }\n    /** @internal */\n    scheduleListenerCallback(count, fn, data) {\n        if (count >= 0 && count < this._microtaskId) {\n            this._zone.run(() => fn(data));\n            return;\n        }\n        if (this._animationCallbacksBuffer.length == 0) {\n            Promise.resolve(null).then(() => {\n                this._zone.run(() => {\n                    this._animationCallbacksBuffer.forEach(tuple => {\n                        const [fn, data] = tuple;\n                        fn(data);\n                    });\n                    this._animationCallbacksBuffer = [];\n                });\n            });\n        }\n        this._animationCallbacksBuffer.push([fn, data]);\n    }\n    end() {\n        this._cdRecurDepth--;\n        // this is to prevent animations from running twice when an inner\n        // component does CD when a parent component instead has inserted it\n        if (this._cdRecurDepth == 0) {\n            this._zone.runOutsideAngular(() => {\n                this._scheduleCountTask();\n                this.engine.flush(this._microtaskId);\n            });\n        }\n        if (this.delegate.end) {\n            this.delegate.end();\n        }\n    }\n    whenRenderingDone() {\n        return this.engine.whenRenderingDone();\n    }\n}\nAnimationRendererFactory.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: AnimationRendererFactory, deps: [{ token: i0.RendererFactory2 }, { token: i1.ɵAnimationEngine }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\nAnimationRendererFactory.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: AnimationRendererFactory });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: AnimationRendererFactory, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i0.RendererFactory2 }, { type: i1.ɵAnimationEngine }, { type: i0.NgZone }]; } });\nclass BaseAnimationRenderer {\n    constructor(namespaceId, delegate, engine, _onDestroy) {\n        this.namespaceId = namespaceId;\n        this.delegate = delegate;\n        this.engine = engine;\n        this._onDestroy = _onDestroy;\n        this.destroyNode = this.delegate.destroyNode ? (n) => delegate.destroyNode(n) : null;\n    }\n    get data() {\n        return this.delegate.data;\n    }\n    destroy() {\n        this.engine.destroy(this.namespaceId, this.delegate);\n        this.delegate.destroy();\n        this._onDestroy?.();\n    }\n    createElement(name, namespace) {\n        return this.delegate.createElement(name, namespace);\n    }\n    createComment(value) {\n        return this.delegate.createComment(value);\n    }\n    createText(value) {\n        return this.delegate.createText(value);\n    }\n    appendChild(parent, newChild) {\n        this.delegate.appendChild(parent, newChild);\n        this.engine.onInsert(this.namespaceId, newChild, parent, false);\n    }\n    insertBefore(parent, newChild, refChild, isMove = true) {\n        this.delegate.insertBefore(parent, newChild, refChild);\n        // If `isMove` true than we should animate this insert.\n        this.engine.onInsert(this.namespaceId, newChild, parent, isMove);\n    }\n    removeChild(parent, oldChild, isHostElement) {\n        this.engine.onRemove(this.namespaceId, oldChild, this.delegate, isHostElement);\n    }\n    selectRootElement(selectorOrNode, preserveContent) {\n        return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n    }\n    parentNode(node) {\n        return this.delegate.parentNode(node);\n    }\n    nextSibling(node) {\n        return this.delegate.nextSibling(node);\n    }\n    setAttribute(el, name, value, namespace) {\n        this.delegate.setAttribute(el, name, value, namespace);\n    }\n    removeAttribute(el, name, namespace) {\n        this.delegate.removeAttribute(el, name, namespace);\n    }\n    addClass(el, name) {\n        this.delegate.addClass(el, name);\n    }\n    removeClass(el, name) {\n        this.delegate.removeClass(el, name);\n    }\n    setStyle(el, style, value, flags) {\n        this.delegate.setStyle(el, style, value, flags);\n    }\n    removeStyle(el, style, flags) {\n        this.delegate.removeStyle(el, style, flags);\n    }\n    setProperty(el, name, value) {\n        if (name.charAt(0) == ANIMATION_PREFIX && name == DISABLE_ANIMATIONS_FLAG) {\n            this.disableAnimations(el, !!value);\n        }\n        else {\n            this.delegate.setProperty(el, name, value);\n        }\n    }\n    setValue(node, value) {\n        this.delegate.setValue(node, value);\n    }\n    listen(target, eventName, callback) {\n        return this.delegate.listen(target, eventName, callback);\n    }\n    disableAnimations(element, value) {\n        this.engine.disableAnimations(element, value);\n    }\n}\nclass AnimationRenderer extends BaseAnimationRenderer {\n    constructor(factory, namespaceId, delegate, engine, onDestroy) {\n        super(namespaceId, delegate, engine, onDestroy);\n        this.factory = factory;\n        this.namespaceId = namespaceId;\n    }\n    setProperty(el, name, value) {\n        if (name.charAt(0) == ANIMATION_PREFIX) {\n            if (name.charAt(1) == '.' && name == DISABLE_ANIMATIONS_FLAG) {\n                value = value === undefined ? true : !!value;\n                this.disableAnimations(el, value);\n            }\n            else {\n                this.engine.process(this.namespaceId, el, name.slice(1), value);\n            }\n        }\n        else {\n            this.delegate.setProperty(el, name, value);\n        }\n    }\n    listen(target, eventName, callback) {\n        if (eventName.charAt(0) == ANIMATION_PREFIX) {\n            const element = resolveElementFromTarget(target);\n            let name = eventName.slice(1);\n            let phase = '';\n            // @listener.phase is for trigger animation callbacks\n            // @@listener is for animation builder callbacks\n            if (name.charAt(0) != ANIMATION_PREFIX) {\n                [name, phase] = parseTriggerCallbackName(name);\n            }\n            return this.engine.listen(this.namespaceId, element, name, phase, event => {\n                const countId = event['_data'] || -1;\n                this.factory.scheduleListenerCallback(countId, callback, event);\n            });\n        }\n        return this.delegate.listen(target, eventName, callback);\n    }\n}\nfunction resolveElementFromTarget(target) {\n    switch (target) {\n        case 'body':\n            return document.body;\n        case 'document':\n            return document;\n        case 'window':\n            return window;\n        default:\n            return target;\n    }\n}\nfunction parseTriggerCallbackName(triggerName) {\n    const dotIndex = triggerName.indexOf('.');\n    const trigger = triggerName.substring(0, dotIndex);\n    const phase = triggerName.slice(dotIndex + 1);\n    return [trigger, phase];\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass InjectableAnimationEngine extends ɵAnimationEngine {\n    // The `ApplicationRef` is injected here explicitly to force the dependency ordering.\n    // Since the `ApplicationRef` should be created earlier before the `AnimationEngine`, they\n    // both have `ngOnDestroy` hooks and `flush()` must be called after all views are destroyed.\n    constructor(doc, driver, normalizer, appRef) {\n        super(doc.body, driver, normalizer);\n    }\n    ngOnDestroy() {\n        this.flush();\n    }\n}\nInjectableAnimationEngine.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: InjectableAnimationEngine, deps: [{ token: DOCUMENT }, { token: i1.AnimationDriver }, { token: i1.ɵAnimationStyleNormalizer }, { token: i0.ApplicationRef }], target: i0.ɵɵFactoryTarget.Injectable });\nInjectableAnimationEngine.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: InjectableAnimationEngine });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: InjectableAnimationEngine, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1.AnimationDriver }, { type: i1.ɵAnimationStyleNormalizer }, { type: i0.ApplicationRef }]; } });\nfunction instantiateDefaultStyleNormalizer() {\n    return new ɵWebAnimationsStyleNormalizer();\n}\nfunction instantiateRendererFactory(renderer, engine, zone) {\n    return new AnimationRendererFactory(renderer, engine, zone);\n}\nconst SHARED_ANIMATION_PROVIDERS = [\n    { provide: AnimationBuilder, useClass: BrowserAnimationBuilder },\n    { provide: ɵAnimationStyleNormalizer, useFactory: instantiateDefaultStyleNormalizer },\n    { provide: ɵAnimationEngine, useClass: InjectableAnimationEngine }, {\n        provide: RendererFactory2,\n        useFactory: instantiateRendererFactory,\n        deps: [ɵDomRendererFactory2, ɵAnimationEngine, NgZone]\n    }\n];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserModule.\n */\nconst BROWSER_ANIMATIONS_PROVIDERS = [\n    { provide: AnimationDriver, useFactory: () => new ɵWebAnimationsDriver() },\n    { provide: ANIMATION_MODULE_TYPE, useValue: 'BrowserAnimations' }, ...SHARED_ANIMATION_PROVIDERS\n];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserTestingModule.\n */\nconst BROWSER_NOOP_ANIMATIONS_PROVIDERS = [\n    { provide: AnimationDriver, useClass: ɵNoopAnimationDriver },\n    { provide: ANIMATION_MODULE_TYPE, useValue: 'NoopAnimations' }, ...SHARED_ANIMATION_PROVIDERS\n];\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Exports `BrowserModule` with additional [dependency-injection providers](guide/glossary#provider)\n * for use with animations. See [Animations](guide/animations).\n * @publicApi\n */\nclass BrowserAnimationsModule {\n    /**\n     * Configures the module based on the specified object.\n     *\n     * @param config Object used to configure the behavior of the `BrowserAnimationsModule`.\n     * @see `BrowserAnimationsModuleConfig`\n     *\n     * @usageNotes\n     * When registering the `BrowserAnimationsModule`, you can use the `withConfig`\n     * function as follows:\n     * ```\n     * @NgModule({\n     *   imports: [BrowserAnimationsModule.withConfig(config)]\n     * })\n     * class MyNgModule {}\n     * ```\n     */\n    static withConfig(config) {\n        return {\n            ngModule: BrowserAnimationsModule,\n            providers: config.disableAnimations ? BROWSER_NOOP_ANIMATIONS_PROVIDERS :\n                BROWSER_ANIMATIONS_PROVIDERS\n        };\n    }\n}\nBrowserAnimationsModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: BrowserAnimationsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nBrowserAnimationsModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.3.0\", ngImport: i0, type: BrowserAnimationsModule, exports: [BrowserModule] });\nBrowserAnimationsModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: BrowserAnimationsModule, providers: BROWSER_ANIMATIONS_PROVIDERS, imports: [BrowserModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: BrowserAnimationsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [BrowserModule],\n                    providers: BROWSER_ANIMATIONS_PROVIDERS,\n                }]\n        }] });\n/**\n * Returns the set of [dependency-injection providers](guide/glossary#provider)\n * to enable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to enable animations in an application\n * bootstrapped using the `bootstrapApplication` function. In this scenario there\n * is no need to import the `BrowserAnimationsModule` NgModule at all, just add\n * providers returned by this function to the `providers` list as show below.\n *\n * ```typescript\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n * @developerPreview\n */\nfunction provideAnimations() {\n    // Return a copy to prevent changes to the original array in case any in-place\n    // alterations are performed to the `provideAnimations` call results in app code.\n    return [...BROWSER_ANIMATIONS_PROVIDERS];\n}\n/**\n * A null player that must be imported to allow disabling of animations.\n * @publicApi\n */\nclass NoopAnimationsModule {\n}\nNoopAnimationsModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: NoopAnimationsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nNoopAnimationsModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.3.0\", ngImport: i0, type: NoopAnimationsModule, exports: [BrowserModule] });\nNoopAnimationsModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: NoopAnimationsModule, providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS, imports: [BrowserModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: NoopAnimationsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [BrowserModule],\n                    providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS,\n                }]\n        }] });\n/**\n * Returns the set of [dependency-injection providers](guide/glossary#provider)\n * to disable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to bootstrap an application using\n * the `bootstrapApplication` function, but you need to disable animations\n * (for example, when running tests).\n *\n * ```typescript\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideNoopAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n * @developerPreview\n */\nfunction provideNoopAnimations() {\n    // Return a copy to prevent changes to the original array in case any in-place\n    // alterations are performed to the `provideNoopAnimations` call results in app code.\n    return [...BROWSER_NOOP_ANIMATIONS_PROVIDERS];\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserAnimationsModule, NoopAnimationsModule, provideAnimations, provideNoopAnimations, AnimationRenderer as ɵAnimationRenderer, AnimationRendererFactory as ɵAnimationRendererFactory, BrowserAnimationBuilder as ɵBrowserAnimationBuilder, BrowserAnimationFactory as ɵBrowserAnimationFactory, InjectableAnimationEngine as ɵInjectableAnimationEngine };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AAEA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,iBAAT,EAA4BC,UAA5B,EAAwCC,MAAxC,EAAgDC,gBAAhD,EAAkEC,MAAlE,EAA0EC,qBAA1E,EAAiGC,QAAjG,QAAiH,eAAjH;AACA,SAASD,qBAAT,QAAsC,eAAtC;AACA,SAASE,oBAAT,EAA+BC,aAA/B,QAAoD,2BAApD;AACA,SAASC,gBAAT,EAA2BC,QAA3B,EAAqCC,gBAArC,QAA6D,qBAA7D;AACA,OAAO,KAAKC,EAAZ,MAAoB,6BAApB;AACA,SAASC,gBAAT,EAA2BC,6BAA3B,EAA0DC,yBAA1D,EAAqFC,eAArF,EAAsGC,oBAAtG,EAA4HC,oBAA5H,QAAwJ,6BAAxJ;AACA,SAASC,QAAT,QAAyB,iBAAzB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,uBAAN,SAAsCX,gBAAtC,CAAuD;EACnDY,WAAW,CAACC,YAAD,EAAeC,GAAf,EAAoB;IAC3B;IACA,KAAKC,gBAAL,GAAwB,CAAxB;IACA,MAAMC,QAAQ,GAAG;MAAEC,EAAE,EAAE,GAAN;MAAWC,aAAa,EAAE3B,iBAAiB,CAAC4B,IAA5C;MAAkDC,MAAM,EAAE,EAA1D;MAA8DC,IAAI,EAAE;QAAEC,SAAS,EAAE;MAAb;IAApE,CAAjB;IACA,KAAKC,SAAL,GAAiBV,YAAY,CAACW,cAAb,CAA4BV,GAAG,CAACW,IAAhC,EAAsCT,QAAtC,CAAjB;EACH;;EACDU,KAAK,CAACJ,SAAD,EAAY;IACb,MAAML,EAAE,GAAG,KAAKF,gBAAL,CAAsBY,QAAtB,EAAX;;IACA,KAAKZ,gBAAL;IACA,MAAMa,KAAK,GAAGC,KAAK,CAACC,OAAN,CAAcR,SAAd,IAA2BrB,QAAQ,CAACqB,SAAD,CAAnC,GAAiDA,SAA/D;IACAS,qBAAqB,CAAC,KAAKR,SAAN,EAAiB,IAAjB,EAAuBN,EAAvB,EAA2B,UAA3B,EAAuC,CAACW,KAAD,CAAvC,CAArB;IACA,OAAO,IAAII,uBAAJ,CAA4Bf,EAA5B,EAAgC,KAAKM,SAArC,CAAP;EACH;;AAbkD;;AAevDZ,uBAAuB,CAACsB,IAAxB;EAAA,iBAAoHtB,uBAApH,EAA0GrB,EAA1G,UAA6JA,EAAE,CAACI,gBAAhK,GAA0GJ,EAA1G,UAA6LoB,QAA7L;AAAA;;AACAC,uBAAuB,CAACuB,KAAxB,kBAD0G5C,EAC1G;EAAA,OAAwHqB,uBAAxH;EAAA,SAAwHA,uBAAxH;AAAA;;AACA;EAAA,mDAF0GrB,EAE1G,mBAA2FqB,uBAA3F,EAAgI,CAAC;IACrHwB,IAAI,EAAE3C;EAD+G,CAAD,CAAhI,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAE2C,IAAI,EAAE7C,EAAE,CAACI;IAAX,CAAD,EAAgC;MAAEyC,IAAI,EAAEC,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC7FF,IAAI,EAAE1C,MADuF;QAE7F6C,IAAI,EAAE,CAAC5B,QAAD;MAFuF,CAAD;IAA/B,CAAhC,CAAP;EAGlB,CALxB;AAAA;;AAMA,MAAMsB,uBAAN,SAAsC9B,gBAAtC,CAAuD;EACnDU,WAAW,CAAC2B,GAAD,EAAMhB,SAAN,EAAiB;IACxB;IACA,KAAKgB,GAAL,GAAWA,GAAX;IACA,KAAKhB,SAAL,GAAiBA,SAAjB;EACH;;EACDiB,MAAM,CAACC,OAAD,EAAUC,OAAV,EAAmB;IACrB,OAAO,IAAIC,uBAAJ,CAA4B,KAAKJ,GAAjC,EAAsCE,OAAtC,EAA+CC,OAAO,IAAI,EAA1D,EAA8D,KAAKnB,SAAnE,CAAP;EACH;;AARkD;;AAUvD,MAAMoB,uBAAN,CAA8B;EAC1B/B,WAAW,CAACK,EAAD,EAAKwB,OAAL,EAAcC,OAAd,EAAuBnB,SAAvB,EAAkC;IACzC,KAAKN,EAAL,GAAUA,EAAV;IACA,KAAKwB,OAAL,GAAeA,OAAf;IACA,KAAKlB,SAAL,GAAiBA,SAAjB;IACA,KAAKqB,YAAL,GAAoB,IAApB;IACA,KAAKC,QAAL,GAAgB,KAAhB;IACA,KAAKC,SAAL,GAAiB,CAAjB;;IACA,KAAKC,QAAL,CAAc,QAAd,EAAwBL,OAAxB;EACH;;EACDM,OAAO,CAACC,SAAD,EAAYC,QAAZ,EAAsB;IACzB,OAAO,KAAK3B,SAAL,CAAe4B,MAAf,CAAsB,KAAKV,OAA3B,EAAqC,KAAI,KAAKxB,EAAG,IAAGgC,SAAU,EAA9D,EAAiEC,QAAjE,CAAP;EACH;;EACDH,QAAQ,CAACK,OAAD,EAAU,GAAGd,IAAb,EAAmB;IACvB,OAAOP,qBAAqB,CAAC,KAAKR,SAAN,EAAiB,KAAKkB,OAAtB,EAA+B,KAAKxB,EAApC,EAAwCmC,OAAxC,EAAiDd,IAAjD,CAA5B;EACH;;EACDe,MAAM,CAACC,EAAD,EAAK;IACP,KAAKN,OAAL,CAAa,MAAb,EAAqBM,EAArB;EACH;;EACDC,OAAO,CAACD,EAAD,EAAK;IACR,KAAKN,OAAL,CAAa,OAAb,EAAsBM,EAAtB;EACH;;EACDE,SAAS,CAACF,EAAD,EAAK;IACV,KAAKN,OAAL,CAAa,SAAb,EAAwBM,EAAxB;EACH;;EACDG,IAAI,GAAG;IACH,KAAKV,QAAL,CAAc,MAAd;EACH;;EACDW,UAAU,GAAG;IACT,OAAO,KAAKb,QAAZ;EACH;;EACDc,IAAI,GAAG;IACH,KAAKZ,QAAL,CAAc,MAAd;;IACA,KAAKF,QAAL,GAAgB,IAAhB;EACH;;EACDe,KAAK,GAAG;IACJ,KAAKb,QAAL,CAAc,OAAd;EACH;;EACDc,OAAO,GAAG;IACN,KAAKd,QAAL,CAAc,SAAd;EACH;;EACDe,MAAM,GAAG;IACL,KAAKf,QAAL,CAAc,QAAd;EACH;;EACDgB,OAAO,GAAG;IACN,KAAKhB,QAAL,CAAc,SAAd;EACH;;EACDiB,KAAK,GAAG;IACJ,KAAKjB,QAAL,CAAc,OAAd;;IACA,KAAKF,QAAL,GAAgB,KAAhB;EACH;;EACDoB,WAAW,CAACC,CAAD,EAAI;IACX,KAAKnB,QAAL,CAAc,aAAd,EAA6BmB,CAA7B;EACH;;EACDC,WAAW,GAAG;IACV,OAAO,KAAK5C,SAAL,CAAe6C,MAAf,CAAsBC,OAAtB,CAA8B,CAAC,KAAKpD,EAApC,GAAyCkD,WAAzC,MAA0D,CAAjE;EACH;;AAxDyB;;AA0D9B,SAASpC,qBAAT,CAA+BuC,QAA/B,EAAyC7B,OAAzC,EAAkDxB,EAAlD,EAAsDmC,OAAtD,EAA+Dd,IAA/D,EAAqE;EACjE,OAAOgC,QAAQ,CAACC,WAAT,CAAqB9B,OAArB,EAA+B,KAAIxB,EAAG,IAAGmC,OAAQ,EAAjD,EAAoDd,IAApD,CAAP;AACH;;AAED,MAAMkC,gBAAgB,GAAG,GAAzB;AACA,MAAMC,uBAAuB,GAAG,YAAhC;;AACA,MAAMC,wBAAN,CAA+B;EAC3B9D,WAAW,CAAC+D,QAAD,EAAWP,MAAX,EAAmBQ,KAAnB,EAA0B;IACjC,KAAKD,QAAL,GAAgBA,QAAhB;IACA,KAAKP,MAAL,GAAcA,MAAd;IACA,KAAKQ,KAAL,GAAaA,KAAb;IACA,KAAKC,UAAL,GAAkB,CAAlB;IACA,KAAKC,YAAL,GAAoB,CAApB;IACA,KAAKC,yBAAL,GAAiC,EAAjC;IACA,KAAKC,cAAL,GAAsB,IAAIC,GAAJ,EAAtB;IACA,KAAKC,aAAL,GAAqB,CAArB;IACA,KAAKC,OAAL,GAAeC,OAAO,CAACC,OAAR,CAAgB,CAAhB,CAAf;;IACAjB,MAAM,CAACkB,iBAAP,GAA2B,CAAC7C,OAAD,EAAUkC,QAAV,KAAuB;MAC9C;MACA;MACA;MACA;MACA,MAAMY,UAAU,GAAGZ,QAAQ,EAAEY,UAAV,CAAqB9C,OAArB,CAAnB;;MACA,IAAI8C,UAAJ,EAAgB;QACZZ,QAAQ,CAACa,WAAT,CAAqBD,UAArB,EAAiC9C,OAAjC;MACH;IACJ,CATD;EAUH;;EACDjB,cAAc,CAACiE,WAAD,EAActD,IAAd,EAAoB;IAC9B,MAAMuD,kBAAkB,GAAG,EAA3B,CAD8B,CAE9B;IACA;;IACA,MAAMf,QAAQ,GAAG,KAAKA,QAAL,CAAcnD,cAAd,CAA6BiE,WAA7B,EAA0CtD,IAA1C,CAAjB;;IACA,IAAI,CAACsD,WAAD,IAAgB,CAACtD,IAAjB,IAAyB,CAACA,IAAI,CAACd,IAA/B,IAAuC,CAACc,IAAI,CAACd,IAAL,CAAU,WAAV,CAA5C,EAAoE;MAChE,IAAIiD,QAAQ,GAAG,KAAKU,cAAL,CAAoBW,GAApB,CAAwBhB,QAAxB,CAAf;;MACA,IAAI,CAACL,QAAL,EAAe;QACX;QACA;QACA,MAAMsB,iBAAiB,GAAG,MAAM,KAAKZ,cAAL,CAAoBa,MAApB,CAA2BlB,QAA3B,CAAhC;;QACAL,QAAQ,GACJ,IAAIwB,qBAAJ,CAA0BJ,kBAA1B,EAA8Cf,QAA9C,EAAwD,KAAKP,MAA7D,EAAqEwB,iBAArE,CADJ,CAJW,CAMX;;QACA,KAAKZ,cAAL,CAAoBe,GAApB,CAAwBpB,QAAxB,EAAkCL,QAAlC;MACH;;MACD,OAAOA,QAAP;IACH;;IACD,MAAM0B,WAAW,GAAG7D,IAAI,CAAClB,EAAzB;IACA,MAAMgF,WAAW,GAAG9D,IAAI,CAAClB,EAAL,GAAU,GAAV,GAAgB,KAAK4D,UAAzC;IACA,KAAKA,UAAL;IACA,KAAKT,MAAL,CAAY8B,QAAZ,CAAqBD,WAArB,EAAkCR,WAAlC;;IACA,MAAMU,eAAe,GAAIC,OAAD,IAAa;MACjC,IAAIvE,KAAK,CAACC,OAAN,CAAcsE,OAAd,CAAJ,EAA4B;QACxBA,OAAO,CAACC,OAAR,CAAgBF,eAAhB;MACH,CAFD,MAGK;QACD,KAAK/B,MAAL,CAAY+B,eAAZ,CAA4BH,WAA5B,EAAyCC,WAAzC,EAAsDR,WAAtD,EAAmEW,OAAO,CAACE,IAA3E,EAAiFF,OAAjF;MACH;IACJ,CAPD;;IAQA,MAAMG,iBAAiB,GAAGpE,IAAI,CAACd,IAAL,CAAU,WAAV,CAA1B;IACAkF,iBAAiB,CAACF,OAAlB,CAA0BF,eAA1B;IACA,OAAO,IAAIK,iBAAJ,CAAsB,IAAtB,EAA4BP,WAA5B,EAAyCtB,QAAzC,EAAmD,KAAKP,MAAxD,CAAP;EACH;;EACDqC,KAAK,GAAG;IACJ,KAAKvB,aAAL;;IACA,IAAI,KAAKP,QAAL,CAAc8B,KAAlB,EAAyB;MACrB,KAAK9B,QAAL,CAAc8B,KAAd;IACH;EACJ;;EACDC,kBAAkB,GAAG;IACjB;IACA,KAAKvB,OAAL,CAAawB,IAAb,CAAkB,MAAM;MACpB,KAAK7B,YAAL;IACH,CAFD;EAGH;EACD;;;EACA8B,wBAAwB,CAACC,KAAD,EAAQvD,EAAR,EAAYjC,IAAZ,EAAkB;IACtC,IAAIwF,KAAK,IAAI,CAAT,IAAcA,KAAK,GAAG,KAAK/B,YAA/B,EAA6C;MACzC,KAAKF,KAAL,CAAWkC,GAAX,CAAe,MAAMxD,EAAE,CAACjC,IAAD,CAAvB;;MACA;IACH;;IACD,IAAI,KAAK0D,yBAAL,CAA+BgC,MAA/B,IAAyC,CAA7C,EAAgD;MAC5C3B,OAAO,CAACC,OAAR,CAAgB,IAAhB,EAAsBsB,IAAtB,CAA2B,MAAM;QAC7B,KAAK/B,KAAL,CAAWkC,GAAX,CAAe,MAAM;UACjB,KAAK/B,yBAAL,CAA+BsB,OAA/B,CAAuCW,KAAK,IAAI;YAC5C,MAAM,CAAC1D,EAAD,EAAKjC,IAAL,IAAa2F,KAAnB;YACA1D,EAAE,CAACjC,IAAD,CAAF;UACH,CAHD;;UAIA,KAAK0D,yBAAL,GAAiC,EAAjC;QACH,CAND;MAOH,CARD;IASH;;IACD,KAAKA,yBAAL,CAA+BkC,IAA/B,CAAoC,CAAC3D,EAAD,EAAKjC,IAAL,CAApC;EACH;;EACD6F,GAAG,GAAG;IACF,KAAKhC,aAAL,GADE,CAEF;IACA;;IACA,IAAI,KAAKA,aAAL,IAAsB,CAA1B,EAA6B;MACzB,KAAKN,KAAL,CAAWuC,iBAAX,CAA6B,MAAM;QAC/B,KAAKT,kBAAL;;QACA,KAAKtC,MAAL,CAAYgD,KAAZ,CAAkB,KAAKtC,YAAvB;MACH,CAHD;IAIH;;IACD,IAAI,KAAKH,QAAL,CAAcuC,GAAlB,EAAuB;MACnB,KAAKvC,QAAL,CAAcuC,GAAd;IACH;EACJ;;EACDG,iBAAiB,GAAG;IAChB,OAAO,KAAKjD,MAAL,CAAYiD,iBAAZ,EAAP;EACH;;AAvG0B;;AAyG/B3C,wBAAwB,CAACzC,IAAzB;EAAA,iBAAqHyC,wBAArH,EA3L0GpF,EA2L1G,UAA+JA,EAAE,CAACI,gBAAlK,GA3L0GJ,EA2L1G,UAA+La,EAAE,CAACC,gBAAlM,GA3L0Gd,EA2L1G,UAA+NA,EAAE,CAACK,MAAlO;AAAA;;AACA+E,wBAAwB,CAACxC,KAAzB,kBA5L0G5C,EA4L1G;EAAA,OAAyHoF,wBAAzH;EAAA,SAAyHA,wBAAzH;AAAA;;AACA;EAAA,mDA7L0GpF,EA6L1G,mBAA2FoF,wBAA3F,EAAiI,CAAC;IACtHvC,IAAI,EAAE3C;EADgH,CAAD,CAAjI,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAE2C,IAAI,EAAE7C,EAAE,CAACI;IAAX,CAAD,EAAgC;MAAEyC,IAAI,EAAEhC,EAAE,CAACC;IAAX,CAAhC,EAA+D;MAAE+B,IAAI,EAAE7C,EAAE,CAACK;IAAX,CAA/D,CAAP;EAA6F,CAFvI;AAAA;;AAGA,MAAMmG,qBAAN,CAA4B;EACxBlF,WAAW,CAACqF,WAAD,EAActB,QAAd,EAAwBP,MAAxB,EAAgCkD,UAAhC,EAA4C;IACnD,KAAKrB,WAAL,GAAmBA,WAAnB;IACA,KAAKtB,QAAL,GAAgBA,QAAhB;IACA,KAAKP,MAAL,GAAcA,MAAd;IACA,KAAKkD,UAAL,GAAkBA,UAAlB;IACA,KAAKC,WAAL,GAAmB,KAAK5C,QAAL,CAAc4C,WAAd,GAA6BC,CAAD,IAAO7C,QAAQ,CAAC4C,WAAT,CAAqBC,CAArB,CAAnC,GAA6D,IAAhF;EACH;;EACO,IAAJnG,IAAI,GAAG;IACP,OAAO,KAAKsD,QAAL,CAActD,IAArB;EACH;;EACD0C,OAAO,GAAG;IACN,KAAKK,MAAL,CAAYL,OAAZ,CAAoB,KAAKkC,WAAzB,EAAsC,KAAKtB,QAA3C;IACA,KAAKA,QAAL,CAAcZ,OAAd;IACA,KAAKuD,UAAL;EACH;;EACDG,aAAa,CAACnB,IAAD,EAAOoB,SAAP,EAAkB;IAC3B,OAAO,KAAK/C,QAAL,CAAc8C,aAAd,CAA4BnB,IAA5B,EAAkCoB,SAAlC,CAAP;EACH;;EACDC,aAAa,CAACC,KAAD,EAAQ;IACjB,OAAO,KAAKjD,QAAL,CAAcgD,aAAd,CAA4BC,KAA5B,CAAP;EACH;;EACDC,UAAU,CAACD,KAAD,EAAQ;IACd,OAAO,KAAKjD,QAAL,CAAckD,UAAd,CAAyBD,KAAzB,CAAP;EACH;;EACDE,WAAW,CAACC,MAAD,EAASC,QAAT,EAAmB;IAC1B,KAAKrD,QAAL,CAAcmD,WAAd,CAA0BC,MAA1B,EAAkCC,QAAlC;IACA,KAAK5D,MAAL,CAAY6D,QAAZ,CAAqB,KAAKhC,WAA1B,EAAuC+B,QAAvC,EAAiDD,MAAjD,EAAyD,KAAzD;EACH;;EACDG,YAAY,CAACH,MAAD,EAASC,QAAT,EAAmBG,QAAnB,EAA6BC,MAAM,GAAG,IAAtC,EAA4C;IACpD,KAAKzD,QAAL,CAAcuD,YAAd,CAA2BH,MAA3B,EAAmCC,QAAnC,EAA6CG,QAA7C,EADoD,CAEpD;;IACA,KAAK/D,MAAL,CAAY6D,QAAZ,CAAqB,KAAKhC,WAA1B,EAAuC+B,QAAvC,EAAiDD,MAAjD,EAAyDK,MAAzD;EACH;;EACD5C,WAAW,CAACuC,MAAD,EAASM,QAAT,EAAmBC,aAAnB,EAAkC;IACzC,KAAKlE,MAAL,CAAYmE,QAAZ,CAAqB,KAAKtC,WAA1B,EAAuCoC,QAAvC,EAAiD,KAAK1D,QAAtD,EAAgE2D,aAAhE;EACH;;EACDE,iBAAiB,CAACC,cAAD,EAAiBC,eAAjB,EAAkC;IAC/C,OAAO,KAAK/D,QAAL,CAAc6D,iBAAd,CAAgCC,cAAhC,EAAgDC,eAAhD,CAAP;EACH;;EACDnD,UAAU,CAACoD,IAAD,EAAO;IACb,OAAO,KAAKhE,QAAL,CAAcY,UAAd,CAAyBoD,IAAzB,CAAP;EACH;;EACDC,WAAW,CAACD,IAAD,EAAO;IACd,OAAO,KAAKhE,QAAL,CAAciE,WAAd,CAA0BD,IAA1B,CAAP;EACH;;EACDE,YAAY,CAACC,EAAD,EAAKxC,IAAL,EAAWsB,KAAX,EAAkBF,SAAlB,EAA6B;IACrC,KAAK/C,QAAL,CAAckE,YAAd,CAA2BC,EAA3B,EAA+BxC,IAA/B,EAAqCsB,KAArC,EAA4CF,SAA5C;EACH;;EACDqB,eAAe,CAACD,EAAD,EAAKxC,IAAL,EAAWoB,SAAX,EAAsB;IACjC,KAAK/C,QAAL,CAAcoE,eAAd,CAA8BD,EAA9B,EAAkCxC,IAAlC,EAAwCoB,SAAxC;EACH;;EACDsB,QAAQ,CAACF,EAAD,EAAKxC,IAAL,EAAW;IACf,KAAK3B,QAAL,CAAcqE,QAAd,CAAuBF,EAAvB,EAA2BxC,IAA3B;EACH;;EACD2C,WAAW,CAACH,EAAD,EAAKxC,IAAL,EAAW;IAClB,KAAK3B,QAAL,CAAcsE,WAAd,CAA0BH,EAA1B,EAA8BxC,IAA9B;EACH;;EACD4C,QAAQ,CAACJ,EAAD,EAAKK,KAAL,EAAYvB,KAAZ,EAAmBwB,KAAnB,EAA0B;IAC9B,KAAKzE,QAAL,CAAcuE,QAAd,CAAuBJ,EAAvB,EAA2BK,KAA3B,EAAkCvB,KAAlC,EAAyCwB,KAAzC;EACH;;EACDC,WAAW,CAACP,EAAD,EAAKK,KAAL,EAAYC,KAAZ,EAAmB;IAC1B,KAAKzE,QAAL,CAAc0E,WAAd,CAA0BP,EAA1B,EAA8BK,KAA9B,EAAqCC,KAArC;EACH;;EACD7E,WAAW,CAACuE,EAAD,EAAKxC,IAAL,EAAWsB,KAAX,EAAkB;IACzB,IAAItB,IAAI,CAACgD,MAAL,CAAY,CAAZ,KAAkB9E,gBAAlB,IAAsC8B,IAAI,IAAI7B,uBAAlD,EAA2E;MACvE,KAAK8E,iBAAL,CAAuBT,EAAvB,EAA2B,CAAC,CAAClB,KAA7B;IACH,CAFD,MAGK;MACD,KAAKjD,QAAL,CAAcJ,WAAd,CAA0BuE,EAA1B,EAA8BxC,IAA9B,EAAoCsB,KAApC;IACH;EACJ;;EACD4B,QAAQ,CAACb,IAAD,EAAOf,KAAP,EAAc;IAClB,KAAKjD,QAAL,CAAc6E,QAAd,CAAuBb,IAAvB,EAA6Bf,KAA7B;EACH;;EACDzE,MAAM,CAACsG,MAAD,EAASxG,SAAT,EAAoBC,QAApB,EAA8B;IAChC,OAAO,KAAKyB,QAAL,CAAcxB,MAAd,CAAqBsG,MAArB,EAA6BxG,SAA7B,EAAwCC,QAAxC,CAAP;EACH;;EACDqG,iBAAiB,CAAC9G,OAAD,EAAUmF,KAAV,EAAiB;IAC9B,KAAKxD,MAAL,CAAYmF,iBAAZ,CAA8B9G,OAA9B,EAAuCmF,KAAvC;EACH;;AAhFuB;;AAkF5B,MAAMpB,iBAAN,SAAgCV,qBAAhC,CAAsD;EAClDlF,WAAW,CAAC8I,OAAD,EAAUzD,WAAV,EAAuBtB,QAAvB,EAAiCP,MAAjC,EAAyCZ,SAAzC,EAAoD;IAC3D,MAAMyC,WAAN,EAAmBtB,QAAnB,EAA6BP,MAA7B,EAAqCZ,SAArC;IACA,KAAKkG,OAAL,GAAeA,OAAf;IACA,KAAKzD,WAAL,GAAmBA,WAAnB;EACH;;EACD1B,WAAW,CAACuE,EAAD,EAAKxC,IAAL,EAAWsB,KAAX,EAAkB;IACzB,IAAItB,IAAI,CAACgD,MAAL,CAAY,CAAZ,KAAkB9E,gBAAtB,EAAwC;MACpC,IAAI8B,IAAI,CAACgD,MAAL,CAAY,CAAZ,KAAkB,GAAlB,IAAyBhD,IAAI,IAAI7B,uBAArC,EAA8D;QAC1DmD,KAAK,GAAGA,KAAK,KAAKxF,SAAV,GAAsB,IAAtB,GAA6B,CAAC,CAACwF,KAAvC;QACA,KAAK2B,iBAAL,CAAuBT,EAAvB,EAA2BlB,KAA3B;MACH,CAHD,MAIK;QACD,KAAKxD,MAAL,CAAYuF,OAAZ,CAAoB,KAAK1D,WAAzB,EAAsC6C,EAAtC,EAA0CxC,IAAI,CAACsD,KAAL,CAAW,CAAX,CAA1C,EAAyDhC,KAAzD;MACH;IACJ,CARD,MASK;MACD,KAAKjD,QAAL,CAAcJ,WAAd,CAA0BuE,EAA1B,EAA8BxC,IAA9B,EAAoCsB,KAApC;IACH;EACJ;;EACDzE,MAAM,CAACsG,MAAD,EAASxG,SAAT,EAAoBC,QAApB,EAA8B;IAChC,IAAID,SAAS,CAACqG,MAAV,CAAiB,CAAjB,KAAuB9E,gBAA3B,EAA6C;MACzC,MAAM/B,OAAO,GAAGoH,wBAAwB,CAACJ,MAAD,CAAxC;MACA,IAAInD,IAAI,GAAGrD,SAAS,CAAC2G,KAAV,CAAgB,CAAhB,CAAX;MACA,IAAIE,KAAK,GAAG,EAAZ,CAHyC,CAIzC;MACA;;MACA,IAAIxD,IAAI,CAACgD,MAAL,CAAY,CAAZ,KAAkB9E,gBAAtB,EAAwC;QACpC,CAAC8B,IAAD,EAAOwD,KAAP,IAAgBC,wBAAwB,CAACzD,IAAD,CAAxC;MACH;;MACD,OAAO,KAAKlC,MAAL,CAAYjB,MAAZ,CAAmB,KAAK8C,WAAxB,EAAqCxD,OAArC,EAA8C6D,IAA9C,EAAoDwD,KAApD,EAA2DE,KAAK,IAAI;QACvE,MAAMC,OAAO,GAAGD,KAAK,CAAC,OAAD,CAAL,IAAkB,CAAC,CAAnC;QACA,KAAKN,OAAL,CAAa9C,wBAAb,CAAsCqD,OAAtC,EAA+C/G,QAA/C,EAAyD8G,KAAzD;MACH,CAHM,CAAP;IAIH;;IACD,OAAO,KAAKrF,QAAL,CAAcxB,MAAd,CAAqBsG,MAArB,EAA6BxG,SAA7B,EAAwCC,QAAxC,CAAP;EACH;;AApCiD;;AAsCtD,SAAS2G,wBAAT,CAAkCJ,MAAlC,EAA0C;EACtC,QAAQA,MAAR;IACI,KAAK,MAAL;MACI,OAAOS,QAAQ,CAACzI,IAAhB;;IACJ,KAAK,UAAL;MACI,OAAOyI,QAAP;;IACJ,KAAK,QAAL;MACI,OAAOC,MAAP;;IACJ;MACI,OAAOV,MAAP;EARR;AAUH;;AACD,SAASM,wBAAT,CAAkCK,WAAlC,EAA+C;EAC3C,MAAMC,QAAQ,GAAGD,WAAW,CAACE,OAAZ,CAAoB,GAApB,CAAjB;EACA,MAAMlE,OAAO,GAAGgE,WAAW,CAACG,SAAZ,CAAsB,CAAtB,EAAyBF,QAAzB,CAAhB;EACA,MAAMP,KAAK,GAAGM,WAAW,CAACR,KAAZ,CAAkBS,QAAQ,GAAG,CAA7B,CAAd;EACA,OAAO,CAACjE,OAAD,EAAU0D,KAAV,CAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMU,yBAAN,SAAwCpK,gBAAxC,CAAyD;EACrD;EACA;EACA;EACAQ,WAAW,CAACE,GAAD,EAAM2J,MAAN,EAAcC,UAAd,EAA0BC,MAA1B,EAAkC;IACzC,MAAM7J,GAAG,CAACW,IAAV,EAAgBgJ,MAAhB,EAAwBC,UAAxB;EACH;;EACDE,WAAW,GAAG;IACV,KAAKxD,KAAL;EACH;;AAToD;;AAWzDoD,yBAAyB,CAACvI,IAA1B;EAAA,iBAAsHuI,yBAAtH,EA7V0GlL,EA6V1G,UAAiKoB,QAAjK,GA7V0GpB,EA6V1G,UAAsLa,EAAE,CAACI,eAAzL,GA7V0GjB,EA6V1G,UAAqNa,EAAE,CAACG,yBAAxN,GA7V0GhB,EA6V1G,UAA8PA,EAAE,CAACuL,cAAjQ;AAAA;;AACAL,yBAAyB,CAACtI,KAA1B,kBA9V0G5C,EA8V1G;EAAA,OAA0HkL,yBAA1H;EAAA,SAA0HA,yBAA1H;AAAA;;AACA;EAAA,mDA/V0GlL,EA+V1G,mBAA2FkL,yBAA3F,EAAkI,CAAC;IACvHrI,IAAI,EAAE3C;EADiH,CAAD,CAAlI,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAE2C,IAAI,EAAEC,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9DF,IAAI,EAAE1C,MADwD;QAE9D6C,IAAI,EAAE,CAAC5B,QAAD;MAFwD,CAAD;IAA/B,CAAD,EAG3B;MAAEyB,IAAI,EAAEhC,EAAE,CAACI;IAAX,CAH2B,EAGG;MAAE4B,IAAI,EAAEhC,EAAE,CAACG;IAAX,CAHH,EAG2C;MAAE6B,IAAI,EAAE7C,EAAE,CAACuL;IAAX,CAH3C,CAAP;EAGiF,CAL3H;AAAA;;AAMA,SAASC,iCAAT,GAA6C;EACzC,OAAO,IAAIzK,6BAAJ,EAAP;AACH;;AACD,SAAS0K,0BAAT,CAAoCzG,QAApC,EAA8CF,MAA9C,EAAsD4G,IAAtD,EAA4D;EACxD,OAAO,IAAItG,wBAAJ,CAA6BJ,QAA7B,EAAuCF,MAAvC,EAA+C4G,IAA/C,CAAP;AACH;;AACD,MAAMC,0BAA0B,GAAG,CAC/B;EAAEC,OAAO,EAAElL,gBAAX;EAA6BmL,QAAQ,EAAExK;AAAvC,CAD+B,EAE/B;EAAEuK,OAAO,EAAE5K,yBAAX;EAAsC8K,UAAU,EAAEN;AAAlD,CAF+B,EAG/B;EAAEI,OAAO,EAAE9K,gBAAX;EAA6B+K,QAAQ,EAAEX;AAAvC,CAH+B,EAGqC;EAChEU,OAAO,EAAExL,gBADuD;EAEhE0L,UAAU,EAAEL,0BAFoD;EAGhEM,IAAI,EAAE,CAACvL,oBAAD,EAAuBM,gBAAvB,EAAyCT,MAAzC;AAH0D,CAHrC,CAAnC;AASA;AACA;AACA;AACA;;AACA,MAAM2L,4BAA4B,GAAG,CACjC;EAAEJ,OAAO,EAAE3K,eAAX;EAA4B6K,UAAU,EAAE,MAAM,IAAI5K,oBAAJ;AAA9C,CADiC,EAEjC;EAAE0K,OAAO,EAAEtL,qBAAX;EAAkC2L,QAAQ,EAAE;AAA5C,CAFiC,EAEkC,GAAGN,0BAFrC,CAArC;AAIA;AACA;AACA;AACA;;AACA,MAAMO,iCAAiC,GAAG,CACtC;EAAEN,OAAO,EAAE3K,eAAX;EAA4B4K,QAAQ,EAAE1K;AAAtC,CADsC,EAEtC;EAAEyK,OAAO,EAAEtL,qBAAX;EAAkC2L,QAAQ,EAAE;AAA5C,CAFsC,EAE0B,GAAGN,0BAF7B,CAA1C;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMQ,uBAAN,CAA8B;EAC1B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACqB,OAAVC,UAAU,CAACC,MAAD,EAAS;IACtB,OAAO;MACHC,QAAQ,EAAEH,uBADP;MAEHI,SAAS,EAAEF,MAAM,CAACpC,iBAAP,GAA2BiC,iCAA3B,GACPF;IAHD,CAAP;EAKH;;AAvByB;;AAyB9BG,uBAAuB,CAACxJ,IAAxB;EAAA,iBAAoHwJ,uBAApH;AAAA;;AACAA,uBAAuB,CAACK,IAAxB,kBA3a0GxM,EA2a1G;EAAA,MAAqHmM,uBAArH;EAAA,UAAwJ1L,aAAxJ;AAAA;AACA0L,uBAAuB,CAACM,IAAxB,kBA5a0GzM,EA4a1G;EAAA,WAAyJgM,4BAAzJ;EAAA,UAAiMvL,aAAjM;AAAA;;AACA;EAAA,mDA7a0GT,EA6a1G,mBAA2FmM,uBAA3F,EAAgI,CAAC;IACrHtJ,IAAI,EAAEtC,QAD+G;IAErHyC,IAAI,EAAE,CAAC;MACC0J,OAAO,EAAE,CAACjM,aAAD,CADV;MAEC8L,SAAS,EAAEP;IAFZ,CAAD;EAF+G,CAAD,CAAhI;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASW,iBAAT,GAA6B;EACzB;EACA;EACA,OAAO,CAAC,GAAGX,4BAAJ,CAAP;AACH;AACD;AACA;AACA;AACA;;;AACA,MAAMY,oBAAN,CAA2B;;AAE3BA,oBAAoB,CAACjK,IAArB;EAAA,iBAAiHiK,oBAAjH;AAAA;;AACAA,oBAAoB,CAACJ,IAArB,kBAvd0GxM,EAud1G;EAAA,MAAkH4M,oBAAlH;EAAA,UAAkJnM,aAAlJ;AAAA;AACAmM,oBAAoB,CAACH,IAArB,kBAxd0GzM,EAwd1G;EAAA,WAAmJkM,iCAAnJ;EAAA,UAAgMzL,aAAhM;AAAA;;AACA;EAAA,mDAzd0GT,EAyd1G,mBAA2F4M,oBAA3F,EAA6H,CAAC;IAClH/J,IAAI,EAAEtC,QAD4G;IAElHyC,IAAI,EAAE,CAAC;MACC0J,OAAO,EAAE,CAACjM,aAAD,CADV;MAEC8L,SAAS,EAAEL;IAFZ,CAAD;EAF4G,CAAD,CAA7H;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASW,qBAAT,GAAiC;EAC7B;EACA;EACA,OAAO,CAAC,GAAGX,iCAAJ,CAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASC,uBAAT,EAAkCS,oBAAlC,EAAwDD,iBAAxD,EAA2EE,qBAA3E,EAAkG3F,iBAAiB,IAAI4F,kBAAvH,EAA2I1H,wBAAwB,IAAI2H,yBAAvK,EAAkM1L,uBAAuB,IAAI2L,wBAA7N,EAAuPtK,uBAAuB,IAAIuK,wBAAlR,EAA4S/B,yBAAyB,IAAIgC,0BAAzU"}, "metadata": {}, "sourceType": "module"}