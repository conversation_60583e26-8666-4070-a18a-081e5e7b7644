{"ast": null, "code": "const EmptyErrorImpl = (() => {\n  function EmptyErrorImpl() {\n    Error.call(this);\n    this.message = 'no elements in sequence';\n    this.name = 'EmptyError';\n    return this;\n  }\n\n  EmptyErrorImpl.prototype = Object.create(Error.prototype);\n  return EmptyErrorImpl;\n})();\n\nexport const EmptyError = EmptyErrorImpl;", "map": {"version": 3, "names": ["EmptyErrorImpl", "Error", "call", "message", "name", "prototype", "Object", "create", "EmptyError"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/EmptyError.js"], "sourcesContent": ["const EmptyErrorImpl = (() => {\n    function EmptyErrorImpl() {\n        Error.call(this);\n        this.message = 'no elements in sequence';\n        this.name = 'EmptyError';\n        return this;\n    }\n    EmptyErrorImpl.prototype = Object.create(Error.prototype);\n    return EmptyErrorImpl;\n})();\nexport const EmptyError = EmptyErrorImpl;\n"], "mappings": "AAAA,MAAMA,cAAc,GAAG,CAAC,MAAM;EAC1B,SAASA,cAAT,GAA0B;IACtBC,KAAK,CAACC,IAAN,CAAW,IAAX;IACA,KAAKC,OAAL,GAAe,yBAAf;IACA,KAAKC,IAAL,GAAY,YAAZ;IACA,OAAO,IAAP;EACH;;EACDJ,cAAc,CAACK,SAAf,GAA2BC,MAAM,CAACC,MAAP,CAAcN,KAAK,CAACI,SAApB,CAA3B;EACA,OAAOL,cAAP;AACH,CATsB,GAAvB;;AAUA,OAAO,MAAMQ,UAAU,GAAGR,cAAnB"}, "metadata": {}, "sourceType": "module"}