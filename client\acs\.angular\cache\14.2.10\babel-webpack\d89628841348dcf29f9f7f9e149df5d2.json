{"ast": null, "code": "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "map": {"version": 3, "names": ["getBasePlacement", "getVariation", "getMainAxisFromPlacement", "top", "right", "bottom", "left", "start", "end", "computeOffsets", "_ref", "reference", "element", "placement", "basePlacement", "variation", "commonX", "x", "width", "commonY", "y", "height", "offsets", "mainAxis", "len"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/utils/computeOffsets.js"], "sourcesContent": ["import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}"], "mappings": "AAAA,OAAOA,gBAAP,MAA6B,uBAA7B;AACA,OAAOC,YAAP,MAAyB,mBAAzB;AACA,OAAOC,wBAAP,MAAqC,+BAArC;AACA,SAASC,GAAT,EAAcC,KAAd,EAAqBC,MAArB,EAA6BC,IAA7B,EAAmCC,KAAnC,EAA0CC,GAA1C,QAAqD,aAArD;AACA,eAAe,SAASC,cAAT,CAAwBC,IAAxB,EAA8B;EAC3C,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAArB;EAAA,IACIC,OAAO,GAAGF,IAAI,CAACE,OADnB;EAAA,IAEIC,SAAS,GAAGH,IAAI,CAACG,SAFrB;EAGA,IAAIC,aAAa,GAAGD,SAAS,GAAGb,gBAAgB,CAACa,SAAD,CAAnB,GAAiC,IAA9D;EACA,IAAIE,SAAS,GAAGF,SAAS,GAAGZ,YAAY,CAACY,SAAD,CAAf,GAA6B,IAAtD;EACA,IAAIG,OAAO,GAAGL,SAAS,CAACM,CAAV,GAAcN,SAAS,CAACO,KAAV,GAAkB,CAAhC,GAAoCN,OAAO,CAACM,KAAR,GAAgB,CAAlE;EACA,IAAIC,OAAO,GAAGR,SAAS,CAACS,CAAV,GAAcT,SAAS,CAACU,MAAV,GAAmB,CAAjC,GAAqCT,OAAO,CAACS,MAAR,GAAiB,CAApE;EACA,IAAIC,OAAJ;;EAEA,QAAQR,aAAR;IACE,KAAKX,GAAL;MACEmB,OAAO,GAAG;QACRL,CAAC,EAAED,OADK;QAERI,CAAC,EAAET,SAAS,CAACS,CAAV,GAAcR,OAAO,CAACS;MAFjB,CAAV;MAIA;;IAEF,KAAKhB,MAAL;MACEiB,OAAO,GAAG;QACRL,CAAC,EAAED,OADK;QAERI,CAAC,EAAET,SAAS,CAACS,CAAV,GAAcT,SAAS,CAACU;MAFnB,CAAV;MAIA;;IAEF,KAAKjB,KAAL;MACEkB,OAAO,GAAG;QACRL,CAAC,EAAEN,SAAS,CAACM,CAAV,GAAcN,SAAS,CAACO,KADnB;QAERE,CAAC,EAAED;MAFK,CAAV;MAIA;;IAEF,KAAKb,IAAL;MACEgB,OAAO,GAAG;QACRL,CAAC,EAAEN,SAAS,CAACM,CAAV,GAAcL,OAAO,CAACM,KADjB;QAERE,CAAC,EAAED;MAFK,CAAV;MAIA;;IAEF;MACEG,OAAO,GAAG;QACRL,CAAC,EAAEN,SAAS,CAACM,CADL;QAERG,CAAC,EAAET,SAAS,CAACS;MAFL,CAAV;EA9BJ;;EAoCA,IAAIG,QAAQ,GAAGT,aAAa,GAAGZ,wBAAwB,CAACY,aAAD,CAA3B,GAA6C,IAAzE;;EAEA,IAAIS,QAAQ,IAAI,IAAhB,EAAsB;IACpB,IAAIC,GAAG,GAAGD,QAAQ,KAAK,GAAb,GAAmB,QAAnB,GAA8B,OAAxC;;IAEA,QAAQR,SAAR;MACE,KAAKR,KAAL;QACEe,OAAO,CAACC,QAAD,CAAP,GAAoBD,OAAO,CAACC,QAAD,CAAP,IAAqBZ,SAAS,CAACa,GAAD,CAAT,GAAiB,CAAjB,GAAqBZ,OAAO,CAACY,GAAD,CAAP,GAAe,CAAzD,CAApB;QACA;;MAEF,KAAKhB,GAAL;QACEc,OAAO,CAACC,QAAD,CAAP,GAAoBD,OAAO,CAACC,QAAD,CAAP,IAAqBZ,SAAS,CAACa,GAAD,CAAT,GAAiB,CAAjB,GAAqBZ,OAAO,CAACY,GAAD,CAAP,GAAe,CAAzD,CAApB;QACA;;MAEF;IATF;EAWD;;EAED,OAAOF,OAAP;AACD"}, "metadata": {}, "sourceType": "module"}