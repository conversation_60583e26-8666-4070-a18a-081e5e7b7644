{"ast": null, "code": "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "map": {"version": 3, "names": ["isElement", "getDocumentElement", "element", "ownerDocument", "document", "window", "documentElement"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js"], "sourcesContent": ["import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}"], "mappings": "AAAA,SAASA,SAAT,QAA0B,iBAA1B;AACA,eAAe,SAASC,kBAAT,CAA4BC,OAA5B,EAAqC;EAClD;EACA,OAAO,CAAC,CAACF,SAAS,CAACE,OAAD,CAAT,GAAqBA,OAAO,CAACC,aAA7B,GAA6C;EACtDD,OAAO,CAACE,QADA,KACaC,MAAM,CAACD,QADrB,EAC+BE,eADtC;AAED"}, "metadata": {}, "sourceType": "module"}