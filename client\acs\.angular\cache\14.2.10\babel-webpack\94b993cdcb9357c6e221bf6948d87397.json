{"ast": null, "code": "import { zip as zipStatic } from '../observable/zip';\nexport function zip(...observables) {\n  return function zipOperatorFunction(source) {\n    return source.lift.call(zipStatic(source, ...observables));\n  };\n}", "map": {"version": 3, "names": ["zip", "zipStatic", "observables", "zipOperatorFunction", "source", "lift", "call"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/zip.js"], "sourcesContent": ["import { zip as zipStatic } from '../observable/zip';\nexport function zip(...observables) {\n    return function zipOperatorFunction(source) {\n        return source.lift.call(zipStatic(source, ...observables));\n    };\n}\n"], "mappings": "AAAA,SAASA,GAAG,IAAIC,SAAhB,QAAiC,mBAAjC;AACA,OAAO,SAASD,GAAT,CAAa,GAAGE,WAAhB,EAA6B;EAChC,OAAO,SAASC,mBAAT,CAA6BC,MAA7B,EAAqC;IACxC,OAAOA,MAAM,CAACC,IAAP,CAAYC,IAAZ,CAAiBL,SAAS,CAACG,MAAD,EAAS,GAAGF,WAAZ,CAA1B,CAAP;EACH,CAFD;AAGH"}, "metadata": {}, "sourceType": "module"}