{"ast": null, "code": "import { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nimport { observable as Symbol_observable } from '../symbol/observable';\nexport function scheduleObservable(input, scheduler) {\n  return new Observable(subscriber => {\n    const sub = new Subscription();\n    sub.add(scheduler.schedule(() => {\n      const observable = input[Symbol_observable]();\n      sub.add(observable.subscribe({\n        next(value) {\n          sub.add(scheduler.schedule(() => subscriber.next(value)));\n        },\n\n        error(err) {\n          sub.add(scheduler.schedule(() => subscriber.error(err)));\n        },\n\n        complete() {\n          sub.add(scheduler.schedule(() => subscriber.complete()));\n        }\n\n      }));\n    }));\n    return sub;\n  });\n}", "map": {"version": 3, "names": ["Observable", "Subscription", "observable", "Symbol_observable", "scheduleObservable", "input", "scheduler", "subscriber", "sub", "add", "schedule", "subscribe", "next", "value", "error", "err", "complete"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/scheduled/scheduleObservable.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nimport { observable as Symbol_observable } from '../symbol/observable';\nexport function scheduleObservable(input, scheduler) {\n    return new Observable(subscriber => {\n        const sub = new Subscription();\n        sub.add(scheduler.schedule(() => {\n            const observable = input[Symbol_observable]();\n            sub.add(observable.subscribe({\n                next(value) { sub.add(scheduler.schedule(() => subscriber.next(value))); },\n                error(err) { sub.add(scheduler.schedule(() => subscriber.error(err))); },\n                complete() { sub.add(scheduler.schedule(() => subscriber.complete())); },\n            }));\n        }));\n        return sub;\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,UAAU,IAAIC,iBAAvB,QAAgD,sBAAhD;AACA,OAAO,SAASC,kBAAT,CAA4BC,KAA5B,EAAmCC,SAAnC,EAA8C;EACjD,OAAO,IAAIN,UAAJ,CAAeO,UAAU,IAAI;IAChC,MAAMC,GAAG,GAAG,IAAIP,YAAJ,EAAZ;IACAO,GAAG,CAACC,GAAJ,CAAQH,SAAS,CAACI,QAAV,CAAmB,MAAM;MAC7B,MAAMR,UAAU,GAAGG,KAAK,CAACF,iBAAD,CAAL,EAAnB;MACAK,GAAG,CAACC,GAAJ,CAAQP,UAAU,CAACS,SAAX,CAAqB;QACzBC,IAAI,CAACC,KAAD,EAAQ;UAAEL,GAAG,CAACC,GAAJ,CAAQH,SAAS,CAACI,QAAV,CAAmB,MAAMH,UAAU,CAACK,IAAX,CAAgBC,KAAhB,CAAzB,CAAR;QAA4D,CADjD;;QAEzBC,KAAK,CAACC,GAAD,EAAM;UAAEP,GAAG,CAACC,GAAJ,CAAQH,SAAS,CAACI,QAAV,CAAmB,MAAMH,UAAU,CAACO,KAAX,CAAiBC,GAAjB,CAAzB,CAAR;QAA2D,CAF/C;;QAGzBC,QAAQ,GAAG;UAAER,GAAG,CAACC,GAAJ,CAAQH,SAAS,CAACI,QAAV,CAAmB,MAAMH,UAAU,CAACS,QAAX,EAAzB,CAAR;QAA2D;;MAH/C,CAArB,CAAR;IAKH,CAPO,CAAR;IAQA,OAAOR,GAAP;EACH,CAXM,CAAP;AAYH"}, "metadata": {}, "sourceType": "module"}