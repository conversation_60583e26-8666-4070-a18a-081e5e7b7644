{"ast": null, "code": "import { canReportError } from './util/canReportError';\nimport { toSubscriber } from './util/toSubscriber';\nimport { observable as Symbol_observable } from './symbol/observable';\nimport { pipeFromArray } from './util/pipe';\nimport { config } from './config';\nexport class Observable {\n  constructor(subscribe) {\n    this._isScalar = false;\n\n    if (subscribe) {\n      this._subscribe = subscribe;\n    }\n  }\n\n  lift(operator) {\n    const observable = new Observable();\n    observable.source = this;\n    observable.operator = operator;\n    return observable;\n  }\n\n  subscribe(observerOrNext, error, complete) {\n    const {\n      operator\n    } = this;\n    const sink = toSubscriber(observerOrNext, error, complete);\n\n    if (operator) {\n      sink.add(operator.call(sink, this.source));\n    } else {\n      sink.add(this.source || config.useDeprecatedSynchronousErrorHandling && !sink.syncErrorThrowable ? this._subscribe(sink) : this._trySubscribe(sink));\n    }\n\n    if (config.useDeprecatedSynchronousErrorHandling) {\n      if (sink.syncErrorThrowable) {\n        sink.syncErrorThrowable = false;\n\n        if (sink.syncErrorThrown) {\n          throw sink.syncErrorValue;\n        }\n      }\n    }\n\n    return sink;\n  }\n\n  _trySubscribe(sink) {\n    try {\n      return this._subscribe(sink);\n    } catch (err) {\n      if (config.useDeprecatedSynchronousErrorHandling) {\n        sink.syncErrorThrown = true;\n        sink.syncErrorValue = err;\n      }\n\n      if (canReportError(sink)) {\n        sink.error(err);\n      } else {\n        console.warn(err);\n      }\n    }\n  }\n\n  forEach(next, promiseCtor) {\n    promiseCtor = getPromiseCtor(promiseCtor);\n    return new promiseCtor((resolve, reject) => {\n      let subscription;\n      subscription = this.subscribe(value => {\n        try {\n          next(value);\n        } catch (err) {\n          reject(err);\n\n          if (subscription) {\n            subscription.unsubscribe();\n          }\n        }\n      }, reject, resolve);\n    });\n  }\n\n  _subscribe(subscriber) {\n    const {\n      source\n    } = this;\n    return source && source.subscribe(subscriber);\n  }\n\n  [Symbol_observable]() {\n    return this;\n  }\n\n  pipe(...operations) {\n    if (operations.length === 0) {\n      return this;\n    }\n\n    return pipeFromArray(operations)(this);\n  }\n\n  toPromise(promiseCtor) {\n    promiseCtor = getPromiseCtor(promiseCtor);\n    return new promiseCtor((resolve, reject) => {\n      let value;\n      this.subscribe(x => value = x, err => reject(err), () => resolve(value));\n    });\n  }\n\n}\n\nObservable.create = subscribe => {\n  return new Observable(subscribe);\n};\n\nfunction getPromiseCtor(promiseCtor) {\n  if (!promiseCtor) {\n    promiseCtor = config.Promise || Promise;\n  }\n\n  if (!promiseCtor) {\n    throw new Error('no Promise impl found');\n  }\n\n  return promiseCtor;\n}", "map": {"version": 3, "names": ["canReportError", "toSubscriber", "observable", "Symbol_observable", "pipeFromArray", "config", "Observable", "constructor", "subscribe", "_isScalar", "_subscribe", "lift", "operator", "source", "observerOrNext", "error", "complete", "sink", "add", "call", "useDeprecatedSynchronousErrorHandling", "syncErrorThrowable", "_trySubscribe", "syncErrorThrown", "syncErrorValue", "err", "console", "warn", "for<PERSON>ach", "next", "promiseCtor", "getPromiseCtor", "resolve", "reject", "subscription", "value", "unsubscribe", "subscriber", "pipe", "operations", "length", "to<PERSON>romise", "x", "create", "Promise", "Error"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/Observable.js"], "sourcesContent": ["import { canReportError } from './util/canReportError';\nimport { toSubscriber } from './util/toSubscriber';\nimport { observable as Symbol_observable } from './symbol/observable';\nimport { pipeFromArray } from './util/pipe';\nimport { config } from './config';\nexport class Observable {\n    constructor(subscribe) {\n        this._isScalar = false;\n        if (subscribe) {\n            this._subscribe = subscribe;\n        }\n    }\n    lift(operator) {\n        const observable = new Observable();\n        observable.source = this;\n        observable.operator = operator;\n        return observable;\n    }\n    subscribe(observerOrNext, error, complete) {\n        const { operator } = this;\n        const sink = toSubscriber(observerOrNext, error, complete);\n        if (operator) {\n            sink.add(operator.call(sink, this.source));\n        }\n        else {\n            sink.add(this.source || (config.useDeprecatedSynchronousErrorHandling && !sink.syncErrorThrowable) ?\n                this._subscribe(sink) :\n                this._trySubscribe(sink));\n        }\n        if (config.useDeprecatedSynchronousErrorHandling) {\n            if (sink.syncErrorThrowable) {\n                sink.syncErrorThrowable = false;\n                if (sink.syncErrorThrown) {\n                    throw sink.syncErrorValue;\n                }\n            }\n        }\n        return sink;\n    }\n    _trySubscribe(sink) {\n        try {\n            return this._subscribe(sink);\n        }\n        catch (err) {\n            if (config.useDeprecatedSynchronousErrorHandling) {\n                sink.syncErrorThrown = true;\n                sink.syncErrorValue = err;\n            }\n            if (canReportError(sink)) {\n                sink.error(err);\n            }\n            else {\n                console.warn(err);\n            }\n        }\n    }\n    forEach(next, promiseCtor) {\n        promiseCtor = getPromiseCtor(promiseCtor);\n        return new promiseCtor((resolve, reject) => {\n            let subscription;\n            subscription = this.subscribe((value) => {\n                try {\n                    next(value);\n                }\n                catch (err) {\n                    reject(err);\n                    if (subscription) {\n                        subscription.unsubscribe();\n                    }\n                }\n            }, reject, resolve);\n        });\n    }\n    _subscribe(subscriber) {\n        const { source } = this;\n        return source && source.subscribe(subscriber);\n    }\n    [Symbol_observable]() {\n        return this;\n    }\n    pipe(...operations) {\n        if (operations.length === 0) {\n            return this;\n        }\n        return pipeFromArray(operations)(this);\n    }\n    toPromise(promiseCtor) {\n        promiseCtor = getPromiseCtor(promiseCtor);\n        return new promiseCtor((resolve, reject) => {\n            let value;\n            this.subscribe((x) => value = x, (err) => reject(err), () => resolve(value));\n        });\n    }\n}\nObservable.create = (subscribe) => {\n    return new Observable(subscribe);\n};\nfunction getPromiseCtor(promiseCtor) {\n    if (!promiseCtor) {\n        promiseCtor = config.Promise || Promise;\n    }\n    if (!promiseCtor) {\n        throw new Error('no Promise impl found');\n    }\n    return promiseCtor;\n}\n"], "mappings": "AAAA,SAASA,cAAT,QAA+B,uBAA/B;AACA,SAASC,YAAT,QAA6B,qBAA7B;AACA,SAASC,UAAU,IAAIC,iBAAvB,QAAgD,qBAAhD;AACA,SAASC,aAAT,QAA8B,aAA9B;AACA,SAASC,MAAT,QAAuB,UAAvB;AACA,OAAO,MAAMC,UAAN,CAAiB;EACpBC,WAAW,CAACC,SAAD,EAAY;IACnB,KAAKC,SAAL,GAAiB,KAAjB;;IACA,IAAID,SAAJ,EAAe;MACX,KAAKE,UAAL,GAAkBF,SAAlB;IACH;EACJ;;EACDG,IAAI,CAACC,QAAD,EAAW;IACX,MAAMV,UAAU,GAAG,IAAII,UAAJ,EAAnB;IACAJ,UAAU,CAACW,MAAX,GAAoB,IAApB;IACAX,UAAU,CAACU,QAAX,GAAsBA,QAAtB;IACA,OAAOV,UAAP;EACH;;EACDM,SAAS,CAACM,cAAD,EAAiBC,KAAjB,EAAwBC,QAAxB,EAAkC;IACvC,MAAM;MAAEJ;IAAF,IAAe,IAArB;IACA,MAAMK,IAAI,GAAGhB,YAAY,CAACa,cAAD,EAAiBC,KAAjB,EAAwBC,QAAxB,CAAzB;;IACA,IAAIJ,QAAJ,EAAc;MACVK,IAAI,CAACC,GAAL,CAASN,QAAQ,CAACO,IAAT,CAAcF,IAAd,EAAoB,KAAKJ,MAAzB,CAAT;IACH,CAFD,MAGK;MACDI,IAAI,CAACC,GAAL,CAAS,KAAKL,MAAL,IAAgBR,MAAM,CAACe,qCAAP,IAAgD,CAACH,IAAI,CAACI,kBAAtE,GACL,KAAKX,UAAL,CAAgBO,IAAhB,CADK,GAEL,KAAKK,aAAL,CAAmBL,IAAnB,CAFJ;IAGH;;IACD,IAAIZ,MAAM,CAACe,qCAAX,EAAkD;MAC9C,IAAIH,IAAI,CAACI,kBAAT,EAA6B;QACzBJ,IAAI,CAACI,kBAAL,GAA0B,KAA1B;;QACA,IAAIJ,IAAI,CAACM,eAAT,EAA0B;UACtB,MAAMN,IAAI,CAACO,cAAX;QACH;MACJ;IACJ;;IACD,OAAOP,IAAP;EACH;;EACDK,aAAa,CAACL,IAAD,EAAO;IAChB,IAAI;MACA,OAAO,KAAKP,UAAL,CAAgBO,IAAhB,CAAP;IACH,CAFD,CAGA,OAAOQ,GAAP,EAAY;MACR,IAAIpB,MAAM,CAACe,qCAAX,EAAkD;QAC9CH,IAAI,CAACM,eAAL,GAAuB,IAAvB;QACAN,IAAI,CAACO,cAAL,GAAsBC,GAAtB;MACH;;MACD,IAAIzB,cAAc,CAACiB,IAAD,CAAlB,EAA0B;QACtBA,IAAI,CAACF,KAAL,CAAWU,GAAX;MACH,CAFD,MAGK;QACDC,OAAO,CAACC,IAAR,CAAaF,GAAb;MACH;IACJ;EACJ;;EACDG,OAAO,CAACC,IAAD,EAAOC,WAAP,EAAoB;IACvBA,WAAW,GAAGC,cAAc,CAACD,WAAD,CAA5B;IACA,OAAO,IAAIA,WAAJ,CAAgB,CAACE,OAAD,EAAUC,MAAV,KAAqB;MACxC,IAAIC,YAAJ;MACAA,YAAY,GAAG,KAAK1B,SAAL,CAAgB2B,KAAD,IAAW;QACrC,IAAI;UACAN,IAAI,CAACM,KAAD,CAAJ;QACH,CAFD,CAGA,OAAOV,GAAP,EAAY;UACRQ,MAAM,CAACR,GAAD,CAAN;;UACA,IAAIS,YAAJ,EAAkB;YACdA,YAAY,CAACE,WAAb;UACH;QACJ;MACJ,CAVc,EAUZH,MAVY,EAUJD,OAVI,CAAf;IAWH,CAbM,CAAP;EAcH;;EACDtB,UAAU,CAAC2B,UAAD,EAAa;IACnB,MAAM;MAAExB;IAAF,IAAa,IAAnB;IACA,OAAOA,MAAM,IAAIA,MAAM,CAACL,SAAP,CAAiB6B,UAAjB,CAAjB;EACH;;EACiB,CAAjBlC,iBAAiB,IAAI;IAClB,OAAO,IAAP;EACH;;EACDmC,IAAI,CAAC,GAAGC,UAAJ,EAAgB;IAChB,IAAIA,UAAU,CAACC,MAAX,KAAsB,CAA1B,EAA6B;MACzB,OAAO,IAAP;IACH;;IACD,OAAOpC,aAAa,CAACmC,UAAD,CAAb,CAA0B,IAA1B,CAAP;EACH;;EACDE,SAAS,CAACX,WAAD,EAAc;IACnBA,WAAW,GAAGC,cAAc,CAACD,WAAD,CAA5B;IACA,OAAO,IAAIA,WAAJ,CAAgB,CAACE,OAAD,EAAUC,MAAV,KAAqB;MACxC,IAAIE,KAAJ;MACA,KAAK3B,SAAL,CAAgBkC,CAAD,IAAOP,KAAK,GAAGO,CAA9B,EAAkCjB,GAAD,IAASQ,MAAM,CAACR,GAAD,CAAhD,EAAuD,MAAMO,OAAO,CAACG,KAAD,CAApE;IACH,CAHM,CAAP;EAIH;;AAvFmB;;AAyFxB7B,UAAU,CAACqC,MAAX,GAAqBnC,SAAD,IAAe;EAC/B,OAAO,IAAIF,UAAJ,CAAeE,SAAf,CAAP;AACH,CAFD;;AAGA,SAASuB,cAAT,CAAwBD,WAAxB,EAAqC;EACjC,IAAI,CAACA,WAAL,EAAkB;IACdA,WAAW,GAAGzB,MAAM,CAACuC,OAAP,IAAkBA,OAAhC;EACH;;EACD,IAAI,CAACd,WAAL,EAAkB;IACd,MAAM,IAAIe,KAAJ,CAAU,uBAAV,CAAN;EACH;;EACD,OAAOf,WAAP;AACH"}, "metadata": {}, "sourceType": "module"}