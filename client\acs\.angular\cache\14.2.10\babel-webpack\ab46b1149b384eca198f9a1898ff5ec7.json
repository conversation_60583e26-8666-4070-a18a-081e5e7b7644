{"ast": null, "code": "import { Observable } from '../Observable';\nimport { subscribeToArray } from '../util/subscribeToArray';\nimport { scheduleArray } from '../scheduled/scheduleArray';\nexport function fromArray(input, scheduler) {\n  if (!scheduler) {\n    return new Observable(subscribeToArray(input));\n  } else {\n    return scheduleArray(input, scheduler);\n  }\n}", "map": {"version": 3, "names": ["Observable", "subscribeToArray", "scheduleArray", "fromArray", "input", "scheduler"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/fromArray.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { subscribeToArray } from '../util/subscribeToArray';\nimport { scheduleArray } from '../scheduled/scheduleArray';\nexport function fromArray(input, scheduler) {\n    if (!scheduler) {\n        return new Observable(subscribeToArray(input));\n    }\n    else {\n        return scheduleArray(input, scheduler);\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,gBAAT,QAAiC,0BAAjC;AACA,SAASC,aAAT,QAA8B,4BAA9B;AACA,OAAO,SAASC,SAAT,CAAmBC,KAAnB,EAA0BC,SAA1B,EAAqC;EACxC,IAAI,CAACA,SAAL,EAAgB;IACZ,OAAO,IAAIL,UAAJ,CAAeC,gBAAgB,CAACG,KAAD,CAA/B,CAAP;EACH,CAFD,MAGK;IACD,OAAOF,aAAa,CAACE,KAAD,EAAQC,SAAR,CAApB;EACH;AACJ"}, "metadata": {}, "sourceType": "module"}