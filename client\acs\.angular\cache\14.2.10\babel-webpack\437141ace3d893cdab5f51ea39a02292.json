{"ast": null, "code": "import { of } from './of';\nimport { concatAll } from '../operators/concatAll';\nexport function concat(...observables) {\n  return concatAll()(of(...observables));\n}", "map": {"version": 3, "names": ["of", "concatAll", "concat", "observables"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/concat.js"], "sourcesContent": ["import { of } from './of';\nimport { concatAll } from '../operators/concatAll';\nexport function concat(...observables) {\n    return concatAll()(of(...observables));\n}\n"], "mappings": "AAAA,SAASA,EAAT,QAAmB,MAAnB;AACA,SAASC,SAAT,QAA0B,wBAA1B;AACA,OAAO,SAASC,MAAT,CAAgB,GAAGC,WAAnB,EAAgC;EACnC,OAAOF,SAAS,GAAGD,EAAE,CAAC,GAAGG,WAAJ,CAAL,CAAhB;AACH"}, "metadata": {}, "sourceType": "module"}