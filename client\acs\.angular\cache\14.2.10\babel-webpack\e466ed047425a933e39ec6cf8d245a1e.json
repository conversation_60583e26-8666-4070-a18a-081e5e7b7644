{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { Subscriber } from '../Subscriber';\nimport { isScheduler } from '../util/isScheduler';\nexport function bufferTime(bufferTimeSpan) {\n  let length = arguments.length;\n  let scheduler = async;\n\n  if (isScheduler(arguments[arguments.length - 1])) {\n    scheduler = arguments[arguments.length - 1];\n    length--;\n  }\n\n  let bufferCreationInterval = null;\n\n  if (length >= 2) {\n    bufferCreationInterval = arguments[1];\n  }\n\n  let maxBufferSize = Number.POSITIVE_INFINITY;\n\n  if (length >= 3) {\n    maxBufferSize = arguments[2];\n  }\n\n  return function bufferTimeOperatorFunction(source) {\n    return source.lift(new BufferTimeOperator(bufferTimeSpan, bufferCreationInterval, maxBufferSize, scheduler));\n  };\n}\n\nclass BufferTimeOperator {\n  constructor(bufferTimeSpan, bufferCreationInterval, maxBufferSize, scheduler) {\n    this.bufferTimeSpan = bufferTimeSpan;\n    this.bufferCreationInterval = bufferCreationInterval;\n    this.maxBufferSize = maxBufferSize;\n    this.scheduler = scheduler;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new BufferTimeSubscriber(subscriber, this.bufferTimeSpan, this.bufferCreationInterval, this.maxBufferSize, this.scheduler));\n  }\n\n}\n\nclass Context {\n  constructor() {\n    this.buffer = [];\n  }\n\n}\n\nclass BufferTimeSubscriber extends Subscriber {\n  constructor(destination, bufferTimeSpan, bufferCreationInterval, maxBufferSize, scheduler) {\n    super(destination);\n    this.bufferTimeSpan = bufferTimeSpan;\n    this.bufferCreationInterval = bufferCreationInterval;\n    this.maxBufferSize = maxBufferSize;\n    this.scheduler = scheduler;\n    this.contexts = [];\n    const context = this.openContext();\n    this.timespanOnly = bufferCreationInterval == null || bufferCreationInterval < 0;\n\n    if (this.timespanOnly) {\n      const timeSpanOnlyState = {\n        subscriber: this,\n        context,\n        bufferTimeSpan\n      };\n      this.add(context.closeAction = scheduler.schedule(dispatchBufferTimeSpanOnly, bufferTimeSpan, timeSpanOnlyState));\n    } else {\n      const closeState = {\n        subscriber: this,\n        context\n      };\n      const creationState = {\n        bufferTimeSpan,\n        bufferCreationInterval,\n        subscriber: this,\n        scheduler\n      };\n      this.add(context.closeAction = scheduler.schedule(dispatchBufferClose, bufferTimeSpan, closeState));\n      this.add(scheduler.schedule(dispatchBufferCreation, bufferCreationInterval, creationState));\n    }\n  }\n\n  _next(value) {\n    const contexts = this.contexts;\n    const len = contexts.length;\n    let filledBufferContext;\n\n    for (let i = 0; i < len; i++) {\n      const context = contexts[i];\n      const buffer = context.buffer;\n      buffer.push(value);\n\n      if (buffer.length == this.maxBufferSize) {\n        filledBufferContext = context;\n      }\n    }\n\n    if (filledBufferContext) {\n      this.onBufferFull(filledBufferContext);\n    }\n  }\n\n  _error(err) {\n    this.contexts.length = 0;\n\n    super._error(err);\n  }\n\n  _complete() {\n    const {\n      contexts,\n      destination\n    } = this;\n\n    while (contexts.length > 0) {\n      const context = contexts.shift();\n      destination.next(context.buffer);\n    }\n\n    super._complete();\n  }\n\n  _unsubscribe() {\n    this.contexts = null;\n  }\n\n  onBufferFull(context) {\n    this.closeContext(context);\n    const closeAction = context.closeAction;\n    closeAction.unsubscribe();\n    this.remove(closeAction);\n\n    if (!this.closed && this.timespanOnly) {\n      context = this.openContext();\n      const bufferTimeSpan = this.bufferTimeSpan;\n      const timeSpanOnlyState = {\n        subscriber: this,\n        context,\n        bufferTimeSpan\n      };\n      this.add(context.closeAction = this.scheduler.schedule(dispatchBufferTimeSpanOnly, bufferTimeSpan, timeSpanOnlyState));\n    }\n  }\n\n  openContext() {\n    const context = new Context();\n    this.contexts.push(context);\n    return context;\n  }\n\n  closeContext(context) {\n    this.destination.next(context.buffer);\n    const contexts = this.contexts;\n    const spliceIndex = contexts ? contexts.indexOf(context) : -1;\n\n    if (spliceIndex >= 0) {\n      contexts.splice(contexts.indexOf(context), 1);\n    }\n  }\n\n}\n\nfunction dispatchBufferTimeSpanOnly(state) {\n  const subscriber = state.subscriber;\n  const prevContext = state.context;\n\n  if (prevContext) {\n    subscriber.closeContext(prevContext);\n  }\n\n  if (!subscriber.closed) {\n    state.context = subscriber.openContext();\n    state.context.closeAction = this.schedule(state, state.bufferTimeSpan);\n  }\n}\n\nfunction dispatchBufferCreation(state) {\n  const {\n    bufferCreationInterval,\n    bufferTimeSpan,\n    subscriber,\n    scheduler\n  } = state;\n  const context = subscriber.openContext();\n  const action = this;\n\n  if (!subscriber.closed) {\n    subscriber.add(context.closeAction = scheduler.schedule(dispatchBufferClose, bufferTimeSpan, {\n      subscriber,\n      context\n    }));\n    action.schedule(state, bufferCreationInterval);\n  }\n}\n\nfunction dispatchBufferClose(arg) {\n  const {\n    subscriber,\n    context\n  } = arg;\n  subscriber.closeContext(context);\n}", "map": {"version": 3, "names": ["async", "Subscriber", "isScheduler", "bufferTime", "bufferTimeSpan", "length", "arguments", "scheduler", "bufferCreationInterval", "maxBufferSize", "Number", "POSITIVE_INFINITY", "bufferTimeOperatorFunction", "source", "lift", "BufferTimeOperator", "constructor", "call", "subscriber", "subscribe", "BufferTimeSubscriber", "Context", "buffer", "destination", "contexts", "context", "openContext", "timespanOnly", "timeSpanOnlyState", "add", "closeAction", "schedule", "dispatchBufferTimeSpanOnly", "closeState", "creationState", "dispatchBufferClose", "dispatchBufferCreation", "_next", "value", "len", "filledBufferContext", "i", "push", "onBufferFull", "_error", "err", "_complete", "shift", "next", "_unsubscribe", "closeContext", "unsubscribe", "remove", "closed", "spliceIndex", "indexOf", "splice", "state", "prevContext", "action", "arg"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/bufferTime.js"], "sourcesContent": ["import { async } from '../scheduler/async';\nimport { Subscriber } from '../Subscriber';\nimport { isScheduler } from '../util/isScheduler';\nexport function bufferTime(bufferTimeSpan) {\n    let length = arguments.length;\n    let scheduler = async;\n    if (isScheduler(arguments[arguments.length - 1])) {\n        scheduler = arguments[arguments.length - 1];\n        length--;\n    }\n    let bufferCreationInterval = null;\n    if (length >= 2) {\n        bufferCreationInterval = arguments[1];\n    }\n    let maxBufferSize = Number.POSITIVE_INFINITY;\n    if (length >= 3) {\n        maxBufferSize = arguments[2];\n    }\n    return function bufferTimeOperatorFunction(source) {\n        return source.lift(new BufferTimeOperator(bufferTimeSpan, bufferCreationInterval, maxBufferSize, scheduler));\n    };\n}\nclass BufferTimeOperator {\n    constructor(bufferTimeSpan, bufferCreationInterval, maxBufferSize, scheduler) {\n        this.bufferTimeSpan = bufferTimeSpan;\n        this.bufferCreationInterval = bufferCreationInterval;\n        this.maxBufferSize = maxBufferSize;\n        this.scheduler = scheduler;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new BufferTimeSubscriber(subscriber, this.bufferTimeSpan, this.bufferCreationInterval, this.maxBufferSize, this.scheduler));\n    }\n}\nclass Context {\n    constructor() {\n        this.buffer = [];\n    }\n}\nclass BufferTimeSubscriber extends Subscriber {\n    constructor(destination, bufferTimeSpan, bufferCreationInterval, maxBufferSize, scheduler) {\n        super(destination);\n        this.bufferTimeSpan = bufferTimeSpan;\n        this.bufferCreationInterval = bufferCreationInterval;\n        this.maxBufferSize = maxBufferSize;\n        this.scheduler = scheduler;\n        this.contexts = [];\n        const context = this.openContext();\n        this.timespanOnly = bufferCreationInterval == null || bufferCreationInterval < 0;\n        if (this.timespanOnly) {\n            const timeSpanOnlyState = { subscriber: this, context, bufferTimeSpan };\n            this.add(context.closeAction = scheduler.schedule(dispatchBufferTimeSpanOnly, bufferTimeSpan, timeSpanOnlyState));\n        }\n        else {\n            const closeState = { subscriber: this, context };\n            const creationState = { bufferTimeSpan, bufferCreationInterval, subscriber: this, scheduler };\n            this.add(context.closeAction = scheduler.schedule(dispatchBufferClose, bufferTimeSpan, closeState));\n            this.add(scheduler.schedule(dispatchBufferCreation, bufferCreationInterval, creationState));\n        }\n    }\n    _next(value) {\n        const contexts = this.contexts;\n        const len = contexts.length;\n        let filledBufferContext;\n        for (let i = 0; i < len; i++) {\n            const context = contexts[i];\n            const buffer = context.buffer;\n            buffer.push(value);\n            if (buffer.length == this.maxBufferSize) {\n                filledBufferContext = context;\n            }\n        }\n        if (filledBufferContext) {\n            this.onBufferFull(filledBufferContext);\n        }\n    }\n    _error(err) {\n        this.contexts.length = 0;\n        super._error(err);\n    }\n    _complete() {\n        const { contexts, destination } = this;\n        while (contexts.length > 0) {\n            const context = contexts.shift();\n            destination.next(context.buffer);\n        }\n        super._complete();\n    }\n    _unsubscribe() {\n        this.contexts = null;\n    }\n    onBufferFull(context) {\n        this.closeContext(context);\n        const closeAction = context.closeAction;\n        closeAction.unsubscribe();\n        this.remove(closeAction);\n        if (!this.closed && this.timespanOnly) {\n            context = this.openContext();\n            const bufferTimeSpan = this.bufferTimeSpan;\n            const timeSpanOnlyState = { subscriber: this, context, bufferTimeSpan };\n            this.add(context.closeAction = this.scheduler.schedule(dispatchBufferTimeSpanOnly, bufferTimeSpan, timeSpanOnlyState));\n        }\n    }\n    openContext() {\n        const context = new Context();\n        this.contexts.push(context);\n        return context;\n    }\n    closeContext(context) {\n        this.destination.next(context.buffer);\n        const contexts = this.contexts;\n        const spliceIndex = contexts ? contexts.indexOf(context) : -1;\n        if (spliceIndex >= 0) {\n            contexts.splice(contexts.indexOf(context), 1);\n        }\n    }\n}\nfunction dispatchBufferTimeSpanOnly(state) {\n    const subscriber = state.subscriber;\n    const prevContext = state.context;\n    if (prevContext) {\n        subscriber.closeContext(prevContext);\n    }\n    if (!subscriber.closed) {\n        state.context = subscriber.openContext();\n        state.context.closeAction = this.schedule(state, state.bufferTimeSpan);\n    }\n}\nfunction dispatchBufferCreation(state) {\n    const { bufferCreationInterval, bufferTimeSpan, subscriber, scheduler } = state;\n    const context = subscriber.openContext();\n    const action = this;\n    if (!subscriber.closed) {\n        subscriber.add(context.closeAction = scheduler.schedule(dispatchBufferClose, bufferTimeSpan, { subscriber, context }));\n        action.schedule(state, bufferCreationInterval);\n    }\n}\nfunction dispatchBufferClose(arg) {\n    const { subscriber, context } = arg;\n    subscriber.closeContext(context);\n}\n"], "mappings": "AAAA,SAASA,KAAT,QAAsB,oBAAtB;AACA,SAASC,UAAT,QAA2B,eAA3B;AACA,SAASC,WAAT,QAA4B,qBAA5B;AACA,OAAO,SAASC,UAAT,CAAoBC,cAApB,EAAoC;EACvC,IAAIC,MAAM,GAAGC,SAAS,CAACD,MAAvB;EACA,IAAIE,SAAS,GAAGP,KAAhB;;EACA,IAAIE,WAAW,CAACI,SAAS,CAACA,SAAS,CAACD,MAAV,GAAmB,CAApB,CAAV,CAAf,EAAkD;IAC9CE,SAAS,GAAGD,SAAS,CAACA,SAAS,CAACD,MAAV,GAAmB,CAApB,CAArB;IACAA,MAAM;EACT;;EACD,IAAIG,sBAAsB,GAAG,IAA7B;;EACA,IAAIH,MAAM,IAAI,CAAd,EAAiB;IACbG,sBAAsB,GAAGF,SAAS,CAAC,CAAD,CAAlC;EACH;;EACD,IAAIG,aAAa,GAAGC,MAAM,CAACC,iBAA3B;;EACA,IAAIN,MAAM,IAAI,CAAd,EAAiB;IACbI,aAAa,GAAGH,SAAS,CAAC,CAAD,CAAzB;EACH;;EACD,OAAO,SAASM,0BAAT,CAAoCC,MAApC,EAA4C;IAC/C,OAAOA,MAAM,CAACC,IAAP,CAAY,IAAIC,kBAAJ,CAAuBX,cAAvB,EAAuCI,sBAAvC,EAA+DC,aAA/D,EAA8EF,SAA9E,CAAZ,CAAP;EACH,CAFD;AAGH;;AACD,MAAMQ,kBAAN,CAAyB;EACrBC,WAAW,CAACZ,cAAD,EAAiBI,sBAAjB,EAAyCC,aAAzC,EAAwDF,SAAxD,EAAmE;IAC1E,KAAKH,cAAL,GAAsBA,cAAtB;IACA,KAAKI,sBAAL,GAA8BA,sBAA9B;IACA,KAAKC,aAAL,GAAqBA,aAArB;IACA,KAAKF,SAAL,GAAiBA,SAAjB;EACH;;EACDU,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,oBAAJ,CAAyBF,UAAzB,EAAqC,KAAKd,cAA1C,EAA0D,KAAKI,sBAA/D,EAAuF,KAAKC,aAA5F,EAA2G,KAAKF,SAAhH,CAAjB,CAAP;EACH;;AAToB;;AAWzB,MAAMc,OAAN,CAAc;EACVL,WAAW,GAAG;IACV,KAAKM,MAAL,GAAc,EAAd;EACH;;AAHS;;AAKd,MAAMF,oBAAN,SAAmCnB,UAAnC,CAA8C;EAC1Ce,WAAW,CAACO,WAAD,EAAcnB,cAAd,EAA8BI,sBAA9B,EAAsDC,aAAtD,EAAqEF,SAArE,EAAgF;IACvF,MAAMgB,WAAN;IACA,KAAKnB,cAAL,GAAsBA,cAAtB;IACA,KAAKI,sBAAL,GAA8BA,sBAA9B;IACA,KAAKC,aAAL,GAAqBA,aAArB;IACA,KAAKF,SAAL,GAAiBA,SAAjB;IACA,KAAKiB,QAAL,GAAgB,EAAhB;IACA,MAAMC,OAAO,GAAG,KAAKC,WAAL,EAAhB;IACA,KAAKC,YAAL,GAAoBnB,sBAAsB,IAAI,IAA1B,IAAkCA,sBAAsB,GAAG,CAA/E;;IACA,IAAI,KAAKmB,YAAT,EAAuB;MACnB,MAAMC,iBAAiB,GAAG;QAAEV,UAAU,EAAE,IAAd;QAAoBO,OAApB;QAA6BrB;MAA7B,CAA1B;MACA,KAAKyB,GAAL,CAASJ,OAAO,CAACK,WAAR,GAAsBvB,SAAS,CAACwB,QAAV,CAAmBC,0BAAnB,EAA+C5B,cAA/C,EAA+DwB,iBAA/D,CAA/B;IACH,CAHD,MAIK;MACD,MAAMK,UAAU,GAAG;QAAEf,UAAU,EAAE,IAAd;QAAoBO;MAApB,CAAnB;MACA,MAAMS,aAAa,GAAG;QAAE9B,cAAF;QAAkBI,sBAAlB;QAA0CU,UAAU,EAAE,IAAtD;QAA4DX;MAA5D,CAAtB;MACA,KAAKsB,GAAL,CAASJ,OAAO,CAACK,WAAR,GAAsBvB,SAAS,CAACwB,QAAV,CAAmBI,mBAAnB,EAAwC/B,cAAxC,EAAwD6B,UAAxD,CAA/B;MACA,KAAKJ,GAAL,CAAStB,SAAS,CAACwB,QAAV,CAAmBK,sBAAnB,EAA2C5B,sBAA3C,EAAmE0B,aAAnE,CAAT;IACH;EACJ;;EACDG,KAAK,CAACC,KAAD,EAAQ;IACT,MAAMd,QAAQ,GAAG,KAAKA,QAAtB;IACA,MAAMe,GAAG,GAAGf,QAAQ,CAACnB,MAArB;IACA,IAAImC,mBAAJ;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,GAApB,EAAyBE,CAAC,EAA1B,EAA8B;MAC1B,MAAMhB,OAAO,GAAGD,QAAQ,CAACiB,CAAD,CAAxB;MACA,MAAMnB,MAAM,GAAGG,OAAO,CAACH,MAAvB;MACAA,MAAM,CAACoB,IAAP,CAAYJ,KAAZ;;MACA,IAAIhB,MAAM,CAACjB,MAAP,IAAiB,KAAKI,aAA1B,EAAyC;QACrC+B,mBAAmB,GAAGf,OAAtB;MACH;IACJ;;IACD,IAAIe,mBAAJ,EAAyB;MACrB,KAAKG,YAAL,CAAkBH,mBAAlB;IACH;EACJ;;EACDI,MAAM,CAACC,GAAD,EAAM;IACR,KAAKrB,QAAL,CAAcnB,MAAd,GAAuB,CAAvB;;IACA,MAAMuC,MAAN,CAAaC,GAAb;EACH;;EACDC,SAAS,GAAG;IACR,MAAM;MAAEtB,QAAF;MAAYD;IAAZ,IAA4B,IAAlC;;IACA,OAAOC,QAAQ,CAACnB,MAAT,GAAkB,CAAzB,EAA4B;MACxB,MAAMoB,OAAO,GAAGD,QAAQ,CAACuB,KAAT,EAAhB;MACAxB,WAAW,CAACyB,IAAZ,CAAiBvB,OAAO,CAACH,MAAzB;IACH;;IACD,MAAMwB,SAAN;EACH;;EACDG,YAAY,GAAG;IACX,KAAKzB,QAAL,GAAgB,IAAhB;EACH;;EACDmB,YAAY,CAAClB,OAAD,EAAU;IAClB,KAAKyB,YAAL,CAAkBzB,OAAlB;IACA,MAAMK,WAAW,GAAGL,OAAO,CAACK,WAA5B;IACAA,WAAW,CAACqB,WAAZ;IACA,KAAKC,MAAL,CAAYtB,WAAZ;;IACA,IAAI,CAAC,KAAKuB,MAAN,IAAgB,KAAK1B,YAAzB,EAAuC;MACnCF,OAAO,GAAG,KAAKC,WAAL,EAAV;MACA,MAAMtB,cAAc,GAAG,KAAKA,cAA5B;MACA,MAAMwB,iBAAiB,GAAG;QAAEV,UAAU,EAAE,IAAd;QAAoBO,OAApB;QAA6BrB;MAA7B,CAA1B;MACA,KAAKyB,GAAL,CAASJ,OAAO,CAACK,WAAR,GAAsB,KAAKvB,SAAL,CAAewB,QAAf,CAAwBC,0BAAxB,EAAoD5B,cAApD,EAAoEwB,iBAApE,CAA/B;IACH;EACJ;;EACDF,WAAW,GAAG;IACV,MAAMD,OAAO,GAAG,IAAIJ,OAAJ,EAAhB;IACA,KAAKG,QAAL,CAAckB,IAAd,CAAmBjB,OAAnB;IACA,OAAOA,OAAP;EACH;;EACDyB,YAAY,CAACzB,OAAD,EAAU;IAClB,KAAKF,WAAL,CAAiByB,IAAjB,CAAsBvB,OAAO,CAACH,MAA9B;IACA,MAAME,QAAQ,GAAG,KAAKA,QAAtB;IACA,MAAM8B,WAAW,GAAG9B,QAAQ,GAAGA,QAAQ,CAAC+B,OAAT,CAAiB9B,OAAjB,CAAH,GAA+B,CAAC,CAA5D;;IACA,IAAI6B,WAAW,IAAI,CAAnB,EAAsB;MAClB9B,QAAQ,CAACgC,MAAT,CAAgBhC,QAAQ,CAAC+B,OAAT,CAAiB9B,OAAjB,CAAhB,EAA2C,CAA3C;IACH;EACJ;;AA5EyC;;AA8E9C,SAASO,0BAAT,CAAoCyB,KAApC,EAA2C;EACvC,MAAMvC,UAAU,GAAGuC,KAAK,CAACvC,UAAzB;EACA,MAAMwC,WAAW,GAAGD,KAAK,CAAChC,OAA1B;;EACA,IAAIiC,WAAJ,EAAiB;IACbxC,UAAU,CAACgC,YAAX,CAAwBQ,WAAxB;EACH;;EACD,IAAI,CAACxC,UAAU,CAACmC,MAAhB,EAAwB;IACpBI,KAAK,CAAChC,OAAN,GAAgBP,UAAU,CAACQ,WAAX,EAAhB;IACA+B,KAAK,CAAChC,OAAN,CAAcK,WAAd,GAA4B,KAAKC,QAAL,CAAc0B,KAAd,EAAqBA,KAAK,CAACrD,cAA3B,CAA5B;EACH;AACJ;;AACD,SAASgC,sBAAT,CAAgCqB,KAAhC,EAAuC;EACnC,MAAM;IAAEjD,sBAAF;IAA0BJ,cAA1B;IAA0Cc,UAA1C;IAAsDX;EAAtD,IAAoEkD,KAA1E;EACA,MAAMhC,OAAO,GAAGP,UAAU,CAACQ,WAAX,EAAhB;EACA,MAAMiC,MAAM,GAAG,IAAf;;EACA,IAAI,CAACzC,UAAU,CAACmC,MAAhB,EAAwB;IACpBnC,UAAU,CAACW,GAAX,CAAeJ,OAAO,CAACK,WAAR,GAAsBvB,SAAS,CAACwB,QAAV,CAAmBI,mBAAnB,EAAwC/B,cAAxC,EAAwD;MAAEc,UAAF;MAAcO;IAAd,CAAxD,CAArC;IACAkC,MAAM,CAAC5B,QAAP,CAAgB0B,KAAhB,EAAuBjD,sBAAvB;EACH;AACJ;;AACD,SAAS2B,mBAAT,CAA6ByB,GAA7B,EAAkC;EAC9B,MAAM;IAAE1C,UAAF;IAAcO;EAAd,IAA0BmC,GAAhC;EACA1C,UAAU,CAACgC,YAAX,CAAwBzB,OAAxB;AACH"}, "metadata": {}, "sourceType": "module"}