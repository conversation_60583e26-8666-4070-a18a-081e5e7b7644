{"ast": null, "code": "import { iterator as Symbol_iterator } from '../symbol/iterator';\nexport function isIterable(input) {\n  return input && typeof input[Symbol_iterator] === 'function';\n}", "map": {"version": 3, "names": ["iterator", "Symbol_iterator", "isIterable", "input"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/isIterable.js"], "sourcesContent": ["import { iterator as Symbol_iterator } from '../symbol/iterator';\nexport function isIterable(input) {\n    return input && typeof input[Symbol_iterator] === 'function';\n}\n"], "mappings": "AAAA,SAASA,QAAQ,IAAIC,eAArB,QAA4C,oBAA5C;AACA,OAAO,SAASC,UAAT,CAAoBC,KAApB,EAA2B;EAC9B,OAAOA,KAAK,IAAI,OAAOA,KAAK,CAACF,eAAD,CAAZ,KAAkC,UAAlD;AACH"}, "metadata": {}, "sourceType": "module"}