{"ast": null, "code": ";\n\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * Output Feedback block mode.\n   */\n  CryptoJS.mode.OFB = function () {\n    var OFB = CryptoJS.lib.BlockCipherMode.extend();\n    var Encryptor = OFB.Encryptor = OFB.extend({\n      processBlock: function (words, offset) {\n        // Shortcuts\n        var cipher = this._cipher;\n        var blockSize = cipher.blockSize;\n        var iv = this._iv;\n        var keystream = this._keystream; // Generate keystream\n\n        if (iv) {\n          keystream = this._keystream = iv.slice(0); // Remove IV for subsequent blocks\n\n          this._iv = undefined;\n        }\n\n        cipher.encryptBlock(keystream, 0); // Encrypt\n\n        for (var i = 0; i < blockSize; i++) {\n          words[offset + i] ^= keystream[i];\n        }\n      }\n    });\n    OFB.Decryptor = Encryptor;\n    return OFB;\n  }();\n\n  return CryptoJS.mode.OFB;\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "mode", "OFB", "lib", "BlockCipherMode", "extend", "Encryptor", "processBlock", "words", "offset", "cipher", "_cipher", "blockSize", "iv", "_iv", "keystream", "_keystream", "slice", "undefined", "encryptBlock", "i", "Decryptor"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/crypto-js/mode-ofb.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./cipher-core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t/**\n\t * Output Feedback block mode.\n\t */\n\tCryptoJS.mode.OFB = (function () {\n\t    var OFB = CryptoJS.lib.BlockCipherMode.extend();\n\n\t    var Encryptor = OFB.Encryptor = OFB.extend({\n\t        processBlock: function (words, offset) {\n\t            // Shortcuts\n\t            var cipher = this._cipher\n\t            var blockSize = cipher.blockSize;\n\t            var iv = this._iv;\n\t            var keystream = this._keystream;\n\n\t            // Generate keystream\n\t            if (iv) {\n\t                keystream = this._keystream = iv.slice(0);\n\n\t                // Remove IV for subsequent blocks\n\t                this._iv = undefined;\n\t            }\n\t            cipher.encryptBlock(keystream, 0);\n\n\t            // Encrypt\n\t            for (var i = 0; i < blockSize; i++) {\n\t                words[offset + i] ^= keystream[i];\n\t            }\n\t        }\n\t    });\n\n\t    OFB.Decryptor = Encryptor;\n\n\t    return OFB;\n\t}());\n\n\n\treturn CryptoJS.mode.OFB;\n\n}));"], "mappings": "AAAA;;AAAE,WAAUA,IAAV,EAAgBC,OAAhB,EAAyBC,KAAzB,EAAgC;EACjC,IAAI,OAAOC,OAAP,KAAmB,QAAvB,EAAiC;IAChC;IACAC,MAAM,CAACD,OAAP,GAAiBA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAD,CAAR,EAAoBA,OAAO,CAAC,eAAD,CAA3B,CAAlC;EACA,CAHD,MAIK,IAAI,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAA3C,EAAgD;IACpD;IACAD,MAAM,CAAC,CAAC,QAAD,EAAW,eAAX,CAAD,EAA8BL,OAA9B,CAAN;EACA,CAHI,MAIA;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAN,CAAP;EACA;AACD,CAbC,EAaA,IAbA,EAaM,UAAUA,QAAV,EAAoB;EAE3B;AACD;AACA;EACCA,QAAQ,CAACC,IAAT,CAAcC,GAAd,GAAqB,YAAY;IAC7B,IAAIA,GAAG,GAAGF,QAAQ,CAACG,GAAT,CAAaC,eAAb,CAA6BC,MAA7B,EAAV;IAEA,IAAIC,SAAS,GAAGJ,GAAG,CAACI,SAAJ,GAAgBJ,GAAG,CAACG,MAAJ,CAAW;MACvCE,YAAY,EAAE,UAAUC,KAAV,EAAiBC,MAAjB,EAAyB;QACnC;QACA,IAAIC,MAAM,GAAG,KAAKC,OAAlB;QACA,IAAIC,SAAS,GAAGF,MAAM,CAACE,SAAvB;QACA,IAAIC,EAAE,GAAG,KAAKC,GAAd;QACA,IAAIC,SAAS,GAAG,KAAKC,UAArB,CALmC,CAOnC;;QACA,IAAIH,EAAJ,EAAQ;UACJE,SAAS,GAAG,KAAKC,UAAL,GAAkBH,EAAE,CAACI,KAAH,CAAS,CAAT,CAA9B,CADI,CAGJ;;UACA,KAAKH,GAAL,GAAWI,SAAX;QACH;;QACDR,MAAM,CAACS,YAAP,CAAoBJ,SAApB,EAA+B,CAA/B,EAdmC,CAgBnC;;QACA,KAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGR,SAApB,EAA+BQ,CAAC,EAAhC,EAAoC;UAChCZ,KAAK,CAACC,MAAM,GAAGW,CAAV,CAAL,IAAqBL,SAAS,CAACK,CAAD,CAA9B;QACH;MACJ;IArBsC,CAAX,CAAhC;IAwBAlB,GAAG,CAACmB,SAAJ,GAAgBf,SAAhB;IAEA,OAAOJ,GAAP;EACH,CA9BoB,EAArB;;EAiCA,OAAOF,QAAQ,CAACC,IAAT,CAAcC,GAArB;AAEA,CArDC,CAAD"}, "metadata": {}, "sourceType": "script"}