{"ast": null, "code": "import { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nimport { iterator as Symbol_iterator } from '../symbol/iterator';\nexport function scheduleIterable(input, scheduler) {\n  if (!input) {\n    throw new Error('Iterable cannot be null');\n  }\n\n  return new Observable(subscriber => {\n    const sub = new Subscription();\n    let iterator;\n    sub.add(() => {\n      if (iterator && typeof iterator.return === 'function') {\n        iterator.return();\n      }\n    });\n    sub.add(scheduler.schedule(() => {\n      iterator = input[Symbol_iterator]();\n      sub.add(scheduler.schedule(function () {\n        if (subscriber.closed) {\n          return;\n        }\n\n        let value;\n        let done;\n\n        try {\n          const result = iterator.next();\n          value = result.value;\n          done = result.done;\n        } catch (err) {\n          subscriber.error(err);\n          return;\n        }\n\n        if (done) {\n          subscriber.complete();\n        } else {\n          subscriber.next(value);\n          this.schedule();\n        }\n      }));\n    }));\n    return sub;\n  });\n}", "map": {"version": 3, "names": ["Observable", "Subscription", "iterator", "Symbol_iterator", "scheduleIterable", "input", "scheduler", "Error", "subscriber", "sub", "add", "return", "schedule", "closed", "value", "done", "result", "next", "err", "error", "complete"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/scheduled/scheduleIterable.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nimport { iterator as Symbol_iterator } from '../symbol/iterator';\nexport function scheduleIterable(input, scheduler) {\n    if (!input) {\n        throw new Error('Iterable cannot be null');\n    }\n    return new Observable(subscriber => {\n        const sub = new Subscription();\n        let iterator;\n        sub.add(() => {\n            if (iterator && typeof iterator.return === 'function') {\n                iterator.return();\n            }\n        });\n        sub.add(scheduler.schedule(() => {\n            iterator = input[Symbol_iterator]();\n            sub.add(scheduler.schedule(function () {\n                if (subscriber.closed) {\n                    return;\n                }\n                let value;\n                let done;\n                try {\n                    const result = iterator.next();\n                    value = result.value;\n                    done = result.done;\n                }\n                catch (err) {\n                    subscriber.error(err);\n                    return;\n                }\n                if (done) {\n                    subscriber.complete();\n                }\n                else {\n                    subscriber.next(value);\n                    this.schedule();\n                }\n            }));\n        }));\n        return sub;\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,QAAQ,IAAIC,eAArB,QAA4C,oBAA5C;AACA,OAAO,SAASC,gBAAT,CAA0BC,KAA1B,EAAiCC,SAAjC,EAA4C;EAC/C,IAAI,CAACD,KAAL,EAAY;IACR,MAAM,IAAIE,KAAJ,CAAU,yBAAV,CAAN;EACH;;EACD,OAAO,IAAIP,UAAJ,CAAeQ,UAAU,IAAI;IAChC,MAAMC,GAAG,GAAG,IAAIR,YAAJ,EAAZ;IACA,IAAIC,QAAJ;IACAO,GAAG,CAACC,GAAJ,CAAQ,MAAM;MACV,IAAIR,QAAQ,IAAI,OAAOA,QAAQ,CAACS,MAAhB,KAA2B,UAA3C,EAAuD;QACnDT,QAAQ,CAACS,MAAT;MACH;IACJ,CAJD;IAKAF,GAAG,CAACC,GAAJ,CAAQJ,SAAS,CAACM,QAAV,CAAmB,MAAM;MAC7BV,QAAQ,GAAGG,KAAK,CAACF,eAAD,CAAL,EAAX;MACAM,GAAG,CAACC,GAAJ,CAAQJ,SAAS,CAACM,QAAV,CAAmB,YAAY;QACnC,IAAIJ,UAAU,CAACK,MAAf,EAAuB;UACnB;QACH;;QACD,IAAIC,KAAJ;QACA,IAAIC,IAAJ;;QACA,IAAI;UACA,MAAMC,MAAM,GAAGd,QAAQ,CAACe,IAAT,EAAf;UACAH,KAAK,GAAGE,MAAM,CAACF,KAAf;UACAC,IAAI,GAAGC,MAAM,CAACD,IAAd;QACH,CAJD,CAKA,OAAOG,GAAP,EAAY;UACRV,UAAU,CAACW,KAAX,CAAiBD,GAAjB;UACA;QACH;;QACD,IAAIH,IAAJ,EAAU;UACNP,UAAU,CAACY,QAAX;QACH,CAFD,MAGK;UACDZ,UAAU,CAACS,IAAX,CAAgBH,KAAhB;UACA,KAAKF,QAAL;QACH;MACJ,CAtBO,CAAR;IAuBH,CAzBO,CAAR;IA0BA,OAAOH,GAAP;EACH,CAnCM,CAAP;AAoCH"}, "metadata": {}, "sourceType": "module"}