{"ast": null, "code": "let _enable_super_gross_mode_that_will_cause_bad_things = false;\nexport const config = {\n  Promise: undefined,\n\n  set useDeprecatedSynchronousErrorHandling(value) {\n    if (value) {\n      const error = new Error();\n      console.warn('DEPRECATED! RxJS was set to use deprecated synchronous error handling behavior by code at: \\n' + error.stack);\n    } else if (_enable_super_gross_mode_that_will_cause_bad_things) {\n      console.log('RxJS: Back to a better error behavior. Thank you. <3');\n    }\n\n    _enable_super_gross_mode_that_will_cause_bad_things = value;\n  },\n\n  get useDeprecatedSynchronousErrorHandling() {\n    return _enable_super_gross_mode_that_will_cause_bad_things;\n  }\n\n};", "map": {"version": 3, "names": ["_enable_super_gross_mode_that_will_cause_bad_things", "config", "Promise", "undefined", "useDeprecatedSynchronousErrorHandling", "value", "error", "Error", "console", "warn", "stack", "log"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/config.js"], "sourcesContent": ["let _enable_super_gross_mode_that_will_cause_bad_things = false;\nexport const config = {\n    Promise: undefined,\n    set useDeprecatedSynchronousErrorHandling(value) {\n        if (value) {\n            const error = new Error();\n            console.warn('DEPRECATED! RxJS was set to use deprecated synchronous error handling behavior by code at: \\n' + error.stack);\n        }\n        else if (_enable_super_gross_mode_that_will_cause_bad_things) {\n            console.log('RxJS: Back to a better error behavior. Thank you. <3');\n        }\n        _enable_super_gross_mode_that_will_cause_bad_things = value;\n    },\n    get useDeprecatedSynchronousErrorHandling() {\n        return _enable_super_gross_mode_that_will_cause_bad_things;\n    },\n};\n"], "mappings": "AAAA,IAAIA,mDAAmD,GAAG,KAA1D;AACA,OAAO,MAAMC,MAAM,GAAG;EAClBC,OAAO,EAAEC,SADS;;EAElB,IAAIC,qCAAJ,CAA0CC,KAA1C,EAAiD;IAC7C,IAAIA,KAAJ,EAAW;MACP,MAAMC,KAAK,GAAG,IAAIC,KAAJ,EAAd;MACAC,OAAO,CAACC,IAAR,CAAa,kGAAkGH,KAAK,CAACI,KAArH;IACH,CAHD,MAIK,IAAIV,mDAAJ,EAAyD;MAC1DQ,OAAO,CAACG,GAAR,CAAY,sDAAZ;IACH;;IACDX,mDAAmD,GAAGK,KAAtD;EACH,CAXiB;;EAYlB,IAAID,qCAAJ,GAA4C;IACxC,OAAOJ,mDAAP;EACH;;AAdiB,CAAf"}, "metadata": {}, "sourceType": "module"}