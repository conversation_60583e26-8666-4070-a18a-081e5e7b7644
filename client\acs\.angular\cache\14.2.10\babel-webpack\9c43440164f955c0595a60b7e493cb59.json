{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function takeWhile(predicate, inclusive = false) {\n  return source => source.lift(new TakeWhileOperator(predicate, inclusive));\n}\n\nclass TakeWhileOperator {\n  constructor(predicate, inclusive) {\n    this.predicate = predicate;\n    this.inclusive = inclusive;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new TakeWhileSubscriber(subscriber, this.predicate, this.inclusive));\n  }\n\n}\n\nclass TakeWhileSubscriber extends Subscriber {\n  constructor(destination, predicate, inclusive) {\n    super(destination);\n    this.predicate = predicate;\n    this.inclusive = inclusive;\n    this.index = 0;\n  }\n\n  _next(value) {\n    const destination = this.destination;\n    let result;\n\n    try {\n      result = this.predicate(value, this.index++);\n    } catch (err) {\n      destination.error(err);\n      return;\n    }\n\n    this.nextOrComplete(value, result);\n  }\n\n  nextOrComplete(value, predicateResult) {\n    const destination = this.destination;\n\n    if (Boolean(predicateResult)) {\n      destination.next(value);\n    } else {\n      if (this.inclusive) {\n        destination.next(value);\n      }\n\n      destination.complete();\n    }\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "<PERSON><PERSON><PERSON><PERSON>", "predicate", "inclusive", "source", "lift", "TakeWhileOperator", "constructor", "call", "subscriber", "subscribe", "TakeWhileSubscriber", "destination", "index", "_next", "value", "result", "err", "error", "nextOrComplete", "predicateResult", "Boolean", "next", "complete"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/takeWhile.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function takeWhile(predicate, inclusive = false) {\n    return (source) => source.lift(new TakeWhileOperator(predicate, inclusive));\n}\nclass TakeWhileOperator {\n    constructor(predicate, inclusive) {\n        this.predicate = predicate;\n        this.inclusive = inclusive;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new TakeWhileSubscriber(subscriber, this.predicate, this.inclusive));\n    }\n}\nclass TakeWhileSubscriber extends Subscriber {\n    constructor(destination, predicate, inclusive) {\n        super(destination);\n        this.predicate = predicate;\n        this.inclusive = inclusive;\n        this.index = 0;\n    }\n    _next(value) {\n        const destination = this.destination;\n        let result;\n        try {\n            result = this.predicate(value, this.index++);\n        }\n        catch (err) {\n            destination.error(err);\n            return;\n        }\n        this.nextOrComplete(value, result);\n    }\n    nextOrComplete(value, predicateResult) {\n        const destination = this.destination;\n        if (Boolean(predicateResult)) {\n            destination.next(value);\n        }\n        else {\n            if (this.inclusive) {\n                destination.next(value);\n            }\n            destination.complete();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,SAAT,CAAmBC,SAAnB,EAA8BC,SAAS,GAAG,KAA1C,EAAiD;EACpD,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,iBAAJ,CAAsBJ,SAAtB,EAAiCC,SAAjC,CAAZ,CAAnB;AACH;;AACD,MAAMG,iBAAN,CAAwB;EACpBC,WAAW,CAACL,SAAD,EAAYC,SAAZ,EAAuB;IAC9B,KAAKD,SAAL,GAAiBA,SAAjB;IACA,KAAKC,SAAL,GAAiBA,SAAjB;EACH;;EACDK,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,mBAAJ,CAAwBF,UAAxB,EAAoC,KAAKP,SAAzC,EAAoD,KAAKC,SAAzD,CAAjB,CAAP;EACH;;AAPmB;;AASxB,MAAMQ,mBAAN,SAAkCX,UAAlC,CAA6C;EACzCO,WAAW,CAACK,WAAD,EAAcV,SAAd,EAAyBC,SAAzB,EAAoC;IAC3C,MAAMS,WAAN;IACA,KAAKV,SAAL,GAAiBA,SAAjB;IACA,KAAKC,SAAL,GAAiBA,SAAjB;IACA,KAAKU,KAAL,GAAa,CAAb;EACH;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACT,MAAMH,WAAW,GAAG,KAAKA,WAAzB;IACA,IAAII,MAAJ;;IACA,IAAI;MACAA,MAAM,GAAG,KAAKd,SAAL,CAAea,KAAf,EAAsB,KAAKF,KAAL,EAAtB,CAAT;IACH,CAFD,CAGA,OAAOI,GAAP,EAAY;MACRL,WAAW,CAACM,KAAZ,CAAkBD,GAAlB;MACA;IACH;;IACD,KAAKE,cAAL,CAAoBJ,KAApB,EAA2BC,MAA3B;EACH;;EACDG,cAAc,CAACJ,KAAD,EAAQK,eAAR,EAAyB;IACnC,MAAMR,WAAW,GAAG,KAAKA,WAAzB;;IACA,IAAIS,OAAO,CAACD,eAAD,CAAX,EAA8B;MAC1BR,WAAW,CAACU,IAAZ,CAAiBP,KAAjB;IACH,CAFD,MAGK;MACD,IAAI,KAAKZ,SAAT,EAAoB;QAChBS,WAAW,CAACU,IAAZ,CAAiBP,KAAjB;MACH;;MACDH,WAAW,CAACW,QAAZ;IACH;EACJ;;AA9BwC"}, "metadata": {}, "sourceType": "module"}