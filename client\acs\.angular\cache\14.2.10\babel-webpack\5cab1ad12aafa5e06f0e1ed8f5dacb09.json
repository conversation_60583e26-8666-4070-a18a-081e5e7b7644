{"ast": null, "code": "import { ZipOperator } from '../observable/zip';\nexport function zipAll(project) {\n  return source => source.lift(new ZipOperator(project));\n}", "map": {"version": 3, "names": ["ZipOperator", "zipAll", "project", "source", "lift"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/zipAll.js"], "sourcesContent": ["import { ZipOperator } from '../observable/zip';\nexport function zipAll(project) {\n    return (source) => source.lift(new ZipOperator(project));\n}\n"], "mappings": "AAAA,SAASA,WAAT,QAA4B,mBAA5B;AACA,OAAO,SAASC,MAAT,CAAgBC,OAAhB,EAAyB;EAC5B,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIJ,WAAJ,CAAgBE,OAAhB,CAAZ,CAAnB;AACH"}, "metadata": {}, "sourceType": "module"}