{"ast": null, "code": "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "map": {"version": 3, "names": ["popperGenerator", "detectOverflow", "eventListeners", "popperOffsets", "computeStyles", "applyStyles", "offset", "flip", "preventOverflow", "arrow", "hide", "defaultModifiers", "createPopper", "createPopperLite"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/popper.js"], "sourcesContent": ["import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";"], "mappings": "AAAA,SAASA,eAAT,EAA0BC,cAA1B,QAAgD,mBAAhD;AACA,OAAOC,cAAP,MAA2B,+BAA3B;AACA,OAAOC,aAAP,MAA0B,8BAA1B;AACA,OAAOC,aAAP,MAA0B,8BAA1B;AACA,OAAOC,WAAP,MAAwB,4BAAxB;AACA,OAAOC,MAAP,MAAmB,uBAAnB;AACA,OAAOC,IAAP,MAAiB,qBAAjB;AACA,OAAOC,eAAP,MAA4B,gCAA5B;AACA,OAAOC,KAAP,MAAkB,sBAAlB;AACA,OAAOC,IAAP,MAAiB,qBAAjB;AACA,IAAIC,gBAAgB,GAAG,CAACT,cAAD,EAAiBC,aAAjB,EAAgCC,aAAhC,EAA+CC,WAA/C,EAA4DC,MAA5D,EAAoEC,IAApE,EAA0EC,eAA1E,EAA2FC,KAA3F,EAAkGC,IAAlG,CAAvB;AACA,IAAIE,YAAY,GAAG,aAAaZ,eAAe,CAAC;EAC9CW,gBAAgB,EAAEA;AAD4B,CAAD,CAA/C,C,CAEI;;AAEJ,SAASC,YAAT,EAAuBZ,eAAvB,EAAwCW,gBAAxC,EAA0DV,cAA1D,G,CAA4E;;AAE5E,SAASW,YAAY,IAAIC,gBAAzB,QAAiD,kBAAjD,C,CAAqE;;AAErE,cAAc,sBAAd"}, "metadata": {}, "sourceType": "module"}