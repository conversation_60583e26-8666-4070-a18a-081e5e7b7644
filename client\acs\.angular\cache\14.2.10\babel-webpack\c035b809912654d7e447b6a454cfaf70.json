{"ast": null, "code": "/* global __webpack_dev_server_client__ */\nimport WebSocketClient from \"./clients/WebSocketClient.js\";\nimport { log } from \"./utils/log.js\"; // this WebsocketClient is here as a default fallback, in case the client is not injected\n\n/* eslint-disable camelcase */\n\nvar Client = // eslint-disable-next-line no-nested-ternary\ntypeof __webpack_dev_server_client__ !== \"undefined\" ? typeof __webpack_dev_server_client__.default !== \"undefined\" ? __webpack_dev_server_client__.default : __webpack_dev_server_client__ : WebSocketClient;\n/* eslint-enable camelcase */\n\nvar retries = 0;\nvar maxRetries = 10; // Initialized client is exported so external consumers can utilize the same instance\n// It is mutable to enforce singleton\n// eslint-disable-next-line import/no-mutable-exports\n\nexport var client = null;\n/**\n * @param {string} url\n * @param {{ [handler: string]: (data?: any, params?: any) => any }} handlers\n * @param {number} [reconnect]\n */\n\nvar socket = function initSocket(url, handlers, reconnect) {\n  client = new Client(url);\n  client.onOpen(function () {\n    retries = 0;\n\n    if (typeof reconnect !== \"undefined\") {\n      maxRetries = reconnect;\n    }\n  });\n  client.onClose(function () {\n    if (retries === 0) {\n      handlers.close();\n    } // Try to reconnect.\n\n\n    client = null; // After 10 retries stop trying, to prevent logspam.\n\n    if (retries < maxRetries) {\n      // Exponentially increase timeout to reconnect.\n      // Respectfully copied from the package `got`.\n      // eslint-disable-next-line no-restricted-properties\n      var retryInMs = 1000 * Math.pow(2, retries) + Math.random() * 100;\n      retries += 1;\n      log.info(\"Trying to reconnect...\");\n      setTimeout(function () {\n        socket(url, handlers, reconnect);\n      }, retryInMs);\n    }\n  });\n  client.onMessage(\n  /**\n   * @param {any} data\n   */\n  function (data) {\n    var message = JSON.parse(data);\n\n    if (handlers[message.type]) {\n      handlers[message.type](message.data, message.params);\n    }\n  });\n};\n\nexport default socket;", "map": {"version": 3, "names": ["WebSocketClient", "log", "Client", "__webpack_dev_server_client__", "default", "retries", "maxRetries", "client", "socket", "initSocket", "url", "handlers", "reconnect", "onOpen", "onClose", "close", "retryInMs", "Math", "pow", "random", "info", "setTimeout", "onMessage", "data", "message", "JSON", "parse", "type", "params"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/webpack-dev-server/client/socket.js"], "sourcesContent": ["/* global __webpack_dev_server_client__ */\nimport WebSocketClient from \"./clients/WebSocketClient.js\";\nimport { log } from \"./utils/log.js\"; // this WebsocketClient is here as a default fallback, in case the client is not injected\n\n/* eslint-disable camelcase */\n\nvar Client = // eslint-disable-next-line no-nested-ternary\ntypeof __webpack_dev_server_client__ !== \"undefined\" ? typeof __webpack_dev_server_client__.default !== \"undefined\" ? __webpack_dev_server_client__.default : __webpack_dev_server_client__ : WebSocketClient;\n/* eslint-enable camelcase */\n\nvar retries = 0;\nvar maxRetries = 10; // Initialized client is exported so external consumers can utilize the same instance\n// It is mutable to enforce singleton\n// eslint-disable-next-line import/no-mutable-exports\n\nexport var client = null;\n/**\n * @param {string} url\n * @param {{ [handler: string]: (data?: any, params?: any) => any }} handlers\n * @param {number} [reconnect]\n */\n\nvar socket = function initSocket(url, handlers, reconnect) {\n  client = new Client(url);\n  client.onOpen(function () {\n    retries = 0;\n\n    if (typeof reconnect !== \"undefined\") {\n      maxRetries = reconnect;\n    }\n  });\n  client.onClose(function () {\n    if (retries === 0) {\n      handlers.close();\n    } // Try to reconnect.\n\n\n    client = null; // After 10 retries stop trying, to prevent logspam.\n\n    if (retries < maxRetries) {\n      // Exponentially increase timeout to reconnect.\n      // Respectfully copied from the package `got`.\n      // eslint-disable-next-line no-restricted-properties\n      var retryInMs = 1000 * Math.pow(2, retries) + Math.random() * 100;\n      retries += 1;\n      log.info(\"Trying to reconnect...\");\n      setTimeout(function () {\n        socket(url, handlers, reconnect);\n      }, retryInMs);\n    }\n  });\n  client.onMessage(\n  /**\n   * @param {any} data\n   */\n  function (data) {\n    var message = JSON.parse(data);\n\n    if (handlers[message.type]) {\n      handlers[message.type](message.data, message.params);\n    }\n  });\n};\n\nexport default socket;"], "mappings": "AAAA;AACA,OAAOA,eAAP,MAA4B,8BAA5B;AACA,SAASC,GAAT,QAAoB,gBAApB,C,CAAsC;;AAEtC;;AAEA,IAAIC,MAAM,GAAG;AACb,OAAOC,6BAAP,KAAyC,WAAzC,GAAuD,OAAOA,6BAA6B,CAACC,OAArC,KAAiD,WAAjD,GAA+DD,6BAA6B,CAACC,OAA7F,GAAuGD,6BAA9J,GAA8LH,eAD9L;AAEA;;AAEA,IAAIK,OAAO,GAAG,CAAd;AACA,IAAIC,UAAU,GAAG,EAAjB,C,CAAqB;AACrB;AACA;;AAEA,OAAO,IAAIC,MAAM,GAAG,IAAb;AACP;AACA;AACA;AACA;AACA;;AAEA,IAAIC,MAAM,GAAG,SAASC,UAAT,CAAoBC,GAApB,EAAyBC,QAAzB,EAAmCC,SAAnC,EAA8C;EACzDL,MAAM,GAAG,IAAIL,MAAJ,CAAWQ,GAAX,CAAT;EACAH,MAAM,CAACM,MAAP,CAAc,YAAY;IACxBR,OAAO,GAAG,CAAV;;IAEA,IAAI,OAAOO,SAAP,KAAqB,WAAzB,EAAsC;MACpCN,UAAU,GAAGM,SAAb;IACD;EACF,CAND;EAOAL,MAAM,CAACO,OAAP,CAAe,YAAY;IACzB,IAAIT,OAAO,KAAK,CAAhB,EAAmB;MACjBM,QAAQ,CAACI,KAAT;IACD,CAHwB,CAGvB;;;IAGFR,MAAM,GAAG,IAAT,CANyB,CAMV;;IAEf,IAAIF,OAAO,GAAGC,UAAd,EAA0B;MACxB;MACA;MACA;MACA,IAAIU,SAAS,GAAG,OAAOC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYb,OAAZ,CAAP,GAA8BY,IAAI,CAACE,MAAL,KAAgB,GAA9D;MACAd,OAAO,IAAI,CAAX;MACAJ,GAAG,CAACmB,IAAJ,CAAS,wBAAT;MACAC,UAAU,CAAC,YAAY;QACrBb,MAAM,CAACE,GAAD,EAAMC,QAAN,EAAgBC,SAAhB,CAAN;MACD,CAFS,EAEPI,SAFO,CAAV;IAGD;EACF,CAnBD;EAoBAT,MAAM,CAACe,SAAP;EACA;AACF;AACA;EACE,UAAUC,IAAV,EAAgB;IACd,IAAIC,OAAO,GAAGC,IAAI,CAACC,KAAL,CAAWH,IAAX,CAAd;;IAEA,IAAIZ,QAAQ,CAACa,OAAO,CAACG,IAAT,CAAZ,EAA4B;MAC1BhB,QAAQ,CAACa,OAAO,CAACG,IAAT,CAAR,CAAuBH,OAAO,CAACD,IAA/B,EAAqCC,OAAO,CAACI,MAA7C;IACD;EACF,CAVD;AAWD,CAxCD;;AA0CA,eAAepB,MAAf"}, "metadata": {}, "sourceType": "module"}