{"ast": null, "code": "export * from \"./enums.js\";\nexport * from \"./modifiers/index.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport { popperGenerator, detectOverflow, createPopper as createPopperBase } from \"./createPopper.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper } from \"./popper.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\";", "map": {"version": 3, "names": ["popperGenerator", "detectOverflow", "createPopper", "createPopperBase", "createPopperLite"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/index.js"], "sourcesContent": ["export * from \"./enums.js\";\nexport * from \"./modifiers/index.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport { popperGenerator, detectOverflow, createPopper as createPopperBase } from \"./createPopper.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper } from \"./popper.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\";"], "mappings": "AAAA,cAAc,YAAd;AACA,cAAc,sBAAd,C,CAAsC;;AAEtC,SAASA,eAAT,EAA0BC,cAA1B,EAA0CC,YAAY,IAAIC,gBAA1D,QAAkF,mBAAlF,C,CAAuG;;AAEvG,SAASD,YAAT,QAA6B,aAA7B,C,CAA4C;;AAE5C,SAASA,YAAY,IAAIE,gBAAzB,QAAiD,kBAAjD"}, "metadata": {}, "sourceType": "module"}