{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { ElementRef, Injector, Directive, EventEmitter, Inject, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Throws an exception when attempting to attach a null portal to a host.\n * @docs-private\n */\n\nfunction throwNullPortalError() {\n  throw Error('Must provide a portal to attach');\n}\n/**\n * Throws an exception when attempting to attach a portal to a host that is already attached.\n * @docs-private\n */\n\n\nfunction throwPortalAlreadyAttachedError() {\n  throw Error('Host already has a portal attached');\n}\n/**\n * Throws an exception when attempting to attach a portal to an already-disposed host.\n * @docs-private\n */\n\n\nfunction throwPortalOutletAlreadyDisposedError() {\n  throw Error('This PortalOutlet has already been disposed');\n}\n/**\n * Throws an exception when attempting to attach an unknown portal type.\n * @docs-private\n */\n\n\nfunction throwUnknownPortalTypeError() {\n  throw Error('Attempting to attach an unknown Portal type. BasePortalOutlet accepts either ' + 'a ComponentPortal or a TemplatePortal.');\n}\n/**\n * Throws an exception when attempting to attach a portal to a null host.\n * @docs-private\n */\n\n\nfunction throwNullPortalOutletError() {\n  throw Error('Attempting to attach a portal to a null PortalOutlet');\n}\n/**\n * Throws an exception when attempting to detach a portal that is not attached.\n * @docs-private\n */\n\n\nfunction throwNoPortalAttachedError() {\n  throw Error('Attempting to detach a portal that is not attached to a host');\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A `Portal` is something that you want to render somewhere else.\n * It can be attach to / detached from a `PortalOutlet`.\n */\n\n\nclass Portal {\n  /** Attach this portal to a host. */\n  attach(host) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (host == null) {\n        throwNullPortalOutletError();\n      }\n\n      if (host.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n    }\n\n    this._attachedHost = host;\n    return host.attach(this);\n  }\n  /** Detach this portal from its host */\n\n\n  detach() {\n    let host = this._attachedHost;\n\n    if (host != null) {\n      this._attachedHost = null;\n      host.detach();\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwNoPortalAttachedError();\n    }\n  }\n  /** Whether this portal is attached to a host. */\n\n\n  get isAttached() {\n    return this._attachedHost != null;\n  }\n  /**\n   * Sets the PortalOutlet reference without performing `attach()`. This is used directly by\n   * the PortalOutlet when it is performing an `attach()` or `detach()`.\n   */\n\n\n  setAttachedHost(host) {\n    this._attachedHost = host;\n  }\n\n}\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\n\n\nclass ComponentPortal extends Portal {\n  constructor(component, viewContainerRef, injector, componentFactoryResolver) {\n    super();\n    this.component = component;\n    this.viewContainerRef = viewContainerRef;\n    this.injector = injector;\n    this.componentFactoryResolver = componentFactoryResolver;\n  }\n\n}\n/**\n * A `TemplatePortal` is a portal that represents some embedded template (TemplateRef).\n */\n\n\nclass TemplatePortal extends Portal {\n  constructor(\n  /** The embedded template that will be used to instantiate an embedded View in the host. */\n  templateRef,\n  /** Reference to the ViewContainer into which the template will be stamped out. */\n  viewContainerRef,\n  /** Contextual data to be passed in to the embedded view. */\n  context,\n  /** The injector to use for the embedded view. */\n  injector) {\n    super();\n    this.templateRef = templateRef;\n    this.viewContainerRef = viewContainerRef;\n    this.context = context;\n    this.injector = injector;\n  }\n\n  get origin() {\n    return this.templateRef.elementRef;\n  }\n  /**\n   * Attach the portal to the provided `PortalOutlet`.\n   * When a context is provided it will override the `context` property of the `TemplatePortal`\n   * instance.\n   */\n\n\n  attach(host, context = this.context) {\n    this.context = context;\n    return super.attach(host);\n  }\n\n  detach() {\n    this.context = undefined;\n    return super.detach();\n  }\n\n}\n/**\n * A `DomPortal` is a portal whose DOM element will be taken from its current position\n * in the DOM and moved into a portal outlet, when it is attached. On detach, the content\n * will be restored to its original position.\n */\n\n\nclass DomPortal extends Portal {\n  constructor(element) {\n    super();\n    this.element = element instanceof ElementRef ? element.nativeElement : element;\n  }\n\n}\n/**\n * Partial implementation of PortalOutlet that handles attaching\n * ComponentPortal and TemplatePortal.\n */\n\n\nclass BasePortalOutlet {\n  constructor() {\n    /** Whether this host has already been permanently disposed. */\n    this._isDisposed = false; // @breaking-change 10.0.0 `attachDomPortal` to become a required abstract method.\n\n    this.attachDomPortal = null;\n  }\n  /** Whether this host has an attached portal. */\n\n\n  hasAttached() {\n    return !!this._attachedPortal;\n  }\n  /** Attaches a portal. */\n\n\n  attach(portal) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!portal) {\n        throwNullPortalError();\n      }\n\n      if (this.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n\n      if (this._isDisposed) {\n        throwPortalOutletAlreadyDisposedError();\n      }\n    }\n\n    if (portal instanceof ComponentPortal) {\n      this._attachedPortal = portal;\n      return this.attachComponentPortal(portal);\n    } else if (portal instanceof TemplatePortal) {\n      this._attachedPortal = portal;\n      return this.attachTemplatePortal(portal); // @breaking-change 10.0.0 remove null check for `this.attachDomPortal`.\n    } else if (this.attachDomPortal && portal instanceof DomPortal) {\n      this._attachedPortal = portal;\n      return this.attachDomPortal(portal);\n    }\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwUnknownPortalTypeError();\n    }\n  }\n  /** Detaches a previously attached portal. */\n\n\n  detach() {\n    if (this._attachedPortal) {\n      this._attachedPortal.setAttachedHost(null);\n\n      this._attachedPortal = null;\n    }\n\n    this._invokeDisposeFn();\n  }\n  /** Permanently dispose of this portal host. */\n\n\n  dispose() {\n    if (this.hasAttached()) {\n      this.detach();\n    }\n\n    this._invokeDisposeFn();\n\n    this._isDisposed = true;\n  }\n  /** @docs-private */\n\n\n  setDisposeFn(fn) {\n    this._disposeFn = fn;\n  }\n\n  _invokeDisposeFn() {\n    if (this._disposeFn) {\n      this._disposeFn();\n\n      this._disposeFn = null;\n    }\n  }\n\n}\n/**\n * @deprecated Use `BasePortalOutlet` instead.\n * @breaking-change 9.0.0\n */\n\n\nclass BasePortalHost extends BasePortalOutlet {}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A PortalOutlet for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n */\n\n\nclass DomPortalOutlet extends BasePortalOutlet {\n  /**\n   * @param outletElement Element into which the content is projected.\n   * @param _componentFactoryResolver Used to resolve the component factory.\n   *   Only required when attaching component portals.\n   * @param _appRef Reference to the application. Only used in component portals when there\n   *   is no `ViewContainerRef` available.\n   * @param _defaultInjector Injector to use as a fallback when the portal being attached doesn't\n   *   have one. Only used for component portals.\n   * @param _document Reference to the document. Used when attaching a DOM portal. Will eventually\n   *   become a required parameter.\n   */\n  constructor(\n  /** Element into which the content is projected. */\n  outletElement, _componentFactoryResolver, _appRef, _defaultInjector,\n  /**\n   * @deprecated `_document` Parameter to be made required.\n   * @breaking-change 10.0.0\n   */\n  _document) {\n    super();\n    this.outletElement = outletElement;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._appRef = _appRef;\n    this._defaultInjector = _defaultInjector;\n    /**\n     * Attaches a DOM portal by transferring its content into the outlet.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n\n    this.attachDomPortal = portal => {\n      // @breaking-change 10.0.0 Remove check and error once the\n      // `_document` constructor parameter is required.\n      if (!this._document && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Cannot attach DOM portal without _document constructor parameter');\n      }\n\n      const element = portal.element;\n\n      if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('DOM portal content must be attached to a parent node.');\n      } // Anchor used to save the element's previous position so\n      // that we can restore it when the portal is detached.\n\n\n      const anchorNode = this._document.createComment('dom-portal');\n\n      element.parentNode.insertBefore(anchorNode, element);\n      this.outletElement.appendChild(element);\n      this._attachedPortal = portal;\n      super.setDisposeFn(() => {\n        // We can't use `replaceWith` here because IE doesn't support it.\n        if (anchorNode.parentNode) {\n          anchorNode.parentNode.replaceChild(element, anchorNode);\n        }\n      });\n    };\n\n    this._document = _document;\n  }\n  /**\n   * Attach the given ComponentPortal to DOM element using the ComponentFactoryResolver.\n   * @param portal Portal to be attached\n   * @returns Reference to the created component.\n   */\n\n\n  attachComponentPortal(portal) {\n    const resolver = portal.componentFactoryResolver || this._componentFactoryResolver;\n\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !resolver) {\n      throw Error('Cannot attach component portal to outlet without a ComponentFactoryResolver.');\n    }\n\n    const componentFactory = resolver.resolveComponentFactory(portal.component);\n    let componentRef; // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n    // for the component (in terms of Angular's component tree, not rendering).\n    // When the ViewContainerRef is missing, we use the factory to create the component directly\n    // and then manually attach the view to the application.\n\n    if (portal.viewContainerRef) {\n      componentRef = portal.viewContainerRef.createComponent(componentFactory, portal.viewContainerRef.length, portal.injector || portal.viewContainerRef.injector);\n      this.setDisposeFn(() => componentRef.destroy());\n    } else {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._appRef) {\n        throw Error('Cannot attach component portal to outlet without an ApplicationRef.');\n      }\n\n      componentRef = componentFactory.create(portal.injector || this._defaultInjector || Injector.NULL);\n\n      this._appRef.attachView(componentRef.hostView);\n\n      this.setDisposeFn(() => {\n        // Verify that the ApplicationRef has registered views before trying to detach a host view.\n        // This check also protects the `detachView` from being called on a destroyed ApplicationRef.\n        if (this._appRef.viewCount > 0) {\n          this._appRef.detachView(componentRef.hostView);\n        }\n\n        componentRef.destroy();\n      });\n    } // At this point the component has been instantiated, so we move it to the location in the DOM\n    // where we want it to be rendered.\n\n\n    this.outletElement.appendChild(this._getComponentRootNode(componentRef));\n    this._attachedPortal = portal;\n    return componentRef;\n  }\n  /**\n   * Attaches a template portal to the DOM as an embedded view.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n\n\n  attachTemplatePortal(portal) {\n    let viewContainer = portal.viewContainerRef;\n    let viewRef = viewContainer.createEmbeddedView(portal.templateRef, portal.context, {\n      injector: portal.injector\n    }); // The method `createEmbeddedView` will add the view as a child of the viewContainer.\n    // But for the DomPortalOutlet the view can be added everywhere in the DOM\n    // (e.g Overlay Container) To move the view to the specified host element. We just\n    // re-append the existing root nodes.\n\n    viewRef.rootNodes.forEach(rootNode => this.outletElement.appendChild(rootNode)); // Note that we want to detect changes after the nodes have been moved so that\n    // any directives inside the portal that are looking at the DOM inside a lifecycle\n    // hook won't be invoked too early.\n\n    viewRef.detectChanges();\n    this.setDisposeFn(() => {\n      let index = viewContainer.indexOf(viewRef);\n\n      if (index !== -1) {\n        viewContainer.remove(index);\n      }\n    });\n    this._attachedPortal = portal; // TODO(jelbourn): Return locals from view.\n\n    return viewRef;\n  }\n  /**\n   * Clears out a portal from the DOM.\n   */\n\n\n  dispose() {\n    super.dispose();\n    this.outletElement.remove();\n  }\n  /** Gets the root HTMLElement for an instantiated component. */\n\n\n  _getComponentRootNode(componentRef) {\n    return componentRef.hostView.rootNodes[0];\n  }\n\n}\n/**\n * @deprecated Use `DomPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\n\n\nclass DomPortalHost extends DomPortalOutlet {}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Directive version of a `TemplatePortal`. Because the directive *is* a TemplatePortal,\n * the directive instance itself can be attached to a host, enabling declarative use of portals.\n */\n\n\nclass CdkPortal extends TemplatePortal {\n  constructor(templateRef, viewContainerRef) {\n    super(templateRef, viewContainerRef);\n  }\n\n}\n\nCdkPortal.ɵfac = function CdkPortal_Factory(t) {\n  return new (t || CdkPortal)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n};\n\nCdkPortal.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkPortal,\n  selectors: [[\"\", \"cdkPortal\", \"\"]],\n  exportAs: [\"cdkPortal\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkPortal, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortal]',\n      exportAs: 'cdkPortal'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }, {\n      type: i0.ViewContainerRef\n    }];\n  }, null);\n})();\n/**\n * @deprecated Use `CdkPortal` instead.\n * @breaking-change 9.0.0\n */\n\n\nclass TemplatePortalDirective extends CdkPortal {}\n\nTemplatePortalDirective.ɵfac = /* @__PURE__ */function () {\n  let ɵTemplatePortalDirective_BaseFactory;\n  return function TemplatePortalDirective_Factory(t) {\n    return (ɵTemplatePortalDirective_BaseFactory || (ɵTemplatePortalDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TemplatePortalDirective)))(t || TemplatePortalDirective);\n  };\n}();\n\nTemplatePortalDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: TemplatePortalDirective,\n  selectors: [[\"\", \"cdk-portal\", \"\"], [\"\", \"portal\", \"\"]],\n  exportAs: [\"cdkPortal\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkPortal,\n    useExisting: TemplatePortalDirective\n  }]), i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TemplatePortalDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-portal], [portal]',\n      exportAs: 'cdkPortal',\n      providers: [{\n        provide: CdkPortal,\n        useExisting: TemplatePortalDirective\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Directive version of a PortalOutlet. Because the directive *is* a PortalOutlet, portals can be\n * directly attached to it, enabling declarative use.\n *\n * Usage:\n * `<ng-template [cdkPortalOutlet]=\"greeting\"></ng-template>`\n */\n\n\nclass CdkPortalOutlet extends BasePortalOutlet {\n  constructor(_componentFactoryResolver, _viewContainerRef,\n  /**\n   * @deprecated `_document` parameter to be made required.\n   * @breaking-change 9.0.0\n   */\n  _document) {\n    super();\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._viewContainerRef = _viewContainerRef;\n    /** Whether the portal component is initialized. */\n\n    this._isInitialized = false;\n    /** Emits when a portal is attached to the outlet. */\n\n    this.attached = new EventEmitter();\n    /**\n     * Attaches the given DomPortal to this PortalHost by moving all of the portal content into it.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n\n    this.attachDomPortal = portal => {\n      // @breaking-change 9.0.0 Remove check and error once the\n      // `_document` constructor parameter is required.\n      if (!this._document && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Cannot attach DOM portal without _document constructor parameter');\n      }\n\n      const element = portal.element;\n\n      if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('DOM portal content must be attached to a parent node.');\n      } // Anchor used to save the element's previous position so\n      // that we can restore it when the portal is detached.\n\n\n      const anchorNode = this._document.createComment('dom-portal');\n\n      portal.setAttachedHost(this);\n      element.parentNode.insertBefore(anchorNode, element);\n\n      this._getRootNode().appendChild(element);\n\n      this._attachedPortal = portal;\n      super.setDisposeFn(() => {\n        if (anchorNode.parentNode) {\n          anchorNode.parentNode.replaceChild(element, anchorNode);\n        }\n      });\n    };\n\n    this._document = _document;\n  }\n  /** Portal associated with the Portal outlet. */\n\n\n  get portal() {\n    return this._attachedPortal;\n  }\n\n  set portal(portal) {\n    // Ignore the cases where the `portal` is set to a falsy value before the lifecycle hooks have\n    // run. This handles the cases where the user might do something like `<div cdkPortalOutlet>`\n    // and attach a portal programmatically in the parent component. When Angular does the first CD\n    // round, it will fire the setter with empty string, causing the user's content to be cleared.\n    if (this.hasAttached() && !portal && !this._isInitialized) {\n      return;\n    }\n\n    if (this.hasAttached()) {\n      super.detach();\n    }\n\n    if (portal) {\n      super.attach(portal);\n    }\n\n    this._attachedPortal = portal || null;\n  }\n  /** Component or view reference that is attached to the portal. */\n\n\n  get attachedRef() {\n    return this._attachedRef;\n  }\n\n  ngOnInit() {\n    this._isInitialized = true;\n  }\n\n  ngOnDestroy() {\n    super.dispose();\n    this._attachedPortal = null;\n    this._attachedRef = null;\n  }\n  /**\n   * Attach the given ComponentPortal to this PortalOutlet using the ComponentFactoryResolver.\n   *\n   * @param portal Portal to be attached to the portal outlet.\n   * @returns Reference to the created component.\n   */\n\n\n  attachComponentPortal(portal) {\n    portal.setAttachedHost(this); // If the portal specifies an origin, use that as the logical location of the component\n    // in the application tree. Otherwise use the location of this PortalOutlet.\n\n    const viewContainerRef = portal.viewContainerRef != null ? portal.viewContainerRef : this._viewContainerRef;\n    const resolver = portal.componentFactoryResolver || this._componentFactoryResolver;\n    const componentFactory = resolver.resolveComponentFactory(portal.component);\n    const ref = viewContainerRef.createComponent(componentFactory, viewContainerRef.length, portal.injector || viewContainerRef.injector); // If we're using a view container that's different from the injected one (e.g. when the portal\n    // specifies its own) we need to move the component into the outlet, otherwise it'll be rendered\n    // inside of the alternate view container.\n\n    if (viewContainerRef !== this._viewContainerRef) {\n      this._getRootNode().appendChild(ref.hostView.rootNodes[0]);\n    }\n\n    super.setDisposeFn(() => ref.destroy());\n    this._attachedPortal = portal;\n    this._attachedRef = ref;\n    this.attached.emit(ref);\n    return ref;\n  }\n  /**\n   * Attach the given TemplatePortal to this PortalHost as an embedded View.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n\n\n  attachTemplatePortal(portal) {\n    portal.setAttachedHost(this);\n\n    const viewRef = this._viewContainerRef.createEmbeddedView(portal.templateRef, portal.context, {\n      injector: portal.injector\n    });\n\n    super.setDisposeFn(() => this._viewContainerRef.clear());\n    this._attachedPortal = portal;\n    this._attachedRef = viewRef;\n    this.attached.emit(viewRef);\n    return viewRef;\n  }\n  /** Gets the root node of the portal outlet. */\n\n\n  _getRootNode() {\n    const nativeElement = this._viewContainerRef.element.nativeElement; // The directive could be set on a template which will result in a comment\n    // node being the root. Use the comment's parent node if that is the case.\n\n    return nativeElement.nodeType === nativeElement.ELEMENT_NODE ? nativeElement : nativeElement.parentNode;\n  }\n\n}\n\nCdkPortalOutlet.ɵfac = function CdkPortalOutlet_Factory(t) {\n  return new (t || CdkPortalOutlet)(i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DOCUMENT));\n};\n\nCdkPortalOutlet.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkPortalOutlet,\n  selectors: [[\"\", \"cdkPortalOutlet\", \"\"]],\n  inputs: {\n    portal: [\"cdkPortalOutlet\", \"portal\"]\n  },\n  outputs: {\n    attached: \"attached\"\n  },\n  exportAs: [\"cdkPortalOutlet\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkPortalOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortalOutlet]',\n      exportAs: 'cdkPortalOutlet',\n      inputs: ['portal: cdkPortalOutlet']\n    }]\n  }], function () {\n    return [{\n      type: i0.ComponentFactoryResolver\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, {\n    attached: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * @deprecated Use `CdkPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\n\n\nclass PortalHostDirective extends CdkPortalOutlet {}\n\nPortalHostDirective.ɵfac = /* @__PURE__ */function () {\n  let ɵPortalHostDirective_BaseFactory;\n  return function PortalHostDirective_Factory(t) {\n    return (ɵPortalHostDirective_BaseFactory || (ɵPortalHostDirective_BaseFactory = i0.ɵɵgetInheritedFactory(PortalHostDirective)))(t || PortalHostDirective);\n  };\n}();\n\nPortalHostDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: PortalHostDirective,\n  selectors: [[\"\", \"cdkPortalHost\", \"\"], [\"\", \"portalHost\", \"\"]],\n  inputs: {\n    portal: [\"cdkPortalHost\", \"portal\"]\n  },\n  exportAs: [\"cdkPortalHost\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkPortalOutlet,\n    useExisting: PortalHostDirective\n  }]), i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PortalHostDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortalHost], [portalHost]',\n      exportAs: 'cdkPortalHost',\n      inputs: ['portal: cdkPortalHost'],\n      providers: [{\n        provide: CdkPortalOutlet,\n        useExisting: PortalHostDirective\n      }]\n    }]\n  }], null, null);\n})();\n\nclass PortalModule {}\n\nPortalModule.ɵfac = function PortalModule_Factory(t) {\n  return new (t || PortalModule)();\n};\n\nPortalModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: PortalModule,\n  declarations: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n  exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective]\n});\nPortalModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PortalModule, [{\n    type: NgModule,\n    args: [{\n      exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n      declarations: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Custom injector to be used when providing custom\n * injection tokens to components inside a portal.\n * @docs-private\n * @deprecated Use `Injector.create` instead.\n * @breaking-change 11.0.0\n */\n\n\nclass PortalInjector {\n  constructor(_parentInjector, _customTokens) {\n    this._parentInjector = _parentInjector;\n    this._customTokens = _customTokens;\n  }\n\n  get(token, notFoundValue) {\n    const value = this._customTokens.get(token);\n\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n\n    return this._parentInjector.get(token, notFoundValue);\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { BasePortalHost, BasePortalOutlet, CdkPortal, CdkPortalOutlet, ComponentPortal, DomPortal, DomPortalHost, DomPortalOutlet, Portal, PortalHostDirective, PortalInjector, PortalModule, TemplatePortal, TemplatePortalDirective };", "map": {"version": 3, "names": ["i0", "ElementRef", "Injector", "Directive", "EventEmitter", "Inject", "Output", "NgModule", "DOCUMENT", "throwNullPortalError", "Error", "throwPortalAlreadyAttachedError", "throwPortalOutletAlreadyDisposedError", "throwUnknownPortalTypeError", "throwNullPortalOutletError", "throwNoPortalAttachedError", "Portal", "attach", "host", "ngDevMode", "has<PERSON>tta<PERSON>", "_attachedHost", "detach", "isAttached", "setAttachedHost", "ComponentPortal", "constructor", "component", "viewContainerRef", "injector", "componentFactoryResolver", "TemplatePortal", "templateRef", "context", "origin", "elementRef", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "element", "nativeElement", "BasePortalOutlet", "_isDisposed", "attachDomPortal", "_attachedPortal", "portal", "attachComponentPortal", "attachTemplatePortal", "_invokeDisposeFn", "dispose", "setDisposeFn", "fn", "_disposeFn", "BasePortalHost", "DomPortalOutlet", "outletElement", "_componentFactoryResolver", "_appRef", "_defaultInjector", "_document", "parentNode", "anchorNode", "createComment", "insertBefore", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "resolver", "componentFactory", "resolveComponentFactory", "componentRef", "createComponent", "length", "destroy", "create", "NULL", "attachView", "<PERSON><PERSON><PERSON><PERSON>", "viewCount", "detach<PERSON>iew", "_getComponentRootNode", "viewContainer", "viewRef", "createEmbeddedView", "rootNodes", "for<PERSON>ach", "rootNode", "detectChanges", "index", "indexOf", "remove", "DomPortalHost", "CdkPortal", "ɵfac", "TemplateRef", "ViewContainerRef", "ɵdir", "type", "args", "selector", "exportAs", "TemplatePortalDirective", "provide", "useExisting", "providers", "CdkPortalOutlet", "_viewContainerRef", "_isInitialized", "attached", "_getRootNode", "attachedRef", "_attachedRef", "ngOnInit", "ngOnDestroy", "ref", "emit", "clear", "nodeType", "ELEMENT_NODE", "ComponentFactoryResolver", "inputs", "decorators", "PortalHostDirective", "PortalModule", "ɵmod", "ɵinj", "exports", "declarations", "PortalInjector", "_parentInjector", "_customTokens", "get", "token", "notFoundValue", "value"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/cdk/fesm2020/portal.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { ElementRef, Injector, Directive, EventEmitter, Inject, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Throws an exception when attempting to attach a null portal to a host.\n * @docs-private\n */\nfunction throwNullPortalError() {\n    throw Error('Must provide a portal to attach');\n}\n/**\n * Throws an exception when attempting to attach a portal to a host that is already attached.\n * @docs-private\n */\nfunction throwPortalAlreadyAttachedError() {\n    throw Error('Host already has a portal attached');\n}\n/**\n * Throws an exception when attempting to attach a portal to an already-disposed host.\n * @docs-private\n */\nfunction throwPortalOutletAlreadyDisposedError() {\n    throw Error('This PortalOutlet has already been disposed');\n}\n/**\n * Throws an exception when attempting to attach an unknown portal type.\n * @docs-private\n */\nfunction throwUnknownPortalTypeError() {\n    throw Error('Attempting to attach an unknown Portal type. BasePortalOutlet accepts either ' +\n        'a ComponentPortal or a TemplatePortal.');\n}\n/**\n * Throws an exception when attempting to attach a portal to a null host.\n * @docs-private\n */\nfunction throwNullPortalOutletError() {\n    throw Error('Attempting to attach a portal to a null PortalOutlet');\n}\n/**\n * Throws an exception when attempting to detach a portal that is not attached.\n * @docs-private\n */\nfunction throwNoPortalAttachedError() {\n    throw Error('Attempting to detach a portal that is not attached to a host');\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * A `Portal` is something that you want to render somewhere else.\n * It can be attach to / detached from a `PortalOutlet`.\n */\nclass Portal {\n    /** Attach this portal to a host. */\n    attach(host) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (host == null) {\n                throwNullPortalOutletError();\n            }\n            if (host.hasAttached()) {\n                throwPortalAlreadyAttachedError();\n            }\n        }\n        this._attachedHost = host;\n        return host.attach(this);\n    }\n    /** Detach this portal from its host */\n    detach() {\n        let host = this._attachedHost;\n        if (host != null) {\n            this._attachedHost = null;\n            host.detach();\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throwNoPortalAttachedError();\n        }\n    }\n    /** Whether this portal is attached to a host. */\n    get isAttached() {\n        return this._attachedHost != null;\n    }\n    /**\n     * Sets the PortalOutlet reference without performing `attach()`. This is used directly by\n     * the PortalOutlet when it is performing an `attach()` or `detach()`.\n     */\n    setAttachedHost(host) {\n        this._attachedHost = host;\n    }\n}\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nclass ComponentPortal extends Portal {\n    constructor(component, viewContainerRef, injector, componentFactoryResolver) {\n        super();\n        this.component = component;\n        this.viewContainerRef = viewContainerRef;\n        this.injector = injector;\n        this.componentFactoryResolver = componentFactoryResolver;\n    }\n}\n/**\n * A `TemplatePortal` is a portal that represents some embedded template (TemplateRef).\n */\nclass TemplatePortal extends Portal {\n    constructor(\n    /** The embedded template that will be used to instantiate an embedded View in the host. */\n    templateRef, \n    /** Reference to the ViewContainer into which the template will be stamped out. */\n    viewContainerRef, \n    /** Contextual data to be passed in to the embedded view. */\n    context, \n    /** The injector to use for the embedded view. */\n    injector) {\n        super();\n        this.templateRef = templateRef;\n        this.viewContainerRef = viewContainerRef;\n        this.context = context;\n        this.injector = injector;\n    }\n    get origin() {\n        return this.templateRef.elementRef;\n    }\n    /**\n     * Attach the portal to the provided `PortalOutlet`.\n     * When a context is provided it will override the `context` property of the `TemplatePortal`\n     * instance.\n     */\n    attach(host, context = this.context) {\n        this.context = context;\n        return super.attach(host);\n    }\n    detach() {\n        this.context = undefined;\n        return super.detach();\n    }\n}\n/**\n * A `DomPortal` is a portal whose DOM element will be taken from its current position\n * in the DOM and moved into a portal outlet, when it is attached. On detach, the content\n * will be restored to its original position.\n */\nclass DomPortal extends Portal {\n    constructor(element) {\n        super();\n        this.element = element instanceof ElementRef ? element.nativeElement : element;\n    }\n}\n/**\n * Partial implementation of PortalOutlet that handles attaching\n * ComponentPortal and TemplatePortal.\n */\nclass BasePortalOutlet {\n    constructor() {\n        /** Whether this host has already been permanently disposed. */\n        this._isDisposed = false;\n        // @breaking-change 10.0.0 `attachDomPortal` to become a required abstract method.\n        this.attachDomPortal = null;\n    }\n    /** Whether this host has an attached portal. */\n    hasAttached() {\n        return !!this._attachedPortal;\n    }\n    /** Attaches a portal. */\n    attach(portal) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (!portal) {\n                throwNullPortalError();\n            }\n            if (this.hasAttached()) {\n                throwPortalAlreadyAttachedError();\n            }\n            if (this._isDisposed) {\n                throwPortalOutletAlreadyDisposedError();\n            }\n        }\n        if (portal instanceof ComponentPortal) {\n            this._attachedPortal = portal;\n            return this.attachComponentPortal(portal);\n        }\n        else if (portal instanceof TemplatePortal) {\n            this._attachedPortal = portal;\n            return this.attachTemplatePortal(portal);\n            // @breaking-change 10.0.0 remove null check for `this.attachDomPortal`.\n        }\n        else if (this.attachDomPortal && portal instanceof DomPortal) {\n            this._attachedPortal = portal;\n            return this.attachDomPortal(portal);\n        }\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throwUnknownPortalTypeError();\n        }\n    }\n    /** Detaches a previously attached portal. */\n    detach() {\n        if (this._attachedPortal) {\n            this._attachedPortal.setAttachedHost(null);\n            this._attachedPortal = null;\n        }\n        this._invokeDisposeFn();\n    }\n    /** Permanently dispose of this portal host. */\n    dispose() {\n        if (this.hasAttached()) {\n            this.detach();\n        }\n        this._invokeDisposeFn();\n        this._isDisposed = true;\n    }\n    /** @docs-private */\n    setDisposeFn(fn) {\n        this._disposeFn = fn;\n    }\n    _invokeDisposeFn() {\n        if (this._disposeFn) {\n            this._disposeFn();\n            this._disposeFn = null;\n        }\n    }\n}\n/**\n * @deprecated Use `BasePortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass BasePortalHost extends BasePortalOutlet {\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * A PortalOutlet for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n */\nclass DomPortalOutlet extends BasePortalOutlet {\n    /**\n     * @param outletElement Element into which the content is projected.\n     * @param _componentFactoryResolver Used to resolve the component factory.\n     *   Only required when attaching component portals.\n     * @param _appRef Reference to the application. Only used in component portals when there\n     *   is no `ViewContainerRef` available.\n     * @param _defaultInjector Injector to use as a fallback when the portal being attached doesn't\n     *   have one. Only used for component portals.\n     * @param _document Reference to the document. Used when attaching a DOM portal. Will eventually\n     *   become a required parameter.\n     */\n    constructor(\n    /** Element into which the content is projected. */\n    outletElement, _componentFactoryResolver, _appRef, _defaultInjector, \n    /**\n     * @deprecated `_document` Parameter to be made required.\n     * @breaking-change 10.0.0\n     */\n    _document) {\n        super();\n        this.outletElement = outletElement;\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._appRef = _appRef;\n        this._defaultInjector = _defaultInjector;\n        /**\n         * Attaches a DOM portal by transferring its content into the outlet.\n         * @param portal Portal to be attached.\n         * @deprecated To be turned into a method.\n         * @breaking-change 10.0.0\n         */\n        this.attachDomPortal = (portal) => {\n            // @breaking-change 10.0.0 Remove check and error once the\n            // `_document` constructor parameter is required.\n            if (!this._document && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('Cannot attach DOM portal without _document constructor parameter');\n            }\n            const element = portal.element;\n            if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('DOM portal content must be attached to a parent node.');\n            }\n            // Anchor used to save the element's previous position so\n            // that we can restore it when the portal is detached.\n            const anchorNode = this._document.createComment('dom-portal');\n            element.parentNode.insertBefore(anchorNode, element);\n            this.outletElement.appendChild(element);\n            this._attachedPortal = portal;\n            super.setDisposeFn(() => {\n                // We can't use `replaceWith` here because IE doesn't support it.\n                if (anchorNode.parentNode) {\n                    anchorNode.parentNode.replaceChild(element, anchorNode);\n                }\n            });\n        };\n        this._document = _document;\n    }\n    /**\n     * Attach the given ComponentPortal to DOM element using the ComponentFactoryResolver.\n     * @param portal Portal to be attached\n     * @returns Reference to the created component.\n     */\n    attachComponentPortal(portal) {\n        const resolver = (portal.componentFactoryResolver || this._componentFactoryResolver);\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && !resolver) {\n            throw Error('Cannot attach component portal to outlet without a ComponentFactoryResolver.');\n        }\n        const componentFactory = resolver.resolveComponentFactory(portal.component);\n        let componentRef;\n        // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n        // for the component (in terms of Angular's component tree, not rendering).\n        // When the ViewContainerRef is missing, we use the factory to create the component directly\n        // and then manually attach the view to the application.\n        if (portal.viewContainerRef) {\n            componentRef = portal.viewContainerRef.createComponent(componentFactory, portal.viewContainerRef.length, portal.injector || portal.viewContainerRef.injector);\n            this.setDisposeFn(() => componentRef.destroy());\n        }\n        else {\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._appRef) {\n                throw Error('Cannot attach component portal to outlet without an ApplicationRef.');\n            }\n            componentRef = componentFactory.create(portal.injector || this._defaultInjector || Injector.NULL);\n            this._appRef.attachView(componentRef.hostView);\n            this.setDisposeFn(() => {\n                // Verify that the ApplicationRef has registered views before trying to detach a host view.\n                // This check also protects the `detachView` from being called on a destroyed ApplicationRef.\n                if (this._appRef.viewCount > 0) {\n                    this._appRef.detachView(componentRef.hostView);\n                }\n                componentRef.destroy();\n            });\n        }\n        // At this point the component has been instantiated, so we move it to the location in the DOM\n        // where we want it to be rendered.\n        this.outletElement.appendChild(this._getComponentRootNode(componentRef));\n        this._attachedPortal = portal;\n        return componentRef;\n    }\n    /**\n     * Attaches a template portal to the DOM as an embedded view.\n     * @param portal Portal to be attached.\n     * @returns Reference to the created embedded view.\n     */\n    attachTemplatePortal(portal) {\n        let viewContainer = portal.viewContainerRef;\n        let viewRef = viewContainer.createEmbeddedView(portal.templateRef, portal.context, {\n            injector: portal.injector,\n        });\n        // The method `createEmbeddedView` will add the view as a child of the viewContainer.\n        // But for the DomPortalOutlet the view can be added everywhere in the DOM\n        // (e.g Overlay Container) To move the view to the specified host element. We just\n        // re-append the existing root nodes.\n        viewRef.rootNodes.forEach(rootNode => this.outletElement.appendChild(rootNode));\n        // Note that we want to detect changes after the nodes have been moved so that\n        // any directives inside the portal that are looking at the DOM inside a lifecycle\n        // hook won't be invoked too early.\n        viewRef.detectChanges();\n        this.setDisposeFn(() => {\n            let index = viewContainer.indexOf(viewRef);\n            if (index !== -1) {\n                viewContainer.remove(index);\n            }\n        });\n        this._attachedPortal = portal;\n        // TODO(jelbourn): Return locals from view.\n        return viewRef;\n    }\n    /**\n     * Clears out a portal from the DOM.\n     */\n    dispose() {\n        super.dispose();\n        this.outletElement.remove();\n    }\n    /** Gets the root HTMLElement for an instantiated component. */\n    _getComponentRootNode(componentRef) {\n        return componentRef.hostView.rootNodes[0];\n    }\n}\n/**\n * @deprecated Use `DomPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass DomPortalHost extends DomPortalOutlet {\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Directive version of a `TemplatePortal`. Because the directive *is* a TemplatePortal,\n * the directive instance itself can be attached to a host, enabling declarative use of portals.\n */\nclass CdkPortal extends TemplatePortal {\n    constructor(templateRef, viewContainerRef) {\n        super(templateRef, viewContainerRef);\n    }\n}\nCdkPortal.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkPortal, deps: [{ token: i0.TemplateRef }, { token: i0.ViewContainerRef }], target: i0.ɵɵFactoryTarget.Directive });\nCdkPortal.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: CdkPortal, selector: \"[cdkPortal]\", exportAs: [\"cdkPortal\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkPortal, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkPortal]',\n                    exportAs: 'cdkPortal',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }, { type: i0.ViewContainerRef }]; } });\n/**\n * @deprecated Use `CdkPortal` instead.\n * @breaking-change 9.0.0\n */\nclass TemplatePortalDirective extends CdkPortal {\n}\nTemplatePortalDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: TemplatePortalDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nTemplatePortalDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: TemplatePortalDirective, selector: \"[cdk-portal], [portal]\", providers: [\n        {\n            provide: CdkPortal,\n            useExisting: TemplatePortalDirective,\n        },\n    ], exportAs: [\"cdkPortal\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: TemplatePortalDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-portal], [portal]',\n                    exportAs: 'cdkPortal',\n                    providers: [\n                        {\n                            provide: CdkPortal,\n                            useExisting: TemplatePortalDirective,\n                        },\n                    ],\n                }]\n        }] });\n/**\n * Directive version of a PortalOutlet. Because the directive *is* a PortalOutlet, portals can be\n * directly attached to it, enabling declarative use.\n *\n * Usage:\n * `<ng-template [cdkPortalOutlet]=\"greeting\"></ng-template>`\n */\nclass CdkPortalOutlet extends BasePortalOutlet {\n    constructor(_componentFactoryResolver, _viewContainerRef, \n    /**\n     * @deprecated `_document` parameter to be made required.\n     * @breaking-change 9.0.0\n     */\n    _document) {\n        super();\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._viewContainerRef = _viewContainerRef;\n        /** Whether the portal component is initialized. */\n        this._isInitialized = false;\n        /** Emits when a portal is attached to the outlet. */\n        this.attached = new EventEmitter();\n        /**\n         * Attaches the given DomPortal to this PortalHost by moving all of the portal content into it.\n         * @param portal Portal to be attached.\n         * @deprecated To be turned into a method.\n         * @breaking-change 10.0.0\n         */\n        this.attachDomPortal = (portal) => {\n            // @breaking-change 9.0.0 Remove check and error once the\n            // `_document` constructor parameter is required.\n            if (!this._document && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('Cannot attach DOM portal without _document constructor parameter');\n            }\n            const element = portal.element;\n            if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('DOM portal content must be attached to a parent node.');\n            }\n            // Anchor used to save the element's previous position so\n            // that we can restore it when the portal is detached.\n            const anchorNode = this._document.createComment('dom-portal');\n            portal.setAttachedHost(this);\n            element.parentNode.insertBefore(anchorNode, element);\n            this._getRootNode().appendChild(element);\n            this._attachedPortal = portal;\n            super.setDisposeFn(() => {\n                if (anchorNode.parentNode) {\n                    anchorNode.parentNode.replaceChild(element, anchorNode);\n                }\n            });\n        };\n        this._document = _document;\n    }\n    /** Portal associated with the Portal outlet. */\n    get portal() {\n        return this._attachedPortal;\n    }\n    set portal(portal) {\n        // Ignore the cases where the `portal` is set to a falsy value before the lifecycle hooks have\n        // run. This handles the cases where the user might do something like `<div cdkPortalOutlet>`\n        // and attach a portal programmatically in the parent component. When Angular does the first CD\n        // round, it will fire the setter with empty string, causing the user's content to be cleared.\n        if (this.hasAttached() && !portal && !this._isInitialized) {\n            return;\n        }\n        if (this.hasAttached()) {\n            super.detach();\n        }\n        if (portal) {\n            super.attach(portal);\n        }\n        this._attachedPortal = portal || null;\n    }\n    /** Component or view reference that is attached to the portal. */\n    get attachedRef() {\n        return this._attachedRef;\n    }\n    ngOnInit() {\n        this._isInitialized = true;\n    }\n    ngOnDestroy() {\n        super.dispose();\n        this._attachedPortal = null;\n        this._attachedRef = null;\n    }\n    /**\n     * Attach the given ComponentPortal to this PortalOutlet using the ComponentFactoryResolver.\n     *\n     * @param portal Portal to be attached to the portal outlet.\n     * @returns Reference to the created component.\n     */\n    attachComponentPortal(portal) {\n        portal.setAttachedHost(this);\n        // If the portal specifies an origin, use that as the logical location of the component\n        // in the application tree. Otherwise use the location of this PortalOutlet.\n        const viewContainerRef = portal.viewContainerRef != null ? portal.viewContainerRef : this._viewContainerRef;\n        const resolver = portal.componentFactoryResolver || this._componentFactoryResolver;\n        const componentFactory = resolver.resolveComponentFactory(portal.component);\n        const ref = viewContainerRef.createComponent(componentFactory, viewContainerRef.length, portal.injector || viewContainerRef.injector);\n        // If we're using a view container that's different from the injected one (e.g. when the portal\n        // specifies its own) we need to move the component into the outlet, otherwise it'll be rendered\n        // inside of the alternate view container.\n        if (viewContainerRef !== this._viewContainerRef) {\n            this._getRootNode().appendChild(ref.hostView.rootNodes[0]);\n        }\n        super.setDisposeFn(() => ref.destroy());\n        this._attachedPortal = portal;\n        this._attachedRef = ref;\n        this.attached.emit(ref);\n        return ref;\n    }\n    /**\n     * Attach the given TemplatePortal to this PortalHost as an embedded View.\n     * @param portal Portal to be attached.\n     * @returns Reference to the created embedded view.\n     */\n    attachTemplatePortal(portal) {\n        portal.setAttachedHost(this);\n        const viewRef = this._viewContainerRef.createEmbeddedView(portal.templateRef, portal.context, {\n            injector: portal.injector,\n        });\n        super.setDisposeFn(() => this._viewContainerRef.clear());\n        this._attachedPortal = portal;\n        this._attachedRef = viewRef;\n        this.attached.emit(viewRef);\n        return viewRef;\n    }\n    /** Gets the root node of the portal outlet. */\n    _getRootNode() {\n        const nativeElement = this._viewContainerRef.element.nativeElement;\n        // The directive could be set on a template which will result in a comment\n        // node being the root. Use the comment's parent node if that is the case.\n        return (nativeElement.nodeType === nativeElement.ELEMENT_NODE\n            ? nativeElement\n            : nativeElement.parentNode);\n    }\n}\nCdkPortalOutlet.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkPortalOutlet, deps: [{ token: i0.ComponentFactoryResolver }, { token: i0.ViewContainerRef }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive });\nCdkPortalOutlet.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: { portal: [\"cdkPortalOutlet\", \"portal\"] }, outputs: { attached: \"attached\" }, exportAs: [\"cdkPortalOutlet\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkPortalOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkPortalOutlet]',\n                    exportAs: 'cdkPortalOutlet',\n                    inputs: ['portal: cdkPortalOutlet'],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ComponentFactoryResolver }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; }, propDecorators: { attached: [{\n                type: Output\n            }] } });\n/**\n * @deprecated Use `CdkPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass PortalHostDirective extends CdkPortalOutlet {\n}\nPortalHostDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: PortalHostDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nPortalHostDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: PortalHostDirective, selector: \"[cdkPortalHost], [portalHost]\", inputs: { portal: [\"cdkPortalHost\", \"portal\"] }, providers: [\n        {\n            provide: CdkPortalOutlet,\n            useExisting: PortalHostDirective,\n        },\n    ], exportAs: [\"cdkPortalHost\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: PortalHostDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkPortalHost], [portalHost]',\n                    exportAs: 'cdkPortalHost',\n                    inputs: ['portal: cdkPortalHost'],\n                    providers: [\n                        {\n                            provide: CdkPortalOutlet,\n                            useExisting: PortalHostDirective,\n                        },\n                    ],\n                }]\n        }] });\nclass PortalModule {\n}\nPortalModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: PortalModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nPortalModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: PortalModule, declarations: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective], exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective] });\nPortalModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: PortalModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: PortalModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n                    declarations: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Custom injector to be used when providing custom\n * injection tokens to components inside a portal.\n * @docs-private\n * @deprecated Use `Injector.create` instead.\n * @breaking-change 11.0.0\n */\nclass PortalInjector {\n    constructor(_parentInjector, _customTokens) {\n        this._parentInjector = _parentInjector;\n        this._customTokens = _customTokens;\n    }\n    get(token, notFoundValue) {\n        const value = this._customTokens.get(token);\n        if (typeof value !== 'undefined') {\n            return value;\n        }\n        return this._parentInjector.get(token, notFoundValue);\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BasePortalHost, BasePortalOutlet, CdkPortal, CdkPortalOutlet, ComponentPortal, DomPortal, DomPortalHost, DomPortalOutlet, Portal, PortalHostDirective, PortalInjector, PortalModule, TemplatePortal, TemplatePortalDirective };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,QAArB,EAA+BC,SAA/B,EAA0CC,YAA1C,EAAwDC,MAAxD,EAAgEC,MAAhE,EAAwEC,QAAxE,QAAwF,eAAxF;AACA,SAASC,QAAT,QAAyB,iBAAzB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;AACA,SAASC,oBAAT,GAAgC;EAC5B,MAAMC,KAAK,CAAC,iCAAD,CAAX;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASC,+BAAT,GAA2C;EACvC,MAAMD,KAAK,CAAC,oCAAD,CAAX;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASE,qCAAT,GAAiD;EAC7C,MAAMF,KAAK,CAAC,6CAAD,CAAX;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASG,2BAAT,GAAuC;EACnC,MAAMH,KAAK,CAAC,kFACR,wCADO,CAAX;AAEH;AACD;AACA;AACA;AACA;;;AACA,SAASI,0BAAT,GAAsC;EAClC,MAAMJ,KAAK,CAAC,sDAAD,CAAX;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASK,0BAAT,GAAsC;EAClC,MAAML,KAAK,CAAC,8DAAD,CAAX;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMM,MAAN,CAAa;EACT;EACAC,MAAM,CAACC,IAAD,EAAO;IACT,IAAI,OAAOC,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;MAC/C,IAAID,IAAI,IAAI,IAAZ,EAAkB;QACdJ,0BAA0B;MAC7B;;MACD,IAAII,IAAI,CAACE,WAAL,EAAJ,EAAwB;QACpBT,+BAA+B;MAClC;IACJ;;IACD,KAAKU,aAAL,GAAqBH,IAArB;IACA,OAAOA,IAAI,CAACD,MAAL,CAAY,IAAZ,CAAP;EACH;EACD;;;EACAK,MAAM,GAAG;IACL,IAAIJ,IAAI,GAAG,KAAKG,aAAhB;;IACA,IAAIH,IAAI,IAAI,IAAZ,EAAkB;MACd,KAAKG,aAAL,GAAqB,IAArB;MACAH,IAAI,CAACI,MAAL;IACH,CAHD,MAIK,IAAI,OAAOH,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;MACpDJ,0BAA0B;IAC7B;EACJ;EACD;;;EACc,IAAVQ,UAAU,GAAG;IACb,OAAO,KAAKF,aAAL,IAAsB,IAA7B;EACH;EACD;AACJ;AACA;AACA;;;EACIG,eAAe,CAACN,IAAD,EAAO;IAClB,KAAKG,aAAL,GAAqBH,IAArB;EACH;;AAnCQ;AAqCb;AACA;AACA;;;AACA,MAAMO,eAAN,SAA8BT,MAA9B,CAAqC;EACjCU,WAAW,CAACC,SAAD,EAAYC,gBAAZ,EAA8BC,QAA9B,EAAwCC,wBAAxC,EAAkE;IACzE;IACA,KAAKH,SAAL,GAAiBA,SAAjB;IACA,KAAKC,gBAAL,GAAwBA,gBAAxB;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,wBAAL,GAAgCA,wBAAhC;EACH;;AAPgC;AASrC;AACA;AACA;;;AACA,MAAMC,cAAN,SAA6Bf,MAA7B,CAAoC;EAChCU,WAAW;EACX;EACAM,WAFW;EAGX;EACAJ,gBAJW;EAKX;EACAK,OANW;EAOX;EACAJ,QARW,EAQD;IACN;IACA,KAAKG,WAAL,GAAmBA,WAAnB;IACA,KAAKJ,gBAAL,GAAwBA,gBAAxB;IACA,KAAKK,OAAL,GAAeA,OAAf;IACA,KAAKJ,QAAL,GAAgBA,QAAhB;EACH;;EACS,IAANK,MAAM,GAAG;IACT,OAAO,KAAKF,WAAL,CAAiBG,UAAxB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIlB,MAAM,CAACC,IAAD,EAAOe,OAAO,GAAG,KAAKA,OAAtB,EAA+B;IACjC,KAAKA,OAAL,GAAeA,OAAf;IACA,OAAO,MAAMhB,MAAN,CAAaC,IAAb,CAAP;EACH;;EACDI,MAAM,GAAG;IACL,KAAKW,OAAL,GAAeG,SAAf;IACA,OAAO,MAAMd,MAAN,EAAP;EACH;;AA/B+B;AAiCpC;AACA;AACA;AACA;AACA;;;AACA,MAAMe,SAAN,SAAwBrB,MAAxB,CAA+B;EAC3BU,WAAW,CAACY,OAAD,EAAU;IACjB;IACA,KAAKA,OAAL,GAAeA,OAAO,YAAYrC,UAAnB,GAAgCqC,OAAO,CAACC,aAAxC,GAAwDD,OAAvE;EACH;;AAJ0B;AAM/B;AACA;AACA;AACA;;;AACA,MAAME,gBAAN,CAAuB;EACnBd,WAAW,GAAG;IACV;IACA,KAAKe,WAAL,GAAmB,KAAnB,CAFU,CAGV;;IACA,KAAKC,eAAL,GAAuB,IAAvB;EACH;EACD;;;EACAtB,WAAW,GAAG;IACV,OAAO,CAAC,CAAC,KAAKuB,eAAd;EACH;EACD;;;EACA1B,MAAM,CAAC2B,MAAD,EAAS;IACX,IAAI,OAAOzB,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;MAC/C,IAAI,CAACyB,MAAL,EAAa;QACTnC,oBAAoB;MACvB;;MACD,IAAI,KAAKW,WAAL,EAAJ,EAAwB;QACpBT,+BAA+B;MAClC;;MACD,IAAI,KAAK8B,WAAT,EAAsB;QAClB7B,qCAAqC;MACxC;IACJ;;IACD,IAAIgC,MAAM,YAAYnB,eAAtB,EAAuC;MACnC,KAAKkB,eAAL,GAAuBC,MAAvB;MACA,OAAO,KAAKC,qBAAL,CAA2BD,MAA3B,CAAP;IACH,CAHD,MAIK,IAAIA,MAAM,YAAYb,cAAtB,EAAsC;MACvC,KAAKY,eAAL,GAAuBC,MAAvB;MACA,OAAO,KAAKE,oBAAL,CAA0BF,MAA1B,CAAP,CAFuC,CAGvC;IACH,CAJI,MAKA,IAAI,KAAKF,eAAL,IAAwBE,MAAM,YAAYP,SAA9C,EAAyD;MAC1D,KAAKM,eAAL,GAAuBC,MAAvB;MACA,OAAO,KAAKF,eAAL,CAAqBE,MAArB,CAAP;IACH;;IACD,IAAI,OAAOzB,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;MAC/CN,2BAA2B;IAC9B;EACJ;EACD;;;EACAS,MAAM,GAAG;IACL,IAAI,KAAKqB,eAAT,EAA0B;MACtB,KAAKA,eAAL,CAAqBnB,eAArB,CAAqC,IAArC;;MACA,KAAKmB,eAAL,GAAuB,IAAvB;IACH;;IACD,KAAKI,gBAAL;EACH;EACD;;;EACAC,OAAO,GAAG;IACN,IAAI,KAAK5B,WAAL,EAAJ,EAAwB;MACpB,KAAKE,MAAL;IACH;;IACD,KAAKyB,gBAAL;;IACA,KAAKN,WAAL,GAAmB,IAAnB;EACH;EACD;;;EACAQ,YAAY,CAACC,EAAD,EAAK;IACb,KAAKC,UAAL,GAAkBD,EAAlB;EACH;;EACDH,gBAAgB,GAAG;IACf,IAAI,KAAKI,UAAT,EAAqB;MACjB,KAAKA,UAAL;;MACA,KAAKA,UAAL,GAAkB,IAAlB;IACH;EACJ;;AAlEkB;AAoEvB;AACA;AACA;AACA;;;AACA,MAAMC,cAAN,SAA6BZ,gBAA7B,CAA8C;AAG9C;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMa,eAAN,SAA8Bb,gBAA9B,CAA+C;EAC3C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACId,WAAW;EACX;EACA4B,aAFW,EAEIC,yBAFJ,EAE+BC,OAF/B,EAEwCC,gBAFxC;EAGX;AACJ;AACA;AACA;EACIC,SAPW,EAOA;IACP;IACA,KAAKJ,aAAL,GAAqBA,aAArB;IACA,KAAKC,yBAAL,GAAiCA,yBAAjC;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKC,gBAAL,GAAwBA,gBAAxB;IACA;AACR;AACA;AACA;AACA;AACA;;IACQ,KAAKf,eAAL,GAAwBE,MAAD,IAAY;MAC/B;MACA;MACA,IAAI,CAAC,KAAKc,SAAN,KAAoB,OAAOvC,SAAP,KAAqB,WAArB,IAAoCA,SAAxD,CAAJ,EAAwE;QACpE,MAAMT,KAAK,CAAC,kEAAD,CAAX;MACH;;MACD,MAAM4B,OAAO,GAAGM,MAAM,CAACN,OAAvB;;MACA,IAAI,CAACA,OAAO,CAACqB,UAAT,KAAwB,OAAOxC,SAAP,KAAqB,WAArB,IAAoCA,SAA5D,CAAJ,EAA4E;QACxE,MAAMT,KAAK,CAAC,uDAAD,CAAX;MACH,CAT8B,CAU/B;MACA;;;MACA,MAAMkD,UAAU,GAAG,KAAKF,SAAL,CAAeG,aAAf,CAA6B,YAA7B,CAAnB;;MACAvB,OAAO,CAACqB,UAAR,CAAmBG,YAAnB,CAAgCF,UAAhC,EAA4CtB,OAA5C;MACA,KAAKgB,aAAL,CAAmBS,WAAnB,CAA+BzB,OAA/B;MACA,KAAKK,eAAL,GAAuBC,MAAvB;MACA,MAAMK,YAAN,CAAmB,MAAM;QACrB;QACA,IAAIW,UAAU,CAACD,UAAf,EAA2B;UACvBC,UAAU,CAACD,UAAX,CAAsBK,YAAtB,CAAmC1B,OAAnC,EAA4CsB,UAA5C;QACH;MACJ,CALD;IAMH,CAtBD;;IAuBA,KAAKF,SAAL,GAAiBA,SAAjB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIb,qBAAqB,CAACD,MAAD,EAAS;IAC1B,MAAMqB,QAAQ,GAAIrB,MAAM,CAACd,wBAAP,IAAmC,KAAKyB,yBAA1D;;IACA,IAAI,CAAC,OAAOpC,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KAAmD,CAAC8C,QAAxD,EAAkE;MAC9D,MAAMvD,KAAK,CAAC,8EAAD,CAAX;IACH;;IACD,MAAMwD,gBAAgB,GAAGD,QAAQ,CAACE,uBAAT,CAAiCvB,MAAM,CAACjB,SAAxC,CAAzB;IACA,IAAIyC,YAAJ,CAN0B,CAO1B;IACA;IACA;IACA;;IACA,IAAIxB,MAAM,CAAChB,gBAAX,EAA6B;MACzBwC,YAAY,GAAGxB,MAAM,CAAChB,gBAAP,CAAwByC,eAAxB,CAAwCH,gBAAxC,EAA0DtB,MAAM,CAAChB,gBAAP,CAAwB0C,MAAlF,EAA0F1B,MAAM,CAACf,QAAP,IAAmBe,MAAM,CAAChB,gBAAP,CAAwBC,QAArI,CAAf;MACA,KAAKoB,YAAL,CAAkB,MAAMmB,YAAY,CAACG,OAAb,EAAxB;IACH,CAHD,MAIK;MACD,IAAI,CAAC,OAAOpD,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KAAmD,CAAC,KAAKqC,OAA7D,EAAsE;QAClE,MAAM9C,KAAK,CAAC,qEAAD,CAAX;MACH;;MACD0D,YAAY,GAAGF,gBAAgB,CAACM,MAAjB,CAAwB5B,MAAM,CAACf,QAAP,IAAmB,KAAK4B,gBAAxB,IAA4CvD,QAAQ,CAACuE,IAA7E,CAAf;;MACA,KAAKjB,OAAL,CAAakB,UAAb,CAAwBN,YAAY,CAACO,QAArC;;MACA,KAAK1B,YAAL,CAAkB,MAAM;QACpB;QACA;QACA,IAAI,KAAKO,OAAL,CAAaoB,SAAb,GAAyB,CAA7B,EAAgC;UAC5B,KAAKpB,OAAL,CAAaqB,UAAb,CAAwBT,YAAY,CAACO,QAArC;QACH;;QACDP,YAAY,CAACG,OAAb;MACH,CAPD;IAQH,CA7ByB,CA8B1B;IACA;;;IACA,KAAKjB,aAAL,CAAmBS,WAAnB,CAA+B,KAAKe,qBAAL,CAA2BV,YAA3B,CAA/B;IACA,KAAKzB,eAAL,GAAuBC,MAAvB;IACA,OAAOwB,YAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACItB,oBAAoB,CAACF,MAAD,EAAS;IACzB,IAAImC,aAAa,GAAGnC,MAAM,CAAChB,gBAA3B;IACA,IAAIoD,OAAO,GAAGD,aAAa,CAACE,kBAAd,CAAiCrC,MAAM,CAACZ,WAAxC,EAAqDY,MAAM,CAACX,OAA5D,EAAqE;MAC/EJ,QAAQ,EAAEe,MAAM,CAACf;IAD8D,CAArE,CAAd,CAFyB,CAKzB;IACA;IACA;IACA;;IACAmD,OAAO,CAACE,SAAR,CAAkBC,OAAlB,CAA0BC,QAAQ,IAAI,KAAK9B,aAAL,CAAmBS,WAAnB,CAA+BqB,QAA/B,CAAtC,EATyB,CAUzB;IACA;IACA;;IACAJ,OAAO,CAACK,aAAR;IACA,KAAKpC,YAAL,CAAkB,MAAM;MACpB,IAAIqC,KAAK,GAAGP,aAAa,CAACQ,OAAd,CAAsBP,OAAtB,CAAZ;;MACA,IAAIM,KAAK,KAAK,CAAC,CAAf,EAAkB;QACdP,aAAa,CAACS,MAAd,CAAqBF,KAArB;MACH;IACJ,CALD;IAMA,KAAK3C,eAAL,GAAuBC,MAAvB,CApByB,CAqBzB;;IACA,OAAOoC,OAAP;EACH;EACD;AACJ;AACA;;;EACIhC,OAAO,GAAG;IACN,MAAMA,OAAN;IACA,KAAKM,aAAL,CAAmBkC,MAAnB;EACH;EACD;;;EACAV,qBAAqB,CAACV,YAAD,EAAe;IAChC,OAAOA,YAAY,CAACO,QAAb,CAAsBO,SAAtB,CAAgC,CAAhC,CAAP;EACH;;AAxI0C;AA0I/C;AACA;AACA;AACA;;;AACA,MAAMO,aAAN,SAA4BpC,eAA5B,CAA4C;AAG5C;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMqC,SAAN,SAAwB3D,cAAxB,CAAuC;EACnCL,WAAW,CAACM,WAAD,EAAcJ,gBAAd,EAAgC;IACvC,MAAMI,WAAN,EAAmBJ,gBAAnB;EACH;;AAHkC;;AAKvC8D,SAAS,CAACC,IAAV;EAAA,iBAAsGD,SAAtG,EAA4F1F,EAA5F,mBAAiIA,EAAE,CAAC4F,WAApI,GAA4F5F,EAA5F,mBAA4JA,EAAE,CAAC6F,gBAA/J;AAAA;;AACAH,SAAS,CAACI,IAAV,kBAD4F9F,EAC5F;EAAA,MAA0F0F,SAA1F;EAAA;EAAA;EAAA,WAD4F1F,EAC5F;AAAA;;AACA;EAAA,mDAF4FA,EAE5F,mBAA2F0F,SAA3F,EAAkH,CAAC;IACvGK,IAAI,EAAE5F,SADiG;IAEvG6F,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,aADX;MAECC,QAAQ,EAAE;IAFX,CAAD;EAFiG,CAAD,CAAlH,EAM4B,YAAY;IAAE,OAAO,CAAC;MAAEH,IAAI,EAAE/F,EAAE,CAAC4F;IAAX,CAAD,EAA2B;MAAEG,IAAI,EAAE/F,EAAE,CAAC6F;IAAX,CAA3B,CAAP;EAAmE,CAN7G;AAAA;AAOA;AACA;AACA;AACA;;;AACA,MAAMM,uBAAN,SAAsCT,SAAtC,CAAgD;;AAEhDS,uBAAuB,CAACR,IAAxB;EAAA;EAAA;IAAA,wFAf4F3F,EAe5F,uBAAoHmG,uBAApH,SAAoHA,uBAApH;EAAA;AAAA;;AACAA,uBAAuB,CAACL,IAAxB,kBAhB4F9F,EAgB5F;EAAA,MAAwGmG,uBAAxG;EAAA;EAAA;EAAA,WAhB4FnG,EAgB5F,oBAAgL,CACxK;IACIoG,OAAO,EAAEV,SADb;IAEIW,WAAW,EAAEF;EAFjB,CADwK,CAAhL,GAhB4FnG,EAgB5F;AAAA;;AAMA;EAAA,mDAtB4FA,EAsB5F,mBAA2FmG,uBAA3F,EAAgI,CAAC;IACrHJ,IAAI,EAAE5F,SAD+G;IAErH6F,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wBADX;MAECC,QAAQ,EAAE,WAFX;MAGCI,SAAS,EAAE,CACP;QACIF,OAAO,EAAEV,SADb;QAEIW,WAAW,EAAEF;MAFjB,CADO;IAHZ,CAAD;EAF+G,CAAD,CAAhI;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMI,eAAN,SAA8B/D,gBAA9B,CAA+C;EAC3Cd,WAAW,CAAC6B,yBAAD,EAA4BiD,iBAA5B;EACX;AACJ;AACA;AACA;EACI9C,SALW,EAKA;IACP;IACA,KAAKH,yBAAL,GAAiCA,yBAAjC;IACA,KAAKiD,iBAAL,GAAyBA,iBAAzB;IACA;;IACA,KAAKC,cAAL,GAAsB,KAAtB;IACA;;IACA,KAAKC,QAAL,GAAgB,IAAItG,YAAJ,EAAhB;IACA;AACR;AACA;AACA;AACA;AACA;;IACQ,KAAKsC,eAAL,GAAwBE,MAAD,IAAY;MAC/B;MACA;MACA,IAAI,CAAC,KAAKc,SAAN,KAAoB,OAAOvC,SAAP,KAAqB,WAArB,IAAoCA,SAAxD,CAAJ,EAAwE;QACpE,MAAMT,KAAK,CAAC,kEAAD,CAAX;MACH;;MACD,MAAM4B,OAAO,GAAGM,MAAM,CAACN,OAAvB;;MACA,IAAI,CAACA,OAAO,CAACqB,UAAT,KAAwB,OAAOxC,SAAP,KAAqB,WAArB,IAAoCA,SAA5D,CAAJ,EAA4E;QACxE,MAAMT,KAAK,CAAC,uDAAD,CAAX;MACH,CAT8B,CAU/B;MACA;;;MACA,MAAMkD,UAAU,GAAG,KAAKF,SAAL,CAAeG,aAAf,CAA6B,YAA7B,CAAnB;;MACAjB,MAAM,CAACpB,eAAP,CAAuB,IAAvB;MACAc,OAAO,CAACqB,UAAR,CAAmBG,YAAnB,CAAgCF,UAAhC,EAA4CtB,OAA5C;;MACA,KAAKqE,YAAL,GAAoB5C,WAApB,CAAgCzB,OAAhC;;MACA,KAAKK,eAAL,GAAuBC,MAAvB;MACA,MAAMK,YAAN,CAAmB,MAAM;QACrB,IAAIW,UAAU,CAACD,UAAf,EAA2B;UACvBC,UAAU,CAACD,UAAX,CAAsBK,YAAtB,CAAmC1B,OAAnC,EAA4CsB,UAA5C;QACH;MACJ,CAJD;IAKH,CAtBD;;IAuBA,KAAKF,SAAL,GAAiBA,SAAjB;EACH;EACD;;;EACU,IAANd,MAAM,GAAG;IACT,OAAO,KAAKD,eAAZ;EACH;;EACS,IAANC,MAAM,CAACA,MAAD,EAAS;IACf;IACA;IACA;IACA;IACA,IAAI,KAAKxB,WAAL,MAAsB,CAACwB,MAAvB,IAAiC,CAAC,KAAK6D,cAA3C,EAA2D;MACvD;IACH;;IACD,IAAI,KAAKrF,WAAL,EAAJ,EAAwB;MACpB,MAAME,MAAN;IACH;;IACD,IAAIsB,MAAJ,EAAY;MACR,MAAM3B,MAAN,CAAa2B,MAAb;IACH;;IACD,KAAKD,eAAL,GAAuBC,MAAM,IAAI,IAAjC;EACH;EACD;;;EACe,IAAXgE,WAAW,GAAG;IACd,OAAO,KAAKC,YAAZ;EACH;;EACDC,QAAQ,GAAG;IACP,KAAKL,cAAL,GAAsB,IAAtB;EACH;;EACDM,WAAW,GAAG;IACV,MAAM/D,OAAN;IACA,KAAKL,eAAL,GAAuB,IAAvB;IACA,KAAKkE,YAAL,GAAoB,IAApB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIhE,qBAAqB,CAACD,MAAD,EAAS;IAC1BA,MAAM,CAACpB,eAAP,CAAuB,IAAvB,EAD0B,CAE1B;IACA;;IACA,MAAMI,gBAAgB,GAAGgB,MAAM,CAAChB,gBAAP,IAA2B,IAA3B,GAAkCgB,MAAM,CAAChB,gBAAzC,GAA4D,KAAK4E,iBAA1F;IACA,MAAMvC,QAAQ,GAAGrB,MAAM,CAACd,wBAAP,IAAmC,KAAKyB,yBAAzD;IACA,MAAMW,gBAAgB,GAAGD,QAAQ,CAACE,uBAAT,CAAiCvB,MAAM,CAACjB,SAAxC,CAAzB;IACA,MAAMqF,GAAG,GAAGpF,gBAAgB,CAACyC,eAAjB,CAAiCH,gBAAjC,EAAmDtC,gBAAgB,CAAC0C,MAApE,EAA4E1B,MAAM,CAACf,QAAP,IAAmBD,gBAAgB,CAACC,QAAhH,CAAZ,CAP0B,CAQ1B;IACA;IACA;;IACA,IAAID,gBAAgB,KAAK,KAAK4E,iBAA9B,EAAiD;MAC7C,KAAKG,YAAL,GAAoB5C,WAApB,CAAgCiD,GAAG,CAACrC,QAAJ,CAAaO,SAAb,CAAuB,CAAvB,CAAhC;IACH;;IACD,MAAMjC,YAAN,CAAmB,MAAM+D,GAAG,CAACzC,OAAJ,EAAzB;IACA,KAAK5B,eAAL,GAAuBC,MAAvB;IACA,KAAKiE,YAAL,GAAoBG,GAApB;IACA,KAAKN,QAAL,CAAcO,IAAd,CAAmBD,GAAnB;IACA,OAAOA,GAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIlE,oBAAoB,CAACF,MAAD,EAAS;IACzBA,MAAM,CAACpB,eAAP,CAAuB,IAAvB;;IACA,MAAMwD,OAAO,GAAG,KAAKwB,iBAAL,CAAuBvB,kBAAvB,CAA0CrC,MAAM,CAACZ,WAAjD,EAA8DY,MAAM,CAACX,OAArE,EAA8E;MAC1FJ,QAAQ,EAAEe,MAAM,CAACf;IADyE,CAA9E,CAAhB;;IAGA,MAAMoB,YAAN,CAAmB,MAAM,KAAKuD,iBAAL,CAAuBU,KAAvB,EAAzB;IACA,KAAKvE,eAAL,GAAuBC,MAAvB;IACA,KAAKiE,YAAL,GAAoB7B,OAApB;IACA,KAAK0B,QAAL,CAAcO,IAAd,CAAmBjC,OAAnB;IACA,OAAOA,OAAP;EACH;EACD;;;EACA2B,YAAY,GAAG;IACX,MAAMpE,aAAa,GAAG,KAAKiE,iBAAL,CAAuBlE,OAAvB,CAA+BC,aAArD,CADW,CAEX;IACA;;IACA,OAAQA,aAAa,CAAC4E,QAAd,KAA2B5E,aAAa,CAAC6E,YAAzC,GACF7E,aADE,GAEFA,aAAa,CAACoB,UAFpB;EAGH;;AA/H0C;;AAiI/C4C,eAAe,CAACZ,IAAhB;EAAA,iBAA4GY,eAA5G,EA3K4FvG,EA2K5F,mBAA6IA,EAAE,CAACqH,wBAAhJ,GA3K4FrH,EA2K5F,mBAAqLA,EAAE,CAAC6F,gBAAxL,GA3K4F7F,EA2K5F,mBAAqNQ,QAArN;AAAA;;AACA+F,eAAe,CAACT,IAAhB,kBA5K4F9F,EA4K5F;EAAA,MAAgGuG,eAAhG;EAAA;EAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA,WA5K4FvG,EA4K5F;AAAA;;AACA;EAAA,mDA7K4FA,EA6K5F,mBAA2FuG,eAA3F,EAAwH,CAAC;IAC7GR,IAAI,EAAE5F,SADuG;IAE7G6F,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBADX;MAECC,QAAQ,EAAE,iBAFX;MAGCoB,MAAM,EAAE,CAAC,yBAAD;IAHT,CAAD;EAFuG,CAAD,CAAxH,EAO4B,YAAY;IAAE,OAAO,CAAC;MAAEvB,IAAI,EAAE/F,EAAE,CAACqH;IAAX,CAAD,EAAwC;MAAEtB,IAAI,EAAE/F,EAAE,CAAC6F;IAAX,CAAxC,EAAuE;MAAEE,IAAI,EAAE3D,SAAR;MAAmBmF,UAAU,EAAE,CAAC;QACpIxB,IAAI,EAAE1F,MAD8H;QAEpI2F,IAAI,EAAE,CAACxF,QAAD;MAF8H,CAAD;IAA/B,CAAvE,CAAP;EAGlB,CAVxB,EAU0C;IAAEkG,QAAQ,EAAE,CAAC;MACvCX,IAAI,EAAEzF;IADiC,CAAD;EAAZ,CAV1C;AAAA;AAaA;AACA;AACA;AACA;;;AACA,MAAMkH,mBAAN,SAAkCjB,eAAlC,CAAkD;;AAElDiB,mBAAmB,CAAC7B,IAApB;EAAA;EAAA;IAAA,gFAhM4F3F,EAgM5F,uBAAgHwH,mBAAhH,SAAgHA,mBAAhH;EAAA;AAAA;;AACAA,mBAAmB,CAAC1B,IAApB,kBAjM4F9F,EAiM5F;EAAA,MAAoGwH,mBAApG;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA,WAjM4FxH,EAiM5F,oBAAgO,CACxN;IACIoG,OAAO,EAAEG,eADb;IAEIF,WAAW,EAAEmB;EAFjB,CADwN,CAAhO,GAjM4FxH,EAiM5F;AAAA;;AAMA;EAAA,mDAvM4FA,EAuM5F,mBAA2FwH,mBAA3F,EAA4H,CAAC;IACjHzB,IAAI,EAAE5F,SAD2G;IAEjH6F,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,+BADX;MAECC,QAAQ,EAAE,eAFX;MAGCoB,MAAM,EAAE,CAAC,uBAAD,CAHT;MAIChB,SAAS,EAAE,CACP;QACIF,OAAO,EAAEG,eADb;QAEIF,WAAW,EAAEmB;MAFjB,CADO;IAJZ,CAAD;EAF2G,CAAD,CAA5H;AAAA;;AAcA,MAAMC,YAAN,CAAmB;;AAEnBA,YAAY,CAAC9B,IAAb;EAAA,iBAAyG8B,YAAzG;AAAA;;AACAA,YAAY,CAACC,IAAb,kBAxN4F1H,EAwN5F;EAAA,MAA0GyH,YAA1G;EAAA,eAAuI/B,SAAvI,EAAkJa,eAAlJ,EAAmKJ,uBAAnK,EAA4LqB,mBAA5L;EAAA,UAA4N9B,SAA5N,EAAuOa,eAAvO,EAAwPJ,uBAAxP,EAAiRqB,mBAAjR;AAAA;AACAC,YAAY,CAACE,IAAb,kBAzN4F3H,EAyN5F;;AACA;EAAA,mDA1N4FA,EA0N5F,mBAA2FyH,YAA3F,EAAqH,CAAC;IAC1G1B,IAAI,EAAExF,QADoG;IAE1GyF,IAAI,EAAE,CAAC;MACC4B,OAAO,EAAE,CAAClC,SAAD,EAAYa,eAAZ,EAA6BJ,uBAA7B,EAAsDqB,mBAAtD,CADV;MAECK,YAAY,EAAE,CAACnC,SAAD,EAAYa,eAAZ,EAA6BJ,uBAA7B,EAAsDqB,mBAAtD;IAFf,CAAD;EAFoG,CAAD,CAArH;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMM,cAAN,CAAqB;EACjBpG,WAAW,CAACqG,eAAD,EAAkBC,aAAlB,EAAiC;IACxC,KAAKD,eAAL,GAAuBA,eAAvB;IACA,KAAKC,aAAL,GAAqBA,aAArB;EACH;;EACDC,GAAG,CAACC,KAAD,EAAQC,aAAR,EAAuB;IACtB,MAAMC,KAAK,GAAG,KAAKJ,aAAL,CAAmBC,GAAnB,CAAuBC,KAAvB,CAAd;;IACA,IAAI,OAAOE,KAAP,KAAiB,WAArB,EAAkC;MAC9B,OAAOA,KAAP;IACH;;IACD,OAAO,KAAKL,eAAL,CAAqBE,GAArB,CAAyBC,KAAzB,EAAgCC,aAAhC,CAAP;EACH;;AAXgB;AAcrB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAAS/E,cAAT,EAAyBZ,gBAAzB,EAA2CkD,SAA3C,EAAsDa,eAAtD,EAAuE9E,eAAvE,EAAwFY,SAAxF,EAAmGoD,aAAnG,EAAkHpC,eAAlH,EAAmIrC,MAAnI,EAA2IwG,mBAA3I,EAAgKM,cAAhK,EAAgLL,YAAhL,EAA8L1F,cAA9L,EAA8MoE,uBAA9M"}, "metadata": {}, "sourceType": "module"}