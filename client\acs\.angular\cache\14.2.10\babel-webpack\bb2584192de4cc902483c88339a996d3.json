{"ast": null, "code": "export function isPromise(value) {\n  return !!value && typeof value.subscribe !== 'function' && typeof value.then === 'function';\n}", "map": {"version": 3, "names": ["isPromise", "value", "subscribe", "then"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/isPromise.js"], "sourcesContent": ["export function isPromise(value) {\n    return !!value && typeof value.subscribe !== 'function' && typeof value.then === 'function';\n}\n"], "mappings": "AAAA,OAAO,SAASA,SAAT,CAAmBC,KAAnB,EAA0B;EAC7B,OAAO,CAAC,CAACA,KAAF,IAAW,OAAOA,KAAK,CAACC,SAAb,KAA2B,UAAtC,IAAoD,OAAOD,KAAK,CAACE,IAAb,KAAsB,UAAjF;AACH"}, "metadata": {}, "sourceType": "module"}