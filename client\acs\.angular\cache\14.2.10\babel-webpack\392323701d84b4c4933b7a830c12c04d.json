{"ast": null, "code": "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "map": {"version": 3, "names": ["getWindow", "getWindowScroll", "node", "win", "scrollLeft", "pageXOffset", "scrollTop", "pageYOffset"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js"], "sourcesContent": ["import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,gBAAtB;AACA,eAAe,SAASC,eAAT,CAAyBC,IAAzB,EAA+B;EAC5C,IAAIC,GAAG,GAAGH,SAAS,CAACE,IAAD,CAAnB;EACA,IAAIE,UAAU,GAAGD,GAAG,CAACE,WAArB;EACA,IAAIC,SAAS,GAAGH,GAAG,CAACI,WAApB;EACA,OAAO;IACLH,UAAU,EAAEA,UADP;IAELE,SAAS,EAAEA;EAFN,CAAP;AAID"}, "metadata": {}, "sourceType": "module"}