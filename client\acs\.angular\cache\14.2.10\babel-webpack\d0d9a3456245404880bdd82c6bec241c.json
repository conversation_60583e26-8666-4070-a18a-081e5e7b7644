{"ast": null, "code": "import { concat as concatStatic } from '../observable/concat';\nexport function concat(...observables) {\n  return source => source.lift.call(concatStatic(source, ...observables));\n}", "map": {"version": 3, "names": ["concat", "concatStatic", "observables", "source", "lift", "call"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/concat.js"], "sourcesContent": ["import { concat as concatStatic } from '../observable/concat';\nexport function concat(...observables) {\n    return (source) => source.lift.call(concatStatic(source, ...observables));\n}\n"], "mappings": "AAAA,SAASA,MAAM,IAAIC,YAAnB,QAAuC,sBAAvC;AACA,OAAO,SAASD,MAAT,CAAgB,GAAGE,WAAnB,EAAgC;EACnC,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAYC,IAAZ,CAAiBJ,YAAY,CAACE,MAAD,EAAS,GAAGD,WAAZ,CAA7B,CAAnB;AACH"}, "metadata": {}, "sourceType": "module"}