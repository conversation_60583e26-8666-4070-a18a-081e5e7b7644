{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function find(predicate, thisArg) {\n  if (typeof predicate !== 'function') {\n    throw new TypeError('predicate is not a function');\n  }\n\n  return source => source.lift(new FindValueOperator(predicate, source, false, thisArg));\n}\nexport class FindValueOperator {\n  constructor(predicate, source, yieldIndex, thisArg) {\n    this.predicate = predicate;\n    this.source = source;\n    this.yieldIndex = yieldIndex;\n    this.thisArg = thisArg;\n  }\n\n  call(observer, source) {\n    return source.subscribe(new FindValueSubscriber(observer, this.predicate, this.source, this.yieldIndex, this.thisArg));\n  }\n\n}\nexport class FindValueSubscriber extends Subscriber {\n  constructor(destination, predicate, source, yieldIndex, thisArg) {\n    super(destination);\n    this.predicate = predicate;\n    this.source = source;\n    this.yieldIndex = yieldIndex;\n    this.thisArg = thisArg;\n    this.index = 0;\n  }\n\n  notifyComplete(value) {\n    const destination = this.destination;\n    destination.next(value);\n    destination.complete();\n    this.unsubscribe();\n  }\n\n  _next(value) {\n    const {\n      predicate,\n      thisArg\n    } = this;\n    const index = this.index++;\n\n    try {\n      const result = predicate.call(thisArg || this, value, index, this.source);\n\n      if (result) {\n        this.notifyComplete(this.yieldIndex ? index : value);\n      }\n    } catch (err) {\n      this.destination.error(err);\n    }\n  }\n\n  _complete() {\n    this.notifyComplete(this.yieldIndex ? -1 : undefined);\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "find", "predicate", "thisArg", "TypeError", "source", "lift", "FindValueOperator", "constructor", "yieldIndex", "call", "observer", "subscribe", "FindValueSubscriber", "destination", "index", "notifyComplete", "value", "next", "complete", "unsubscribe", "_next", "result", "err", "error", "_complete", "undefined"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/find.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function find(predicate, thisArg) {\n    if (typeof predicate !== 'function') {\n        throw new TypeError('predicate is not a function');\n    }\n    return (source) => source.lift(new FindValueOperator(predicate, source, false, thisArg));\n}\nexport class FindValueOperator {\n    constructor(predicate, source, yieldIndex, thisArg) {\n        this.predicate = predicate;\n        this.source = source;\n        this.yieldIndex = yieldIndex;\n        this.thisArg = thisArg;\n    }\n    call(observer, source) {\n        return source.subscribe(new FindValueSubscriber(observer, this.predicate, this.source, this.yieldIndex, this.thisArg));\n    }\n}\nexport class FindValueSubscriber extends Subscriber {\n    constructor(destination, predicate, source, yieldIndex, thisArg) {\n        super(destination);\n        this.predicate = predicate;\n        this.source = source;\n        this.yieldIndex = yieldIndex;\n        this.thisArg = thisArg;\n        this.index = 0;\n    }\n    notifyComplete(value) {\n        const destination = this.destination;\n        destination.next(value);\n        destination.complete();\n        this.unsubscribe();\n    }\n    _next(value) {\n        const { predicate, thisArg } = this;\n        const index = this.index++;\n        try {\n            const result = predicate.call(thisArg || this, value, index, this.source);\n            if (result) {\n                this.notifyComplete(this.yieldIndex ? index : value);\n            }\n        }\n        catch (err) {\n            this.destination.error(err);\n        }\n    }\n    _complete() {\n        this.notifyComplete(this.yieldIndex ? -1 : undefined);\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,IAAT,CAAcC,SAAd,EAAyBC,OAAzB,EAAkC;EACrC,IAAI,OAAOD,SAAP,KAAqB,UAAzB,EAAqC;IACjC,MAAM,IAAIE,SAAJ,CAAc,6BAAd,CAAN;EACH;;EACD,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,iBAAJ,CAAsBL,SAAtB,EAAiCG,MAAjC,EAAyC,KAAzC,EAAgDF,OAAhD,CAAZ,CAAnB;AACH;AACD,OAAO,MAAMI,iBAAN,CAAwB;EAC3BC,WAAW,CAACN,SAAD,EAAYG,MAAZ,EAAoBI,UAApB,EAAgCN,OAAhC,EAAyC;IAChD,KAAKD,SAAL,GAAiBA,SAAjB;IACA,KAAKG,MAAL,GAAcA,MAAd;IACA,KAAKI,UAAL,GAAkBA,UAAlB;IACA,KAAKN,OAAL,GAAeA,OAAf;EACH;;EACDO,IAAI,CAACC,QAAD,EAAWN,MAAX,EAAmB;IACnB,OAAOA,MAAM,CAACO,SAAP,CAAiB,IAAIC,mBAAJ,CAAwBF,QAAxB,EAAkC,KAAKT,SAAvC,EAAkD,KAAKG,MAAvD,EAA+D,KAAKI,UAApE,EAAgF,KAAKN,OAArF,CAAjB,CAAP;EACH;;AAT0B;AAW/B,OAAO,MAAMU,mBAAN,SAAkCb,UAAlC,CAA6C;EAChDQ,WAAW,CAACM,WAAD,EAAcZ,SAAd,EAAyBG,MAAzB,EAAiCI,UAAjC,EAA6CN,OAA7C,EAAsD;IAC7D,MAAMW,WAAN;IACA,KAAKZ,SAAL,GAAiBA,SAAjB;IACA,KAAKG,MAAL,GAAcA,MAAd;IACA,KAAKI,UAAL,GAAkBA,UAAlB;IACA,KAAKN,OAAL,GAAeA,OAAf;IACA,KAAKY,KAAL,GAAa,CAAb;EACH;;EACDC,cAAc,CAACC,KAAD,EAAQ;IAClB,MAAMH,WAAW,GAAG,KAAKA,WAAzB;IACAA,WAAW,CAACI,IAAZ,CAAiBD,KAAjB;IACAH,WAAW,CAACK,QAAZ;IACA,KAAKC,WAAL;EACH;;EACDC,KAAK,CAACJ,KAAD,EAAQ;IACT,MAAM;MAAEf,SAAF;MAAaC;IAAb,IAAyB,IAA/B;IACA,MAAMY,KAAK,GAAG,KAAKA,KAAL,EAAd;;IACA,IAAI;MACA,MAAMO,MAAM,GAAGpB,SAAS,CAACQ,IAAV,CAAeP,OAAO,IAAI,IAA1B,EAAgCc,KAAhC,EAAuCF,KAAvC,EAA8C,KAAKV,MAAnD,CAAf;;MACA,IAAIiB,MAAJ,EAAY;QACR,KAAKN,cAAL,CAAoB,KAAKP,UAAL,GAAkBM,KAAlB,GAA0BE,KAA9C;MACH;IACJ,CALD,CAMA,OAAOM,GAAP,EAAY;MACR,KAAKT,WAAL,CAAiBU,KAAjB,CAAuBD,GAAvB;IACH;EACJ;;EACDE,SAAS,GAAG;IACR,KAAKT,cAAL,CAAoB,KAAKP,UAAL,GAAkB,CAAC,CAAnB,GAAuBiB,SAA3C;EACH;;AA9B+C"}, "metadata": {}, "sourceType": "module"}