{"ast": null, "code": "import { Observable } from '../Observable';\nexport function range(start = 0, count, scheduler) {\n  return new Observable(subscriber => {\n    if (count === undefined) {\n      count = start;\n      start = 0;\n    }\n\n    let index = 0;\n    let current = start;\n\n    if (scheduler) {\n      return scheduler.schedule(dispatch, 0, {\n        index,\n        count,\n        start,\n        subscriber\n      });\n    } else {\n      do {\n        if (index++ >= count) {\n          subscriber.complete();\n          break;\n        }\n\n        subscriber.next(current++);\n\n        if (subscriber.closed) {\n          break;\n        }\n      } while (true);\n    }\n\n    return undefined;\n  });\n}\nexport function dispatch(state) {\n  const {\n    start,\n    index,\n    count,\n    subscriber\n  } = state;\n\n  if (index >= count) {\n    subscriber.complete();\n    return;\n  }\n\n  subscriber.next(start);\n\n  if (subscriber.closed) {\n    return;\n  }\n\n  state.index = index + 1;\n  state.start = start + 1;\n  this.schedule(state);\n}", "map": {"version": 3, "names": ["Observable", "range", "start", "count", "scheduler", "subscriber", "undefined", "index", "current", "schedule", "dispatch", "complete", "next", "closed", "state"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/range.js"], "sourcesContent": ["import { Observable } from '../Observable';\nexport function range(start = 0, count, scheduler) {\n    return new Observable(subscriber => {\n        if (count === undefined) {\n            count = start;\n            start = 0;\n        }\n        let index = 0;\n        let current = start;\n        if (scheduler) {\n            return scheduler.schedule(dispatch, 0, {\n                index, count, start, subscriber\n            });\n        }\n        else {\n            do {\n                if (index++ >= count) {\n                    subscriber.complete();\n                    break;\n                }\n                subscriber.next(current++);\n                if (subscriber.closed) {\n                    break;\n                }\n            } while (true);\n        }\n        return undefined;\n    });\n}\nexport function dispatch(state) {\n    const { start, index, count, subscriber } = state;\n    if (index >= count) {\n        subscriber.complete();\n        return;\n    }\n    subscriber.next(start);\n    if (subscriber.closed) {\n        return;\n    }\n    state.index = index + 1;\n    state.start = start + 1;\n    this.schedule(state);\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,KAAT,CAAeC,KAAK,GAAG,CAAvB,EAA0BC,KAA1B,EAAiCC,SAAjC,EAA4C;EAC/C,OAAO,IAAIJ,UAAJ,CAAeK,UAAU,IAAI;IAChC,IAAIF,KAAK,KAAKG,SAAd,EAAyB;MACrBH,KAAK,GAAGD,KAAR;MACAA,KAAK,GAAG,CAAR;IACH;;IACD,IAAIK,KAAK,GAAG,CAAZ;IACA,IAAIC,OAAO,GAAGN,KAAd;;IACA,IAAIE,SAAJ,EAAe;MACX,OAAOA,SAAS,CAACK,QAAV,CAAmBC,QAAnB,EAA6B,CAA7B,EAAgC;QACnCH,KADmC;QAC5BJ,KAD4B;QACrBD,KADqB;QACdG;MADc,CAAhC,CAAP;IAGH,CAJD,MAKK;MACD,GAAG;QACC,IAAIE,KAAK,MAAMJ,KAAf,EAAsB;UAClBE,UAAU,CAACM,QAAX;UACA;QACH;;QACDN,UAAU,CAACO,IAAX,CAAgBJ,OAAO,EAAvB;;QACA,IAAIH,UAAU,CAACQ,MAAf,EAAuB;UACnB;QACH;MACJ,CATD,QASS,IATT;IAUH;;IACD,OAAOP,SAAP;EACH,CAzBM,CAAP;AA0BH;AACD,OAAO,SAASI,QAAT,CAAkBI,KAAlB,EAAyB;EAC5B,MAAM;IAAEZ,KAAF;IAASK,KAAT;IAAgBJ,KAAhB;IAAuBE;EAAvB,IAAsCS,KAA5C;;EACA,IAAIP,KAAK,IAAIJ,KAAb,EAAoB;IAChBE,UAAU,CAACM,QAAX;IACA;EACH;;EACDN,UAAU,CAACO,IAAX,CAAgBV,KAAhB;;EACA,IAAIG,UAAU,CAACQ,MAAf,EAAuB;IACnB;EACH;;EACDC,KAAK,CAACP,KAAN,GAAcA,KAAK,GAAG,CAAtB;EACAO,KAAK,CAACZ,KAAN,GAAcA,KAAK,GAAG,CAAtB;EACA,KAAKO,QAAL,CAAcK,KAAd;AACH"}, "metadata": {}, "sourceType": "module"}