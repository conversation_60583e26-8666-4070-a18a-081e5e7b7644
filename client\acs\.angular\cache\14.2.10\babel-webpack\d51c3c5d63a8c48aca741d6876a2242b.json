{"ast": null, "code": "export function getSymbolIterator() {\n  if (typeof Symbol !== 'function' || !Symbol.iterator) {\n    return '@@iterator';\n  }\n\n  return Symbol.iterator;\n}\nexport const iterator = getSymbolIterator();\nexport const $$iterator = iterator;", "map": {"version": 3, "names": ["getSymbolIterator", "Symbol", "iterator", "$$iterator"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/symbol/iterator.js"], "sourcesContent": ["export function getSymbolIterator() {\n    if (typeof Symbol !== 'function' || !Symbol.iterator) {\n        return '@@iterator';\n    }\n    return Symbol.iterator;\n}\nexport const iterator = getSymbolIterator();\nexport const $$iterator = iterator;\n"], "mappings": "AAAA,OAAO,SAASA,iBAAT,GAA6B;EAChC,IAAI,OAAOC,MAAP,KAAkB,UAAlB,IAAgC,CAACA,MAAM,CAACC,QAA5C,EAAsD;IAClD,OAAO,YAAP;EACH;;EACD,OAAOD,MAAM,CAACC,QAAd;AACH;AACD,OAAO,MAAMA,QAAQ,GAAGF,iBAAiB,EAAlC;AACP,OAAO,MAAMG,UAAU,GAAGD,QAAnB"}, "metadata": {}, "sourceType": "module"}