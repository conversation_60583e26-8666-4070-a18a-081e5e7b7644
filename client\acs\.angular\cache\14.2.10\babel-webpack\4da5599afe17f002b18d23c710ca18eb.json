{"ast": null, "code": "import { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport const defaultThrottleConfig = {\n  leading: true,\n  trailing: false\n};\nexport function throttle(durationSelector, config = defaultThrottleConfig) {\n  return source => source.lift(new ThrottleOperator(durationSelector, !!config.leading, !!config.trailing));\n}\n\nclass ThrottleOperator {\n  constructor(durationSelector, leading, trailing) {\n    this.durationSelector = durationSelector;\n    this.leading = leading;\n    this.trailing = trailing;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new ThrottleSubscriber(subscriber, this.durationSelector, this.leading, this.trailing));\n  }\n\n}\n\nclass ThrottleSubscriber extends SimpleOuterSubscriber {\n  constructor(destination, durationSelector, _leading, _trailing) {\n    super(destination);\n    this.destination = destination;\n    this.durationSelector = durationSelector;\n    this._leading = _leading;\n    this._trailing = _trailing;\n    this._hasValue = false;\n  }\n\n  _next(value) {\n    this._hasValue = true;\n    this._sendValue = value;\n\n    if (!this._throttled) {\n      if (this._leading) {\n        this.send();\n      } else {\n        this.throttle(value);\n      }\n    }\n  }\n\n  send() {\n    const {\n      _hasValue,\n      _sendValue\n    } = this;\n\n    if (_hasValue) {\n      this.destination.next(_sendValue);\n      this.throttle(_sendValue);\n    }\n\n    this._hasValue = false;\n    this._sendValue = undefined;\n  }\n\n  throttle(value) {\n    const duration = this.tryDurationSelector(value);\n\n    if (!!duration) {\n      this.add(this._throttled = innerSubscribe(duration, new SimpleInnerSubscriber(this)));\n    }\n  }\n\n  tryDurationSelector(value) {\n    try {\n      return this.durationSelector(value);\n    } catch (err) {\n      this.destination.error(err);\n      return null;\n    }\n  }\n\n  throttlingDone() {\n    const {\n      _throttled,\n      _trailing\n    } = this;\n\n    if (_throttled) {\n      _throttled.unsubscribe();\n    }\n\n    this._throttled = undefined;\n\n    if (_trailing) {\n      this.send();\n    }\n  }\n\n  notifyNext() {\n    this.throttlingDone();\n  }\n\n  notifyComplete() {\n    this.throttlingDone();\n  }\n\n}", "map": {"version": 3, "names": ["SimpleOuterSubscriber", "innerSubscribe", "SimpleInnerSubscriber", "defaultThrottleConfig", "leading", "trailing", "throttle", "durationSelector", "config", "source", "lift", "ThrottleOperator", "constructor", "call", "subscriber", "subscribe", "ThrottleSubscriber", "destination", "_leading", "_trailing", "_hasValue", "_next", "value", "_sendValue", "_throttled", "send", "next", "undefined", "duration", "tryDurationSelector", "add", "err", "error", "throttlingDone", "unsubscribe", "notifyNext", "notifyComplete"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/throttle.js"], "sourcesContent": ["import { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport const defaultThrottleConfig = {\n    leading: true,\n    trailing: false\n};\nexport function throttle(durationSelector, config = defaultThrottleConfig) {\n    return (source) => source.lift(new ThrottleOperator(durationSelector, !!config.leading, !!config.trailing));\n}\nclass ThrottleOperator {\n    constructor(durationSelector, leading, trailing) {\n        this.durationSelector = durationSelector;\n        this.leading = leading;\n        this.trailing = trailing;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new ThrottleSubscriber(subscriber, this.durationSelector, this.leading, this.trailing));\n    }\n}\nclass ThrottleSubscriber extends SimpleOuterSubscriber {\n    constructor(destination, durationSelector, _leading, _trailing) {\n        super(destination);\n        this.destination = destination;\n        this.durationSelector = durationSelector;\n        this._leading = _leading;\n        this._trailing = _trailing;\n        this._hasValue = false;\n    }\n    _next(value) {\n        this._hasValue = true;\n        this._sendValue = value;\n        if (!this._throttled) {\n            if (this._leading) {\n                this.send();\n            }\n            else {\n                this.throttle(value);\n            }\n        }\n    }\n    send() {\n        const { _hasValue, _sendValue } = this;\n        if (_hasValue) {\n            this.destination.next(_sendValue);\n            this.throttle(_sendValue);\n        }\n        this._hasValue = false;\n        this._sendValue = undefined;\n    }\n    throttle(value) {\n        const duration = this.tryDurationSelector(value);\n        if (!!duration) {\n            this.add(this._throttled = innerSubscribe(duration, new SimpleInnerSubscriber(this)));\n        }\n    }\n    tryDurationSelector(value) {\n        try {\n            return this.durationSelector(value);\n        }\n        catch (err) {\n            this.destination.error(err);\n            return null;\n        }\n    }\n    throttlingDone() {\n        const { _throttled, _trailing } = this;\n        if (_throttled) {\n            _throttled.unsubscribe();\n        }\n        this._throttled = undefined;\n        if (_trailing) {\n            this.send();\n        }\n    }\n    notifyNext() {\n        this.throttlingDone();\n    }\n    notifyComplete() {\n        this.throttlingDone();\n    }\n}\n"], "mappings": "AAAA,SAASA,qBAAT,EAAgCC,cAAhC,EAAgDC,qBAAhD,QAA6E,mBAA7E;AACA,OAAO,MAAMC,qBAAqB,GAAG;EACjCC,OAAO,EAAE,IADwB;EAEjCC,QAAQ,EAAE;AAFuB,CAA9B;AAIP,OAAO,SAASC,QAAT,CAAkBC,gBAAlB,EAAoCC,MAAM,GAAGL,qBAA7C,EAAoE;EACvE,OAAQM,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,gBAAJ,CAAqBJ,gBAArB,EAAuC,CAAC,CAACC,MAAM,CAACJ,OAAhD,EAAyD,CAAC,CAACI,MAAM,CAACH,QAAlE,CAAZ,CAAnB;AACH;;AACD,MAAMM,gBAAN,CAAuB;EACnBC,WAAW,CAACL,gBAAD,EAAmBH,OAAnB,EAA4BC,QAA5B,EAAsC;IAC7C,KAAKE,gBAAL,GAAwBA,gBAAxB;IACA,KAAKH,OAAL,GAAeA,OAAf;IACA,KAAKC,QAAL,GAAgBA,QAAhB;EACH;;EACDQ,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,kBAAJ,CAAuBF,UAAvB,EAAmC,KAAKP,gBAAxC,EAA0D,KAAKH,OAA/D,EAAwE,KAAKC,QAA7E,CAAjB,CAAP;EACH;;AARkB;;AAUvB,MAAMW,kBAAN,SAAiChB,qBAAjC,CAAuD;EACnDY,WAAW,CAACK,WAAD,EAAcV,gBAAd,EAAgCW,QAAhC,EAA0CC,SAA1C,EAAqD;IAC5D,MAAMF,WAAN;IACA,KAAKA,WAAL,GAAmBA,WAAnB;IACA,KAAKV,gBAAL,GAAwBA,gBAAxB;IACA,KAAKW,QAAL,GAAgBA,QAAhB;IACA,KAAKC,SAAL,GAAiBA,SAAjB;IACA,KAAKC,SAAL,GAAiB,KAAjB;EACH;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACT,KAAKF,SAAL,GAAiB,IAAjB;IACA,KAAKG,UAAL,GAAkBD,KAAlB;;IACA,IAAI,CAAC,KAAKE,UAAV,EAAsB;MAClB,IAAI,KAAKN,QAAT,EAAmB;QACf,KAAKO,IAAL;MACH,CAFD,MAGK;QACD,KAAKnB,QAAL,CAAcgB,KAAd;MACH;IACJ;EACJ;;EACDG,IAAI,GAAG;IACH,MAAM;MAAEL,SAAF;MAAaG;IAAb,IAA4B,IAAlC;;IACA,IAAIH,SAAJ,EAAe;MACX,KAAKH,WAAL,CAAiBS,IAAjB,CAAsBH,UAAtB;MACA,KAAKjB,QAAL,CAAciB,UAAd;IACH;;IACD,KAAKH,SAAL,GAAiB,KAAjB;IACA,KAAKG,UAAL,GAAkBI,SAAlB;EACH;;EACDrB,QAAQ,CAACgB,KAAD,EAAQ;IACZ,MAAMM,QAAQ,GAAG,KAAKC,mBAAL,CAAyBP,KAAzB,CAAjB;;IACA,IAAI,CAAC,CAACM,QAAN,EAAgB;MACZ,KAAKE,GAAL,CAAS,KAAKN,UAAL,GAAkBvB,cAAc,CAAC2B,QAAD,EAAW,IAAI1B,qBAAJ,CAA0B,IAA1B,CAAX,CAAzC;IACH;EACJ;;EACD2B,mBAAmB,CAACP,KAAD,EAAQ;IACvB,IAAI;MACA,OAAO,KAAKf,gBAAL,CAAsBe,KAAtB,CAAP;IACH,CAFD,CAGA,OAAOS,GAAP,EAAY;MACR,KAAKd,WAAL,CAAiBe,KAAjB,CAAuBD,GAAvB;MACA,OAAO,IAAP;IACH;EACJ;;EACDE,cAAc,GAAG;IACb,MAAM;MAAET,UAAF;MAAcL;IAAd,IAA4B,IAAlC;;IACA,IAAIK,UAAJ,EAAgB;MACZA,UAAU,CAACU,WAAX;IACH;;IACD,KAAKV,UAAL,GAAkBG,SAAlB;;IACA,IAAIR,SAAJ,EAAe;MACX,KAAKM,IAAL;IACH;EACJ;;EACDU,UAAU,GAAG;IACT,KAAKF,cAAL;EACH;;EACDG,cAAc,GAAG;IACb,KAAKH,cAAL;EACH;;AA5DkD"}, "metadata": {}, "sourceType": "module"}