{"ast": null, "code": "import { Action } from './Action';\nexport class AsyncAction extends Action {\n  constructor(scheduler, work) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n    this.pending = false;\n  }\n\n  schedule(state, delay = 0) {\n    if (this.closed) {\n      return this;\n    }\n\n    this.state = state;\n    const id = this.id;\n    const scheduler = this.scheduler;\n\n    if (id != null) {\n      this.id = this.recycleAsyncId(scheduler, id, delay);\n    }\n\n    this.pending = true;\n    this.delay = delay;\n    this.id = this.id || this.requestAsyncId(scheduler, this.id, delay);\n    return this;\n  }\n\n  requestAsyncId(scheduler, id, delay = 0) {\n    return setInterval(scheduler.flush.bind(scheduler, this), delay);\n  }\n\n  recycleAsyncId(scheduler, id, delay = 0) {\n    if (delay !== null && this.delay === delay && this.pending === false) {\n      return id;\n    }\n\n    clearInterval(id);\n    return undefined;\n  }\n\n  execute(state, delay) {\n    if (this.closed) {\n      return new Error('executing a cancelled action');\n    }\n\n    this.pending = false;\n\n    const error = this._execute(state, delay);\n\n    if (error) {\n      return error;\n    } else if (this.pending === false && this.id != null) {\n      this.id = this.recycleAsyncId(this.scheduler, this.id, null);\n    }\n  }\n\n  _execute(state, delay) {\n    let errored = false;\n    let errorValue = undefined;\n\n    try {\n      this.work(state);\n    } catch (e) {\n      errored = true;\n      errorValue = !!e && e || new Error(e);\n    }\n\n    if (errored) {\n      this.unsubscribe();\n      return errorValue;\n    }\n  }\n\n  _unsubscribe() {\n    const id = this.id;\n    const scheduler = this.scheduler;\n    const actions = scheduler.actions;\n    const index = actions.indexOf(this);\n    this.work = null;\n    this.state = null;\n    this.pending = false;\n    this.scheduler = null;\n\n    if (index !== -1) {\n      actions.splice(index, 1);\n    }\n\n    if (id != null) {\n      this.id = this.recycleAsyncId(scheduler, id, null);\n    }\n\n    this.delay = null;\n  }\n\n}", "map": {"version": 3, "names": ["Action", "AsyncAction", "constructor", "scheduler", "work", "pending", "schedule", "state", "delay", "closed", "id", "recycleAsyncId", "requestAsyncId", "setInterval", "flush", "bind", "clearInterval", "undefined", "execute", "Error", "error", "_execute", "errored", "errorValue", "e", "unsubscribe", "_unsubscribe", "actions", "index", "indexOf", "splice"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/scheduler/AsyncAction.js"], "sourcesContent": ["import { Action } from './Action';\nexport class AsyncAction extends Action {\n    constructor(scheduler, work) {\n        super(scheduler, work);\n        this.scheduler = scheduler;\n        this.work = work;\n        this.pending = false;\n    }\n    schedule(state, delay = 0) {\n        if (this.closed) {\n            return this;\n        }\n        this.state = state;\n        const id = this.id;\n        const scheduler = this.scheduler;\n        if (id != null) {\n            this.id = this.recycleAsyncId(scheduler, id, delay);\n        }\n        this.pending = true;\n        this.delay = delay;\n        this.id = this.id || this.requestAsyncId(scheduler, this.id, delay);\n        return this;\n    }\n    requestAsyncId(scheduler, id, delay = 0) {\n        return setInterval(scheduler.flush.bind(scheduler, this), delay);\n    }\n    recycleAsyncId(scheduler, id, delay = 0) {\n        if (delay !== null && this.delay === delay && this.pending === false) {\n            return id;\n        }\n        clearInterval(id);\n        return undefined;\n    }\n    execute(state, delay) {\n        if (this.closed) {\n            return new Error('executing a cancelled action');\n        }\n        this.pending = false;\n        const error = this._execute(state, delay);\n        if (error) {\n            return error;\n        }\n        else if (this.pending === false && this.id != null) {\n            this.id = this.recycleAsyncId(this.scheduler, this.id, null);\n        }\n    }\n    _execute(state, delay) {\n        let errored = false;\n        let errorValue = undefined;\n        try {\n            this.work(state);\n        }\n        catch (e) {\n            errored = true;\n            errorValue = !!e && e || new Error(e);\n        }\n        if (errored) {\n            this.unsubscribe();\n            return errorValue;\n        }\n    }\n    _unsubscribe() {\n        const id = this.id;\n        const scheduler = this.scheduler;\n        const actions = scheduler.actions;\n        const index = actions.indexOf(this);\n        this.work = null;\n        this.state = null;\n        this.pending = false;\n        this.scheduler = null;\n        if (index !== -1) {\n            actions.splice(index, 1);\n        }\n        if (id != null) {\n            this.id = this.recycleAsyncId(scheduler, id, null);\n        }\n        this.delay = null;\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAT,QAAuB,UAAvB;AACA,OAAO,MAAMC,WAAN,SAA0BD,MAA1B,CAAiC;EACpCE,WAAW,CAACC,SAAD,EAAYC,IAAZ,EAAkB;IACzB,MAAMD,SAAN,EAAiBC,IAAjB;IACA,KAAKD,SAAL,GAAiBA,SAAjB;IACA,KAAKC,IAAL,GAAYA,IAAZ;IACA,KAAKC,OAAL,GAAe,KAAf;EACH;;EACDC,QAAQ,CAACC,KAAD,EAAQC,KAAK,GAAG,CAAhB,EAAmB;IACvB,IAAI,KAAKC,MAAT,EAAiB;MACb,OAAO,IAAP;IACH;;IACD,KAAKF,KAAL,GAAaA,KAAb;IACA,MAAMG,EAAE,GAAG,KAAKA,EAAhB;IACA,MAAMP,SAAS,GAAG,KAAKA,SAAvB;;IACA,IAAIO,EAAE,IAAI,IAAV,EAAgB;MACZ,KAAKA,EAAL,GAAU,KAAKC,cAAL,CAAoBR,SAApB,EAA+BO,EAA/B,EAAmCF,KAAnC,CAAV;IACH;;IACD,KAAKH,OAAL,GAAe,IAAf;IACA,KAAKG,KAAL,GAAaA,KAAb;IACA,KAAKE,EAAL,GAAU,KAAKA,EAAL,IAAW,KAAKE,cAAL,CAAoBT,SAApB,EAA+B,KAAKO,EAApC,EAAwCF,KAAxC,CAArB;IACA,OAAO,IAAP;EACH;;EACDI,cAAc,CAACT,SAAD,EAAYO,EAAZ,EAAgBF,KAAK,GAAG,CAAxB,EAA2B;IACrC,OAAOK,WAAW,CAACV,SAAS,CAACW,KAAV,CAAgBC,IAAhB,CAAqBZ,SAArB,EAAgC,IAAhC,CAAD,EAAwCK,KAAxC,CAAlB;EACH;;EACDG,cAAc,CAACR,SAAD,EAAYO,EAAZ,EAAgBF,KAAK,GAAG,CAAxB,EAA2B;IACrC,IAAIA,KAAK,KAAK,IAAV,IAAkB,KAAKA,KAAL,KAAeA,KAAjC,IAA0C,KAAKH,OAAL,KAAiB,KAA/D,EAAsE;MAClE,OAAOK,EAAP;IACH;;IACDM,aAAa,CAACN,EAAD,CAAb;IACA,OAAOO,SAAP;EACH;;EACDC,OAAO,CAACX,KAAD,EAAQC,KAAR,EAAe;IAClB,IAAI,KAAKC,MAAT,EAAiB;MACb,OAAO,IAAIU,KAAJ,CAAU,8BAAV,CAAP;IACH;;IACD,KAAKd,OAAL,GAAe,KAAf;;IACA,MAAMe,KAAK,GAAG,KAAKC,QAAL,CAAcd,KAAd,EAAqBC,KAArB,CAAd;;IACA,IAAIY,KAAJ,EAAW;MACP,OAAOA,KAAP;IACH,CAFD,MAGK,IAAI,KAAKf,OAAL,KAAiB,KAAjB,IAA0B,KAAKK,EAAL,IAAW,IAAzC,EAA+C;MAChD,KAAKA,EAAL,GAAU,KAAKC,cAAL,CAAoB,KAAKR,SAAzB,EAAoC,KAAKO,EAAzC,EAA6C,IAA7C,CAAV;IACH;EACJ;;EACDW,QAAQ,CAACd,KAAD,EAAQC,KAAR,EAAe;IACnB,IAAIc,OAAO,GAAG,KAAd;IACA,IAAIC,UAAU,GAAGN,SAAjB;;IACA,IAAI;MACA,KAAKb,IAAL,CAAUG,KAAV;IACH,CAFD,CAGA,OAAOiB,CAAP,EAAU;MACNF,OAAO,GAAG,IAAV;MACAC,UAAU,GAAG,CAAC,CAACC,CAAF,IAAOA,CAAP,IAAY,IAAIL,KAAJ,CAAUK,CAAV,CAAzB;IACH;;IACD,IAAIF,OAAJ,EAAa;MACT,KAAKG,WAAL;MACA,OAAOF,UAAP;IACH;EACJ;;EACDG,YAAY,GAAG;IACX,MAAMhB,EAAE,GAAG,KAAKA,EAAhB;IACA,MAAMP,SAAS,GAAG,KAAKA,SAAvB;IACA,MAAMwB,OAAO,GAAGxB,SAAS,CAACwB,OAA1B;IACA,MAAMC,KAAK,GAAGD,OAAO,CAACE,OAAR,CAAgB,IAAhB,CAAd;IACA,KAAKzB,IAAL,GAAY,IAAZ;IACA,KAAKG,KAAL,GAAa,IAAb;IACA,KAAKF,OAAL,GAAe,KAAf;IACA,KAAKF,SAAL,GAAiB,IAAjB;;IACA,IAAIyB,KAAK,KAAK,CAAC,CAAf,EAAkB;MACdD,OAAO,CAACG,MAAR,CAAeF,KAAf,EAAsB,CAAtB;IACH;;IACD,IAAIlB,EAAE,IAAI,IAAV,EAAgB;MACZ,KAAKA,EAAL,GAAU,KAAKC,cAAL,CAAoBR,SAApB,EAA+BO,EAA/B,EAAmC,IAAnC,CAAV;IACH;;IACD,KAAKF,KAAL,GAAa,IAAb;EACH;;AA5EmC"}, "metadata": {}, "sourceType": "module"}