{"ast": null, "code": "import { isArray } from './util/isArray';\nimport { isObject } from './util/isObject';\nimport { isFunction } from './util/isFunction';\nimport { UnsubscriptionError } from './util/UnsubscriptionError';\nexport class Subscription {\n  constructor(unsubscribe) {\n    this.closed = false;\n    this._parentOrParents = null;\n    this._subscriptions = null;\n\n    if (unsubscribe) {\n      this._ctorUnsubscribe = true;\n      this._unsubscribe = unsubscribe;\n    }\n  }\n\n  unsubscribe() {\n    let errors;\n\n    if (this.closed) {\n      return;\n    }\n\n    let {\n      _parentOrParents,\n      _ctorUnsubscribe,\n      _unsubscribe,\n      _subscriptions\n    } = this;\n    this.closed = true;\n    this._parentOrParents = null;\n    this._subscriptions = null;\n\n    if (_parentOrParents instanceof Subscription) {\n      _parentOrParents.remove(this);\n    } else if (_parentOrParents !== null) {\n      for (let index = 0; index < _parentOrParents.length; ++index) {\n        const parent = _parentOrParents[index];\n        parent.remove(this);\n      }\n    }\n\n    if (isFunction(_unsubscribe)) {\n      if (_ctorUnsubscribe) {\n        this._unsubscribe = undefined;\n      }\n\n      try {\n        _unsubscribe.call(this);\n      } catch (e) {\n        errors = e instanceof UnsubscriptionError ? flattenUnsubscriptionErrors(e.errors) : [e];\n      }\n    }\n\n    if (isArray(_subscriptions)) {\n      let index = -1;\n      let len = _subscriptions.length;\n\n      while (++index < len) {\n        const sub = _subscriptions[index];\n\n        if (isObject(sub)) {\n          try {\n            sub.unsubscribe();\n          } catch (e) {\n            errors = errors || [];\n\n            if (e instanceof UnsubscriptionError) {\n              errors = errors.concat(flattenUnsubscriptionErrors(e.errors));\n            } else {\n              errors.push(e);\n            }\n          }\n        }\n      }\n    }\n\n    if (errors) {\n      throw new UnsubscriptionError(errors);\n    }\n  }\n\n  add(teardown) {\n    let subscription = teardown;\n\n    if (!teardown) {\n      return Subscription.EMPTY;\n    }\n\n    switch (typeof teardown) {\n      case 'function':\n        subscription = new Subscription(teardown);\n\n      case 'object':\n        if (subscription === this || subscription.closed || typeof subscription.unsubscribe !== 'function') {\n          return subscription;\n        } else if (this.closed) {\n          subscription.unsubscribe();\n          return subscription;\n        } else if (!(subscription instanceof Subscription)) {\n          const tmp = subscription;\n          subscription = new Subscription();\n          subscription._subscriptions = [tmp];\n        }\n\n        break;\n\n      default:\n        {\n          throw new Error('unrecognized teardown ' + teardown + ' added to Subscription.');\n        }\n    }\n\n    let {\n      _parentOrParents\n    } = subscription;\n\n    if (_parentOrParents === null) {\n      subscription._parentOrParents = this;\n    } else if (_parentOrParents instanceof Subscription) {\n      if (_parentOrParents === this) {\n        return subscription;\n      }\n\n      subscription._parentOrParents = [_parentOrParents, this];\n    } else if (_parentOrParents.indexOf(this) === -1) {\n      _parentOrParents.push(this);\n    } else {\n      return subscription;\n    }\n\n    const subscriptions = this._subscriptions;\n\n    if (subscriptions === null) {\n      this._subscriptions = [subscription];\n    } else {\n      subscriptions.push(subscription);\n    }\n\n    return subscription;\n  }\n\n  remove(subscription) {\n    const subscriptions = this._subscriptions;\n\n    if (subscriptions) {\n      const subscriptionIndex = subscriptions.indexOf(subscription);\n\n      if (subscriptionIndex !== -1) {\n        subscriptions.splice(subscriptionIndex, 1);\n      }\n    }\n  }\n\n}\n\nSubscription.EMPTY = function (empty) {\n  empty.closed = true;\n  return empty;\n}(new Subscription());\n\nfunction flattenUnsubscriptionErrors(errors) {\n  return errors.reduce((errs, err) => errs.concat(err instanceof UnsubscriptionError ? err.errors : err), []);\n}", "map": {"version": 3, "names": ["isArray", "isObject", "isFunction", "UnsubscriptionError", "Subscription", "constructor", "unsubscribe", "closed", "_parentOrParents", "_subscriptions", "_ctorUnsubscribe", "_unsubscribe", "errors", "remove", "index", "length", "parent", "undefined", "call", "e", "flattenUnsubscriptionErrors", "len", "sub", "concat", "push", "add", "teardown", "subscription", "EMPTY", "tmp", "Error", "indexOf", "subscriptions", "subscriptionIndex", "splice", "empty", "reduce", "errs", "err"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/Subscription.js"], "sourcesContent": ["import { isArray } from './util/isArray';\nimport { isObject } from './util/isObject';\nimport { isFunction } from './util/isFunction';\nimport { UnsubscriptionError } from './util/UnsubscriptionError';\nexport class Subscription {\n    constructor(unsubscribe) {\n        this.closed = false;\n        this._parentOrParents = null;\n        this._subscriptions = null;\n        if (unsubscribe) {\n            this._ctorUnsubscribe = true;\n            this._unsubscribe = unsubscribe;\n        }\n    }\n    unsubscribe() {\n        let errors;\n        if (this.closed) {\n            return;\n        }\n        let { _parentOrParents, _ctorUnsubscribe, _unsubscribe, _subscriptions } = this;\n        this.closed = true;\n        this._parentOrParents = null;\n        this._subscriptions = null;\n        if (_parentOrParents instanceof Subscription) {\n            _parentOrParents.remove(this);\n        }\n        else if (_parentOrParents !== null) {\n            for (let index = 0; index < _parentOrParents.length; ++index) {\n                const parent = _parentOrParents[index];\n                parent.remove(this);\n            }\n        }\n        if (isFunction(_unsubscribe)) {\n            if (_ctorUnsubscribe) {\n                this._unsubscribe = undefined;\n            }\n            try {\n                _unsubscribe.call(this);\n            }\n            catch (e) {\n                errors = e instanceof UnsubscriptionError ? flattenUnsubscriptionErrors(e.errors) : [e];\n            }\n        }\n        if (isArray(_subscriptions)) {\n            let index = -1;\n            let len = _subscriptions.length;\n            while (++index < len) {\n                const sub = _subscriptions[index];\n                if (isObject(sub)) {\n                    try {\n                        sub.unsubscribe();\n                    }\n                    catch (e) {\n                        errors = errors || [];\n                        if (e instanceof UnsubscriptionError) {\n                            errors = errors.concat(flattenUnsubscriptionErrors(e.errors));\n                        }\n                        else {\n                            errors.push(e);\n                        }\n                    }\n                }\n            }\n        }\n        if (errors) {\n            throw new UnsubscriptionError(errors);\n        }\n    }\n    add(teardown) {\n        let subscription = teardown;\n        if (!teardown) {\n            return Subscription.EMPTY;\n        }\n        switch (typeof teardown) {\n            case 'function':\n                subscription = new Subscription(teardown);\n            case 'object':\n                if (subscription === this || subscription.closed || typeof subscription.unsubscribe !== 'function') {\n                    return subscription;\n                }\n                else if (this.closed) {\n                    subscription.unsubscribe();\n                    return subscription;\n                }\n                else if (!(subscription instanceof Subscription)) {\n                    const tmp = subscription;\n                    subscription = new Subscription();\n                    subscription._subscriptions = [tmp];\n                }\n                break;\n            default: {\n                throw new Error('unrecognized teardown ' + teardown + ' added to Subscription.');\n            }\n        }\n        let { _parentOrParents } = subscription;\n        if (_parentOrParents === null) {\n            subscription._parentOrParents = this;\n        }\n        else if (_parentOrParents instanceof Subscription) {\n            if (_parentOrParents === this) {\n                return subscription;\n            }\n            subscription._parentOrParents = [_parentOrParents, this];\n        }\n        else if (_parentOrParents.indexOf(this) === -1) {\n            _parentOrParents.push(this);\n        }\n        else {\n            return subscription;\n        }\n        const subscriptions = this._subscriptions;\n        if (subscriptions === null) {\n            this._subscriptions = [subscription];\n        }\n        else {\n            subscriptions.push(subscription);\n        }\n        return subscription;\n    }\n    remove(subscription) {\n        const subscriptions = this._subscriptions;\n        if (subscriptions) {\n            const subscriptionIndex = subscriptions.indexOf(subscription);\n            if (subscriptionIndex !== -1) {\n                subscriptions.splice(subscriptionIndex, 1);\n            }\n        }\n    }\n}\nSubscription.EMPTY = (function (empty) {\n    empty.closed = true;\n    return empty;\n}(new Subscription()));\nfunction flattenUnsubscriptionErrors(errors) {\n    return errors.reduce((errs, err) => errs.concat((err instanceof UnsubscriptionError) ? err.errors : err), []);\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,gBAAxB;AACA,SAASC,QAAT,QAAyB,iBAAzB;AACA,SAASC,UAAT,QAA2B,mBAA3B;AACA,SAASC,mBAAT,QAAoC,4BAApC;AACA,OAAO,MAAMC,YAAN,CAAmB;EACtBC,WAAW,CAACC,WAAD,EAAc;IACrB,KAAKC,MAAL,GAAc,KAAd;IACA,KAAKC,gBAAL,GAAwB,IAAxB;IACA,KAAKC,cAAL,GAAsB,IAAtB;;IACA,IAAIH,WAAJ,EAAiB;MACb,KAAKI,gBAAL,GAAwB,IAAxB;MACA,KAAKC,YAAL,GAAoBL,WAApB;IACH;EACJ;;EACDA,WAAW,GAAG;IACV,IAAIM,MAAJ;;IACA,IAAI,KAAKL,MAAT,EAAiB;MACb;IACH;;IACD,IAAI;MAAEC,gBAAF;MAAoBE,gBAApB;MAAsCC,YAAtC;MAAoDF;IAApD,IAAuE,IAA3E;IACA,KAAKF,MAAL,GAAc,IAAd;IACA,KAAKC,gBAAL,GAAwB,IAAxB;IACA,KAAKC,cAAL,GAAsB,IAAtB;;IACA,IAAID,gBAAgB,YAAYJ,YAAhC,EAA8C;MAC1CI,gBAAgB,CAACK,MAAjB,CAAwB,IAAxB;IACH,CAFD,MAGK,IAAIL,gBAAgB,KAAK,IAAzB,EAA+B;MAChC,KAAK,IAAIM,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGN,gBAAgB,CAACO,MAA7C,EAAqD,EAAED,KAAvD,EAA8D;QAC1D,MAAME,MAAM,GAAGR,gBAAgB,CAACM,KAAD,CAA/B;QACAE,MAAM,CAACH,MAAP,CAAc,IAAd;MACH;IACJ;;IACD,IAAIX,UAAU,CAACS,YAAD,CAAd,EAA8B;MAC1B,IAAID,gBAAJ,EAAsB;QAClB,KAAKC,YAAL,GAAoBM,SAApB;MACH;;MACD,IAAI;QACAN,YAAY,CAACO,IAAb,CAAkB,IAAlB;MACH,CAFD,CAGA,OAAOC,CAAP,EAAU;QACNP,MAAM,GAAGO,CAAC,YAAYhB,mBAAb,GAAmCiB,2BAA2B,CAACD,CAAC,CAACP,MAAH,CAA9D,GAA2E,CAACO,CAAD,CAApF;MACH;IACJ;;IACD,IAAInB,OAAO,CAACS,cAAD,CAAX,EAA6B;MACzB,IAAIK,KAAK,GAAG,CAAC,CAAb;MACA,IAAIO,GAAG,GAAGZ,cAAc,CAACM,MAAzB;;MACA,OAAO,EAAED,KAAF,GAAUO,GAAjB,EAAsB;QAClB,MAAMC,GAAG,GAAGb,cAAc,CAACK,KAAD,CAA1B;;QACA,IAAIb,QAAQ,CAACqB,GAAD,CAAZ,EAAmB;UACf,IAAI;YACAA,GAAG,CAAChB,WAAJ;UACH,CAFD,CAGA,OAAOa,CAAP,EAAU;YACNP,MAAM,GAAGA,MAAM,IAAI,EAAnB;;YACA,IAAIO,CAAC,YAAYhB,mBAAjB,EAAsC;cAClCS,MAAM,GAAGA,MAAM,CAACW,MAAP,CAAcH,2BAA2B,CAACD,CAAC,CAACP,MAAH,CAAzC,CAAT;YACH,CAFD,MAGK;cACDA,MAAM,CAACY,IAAP,CAAYL,CAAZ;YACH;UACJ;QACJ;MACJ;IACJ;;IACD,IAAIP,MAAJ,EAAY;MACR,MAAM,IAAIT,mBAAJ,CAAwBS,MAAxB,CAAN;IACH;EACJ;;EACDa,GAAG,CAACC,QAAD,EAAW;IACV,IAAIC,YAAY,GAAGD,QAAnB;;IACA,IAAI,CAACA,QAAL,EAAe;MACX,OAAOtB,YAAY,CAACwB,KAApB;IACH;;IACD,QAAQ,OAAOF,QAAf;MACI,KAAK,UAAL;QACIC,YAAY,GAAG,IAAIvB,YAAJ,CAAiBsB,QAAjB,CAAf;;MACJ,KAAK,QAAL;QACI,IAAIC,YAAY,KAAK,IAAjB,IAAyBA,YAAY,CAACpB,MAAtC,IAAgD,OAAOoB,YAAY,CAACrB,WAApB,KAAoC,UAAxF,EAAoG;UAChG,OAAOqB,YAAP;QACH,CAFD,MAGK,IAAI,KAAKpB,MAAT,EAAiB;UAClBoB,YAAY,CAACrB,WAAb;UACA,OAAOqB,YAAP;QACH,CAHI,MAIA,IAAI,EAAEA,YAAY,YAAYvB,YAA1B,CAAJ,EAA6C;UAC9C,MAAMyB,GAAG,GAAGF,YAAZ;UACAA,YAAY,GAAG,IAAIvB,YAAJ,EAAf;UACAuB,YAAY,CAAClB,cAAb,GAA8B,CAACoB,GAAD,CAA9B;QACH;;QACD;;MACJ;QAAS;UACL,MAAM,IAAIC,KAAJ,CAAU,2BAA2BJ,QAA3B,GAAsC,yBAAhD,CAAN;QACH;IAnBL;;IAqBA,IAAI;MAAElB;IAAF,IAAuBmB,YAA3B;;IACA,IAAInB,gBAAgB,KAAK,IAAzB,EAA+B;MAC3BmB,YAAY,CAACnB,gBAAb,GAAgC,IAAhC;IACH,CAFD,MAGK,IAAIA,gBAAgB,YAAYJ,YAAhC,EAA8C;MAC/C,IAAII,gBAAgB,KAAK,IAAzB,EAA+B;QAC3B,OAAOmB,YAAP;MACH;;MACDA,YAAY,CAACnB,gBAAb,GAAgC,CAACA,gBAAD,EAAmB,IAAnB,CAAhC;IACH,CALI,MAMA,IAAIA,gBAAgB,CAACuB,OAAjB,CAAyB,IAAzB,MAAmC,CAAC,CAAxC,EAA2C;MAC5CvB,gBAAgB,CAACgB,IAAjB,CAAsB,IAAtB;IACH,CAFI,MAGA;MACD,OAAOG,YAAP;IACH;;IACD,MAAMK,aAAa,GAAG,KAAKvB,cAA3B;;IACA,IAAIuB,aAAa,KAAK,IAAtB,EAA4B;MACxB,KAAKvB,cAAL,GAAsB,CAACkB,YAAD,CAAtB;IACH,CAFD,MAGK;MACDK,aAAa,CAACR,IAAd,CAAmBG,YAAnB;IACH;;IACD,OAAOA,YAAP;EACH;;EACDd,MAAM,CAACc,YAAD,EAAe;IACjB,MAAMK,aAAa,GAAG,KAAKvB,cAA3B;;IACA,IAAIuB,aAAJ,EAAmB;MACf,MAAMC,iBAAiB,GAAGD,aAAa,CAACD,OAAd,CAAsBJ,YAAtB,CAA1B;;MACA,IAAIM,iBAAiB,KAAK,CAAC,CAA3B,EAA8B;QAC1BD,aAAa,CAACE,MAAd,CAAqBD,iBAArB,EAAwC,CAAxC;MACH;IACJ;EACJ;;AA3HqB;;AA6H1B7B,YAAY,CAACwB,KAAb,GAAsB,UAAUO,KAAV,EAAiB;EACnCA,KAAK,CAAC5B,MAAN,GAAe,IAAf;EACA,OAAO4B,KAAP;AACH,CAHqB,CAGpB,IAAI/B,YAAJ,EAHoB,CAAtB;;AAIA,SAASgB,2BAAT,CAAqCR,MAArC,EAA6C;EACzC,OAAOA,MAAM,CAACwB,MAAP,CAAc,CAACC,IAAD,EAAOC,GAAP,KAAeD,IAAI,CAACd,MAAL,CAAae,GAAG,YAAYnC,mBAAhB,GAAuCmC,GAAG,CAAC1B,MAA3C,GAAoD0B,GAAhE,CAA7B,EAAmG,EAAnG,CAAP;AACH"}, "metadata": {}, "sourceType": "module"}