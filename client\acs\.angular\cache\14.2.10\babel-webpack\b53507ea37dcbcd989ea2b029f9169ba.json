{"ast": null, "code": "import { Observable } from '../Observable';\nimport { subscribeTo } from '../util/subscribeTo';\nimport { scheduled } from '../scheduled/scheduled';\nexport function from(input, scheduler) {\n  if (!scheduler) {\n    if (input instanceof Observable) {\n      return input;\n    }\n\n    return new Observable(subscribeTo(input));\n  } else {\n    return scheduled(input, scheduler);\n  }\n}", "map": {"version": 3, "names": ["Observable", "subscribeTo", "scheduled", "from", "input", "scheduler"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/from.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { subscribeTo } from '../util/subscribeTo';\nimport { scheduled } from '../scheduled/scheduled';\nexport function from(input, scheduler) {\n    if (!scheduler) {\n        if (input instanceof Observable) {\n            return input;\n        }\n        return new Observable(subscribeTo(input));\n    }\n    else {\n        return scheduled(input, scheduler);\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,WAAT,QAA4B,qBAA5B;AACA,SAASC,SAAT,QAA0B,wBAA1B;AACA,OAAO,SAASC,IAAT,CAAcC,KAAd,EAAqBC,SAArB,EAAgC;EACnC,IAAI,CAACA,SAAL,EAAgB;IACZ,IAAID,KAAK,YAAYJ,UAArB,EAAiC;MAC7B,OAAOI,KAAP;IACH;;IACD,OAAO,IAAIJ,UAAJ,CAAeC,WAAW,CAACG,KAAD,CAA1B,CAAP;EACH,CALD,MAMK;IACD,OAAOF,SAAS,CAACE,KAAD,EAAQC,SAAR,CAAhB;EACH;AACJ"}, "metadata": {}, "sourceType": "module"}