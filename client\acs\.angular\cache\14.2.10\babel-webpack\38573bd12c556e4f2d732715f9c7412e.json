{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { isDate } from '../util/isDate';\nimport { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function timeoutWith(due, withObservable, scheduler = async) {\n  return source => {\n    let absoluteTimeout = isDate(due);\n    let waitFor = absoluteTimeout ? +due - scheduler.now() : Math.abs(due);\n    return source.lift(new TimeoutWithOperator(waitFor, absoluteTimeout, withObservable, scheduler));\n  };\n}\n\nclass TimeoutWithOperator {\n  constructor(waitFor, absoluteTimeout, withObservable, scheduler) {\n    this.waitFor = waitFor;\n    this.absoluteTimeout = absoluteTimeout;\n    this.withObservable = withObservable;\n    this.scheduler = scheduler;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new TimeoutWithSubscriber(subscriber, this.absoluteTimeout, this.waitFor, this.withObservable, this.scheduler));\n  }\n\n}\n\nclass TimeoutWithSubscriber extends SimpleOuterSubscriber {\n  constructor(destination, absoluteTimeout, waitFor, withObservable, scheduler) {\n    super(destination);\n    this.absoluteTimeout = absoluteTimeout;\n    this.waitFor = waitFor;\n    this.withObservable = withObservable;\n    this.scheduler = scheduler;\n    this.scheduleTimeout();\n  }\n\n  static dispatchTimeout(subscriber) {\n    const {\n      withObservable\n    } = subscriber;\n\n    subscriber._unsubscribeAndRecycle();\n\n    subscriber.add(innerSubscribe(withObservable, new SimpleInnerSubscriber(subscriber)));\n  }\n\n  scheduleTimeout() {\n    const {\n      action\n    } = this;\n\n    if (action) {\n      this.action = action.schedule(this, this.waitFor);\n    } else {\n      this.add(this.action = this.scheduler.schedule(TimeoutWithSubscriber.dispatchTimeout, this.waitFor, this));\n    }\n  }\n\n  _next(value) {\n    if (!this.absoluteTimeout) {\n      this.scheduleTimeout();\n    }\n\n    super._next(value);\n  }\n\n  _unsubscribe() {\n    this.action = undefined;\n    this.scheduler = null;\n    this.withObservable = null;\n  }\n\n}", "map": {"version": 3, "names": ["async", "isDate", "SimpleOuterSubscriber", "innerSubscribe", "SimpleInnerSubscriber", "timeoutWith", "due", "withObservable", "scheduler", "source", "absoluteTimeout", "waitFor", "now", "Math", "abs", "lift", "TimeoutWithOperator", "constructor", "call", "subscriber", "subscribe", "TimeoutWithSubscriber", "destination", "scheduleTimeout", "dispatchTimeout", "_unsubscribeAndRecycle", "add", "action", "schedule", "_next", "value", "_unsubscribe", "undefined"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/timeoutWith.js"], "sourcesContent": ["import { async } from '../scheduler/async';\nimport { isDate } from '../util/isDate';\nimport { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function timeoutWith(due, withObservable, scheduler = async) {\n    return (source) => {\n        let absoluteTimeout = isDate(due);\n        let waitFor = absoluteTimeout ? (+due - scheduler.now()) : Math.abs(due);\n        return source.lift(new TimeoutWithOperator(waitFor, absoluteTimeout, withObservable, scheduler));\n    };\n}\nclass TimeoutWithOperator {\n    constructor(waitFor, absoluteTimeout, withObservable, scheduler) {\n        this.waitFor = waitFor;\n        this.absoluteTimeout = absoluteTimeout;\n        this.withObservable = withObservable;\n        this.scheduler = scheduler;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new TimeoutWithSubscriber(subscriber, this.absoluteTimeout, this.waitFor, this.withObservable, this.scheduler));\n    }\n}\nclass TimeoutWithSubscriber extends SimpleOuterSubscriber {\n    constructor(destination, absoluteTimeout, waitFor, withObservable, scheduler) {\n        super(destination);\n        this.absoluteTimeout = absoluteTimeout;\n        this.waitFor = waitFor;\n        this.withObservable = withObservable;\n        this.scheduler = scheduler;\n        this.scheduleTimeout();\n    }\n    static dispatchTimeout(subscriber) {\n        const { withObservable } = subscriber;\n        subscriber._unsubscribeAndRecycle();\n        subscriber.add(innerSubscribe(withObservable, new SimpleInnerSubscriber(subscriber)));\n    }\n    scheduleTimeout() {\n        const { action } = this;\n        if (action) {\n            this.action = action.schedule(this, this.waitFor);\n        }\n        else {\n            this.add(this.action = this.scheduler.schedule(TimeoutWithSubscriber.dispatchTimeout, this.waitFor, this));\n        }\n    }\n    _next(value) {\n        if (!this.absoluteTimeout) {\n            this.scheduleTimeout();\n        }\n        super._next(value);\n    }\n    _unsubscribe() {\n        this.action = undefined;\n        this.scheduler = null;\n        this.withObservable = null;\n    }\n}\n"], "mappings": "AAAA,SAASA,KAAT,QAAsB,oBAAtB;AACA,SAASC,MAAT,QAAuB,gBAAvB;AACA,SAASC,qBAAT,EAAgCC,cAAhC,EAAgDC,qBAAhD,QAA6E,mBAA7E;AACA,OAAO,SAASC,WAAT,CAAqBC,GAArB,EAA0BC,cAA1B,EAA0CC,SAAS,GAAGR,KAAtD,EAA6D;EAChE,OAAQS,MAAD,IAAY;IACf,IAAIC,eAAe,GAAGT,MAAM,CAACK,GAAD,CAA5B;IACA,IAAIK,OAAO,GAAGD,eAAe,GAAI,CAACJ,GAAD,GAAOE,SAAS,CAACI,GAAV,EAAX,GAA8BC,IAAI,CAACC,GAAL,CAASR,GAAT,CAA3D;IACA,OAAOG,MAAM,CAACM,IAAP,CAAY,IAAIC,mBAAJ,CAAwBL,OAAxB,EAAiCD,eAAjC,EAAkDH,cAAlD,EAAkEC,SAAlE,CAAZ,CAAP;EACH,CAJD;AAKH;;AACD,MAAMQ,mBAAN,CAA0B;EACtBC,WAAW,CAACN,OAAD,EAAUD,eAAV,EAA2BH,cAA3B,EAA2CC,SAA3C,EAAsD;IAC7D,KAAKG,OAAL,GAAeA,OAAf;IACA,KAAKD,eAAL,GAAuBA,eAAvB;IACA,KAAKH,cAAL,GAAsBA,cAAtB;IACA,KAAKC,SAAL,GAAiBA,SAAjB;EACH;;EACDU,IAAI,CAACC,UAAD,EAAaV,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACW,SAAP,CAAiB,IAAIC,qBAAJ,CAA0BF,UAA1B,EAAsC,KAAKT,eAA3C,EAA4D,KAAKC,OAAjE,EAA0E,KAAKJ,cAA/E,EAA+F,KAAKC,SAApG,CAAjB,CAAP;EACH;;AATqB;;AAW1B,MAAMa,qBAAN,SAAoCnB,qBAApC,CAA0D;EACtDe,WAAW,CAACK,WAAD,EAAcZ,eAAd,EAA+BC,OAA/B,EAAwCJ,cAAxC,EAAwDC,SAAxD,EAAmE;IAC1E,MAAMc,WAAN;IACA,KAAKZ,eAAL,GAAuBA,eAAvB;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKJ,cAAL,GAAsBA,cAAtB;IACA,KAAKC,SAAL,GAAiBA,SAAjB;IACA,KAAKe,eAAL;EACH;;EACqB,OAAfC,eAAe,CAACL,UAAD,EAAa;IAC/B,MAAM;MAAEZ;IAAF,IAAqBY,UAA3B;;IACAA,UAAU,CAACM,sBAAX;;IACAN,UAAU,CAACO,GAAX,CAAevB,cAAc,CAACI,cAAD,EAAiB,IAAIH,qBAAJ,CAA0Be,UAA1B,CAAjB,CAA7B;EACH;;EACDI,eAAe,GAAG;IACd,MAAM;MAAEI;IAAF,IAAa,IAAnB;;IACA,IAAIA,MAAJ,EAAY;MACR,KAAKA,MAAL,GAAcA,MAAM,CAACC,QAAP,CAAgB,IAAhB,EAAsB,KAAKjB,OAA3B,CAAd;IACH,CAFD,MAGK;MACD,KAAKe,GAAL,CAAS,KAAKC,MAAL,GAAc,KAAKnB,SAAL,CAAeoB,QAAf,CAAwBP,qBAAqB,CAACG,eAA9C,EAA+D,KAAKb,OAApE,EAA6E,IAA7E,CAAvB;IACH;EACJ;;EACDkB,KAAK,CAACC,KAAD,EAAQ;IACT,IAAI,CAAC,KAAKpB,eAAV,EAA2B;MACvB,KAAKa,eAAL;IACH;;IACD,MAAMM,KAAN,CAAYC,KAAZ;EACH;;EACDC,YAAY,GAAG;IACX,KAAKJ,MAAL,GAAcK,SAAd;IACA,KAAKxB,SAAL,GAAiB,IAAjB;IACA,KAAKD,cAAL,GAAsB,IAAtB;EACH;;AAjCqD"}, "metadata": {}, "sourceType": "module"}