{"ast": null, "code": "import { __decorate, __metadata, __param } from 'tslib';\nimport { ElementRef, Directive, NgModule, InjectionToken, Inject, ɵɵdefineInjectable, ɵɵinject, Injectable, ComponentFactoryResolver, ApplicationRef, SecurityContext, Injector, NgZone, INJECTOR, HostBinding, HostListener, Component } from '@angular/core';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { Subject } from 'rxjs';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as ɵngcc0 from '@angular/core';\nimport * as ɵngcc1 from '@angular/platform-browser';\nimport * as ɵngcc2 from '@angular/common';\nconst _c0 = [\"toast-component\", \"\"];\n\nfunction Toast_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"button\", 5);\n    ɵngcc0.ɵɵlistener(\"click\", function Toast_button_0_Template_button_click_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r6);\n      const ctx_r5 = ɵngcc0.ɵɵnextContext();\n      return ɵngcc0.ɵɵresetView(ctx_r5.remove());\n    });\n    ɵngcc0.ɵɵelementStart(1, \"span\", 6);\n    ɵngcc0.ɵɵtext(2, \"\\u00D7\");\n    ɵngcc0.ɵɵelementEnd()();\n  }\n}\n\nfunction Toast_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementContainerStart(0);\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate1(\"[\", ctx_r7.duplicatesCount + 1, \"]\");\n  }\n}\n\nfunction Toast_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\");\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵtemplate(2, Toast_div_1_ng_container_2_Template, 2, 1, \"ng-container\", 4);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassMap(ctx_r1.options.titleClass);\n    ɵngcc0.ɵɵattribute(\"aria-label\", ctx_r1.title);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate1(\" \", ctx_r1.title, \" \");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r1.duplicatesCount);\n  }\n}\n\nfunction Toast_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"div\", 7);\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassMap(ctx_r2.options.messageClass);\n    ɵngcc0.ɵɵproperty(\"innerHTML\", ctx_r2.message, ɵngcc0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction Toast_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 8);\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassMap(ctx_r3.options.messageClass);\n    ɵngcc0.ɵɵattribute(\"aria-label\", ctx_r3.message);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate1(\" \", ctx_r3.message, \" \");\n  }\n}\n\nfunction Toast_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\");\n    ɵngcc0.ɵɵelement(1, \"div\", 9);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵstyleProp(\"width\", ctx_r4.width + \"%\");\n  }\n}\n\nfunction ToastNoAnimation_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = ɵngcc0.ɵɵgetCurrentView();\n\n    ɵngcc0.ɵɵelementStart(0, \"button\", 5);\n    ɵngcc0.ɵɵlistener(\"click\", function ToastNoAnimation_button_0_Template_button_click_0_listener() {\n      ɵngcc0.ɵɵrestoreView(_r6);\n      const ctx_r5 = ɵngcc0.ɵɵnextContext();\n      return ɵngcc0.ɵɵresetView(ctx_r5.remove());\n    });\n    ɵngcc0.ɵɵelementStart(1, \"span\", 6);\n    ɵngcc0.ɵɵtext(2, \"\\u00D7\");\n    ɵngcc0.ɵɵelementEnd()();\n  }\n}\n\nfunction ToastNoAnimation_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementContainerStart(0);\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate1(\"[\", ctx_r7.duplicatesCount + 1, \"]\");\n  }\n}\n\nfunction ToastNoAnimation_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\");\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵtemplate(2, ToastNoAnimation_div_1_ng_container_2_Template, 2, 1, \"ng-container\", 4);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassMap(ctx_r1.options.titleClass);\n    ɵngcc0.ɵɵattribute(\"aria-label\", ctx_r1.title);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate1(\" \", ctx_r1.title, \" \");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r1.duplicatesCount);\n  }\n}\n\nfunction ToastNoAnimation_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"div\", 7);\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassMap(ctx_r2.options.messageClass);\n    ɵngcc0.ɵɵproperty(\"innerHTML\", ctx_r2.message, ɵngcc0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction ToastNoAnimation_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 8);\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassMap(ctx_r3.options.messageClass);\n    ɵngcc0.ɵɵattribute(\"aria-label\", ctx_r3.message);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate1(\" \", ctx_r3.message, \" \");\n  }\n}\n\nfunction ToastNoAnimation_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\");\n    ɵngcc0.ɵɵelement(1, \"div\", 9);\n    ɵngcc0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵstyleProp(\"width\", ctx_r4.width + \"%\");\n  }\n}\n\nlet ToastContainerDirective = class ToastContainerDirective {\n  constructor(el) {\n    this.el = el;\n  }\n\n  getContainerElement() {\n    return this.el.nativeElement;\n  }\n\n};\n\nToastContainerDirective.ɵfac = function ToastContainerDirective_Factory(t) {\n  return new (t || ToastContainerDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef));\n};\n\nToastContainerDirective.ɵdir = /*@__PURE__*/ɵngcc0.ɵɵdefineDirective({\n  type: ToastContainerDirective,\n  selectors: [[\"\", \"toastContainer\", \"\"]],\n  exportAs: [\"toastContainer\"]\n});\n\nToastContainerDirective.ctorParameters = () => [{\n  type: ElementRef\n}];\n\nToastContainerDirective = __decorate([__metadata(\"design:paramtypes\", [ElementRef])], ToastContainerDirective);\nlet ToastContainerModule = class ToastContainerModule {};\n\nToastContainerModule.ɵfac = function ToastContainerModule_Factory(t) {\n  return new (t || ToastContainerModule)();\n};\n\nToastContainerModule.ɵmod = /*@__PURE__*/ɵngcc0.ɵɵdefineNgModule({\n  type: ToastContainerModule\n});\nToastContainerModule.ɵinj = /*@__PURE__*/ɵngcc0.ɵɵdefineInjector({});\n/**\n * Everything a toast needs to launch\n */\n\nclass ToastPackage {\n  constructor(toastId, config, message, title, toastType, toastRef) {\n    this.toastId = toastId;\n    this.config = config;\n    this.message = message;\n    this.title = title;\n    this.toastType = toastType;\n    this.toastRef = toastRef;\n    this._onTap = new Subject();\n    this._onAction = new Subject();\n    this.toastRef.afterClosed().subscribe(() => {\n      this._onAction.complete();\n\n      this._onTap.complete();\n    });\n  }\n  /** Fired on click */\n\n\n  triggerTap() {\n    this._onTap.next();\n\n    if (this.config.tapToDismiss) {\n      this._onTap.complete();\n    }\n  }\n\n  onTap() {\n    return this._onTap.asObservable();\n  }\n  /** available for use in custom toast */\n\n\n  triggerAction(action) {\n    this._onAction.next(action);\n  }\n\n  onAction() {\n    return this._onAction.asObservable();\n  }\n\n}\n\nconst DefaultNoComponentGlobalConfig = {\n  maxOpened: 0,\n  autoDismiss: false,\n  newestOnTop: true,\n  preventDuplicates: false,\n  countDuplicates: false,\n  resetTimeoutOnDuplicate: false,\n  iconClasses: {\n    error: 'toast-error',\n    info: 'toast-info',\n    success: 'toast-success',\n    warning: 'toast-warning'\n  },\n  // Individual\n  closeButton: false,\n  disableTimeOut: false,\n  timeOut: 5000,\n  extendedTimeOut: 1000,\n  enableHtml: false,\n  progressBar: false,\n  toastClass: 'ngx-toastr',\n  positionClass: 'toast-top-right',\n  titleClass: 'toast-title',\n  messageClass: 'toast-message',\n  easing: 'ease-in',\n  easeTime: 300,\n  tapToDismiss: true,\n  onActivateTick: false,\n  progressAnimation: 'decreasing'\n};\nconst TOAST_CONFIG = new InjectionToken('ToastConfig');\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\n\nclass ComponentPortal {\n  constructor(component, injector) {\n    this.component = component;\n    this.injector = injector;\n  }\n  /** Attach this portal to a host. */\n\n\n  attach(host, newestOnTop) {\n    this._attachedHost = host;\n    return host.attach(this, newestOnTop);\n  }\n  /** Detach this portal from its host */\n\n\n  detach() {\n    const host = this._attachedHost;\n\n    if (host) {\n      this._attachedHost = undefined;\n      return host.detach();\n    }\n  }\n  /** Whether this portal is attached to a host. */\n\n\n  get isAttached() {\n    return this._attachedHost != null;\n  }\n  /**\n   * Sets the PortalHost reference without performing `attach()`. This is used directly by\n   * the PortalHost when it is performing an `attach()` or `detach()`.\n   */\n\n\n  setAttachedHost(host) {\n    this._attachedHost = host;\n  }\n\n}\n/**\n * Partial implementation of PortalHost that only deals with attaching a\n * ComponentPortal\n */\n\n\nclass BasePortalHost {\n  attach(portal, newestOnTop) {\n    this._attachedPortal = portal;\n    return this.attachComponentPortal(portal, newestOnTop);\n  }\n\n  detach() {\n    if (this._attachedPortal) {\n      this._attachedPortal.setAttachedHost();\n    }\n\n    this._attachedPortal = undefined;\n\n    if (this._disposeFn) {\n      this._disposeFn();\n\n      this._disposeFn = undefined;\n    }\n  }\n\n  setDisposeFn(fn) {\n    this._disposeFn = fn;\n  }\n\n}\n/**\n * A PortalHost for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n *\n * This is the only part of the portal core that directly touches the DOM.\n */\n\n\nclass DomPortalHost extends BasePortalHost {\n  constructor(_hostDomElement, _componentFactoryResolver, _appRef) {\n    super();\n    this._hostDomElement = _hostDomElement;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._appRef = _appRef;\n  }\n  /**\n   * Attach the given ComponentPortal to DOM element using the ComponentFactoryResolver.\n   * @param portal Portal to be attached\n   */\n\n\n  attachComponentPortal(portal, newestOnTop) {\n    const componentFactory = this._componentFactoryResolver.resolveComponentFactory(portal.component);\n\n    let componentRef; // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n    // for the component (in terms of Angular's component tree, not rendering).\n    // When the ViewContainerRef is missing, we use the factory to create the component directly\n    // and then manually attach the ChangeDetector for that component to the application (which\n    // happens automatically when using a ViewContainer).\n\n    componentRef = componentFactory.create(portal.injector); // When creating a component outside of a ViewContainer, we need to manually register\n    // its ChangeDetector with the application. This API is unfortunately not yet published\n    // in Angular core. The change detector must also be deregistered when the component\n    // is destroyed to prevent memory leaks.\n\n    this._appRef.attachView(componentRef.hostView);\n\n    this.setDisposeFn(() => {\n      this._appRef.detachView(componentRef.hostView);\n\n      componentRef.destroy();\n    }); // At this point the component has been instantiated, so we move it to the location in the DOM\n    // where we want it to be rendered.\n\n    if (newestOnTop) {\n      this._hostDomElement.insertBefore(this._getComponentRootNode(componentRef), this._hostDomElement.firstChild);\n    } else {\n      this._hostDomElement.appendChild(this._getComponentRootNode(componentRef));\n    }\n\n    return componentRef;\n  }\n  /** Gets the root HTMLElement for an instantiated component. */\n\n\n  _getComponentRootNode(componentRef) {\n    return componentRef.hostView.rootNodes[0];\n  }\n\n}\n/** Container inside which all toasts will render. */\n\n\nlet OverlayContainer = class OverlayContainer {\n  constructor(_document) {\n    this._document = _document;\n  }\n\n  ngOnDestroy() {\n    if (this._containerElement && this._containerElement.parentNode) {\n      this._containerElement.parentNode.removeChild(this._containerElement);\n    }\n  }\n  /**\n   * This method returns the overlay container element. It will lazily\n   * create the element the first time  it is called to facilitate using\n   * the container in non-browser environments.\n   * @returns the container element\n   */\n\n\n  getContainerElement() {\n    if (!this._containerElement) {\n      this._createContainer();\n    }\n\n    return this._containerElement;\n  }\n  /**\n   * Create the overlay container element, which is simply a div\n   * with the 'cdk-overlay-container' class on the document body.\n   */\n\n\n  _createContainer() {\n    const container = this._document.createElement('div');\n\n    container.classList.add('overlay-container');\n\n    this._document.body.appendChild(container);\n\n    this._containerElement = container;\n  }\n\n};\n\nOverlayContainer.ɵfac = function OverlayContainer_Factory(t) {\n  return new (t || OverlayContainer)(ɵngcc0.ɵɵinject(DOCUMENT));\n};\n\nOverlayContainer.ɵprov = /*@__PURE__*/ɵngcc0.ɵɵdefineInjectable({\n  token: OverlayContainer,\n  factory: function (t) {\n    return OverlayContainer.ɵfac(t);\n  },\n  providedIn: 'root'\n});\n\nOverlayContainer.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [DOCUMENT]\n  }]\n}];\n\nOverlayContainer.ngInjectableDef = ɵɵdefineInjectable({\n  factory: function OverlayContainer_Factory() {\n    return new OverlayContainer(ɵɵinject(DOCUMENT));\n  },\n  token: OverlayContainer,\n  providedIn: \"root\"\n});\nOverlayContainer = __decorate([__param(0, Inject(DOCUMENT)), __metadata(\"design:paramtypes\", [Object])], OverlayContainer);\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\n\nclass OverlayRef {\n  constructor(_portalHost) {\n    this._portalHost = _portalHost;\n  }\n\n  attach(portal, newestOnTop = true) {\n    return this._portalHost.attach(portal, newestOnTop);\n  }\n  /**\n   * Detaches an overlay from a portal.\n   * @returns Resolves when the overlay has been detached.\n   */\n\n\n  detach() {\n    return this._portalHost.detach();\n  }\n\n}\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalHost, so any kind of Portal can be loaded into one.\n */\n\n\nlet Overlay = class Overlay {\n  constructor(_overlayContainer, _componentFactoryResolver, _appRef, _document) {\n    this._overlayContainer = _overlayContainer;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._appRef = _appRef;\n    this._document = _document; // Namespace panes by overlay container\n\n    this._paneElements = new Map();\n  }\n  /**\n   * Creates an overlay.\n   * @returns A reference to the created overlay.\n   */\n\n\n  create(positionClass, overlayContainer) {\n    // get existing pane if possible\n    return this._createOverlayRef(this.getPaneElement(positionClass, overlayContainer));\n  }\n\n  getPaneElement(positionClass = '', overlayContainer) {\n    if (!this._paneElements.get(overlayContainer)) {\n      this._paneElements.set(overlayContainer, {});\n    }\n\n    if (!this._paneElements.get(overlayContainer)[positionClass]) {\n      this._paneElements.get(overlayContainer)[positionClass] = this._createPaneElement(positionClass, overlayContainer);\n    }\n\n    return this._paneElements.get(overlayContainer)[positionClass];\n  }\n  /**\n   * Creates the DOM element for an overlay and appends it to the overlay container.\n   * @returns Newly-created pane element\n   */\n\n\n  _createPaneElement(positionClass, overlayContainer) {\n    const pane = this._document.createElement('div');\n\n    pane.id = 'toast-container';\n    pane.classList.add(positionClass);\n    pane.classList.add('toast-container');\n\n    if (!overlayContainer) {\n      this._overlayContainer.getContainerElement().appendChild(pane);\n    } else {\n      overlayContainer.getContainerElement().appendChild(pane);\n    }\n\n    return pane;\n  }\n  /**\n   * Create a DomPortalHost into which the overlay content can be loaded.\n   * @param pane The DOM element to turn into a portal host.\n   * @returns A portal host for the given DOM element.\n   */\n\n\n  _createPortalHost(pane) {\n    return new DomPortalHost(pane, this._componentFactoryResolver, this._appRef);\n  }\n  /**\n   * Creates an OverlayRef for an overlay in the given DOM element.\n   * @param pane DOM element for the overlay\n   */\n\n\n  _createOverlayRef(pane) {\n    return new OverlayRef(this._createPortalHost(pane));\n  }\n\n};\n\nOverlay.ɵfac = function Overlay_Factory(t) {\n  return new (t || Overlay)(ɵngcc0.ɵɵinject(OverlayContainer), ɵngcc0.ɵɵinject(ɵngcc0.ComponentFactoryResolver), ɵngcc0.ɵɵinject(ɵngcc0.ApplicationRef), ɵngcc0.ɵɵinject(DOCUMENT));\n};\n\nOverlay.ɵprov = /*@__PURE__*/ɵngcc0.ɵɵdefineInjectable({\n  token: Overlay,\n  factory: function (t) {\n    return Overlay.ɵfac(t);\n  },\n  providedIn: 'root'\n});\n\nOverlay.ctorParameters = () => [{\n  type: OverlayContainer\n}, {\n  type: ComponentFactoryResolver\n}, {\n  type: ApplicationRef\n}, {\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [DOCUMENT]\n  }]\n}];\n\nOverlay.ngInjectableDef = ɵɵdefineInjectable({\n  factory: function Overlay_Factory() {\n    return new Overlay(ɵɵinject(OverlayContainer), ɵɵinject(ComponentFactoryResolver), ɵɵinject(ApplicationRef), ɵɵinject(DOCUMENT));\n  },\n  token: Overlay,\n  providedIn: \"root\"\n});\nOverlay = __decorate([__param(3, Inject(DOCUMENT)), __metadata(\"design:paramtypes\", [OverlayContainer, ComponentFactoryResolver, ApplicationRef, Object])], Overlay);\n/**\n * Reference to a toast opened via the Toastr service.\n */\n\nclass ToastRef {\n  constructor(_overlayRef) {\n    this._overlayRef = _overlayRef;\n    /** Count of duplicates of this toast */\n\n    this.duplicatesCount = 0;\n    /** Subject for notifying the user that the toast has finished closing. */\n\n    this._afterClosed = new Subject();\n    /** triggered when toast is activated */\n\n    this._activate = new Subject();\n    /** notifies the toast that it should close before the timeout */\n\n    this._manualClose = new Subject();\n    /** notifies the toast that it should reset the timeouts */\n\n    this._resetTimeout = new Subject();\n    /** notifies the toast that it should count a duplicate toast */\n\n    this._countDuplicate = new Subject();\n  }\n\n  manualClose() {\n    this._manualClose.next();\n\n    this._manualClose.complete();\n  }\n\n  manualClosed() {\n    return this._manualClose.asObservable();\n  }\n\n  timeoutReset() {\n    return this._resetTimeout.asObservable();\n  }\n\n  countDuplicate() {\n    return this._countDuplicate.asObservable();\n  }\n  /**\n   * Close the toast.\n   */\n\n\n  close() {\n    this._overlayRef.detach();\n\n    this._afterClosed.next();\n\n    this._manualClose.next();\n\n    this._afterClosed.complete();\n\n    this._manualClose.complete();\n\n    this._activate.complete();\n\n    this._resetTimeout.complete();\n\n    this._countDuplicate.complete();\n  }\n  /** Gets an observable that is notified when the toast is finished closing. */\n\n\n  afterClosed() {\n    return this._afterClosed.asObservable();\n  }\n\n  isInactive() {\n    return this._activate.isStopped;\n  }\n\n  activate() {\n    this._activate.next();\n\n    this._activate.complete();\n  }\n  /** Gets an observable that is notified when the toast has started opening. */\n\n\n  afterActivate() {\n    return this._activate.asObservable();\n  }\n  /** Reset the toast timouts and count duplicates */\n\n\n  onDuplicate(resetTimeout, countDuplicate) {\n    if (resetTimeout) {\n      this._resetTimeout.next();\n    }\n\n    if (countDuplicate) {\n      this._countDuplicate.next(++this.duplicatesCount);\n    }\n  }\n\n}\n/** Custom injector type specifically for instantiating components with a toast. */\n\n\nclass ToastInjector {\n  constructor(_toastPackage, _parentInjector) {\n    this._toastPackage = _toastPackage;\n    this._parentInjector = _parentInjector;\n  }\n\n  get(token, notFoundValue, flags) {\n    if (token === ToastPackage) {\n      return this._toastPackage;\n    }\n\n    return this._parentInjector.get(token, notFoundValue, flags);\n  }\n\n}\n\nlet ToastrService = class ToastrService {\n  constructor(token, overlay, _injector, sanitizer, ngZone) {\n    this.overlay = overlay;\n    this._injector = _injector;\n    this.sanitizer = sanitizer;\n    this.ngZone = ngZone;\n    this.currentlyActive = 0;\n    this.toasts = [];\n    this.index = 0;\n    this.toastrConfig = Object.assign({}, token.default, token.config);\n\n    if (token.config.iconClasses) {\n      this.toastrConfig.iconClasses = Object.assign({}, token.default.iconClasses, token.config.iconClasses);\n    }\n  }\n  /** show toast */\n\n\n  show(message, title, override = {}, type = '') {\n    return this._preBuildNotification(type, message, title, this.applyConfig(override));\n  }\n  /** show successful toast */\n\n\n  success(message, title, override = {}) {\n    const type = this.toastrConfig.iconClasses.success || '';\n    return this._preBuildNotification(type, message, title, this.applyConfig(override));\n  }\n  /** show error toast */\n\n\n  error(message, title, override = {}) {\n    const type = this.toastrConfig.iconClasses.error || '';\n    return this._preBuildNotification(type, message, title, this.applyConfig(override));\n  }\n  /** show info toast */\n\n\n  info(message, title, override = {}) {\n    const type = this.toastrConfig.iconClasses.info || '';\n    return this._preBuildNotification(type, message, title, this.applyConfig(override));\n  }\n  /** show warning toast */\n\n\n  warning(message, title, override = {}) {\n    const type = this.toastrConfig.iconClasses.warning || '';\n    return this._preBuildNotification(type, message, title, this.applyConfig(override));\n  }\n  /**\n   * Remove all or a single toast by id\n   */\n\n\n  clear(toastId) {\n    // Call every toastRef manualClose function\n    for (const toast of this.toasts) {\n      if (toastId !== undefined) {\n        if (toast.toastId === toastId) {\n          toast.toastRef.manualClose();\n          return;\n        }\n      } else {\n        toast.toastRef.manualClose();\n      }\n    }\n  }\n  /**\n   * Remove and destroy a single toast by id\n   */\n\n\n  remove(toastId) {\n    const found = this._findToast(toastId);\n\n    if (!found) {\n      return false;\n    }\n\n    found.activeToast.toastRef.close();\n    this.toasts.splice(found.index, 1);\n    this.currentlyActive = this.currentlyActive - 1;\n\n    if (!this.toastrConfig.maxOpened || !this.toasts.length) {\n      return false;\n    }\n\n    if (this.currentlyActive < this.toastrConfig.maxOpened && this.toasts[this.currentlyActive]) {\n      const p = this.toasts[this.currentlyActive].toastRef;\n\n      if (!p.isInactive()) {\n        this.currentlyActive = this.currentlyActive + 1;\n        p.activate();\n      }\n    }\n\n    return true;\n  }\n  /**\n   * Determines if toast message is already shown\n   */\n\n\n  findDuplicate(message, resetOnDuplicate, countDuplicates) {\n    for (const toast of this.toasts) {\n      if (toast.message === message) {\n        toast.toastRef.onDuplicate(resetOnDuplicate, countDuplicates);\n        return toast;\n      }\n    }\n\n    return null;\n  }\n  /** create a clone of global config and apply individual settings */\n\n\n  applyConfig(override = {}) {\n    return Object.assign({}, this.toastrConfig, override);\n  }\n  /**\n   * Find toast object by id\n   */\n\n\n  _findToast(toastId) {\n    for (let i = 0; i < this.toasts.length; i++) {\n      if (this.toasts[i].toastId === toastId) {\n        return {\n          index: i,\n          activeToast: this.toasts[i]\n        };\n      }\n    }\n\n    return null;\n  }\n  /**\n   * Determines the need to run inside angular's zone then builds the toast\n   */\n\n\n  _preBuildNotification(toastType, message, title, config) {\n    if (config.onActivateTick) {\n      return this.ngZone.run(() => this._buildNotification(toastType, message, title, config));\n    }\n\n    return this._buildNotification(toastType, message, title, config);\n  }\n  /**\n   * Creates and attaches toast data to component\n   * returns the active toast, or in case preventDuplicates is enabled the original/non-duplicate active toast.\n   */\n\n\n  _buildNotification(toastType, message, title, config) {\n    if (!config.toastComponent) {\n      throw new Error('toastComponent required');\n    } // max opened and auto dismiss = true\n    // if timeout = 0 resetting it would result in setting this.hideTime = Date.now(). Hence, we only want to reset timeout if there is\n    // a timeout at all\n\n\n    const duplicate = this.findDuplicate(message, this.toastrConfig.resetTimeoutOnDuplicate && config.timeOut > 0, this.toastrConfig.countDuplicates);\n\n    if (message && this.toastrConfig.preventDuplicates && duplicate !== null) {\n      return duplicate;\n    }\n\n    this.previousToastMessage = message;\n    let keepInactive = false;\n\n    if (this.toastrConfig.maxOpened && this.currentlyActive >= this.toastrConfig.maxOpened) {\n      keepInactive = true;\n\n      if (this.toastrConfig.autoDismiss) {\n        this.clear(this.toasts[0].toastId);\n      }\n    }\n\n    const overlayRef = this.overlay.create(config.positionClass, this.overlayContainer);\n    this.index = this.index + 1;\n    let sanitizedMessage = message;\n\n    if (message && config.enableHtml) {\n      sanitizedMessage = this.sanitizer.sanitize(SecurityContext.HTML, message);\n    }\n\n    const toastRef = new ToastRef(overlayRef);\n    const toastPackage = new ToastPackage(this.index, config, sanitizedMessage, title, toastType, toastRef);\n    const toastInjector = new ToastInjector(toastPackage, this._injector);\n    const component = new ComponentPortal(config.toastComponent, toastInjector);\n    const portal = overlayRef.attach(component, this.toastrConfig.newestOnTop);\n    toastRef.componentInstance = portal._component;\n    const ins = {\n      toastId: this.index,\n      message: message || '',\n      toastRef,\n      onShown: toastRef.afterActivate(),\n      onHidden: toastRef.afterClosed(),\n      onTap: toastPackage.onTap(),\n      onAction: toastPackage.onAction(),\n      portal\n    };\n\n    if (!keepInactive) {\n      this.currentlyActive = this.currentlyActive + 1;\n      setTimeout(() => {\n        ins.toastRef.activate();\n      });\n    }\n\n    this.toasts.push(ins);\n    return ins;\n  }\n\n};\n\nToastrService.ɵfac = function ToastrService_Factory(t) {\n  return new (t || ToastrService)(ɵngcc0.ɵɵinject(TOAST_CONFIG), ɵngcc0.ɵɵinject(Overlay), ɵngcc0.ɵɵinject(ɵngcc0.Injector), ɵngcc0.ɵɵinject(ɵngcc1.DomSanitizer), ɵngcc0.ɵɵinject(ɵngcc0.NgZone));\n};\n\nToastrService.ɵprov = /*@__PURE__*/ɵngcc0.ɵɵdefineInjectable({\n  token: ToastrService,\n  factory: function (t) {\n    return ToastrService.ɵfac(t);\n  },\n  providedIn: 'root'\n});\n\nToastrService.ctorParameters = () => [{\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [TOAST_CONFIG]\n  }]\n}, {\n  type: Overlay\n}, {\n  type: Injector\n}, {\n  type: DomSanitizer\n}, {\n  type: NgZone\n}];\n\nToastrService.ngInjectableDef = ɵɵdefineInjectable({\n  factory: function ToastrService_Factory() {\n    return new ToastrService(ɵɵinject(TOAST_CONFIG), ɵɵinject(Overlay), ɵɵinject(INJECTOR), ɵɵinject(DomSanitizer), ɵɵinject(NgZone));\n  },\n  token: ToastrService,\n  providedIn: \"root\"\n});\nToastrService = __decorate([__param(0, Inject(TOAST_CONFIG)), __metadata(\"design:paramtypes\", [Object, Overlay, Injector, DomSanitizer, NgZone])], ToastrService);\nlet Toast = class Toast {\n  constructor(toastrService, toastPackage, ngZone) {\n    this.toastrService = toastrService;\n    this.toastPackage = toastPackage;\n    this.ngZone = ngZone;\n    /** width of progress bar */\n\n    this.width = -1;\n    /** a combination of toast type and options.toastClass */\n\n    this.toastClasses = '';\n    /** controls animation */\n\n    this.state = {\n      value: 'inactive',\n      params: {\n        easeTime: this.toastPackage.config.easeTime,\n        easing: 'ease-in'\n      }\n    };\n    this.message = toastPackage.message;\n    this.title = toastPackage.title;\n    this.options = toastPackage.config;\n    this.originalTimeout = toastPackage.config.timeOut;\n    this.toastClasses = `${toastPackage.toastType} ${toastPackage.config.toastClass}`;\n    this.sub = toastPackage.toastRef.afterActivate().subscribe(() => {\n      this.activateToast();\n    });\n    this.sub1 = toastPackage.toastRef.manualClosed().subscribe(() => {\n      this.remove();\n    });\n    this.sub2 = toastPackage.toastRef.timeoutReset().subscribe(() => {\n      this.resetTimeout();\n    });\n    this.sub3 = toastPackage.toastRef.countDuplicate().subscribe(count => {\n      this.duplicatesCount = count;\n    });\n  }\n  /** hides component when waiting to be displayed */\n\n\n  get displayStyle() {\n    if (this.state.value === 'inactive') {\n      return 'none';\n    }\n  }\n\n  ngOnDestroy() {\n    this.sub.unsubscribe();\n    this.sub1.unsubscribe();\n    this.sub2.unsubscribe();\n    this.sub3.unsubscribe();\n    clearInterval(this.intervalId);\n    clearTimeout(this.timeout);\n  }\n  /**\n   * activates toast and sets timeout\n   */\n\n\n  activateToast() {\n    this.state = Object.assign({}, this.state, {\n      value: 'active'\n    });\n\n    if (!(this.options.disableTimeOut === true || this.options.disableTimeOut === 'timeOut') && this.options.timeOut) {\n      this.outsideTimeout(() => this.remove(), this.options.timeOut);\n      this.hideTime = new Date().getTime() + this.options.timeOut;\n\n      if (this.options.progressBar) {\n        this.outsideInterval(() => this.updateProgress(), 10);\n      }\n    }\n  }\n  /**\n   * updates progress bar width\n   */\n\n\n  updateProgress() {\n    if (this.width === 0 || this.width === 100 || !this.options.timeOut) {\n      return;\n    }\n\n    const now = new Date().getTime();\n    const remaining = this.hideTime - now;\n    this.width = remaining / this.options.timeOut * 100;\n\n    if (this.options.progressAnimation === 'increasing') {\n      this.width = 100 - this.width;\n    }\n\n    if (this.width <= 0) {\n      this.width = 0;\n    }\n\n    if (this.width >= 100) {\n      this.width = 100;\n    }\n  }\n\n  resetTimeout() {\n    clearTimeout(this.timeout);\n    clearInterval(this.intervalId);\n    this.state = Object.assign({}, this.state, {\n      value: 'active'\n    });\n    this.outsideTimeout(() => this.remove(), this.originalTimeout);\n    this.options.timeOut = this.originalTimeout;\n    this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n    this.width = -1;\n\n    if (this.options.progressBar) {\n      this.outsideInterval(() => this.updateProgress(), 10);\n    }\n  }\n  /**\n   * tells toastrService to remove this toast after animation time\n   */\n\n\n  remove() {\n    if (this.state.value === 'removed') {\n      return;\n    }\n\n    clearTimeout(this.timeout);\n    this.state = Object.assign({}, this.state, {\n      value: 'removed'\n    });\n    this.outsideTimeout(() => this.toastrService.remove(this.toastPackage.toastId), +this.toastPackage.config.easeTime);\n  }\n\n  tapToast() {\n    if (this.state.value === 'removed') {\n      return;\n    }\n\n    this.toastPackage.triggerTap();\n\n    if (this.options.tapToDismiss) {\n      this.remove();\n    }\n  }\n\n  stickAround() {\n    if (this.state.value === 'removed') {\n      return;\n    }\n\n    clearTimeout(this.timeout);\n    this.options.timeOut = 0;\n    this.hideTime = 0; // disable progressBar\n\n    clearInterval(this.intervalId);\n    this.width = 0;\n  }\n\n  delayedHideToast() {\n    if (this.options.disableTimeOut === true || this.options.disableTimeOut === 'extendedTimeOut' || this.options.extendedTimeOut === 0 || this.state.value === 'removed') {\n      return;\n    }\n\n    this.outsideTimeout(() => this.remove(), this.options.extendedTimeOut);\n    this.options.timeOut = this.options.extendedTimeOut;\n    this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n    this.width = -1;\n\n    if (this.options.progressBar) {\n      this.outsideInterval(() => this.updateProgress(), 10);\n    }\n  }\n\n  outsideTimeout(func, timeout) {\n    if (this.ngZone) {\n      this.ngZone.runOutsideAngular(() => this.timeout = setTimeout(() => this.runInsideAngular(func), timeout));\n    } else {\n      this.timeout = setTimeout(() => func(), timeout);\n    }\n  }\n\n  outsideInterval(func, timeout) {\n    if (this.ngZone) {\n      this.ngZone.runOutsideAngular(() => this.intervalId = setInterval(() => this.runInsideAngular(func), timeout));\n    } else {\n      this.intervalId = setInterval(() => func(), timeout);\n    }\n  }\n\n  runInsideAngular(func) {\n    if (this.ngZone) {\n      this.ngZone.run(() => func());\n    } else {\n      func();\n    }\n  }\n\n};\n\nToast.ɵfac = function Toast_Factory(t) {\n  return new (t || Toast)(ɵngcc0.ɵɵdirectiveInject(ToastrService), ɵngcc0.ɵɵdirectiveInject(ToastPackage), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.NgZone));\n};\n\nToast.ɵcmp = /*@__PURE__*/ɵngcc0.ɵɵdefineComponent({\n  type: Toast,\n  selectors: [[\"\", \"toast-component\", \"\"]],\n  hostVars: 5,\n  hostBindings: function Toast_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      ɵngcc0.ɵɵlistener(\"click\", function Toast_click_HostBindingHandler() {\n        return ctx.tapToast();\n      })(\"mouseenter\", function Toast_mouseenter_HostBindingHandler() {\n        return ctx.stickAround();\n      })(\"mouseleave\", function Toast_mouseleave_HostBindingHandler() {\n        return ctx.delayedHideToast();\n      });\n    }\n\n    if (rf & 2) {\n      ɵngcc0.ɵɵsyntheticHostProperty(\"@flyInOut\", ctx.state);\n      ɵngcc0.ɵɵclassMap(ctx.toastClasses);\n      ɵngcc0.ɵɵstyleProp(\"display\", ctx.displayStyle);\n    }\n  },\n  attrs: _c0,\n  decls: 5,\n  vars: 5,\n  consts: [[\"class\", \"toast-close-button\", \"aria-label\", \"Close\", 3, \"click\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [\"role\", \"alertdialog\", \"aria-live\", \"polite\", 3, \"class\", \"innerHTML\", 4, \"ngIf\"], [\"role\", \"alertdialog\", \"aria-live\", \"polite\", 3, \"class\", 4, \"ngIf\"], [4, \"ngIf\"], [\"aria-label\", \"Close\", 1, \"toast-close-button\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"role\", \"alertdialog\", \"aria-live\", \"polite\", 3, \"innerHTML\"], [\"role\", \"alertdialog\", \"aria-live\", \"polite\"], [1, \"toast-progress\"]],\n  template: function Toast_Template(rf, ctx) {\n    if (rf & 1) {\n      ɵngcc0.ɵɵtemplate(0, Toast_button_0_Template, 3, 0, \"button\", 0);\n      ɵngcc0.ɵɵtemplate(1, Toast_div_1_Template, 3, 5, \"div\", 1);\n      ɵngcc0.ɵɵtemplate(2, Toast_div_2_Template, 1, 3, \"div\", 2);\n      ɵngcc0.ɵɵtemplate(3, Toast_div_3_Template, 2, 4, \"div\", 3);\n      ɵngcc0.ɵɵtemplate(4, Toast_div_4_Template, 2, 2, \"div\", 4);\n    }\n\n    if (rf & 2) {\n      ɵngcc0.ɵɵproperty(\"ngIf\", ctx.options.closeButton);\n      ɵngcc0.ɵɵadvance(1);\n      ɵngcc0.ɵɵproperty(\"ngIf\", ctx.title);\n      ɵngcc0.ɵɵadvance(1);\n      ɵngcc0.ɵɵproperty(\"ngIf\", ctx.message && ctx.options.enableHtml);\n      ɵngcc0.ɵɵadvance(1);\n      ɵngcc0.ɵɵproperty(\"ngIf\", ctx.message && !ctx.options.enableHtml);\n      ɵngcc0.ɵɵadvance(1);\n      ɵngcc0.ɵɵproperty(\"ngIf\", ctx.options.progressBar);\n    }\n  },\n  dependencies: [ɵngcc2.NgIf],\n  encapsulation: 2,\n  data: {\n    animation: [trigger('flyInOut', [state('inactive', style({\n      opacity: 0\n    })), state('active', style({\n      opacity: 1\n    })), state('removed', style({\n      opacity: 0\n    })), transition('inactive => active', animate('{{ easeTime }}ms {{ easing }}')), transition('active => removed', animate('{{ easeTime }}ms {{ easing }}'))])]\n  }\n});\n\nToast.ctorParameters = () => [{\n  type: ToastrService\n}, {\n  type: ToastPackage\n}, {\n  type: NgZone\n}];\n\n__decorate([HostBinding('class'), __metadata(\"design:type\", Object)], Toast.prototype, \"toastClasses\", void 0);\n\n__decorate([HostBinding('@flyInOut'), __metadata(\"design:type\", Object)], Toast.prototype, \"state\", void 0);\n\n__decorate([HostBinding('style.display'), __metadata(\"design:type\", Object), __metadata(\"design:paramtypes\", [])], Toast.prototype, \"displayStyle\", null);\n\n__decorate([HostListener('click'), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", []), __metadata(\"design:returntype\", void 0)], Toast.prototype, \"tapToast\", null);\n\n__decorate([HostListener('mouseenter'), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", []), __metadata(\"design:returntype\", void 0)], Toast.prototype, \"stickAround\", null);\n\n__decorate([HostListener('mouseleave'), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", []), __metadata(\"design:returntype\", void 0)], Toast.prototype, \"delayedHideToast\", null);\n\nToast = __decorate([__metadata(\"design:paramtypes\", [ToastrService, ToastPackage, NgZone])], Toast);\nvar ToastrModule_1;\nconst DefaultGlobalConfig = Object.assign({}, DefaultNoComponentGlobalConfig, {\n  toastComponent: Toast\n});\nlet ToastrModule = ToastrModule_1 = class ToastrModule {\n  static forRoot(config = {}) {\n    return {\n      ngModule: ToastrModule_1,\n      providers: [{\n        provide: TOAST_CONFIG,\n        useValue: {\n          default: DefaultGlobalConfig,\n          config\n        }\n      }]\n    };\n  }\n\n};\n\nToastrModule.ɵfac = function ToastrModule_Factory(t) {\n  return new (t || ToastrModule)();\n};\n\nToastrModule.ɵmod = /*@__PURE__*/ɵngcc0.ɵɵdefineNgModule({\n  type: ToastrModule\n});\nToastrModule.ɵinj = /*@__PURE__*/ɵngcc0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\nlet ToastrComponentlessModule = class ToastrComponentlessModule {\n  static forRoot(config = {}) {\n    return {\n      ngModule: ToastrModule,\n      providers: [{\n        provide: TOAST_CONFIG,\n        useValue: {\n          default: DefaultNoComponentGlobalConfig,\n          config\n        }\n      }]\n    };\n  }\n\n};\n\nToastrComponentlessModule.ɵfac = function ToastrComponentlessModule_Factory(t) {\n  return new (t || ToastrComponentlessModule)();\n};\n\nToastrComponentlessModule.ɵmod = /*@__PURE__*/ɵngcc0.ɵɵdefineNgModule({\n  type: ToastrComponentlessModule\n});\nToastrComponentlessModule.ɵinj = /*@__PURE__*/ɵngcc0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\nvar ToastNoAnimationModule_1;\nlet ToastNoAnimation = class ToastNoAnimation {\n  constructor(toastrService, toastPackage, appRef) {\n    this.toastrService = toastrService;\n    this.toastPackage = toastPackage;\n    this.appRef = appRef;\n    /** width of progress bar */\n\n    this.width = -1;\n    /** a combination of toast type and options.toastClass */\n\n    this.toastClasses = '';\n    /** controls animation */\n\n    this.state = 'inactive';\n    this.message = toastPackage.message;\n    this.title = toastPackage.title;\n    this.options = toastPackage.config;\n    this.originalTimeout = toastPackage.config.timeOut;\n    this.toastClasses = `${toastPackage.toastType} ${toastPackage.config.toastClass}`;\n    this.sub = toastPackage.toastRef.afterActivate().subscribe(() => {\n      this.activateToast();\n    });\n    this.sub1 = toastPackage.toastRef.manualClosed().subscribe(() => {\n      this.remove();\n    });\n    this.sub2 = toastPackage.toastRef.timeoutReset().subscribe(() => {\n      this.resetTimeout();\n    });\n    this.sub3 = toastPackage.toastRef.countDuplicate().subscribe(count => {\n      this.duplicatesCount = count;\n    });\n  }\n  /** hides component when waiting to be displayed */\n\n\n  get displayStyle() {\n    if (this.state === 'inactive') {\n      return 'none';\n    }\n  }\n\n  ngOnDestroy() {\n    this.sub.unsubscribe();\n    this.sub1.unsubscribe();\n    this.sub2.unsubscribe();\n    this.sub3.unsubscribe();\n    clearInterval(this.intervalId);\n    clearTimeout(this.timeout);\n  }\n  /**\n   * activates toast and sets timeout\n   */\n\n\n  activateToast() {\n    this.state = 'active';\n\n    if (!(this.options.disableTimeOut === true || this.options.disableTimeOut === 'timeOut') && this.options.timeOut) {\n      this.timeout = setTimeout(() => {\n        this.remove();\n      }, this.options.timeOut);\n      this.hideTime = new Date().getTime() + this.options.timeOut;\n\n      if (this.options.progressBar) {\n        this.intervalId = setInterval(() => this.updateProgress(), 10);\n      }\n    }\n\n    if (this.options.onActivateTick) {\n      this.appRef.tick();\n    }\n  }\n  /**\n   * updates progress bar width\n   */\n\n\n  updateProgress() {\n    if (this.width === 0 || this.width === 100 || !this.options.timeOut) {\n      return;\n    }\n\n    const now = new Date().getTime();\n    const remaining = this.hideTime - now;\n    this.width = remaining / this.options.timeOut * 100;\n\n    if (this.options.progressAnimation === 'increasing') {\n      this.width = 100 - this.width;\n    }\n\n    if (this.width <= 0) {\n      this.width = 0;\n    }\n\n    if (this.width >= 100) {\n      this.width = 100;\n    }\n  }\n\n  resetTimeout() {\n    clearTimeout(this.timeout);\n    clearInterval(this.intervalId);\n    this.state = 'active';\n    this.options.timeOut = this.originalTimeout;\n    this.timeout = setTimeout(() => this.remove(), this.originalTimeout);\n    this.hideTime = new Date().getTime() + (this.originalTimeout || 0);\n    this.width = -1;\n\n    if (this.options.progressBar) {\n      this.intervalId = setInterval(() => this.updateProgress(), 10);\n    }\n  }\n  /**\n   * tells toastrService to remove this toast after animation time\n   */\n\n\n  remove() {\n    if (this.state === 'removed') {\n      return;\n    }\n\n    clearTimeout(this.timeout);\n    this.state = 'removed';\n    this.timeout = setTimeout(() => this.toastrService.remove(this.toastPackage.toastId));\n  }\n\n  tapToast() {\n    if (this.state === 'removed') {\n      return;\n    }\n\n    this.toastPackage.triggerTap();\n\n    if (this.options.tapToDismiss) {\n      this.remove();\n    }\n  }\n\n  stickAround() {\n    if (this.state === 'removed') {\n      return;\n    }\n\n    clearTimeout(this.timeout);\n    this.options.timeOut = 0;\n    this.hideTime = 0; // disable progressBar\n\n    clearInterval(this.intervalId);\n    this.width = 0;\n  }\n\n  delayedHideToast() {\n    if (this.options.disableTimeOut === true || this.options.disableTimeOut === 'extendedTimeOut' || this.options.extendedTimeOut === 0 || this.state === 'removed') {\n      return;\n    }\n\n    this.timeout = setTimeout(() => this.remove(), this.options.extendedTimeOut);\n    this.options.timeOut = this.options.extendedTimeOut;\n    this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n    this.width = -1;\n\n    if (this.options.progressBar) {\n      this.intervalId = setInterval(() => this.updateProgress(), 10);\n    }\n  }\n\n};\n\nToastNoAnimation.ɵfac = function ToastNoAnimation_Factory(t) {\n  return new (t || ToastNoAnimation)(ɵngcc0.ɵɵdirectiveInject(ToastrService), ɵngcc0.ɵɵdirectiveInject(ToastPackage), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ApplicationRef));\n};\n\nToastNoAnimation.ɵcmp = /*@__PURE__*/ɵngcc0.ɵɵdefineComponent({\n  type: ToastNoAnimation,\n  selectors: [[\"\", \"toast-component\", \"\"]],\n  hostVars: 4,\n  hostBindings: function ToastNoAnimation_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      ɵngcc0.ɵɵlistener(\"click\", function ToastNoAnimation_click_HostBindingHandler() {\n        return ctx.tapToast();\n      })(\"mouseenter\", function ToastNoAnimation_mouseenter_HostBindingHandler() {\n        return ctx.stickAround();\n      })(\"mouseleave\", function ToastNoAnimation_mouseleave_HostBindingHandler() {\n        return ctx.delayedHideToast();\n      });\n    }\n\n    if (rf & 2) {\n      ɵngcc0.ɵɵclassMap(ctx.toastClasses);\n      ɵngcc0.ɵɵstyleProp(\"display\", ctx.displayStyle);\n    }\n  },\n  attrs: _c0,\n  decls: 5,\n  vars: 5,\n  consts: [[\"class\", \"toast-close-button\", \"aria-label\", \"Close\", 3, \"click\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [\"role\", \"alert\", \"aria-live\", \"polite\", 3, \"class\", \"innerHTML\", 4, \"ngIf\"], [\"role\", \"alert\", \"aria-live\", \"polite\", 3, \"class\", 4, \"ngIf\"], [4, \"ngIf\"], [\"aria-label\", \"Close\", 1, \"toast-close-button\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"role\", \"alert\", \"aria-live\", \"polite\", 3, \"innerHTML\"], [\"role\", \"alert\", \"aria-live\", \"polite\"], [1, \"toast-progress\"]],\n  template: function ToastNoAnimation_Template(rf, ctx) {\n    if (rf & 1) {\n      ɵngcc0.ɵɵtemplate(0, ToastNoAnimation_button_0_Template, 3, 0, \"button\", 0);\n      ɵngcc0.ɵɵtemplate(1, ToastNoAnimation_div_1_Template, 3, 5, \"div\", 1);\n      ɵngcc0.ɵɵtemplate(2, ToastNoAnimation_div_2_Template, 1, 3, \"div\", 2);\n      ɵngcc0.ɵɵtemplate(3, ToastNoAnimation_div_3_Template, 2, 4, \"div\", 3);\n      ɵngcc0.ɵɵtemplate(4, ToastNoAnimation_div_4_Template, 2, 2, \"div\", 4);\n    }\n\n    if (rf & 2) {\n      ɵngcc0.ɵɵproperty(\"ngIf\", ctx.options.closeButton);\n      ɵngcc0.ɵɵadvance(1);\n      ɵngcc0.ɵɵproperty(\"ngIf\", ctx.title);\n      ɵngcc0.ɵɵadvance(1);\n      ɵngcc0.ɵɵproperty(\"ngIf\", ctx.message && ctx.options.enableHtml);\n      ɵngcc0.ɵɵadvance(1);\n      ɵngcc0.ɵɵproperty(\"ngIf\", ctx.message && !ctx.options.enableHtml);\n      ɵngcc0.ɵɵadvance(1);\n      ɵngcc0.ɵɵproperty(\"ngIf\", ctx.options.progressBar);\n    }\n  },\n  dependencies: [ɵngcc2.NgIf],\n  encapsulation: 2\n});\n\nToastNoAnimation.ctorParameters = () => [{\n  type: ToastrService\n}, {\n  type: ToastPackage\n}, {\n  type: ApplicationRef\n}];\n\n__decorate([HostBinding('class'), __metadata(\"design:type\", Object)], ToastNoAnimation.prototype, \"toastClasses\", void 0);\n\n__decorate([HostBinding('style.display'), __metadata(\"design:type\", Object), __metadata(\"design:paramtypes\", [])], ToastNoAnimation.prototype, \"displayStyle\", null);\n\n__decorate([HostListener('click'), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", []), __metadata(\"design:returntype\", void 0)], ToastNoAnimation.prototype, \"tapToast\", null);\n\n__decorate([HostListener('mouseenter'), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", []), __metadata(\"design:returntype\", void 0)], ToastNoAnimation.prototype, \"stickAround\", null);\n\n__decorate([HostListener('mouseleave'), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", []), __metadata(\"design:returntype\", void 0)], ToastNoAnimation.prototype, \"delayedHideToast\", null);\n\nToastNoAnimation = __decorate([__metadata(\"design:paramtypes\", [ToastrService, ToastPackage, ApplicationRef])], ToastNoAnimation);\nconst DefaultNoAnimationsGlobalConfig = Object.assign({}, DefaultNoComponentGlobalConfig, {\n  toastComponent: ToastNoAnimation\n});\nlet ToastNoAnimationModule = ToastNoAnimationModule_1 = class ToastNoAnimationModule {\n  static forRoot(config = {}) {\n    return {\n      ngModule: ToastNoAnimationModule_1,\n      providers: [{\n        provide: TOAST_CONFIG,\n        useValue: {\n          default: DefaultNoAnimationsGlobalConfig,\n          config\n        }\n      }]\n    };\n  }\n\n};\n\nToastNoAnimationModule.ɵfac = function ToastNoAnimationModule_Factory(t) {\n  return new (t || ToastNoAnimationModule)();\n};\n\nToastNoAnimationModule.ɵmod = /*@__PURE__*/ɵngcc0.ɵɵdefineNgModule({\n  type: ToastNoAnimationModule\n});\nToastNoAnimationModule.ɵinj = /*@__PURE__*/ɵngcc0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(ToastContainerDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[toastContainer]',\n      exportAs: 'toastContainer'\n    }]\n  }], function () {\n    return [{\n      type: ɵngcc0.ElementRef\n    }];\n  }, null);\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(ToastContainerModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [ToastContainerDirective],\n      exports: [ToastContainerDirective]\n    }]\n  }], null, null);\n})();\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(ToastContainerModule, {\n    declarations: [ToastContainerDirective],\n    exports: [ToastContainerDirective]\n  });\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(OverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(Overlay, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: OverlayContainer\n    }, {\n      type: ɵngcc0.ComponentFactoryResolver\n    }, {\n      type: ɵngcc0.ApplicationRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(ToastrService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TOAST_CONFIG]\n      }]\n    }, {\n      type: Overlay\n    }, {\n      type: ɵngcc0.Injector\n    }, {\n      type: ɵngcc1.DomSanitizer\n    }, {\n      type: ɵngcc0.NgZone\n    }];\n  }, null);\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(Toast, [{\n    type: Component,\n    args: [{\n      selector: '[toast-component]',\n      template: `\n  <button *ngIf=\"options.closeButton\" (click)=\"remove()\" class=\"toast-close-button\" aria-label=\"Close\">\n    <span aria-hidden=\"true\">&times;</span>\n  </button>\n  <div *ngIf=\"title\" [class]=\"options.titleClass\" [attr.aria-label]=\"title\">\n    {{ title }} <ng-container *ngIf=\"duplicatesCount\">[{{ duplicatesCount + 1 }}]</ng-container>\n  </div>\n  <div *ngIf=\"message && options.enableHtml\" role=\"alertdialog\" aria-live=\"polite\"\n    [class]=\"options.messageClass\" [innerHTML]=\"message\">\n  </div>\n  <div *ngIf=\"message && !options.enableHtml\" role=\"alertdialog\" aria-live=\"polite\"\n    [class]=\"options.messageClass\" [attr.aria-label]=\"message\">\n    {{ message }}\n  </div>\n  <div *ngIf=\"options.progressBar\">\n    <div class=\"toast-progress\" [style.width]=\"width + '%'\"></div>\n  </div>\n  `,\n      animations: [trigger('flyInOut', [state('inactive', style({\n        opacity: 0\n      })), state('active', style({\n        opacity: 1\n      })), state('removed', style({\n        opacity: 0\n      })), transition('inactive => active', animate('{{ easeTime }}ms {{ easing }}')), transition('active => removed', animate('{{ easeTime }}ms {{ easing }}'))])],\n      preserveWhitespaces: false\n    }]\n  }], function () {\n    return [{\n      type: ToastrService\n    }, {\n      type: ToastPackage\n    }, {\n      type: ɵngcc0.NgZone\n    }];\n  }, {\n    toastClasses: [{\n      type: HostBinding,\n      args: ['class']\n    }],\n    state: [{\n      type: HostBinding,\n      args: ['@flyInOut']\n    }],\n    displayStyle: [{\n      type: HostBinding,\n      args: ['style.display']\n    }],\n    tapToast: [{\n      type: HostListener,\n      args: ['click']\n    }],\n    stickAround: [{\n      type: HostListener,\n      args: ['mouseenter']\n    }],\n    delayedHideToast: [{\n      type: HostListener,\n      args: ['mouseleave']\n    }]\n  });\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(ToastrModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [Toast],\n      exports: [Toast],\n      entryComponents: [Toast]\n    }]\n  }], null, null);\n})();\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(ToastrModule, {\n    declarations: function () {\n      return [Toast];\n    },\n    imports: function () {\n      return [CommonModule];\n    },\n    exports: function () {\n      return [Toast];\n    }\n  });\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(ToastrComponentlessModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule]\n    }]\n  }], null, null);\n})();\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(ToastrComponentlessModule, {\n    imports: function () {\n      return [CommonModule];\n    }\n  });\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(ToastNoAnimation, [{\n    type: Component,\n    args: [{\n      selector: '[toast-component]',\n      template: `\n  <button *ngIf=\"options.closeButton\" (click)=\"remove()\" class=\"toast-close-button\" aria-label=\"Close\">\n    <span aria-hidden=\"true\">&times;</span>\n  </button>\n  <div *ngIf=\"title\" [class]=\"options.titleClass\" [attr.aria-label]=\"title\">\n    {{ title }} <ng-container *ngIf=\"duplicatesCount\">[{{ duplicatesCount + 1 }}]</ng-container>\n  </div>\n  <div *ngIf=\"message && options.enableHtml\" role=\"alert\" aria-live=\"polite\"\n    [class]=\"options.messageClass\" [innerHTML]=\"message\">\n  </div>\n  <div *ngIf=\"message && !options.enableHtml\" role=\"alert\" aria-live=\"polite\"\n    [class]=\"options.messageClass\" [attr.aria-label]=\"message\">\n    {{ message }}\n  </div>\n  <div *ngIf=\"options.progressBar\">\n    <div class=\"toast-progress\" [style.width]=\"width + '%'\"></div>\n  </div>\n  `\n    }]\n  }], function () {\n    return [{\n      type: ToastrService\n    }, {\n      type: ToastPackage\n    }, {\n      type: ɵngcc0.ApplicationRef\n    }];\n  }, {\n    toastClasses: [{\n      type: HostBinding,\n      args: ['class']\n    }],\n    displayStyle: [{\n      type: HostBinding,\n      args: ['style.display']\n    }],\n    tapToast: [{\n      type: HostListener,\n      args: ['click']\n    }],\n    stickAround: [{\n      type: HostListener,\n      args: ['mouseenter']\n    }],\n    delayedHideToast: [{\n      type: HostListener,\n      args: ['mouseleave']\n    }]\n  });\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(ToastNoAnimationModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [ToastNoAnimation],\n      exports: [ToastNoAnimation],\n      entryComponents: [ToastNoAnimation]\n    }]\n  }], null, null);\n})();\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(ToastNoAnimationModule, {\n    declarations: function () {\n      return [ToastNoAnimation];\n    },\n    imports: function () {\n      return [CommonModule];\n    },\n    exports: function () {\n      return [ToastNoAnimation];\n    }\n  });\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { BasePortalHost, ComponentPortal, DefaultGlobalConfig, DefaultNoAnimationsGlobalConfig, DefaultNoComponentGlobalConfig, Overlay, OverlayContainer, OverlayRef, TOAST_CONFIG, Toast, ToastContainerDirective, ToastContainerModule, ToastInjector, ToastNoAnimation, ToastNoAnimationModule, ToastPackage, ToastRef, ToastrComponentlessModule, ToastrModule, ToastrService };", "map": {"version": 3, "names": ["__decorate", "__metadata", "__param", "ElementRef", "Directive", "NgModule", "InjectionToken", "Inject", "ɵɵdefineInjectable", "ɵɵinject", "Injectable", "ComponentFactoryResolver", "ApplicationRef", "SecurityContext", "Injector", "NgZone", "INJECTOR", "HostBinding", "HostListener", "Component", "trigger", "state", "style", "transition", "animate", "Subject", "Dom<PERSON><PERSON><PERSON>zer", "DOCUMENT", "CommonModule", "ɵngcc0", "ɵngcc1", "ɵngcc2", "_c0", "Toast_button_0_Template", "rf", "ctx", "_r6", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "Toast_button_0_Template_button_click_0_listener", "ɵɵrestoreView", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "remove", "ɵɵtext", "ɵɵelementEnd", "Toast_div_1_ng_container_2_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ctx_r7", "ɵɵadvance", "ɵɵtextInterpolate1", "duplicatesCount", "Toast_div_1_Template", "ɵɵtemplate", "ctx_r1", "ɵɵclassMap", "options", "titleClass", "ɵɵattribute", "title", "ɵɵproperty", "Toast_div_2_Template", "ɵɵelement", "ctx_r2", "messageClass", "message", "ɵɵsanitizeHtml", "Toast_div_3_Template", "ctx_r3", "Toast_div_4_Template", "ctx_r4", "ɵɵstyleProp", "width", "ToastNoAnimation_button_0_Template", "ToastNoAnimation_button_0_Template_button_click_0_listener", "ToastNoAnimation_div_1_ng_container_2_Template", "ToastNoAnimation_div_1_Template", "ToastNoAnimation_div_2_Template", "ToastNoAnimation_div_3_Template", "ToastNoAnimation_div_4_Template", "ToastContainerDirective", "constructor", "el", "getContainerElement", "nativeElement", "ɵfac", "ToastContainerDirective_Factory", "t", "ɵɵdirectiveInject", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "exportAs", "ctorParameters", "ToastContainerModule", "ToastContainerModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "ToastPackage", "toastId", "config", "toastType", "toastRef", "_onTap", "_onAction", "afterClosed", "subscribe", "complete", "triggerTap", "next", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onTap", "asObservable", "triggerAction", "action", "onAction", "DefaultNoComponentGlobalConfig", "maxOpened", "autoDismiss", "newestOnTop", "preventDuplicates", "countDuplicates", "resetTimeoutOnDuplicate", "iconClasses", "error", "info", "success", "warning", "closeButton", "disableTimeOut", "timeOut", "extendedTimeOut", "enableHtml", "progressBar", "toastClass", "positionClass", "easing", "easeTime", "onActivateTick", "progressAnimation", "TOAST_CONFIG", "ComponentPortal", "component", "injector", "attach", "host", "_attachedHost", "detach", "undefined", "isAttached", "setAttachedHost", "BasePortalHost", "portal", "_attachedPortal", "attachComponentPortal", "_disposeFn", "setDisposeFn", "fn", "DomPortalHost", "_hostDomElement", "_componentFactoryResolver", "_appRef", "componentFactory", "resolveComponentFactory", "componentRef", "create", "attachView", "<PERSON><PERSON><PERSON><PERSON>", "detach<PERSON>iew", "destroy", "insertBefore", "_getComponentRootNode", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "rootNodes", "OverlayContainer", "_document", "ngOnDestroy", "_containerElement", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "_createContainer", "container", "createElement", "classList", "add", "body", "OverlayContainer_Factory", "ɵprov", "token", "factory", "providedIn", "decorators", "args", "ngInjectableDef", "Object", "OverlayRef", "_portalHost", "Overlay", "_overlayContainer", "_paneElements", "Map", "overlayContainer", "_createOverlayRef", "getPaneElement", "get", "set", "_createPaneElement", "pane", "id", "_createPortalHost", "Overlay_Factory", "ToastRef", "_overlayRef", "_afterClosed", "_activate", "_manualClose", "_resetTimeout", "_countDuplicate", "manualClose", "manualClosed", "timeoutReset", "countDuplicate", "close", "isInactive", "isStopped", "activate", "afterActivate", "onDuplicate", "resetTimeout", "ToastInjector", "_toastPackage", "_parentInjector", "notFoundValue", "flags", "ToastrService", "overlay", "_injector", "sanitizer", "ngZone", "currentlyActive", "toasts", "index", "toastrConfig", "assign", "default", "show", "override", "_preBuildNotification", "applyConfig", "clear", "toast", "found", "_findToast", "activeToast", "splice", "length", "p", "findDuplicate", "resetOnDuplicate", "i", "run", "_buildNotification", "toastComponent", "Error", "duplicate", "previousToastMessage", "keepInactive", "overlayRef", "sanitizedMessage", "sanitize", "HTML", "toastPackage", "toastInjector", "componentInstance", "_component", "ins", "onShown", "onHidden", "setTimeout", "push", "ToastrService_Factory", "Toast", "toastrService", "toastClasses", "value", "params", "originalTimeout", "sub", "activateToast", "sub1", "sub2", "sub3", "count", "displayStyle", "unsubscribe", "clearInterval", "intervalId", "clearTimeout", "timeout", "outsideTimeout", "hideTime", "Date", "getTime", "outsideInterval", "updateProgress", "now", "remaining", "tapToast", "stickAround", "delayedHideToast", "func", "runOutsideAngular", "runInsideAngular", "setInterval", "Toast_Factory", "ɵcmp", "ɵɵdefineComponent", "hostVars", "hostBindings", "Toast_HostBindings", "Toast_click_HostBindingHandler", "Toast_mouseenter_HostBindingHandler", "Toast_mouseleave_HostBindingHandler", "ɵɵsyntheticHostProperty", "attrs", "decls", "vars", "consts", "template", "Toast_Template", "dependencies", "NgIf", "encapsulation", "data", "animation", "opacity", "prototype", "Function", "ToastrModule_1", "DefaultGlobalConfig", "ToastrModule", "forRoot", "ngModule", "providers", "provide", "useValue", "ToastrModule_Factory", "imports", "ToastrComponentlessModule", "ToastrComponentlessModule_Factory", "ToastNoAnimationModule_1", "ToastNoAnimation", "appRef", "tick", "ToastNoAnimation_Factory", "ToastNoAnimation_HostBindings", "ToastNoAnimation_click_HostBindingHandler", "ToastNoAnimation_mouseenter_HostBindingHandler", "ToastNoAnimation_mouseleave_HostBindingHandler", "ToastNoAnimation_Template", "DefaultNoAnimationsGlobalConfig", "ToastNoAnimationModule", "ToastNoAnimationModule_Factory", "ngDevMode", "ɵsetClassMetadata", "selector", "declarations", "exports", "ngJitMode", "ɵɵsetNgModuleScope", "animations", "preserveWhitespaces", "entryComponents"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/ngx-toastr/__ivy_ngcc__/fesm2015/ngx-toastr.js"], "sourcesContent": ["import { __decorate, __metadata, __param } from 'tslib';\nimport { ElementRef, Directive, NgModule, InjectionToken, Inject, ɵɵdefineInjectable, ɵɵinject, Injectable, ComponentFactoryResolver, ApplicationRef, SecurityContext, Injector, NgZone, INJECTOR, HostBinding, HostListener, Component } from '@angular/core';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { Subject } from 'rxjs';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport { DOCUMENT, CommonModule } from '@angular/common';\n\nimport * as ɵngcc0 from '@angular/core';\nimport * as ɵngcc1 from '@angular/platform-browser';\nimport * as ɵngcc2 from '@angular/common';\n\nconst _c0 = [\"toast-component\", \"\"];\nfunction Toast_button_0_Template(rf, ctx) { if (rf & 1) {\n    const _r6 = ɵngcc0.ɵɵgetCurrentView();\n    ɵngcc0.ɵɵelementStart(0, \"button\", 5);\n    ɵngcc0.ɵɵlistener(\"click\", function Toast_button_0_Template_button_click_0_listener() { ɵngcc0.ɵɵrestoreView(_r6); const ctx_r5 = ɵngcc0.ɵɵnextContext(); return ɵngcc0.ɵɵresetView(ctx_r5.remove()); });\n    ɵngcc0.ɵɵelementStart(1, \"span\", 6);\n    ɵngcc0.ɵɵtext(2, \"\\u00D7\");\n    ɵngcc0.ɵɵelementEnd()();\n} }\nfunction Toast_div_1_ng_container_2_Template(rf, ctx) { if (rf & 1) {\n    ɵngcc0.ɵɵelementContainerStart(0);\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementContainerEnd();\n} if (rf & 2) {\n    const ctx_r7 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate1(\"[\", ctx_r7.duplicatesCount + 1, \"]\");\n} }\nfunction Toast_div_1_Template(rf, ctx) { if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\");\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵtemplate(2, Toast_div_1_ng_container_2_Template, 2, 1, \"ng-container\", 4);\n    ɵngcc0.ɵɵelementEnd();\n} if (rf & 2) {\n    const ctx_r1 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassMap(ctx_r1.options.titleClass);\n    ɵngcc0.ɵɵattribute(\"aria-label\", ctx_r1.title);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate1(\" \", ctx_r1.title, \" \");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r1.duplicatesCount);\n} }\nfunction Toast_div_2_Template(rf, ctx) { if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"div\", 7);\n} if (rf & 2) {\n    const ctx_r2 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassMap(ctx_r2.options.messageClass);\n    ɵngcc0.ɵɵproperty(\"innerHTML\", ctx_r2.message, ɵngcc0.ɵɵsanitizeHtml);\n} }\nfunction Toast_div_3_Template(rf, ctx) { if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 8);\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementEnd();\n} if (rf & 2) {\n    const ctx_r3 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassMap(ctx_r3.options.messageClass);\n    ɵngcc0.ɵɵattribute(\"aria-label\", ctx_r3.message);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate1(\" \", ctx_r3.message, \" \");\n} }\nfunction Toast_div_4_Template(rf, ctx) { if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\");\n    ɵngcc0.ɵɵelement(1, \"div\", 9);\n    ɵngcc0.ɵɵelementEnd();\n} if (rf & 2) {\n    const ctx_r4 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵstyleProp(\"width\", ctx_r4.width + \"%\");\n} }\nfunction ToastNoAnimation_button_0_Template(rf, ctx) { if (rf & 1) {\n    const _r6 = ɵngcc0.ɵɵgetCurrentView();\n    ɵngcc0.ɵɵelementStart(0, \"button\", 5);\n    ɵngcc0.ɵɵlistener(\"click\", function ToastNoAnimation_button_0_Template_button_click_0_listener() { ɵngcc0.ɵɵrestoreView(_r6); const ctx_r5 = ɵngcc0.ɵɵnextContext(); return ɵngcc0.ɵɵresetView(ctx_r5.remove()); });\n    ɵngcc0.ɵɵelementStart(1, \"span\", 6);\n    ɵngcc0.ɵɵtext(2, \"\\u00D7\");\n    ɵngcc0.ɵɵelementEnd()();\n} }\nfunction ToastNoAnimation_div_1_ng_container_2_Template(rf, ctx) { if (rf & 1) {\n    ɵngcc0.ɵɵelementContainerStart(0);\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementContainerEnd();\n} if (rf & 2) {\n    const ctx_r7 = ɵngcc0.ɵɵnextContext(2);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate1(\"[\", ctx_r7.duplicatesCount + 1, \"]\");\n} }\nfunction ToastNoAnimation_div_1_Template(rf, ctx) { if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\");\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵtemplate(2, ToastNoAnimation_div_1_ng_container_2_Template, 2, 1, \"ng-container\", 4);\n    ɵngcc0.ɵɵelementEnd();\n} if (rf & 2) {\n    const ctx_r1 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassMap(ctx_r1.options.titleClass);\n    ɵngcc0.ɵɵattribute(\"aria-label\", ctx_r1.title);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate1(\" \", ctx_r1.title, \" \");\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵproperty(\"ngIf\", ctx_r1.duplicatesCount);\n} }\nfunction ToastNoAnimation_div_2_Template(rf, ctx) { if (rf & 1) {\n    ɵngcc0.ɵɵelement(0, \"div\", 7);\n} if (rf & 2) {\n    const ctx_r2 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassMap(ctx_r2.options.messageClass);\n    ɵngcc0.ɵɵproperty(\"innerHTML\", ctx_r2.message, ɵngcc0.ɵɵsanitizeHtml);\n} }\nfunction ToastNoAnimation_div_3_Template(rf, ctx) { if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\", 8);\n    ɵngcc0.ɵɵtext(1);\n    ɵngcc0.ɵɵelementEnd();\n} if (rf & 2) {\n    const ctx_r3 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵclassMap(ctx_r3.options.messageClass);\n    ɵngcc0.ɵɵattribute(\"aria-label\", ctx_r3.message);\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵtextInterpolate1(\" \", ctx_r3.message, \" \");\n} }\nfunction ToastNoAnimation_div_4_Template(rf, ctx) { if (rf & 1) {\n    ɵngcc0.ɵɵelementStart(0, \"div\");\n    ɵngcc0.ɵɵelement(1, \"div\", 9);\n    ɵngcc0.ɵɵelementEnd();\n} if (rf & 2) {\n    const ctx_r4 = ɵngcc0.ɵɵnextContext();\n    ɵngcc0.ɵɵadvance(1);\n    ɵngcc0.ɵɵstyleProp(\"width\", ctx_r4.width + \"%\");\n} }\nlet ToastContainerDirective = class ToastContainerDirective {\n    constructor(el) {\n        this.el = el;\n    }\n    getContainerElement() {\n        return this.el.nativeElement;\n    }\n};\nToastContainerDirective.ɵfac = function ToastContainerDirective_Factory(t) { return new (t || ToastContainerDirective)(ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ElementRef)); };\nToastContainerDirective.ɵdir = /*@__PURE__*/ ɵngcc0.ɵɵdefineDirective({ type: ToastContainerDirective, selectors: [[\"\", \"toastContainer\", \"\"]], exportAs: [\"toastContainer\"] });\nToastContainerDirective.ctorParameters = () => [\n    { type: ElementRef }\n];\nToastContainerDirective = __decorate([ __metadata(\"design:paramtypes\", [ElementRef])\n], ToastContainerDirective);\nlet ToastContainerModule = class ToastContainerModule {\n};\nToastContainerModule.ɵfac = function ToastContainerModule_Factory(t) { return new (t || ToastContainerModule)(); };\nToastContainerModule.ɵmod = /*@__PURE__*/ ɵngcc0.ɵɵdefineNgModule({ type: ToastContainerModule });\nToastContainerModule.ɵinj = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjector({});\n\n/**\n * Everything a toast needs to launch\n */\nclass ToastPackage {\n    constructor(toastId, config, message, title, toastType, toastRef) {\n        this.toastId = toastId;\n        this.config = config;\n        this.message = message;\n        this.title = title;\n        this.toastType = toastType;\n        this.toastRef = toastRef;\n        this._onTap = new Subject();\n        this._onAction = new Subject();\n        this.toastRef.afterClosed().subscribe(() => {\n            this._onAction.complete();\n            this._onTap.complete();\n        });\n    }\n    /** Fired on click */\n    triggerTap() {\n        this._onTap.next();\n        if (this.config.tapToDismiss) {\n            this._onTap.complete();\n        }\n    }\n    onTap() {\n        return this._onTap.asObservable();\n    }\n    /** available for use in custom toast */\n    triggerAction(action) {\n        this._onAction.next(action);\n    }\n    onAction() {\n        return this._onAction.asObservable();\n    }\n}\nconst DefaultNoComponentGlobalConfig = {\n    maxOpened: 0,\n    autoDismiss: false,\n    newestOnTop: true,\n    preventDuplicates: false,\n    countDuplicates: false,\n    resetTimeoutOnDuplicate: false,\n    iconClasses: {\n        error: 'toast-error',\n        info: 'toast-info',\n        success: 'toast-success',\n        warning: 'toast-warning',\n    },\n    // Individual\n    closeButton: false,\n    disableTimeOut: false,\n    timeOut: 5000,\n    extendedTimeOut: 1000,\n    enableHtml: false,\n    progressBar: false,\n    toastClass: 'ngx-toastr',\n    positionClass: 'toast-top-right',\n    titleClass: 'toast-title',\n    messageClass: 'toast-message',\n    easing: 'ease-in',\n    easeTime: 300,\n    tapToDismiss: true,\n    onActivateTick: false,\n    progressAnimation: 'decreasing',\n};\nconst TOAST_CONFIG = new InjectionToken('ToastConfig');\n\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nclass ComponentPortal {\n    constructor(component, injector) {\n        this.component = component;\n        this.injector = injector;\n    }\n    /** Attach this portal to a host. */\n    attach(host, newestOnTop) {\n        this._attachedHost = host;\n        return host.attach(this, newestOnTop);\n    }\n    /** Detach this portal from its host */\n    detach() {\n        const host = this._attachedHost;\n        if (host) {\n            this._attachedHost = undefined;\n            return host.detach();\n        }\n    }\n    /** Whether this portal is attached to a host. */\n    get isAttached() {\n        return this._attachedHost != null;\n    }\n    /**\n     * Sets the PortalHost reference without performing `attach()`. This is used directly by\n     * the PortalHost when it is performing an `attach()` or `detach()`.\n     */\n    setAttachedHost(host) {\n        this._attachedHost = host;\n    }\n}\n/**\n * Partial implementation of PortalHost that only deals with attaching a\n * ComponentPortal\n */\nclass BasePortalHost {\n    attach(portal, newestOnTop) {\n        this._attachedPortal = portal;\n        return this.attachComponentPortal(portal, newestOnTop);\n    }\n    detach() {\n        if (this._attachedPortal) {\n            this._attachedPortal.setAttachedHost();\n        }\n        this._attachedPortal = undefined;\n        if (this._disposeFn) {\n            this._disposeFn();\n            this._disposeFn = undefined;\n        }\n    }\n    setDisposeFn(fn) {\n        this._disposeFn = fn;\n    }\n}\n\n/**\n * A PortalHost for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n *\n * This is the only part of the portal core that directly touches the DOM.\n */\nclass DomPortalHost extends BasePortalHost {\n    constructor(_hostDomElement, _componentFactoryResolver, _appRef) {\n        super();\n        this._hostDomElement = _hostDomElement;\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._appRef = _appRef;\n    }\n    /**\n     * Attach the given ComponentPortal to DOM element using the ComponentFactoryResolver.\n     * @param portal Portal to be attached\n     */\n    attachComponentPortal(portal, newestOnTop) {\n        const componentFactory = this._componentFactoryResolver.resolveComponentFactory(portal.component);\n        let componentRef;\n        // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n        // for the component (in terms of Angular's component tree, not rendering).\n        // When the ViewContainerRef is missing, we use the factory to create the component directly\n        // and then manually attach the ChangeDetector for that component to the application (which\n        // happens automatically when using a ViewContainer).\n        componentRef = componentFactory.create(portal.injector);\n        // When creating a component outside of a ViewContainer, we need to manually register\n        // its ChangeDetector with the application. This API is unfortunately not yet published\n        // in Angular core. The change detector must also be deregistered when the component\n        // is destroyed to prevent memory leaks.\n        this._appRef.attachView(componentRef.hostView);\n        this.setDisposeFn(() => {\n            this._appRef.detachView(componentRef.hostView);\n            componentRef.destroy();\n        });\n        // At this point the component has been instantiated, so we move it to the location in the DOM\n        // where we want it to be rendered.\n        if (newestOnTop) {\n            this._hostDomElement.insertBefore(this._getComponentRootNode(componentRef), this._hostDomElement.firstChild);\n        }\n        else {\n            this._hostDomElement.appendChild(this._getComponentRootNode(componentRef));\n        }\n        return componentRef;\n    }\n    /** Gets the root HTMLElement for an instantiated component. */\n    _getComponentRootNode(componentRef) {\n        return componentRef.hostView.rootNodes[0];\n    }\n}\n\n/** Container inside which all toasts will render. */\nlet OverlayContainer = class OverlayContainer {\n    constructor(_document) {\n        this._document = _document;\n    }\n    ngOnDestroy() {\n        if (this._containerElement && this._containerElement.parentNode) {\n            this._containerElement.parentNode.removeChild(this._containerElement);\n        }\n    }\n    /**\n     * This method returns the overlay container element. It will lazily\n     * create the element the first time  it is called to facilitate using\n     * the container in non-browser environments.\n     * @returns the container element\n     */\n    getContainerElement() {\n        if (!this._containerElement) {\n            this._createContainer();\n        }\n        return this._containerElement;\n    }\n    /**\n     * Create the overlay container element, which is simply a div\n     * with the 'cdk-overlay-container' class on the document body.\n     */\n    _createContainer() {\n        const container = this._document.createElement('div');\n        container.classList.add('overlay-container');\n        this._document.body.appendChild(container);\n        this._containerElement = container;\n    }\n};\nOverlayContainer.ɵfac = function OverlayContainer_Factory(t) { return new (t || OverlayContainer)(ɵngcc0.ɵɵinject(DOCUMENT)); };\nOverlayContainer.ɵprov = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjectable({ token: OverlayContainer, factory: function (t) { return OverlayContainer.ɵfac(t); }, providedIn: 'root' });\nOverlayContainer.ctorParameters = () => [\n    { type: undefined, decorators: [{ type: Inject, args: [DOCUMENT,] }] }\n];\nOverlayContainer.ngInjectableDef = ɵɵdefineInjectable({ factory: function OverlayContainer_Factory() { return new OverlayContainer(ɵɵinject(DOCUMENT)); }, token: OverlayContainer, providedIn: \"root\" });\nOverlayContainer = __decorate([ __param(0, Inject(DOCUMENT)),\n    __metadata(\"design:paramtypes\", [Object])\n], OverlayContainer);\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n    constructor(_portalHost) {\n        this._portalHost = _portalHost;\n    }\n    attach(portal, newestOnTop = true) {\n        return this._portalHost.attach(portal, newestOnTop);\n    }\n    /**\n     * Detaches an overlay from a portal.\n     * @returns Resolves when the overlay has been detached.\n     */\n    detach() {\n        return this._portalHost.detach();\n    }\n}\n\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalHost, so any kind of Portal can be loaded into one.\n */\nlet Overlay = class Overlay {\n    constructor(_overlayContainer, _componentFactoryResolver, _appRef, _document) {\n        this._overlayContainer = _overlayContainer;\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._appRef = _appRef;\n        this._document = _document;\n        // Namespace panes by overlay container\n        this._paneElements = new Map();\n    }\n    /**\n     * Creates an overlay.\n     * @returns A reference to the created overlay.\n     */\n    create(positionClass, overlayContainer) {\n        // get existing pane if possible\n        return this._createOverlayRef(this.getPaneElement(positionClass, overlayContainer));\n    }\n    getPaneElement(positionClass = '', overlayContainer) {\n        if (!this._paneElements.get(overlayContainer)) {\n            this._paneElements.set(overlayContainer, {});\n        }\n        if (!this._paneElements.get(overlayContainer)[positionClass]) {\n            this._paneElements.get(overlayContainer)[positionClass] = this._createPaneElement(positionClass, overlayContainer);\n        }\n        return this._paneElements.get(overlayContainer)[positionClass];\n    }\n    /**\n     * Creates the DOM element for an overlay and appends it to the overlay container.\n     * @returns Newly-created pane element\n     */\n    _createPaneElement(positionClass, overlayContainer) {\n        const pane = this._document.createElement('div');\n        pane.id = 'toast-container';\n        pane.classList.add(positionClass);\n        pane.classList.add('toast-container');\n        if (!overlayContainer) {\n            this._overlayContainer.getContainerElement().appendChild(pane);\n        }\n        else {\n            overlayContainer.getContainerElement().appendChild(pane);\n        }\n        return pane;\n    }\n    /**\n     * Create a DomPortalHost into which the overlay content can be loaded.\n     * @param pane The DOM element to turn into a portal host.\n     * @returns A portal host for the given DOM element.\n     */\n    _createPortalHost(pane) {\n        return new DomPortalHost(pane, this._componentFactoryResolver, this._appRef);\n    }\n    /**\n     * Creates an OverlayRef for an overlay in the given DOM element.\n     * @param pane DOM element for the overlay\n     */\n    _createOverlayRef(pane) {\n        return new OverlayRef(this._createPortalHost(pane));\n    }\n};\nOverlay.ɵfac = function Overlay_Factory(t) { return new (t || Overlay)(ɵngcc0.ɵɵinject(OverlayContainer), ɵngcc0.ɵɵinject(ɵngcc0.ComponentFactoryResolver), ɵngcc0.ɵɵinject(ɵngcc0.ApplicationRef), ɵngcc0.ɵɵinject(DOCUMENT)); };\nOverlay.ɵprov = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjectable({ token: Overlay, factory: function (t) { return Overlay.ɵfac(t); }, providedIn: 'root' });\nOverlay.ctorParameters = () => [\n    { type: OverlayContainer },\n    { type: ComponentFactoryResolver },\n    { type: ApplicationRef },\n    { type: undefined, decorators: [{ type: Inject, args: [DOCUMENT,] }] }\n];\nOverlay.ngInjectableDef = ɵɵdefineInjectable({ factory: function Overlay_Factory() { return new Overlay(ɵɵinject(OverlayContainer), ɵɵinject(ComponentFactoryResolver), ɵɵinject(ApplicationRef), ɵɵinject(DOCUMENT)); }, token: Overlay, providedIn: \"root\" });\nOverlay = __decorate([ __param(3, Inject(DOCUMENT)),\n    __metadata(\"design:paramtypes\", [OverlayContainer,\n        ComponentFactoryResolver,\n        ApplicationRef, Object])\n], Overlay);\n\n/**\n * Reference to a toast opened via the Toastr service.\n */\nclass ToastRef {\n    constructor(_overlayRef) {\n        this._overlayRef = _overlayRef;\n        /** Count of duplicates of this toast */\n        this.duplicatesCount = 0;\n        /** Subject for notifying the user that the toast has finished closing. */\n        this._afterClosed = new Subject();\n        /** triggered when toast is activated */\n        this._activate = new Subject();\n        /** notifies the toast that it should close before the timeout */\n        this._manualClose = new Subject();\n        /** notifies the toast that it should reset the timeouts */\n        this._resetTimeout = new Subject();\n        /** notifies the toast that it should count a duplicate toast */\n        this._countDuplicate = new Subject();\n    }\n    manualClose() {\n        this._manualClose.next();\n        this._manualClose.complete();\n    }\n    manualClosed() {\n        return this._manualClose.asObservable();\n    }\n    timeoutReset() {\n        return this._resetTimeout.asObservable();\n    }\n    countDuplicate() {\n        return this._countDuplicate.asObservable();\n    }\n    /**\n     * Close the toast.\n     */\n    close() {\n        this._overlayRef.detach();\n        this._afterClosed.next();\n        this._manualClose.next();\n        this._afterClosed.complete();\n        this._manualClose.complete();\n        this._activate.complete();\n        this._resetTimeout.complete();\n        this._countDuplicate.complete();\n    }\n    /** Gets an observable that is notified when the toast is finished closing. */\n    afterClosed() {\n        return this._afterClosed.asObservable();\n    }\n    isInactive() {\n        return this._activate.isStopped;\n    }\n    activate() {\n        this._activate.next();\n        this._activate.complete();\n    }\n    /** Gets an observable that is notified when the toast has started opening. */\n    afterActivate() {\n        return this._activate.asObservable();\n    }\n    /** Reset the toast timouts and count duplicates */\n    onDuplicate(resetTimeout, countDuplicate) {\n        if (resetTimeout) {\n            this._resetTimeout.next();\n        }\n        if (countDuplicate) {\n            this._countDuplicate.next(++this.duplicatesCount);\n        }\n    }\n}\n/** Custom injector type specifically for instantiating components with a toast. */\nclass ToastInjector {\n    constructor(_toastPackage, _parentInjector) {\n        this._toastPackage = _toastPackage;\n        this._parentInjector = _parentInjector;\n    }\n    get(token, notFoundValue, flags) {\n        if (token === ToastPackage) {\n            return this._toastPackage;\n        }\n        return this._parentInjector.get(token, notFoundValue, flags);\n    }\n}\n\nlet ToastrService = class ToastrService {\n    constructor(token, overlay, _injector, sanitizer, ngZone) {\n        this.overlay = overlay;\n        this._injector = _injector;\n        this.sanitizer = sanitizer;\n        this.ngZone = ngZone;\n        this.currentlyActive = 0;\n        this.toasts = [];\n        this.index = 0;\n        this.toastrConfig = Object.assign({}, token.default, token.config);\n        if (token.config.iconClasses) {\n            this.toastrConfig.iconClasses = Object.assign({}, token.default.iconClasses, token.config.iconClasses);\n        }\n    }\n    /** show toast */\n    show(message, title, override = {}, type = '') {\n        return this._preBuildNotification(type, message, title, this.applyConfig(override));\n    }\n    /** show successful toast */\n    success(message, title, override = {}) {\n        const type = this.toastrConfig.iconClasses.success || '';\n        return this._preBuildNotification(type, message, title, this.applyConfig(override));\n    }\n    /** show error toast */\n    error(message, title, override = {}) {\n        const type = this.toastrConfig.iconClasses.error || '';\n        return this._preBuildNotification(type, message, title, this.applyConfig(override));\n    }\n    /** show info toast */\n    info(message, title, override = {}) {\n        const type = this.toastrConfig.iconClasses.info || '';\n        return this._preBuildNotification(type, message, title, this.applyConfig(override));\n    }\n    /** show warning toast */\n    warning(message, title, override = {}) {\n        const type = this.toastrConfig.iconClasses.warning || '';\n        return this._preBuildNotification(type, message, title, this.applyConfig(override));\n    }\n    /**\n     * Remove all or a single toast by id\n     */\n    clear(toastId) {\n        // Call every toastRef manualClose function\n        for (const toast of this.toasts) {\n            if (toastId !== undefined) {\n                if (toast.toastId === toastId) {\n                    toast.toastRef.manualClose();\n                    return;\n                }\n            }\n            else {\n                toast.toastRef.manualClose();\n            }\n        }\n    }\n    /**\n     * Remove and destroy a single toast by id\n     */\n    remove(toastId) {\n        const found = this._findToast(toastId);\n        if (!found) {\n            return false;\n        }\n        found.activeToast.toastRef.close();\n        this.toasts.splice(found.index, 1);\n        this.currentlyActive = this.currentlyActive - 1;\n        if (!this.toastrConfig.maxOpened || !this.toasts.length) {\n            return false;\n        }\n        if (this.currentlyActive < this.toastrConfig.maxOpened &&\n            this.toasts[this.currentlyActive]) {\n            const p = this.toasts[this.currentlyActive].toastRef;\n            if (!p.isInactive()) {\n                this.currentlyActive = this.currentlyActive + 1;\n                p.activate();\n            }\n        }\n        return true;\n    }\n    /**\n     * Determines if toast message is already shown\n     */\n    findDuplicate(message, resetOnDuplicate, countDuplicates) {\n        for (const toast of this.toasts) {\n            if (toast.message === message) {\n                toast.toastRef.onDuplicate(resetOnDuplicate, countDuplicates);\n                return toast;\n            }\n        }\n        return null;\n    }\n    /** create a clone of global config and apply individual settings */\n    applyConfig(override = {}) {\n        return Object.assign({}, this.toastrConfig, override);\n    }\n    /**\n     * Find toast object by id\n     */\n    _findToast(toastId) {\n        for (let i = 0; i < this.toasts.length; i++) {\n            if (this.toasts[i].toastId === toastId) {\n                return { index: i, activeToast: this.toasts[i] };\n            }\n        }\n        return null;\n    }\n    /**\n     * Determines the need to run inside angular's zone then builds the toast\n     */\n    _preBuildNotification(toastType, message, title, config) {\n        if (config.onActivateTick) {\n            return this.ngZone.run(() => this._buildNotification(toastType, message, title, config));\n        }\n        return this._buildNotification(toastType, message, title, config);\n    }\n    /**\n     * Creates and attaches toast data to component\n     * returns the active toast, or in case preventDuplicates is enabled the original/non-duplicate active toast.\n     */\n    _buildNotification(toastType, message, title, config) {\n        if (!config.toastComponent) {\n            throw new Error('toastComponent required');\n        }\n        // max opened and auto dismiss = true\n        // if timeout = 0 resetting it would result in setting this.hideTime = Date.now(). Hence, we only want to reset timeout if there is\n        // a timeout at all\n        const duplicate = this.findDuplicate(message, this.toastrConfig.resetTimeoutOnDuplicate && config.timeOut > 0, this.toastrConfig.countDuplicates);\n        if (message && this.toastrConfig.preventDuplicates && duplicate !== null) {\n            return duplicate;\n        }\n        this.previousToastMessage = message;\n        let keepInactive = false;\n        if (this.toastrConfig.maxOpened &&\n            this.currentlyActive >= this.toastrConfig.maxOpened) {\n            keepInactive = true;\n            if (this.toastrConfig.autoDismiss) {\n                this.clear(this.toasts[0].toastId);\n            }\n        }\n        const overlayRef = this.overlay.create(config.positionClass, this.overlayContainer);\n        this.index = this.index + 1;\n        let sanitizedMessage = message;\n        if (message && config.enableHtml) {\n            sanitizedMessage = this.sanitizer.sanitize(SecurityContext.HTML, message);\n        }\n        const toastRef = new ToastRef(overlayRef);\n        const toastPackage = new ToastPackage(this.index, config, sanitizedMessage, title, toastType, toastRef);\n        const toastInjector = new ToastInjector(toastPackage, this._injector);\n        const component = new ComponentPortal(config.toastComponent, toastInjector);\n        const portal = overlayRef.attach(component, this.toastrConfig.newestOnTop);\n        toastRef.componentInstance = portal._component;\n        const ins = {\n            toastId: this.index,\n            message: message || '',\n            toastRef,\n            onShown: toastRef.afterActivate(),\n            onHidden: toastRef.afterClosed(),\n            onTap: toastPackage.onTap(),\n            onAction: toastPackage.onAction(),\n            portal\n        };\n        if (!keepInactive) {\n            this.currentlyActive = this.currentlyActive + 1;\n            setTimeout(() => {\n                ins.toastRef.activate();\n            });\n        }\n        this.toasts.push(ins);\n        return ins;\n    }\n};\nToastrService.ɵfac = function ToastrService_Factory(t) { return new (t || ToastrService)(ɵngcc0.ɵɵinject(TOAST_CONFIG), ɵngcc0.ɵɵinject(Overlay), ɵngcc0.ɵɵinject(ɵngcc0.Injector), ɵngcc0.ɵɵinject(ɵngcc1.DomSanitizer), ɵngcc0.ɵɵinject(ɵngcc0.NgZone)); };\nToastrService.ɵprov = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjectable({ token: ToastrService, factory: function (t) { return ToastrService.ɵfac(t); }, providedIn: 'root' });\nToastrService.ctorParameters = () => [\n    { type: undefined, decorators: [{ type: Inject, args: [TOAST_CONFIG,] }] },\n    { type: Overlay },\n    { type: Injector },\n    { type: DomSanitizer },\n    { type: NgZone }\n];\nToastrService.ngInjectableDef = ɵɵdefineInjectable({ factory: function ToastrService_Factory() { return new ToastrService(ɵɵinject(TOAST_CONFIG), ɵɵinject(Overlay), ɵɵinject(INJECTOR), ɵɵinject(DomSanitizer), ɵɵinject(NgZone)); }, token: ToastrService, providedIn: \"root\" });\nToastrService = __decorate([ __param(0, Inject(TOAST_CONFIG)),\n    __metadata(\"design:paramtypes\", [Object, Overlay,\n        Injector,\n        DomSanitizer,\n        NgZone])\n], ToastrService);\n\nlet Toast = class Toast {\n    constructor(toastrService, toastPackage, ngZone) {\n        this.toastrService = toastrService;\n        this.toastPackage = toastPackage;\n        this.ngZone = ngZone;\n        /** width of progress bar */\n        this.width = -1;\n        /** a combination of toast type and options.toastClass */\n        this.toastClasses = '';\n        /** controls animation */\n        this.state = {\n            value: 'inactive',\n            params: {\n                easeTime: this.toastPackage.config.easeTime,\n                easing: 'ease-in'\n            }\n        };\n        this.message = toastPackage.message;\n        this.title = toastPackage.title;\n        this.options = toastPackage.config;\n        this.originalTimeout = toastPackage.config.timeOut;\n        this.toastClasses = `${toastPackage.toastType} ${toastPackage.config.toastClass}`;\n        this.sub = toastPackage.toastRef.afterActivate().subscribe(() => {\n            this.activateToast();\n        });\n        this.sub1 = toastPackage.toastRef.manualClosed().subscribe(() => {\n            this.remove();\n        });\n        this.sub2 = toastPackage.toastRef.timeoutReset().subscribe(() => {\n            this.resetTimeout();\n        });\n        this.sub3 = toastPackage.toastRef.countDuplicate().subscribe(count => {\n            this.duplicatesCount = count;\n        });\n    }\n    /** hides component when waiting to be displayed */\n    get displayStyle() {\n        if (this.state.value === 'inactive') {\n            return 'none';\n        }\n    }\n    ngOnDestroy() {\n        this.sub.unsubscribe();\n        this.sub1.unsubscribe();\n        this.sub2.unsubscribe();\n        this.sub3.unsubscribe();\n        clearInterval(this.intervalId);\n        clearTimeout(this.timeout);\n    }\n    /**\n     * activates toast and sets timeout\n     */\n    activateToast() {\n        this.state = Object.assign({}, this.state, { value: 'active' });\n        if (!(this.options.disableTimeOut === true || this.options.disableTimeOut === 'timeOut') && this.options.timeOut) {\n            this.outsideTimeout(() => this.remove(), this.options.timeOut);\n            this.hideTime = new Date().getTime() + this.options.timeOut;\n            if (this.options.progressBar) {\n                this.outsideInterval(() => this.updateProgress(), 10);\n            }\n        }\n    }\n    /**\n     * updates progress bar width\n     */\n    updateProgress() {\n        if (this.width === 0 || this.width === 100 || !this.options.timeOut) {\n            return;\n        }\n        const now = new Date().getTime();\n        const remaining = this.hideTime - now;\n        this.width = (remaining / this.options.timeOut) * 100;\n        if (this.options.progressAnimation === 'increasing') {\n            this.width = 100 - this.width;\n        }\n        if (this.width <= 0) {\n            this.width = 0;\n        }\n        if (this.width >= 100) {\n            this.width = 100;\n        }\n    }\n    resetTimeout() {\n        clearTimeout(this.timeout);\n        clearInterval(this.intervalId);\n        this.state = Object.assign({}, this.state, { value: 'active' });\n        this.outsideTimeout(() => this.remove(), this.originalTimeout);\n        this.options.timeOut = this.originalTimeout;\n        this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n        this.width = -1;\n        if (this.options.progressBar) {\n            this.outsideInterval(() => this.updateProgress(), 10);\n        }\n    }\n    /**\n     * tells toastrService to remove this toast after animation time\n     */\n    remove() {\n        if (this.state.value === 'removed') {\n            return;\n        }\n        clearTimeout(this.timeout);\n        this.state = Object.assign({}, this.state, { value: 'removed' });\n        this.outsideTimeout(() => this.toastrService.remove(this.toastPackage.toastId), +this.toastPackage.config.easeTime);\n    }\n    tapToast() {\n        if (this.state.value === 'removed') {\n            return;\n        }\n        this.toastPackage.triggerTap();\n        if (this.options.tapToDismiss) {\n            this.remove();\n        }\n    }\n    stickAround() {\n        if (this.state.value === 'removed') {\n            return;\n        }\n        clearTimeout(this.timeout);\n        this.options.timeOut = 0;\n        this.hideTime = 0;\n        // disable progressBar\n        clearInterval(this.intervalId);\n        this.width = 0;\n    }\n    delayedHideToast() {\n        if ((this.options.disableTimeOut === true || this.options.disableTimeOut === 'extendedTimeOut') ||\n            this.options.extendedTimeOut === 0 ||\n            this.state.value === 'removed') {\n            return;\n        }\n        this.outsideTimeout(() => this.remove(), this.options.extendedTimeOut);\n        this.options.timeOut = this.options.extendedTimeOut;\n        this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n        this.width = -1;\n        if (this.options.progressBar) {\n            this.outsideInterval(() => this.updateProgress(), 10);\n        }\n    }\n    outsideTimeout(func, timeout) {\n        if (this.ngZone) {\n            this.ngZone.runOutsideAngular(() => (this.timeout = setTimeout(() => this.runInsideAngular(func), timeout)));\n        }\n        else {\n            this.timeout = setTimeout(() => func(), timeout);\n        }\n    }\n    outsideInterval(func, timeout) {\n        if (this.ngZone) {\n            this.ngZone.runOutsideAngular(() => (this.intervalId = setInterval(() => this.runInsideAngular(func), timeout)));\n        }\n        else {\n            this.intervalId = setInterval(() => func(), timeout);\n        }\n    }\n    runInsideAngular(func) {\n        if (this.ngZone) {\n            this.ngZone.run(() => func());\n        }\n        else {\n            func();\n        }\n    }\n};\nToast.ɵfac = function Toast_Factory(t) { return new (t || Toast)(ɵngcc0.ɵɵdirectiveInject(ToastrService), ɵngcc0.ɵɵdirectiveInject(ToastPackage), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.NgZone)); };\nToast.ɵcmp = /*@__PURE__*/ ɵngcc0.ɵɵdefineComponent({ type: Toast, selectors: [[\"\", \"toast-component\", \"\"]], hostVars: 5, hostBindings: function Toast_HostBindings(rf, ctx) { if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"click\", function Toast_click_HostBindingHandler() { return ctx.tapToast(); })(\"mouseenter\", function Toast_mouseenter_HostBindingHandler() { return ctx.stickAround(); })(\"mouseleave\", function Toast_mouseleave_HostBindingHandler() { return ctx.delayedHideToast(); });\n    } if (rf & 2) {\n        ɵngcc0.ɵɵsyntheticHostProperty(\"@flyInOut\", ctx.state);\n        ɵngcc0.ɵɵclassMap(ctx.toastClasses);\n        ɵngcc0.ɵɵstyleProp(\"display\", ctx.displayStyle);\n    } }, attrs: _c0, decls: 5, vars: 5, consts: [[\"class\", \"toast-close-button\", \"aria-label\", \"Close\", 3, \"click\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [\"role\", \"alertdialog\", \"aria-live\", \"polite\", 3, \"class\", \"innerHTML\", 4, \"ngIf\"], [\"role\", \"alertdialog\", \"aria-live\", \"polite\", 3, \"class\", 4, \"ngIf\"], [4, \"ngIf\"], [\"aria-label\", \"Close\", 1, \"toast-close-button\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"role\", \"alertdialog\", \"aria-live\", \"polite\", 3, \"innerHTML\"], [\"role\", \"alertdialog\", \"aria-live\", \"polite\"], [1, \"toast-progress\"]], template: function Toast_Template(rf, ctx) { if (rf & 1) {\n        ɵngcc0.ɵɵtemplate(0, Toast_button_0_Template, 3, 0, \"button\", 0);\n        ɵngcc0.ɵɵtemplate(1, Toast_div_1_Template, 3, 5, \"div\", 1);\n        ɵngcc0.ɵɵtemplate(2, Toast_div_2_Template, 1, 3, \"div\", 2);\n        ɵngcc0.ɵɵtemplate(3, Toast_div_3_Template, 2, 4, \"div\", 3);\n        ɵngcc0.ɵɵtemplate(4, Toast_div_4_Template, 2, 2, \"div\", 4);\n    } if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.options.closeButton);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.title);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.message && ctx.options.enableHtml);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.message && !ctx.options.enableHtml);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.options.progressBar);\n    } }, dependencies: [ɵngcc2.NgIf], encapsulation: 2, data: { animation: [\n            trigger('flyInOut', [\n                state('inactive', style({ opacity: 0 })),\n                state('active', style({ opacity: 1 })),\n                state('removed', style({ opacity: 0 })),\n                transition('inactive => active', animate('{{ easeTime }}ms {{ easing }}')),\n                transition('active => removed', animate('{{ easeTime }}ms {{ easing }}'))\n            ])\n        ] } });\nToast.ctorParameters = () => [\n    { type: ToastrService },\n    { type: ToastPackage },\n    { type: NgZone }\n];\n__decorate([\n    HostBinding('class'),\n    __metadata(\"design:type\", Object)\n], Toast.prototype, \"toastClasses\", void 0);\n__decorate([\n    HostBinding('@flyInOut'),\n    __metadata(\"design:type\", Object)\n], Toast.prototype, \"state\", void 0);\n__decorate([\n    HostBinding('style.display'),\n    __metadata(\"design:type\", Object),\n    __metadata(\"design:paramtypes\", [])\n], Toast.prototype, \"displayStyle\", null);\n__decorate([\n    HostListener('click'),\n    __metadata(\"design:type\", Function),\n    __metadata(\"design:paramtypes\", []),\n    __metadata(\"design:returntype\", void 0)\n], Toast.prototype, \"tapToast\", null);\n__decorate([\n    HostListener('mouseenter'),\n    __metadata(\"design:type\", Function),\n    __metadata(\"design:paramtypes\", []),\n    __metadata(\"design:returntype\", void 0)\n], Toast.prototype, \"stickAround\", null);\n__decorate([\n    HostListener('mouseleave'),\n    __metadata(\"design:type\", Function),\n    __metadata(\"design:paramtypes\", []),\n    __metadata(\"design:returntype\", void 0)\n], Toast.prototype, \"delayedHideToast\", null);\nToast = __decorate([ __metadata(\"design:paramtypes\", [ToastrService,\n        ToastPackage,\n        NgZone])\n], Toast);\n\nvar ToastrModule_1;\nconst DefaultGlobalConfig = Object.assign({}, DefaultNoComponentGlobalConfig, { toastComponent: Toast });\nlet ToastrModule = ToastrModule_1 = class ToastrModule {\n    static forRoot(config = {}) {\n        return {\n            ngModule: ToastrModule_1,\n            providers: [\n                {\n                    provide: TOAST_CONFIG,\n                    useValue: {\n                        default: DefaultGlobalConfig,\n                        config,\n                    },\n                },\n            ],\n        };\n    }\n};\nToastrModule.ɵfac = function ToastrModule_Factory(t) { return new (t || ToastrModule)(); };\nToastrModule.ɵmod = /*@__PURE__*/ ɵngcc0.ɵɵdefineNgModule({ type: ToastrModule });\nToastrModule.ɵinj = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjector({ imports: [CommonModule] });\nlet ToastrComponentlessModule = class ToastrComponentlessModule {\n    static forRoot(config = {}) {\n        return {\n            ngModule: ToastrModule,\n            providers: [\n                {\n                    provide: TOAST_CONFIG,\n                    useValue: {\n                        default: DefaultNoComponentGlobalConfig,\n                        config,\n                    },\n                },\n            ],\n        };\n    }\n};\nToastrComponentlessModule.ɵfac = function ToastrComponentlessModule_Factory(t) { return new (t || ToastrComponentlessModule)(); };\nToastrComponentlessModule.ɵmod = /*@__PURE__*/ ɵngcc0.ɵɵdefineNgModule({ type: ToastrComponentlessModule });\nToastrComponentlessModule.ɵinj = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjector({ imports: [CommonModule] });\n\nvar ToastNoAnimationModule_1;\nlet ToastNoAnimation = class ToastNoAnimation {\n    constructor(toastrService, toastPackage, appRef) {\n        this.toastrService = toastrService;\n        this.toastPackage = toastPackage;\n        this.appRef = appRef;\n        /** width of progress bar */\n        this.width = -1;\n        /** a combination of toast type and options.toastClass */\n        this.toastClasses = '';\n        /** controls animation */\n        this.state = 'inactive';\n        this.message = toastPackage.message;\n        this.title = toastPackage.title;\n        this.options = toastPackage.config;\n        this.originalTimeout = toastPackage.config.timeOut;\n        this.toastClasses = `${toastPackage.toastType} ${toastPackage.config.toastClass}`;\n        this.sub = toastPackage.toastRef.afterActivate().subscribe(() => {\n            this.activateToast();\n        });\n        this.sub1 = toastPackage.toastRef.manualClosed().subscribe(() => {\n            this.remove();\n        });\n        this.sub2 = toastPackage.toastRef.timeoutReset().subscribe(() => {\n            this.resetTimeout();\n        });\n        this.sub3 = toastPackage.toastRef.countDuplicate().subscribe(count => {\n            this.duplicatesCount = count;\n        });\n    }\n    /** hides component when waiting to be displayed */\n    get displayStyle() {\n        if (this.state === 'inactive') {\n            return 'none';\n        }\n    }\n    ngOnDestroy() {\n        this.sub.unsubscribe();\n        this.sub1.unsubscribe();\n        this.sub2.unsubscribe();\n        this.sub3.unsubscribe();\n        clearInterval(this.intervalId);\n        clearTimeout(this.timeout);\n    }\n    /**\n     * activates toast and sets timeout\n     */\n    activateToast() {\n        this.state = 'active';\n        if (!(this.options.disableTimeOut === true || this.options.disableTimeOut === 'timeOut') && this.options.timeOut) {\n            this.timeout = setTimeout(() => {\n                this.remove();\n            }, this.options.timeOut);\n            this.hideTime = new Date().getTime() + this.options.timeOut;\n            if (this.options.progressBar) {\n                this.intervalId = setInterval(() => this.updateProgress(), 10);\n            }\n        }\n        if (this.options.onActivateTick) {\n            this.appRef.tick();\n        }\n    }\n    /**\n     * updates progress bar width\n     */\n    updateProgress() {\n        if (this.width === 0 || this.width === 100 || !this.options.timeOut) {\n            return;\n        }\n        const now = new Date().getTime();\n        const remaining = this.hideTime - now;\n        this.width = (remaining / this.options.timeOut) * 100;\n        if (this.options.progressAnimation === 'increasing') {\n            this.width = 100 - this.width;\n        }\n        if (this.width <= 0) {\n            this.width = 0;\n        }\n        if (this.width >= 100) {\n            this.width = 100;\n        }\n    }\n    resetTimeout() {\n        clearTimeout(this.timeout);\n        clearInterval(this.intervalId);\n        this.state = 'active';\n        this.options.timeOut = this.originalTimeout;\n        this.timeout = setTimeout(() => this.remove(), this.originalTimeout);\n        this.hideTime = new Date().getTime() + (this.originalTimeout || 0);\n        this.width = -1;\n        if (this.options.progressBar) {\n            this.intervalId = setInterval(() => this.updateProgress(), 10);\n        }\n    }\n    /**\n     * tells toastrService to remove this toast after animation time\n     */\n    remove() {\n        if (this.state === 'removed') {\n            return;\n        }\n        clearTimeout(this.timeout);\n        this.state = 'removed';\n        this.timeout = setTimeout(() => this.toastrService.remove(this.toastPackage.toastId));\n    }\n    tapToast() {\n        if (this.state === 'removed') {\n            return;\n        }\n        this.toastPackage.triggerTap();\n        if (this.options.tapToDismiss) {\n            this.remove();\n        }\n    }\n    stickAround() {\n        if (this.state === 'removed') {\n            return;\n        }\n        clearTimeout(this.timeout);\n        this.options.timeOut = 0;\n        this.hideTime = 0;\n        // disable progressBar\n        clearInterval(this.intervalId);\n        this.width = 0;\n    }\n    delayedHideToast() {\n        if ((this.options.disableTimeOut === true || this.options.disableTimeOut === 'extendedTimeOut') ||\n            this.options.extendedTimeOut === 0 ||\n            this.state === 'removed') {\n            return;\n        }\n        this.timeout = setTimeout(() => this.remove(), this.options.extendedTimeOut);\n        this.options.timeOut = this.options.extendedTimeOut;\n        this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n        this.width = -1;\n        if (this.options.progressBar) {\n            this.intervalId = setInterval(() => this.updateProgress(), 10);\n        }\n    }\n};\nToastNoAnimation.ɵfac = function ToastNoAnimation_Factory(t) { return new (t || ToastNoAnimation)(ɵngcc0.ɵɵdirectiveInject(ToastrService), ɵngcc0.ɵɵdirectiveInject(ToastPackage), ɵngcc0.ɵɵdirectiveInject(ɵngcc0.ApplicationRef)); };\nToastNoAnimation.ɵcmp = /*@__PURE__*/ ɵngcc0.ɵɵdefineComponent({ type: ToastNoAnimation, selectors: [[\"\", \"toast-component\", \"\"]], hostVars: 4, hostBindings: function ToastNoAnimation_HostBindings(rf, ctx) { if (rf & 1) {\n        ɵngcc0.ɵɵlistener(\"click\", function ToastNoAnimation_click_HostBindingHandler() { return ctx.tapToast(); })(\"mouseenter\", function ToastNoAnimation_mouseenter_HostBindingHandler() { return ctx.stickAround(); })(\"mouseleave\", function ToastNoAnimation_mouseleave_HostBindingHandler() { return ctx.delayedHideToast(); });\n    } if (rf & 2) {\n        ɵngcc0.ɵɵclassMap(ctx.toastClasses);\n        ɵngcc0.ɵɵstyleProp(\"display\", ctx.displayStyle);\n    } }, attrs: _c0, decls: 5, vars: 5, consts: [[\"class\", \"toast-close-button\", \"aria-label\", \"Close\", 3, \"click\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [\"role\", \"alert\", \"aria-live\", \"polite\", 3, \"class\", \"innerHTML\", 4, \"ngIf\"], [\"role\", \"alert\", \"aria-live\", \"polite\", 3, \"class\", 4, \"ngIf\"], [4, \"ngIf\"], [\"aria-label\", \"Close\", 1, \"toast-close-button\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"role\", \"alert\", \"aria-live\", \"polite\", 3, \"innerHTML\"], [\"role\", \"alert\", \"aria-live\", \"polite\"], [1, \"toast-progress\"]], template: function ToastNoAnimation_Template(rf, ctx) { if (rf & 1) {\n        ɵngcc0.ɵɵtemplate(0, ToastNoAnimation_button_0_Template, 3, 0, \"button\", 0);\n        ɵngcc0.ɵɵtemplate(1, ToastNoAnimation_div_1_Template, 3, 5, \"div\", 1);\n        ɵngcc0.ɵɵtemplate(2, ToastNoAnimation_div_2_Template, 1, 3, \"div\", 2);\n        ɵngcc0.ɵɵtemplate(3, ToastNoAnimation_div_3_Template, 2, 4, \"div\", 3);\n        ɵngcc0.ɵɵtemplate(4, ToastNoAnimation_div_4_Template, 2, 2, \"div\", 4);\n    } if (rf & 2) {\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.options.closeButton);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.title);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.message && ctx.options.enableHtml);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.message && !ctx.options.enableHtml);\n        ɵngcc0.ɵɵadvance(1);\n        ɵngcc0.ɵɵproperty(\"ngIf\", ctx.options.progressBar);\n    } }, dependencies: [ɵngcc2.NgIf], encapsulation: 2 });\nToastNoAnimation.ctorParameters = () => [\n    { type: ToastrService },\n    { type: ToastPackage },\n    { type: ApplicationRef }\n];\n__decorate([\n    HostBinding('class'),\n    __metadata(\"design:type\", Object)\n], ToastNoAnimation.prototype, \"toastClasses\", void 0);\n__decorate([\n    HostBinding('style.display'),\n    __metadata(\"design:type\", Object),\n    __metadata(\"design:paramtypes\", [])\n], ToastNoAnimation.prototype, \"displayStyle\", null);\n__decorate([\n    HostListener('click'),\n    __metadata(\"design:type\", Function),\n    __metadata(\"design:paramtypes\", []),\n    __metadata(\"design:returntype\", void 0)\n], ToastNoAnimation.prototype, \"tapToast\", null);\n__decorate([\n    HostListener('mouseenter'),\n    __metadata(\"design:type\", Function),\n    __metadata(\"design:paramtypes\", []),\n    __metadata(\"design:returntype\", void 0)\n], ToastNoAnimation.prototype, \"stickAround\", null);\n__decorate([\n    HostListener('mouseleave'),\n    __metadata(\"design:type\", Function),\n    __metadata(\"design:paramtypes\", []),\n    __metadata(\"design:returntype\", void 0)\n], ToastNoAnimation.prototype, \"delayedHideToast\", null);\nToastNoAnimation = __decorate([ __metadata(\"design:paramtypes\", [ToastrService,\n        ToastPackage,\n        ApplicationRef])\n], ToastNoAnimation);\nconst DefaultNoAnimationsGlobalConfig = Object.assign({}, DefaultNoComponentGlobalConfig, { toastComponent: ToastNoAnimation });\nlet ToastNoAnimationModule = ToastNoAnimationModule_1 = class ToastNoAnimationModule {\n    static forRoot(config = {}) {\n        return {\n            ngModule: ToastNoAnimationModule_1,\n            providers: [\n                {\n                    provide: TOAST_CONFIG,\n                    useValue: {\n                        default: DefaultNoAnimationsGlobalConfig,\n                        config,\n                    },\n                },\n            ],\n        };\n    }\n};\nToastNoAnimationModule.ɵfac = function ToastNoAnimationModule_Factory(t) { return new (t || ToastNoAnimationModule)(); };\nToastNoAnimationModule.ɵmod = /*@__PURE__*/ ɵngcc0.ɵɵdefineNgModule({ type: ToastNoAnimationModule });\nToastNoAnimationModule.ɵinj = /*@__PURE__*/ ɵngcc0.ɵɵdefineInjector({ imports: [CommonModule] });\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(ToastContainerDirective, [{\n        type: Directive,\n        args: [{\n                selector: '[toastContainer]',\n                exportAs: 'toastContainer'\n            }]\n    }], function () { return [{ type: ɵngcc0.ElementRef }]; }, null); })();\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(ToastContainerModule, [{\n        type: NgModule,\n        args: [{\n                declarations: [ToastContainerDirective],\n                exports: [ToastContainerDirective]\n            }]\n    }], null, null); })();\n(function () { (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(ToastContainerModule, { declarations: [ToastContainerDirective], exports: [ToastContainerDirective] }); })();\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(OverlayContainer, [{\n        type: Injectable,\n        args: [{ providedIn: 'root' }]\n    }], function () { return [{ type: undefined, decorators: [{\n                type: Inject,\n                args: [DOCUMENT]\n            }] }]; }, null); })();\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(Overlay, [{\n        type: Injectable,\n        args: [{ providedIn: 'root' }]\n    }], function () { return [{ type: OverlayContainer }, { type: ɵngcc0.ComponentFactoryResolver }, { type: ɵngcc0.ApplicationRef }, { type: undefined, decorators: [{\n                type: Inject,\n                args: [DOCUMENT]\n            }] }]; }, null); })();\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(ToastrService, [{\n        type: Injectable,\n        args: [{ providedIn: 'root' }]\n    }], function () { return [{ type: undefined, decorators: [{\n                type: Inject,\n                args: [TOAST_CONFIG]\n            }] }, { type: Overlay }, { type: ɵngcc0.Injector }, { type: ɵngcc1.DomSanitizer }, { type: ɵngcc0.NgZone }]; }, null); })();\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(Toast, [{\n        type: Component,\n        args: [{\n                selector: '[toast-component]',\n                template: `\n  <button *ngIf=\"options.closeButton\" (click)=\"remove()\" class=\"toast-close-button\" aria-label=\"Close\">\n    <span aria-hidden=\"true\">&times;</span>\n  </button>\n  <div *ngIf=\"title\" [class]=\"options.titleClass\" [attr.aria-label]=\"title\">\n    {{ title }} <ng-container *ngIf=\"duplicatesCount\">[{{ duplicatesCount + 1 }}]</ng-container>\n  </div>\n  <div *ngIf=\"message && options.enableHtml\" role=\"alertdialog\" aria-live=\"polite\"\n    [class]=\"options.messageClass\" [innerHTML]=\"message\">\n  </div>\n  <div *ngIf=\"message && !options.enableHtml\" role=\"alertdialog\" aria-live=\"polite\"\n    [class]=\"options.messageClass\" [attr.aria-label]=\"message\">\n    {{ message }}\n  </div>\n  <div *ngIf=\"options.progressBar\">\n    <div class=\"toast-progress\" [style.width]=\"width + '%'\"></div>\n  </div>\n  `,\n                animations: [\n                    trigger('flyInOut', [\n                        state('inactive', style({ opacity: 0 })),\n                        state('active', style({ opacity: 1 })),\n                        state('removed', style({ opacity: 0 })),\n                        transition('inactive => active', animate('{{ easeTime }}ms {{ easing }}')),\n                        transition('active => removed', animate('{{ easeTime }}ms {{ easing }}'))\n                    ])\n                ],\n                preserveWhitespaces: false\n            }]\n    }], function () { return [{ type: ToastrService }, { type: ToastPackage }, { type: ɵngcc0.NgZone }]; }, { toastClasses: [{\n            type: HostBinding,\n            args: ['class']\n        }], state: [{\n            type: HostBinding,\n            args: ['@flyInOut']\n        }], displayStyle: [{\n            type: HostBinding,\n            args: ['style.display']\n        }], tapToast: [{\n            type: HostListener,\n            args: ['click']\n        }], stickAround: [{\n            type: HostListener,\n            args: ['mouseenter']\n        }], delayedHideToast: [{\n            type: HostListener,\n            args: ['mouseleave']\n        }] }); })();\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(ToastrModule, [{\n        type: NgModule,\n        args: [{\n                imports: [CommonModule],\n                declarations: [Toast],\n                exports: [Toast],\n                entryComponents: [Toast]\n            }]\n    }], null, null); })();\n(function () { (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(ToastrModule, { declarations: function () { return [Toast]; }, imports: function () { return [CommonModule]; }, exports: function () { return [Toast]; } }); })();\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(ToastrComponentlessModule, [{\n        type: NgModule,\n        args: [{\n                imports: [CommonModule]\n            }]\n    }], null, null); })();\n(function () { (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(ToastrComponentlessModule, { imports: function () { return [CommonModule]; } }); })();\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(ToastNoAnimation, [{\n        type: Component,\n        args: [{\n                selector: '[toast-component]',\n                template: `\n  <button *ngIf=\"options.closeButton\" (click)=\"remove()\" class=\"toast-close-button\" aria-label=\"Close\">\n    <span aria-hidden=\"true\">&times;</span>\n  </button>\n  <div *ngIf=\"title\" [class]=\"options.titleClass\" [attr.aria-label]=\"title\">\n    {{ title }} <ng-container *ngIf=\"duplicatesCount\">[{{ duplicatesCount + 1 }}]</ng-container>\n  </div>\n  <div *ngIf=\"message && options.enableHtml\" role=\"alert\" aria-live=\"polite\"\n    [class]=\"options.messageClass\" [innerHTML]=\"message\">\n  </div>\n  <div *ngIf=\"message && !options.enableHtml\" role=\"alert\" aria-live=\"polite\"\n    [class]=\"options.messageClass\" [attr.aria-label]=\"message\">\n    {{ message }}\n  </div>\n  <div *ngIf=\"options.progressBar\">\n    <div class=\"toast-progress\" [style.width]=\"width + '%'\"></div>\n  </div>\n  `\n            }]\n    }], function () { return [{ type: ToastrService }, { type: ToastPackage }, { type: ɵngcc0.ApplicationRef }]; }, { toastClasses: [{\n            type: HostBinding,\n            args: ['class']\n        }], displayStyle: [{\n            type: HostBinding,\n            args: ['style.display']\n        }], tapToast: [{\n            type: HostListener,\n            args: ['click']\n        }], stickAround: [{\n            type: HostListener,\n            args: ['mouseenter']\n        }], delayedHideToast: [{\n            type: HostListener,\n            args: ['mouseleave']\n        }] }); })();\n(function () { (typeof ngDevMode === \"undefined\" || ngDevMode) && ɵngcc0.ɵsetClassMetadata(ToastNoAnimationModule, [{\n        type: NgModule,\n        args: [{\n                imports: [CommonModule],\n                declarations: [ToastNoAnimation],\n                exports: [ToastNoAnimation],\n                entryComponents: [ToastNoAnimation]\n            }]\n    }], null, null); })();\n(function () { (typeof ngJitMode === \"undefined\" || ngJitMode) && ɵngcc0.ɵɵsetNgModuleScope(ToastNoAnimationModule, { declarations: function () { return [ToastNoAnimation]; }, imports: function () { return [CommonModule]; }, exports: function () { return [ToastNoAnimation]; } }); })();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BasePortalHost, ComponentPortal, DefaultGlobalConfig, DefaultNoAnimationsGlobalConfig, DefaultNoComponentGlobalConfig, Overlay, OverlayContainer, OverlayRef, TOAST_CONFIG, Toast, ToastContainerDirective, ToastContainerModule, ToastInjector, ToastNoAnimation, ToastNoAnimationModule, ToastPackage, ToastRef, ToastrComponentlessModule, ToastrModule, ToastrService };\n\n"], "mappings": "AAAA,SAASA,UAAT,EAAqBC,UAArB,EAAiCC,OAAjC,QAAgD,OAAhD;AACA,SAASC,UAAT,EAAqBC,SAArB,EAAgCC,QAAhC,EAA0CC,cAA1C,EAA0DC,MAA1D,EAAkEC,kBAAlE,EAAsFC,QAAtF,EAAgGC,UAAhG,EAA4GC,wBAA5G,EAAsIC,cAAtI,EAAsJC,eAAtJ,EAAuKC,QAAvK,EAAiLC,MAAjL,EAAyLC,QAAzL,EAAmMC,WAAnM,EAAgNC,YAAhN,EAA8NC,SAA9N,QAA+O,eAA/O;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,KAAzB,EAAgCC,UAAhC,EAA4CC,OAA5C,QAA2D,qBAA3D;AACA,SAASC,OAAT,QAAwB,MAAxB;AACA,SAASC,YAAT,QAA6B,2BAA7B;AACA,SAASC,QAAT,EAAmBC,YAAnB,QAAuC,iBAAvC;AAEA,OAAO,KAAKC,MAAZ,MAAwB,eAAxB;AACA,OAAO,KAAKC,MAAZ,MAAwB,2BAAxB;AACA,OAAO,KAAKC,MAAZ,MAAwB,iBAAxB;AAEA,MAAMC,GAAG,GAAG,CAAC,iBAAD,EAAoB,EAApB,CAAZ;;AACA,SAASC,uBAAT,CAAiCC,EAAjC,EAAqCC,GAArC,EAA0C;EAAE,IAAID,EAAE,GAAG,CAAT,EAAY;IACpD,MAAME,GAAG,GAAGP,MAAM,CAACQ,gBAAP,EAAZ;;IACAR,MAAM,CAACS,cAAP,CAAsB,CAAtB,EAAyB,QAAzB,EAAmC,CAAnC;IACAT,MAAM,CAACU,UAAP,CAAkB,OAAlB,EAA2B,SAASC,+CAAT,GAA2D;MAAEX,MAAM,CAACY,aAAP,CAAqBL,GAArB;MAA2B,MAAMM,MAAM,GAAGb,MAAM,CAACc,aAAP,EAAf;MAAuC,OAAOd,MAAM,CAACe,WAAP,CAAmBF,MAAM,CAACG,MAAP,EAAnB,CAAP;IAA6C,CAAvM;IACAhB,MAAM,CAACS,cAAP,CAAsB,CAAtB,EAAyB,MAAzB,EAAiC,CAAjC;IACAT,MAAM,CAACiB,MAAP,CAAc,CAAd,EAAiB,QAAjB;IACAjB,MAAM,CAACkB,YAAP;EACH;AAAE;;AACH,SAASC,mCAAT,CAA6Cd,EAA7C,EAAiDC,GAAjD,EAAsD;EAAE,IAAID,EAAE,GAAG,CAAT,EAAY;IAChEL,MAAM,CAACoB,uBAAP,CAA+B,CAA/B;IACApB,MAAM,CAACiB,MAAP,CAAc,CAAd;IACAjB,MAAM,CAACqB,qBAAP;EACH;;EAAC,IAAIhB,EAAE,GAAG,CAAT,EAAY;IACV,MAAMiB,MAAM,GAAGtB,MAAM,CAACc,aAAP,CAAqB,CAArB,CAAf;IACAd,MAAM,CAACuB,SAAP,CAAiB,CAAjB;IACAvB,MAAM,CAACwB,kBAAP,CAA0B,GAA1B,EAA+BF,MAAM,CAACG,eAAP,GAAyB,CAAxD,EAA2D,GAA3D;EACH;AAAE;;AACH,SAASC,oBAAT,CAA8BrB,EAA9B,EAAkCC,GAAlC,EAAuC;EAAE,IAAID,EAAE,GAAG,CAAT,EAAY;IACjDL,MAAM,CAACS,cAAP,CAAsB,CAAtB,EAAyB,KAAzB;IACAT,MAAM,CAACiB,MAAP,CAAc,CAAd;IACAjB,MAAM,CAAC2B,UAAP,CAAkB,CAAlB,EAAqBR,mCAArB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,cAAhE,EAAgF,CAAhF;IACAnB,MAAM,CAACkB,YAAP;EACH;;EAAC,IAAIb,EAAE,GAAG,CAAT,EAAY;IACV,MAAMuB,MAAM,GAAG5B,MAAM,CAACc,aAAP,EAAf;IACAd,MAAM,CAAC6B,UAAP,CAAkBD,MAAM,CAACE,OAAP,CAAeC,UAAjC;IACA/B,MAAM,CAACgC,WAAP,CAAmB,YAAnB,EAAiCJ,MAAM,CAACK,KAAxC;IACAjC,MAAM,CAACuB,SAAP,CAAiB,CAAjB;IACAvB,MAAM,CAACwB,kBAAP,CAA0B,GAA1B,EAA+BI,MAAM,CAACK,KAAtC,EAA6C,GAA7C;IACAjC,MAAM,CAACuB,SAAP,CAAiB,CAAjB;IACAvB,MAAM,CAACkC,UAAP,CAAkB,MAAlB,EAA0BN,MAAM,CAACH,eAAjC;EACH;AAAE;;AACH,SAASU,oBAAT,CAA8B9B,EAA9B,EAAkCC,GAAlC,EAAuC;EAAE,IAAID,EAAE,GAAG,CAAT,EAAY;IACjDL,MAAM,CAACoC,SAAP,CAAiB,CAAjB,EAAoB,KAApB,EAA2B,CAA3B;EACH;;EAAC,IAAI/B,EAAE,GAAG,CAAT,EAAY;IACV,MAAMgC,MAAM,GAAGrC,MAAM,CAACc,aAAP,EAAf;IACAd,MAAM,CAAC6B,UAAP,CAAkBQ,MAAM,CAACP,OAAP,CAAeQ,YAAjC;IACAtC,MAAM,CAACkC,UAAP,CAAkB,WAAlB,EAA+BG,MAAM,CAACE,OAAtC,EAA+CvC,MAAM,CAACwC,cAAtD;EACH;AAAE;;AACH,SAASC,oBAAT,CAA8BpC,EAA9B,EAAkCC,GAAlC,EAAuC;EAAE,IAAID,EAAE,GAAG,CAAT,EAAY;IACjDL,MAAM,CAACS,cAAP,CAAsB,CAAtB,EAAyB,KAAzB,EAAgC,CAAhC;IACAT,MAAM,CAACiB,MAAP,CAAc,CAAd;IACAjB,MAAM,CAACkB,YAAP;EACH;;EAAC,IAAIb,EAAE,GAAG,CAAT,EAAY;IACV,MAAMqC,MAAM,GAAG1C,MAAM,CAACc,aAAP,EAAf;IACAd,MAAM,CAAC6B,UAAP,CAAkBa,MAAM,CAACZ,OAAP,CAAeQ,YAAjC;IACAtC,MAAM,CAACgC,WAAP,CAAmB,YAAnB,EAAiCU,MAAM,CAACH,OAAxC;IACAvC,MAAM,CAACuB,SAAP,CAAiB,CAAjB;IACAvB,MAAM,CAACwB,kBAAP,CAA0B,GAA1B,EAA+BkB,MAAM,CAACH,OAAtC,EAA+C,GAA/C;EACH;AAAE;;AACH,SAASI,oBAAT,CAA8BtC,EAA9B,EAAkCC,GAAlC,EAAuC;EAAE,IAAID,EAAE,GAAG,CAAT,EAAY;IACjDL,MAAM,CAACS,cAAP,CAAsB,CAAtB,EAAyB,KAAzB;IACAT,MAAM,CAACoC,SAAP,CAAiB,CAAjB,EAAoB,KAApB,EAA2B,CAA3B;IACApC,MAAM,CAACkB,YAAP;EACH;;EAAC,IAAIb,EAAE,GAAG,CAAT,EAAY;IACV,MAAMuC,MAAM,GAAG5C,MAAM,CAACc,aAAP,EAAf;IACAd,MAAM,CAACuB,SAAP,CAAiB,CAAjB;IACAvB,MAAM,CAAC6C,WAAP,CAAmB,OAAnB,EAA4BD,MAAM,CAACE,KAAP,GAAe,GAA3C;EACH;AAAE;;AACH,SAASC,kCAAT,CAA4C1C,EAA5C,EAAgDC,GAAhD,EAAqD;EAAE,IAAID,EAAE,GAAG,CAAT,EAAY;IAC/D,MAAME,GAAG,GAAGP,MAAM,CAACQ,gBAAP,EAAZ;;IACAR,MAAM,CAACS,cAAP,CAAsB,CAAtB,EAAyB,QAAzB,EAAmC,CAAnC;IACAT,MAAM,CAACU,UAAP,CAAkB,OAAlB,EAA2B,SAASsC,0DAAT,GAAsE;MAAEhD,MAAM,CAACY,aAAP,CAAqBL,GAArB;MAA2B,MAAMM,MAAM,GAAGb,MAAM,CAACc,aAAP,EAAf;MAAuC,OAAOd,MAAM,CAACe,WAAP,CAAmBF,MAAM,CAACG,MAAP,EAAnB,CAAP;IAA6C,CAAlN;IACAhB,MAAM,CAACS,cAAP,CAAsB,CAAtB,EAAyB,MAAzB,EAAiC,CAAjC;IACAT,MAAM,CAACiB,MAAP,CAAc,CAAd,EAAiB,QAAjB;IACAjB,MAAM,CAACkB,YAAP;EACH;AAAE;;AACH,SAAS+B,8CAAT,CAAwD5C,EAAxD,EAA4DC,GAA5D,EAAiE;EAAE,IAAID,EAAE,GAAG,CAAT,EAAY;IAC3EL,MAAM,CAACoB,uBAAP,CAA+B,CAA/B;IACApB,MAAM,CAACiB,MAAP,CAAc,CAAd;IACAjB,MAAM,CAACqB,qBAAP;EACH;;EAAC,IAAIhB,EAAE,GAAG,CAAT,EAAY;IACV,MAAMiB,MAAM,GAAGtB,MAAM,CAACc,aAAP,CAAqB,CAArB,CAAf;IACAd,MAAM,CAACuB,SAAP,CAAiB,CAAjB;IACAvB,MAAM,CAACwB,kBAAP,CAA0B,GAA1B,EAA+BF,MAAM,CAACG,eAAP,GAAyB,CAAxD,EAA2D,GAA3D;EACH;AAAE;;AACH,SAASyB,+BAAT,CAAyC7C,EAAzC,EAA6CC,GAA7C,EAAkD;EAAE,IAAID,EAAE,GAAG,CAAT,EAAY;IAC5DL,MAAM,CAACS,cAAP,CAAsB,CAAtB,EAAyB,KAAzB;IACAT,MAAM,CAACiB,MAAP,CAAc,CAAd;IACAjB,MAAM,CAAC2B,UAAP,CAAkB,CAAlB,EAAqBsB,8CAArB,EAAqE,CAArE,EAAwE,CAAxE,EAA2E,cAA3E,EAA2F,CAA3F;IACAjD,MAAM,CAACkB,YAAP;EACH;;EAAC,IAAIb,EAAE,GAAG,CAAT,EAAY;IACV,MAAMuB,MAAM,GAAG5B,MAAM,CAACc,aAAP,EAAf;IACAd,MAAM,CAAC6B,UAAP,CAAkBD,MAAM,CAACE,OAAP,CAAeC,UAAjC;IACA/B,MAAM,CAACgC,WAAP,CAAmB,YAAnB,EAAiCJ,MAAM,CAACK,KAAxC;IACAjC,MAAM,CAACuB,SAAP,CAAiB,CAAjB;IACAvB,MAAM,CAACwB,kBAAP,CAA0B,GAA1B,EAA+BI,MAAM,CAACK,KAAtC,EAA6C,GAA7C;IACAjC,MAAM,CAACuB,SAAP,CAAiB,CAAjB;IACAvB,MAAM,CAACkC,UAAP,CAAkB,MAAlB,EAA0BN,MAAM,CAACH,eAAjC;EACH;AAAE;;AACH,SAAS0B,+BAAT,CAAyC9C,EAAzC,EAA6CC,GAA7C,EAAkD;EAAE,IAAID,EAAE,GAAG,CAAT,EAAY;IAC5DL,MAAM,CAACoC,SAAP,CAAiB,CAAjB,EAAoB,KAApB,EAA2B,CAA3B;EACH;;EAAC,IAAI/B,EAAE,GAAG,CAAT,EAAY;IACV,MAAMgC,MAAM,GAAGrC,MAAM,CAACc,aAAP,EAAf;IACAd,MAAM,CAAC6B,UAAP,CAAkBQ,MAAM,CAACP,OAAP,CAAeQ,YAAjC;IACAtC,MAAM,CAACkC,UAAP,CAAkB,WAAlB,EAA+BG,MAAM,CAACE,OAAtC,EAA+CvC,MAAM,CAACwC,cAAtD;EACH;AAAE;;AACH,SAASY,+BAAT,CAAyC/C,EAAzC,EAA6CC,GAA7C,EAAkD;EAAE,IAAID,EAAE,GAAG,CAAT,EAAY;IAC5DL,MAAM,CAACS,cAAP,CAAsB,CAAtB,EAAyB,KAAzB,EAAgC,CAAhC;IACAT,MAAM,CAACiB,MAAP,CAAc,CAAd;IACAjB,MAAM,CAACkB,YAAP;EACH;;EAAC,IAAIb,EAAE,GAAG,CAAT,EAAY;IACV,MAAMqC,MAAM,GAAG1C,MAAM,CAACc,aAAP,EAAf;IACAd,MAAM,CAAC6B,UAAP,CAAkBa,MAAM,CAACZ,OAAP,CAAeQ,YAAjC;IACAtC,MAAM,CAACgC,WAAP,CAAmB,YAAnB,EAAiCU,MAAM,CAACH,OAAxC;IACAvC,MAAM,CAACuB,SAAP,CAAiB,CAAjB;IACAvB,MAAM,CAACwB,kBAAP,CAA0B,GAA1B,EAA+BkB,MAAM,CAACH,OAAtC,EAA+C,GAA/C;EACH;AAAE;;AACH,SAASc,+BAAT,CAAyChD,EAAzC,EAA6CC,GAA7C,EAAkD;EAAE,IAAID,EAAE,GAAG,CAAT,EAAY;IAC5DL,MAAM,CAACS,cAAP,CAAsB,CAAtB,EAAyB,KAAzB;IACAT,MAAM,CAACoC,SAAP,CAAiB,CAAjB,EAAoB,KAApB,EAA2B,CAA3B;IACApC,MAAM,CAACkB,YAAP;EACH;;EAAC,IAAIb,EAAE,GAAG,CAAT,EAAY;IACV,MAAMuC,MAAM,GAAG5C,MAAM,CAACc,aAAP,EAAf;IACAd,MAAM,CAACuB,SAAP,CAAiB,CAAjB;IACAvB,MAAM,CAAC6C,WAAP,CAAmB,OAAnB,EAA4BD,MAAM,CAACE,KAAP,GAAe,GAA3C;EACH;AAAE;;AACH,IAAIQ,uBAAuB,GAAG,MAAMA,uBAAN,CAA8B;EACxDC,WAAW,CAACC,EAAD,EAAK;IACZ,KAAKA,EAAL,GAAUA,EAAV;EACH;;EACDC,mBAAmB,GAAG;IAClB,OAAO,KAAKD,EAAL,CAAQE,aAAf;EACH;;AANuD,CAA5D;;AAQAJ,uBAAuB,CAACK,IAAxB,GAA+B,SAASC,+BAAT,CAAyCC,CAAzC,EAA4C;EAAE,OAAO,KAAKA,CAAC,IAAIP,uBAAV,EAAmCtD,MAAM,CAAC8D,iBAAP,CAAyB9D,MAAM,CAAC1B,UAAhC,CAAnC,CAAP;AAAyF,CAAtK;;AACAgF,uBAAuB,CAACS,IAAxB,GAA+B,aAAc/D,MAAM,CAACgE,iBAAP,CAAyB;EAAEC,IAAI,EAAEX,uBAAR;EAAiCY,SAAS,EAAE,CAAC,CAAC,EAAD,EAAK,gBAAL,EAAuB,EAAvB,CAAD,CAA5C;EAA0EC,QAAQ,EAAE,CAAC,gBAAD;AAApF,CAAzB,CAA7C;;AACAb,uBAAuB,CAACc,cAAxB,GAAyC,MAAM,CAC3C;EAAEH,IAAI,EAAE3F;AAAR,CAD2C,CAA/C;;AAGAgF,uBAAuB,GAAGnF,UAAU,CAAC,CAAEC,UAAU,CAAC,mBAAD,EAAsB,CAACE,UAAD,CAAtB,CAAZ,CAAD,EACjCgF,uBADiC,CAApC;AAEA,IAAIe,oBAAoB,GAAG,MAAMA,oBAAN,CAA2B,EAAtD;;AAEAA,oBAAoB,CAACV,IAArB,GAA4B,SAASW,4BAAT,CAAsCT,CAAtC,EAAyC;EAAE,OAAO,KAAKA,CAAC,IAAIQ,oBAAV,GAAP;AAA2C,CAAlH;;AACAA,oBAAoB,CAACE,IAArB,GAA4B,aAAcvE,MAAM,CAACwE,gBAAP,CAAwB;EAAEP,IAAI,EAAEI;AAAR,CAAxB,CAA1C;AACAA,oBAAoB,CAACI,IAArB,GAA4B,aAAczE,MAAM,CAAC0E,gBAAP,CAAwB,EAAxB,CAA1C;AAEA;AACA;AACA;;AACA,MAAMC,YAAN,CAAmB;EACfpB,WAAW,CAACqB,OAAD,EAAUC,MAAV,EAAkBtC,OAAlB,EAA2BN,KAA3B,EAAkC6C,SAAlC,EAA6CC,QAA7C,EAAuD;IAC9D,KAAKH,OAAL,GAAeA,OAAf;IACA,KAAKC,MAAL,GAAcA,MAAd;IACA,KAAKtC,OAAL,GAAeA,OAAf;IACA,KAAKN,KAAL,GAAaA,KAAb;IACA,KAAK6C,SAAL,GAAiBA,SAAjB;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,MAAL,GAAc,IAAIpF,OAAJ,EAAd;IACA,KAAKqF,SAAL,GAAiB,IAAIrF,OAAJ,EAAjB;IACA,KAAKmF,QAAL,CAAcG,WAAd,GAA4BC,SAA5B,CAAsC,MAAM;MACxC,KAAKF,SAAL,CAAeG,QAAf;;MACA,KAAKJ,MAAL,CAAYI,QAAZ;IACH,CAHD;EAIH;EACD;;;EACAC,UAAU,GAAG;IACT,KAAKL,MAAL,CAAYM,IAAZ;;IACA,IAAI,KAAKT,MAAL,CAAYU,YAAhB,EAA8B;MAC1B,KAAKP,MAAL,CAAYI,QAAZ;IACH;EACJ;;EACDI,KAAK,GAAG;IACJ,OAAO,KAAKR,MAAL,CAAYS,YAAZ,EAAP;EACH;EACD;;;EACAC,aAAa,CAACC,MAAD,EAAS;IAClB,KAAKV,SAAL,CAAeK,IAAf,CAAoBK,MAApB;EACH;;EACDC,QAAQ,GAAG;IACP,OAAO,KAAKX,SAAL,CAAeQ,YAAf,EAAP;EACH;;AA/Bc;;AAiCnB,MAAMI,8BAA8B,GAAG;EACnCC,SAAS,EAAE,CADwB;EAEnCC,WAAW,EAAE,KAFsB;EAGnCC,WAAW,EAAE,IAHsB;EAInCC,iBAAiB,EAAE,KAJgB;EAKnCC,eAAe,EAAE,KALkB;EAMnCC,uBAAuB,EAAE,KANU;EAOnCC,WAAW,EAAE;IACTC,KAAK,EAAE,aADE;IAETC,IAAI,EAAE,YAFG;IAGTC,OAAO,EAAE,eAHA;IAITC,OAAO,EAAE;EAJA,CAPsB;EAanC;EACAC,WAAW,EAAE,KAdsB;EAenCC,cAAc,EAAE,KAfmB;EAgBnCC,OAAO,EAAE,IAhB0B;EAiBnCC,eAAe,EAAE,IAjBkB;EAkBnCC,UAAU,EAAE,KAlBuB;EAmBnCC,WAAW,EAAE,KAnBsB;EAoBnCC,UAAU,EAAE,YApBuB;EAqBnCC,aAAa,EAAE,iBArBoB;EAsBnCjF,UAAU,EAAE,aAtBuB;EAuBnCO,YAAY,EAAE,eAvBqB;EAwBnC2E,MAAM,EAAE,SAxB2B;EAyBnCC,QAAQ,EAAE,GAzByB;EA0BnC3B,YAAY,EAAE,IA1BqB;EA2BnC4B,cAAc,EAAE,KA3BmB;EA4BnCC,iBAAiB,EAAE;AA5BgB,CAAvC;AA8BA,MAAMC,YAAY,GAAG,IAAI5I,cAAJ,CAAmB,aAAnB,CAArB;AAEA;AACA;AACA;;AACA,MAAM6I,eAAN,CAAsB;EAClB/D,WAAW,CAACgE,SAAD,EAAYC,QAAZ,EAAsB;IAC7B,KAAKD,SAAL,GAAiBA,SAAjB;IACA,KAAKC,QAAL,GAAgBA,QAAhB;EACH;EACD;;;EACAC,MAAM,CAACC,IAAD,EAAO1B,WAAP,EAAoB;IACtB,KAAK2B,aAAL,GAAqBD,IAArB;IACA,OAAOA,IAAI,CAACD,MAAL,CAAY,IAAZ,EAAkBzB,WAAlB,CAAP;EACH;EACD;;;EACA4B,MAAM,GAAG;IACL,MAAMF,IAAI,GAAG,KAAKC,aAAlB;;IACA,IAAID,IAAJ,EAAU;MACN,KAAKC,aAAL,GAAqBE,SAArB;MACA,OAAOH,IAAI,CAACE,MAAL,EAAP;IACH;EACJ;EACD;;;EACc,IAAVE,UAAU,GAAG;IACb,OAAO,KAAKH,aAAL,IAAsB,IAA7B;EACH;EACD;AACJ;AACA;AACA;;;EACII,eAAe,CAACL,IAAD,EAAO;IAClB,KAAKC,aAAL,GAAqBD,IAArB;EACH;;AA5BiB;AA8BtB;AACA;AACA;AACA;;;AACA,MAAMM,cAAN,CAAqB;EACjBP,MAAM,CAACQ,MAAD,EAASjC,WAAT,EAAsB;IACxB,KAAKkC,eAAL,GAAuBD,MAAvB;IACA,OAAO,KAAKE,qBAAL,CAA2BF,MAA3B,EAAmCjC,WAAnC,CAAP;EACH;;EACD4B,MAAM,GAAG;IACL,IAAI,KAAKM,eAAT,EAA0B;MACtB,KAAKA,eAAL,CAAqBH,eAArB;IACH;;IACD,KAAKG,eAAL,GAAuBL,SAAvB;;IACA,IAAI,KAAKO,UAAT,EAAqB;MACjB,KAAKA,UAAL;;MACA,KAAKA,UAAL,GAAkBP,SAAlB;IACH;EACJ;;EACDQ,YAAY,CAACC,EAAD,EAAK;IACb,KAAKF,UAAL,GAAkBE,EAAlB;EACH;;AAjBgB;AAoBrB;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,aAAN,SAA4BP,cAA5B,CAA2C;EACvCzE,WAAW,CAACiF,eAAD,EAAkBC,yBAAlB,EAA6CC,OAA7C,EAAsD;IAC7D;IACA,KAAKF,eAAL,GAAuBA,eAAvB;IACA,KAAKC,yBAAL,GAAiCA,yBAAjC;IACA,KAAKC,OAAL,GAAeA,OAAf;EACH;EACD;AACJ;AACA;AACA;;;EACIP,qBAAqB,CAACF,MAAD,EAASjC,WAAT,EAAsB;IACvC,MAAM2C,gBAAgB,GAAG,KAAKF,yBAAL,CAA+BG,uBAA/B,CAAuDX,MAAM,CAACV,SAA9D,CAAzB;;IACA,IAAIsB,YAAJ,CAFuC,CAGvC;IACA;IACA;IACA;IACA;;IACAA,YAAY,GAAGF,gBAAgB,CAACG,MAAjB,CAAwBb,MAAM,CAACT,QAA/B,CAAf,CARuC,CASvC;IACA;IACA;IACA;;IACA,KAAKkB,OAAL,CAAaK,UAAb,CAAwBF,YAAY,CAACG,QAArC;;IACA,KAAKX,YAAL,CAAkB,MAAM;MACpB,KAAKK,OAAL,CAAaO,UAAb,CAAwBJ,YAAY,CAACG,QAArC;;MACAH,YAAY,CAACK,OAAb;IACH,CAHD,EAduC,CAkBvC;IACA;;IACA,IAAIlD,WAAJ,EAAiB;MACb,KAAKwC,eAAL,CAAqBW,YAArB,CAAkC,KAAKC,qBAAL,CAA2BP,YAA3B,CAAlC,EAA4E,KAAKL,eAAL,CAAqBa,UAAjG;IACH,CAFD,MAGK;MACD,KAAKb,eAAL,CAAqBc,WAArB,CAAiC,KAAKF,qBAAL,CAA2BP,YAA3B,CAAjC;IACH;;IACD,OAAOA,YAAP;EACH;EACD;;;EACAO,qBAAqB,CAACP,YAAD,EAAe;IAChC,OAAOA,YAAY,CAACG,QAAb,CAAsBO,SAAtB,CAAgC,CAAhC,CAAP;EACH;;AA1CsC;AA6C3C;;;AACA,IAAIC,gBAAgB,GAAG,MAAMA,gBAAN,CAAuB;EAC1CjG,WAAW,CAACkG,SAAD,EAAY;IACnB,KAAKA,SAAL,GAAiBA,SAAjB;EACH;;EACDC,WAAW,GAAG;IACV,IAAI,KAAKC,iBAAL,IAA0B,KAAKA,iBAAL,CAAuBC,UAArD,EAAiE;MAC7D,KAAKD,iBAAL,CAAuBC,UAAvB,CAAkCC,WAAlC,CAA8C,KAAKF,iBAAnD;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIlG,mBAAmB,GAAG;IAClB,IAAI,CAAC,KAAKkG,iBAAV,EAA6B;MACzB,KAAKG,gBAAL;IACH;;IACD,OAAO,KAAKH,iBAAZ;EACH;EACD;AACJ;AACA;AACA;;;EACIG,gBAAgB,GAAG;IACf,MAAMC,SAAS,GAAG,KAAKN,SAAL,CAAeO,aAAf,CAA6B,KAA7B,CAAlB;;IACAD,SAAS,CAACE,SAAV,CAAoBC,GAApB,CAAwB,mBAAxB;;IACA,KAAKT,SAAL,CAAeU,IAAf,CAAoBb,WAApB,CAAgCS,SAAhC;;IACA,KAAKJ,iBAAL,GAAyBI,SAAzB;EACH;;AA9ByC,CAA9C;;AAgCAP,gBAAgB,CAAC7F,IAAjB,GAAwB,SAASyG,wBAAT,CAAkCvG,CAAlC,EAAqC;EAAE,OAAO,KAAKA,CAAC,IAAI2F,gBAAV,EAA4BxJ,MAAM,CAACpB,QAAP,CAAgBkB,QAAhB,CAA5B,CAAP;AAAgE,CAA/H;;AACA0J,gBAAgB,CAACa,KAAjB,GAAyB,aAAcrK,MAAM,CAACrB,kBAAP,CAA0B;EAAE2L,KAAK,EAAEd,gBAAT;EAA2Be,OAAO,EAAE,UAAU1G,CAAV,EAAa;IAAE,OAAO2F,gBAAgB,CAAC7F,IAAjB,CAAsBE,CAAtB,CAAP;EAAkC,CAArF;EAAuF2G,UAAU,EAAE;AAAnG,CAA1B,CAAvC;;AACAhB,gBAAgB,CAACpF,cAAjB,GAAkC,MAAM,CACpC;EAAEH,IAAI,EAAE4D,SAAR;EAAmB4C,UAAU,EAAE,CAAC;IAAExG,IAAI,EAAEvF,MAAR;IAAgBgM,IAAI,EAAE,CAAC5K,QAAD;EAAtB,CAAD;AAA/B,CADoC,CAAxC;;AAGA0J,gBAAgB,CAACmB,eAAjB,GAAmChM,kBAAkB,CAAC;EAAE4L,OAAO,EAAE,SAASH,wBAAT,GAAoC;IAAE,OAAO,IAAIZ,gBAAJ,CAAqB5K,QAAQ,CAACkB,QAAD,CAA7B,CAAP;EAAkD,CAAnG;EAAqGwK,KAAK,EAAEd,gBAA5G;EAA8HgB,UAAU,EAAE;AAA1I,CAAD,CAArD;AACAhB,gBAAgB,GAAGrL,UAAU,CAAC,CAAEE,OAAO,CAAC,CAAD,EAAIK,MAAM,CAACoB,QAAD,CAAV,CAAT,EAC1B1B,UAAU,CAAC,mBAAD,EAAsB,CAACwM,MAAD,CAAtB,CADgB,CAAD,EAE1BpB,gBAF0B,CAA7B;AAIA;AACA;AACA;AACA;;AACA,MAAMqB,UAAN,CAAiB;EACbtH,WAAW,CAACuH,WAAD,EAAc;IACrB,KAAKA,WAAL,GAAmBA,WAAnB;EACH;;EACDrD,MAAM,CAACQ,MAAD,EAASjC,WAAW,GAAG,IAAvB,EAA6B;IAC/B,OAAO,KAAK8E,WAAL,CAAiBrD,MAAjB,CAAwBQ,MAAxB,EAAgCjC,WAAhC,CAAP;EACH;EACD;AACJ;AACA;AACA;;;EACI4B,MAAM,GAAG;IACL,OAAO,KAAKkD,WAAL,CAAiBlD,MAAjB,EAAP;EACH;;AAbY;AAgBjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAImD,OAAO,GAAG,MAAMA,OAAN,CAAc;EACxBxH,WAAW,CAACyH,iBAAD,EAAoBvC,yBAApB,EAA+CC,OAA/C,EAAwDe,SAAxD,EAAmE;IAC1E,KAAKuB,iBAAL,GAAyBA,iBAAzB;IACA,KAAKvC,yBAAL,GAAiCA,yBAAjC;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKe,SAAL,GAAiBA,SAAjB,CAJ0E,CAK1E;;IACA,KAAKwB,aAAL,GAAqB,IAAIC,GAAJ,EAArB;EACH;EACD;AACJ;AACA;AACA;;;EACIpC,MAAM,CAAC9B,aAAD,EAAgBmE,gBAAhB,EAAkC;IACpC;IACA,OAAO,KAAKC,iBAAL,CAAuB,KAAKC,cAAL,CAAoBrE,aAApB,EAAmCmE,gBAAnC,CAAvB,CAAP;EACH;;EACDE,cAAc,CAACrE,aAAa,GAAG,EAAjB,EAAqBmE,gBAArB,EAAuC;IACjD,IAAI,CAAC,KAAKF,aAAL,CAAmBK,GAAnB,CAAuBH,gBAAvB,CAAL,EAA+C;MAC3C,KAAKF,aAAL,CAAmBM,GAAnB,CAAuBJ,gBAAvB,EAAyC,EAAzC;IACH;;IACD,IAAI,CAAC,KAAKF,aAAL,CAAmBK,GAAnB,CAAuBH,gBAAvB,EAAyCnE,aAAzC,CAAL,EAA8D;MAC1D,KAAKiE,aAAL,CAAmBK,GAAnB,CAAuBH,gBAAvB,EAAyCnE,aAAzC,IAA0D,KAAKwE,kBAAL,CAAwBxE,aAAxB,EAAuCmE,gBAAvC,CAA1D;IACH;;IACD,OAAO,KAAKF,aAAL,CAAmBK,GAAnB,CAAuBH,gBAAvB,EAAyCnE,aAAzC,CAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIwE,kBAAkB,CAACxE,aAAD,EAAgBmE,gBAAhB,EAAkC;IAChD,MAAMM,IAAI,GAAG,KAAKhC,SAAL,CAAeO,aAAf,CAA6B,KAA7B,CAAb;;IACAyB,IAAI,CAACC,EAAL,GAAU,iBAAV;IACAD,IAAI,CAACxB,SAAL,CAAeC,GAAf,CAAmBlD,aAAnB;IACAyE,IAAI,CAACxB,SAAL,CAAeC,GAAf,CAAmB,iBAAnB;;IACA,IAAI,CAACiB,gBAAL,EAAuB;MACnB,KAAKH,iBAAL,CAAuBvH,mBAAvB,GAA6C6F,WAA7C,CAAyDmC,IAAzD;IACH,CAFD,MAGK;MACDN,gBAAgB,CAAC1H,mBAAjB,GAAuC6F,WAAvC,CAAmDmC,IAAnD;IACH;;IACD,OAAOA,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIE,iBAAiB,CAACF,IAAD,EAAO;IACpB,OAAO,IAAIlD,aAAJ,CAAkBkD,IAAlB,EAAwB,KAAKhD,yBAA7B,EAAwD,KAAKC,OAA7D,CAAP;EACH;EACD;AACJ;AACA;AACA;;;EACI0C,iBAAiB,CAACK,IAAD,EAAO;IACpB,OAAO,IAAIZ,UAAJ,CAAe,KAAKc,iBAAL,CAAuBF,IAAvB,CAAf,CAAP;EACH;;AAzDuB,CAA5B;;AA2DAV,OAAO,CAACpH,IAAR,GAAe,SAASiI,eAAT,CAAyB/H,CAAzB,EAA4B;EAAE,OAAO,KAAKA,CAAC,IAAIkH,OAAV,EAAmB/K,MAAM,CAACpB,QAAP,CAAgB4K,gBAAhB,CAAnB,EAAsDxJ,MAAM,CAACpB,QAAP,CAAgBoB,MAAM,CAAClB,wBAAvB,CAAtD,EAAwGkB,MAAM,CAACpB,QAAP,CAAgBoB,MAAM,CAACjB,cAAvB,CAAxG,EAAgJiB,MAAM,CAACpB,QAAP,CAAgBkB,QAAhB,CAAhJ,CAAP;AAAoL,CAAjO;;AACAiL,OAAO,CAACV,KAAR,GAAgB,aAAcrK,MAAM,CAACrB,kBAAP,CAA0B;EAAE2L,KAAK,EAAES,OAAT;EAAkBR,OAAO,EAAE,UAAU1G,CAAV,EAAa;IAAE,OAAOkH,OAAO,CAACpH,IAAR,CAAaE,CAAb,CAAP;EAAyB,CAAnE;EAAqE2G,UAAU,EAAE;AAAjF,CAA1B,CAA9B;;AACAO,OAAO,CAAC3G,cAAR,GAAyB,MAAM,CAC3B;EAAEH,IAAI,EAAEuF;AAAR,CAD2B,EAE3B;EAAEvF,IAAI,EAAEnF;AAAR,CAF2B,EAG3B;EAAEmF,IAAI,EAAElF;AAAR,CAH2B,EAI3B;EAAEkF,IAAI,EAAE4D,SAAR;EAAmB4C,UAAU,EAAE,CAAC;IAAExG,IAAI,EAAEvF,MAAR;IAAgBgM,IAAI,EAAE,CAAC5K,QAAD;EAAtB,CAAD;AAA/B,CAJ2B,CAA/B;;AAMAiL,OAAO,CAACJ,eAAR,GAA0BhM,kBAAkB,CAAC;EAAE4L,OAAO,EAAE,SAASqB,eAAT,GAA2B;IAAE,OAAO,IAAIb,OAAJ,CAAYnM,QAAQ,CAAC4K,gBAAD,CAApB,EAAwC5K,QAAQ,CAACE,wBAAD,CAAhD,EAA4EF,QAAQ,CAACG,cAAD,CAApF,EAAsGH,QAAQ,CAACkB,QAAD,CAA9G,CAAP;EAAmI,CAA3K;EAA6KwK,KAAK,EAAES,OAApL;EAA6LP,UAAU,EAAE;AAAzM,CAAD,CAA5C;AACAO,OAAO,GAAG5M,UAAU,CAAC,CAAEE,OAAO,CAAC,CAAD,EAAIK,MAAM,CAACoB,QAAD,CAAV,CAAT,EACjB1B,UAAU,CAAC,mBAAD,EAAsB,CAACoL,gBAAD,EAC5B1K,wBAD4B,EAE5BC,cAF4B,EAEZ6L,MAFY,CAAtB,CADO,CAAD,EAIjBG,OAJiB,CAApB;AAMA;AACA;AACA;;AACA,MAAMc,QAAN,CAAe;EACXtI,WAAW,CAACuI,WAAD,EAAc;IACrB,KAAKA,WAAL,GAAmBA,WAAnB;IACA;;IACA,KAAKrK,eAAL,GAAuB,CAAvB;IACA;;IACA,KAAKsK,YAAL,GAAoB,IAAInM,OAAJ,EAApB;IACA;;IACA,KAAKoM,SAAL,GAAiB,IAAIpM,OAAJ,EAAjB;IACA;;IACA,KAAKqM,YAAL,GAAoB,IAAIrM,OAAJ,EAApB;IACA;;IACA,KAAKsM,aAAL,GAAqB,IAAItM,OAAJ,EAArB;IACA;;IACA,KAAKuM,eAAL,GAAuB,IAAIvM,OAAJ,EAAvB;EACH;;EACDwM,WAAW,GAAG;IACV,KAAKH,YAAL,CAAkB3G,IAAlB;;IACA,KAAK2G,YAAL,CAAkB7G,QAAlB;EACH;;EACDiH,YAAY,GAAG;IACX,OAAO,KAAKJ,YAAL,CAAkBxG,YAAlB,EAAP;EACH;;EACD6G,YAAY,GAAG;IACX,OAAO,KAAKJ,aAAL,CAAmBzG,YAAnB,EAAP;EACH;;EACD8G,cAAc,GAAG;IACb,OAAO,KAAKJ,eAAL,CAAqB1G,YAArB,EAAP;EACH;EACD;AACJ;AACA;;;EACI+G,KAAK,GAAG;IACJ,KAAKV,WAAL,CAAiBlE,MAAjB;;IACA,KAAKmE,YAAL,CAAkBzG,IAAlB;;IACA,KAAK2G,YAAL,CAAkB3G,IAAlB;;IACA,KAAKyG,YAAL,CAAkB3G,QAAlB;;IACA,KAAK6G,YAAL,CAAkB7G,QAAlB;;IACA,KAAK4G,SAAL,CAAe5G,QAAf;;IACA,KAAK8G,aAAL,CAAmB9G,QAAnB;;IACA,KAAK+G,eAAL,CAAqB/G,QAArB;EACH;EACD;;;EACAF,WAAW,GAAG;IACV,OAAO,KAAK6G,YAAL,CAAkBtG,YAAlB,EAAP;EACH;;EACDgH,UAAU,GAAG;IACT,OAAO,KAAKT,SAAL,CAAeU,SAAtB;EACH;;EACDC,QAAQ,GAAG;IACP,KAAKX,SAAL,CAAe1G,IAAf;;IACA,KAAK0G,SAAL,CAAe5G,QAAf;EACH;EACD;;;EACAwH,aAAa,GAAG;IACZ,OAAO,KAAKZ,SAAL,CAAevG,YAAf,EAAP;EACH;EACD;;;EACAoH,WAAW,CAACC,YAAD,EAAeP,cAAf,EAA+B;IACtC,IAAIO,YAAJ,EAAkB;MACd,KAAKZ,aAAL,CAAmB5G,IAAnB;IACH;;IACD,IAAIiH,cAAJ,EAAoB;MAChB,KAAKJ,eAAL,CAAqB7G,IAArB,CAA0B,EAAE,KAAK7D,eAAjC;IACH;EACJ;;AAjEU;AAmEf;;;AACA,MAAMsL,aAAN,CAAoB;EAChBxJ,WAAW,CAACyJ,aAAD,EAAgBC,eAAhB,EAAiC;IACxC,KAAKD,aAAL,GAAqBA,aAArB;IACA,KAAKC,eAAL,GAAuBA,eAAvB;EACH;;EACD3B,GAAG,CAAChB,KAAD,EAAQ4C,aAAR,EAAuBC,KAAvB,EAA8B;IAC7B,IAAI7C,KAAK,KAAK3F,YAAd,EAA4B;MACxB,OAAO,KAAKqI,aAAZ;IACH;;IACD,OAAO,KAAKC,eAAL,CAAqB3B,GAArB,CAAyBhB,KAAzB,EAAgC4C,aAAhC,EAA+CC,KAA/C,CAAP;EACH;;AAVe;;AAapB,IAAIC,aAAa,GAAG,MAAMA,aAAN,CAAoB;EACpC7J,WAAW,CAAC+G,KAAD,EAAQ+C,OAAR,EAAiBC,SAAjB,EAA4BC,SAA5B,EAAuCC,MAAvC,EAA+C;IACtD,KAAKH,OAAL,GAAeA,OAAf;IACA,KAAKC,SAAL,GAAiBA,SAAjB;IACA,KAAKC,SAAL,GAAiBA,SAAjB;IACA,KAAKC,MAAL,GAAcA,MAAd;IACA,KAAKC,eAAL,GAAuB,CAAvB;IACA,KAAKC,MAAL,GAAc,EAAd;IACA,KAAKC,KAAL,GAAa,CAAb;IACA,KAAKC,YAAL,GAAoBhD,MAAM,CAACiD,MAAP,CAAc,EAAd,EAAkBvD,KAAK,CAACwD,OAAxB,EAAiCxD,KAAK,CAACzF,MAAvC,CAApB;;IACA,IAAIyF,KAAK,CAACzF,MAAN,CAAauB,WAAjB,EAA8B;MAC1B,KAAKwH,YAAL,CAAkBxH,WAAlB,GAAgCwE,MAAM,CAACiD,MAAP,CAAc,EAAd,EAAkBvD,KAAK,CAACwD,OAAN,CAAc1H,WAAhC,EAA6CkE,KAAK,CAACzF,MAAN,CAAauB,WAA1D,CAAhC;IACH;EACJ;EACD;;;EACA2H,IAAI,CAACxL,OAAD,EAAUN,KAAV,EAAiB+L,QAAQ,GAAG,EAA5B,EAAgC/J,IAAI,GAAG,EAAvC,EAA2C;IAC3C,OAAO,KAAKgK,qBAAL,CAA2BhK,IAA3B,EAAiC1B,OAAjC,EAA0CN,KAA1C,EAAiD,KAAKiM,WAAL,CAAiBF,QAAjB,CAAjD,CAAP;EACH;EACD;;;EACAzH,OAAO,CAAChE,OAAD,EAAUN,KAAV,EAAiB+L,QAAQ,GAAG,EAA5B,EAAgC;IACnC,MAAM/J,IAAI,GAAG,KAAK2J,YAAL,CAAkBxH,WAAlB,CAA8BG,OAA9B,IAAyC,EAAtD;IACA,OAAO,KAAK0H,qBAAL,CAA2BhK,IAA3B,EAAiC1B,OAAjC,EAA0CN,KAA1C,EAAiD,KAAKiM,WAAL,CAAiBF,QAAjB,CAAjD,CAAP;EACH;EACD;;;EACA3H,KAAK,CAAC9D,OAAD,EAAUN,KAAV,EAAiB+L,QAAQ,GAAG,EAA5B,EAAgC;IACjC,MAAM/J,IAAI,GAAG,KAAK2J,YAAL,CAAkBxH,WAAlB,CAA8BC,KAA9B,IAAuC,EAApD;IACA,OAAO,KAAK4H,qBAAL,CAA2BhK,IAA3B,EAAiC1B,OAAjC,EAA0CN,KAA1C,EAAiD,KAAKiM,WAAL,CAAiBF,QAAjB,CAAjD,CAAP;EACH;EACD;;;EACA1H,IAAI,CAAC/D,OAAD,EAAUN,KAAV,EAAiB+L,QAAQ,GAAG,EAA5B,EAAgC;IAChC,MAAM/J,IAAI,GAAG,KAAK2J,YAAL,CAAkBxH,WAAlB,CAA8BE,IAA9B,IAAsC,EAAnD;IACA,OAAO,KAAK2H,qBAAL,CAA2BhK,IAA3B,EAAiC1B,OAAjC,EAA0CN,KAA1C,EAAiD,KAAKiM,WAAL,CAAiBF,QAAjB,CAAjD,CAAP;EACH;EACD;;;EACAxH,OAAO,CAACjE,OAAD,EAAUN,KAAV,EAAiB+L,QAAQ,GAAG,EAA5B,EAAgC;IACnC,MAAM/J,IAAI,GAAG,KAAK2J,YAAL,CAAkBxH,WAAlB,CAA8BI,OAA9B,IAAyC,EAAtD;IACA,OAAO,KAAKyH,qBAAL,CAA2BhK,IAA3B,EAAiC1B,OAAjC,EAA0CN,KAA1C,EAAiD,KAAKiM,WAAL,CAAiBF,QAAjB,CAAjD,CAAP;EACH;EACD;AACJ;AACA;;;EACIG,KAAK,CAACvJ,OAAD,EAAU;IACX;IACA,KAAK,MAAMwJ,KAAX,IAAoB,KAAKV,MAAzB,EAAiC;MAC7B,IAAI9I,OAAO,KAAKiD,SAAhB,EAA2B;QACvB,IAAIuG,KAAK,CAACxJ,OAAN,KAAkBA,OAAtB,EAA+B;UAC3BwJ,KAAK,CAACrJ,QAAN,CAAeqH,WAAf;UACA;QACH;MACJ,CALD,MAMK;QACDgC,KAAK,CAACrJ,QAAN,CAAeqH,WAAf;MACH;IACJ;EACJ;EACD;AACJ;AACA;;;EACIpL,MAAM,CAAC4D,OAAD,EAAU;IACZ,MAAMyJ,KAAK,GAAG,KAAKC,UAAL,CAAgB1J,OAAhB,CAAd;;IACA,IAAI,CAACyJ,KAAL,EAAY;MACR,OAAO,KAAP;IACH;;IACDA,KAAK,CAACE,WAAN,CAAkBxJ,QAAlB,CAA2ByH,KAA3B;IACA,KAAKkB,MAAL,CAAYc,MAAZ,CAAmBH,KAAK,CAACV,KAAzB,EAAgC,CAAhC;IACA,KAAKF,eAAL,GAAuB,KAAKA,eAAL,GAAuB,CAA9C;;IACA,IAAI,CAAC,KAAKG,YAAL,CAAkB9H,SAAnB,IAAgC,CAAC,KAAK4H,MAAL,CAAYe,MAAjD,EAAyD;MACrD,OAAO,KAAP;IACH;;IACD,IAAI,KAAKhB,eAAL,GAAuB,KAAKG,YAAL,CAAkB9H,SAAzC,IACA,KAAK4H,MAAL,CAAY,KAAKD,eAAjB,CADJ,EACuC;MACnC,MAAMiB,CAAC,GAAG,KAAKhB,MAAL,CAAY,KAAKD,eAAjB,EAAkC1I,QAA5C;;MACA,IAAI,CAAC2J,CAAC,CAACjC,UAAF,EAAL,EAAqB;QACjB,KAAKgB,eAAL,GAAuB,KAAKA,eAAL,GAAuB,CAA9C;QACAiB,CAAC,CAAC/B,QAAF;MACH;IACJ;;IACD,OAAO,IAAP;EACH;EACD;AACJ;AACA;;;EACIgC,aAAa,CAACpM,OAAD,EAAUqM,gBAAV,EAA4B1I,eAA5B,EAA6C;IACtD,KAAK,MAAMkI,KAAX,IAAoB,KAAKV,MAAzB,EAAiC;MAC7B,IAAIU,KAAK,CAAC7L,OAAN,KAAkBA,OAAtB,EAA+B;QAC3B6L,KAAK,CAACrJ,QAAN,CAAe8H,WAAf,CAA2B+B,gBAA3B,EAA6C1I,eAA7C;QACA,OAAOkI,KAAP;MACH;IACJ;;IACD,OAAO,IAAP;EACH;EACD;;;EACAF,WAAW,CAACF,QAAQ,GAAG,EAAZ,EAAgB;IACvB,OAAOpD,MAAM,CAACiD,MAAP,CAAc,EAAd,EAAkB,KAAKD,YAAvB,EAAqCI,QAArC,CAAP;EACH;EACD;AACJ;AACA;;;EACIM,UAAU,CAAC1J,OAAD,EAAU;IAChB,KAAK,IAAIiK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKnB,MAAL,CAAYe,MAAhC,EAAwCI,CAAC,EAAzC,EAA6C;MACzC,IAAI,KAAKnB,MAAL,CAAYmB,CAAZ,EAAejK,OAAf,KAA2BA,OAA/B,EAAwC;QACpC,OAAO;UAAE+I,KAAK,EAAEkB,CAAT;UAAYN,WAAW,EAAE,KAAKb,MAAL,CAAYmB,CAAZ;QAAzB,CAAP;MACH;IACJ;;IACD,OAAO,IAAP;EACH;EACD;AACJ;AACA;;;EACIZ,qBAAqB,CAACnJ,SAAD,EAAYvC,OAAZ,EAAqBN,KAArB,EAA4B4C,MAA5B,EAAoC;IACrD,IAAIA,MAAM,CAACsC,cAAX,EAA2B;MACvB,OAAO,KAAKqG,MAAL,CAAYsB,GAAZ,CAAgB,MAAM,KAAKC,kBAAL,CAAwBjK,SAAxB,EAAmCvC,OAAnC,EAA4CN,KAA5C,EAAmD4C,MAAnD,CAAtB,CAAP;IACH;;IACD,OAAO,KAAKkK,kBAAL,CAAwBjK,SAAxB,EAAmCvC,OAAnC,EAA4CN,KAA5C,EAAmD4C,MAAnD,CAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIkK,kBAAkB,CAACjK,SAAD,EAAYvC,OAAZ,EAAqBN,KAArB,EAA4B4C,MAA5B,EAAoC;IAClD,IAAI,CAACA,MAAM,CAACmK,cAAZ,EAA4B;MACxB,MAAM,IAAIC,KAAJ,CAAU,yBAAV,CAAN;IACH,CAHiD,CAIlD;IACA;IACA;;;IACA,MAAMC,SAAS,GAAG,KAAKP,aAAL,CAAmBpM,OAAnB,EAA4B,KAAKqL,YAAL,CAAkBzH,uBAAlB,IAA6CtB,MAAM,CAAC8B,OAAP,GAAiB,CAA1F,EAA6F,KAAKiH,YAAL,CAAkB1H,eAA/G,CAAlB;;IACA,IAAI3D,OAAO,IAAI,KAAKqL,YAAL,CAAkB3H,iBAA7B,IAAkDiJ,SAAS,KAAK,IAApE,EAA0E;MACtE,OAAOA,SAAP;IACH;;IACD,KAAKC,oBAAL,GAA4B5M,OAA5B;IACA,IAAI6M,YAAY,GAAG,KAAnB;;IACA,IAAI,KAAKxB,YAAL,CAAkB9H,SAAlB,IACA,KAAK2H,eAAL,IAAwB,KAAKG,YAAL,CAAkB9H,SAD9C,EACyD;MACrDsJ,YAAY,GAAG,IAAf;;MACA,IAAI,KAAKxB,YAAL,CAAkB7H,WAAtB,EAAmC;QAC/B,KAAKoI,KAAL,CAAW,KAAKT,MAAL,CAAY,CAAZ,EAAe9I,OAA1B;MACH;IACJ;;IACD,MAAMyK,UAAU,GAAG,KAAKhC,OAAL,CAAavE,MAAb,CAAoBjE,MAAM,CAACmC,aAA3B,EAA0C,KAAKmE,gBAA/C,CAAnB;IACA,KAAKwC,KAAL,GAAa,KAAKA,KAAL,GAAa,CAA1B;IACA,IAAI2B,gBAAgB,GAAG/M,OAAvB;;IACA,IAAIA,OAAO,IAAIsC,MAAM,CAACgC,UAAtB,EAAkC;MAC9ByI,gBAAgB,GAAG,KAAK/B,SAAL,CAAegC,QAAf,CAAwBvQ,eAAe,CAACwQ,IAAxC,EAA8CjN,OAA9C,CAAnB;IACH;;IACD,MAAMwC,QAAQ,GAAG,IAAI8G,QAAJ,CAAawD,UAAb,CAAjB;IACA,MAAMI,YAAY,GAAG,IAAI9K,YAAJ,CAAiB,KAAKgJ,KAAtB,EAA6B9I,MAA7B,EAAqCyK,gBAArC,EAAuDrN,KAAvD,EAA8D6C,SAA9D,EAAyEC,QAAzE,CAArB;IACA,MAAM2K,aAAa,GAAG,IAAI3C,aAAJ,CAAkB0C,YAAlB,EAAgC,KAAKnC,SAArC,CAAtB;IACA,MAAM/F,SAAS,GAAG,IAAID,eAAJ,CAAoBzC,MAAM,CAACmK,cAA3B,EAA2CU,aAA3C,CAAlB;IACA,MAAMzH,MAAM,GAAGoH,UAAU,CAAC5H,MAAX,CAAkBF,SAAlB,EAA6B,KAAKqG,YAAL,CAAkB5H,WAA/C,CAAf;IACAjB,QAAQ,CAAC4K,iBAAT,GAA6B1H,MAAM,CAAC2H,UAApC;IACA,MAAMC,GAAG,GAAG;MACRjL,OAAO,EAAE,KAAK+I,KADN;MAERpL,OAAO,EAAEA,OAAO,IAAI,EAFZ;MAGRwC,QAHQ;MAIR+K,OAAO,EAAE/K,QAAQ,CAAC6H,aAAT,EAJD;MAKRmD,QAAQ,EAAEhL,QAAQ,CAACG,WAAT,EALF;MAMRM,KAAK,EAAEiK,YAAY,CAACjK,KAAb,EANC;MAORI,QAAQ,EAAE6J,YAAY,CAAC7J,QAAb,EAPF;MAQRqC;IARQ,CAAZ;;IAUA,IAAI,CAACmH,YAAL,EAAmB;MACf,KAAK3B,eAAL,GAAuB,KAAKA,eAAL,GAAuB,CAA9C;MACAuC,UAAU,CAAC,MAAM;QACbH,GAAG,CAAC9K,QAAJ,CAAa4H,QAAb;MACH,CAFS,CAAV;IAGH;;IACD,KAAKe,MAAL,CAAYuC,IAAZ,CAAiBJ,GAAjB;IACA,OAAOA,GAAP;EACH;;AAzKmC,CAAxC;;AA2KAzC,aAAa,CAACzJ,IAAd,GAAqB,SAASuM,qBAAT,CAA+BrM,CAA/B,EAAkC;EAAE,OAAO,KAAKA,CAAC,IAAIuJ,aAAV,EAAyBpN,MAAM,CAACpB,QAAP,CAAgByI,YAAhB,CAAzB,EAAwDrH,MAAM,CAACpB,QAAP,CAAgBmM,OAAhB,CAAxD,EAAkF/K,MAAM,CAACpB,QAAP,CAAgBoB,MAAM,CAACf,QAAvB,CAAlF,EAAoHe,MAAM,CAACpB,QAAP,CAAgBqB,MAAM,CAACJ,YAAvB,CAApH,EAA0JG,MAAM,CAACpB,QAAP,CAAgBoB,MAAM,CAACd,MAAvB,CAA1J,CAAP;AAAmM,CAA5P;;AACAkO,aAAa,CAAC/C,KAAd,GAAsB,aAAcrK,MAAM,CAACrB,kBAAP,CAA0B;EAAE2L,KAAK,EAAE8C,aAAT;EAAwB7C,OAAO,EAAE,UAAU1G,CAAV,EAAa;IAAE,OAAOuJ,aAAa,CAACzJ,IAAd,CAAmBE,CAAnB,CAAP;EAA+B,CAA/E;EAAiF2G,UAAU,EAAE;AAA7F,CAA1B,CAApC;;AACA4C,aAAa,CAAChJ,cAAd,GAA+B,MAAM,CACjC;EAAEH,IAAI,EAAE4D,SAAR;EAAmB4C,UAAU,EAAE,CAAC;IAAExG,IAAI,EAAEvF,MAAR;IAAgBgM,IAAI,EAAE,CAACrD,YAAD;EAAtB,CAAD;AAA/B,CADiC,EAEjC;EAAEpD,IAAI,EAAE8G;AAAR,CAFiC,EAGjC;EAAE9G,IAAI,EAAEhF;AAAR,CAHiC,EAIjC;EAAEgF,IAAI,EAAEpE;AAAR,CAJiC,EAKjC;EAAEoE,IAAI,EAAE/E;AAAR,CALiC,CAArC;;AAOAkO,aAAa,CAACzC,eAAd,GAAgChM,kBAAkB,CAAC;EAAE4L,OAAO,EAAE,SAAS2F,qBAAT,GAAiC;IAAE,OAAO,IAAI9C,aAAJ,CAAkBxO,QAAQ,CAACyI,YAAD,CAA1B,EAA0CzI,QAAQ,CAACmM,OAAD,CAAlD,EAA6DnM,QAAQ,CAACO,QAAD,CAArE,EAAiFP,QAAQ,CAACiB,YAAD,CAAzF,EAAyGjB,QAAQ,CAACM,MAAD,CAAjH,CAAP;EAAoI,CAAlL;EAAoLoL,KAAK,EAAE8C,aAA3L;EAA0M5C,UAAU,EAAE;AAAtN,CAAD,CAAlD;AACA4C,aAAa,GAAGjP,UAAU,CAAC,CAAEE,OAAO,CAAC,CAAD,EAAIK,MAAM,CAAC2I,YAAD,CAAV,CAAT,EACvBjJ,UAAU,CAAC,mBAAD,EAAsB,CAACwM,MAAD,EAASG,OAAT,EAC5B9L,QAD4B,EAE5BY,YAF4B,EAG5BX,MAH4B,CAAtB,CADa,CAAD,EAKvBkO,aALuB,CAA1B;AAOA,IAAI+C,KAAK,GAAG,MAAMA,KAAN,CAAY;EACpB5M,WAAW,CAAC6M,aAAD,EAAgBX,YAAhB,EAA8BjC,MAA9B,EAAsC;IAC7C,KAAK4C,aAAL,GAAqBA,aAArB;IACA,KAAKX,YAAL,GAAoBA,YAApB;IACA,KAAKjC,MAAL,GAAcA,MAAd;IACA;;IACA,KAAK1K,KAAL,GAAa,CAAC,CAAd;IACA;;IACA,KAAKuN,YAAL,GAAoB,EAApB;IACA;;IACA,KAAK7Q,KAAL,GAAa;MACT8Q,KAAK,EAAE,UADE;MAETC,MAAM,EAAE;QACJrJ,QAAQ,EAAE,KAAKuI,YAAL,CAAkB5K,MAAlB,CAAyBqC,QAD/B;QAEJD,MAAM,EAAE;MAFJ;IAFC,CAAb;IAOA,KAAK1E,OAAL,GAAekN,YAAY,CAAClN,OAA5B;IACA,KAAKN,KAAL,GAAawN,YAAY,CAACxN,KAA1B;IACA,KAAKH,OAAL,GAAe2N,YAAY,CAAC5K,MAA5B;IACA,KAAK2L,eAAL,GAAuBf,YAAY,CAAC5K,MAAb,CAAoB8B,OAA3C;IACA,KAAK0J,YAAL,GAAqB,GAAEZ,YAAY,CAAC3K,SAAU,IAAG2K,YAAY,CAAC5K,MAAb,CAAoBkC,UAAW,EAAhF;IACA,KAAK0J,GAAL,GAAWhB,YAAY,CAAC1K,QAAb,CAAsB6H,aAAtB,GAAsCzH,SAAtC,CAAgD,MAAM;MAC7D,KAAKuL,aAAL;IACH,CAFU,CAAX;IAGA,KAAKC,IAAL,GAAYlB,YAAY,CAAC1K,QAAb,CAAsBsH,YAAtB,GAAqClH,SAArC,CAA+C,MAAM;MAC7D,KAAKnE,MAAL;IACH,CAFW,CAAZ;IAGA,KAAK4P,IAAL,GAAYnB,YAAY,CAAC1K,QAAb,CAAsBuH,YAAtB,GAAqCnH,SAArC,CAA+C,MAAM;MAC7D,KAAK2H,YAAL;IACH,CAFW,CAAZ;IAGA,KAAK+D,IAAL,GAAYpB,YAAY,CAAC1K,QAAb,CAAsBwH,cAAtB,GAAuCpH,SAAvC,CAAiD2L,KAAK,IAAI;MAClE,KAAKrP,eAAL,GAAuBqP,KAAvB;IACH,CAFW,CAAZ;EAGH;EACD;;;EACgB,IAAZC,YAAY,GAAG;IACf,IAAI,KAAKvR,KAAL,CAAW8Q,KAAX,KAAqB,UAAzB,EAAqC;MACjC,OAAO,MAAP;IACH;EACJ;;EACD5G,WAAW,GAAG;IACV,KAAK+G,GAAL,CAASO,WAAT;IACA,KAAKL,IAAL,CAAUK,WAAV;IACA,KAAKJ,IAAL,CAAUI,WAAV;IACA,KAAKH,IAAL,CAAUG,WAAV;IACAC,aAAa,CAAC,KAAKC,UAAN,CAAb;IACAC,YAAY,CAAC,KAAKC,OAAN,CAAZ;EACH;EACD;AACJ;AACA;;;EACIV,aAAa,GAAG;IACZ,KAAKlR,KAAL,GAAaoL,MAAM,CAACiD,MAAP,CAAc,EAAd,EAAkB,KAAKrO,KAAvB,EAA8B;MAAE8Q,KAAK,EAAE;IAAT,CAA9B,CAAb;;IACA,IAAI,EAAE,KAAKxO,OAAL,CAAa4E,cAAb,KAAgC,IAAhC,IAAwC,KAAK5E,OAAL,CAAa4E,cAAb,KAAgC,SAA1E,KAAwF,KAAK5E,OAAL,CAAa6E,OAAzG,EAAkH;MAC9G,KAAK0K,cAAL,CAAoB,MAAM,KAAKrQ,MAAL,EAA1B,EAAyC,KAAKc,OAAL,CAAa6E,OAAtD;MACA,KAAK2K,QAAL,GAAgB,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,KAAK1P,OAAL,CAAa6E,OAApD;;MACA,IAAI,KAAK7E,OAAL,CAAagF,WAAjB,EAA8B;QAC1B,KAAK2K,eAAL,CAAqB,MAAM,KAAKC,cAAL,EAA3B,EAAkD,EAAlD;MACH;IACJ;EACJ;EACD;AACJ;AACA;;;EACIA,cAAc,GAAG;IACb,IAAI,KAAK5O,KAAL,KAAe,CAAf,IAAoB,KAAKA,KAAL,KAAe,GAAnC,IAA0C,CAAC,KAAKhB,OAAL,CAAa6E,OAA5D,EAAqE;MACjE;IACH;;IACD,MAAMgL,GAAG,GAAG,IAAIJ,IAAJ,GAAWC,OAAX,EAAZ;IACA,MAAMI,SAAS,GAAG,KAAKN,QAAL,GAAgBK,GAAlC;IACA,KAAK7O,KAAL,GAAc8O,SAAS,GAAG,KAAK9P,OAAL,CAAa6E,OAA1B,GAAqC,GAAlD;;IACA,IAAI,KAAK7E,OAAL,CAAasF,iBAAb,KAAmC,YAAvC,EAAqD;MACjD,KAAKtE,KAAL,GAAa,MAAM,KAAKA,KAAxB;IACH;;IACD,IAAI,KAAKA,KAAL,IAAc,CAAlB,EAAqB;MACjB,KAAKA,KAAL,GAAa,CAAb;IACH;;IACD,IAAI,KAAKA,KAAL,IAAc,GAAlB,EAAuB;MACnB,KAAKA,KAAL,GAAa,GAAb;IACH;EACJ;;EACDgK,YAAY,GAAG;IACXqE,YAAY,CAAC,KAAKC,OAAN,CAAZ;IACAH,aAAa,CAAC,KAAKC,UAAN,CAAb;IACA,KAAK1R,KAAL,GAAaoL,MAAM,CAACiD,MAAP,CAAc,EAAd,EAAkB,KAAKrO,KAAvB,EAA8B;MAAE8Q,KAAK,EAAE;IAAT,CAA9B,CAAb;IACA,KAAKe,cAAL,CAAoB,MAAM,KAAKrQ,MAAL,EAA1B,EAAyC,KAAKwP,eAA9C;IACA,KAAK1O,OAAL,CAAa6E,OAAb,GAAuB,KAAK6J,eAA5B;IACA,KAAKc,QAAL,GAAgB,IAAIC,IAAJ,GAAWC,OAAX,MAAwB,KAAK1P,OAAL,CAAa6E,OAAb,IAAwB,CAAhD,CAAhB;IACA,KAAK7D,KAAL,GAAa,CAAC,CAAd;;IACA,IAAI,KAAKhB,OAAL,CAAagF,WAAjB,EAA8B;MAC1B,KAAK2K,eAAL,CAAqB,MAAM,KAAKC,cAAL,EAA3B,EAAkD,EAAlD;IACH;EACJ;EACD;AACJ;AACA;;;EACI1Q,MAAM,GAAG;IACL,IAAI,KAAKxB,KAAL,CAAW8Q,KAAX,KAAqB,SAAzB,EAAoC;MAChC;IACH;;IACDa,YAAY,CAAC,KAAKC,OAAN,CAAZ;IACA,KAAK5R,KAAL,GAAaoL,MAAM,CAACiD,MAAP,CAAc,EAAd,EAAkB,KAAKrO,KAAvB,EAA8B;MAAE8Q,KAAK,EAAE;IAAT,CAA9B,CAAb;IACA,KAAKe,cAAL,CAAoB,MAAM,KAAKjB,aAAL,CAAmBpP,MAAnB,CAA0B,KAAKyO,YAAL,CAAkB7K,OAA5C,CAA1B,EAAgF,CAAC,KAAK6K,YAAL,CAAkB5K,MAAlB,CAAyBqC,QAA1G;EACH;;EACD2K,QAAQ,GAAG;IACP,IAAI,KAAKrS,KAAL,CAAW8Q,KAAX,KAAqB,SAAzB,EAAoC;MAChC;IACH;;IACD,KAAKb,YAAL,CAAkBpK,UAAlB;;IACA,IAAI,KAAKvD,OAAL,CAAayD,YAAjB,EAA+B;MAC3B,KAAKvE,MAAL;IACH;EACJ;;EACD8Q,WAAW,GAAG;IACV,IAAI,KAAKtS,KAAL,CAAW8Q,KAAX,KAAqB,SAAzB,EAAoC;MAChC;IACH;;IACDa,YAAY,CAAC,KAAKC,OAAN,CAAZ;IACA,KAAKtP,OAAL,CAAa6E,OAAb,GAAuB,CAAvB;IACA,KAAK2K,QAAL,GAAgB,CAAhB,CANU,CAOV;;IACAL,aAAa,CAAC,KAAKC,UAAN,CAAb;IACA,KAAKpO,KAAL,GAAa,CAAb;EACH;;EACDiP,gBAAgB,GAAG;IACf,IAAK,KAAKjQ,OAAL,CAAa4E,cAAb,KAAgC,IAAhC,IAAwC,KAAK5E,OAAL,CAAa4E,cAAb,KAAgC,iBAAzE,IACA,KAAK5E,OAAL,CAAa8E,eAAb,KAAiC,CADjC,IAEA,KAAKpH,KAAL,CAAW8Q,KAAX,KAAqB,SAFzB,EAEoC;MAChC;IACH;;IACD,KAAKe,cAAL,CAAoB,MAAM,KAAKrQ,MAAL,EAA1B,EAAyC,KAAKc,OAAL,CAAa8E,eAAtD;IACA,KAAK9E,OAAL,CAAa6E,OAAb,GAAuB,KAAK7E,OAAL,CAAa8E,eAApC;IACA,KAAK0K,QAAL,GAAgB,IAAIC,IAAJ,GAAWC,OAAX,MAAwB,KAAK1P,OAAL,CAAa6E,OAAb,IAAwB,CAAhD,CAAhB;IACA,KAAK7D,KAAL,GAAa,CAAC,CAAd;;IACA,IAAI,KAAKhB,OAAL,CAAagF,WAAjB,EAA8B;MAC1B,KAAK2K,eAAL,CAAqB,MAAM,KAAKC,cAAL,EAA3B,EAAkD,EAAlD;IACH;EACJ;;EACDL,cAAc,CAACW,IAAD,EAAOZ,OAAP,EAAgB;IAC1B,IAAI,KAAK5D,MAAT,EAAiB;MACb,KAAKA,MAAL,CAAYyE,iBAAZ,CAA8B,MAAO,KAAKb,OAAL,GAAepB,UAAU,CAAC,MAAM,KAAKkC,gBAAL,CAAsBF,IAAtB,CAAP,EAAoCZ,OAApC,CAA9D;IACH,CAFD,MAGK;MACD,KAAKA,OAAL,GAAepB,UAAU,CAAC,MAAMgC,IAAI,EAAX,EAAeZ,OAAf,CAAzB;IACH;EACJ;;EACDK,eAAe,CAACO,IAAD,EAAOZ,OAAP,EAAgB;IAC3B,IAAI,KAAK5D,MAAT,EAAiB;MACb,KAAKA,MAAL,CAAYyE,iBAAZ,CAA8B,MAAO,KAAKf,UAAL,GAAkBiB,WAAW,CAAC,MAAM,KAAKD,gBAAL,CAAsBF,IAAtB,CAAP,EAAoCZ,OAApC,CAAlE;IACH,CAFD,MAGK;MACD,KAAKF,UAAL,GAAkBiB,WAAW,CAAC,MAAMH,IAAI,EAAX,EAAeZ,OAAf,CAA7B;IACH;EACJ;;EACDc,gBAAgB,CAACF,IAAD,EAAO;IACnB,IAAI,KAAKxE,MAAT,EAAiB;MACb,KAAKA,MAAL,CAAYsB,GAAZ,CAAgB,MAAMkD,IAAI,EAA1B;IACH,CAFD,MAGK;MACDA,IAAI;IACP;EACJ;;AAlKmB,CAAxB;;AAoKA7B,KAAK,CAACxM,IAAN,GAAa,SAASyO,aAAT,CAAuBvO,CAAvB,EAA0B;EAAE,OAAO,KAAKA,CAAC,IAAIsM,KAAV,EAAiBnQ,MAAM,CAAC8D,iBAAP,CAAyBsJ,aAAzB,CAAjB,EAA0DpN,MAAM,CAAC8D,iBAAP,CAAyBa,YAAzB,CAA1D,EAAkG3E,MAAM,CAAC8D,iBAAP,CAAyB9D,MAAM,CAACd,MAAhC,CAAlG,CAAP;AAAoJ,CAA7L;;AACAiR,KAAK,CAACkC,IAAN,GAAa,aAAcrS,MAAM,CAACsS,iBAAP,CAAyB;EAAErO,IAAI,EAAEkM,KAAR;EAAejM,SAAS,EAAE,CAAC,CAAC,EAAD,EAAK,iBAAL,EAAwB,EAAxB,CAAD,CAA1B;EAAyDqO,QAAQ,EAAE,CAAnE;EAAsEC,YAAY,EAAE,SAASC,kBAAT,CAA4BpS,EAA5B,EAAgCC,GAAhC,EAAqC;IAAE,IAAID,EAAE,GAAG,CAAT,EAAY;MACnLL,MAAM,CAACU,UAAP,CAAkB,OAAlB,EAA2B,SAASgS,8BAAT,GAA0C;QAAE,OAAOpS,GAAG,CAACuR,QAAJ,EAAP;MAAwB,CAA/F,EAAiG,YAAjG,EAA+G,SAASc,mCAAT,GAA+C;QAAE,OAAOrS,GAAG,CAACwR,WAAJ,EAAP;MAA2B,CAA3L,EAA6L,YAA7L,EAA2M,SAASc,mCAAT,GAA+C;QAAE,OAAOtS,GAAG,CAACyR,gBAAJ,EAAP;MAAgC,CAA5R;IACH;;IAAC,IAAI1R,EAAE,GAAG,CAAT,EAAY;MACVL,MAAM,CAAC6S,uBAAP,CAA+B,WAA/B,EAA4CvS,GAAG,CAACd,KAAhD;MACAQ,MAAM,CAAC6B,UAAP,CAAkBvB,GAAG,CAAC+P,YAAtB;MACArQ,MAAM,CAAC6C,WAAP,CAAmB,SAAnB,EAA8BvC,GAAG,CAACyQ,YAAlC;IACH;EAAE,CAN6C;EAM3C+B,KAAK,EAAE3S,GANoC;EAM/B4S,KAAK,EAAE,CANwB;EAMrBC,IAAI,EAAE,CANe;EAMZC,MAAM,EAAE,CAAC,CAAC,OAAD,EAAU,oBAAV,EAAgC,YAAhC,EAA8C,OAA9C,EAAuD,CAAvD,EAA0D,OAA1D,EAAmE,CAAnE,EAAsE,MAAtE,CAAD,EAAgF,CAAC,CAAD,EAAI,OAAJ,EAAa,CAAb,EAAgB,MAAhB,CAAhF,EAAyG,CAAC,MAAD,EAAS,aAAT,EAAwB,WAAxB,EAAqC,QAArC,EAA+C,CAA/C,EAAkD,OAAlD,EAA2D,WAA3D,EAAwE,CAAxE,EAA2E,MAA3E,CAAzG,EAA6L,CAAC,MAAD,EAAS,aAAT,EAAwB,WAAxB,EAAqC,QAArC,EAA+C,CAA/C,EAAkD,OAAlD,EAA2D,CAA3D,EAA8D,MAA9D,CAA7L,EAAoQ,CAAC,CAAD,EAAI,MAAJ,CAApQ,EAAiR,CAAC,YAAD,EAAe,OAAf,EAAwB,CAAxB,EAA2B,oBAA3B,EAAiD,CAAjD,EAAoD,OAApD,CAAjR,EAA+U,CAAC,aAAD,EAAgB,MAAhB,CAA/U,EAAwW,CAAC,MAAD,EAAS,aAAT,EAAwB,WAAxB,EAAqC,QAArC,EAA+C,CAA/C,EAAkD,WAAlD,CAAxW,EAAwa,CAAC,MAAD,EAAS,aAAT,EAAwB,WAAxB,EAAqC,QAArC,CAAxa,EAAwd,CAAC,CAAD,EAAI,gBAAJ,CAAxd,CANI;EAM4eC,QAAQ,EAAE,SAASC,cAAT,CAAwB9S,EAAxB,EAA4BC,GAA5B,EAAiC;IAAE,IAAID,EAAE,GAAG,CAAT,EAAY;MACjlBL,MAAM,CAAC2B,UAAP,CAAkB,CAAlB,EAAqBvB,uBAArB,EAA8C,CAA9C,EAAiD,CAAjD,EAAoD,QAApD,EAA8D,CAA9D;MACAJ,MAAM,CAAC2B,UAAP,CAAkB,CAAlB,EAAqBD,oBAArB,EAA2C,CAA3C,EAA8C,CAA9C,EAAiD,KAAjD,EAAwD,CAAxD;MACA1B,MAAM,CAAC2B,UAAP,CAAkB,CAAlB,EAAqBQ,oBAArB,EAA2C,CAA3C,EAA8C,CAA9C,EAAiD,KAAjD,EAAwD,CAAxD;MACAnC,MAAM,CAAC2B,UAAP,CAAkB,CAAlB,EAAqBc,oBAArB,EAA2C,CAA3C,EAA8C,CAA9C,EAAiD,KAAjD,EAAwD,CAAxD;MACAzC,MAAM,CAAC2B,UAAP,CAAkB,CAAlB,EAAqBgB,oBAArB,EAA2C,CAA3C,EAA8C,CAA9C,EAAiD,KAAjD,EAAwD,CAAxD;IACH;;IAAC,IAAItC,EAAE,GAAG,CAAT,EAAY;MACVL,MAAM,CAACkC,UAAP,CAAkB,MAAlB,EAA0B5B,GAAG,CAACwB,OAAJ,CAAY2E,WAAtC;MACAzG,MAAM,CAACuB,SAAP,CAAiB,CAAjB;MACAvB,MAAM,CAACkC,UAAP,CAAkB,MAAlB,EAA0B5B,GAAG,CAAC2B,KAA9B;MACAjC,MAAM,CAACuB,SAAP,CAAiB,CAAjB;MACAvB,MAAM,CAACkC,UAAP,CAAkB,MAAlB,EAA0B5B,GAAG,CAACiC,OAAJ,IAAejC,GAAG,CAACwB,OAAJ,CAAY+E,UAArD;MACA7G,MAAM,CAACuB,SAAP,CAAiB,CAAjB;MACAvB,MAAM,CAACkC,UAAP,CAAkB,MAAlB,EAA0B5B,GAAG,CAACiC,OAAJ,IAAe,CAACjC,GAAG,CAACwB,OAAJ,CAAY+E,UAAtD;MACA7G,MAAM,CAACuB,SAAP,CAAiB,CAAjB;MACAvB,MAAM,CAACkC,UAAP,CAAkB,MAAlB,EAA0B5B,GAAG,CAACwB,OAAJ,CAAYgF,WAAtC;IACH;EAAE,CAtB6C;EAsB3CsM,YAAY,EAAE,CAAClT,MAAM,CAACmT,IAAR,CAtB6B;EAsBdC,aAAa,EAAE,CAtBD;EAsBIC,IAAI,EAAE;IAAEC,SAAS,EAAE,CAC/DjU,OAAO,CAAC,UAAD,EAAa,CAChBC,KAAK,CAAC,UAAD,EAAaC,KAAK,CAAC;MAAEgU,OAAO,EAAE;IAAX,CAAD,CAAlB,CADW,EAEhBjU,KAAK,CAAC,QAAD,EAAWC,KAAK,CAAC;MAAEgU,OAAO,EAAE;IAAX,CAAD,CAAhB,CAFW,EAGhBjU,KAAK,CAAC,SAAD,EAAYC,KAAK,CAAC;MAAEgU,OAAO,EAAE;IAAX,CAAD,CAAjB,CAHW,EAIhB/T,UAAU,CAAC,oBAAD,EAAuBC,OAAO,CAAC,+BAAD,CAA9B,CAJM,EAKhBD,UAAU,CAAC,mBAAD,EAAsBC,OAAO,CAAC,+BAAD,CAA7B,CALM,CAAb,CADwD;EAAb;AAtBV,CAAzB,CAA3B;;AA+BAwQ,KAAK,CAAC/L,cAAN,GAAuB,MAAM,CACzB;EAAEH,IAAI,EAAEmJ;AAAR,CADyB,EAEzB;EAAEnJ,IAAI,EAAEU;AAAR,CAFyB,EAGzB;EAAEV,IAAI,EAAE/E;AAAR,CAHyB,CAA7B;;AAKAf,UAAU,CAAC,CACPiB,WAAW,CAAC,OAAD,CADJ,EAEPhB,UAAU,CAAC,aAAD,EAAgBwM,MAAhB,CAFH,CAAD,EAGPuF,KAAK,CAACuD,SAHC,EAGU,cAHV,EAG0B,KAAK,CAH/B,CAAV;;AAIAvV,UAAU,CAAC,CACPiB,WAAW,CAAC,WAAD,CADJ,EAEPhB,UAAU,CAAC,aAAD,EAAgBwM,MAAhB,CAFH,CAAD,EAGPuF,KAAK,CAACuD,SAHC,EAGU,OAHV,EAGmB,KAAK,CAHxB,CAAV;;AAIAvV,UAAU,CAAC,CACPiB,WAAW,CAAC,eAAD,CADJ,EAEPhB,UAAU,CAAC,aAAD,EAAgBwM,MAAhB,CAFH,EAGPxM,UAAU,CAAC,mBAAD,EAAsB,EAAtB,CAHH,CAAD,EAIP+R,KAAK,CAACuD,SAJC,EAIU,cAJV,EAI0B,IAJ1B,CAAV;;AAKAvV,UAAU,CAAC,CACPkB,YAAY,CAAC,OAAD,CADL,EAEPjB,UAAU,CAAC,aAAD,EAAgBuV,QAAhB,CAFH,EAGPvV,UAAU,CAAC,mBAAD,EAAsB,EAAtB,CAHH,EAIPA,UAAU,CAAC,mBAAD,EAAsB,KAAK,CAA3B,CAJH,CAAD,EAKP+R,KAAK,CAACuD,SALC,EAKU,UALV,EAKsB,IALtB,CAAV;;AAMAvV,UAAU,CAAC,CACPkB,YAAY,CAAC,YAAD,CADL,EAEPjB,UAAU,CAAC,aAAD,EAAgBuV,QAAhB,CAFH,EAGPvV,UAAU,CAAC,mBAAD,EAAsB,EAAtB,CAHH,EAIPA,UAAU,CAAC,mBAAD,EAAsB,KAAK,CAA3B,CAJH,CAAD,EAKP+R,KAAK,CAACuD,SALC,EAKU,aALV,EAKyB,IALzB,CAAV;;AAMAvV,UAAU,CAAC,CACPkB,YAAY,CAAC,YAAD,CADL,EAEPjB,UAAU,CAAC,aAAD,EAAgBuV,QAAhB,CAFH,EAGPvV,UAAU,CAAC,mBAAD,EAAsB,EAAtB,CAHH,EAIPA,UAAU,CAAC,mBAAD,EAAsB,KAAK,CAA3B,CAJH,CAAD,EAKP+R,KAAK,CAACuD,SALC,EAKU,kBALV,EAK8B,IAL9B,CAAV;;AAMAvD,KAAK,GAAGhS,UAAU,CAAC,CAAEC,UAAU,CAAC,mBAAD,EAAsB,CAACgP,aAAD,EAC7CzI,YAD6C,EAE7CzF,MAF6C,CAAtB,CAAZ,CAAD,EAGfiR,KAHe,CAAlB;AAKA,IAAIyD,cAAJ;AACA,MAAMC,mBAAmB,GAAGjJ,MAAM,CAACiD,MAAP,CAAc,EAAd,EAAkBhI,8BAAlB,EAAkD;EAAEmJ,cAAc,EAAEmB;AAAlB,CAAlD,CAA5B;AACA,IAAI2D,YAAY,GAAGF,cAAc,GAAG,MAAME,YAAN,CAAmB;EACrC,OAAPC,OAAO,CAAClP,MAAM,GAAG,EAAV,EAAc;IACxB,OAAO;MACHmP,QAAQ,EAAEJ,cADP;MAEHK,SAAS,EAAE,CACP;QACIC,OAAO,EAAE7M,YADb;QAEI8M,QAAQ,EAAE;UACNrG,OAAO,EAAE+F,mBADH;UAENhP;QAFM;MAFd,CADO;IAFR,CAAP;EAYH;;AAdkD,CAAvD;;AAgBAiP,YAAY,CAACnQ,IAAb,GAAoB,SAASyQ,oBAAT,CAA8BvQ,CAA9B,EAAiC;EAAE,OAAO,KAAKA,CAAC,IAAIiQ,YAAV,GAAP;AAAmC,CAA1F;;AACAA,YAAY,CAACvP,IAAb,GAAoB,aAAcvE,MAAM,CAACwE,gBAAP,CAAwB;EAAEP,IAAI,EAAE6P;AAAR,CAAxB,CAAlC;AACAA,YAAY,CAACrP,IAAb,GAAoB,aAAczE,MAAM,CAAC0E,gBAAP,CAAwB;EAAE2P,OAAO,EAAE,CAACtU,YAAD;AAAX,CAAxB,CAAlC;AACA,IAAIuU,yBAAyB,GAAG,MAAMA,yBAAN,CAAgC;EAC9C,OAAPP,OAAO,CAAClP,MAAM,GAAG,EAAV,EAAc;IACxB,OAAO;MACHmP,QAAQ,EAAEF,YADP;MAEHG,SAAS,EAAE,CACP;QACIC,OAAO,EAAE7M,YADb;QAEI8M,QAAQ,EAAE;UACNrG,OAAO,EAAEjI,8BADH;UAENhB;QAFM;MAFd,CADO;IAFR,CAAP;EAYH;;AAd2D,CAAhE;;AAgBAyP,yBAAyB,CAAC3Q,IAA1B,GAAiC,SAAS4Q,iCAAT,CAA2C1Q,CAA3C,EAA8C;EAAE,OAAO,KAAKA,CAAC,IAAIyQ,yBAAV,GAAP;AAAgD,CAAjI;;AACAA,yBAAyB,CAAC/P,IAA1B,GAAiC,aAAcvE,MAAM,CAACwE,gBAAP,CAAwB;EAAEP,IAAI,EAAEqQ;AAAR,CAAxB,CAA/C;AACAA,yBAAyB,CAAC7P,IAA1B,GAAiC,aAAczE,MAAM,CAAC0E,gBAAP,CAAwB;EAAE2P,OAAO,EAAE,CAACtU,YAAD;AAAX,CAAxB,CAA/C;AAEA,IAAIyU,wBAAJ;AACA,IAAIC,gBAAgB,GAAG,MAAMA,gBAAN,CAAuB;EAC1ClR,WAAW,CAAC6M,aAAD,EAAgBX,YAAhB,EAA8BiF,MAA9B,EAAsC;IAC7C,KAAKtE,aAAL,GAAqBA,aAArB;IACA,KAAKX,YAAL,GAAoBA,YAApB;IACA,KAAKiF,MAAL,GAAcA,MAAd;IACA;;IACA,KAAK5R,KAAL,GAAa,CAAC,CAAd;IACA;;IACA,KAAKuN,YAAL,GAAoB,EAApB;IACA;;IACA,KAAK7Q,KAAL,GAAa,UAAb;IACA,KAAK+C,OAAL,GAAekN,YAAY,CAAClN,OAA5B;IACA,KAAKN,KAAL,GAAawN,YAAY,CAACxN,KAA1B;IACA,KAAKH,OAAL,GAAe2N,YAAY,CAAC5K,MAA5B;IACA,KAAK2L,eAAL,GAAuBf,YAAY,CAAC5K,MAAb,CAAoB8B,OAA3C;IACA,KAAK0J,YAAL,GAAqB,GAAEZ,YAAY,CAAC3K,SAAU,IAAG2K,YAAY,CAAC5K,MAAb,CAAoBkC,UAAW,EAAhF;IACA,KAAK0J,GAAL,GAAWhB,YAAY,CAAC1K,QAAb,CAAsB6H,aAAtB,GAAsCzH,SAAtC,CAAgD,MAAM;MAC7D,KAAKuL,aAAL;IACH,CAFU,CAAX;IAGA,KAAKC,IAAL,GAAYlB,YAAY,CAAC1K,QAAb,CAAsBsH,YAAtB,GAAqClH,SAArC,CAA+C,MAAM;MAC7D,KAAKnE,MAAL;IACH,CAFW,CAAZ;IAGA,KAAK4P,IAAL,GAAYnB,YAAY,CAAC1K,QAAb,CAAsBuH,YAAtB,GAAqCnH,SAArC,CAA+C,MAAM;MAC7D,KAAK2H,YAAL;IACH,CAFW,CAAZ;IAGA,KAAK+D,IAAL,GAAYpB,YAAY,CAAC1K,QAAb,CAAsBwH,cAAtB,GAAuCpH,SAAvC,CAAiD2L,KAAK,IAAI;MAClE,KAAKrP,eAAL,GAAuBqP,KAAvB;IACH,CAFW,CAAZ;EAGH;EACD;;;EACgB,IAAZC,YAAY,GAAG;IACf,IAAI,KAAKvR,KAAL,KAAe,UAAnB,EAA+B;MAC3B,OAAO,MAAP;IACH;EACJ;;EACDkK,WAAW,GAAG;IACV,KAAK+G,GAAL,CAASO,WAAT;IACA,KAAKL,IAAL,CAAUK,WAAV;IACA,KAAKJ,IAAL,CAAUI,WAAV;IACA,KAAKH,IAAL,CAAUG,WAAV;IACAC,aAAa,CAAC,KAAKC,UAAN,CAAb;IACAC,YAAY,CAAC,KAAKC,OAAN,CAAZ;EACH;EACD;AACJ;AACA;;;EACIV,aAAa,GAAG;IACZ,KAAKlR,KAAL,GAAa,QAAb;;IACA,IAAI,EAAE,KAAKsC,OAAL,CAAa4E,cAAb,KAAgC,IAAhC,IAAwC,KAAK5E,OAAL,CAAa4E,cAAb,KAAgC,SAA1E,KAAwF,KAAK5E,OAAL,CAAa6E,OAAzG,EAAkH;MAC9G,KAAKyK,OAAL,GAAepB,UAAU,CAAC,MAAM;QAC5B,KAAKhP,MAAL;MACH,CAFwB,EAEtB,KAAKc,OAAL,CAAa6E,OAFS,CAAzB;MAGA,KAAK2K,QAAL,GAAgB,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,KAAK1P,OAAL,CAAa6E,OAApD;;MACA,IAAI,KAAK7E,OAAL,CAAagF,WAAjB,EAA8B;QAC1B,KAAKoK,UAAL,GAAkBiB,WAAW,CAAC,MAAM,KAAKT,cAAL,EAAP,EAA8B,EAA9B,CAA7B;MACH;IACJ;;IACD,IAAI,KAAK5P,OAAL,CAAaqF,cAAjB,EAAiC;MAC7B,KAAKuN,MAAL,CAAYC,IAAZ;IACH;EACJ;EACD;AACJ;AACA;;;EACIjD,cAAc,GAAG;IACb,IAAI,KAAK5O,KAAL,KAAe,CAAf,IAAoB,KAAKA,KAAL,KAAe,GAAnC,IAA0C,CAAC,KAAKhB,OAAL,CAAa6E,OAA5D,EAAqE;MACjE;IACH;;IACD,MAAMgL,GAAG,GAAG,IAAIJ,IAAJ,GAAWC,OAAX,EAAZ;IACA,MAAMI,SAAS,GAAG,KAAKN,QAAL,GAAgBK,GAAlC;IACA,KAAK7O,KAAL,GAAc8O,SAAS,GAAG,KAAK9P,OAAL,CAAa6E,OAA1B,GAAqC,GAAlD;;IACA,IAAI,KAAK7E,OAAL,CAAasF,iBAAb,KAAmC,YAAvC,EAAqD;MACjD,KAAKtE,KAAL,GAAa,MAAM,KAAKA,KAAxB;IACH;;IACD,IAAI,KAAKA,KAAL,IAAc,CAAlB,EAAqB;MACjB,KAAKA,KAAL,GAAa,CAAb;IACH;;IACD,IAAI,KAAKA,KAAL,IAAc,GAAlB,EAAuB;MACnB,KAAKA,KAAL,GAAa,GAAb;IACH;EACJ;;EACDgK,YAAY,GAAG;IACXqE,YAAY,CAAC,KAAKC,OAAN,CAAZ;IACAH,aAAa,CAAC,KAAKC,UAAN,CAAb;IACA,KAAK1R,KAAL,GAAa,QAAb;IACA,KAAKsC,OAAL,CAAa6E,OAAb,GAAuB,KAAK6J,eAA5B;IACA,KAAKY,OAAL,GAAepB,UAAU,CAAC,MAAM,KAAKhP,MAAL,EAAP,EAAsB,KAAKwP,eAA3B,CAAzB;IACA,KAAKc,QAAL,GAAgB,IAAIC,IAAJ,GAAWC,OAAX,MAAwB,KAAKhB,eAAL,IAAwB,CAAhD,CAAhB;IACA,KAAK1N,KAAL,GAAa,CAAC,CAAd;;IACA,IAAI,KAAKhB,OAAL,CAAagF,WAAjB,EAA8B;MAC1B,KAAKoK,UAAL,GAAkBiB,WAAW,CAAC,MAAM,KAAKT,cAAL,EAAP,EAA8B,EAA9B,CAA7B;IACH;EACJ;EACD;AACJ;AACA;;;EACI1Q,MAAM,GAAG;IACL,IAAI,KAAKxB,KAAL,KAAe,SAAnB,EAA8B;MAC1B;IACH;;IACD2R,YAAY,CAAC,KAAKC,OAAN,CAAZ;IACA,KAAK5R,KAAL,GAAa,SAAb;IACA,KAAK4R,OAAL,GAAepB,UAAU,CAAC,MAAM,KAAKI,aAAL,CAAmBpP,MAAnB,CAA0B,KAAKyO,YAAL,CAAkB7K,OAA5C,CAAP,CAAzB;EACH;;EACDiN,QAAQ,GAAG;IACP,IAAI,KAAKrS,KAAL,KAAe,SAAnB,EAA8B;MAC1B;IACH;;IACD,KAAKiQ,YAAL,CAAkBpK,UAAlB;;IACA,IAAI,KAAKvD,OAAL,CAAayD,YAAjB,EAA+B;MAC3B,KAAKvE,MAAL;IACH;EACJ;;EACD8Q,WAAW,GAAG;IACV,IAAI,KAAKtS,KAAL,KAAe,SAAnB,EAA8B;MAC1B;IACH;;IACD2R,YAAY,CAAC,KAAKC,OAAN,CAAZ;IACA,KAAKtP,OAAL,CAAa6E,OAAb,GAAuB,CAAvB;IACA,KAAK2K,QAAL,GAAgB,CAAhB,CANU,CAOV;;IACAL,aAAa,CAAC,KAAKC,UAAN,CAAb;IACA,KAAKpO,KAAL,GAAa,CAAb;EACH;;EACDiP,gBAAgB,GAAG;IACf,IAAK,KAAKjQ,OAAL,CAAa4E,cAAb,KAAgC,IAAhC,IAAwC,KAAK5E,OAAL,CAAa4E,cAAb,KAAgC,iBAAzE,IACA,KAAK5E,OAAL,CAAa8E,eAAb,KAAiC,CADjC,IAEA,KAAKpH,KAAL,KAAe,SAFnB,EAE8B;MAC1B;IACH;;IACD,KAAK4R,OAAL,GAAepB,UAAU,CAAC,MAAM,KAAKhP,MAAL,EAAP,EAAsB,KAAKc,OAAL,CAAa8E,eAAnC,CAAzB;IACA,KAAK9E,OAAL,CAAa6E,OAAb,GAAuB,KAAK7E,OAAL,CAAa8E,eAApC;IACA,KAAK0K,QAAL,GAAgB,IAAIC,IAAJ,GAAWC,OAAX,MAAwB,KAAK1P,OAAL,CAAa6E,OAAb,IAAwB,CAAhD,CAAhB;IACA,KAAK7D,KAAL,GAAa,CAAC,CAAd;;IACA,IAAI,KAAKhB,OAAL,CAAagF,WAAjB,EAA8B;MAC1B,KAAKoK,UAAL,GAAkBiB,WAAW,CAAC,MAAM,KAAKT,cAAL,EAAP,EAA8B,EAA9B,CAA7B;IACH;EACJ;;AAzIyC,CAA9C;;AA2IA+C,gBAAgB,CAAC9Q,IAAjB,GAAwB,SAASiR,wBAAT,CAAkC/Q,CAAlC,EAAqC;EAAE,OAAO,KAAKA,CAAC,IAAI4Q,gBAAV,EAA4BzU,MAAM,CAAC8D,iBAAP,CAAyBsJ,aAAzB,CAA5B,EAAqEpN,MAAM,CAAC8D,iBAAP,CAAyBa,YAAzB,CAArE,EAA6G3E,MAAM,CAAC8D,iBAAP,CAAyB9D,MAAM,CAACjB,cAAhC,CAA7G,CAAP;AAAuK,CAAtO;;AACA0V,gBAAgB,CAACpC,IAAjB,GAAwB,aAAcrS,MAAM,CAACsS,iBAAP,CAAyB;EAAErO,IAAI,EAAEwQ,gBAAR;EAA0BvQ,SAAS,EAAE,CAAC,CAAC,EAAD,EAAK,iBAAL,EAAwB,EAAxB,CAAD,CAArC;EAAoEqO,QAAQ,EAAE,CAA9E;EAAiFC,YAAY,EAAE,SAASqC,6BAAT,CAAuCxU,EAAvC,EAA2CC,GAA3C,EAAgD;IAAE,IAAID,EAAE,GAAG,CAAT,EAAY;MACpNL,MAAM,CAACU,UAAP,CAAkB,OAAlB,EAA2B,SAASoU,yCAAT,GAAqD;QAAE,OAAOxU,GAAG,CAACuR,QAAJ,EAAP;MAAwB,CAA1G,EAA4G,YAA5G,EAA0H,SAASkD,8CAAT,GAA0D;QAAE,OAAOzU,GAAG,CAACwR,WAAJ,EAAP;MAA2B,CAAjN,EAAmN,YAAnN,EAAiO,SAASkD,8CAAT,GAA0D;QAAE,OAAO1U,GAAG,CAACyR,gBAAJ,EAAP;MAAgC,CAA7T;IACH;;IAAC,IAAI1R,EAAE,GAAG,CAAT,EAAY;MACVL,MAAM,CAAC6B,UAAP,CAAkBvB,GAAG,CAAC+P,YAAtB;MACArQ,MAAM,CAAC6C,WAAP,CAAmB,SAAnB,EAA8BvC,GAAG,CAACyQ,YAAlC;IACH;EAAE,CALwD;EAKtD+B,KAAK,EAAE3S,GAL+C;EAK1C4S,KAAK,EAAE,CALmC;EAKhCC,IAAI,EAAE,CAL0B;EAKvBC,MAAM,EAAE,CAAC,CAAC,OAAD,EAAU,oBAAV,EAAgC,YAAhC,EAA8C,OAA9C,EAAuD,CAAvD,EAA0D,OAA1D,EAAmE,CAAnE,EAAsE,MAAtE,CAAD,EAAgF,CAAC,CAAD,EAAI,OAAJ,EAAa,CAAb,EAAgB,MAAhB,CAAhF,EAAyG,CAAC,MAAD,EAAS,OAAT,EAAkB,WAAlB,EAA+B,QAA/B,EAAyC,CAAzC,EAA4C,OAA5C,EAAqD,WAArD,EAAkE,CAAlE,EAAqE,MAArE,CAAzG,EAAuL,CAAC,MAAD,EAAS,OAAT,EAAkB,WAAlB,EAA+B,QAA/B,EAAyC,CAAzC,EAA4C,OAA5C,EAAqD,CAArD,EAAwD,MAAxD,CAAvL,EAAwP,CAAC,CAAD,EAAI,MAAJ,CAAxP,EAAqQ,CAAC,YAAD,EAAe,OAAf,EAAwB,CAAxB,EAA2B,oBAA3B,EAAiD,CAAjD,EAAoD,OAApD,CAArQ,EAAmU,CAAC,aAAD,EAAgB,MAAhB,CAAnU,EAA4V,CAAC,MAAD,EAAS,OAAT,EAAkB,WAAlB,EAA+B,QAA/B,EAAyC,CAAzC,EAA4C,WAA5C,CAA5V,EAAsZ,CAAC,MAAD,EAAS,OAAT,EAAkB,WAAlB,EAA+B,QAA/B,CAAtZ,EAAgc,CAAC,CAAD,EAAI,gBAAJ,CAAhc,CALe;EAKycC,QAAQ,EAAE,SAAS+B,yBAAT,CAAmC5U,EAAnC,EAAuCC,GAAvC,EAA4C;IAAE,IAAID,EAAE,GAAG,CAAT,EAAY;MACpkBL,MAAM,CAAC2B,UAAP,CAAkB,CAAlB,EAAqBoB,kCAArB,EAAyD,CAAzD,EAA4D,CAA5D,EAA+D,QAA/D,EAAyE,CAAzE;MACA/C,MAAM,CAAC2B,UAAP,CAAkB,CAAlB,EAAqBuB,+BAArB,EAAsD,CAAtD,EAAyD,CAAzD,EAA4D,KAA5D,EAAmE,CAAnE;MACAlD,MAAM,CAAC2B,UAAP,CAAkB,CAAlB,EAAqBwB,+BAArB,EAAsD,CAAtD,EAAyD,CAAzD,EAA4D,KAA5D,EAAmE,CAAnE;MACAnD,MAAM,CAAC2B,UAAP,CAAkB,CAAlB,EAAqByB,+BAArB,EAAsD,CAAtD,EAAyD,CAAzD,EAA4D,KAA5D,EAAmE,CAAnE;MACApD,MAAM,CAAC2B,UAAP,CAAkB,CAAlB,EAAqB0B,+BAArB,EAAsD,CAAtD,EAAyD,CAAzD,EAA4D,KAA5D,EAAmE,CAAnE;IACH;;IAAC,IAAIhD,EAAE,GAAG,CAAT,EAAY;MACVL,MAAM,CAACkC,UAAP,CAAkB,MAAlB,EAA0B5B,GAAG,CAACwB,OAAJ,CAAY2E,WAAtC;MACAzG,MAAM,CAACuB,SAAP,CAAiB,CAAjB;MACAvB,MAAM,CAACkC,UAAP,CAAkB,MAAlB,EAA0B5B,GAAG,CAAC2B,KAA9B;MACAjC,MAAM,CAACuB,SAAP,CAAiB,CAAjB;MACAvB,MAAM,CAACkC,UAAP,CAAkB,MAAlB,EAA0B5B,GAAG,CAACiC,OAAJ,IAAejC,GAAG,CAACwB,OAAJ,CAAY+E,UAArD;MACA7G,MAAM,CAACuB,SAAP,CAAiB,CAAjB;MACAvB,MAAM,CAACkC,UAAP,CAAkB,MAAlB,EAA0B5B,GAAG,CAACiC,OAAJ,IAAe,CAACjC,GAAG,CAACwB,OAAJ,CAAY+E,UAAtD;MACA7G,MAAM,CAACuB,SAAP,CAAiB,CAAjB;MACAvB,MAAM,CAACkC,UAAP,CAAkB,MAAlB,EAA0B5B,GAAG,CAACwB,OAAJ,CAAYgF,WAAtC;IACH;EAAE,CArBwD;EAqBtDsM,YAAY,EAAE,CAAClT,MAAM,CAACmT,IAAR,CArBwC;EAqBzBC,aAAa,EAAE;AArBU,CAAzB,CAAtC;;AAsBAmB,gBAAgB,CAACrQ,cAAjB,GAAkC,MAAM,CACpC;EAAEH,IAAI,EAAEmJ;AAAR,CADoC,EAEpC;EAAEnJ,IAAI,EAAEU;AAAR,CAFoC,EAGpC;EAAEV,IAAI,EAAElF;AAAR,CAHoC,CAAxC;;AAKAZ,UAAU,CAAC,CACPiB,WAAW,CAAC,OAAD,CADJ,EAEPhB,UAAU,CAAC,aAAD,EAAgBwM,MAAhB,CAFH,CAAD,EAGP6J,gBAAgB,CAACf,SAHV,EAGqB,cAHrB,EAGqC,KAAK,CAH1C,CAAV;;AAIAvV,UAAU,CAAC,CACPiB,WAAW,CAAC,eAAD,CADJ,EAEPhB,UAAU,CAAC,aAAD,EAAgBwM,MAAhB,CAFH,EAGPxM,UAAU,CAAC,mBAAD,EAAsB,EAAtB,CAHH,CAAD,EAIPqW,gBAAgB,CAACf,SAJV,EAIqB,cAJrB,EAIqC,IAJrC,CAAV;;AAKAvV,UAAU,CAAC,CACPkB,YAAY,CAAC,OAAD,CADL,EAEPjB,UAAU,CAAC,aAAD,EAAgBuV,QAAhB,CAFH,EAGPvV,UAAU,CAAC,mBAAD,EAAsB,EAAtB,CAHH,EAIPA,UAAU,CAAC,mBAAD,EAAsB,KAAK,CAA3B,CAJH,CAAD,EAKPqW,gBAAgB,CAACf,SALV,EAKqB,UALrB,EAKiC,IALjC,CAAV;;AAMAvV,UAAU,CAAC,CACPkB,YAAY,CAAC,YAAD,CADL,EAEPjB,UAAU,CAAC,aAAD,EAAgBuV,QAAhB,CAFH,EAGPvV,UAAU,CAAC,mBAAD,EAAsB,EAAtB,CAHH,EAIPA,UAAU,CAAC,mBAAD,EAAsB,KAAK,CAA3B,CAJH,CAAD,EAKPqW,gBAAgB,CAACf,SALV,EAKqB,aALrB,EAKoC,IALpC,CAAV;;AAMAvV,UAAU,CAAC,CACPkB,YAAY,CAAC,YAAD,CADL,EAEPjB,UAAU,CAAC,aAAD,EAAgBuV,QAAhB,CAFH,EAGPvV,UAAU,CAAC,mBAAD,EAAsB,EAAtB,CAHH,EAIPA,UAAU,CAAC,mBAAD,EAAsB,KAAK,CAA3B,CAJH,CAAD,EAKPqW,gBAAgB,CAACf,SALV,EAKqB,kBALrB,EAKyC,IALzC,CAAV;;AAMAe,gBAAgB,GAAGtW,UAAU,CAAC,CAAEC,UAAU,CAAC,mBAAD,EAAsB,CAACgP,aAAD,EACxDzI,YADwD,EAExD5F,cAFwD,CAAtB,CAAZ,CAAD,EAG1B0V,gBAH0B,CAA7B;AAIA,MAAMS,+BAA+B,GAAGtK,MAAM,CAACiD,MAAP,CAAc,EAAd,EAAkBhI,8BAAlB,EAAkD;EAAEmJ,cAAc,EAAEyF;AAAlB,CAAlD,CAAxC;AACA,IAAIU,sBAAsB,GAAGX,wBAAwB,GAAG,MAAMW,sBAAN,CAA6B;EACnE,OAAPpB,OAAO,CAAClP,MAAM,GAAG,EAAV,EAAc;IACxB,OAAO;MACHmP,QAAQ,EAAEQ,wBADP;MAEHP,SAAS,EAAE,CACP;QACIC,OAAO,EAAE7M,YADb;QAEI8M,QAAQ,EAAE;UACNrG,OAAO,EAAEoH,+BADH;UAENrQ;QAFM;MAFd,CADO;IAFR,CAAP;EAYH;;AAdgF,CAArF;;AAgBAsQ,sBAAsB,CAACxR,IAAvB,GAA8B,SAASyR,8BAAT,CAAwCvR,CAAxC,EAA2C;EAAE,OAAO,KAAKA,CAAC,IAAIsR,sBAAV,GAAP;AAA6C,CAAxH;;AACAA,sBAAsB,CAAC5Q,IAAvB,GAA8B,aAAcvE,MAAM,CAACwE,gBAAP,CAAwB;EAAEP,IAAI,EAAEkR;AAAR,CAAxB,CAA5C;AACAA,sBAAsB,CAAC1Q,IAAvB,GAA8B,aAAczE,MAAM,CAAC0E,gBAAP,CAAwB;EAAE2P,OAAO,EAAE,CAACtU,YAAD;AAAX,CAAxB,CAA5C;;AACA,CAAC,YAAY;EAAE,CAAC,OAAOsV,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KAAmDrV,MAAM,CAACsV,iBAAP,CAAyBhS,uBAAzB,EAAkD,CAAC;IAC7GW,IAAI,EAAE1F,SADuG;IAE7GmM,IAAI,EAAE,CAAC;MACC6K,QAAQ,EAAE,kBADX;MAECpR,QAAQ,EAAE;IAFX,CAAD;EAFuG,CAAD,CAAlD,EAM1D,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAEjE,MAAM,CAAC1B;IAAf,CAAD,CAAP;EAAuC,CANK,EAMH,IANG,CAAnD;AAMwD,CANvE;;AAOA,CAAC,YAAY;EAAE,CAAC,OAAO+W,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KAAmDrV,MAAM,CAACsV,iBAAP,CAAyBjR,oBAAzB,EAA+C,CAAC;IAC1GJ,IAAI,EAAEzF,QADoG;IAE1GkM,IAAI,EAAE,CAAC;MACC8K,YAAY,EAAE,CAAClS,uBAAD,CADf;MAECmS,OAAO,EAAE,CAACnS,uBAAD;IAFV,CAAD;EAFoG,CAAD,CAA/C,EAM1D,IAN0D,EAMpD,IANoD,CAAnD;AAMO,CANtB;;AAOA,CAAC,YAAY;EAAE,CAAC,OAAOoS,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KAAmD1V,MAAM,CAAC2V,kBAAP,CAA0BtR,oBAA1B,EAAgD;IAAEmR,YAAY,EAAE,CAAClS,uBAAD,CAAhB;IAA2CmS,OAAO,EAAE,CAACnS,uBAAD;EAApD,CAAhD,CAAnD;AAAsL,CAArM;;AACA,CAAC,YAAY;EAAE,CAAC,OAAO+R,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KAAmDrV,MAAM,CAACsV,iBAAP,CAAyB9L,gBAAzB,EAA2C,CAAC;IACtGvF,IAAI,EAAEpF,UADgG;IAEtG6L,IAAI,EAAE,CAAC;MAAEF,UAAU,EAAE;IAAd,CAAD;EAFgG,CAAD,CAA3C,EAG1D,YAAY;IAAE,OAAO,CAAC;MAAEvG,IAAI,EAAE4D,SAAR;MAAmB4C,UAAU,EAAE,CAAC;QAC9CxG,IAAI,EAAEvF,MADwC;QAE9CgM,IAAI,EAAE,CAAC5K,QAAD;MAFwC,CAAD;IAA/B,CAAD,CAAP;EAGF,CAN8C,EAM5C,IAN4C,CAAnD;AAMe,CAN9B;;AAOA,CAAC,YAAY;EAAE,CAAC,OAAOuV,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KAAmDrV,MAAM,CAACsV,iBAAP,CAAyBvK,OAAzB,EAAkC,CAAC;IAC7F9G,IAAI,EAAEpF,UADuF;IAE7F6L,IAAI,EAAE,CAAC;MAAEF,UAAU,EAAE;IAAd,CAAD;EAFuF,CAAD,CAAlC,EAG1D,YAAY;IAAE,OAAO,CAAC;MAAEvG,IAAI,EAAEuF;IAAR,CAAD,EAA6B;MAAEvF,IAAI,EAAEjE,MAAM,CAAClB;IAAf,CAA7B,EAAwE;MAAEmF,IAAI,EAAEjE,MAAM,CAACjB;IAAf,CAAxE,EAAyG;MAAEkF,IAAI,EAAE4D,SAAR;MAAmB4C,UAAU,EAAE,CAAC;QACtJxG,IAAI,EAAEvF,MADgJ;QAEtJgM,IAAI,EAAE,CAAC5K,QAAD;MAFgJ,CAAD;IAA/B,CAAzG,CAAP;EAGF,CAN8C,EAM5C,IAN4C,CAAnD;AAMe,CAN9B;;AAOA,CAAC,YAAY;EAAE,CAAC,OAAOuV,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KAAmDrV,MAAM,CAACsV,iBAAP,CAAyBlI,aAAzB,EAAwC,CAAC;IACnGnJ,IAAI,EAAEpF,UAD6F;IAEnG6L,IAAI,EAAE,CAAC;MAAEF,UAAU,EAAE;IAAd,CAAD;EAF6F,CAAD,CAAxC,EAG1D,YAAY;IAAE,OAAO,CAAC;MAAEvG,IAAI,EAAE4D,SAAR;MAAmB4C,UAAU,EAAE,CAAC;QAC9CxG,IAAI,EAAEvF,MADwC;QAE9CgM,IAAI,EAAE,CAACrD,YAAD;MAFwC,CAAD;IAA/B,CAAD,EAGX;MAAEpD,IAAI,EAAE8G;IAAR,CAHW,EAGQ;MAAE9G,IAAI,EAAEjE,MAAM,CAACf;IAAf,CAHR,EAGmC;MAAEgF,IAAI,EAAEhE,MAAM,CAACJ;IAAf,CAHnC,EAGkE;MAAEoE,IAAI,EAAEjE,MAAM,CAACd;IAAf,CAHlE,CAAP;EAGoG,CANxD,EAM0D,IAN1D,CAAnD;AAMqH,CANpI;;AAOA,CAAC,YAAY;EAAE,CAAC,OAAOmW,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KAAmDrV,MAAM,CAACsV,iBAAP,CAAyBnF,KAAzB,EAAgC,CAAC;IAC3FlM,IAAI,EAAE3E,SADqF;IAE3FoL,IAAI,EAAE,CAAC;MACC6K,QAAQ,EAAE,mBADX;MAECrC,QAAQ,EAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAnBe;MAoBC0C,UAAU,EAAE,CACRrW,OAAO,CAAC,UAAD,EAAa,CAChBC,KAAK,CAAC,UAAD,EAAaC,KAAK,CAAC;QAAEgU,OAAO,EAAE;MAAX,CAAD,CAAlB,CADW,EAEhBjU,KAAK,CAAC,QAAD,EAAWC,KAAK,CAAC;QAAEgU,OAAO,EAAE;MAAX,CAAD,CAAhB,CAFW,EAGhBjU,KAAK,CAAC,SAAD,EAAYC,KAAK,CAAC;QAAEgU,OAAO,EAAE;MAAX,CAAD,CAAjB,CAHW,EAIhB/T,UAAU,CAAC,oBAAD,EAAuBC,OAAO,CAAC,+BAAD,CAA9B,CAJM,EAKhBD,UAAU,CAAC,mBAAD,EAAsBC,OAAO,CAAC,+BAAD,CAA7B,CALM,CAAb,CADC,CApBb;MA6BCkW,mBAAmB,EAAE;IA7BtB,CAAD;EAFqF,CAAD,CAAhC,EAiC1D,YAAY;IAAE,OAAO,CAAC;MAAE5R,IAAI,EAAEmJ;IAAR,CAAD,EAA0B;MAAEnJ,IAAI,EAAEU;IAAR,CAA1B,EAAkD;MAAEV,IAAI,EAAEjE,MAAM,CAACd;IAAf,CAAlD,CAAP;EAAoF,CAjCxC,EAiC0C;IAAEmR,YAAY,EAAE,CAAC;MACjHpM,IAAI,EAAE7E,WAD2G;MAEjHsL,IAAI,EAAE,CAAC,OAAD;IAF2G,CAAD,CAAhB;IAGhGlL,KAAK,EAAE,CAAC;MACRyE,IAAI,EAAE7E,WADE;MAERsL,IAAI,EAAE,CAAC,WAAD;IAFE,CAAD,CAHyF;IAMhGqG,YAAY,EAAE,CAAC;MACf9M,IAAI,EAAE7E,WADS;MAEfsL,IAAI,EAAE,CAAC,eAAD;IAFS,CAAD,CANkF;IAShGmH,QAAQ,EAAE,CAAC;MACX5N,IAAI,EAAE5E,YADK;MAEXqL,IAAI,EAAE,CAAC,OAAD;IAFK,CAAD,CATsF;IAYhGoH,WAAW,EAAE,CAAC;MACd7N,IAAI,EAAE5E,YADQ;MAEdqL,IAAI,EAAE,CAAC,YAAD;IAFQ,CAAD,CAZmF;IAehGqH,gBAAgB,EAAE,CAAC;MACnB9N,IAAI,EAAE5E,YADa;MAEnBqL,IAAI,EAAE,CAAC,YAAD;IAFa,CAAD;EAf8E,CAjC1C,CAAnD;AAmDC,CAnDhB;;AAoDA,CAAC,YAAY;EAAE,CAAC,OAAO2K,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KAAmDrV,MAAM,CAACsV,iBAAP,CAAyBxB,YAAzB,EAAuC,CAAC;IAClG7P,IAAI,EAAEzF,QAD4F;IAElGkM,IAAI,EAAE,CAAC;MACC2J,OAAO,EAAE,CAACtU,YAAD,CADV;MAECyV,YAAY,EAAE,CAACrF,KAAD,CAFf;MAGCsF,OAAO,EAAE,CAACtF,KAAD,CAHV;MAIC2F,eAAe,EAAE,CAAC3F,KAAD;IAJlB,CAAD;EAF4F,CAAD,CAAvC,EAQ1D,IAR0D,EAQpD,IARoD,CAAnD;AAQO,CARtB;;AASA,CAAC,YAAY;EAAE,CAAC,OAAOuF,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KAAmD1V,MAAM,CAAC2V,kBAAP,CAA0B7B,YAA1B,EAAwC;IAAE0B,YAAY,EAAE,YAAY;MAAE,OAAO,CAACrF,KAAD,CAAP;IAAiB,CAA/C;IAAiDkE,OAAO,EAAE,YAAY;MAAE,OAAO,CAACtU,YAAD,CAAP;IAAwB,CAAhG;IAAkG0V,OAAO,EAAE,YAAY;MAAE,OAAO,CAACtF,KAAD,CAAP;IAAiB;EAA1I,CAAxC,CAAnD;AAA2O,CAA1P;;AACA,CAAC,YAAY;EAAE,CAAC,OAAOkF,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KAAmDrV,MAAM,CAACsV,iBAAP,CAAyBhB,yBAAzB,EAAoD,CAAC;IAC/GrQ,IAAI,EAAEzF,QADyG;IAE/GkM,IAAI,EAAE,CAAC;MACC2J,OAAO,EAAE,CAACtU,YAAD;IADV,CAAD;EAFyG,CAAD,CAApD,EAK1D,IAL0D,EAKpD,IALoD,CAAnD;AAKO,CALtB;;AAMA,CAAC,YAAY;EAAE,CAAC,OAAO2V,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KAAmD1V,MAAM,CAAC2V,kBAAP,CAA0BrB,yBAA1B,EAAqD;IAAED,OAAO,EAAE,YAAY;MAAE,OAAO,CAACtU,YAAD,CAAP;IAAwB;EAAjD,CAArD,CAAnD;AAA+J,CAA9K;;AACA,CAAC,YAAY;EAAE,CAAC,OAAOsV,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KAAmDrV,MAAM,CAACsV,iBAAP,CAAyBb,gBAAzB,EAA2C,CAAC;IACtGxQ,IAAI,EAAE3E,SADgG;IAEtGoL,IAAI,EAAE,CAAC;MACC6K,QAAQ,EAAE,mBADX;MAECrC,QAAQ,EAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAnBe,CAAD;EAFgG,CAAD,CAA3C,EAuB1D,YAAY;IAAE,OAAO,CAAC;MAAEjP,IAAI,EAAEmJ;IAAR,CAAD,EAA0B;MAAEnJ,IAAI,EAAEU;IAAR,CAA1B,EAAkD;MAAEV,IAAI,EAAEjE,MAAM,CAACjB;IAAf,CAAlD,CAAP;EAA4F,CAvBhD,EAuBkD;IAAEsR,YAAY,EAAE,CAAC;MACzHpM,IAAI,EAAE7E,WADmH;MAEzHsL,IAAI,EAAE,CAAC,OAAD;IAFmH,CAAD,CAAhB;IAGxGqG,YAAY,EAAE,CAAC;MACf9M,IAAI,EAAE7E,WADS;MAEfsL,IAAI,EAAE,CAAC,eAAD;IAFS,CAAD,CAH0F;IAMxGmH,QAAQ,EAAE,CAAC;MACX5N,IAAI,EAAE5E,YADK;MAEXqL,IAAI,EAAE,CAAC,OAAD;IAFK,CAAD,CAN8F;IASxGoH,WAAW,EAAE,CAAC;MACd7N,IAAI,EAAE5E,YADQ;MAEdqL,IAAI,EAAE,CAAC,YAAD;IAFQ,CAAD,CAT2F;IAYxGqH,gBAAgB,EAAE,CAAC;MACnB9N,IAAI,EAAE5E,YADa;MAEnBqL,IAAI,EAAE,CAAC,YAAD;IAFa,CAAD;EAZsF,CAvBlD,CAAnD;AAsCC,CAtChB;;AAuCA,CAAC,YAAY;EAAE,CAAC,OAAO2K,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KAAmDrV,MAAM,CAACsV,iBAAP,CAAyBH,sBAAzB,EAAiD,CAAC;IAC5GlR,IAAI,EAAEzF,QADsG;IAE5GkM,IAAI,EAAE,CAAC;MACC2J,OAAO,EAAE,CAACtU,YAAD,CADV;MAECyV,YAAY,EAAE,CAACf,gBAAD,CAFf;MAGCgB,OAAO,EAAE,CAAChB,gBAAD,CAHV;MAICqB,eAAe,EAAE,CAACrB,gBAAD;IAJlB,CAAD;EAFsG,CAAD,CAAjD,EAQ1D,IAR0D,EAQpD,IARoD,CAAnD;AAQO,CARtB;;AASA,CAAC,YAAY;EAAE,CAAC,OAAOiB,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KAAmD1V,MAAM,CAAC2V,kBAAP,CAA0BR,sBAA1B,EAAkD;IAAEK,YAAY,EAAE,YAAY;MAAE,OAAO,CAACf,gBAAD,CAAP;IAA4B,CAA1D;IAA4DJ,OAAO,EAAE,YAAY;MAAE,OAAO,CAACtU,YAAD,CAAP;IAAwB,CAA3G;IAA6G0V,OAAO,EAAE,YAAY;MAAE,OAAO,CAAChB,gBAAD,CAAP;IAA4B;EAAhK,CAAlD,CAAnD;AAA2Q,CAA1R;AAEA;AACA;AACA;;;AAEA,SAASzM,cAAT,EAAyBV,eAAzB,EAA0CuM,mBAA1C,EAA+DqB,+BAA/D,EAAgGrP,8BAAhG,EAAgIkF,OAAhI,EAAyIvB,gBAAzI,EAA2JqB,UAA3J,EAAuKxD,YAAvK,EAAqL8I,KAArL,EAA4L7M,uBAA5L,EAAqNe,oBAArN,EAA2O0I,aAA3O,EAA0P0H,gBAA1P,EAA4QU,sBAA5Q,EAAoSxQ,YAApS,EAAkTkH,QAAlT,EAA4TyI,yBAA5T,EAAuVR,YAAvV,EAAqW1G,aAArW"}, "metadata": {}, "sourceType": "module"}