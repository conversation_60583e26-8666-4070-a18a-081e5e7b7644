{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function isEmpty() {\n  return source => source.lift(new IsEmptyOperator());\n}\n\nclass IsEmptyOperator {\n  call(observer, source) {\n    return source.subscribe(new IsEmptySubscriber(observer));\n  }\n\n}\n\nclass IsEmptySubscriber extends Subscriber {\n  constructor(destination) {\n    super(destination);\n  }\n\n  notifyComplete(isEmpty) {\n    const destination = this.destination;\n    destination.next(isEmpty);\n    destination.complete();\n  }\n\n  _next(value) {\n    this.notifyComplete(false);\n  }\n\n  _complete() {\n    this.notifyComplete(true);\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "isEmpty", "source", "lift", "IsEmptyOperator", "call", "observer", "subscribe", "IsEmptySubscriber", "constructor", "destination", "notifyComplete", "next", "complete", "_next", "value", "_complete"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/isEmpty.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function isEmpty() {\n    return (source) => source.lift(new IsEmptyOperator());\n}\nclass IsEmptyOperator {\n    call(observer, source) {\n        return source.subscribe(new IsEmptySubscriber(observer));\n    }\n}\nclass IsEmptySubscriber extends Subscriber {\n    constructor(destination) {\n        super(destination);\n    }\n    notifyComplete(isEmpty) {\n        const destination = this.destination;\n        destination.next(isEmpty);\n        destination.complete();\n    }\n    _next(value) {\n        this.notifyComplete(false);\n    }\n    _complete() {\n        this.notifyComplete(true);\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,OAAT,GAAmB;EACtB,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,eAAJ,EAAZ,CAAnB;AACH;;AACD,MAAMA,eAAN,CAAsB;EAClBC,IAAI,CAACC,QAAD,EAAWJ,MAAX,EAAmB;IACnB,OAAOA,MAAM,CAACK,SAAP,CAAiB,IAAIC,iBAAJ,CAAsBF,QAAtB,CAAjB,CAAP;EACH;;AAHiB;;AAKtB,MAAME,iBAAN,SAAgCR,UAAhC,CAA2C;EACvCS,WAAW,CAACC,WAAD,EAAc;IACrB,MAAMA,WAAN;EACH;;EACDC,cAAc,CAACV,OAAD,EAAU;IACpB,MAAMS,WAAW,GAAG,KAAKA,WAAzB;IACAA,WAAW,CAACE,IAAZ,CAAiBX,OAAjB;IACAS,WAAW,CAACG,QAAZ;EACH;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACT,KAAKJ,cAAL,CAAoB,KAApB;EACH;;EACDK,SAAS,GAAG;IACR,KAAKL,cAAL,CAAoB,IAApB;EACH;;AAdsC"}, "metadata": {}, "sourceType": "module"}