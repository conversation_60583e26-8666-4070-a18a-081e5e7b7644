{"ast": null, "code": "import { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nexport function schedulePromise(input, scheduler) {\n  return new Observable(subscriber => {\n    const sub = new Subscription();\n    sub.add(scheduler.schedule(() => input.then(value => {\n      sub.add(scheduler.schedule(() => {\n        subscriber.next(value);\n        sub.add(scheduler.schedule(() => subscriber.complete()));\n      }));\n    }, err => {\n      sub.add(scheduler.schedule(() => subscriber.error(err)));\n    })));\n    return sub;\n  });\n}", "map": {"version": 3, "names": ["Observable", "Subscription", "schedulePromise", "input", "scheduler", "subscriber", "sub", "add", "schedule", "then", "value", "next", "complete", "err", "error"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/scheduled/schedulePromise.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nexport function schedulePromise(input, scheduler) {\n    return new Observable(subscriber => {\n        const sub = new Subscription();\n        sub.add(scheduler.schedule(() => input.then(value => {\n            sub.add(scheduler.schedule(() => {\n                subscriber.next(value);\n                sub.add(scheduler.schedule(() => subscriber.complete()));\n            }));\n        }, err => {\n            sub.add(scheduler.schedule(() => subscriber.error(err)));\n        })));\n        return sub;\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,SAASC,eAAT,CAAyBC,KAAzB,EAAgCC,SAAhC,EAA2C;EAC9C,OAAO,IAAIJ,UAAJ,CAAeK,UAAU,IAAI;IAChC,MAAMC,GAAG,GAAG,IAAIL,YAAJ,EAAZ;IACAK,GAAG,CAACC,GAAJ,CAAQH,SAAS,CAACI,QAAV,CAAmB,MAAML,KAAK,CAACM,IAAN,CAAWC,KAAK,IAAI;MACjDJ,GAAG,CAACC,GAAJ,CAAQH,SAAS,CAACI,QAAV,CAAmB,MAAM;QAC7BH,UAAU,CAACM,IAAX,CAAgBD,KAAhB;QACAJ,GAAG,CAACC,GAAJ,CAAQH,SAAS,CAACI,QAAV,CAAmB,MAAMH,UAAU,CAACO,QAAX,EAAzB,CAAR;MACH,CAHO,CAAR;IAIH,CALgC,EAK9BC,GAAG,IAAI;MACNP,GAAG,CAACC,GAAJ,CAAQH,SAAS,CAACI,QAAV,CAAmB,MAAMH,UAAU,CAACS,KAAX,CAAiBD,GAAjB,CAAzB,CAAR;IACH,CAPgC,CAAzB,CAAR;IAQA,OAAOP,GAAP;EACH,CAXM,CAAP;AAYH"}, "metadata": {}, "sourceType": "module"}