{"ast": null, "code": "import { Subject } from '../Subject';\nimport { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function window(windowBoundaries) {\n  return function windowOperatorFunction(source) {\n    return source.lift(new WindowOperator(windowBoundaries));\n  };\n}\n\nclass WindowOperator {\n  constructor(windowBoundaries) {\n    this.windowBoundaries = windowBoundaries;\n  }\n\n  call(subscriber, source) {\n    const windowSubscriber = new WindowSubscriber(subscriber);\n    const sourceSubscription = source.subscribe(windowSubscriber);\n\n    if (!sourceSubscription.closed) {\n      windowSubscriber.add(innerSubscribe(this.windowBoundaries, new SimpleInnerSubscriber(windowSubscriber)));\n    }\n\n    return sourceSubscription;\n  }\n\n}\n\nclass WindowSubscriber extends SimpleOuterSubscriber {\n  constructor(destination) {\n    super(destination);\n    this.window = new Subject();\n    destination.next(this.window);\n  }\n\n  notifyNext() {\n    this.openWindow();\n  }\n\n  notifyError(error) {\n    this._error(error);\n  }\n\n  notifyComplete() {\n    this._complete();\n  }\n\n  _next(value) {\n    this.window.next(value);\n  }\n\n  _error(err) {\n    this.window.error(err);\n    this.destination.error(err);\n  }\n\n  _complete() {\n    this.window.complete();\n    this.destination.complete();\n  }\n\n  _unsubscribe() {\n    this.window = null;\n  }\n\n  openWindow() {\n    const prevWindow = this.window;\n\n    if (prevWindow) {\n      prevWindow.complete();\n    }\n\n    const destination = this.destination;\n    const newWindow = this.window = new Subject();\n    destination.next(newWindow);\n  }\n\n}", "map": {"version": 3, "names": ["Subject", "SimpleOuterSubscriber", "innerSubscribe", "SimpleInnerSubscriber", "window", "windowBoundaries", "windowOperatorFunction", "source", "lift", "WindowOperator", "constructor", "call", "subscriber", "windowSubscriber", "WindowSubscriber", "sourceSubscription", "subscribe", "closed", "add", "destination", "next", "notifyNext", "openWindow", "notifyError", "error", "_error", "notifyComplete", "_complete", "_next", "value", "err", "complete", "_unsubscribe", "prevWindow", "newWindow"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/window.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function window(windowBoundaries) {\n    return function windowOperatorFunction(source) {\n        return source.lift(new WindowOperator(windowBoundaries));\n    };\n}\nclass WindowOperator {\n    constructor(windowBoundaries) {\n        this.windowBoundaries = windowBoundaries;\n    }\n    call(subscriber, source) {\n        const windowSubscriber = new WindowSubscriber(subscriber);\n        const sourceSubscription = source.subscribe(windowSubscriber);\n        if (!sourceSubscription.closed) {\n            windowSubscriber.add(innerSubscribe(this.windowBoundaries, new SimpleInnerSubscriber(windowSubscriber)));\n        }\n        return sourceSubscription;\n    }\n}\nclass WindowSubscriber extends SimpleOuterSubscriber {\n    constructor(destination) {\n        super(destination);\n        this.window = new Subject();\n        destination.next(this.window);\n    }\n    notifyNext() {\n        this.openWindow();\n    }\n    notifyError(error) {\n        this._error(error);\n    }\n    notifyComplete() {\n        this._complete();\n    }\n    _next(value) {\n        this.window.next(value);\n    }\n    _error(err) {\n        this.window.error(err);\n        this.destination.error(err);\n    }\n    _complete() {\n        this.window.complete();\n        this.destination.complete();\n    }\n    _unsubscribe() {\n        this.window = null;\n    }\n    openWindow() {\n        const prevWindow = this.window;\n        if (prevWindow) {\n            prevWindow.complete();\n        }\n        const destination = this.destination;\n        const newWindow = this.window = new Subject();\n        destination.next(newWindow);\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,YAAxB;AACA,SAASC,qBAAT,EAAgCC,cAAhC,EAAgDC,qBAAhD,QAA6E,mBAA7E;AACA,OAAO,SAASC,MAAT,CAAgBC,gBAAhB,EAAkC;EACrC,OAAO,SAASC,sBAAT,CAAgCC,MAAhC,EAAwC;IAC3C,OAAOA,MAAM,CAACC,IAAP,CAAY,IAAIC,cAAJ,CAAmBJ,gBAAnB,CAAZ,CAAP;EACH,CAFD;AAGH;;AACD,MAAMI,cAAN,CAAqB;EACjBC,WAAW,CAACL,gBAAD,EAAmB;IAC1B,KAAKA,gBAAL,GAAwBA,gBAAxB;EACH;;EACDM,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,MAAMM,gBAAgB,GAAG,IAAIC,gBAAJ,CAAqBF,UAArB,CAAzB;IACA,MAAMG,kBAAkB,GAAGR,MAAM,CAACS,SAAP,CAAiBH,gBAAjB,CAA3B;;IACA,IAAI,CAACE,kBAAkB,CAACE,MAAxB,EAAgC;MAC5BJ,gBAAgB,CAACK,GAAjB,CAAqBhB,cAAc,CAAC,KAAKG,gBAAN,EAAwB,IAAIF,qBAAJ,CAA0BU,gBAA1B,CAAxB,CAAnC;IACH;;IACD,OAAOE,kBAAP;EACH;;AAXgB;;AAarB,MAAMD,gBAAN,SAA+Bb,qBAA/B,CAAqD;EACjDS,WAAW,CAACS,WAAD,EAAc;IACrB,MAAMA,WAAN;IACA,KAAKf,MAAL,GAAc,IAAIJ,OAAJ,EAAd;IACAmB,WAAW,CAACC,IAAZ,CAAiB,KAAKhB,MAAtB;EACH;;EACDiB,UAAU,GAAG;IACT,KAAKC,UAAL;EACH;;EACDC,WAAW,CAACC,KAAD,EAAQ;IACf,KAAKC,MAAL,CAAYD,KAAZ;EACH;;EACDE,cAAc,GAAG;IACb,KAAKC,SAAL;EACH;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACT,KAAKzB,MAAL,CAAYgB,IAAZ,CAAiBS,KAAjB;EACH;;EACDJ,MAAM,CAACK,GAAD,EAAM;IACR,KAAK1B,MAAL,CAAYoB,KAAZ,CAAkBM,GAAlB;IACA,KAAKX,WAAL,CAAiBK,KAAjB,CAAuBM,GAAvB;EACH;;EACDH,SAAS,GAAG;IACR,KAAKvB,MAAL,CAAY2B,QAAZ;IACA,KAAKZ,WAAL,CAAiBY,QAAjB;EACH;;EACDC,YAAY,GAAG;IACX,KAAK5B,MAAL,GAAc,IAAd;EACH;;EACDkB,UAAU,GAAG;IACT,MAAMW,UAAU,GAAG,KAAK7B,MAAxB;;IACA,IAAI6B,UAAJ,EAAgB;MACZA,UAAU,CAACF,QAAX;IACH;;IACD,MAAMZ,WAAW,GAAG,KAAKA,WAAzB;IACA,MAAMe,SAAS,GAAG,KAAK9B,MAAL,GAAc,IAAIJ,OAAJ,EAAhC;IACAmB,WAAW,CAACC,IAAZ,CAAiBc,SAAjB;EACH;;AArCgD"}, "metadata": {}, "sourceType": "module"}