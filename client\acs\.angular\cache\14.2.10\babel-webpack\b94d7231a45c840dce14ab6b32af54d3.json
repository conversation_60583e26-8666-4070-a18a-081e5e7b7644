{"ast": null, "code": "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "map": {"version": 3, "names": ["expandToHashMap", "value", "keys", "reduce", "hashMap", "key"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/utils/expandToHashMap.js"], "sourcesContent": ["export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}"], "mappings": "AAAA,eAAe,SAASA,eAAT,CAAyBC,KAAzB,EAAgCC,IAAhC,EAAsC;EACnD,OAAOA,IAAI,CAACC,MAAL,CAAY,UAAUC,OAAV,EAAmBC,GAAnB,EAAwB;IACzCD,OAAO,CAACC,GAAD,CAAP,GAAeJ,KAAf;IACA,OAAOG,OAAP;EACD,CAHM,EAGJ,EAHI,CAAP;AAID"}, "metadata": {}, "sourceType": "module"}