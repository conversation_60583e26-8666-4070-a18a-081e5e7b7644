{"ast": null, "code": "import { Observable } from '../Observable';\nexport function isObservable(obj) {\n  return !!obj && (obj instanceof Observable || typeof obj.lift === 'function' && typeof obj.subscribe === 'function');\n}", "map": {"version": 3, "names": ["Observable", "isObservable", "obj", "lift", "subscribe"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/isObservable.js"], "sourcesContent": ["import { Observable } from '../Observable';\nexport function isObservable(obj) {\n    return !!obj && (obj instanceof Observable || (typeof obj.lift === 'function' && typeof obj.subscribe === 'function'));\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,YAAT,CAAsBC,GAAtB,EAA2B;EAC9B,OAAO,CAAC,CAACA,GAAF,KAAUA,GAAG,YAAYF,UAAf,IAA8B,OAAOE,GAAG,CAACC,IAAX,KAAoB,UAApB,IAAkC,OAAOD,GAAG,CAACE,SAAX,KAAyB,UAAnG,CAAP;AACH"}, "metadata": {}, "sourceType": "module"}