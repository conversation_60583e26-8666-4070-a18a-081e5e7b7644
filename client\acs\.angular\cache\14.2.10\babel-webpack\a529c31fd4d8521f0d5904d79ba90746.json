{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function canReportError(observer) {\n  while (observer) {\n    const {\n      closed,\n      destination,\n      isStopped\n    } = observer;\n\n    if (closed || isStopped) {\n      return false;\n    } else if (destination && destination instanceof Subscriber) {\n      observer = destination;\n    } else {\n      observer = null;\n    }\n  }\n\n  return true;\n}", "map": {"version": 3, "names": ["Subscriber", "canReportError", "observer", "closed", "destination", "isStopped"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/canReportError.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function canReportError(observer) {\n    while (observer) {\n        const { closed, destination, isStopped } = observer;\n        if (closed || isStopped) {\n            return false;\n        }\n        else if (destination && destination instanceof Subscriber) {\n            observer = destination;\n        }\n        else {\n            observer = null;\n        }\n    }\n    return true;\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,cAAT,CAAwBC,QAAxB,EAAkC;EACrC,OAAOA,QAAP,EAAiB;IACb,MAAM;MAAEC,MAAF;MAAUC,WAAV;MAAuBC;IAAvB,IAAqCH,QAA3C;;IACA,IAAIC,MAAM,IAAIE,SAAd,EAAyB;MACrB,OAAO,KAAP;IACH,CAFD,MAGK,IAAID,WAAW,IAAIA,WAAW,YAAYJ,UAA1C,EAAsD;MACvDE,QAAQ,GAAGE,WAAX;IACH,CAFI,MAGA;MACDF,QAAQ,GAAG,IAAX;IACH;EACJ;;EACD,OAAO,IAAP;AACH"}, "metadata": {}, "sourceType": "module"}