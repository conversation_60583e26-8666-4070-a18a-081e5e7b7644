{"ast": null, "code": "import { observable as Symbol_observable } from '../symbol/observable';\nexport function isInteropObservable(input) {\n  return input && typeof input[Symbol_observable] === 'function';\n}", "map": {"version": 3, "names": ["observable", "Symbol_observable", "isInteropObservable", "input"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/isInteropObservable.js"], "sourcesContent": ["import { observable as Symbol_observable } from '../symbol/observable';\nexport function isInteropObservable(input) {\n    return input && typeof input[Symbol_observable] === 'function';\n}\n"], "mappings": "AAAA,SAASA,UAAU,IAAIC,iBAAvB,QAAgD,sBAAhD;AACA,OAAO,SAASC,mBAAT,CAA6BC,KAA7B,EAAoC;EACvC,OAAOA,KAAK,IAAI,OAAOA,KAAK,CAACF,iBAAD,CAAZ,KAAoC,UAApD;AACH"}, "metadata": {}, "sourceType": "module"}