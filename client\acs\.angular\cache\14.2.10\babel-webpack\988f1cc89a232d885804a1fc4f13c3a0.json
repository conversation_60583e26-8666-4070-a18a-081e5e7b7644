{"ast": null, "code": "/**\n * @license Angular v14.3.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\nimport { computeMsgId } from '@angular/compiler';\nexport { computeMsgId as ɵcomputeMsgId } from '@angular/compiler';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * The character used to mark the start and end of a \"block\" in a `$localize` tagged string.\n * A block can indicate metadata about the message or specify a name of a placeholder for a\n * substitution expressions.\n *\n * For example:\n *\n * ```ts\n * $localize`Hello, ${title}:title:!`;\n * $localize`:meaning|description@@id:source message text`;\n * ```\n */\n\nconst BLOCK_MARKER$1 = ':';\n/**\n * The marker used to separate a message's \"meaning\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:correct|Indicates that the user got the answer correct: Right!`;\n * $localize `:movement|Button label for moving to the right: Right!`;\n * ```\n */\n\nconst MEANING_SEPARATOR = '|';\n/**\n * The marker used to separate a message's custom \"id\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:A welcome message on the home page@@myApp-homepage-welcome: Welcome!`;\n * ```\n */\n\nconst ID_SEPARATOR = '@@';\n/**\n * The marker used to separate legacy message ids from the rest of a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:@@custom-id␟2df64767cd895a8fabe3e18b94b5b6b6f9e2e3f0: Welcome!`;\n * ```\n *\n * Note that this character is the \"symbol for the unit separator\" (␟) not the \"unit separator\n * character\" itself, since that has no visual representation. See https://graphemica.com/%E2%90%9F.\n *\n * Here is some background for the original \"unit separator character\":\n * https://stackoverflow.com/questions/8695118/whats-the-file-group-record-unit-separator-control-characters-and-its-usage\n */\n\nconst LEGACY_ID_INDICATOR = '\\u241F';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Parse a `$localize` tagged string into a structure that can be used for translation or\n * extraction.\n *\n * See `ParsedMessage` for an example.\n */\n\nfunction parseMessage(messageParts, expressions, location, messagePartLocations, expressionLocations = []) {\n  const substitutions = {};\n  const substitutionLocations = {};\n  const associatedMessageIds = {};\n  const metadata = parseMetadata(messageParts[0], messageParts.raw[0]);\n  const cleanedMessageParts = [metadata.text];\n  const placeholderNames = [];\n  let messageString = metadata.text;\n\n  for (let i = 1; i < messageParts.length; i++) {\n    const {\n      messagePart,\n      placeholderName = computePlaceholderName(i),\n      associatedMessageId\n    } = parsePlaceholder(messageParts[i], messageParts.raw[i]);\n    messageString += `{$${placeholderName}}${messagePart}`;\n\n    if (expressions !== undefined) {\n      substitutions[placeholderName] = expressions[i - 1];\n      substitutionLocations[placeholderName] = expressionLocations[i - 1];\n    }\n\n    placeholderNames.push(placeholderName);\n\n    if (associatedMessageId !== undefined) {\n      associatedMessageIds[placeholderName] = associatedMessageId;\n    }\n\n    cleanedMessageParts.push(messagePart);\n  }\n\n  const messageId = metadata.customId || computeMsgId(messageString, metadata.meaning || '');\n  const legacyIds = metadata.legacyIds ? metadata.legacyIds.filter(id => id !== messageId) : [];\n  return {\n    id: messageId,\n    legacyIds,\n    substitutions,\n    substitutionLocations,\n    text: messageString,\n    customId: metadata.customId,\n    meaning: metadata.meaning || '',\n    description: metadata.description || '',\n    messageParts: cleanedMessageParts,\n    messagePartLocations,\n    placeholderNames,\n    associatedMessageIds,\n    location\n  };\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract the message metadata from the text.\n *\n * If the message part has a metadata block this function will extract the `meaning`,\n * `description`, `customId` and `legacyId` (if provided) from the block. These metadata properties\n * are serialized in the string delimited by `|`, `@@` and `␟` respectively.\n *\n * (Note that `␟` is the `LEGACY_ID_INDICATOR` - see `constants.ts`.)\n *\n * For example:\n *\n * ```ts\n * `:meaning|description@@custom-id:`\n * `:meaning|@@custom-id:`\n * `:meaning|description:`\n * `:description@@custom-id:`\n * `:meaning|:`\n * `:description:`\n * `:@@custom-id:`\n * `:meaning|description@@custom-id␟legacy-id-1␟legacy-id-2:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing any metadata that was parsed from the message part.\n */\n\n\nfunction parseMetadata(cooked, raw) {\n  const {\n    text: messageString,\n    block\n  } = splitBlock(cooked, raw);\n\n  if (block === undefined) {\n    return {\n      text: messageString\n    };\n  } else {\n    const [meaningDescAndId, ...legacyIds] = block.split(LEGACY_ID_INDICATOR);\n    const [meaningAndDesc, customId] = meaningDescAndId.split(ID_SEPARATOR, 2);\n    let [meaning, description] = meaningAndDesc.split(MEANING_SEPARATOR, 2);\n\n    if (description === undefined) {\n      description = meaning;\n      meaning = undefined;\n    }\n\n    if (description === '') {\n      description = undefined;\n    }\n\n    return {\n      text: messageString,\n      meaning,\n      description,\n      customId,\n      legacyIds\n    };\n  }\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract any placeholder metadata from the\n * text.\n *\n * If the message part has a metadata block this function will extract the `placeholderName` and\n * `associatedMessageId` (if provided) from the block.\n *\n * These metadata properties are serialized in the string delimited by `@@`.\n *\n * For example:\n *\n * ```ts\n * `:placeholder-name@@associated-id:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing the metadata (`placeholderName` and `associatedMessageId`) of the\n *     preceding placeholder, along with the static text that follows.\n */\n\n\nfunction parsePlaceholder(cooked, raw) {\n  const {\n    text: messagePart,\n    block\n  } = splitBlock(cooked, raw);\n\n  if (block === undefined) {\n    return {\n      messagePart\n    };\n  } else {\n    const [placeholderName, associatedMessageId] = block.split(ID_SEPARATOR);\n    return {\n      messagePart,\n      placeholderName,\n      associatedMessageId\n    };\n  }\n}\n/**\n * Split a message part (`cooked` + `raw`) into an optional delimited \"block\" off the front and the\n * rest of the text of the message part.\n *\n * Blocks appear at the start of message parts. They are delimited by a colon `:` character at the\n * start and end of the block.\n *\n * If the block is in the first message part then it will be metadata about the whole message:\n * meaning, description, id.  Otherwise it will be metadata about the immediately preceding\n * substitution: placeholder name.\n *\n * Since blocks are optional, it is possible that the content of a message block actually starts\n * with a block marker. In this case the marker must be escaped `\\:`.\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns An object containing the `text` of the message part and the text of the `block`, if it\n * exists.\n * @throws an error if the `block` is unterminated\n */\n\n\nfunction splitBlock(cooked, raw) {\n  if (raw.charAt(0) !== BLOCK_MARKER$1) {\n    return {\n      text: cooked\n    };\n  } else {\n    const endOfBlock = findEndOfBlock(cooked, raw);\n    return {\n      block: cooked.substring(1, endOfBlock),\n      text: cooked.substring(endOfBlock + 1)\n    };\n  }\n}\n\nfunction computePlaceholderName(index) {\n  return index === 1 ? 'PH' : `PH_${index - 1}`;\n}\n/**\n * Find the end of a \"marked block\" indicated by the first non-escaped colon.\n *\n * @param cooked The cooked string (where escaped chars have been processed)\n * @param raw The raw string (where escape sequences are still in place)\n *\n * @returns the index of the end of block marker\n * @throws an error if the block is unterminated\n */\n\n\nfunction findEndOfBlock(cooked, raw) {\n  for (let cookedIndex = 1, rawIndex = 1; cookedIndex < cooked.length; cookedIndex++, rawIndex++) {\n    if (raw[rawIndex] === '\\\\') {\n      rawIndex++;\n    } else if (cooked[cookedIndex] === BLOCK_MARKER$1) {\n      return cookedIndex;\n    }\n  }\n\n  throw new Error(`Unterminated $localize metadata block in \"${raw}\".`);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MissingTranslationError extends Error {\n  constructor(parsedMessage) {\n    super(`No translation found for ${describeMessage(parsedMessage)}.`);\n    this.parsedMessage = parsedMessage;\n    this.type = 'MissingTranslationError';\n  }\n\n}\n\nfunction isMissingTranslationError(e) {\n  return e.type === 'MissingTranslationError';\n}\n/**\n * Translate the text of the `$localize` tagged-string (i.e. `messageParts` and\n * `substitutions`) using the given `translations`.\n *\n * The tagged-string is parsed to extract its `messageId` which is used to find an appropriate\n * `ParsedTranslation`. If this doesn't match and there are legacy ids then try matching a\n * translation using those.\n *\n * If one is found then it is used to translate the message into a new set of `messageParts` and\n * `substitutions`.\n * The translation may reorder (or remove) substitutions as appropriate.\n *\n * If there is no translation with a matching message id then an error is thrown.\n * If a translation contains a placeholder that is not found in the message being translated then an\n * error is thrown.\n */\n\n\nfunction translate$1(translations, messageParts, substitutions) {\n  const message = parseMessage(messageParts, substitutions); // Look up the translation using the messageId, and then the legacyId if available.\n\n  let translation = translations[message.id]; // If the messageId did not match a translation, try matching the legacy ids instead\n\n  if (message.legacyIds !== undefined) {\n    for (let i = 0; i < message.legacyIds.length && translation === undefined; i++) {\n      translation = translations[message.legacyIds[i]];\n    }\n  }\n\n  if (translation === undefined) {\n    throw new MissingTranslationError(message);\n  }\n\n  return [translation.messageParts, translation.placeholderNames.map(placeholder => {\n    if (message.substitutions.hasOwnProperty(placeholder)) {\n      return message.substitutions[placeholder];\n    } else {\n      throw new Error(`There is a placeholder name mismatch with the translation provided for the message ${describeMessage(message)}.\\n` + `The translation contains a placeholder with name ${placeholder}, which does not exist in the message.`);\n    }\n  })];\n}\n/**\n * Parse the `messageParts` and `placeholderNames` out of a target `message`.\n *\n * Used by `loadTranslations()` to convert target message strings into a structure that is more\n * appropriate for doing translation.\n *\n * @param message the message to be parsed.\n */\n\n\nfunction parseTranslation(messageString) {\n  const parts = messageString.split(/{\\$([^}]*)}/);\n  const messageParts = [parts[0]];\n  const placeholderNames = [];\n\n  for (let i = 1; i < parts.length - 1; i += 2) {\n    placeholderNames.push(parts[i]);\n    messageParts.push(`${parts[i + 1]}`);\n  }\n\n  const rawMessageParts = messageParts.map(part => part.charAt(0) === BLOCK_MARKER$1 ? '\\\\' + part : part);\n  return {\n    text: messageString,\n    messageParts: makeTemplateObject(messageParts, rawMessageParts),\n    placeholderNames\n  };\n}\n/**\n * Create a `ParsedTranslation` from a set of `messageParts` and `placeholderNames`.\n *\n * @param messageParts The message parts to appear in the ParsedTranslation.\n * @param placeholderNames The names of the placeholders to intersperse between the `messageParts`.\n */\n\n\nfunction makeParsedTranslation(messageParts, placeholderNames = []) {\n  let messageString = messageParts[0];\n\n  for (let i = 0; i < placeholderNames.length; i++) {\n    messageString += `{$${placeholderNames[i]}}${messageParts[i + 1]}`;\n  }\n\n  return {\n    text: messageString,\n    messageParts: makeTemplateObject(messageParts, messageParts),\n    placeholderNames\n  };\n}\n/**\n * Create the specialized array that is passed to tagged-string tag functions.\n *\n * @param cooked The message parts with their escape codes processed.\n * @param raw The message parts with their escaped codes as-is.\n */\n\n\nfunction makeTemplateObject(cooked, raw) {\n  Object.defineProperty(cooked, 'raw', {\n    value: raw\n  });\n  return cooked;\n}\n\nfunction describeMessage(message) {\n  const meaningString = message.meaning && ` - \"${message.meaning}\"`;\n  const legacy = message.legacyIds && message.legacyIds.length > 0 ? ` [${message.legacyIds.map(l => `\"${l}\"`).join(', ')}]` : '';\n  return `\"${message.id}\"${legacy} (\"${message.text}\"${meaningString})`;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Load translations for use by `$localize`, if doing runtime translation.\n *\n * If the `$localize` tagged strings are not going to be replaced at compiled time, it is possible\n * to load a set of translations that will be applied to the `$localize` tagged strings at runtime,\n * in the browser.\n *\n * Loading a new translation will overwrite a previous translation if it has the same `MessageId`.\n *\n * Note that `$localize` messages are only processed once, when the tagged string is first\n * encountered, and does not provide dynamic language changing without refreshing the browser.\n * Loading new translations later in the application life-cycle will not change the translated text\n * of messages that have already been translated.\n *\n * The message IDs and translations are in the same format as that rendered to \"simple JSON\"\n * translation files when extracting messages. In particular, placeholders in messages are rendered\n * using the `{$PLACEHOLDER_NAME}` syntax. For example the message from the following template:\n *\n * ```html\n * <div i18n>pre<span>inner-pre<b>bold</b>inner-post</span>post</div>\n * ```\n *\n * would have the following form in the `translations` map:\n *\n * ```ts\n * {\n *   \"2932901491976224757\":\n *      \"pre{$START_TAG_SPAN}inner-pre{$START_BOLD_TEXT}bold{$CLOSE_BOLD_TEXT}inner-post{$CLOSE_TAG_SPAN}post\"\n * }\n * ```\n *\n * @param translations A map from message ID to translated message.\n *\n * These messages are processed and added to a lookup based on their `MessageId`.\n *\n * @see `clearTranslations()` for removing translations loaded using this function.\n * @see `$localize` for tagging messages as needing to be translated.\n * @publicApi\n */\n\n\nfunction loadTranslations(translations) {\n  // Ensure the translate function exists\n  if (!$localize.translate) {\n    $localize.translate = translate;\n  }\n\n  if (!$localize.TRANSLATIONS) {\n    $localize.TRANSLATIONS = {};\n  }\n\n  Object.keys(translations).forEach(key => {\n    $localize.TRANSLATIONS[key] = parseTranslation(translations[key]);\n  });\n}\n/**\n * Remove all translations for `$localize`, if doing runtime translation.\n *\n * All translations that had been loading into memory using `loadTranslations()` will be removed.\n *\n * @see `loadTranslations()` for loading translations at runtime.\n * @see `$localize` for tagging messages as needing to be translated.\n *\n * @publicApi\n */\n\n\nfunction clearTranslations() {\n  $localize.translate = undefined;\n  $localize.TRANSLATIONS = {};\n}\n/**\n * Translate the text of the given message, using the loaded translations.\n *\n * This function may reorder (or remove) substitutions as indicated in the matching translation.\n */\n\n\nfunction translate(messageParts, substitutions) {\n  try {\n    return translate$1($localize.TRANSLATIONS, messageParts, substitutions);\n  } catch (e) {\n    console.warn(e.message);\n    return [messageParts, substitutions];\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Always use __globalThis if available, which is the spec-defined global variable across all\n// environments, then fallback to __global first, because in Node tests both __global and\n// __window may be defined and _global should be __global in that case. Note: Typeof/Instanceof\n// checks are considered side-effects in Terser. We explicitly mark this as side-effect free:\n// https://github.com/terser/terser/issues/250.\n\n\nconst _global = /* @__PURE__ */(() => typeof globalThis !== 'undefined' && globalThis || typeof global !== 'undefined' && global || typeof window !== 'undefined' && window || typeof self !== 'undefined' && typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope && self)();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Tag a template literal string for localization.\n *\n * For example:\n *\n * ```ts\n * $localize `some string to localize`\n * ```\n *\n * **Providing meaning, description and id**\n *\n * You can optionally specify one or more of `meaning`, `description` and `id` for a localized\n * string by pre-pending it with a colon delimited block of the form:\n *\n * ```ts\n * $localize`:meaning|description@@id:source message text`;\n *\n * $localize`:meaning|:source message text`;\n * $localize`:description:source message text`;\n * $localize`:@@id:source message text`;\n * ```\n *\n * This format is the same as that used for `i18n` markers in Angular templates. See the\n * [Angular i18n guide](guide/i18n-common-prepare#mark-text-in-component-template).\n *\n * **Naming placeholders**\n *\n * If the template literal string contains expressions, then the expressions will be automatically\n * associated with placeholder names for you.\n *\n * For example:\n *\n * ```ts\n * $localize `Hi ${name}! There are ${items.length} items.`;\n * ```\n *\n * will generate a message-source of `Hi {$PH}! There are {$PH_1} items`.\n *\n * The recommended practice is to name the placeholder associated with each expression though.\n *\n * Do this by providing the placeholder name wrapped in `:` characters directly after the\n * expression. These placeholder names are stripped out of the rendered localized string.\n *\n * For example, to name the `items.length` expression placeholder `itemCount` you write:\n *\n * ```ts\n * $localize `There are ${items.length}:itemCount: items`;\n * ```\n *\n * **Escaping colon markers**\n *\n * If you need to use a `:` character directly at the start of a tagged string that has no\n * metadata block, or directly after a substitution expression that has no name you must escape\n * the `:` by preceding it with a backslash:\n *\n * For example:\n *\n * ```ts\n * // message has a metadata block so no need to escape colon\n * $localize `:some description::this message starts with a colon (:)`;\n * // no metadata block so the colon must be escaped\n * $localize `\\:this message starts with a colon (:)`;\n * ```\n *\n * ```ts\n * // named substitution so no need to escape colon\n * $localize `${label}:label:: ${}`\n * // anonymous substitution so colon must be escaped\n * $localize `${label}\\: ${}`\n * ```\n *\n * **Processing localized strings:**\n *\n * There are three scenarios:\n *\n * * **compile-time inlining**: the `$localize` tag is transformed at compile time by a\n * transpiler, removing the tag and replacing the template literal string with a translated\n * literal string from a collection of translations provided to the transpilation tool.\n *\n * * **run-time evaluation**: the `$localize` tag is a run-time function that replaces and\n * reorders the parts (static strings and expressions) of the template literal string with strings\n * from a collection of translations loaded at run-time.\n *\n * * **pass-through evaluation**: the `$localize` tag is a run-time function that simply evaluates\n * the original template literal string without applying any translations to the parts. This\n * version is used during development or where there is no need to translate the localized\n * template literals.\n *\n * @param messageParts a collection of the static parts of the template string.\n * @param expressions a collection of the values of each placeholder in the template string.\n * @returns the translated string, with the `messageParts` and `expressions` interleaved together.\n *\n * @globalApi\n * @publicApi\n */\n\n\nconst $localize$1 = function (messageParts, ...expressions) {\n  if ($localize$1.translate) {\n    // Don't use array expansion here to avoid the compiler adding `__read()` helper unnecessarily.\n    const translation = $localize$1.translate(messageParts, expressions);\n    messageParts = translation[0];\n    expressions = translation[1];\n  }\n\n  let message = stripBlock(messageParts[0], messageParts.raw[0]);\n\n  for (let i = 1; i < messageParts.length; i++) {\n    message += expressions[i - 1] + stripBlock(messageParts[i], messageParts.raw[i]);\n  }\n\n  return message;\n};\n\nconst BLOCK_MARKER = ':';\n/**\n * Strip a delimited \"block\" from the start of the `messagePart`, if it is found.\n *\n * If a marker character (:) actually appears in the content at the start of a tagged string or\n * after a substitution expression, where a block has not been provided the character must be\n * escaped with a backslash, `\\:`. This function checks for this by looking at the `raw`\n * messagePart, which should still contain the backslash.\n *\n * @param messagePart The cooked message part to process.\n * @param rawMessagePart The raw message part to check.\n * @returns the message part with the placeholder name stripped, if found.\n * @throws an error if the block is unterminated\n */\n\nfunction stripBlock(messagePart, rawMessagePart) {\n  return rawMessagePart.charAt(0) === BLOCK_MARKER ? messagePart.substring(findEndOfBlock(messagePart, rawMessagePart) + 1) : messagePart;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nexport { clearTranslations, loadTranslations, $localize$1 as ɵ$localize, MissingTranslationError as ɵMissingTranslationError, _global as ɵ_global, findEndOfBlock as ɵfindEndOfBlock, isMissingTranslationError as ɵisMissingTranslationError, makeParsedTranslation as ɵmakeParsedTranslation, makeTemplateObject as ɵmakeTemplateObject, parseMessage as ɵparseMessage, parseMetadata as ɵparseMetadata, parseTranslation as ɵparseTranslation, splitBlock as ɵsplitBlock, translate$1 as ɵtranslate };", "map": {"version": 3, "names": ["computeMsgId", "ɵcomputeMsgId", "BLOCK_MARKER$1", "MEANING_SEPARATOR", "ID_SEPARATOR", "LEGACY_ID_INDICATOR", "parseMessage", "messageParts", "expressions", "location", "messagePartLocations", "expressionLocations", "substitutions", "substitutionLocations", "associatedMessageIds", "metadata", "parseMetadata", "raw", "cleanedMessageParts", "text", "placeholder<PERSON><PERSON><PERSON>", "messageString", "i", "length", "messagePart", "placeholder<PERSON><PERSON>", "computePlaceholderName", "associatedMessageId", "parsePlaceholder", "undefined", "push", "messageId", "customId", "meaning", "legacyIds", "filter", "id", "description", "cooked", "block", "splitBlock", "meaningDescAndId", "split", "meaningAndDesc", "char<PERSON>t", "endOfBlock", "findEndOfBlock", "substring", "index", "cookedIndex", "rawIndex", "Error", "MissingTranslationError", "constructor", "parsedMessage", "describeMessage", "type", "isMissingTranslationError", "e", "translate$1", "translations", "message", "translation", "map", "placeholder", "hasOwnProperty", "parseTranslation", "parts", "rawMessageParts", "part", "makeTemplateObject", "makeParsedTranslation", "Object", "defineProperty", "value", "meaningString", "legacy", "l", "join", "loadTranslations", "$localize", "translate", "TRANSLATIONS", "keys", "for<PERSON>ach", "key", "clearTranslations", "console", "warn", "_global", "globalThis", "global", "window", "self", "WorkerGlobalScope", "$localize$1", "stripBlock", "BLOCK_MARKER", "rawMessagePart", "ɵ$localize", "ɵMissingTranslationError", "ɵ_global", "ɵfindEndOfBlock", "ɵisMissingTranslationError", "ɵmakeParsedTranslation", "ɵmakeTemplateObject", "ɵparseMessage", "ɵparseMetadata", "ɵparseTranslation", "ɵsplitBlock", "ɵtranslate"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/localize/fesm2020/localize.mjs"], "sourcesContent": ["/**\n * @license Angular v14.3.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { computeMsgId } from '@angular/compiler';\nexport { computeMsgId as ɵcomputeMsgId } from '@angular/compiler';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * The character used to mark the start and end of a \"block\" in a `$localize` tagged string.\n * A block can indicate metadata about the message or specify a name of a placeholder for a\n * substitution expressions.\n *\n * For example:\n *\n * ```ts\n * $localize`Hello, ${title}:title:!`;\n * $localize`:meaning|description@@id:source message text`;\n * ```\n */\nconst BLOCK_MARKER$1 = ':';\n/**\n * The marker used to separate a message's \"meaning\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:correct|Indicates that the user got the answer correct: Right!`;\n * $localize `:movement|Button label for moving to the right: Right!`;\n * ```\n */\nconst MEANING_SEPARATOR = '|';\n/**\n * The marker used to separate a message's custom \"id\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:A welcome message on the home page@@myApp-homepage-welcome: Welcome!`;\n * ```\n */\nconst ID_SEPARATOR = '@@';\n/**\n * The marker used to separate legacy message ids from the rest of a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:@@custom-id␟2df64767cd895a8fabe3e18b94b5b6b6f9e2e3f0: Welcome!`;\n * ```\n *\n * Note that this character is the \"symbol for the unit separator\" (␟) not the \"unit separator\n * character\" itself, since that has no visual representation. See https://graphemica.com/%E2%90%9F.\n *\n * Here is some background for the original \"unit separator character\":\n * https://stackoverflow.com/questions/8695118/whats-the-file-group-record-unit-separator-control-characters-and-its-usage\n */\nconst LEGACY_ID_INDICATOR = '\\u241F';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Parse a `$localize` tagged string into a structure that can be used for translation or\n * extraction.\n *\n * See `ParsedMessage` for an example.\n */\nfunction parseMessage(messageParts, expressions, location, messagePartLocations, expressionLocations = []) {\n    const substitutions = {};\n    const substitutionLocations = {};\n    const associatedMessageIds = {};\n    const metadata = parseMetadata(messageParts[0], messageParts.raw[0]);\n    const cleanedMessageParts = [metadata.text];\n    const placeholderNames = [];\n    let messageString = metadata.text;\n    for (let i = 1; i < messageParts.length; i++) {\n        const { messagePart, placeholderName = computePlaceholderName(i), associatedMessageId } = parsePlaceholder(messageParts[i], messageParts.raw[i]);\n        messageString += `{$${placeholderName}}${messagePart}`;\n        if (expressions !== undefined) {\n            substitutions[placeholderName] = expressions[i - 1];\n            substitutionLocations[placeholderName] = expressionLocations[i - 1];\n        }\n        placeholderNames.push(placeholderName);\n        if (associatedMessageId !== undefined) {\n            associatedMessageIds[placeholderName] = associatedMessageId;\n        }\n        cleanedMessageParts.push(messagePart);\n    }\n    const messageId = metadata.customId || computeMsgId(messageString, metadata.meaning || '');\n    const legacyIds = metadata.legacyIds ? metadata.legacyIds.filter(id => id !== messageId) : [];\n    return {\n        id: messageId,\n        legacyIds,\n        substitutions,\n        substitutionLocations,\n        text: messageString,\n        customId: metadata.customId,\n        meaning: metadata.meaning || '',\n        description: metadata.description || '',\n        messageParts: cleanedMessageParts,\n        messagePartLocations,\n        placeholderNames,\n        associatedMessageIds,\n        location,\n    };\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract the message metadata from the text.\n *\n * If the message part has a metadata block this function will extract the `meaning`,\n * `description`, `customId` and `legacyId` (if provided) from the block. These metadata properties\n * are serialized in the string delimited by `|`, `@@` and `␟` respectively.\n *\n * (Note that `␟` is the `LEGACY_ID_INDICATOR` - see `constants.ts`.)\n *\n * For example:\n *\n * ```ts\n * `:meaning|description@@custom-id:`\n * `:meaning|@@custom-id:`\n * `:meaning|description:`\n * `:description@@custom-id:`\n * `:meaning|:`\n * `:description:`\n * `:@@custom-id:`\n * `:meaning|description@@custom-id␟legacy-id-1␟legacy-id-2:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing any metadata that was parsed from the message part.\n */\nfunction parseMetadata(cooked, raw) {\n    const { text: messageString, block } = splitBlock(cooked, raw);\n    if (block === undefined) {\n        return { text: messageString };\n    }\n    else {\n        const [meaningDescAndId, ...legacyIds] = block.split(LEGACY_ID_INDICATOR);\n        const [meaningAndDesc, customId] = meaningDescAndId.split(ID_SEPARATOR, 2);\n        let [meaning, description] = meaningAndDesc.split(MEANING_SEPARATOR, 2);\n        if (description === undefined) {\n            description = meaning;\n            meaning = undefined;\n        }\n        if (description === '') {\n            description = undefined;\n        }\n        return { text: messageString, meaning, description, customId, legacyIds };\n    }\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract any placeholder metadata from the\n * text.\n *\n * If the message part has a metadata block this function will extract the `placeholderName` and\n * `associatedMessageId` (if provided) from the block.\n *\n * These metadata properties are serialized in the string delimited by `@@`.\n *\n * For example:\n *\n * ```ts\n * `:placeholder-name@@associated-id:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing the metadata (`placeholderName` and `associatedMessageId`) of the\n *     preceding placeholder, along with the static text that follows.\n */\nfunction parsePlaceholder(cooked, raw) {\n    const { text: messagePart, block } = splitBlock(cooked, raw);\n    if (block === undefined) {\n        return { messagePart };\n    }\n    else {\n        const [placeholderName, associatedMessageId] = block.split(ID_SEPARATOR);\n        return { messagePart, placeholderName, associatedMessageId };\n    }\n}\n/**\n * Split a message part (`cooked` + `raw`) into an optional delimited \"block\" off the front and the\n * rest of the text of the message part.\n *\n * Blocks appear at the start of message parts. They are delimited by a colon `:` character at the\n * start and end of the block.\n *\n * If the block is in the first message part then it will be metadata about the whole message:\n * meaning, description, id.  Otherwise it will be metadata about the immediately preceding\n * substitution: placeholder name.\n *\n * Since blocks are optional, it is possible that the content of a message block actually starts\n * with a block marker. In this case the marker must be escaped `\\:`.\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns An object containing the `text` of the message part and the text of the `block`, if it\n * exists.\n * @throws an error if the `block` is unterminated\n */\nfunction splitBlock(cooked, raw) {\n    if (raw.charAt(0) !== BLOCK_MARKER$1) {\n        return { text: cooked };\n    }\n    else {\n        const endOfBlock = findEndOfBlock(cooked, raw);\n        return {\n            block: cooked.substring(1, endOfBlock),\n            text: cooked.substring(endOfBlock + 1),\n        };\n    }\n}\nfunction computePlaceholderName(index) {\n    return index === 1 ? 'PH' : `PH_${index - 1}`;\n}\n/**\n * Find the end of a \"marked block\" indicated by the first non-escaped colon.\n *\n * @param cooked The cooked string (where escaped chars have been processed)\n * @param raw The raw string (where escape sequences are still in place)\n *\n * @returns the index of the end of block marker\n * @throws an error if the block is unterminated\n */\nfunction findEndOfBlock(cooked, raw) {\n    for (let cookedIndex = 1, rawIndex = 1; cookedIndex < cooked.length; cookedIndex++, rawIndex++) {\n        if (raw[rawIndex] === '\\\\') {\n            rawIndex++;\n        }\n        else if (cooked[cookedIndex] === BLOCK_MARKER$1) {\n            return cookedIndex;\n        }\n    }\n    throw new Error(`Unterminated $localize metadata block in \"${raw}\".`);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MissingTranslationError extends Error {\n    constructor(parsedMessage) {\n        super(`No translation found for ${describeMessage(parsedMessage)}.`);\n        this.parsedMessage = parsedMessage;\n        this.type = 'MissingTranslationError';\n    }\n}\nfunction isMissingTranslationError(e) {\n    return e.type === 'MissingTranslationError';\n}\n/**\n * Translate the text of the `$localize` tagged-string (i.e. `messageParts` and\n * `substitutions`) using the given `translations`.\n *\n * The tagged-string is parsed to extract its `messageId` which is used to find an appropriate\n * `ParsedTranslation`. If this doesn't match and there are legacy ids then try matching a\n * translation using those.\n *\n * If one is found then it is used to translate the message into a new set of `messageParts` and\n * `substitutions`.\n * The translation may reorder (or remove) substitutions as appropriate.\n *\n * If there is no translation with a matching message id then an error is thrown.\n * If a translation contains a placeholder that is not found in the message being translated then an\n * error is thrown.\n */\nfunction translate$1(translations, messageParts, substitutions) {\n    const message = parseMessage(messageParts, substitutions);\n    // Look up the translation using the messageId, and then the legacyId if available.\n    let translation = translations[message.id];\n    // If the messageId did not match a translation, try matching the legacy ids instead\n    if (message.legacyIds !== undefined) {\n        for (let i = 0; i < message.legacyIds.length && translation === undefined; i++) {\n            translation = translations[message.legacyIds[i]];\n        }\n    }\n    if (translation === undefined) {\n        throw new MissingTranslationError(message);\n    }\n    return [\n        translation.messageParts, translation.placeholderNames.map(placeholder => {\n            if (message.substitutions.hasOwnProperty(placeholder)) {\n                return message.substitutions[placeholder];\n            }\n            else {\n                throw new Error(`There is a placeholder name mismatch with the translation provided for the message ${describeMessage(message)}.\\n` +\n                    `The translation contains a placeholder with name ${placeholder}, which does not exist in the message.`);\n            }\n        })\n    ];\n}\n/**\n * Parse the `messageParts` and `placeholderNames` out of a target `message`.\n *\n * Used by `loadTranslations()` to convert target message strings into a structure that is more\n * appropriate for doing translation.\n *\n * @param message the message to be parsed.\n */\nfunction parseTranslation(messageString) {\n    const parts = messageString.split(/{\\$([^}]*)}/);\n    const messageParts = [parts[0]];\n    const placeholderNames = [];\n    for (let i = 1; i < parts.length - 1; i += 2) {\n        placeholderNames.push(parts[i]);\n        messageParts.push(`${parts[i + 1]}`);\n    }\n    const rawMessageParts = messageParts.map(part => part.charAt(0) === BLOCK_MARKER$1 ? '\\\\' + part : part);\n    return {\n        text: messageString,\n        messageParts: makeTemplateObject(messageParts, rawMessageParts),\n        placeholderNames,\n    };\n}\n/**\n * Create a `ParsedTranslation` from a set of `messageParts` and `placeholderNames`.\n *\n * @param messageParts The message parts to appear in the ParsedTranslation.\n * @param placeholderNames The names of the placeholders to intersperse between the `messageParts`.\n */\nfunction makeParsedTranslation(messageParts, placeholderNames = []) {\n    let messageString = messageParts[0];\n    for (let i = 0; i < placeholderNames.length; i++) {\n        messageString += `{$${placeholderNames[i]}}${messageParts[i + 1]}`;\n    }\n    return {\n        text: messageString,\n        messageParts: makeTemplateObject(messageParts, messageParts),\n        placeholderNames\n    };\n}\n/**\n * Create the specialized array that is passed to tagged-string tag functions.\n *\n * @param cooked The message parts with their escape codes processed.\n * @param raw The message parts with their escaped codes as-is.\n */\nfunction makeTemplateObject(cooked, raw) {\n    Object.defineProperty(cooked, 'raw', { value: raw });\n    return cooked;\n}\nfunction describeMessage(message) {\n    const meaningString = message.meaning && ` - \"${message.meaning}\"`;\n    const legacy = message.legacyIds && message.legacyIds.length > 0 ?\n        ` [${message.legacyIds.map(l => `\"${l}\"`).join(', ')}]` :\n        '';\n    return `\"${message.id}\"${legacy} (\"${message.text}\"${meaningString})`;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Load translations for use by `$localize`, if doing runtime translation.\n *\n * If the `$localize` tagged strings are not going to be replaced at compiled time, it is possible\n * to load a set of translations that will be applied to the `$localize` tagged strings at runtime,\n * in the browser.\n *\n * Loading a new translation will overwrite a previous translation if it has the same `MessageId`.\n *\n * Note that `$localize` messages are only processed once, when the tagged string is first\n * encountered, and does not provide dynamic language changing without refreshing the browser.\n * Loading new translations later in the application life-cycle will not change the translated text\n * of messages that have already been translated.\n *\n * The message IDs and translations are in the same format as that rendered to \"simple JSON\"\n * translation files when extracting messages. In particular, placeholders in messages are rendered\n * using the `{$PLACEHOLDER_NAME}` syntax. For example the message from the following template:\n *\n * ```html\n * <div i18n>pre<span>inner-pre<b>bold</b>inner-post</span>post</div>\n * ```\n *\n * would have the following form in the `translations` map:\n *\n * ```ts\n * {\n *   \"2932901491976224757\":\n *      \"pre{$START_TAG_SPAN}inner-pre{$START_BOLD_TEXT}bold{$CLOSE_BOLD_TEXT}inner-post{$CLOSE_TAG_SPAN}post\"\n * }\n * ```\n *\n * @param translations A map from message ID to translated message.\n *\n * These messages are processed and added to a lookup based on their `MessageId`.\n *\n * @see `clearTranslations()` for removing translations loaded using this function.\n * @see `$localize` for tagging messages as needing to be translated.\n * @publicApi\n */\nfunction loadTranslations(translations) {\n    // Ensure the translate function exists\n    if (!$localize.translate) {\n        $localize.translate = translate;\n    }\n    if (!$localize.TRANSLATIONS) {\n        $localize.TRANSLATIONS = {};\n    }\n    Object.keys(translations).forEach(key => {\n        $localize.TRANSLATIONS[key] = parseTranslation(translations[key]);\n    });\n}\n/**\n * Remove all translations for `$localize`, if doing runtime translation.\n *\n * All translations that had been loading into memory using `loadTranslations()` will be removed.\n *\n * @see `loadTranslations()` for loading translations at runtime.\n * @see `$localize` for tagging messages as needing to be translated.\n *\n * @publicApi\n */\nfunction clearTranslations() {\n    $localize.translate = undefined;\n    $localize.TRANSLATIONS = {};\n}\n/**\n * Translate the text of the given message, using the loaded translations.\n *\n * This function may reorder (or remove) substitutions as indicated in the matching translation.\n */\nfunction translate(messageParts, substitutions) {\n    try {\n        return translate$1($localize.TRANSLATIONS, messageParts, substitutions);\n    }\n    catch (e) {\n        console.warn(e.message);\n        return [messageParts, substitutions];\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Always use __globalThis if available, which is the spec-defined global variable across all\n// environments, then fallback to __global first, because in Node tests both __global and\n// __window may be defined and _global should be __global in that case. Note: Typeof/Instanceof\n// checks are considered side-effects in Terser. We explicitly mark this as side-effect free:\n// https://github.com/terser/terser/issues/250.\nconst _global = ( /* @__PURE__ */(() => (typeof globalThis !== 'undefined' && globalThis) ||\n    (typeof global !== 'undefined' && global) || (typeof window !== 'undefined' && window) ||\n    (typeof self !== 'undefined' && typeof WorkerGlobalScope !== 'undefined' &&\n        self instanceof WorkerGlobalScope && self))());\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Tag a template literal string for localization.\n *\n * For example:\n *\n * ```ts\n * $localize `some string to localize`\n * ```\n *\n * **Providing meaning, description and id**\n *\n * You can optionally specify one or more of `meaning`, `description` and `id` for a localized\n * string by pre-pending it with a colon delimited block of the form:\n *\n * ```ts\n * $localize`:meaning|description@@id:source message text`;\n *\n * $localize`:meaning|:source message text`;\n * $localize`:description:source message text`;\n * $localize`:@@id:source message text`;\n * ```\n *\n * This format is the same as that used for `i18n` markers in Angular templates. See the\n * [Angular i18n guide](guide/i18n-common-prepare#mark-text-in-component-template).\n *\n * **Naming placeholders**\n *\n * If the template literal string contains expressions, then the expressions will be automatically\n * associated with placeholder names for you.\n *\n * For example:\n *\n * ```ts\n * $localize `Hi ${name}! There are ${items.length} items.`;\n * ```\n *\n * will generate a message-source of `Hi {$PH}! There are {$PH_1} items`.\n *\n * The recommended practice is to name the placeholder associated with each expression though.\n *\n * Do this by providing the placeholder name wrapped in `:` characters directly after the\n * expression. These placeholder names are stripped out of the rendered localized string.\n *\n * For example, to name the `items.length` expression placeholder `itemCount` you write:\n *\n * ```ts\n * $localize `There are ${items.length}:itemCount: items`;\n * ```\n *\n * **Escaping colon markers**\n *\n * If you need to use a `:` character directly at the start of a tagged string that has no\n * metadata block, or directly after a substitution expression that has no name you must escape\n * the `:` by preceding it with a backslash:\n *\n * For example:\n *\n * ```ts\n * // message has a metadata block so no need to escape colon\n * $localize `:some description::this message starts with a colon (:)`;\n * // no metadata block so the colon must be escaped\n * $localize `\\:this message starts with a colon (:)`;\n * ```\n *\n * ```ts\n * // named substitution so no need to escape colon\n * $localize `${label}:label:: ${}`\n * // anonymous substitution so colon must be escaped\n * $localize `${label}\\: ${}`\n * ```\n *\n * **Processing localized strings:**\n *\n * There are three scenarios:\n *\n * * **compile-time inlining**: the `$localize` tag is transformed at compile time by a\n * transpiler, removing the tag and replacing the template literal string with a translated\n * literal string from a collection of translations provided to the transpilation tool.\n *\n * * **run-time evaluation**: the `$localize` tag is a run-time function that replaces and\n * reorders the parts (static strings and expressions) of the template literal string with strings\n * from a collection of translations loaded at run-time.\n *\n * * **pass-through evaluation**: the `$localize` tag is a run-time function that simply evaluates\n * the original template literal string without applying any translations to the parts. This\n * version is used during development or where there is no need to translate the localized\n * template literals.\n *\n * @param messageParts a collection of the static parts of the template string.\n * @param expressions a collection of the values of each placeholder in the template string.\n * @returns the translated string, with the `messageParts` and `expressions` interleaved together.\n *\n * @globalApi\n * @publicApi\n */\nconst $localize$1 = function (messageParts, ...expressions) {\n    if ($localize$1.translate) {\n        // Don't use array expansion here to avoid the compiler adding `__read()` helper unnecessarily.\n        const translation = $localize$1.translate(messageParts, expressions);\n        messageParts = translation[0];\n        expressions = translation[1];\n    }\n    let message = stripBlock(messageParts[0], messageParts.raw[0]);\n    for (let i = 1; i < messageParts.length; i++) {\n        message += expressions[i - 1] + stripBlock(messageParts[i], messageParts.raw[i]);\n    }\n    return message;\n};\nconst BLOCK_MARKER = ':';\n/**\n * Strip a delimited \"block\" from the start of the `messagePart`, if it is found.\n *\n * If a marker character (:) actually appears in the content at the start of a tagged string or\n * after a substitution expression, where a block has not been provided the character must be\n * escaped with a backslash, `\\:`. This function checks for this by looking at the `raw`\n * messagePart, which should still contain the backslash.\n *\n * @param messagePart The cooked message part to process.\n * @param rawMessagePart The raw message part to check.\n * @returns the message part with the placeholder name stripped, if found.\n * @throws an error if the block is unterminated\n */\nfunction stripBlock(messagePart, rawMessagePart) {\n    return rawMessagePart.charAt(0) === BLOCK_MARKER ?\n        messagePart.substring(findEndOfBlock(messagePart, rawMessagePart) + 1) :\n        messagePart;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport { clearTranslations, loadTranslations, $localize$1 as ɵ$localize, MissingTranslationError as ɵMissingTranslationError, _global as ɵ_global, findEndOfBlock as ɵfindEndOfBlock, isMissingTranslationError as ɵisMissingTranslationError, makeParsedTranslation as ɵmakeParsedTranslation, makeTemplateObject as ɵmakeTemplateObject, parseMessage as ɵparseMessage, parseMetadata as ɵparseMetadata, parseTranslation as ɵparseTranslation, splitBlock as ɵsplitBlock, translate$1 as ɵtranslate };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AAEA,SAASA,YAAT,QAA6B,mBAA7B;AACA,SAASA,YAAY,IAAIC,aAAzB,QAA8C,mBAA9C;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,cAAc,GAAG,GAAvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,iBAAiB,GAAG,GAA1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,YAAY,GAAG,IAArB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,mBAAmB,GAAG,QAA5B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,YAAT,CAAsBC,YAAtB,EAAoCC,WAApC,EAAiDC,QAAjD,EAA2DC,oBAA3D,EAAiFC,mBAAmB,GAAG,EAAvG,EAA2G;EACvG,MAAMC,aAAa,GAAG,EAAtB;EACA,MAAMC,qBAAqB,GAAG,EAA9B;EACA,MAAMC,oBAAoB,GAAG,EAA7B;EACA,MAAMC,QAAQ,GAAGC,aAAa,CAACT,YAAY,CAAC,CAAD,CAAb,EAAkBA,YAAY,CAACU,GAAb,CAAiB,CAAjB,CAAlB,CAA9B;EACA,MAAMC,mBAAmB,GAAG,CAACH,QAAQ,CAACI,IAAV,CAA5B;EACA,MAAMC,gBAAgB,GAAG,EAAzB;EACA,IAAIC,aAAa,GAAGN,QAAQ,CAACI,IAA7B;;EACA,KAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGf,YAAY,CAACgB,MAAjC,EAAyCD,CAAC,EAA1C,EAA8C;IAC1C,MAAM;MAAEE,WAAF;MAAeC,eAAe,GAAGC,sBAAsB,CAACJ,CAAD,CAAvD;MAA4DK;IAA5D,IAAoFC,gBAAgB,CAACrB,YAAY,CAACe,CAAD,CAAb,EAAkBf,YAAY,CAACU,GAAb,CAAiBK,CAAjB,CAAlB,CAA1G;IACAD,aAAa,IAAK,KAAII,eAAgB,IAAGD,WAAY,EAArD;;IACA,IAAIhB,WAAW,KAAKqB,SAApB,EAA+B;MAC3BjB,aAAa,CAACa,eAAD,CAAb,GAAiCjB,WAAW,CAACc,CAAC,GAAG,CAAL,CAA5C;MACAT,qBAAqB,CAACY,eAAD,CAArB,GAAyCd,mBAAmB,CAACW,CAAC,GAAG,CAAL,CAA5D;IACH;;IACDF,gBAAgB,CAACU,IAAjB,CAAsBL,eAAtB;;IACA,IAAIE,mBAAmB,KAAKE,SAA5B,EAAuC;MACnCf,oBAAoB,CAACW,eAAD,CAApB,GAAwCE,mBAAxC;IACH;;IACDT,mBAAmB,CAACY,IAApB,CAAyBN,WAAzB;EACH;;EACD,MAAMO,SAAS,GAAGhB,QAAQ,CAACiB,QAAT,IAAqBhC,YAAY,CAACqB,aAAD,EAAgBN,QAAQ,CAACkB,OAAT,IAAoB,EAApC,CAAnD;EACA,MAAMC,SAAS,GAAGnB,QAAQ,CAACmB,SAAT,GAAqBnB,QAAQ,CAACmB,SAAT,CAAmBC,MAAnB,CAA0BC,EAAE,IAAIA,EAAE,KAAKL,SAAvC,CAArB,GAAyE,EAA3F;EACA,OAAO;IACHK,EAAE,EAAEL,SADD;IAEHG,SAFG;IAGHtB,aAHG;IAIHC,qBAJG;IAKHM,IAAI,EAAEE,aALH;IAMHW,QAAQ,EAAEjB,QAAQ,CAACiB,QANhB;IAOHC,OAAO,EAAElB,QAAQ,CAACkB,OAAT,IAAoB,EAP1B;IAQHI,WAAW,EAAEtB,QAAQ,CAACsB,WAAT,IAAwB,EARlC;IASH9B,YAAY,EAAEW,mBATX;IAUHR,oBAVG;IAWHU,gBAXG;IAYHN,oBAZG;IAaHL;EAbG,CAAP;AAeH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASO,aAAT,CAAuBsB,MAAvB,EAA+BrB,GAA/B,EAAoC;EAChC,MAAM;IAAEE,IAAI,EAAEE,aAAR;IAAuBkB;EAAvB,IAAiCC,UAAU,CAACF,MAAD,EAASrB,GAAT,CAAjD;;EACA,IAAIsB,KAAK,KAAKV,SAAd,EAAyB;IACrB,OAAO;MAAEV,IAAI,EAAEE;IAAR,CAAP;EACH,CAFD,MAGK;IACD,MAAM,CAACoB,gBAAD,EAAmB,GAAGP,SAAtB,IAAmCK,KAAK,CAACG,KAAN,CAAYrC,mBAAZ,CAAzC;IACA,MAAM,CAACsC,cAAD,EAAiBX,QAAjB,IAA6BS,gBAAgB,CAACC,KAAjB,CAAuBtC,YAAvB,EAAqC,CAArC,CAAnC;IACA,IAAI,CAAC6B,OAAD,EAAUI,WAAV,IAAyBM,cAAc,CAACD,KAAf,CAAqBvC,iBAArB,EAAwC,CAAxC,CAA7B;;IACA,IAAIkC,WAAW,KAAKR,SAApB,EAA+B;MAC3BQ,WAAW,GAAGJ,OAAd;MACAA,OAAO,GAAGJ,SAAV;IACH;;IACD,IAAIQ,WAAW,KAAK,EAApB,EAAwB;MACpBA,WAAW,GAAGR,SAAd;IACH;;IACD,OAAO;MAAEV,IAAI,EAAEE,aAAR;MAAuBY,OAAvB;MAAgCI,WAAhC;MAA6CL,QAA7C;MAAuDE;IAAvD,CAAP;EACH;AACJ;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASN,gBAAT,CAA0BU,MAA1B,EAAkCrB,GAAlC,EAAuC;EACnC,MAAM;IAAEE,IAAI,EAAEK,WAAR;IAAqBe;EAArB,IAA+BC,UAAU,CAACF,MAAD,EAASrB,GAAT,CAA/C;;EACA,IAAIsB,KAAK,KAAKV,SAAd,EAAyB;IACrB,OAAO;MAAEL;IAAF,CAAP;EACH,CAFD,MAGK;IACD,MAAM,CAACC,eAAD,EAAkBE,mBAAlB,IAAyCY,KAAK,CAACG,KAAN,CAAYtC,YAAZ,CAA/C;IACA,OAAO;MAAEoB,WAAF;MAAeC,eAAf;MAAgCE;IAAhC,CAAP;EACH;AACJ;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASa,UAAT,CAAoBF,MAApB,EAA4BrB,GAA5B,EAAiC;EAC7B,IAAIA,GAAG,CAAC2B,MAAJ,CAAW,CAAX,MAAkB1C,cAAtB,EAAsC;IAClC,OAAO;MAAEiB,IAAI,EAAEmB;IAAR,CAAP;EACH,CAFD,MAGK;IACD,MAAMO,UAAU,GAAGC,cAAc,CAACR,MAAD,EAASrB,GAAT,CAAjC;IACA,OAAO;MACHsB,KAAK,EAAED,MAAM,CAACS,SAAP,CAAiB,CAAjB,EAAoBF,UAApB,CADJ;MAEH1B,IAAI,EAAEmB,MAAM,CAACS,SAAP,CAAiBF,UAAU,GAAG,CAA9B;IAFH,CAAP;EAIH;AACJ;;AACD,SAASnB,sBAAT,CAAgCsB,KAAhC,EAAuC;EACnC,OAAOA,KAAK,KAAK,CAAV,GAAc,IAAd,GAAsB,MAAKA,KAAK,GAAG,CAAE,EAA5C;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASF,cAAT,CAAwBR,MAAxB,EAAgCrB,GAAhC,EAAqC;EACjC,KAAK,IAAIgC,WAAW,GAAG,CAAlB,EAAqBC,QAAQ,GAAG,CAArC,EAAwCD,WAAW,GAAGX,MAAM,CAACf,MAA7D,EAAqE0B,WAAW,IAAIC,QAAQ,EAA5F,EAAgG;IAC5F,IAAIjC,GAAG,CAACiC,QAAD,CAAH,KAAkB,IAAtB,EAA4B;MACxBA,QAAQ;IACX,CAFD,MAGK,IAAIZ,MAAM,CAACW,WAAD,CAAN,KAAwB/C,cAA5B,EAA4C;MAC7C,OAAO+C,WAAP;IACH;EACJ;;EACD,MAAM,IAAIE,KAAJ,CAAW,6CAA4ClC,GAAI,IAA3D,CAAN;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMmC,uBAAN,SAAsCD,KAAtC,CAA4C;EACxCE,WAAW,CAACC,aAAD,EAAgB;IACvB,MAAO,4BAA2BC,eAAe,CAACD,aAAD,CAAgB,GAAjE;IACA,KAAKA,aAAL,GAAqBA,aAArB;IACA,KAAKE,IAAL,GAAY,yBAAZ;EACH;;AALuC;;AAO5C,SAASC,yBAAT,CAAmCC,CAAnC,EAAsC;EAClC,OAAOA,CAAC,CAACF,IAAF,KAAW,yBAAlB;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASG,WAAT,CAAqBC,YAArB,EAAmCrD,YAAnC,EAAiDK,aAAjD,EAAgE;EAC5D,MAAMiD,OAAO,GAAGvD,YAAY,CAACC,YAAD,EAAeK,aAAf,CAA5B,CAD4D,CAE5D;;EACA,IAAIkD,WAAW,GAAGF,YAAY,CAACC,OAAO,CAACzB,EAAT,CAA9B,CAH4D,CAI5D;;EACA,IAAIyB,OAAO,CAAC3B,SAAR,KAAsBL,SAA1B,EAAqC;IACjC,KAAK,IAAIP,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuC,OAAO,CAAC3B,SAAR,CAAkBX,MAAtB,IAAgCuC,WAAW,KAAKjC,SAAhE,EAA2EP,CAAC,EAA5E,EAAgF;MAC5EwC,WAAW,GAAGF,YAAY,CAACC,OAAO,CAAC3B,SAAR,CAAkBZ,CAAlB,CAAD,CAA1B;IACH;EACJ;;EACD,IAAIwC,WAAW,KAAKjC,SAApB,EAA+B;IAC3B,MAAM,IAAIuB,uBAAJ,CAA4BS,OAA5B,CAAN;EACH;;EACD,OAAO,CACHC,WAAW,CAACvD,YADT,EACuBuD,WAAW,CAAC1C,gBAAZ,CAA6B2C,GAA7B,CAAiCC,WAAW,IAAI;IACtE,IAAIH,OAAO,CAACjD,aAAR,CAAsBqD,cAAtB,CAAqCD,WAArC,CAAJ,EAAuD;MACnD,OAAOH,OAAO,CAACjD,aAAR,CAAsBoD,WAAtB,CAAP;IACH,CAFD,MAGK;MACD,MAAM,IAAIb,KAAJ,CAAW,sFAAqFI,eAAe,CAACM,OAAD,CAAU,KAA/G,GACX,oDAAmDG,WAAY,wCAD9D,CAAN;IAEH;EACJ,CARyB,CADvB,CAAP;AAWH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASE,gBAAT,CAA0B7C,aAA1B,EAAyC;EACrC,MAAM8C,KAAK,GAAG9C,aAAa,CAACqB,KAAd,CAAoB,aAApB,CAAd;EACA,MAAMnC,YAAY,GAAG,CAAC4D,KAAK,CAAC,CAAD,CAAN,CAArB;EACA,MAAM/C,gBAAgB,GAAG,EAAzB;;EACA,KAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6C,KAAK,CAAC5C,MAAN,GAAe,CAAnC,EAAsCD,CAAC,IAAI,CAA3C,EAA8C;IAC1CF,gBAAgB,CAACU,IAAjB,CAAsBqC,KAAK,CAAC7C,CAAD,CAA3B;IACAf,YAAY,CAACuB,IAAb,CAAmB,GAAEqC,KAAK,CAAC7C,CAAC,GAAG,CAAL,CAAQ,EAAlC;EACH;;EACD,MAAM8C,eAAe,GAAG7D,YAAY,CAACwD,GAAb,CAAiBM,IAAI,IAAIA,IAAI,CAACzB,MAAL,CAAY,CAAZ,MAAmB1C,cAAnB,GAAoC,OAAOmE,IAA3C,GAAkDA,IAA3E,CAAxB;EACA,OAAO;IACHlD,IAAI,EAAEE,aADH;IAEHd,YAAY,EAAE+D,kBAAkB,CAAC/D,YAAD,EAAe6D,eAAf,CAF7B;IAGHhD;EAHG,CAAP;AAKH;AACD;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASmD,qBAAT,CAA+BhE,YAA/B,EAA6Ca,gBAAgB,GAAG,EAAhE,EAAoE;EAChE,IAAIC,aAAa,GAAGd,YAAY,CAAC,CAAD,CAAhC;;EACA,KAAK,IAAIe,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,gBAAgB,CAACG,MAArC,EAA6CD,CAAC,EAA9C,EAAkD;IAC9CD,aAAa,IAAK,KAAID,gBAAgB,CAACE,CAAD,CAAI,IAAGf,YAAY,CAACe,CAAC,GAAG,CAAL,CAAQ,EAAjE;EACH;;EACD,OAAO;IACHH,IAAI,EAAEE,aADH;IAEHd,YAAY,EAAE+D,kBAAkB,CAAC/D,YAAD,EAAeA,YAAf,CAF7B;IAGHa;EAHG,CAAP;AAKH;AACD;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASkD,kBAAT,CAA4BhC,MAA5B,EAAoCrB,GAApC,EAAyC;EACrCuD,MAAM,CAACC,cAAP,CAAsBnC,MAAtB,EAA8B,KAA9B,EAAqC;IAAEoC,KAAK,EAAEzD;EAAT,CAArC;EACA,OAAOqB,MAAP;AACH;;AACD,SAASiB,eAAT,CAAyBM,OAAzB,EAAkC;EAC9B,MAAMc,aAAa,GAAGd,OAAO,CAAC5B,OAAR,IAAoB,OAAM4B,OAAO,CAAC5B,OAAQ,GAAhE;EACA,MAAM2C,MAAM,GAAGf,OAAO,CAAC3B,SAAR,IAAqB2B,OAAO,CAAC3B,SAAR,CAAkBX,MAAlB,GAA2B,CAAhD,GACV,KAAIsC,OAAO,CAAC3B,SAAR,CAAkB6B,GAAlB,CAAsBc,CAAC,IAAK,IAAGA,CAAE,GAAjC,EAAqCC,IAArC,CAA0C,IAA1C,CAAgD,GAD1C,GAEX,EAFJ;EAGA,OAAQ,IAAGjB,OAAO,CAACzB,EAAG,IAAGwC,MAAO,MAAKf,OAAO,CAAC1C,IAAK,IAAGwD,aAAc,GAAnE;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASI,gBAAT,CAA0BnB,YAA1B,EAAwC;EACpC;EACA,IAAI,CAACoB,SAAS,CAACC,SAAf,EAA0B;IACtBD,SAAS,CAACC,SAAV,GAAsBA,SAAtB;EACH;;EACD,IAAI,CAACD,SAAS,CAACE,YAAf,EAA6B;IACzBF,SAAS,CAACE,YAAV,GAAyB,EAAzB;EACH;;EACDV,MAAM,CAACW,IAAP,CAAYvB,YAAZ,EAA0BwB,OAA1B,CAAkCC,GAAG,IAAI;IACrCL,SAAS,CAACE,YAAV,CAAuBG,GAAvB,IAA8BnB,gBAAgB,CAACN,YAAY,CAACyB,GAAD,CAAb,CAA9C;EACH,CAFD;AAGH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,iBAAT,GAA6B;EACzBN,SAAS,CAACC,SAAV,GAAsBpD,SAAtB;EACAmD,SAAS,CAACE,YAAV,GAAyB,EAAzB;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASD,SAAT,CAAmB1E,YAAnB,EAAiCK,aAAjC,EAAgD;EAC5C,IAAI;IACA,OAAO+C,WAAW,CAACqB,SAAS,CAACE,YAAX,EAAyB3E,YAAzB,EAAuCK,aAAvC,CAAlB;EACH,CAFD,CAGA,OAAO8C,CAAP,EAAU;IACN6B,OAAO,CAACC,IAAR,CAAa9B,CAAC,CAACG,OAAf;IACA,OAAO,CAACtD,YAAD,EAAeK,aAAf,CAAP;EACH;AACJ;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM6E,OAAO,GAAK,eAAe,CAAC,MAAO,OAAOC,UAAP,KAAsB,WAAtB,IAAqCA,UAAtC,IACnC,OAAOC,MAAP,KAAkB,WAAlB,IAAiCA,MADE,IACU,OAAOC,MAAP,KAAkB,WAAlB,IAAiCA,MAD3C,IAEnC,OAAOC,IAAP,KAAgB,WAAhB,IAA+B,OAAOC,iBAAP,KAA6B,WAA5D,IACGD,IAAI,YAAYC,iBADnB,IACwCD,IAHZ,GAAjC;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAME,WAAW,GAAG,UAAUxF,YAAV,EAAwB,GAAGC,WAA3B,EAAwC;EACxD,IAAIuF,WAAW,CAACd,SAAhB,EAA2B;IACvB;IACA,MAAMnB,WAAW,GAAGiC,WAAW,CAACd,SAAZ,CAAsB1E,YAAtB,EAAoCC,WAApC,CAApB;IACAD,YAAY,GAAGuD,WAAW,CAAC,CAAD,CAA1B;IACAtD,WAAW,GAAGsD,WAAW,CAAC,CAAD,CAAzB;EACH;;EACD,IAAID,OAAO,GAAGmC,UAAU,CAACzF,YAAY,CAAC,CAAD,CAAb,EAAkBA,YAAY,CAACU,GAAb,CAAiB,CAAjB,CAAlB,CAAxB;;EACA,KAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGf,YAAY,CAACgB,MAAjC,EAAyCD,CAAC,EAA1C,EAA8C;IAC1CuC,OAAO,IAAIrD,WAAW,CAACc,CAAC,GAAG,CAAL,CAAX,GAAqB0E,UAAU,CAACzF,YAAY,CAACe,CAAD,CAAb,EAAkBf,YAAY,CAACU,GAAb,CAAiBK,CAAjB,CAAlB,CAA1C;EACH;;EACD,OAAOuC,OAAP;AACH,CAZD;;AAaA,MAAMoC,YAAY,GAAG,GAArB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASD,UAAT,CAAoBxE,WAApB,EAAiC0E,cAAjC,EAAiD;EAC7C,OAAOA,cAAc,CAACtD,MAAf,CAAsB,CAAtB,MAA6BqD,YAA7B,GACHzE,WAAW,CAACuB,SAAZ,CAAsBD,cAAc,CAACtB,WAAD,EAAc0E,cAAd,CAAd,GAA8C,CAApE,CADG,GAEH1E,WAFJ;AAGH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA,SAAS8D,iBAAT,EAA4BP,gBAA5B,EAA8CgB,WAAW,IAAII,UAA7D,EAAyE/C,uBAAuB,IAAIgD,wBAApG,EAA8HX,OAAO,IAAIY,QAAzI,EAAmJvD,cAAc,IAAIwD,eAArK,EAAsL7C,yBAAyB,IAAI8C,0BAAnN,EAA+OhC,qBAAqB,IAAIiC,sBAAxQ,EAAgSlC,kBAAkB,IAAImC,mBAAtT,EAA2UnG,YAAY,IAAIoG,aAA3V,EAA0W1F,aAAa,IAAI2F,cAA3X,EAA2YzC,gBAAgB,IAAI0C,iBAA/Z,EAAkbpE,UAAU,IAAIqE,WAAhc,EAA6clD,WAAW,IAAImD,UAA5d"}, "metadata": {}, "sourceType": "module"}