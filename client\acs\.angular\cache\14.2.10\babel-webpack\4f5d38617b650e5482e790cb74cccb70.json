{"ast": null, "code": "import { Subject } from './Subject';\nimport { queue } from './scheduler/queue';\nimport { Subscription } from './Subscription';\nimport { ObserveOnSubscriber } from './operators/observeOn';\nimport { ObjectUnsubscribedError } from './util/ObjectUnsubscribedError';\nimport { SubjectSubscription } from './SubjectSubscription';\nexport class ReplaySubject extends Subject {\n  constructor(bufferSize = Number.POSITIVE_INFINITY, windowTime = Number.POSITIVE_INFINITY, scheduler) {\n    super();\n    this.scheduler = scheduler;\n    this._events = [];\n    this._infiniteTimeWindow = false;\n    this._bufferSize = bufferSize < 1 ? 1 : bufferSize;\n    this._windowTime = windowTime < 1 ? 1 : windowTime;\n\n    if (windowTime === Number.POSITIVE_INFINITY) {\n      this._infiniteTimeWindow = true;\n      this.next = this.nextInfiniteTimeWindow;\n    } else {\n      this.next = this.nextTimeWindow;\n    }\n  }\n\n  nextInfiniteTimeWindow(value) {\n    if (!this.isStopped) {\n      const _events = this._events;\n\n      _events.push(value);\n\n      if (_events.length > this._bufferSize) {\n        _events.shift();\n      }\n    }\n\n    super.next(value);\n  }\n\n  nextTimeWindow(value) {\n    if (!this.isStopped) {\n      this._events.push(new ReplayEvent(this._getNow(), value));\n\n      this._trimBufferThenGetEvents();\n    }\n\n    super.next(value);\n  }\n\n  _subscribe(subscriber) {\n    const _infiniteTimeWindow = this._infiniteTimeWindow;\n\n    const _events = _infiniteTimeWindow ? this._events : this._trimBufferThenGetEvents();\n\n    const scheduler = this.scheduler;\n    const len = _events.length;\n    let subscription;\n\n    if (this.closed) {\n      throw new ObjectUnsubscribedError();\n    } else if (this.isStopped || this.hasError) {\n      subscription = Subscription.EMPTY;\n    } else {\n      this.observers.push(subscriber);\n      subscription = new SubjectSubscription(this, subscriber);\n    }\n\n    if (scheduler) {\n      subscriber.add(subscriber = new ObserveOnSubscriber(subscriber, scheduler));\n    }\n\n    if (_infiniteTimeWindow) {\n      for (let i = 0; i < len && !subscriber.closed; i++) {\n        subscriber.next(_events[i]);\n      }\n    } else {\n      for (let i = 0; i < len && !subscriber.closed; i++) {\n        subscriber.next(_events[i].value);\n      }\n    }\n\n    if (this.hasError) {\n      subscriber.error(this.thrownError);\n    } else if (this.isStopped) {\n      subscriber.complete();\n    }\n\n    return subscription;\n  }\n\n  _getNow() {\n    return (this.scheduler || queue).now();\n  }\n\n  _trimBufferThenGetEvents() {\n    const now = this._getNow();\n\n    const _bufferSize = this._bufferSize;\n    const _windowTime = this._windowTime;\n    const _events = this._events;\n    const eventsCount = _events.length;\n    let spliceCount = 0;\n\n    while (spliceCount < eventsCount) {\n      if (now - _events[spliceCount].time < _windowTime) {\n        break;\n      }\n\n      spliceCount++;\n    }\n\n    if (eventsCount > _bufferSize) {\n      spliceCount = Math.max(spliceCount, eventsCount - _bufferSize);\n    }\n\n    if (spliceCount > 0) {\n      _events.splice(0, spliceCount);\n    }\n\n    return _events;\n  }\n\n}\n\nclass ReplayEvent {\n  constructor(time, value) {\n    this.time = time;\n    this.value = value;\n  }\n\n}", "map": {"version": 3, "names": ["Subject", "queue", "Subscription", "ObserveOnSubscriber", "ObjectUnsubscribedError", "SubjectSubscription", "ReplaySubject", "constructor", "bufferSize", "Number", "POSITIVE_INFINITY", "windowTime", "scheduler", "_events", "_infiniteTimeWindow", "_bufferSize", "_windowTime", "next", "nextInfiniteTimeWindow", "nextTimeWindow", "value", "isStopped", "push", "length", "shift", "ReplayEvent", "_getNow", "_trimBufferThenGetEvents", "_subscribe", "subscriber", "len", "subscription", "closed", "<PERSON><PERSON><PERSON><PERSON>", "EMPTY", "observers", "add", "i", "error", "thrownError", "complete", "now", "eventsCount", "spliceCount", "time", "Math", "max", "splice"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/ReplaySubject.js"], "sourcesContent": ["import { Subject } from './Subject';\nimport { queue } from './scheduler/queue';\nimport { Subscription } from './Subscription';\nimport { ObserveOnSubscriber } from './operators/observeOn';\nimport { ObjectUnsubscribedError } from './util/ObjectUnsubscribedError';\nimport { SubjectSubscription } from './SubjectSubscription';\nexport class ReplaySubject extends Subject {\n    constructor(bufferSize = Number.POSITIVE_INFINITY, windowTime = Number.POSITIVE_INFINITY, scheduler) {\n        super();\n        this.scheduler = scheduler;\n        this._events = [];\n        this._infiniteTimeWindow = false;\n        this._bufferSize = bufferSize < 1 ? 1 : bufferSize;\n        this._windowTime = windowTime < 1 ? 1 : windowTime;\n        if (windowTime === Number.POSITIVE_INFINITY) {\n            this._infiniteTimeWindow = true;\n            this.next = this.nextInfiniteTimeWindow;\n        }\n        else {\n            this.next = this.nextTimeWindow;\n        }\n    }\n    nextInfiniteTimeWindow(value) {\n        if (!this.isStopped) {\n            const _events = this._events;\n            _events.push(value);\n            if (_events.length > this._bufferSize) {\n                _events.shift();\n            }\n        }\n        super.next(value);\n    }\n    nextTimeWindow(value) {\n        if (!this.isStopped) {\n            this._events.push(new ReplayEvent(this._getNow(), value));\n            this._trimBufferThenGetEvents();\n        }\n        super.next(value);\n    }\n    _subscribe(subscriber) {\n        const _infiniteTimeWindow = this._infiniteTimeWindow;\n        const _events = _infiniteTimeWindow ? this._events : this._trimBufferThenGetEvents();\n        const scheduler = this.scheduler;\n        const len = _events.length;\n        let subscription;\n        if (this.closed) {\n            throw new ObjectUnsubscribedError();\n        }\n        else if (this.isStopped || this.hasError) {\n            subscription = Subscription.EMPTY;\n        }\n        else {\n            this.observers.push(subscriber);\n            subscription = new SubjectSubscription(this, subscriber);\n        }\n        if (scheduler) {\n            subscriber.add(subscriber = new ObserveOnSubscriber(subscriber, scheduler));\n        }\n        if (_infiniteTimeWindow) {\n            for (let i = 0; i < len && !subscriber.closed; i++) {\n                subscriber.next(_events[i]);\n            }\n        }\n        else {\n            for (let i = 0; i < len && !subscriber.closed; i++) {\n                subscriber.next(_events[i].value);\n            }\n        }\n        if (this.hasError) {\n            subscriber.error(this.thrownError);\n        }\n        else if (this.isStopped) {\n            subscriber.complete();\n        }\n        return subscription;\n    }\n    _getNow() {\n        return (this.scheduler || queue).now();\n    }\n    _trimBufferThenGetEvents() {\n        const now = this._getNow();\n        const _bufferSize = this._bufferSize;\n        const _windowTime = this._windowTime;\n        const _events = this._events;\n        const eventsCount = _events.length;\n        let spliceCount = 0;\n        while (spliceCount < eventsCount) {\n            if ((now - _events[spliceCount].time) < _windowTime) {\n                break;\n            }\n            spliceCount++;\n        }\n        if (eventsCount > _bufferSize) {\n            spliceCount = Math.max(spliceCount, eventsCount - _bufferSize);\n        }\n        if (spliceCount > 0) {\n            _events.splice(0, spliceCount);\n        }\n        return _events;\n    }\n}\nclass ReplayEvent {\n    constructor(time, value) {\n        this.time = time;\n        this.value = value;\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,WAAxB;AACA,SAASC,KAAT,QAAsB,mBAAtB;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,SAASC,mBAAT,QAAoC,uBAApC;AACA,SAASC,uBAAT,QAAwC,gCAAxC;AACA,SAASC,mBAAT,QAAoC,uBAApC;AACA,OAAO,MAAMC,aAAN,SAA4BN,OAA5B,CAAoC;EACvCO,WAAW,CAACC,UAAU,GAAGC,MAAM,CAACC,iBAArB,EAAwCC,UAAU,GAAGF,MAAM,CAACC,iBAA5D,EAA+EE,SAA/E,EAA0F;IACjG;IACA,KAAKA,SAAL,GAAiBA,SAAjB;IACA,KAAKC,OAAL,GAAe,EAAf;IACA,KAAKC,mBAAL,GAA2B,KAA3B;IACA,KAAKC,WAAL,GAAmBP,UAAU,GAAG,CAAb,GAAiB,CAAjB,GAAqBA,UAAxC;IACA,KAAKQ,WAAL,GAAmBL,UAAU,GAAG,CAAb,GAAiB,CAAjB,GAAqBA,UAAxC;;IACA,IAAIA,UAAU,KAAKF,MAAM,CAACC,iBAA1B,EAA6C;MACzC,KAAKI,mBAAL,GAA2B,IAA3B;MACA,KAAKG,IAAL,GAAY,KAAKC,sBAAjB;IACH,CAHD,MAIK;MACD,KAAKD,IAAL,GAAY,KAAKE,cAAjB;IACH;EACJ;;EACDD,sBAAsB,CAACE,KAAD,EAAQ;IAC1B,IAAI,CAAC,KAAKC,SAAV,EAAqB;MACjB,MAAMR,OAAO,GAAG,KAAKA,OAArB;;MACAA,OAAO,CAACS,IAAR,CAAaF,KAAb;;MACA,IAAIP,OAAO,CAACU,MAAR,GAAiB,KAAKR,WAA1B,EAAuC;QACnCF,OAAO,CAACW,KAAR;MACH;IACJ;;IACD,MAAMP,IAAN,CAAWG,KAAX;EACH;;EACDD,cAAc,CAACC,KAAD,EAAQ;IAClB,IAAI,CAAC,KAAKC,SAAV,EAAqB;MACjB,KAAKR,OAAL,CAAaS,IAAb,CAAkB,IAAIG,WAAJ,CAAgB,KAAKC,OAAL,EAAhB,EAAgCN,KAAhC,CAAlB;;MACA,KAAKO,wBAAL;IACH;;IACD,MAAMV,IAAN,CAAWG,KAAX;EACH;;EACDQ,UAAU,CAACC,UAAD,EAAa;IACnB,MAAMf,mBAAmB,GAAG,KAAKA,mBAAjC;;IACA,MAAMD,OAAO,GAAGC,mBAAmB,GAAG,KAAKD,OAAR,GAAkB,KAAKc,wBAAL,EAArD;;IACA,MAAMf,SAAS,GAAG,KAAKA,SAAvB;IACA,MAAMkB,GAAG,GAAGjB,OAAO,CAACU,MAApB;IACA,IAAIQ,YAAJ;;IACA,IAAI,KAAKC,MAAT,EAAiB;MACb,MAAM,IAAI5B,uBAAJ,EAAN;IACH,CAFD,MAGK,IAAI,KAAKiB,SAAL,IAAkB,KAAKY,QAA3B,EAAqC;MACtCF,YAAY,GAAG7B,YAAY,CAACgC,KAA5B;IACH,CAFI,MAGA;MACD,KAAKC,SAAL,CAAeb,IAAf,CAAoBO,UAApB;MACAE,YAAY,GAAG,IAAI1B,mBAAJ,CAAwB,IAAxB,EAA8BwB,UAA9B,CAAf;IACH;;IACD,IAAIjB,SAAJ,EAAe;MACXiB,UAAU,CAACO,GAAX,CAAeP,UAAU,GAAG,IAAI1B,mBAAJ,CAAwB0B,UAAxB,EAAoCjB,SAApC,CAA5B;IACH;;IACD,IAAIE,mBAAJ,EAAyB;MACrB,KAAK,IAAIuB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGP,GAAJ,IAAW,CAACD,UAAU,CAACG,MAAvC,EAA+CK,CAAC,EAAhD,EAAoD;QAChDR,UAAU,CAACZ,IAAX,CAAgBJ,OAAO,CAACwB,CAAD,CAAvB;MACH;IACJ,CAJD,MAKK;MACD,KAAK,IAAIA,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGP,GAAJ,IAAW,CAACD,UAAU,CAACG,MAAvC,EAA+CK,CAAC,EAAhD,EAAoD;QAChDR,UAAU,CAACZ,IAAX,CAAgBJ,OAAO,CAACwB,CAAD,CAAP,CAAWjB,KAA3B;MACH;IACJ;;IACD,IAAI,KAAKa,QAAT,EAAmB;MACfJ,UAAU,CAACS,KAAX,CAAiB,KAAKC,WAAtB;IACH,CAFD,MAGK,IAAI,KAAKlB,SAAT,EAAoB;MACrBQ,UAAU,CAACW,QAAX;IACH;;IACD,OAAOT,YAAP;EACH;;EACDL,OAAO,GAAG;IACN,OAAO,CAAC,KAAKd,SAAL,IAAkBX,KAAnB,EAA0BwC,GAA1B,EAAP;EACH;;EACDd,wBAAwB,GAAG;IACvB,MAAMc,GAAG,GAAG,KAAKf,OAAL,EAAZ;;IACA,MAAMX,WAAW,GAAG,KAAKA,WAAzB;IACA,MAAMC,WAAW,GAAG,KAAKA,WAAzB;IACA,MAAMH,OAAO,GAAG,KAAKA,OAArB;IACA,MAAM6B,WAAW,GAAG7B,OAAO,CAACU,MAA5B;IACA,IAAIoB,WAAW,GAAG,CAAlB;;IACA,OAAOA,WAAW,GAAGD,WAArB,EAAkC;MAC9B,IAAKD,GAAG,GAAG5B,OAAO,CAAC8B,WAAD,CAAP,CAAqBC,IAA5B,GAAoC5B,WAAxC,EAAqD;QACjD;MACH;;MACD2B,WAAW;IACd;;IACD,IAAID,WAAW,GAAG3B,WAAlB,EAA+B;MAC3B4B,WAAW,GAAGE,IAAI,CAACC,GAAL,CAASH,WAAT,EAAsBD,WAAW,GAAG3B,WAApC,CAAd;IACH;;IACD,IAAI4B,WAAW,GAAG,CAAlB,EAAqB;MACjB9B,OAAO,CAACkC,MAAR,CAAe,CAAf,EAAkBJ,WAAlB;IACH;;IACD,OAAO9B,OAAP;EACH;;AA7FsC;;AA+F3C,MAAMY,WAAN,CAAkB;EACdlB,WAAW,CAACqC,IAAD,EAAOxB,KAAP,EAAc;IACrB,KAAKwB,IAAL,GAAYA,IAAZ;IACA,KAAKxB,KAAL,GAAaA,KAAb;EACH;;AAJa"}, "metadata": {}, "sourceType": "module"}