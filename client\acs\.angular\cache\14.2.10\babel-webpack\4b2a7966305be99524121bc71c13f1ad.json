{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./certificate-details.component.html?ngResource\";\nimport { Component, EventEmitter, ViewChild, ChangeDetectorRef, Input } from '@angular/core';\nimport { MembershipCertificate, Legend } from 'src/app/Modules/Shared/Models/IndividualProducts/MembershipCertificate';\nimport { Validators, FormControl, FormGroup } from '@angular/forms';\nimport { ToastrService } from 'ngx-toastr';\nimport { ColorSelectorComponent } from 'src/app/Modules/Shared/Components/formComponents/color-selector/color-selector.component';\nimport { CustomSharedValidations } from 'src/app/Modules/Shared/functions/custom-validations';\nimport { StringValue } from 'src/app/Modules/Shared/Enums/string-values.enum';\nimport { MultiCertificateService } from 'src/app/Modules/IndividualProducts/Services/multi-certificate.service';\nlet CertificateDetailsComponent = class CertificateDetailsComponent {\n  constructor(cdr, toaster, mutliCertService) {\n    this.cdr = cdr;\n    this.toaster = toaster;\n    this.mutliCertService = mutliCertService;\n    this.MembershipCertificate = new MembershipCertificate();\n    this.isAdd = false;\n    this.certificateNumbering = [];\n    this.Designations = [];\n    this.CertificateSignature1 = [];\n    this.CertificateSignature2 = [];\n    this.States = [];\n    this.Pars = [];\n    this.certificateColor = [];\n    this.AdditionalLegends = [];\n    this.otherLegends = [];\n    this.CustomizedLegends = [];\n    this.$onAddOrder = new EventEmitter();\n    this.buttonText = StringValue.addToOrder;\n    this.showCertificateNumbering = false;\n    this.formationSubTypes = [];\n    this.showLegend = false;\n    this.allLegendData = [];\n    this.designationData = [];\n    this.showMultiCerts = false;\n    this.Form = new FormGroup({\n      Designation: new FormControl('', [Validators.required]),\n      OtherDesignation: new FormControl(''),\n      CertificateNumbering: new FormControl('', [Validators.required]),\n      CertificateFromNo: new FormControl('', [Validators.required, CustomSharedValidations.validateCertificateNumbering('CertificateFromNo', 'CertificateToNo')]),\n      CertificateToNo: new FormControl('', [Validators.required, CustomSharedValidations.validateCertificateNumbering('CertificateFromNo', 'CertificateToNo')]),\n      TotalAuthorisedShares: new FormControl('', [Validators.required]),\n      //ProfessionalCorporation: new FormControl(null),\n      OtherProfessionalCorpType: new FormControl(null),\n      MultiCerts: new FormControl(null),\n      // ReflectiveCerts : new FormControl(null),\n      ParValue: new FormControl('', Validators.required),\n      ParValueCustomized: new FormControl('', Validators.required),\n      CertificateColor: new FormControl('', [Validators.required]),\n      CertificateSignature1: new FormControl('', [Validators.required]),\n      OtherSignatureTitle1: new FormControl(''),\n      CertificateSignature2: new FormControl('', [Validators.required]),\n      OtherSignatureTitle2: new FormControl('', Validators.required),\n      AddLegends: new FormControl('', [Validators.required]),\n      AdditionalLegends: new FormControl(''),\n      CustomizedLegends: new FormControl(''),\n      OtherLegends: new FormControl(''),\n      Remarks: new FormControl('', [CustomSharedValidations.maxLength(250)])\n    });\n  }\n\n  set CertCompo(CertificateColorComponent) {\n    this.CertificateColorComponent = CertificateColorComponent;\n  }\n\n  set certControlsComponent(certControlsComponent) {\n    if (certControlsComponent) {\n      this.CertControlsComponent = certControlsComponent;\n      this.CertControlsComponent.FormationType = this.MembershipCertificate.CorporationType;\n      this.CertControlsComponent.isAdd = this.isAdd;\n    }\n\n    this.cdr.detectChanges();\n  }\n\n  ngOnInit() {}\n\n  ngAfterContentChecked() {// this.renderCertificateColors();\n    // this.checkParValue();\n  }\n\n  validateAuthorisedShares() {\n    if (this.MembershipCertificate.FormationType != '010' && this.MembershipCertificate.FormationType != 'FP') return;\n\n    if (this.Form.controls.TotalAuthorisedShares.value && this.Form.controls.TotalAuthorisedShares.value < this.CertControlsComponent.certificateNumberingCount) {\n      this.toaster.warning(\"Total Authorized Shares should not be less than total Certificates.\", \"Warning\");\n      return true;\n    }\n  }\n\n  onAddOrder() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        _this.CertControlsComponent.getCertificateNumberingCount();\n\n        _this.CertControlsComponent.removeValidationFromDesignation();\n\n        var multiCertificates = yield _this.CertControlsComponent.getCertificateData();\n\n        _this.validateAuthorisedShares();\n\n        if (_this.Form.valid && _this.CertControlsComponent.Form.controls.CertificatesForm.valid) {\n          _this.mapToModel(multiCertificates);\n\n          _this.MembershipCertificate.AdditionalLegends = _this.showLegend ? _this.AdditionalLegends.filter(x => x.IsChecked) : [];\n          _this.MembershipCertificate.CustomizedLegends = _this.showLegend ? _this.CustomizedLegends.filter(x => x.IsChecked) : [];\n          _this.MembershipCertificate.OtherLegends = _this.showLegend ? _this.otherLegends.filter(x => x.IsChecked) : [];\n\n          let validation = _this.legendValidation();\n\n          if (!validation.IsValid) {\n            _this.toaster.error(validation.Msg, \"Unable to Add To Cart.\");\n\n            return;\n          }\n\n          if (_this.MembershipCertificate.AddLegends == 'N') {\n            _this.MembershipCertificate.AdditionalLegends = [];\n            _this.MembershipCertificate.CustomizedLegends = [];\n          }\n\n          _this.$onAddOrder.emit(_this.MembershipCertificate);\n        }\n      } catch (ex) {\n        console.error(ex);\n      }\n    })();\n  }\n\n  mapToModel(multiCertificates) {\n    this.MembershipCertificate.TotalAuthorisedShares = this.MembershipCertificate.FormationType != '030' && this.MembershipCertificate.FormationType != 'NP' && this.MembershipCertificate.CorporationType == '001' ? this.Form.controls['TotalAuthorisedShares'].value : 0; // this.MembershipCertificate.Reflective = this.Form.controls['ReflectiveCerts'].value\n\n    this.MembershipCertificate.AddLegends = this.Form.controls['AddLegends'].value;\n    this.MembershipCertificate.Remarks = this.Form.controls['Remarks'].value;\n    this.MembershipCertificate.Certificates = multiCertificates;\n  }\n\n  legendValidation() {\n    let validationMsg = \"\";\n    let IsValid = true;\n\n    if (this.Form.controls.AddLegends.value == 'Y') {\n      // Validate Additional Legend : Not Required\n      //Validate Customized Legends \n      IsValid = this.MembershipCertificate.CustomizedLegends.filter(x => x.IsChecked).every(legend => {\n        if (!legend.LegendValue) {\n          validationMsg = \"Please enter the value for Aditional Legend \\\"\" + legend.LegendText + \"\\\"\";\n          return false;\n        }\n\n        return true;\n      });\n    }\n\n    IsValid = validationMsg ? false : true;\n    return {\n      IsValid: IsValid,\n      Msg: validationMsg\n    };\n  }\n\n  renderCertificateColors() {\n    this.CertificateColorComponent.IsVisible = true;\n    this.CertificateColorComponent.Label = 'Certificate Color';\n    this.CertificateColorComponent.Colors = this.certificateColor;\n  }\n\n  checkParValue() {\n    if (this.Form.controls['ParValue'].value != 'CRPVSH') {\n      this.Form.controls.ParValueCustomized.reset();\n      this.Form.controls.ParValueCustomized.disable();\n      this.Form.controls.ParValueCustomized.clearValidators;\n    } else {\n      this.Form.controls.ParValueCustomized.enable();\n    }\n  }\n\n  OnCertificateNumberingChange(value) {\n    if (value == \"Yes\") {\n      this.Form.controls.CertificateFromNo.setValue(1);\n      this.Form.controls.CertificateToNo.setValue(20);\n      this.Form.controls.CertificateFromNo.setValidators([Validators.required, CustomSharedValidations.validateCertificateNumbering('CertificateFromNo', 'CertificateToNo')]);\n    } else {\n      this.Form.controls.CertificateFromNo.clearValidators();\n      this.Form.controls.CertificateFromNo.updateValueAndValidity();\n      this.Form.controls.CertificateToNo.setValue(20);\n      this.Form.controls.CertificateFromNo.setValue(\"\");\n    }\n  }\n\n  sendMultiCertificateData(certificateNumbering, certificateSignatures, certificateColors, allLegendData, professionalCorporation) {\n    var response = {\n      parValueData: this.MapMasterDataByControlId(this.Pars),\n      certificateColors: this.MapMasterDataByControlId(certificateColors),\n      certificateSignatures: this.MapMasterDataByControlId(certificateSignatures),\n      certificateNumbering: certificateNumbering,\n      subCategoryCode: this.MembershipCertificate.SubCategoryCode,\n      productCode: this.productCode,\n      categoryCode: this.categoryCode,\n      formationType: this.MembershipCertificate.FormationType || '',\n      corporationType: this.MembershipCertificate.CorporationType,\n      certificates: this.MembershipCertificate.Certificates,\n      isAdd: this.isAdd,\n      designations: this.designationData.map(x => {\n        var data = {\n          id: x.classCode,\n          value: x.description,\n          showTextbox: x.showTextbox\n        };\n        return data;\n      }),\n      allLegendData: allLegendData,\n      professionalCorporation: this.MembershipCertificate.ProfessionalCorporation\n    };\n\n    if (this.MembershipCertificate.SubCategoryCode == 'CDX' || this.MembershipCertificate.SubCategoryCode != 'CDX' && this.MembershipCertificate.CorporationType != 'FP' && this.MembershipCertificate.CorporationType != 'SC' && this.MembershipCertificate.CorporationType != '001' || this.MembershipCertificate.FormationType != 'FP' && this.MembershipCertificate.FormationType != '010') {\n      this.ToggleMulticerts(true);\n    }\n\n    this.mutliCertService.postData(response);\n  }\n\n  MapMasterDataByControlId(data) {\n    let mappedData = data.map(item => {\n      var data = {\n        id: item.controlValue,\n        value: item.controlText\n      };\n      return data;\n    });\n    return mappedData || [];\n  }\n\n  ToggleMulticerts(checked) {\n    if (this.CertControlsComponent.showMulticerts && !checked) {\n      for (let index = this.CertControlsComponent.Certificates.length - 1; index >= 0; index--) {\n        if (index == 0) continue;\n        this.CertControlsComponent.Certificates.removeAt(index);\n      }\n    }\n\n    this.CertControlsComponent.showMulticerts = checked;\n  }\n\n  showLegends() {\n    var isValidProfessionalCorp = this.MembershipCertificate.ProfessionalCorporation && this.formationSubTypes.map(x => x.id).includes(this.MembershipCertificate.ProfessionalCorporation);\n    this.showLegend = this.MembershipCertificate.CorporationType == 'FP' || this.MembershipCertificate.CorporationType == 'SC' || this.MembershipCertificate.CorporationType == '001' ? this.MembershipCertificate.FormationType != '030' && this.MembershipCertificate.FormationType != 'NP' || (this.MembershipCertificate.FormationType == '030' || this.MembershipCertificate.FormationType == 'NP') && isValidProfessionalCorp && !['060', '065'].includes(this.MembershipCertificate.ProfessionalCorporation) : true;\n    return this.showLegend;\n  }\n\n  getFilteredLegends(data, legendType, formationType, corpSubType) {\n    var filteredData = data.find(x => x.legendType === legendType);\n    if (this.MembershipCertificate.CorporationType != 'FP' && this.MembershipCertificate.CorporationType != 'SC' && this.MembershipCertificate.CorporationType != '001') return filteredData.legends;\n\n    if (formationType != '010' && formationType != 'FP' && corpSubType) {\n      return filteredData.legends.filter(x => x.subCategoryCode == formationType && x.optionCode == corpSubType);\n    } else if (formationType == '010' || formationType == 'FP') {\n      return filteredData.legends.filter(x => x.subCategoryCode == '' && x.optionCode == '');\n    } else {\n      return [];\n    }\n  }\n\n  onCorporateSubTypeChange(value) {\n    if (this.CertControlsComponent) {\n      this.CertControlsComponent.mapLegendsOnCorpSubTypeChange(value);\n    }\n  }\n\n  filterLegendData(data, formationType, corpSubType) {\n    if (data.find(x => x.legendType === 'A')) {\n      var filteredLegends = this.getFilteredLegends(data, 'A', formationType, corpSubType);\n      this.AdditionalLegends = filteredLegends.map(x => {\n        var legendData = new Legend();\n        legendData.Price = x.retailPrice;\n        legendData.LegendID = x.productionNumber;\n        legendData.LegendText = x.webDescriptionline;\n        legendData.IsChecked = false;\n        this.legendPriceA = x.retailPrice;\n        return legendData;\n      });\n    }\n\n    if (data.find(x => x.legendType === 'C')) {\n      var filteredLegends = this.getFilteredLegends(data, 'C', formationType, corpSubType);\n      this.CustomizedLegends = filteredLegends.map(x => {\n        var legendData = new Legend();\n        legendData.Price = x.retailPrice;\n        legendData.LegendID = x.productionNumber;\n        legendData.LegendText = x.webDescriptionline;\n        legendData.LegendText2 = x.webDescriptionline2;\n        legendData.IsChecked = false;\n        this.legendPriceC = x.retailPrice;\n        return legendData;\n      });\n    }\n\n    if (data.find(x => x.legendType === 'O')) {\n      var filteredLegends = this.getFilteredLegends(data, 'O', formationType, corpSubType);\n      this.otherLegends = filteredLegends.map(x => {\n        var legendData = new Legend();\n        legendData.Price = x.retailPrice;\n        legendData.LegendID = x.productionNumber;\n        legendData.LegendText = x.webDescriptionline;\n        legendData.LegendText2 = x.webDescriptionline2;\n        legendData.IsChecked = true;\n        this.legendPriceO = x.retailPrice;\n        return legendData;\n      });\n    }\n  }\n\n};\n\nCertificateDetailsComponent.ctorParameters = () => [{\n  type: ChangeDetectorRef\n}, {\n  type: ToastrService\n}, {\n  type: MultiCertificateService\n}];\n\nCertificateDetailsComponent.propDecorators = {\n  label: [{\n    type: Input\n  }],\n  bulkPricing: [{\n    type: Input\n  }],\n  productCode: [{\n    type: Input\n  }],\n  categoryCode: [{\n    type: Input\n  }],\n  CertCompo: [{\n    type: ViewChild,\n    args: ['CertificateColorComponent', {\n      read: ColorSelectorComponent,\n      static: true\n    }]\n  }],\n  certControlsComponent: [{\n    type: ViewChild,\n    args: [\"CertControlsComponent\", {\n      static: false\n    }]\n  }]\n};\nCertificateDetailsComponent = __decorate([Component({\n  selector: 'certificate-details',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [MultiCertificateService]\n})], CertificateDetailsComponent);\nexport { CertificateDetailsComponent };", "map": {"version": 3, "mappings": ";;;AAAA,SAASA,SAAT,EAA4BC,YAA5B,EAA0CC,SAA1C,EAAyFC,iBAAzF,EAA4GC,KAA5G,QAAyH,eAAzH;AACA,SAASC,qBAAT,EAAoDC,MAApD,QAAkE,wEAAlE;AACA,SAASC,UAAT,EAAqBC,WAArB,EAAkCC,SAAlC,QAAmD,gBAAnD;AACA,SAASC,aAAT,QAA8B,YAA9B;AACA,SAASC,sBAAT,QAAuC,0FAAvC;AACA,SAASC,uBAAT,QAAwC,qDAAxC;AACA,SAASC,WAAT,QAA4B,iDAA5B;AAGA,SAASC,uBAAT,QAAwC,uEAAxC;IAOaC,2BAA2B,SAA3BA,2BAA2B;EAMpCC,YAAoBC,GAApB,EAAoDC,OAApD,EAAoFC,gBAApF,EAA6H;IAAzG;IAAgC;IAAgC;IAuBpF,6BAA+C,IAAId,qBAAJ,EAA/C;IACA,aAAiB,KAAjB;IACA,4BAA8B,EAA9B;IACA,oBAAsB,EAAtB;IACA,6BAA+B,EAA/B;IACA,6BAA+B,EAA/B;IACA,cAAgB,EAAhB;IACA,YAAc,EAAd;IACA,wBAA0B,EAA1B;IACA,yBAA8B,EAA9B;IACA,oBAAyB,EAAzB;IACA,yBAA0C,EAA1C;IACA,mBAAc,IAAIJ,YAAJ,EAAd;IACA,kBAAqBY,WAAW,CAACO,UAAjC;IAIA,gCAAoC,KAApC;IAEA,yBAA2B,EAA3B;IACA,kBAAsB,KAAtB;IACA,qBAAuB,EAAvB;IACA,uBAAyB,EAAzB;IACA,sBAA0B,KAA1B;IASA,YAAO,IAAIX,SAAJ,CAAc;MACjBY,WAAW,EAAE,IAAIb,WAAJ,CAA+B,EAA/B,EAAmC,CAACD,UAAU,CAACe,QAAZ,CAAnC,CADI;MAEjBC,gBAAgB,EAAE,IAAIf,WAAJ,CAA+B,EAA/B,CAFD;MAGjBgB,oBAAoB,EAAE,IAAIhB,WAAJ,CAA4B,EAA5B,EAAgC,CAACD,UAAU,CAACe,QAAZ,CAAhC,CAHL;MAIjBG,iBAAiB,EAAE,IAAIjB,WAAJ,CAA4B,EAA5B,EAAgC,CAACD,UAAU,CAACe,QAAZ,EAAsBV,uBAAuB,CAACc,4BAAxB,CAAqD,mBAArD,EAA0E,iBAA1E,CAAtB,CAAhC,CAJF;MAKjBC,eAAe,EAAE,IAAInB,WAAJ,CAA4B,EAA5B,EAAgC,CAACD,UAAU,CAACe,QAAZ,EAAsBV,uBAAuB,CAACc,4BAAxB,CAAqD,mBAArD,EAA0E,iBAA1E,CAAtB,CAAhC,CALA;MAMjBE,qBAAqB,EAAE,IAAIpB,WAAJ,CAA4B,EAA5B,EAAgC,CAACD,UAAU,CAACe,QAAZ,CAAhC,CANN;MAOjB;MACAO,yBAAyB,EAAE,IAAIrB,WAAJ,CAA+B,IAA/B,CARV;MASjBsB,UAAU,EAAE,IAAItB,WAAJ,CAA4B,IAA5B,CATK;MAUjB;MACAuB,QAAQ,EAAE,IAAIvB,WAAJ,CAA4B,EAA5B,EAAgCD,UAAU,CAACe,QAA3C,CAXO;MAYjBU,kBAAkB,EAAE,IAAIxB,WAAJ,CAA4B,EAA5B,EAAgCD,UAAU,CAACe,QAA3C,CAZH;MAajBW,gBAAgB,EAAE,IAAIzB,WAAJ,CAA+B,EAA/B,EAAmC,CAACD,UAAU,CAACe,QAAZ,CAAnC,CAbD;MAcjBY,qBAAqB,EAAE,IAAI1B,WAAJ,CAA+B,EAA/B,EAAmC,CAACD,UAAU,CAACe,QAAZ,CAAnC,CAdN;MAejBa,oBAAoB,EAAE,IAAI3B,WAAJ,CAA+B,EAA/B,CAfL;MAgBjB4B,qBAAqB,EAAE,IAAI5B,WAAJ,CAA+B,EAA/B,EAAmC,CAACD,UAAU,CAACe,QAAZ,CAAnC,CAhBN;MAiBjBe,oBAAoB,EAAE,IAAI7B,WAAJ,CAA+B,EAA/B,EAAmCD,UAAU,CAACe,QAA9C,CAjBL;MAkBjBgB,UAAU,EAAE,IAAI9B,WAAJ,CAA4B,EAA5B,EAAgC,CAACD,UAAU,CAACe,QAAZ,CAAhC,CAlBK;MAmBjBiB,iBAAiB,EAAE,IAAI/B,WAAJ,CAA4B,EAA5B,CAnBF;MAoBjBgC,iBAAiB,EAAE,IAAIhC,WAAJ,CAA4B,EAA5B,CApBF;MAqBjBiC,YAAY,EAAE,IAAIjC,WAAJ,CAA4B,EAA5B,CArBG;MAsBjBkC,OAAO,EAAE,IAAIlC,WAAJ,CAA+B,EAA/B,EAAmC,CAACI,uBAAuB,CAAC+B,SAAxB,CAAkC,GAAlC,CAAD,CAAnC;IAtBQ,CAAd,CAAP;EAvDkI;;EAK7B,IAATC,SAAS,CAACC,yBAAD,EAAkD;IACnJ,KAAKA,yBAAL,GAAiCA,yBAAjC;EACH;;EAIwB,IAArBC,qBAAqB,CACrBA,qBADqB,EAC8B;IAEnD,IAAIA,qBAAJ,EAA2B;MACvB,KAAKC,qBAAL,GAA6BD,qBAA7B;MACA,KAAKC,qBAAL,CAA2BC,aAA3B,GAA2C,KAAK3C,qBAAL,CAA2B4C,eAAtE;MACA,KAAKF,qBAAL,CAA2BG,KAA3B,GAAmC,KAAKA,KAAxC;IAEH;;IACD,KAAKjC,GAAL,CAASkC,aAAT;EACH;;EA2BDC,QAAQ,IACP;;EACDC,qBAAqB,IACjB;IACA;EACH;;EA2BDC,wBAAwB;IACpB,IAAI,KAAKjD,qBAAL,CAA2B2C,aAA3B,IAA4C,KAA5C,IAAqD,KAAK3C,qBAAL,CAA2B2C,aAA3B,IAA4C,IAArG,EAA2G;;IAC3G,IAAI,KAAKO,IAAL,CAAUC,QAAV,CAAmB5B,qBAAnB,CAAyC6B,KAAzC,IAAkD,KAAKF,IAAL,CAAUC,QAAV,CAAmB5B,qBAAnB,CAAyC6B,KAAzC,GAAiD,KAAKV,qBAAL,CAA2BW,yBAAlI,EAA6J;MACzJ,KAAKxC,OAAL,CAAayC,OAAb,CACI,qEADJ,EAEI,SAFJ;MAIA,OAAO,IAAP;IACH;EACJ;;EAEKC,UAAU;IAAA;;IAAA;MACZ,IAAI;QACA,KAAI,CAACb,qBAAL,CAA2Bc,4BAA3B;;QACA,KAAI,CAACd,qBAAL,CAA2Be,+BAA3B;;QACA,IAAIC,iBAAiB,SAAS,KAAI,CAAChB,qBAAL,CAA2BiB,kBAA3B,EAA9B;;QACA,KAAI,CAACV,wBAAL;;QAEA,IAAI,KAAI,CAACC,IAAL,CAAUU,KAAV,IAAmB,KAAI,CAAClB,qBAAL,CAA2BQ,IAA3B,CAAgCC,QAAhC,CAAyCU,gBAAzC,CAA0DD,KAAjF,EAAwF;UACpF,KAAI,CAACE,UAAL,CAAgBJ,iBAAhB;;UACA,KAAI,CAAC1D,qBAAL,CAA2BkC,iBAA3B,GAA+C,KAAI,CAAC6B,UAAL,GAAkB,KAAI,CAAC7B,iBAAL,CAAuB8B,MAAvB,CAA8BC,CAAC,IAAIA,CAAC,CAACC,SAArC,CAAlB,GAAoE,EAAnH;UACA,KAAI,CAAClE,qBAAL,CAA2BmC,iBAA3B,GAA+C,KAAI,CAAC4B,UAAL,GAAkB,KAAI,CAAC5B,iBAAL,CAAuB6B,MAAvB,CAA8BC,CAAC,IAAIA,CAAC,CAACC,SAArC,CAAlB,GAAoE,EAAnH;UACA,KAAI,CAAClE,qBAAL,CAA2BoC,YAA3B,GAA0C,KAAI,CAAC2B,UAAL,GAAkB,KAAI,CAACI,YAAL,CAAkBH,MAAlB,CAAyBC,CAAC,IAAIA,CAAC,CAACC,SAAhC,CAAlB,GAA+D,EAAzG;;UACA,IAAIE,UAAU,GAAG,KAAI,CAACC,gBAAL,EAAjB;;UACA,IAAI,CAACD,UAAU,CAACE,OAAhB,EAAyB;YACrB,KAAI,CAACzD,OAAL,CAAa0D,KAAb,CAAmBH,UAAU,CAACI,GAA9B,EAAmC,wBAAnC;;YACA;UACH;;UACD,IAAI,KAAI,CAACxE,qBAAL,CAA2BiC,UAA3B,IAAyC,GAA7C,EAAkD;YAC9C,KAAI,CAACjC,qBAAL,CAA2BkC,iBAA3B,GAA+C,EAA/C;YACA,KAAI,CAAClC,qBAAL,CAA2BmC,iBAA3B,GAA+C,EAA/C;UACH;;UACD,KAAI,CAACsC,WAAL,CAAiBC,IAAjB,CAAsB,KAAI,CAAC1E,qBAA3B;QACH;MACJ,CAtBD,CAuBA,OAAO2E,EAAP,EAAW;QACPC,OAAO,CAACL,KAAR,CAAcI,EAAd;MACH;IA1BW;EA2Bf;;EACOb,UAAU,CAACJ,iBAAD,EAAkB;IAChC,KAAK1D,qBAAL,CAA2BuB,qBAA3B,GACK,KAAKvB,qBAAL,CAA2B2C,aAA3B,IAA4C,KAA5C,IAAqD,KAAK3C,qBAAL,CAA2B2C,aAA3B,IAA4C,IAAlG,IACO,KAAK3C,qBAAL,CAA2B4C,eAA3B,IAA8C,KADrD,GAC6D,KAAKM,IAAL,CAAUC,QAAV,CAAmB,uBAAnB,EAA4CC,KADzG,GACiH,CAFrH,CADgC,CAIhC;;IACA,KAAKpD,qBAAL,CAA2BiC,UAA3B,GAAwC,KAAKiB,IAAL,CAAUC,QAAV,CAAmB,YAAnB,EAAiCC,KAAzE;IACA,KAAKpD,qBAAL,CAA2BqC,OAA3B,GAAqC,KAAKa,IAAL,CAAUC,QAAV,CAAmB,SAAnB,EAA8BC,KAAnE;IACA,KAAKpD,qBAAL,CAA2B6E,YAA3B,GAA0CnB,iBAA1C;EACH;;EAEOW,gBAAgB;IACpB,IAAIS,aAAa,GAAG,EAApB;IACA,IAAIR,OAAO,GAAG,IAAd;;IACA,IAAI,KAAKpB,IAAL,CAAUC,QAAV,CAAmBlB,UAAnB,CAA8BmB,KAA9B,IAAuC,GAA3C,EAAgD;MAC5C;MACA;MACAkB,OAAO,GAAG,KAAKtE,qBAAL,CAA2BmC,iBAA3B,CAA6C6B,MAA7C,CAAoDC,CAAC,IAAIA,CAAC,CAACC,SAA3D,EACLa,KADK,CACEC,MAAD,IAAW;QACd,IAAI,CAACA,MAAM,CAACC,WAAZ,EAAyB;UACrBH,aAAa,GAAG,mDAAmDE,MAAM,CAACE,UAA1D,GAAuE,IAAvF;UACA,OAAO,KAAP;QACH;;QACD,OAAO,IAAP;MACH,CAPK,CAAV;IAQH;;IACDZ,OAAO,GAAGQ,aAAa,GAAG,KAAH,GAAW,IAAlC;IACA,OAAO;MAAER,OAAO,EAAEA,OAAX;MAAoBE,GAAG,EAAEM;IAAzB,CAAP;EACH;;EACOK,uBAAuB;IAC3B,KAAK3C,yBAAL,CAA+B4C,SAA/B,GAA2C,IAA3C;IACA,KAAK5C,yBAAL,CAA+B6C,KAA/B,GAAuC,mBAAvC;IACA,KAAK7C,yBAAL,CAA+B8C,MAA/B,GAAwC,KAAKC,gBAA7C;EACH;;EACDC,aAAa;IACT,IAAI,KAAKtC,IAAL,CAAUC,QAAV,CAAmB,UAAnB,EAA+BC,KAA/B,IAAwC,QAA5C,EAAsD;MAClD,KAAKF,IAAL,CAAUC,QAAV,CAAmBxB,kBAAnB,CAAsC8D,KAAtC;MACA,KAAKvC,IAAL,CAAUC,QAAV,CAAmBxB,kBAAnB,CAAsC+D,OAAtC;MACA,KAAKxC,IAAL,CAAUC,QAAV,CAAmBxB,kBAAnB,CAAsCgE,eAAtC;IACH,CAJD,MAKK;MACD,KAAKzC,IAAL,CAAUC,QAAV,CAAmBxB,kBAAnB,CAAsCiE,MAAtC;IACH;EACJ;;EAEDC,4BAA4B,CAACzC,KAAD,EAAM;IAC9B,IAAIA,KAAK,IAAI,KAAb,EAAoB;MAChB,KAAKF,IAAL,CAAUC,QAAV,CAAmB/B,iBAAnB,CAAqC0E,QAArC,CAA8C,CAA9C;MACA,KAAK5C,IAAL,CAAUC,QAAV,CAAmB7B,eAAnB,CAAmCwE,QAAnC,CAA4C,EAA5C;MACA,KAAK5C,IAAL,CAAUC,QAAV,CAAmB/B,iBAAnB,CAAqC2E,aAArC,CAAmD,CAAC7F,UAAU,CAACe,QAAZ,EAAsBV,uBAAuB,CAACc,4BAAxB,CAAqD,mBAArD,EAA0E,iBAA1E,CAAtB,CAAnD;IACH,CAJD,MAKK;MACD,KAAK6B,IAAL,CAAUC,QAAV,CAAmB/B,iBAAnB,CAAqCuE,eAArC;MACA,KAAKzC,IAAL,CAAUC,QAAV,CAAmB/B,iBAAnB,CAAqC4E,sBAArC;MACA,KAAK9C,IAAL,CAAUC,QAAV,CAAmB7B,eAAnB,CAAmCwE,QAAnC,CAA4C,EAA5C;MACA,KAAK5C,IAAL,CAAUC,QAAV,CAAmB/B,iBAAnB,CAAqC0E,QAArC,CAA8C,EAA9C;IAEH;EACJ;;EAEDG,wBAAwB,CAACC,oBAAD,EAAuBC,qBAAvB,EAA8CC,iBAA9C,EAAiEC,aAAjE,EAAgFC,uBAAhF,EAAuG;IAE3H,IAAIC,QAAQ,GAAG;MACXC,YAAY,EAAE,KAAKC,wBAAL,CAA8B,KAAKC,IAAnC,CADH;MAEXN,iBAAiB,EAAE,KAAKK,wBAAL,CAA8BL,iBAA9B,CAFR;MAGXD,qBAAqB,EAAE,KAAKM,wBAAL,CAA8BN,qBAA9B,CAHZ;MAIXD,oBAAoB,EAAEA,oBAJX;MAKXS,eAAe,EAAE,KAAK3G,qBAAL,CAA2B4G,eALjC;MAMXC,WAAW,EAAE,KAAKA,WANP;MAOXC,YAAY,EAAE,KAAKA,YAPR;MAQXC,aAAa,EAAE,KAAK/G,qBAAL,CAA2B2C,aAA3B,IAA4C,EARhD;MASXqE,eAAe,EAAE,KAAKhH,qBAAL,CAA2B4C,eATjC;MAUXqE,YAAY,EAAE,KAAKjH,qBAAL,CAA2B6E,YAV9B;MAWXhC,KAAK,EAAE,KAAKA,KAXD;MAYXqE,YAAY,EAAE,KAAKC,eAAL,CAAqBC,GAArB,CAAyBnD,CAAC,IAAG;QACvC,IAAIoD,IAAI,GAAG;UAAEC,EAAE,EAAErD,CAAC,CAACsD,SAAR;UAAmBnE,KAAK,EAAEa,CAAC,CAACuD,WAA5B;UAAyCC,WAAW,EAAExD,CAAC,CAACwD;QAAxD,CAAX;QACA,OAAOJ,IAAP;MACH,CAHa,CAZH;MAgBXhB,aAAa,EAAEA,aAhBJ;MAiBXC,uBAAuB,EAAE,KAAKtG,qBAAL,CAA2B0H;IAjBzC,CAAf;;IAuBA,IAAI,KAAK1H,qBAAL,CAA2B4G,eAA3B,IAA8C,KAA9C,IACI,KAAK5G,qBAAL,CAA2B4G,eAA3B,IAA8C,KAA9C,IACI,KAAK5G,qBAAL,CAA2B4C,eAA3B,IAA8C,IAA9C,IAAsD,KAAK5C,qBAAL,CAA2B4C,eAA3B,IAA8C,IAApG,IACG,KAAK5C,qBAAL,CAA2B4C,eAA3B,IAA8C,KAFrD,IAGI,KAAK5C,qBAAL,CAA2B2C,aAA3B,IAA4C,IAA5C,IAAoD,KAAK3C,qBAAL,CAA2B2C,aAA3B,IAA4C,KAJ5G,EAIqH;MACjH,KAAKgF,gBAAL,CAAsB,IAAtB;IACH;;IAED,KAAK7G,gBAAL,CAAsB8G,QAAtB,CAA+BrB,QAA/B;EAEH;;EAEOE,wBAAwB,CAACY,IAAD,EAAY;IACxC,IAAIQ,UAAU,GAAGR,IAAI,CAChBD,GADY,CACPU,IAAD,IAAS;MACV,IAAIT,IAAI,GAAG;QAAEC,EAAE,EAAEQ,IAAI,CAACC,YAAX;QAAyB3E,KAAK,EAAE0E,IAAI,CAACE;MAArC,CAAX;MACA,OAAOX,IAAP;IACH,CAJY,CAAjB;IAKA,OAAOQ,UAAU,IAAI,EAArB;EACH;;EAEDF,gBAAgB,CAACM,OAAD,EAAQ;IACpB,IAAI,KAAKvF,qBAAL,CAA2BwF,cAA3B,IAA6C,CAACD,OAAlD,EAA2D;MAEvD,KAAK,IAAIE,KAAK,GAAG,KAAKzF,qBAAL,CAA2BmC,YAA3B,CAAwCuD,MAAxC,GAAiD,CAAlE,EAAqED,KAAK,IAAI,CAA9E,EAAiFA,KAAK,EAAtF,EAA0F;QACtF,IAAIA,KAAK,IAAI,CAAb,EAAgB;QAChB,KAAKzF,qBAAL,CAA2BmC,YAA3B,CAAwCwD,QAAxC,CAAiDF,KAAjD;MACH;IACJ;;IAED,KAAKzF,qBAAL,CAA2BwF,cAA3B,GAA4CD,OAA5C;EACH;;EAEDK,WAAW;IAEP,IAAIC,uBAAuB,GAAG,KAAKvI,qBAAL,CAA2B0H,uBAA3B,IAAsD,KAAKc,iBAAL,CAAuBpB,GAAvB,CAA2BnD,CAAC,IAAIA,CAAC,CAACqD,EAAlC,EAAsCmB,QAAtC,CAA+C,KAAKzI,qBAAL,CAA2B0H,uBAA1E,CAApF;IAEA,KAAK3D,UAAL,GAAmB,KAAK/D,qBAAL,CAA2B4C,eAA3B,IAA8C,IAA9C,IAAsD,KAAK5C,qBAAL,CAA2B4C,eAA3B,IAA8C,IAApG,IAA4G,KAAK5C,qBAAL,CAA2B4C,eAA3B,IAA8C,KAA3J,GACX,KAAK5C,qBAAL,CAA2B2C,aAA3B,IAA4C,KAA5C,IAAqD,KAAK3C,qBAAL,CAA2B2C,aAA3B,IAA4C,IAAlG,IACE,CAAC,KAAK3C,qBAAL,CAA2B2C,aAA3B,IAA4C,KAA5C,IAAqD,KAAK3C,qBAAL,CAA2B2C,aAA3B,IAA4C,IAAlG,KACG4F,uBADH,IAC8B,CAAC,CAAC,KAAD,EAAQ,KAAR,EAAeE,QAAf,CAAwB,KAAKzI,qBAAL,CAA2B0H,uBAAnD,CAHrB,GAGoG,IAHtH;IAKA,OAAO,KAAK3D,UAAZ;EACH;;EAED2E,kBAAkB,CAACrB,IAAD,EAAOsB,UAAP,EAAmB5B,aAAnB,EAAkC6B,WAAlC,EAA6C;IAC3D,IAAIC,YAAY,GAAGxB,IAAI,CAACyB,IAAL,CAAW7E,CAAD,IAAOA,CAAC,CAAC0E,UAAF,KAAiBA,UAAlC,CAAnB;IAEA,IAAI,KAAK3I,qBAAL,CAA2B4C,eAA3B,IAA8C,IAA9C,IAAsD,KAAK5C,qBAAL,CAA2B4C,eAA3B,IAA8C,IAApG,IACA,KAAK5C,qBAAL,CAA2B4C,eAA3B,IAA8C,KADlD,EACyD,OAAOiG,YAAY,CAACE,OAApB;;IAEzD,IAAKhC,aAAa,IAAI,KAAjB,IAA0BA,aAAa,IAAI,IAA5C,IAAqD6B,WAAzD,EAAsE;MAClE,OAAOC,YAAY,CAACE,OAAb,CAAqB/E,MAArB,CAA4BC,CAAC,IAAIA,CAAC,CAAC0C,eAAF,IAAqBI,aAArB,IAAsC9C,CAAC,CAAC+E,UAAF,IAAgBJ,WAAvF,CAAP;IACH,CAFD,MAGK,IAAI7B,aAAa,IAAI,KAAjB,IAA0BA,aAAa,IAAI,IAA/C,EAAqD;MACtD,OAAO8B,YAAY,CAACE,OAAb,CAAqB/E,MAArB,CAA4BC,CAAC,IAAIA,CAAC,CAAC0C,eAAF,IAAqB,EAArB,IAA2B1C,CAAC,CAAC+E,UAAF,IAAgB,EAA5E,CAAP;IACH,CAFI,MAGA;MACD,OAAO,EAAP;IACH;EACJ;;EAKDC,wBAAwB,CAAC7F,KAAD,EAAM;IAC1B,IAAI,KAAKV,qBAAT,EAAgC;MAC5B,KAAKA,qBAAL,CAA2BwG,6BAA3B,CAAyD9F,KAAzD;IACH;EACJ;;EAGD+F,gBAAgB,CAAC9B,IAAD,EAAON,aAAP,EAAuB6B,WAAvB,EAAmC;IAC/C,IAAIvB,IAAI,CAACyB,IAAL,CAAW7E,CAAD,IAAOA,CAAC,CAAC0E,UAAF,KAAiB,GAAlC,CAAJ,EAA4C;MACxC,IAAIS,eAAe,GAAG,KAAKV,kBAAL,CAAwBrB,IAAxB,EAA8B,GAA9B,EAAmCN,aAAnC,EAAkD6B,WAAlD,CAAtB;MAEA,KAAK1G,iBAAL,GAAyBkH,eAAe,CAAChC,GAAhB,CAAoBnD,CAAC,IAAG;QAC7C,IAAIoF,UAAU,GAAG,IAAIpJ,MAAJ,EAAjB;QACAoJ,UAAU,CAACC,KAAX,GAAmBrF,CAAC,CAACsF,WAArB;QACAF,UAAU,CAACG,QAAX,GAAsBvF,CAAC,CAACwF,gBAAxB;QACAJ,UAAU,CAACnE,UAAX,GAAwBjB,CAAC,CAACyF,kBAA1B;QACAL,UAAU,CAACnF,SAAX,GAAuB,KAAvB;QACA,KAAKyF,YAAL,GAAoB1F,CAAC,CAACsF,WAAtB;QACA,OAAOF,UAAP;MAEH,CATwB,CAAzB;IAUH;;IAGD,IAAIhC,IAAI,CAACyB,IAAL,CAAW7E,CAAD,IAAOA,CAAC,CAAC0E,UAAF,KAAiB,GAAlC,CAAJ,EAA4C;MAExC,IAAIS,eAAe,GAAG,KAAKV,kBAAL,CAAwBrB,IAAxB,EAA8B,GAA9B,EAAmCN,aAAnC,EAAkD6B,WAAlD,CAAtB;MAGA,KAAKzG,iBAAL,GAAyBiH,eAAe,CAAChC,GAAhB,CAAoBnD,CAAC,IAAG;QAC7C,IAAIoF,UAAU,GAAG,IAAIpJ,MAAJ,EAAjB;QACAoJ,UAAU,CAACC,KAAX,GAAmBrF,CAAC,CAACsF,WAArB;QACAF,UAAU,CAACG,QAAX,GAAsBvF,CAAC,CAACwF,gBAAxB;QACAJ,UAAU,CAACnE,UAAX,GAAwBjB,CAAC,CAACyF,kBAA1B;QACAL,UAAU,CAACO,WAAX,GAAyB3F,CAAC,CAAC4F,mBAA3B;QACAR,UAAU,CAACnF,SAAX,GAAuB,KAAvB;QACA,KAAK4F,YAAL,GAAoB7F,CAAC,CAACsF,WAAtB;QACA,OAAOF,UAAP;MACH,CATwB,CAAzB;IAUH;;IAGD,IAAIhC,IAAI,CAACyB,IAAL,CAAW7E,CAAD,IAAOA,CAAC,CAAC0E,UAAF,KAAiB,GAAlC,CAAJ,EAA4C;MAExC,IAAIS,eAAe,GAAG,KAAKV,kBAAL,CAAwBrB,IAAxB,EAA8B,GAA9B,EAAmCN,aAAnC,EAAkD6B,WAAlD,CAAtB;MAEA,KAAKzE,YAAL,GAAoBiF,eAAe,CAAChC,GAAhB,CAAoBnD,CAAC,IAAG;QACxC,IAAIoF,UAAU,GAAG,IAAIpJ,MAAJ,EAAjB;QACAoJ,UAAU,CAACC,KAAX,GAAmBrF,CAAC,CAACsF,WAArB;QACAF,UAAU,CAACG,QAAX,GAAsBvF,CAAC,CAACwF,gBAAxB;QACAJ,UAAU,CAACnE,UAAX,GAAwBjB,CAAC,CAACyF,kBAA1B;QACAL,UAAU,CAACO,WAAX,GAAyB3F,CAAC,CAAC4F,mBAA3B;QACAR,UAAU,CAACnF,SAAX,GAAuB,IAAvB;QACA,KAAK6F,YAAL,GAAoB9F,CAAC,CAACsF,WAAtB;QACA,OAAOF,UAAP;MACH,CATmB,CAApB;IAUH;EAGJ;;AA7UmC;;;;;;;;;;;;UAEnCtJ;;;UACAA;;;UACAA;;;UACAA;;;UAMAF;IAASmK,OAAC,2BAAD,EAA8B;MAAEC,IAAI,EAAE3J,sBAAR;MAAgC4J,MAAM,EAAE;IAAxC,CAA9B;;;UAKTrK;IAASmK,OAAC,uBAAD,EAA0B;MAAEE,MAAM,EAAE;IAAV,CAA1B;;;AAhBDxJ,2BAA2B,eALvCf,SAAS,CAAC;EACPwK,QAAQ,EAAE,qBADH;EAEPC,8BAFO;EAGPC,SAAS,EAAE,CAAC5J,uBAAD;AAHJ,CAAD,CAK8B,GAA3BC,2BAA2B,CAA3B;SAAAA", "names": ["Component", "EventEmitter", "ViewChild", "ChangeDetectorRef", "Input", "MembershipCertificate", "Legend", "Validators", "FormControl", "FormGroup", "ToastrService", "ColorSelectorComponent", "CustomSharedValidations", "StringValue", "MultiCertificateService", "CertificateDetailsComponent", "constructor", "cdr", "toaster", "mutliCertService", "addToOrder", "Designation", "required", "OtherDesignation", "CertificateNumbering", "CertificateFromNo", "validateCertificateNumbering", "CertificateToNo", "TotalAuthorisedShares", "OtherProfessionalCorpType", "MultiCerts", "ParValue", "ParValueCustomized", "CertificateColor", "CertificateSignature1", "OtherSignatureTitle1", "CertificateSignature2", "OtherSignatureTitle2", "AddLegends", "AdditionalLegends", "CustomizedLegends", "OtherLegends", "Remarks", "max<PERSON><PERSON><PERSON>", "CertCompo", "CertificateColorComponent", "certControlsComponent", "CertControlsComponent", "FormationType", "CorporationType", "isAdd", "detectChanges", "ngOnInit", "ngAfterContentChecked", "validateAuthorisedShares", "Form", "controls", "value", "certificateNumberingCount", "warning", "onAddOrder", "getCertificateNumberingCount", "removeValidationFromDesignation", "multiCertificates", "getCertificateData", "valid", "CertificatesForm", "mapToModel", "showLegend", "filter", "x", "IsChecked", "otherLegends", "validation", "legendValidation", "Is<PERSON><PERSON><PERSON>", "error", "Msg", "$onAddOrder", "emit", "ex", "console", "Certificates", "validationMsg", "every", "legend", "LegendValue", "LegendText", "renderCertificateColors", "IsVisible", "Label", "Colors", "certificateColor", "checkParValue", "reset", "disable", "clearValidators", "enable", "OnCertificateNumberingChange", "setValue", "setValidators", "updateValueAndValidity", "sendMultiCertificateData", "certificateNumbering", "certificateSignatures", "certificateColors", "allLegendData", "professionalCorporation", "response", "parValueData", "MapMasterDataByControlId", "Pars", "subCategoryCode", "SubCategoryCode", "productCode", "categoryCode", "formationType", "corporationType", "certificates", "designations", "designationData", "map", "data", "id", "classCode", "description", "showTextbox", "ProfessionalCorporation", "ToggleMulticerts", "postData", "mappedData", "item", "controlValue", "controlText", "checked", "showMulticerts", "index", "length", "removeAt", "showLegends", "isValidProfessionalCorp", "formationSubTypes", "includes", "getFilteredLegends", "legendType", "corpSubType", "filteredData", "find", "legends", "optionCode", "onCorporateSubTypeChange", "mapLegendsOnCorpSubTypeChange", "filterLegendData", "filteredLegends", "legendData", "Price", "retailPrice", "LegendID", "productionNumber", "webDescriptionline", "legendPriceA", "LegendText2", "webDescriptionline2", "legendPriceC", "legendPriceO", "args", "read", "static", "selector", "template", "providers"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\viatech\\Ecom\\client\\acs\\src\\app\\Modules\\IndividualProducts\\Pages\\membership-certificate\\Components\\certificate-details\\certificate-details.component.ts"], "sourcesContent": ["import { Component, OnInit, EventEmitter, ViewChild, AfterViewInit, AfterContentChecked, ChangeDetectorRef, Input } from '@angular/core';\r\nimport { MembershipCertificate, CustomizableLegend, Legend } from 'src/app/Modules/Shared/Models/IndividualProducts/MembershipCertificate';\r\nimport { Validators, FormControl, FormGroup } from '@angular/forms';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { ColorSelectorComponent } from 'src/app/Modules/Shared/Components/formComponents/color-selector/color-selector.component';\r\nimport { CustomSharedValidations } from 'src/app/Modules/Shared/functions/custom-validations';\r\nimport { StringValue } from 'src/app/Modules/Shared/Enums/string-values.enum';\r\nimport { CertificateControlsComponent } from '../certificate-controls/certificate-controls.component';\r\nimport { ProductDetailMasterDataModel } from 'src/app/Modules/Shared/Models/KitsModel/product-detail-master-data.model';\r\nimport { MultiCertificateService } from 'src/app/Modules/IndividualProducts/Services/multi-certificate.service';\r\n\r\n@Component({\r\n    selector: 'certificate-details',\r\n    templateUrl: 'certificate-details.component.html',\r\n    providers: [MultiCertificateService]\r\n})\r\nexport class CertificateDetailsComponent implements OnInit, AfterContentChecked {\r\n\r\n    @Input() label: string;\r\n    @Input() bulkPricing: string;\r\n    @Input() productCode: string;\r\n    @Input() categoryCode: string;\r\n    constructor(private cdr: ChangeDetectorRef, private toaster: ToastrService, private mutliCertService: MultiCertificateService) { }\r\n\r\n    CertificateColorComponent: ColorSelectorComponent;\r\n    CertControlsComponent: CertificateControlsComponent;\r\n\r\n    @ViewChild('CertificateColorComponent', { read: ColorSelectorComponent, static: true }) set CertCompo(CertificateColorComponent: ColorSelectorComponent) {\r\n        this.CertificateColorComponent = CertificateColorComponent\r\n    }\r\n\r\n\r\n    @ViewChild(\"CertControlsComponent\", { static: false })\r\n    set certControlsComponent(\r\n        certControlsComponent: CertificateControlsComponent\r\n    ) {\r\n        if (certControlsComponent) {\r\n            this.CertControlsComponent = certControlsComponent;\r\n            this.CertControlsComponent.FormationType = this.MembershipCertificate.CorporationType;\r\n            this.CertControlsComponent.isAdd = this.isAdd;\r\n\r\n        }\r\n        this.cdr.detectChanges()\r\n    }\r\n\r\n    MembershipCertificate: MembershipCertificate = new MembershipCertificate();\r\n    isAdd: boolean = false;\r\n    certificateNumbering: any[] = [];\r\n    Designations: any[] = [];\r\n    CertificateSignature1: any[] = [];\r\n    CertificateSignature2: any[] = [];\r\n    States: any[] = [];\r\n    Pars: any[] = [];\r\n    certificateColor: any[] = [];\r\n    AdditionalLegends: Legend[] = [];\r\n    otherLegends: Legend[] = [];\r\n    CustomizedLegends: CustomizableLegend[] = [];\r\n    $onAddOrder = new EventEmitter<any>();\r\n    buttonText: string = StringValue.addToOrder;\r\n    legendPriceA: string;\r\n    legendPriceC: string;\r\n    legendPriceO: string;\r\n    showCertificateNumbering: boolean = false;\r\n    certificateMasterdata: ProductDetailMasterDataModel;\r\n    formationSubTypes: any[] = [];\r\n    showLegend: boolean = false;\r\n    allLegendData: any[] = [];\r\n    designationData: any[] = [];\r\n    showMultiCerts: boolean = false;\r\n\r\n    ngOnInit() {\r\n    }\r\n    ngAfterContentChecked() {\r\n        // this.renderCertificateColors();\r\n        // this.checkParValue();\r\n    }\r\n\r\n    Form = new FormGroup({\r\n        Designation: new FormControl<string | null>('', [Validators.required]),\r\n        OtherDesignation: new FormControl<string | null>(''),\r\n        CertificateNumbering: new FormControl<any | null>('', [Validators.required]),\r\n        CertificateFromNo: new FormControl<any | null>('', [Validators.required, CustomSharedValidations.validateCertificateNumbering('CertificateFromNo', 'CertificateToNo')]),\r\n        CertificateToNo: new FormControl<any | null>('', [Validators.required, CustomSharedValidations.validateCertificateNumbering('CertificateFromNo', 'CertificateToNo')]),\r\n        TotalAuthorisedShares: new FormControl<any | null>('', [Validators.required]),\r\n        //ProfessionalCorporation: new FormControl(null),\r\n        OtherProfessionalCorpType: new FormControl<string | null>(null),\r\n        MultiCerts: new FormControl<any | null>(null),\r\n        // ReflectiveCerts : new FormControl(null),\r\n        ParValue: new FormControl<any | null>('', Validators.required),\r\n        ParValueCustomized: new FormControl<any | null>('', Validators.required),\r\n        CertificateColor: new FormControl<string | null>('', [Validators.required]),\r\n        CertificateSignature1: new FormControl<string | null>('', [Validators.required]),\r\n        OtherSignatureTitle1: new FormControl<string | null>(''),\r\n        CertificateSignature2: new FormControl<string | null>('', [Validators.required]),\r\n        OtherSignatureTitle2: new FormControl<string | null>('', Validators.required),\r\n        AddLegends: new FormControl<any | null>('', [Validators.required]),\r\n        AdditionalLegends: new FormControl<any | null>(''),\r\n        CustomizedLegends: new FormControl<any | null>(''),\r\n        OtherLegends: new FormControl<any | null>(''),\r\n        Remarks: new FormControl<string | null>('', [CustomSharedValidations.maxLength(250)]),\r\n    })\r\n\r\n    validateAuthorisedShares() {\r\n        if (this.MembershipCertificate.FormationType != '010' && this.MembershipCertificate.FormationType != 'FP') return;\r\n        if (this.Form.controls.TotalAuthorisedShares.value && this.Form.controls.TotalAuthorisedShares.value < this.CertControlsComponent.certificateNumberingCount) {\r\n            this.toaster.warning(\r\n                \"Total Authorized Shares should not be less than total Certificates.\",\r\n                \"Warning\"\r\n            );\r\n            return true;\r\n        }\r\n    }\r\n\r\n    async onAddOrder() {\r\n        try {\r\n            this.CertControlsComponent.getCertificateNumberingCount()\r\n            this.CertControlsComponent.removeValidationFromDesignation()\r\n            var multiCertificates = await this.CertControlsComponent.getCertificateData();\r\n            this.validateAuthorisedShares();\r\n\r\n            if (this.Form.valid && this.CertControlsComponent.Form.controls.CertificatesForm.valid) {\r\n                this.mapToModel(multiCertificates);\r\n                this.MembershipCertificate.AdditionalLegends = this.showLegend ? this.AdditionalLegends.filter(x => x.IsChecked) : [];\r\n                this.MembershipCertificate.CustomizedLegends = this.showLegend ? this.CustomizedLegends.filter(x => x.IsChecked) : [];\r\n                this.MembershipCertificate.OtherLegends = this.showLegend ? this.otherLegends.filter(x => x.IsChecked) : [];\r\n                let validation = this.legendValidation();\r\n                if (!validation.IsValid) {\r\n                    this.toaster.error(validation.Msg, \"Unable to Add To Cart.\");\r\n                    return;\r\n                }\r\n                if (this.MembershipCertificate.AddLegends == 'N') {\r\n                    this.MembershipCertificate.AdditionalLegends = [];\r\n                    this.MembershipCertificate.CustomizedLegends = [];\r\n                }\r\n                this.$onAddOrder.emit(this.MembershipCertificate)\r\n            }\r\n        }\r\n        catch (ex) {\r\n            console.error(ex);\r\n        }\r\n    }\r\n    private mapToModel(multiCertificates) {\r\n        this.MembershipCertificate.TotalAuthorisedShares =\r\n            (this.MembershipCertificate.FormationType != '030' && this.MembershipCertificate.FormationType != 'NP')\r\n                && this.MembershipCertificate.CorporationType == '001' ? this.Form.controls['TotalAuthorisedShares'].value : 0\r\n        // this.MembershipCertificate.Reflective = this.Form.controls['ReflectiveCerts'].value\r\n        this.MembershipCertificate.AddLegends = this.Form.controls['AddLegends'].value\r\n        this.MembershipCertificate.Remarks = this.Form.controls['Remarks'].value\r\n        this.MembershipCertificate.Certificates = multiCertificates\r\n    }\r\n\r\n    private legendValidation() {\r\n        let validationMsg = \"\"\r\n        let IsValid = true;\r\n        if (this.Form.controls.AddLegends.value == 'Y') {\r\n            // Validate Additional Legend : Not Required\r\n            //Validate Customized Legends \r\n            IsValid = this.MembershipCertificate.CustomizedLegends.filter(x => x.IsChecked)\r\n                .every((legend) => {\r\n                    if (!legend.LegendValue) {\r\n                        validationMsg = \"Please enter the value for Aditional Legend \\\"\" + legend.LegendText + \"\\\"\";\r\n                        return false\r\n                    }\r\n                    return true;\r\n                })\r\n        }\r\n        IsValid = validationMsg ? false : true;\r\n        return { IsValid: IsValid, Msg: validationMsg }\r\n    }\r\n    private renderCertificateColors() {\r\n        this.CertificateColorComponent.IsVisible = true;\r\n        this.CertificateColorComponent.Label = 'Certificate Color';\r\n        this.CertificateColorComponent.Colors = this.certificateColor;\r\n    }\r\n    checkParValue() {\r\n        if (this.Form.controls['ParValue'].value != 'CRPVSH') {\r\n            this.Form.controls.ParValueCustomized.reset();\r\n            this.Form.controls.ParValueCustomized.disable();\r\n            this.Form.controls.ParValueCustomized.clearValidators;\r\n        }\r\n        else {\r\n            this.Form.controls.ParValueCustomized.enable();\r\n        }\r\n    }\r\n\r\n    OnCertificateNumberingChange(value) {\r\n        if (value == \"Yes\") {\r\n            this.Form.controls.CertificateFromNo.setValue(1);\r\n            this.Form.controls.CertificateToNo.setValue(20);\r\n            this.Form.controls.CertificateFromNo.setValidators([Validators.required, CustomSharedValidations.validateCertificateNumbering('CertificateFromNo', 'CertificateToNo')]);\r\n        }\r\n        else {\r\n            this.Form.controls.CertificateFromNo.clearValidators();\r\n            this.Form.controls.CertificateFromNo.updateValueAndValidity();\r\n            this.Form.controls.CertificateToNo.setValue(20);\r\n            this.Form.controls.CertificateFromNo.setValue(\"\");\r\n\r\n        }\r\n    }\r\n\r\n    sendMultiCertificateData(certificateNumbering, certificateSignatures, certificateColors, allLegendData, professionalCorporation) {\r\n\r\n        var response = {\r\n            parValueData: this.MapMasterDataByControlId(this.Pars),\r\n            certificateColors: this.MapMasterDataByControlId(certificateColors),\r\n            certificateSignatures: this.MapMasterDataByControlId(certificateSignatures),\r\n            certificateNumbering: certificateNumbering,\r\n            subCategoryCode: this.MembershipCertificate.SubCategoryCode,\r\n            productCode: this.productCode,\r\n            categoryCode: this.categoryCode,\r\n            formationType: this.MembershipCertificate.FormationType || '',\r\n            corporationType: this.MembershipCertificate.CorporationType,\r\n            certificates: this.MembershipCertificate.Certificates,\r\n            isAdd: this.isAdd,\r\n            designations: this.designationData.map(x => {\r\n                var data = { id: x.classCode, value: x.description, showTextbox: x.showTextbox };\r\n                return data;\r\n            }),\r\n            allLegendData: allLegendData,\r\n            professionalCorporation: this.MembershipCertificate.ProfessionalCorporation\r\n\r\n\r\n        };\r\n\r\n\r\n        if (this.MembershipCertificate.SubCategoryCode == 'CDX'\r\n            || (this.MembershipCertificate.SubCategoryCode != 'CDX'\r\n                && (this.MembershipCertificate.CorporationType != 'FP' && this.MembershipCertificate.CorporationType != 'SC'\r\n                    && this.MembershipCertificate.CorporationType != '001')\r\n                || (this.MembershipCertificate.FormationType != 'FP' && this.MembershipCertificate.FormationType != '010'))) {\r\n            this.ToggleMulticerts(true);\r\n        }\r\n\r\n        this.mutliCertService.postData(response);\r\n\r\n    }\r\n\r\n    private MapMasterDataByControlId(data: any[]) {\r\n        let mappedData = data\r\n            .map((item) => {\r\n                var data = { id: item.controlValue, value: item.controlText };\r\n                return data;\r\n            });\r\n        return mappedData || [];\r\n    }\r\n\r\n    ToggleMulticerts(checked) {\r\n        if (this.CertControlsComponent.showMulticerts && !checked) {\r\n\r\n            for (let index = this.CertControlsComponent.Certificates.length - 1; index >= 0; index--) {\r\n                if (index == 0) continue;\r\n                this.CertControlsComponent.Certificates.removeAt(index);\r\n            }\r\n        }\r\n\r\n        this.CertControlsComponent.showMulticerts = checked\r\n    }\r\n\r\n    showLegends() {\r\n\r\n        var isValidProfessionalCorp = this.MembershipCertificate.ProfessionalCorporation && this.formationSubTypes.map(x => x.id).includes(this.MembershipCertificate.ProfessionalCorporation);\r\n\r\n        this.showLegend = (this.MembershipCertificate.CorporationType == 'FP' || this.MembershipCertificate.CorporationType == 'SC' || this.MembershipCertificate.CorporationType == '001')\r\n            ? (this.MembershipCertificate.FormationType != '030' && this.MembershipCertificate.FormationType != 'NP')\r\n            || ((this.MembershipCertificate.FormationType == '030' || this.MembershipCertificate.FormationType == 'NP')\r\n                && isValidProfessionalCorp && !['060', '065'].includes(this.MembershipCertificate.ProfessionalCorporation)) : true;\r\n\r\n        return this.showLegend;\r\n    }\r\n\r\n    getFilteredLegends(data, legendType, formationType, corpSubType) {\r\n        var filteredData = data.find((x) => x.legendType === legendType);\r\n\r\n        if (this.MembershipCertificate.CorporationType != 'FP' && this.MembershipCertificate.CorporationType != 'SC' &&\r\n            this.MembershipCertificate.CorporationType != '001') return filteredData.legends;\r\n\r\n        if ((formationType != '010' && formationType != 'FP') && corpSubType) {\r\n            return filteredData.legends.filter(x => x.subCategoryCode == formationType && x.optionCode == corpSubType);\r\n        }\r\n        else if (formationType == '010' || formationType == 'FP') {\r\n            return filteredData.legends.filter(x => x.subCategoryCode == '' && x.optionCode == '');\r\n        }\r\n        else {\r\n            return [];\r\n        }\r\n    }\r\n\r\n\r\n\r\n\r\n    onCorporateSubTypeChange(value) {\r\n        if (this.CertControlsComponent) {\r\n            this.CertControlsComponent.mapLegendsOnCorpSubTypeChange(value);\r\n        }\r\n    }\r\n\r\n\r\n    filterLegendData(data, formationType?, corpSubType?) {\r\n        if (data.find((x) => x.legendType === 'A')) {\r\n            var filteredLegends = this.getFilteredLegends(data, 'A', formationType, corpSubType);\r\n\r\n            this.AdditionalLegends = filteredLegends.map(x => {\r\n                var legendData = new Legend();\r\n                legendData.Price = x.retailPrice;\r\n                legendData.LegendID = x.productionNumber;\r\n                legendData.LegendText = x.webDescriptionline;\r\n                legendData.IsChecked = false;\r\n                this.legendPriceA = x.retailPrice;\r\n                return legendData;\r\n\r\n            })\r\n        }\r\n\r\n\r\n        if (data.find((x) => x.legendType === 'C')) {\r\n\r\n            var filteredLegends = this.getFilteredLegends(data, 'C', formationType, corpSubType);\r\n\r\n\r\n            this.CustomizedLegends = filteredLegends.map(x => {\r\n                var legendData = new Legend();\r\n                legendData.Price = x.retailPrice;\r\n                legendData.LegendID = x.productionNumber;\r\n                legendData.LegendText = x.webDescriptionline;\r\n                legendData.LegendText2 = x.webDescriptionline2;\r\n                legendData.IsChecked = false;\r\n                this.legendPriceC = x.retailPrice\r\n                return legendData;\r\n            })\r\n        }\r\n\r\n\r\n        if (data.find((x) => x.legendType === 'O')) {\r\n\r\n            var filteredLegends = this.getFilteredLegends(data, 'O', formationType, corpSubType);\r\n\r\n            this.otherLegends = filteredLegends.map(x => {\r\n                var legendData = new Legend();\r\n                legendData.Price = x.retailPrice;\r\n                legendData.LegendID = x.productionNumber;\r\n                legendData.LegendText = x.webDescriptionline;\r\n                legendData.LegendText2 = x.webDescriptionline2;\r\n                legendData.IsChecked = true;\r\n                this.legendPriceO = x.retailPrice\r\n                return legendData;\r\n            })\r\n        }\r\n\r\n\r\n    }\r\n\r\n}"]}, "metadata": {}, "sourceType": "module"}