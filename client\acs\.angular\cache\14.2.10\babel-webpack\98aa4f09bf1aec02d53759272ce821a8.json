{"ast": null, "code": "export function isObject(x) {\n  return x !== null && typeof x === 'object';\n}", "map": {"version": 3, "names": ["isObject", "x"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/isObject.js"], "sourcesContent": ["export function isObject(x) {\n    return x !== null && typeof x === 'object';\n}\n"], "mappings": "AAAA,OAAO,SAASA,QAAT,CAAkBC,CAAlB,EAAqB;EACxB,OAAOA,CAAC,KAAK,IAAN,IAAc,OAAOA,CAAP,KAAa,QAAlC;AACH"}, "metadata": {}, "sourceType": "module"}