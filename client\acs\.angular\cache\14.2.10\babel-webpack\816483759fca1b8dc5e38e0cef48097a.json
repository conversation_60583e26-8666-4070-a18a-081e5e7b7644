{"ast": null, "code": "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "map": {"version": 3, "names": ["getAltAxis", "axis"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/utils/getAltAxis.js"], "sourcesContent": ["export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}"], "mappings": "AAAA,eAAe,SAASA,UAAT,CAAoBC,IAApB,EAA0B;EACvC,OAAOA,IAAI,KAAK,GAAT,GAAe,GAAf,GAAqB,GAA5B;AACD"}, "metadata": {}, "sourceType": "module"}