{"ast": null, "code": ";\n\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * ISO 10126 padding strategy.\n   */\n  CryptoJS.pad.Iso10126 = {\n    pad: function (data, blockSize) {\n      // Shortcut\n      var blockSizeBytes = blockSize * 4; // Count padding bytes\n\n      var nPaddingBytes = blockSizeBytes - data.sigBytes % blockSizeBytes; // Pad\n\n      data.concat(CryptoJS.lib.WordArray.random(nPaddingBytes - 1)).concat(CryptoJS.lib.WordArray.create([nPaddingBytes << 24], 1));\n    },\n    unpad: function (data) {\n      // Get number of padding bytes from last byte\n      var nPaddingBytes = data.words[data.sigBytes - 1 >>> 2] & 0xff; // Remove padding\n\n      data.sigBytes -= nPaddingBytes;\n    }\n  };\n  return CryptoJS.pad.Iso10126;\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "pad", "Iso10126", "data", "blockSize", "blockSizeBytes", "nPaddingBytes", "sigBytes", "concat", "lib", "WordArray", "random", "create", "unpad", "words"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/crypto-js/pad-iso10126.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./cipher-core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t/**\n\t * ISO 10126 padding strategy.\n\t */\n\tCryptoJS.pad.Iso10126 = {\n\t    pad: function (data, blockSize) {\n\t        // Shortcut\n\t        var blockSizeBytes = blockSize * 4;\n\n\t        // Count padding bytes\n\t        var nPaddingBytes = blockSizeBytes - data.sigBytes % blockSizeBytes;\n\n\t        // Pad\n\t        data.concat(CryptoJS.lib.WordArray.random(nPaddingBytes - 1)).\n\t             concat(CryptoJS.lib.WordArray.create([nPaddingBytes << 24], 1));\n\t    },\n\n\t    unpad: function (data) {\n\t        // Get number of padding bytes from last byte\n\t        var nPaddingBytes = data.words[(data.sigBytes - 1) >>> 2] & 0xff;\n\n\t        // Remove padding\n\t        data.sigBytes -= nPaddingBytes;\n\t    }\n\t};\n\n\n\treturn CryptoJS.pad.Iso10126;\n\n}));"], "mappings": "AAAA;;AAAE,WAAUA,IAAV,EAAgBC,OAAhB,EAAyBC,KAAzB,EAAgC;EACjC,IAAI,OAAOC,OAAP,KAAmB,QAAvB,EAAiC;IAChC;IACAC,MAAM,CAACD,OAAP,GAAiBA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAD,CAAR,EAAoBA,OAAO,CAAC,eAAD,CAA3B,CAAlC;EACA,CAHD,MAIK,IAAI,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAA3C,EAAgD;IACpD;IACAD,MAAM,CAAC,CAAC,QAAD,EAAW,eAAX,CAAD,EAA8BL,OAA9B,CAAN;EACA,CAHI,MAIA;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAN,CAAP;EACA;AACD,CAbC,EAaA,IAbA,EAaM,UAAUA,QAAV,EAAoB;EAE3B;AACD;AACA;EACCA,QAAQ,CAACC,GAAT,CAAaC,QAAb,GAAwB;IACpBD,GAAG,EAAE,UAAUE,IAAV,EAAgBC,SAAhB,EAA2B;MAC5B;MACA,IAAIC,cAAc,GAAGD,SAAS,GAAG,CAAjC,CAF4B,CAI5B;;MACA,IAAIE,aAAa,GAAGD,cAAc,GAAGF,IAAI,CAACI,QAAL,GAAgBF,cAArD,CAL4B,CAO5B;;MACAF,IAAI,CAACK,MAAL,CAAYR,QAAQ,CAACS,GAAT,CAAaC,SAAb,CAAuBC,MAAvB,CAA8BL,aAAa,GAAG,CAA9C,CAAZ,EACKE,MADL,CACYR,QAAQ,CAACS,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B,CAACN,aAAa,IAAI,EAAlB,CAA9B,EAAqD,CAArD,CADZ;IAEH,CAXmB;IAapBO,KAAK,EAAE,UAAUV,IAAV,EAAgB;MACnB;MACA,IAAIG,aAAa,GAAGH,IAAI,CAACW,KAAL,CAAYX,IAAI,CAACI,QAAL,GAAgB,CAAjB,KAAwB,CAAnC,IAAwC,IAA5D,CAFmB,CAInB;;MACAJ,IAAI,CAACI,QAAL,IAAiBD,aAAjB;IACH;EAnBmB,CAAxB;EAuBA,OAAON,QAAQ,CAACC,GAAT,CAAaC,QAApB;AAEA,CA3CC,CAAD"}, "metadata": {}, "sourceType": "script"}