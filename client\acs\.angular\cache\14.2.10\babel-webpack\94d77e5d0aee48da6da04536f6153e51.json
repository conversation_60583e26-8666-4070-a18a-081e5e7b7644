{"ast": null, "code": "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "map": {"version": 3, "names": ["modifierPhases", "order", "modifiers", "map", "Map", "visited", "Set", "result", "for<PERSON>ach", "modifier", "set", "name", "sort", "add", "requires", "concat", "requiresIfExists", "dep", "has", "depModifier", "get", "push", "orderModifiers", "orderedModifiers", "reduce", "acc", "phase", "filter"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/utils/orderModifiers.js"], "sourcesContent": ["import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}"], "mappings": "AAAA,SAASA,cAAT,QAA+B,aAA/B,C,CAA8C;;AAE9C,SAASC,KAAT,CAAeC,SAAf,EAA0B;EACxB,IAAIC,GAAG,GAAG,IAAIC,GAAJ,EAAV;EACA,IAAIC,OAAO,GAAG,IAAIC,GAAJ,EAAd;EACA,IAAIC,MAAM,GAAG,EAAb;EACAL,SAAS,CAACM,OAAV,CAAkB,UAAUC,QAAV,EAAoB;IACpCN,GAAG,CAACO,GAAJ,CAAQD,QAAQ,CAACE,IAAjB,EAAuBF,QAAvB;EACD,CAFD,EAJwB,CAMpB;;EAEJ,SAASG,IAAT,CAAcH,QAAd,EAAwB;IACtBJ,OAAO,CAACQ,GAAR,CAAYJ,QAAQ,CAACE,IAArB;IACA,IAAIG,QAAQ,GAAG,GAAGC,MAAH,CAAUN,QAAQ,CAACK,QAAT,IAAqB,EAA/B,EAAmCL,QAAQ,CAACO,gBAAT,IAA6B,EAAhE,CAAf;IACAF,QAAQ,CAACN,OAAT,CAAiB,UAAUS,GAAV,EAAe;MAC9B,IAAI,CAACZ,OAAO,CAACa,GAAR,CAAYD,GAAZ,CAAL,EAAuB;QACrB,IAAIE,WAAW,GAAGhB,GAAG,CAACiB,GAAJ,CAAQH,GAAR,CAAlB;;QAEA,IAAIE,WAAJ,EAAiB;UACfP,IAAI,CAACO,WAAD,CAAJ;QACD;MACF;IACF,CARD;IASAZ,MAAM,CAACc,IAAP,CAAYZ,QAAZ;EACD;;EAEDP,SAAS,CAACM,OAAV,CAAkB,UAAUC,QAAV,EAAoB;IACpC,IAAI,CAACJ,OAAO,CAACa,GAAR,CAAYT,QAAQ,CAACE,IAArB,CAAL,EAAiC;MAC/B;MACAC,IAAI,CAACH,QAAD,CAAJ;IACD;EACF,CALD;EAMA,OAAOF,MAAP;AACD;;AAED,eAAe,SAASe,cAAT,CAAwBpB,SAAxB,EAAmC;EAChD;EACA,IAAIqB,gBAAgB,GAAGtB,KAAK,CAACC,SAAD,CAA5B,CAFgD,CAEP;;EAEzC,OAAOF,cAAc,CAACwB,MAAf,CAAsB,UAAUC,GAAV,EAAeC,KAAf,EAAsB;IACjD,OAAOD,GAAG,CAACV,MAAJ,CAAWQ,gBAAgB,CAACI,MAAjB,CAAwB,UAAUlB,QAAV,EAAoB;MAC5D,OAAOA,QAAQ,CAACiB,KAAT,KAAmBA,KAA1B;IACD,CAFiB,CAAX,CAAP;EAGD,CAJM,EAIJ,EAJI,CAAP;AAKD"}, "metadata": {}, "sourceType": "module"}