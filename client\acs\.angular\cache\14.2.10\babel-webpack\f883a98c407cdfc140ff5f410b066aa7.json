{"ast": null, "code": "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "map": {"version": 3, "names": ["getWindow", "passive", "effect", "_ref", "state", "instance", "options", "_options$scroll", "scroll", "_options$resize", "resize", "window", "elements", "popper", "scrollParents", "concat", "reference", "for<PERSON>ach", "scrollParent", "addEventListener", "update", "removeEventListener", "name", "enabled", "phase", "fn", "data"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/modifiers/eventListeners.js"], "sourcesContent": ["import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,2BAAtB,C,CAAmD;;AAEnD,IAAIC,OAAO,GAAG;EACZA,OAAO,EAAE;AADG,CAAd;;AAIA,SAASC,MAAT,CAAgBC,IAAhB,EAAsB;EACpB,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAjB;EAAA,IACIC,QAAQ,GAAGF,IAAI,CAACE,QADpB;EAAA,IAEIC,OAAO,GAAGH,IAAI,CAACG,OAFnB;EAGA,IAAIC,eAAe,GAAGD,OAAO,CAACE,MAA9B;EAAA,IACIA,MAAM,GAAGD,eAAe,KAAK,KAAK,CAAzB,GAA6B,IAA7B,GAAoCA,eADjD;EAAA,IAEIE,eAAe,GAAGH,OAAO,CAACI,MAF9B;EAAA,IAGIA,MAAM,GAAGD,eAAe,KAAK,KAAK,CAAzB,GAA6B,IAA7B,GAAoCA,eAHjD;EAIA,IAAIE,MAAM,GAAGX,SAAS,CAACI,KAAK,CAACQ,QAAN,CAAeC,MAAhB,CAAtB;EACA,IAAIC,aAAa,GAAG,GAAGC,MAAH,CAAUX,KAAK,CAACU,aAAN,CAAoBE,SAA9B,EAAyCZ,KAAK,CAACU,aAAN,CAAoBD,MAA7D,CAApB;;EAEA,IAAIL,MAAJ,EAAY;IACVM,aAAa,CAACG,OAAd,CAAsB,UAAUC,YAAV,EAAwB;MAC5CA,YAAY,CAACC,gBAAb,CAA8B,QAA9B,EAAwCd,QAAQ,CAACe,MAAjD,EAAyDnB,OAAzD;IACD,CAFD;EAGD;;EAED,IAAIS,MAAJ,EAAY;IACVC,MAAM,CAACQ,gBAAP,CAAwB,QAAxB,EAAkCd,QAAQ,CAACe,MAA3C,EAAmDnB,OAAnD;EACD;;EAED,OAAO,YAAY;IACjB,IAAIO,MAAJ,EAAY;MACVM,aAAa,CAACG,OAAd,CAAsB,UAAUC,YAAV,EAAwB;QAC5CA,YAAY,CAACG,mBAAb,CAAiC,QAAjC,EAA2ChB,QAAQ,CAACe,MAApD,EAA4DnB,OAA5D;MACD,CAFD;IAGD;;IAED,IAAIS,MAAJ,EAAY;MACVC,MAAM,CAACU,mBAAP,CAA2B,QAA3B,EAAqChB,QAAQ,CAACe,MAA9C,EAAsDnB,OAAtD;IACD;EACF,CAVD;AAWD,C,CAAC;;;AAGF,eAAe;EACbqB,IAAI,EAAE,gBADO;EAEbC,OAAO,EAAE,IAFI;EAGbC,KAAK,EAAE,OAHM;EAIbC,EAAE,EAAE,SAASA,EAAT,GAAc,CAAE,CAJP;EAKbvB,MAAM,EAAEA,MALK;EAMbwB,IAAI,EAAE;AANO,CAAf"}, "metadata": {}, "sourceType": "module"}