{"ast": null, "code": "import { FindValueOperator } from '../operators/find';\nexport function findIndex(predicate, thisArg) {\n  return source => source.lift(new FindValueOperator(predicate, source, true, thisArg));\n}", "map": {"version": 3, "names": ["FindValueOperator", "findIndex", "predicate", "thisArg", "source", "lift"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/findIndex.js"], "sourcesContent": ["import { FindValueOperator } from '../operators/find';\nexport function findIndex(predicate, thisArg) {\n    return (source) => source.lift(new FindValueOperator(predicate, source, true, thisArg));\n}\n"], "mappings": "AAAA,SAASA,iBAAT,QAAkC,mBAAlC;AACA,OAAO,SAASC,SAAT,CAAmBC,SAAnB,EAA8BC,OAA9B,EAAuC;EAC1C,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIL,iBAAJ,CAAsBE,SAAtB,EAAiCE,MAAjC,EAAyC,IAAzC,EAA+CD,OAA/C,CAAZ,CAAnB;AACH"}, "metadata": {}, "sourceType": "module"}