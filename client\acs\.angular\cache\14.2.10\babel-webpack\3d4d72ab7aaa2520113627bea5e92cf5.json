{"ast": null, "code": "export default function getUAString() {\n  var uaData = navigator.userAgentData;\n\n  if (uaData != null && uaData.brands) {\n    return uaData.brands.map(function (item) {\n      return item.brand + \"/\" + item.version;\n    }).join(' ');\n  }\n\n  return navigator.userAgent;\n}", "map": {"version": 3, "names": ["getUAString", "uaData", "navigator", "userAgentData", "brands", "map", "item", "brand", "version", "join", "userAgent"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/utils/userAgent.js"], "sourcesContent": ["export default function getUAString() {\n  var uaData = navigator.userAgentData;\n\n  if (uaData != null && uaData.brands) {\n    return uaData.brands.map(function (item) {\n      return item.brand + \"/\" + item.version;\n    }).join(' ');\n  }\n\n  return navigator.userAgent;\n}"], "mappings": "AAAA,eAAe,SAASA,WAAT,GAAuB;EACpC,IAAIC,MAAM,GAAGC,SAAS,CAACC,aAAvB;;EAEA,IAAIF,MAAM,IAAI,IAAV,IAAkBA,MAAM,CAACG,MAA7B,EAAqC;IACnC,OAAOH,MAAM,CAACG,MAAP,CAAcC,GAAd,CAAkB,UAAUC,IAAV,EAAgB;MACvC,OAAOA,IAAI,CAACC,KAAL,GAAa,GAAb,GAAmBD,IAAI,CAACE,OAA/B;IACD,CAFM,EAEJC,IAFI,CAEC,GAFD,CAAP;EAGD;;EAED,OAAOP,SAAS,CAACQ,SAAjB;AACD"}, "metadata": {}, "sourceType": "module"}