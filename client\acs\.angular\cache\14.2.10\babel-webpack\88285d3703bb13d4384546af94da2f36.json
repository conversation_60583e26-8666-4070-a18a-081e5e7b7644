{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./edit-imprinted-details.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./edit-imprinted-details.component.css?ngResource\";\nimport { Component, Inject, ViewChild, EventEmitter, Output } from '@angular/core';\nimport { FormControl, FormGroup } from '@angular/forms';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { SimpleProductService } from 'src/app/Modules/Core/Services/Common/simple-product.service';\nimport { QuestionService } from 'src/app/Modules/Shared/Services/Common/question.service';\nimport { GenericModalService } from 'src/app/Modules/Shared/Services/Common/generic-modal.service';\nlet EditImprintedDetailsComponent = class EditImprintedDetailsComponent {\n  constructor(dialogRef, data, simpleProductService, questionService, genericModalService) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.simpleProductService = simpleProductService;\n    this.questionService = questionService;\n    this.genericModalService = genericModalService;\n    this.dynamicFormControlsData = {};\n    this.Form = new FormGroup({\n      FictitiousNameToFile: new FormControl(null),\n      Instructions: new FormControl(null),\n      SelectedFilingOptionType: new FormControl(null)\n    });\n    this.isLoading = false;\n    this.dynamicFormUploadedFiles = [];\n    this.fileUploadEvent = new EventEmitter();\n    this.formValueChanged = false;\n  }\n\n  set ImprintedFormComponent(ImprintedFormComponent) {\n    if (ImprintedFormComponent) {\n      this.ImprintedFormComponentObj = ImprintedFormComponent;\n    }\n  }\n\n  ngOnInit() {\n    this.isLoading = true;\n    this.simpleProductService.getImprintDetails(this.data.item).subscribe(data => {\n      data.forEach(item => {\n        if (item.value == \"null\") {\n          item.defaultValue = \"\";\n        } else {\n          item.defaultValue = item.value;\n        }\n      });\n      this.questions$ = this.questionService.getMappedQuestions(data, [], \"\");\n      this.isLoading = false;\n    });\n  }\n\n  OnSave(dynamicFormData, formCancelled = false) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      if (!dynamicFormData && _this.ImprintedFormComponentObj) {\n        var enteredDynamicFormData = yield _this.ImprintedFormComponentObj.getDynamicFormData();\n      }\n\n      try {\n        if (_this.Form.valid) {\n          _this.dynamicFormControlsData = dynamicFormData || enteredDynamicFormData;\n          _this.formValueChanged = formCancelled;\n\n          if (!_this.formValueChanged) {\n            _this.updateImprintDetails();\n\n            return false;\n          } // this.dialogRef.close();\n\n        }\n      } catch (er) {\n        console.error(er);\n      }\n    })();\n  }\n\n  updateImprintDetails() {\n    this.simpleProductService.updateImprintDetails(this.data.item, this.dynamicFormControlsData.keyValuePair, this.dynamicFormUploadedFiles).subscribe(response => {\n      if (response.responseCode == 200) {\n        this.dialogRef.close();\n      }\n    });\n  }\n\n  clearForm() {\n    this.ImprintedFormComponentObj?.dynamicform?.reset();\n  }\n\n  onCancel() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      const result = yield _this2.genericModalService.openDialog({\n        title: 'Changes are not saved',\n        text: 'All changes will be lost, would you like to continue?',\n        yesButtonText: 'Yes, Discard Changes',\n        noButtonText: 'No'\n      });\n\n      if (result) {\n        _this2.dialogRef.close();\n      }\n\n      return false;\n    })();\n  }\n\n  fileUplaod(files) {\n    this.dynamicFormUploadedFiles = files;\n  }\n\n};\n\nEditImprintedDetailsComponent.ctorParameters = () => [{\n  type: MatDialogRef\n}, {\n  type: undefined,\n  decorators: [{\n    type: Inject,\n    args: [MAT_DIALOG_DATA]\n  }]\n}, {\n  type: SimpleProductService\n}, {\n  type: QuestionService\n}, {\n  type: GenericModalService\n}];\n\nEditImprintedDetailsComponent.propDecorators = {\n  ImprintedFormComponent: [{\n    type: ViewChild,\n    args: [\"ImprintedFormComponent\", {\n      static: false\n    }]\n  }],\n  fileUploadEvent: [{\n    type: Output\n  }]\n};\nEditImprintedDetailsComponent = __decorate([Component({\n  selector: 'app-edit-imprinted-details',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], EditImprintedDetailsComponent);\nexport { EditImprintedDetailsComponent };", "map": {"version": 3, "mappings": ";;;;AAAA,SAASA,SAAT,EAAoBC,MAApB,EAAoCC,SAApC,EAA+CC,YAA/C,EAA6DC,MAA7D,QAA2E,eAA3E;AAIA,SAASC,WAAT,EAAsBC,SAAtB,QAAuC,gBAAvC;AACA,SAASC,eAAT,EAA0BC,YAA1B,QAA8C,0BAA9C;AACA,SAASC,oBAAT,QAAqC,6DAArC;AACA,SAASC,eAAT,QAAgC,yDAAhC;AACA,SAASC,mBAAT,QAAoC,8DAApC;IAOaC,6BAA6B,SAA7BA,6BAA6B;EAoBxCC,YACSC,SADT,EAEkCC,IAFlC,EAGUC,oBAHV,EAIUC,eAJV,EAKUC,mBALV,EAKkD;IAJzC;IACyB;IACxB;IACA;IACA;IAvBV,+BAA+B,EAA/B;IASA,YAAO,IAAIZ,SAAJ,CAAc;MACnBa,oBAAoB,EAAE,IAAId,WAAJ,CAA+B,IAA/B,CADH;MAEnBe,YAAY,EAAE,IAAIf,WAAJ,CAA+B,IAA/B,CAFK;MAGnBgB,wBAAwB,EAAE,IAAIhB,WAAJ,CAA+B,IAA/B;IAHP,CAAd,CAAP;IAKA,iBAAqB,KAArB;IACA,gCAAmC,EAAnC;IACU,uBAAkB,IAAIF,YAAJ,EAAlB;IA0BV,wBAA4B,KAA5B;EAlBK;;EApBqB,IAAtBmB,sBAAsB,CAACA,sBAAD,EAA+C;IACvE,IAAIA,sBAAJ,EAA4B;MAC1B,KAAKC,yBAAL,GAAiCD,sBAAjC;IACD;EACF;;EAkBDE,QAAQ;IACN,KAAKC,SAAL,GAAiB,IAAjB;IACA,KAAKT,oBAAL,CAA0BU,iBAA1B,CAA4C,KAAKX,IAAL,CAAUY,IAAtD,EAA4DC,SAA5D,CAAuEb,IAAD,IAAS;MAC7EA,IAAI,CAACc,OAAL,CAAcF,IAAD,IAAS;QACpB,IAAIA,IAAI,CAACG,KAAL,IAAc,MAAlB,EAA0B;UACxBH,IAAI,CAACI,YAAL,GAAoB,EAApB;QACD,CAFD,MAEO;UACLJ,IAAI,CAACI,YAAL,GAAoBJ,IAAI,CAACG,KAAzB;QACD;MACF,CAND;MAOA,KAAKE,UAAL,GAAkB,KAAKf,eAAL,CAAqBgB,kBAArB,CAAwClB,IAAxC,EAA8C,EAA9C,EAAkD,EAAlD,CAAlB;MAEA,KAAKU,SAAL,GAAiB,KAAjB;IACD,CAXD;EAYD;;EAGKS,MAAM,CAACC,eAAD,EAAmBC,aAAa,GAAG,KAAnC,EAAwC;IAAA;;IAAA;MAClD,IAAI,CAACD,eAAD,IAAoB,KAAI,CAACZ,yBAA7B,EAAwD;QACtD,IAAIc,sBAAsB,SAAS,KAAI,CAACd,yBAAL,CAA+Be,kBAA/B,EAAnC;MACD;;MACD,IAAI;QACF,IAAI,KAAI,CAACC,IAAL,CAAUC,KAAd,EAAqB;UACnB,KAAI,CAACC,uBAAL,GAA+BN,eAAe,IAAIE,sBAAlD;UACA,KAAI,CAACK,gBAAL,GAAwBN,aAAxB;;UACA,IAAI,CAAC,KAAI,CAACM,gBAAV,EAA4B;YAC1B,KAAI,CAACC,oBAAL;;YACA,OAAO,KAAP;UACD,CANkB,CAOnB;;QACD;MACF,CAVD,CAWA,OAAOC,EAAP,EAAW;QACTC,OAAO,CAACC,KAAR,CAAcF,EAAd;MACD;IAjBiD;EAkBnD;;EAEDD,oBAAoB;IAClB,KAAK3B,oBAAL,CAA0B2B,oBAA1B,CAA+C,KAAK5B,IAAL,CAAUY,IAAzD,EAA+D,KAAKc,uBAAL,CAA6BM,YAA5F,EAA0G,KAAKC,wBAA/G,EAAyIpB,SAAzI,CAAoJqB,QAAD,IAAa;MAC9J,IAAIA,QAAQ,CAACC,YAAT,IAAyB,GAA7B,EAAkC;QAChC,KAAKpC,SAAL,CAAeqC,KAAf;MACD;IACF,CAJD;EAKD;;EAEDC,SAAS;IACP,KAAK7B,yBAAL,EAAgC8B,WAAhC,EAA6CC,KAA7C;EACD;;EAEKC,QAAQ;IAAA;;IAAA;MACZ,MAAMC,MAAM,SAAS,MAAI,CAACtC,mBAAL,CAAyBuC,UAAzB,CAAoC;QACvDC,KAAK,EAAE,uBADgD;QAEvDC,IAAI,EAAE,uDAFiD;QAGvDC,aAAa,EAAE,sBAHwC;QAIvDC,YAAY,EAAE;MAJyC,CAApC,CAArB;;MAOA,IAAIL,MAAJ,EAAY;QACV,MAAI,CAAC1C,SAAL,CAAeqC,KAAf;MACD;;MACD,OAAO,KAAP;IAXY;EAYb;;EAEDW,UAAU,CAACC,KAAD,EAAc;IACtB,KAAKf,wBAAL,GAAgCe,KAAhC;EACD;;AA7FuC;;;;;;;UAsBrC9D;IAAM+D,OAACzD,eAAD;;;;;;;;;;;;UAjBRL;IAAS8D,OAAC,wBAAD,EAA2B;MAAEC,MAAM,EAAE;IAAV,CAA3B;;;UAaT7D;;;AAlBUQ,6BAA6B,eALzCZ,SAAS,CAAC;EACTkE,QAAQ,EAAE,4BADD;EAETC,8BAFS;;AAAA,CAAD,CAKgC,GAA7BvD,6BAA6B,CAA7B;SAAAA", "names": ["Component", "Inject", "ViewChild", "EventEmitter", "Output", "FormControl", "FormGroup", "MAT_DIALOG_DATA", "MatDialogRef", "SimpleProductService", "QuestionService", "GenericModalService", "EditImprintedDetailsComponent", "constructor", "dialogRef", "data", "simpleProductService", "questionService", "genericModalService", "FictitiousNameToFile", "Instructions", "SelectedFilingOptionType", "ImprintedFormComponent", "ImprintedFormComponentObj", "ngOnInit", "isLoading", "getImprintDetails", "item", "subscribe", "for<PERSON>ach", "value", "defaultValue", "questions$", "getMappedQuestions", "OnSave", "dynamicFormData", "formCancelled", "enteredDynamicFormData", "getDynamicFormData", "Form", "valid", "dynamicFormControlsData", "formValueChanged", "updateImprintDetails", "er", "console", "error", "keyValuePair", "dynamicFormUploadedFiles", "response", "responseCode", "close", "clearForm", "dynamicform", "reset", "onCancel", "result", "openDialog", "title", "text", "yesButtonText", "noButtonText", "fileUplaod", "files", "args", "static", "selector", "template"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\viatech\\Ecom\\client\\acs\\src\\app\\Modules\\Tabs&Dividers\\components\\edit-imprinted-details\\edit-imprinted-details.component.ts"], "sourcesContent": ["import { Component, Inject, OnInit, ViewChild, EventEmitter, Output } from '@angular/core';\r\nimport { ImprintedFormComponent } from '../imprinted-form/imprinted-form.component';\r\nimport { QuestionBase } from 'src/app/Modules/Shared/Models/DynamicForm/question-base';\r\nimport { Observable } from 'rxjs';\r\nimport { FormControl, FormGroup } from '@angular/forms';\r\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\r\nimport { SimpleProductService } from 'src/app/Modules/Core/Services/Common/simple-product.service';\r\nimport { QuestionService } from 'src/app/Modules/Shared/Services/Common/question.service';\r\nimport { GenericModalService } from 'src/app/Modules/Shared/Services/Common/generic-modal.service';\r\n\r\n@Component({\r\n  selector: 'app-edit-imprinted-details',\r\n  templateUrl: './edit-imprinted-details.component.html',\r\n  styleUrls: ['./edit-imprinted-details.component.css']\r\n})\r\nexport class EditImprintedDetailsComponent implements OnInit {\r\n\r\n  dynamicFormControlsData: any = {};\r\n  questions$: Observable<QuestionBase<any>[]>;\r\n  ImprintedFormComponentObj: ImprintedFormComponent;\r\n  @ViewChild(\"ImprintedFormComponent\", { static: false })\r\n  set ImprintedFormComponent(ImprintedFormComponent: ImprintedFormComponent) {\r\n    if (ImprintedFormComponent) {\r\n      this.ImprintedFormComponentObj = ImprintedFormComponent;\r\n    }\r\n  }\r\n  Form = new FormGroup({\r\n    FictitiousNameToFile: new FormControl<string | null>(null),\r\n    Instructions: new FormControl<string | null>(null),\r\n    SelectedFilingOptionType: new FormControl<number | null>(null)\r\n  })\r\n  isLoading: boolean = false;\r\n  dynamicFormUploadedFiles: File[] = [];\r\n  @Output() fileUploadEvent = new EventEmitter<File[]>();\r\n\r\n  constructor(\r\n    public dialogRef: MatDialogRef<EditImprintedDetailsComponent>,\r\n    @Inject(MAT_DIALOG_DATA) public data: any,\r\n    private simpleProductService: SimpleProductService,\r\n    private questionService: QuestionService,\r\n    private genericModalService: GenericModalService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.isLoading = true;\r\n    this.simpleProductService.getImprintDetails(this.data.item).subscribe((data) => {\r\n      data.forEach((item) => {\r\n        if (item.value == \"null\") {\r\n          item.defaultValue = \"\";\r\n        } else {\r\n          item.defaultValue = item.value;\r\n        }\r\n      })\r\n      this.questions$ = this.questionService.getMappedQuestions(data, [], \"\");\r\n\r\n      this.isLoading = false;\r\n    })\r\n  }\r\n\r\n  formValueChanged: boolean = false;\r\n  async OnSave(dynamicFormData?, formCancelled = false) {\r\n    if (!dynamicFormData && this.ImprintedFormComponentObj) {\r\n      var enteredDynamicFormData = await this.ImprintedFormComponentObj.getDynamicFormData()\r\n    }\r\n    try {\r\n      if (this.Form.valid) {\r\n        this.dynamicFormControlsData = dynamicFormData || enteredDynamicFormData;\r\n        this.formValueChanged = formCancelled;\r\n        if (!this.formValueChanged) {\r\n          this.updateImprintDetails();\r\n          return false;\r\n        }\r\n        // this.dialogRef.close();\r\n      }\r\n    }\r\n    catch (er) {\r\n      console.error(er)\r\n    }\r\n  }\r\n\r\n  updateImprintDetails() {\r\n    this.simpleProductService.updateImprintDetails(this.data.item, this.dynamicFormControlsData.keyValuePair, this.dynamicFormUploadedFiles).subscribe((response) => {\r\n      if (response.responseCode == 200) {\r\n        this.dialogRef.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  clearForm() {\r\n    this.ImprintedFormComponentObj?.dynamicform?.reset();\r\n  }\r\n\r\n  async onCancel() {\r\n    const result = await this.genericModalService.openDialog({\r\n      title: 'Changes are not saved',\r\n      text: 'All changes will be lost, would you like to continue?',\r\n      yesButtonText: 'Yes, Discard Changes',\r\n      noButtonText: 'No'\r\n    });\r\n\r\n    if (result) {\r\n      this.dialogRef.close();\r\n    }\r\n    return false;\r\n  }\r\n\r\n  fileUplaod(files: File[]) {\r\n    this.dynamicFormUploadedFiles = files;\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}