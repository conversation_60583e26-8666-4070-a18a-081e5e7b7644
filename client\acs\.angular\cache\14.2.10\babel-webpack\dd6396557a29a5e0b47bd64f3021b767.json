{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { noop } from '../util/noop';\nimport { isFunction } from '../util/isFunction';\nexport function tap(nextOrObserver, error, complete) {\n  return function tapOperatorFunction(source) {\n    return source.lift(new DoOperator(nextOrObserver, error, complete));\n  };\n}\n\nclass DoOperator {\n  constructor(nextOrObserver, error, complete) {\n    this.nextOrObserver = nextOrObserver;\n    this.error = error;\n    this.complete = complete;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new TapSubscriber(subscriber, this.nextOrObserver, this.error, this.complete));\n  }\n\n}\n\nclass TapSubscriber extends Subscriber {\n  constructor(destination, observerOrNext, error, complete) {\n    super(destination);\n    this._tapNext = noop;\n    this._tapError = noop;\n    this._tapComplete = noop;\n    this._tapError = error || noop;\n    this._tapComplete = complete || noop;\n\n    if (isFunction(observerOrNext)) {\n      this._context = this;\n      this._tapNext = observerOrNext;\n    } else if (observerOrNext) {\n      this._context = observerOrNext;\n      this._tapNext = observerOrNext.next || noop;\n      this._tapError = observerOrNext.error || noop;\n      this._tapComplete = observerOrNext.complete || noop;\n    }\n  }\n\n  _next(value) {\n    try {\n      this._tapNext.call(this._context, value);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n\n    this.destination.next(value);\n  }\n\n  _error(err) {\n    try {\n      this._tapError.call(this._context, err);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n\n    this.destination.error(err);\n  }\n\n  _complete() {\n    try {\n      this._tapComplete.call(this._context);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n\n    return this.destination.complete();\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "noop", "isFunction", "tap", "nextOrObserver", "error", "complete", "tapOperatorFunction", "source", "lift", "DoOperator", "constructor", "call", "subscriber", "subscribe", "TapSubscriber", "destination", "observerOrNext", "_tapNext", "_tapError", "_tapComplete", "_context", "next", "_next", "value", "err", "_error", "_complete"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/tap.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { noop } from '../util/noop';\nimport { isFunction } from '../util/isFunction';\nexport function tap(nextOrObserver, error, complete) {\n    return function tapOperatorFunction(source) {\n        return source.lift(new DoOperator(nextOrObserver, error, complete));\n    };\n}\nclass DoOperator {\n    constructor(nextOrObserver, error, complete) {\n        this.nextOrObserver = nextOrObserver;\n        this.error = error;\n        this.complete = complete;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new TapSubscriber(subscriber, this.nextOrObserver, this.error, this.complete));\n    }\n}\nclass TapSubscriber extends Subscriber {\n    constructor(destination, observerOrNext, error, complete) {\n        super(destination);\n        this._tapNext = noop;\n        this._tapError = noop;\n        this._tapComplete = noop;\n        this._tapError = error || noop;\n        this._tapComplete = complete || noop;\n        if (isFunction(observerOrNext)) {\n            this._context = this;\n            this._tapNext = observerOrNext;\n        }\n        else if (observerOrNext) {\n            this._context = observerOrNext;\n            this._tapNext = observerOrNext.next || noop;\n            this._tapError = observerOrNext.error || noop;\n            this._tapComplete = observerOrNext.complete || noop;\n        }\n    }\n    _next(value) {\n        try {\n            this._tapNext.call(this._context, value);\n        }\n        catch (err) {\n            this.destination.error(err);\n            return;\n        }\n        this.destination.next(value);\n    }\n    _error(err) {\n        try {\n            this._tapError.call(this._context, err);\n        }\n        catch (err) {\n            this.destination.error(err);\n            return;\n        }\n        this.destination.error(err);\n    }\n    _complete() {\n        try {\n            this._tapComplete.call(this._context);\n        }\n        catch (err) {\n            this.destination.error(err);\n            return;\n        }\n        return this.destination.complete();\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,IAAT,QAAqB,cAArB;AACA,SAASC,UAAT,QAA2B,oBAA3B;AACA,OAAO,SAASC,GAAT,CAAaC,cAAb,EAA6BC,KAA7B,EAAoCC,QAApC,EAA8C;EACjD,OAAO,SAASC,mBAAT,CAA6BC,MAA7B,EAAqC;IACxC,OAAOA,MAAM,CAACC,IAAP,CAAY,IAAIC,UAAJ,CAAeN,cAAf,EAA+BC,KAA/B,EAAsCC,QAAtC,CAAZ,CAAP;EACH,CAFD;AAGH;;AACD,MAAMI,UAAN,CAAiB;EACbC,WAAW,CAACP,cAAD,EAAiBC,KAAjB,EAAwBC,QAAxB,EAAkC;IACzC,KAAKF,cAAL,GAAsBA,cAAtB;IACA,KAAKC,KAAL,GAAaA,KAAb;IACA,KAAKC,QAAL,GAAgBA,QAAhB;EACH;;EACDM,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,aAAJ,CAAkBF,UAAlB,EAA8B,KAAKT,cAAnC,EAAmD,KAAKC,KAAxD,EAA+D,KAAKC,QAApE,CAAjB,CAAP;EACH;;AARY;;AAUjB,MAAMS,aAAN,SAA4Bf,UAA5B,CAAuC;EACnCW,WAAW,CAACK,WAAD,EAAcC,cAAd,EAA8BZ,KAA9B,EAAqCC,QAArC,EAA+C;IACtD,MAAMU,WAAN;IACA,KAAKE,QAAL,GAAgBjB,IAAhB;IACA,KAAKkB,SAAL,GAAiBlB,IAAjB;IACA,KAAKmB,YAAL,GAAoBnB,IAApB;IACA,KAAKkB,SAAL,GAAiBd,KAAK,IAAIJ,IAA1B;IACA,KAAKmB,YAAL,GAAoBd,QAAQ,IAAIL,IAAhC;;IACA,IAAIC,UAAU,CAACe,cAAD,CAAd,EAAgC;MAC5B,KAAKI,QAAL,GAAgB,IAAhB;MACA,KAAKH,QAAL,GAAgBD,cAAhB;IACH,CAHD,MAIK,IAAIA,cAAJ,EAAoB;MACrB,KAAKI,QAAL,GAAgBJ,cAAhB;MACA,KAAKC,QAAL,GAAgBD,cAAc,CAACK,IAAf,IAAuBrB,IAAvC;MACA,KAAKkB,SAAL,GAAiBF,cAAc,CAACZ,KAAf,IAAwBJ,IAAzC;MACA,KAAKmB,YAAL,GAAoBH,cAAc,CAACX,QAAf,IAA2BL,IAA/C;IACH;EACJ;;EACDsB,KAAK,CAACC,KAAD,EAAQ;IACT,IAAI;MACA,KAAKN,QAAL,CAAcN,IAAd,CAAmB,KAAKS,QAAxB,EAAkCG,KAAlC;IACH,CAFD,CAGA,OAAOC,GAAP,EAAY;MACR,KAAKT,WAAL,CAAiBX,KAAjB,CAAuBoB,GAAvB;MACA;IACH;;IACD,KAAKT,WAAL,CAAiBM,IAAjB,CAAsBE,KAAtB;EACH;;EACDE,MAAM,CAACD,GAAD,EAAM;IACR,IAAI;MACA,KAAKN,SAAL,CAAeP,IAAf,CAAoB,KAAKS,QAAzB,EAAmCI,GAAnC;IACH,CAFD,CAGA,OAAOA,GAAP,EAAY;MACR,KAAKT,WAAL,CAAiBX,KAAjB,CAAuBoB,GAAvB;MACA;IACH;;IACD,KAAKT,WAAL,CAAiBX,KAAjB,CAAuBoB,GAAvB;EACH;;EACDE,SAAS,GAAG;IACR,IAAI;MACA,KAAKP,YAAL,CAAkBR,IAAlB,CAAuB,KAAKS,QAA5B;IACH,CAFD,CAGA,OAAOI,GAAP,EAAY;MACR,KAAKT,WAAL,CAAiBX,KAAjB,CAAuBoB,GAAvB;MACA;IACH;;IACD,OAAO,KAAKT,WAAL,CAAiBV,QAAjB,EAAP;EACH;;AAhDkC"}, "metadata": {}, "sourceType": "module"}