{"ast": null, "code": ";\n\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * Cipher Feedback block mode.\n   */\n  CryptoJS.mode.CFB = function () {\n    var CFB = CryptoJS.lib.BlockCipherMode.extend();\n    CFB.Encryptor = CFB.extend({\n      processBlock: function (words, offset) {\n        // Shortcuts\n        var cipher = this._cipher;\n        var blockSize = cipher.blockSize;\n        generateKeystreamAndEncrypt.call(this, words, offset, blockSize, cipher); // Remember this block to use with next block\n\n        this._prevBlock = words.slice(offset, offset + blockSize);\n      }\n    });\n    CFB.Decryptor = CFB.extend({\n      processBlock: function (words, offset) {\n        // Shortcuts\n        var cipher = this._cipher;\n        var blockSize = cipher.blockSize; // Remember this block to use with next block\n\n        var thisBlock = words.slice(offset, offset + blockSize);\n        generateKeystreamAndEncrypt.call(this, words, offset, blockSize, cipher); // This block becomes the previous block\n\n        this._prevBlock = thisBlock;\n      }\n    });\n\n    function generateKeystreamAndEncrypt(words, offset, blockSize, cipher) {\n      var keystream; // Shortcut\n\n      var iv = this._iv; // Generate keystream\n\n      if (iv) {\n        keystream = iv.slice(0); // Remove IV for subsequent blocks\n\n        this._iv = undefined;\n      } else {\n        keystream = this._prevBlock;\n      }\n\n      cipher.encryptBlock(keystream, 0); // Encrypt\n\n      for (var i = 0; i < blockSize; i++) {\n        words[offset + i] ^= keystream[i];\n      }\n    }\n\n    return CFB;\n  }();\n\n  return CryptoJS.mode.CFB;\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "mode", "CFB", "lib", "BlockCipherMode", "extend", "Encryptor", "processBlock", "words", "offset", "cipher", "_cipher", "blockSize", "generateKeystreamAndEncrypt", "call", "_prevBlock", "slice", "Decryptor", "thisBlock", "keystream", "iv", "_iv", "undefined", "encryptBlock", "i"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/crypto-js/mode-cfb.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./cipher-core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t/**\n\t * Cipher Feedback block mode.\n\t */\n\tCryptoJS.mode.CFB = (function () {\n\t    var CFB = CryptoJS.lib.BlockCipherMode.extend();\n\n\t    CFB.Encryptor = CFB.extend({\n\t        processBlock: function (words, offset) {\n\t            // Shortcuts\n\t            var cipher = this._cipher;\n\t            var blockSize = cipher.blockSize;\n\n\t            generateKeystreamAndEncrypt.call(this, words, offset, blockSize, cipher);\n\n\t            // Remember this block to use with next block\n\t            this._prevBlock = words.slice(offset, offset + blockSize);\n\t        }\n\t    });\n\n\t    CFB.Decryptor = CFB.extend({\n\t        processBlock: function (words, offset) {\n\t            // Shortcuts\n\t            var cipher = this._cipher;\n\t            var blockSize = cipher.blockSize;\n\n\t            // Remember this block to use with next block\n\t            var thisBlock = words.slice(offset, offset + blockSize);\n\n\t            generateKeystreamAndEncrypt.call(this, words, offset, blockSize, cipher);\n\n\t            // This block becomes the previous block\n\t            this._prevBlock = thisBlock;\n\t        }\n\t    });\n\n\t    function generateKeystreamAndEncrypt(words, offset, blockSize, cipher) {\n\t        var keystream;\n\n\t        // Shortcut\n\t        var iv = this._iv;\n\n\t        // Generate keystream\n\t        if (iv) {\n\t            keystream = iv.slice(0);\n\n\t            // Remove IV for subsequent blocks\n\t            this._iv = undefined;\n\t        } else {\n\t            keystream = this._prevBlock;\n\t        }\n\t        cipher.encryptBlock(keystream, 0);\n\n\t        // Encrypt\n\t        for (var i = 0; i < blockSize; i++) {\n\t            words[offset + i] ^= keystream[i];\n\t        }\n\t    }\n\n\t    return CFB;\n\t}());\n\n\n\treturn CryptoJS.mode.CFB;\n\n}));"], "mappings": "AAAA;;AAAE,WAAUA,IAAV,EAAgBC,OAAhB,EAAyBC,KAAzB,EAAgC;EACjC,IAAI,OAAOC,OAAP,KAAmB,QAAvB,EAAiC;IAChC;IACAC,MAAM,CAACD,OAAP,GAAiBA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAD,CAAR,EAAoBA,OAAO,CAAC,eAAD,CAA3B,CAAlC;EACA,CAHD,MAIK,IAAI,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAA3C,EAAgD;IACpD;IACAD,MAAM,CAAC,CAAC,QAAD,EAAW,eAAX,CAAD,EAA8BL,OAA9B,CAAN;EACA,CAHI,MAIA;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAN,CAAP;EACA;AACD,CAbC,EAaA,IAbA,EAaM,UAAUA,QAAV,EAAoB;EAE3B;AACD;AACA;EACCA,QAAQ,CAACC,IAAT,CAAcC,GAAd,GAAqB,YAAY;IAC7B,IAAIA,GAAG,GAAGF,QAAQ,CAACG,GAAT,CAAaC,eAAb,CAA6BC,MAA7B,EAAV;IAEAH,GAAG,CAACI,SAAJ,GAAgBJ,GAAG,CAACG,MAAJ,CAAW;MACvBE,YAAY,EAAE,UAAUC,KAAV,EAAiBC,MAAjB,EAAyB;QACnC;QACA,IAAIC,MAAM,GAAG,KAAKC,OAAlB;QACA,IAAIC,SAAS,GAAGF,MAAM,CAACE,SAAvB;QAEAC,2BAA2B,CAACC,IAA5B,CAAiC,IAAjC,EAAuCN,KAAvC,EAA8CC,MAA9C,EAAsDG,SAAtD,EAAiEF,MAAjE,EALmC,CAOnC;;QACA,KAAKK,UAAL,GAAkBP,KAAK,CAACQ,KAAN,CAAYP,MAAZ,EAAoBA,MAAM,GAAGG,SAA7B,CAAlB;MACH;IAVsB,CAAX,CAAhB;IAaAV,GAAG,CAACe,SAAJ,GAAgBf,GAAG,CAACG,MAAJ,CAAW;MACvBE,YAAY,EAAE,UAAUC,KAAV,EAAiBC,MAAjB,EAAyB;QACnC;QACA,IAAIC,MAAM,GAAG,KAAKC,OAAlB;QACA,IAAIC,SAAS,GAAGF,MAAM,CAACE,SAAvB,CAHmC,CAKnC;;QACA,IAAIM,SAAS,GAAGV,KAAK,CAACQ,KAAN,CAAYP,MAAZ,EAAoBA,MAAM,GAAGG,SAA7B,CAAhB;QAEAC,2BAA2B,CAACC,IAA5B,CAAiC,IAAjC,EAAuCN,KAAvC,EAA8CC,MAA9C,EAAsDG,SAAtD,EAAiEF,MAAjE,EARmC,CAUnC;;QACA,KAAKK,UAAL,GAAkBG,SAAlB;MACH;IAbsB,CAAX,CAAhB;;IAgBA,SAASL,2BAAT,CAAqCL,KAArC,EAA4CC,MAA5C,EAAoDG,SAApD,EAA+DF,MAA/D,EAAuE;MACnE,IAAIS,SAAJ,CADmE,CAGnE;;MACA,IAAIC,EAAE,GAAG,KAAKC,GAAd,CAJmE,CAMnE;;MACA,IAAID,EAAJ,EAAQ;QACJD,SAAS,GAAGC,EAAE,CAACJ,KAAH,CAAS,CAAT,CAAZ,CADI,CAGJ;;QACA,KAAKK,GAAL,GAAWC,SAAX;MACH,CALD,MAKO;QACHH,SAAS,GAAG,KAAKJ,UAAjB;MACH;;MACDL,MAAM,CAACa,YAAP,CAAoBJ,SAApB,EAA+B,CAA/B,EAfmE,CAiBnE;;MACA,KAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGZ,SAApB,EAA+BY,CAAC,EAAhC,EAAoC;QAChChB,KAAK,CAACC,MAAM,GAAGe,CAAV,CAAL,IAAqBL,SAAS,CAACK,CAAD,CAA9B;MACH;IACJ;;IAED,OAAOtB,GAAP;EACH,CAxDoB,EAArB;;EA2DA,OAAOF,QAAQ,CAACC,IAAT,CAAcC,GAArB;AAEA,CA/EC,CAAD"}, "metadata": {}, "sourceType": "script"}