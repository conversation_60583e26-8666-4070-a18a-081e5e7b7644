{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./beneficial-ownership.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./beneficial-ownership.component.css?ngResource\";\nimport { ChangeDetectorRef, Component, Input, ViewChild } from '@angular/core';\nimport { FormControl, FormGroup } from '@angular/forms';\nimport { ActivatedRoute } from '@angular/router';\nimport { ToastrService } from 'ngx-toastr';\nimport { DynamicFormComponent } from 'src/app/Modules/Shared/Components/dynamic-form/dynamic-form.component';\nimport { CustomSharedValidations } from 'src/app/Modules/Shared/functions/custom-validations';\nimport { StatePriceRequest } from 'src/app/Modules/Shared/Models/Common/state-price-request.model';\nimport { FormModeService } from 'src/app/Modules/Shared/Services/Common/FormModeService';\nimport { QuestionService } from 'src/app/Modules/Shared/Services/Common/question.service';\nimport { FilingServiceResponseObject } from '../../Enums/filing-service-enum';\nimport { LoadFilingService } from '../../Functions/filing-service-load.func';\nimport { BeneficialOwnership } from '../../Models/BeneficialOwnership';\nimport { FilingInfoService } from '../../Services/filing-price.service';\nimport { FilingApiService } from '../../Services/FilingApiService';\nimport { PageTitleService } from 'src/app/Modules/Shared/Services/Common/page-title.service';\nlet BeneficialOwnershipComponent = class BeneficialOwnershipComponent {\n  constructor(cdr, filingInfoService, Api, formMode, activatedRoute, service, toastr, pageTitleService) {\n    this.cdr = cdr;\n    this.filingInfoService = filingInfoService;\n    this.Api = Api;\n    this.formMode = formMode;\n    this.activatedRoute = activatedRoute;\n    this.service = service;\n    this.toastr = toastr;\n    this.pageTitleService = pageTitleService;\n    this.IsUpSelling = false;\n    this.isAdd = true;\n    this.FileData = [];\n    this.BeneficialOwnership = new BeneficialOwnership();\n    this.dynamicFormMasterData = [];\n    this.Form = new FormGroup({\n      FormationState: new FormControl(null),\n      FormationType: new FormControl(null),\n      //SelectedFilingOptionType: new FormControl(null),\n      SpecialInstructions: new FormControl(null, [CustomSharedValidations.specialInstructions])\n    });\n  }\n\n  set _FormRef(FormRef) {\n    this.FormRef = FormRef;\n  }\n\n  getUpSellingData() {\n    setTimeout(() => {\n      this.FormRef.nativeElement.click();\n    }, 10);\n    return new Promise(res => {\n      if (res) {\n        this.UpSellingResolver = res;\n      }\n    });\n  }\n\n  set DynamicFormComponent(dynamicFormComponent) {\n    if (dynamicFormComponent) {\n      this.DynamicFormComponentObj = dynamicFormComponent;\n    }\n  }\n\n  set UploadComponent(uploadComponent) {\n    if (uploadComponent) {\n      this.UploadComponentObj = uploadComponent;\n      this.UploadComponentObj.EmitUploadedData.subscribe(data => {\n        this.FileData = data;\n      }); //Mapping uploaded file data to FileUploadComponent in case of edit\n\n      if (this.BeneficialOwnership.FileData) {\n        this.UploadComponentObj.uploadedFileList = this.BeneficialOwnership.FileData;\n        this.UploadComponentObj.toggleUploadView = 2;\n        this.FileData = this.BeneficialOwnership.FileData;\n      }\n    }\n\n    this.cdr.detectChanges();\n  }\n\n  ngOnInit() {\n    this.filingInfoService.getLabel('BOI', 'FOT').subscribe(label => {\n      this.pageTitleService.setPageTitle(label);\n    });\n    this.BeneficialOwnership.FormationState = \"\";\n    this.Api.LoadDynamicControlsInCache().subscribe();\n    this.filingInfoService.getPrice(this.filingInfoService.SubCatCode.beneficialownership).subscribe(res => {\n      this.price = res;\n      this.basePrice = res;\n    });\n    this.StartLoad(); //this.StateComponent.writeValue(\"NaN\");\n\n    this.Api.GetDynamicFormMasterDataUpload(\"FS\", \"FOT\", \"BOI\", \"NaN\", 'A').subscribe(data => {\n      this.dynamicFormMasterData = data;\n      var dynamicFormData = \"\" === this.BeneficialOwnership.FormationState ? this.BeneficialOwnership.DynamicFormData : [];\n      this.questions$ = this.service.getMappedQuestions(data, dynamicFormData, this.selectedState);\n    });\n  }\n\n  OnSave(dynamicFormData) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      if (!dynamicFormData && _this.DynamicFormComponentObj) {\n        var enteredDynamicFormData = yield _this.DynamicFormComponentObj.getDynamicFormData();\n      }\n\n      try {\n        if (_this.Form.valid) {\n          _this.Save(dynamicFormData || enteredDynamicFormData);\n        }\n      } catch (ex) {\n        console.error(ex);\n      }\n    })();\n  }\n\n  StartLoad() {\n    var _this2 = this;\n\n    this.activatedRoute.queryParams.subscribe( /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (queryString) {\n        if (queryString.on) {\n          _this2.isAdd = false;\n        }\n\n        LoadFilingService(queryString, _this2.Api, FilingServiceResponseObject.BeneficialOwnership, _this2.formMode).then(serviceData => {\n          _this2.BeneficialOwnership = serviceData;\n\n          if (_this2.BeneficialOwnership.FileData) {\n            _this2.UploadComponentObj.uploadedFileList = _this2.BeneficialOwnership.FileData;\n            _this2.UploadComponentObj.toggleUploadView = 2;\n            _this2.FileData = _this2.BeneficialOwnership.FileData;\n          }\n\n          _this2.Load();\n        }).catch(e => console.log(e));\n      });\n\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  }\n\n  Load() {\n    this.getStatePrice(this.BeneficialOwnership.FormationState);\n    this.Form.controls.FormationState.setValue(this.BeneficialOwnership.FormationState);\n    this.Form.controls.SpecialInstructions.setValue(this.BeneficialOwnership.Remarks);\n  }\n\n  Save(dynamicFormData) {\n    this.BeneficialOwnership.FormationState = null;\n    this.BeneficialOwnership.Remarks = this.Form.controls.SpecialInstructions.value;\n\n    if (dynamicFormData) {\n      this.BeneficialOwnership.DynamicFormData = dynamicFormData.keyValuePair || [];\n    }\n\n    if (this.FileData.length) {\n      this.BeneficialOwnership.FileData = this.FileData;\n    } else {\n      this.toastr.error('File upload required.');\n      return;\n    }\n\n    if (this.IsUpSelling) {\n      this.UpSellingResolver(Object.assign({}, this.BeneficialOwnership));\n      return;\n    }\n\n    let filingService = {\n      BeneficialOwnerInformation: this.BeneficialOwnership,\n      productCode: \"FS\",\n      categoryCode: \"FOT\",\n      subCategoryCode: \"BOI\",\n      optionCode: \"NA\"\n    };\n    this.Api.SaveDynamicFormFilingService(filingService).subscribe(() => {\n      this.Form.reset();\n    });\n  }\n\n  getStatePrice(state) {\n    var request = new StatePriceRequest();\n    request.ProductCode = \"FS\";\n    request.CategoryCode = \"FOT\";\n    request.SubCategoryCode = \"BOI\";\n    request.State = state;\n    this.Api.GetStateWisePrice(request).subscribe(res => {\n      this.price = res.price > 0 ? res.price : this.basePrice;\n    });\n  }\n\n};\n\nBeneficialOwnershipComponent.ctorParameters = () => [{\n  type: ChangeDetectorRef\n}, {\n  type: FilingInfoService\n}, {\n  type: FilingApiService\n}, {\n  type: FormModeService\n}, {\n  type: ActivatedRoute\n}, {\n  type: QuestionService\n}, {\n  type: ToastrService\n}, {\n  type: PageTitleService\n}];\n\nBeneficialOwnershipComponent.propDecorators = {\n  _FormRef: [{\n    type: ViewChild,\n    args: [\"submitButton\", {\n      static: true\n    }]\n  }],\n  IsUpSelling: [{\n    type: Input\n  }],\n  DynamicFormComponent: [{\n    type: ViewChild,\n    args: [DynamicFormComponent, {\n      static: false\n    }]\n  }],\n  UploadComponent: [{\n    type: ViewChild,\n    args: ['UploadComponent', {\n      static: false\n    }]\n  }]\n};\nBeneficialOwnershipComponent = __decorate([Component({\n  selector: 'app-beneficial-ownership',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], BeneficialOwnershipComponent);\nexport { BeneficialOwnershipComponent };", "map": {"version": 3, "mappings": ";;;;AAAA,SAASA,iBAAT,EAA4BC,SAA5B,EAAmDC,KAAnD,EAAkEC,SAAlE,QAAmF,eAAnF;AACA,SAASC,WAAT,EAAsBC,SAAtB,QAAuC,gBAAvC;AACA,SAASC,cAAT,QAAuC,iBAAvC;AAEA,SAASC,aAAT,QAA8B,YAA9B;AAGA,SAASC,oBAAT,QAAqC,uEAArC;AAEA,SAASC,uBAAT,QAAwC,qDAAxC;AACA,SAASC,iBAAT,QAAkC,gEAAlC;AAEA,SAASC,eAAT,QAAgC,wDAAhC;AACA,SAASC,eAAT,QAAgC,yDAAhC;AACA,SAASC,2BAAT,QAA4C,iCAA5C;AACA,SAASC,iBAAT,QAAkC,0CAAlC;AACA,SAASC,mBAAT,QAAoC,kCAApC;AACA,SAASC,iBAAT,QAAkC,qCAAlC;AACA,SAASC,gBAAT,QAAiC,iCAAjC;AACA,SAASC,gBAAT,QAAiC,2DAAjC;IAOaC,4BAA4B,SAA5BA,4BAA4B;EA2DvCC,YAAoBC,GAApB,EAAmDC,iBAAnD,EAAiGC,GAAjG,EACSC,QADT,EAC4CC,cAD5C,EACoFC,OADpF,EAEUC,MAFV,EAEyCC,gBAFzC,EAE2E;IAFvD;IAA+B;IAA8C;IACxF;IAAmC;IAAwC;IAC1E;IAA+B;IApDhC,mBAAuB,KAAvB;IAcT,aAAQ,IAAR;IACA,gBAAgB,EAAhB;IAEA,2BAA2C,IAAIb,mBAAJ,EAA3C;IAGA,6BAA+B,EAA/B;IAwEA,YAAO,IAAIV,SAAJ,CAAc;MACnBwB,cAAc,EAAE,IAAIzB,WAAJ,CAA+B,IAA/B,CADG;MAEnB0B,aAAa,EAAE,IAAI1B,WAAJ,CAA+B,IAA/B,CAFI;MAGnB;MACA2B,mBAAmB,EAAE,IAAI3B,WAAJ,CAA+B,IAA/B,EAAqC,CACxDK,uBAAuB,CAACuB,mBADgC,CAArC;IAJF,CAAd,CAAP;EAvCK;;EA3DoD,IAARC,QAAQ,CACvDC,OADuD,EACpC;IAEnB,KAAKA,OAAL,GAAeA,OAAf;EACD;;EAGDC,gBAAgB;IACdC,UAAU,CAAC,MAAK;MACd,KAAKF,OAAL,CAAaG,aAAb,CAA2BC,KAA3B;IACD,CAFS,EAEP,EAFO,CAAV;IAIA,OAAO,IAAIC,OAAJ,CAAaC,GAAD,IAAQ;MACzB,IAAIA,GAAJ,EAAS;QACP,KAAKC,iBAAL,GAAyBD,GAAzB;MACD;IACF,CAJM,CAAP;EAKD;;EAc2E,IAApBhC,oBAAoB,CAACkC,oBAAD,EAA2C;IACrH,IAAIA,oBAAJ,EAA0B;MACxB,KAAKC,uBAAL,GAA+BD,oBAA/B;IACD;EACF;;EAGmE,IAAfE,eAAe,CAACC,eAAD,EAAuC;IACzG,IAAIA,eAAJ,EAAqB;MAEnB,KAAKC,kBAAL,GAA0BD,eAA1B;MAEA,KAAKC,kBAAL,CAAwBC,gBAAxB,CAAyCC,SAAzC,CAAmDC,IAAI,IAAG;QACxD,KAAKC,QAAL,GAAgBD,IAAhB;MACD,CAFD,EAJmB,CAOnB;;MACA,IAAI,KAAKlC,mBAAL,CAAyBmC,QAA7B,EAAuC;QACrC,KAAKJ,kBAAL,CAAwBK,gBAAxB,GAA2C,KAAKpC,mBAAL,CAAyBmC,QAApE;QACA,KAAKJ,kBAAL,CAAwBM,gBAAxB,GAA2C,CAA3C;QACA,KAAKF,QAAL,GAAgB,KAAKnC,mBAAL,CAAyBmC,QAAzC;MACD;IACF;;IACD,KAAK7B,GAAL,CAASgC,aAAT;EACD;;EAODC,QAAQ;IACN,KAAKhC,iBAAL,CAAuBiC,QAAvB,CAAgC,KAAhC,EAAuC,KAAvC,EAA8CP,SAA9C,CAAwDQ,KAAK,IAAG;MAC9D,KAAK5B,gBAAL,CAAsB6B,YAAtB,CAAmCD,KAAnC;IACD,CAFD;IAGA,KAAKzC,mBAAL,CAAyBc,cAAzB,GAA0C,EAA1C;IACA,KAAKN,GAAL,CAASmC,0BAAT,GAAsCV,SAAtC;IAEA,KAAK1B,iBAAL,CACGqC,QADH,CACY,KAAKrC,iBAAL,CAAuBsC,UAAvB,CAAkCC,mBAD9C,EAEGb,SAFH,CAEcR,GAAD,IAAQ;MACjB,KAAKsB,KAAL,GAAatB,GAAb;MACA,KAAKuB,SAAL,GAAiBvB,GAAjB;IACD,CALH;IASA,KAAKwB,SAAL,GAhBM,CAiBN;;IACA,KAAKzC,GAAL,CAAS0C,8BAAT,CACE,IADF,EAEE,KAFF,EAGE,KAHF,EAIE,KAJF,EAKE,GALF,EAMEjB,SANF,CAMaC,IAAD,IAAS;MACnB,KAAKiB,qBAAL,GAA6BjB,IAA7B;MACA,IAAIkB,eAAe,GAAI,OAAO,KAAKpD,mBAAL,CAAyBc,cAAjC,GAAmD,KAAKd,mBAAL,CAAyBqD,eAA5E,GAA8F,EAApH;MACA,KAAKC,UAAL,GAAkB,KAAK3C,OAAL,CAAa4C,kBAAb,CAChBrB,IADgB,EAEhBkB,eAFgB,EAGhB,KAAKI,aAHW,CAAlB;IAKD,CAdD;EAiBD;;EAWKC,MAAM,CAACL,eAAD,EAAiB;IAAA;;IAAA;MAC3B,IAAI,CAACA,eAAD,IAAoB,KAAI,CAACxB,uBAA7B,EAAsD;QACpD,IAAI8B,sBAAsB,SAAS,KAAI,CAAC9B,uBAAL,CAA6B+B,kBAA7B,EAAnC;MACD;;MACD,IAAI;QACF,IAAI,KAAI,CAACC,IAAL,CAAUC,KAAd,EAAqB;UACnB,KAAI,CAACC,IAAL,CAAUV,eAAe,IAAIM,sBAA7B;QACD;MACF,CAJD,CAIE,OAAOK,EAAP,EAAW;QACXC,OAAO,CAACC,KAAR,CAAcF,EAAd;MACD;IAV0B;EAW5B;;EAEDd,SAAS;IAAA;;IACP,KAAKvC,cAAL,CAAoBwD,WAApB,CAAgCjC,SAAhC;MAAA,6BAA0C,WAAOkC,WAAP,EAAsB;QAC9D,IAAIA,WAAW,CAACC,EAAhB,EAAoB;UAClB,MAAI,CAACC,KAAL,GAAa,KAAb;QACD;;QACDtE,iBAAiB,CACfoE,WADe,EAEf,MAAI,CAAC3D,GAFU,EAGfV,2BAA2B,CAACE,mBAHb,EAIf,MAAI,CAACS,QAJU,CAAjB,CAMG6D,IANH,CAMSC,WAAD,IAAgB;UACpB,MAAI,CAACvE,mBAAL,GAA2BuE,WAA3B;;UACA,IAAI,MAAI,CAACvE,mBAAL,CAAyBmC,QAA7B,EAAuC;YACrC,MAAI,CAACJ,kBAAL,CAAwBK,gBAAxB,GAA2C,MAAI,CAACpC,mBAAL,CAAyBmC,QAApE;YACA,MAAI,CAACJ,kBAAL,CAAwBM,gBAAxB,GAA2C,CAA3C;YACA,MAAI,CAACF,QAAL,GAAgB,MAAI,CAACnC,mBAAL,CAAyBmC,QAAzC;UACD;;UACD,MAAI,CAACqC,IAAL;QACD,CAdH,EAeGC,KAfH,CAeUC,CAAD,IAAOV,OAAO,CAACW,GAAR,CAAYD,CAAZ,CAfhB;MAgBD,CApBD;;MAAA;QAAA;MAAA;IAAA;EAqBD;;EAEDF,IAAI;IACF,KAAKI,aAAL,CAAmB,KAAK5E,mBAAL,CAAyBc,cAA5C;IACA,KAAK8C,IAAL,CAAUiB,QAAV,CAAmB/D,cAAnB,CAAkCgE,QAAlC,CAA2C,KAAK9E,mBAAL,CAAyBc,cAApE;IACA,KAAK8C,IAAL,CAAUiB,QAAV,CAAmB7D,mBAAnB,CAAuC8D,QAAvC,CAAgD,KAAK9E,mBAAL,CAAyB+E,OAAzE;EACD;;EAEDjB,IAAI,CAACV,eAAD,EAAgB;IAClB,KAAKpD,mBAAL,CAAyBc,cAAzB,GAA0C,IAA1C;IACA,KAAKd,mBAAL,CAAyB+E,OAAzB,GAAmC,KAAKnB,IAAL,CAAUiB,QAAV,CAAmB7D,mBAAnB,CAAuCgE,KAA1E;;IAEA,IAAI5B,eAAJ,EAAqB;MACnB,KAAKpD,mBAAL,CAAyBqD,eAAzB,GAA2CD,eAAe,CAAC6B,YAAhB,IAAgC,EAA3E;IACD;;IAED,IAAI,KAAK9C,QAAL,CAAc+C,MAAlB,EAA0B;MACxB,KAAKlF,mBAAL,CAAyBmC,QAAzB,GAAoC,KAAKA,QAAzC;IACD,CAFD,MAGK;MACH,KAAKvB,MAAL,CAAYqD,KAAZ,CAAkB,uBAAlB;MACA;IACD;;IAED,IAAI,KAAKkB,WAAT,EAAsB;MACpB,KAAKzD,iBAAL,CAAuB0D,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,KAAKrF,mBAAvB,CAAvB;MACA;IACD;;IAED,IAAIsF,aAAa,GAAG;MAClBC,0BAA0B,EAAE,KAAKvF,mBADf;MAElBwF,WAAW,EAAE,IAFK;MAGlBC,YAAY,EAAE,KAHI;MAIlBC,eAAe,EAAE,KAJC;MAKlBC,UAAU,EAAE;IALM,CAApB;IAQA,KAAKnF,GAAL,CAASoF,4BAAT,CAAsCN,aAAtC,EAAqDrD,SAArD,CAA+D,MAAK;MAClE,KAAK2B,IAAL,CAAUiC,KAAV;IACD,CAFD;EAGD;;EAEDjB,aAAa,CAACkB,KAAD,EAAM;IACjB,IAAIC,OAAO,GAAG,IAAIpG,iBAAJ,EAAd;IACAoG,OAAO,CAACC,WAAR,GAAsB,IAAtB;IACAD,OAAO,CAACE,YAAR,GAAuB,KAAvB;IACAF,OAAO,CAACG,eAAR,GAA0B,KAA1B;IACAH,OAAO,CAACI,KAAR,GAAgBL,KAAhB;IACA,KAAKtF,GAAL,CAAS4F,iBAAT,CAA2BL,OAA3B,EAAoC9D,SAApC,CAA+CR,GAAD,IAAQ;MACpD,KAAKsB,KAAL,GAAatB,GAAG,CAACsB,KAAJ,GAAY,CAAZ,GAAgBtB,GAAG,CAACsB,KAApB,GAA4B,KAAKC,SAA9C;IACD,CAFD;EAGD;;AApMsC;;;;;;;;;;;;;;;;;;;;;;UAGtC5D;IAASiH,OAAC,cAAD,EAAiB;MAAEC,MAAM,EAAE;IAAV,CAAjB;;;UAMTnH;;;UAyBAC;IAASiH,OAAC5G,oBAAD,EAAuB;MAAE6G,MAAM,EAAE;IAAV,CAAvB;;;UAOTlH;IAASiH,OAAC,iBAAD,EAAoB;MAAEC,MAAM,EAAE;IAAV,CAApB;;;AAzCClG,4BAA4B,eALxClB,SAAS,CAAC;EACTqH,QAAQ,EAAE,0BADD;EAETC,8BAFS;;AAAA,CAAD,CAK+B,GAA5BpG,4BAA4B,CAA5B;SAAAA", "names": ["ChangeDetectorRef", "Component", "Input", "ViewChild", "FormControl", "FormGroup", "ActivatedRoute", "ToastrService", "DynamicFormComponent", "CustomSharedValidations", "StatePriceRequest", "FormModeService", "QuestionService", "FilingServiceResponseObject", "LoadFilingService", "BeneficialOwnership", "FilingInfoService", "FilingApiService", "PageTitleService", "BeneficialOwnershipComponent", "constructor", "cdr", "filingInfoService", "Api", "formMode", "activatedRoute", "service", "toastr", "pageTitleService", "FormationState", "FormationType", "SpecialInstructions", "specialInstructions", "_FormRef", "FormRef", "getUpSellingData", "setTimeout", "nativeElement", "click", "Promise", "res", "UpSellingResolver", "dynamicFormComponent", "DynamicFormComponentObj", "UploadComponent", "uploadComponent", "UploadComponentObj", "EmitUploadedData", "subscribe", "data", "FileData", "uploadedFileList", "toggleUploadView", "detectChanges", "ngOnInit", "get<PERSON><PERSON><PERSON>", "label", "setPageTitle", "LoadDynamicControlsInCache", "getPrice", "SubCatCode", "beneficialownership", "price", "basePrice", "StartLoad", "GetDynamicFormMasterDataUpload", "dynamicFormMasterData", "dynamicFormData", "DynamicFormData", "questions$", "getMappedQuestions", "selectedState", "OnSave", "enteredDynamicFormData", "getDynamicFormData", "Form", "valid", "Save", "ex", "console", "error", "queryParams", "queryString", "on", "isAdd", "then", "serviceData", "Load", "catch", "e", "log", "getStatePrice", "controls", "setValue", "Remarks", "value", "keyValuePair", "length", "IsUpSelling", "Object", "assign", "filingService", "BeneficialOwnerInformation", "productCode", "categoryCode", "subCategoryCode", "optionCode", "SaveDynamicFormFilingService", "reset", "state", "request", "ProductCode", "CategoryCode", "SubCategoryCode", "State", "GetStateWisePrice", "args", "static", "selector", "template"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\viatech\\Ecom\\client\\acs\\src\\app\\Modules\\FilingService\\Pages\\beneficial-ownership\\beneficial-ownership.component.ts"], "sourcesContent": ["import { ChangeDetectorRef, Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';\r\nimport { FormControl, FormGroup } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Store } from '@ngrx/store';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { Observable } from 'rxjs';\r\nimport { ComponentMessageService } from 'src/app/Modules/Core/Services/Common/component-message.service';\r\nimport { DynamicFormComponent } from 'src/app/Modules/Shared/Components/dynamic-form/dynamic-form.component';\r\nimport { FileUploaderComponent } from 'src/app/Modules/Shared/Components/file-uploader/file-uploader.component';\r\nimport { CustomSharedValidations } from 'src/app/Modules/Shared/functions/custom-validations';\r\nimport { StatePriceRequest } from 'src/app/Modules/Shared/Models/Common/state-price-request.model';\r\nimport { QuestionBase } from 'src/app/Modules/Shared/Models/DynamicForm/question-base';\r\nimport { FormModeService } from 'src/app/Modules/Shared/Services/Common/FormModeService';\r\nimport { QuestionService } from 'src/app/Modules/Shared/Services/Common/question.service';\r\nimport { FilingServiceResponseObject } from '../../Enums/filing-service-enum';\r\nimport { LoadFilingService } from '../../Functions/filing-service-load.func';\r\nimport { BeneficialOwnership } from '../../Models/BeneficialOwnership';\r\nimport { FilingInfoService } from '../../Services/filing-price.service';\r\nimport { FilingApiService } from '../../Services/FilingApiService';\r\nimport { PageTitleService } from 'src/app/Modules/Shared/Services/Common/page-title.service';\r\n\r\n@Component({\r\n  selector: 'app-beneficial-ownership',\r\n  templateUrl: './beneficial-ownership.component.html',\r\n  styleUrls: ['./beneficial-ownership.component.css']\r\n})\r\nexport class BeneficialOwnershipComponent implements OnInit {\r\n\r\n  FormRef: ElementRef;\r\n  @ViewChild(\"submitButton\", { static: true }) set _FormRef(\r\n    FormRef: ElementRef\r\n  ) {\r\n    this.FormRef = FormRef;\r\n  }\r\n  UpSellingResolver;\r\n  @Input() IsUpSelling: boolean = false;\r\n  getUpSellingData(): Promise<any> {\r\n    setTimeout(() => {\r\n      this.FormRef.nativeElement.click();\r\n    }, 10);\r\n\r\n    return new Promise((res) => {\r\n      if (res) {\r\n        this.UpSellingResolver = res\r\n      }\r\n    });\r\n  }\r\n  questions$: Observable<QuestionBase<any>[]>;\r\n  categoryCode;\r\n  isAdd = true;\r\n  FileData: any = [];\r\n  selectedState: any;\r\n  BeneficialOwnership: BeneficialOwnership = new BeneficialOwnership();\r\n  price: number;\r\n  basePrice: number;\r\n  dynamicFormMasterData: any[] = [];\r\n\r\n  //@ViewChild(StateSelectorComponent, {static : true}) StateComponent: StateSelectorComponent;\r\n\r\n  DynamicFormComponentObj: DynamicFormComponent;\r\n  @ViewChild(DynamicFormComponent, { static: false }) set DynamicFormComponent(dynamicFormComponent: DynamicFormComponent) {\r\n    if (dynamicFormComponent) {\r\n      this.DynamicFormComponentObj = dynamicFormComponent;\r\n    }\r\n  }\r\n\r\n  UploadComponentObj: FileUploaderComponent;\r\n  @ViewChild('UploadComponent', { static: false }) set UploadComponent(uploadComponent: FileUploaderComponent) {\r\n    if (uploadComponent) {\r\n\r\n      this.UploadComponentObj = uploadComponent;\r\n\r\n      this.UploadComponentObj.EmitUploadedData.subscribe(data => {\r\n        this.FileData = data\r\n      });\r\n      //Mapping uploaded file data to FileUploadComponent in case of edit\r\n      if (this.BeneficialOwnership.FileData) {\r\n        this.UploadComponentObj.uploadedFileList = this.BeneficialOwnership.FileData\r\n        this.UploadComponentObj.toggleUploadView = 2\r\n        this.FileData = this.BeneficialOwnership.FileData;\r\n      }\r\n    }\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  constructor(private cdr: ChangeDetectorRef, public filingInfoService: FilingInfoService, private Api: FilingApiService,\r\n    public formMode: FormModeService, private activatedRoute: ActivatedRoute, private service: QuestionService,\r\n    private toastr: ToastrService, private pageTitleService: PageTitleService\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.filingInfoService.getLabel('BOI', 'FOT').subscribe(label => {\r\n      this.pageTitleService.setPageTitle(label);\r\n    });\r\n    this.BeneficialOwnership.FormationState = \"\"\r\n    this.Api.LoadDynamicControlsInCache().subscribe();\r\n\r\n    this.filingInfoService\r\n      .getPrice(this.filingInfoService.SubCatCode.beneficialownership)\r\n      .subscribe((res) => {\r\n        this.price = res;\r\n        this.basePrice = res;\r\n      });\r\n\r\n\r\n\r\n    this.StartLoad();\r\n    //this.StateComponent.writeValue(\"NaN\");\r\n    this.Api.GetDynamicFormMasterDataUpload(\r\n      \"FS\",\r\n      \"FOT\",\r\n      \"BOI\",\r\n      \"NaN\",\r\n      'A'\r\n    ).subscribe((data) => {\r\n      this.dynamicFormMasterData = data;\r\n      var dynamicFormData = (\"\" === this.BeneficialOwnership.FormationState) ? this.BeneficialOwnership.DynamicFormData : [];\r\n      this.questions$ = this.service.getMappedQuestions(\r\n        data,\r\n        dynamicFormData,\r\n        this.selectedState\r\n      );\r\n    });\r\n\r\n\r\n  }\r\n\r\n  Form = new FormGroup({\r\n    FormationState: new FormControl<string | null>(null),\r\n    FormationType: new FormControl<string | null>(null),\r\n    //SelectedFilingOptionType: new FormControl(null),\r\n    SpecialInstructions: new FormControl<string | null>(null, [\r\n      CustomSharedValidations.specialInstructions,\r\n    ]),\r\n  });\r\n\r\n  async OnSave(dynamicFormData?) {\r\n    if (!dynamicFormData && this.DynamicFormComponentObj) {\r\n      var enteredDynamicFormData = await this.DynamicFormComponentObj.getDynamicFormData();\r\n    }\r\n    try {\r\n      if (this.Form.valid) {\r\n        this.Save(dynamicFormData || enteredDynamicFormData);\r\n      }\r\n    } catch (ex) {\r\n      console.error(ex);\r\n    }\r\n  }\r\n\r\n  StartLoad() {\r\n    this.activatedRoute.queryParams.subscribe(async (queryString) => {\r\n      if (queryString.on) {\r\n        this.isAdd = false;\r\n      }\r\n      LoadFilingService<BeneficialOwnership>(\r\n        queryString,\r\n        this.Api,\r\n        FilingServiceResponseObject.BeneficialOwnership,\r\n        this.formMode\r\n      )\r\n        .then((serviceData) => {\r\n          this.BeneficialOwnership = serviceData;\r\n          if (this.BeneficialOwnership.FileData) {\r\n            this.UploadComponentObj.uploadedFileList = this.BeneficialOwnership.FileData\r\n            this.UploadComponentObj.toggleUploadView = 2\r\n            this.FileData = this.BeneficialOwnership.FileData;\r\n          }\r\n          this.Load();\r\n        })\r\n        .catch((e) => console.log(e));\r\n    });\r\n  }\r\n\r\n  Load() {\r\n    this.getStatePrice(this.BeneficialOwnership.FormationState);\r\n    this.Form.controls.FormationState.setValue(this.BeneficialOwnership.FormationState);\r\n    this.Form.controls.SpecialInstructions.setValue(this.BeneficialOwnership.Remarks);\r\n  }\r\n\r\n  Save(dynamicFormData) {\r\n    this.BeneficialOwnership.FormationState = null;\r\n    this.BeneficialOwnership.Remarks = this.Form.controls.SpecialInstructions.value;\r\n\r\n    if (dynamicFormData) {\r\n      this.BeneficialOwnership.DynamicFormData = dynamicFormData.keyValuePair || [];\r\n    }\r\n\r\n    if (this.FileData.length) {\r\n      this.BeneficialOwnership.FileData = this.FileData;\r\n    }\r\n    else {\r\n      this.toastr.error('File upload required.');\r\n      return;\r\n    }\r\n\r\n    if (this.IsUpSelling) {\r\n      this.UpSellingResolver(Object.assign({}, this.BeneficialOwnership));\r\n      return;\r\n    }\r\n\r\n    let filingService = {\r\n      BeneficialOwnerInformation: this.BeneficialOwnership,\r\n      productCode: \"FS\",\r\n      categoryCode: \"FOT\",\r\n      subCategoryCode: \"BOI\",\r\n      optionCode: \"NA\"\r\n    };\r\n\r\n    this.Api.SaveDynamicFormFilingService(filingService).subscribe(() => {\r\n      this.Form.reset();\r\n    });\r\n  }\r\n\r\n  getStatePrice(state) {\r\n    var request = new StatePriceRequest();\r\n    request.ProductCode = \"FS\";\r\n    request.CategoryCode = \"FOT\";\r\n    request.SubCategoryCode = \"BOI\";\r\n    request.State = state;\r\n    this.Api.GetStateWisePrice(request).subscribe((res) => {\r\n      this.price = res.price > 0 ? res.price : this.basePrice;\r\n    });\r\n  }\r\n\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}