{"ast": null, "code": "import { Observable } from '../Observable';\nimport { isScheduler } from '../util/isScheduler';\nimport { mergeAll } from '../operators/mergeAll';\nimport { fromArray } from './fromArray';\nexport function merge(...observables) {\n  let concurrent = Number.POSITIVE_INFINITY;\n  let scheduler = null;\n  let last = observables[observables.length - 1];\n\n  if (isScheduler(last)) {\n    scheduler = observables.pop();\n\n    if (observables.length > 1 && typeof observables[observables.length - 1] === 'number') {\n      concurrent = observables.pop();\n    }\n  } else if (typeof last === 'number') {\n    concurrent = observables.pop();\n  }\n\n  if (scheduler === null && observables.length === 1 && observables[0] instanceof Observable) {\n    return observables[0];\n  }\n\n  return mergeAll(concurrent)(fromArray(observables, scheduler));\n}", "map": {"version": 3, "names": ["Observable", "isScheduler", "mergeAll", "fromArray", "merge", "observables", "concurrent", "Number", "POSITIVE_INFINITY", "scheduler", "last", "length", "pop"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/merge.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { isScheduler } from '../util/isScheduler';\nimport { mergeAll } from '../operators/mergeAll';\nimport { fromArray } from './fromArray';\nexport function merge(...observables) {\n    let concurrent = Number.POSITIVE_INFINITY;\n    let scheduler = null;\n    let last = observables[observables.length - 1];\n    if (isScheduler(last)) {\n        scheduler = observables.pop();\n        if (observables.length > 1 && typeof observables[observables.length - 1] === 'number') {\n            concurrent = observables.pop();\n        }\n    }\n    else if (typeof last === 'number') {\n        concurrent = observables.pop();\n    }\n    if (scheduler === null && observables.length === 1 && observables[0] instanceof Observable) {\n        return observables[0];\n    }\n    return mergeAll(concurrent)(fromArray(observables, scheduler));\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,WAAT,QAA4B,qBAA5B;AACA,SAASC,QAAT,QAAyB,uBAAzB;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,OAAO,SAASC,KAAT,CAAe,GAAGC,WAAlB,EAA+B;EAClC,IAAIC,UAAU,GAAGC,MAAM,CAACC,iBAAxB;EACA,IAAIC,SAAS,GAAG,IAAhB;EACA,IAAIC,IAAI,GAAGL,WAAW,CAACA,WAAW,CAACM,MAAZ,GAAqB,CAAtB,CAAtB;;EACA,IAAIV,WAAW,CAACS,IAAD,CAAf,EAAuB;IACnBD,SAAS,GAAGJ,WAAW,CAACO,GAAZ,EAAZ;;IACA,IAAIP,WAAW,CAACM,MAAZ,GAAqB,CAArB,IAA0B,OAAON,WAAW,CAACA,WAAW,CAACM,MAAZ,GAAqB,CAAtB,CAAlB,KAA+C,QAA7E,EAAuF;MACnFL,UAAU,GAAGD,WAAW,CAACO,GAAZ,EAAb;IACH;EACJ,CALD,MAMK,IAAI,OAAOF,IAAP,KAAgB,QAApB,EAA8B;IAC/BJ,UAAU,GAAGD,WAAW,CAACO,GAAZ,EAAb;EACH;;EACD,IAAIH,SAAS,KAAK,IAAd,IAAsBJ,WAAW,CAACM,MAAZ,KAAuB,CAA7C,IAAkDN,WAAW,CAAC,CAAD,CAAX,YAA0BL,UAAhF,EAA4F;IACxF,OAAOK,WAAW,CAAC,CAAD,CAAlB;EACH;;EACD,OAAOH,QAAQ,CAACI,UAAD,CAAR,CAAqBH,SAAS,CAACE,WAAD,EAAcI,SAAd,CAA9B,CAAP;AACH"}, "metadata": {}, "sourceType": "module"}