{"ast": null, "code": "import { switchMap } from './switchMap';\nexport function switchMapTo(innerObservable, resultSelector) {\n  return resultSelector ? switchMap(() => innerObservable, resultSelector) : switchMap(() => innerObservable);\n}", "map": {"version": 3, "names": ["switchMap", "switchMapTo", "innerObservable", "resultSelector"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/switchMapTo.js"], "sourcesContent": ["import { switchMap } from './switchMap';\nexport function switchMapTo(innerObservable, resultSelector) {\n    return resultSelector ? switchMap(() => innerObservable, resultSelector) : switchMap(() => innerObservable);\n}\n"], "mappings": "AAAA,SAASA,SAAT,QAA0B,aAA1B;AACA,OAAO,SAASC,WAAT,CAAqBC,eAArB,EAAsCC,cAAtC,EAAsD;EACzD,OAAOA,cAAc,GAAGH,SAAS,CAAC,MAAME,eAAP,EAAwBC,cAAxB,CAAZ,GAAsDH,SAAS,CAAC,MAAME,eAAP,CAApF;AACH"}, "metadata": {}, "sourceType": "module"}