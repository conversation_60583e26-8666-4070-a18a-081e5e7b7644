{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, Input, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, NgModule } from '@angular/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { MatCommonModule } from '@angular/material/core';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Content of a card, needed as it's used as a selector in the API.\n * @docs-private\n */\n\nconst _c0 = [\"*\", [[\"mat-card-footer\"]]];\nconst _c1 = [\"*\", \"mat-card-footer\"];\nconst _c2 = [[[\"\", \"mat-card-avatar\", \"\"], [\"\", \"matCardAvatar\", \"\"]], [[\"mat-card-title\"], [\"mat-card-subtitle\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardTitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]], \"*\"];\nconst _c3 = [\"[mat-card-avatar], [matCardAvatar]\", \"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\", \"*\"];\nconst _c4 = [[[\"mat-card-title\"], [\"mat-card-subtitle\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardTitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]], [[\"img\"]], \"*\"];\nconst _c5 = [\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\", \"img\", \"*\"];\n\nclass MatCardContent {}\n\nMatCardContent.ɵfac = function MatCardContent_Factory(t) {\n  return new (t || MatCardContent)();\n};\n\nMatCardContent.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardContent,\n  selectors: [[\"mat-card-content\"], [\"\", \"mat-card-content\", \"\"], [\"\", \"matCardContent\", \"\"]],\n  hostAttrs: [1, \"mat-card-content\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardContent, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-content, [mat-card-content], [matCardContent]',\n      host: {\n        'class': 'mat-card-content'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Title of a card, needed as it's used as a selector in the API.\n * @docs-private\n */\n\n\nclass MatCardTitle {}\n\nMatCardTitle.ɵfac = function MatCardTitle_Factory(t) {\n  return new (t || MatCardTitle)();\n};\n\nMatCardTitle.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardTitle,\n  selectors: [[\"mat-card-title\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"matCardTitle\", \"\"]],\n  hostAttrs: [1, \"mat-card-title\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardTitle, [{\n    type: Directive,\n    args: [{\n      selector: `mat-card-title, [mat-card-title], [matCardTitle]`,\n      host: {\n        'class': 'mat-card-title'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Sub-title of a card, needed as it's used as a selector in the API.\n * @docs-private\n */\n\n\nclass MatCardSubtitle {}\n\nMatCardSubtitle.ɵfac = function MatCardSubtitle_Factory(t) {\n  return new (t || MatCardSubtitle)();\n};\n\nMatCardSubtitle.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardSubtitle,\n  selectors: [[\"mat-card-subtitle\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]],\n  hostAttrs: [1, \"mat-card-subtitle\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardSubtitle, [{\n    type: Directive,\n    args: [{\n      selector: `mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]`,\n      host: {\n        'class': 'mat-card-subtitle'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Action section of a card, needed as it's used as a selector in the API.\n * @docs-private\n */\n\n\nclass MatCardActions {\n  constructor() {\n    /** Position of the actions inside the card. */\n    this.align = 'start';\n  }\n\n}\n\nMatCardActions.ɵfac = function MatCardActions_Factory(t) {\n  return new (t || MatCardActions)();\n};\n\nMatCardActions.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardActions,\n  selectors: [[\"mat-card-actions\"]],\n  hostAttrs: [1, \"mat-card-actions\"],\n  hostVars: 2,\n  hostBindings: function MatCardActions_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-card-actions-align-end\", ctx.align === \"end\");\n    }\n  },\n  inputs: {\n    align: \"align\"\n  },\n  exportAs: [\"matCardActions\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardActions, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-actions',\n      exportAs: 'matCardActions',\n      host: {\n        'class': 'mat-card-actions',\n        '[class.mat-card-actions-align-end]': 'align === \"end\"'\n      }\n    }]\n  }], null, {\n    align: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Footer of a card, needed as it's used as a selector in the API.\n * @docs-private\n */\n\n\nclass MatCardFooter {}\n\nMatCardFooter.ɵfac = function MatCardFooter_Factory(t) {\n  return new (t || MatCardFooter)();\n};\n\nMatCardFooter.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardFooter,\n  selectors: [[\"mat-card-footer\"]],\n  hostAttrs: [1, \"mat-card-footer\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardFooter, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-footer',\n      host: {\n        'class': 'mat-card-footer'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Image used in a card, needed to add the mat- CSS styling.\n * @docs-private\n */\n\n\nclass MatCardImage {}\n\nMatCardImage.ɵfac = function MatCardImage_Factory(t) {\n  return new (t || MatCardImage)();\n};\n\nMatCardImage.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardImage,\n  selectors: [[\"\", \"mat-card-image\", \"\"], [\"\", \"matCardImage\", \"\"]],\n  hostAttrs: [1, \"mat-card-image\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-image], [matCardImage]',\n      host: {\n        'class': 'mat-card-image'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Image used in a card, needed to add the mat- CSS styling.\n * @docs-private\n */\n\n\nclass MatCardSmImage {}\n\nMatCardSmImage.ɵfac = function MatCardSmImage_Factory(t) {\n  return new (t || MatCardSmImage)();\n};\n\nMatCardSmImage.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardSmImage,\n  selectors: [[\"\", \"mat-card-sm-image\", \"\"], [\"\", \"matCardImageSmall\", \"\"]],\n  hostAttrs: [1, \"mat-card-sm-image\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardSmImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-sm-image], [matCardImageSmall]',\n      host: {\n        'class': 'mat-card-sm-image'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Image used in a card, needed to add the mat- CSS styling.\n * @docs-private\n */\n\n\nclass MatCardMdImage {}\n\nMatCardMdImage.ɵfac = function MatCardMdImage_Factory(t) {\n  return new (t || MatCardMdImage)();\n};\n\nMatCardMdImage.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardMdImage,\n  selectors: [[\"\", \"mat-card-md-image\", \"\"], [\"\", \"matCardImageMedium\", \"\"]],\n  hostAttrs: [1, \"mat-card-md-image\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardMdImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-md-image], [matCardImageMedium]',\n      host: {\n        'class': 'mat-card-md-image'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Image used in a card, needed to add the mat- CSS styling.\n * @docs-private\n */\n\n\nclass MatCardLgImage {}\n\nMatCardLgImage.ɵfac = function MatCardLgImage_Factory(t) {\n  return new (t || MatCardLgImage)();\n};\n\nMatCardLgImage.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardLgImage,\n  selectors: [[\"\", \"mat-card-lg-image\", \"\"], [\"\", \"matCardImageLarge\", \"\"]],\n  hostAttrs: [1, \"mat-card-lg-image\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardLgImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-lg-image], [matCardImageLarge]',\n      host: {\n        'class': 'mat-card-lg-image'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Large image used in a card, needed to add the mat- CSS styling.\n * @docs-private\n */\n\n\nclass MatCardXlImage {}\n\nMatCardXlImage.ɵfac = function MatCardXlImage_Factory(t) {\n  return new (t || MatCardXlImage)();\n};\n\nMatCardXlImage.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardXlImage,\n  selectors: [[\"\", \"mat-card-xl-image\", \"\"], [\"\", \"matCardImageXLarge\", \"\"]],\n  hostAttrs: [1, \"mat-card-xl-image\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardXlImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-xl-image], [matCardImageXLarge]',\n      host: {\n        'class': 'mat-card-xl-image'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Avatar image used in a card, needed to add the mat- CSS styling.\n * @docs-private\n */\n\n\nclass MatCardAvatar {}\n\nMatCardAvatar.ɵfac = function MatCardAvatar_Factory(t) {\n  return new (t || MatCardAvatar)();\n};\n\nMatCardAvatar.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatCardAvatar,\n  selectors: [[\"\", \"mat-card-avatar\", \"\"], [\"\", \"matCardAvatar\", \"\"]],\n  hostAttrs: [1, \"mat-card-avatar\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardAvatar, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-avatar], [matCardAvatar]',\n      host: {\n        'class': 'mat-card-avatar'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * A basic content container component that adds the styles of a Material design card.\n *\n * While this component can be used alone, it also provides a number\n * of preset styles for common card sections, including:\n * - mat-card-title\n * - mat-card-subtitle\n * - mat-card-content\n * - mat-card-actions\n * - mat-card-footer\n */\n\n\nclass MatCard {\n  // @breaking-change 9.0.0 `_animationMode` parameter to be made required.\n  constructor(_animationMode) {\n    this._animationMode = _animationMode;\n  }\n\n}\n\nMatCard.ɵfac = function MatCard_Factory(t) {\n  return new (t || MatCard)(i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n\nMatCard.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatCard,\n  selectors: [[\"mat-card\"]],\n  hostAttrs: [1, \"mat-card\", \"mat-focus-indicator\"],\n  hostVars: 2,\n  hostBindings: function MatCard_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n    }\n  },\n  exportAs: [\"matCard\"],\n  ngContentSelectors: _c1,\n  decls: 2,\n  vars: 0,\n  template: function MatCard_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c0);\n      i0.ɵɵprojection(0);\n      i0.ɵɵprojection(1, 1);\n    }\n  },\n  styles: [\".mat-card{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);display:block;position:relative;padding:16px;border-radius:4px}.mat-card._mat-animation-noopable{transition:none !important;animation:none !important}.mat-card>.mat-divider-horizontal{position:absolute;left:0;width:100%}[dir=rtl] .mat-card>.mat-divider-horizontal{left:auto;right:0}.mat-card>.mat-divider-horizontal.mat-divider-inset{position:static;margin:0}[dir=rtl] .mat-card>.mat-divider-horizontal.mat-divider-inset{margin-right:0}.cdk-high-contrast-active .mat-card{outline:solid 1px}.mat-card-actions,.mat-card-subtitle,.mat-card-content{display:block;margin-bottom:16px}.mat-card-title{display:block;margin-bottom:8px}.mat-card-actions{margin-left:-8px;margin-right:-8px;padding:8px 0}.mat-card-actions-align-end{display:flex;justify-content:flex-end}.mat-card-image{width:calc(100% + 32px);margin:0 -16px 16px -16px;display:block;overflow:hidden}.mat-card-image img{width:100%}.mat-card-footer{display:block;margin:0 -16px -16px -16px}.mat-card-actions .mat-button,.mat-card-actions .mat-raised-button,.mat-card-actions .mat-stroked-button{margin:0 8px}.mat-card-header{display:flex;flex-direction:row}.mat-card-header .mat-card-title{margin-bottom:12px}.mat-card-header-text{margin:0 16px}.mat-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;object-fit:cover}.mat-card-title-group{display:flex;justify-content:space-between}.mat-card-sm-image{width:80px;height:80px}.mat-card-md-image{width:112px;height:112px}.mat-card-lg-image{width:152px;height:152px}.mat-card-xl-image{width:240px;height:240px;margin:-8px}.mat-card-title-group>.mat-card-xl-image{margin:-8px 0 8px}@media(max-width: 599px){.mat-card-title-group{margin:0}.mat-card-xl-image{margin-left:0;margin-right:0}}.mat-card>:first-child,.mat-card-content>:first-child{margin-top:0}.mat-card>:last-child:not(.mat-card-footer),.mat-card-content>:last-child:not(.mat-card-footer){margin-bottom:0}.mat-card-image:first-child{margin-top:-16px;border-top-left-radius:inherit;border-top-right-radius:inherit}.mat-card>.mat-card-actions:last-child{margin-bottom:-8px;padding-bottom:0}.mat-card-actions:not(.mat-card-actions-align-end) .mat-button:first-child,.mat-card-actions:not(.mat-card-actions-align-end) .mat-raised-button:first-child,.mat-card-actions:not(.mat-card-actions-align-end) .mat-stroked-button:first-child{margin-left:0;margin-right:0}.mat-card-actions-align-end .mat-button:last-child,.mat-card-actions-align-end .mat-raised-button:last-child,.mat-card-actions-align-end .mat-stroked-button:last-child{margin-left:0;margin-right:0}.mat-card-title:not(:first-child),.mat-card-subtitle:not(:first-child){margin-top:-4px}.mat-card-header .mat-card-subtitle:not(:first-child){margin-top:-8px}.mat-card>.mat-card-xl-image:first-child{margin-top:-8px}.mat-card>.mat-card-xl-image:last-child{margin-bottom:-8px}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCard, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card',\n      exportAs: 'matCard',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-card mat-focus-indicator',\n        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"'\n      },\n      template: \"<ng-content></ng-content>\\n<ng-content select=\\\"mat-card-footer\\\"></ng-content>\\n\",\n      styles: [\".mat-card{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);display:block;position:relative;padding:16px;border-radius:4px}.mat-card._mat-animation-noopable{transition:none !important;animation:none !important}.mat-card>.mat-divider-horizontal{position:absolute;left:0;width:100%}[dir=rtl] .mat-card>.mat-divider-horizontal{left:auto;right:0}.mat-card>.mat-divider-horizontal.mat-divider-inset{position:static;margin:0}[dir=rtl] .mat-card>.mat-divider-horizontal.mat-divider-inset{margin-right:0}.cdk-high-contrast-active .mat-card{outline:solid 1px}.mat-card-actions,.mat-card-subtitle,.mat-card-content{display:block;margin-bottom:16px}.mat-card-title{display:block;margin-bottom:8px}.mat-card-actions{margin-left:-8px;margin-right:-8px;padding:8px 0}.mat-card-actions-align-end{display:flex;justify-content:flex-end}.mat-card-image{width:calc(100% + 32px);margin:0 -16px 16px -16px;display:block;overflow:hidden}.mat-card-image img{width:100%}.mat-card-footer{display:block;margin:0 -16px -16px -16px}.mat-card-actions .mat-button,.mat-card-actions .mat-raised-button,.mat-card-actions .mat-stroked-button{margin:0 8px}.mat-card-header{display:flex;flex-direction:row}.mat-card-header .mat-card-title{margin-bottom:12px}.mat-card-header-text{margin:0 16px}.mat-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;object-fit:cover}.mat-card-title-group{display:flex;justify-content:space-between}.mat-card-sm-image{width:80px;height:80px}.mat-card-md-image{width:112px;height:112px}.mat-card-lg-image{width:152px;height:152px}.mat-card-xl-image{width:240px;height:240px;margin:-8px}.mat-card-title-group>.mat-card-xl-image{margin:-8px 0 8px}@media(max-width: 599px){.mat-card-title-group{margin:0}.mat-card-xl-image{margin-left:0;margin-right:0}}.mat-card>:first-child,.mat-card-content>:first-child{margin-top:0}.mat-card>:last-child:not(.mat-card-footer),.mat-card-content>:last-child:not(.mat-card-footer){margin-bottom:0}.mat-card-image:first-child{margin-top:-16px;border-top-left-radius:inherit;border-top-right-radius:inherit}.mat-card>.mat-card-actions:last-child{margin-bottom:-8px;padding-bottom:0}.mat-card-actions:not(.mat-card-actions-align-end) .mat-button:first-child,.mat-card-actions:not(.mat-card-actions-align-end) .mat-raised-button:first-child,.mat-card-actions:not(.mat-card-actions-align-end) .mat-stroked-button:first-child{margin-left:0;margin-right:0}.mat-card-actions-align-end .mat-button:last-child,.mat-card-actions-align-end .mat-raised-button:last-child,.mat-card-actions-align-end .mat-stroked-button:last-child{margin-left:0;margin-right:0}.mat-card-title:not(:first-child),.mat-card-subtitle:not(:first-child){margin-top:-4px}.mat-card-header .mat-card-subtitle:not(:first-child){margin-top:-8px}.mat-card>.mat-card-xl-image:first-child{margin-top:-8px}.mat-card>.mat-card-xl-image:last-child{margin-bottom:-8px}\"]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Component intended to be used within the `<mat-card>` component. It adds styles for a\n * preset header section (i.e. a title, subtitle, and avatar layout).\n * @docs-private\n */\n\n\nclass MatCardHeader {}\n\nMatCardHeader.ɵfac = function MatCardHeader_Factory(t) {\n  return new (t || MatCardHeader)();\n};\n\nMatCardHeader.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatCardHeader,\n  selectors: [[\"mat-card-header\"]],\n  hostAttrs: [1, \"mat-card-header\"],\n  ngContentSelectors: _c3,\n  decls: 4,\n  vars: 0,\n  consts: [[1, \"mat-card-header-text\"]],\n  template: function MatCardHeader_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c2);\n      i0.ɵɵprojection(0);\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵprojection(2, 1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵprojection(3, 2);\n    }\n  },\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card-header',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-card-header'\n      },\n      template: \"<ng-content select=\\\"[mat-card-avatar], [matCardAvatar]\\\"></ng-content>\\n<div class=\\\"mat-card-header-text\\\">\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content></ng-content>\\n\"\n    }]\n  }], null, null);\n})();\n/**\n * Component intended to be used within the `<mat-card>` component. It adds styles for a preset\n * layout that groups an image with a title section.\n * @docs-private\n */\n\n\nclass MatCardTitleGroup {}\n\nMatCardTitleGroup.ɵfac = function MatCardTitleGroup_Factory(t) {\n  return new (t || MatCardTitleGroup)();\n};\n\nMatCardTitleGroup.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatCardTitleGroup,\n  selectors: [[\"mat-card-title-group\"]],\n  hostAttrs: [1, \"mat-card-title-group\"],\n  ngContentSelectors: _c5,\n  decls: 4,\n  vars: 0,\n  template: function MatCardTitleGroup_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c4);\n      i0.ɵɵelementStart(0, \"div\");\n      i0.ɵɵprojection(1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵprojection(2, 1);\n      i0.ɵɵprojection(3, 2);\n    }\n  },\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardTitleGroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card-title-group',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-card-title-group'\n      },\n      template: \"<div>\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content select=\\\"img\\\"></ng-content>\\n<ng-content></ng-content>\\n\"\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatCardModule {}\n\nMatCardModule.ɵfac = function MatCardModule_Factory(t) {\n  return new (t || MatCardModule)();\n};\n\nMatCardModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatCardModule,\n  declarations: [MatCard, MatCardHeader, MatCardTitleGroup, MatCardContent, MatCardTitle, MatCardSubtitle, MatCardActions, MatCardFooter, MatCardSmImage, MatCardMdImage, MatCardLgImage, MatCardImage, MatCardXlImage, MatCardAvatar],\n  imports: [MatCommonModule],\n  exports: [MatCard, MatCardHeader, MatCardTitleGroup, MatCardContent, MatCardTitle, MatCardSubtitle, MatCardActions, MatCardFooter, MatCardSmImage, MatCardMdImage, MatCardLgImage, MatCardImage, MatCardXlImage, MatCardAvatar, MatCommonModule]\n});\nMatCardModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule, MatCommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule],\n      exports: [MatCard, MatCardHeader, MatCardTitleGroup, MatCardContent, MatCardTitle, MatCardSubtitle, MatCardActions, MatCardFooter, MatCardSmImage, MatCardMdImage, MatCardLgImage, MatCardImage, MatCardXlImage, MatCardAvatar, MatCommonModule],\n      declarations: [MatCard, MatCardHeader, MatCardTitleGroup, MatCardContent, MatCardTitle, MatCardSubtitle, MatCardActions, MatCardFooter, MatCardSmImage, MatCardMdImage, MatCardLgImage, MatCardImage, MatCardXlImage, MatCardAvatar]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardModule, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage };", "map": {"version": 3, "names": ["i0", "Directive", "Input", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Optional", "Inject", "NgModule", "ANIMATION_MODULE_TYPE", "MatCommonModule", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "ɵfac", "ɵdir", "type", "args", "selector", "host", "MatCardTitle", "MatCardSubtitle", "MatCardActions", "constructor", "align", "exportAs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatCardImage", "MatCardSmImage", "MatCardMdImage", "MatCardLgImage", "MatCardXlImage", "MatCardAvatar", "MatCard", "_animationMode", "ɵcmp", "encapsulation", "None", "changeDetection", "OnPush", "template", "styles", "undefined", "decorators", "MatCardHeader", "MatCardTitleGroup", "MatCardModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/material/fesm2020/card.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Input, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, NgModule } from '@angular/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { MatCommonModule } from '@angular/material/core';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Content of a card, needed as it's used as a selector in the API.\n * @docs-private\n */\nclass MatCardContent {\n}\nMatCardContent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardContent, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardContent.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatCardContent, selector: \"mat-card-content, [mat-card-content], [matCardContent]\", host: { classAttribute: \"mat-card-content\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-card-content, [mat-card-content], [matCardContent]',\n                    host: { 'class': 'mat-card-content' },\n                }]\n        }] });\n/**\n * Title of a card, needed as it's used as a selector in the API.\n * @docs-private\n */\nclass MatCardTitle {\n}\nMatCardTitle.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardTitle, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardTitle.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatCardTitle, selector: \"mat-card-title, [mat-card-title], [matCardTitle]\", host: { classAttribute: \"mat-card-title\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardTitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `mat-card-title, [mat-card-title], [matCardTitle]`,\n                    host: {\n                        'class': 'mat-card-title',\n                    },\n                }]\n        }] });\n/**\n * Sub-title of a card, needed as it's used as a selector in the API.\n * @docs-private\n */\nclass MatCardSubtitle {\n}\nMatCardSubtitle.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardSubtitle, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardSubtitle.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatCardSubtitle, selector: \"mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]\", host: { classAttribute: \"mat-card-subtitle\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardSubtitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]`,\n                    host: {\n                        'class': 'mat-card-subtitle',\n                    },\n                }]\n        }] });\n/**\n * Action section of a card, needed as it's used as a selector in the API.\n * @docs-private\n */\nclass MatCardActions {\n    constructor() {\n        /** Position of the actions inside the card. */\n        this.align = 'start';\n    }\n}\nMatCardActions.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardActions, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardActions.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatCardActions, selector: \"mat-card-actions\", inputs: { align: \"align\" }, host: { properties: { \"class.mat-card-actions-align-end\": \"align === \\\"end\\\"\" }, classAttribute: \"mat-card-actions\" }, exportAs: [\"matCardActions\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardActions, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-card-actions',\n                    exportAs: 'matCardActions',\n                    host: {\n                        'class': 'mat-card-actions',\n                        '[class.mat-card-actions-align-end]': 'align === \"end\"',\n                    },\n                }]\n        }], propDecorators: { align: [{\n                type: Input\n            }] } });\n/**\n * Footer of a card, needed as it's used as a selector in the API.\n * @docs-private\n */\nclass MatCardFooter {\n}\nMatCardFooter.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardFooter, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardFooter.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatCardFooter, selector: \"mat-card-footer\", host: { classAttribute: \"mat-card-footer\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardFooter, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-card-footer',\n                    host: { 'class': 'mat-card-footer' },\n                }]\n        }] });\n/**\n * Image used in a card, needed to add the mat- CSS styling.\n * @docs-private\n */\nclass MatCardImage {\n}\nMatCardImage.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardImage.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatCardImage, selector: \"[mat-card-image], [matCardImage]\", host: { classAttribute: \"mat-card-image\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-image], [matCardImage]',\n                    host: { 'class': 'mat-card-image' },\n                }]\n        }] });\n/**\n * Image used in a card, needed to add the mat- CSS styling.\n * @docs-private\n */\nclass MatCardSmImage {\n}\nMatCardSmImage.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardSmImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardSmImage.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatCardSmImage, selector: \"[mat-card-sm-image], [matCardImageSmall]\", host: { classAttribute: \"mat-card-sm-image\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardSmImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-sm-image], [matCardImageSmall]',\n                    host: { 'class': 'mat-card-sm-image' },\n                }]\n        }] });\n/**\n * Image used in a card, needed to add the mat- CSS styling.\n * @docs-private\n */\nclass MatCardMdImage {\n}\nMatCardMdImage.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardMdImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardMdImage.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatCardMdImage, selector: \"[mat-card-md-image], [matCardImageMedium]\", host: { classAttribute: \"mat-card-md-image\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardMdImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-md-image], [matCardImageMedium]',\n                    host: { 'class': 'mat-card-md-image' },\n                }]\n        }] });\n/**\n * Image used in a card, needed to add the mat- CSS styling.\n * @docs-private\n */\nclass MatCardLgImage {\n}\nMatCardLgImage.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardLgImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardLgImage.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatCardLgImage, selector: \"[mat-card-lg-image], [matCardImageLarge]\", host: { classAttribute: \"mat-card-lg-image\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardLgImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-lg-image], [matCardImageLarge]',\n                    host: { 'class': 'mat-card-lg-image' },\n                }]\n        }] });\n/**\n * Large image used in a card, needed to add the mat- CSS styling.\n * @docs-private\n */\nclass MatCardXlImage {\n}\nMatCardXlImage.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardXlImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardXlImage.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatCardXlImage, selector: \"[mat-card-xl-image], [matCardImageXLarge]\", host: { classAttribute: \"mat-card-xl-image\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardXlImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-xl-image], [matCardImageXLarge]',\n                    host: { 'class': 'mat-card-xl-image' },\n                }]\n        }] });\n/**\n * Avatar image used in a card, needed to add the mat- CSS styling.\n * @docs-private\n */\nclass MatCardAvatar {\n}\nMatCardAvatar.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardAvatar, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatCardAvatar.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatCardAvatar, selector: \"[mat-card-avatar], [matCardAvatar]\", host: { classAttribute: \"mat-card-avatar\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardAvatar, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-avatar], [matCardAvatar]',\n                    host: { 'class': 'mat-card-avatar' },\n                }]\n        }] });\n/**\n * A basic content container component that adds the styles of a Material design card.\n *\n * While this component can be used alone, it also provides a number\n * of preset styles for common card sections, including:\n * - mat-card-title\n * - mat-card-subtitle\n * - mat-card-content\n * - mat-card-actions\n * - mat-card-footer\n */\nclass MatCard {\n    // @breaking-change 9.0.0 `_animationMode` parameter to be made required.\n    constructor(_animationMode) {\n        this._animationMode = _animationMode;\n    }\n}\nMatCard.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCard, deps: [{ token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatCard.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatCard, selector: \"mat-card\", host: { properties: { \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\" }, classAttribute: \"mat-card mat-focus-indicator\" }, exportAs: [\"matCard\"], ngImport: i0, template: \"<ng-content></ng-content>\\n<ng-content select=\\\"mat-card-footer\\\"></ng-content>\\n\", styles: [\".mat-card{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);display:block;position:relative;padding:16px;border-radius:4px}.mat-card._mat-animation-noopable{transition:none !important;animation:none !important}.mat-card>.mat-divider-horizontal{position:absolute;left:0;width:100%}[dir=rtl] .mat-card>.mat-divider-horizontal{left:auto;right:0}.mat-card>.mat-divider-horizontal.mat-divider-inset{position:static;margin:0}[dir=rtl] .mat-card>.mat-divider-horizontal.mat-divider-inset{margin-right:0}.cdk-high-contrast-active .mat-card{outline:solid 1px}.mat-card-actions,.mat-card-subtitle,.mat-card-content{display:block;margin-bottom:16px}.mat-card-title{display:block;margin-bottom:8px}.mat-card-actions{margin-left:-8px;margin-right:-8px;padding:8px 0}.mat-card-actions-align-end{display:flex;justify-content:flex-end}.mat-card-image{width:calc(100% + 32px);margin:0 -16px 16px -16px;display:block;overflow:hidden}.mat-card-image img{width:100%}.mat-card-footer{display:block;margin:0 -16px -16px -16px}.mat-card-actions .mat-button,.mat-card-actions .mat-raised-button,.mat-card-actions .mat-stroked-button{margin:0 8px}.mat-card-header{display:flex;flex-direction:row}.mat-card-header .mat-card-title{margin-bottom:12px}.mat-card-header-text{margin:0 16px}.mat-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;object-fit:cover}.mat-card-title-group{display:flex;justify-content:space-between}.mat-card-sm-image{width:80px;height:80px}.mat-card-md-image{width:112px;height:112px}.mat-card-lg-image{width:152px;height:152px}.mat-card-xl-image{width:240px;height:240px;margin:-8px}.mat-card-title-group>.mat-card-xl-image{margin:-8px 0 8px}@media(max-width: 599px){.mat-card-title-group{margin:0}.mat-card-xl-image{margin-left:0;margin-right:0}}.mat-card>:first-child,.mat-card-content>:first-child{margin-top:0}.mat-card>:last-child:not(.mat-card-footer),.mat-card-content>:last-child:not(.mat-card-footer){margin-bottom:0}.mat-card-image:first-child{margin-top:-16px;border-top-left-radius:inherit;border-top-right-radius:inherit}.mat-card>.mat-card-actions:last-child{margin-bottom:-8px;padding-bottom:0}.mat-card-actions:not(.mat-card-actions-align-end) .mat-button:first-child,.mat-card-actions:not(.mat-card-actions-align-end) .mat-raised-button:first-child,.mat-card-actions:not(.mat-card-actions-align-end) .mat-stroked-button:first-child{margin-left:0;margin-right:0}.mat-card-actions-align-end .mat-button:last-child,.mat-card-actions-align-end .mat-raised-button:last-child,.mat-card-actions-align-end .mat-stroked-button:last-child{margin-left:0;margin-right:0}.mat-card-title:not(:first-child),.mat-card-subtitle:not(:first-child){margin-top:-4px}.mat-card-header .mat-card-subtitle:not(:first-child){margin-top:-8px}.mat-card>.mat-card-xl-image:first-child{margin-top:-8px}.mat-card>.mat-card-xl-image:last-child{margin-bottom:-8px}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCard, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-card', exportAs: 'matCard', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        'class': 'mat-card mat-focus-indicator',\n                        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                    }, template: \"<ng-content></ng-content>\\n<ng-content select=\\\"mat-card-footer\\\"></ng-content>\\n\", styles: [\".mat-card{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);display:block;position:relative;padding:16px;border-radius:4px}.mat-card._mat-animation-noopable{transition:none !important;animation:none !important}.mat-card>.mat-divider-horizontal{position:absolute;left:0;width:100%}[dir=rtl] .mat-card>.mat-divider-horizontal{left:auto;right:0}.mat-card>.mat-divider-horizontal.mat-divider-inset{position:static;margin:0}[dir=rtl] .mat-card>.mat-divider-horizontal.mat-divider-inset{margin-right:0}.cdk-high-contrast-active .mat-card{outline:solid 1px}.mat-card-actions,.mat-card-subtitle,.mat-card-content{display:block;margin-bottom:16px}.mat-card-title{display:block;margin-bottom:8px}.mat-card-actions{margin-left:-8px;margin-right:-8px;padding:8px 0}.mat-card-actions-align-end{display:flex;justify-content:flex-end}.mat-card-image{width:calc(100% + 32px);margin:0 -16px 16px -16px;display:block;overflow:hidden}.mat-card-image img{width:100%}.mat-card-footer{display:block;margin:0 -16px -16px -16px}.mat-card-actions .mat-button,.mat-card-actions .mat-raised-button,.mat-card-actions .mat-stroked-button{margin:0 8px}.mat-card-header{display:flex;flex-direction:row}.mat-card-header .mat-card-title{margin-bottom:12px}.mat-card-header-text{margin:0 16px}.mat-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;object-fit:cover}.mat-card-title-group{display:flex;justify-content:space-between}.mat-card-sm-image{width:80px;height:80px}.mat-card-md-image{width:112px;height:112px}.mat-card-lg-image{width:152px;height:152px}.mat-card-xl-image{width:240px;height:240px;margin:-8px}.mat-card-title-group>.mat-card-xl-image{margin:-8px 0 8px}@media(max-width: 599px){.mat-card-title-group{margin:0}.mat-card-xl-image{margin-left:0;margin-right:0}}.mat-card>:first-child,.mat-card-content>:first-child{margin-top:0}.mat-card>:last-child:not(.mat-card-footer),.mat-card-content>:last-child:not(.mat-card-footer){margin-bottom:0}.mat-card-image:first-child{margin-top:-16px;border-top-left-radius:inherit;border-top-right-radius:inherit}.mat-card>.mat-card-actions:last-child{margin-bottom:-8px;padding-bottom:0}.mat-card-actions:not(.mat-card-actions-align-end) .mat-button:first-child,.mat-card-actions:not(.mat-card-actions-align-end) .mat-raised-button:first-child,.mat-card-actions:not(.mat-card-actions-align-end) .mat-stroked-button:first-child{margin-left:0;margin-right:0}.mat-card-actions-align-end .mat-button:last-child,.mat-card-actions-align-end .mat-raised-button:last-child,.mat-card-actions-align-end .mat-stroked-button:last-child{margin-left:0;margin-right:0}.mat-card-title:not(:first-child),.mat-card-subtitle:not(:first-child){margin-top:-4px}.mat-card-header .mat-card-subtitle:not(:first-child){margin-top:-8px}.mat-card>.mat-card-xl-image:first-child{margin-top:-8px}.mat-card>.mat-card-xl-image:last-child{margin-bottom:-8px}\"] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; } });\n/**\n * Component intended to be used within the `<mat-card>` component. It adds styles for a\n * preset header section (i.e. a title, subtitle, and avatar layout).\n * @docs-private\n */\nclass MatCardHeader {\n}\nMatCardHeader.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardHeader, deps: [], target: i0.ɵɵFactoryTarget.Component });\nMatCardHeader.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatCardHeader, selector: \"mat-card-header\", host: { classAttribute: \"mat-card-header\" }, ngImport: i0, template: \"<ng-content select=\\\"[mat-card-avatar], [matCardAvatar]\\\"></ng-content>\\n<div class=\\\"mat-card-header-text\\\">\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content></ng-content>\\n\", changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardHeader, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-card-header', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: { 'class': 'mat-card-header' }, template: \"<ng-content select=\\\"[mat-card-avatar], [matCardAvatar]\\\"></ng-content>\\n<div class=\\\"mat-card-header-text\\\">\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content></ng-content>\\n\" }]\n        }] });\n/**\n * Component intended to be used within the `<mat-card>` component. It adds styles for a preset\n * layout that groups an image with a title section.\n * @docs-private\n */\nclass MatCardTitleGroup {\n}\nMatCardTitleGroup.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardTitleGroup, deps: [], target: i0.ɵɵFactoryTarget.Component });\nMatCardTitleGroup.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatCardTitleGroup, selector: \"mat-card-title-group\", host: { classAttribute: \"mat-card-title-group\" }, ngImport: i0, template: \"<div>\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content select=\\\"img\\\"></ng-content>\\n<ng-content></ng-content>\\n\", changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardTitleGroup, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-card-title-group', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: { 'class': 'mat-card-title-group' }, template: \"<div>\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content select=\\\"img\\\"></ng-content>\\n<ng-content></ng-content>\\n\" }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatCardModule {\n}\nMatCardModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatCardModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardModule, declarations: [MatCard,\n        MatCardHeader,\n        MatCardTitleGroup,\n        MatCardContent,\n        MatCardTitle,\n        MatCardSubtitle,\n        MatCardActions,\n        MatCardFooter,\n        MatCardSmImage,\n        MatCardMdImage,\n        MatCardLgImage,\n        MatCardImage,\n        MatCardXlImage,\n        MatCardAvatar], imports: [MatCommonModule], exports: [MatCard,\n        MatCardHeader,\n        MatCardTitleGroup,\n        MatCardContent,\n        MatCardTitle,\n        MatCardSubtitle,\n        MatCardActions,\n        MatCardFooter,\n        MatCardSmImage,\n        MatCardMdImage,\n        MatCardLgImage,\n        MatCardImage,\n        MatCardXlImage,\n        MatCardAvatar,\n        MatCommonModule] });\nMatCardModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardModule, imports: [MatCommonModule, MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatCardModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule],\n                    exports: [\n                        MatCard,\n                        MatCardHeader,\n                        MatCardTitleGroup,\n                        MatCardContent,\n                        MatCardTitle,\n                        MatCardSubtitle,\n                        MatCardActions,\n                        MatCardFooter,\n                        MatCardSmImage,\n                        MatCardMdImage,\n                        MatCardLgImage,\n                        MatCardImage,\n                        MatCardXlImage,\n                        MatCardAvatar,\n                        MatCommonModule,\n                    ],\n                    declarations: [\n                        MatCard,\n                        MatCardHeader,\n                        MatCardTitleGroup,\n                        MatCardContent,\n                        MatCardTitle,\n                        MatCardSubtitle,\n                        MatCardActions,\n                        MatCardFooter,\n                        MatCardSmImage,\n                        MatCardMdImage,\n                        MatCardLgImage,\n                        MatCardImage,\n                        MatCardXlImage,\n                        MatCardAvatar,\n                    ],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardModule, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,SAAT,EAAoBC,KAApB,EAA2BC,SAA3B,EAAsCC,iBAAtC,EAAyDC,uBAAzD,EAAkFC,QAAlF,EAA4FC,MAA5F,EAAoGC,QAApG,QAAoH,eAApH;AACA,SAASC,qBAAT,QAAsC,sCAAtC;AACA,SAASC,eAAT,QAAgC,wBAAhC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;;;;;;;AACA,MAAMC,cAAN,CAAqB;;AAErBA,cAAc,CAACC,IAAf;EAAA,iBAA2GD,cAA3G;AAAA;;AACAA,cAAc,CAACE,IAAf,kBADiGb,EACjG;EAAA,MAA+FW,cAA/F;EAAA;EAAA;AAAA;;AACA;EAAA,mDAFiGX,EAEjG,mBAA2FW,cAA3F,EAAuH,CAAC;IAC5GG,IAAI,EAAEb,SADsG;IAE5Gc,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wDADX;MAECC,IAAI,EAAE;QAAE,SAAS;MAAX;IAFP,CAAD;EAFsG,CAAD,CAAvH;AAAA;AAOA;AACA;AACA;AACA;;;AACA,MAAMC,YAAN,CAAmB;;AAEnBA,YAAY,CAACN,IAAb;EAAA,iBAAyGM,YAAzG;AAAA;;AACAA,YAAY,CAACL,IAAb,kBAhBiGb,EAgBjG;EAAA,MAA6FkB,YAA7F;EAAA;EAAA;AAAA;;AACA;EAAA,mDAjBiGlB,EAiBjG,mBAA2FkB,YAA3F,EAAqH,CAAC;IAC1GJ,IAAI,EAAEb,SADoG;IAE1Gc,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAG,kDADZ;MAECC,IAAI,EAAE;QACF,SAAS;MADP;IAFP,CAAD;EAFoG,CAAD,CAArH;AAAA;AASA;AACA;AACA;AACA;;;AACA,MAAME,eAAN,CAAsB;;AAEtBA,eAAe,CAACP,IAAhB;EAAA,iBAA4GO,eAA5G;AAAA;;AACAA,eAAe,CAACN,IAAhB,kBAjCiGb,EAiCjG;EAAA,MAAgGmB,eAAhG;EAAA;EAAA;AAAA;;AACA;EAAA,mDAlCiGnB,EAkCjG,mBAA2FmB,eAA3F,EAAwH,CAAC;IAC7GL,IAAI,EAAEb,SADuG;IAE7Gc,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAG,2DADZ;MAECC,IAAI,EAAE;QACF,SAAS;MADP;IAFP,CAAD;EAFuG,CAAD,CAAxH;AAAA;AASA;AACA;AACA;AACA;;;AACA,MAAMG,cAAN,CAAqB;EACjBC,WAAW,GAAG;IACV;IACA,KAAKC,KAAL,GAAa,OAAb;EACH;;AAJgB;;AAMrBF,cAAc,CAACR,IAAf;EAAA,iBAA2GQ,cAA3G;AAAA;;AACAA,cAAc,CAACP,IAAf,kBAtDiGb,EAsDjG;EAAA,MAA+FoB,cAA/F;EAAA;EAAA;EAAA;EAAA;IAAA;MAtDiGpB,EAsDjG;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDAvDiGA,EAuDjG,mBAA2FoB,cAA3F,EAAuH,CAAC;IAC5GN,IAAI,EAAEb,SADsG;IAE5Gc,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBADX;MAECO,QAAQ,EAAE,gBAFX;MAGCN,IAAI,EAAE;QACF,SAAS,kBADP;QAEF,sCAAsC;MAFpC;IAHP,CAAD;EAFsG,CAAD,CAAvH,QAU4B;IAAEK,KAAK,EAAE,CAAC;MACtBR,IAAI,EAAEZ;IADgB,CAAD;EAAT,CAV5B;AAAA;AAaA;AACA;AACA;AACA;;;AACA,MAAMsB,aAAN,CAAoB;;AAEpBA,aAAa,CAACZ,IAAd;EAAA,iBAA0GY,aAA1G;AAAA;;AACAA,aAAa,CAACX,IAAd,kBA3EiGb,EA2EjG;EAAA,MAA8FwB,aAA9F;EAAA;EAAA;AAAA;;AACA;EAAA,mDA5EiGxB,EA4EjG,mBAA2FwB,aAA3F,EAAsH,CAAC;IAC3GV,IAAI,EAAEb,SADqG;IAE3Gc,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBADX;MAECC,IAAI,EAAE;QAAE,SAAS;MAAX;IAFP,CAAD;EAFqG,CAAD,CAAtH;AAAA;AAOA;AACA;AACA;AACA;;;AACA,MAAMQ,YAAN,CAAmB;;AAEnBA,YAAY,CAACb,IAAb;EAAA,iBAAyGa,YAAzG;AAAA;;AACAA,YAAY,CAACZ,IAAb,kBA1FiGb,EA0FjG;EAAA,MAA6FyB,YAA7F;EAAA;EAAA;AAAA;;AACA;EAAA,mDA3FiGzB,EA2FjG,mBAA2FyB,YAA3F,EAAqH,CAAC;IAC1GX,IAAI,EAAEb,SADoG;IAE1Gc,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kCADX;MAECC,IAAI,EAAE;QAAE,SAAS;MAAX;IAFP,CAAD;EAFoG,CAAD,CAArH;AAAA;AAOA;AACA;AACA;AACA;;;AACA,MAAMS,cAAN,CAAqB;;AAErBA,cAAc,CAACd,IAAf;EAAA,iBAA2Gc,cAA3G;AAAA;;AACAA,cAAc,CAACb,IAAf,kBAzGiGb,EAyGjG;EAAA,MAA+F0B,cAA/F;EAAA;EAAA;AAAA;;AACA;EAAA,mDA1GiG1B,EA0GjG,mBAA2F0B,cAA3F,EAAuH,CAAC;IAC5GZ,IAAI,EAAEb,SADsG;IAE5Gc,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,0CADX;MAECC,IAAI,EAAE;QAAE,SAAS;MAAX;IAFP,CAAD;EAFsG,CAAD,CAAvH;AAAA;AAOA;AACA;AACA;AACA;;;AACA,MAAMU,cAAN,CAAqB;;AAErBA,cAAc,CAACf,IAAf;EAAA,iBAA2Ge,cAA3G;AAAA;;AACAA,cAAc,CAACd,IAAf,kBAxHiGb,EAwHjG;EAAA,MAA+F2B,cAA/F;EAAA;EAAA;AAAA;;AACA;EAAA,mDAzHiG3B,EAyHjG,mBAA2F2B,cAA3F,EAAuH,CAAC;IAC5Gb,IAAI,EAAEb,SADsG;IAE5Gc,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2CADX;MAECC,IAAI,EAAE;QAAE,SAAS;MAAX;IAFP,CAAD;EAFsG,CAAD,CAAvH;AAAA;AAOA;AACA;AACA;AACA;;;AACA,MAAMW,cAAN,CAAqB;;AAErBA,cAAc,CAAChB,IAAf;EAAA,iBAA2GgB,cAA3G;AAAA;;AACAA,cAAc,CAACf,IAAf,kBAvIiGb,EAuIjG;EAAA,MAA+F4B,cAA/F;EAAA;EAAA;AAAA;;AACA;EAAA,mDAxIiG5B,EAwIjG,mBAA2F4B,cAA3F,EAAuH,CAAC;IAC5Gd,IAAI,EAAEb,SADsG;IAE5Gc,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,0CADX;MAECC,IAAI,EAAE;QAAE,SAAS;MAAX;IAFP,CAAD;EAFsG,CAAD,CAAvH;AAAA;AAOA;AACA;AACA;AACA;;;AACA,MAAMY,cAAN,CAAqB;;AAErBA,cAAc,CAACjB,IAAf;EAAA,iBAA2GiB,cAA3G;AAAA;;AACAA,cAAc,CAAChB,IAAf,kBAtJiGb,EAsJjG;EAAA,MAA+F6B,cAA/F;EAAA;EAAA;AAAA;;AACA;EAAA,mDAvJiG7B,EAuJjG,mBAA2F6B,cAA3F,EAAuH,CAAC;IAC5Gf,IAAI,EAAEb,SADsG;IAE5Gc,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2CADX;MAECC,IAAI,EAAE;QAAE,SAAS;MAAX;IAFP,CAAD;EAFsG,CAAD,CAAvH;AAAA;AAOA;AACA;AACA;AACA;;;AACA,MAAMa,aAAN,CAAoB;;AAEpBA,aAAa,CAAClB,IAAd;EAAA,iBAA0GkB,aAA1G;AAAA;;AACAA,aAAa,CAACjB,IAAd,kBArKiGb,EAqKjG;EAAA,MAA8F8B,aAA9F;EAAA;EAAA;AAAA;;AACA;EAAA,mDAtKiG9B,EAsKjG,mBAA2F8B,aAA3F,EAAsH,CAAC;IAC3GhB,IAAI,EAAEb,SADqG;IAE3Gc,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCADX;MAECC,IAAI,EAAE;QAAE,SAAS;MAAX;IAFP,CAAD;EAFqG,CAAD,CAAtH;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMc,OAAN,CAAc;EACV;EACAV,WAAW,CAACW,cAAD,EAAiB;IACxB,KAAKA,cAAL,GAAsBA,cAAtB;EACH;;AAJS;;AAMdD,OAAO,CAACnB,IAAR;EAAA,iBAAoGmB,OAApG,EA9LiG/B,EA8LjG,mBAA6HS,qBAA7H;AAAA;;AACAsB,OAAO,CAACE,IAAR,kBA/LiGjC,EA+LjG;EAAA,MAAwF+B,OAAxF;EAAA;EAAA;EAAA;EAAA;IAAA;MA/LiG/B,EA+LjG;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA/LiGA,EA+LjG;MA/LiGA,EA+L0N,gBAA3T;MA/LiGA,EA+LqP,mBAAtV;IAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDAhMiGA,EAgMjG,mBAA2F+B,OAA3F,EAAgH,CAAC;IACrGjB,IAAI,EAAEX,SAD+F;IAErGY,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,UAAZ;MAAwBO,QAAQ,EAAE,SAAlC;MAA6CW,aAAa,EAAE9B,iBAAiB,CAAC+B,IAA9E;MAAoFC,eAAe,EAAE/B,uBAAuB,CAACgC,MAA7H;MAAqIpB,IAAI,EAAE;QACtI,SAAS,8BAD6H;QAEtI,mCAAmC;MAFmG,CAA3I;MAGIqB,QAAQ,EAAE,mFAHd;MAGmGC,MAAM,EAAE,CAAC,yzFAAD;IAH3G,CAAD;EAF+F,CAAD,CAAhH,EAM4B,YAAY;IAAE,OAAO,CAAC;MAAEzB,IAAI,EAAE0B,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9D3B,IAAI,EAAER;MADwD,CAAD,EAE9D;QACCQ,IAAI,EAAEP,MADP;QAECQ,IAAI,EAAE,CAACN,qBAAD;MAFP,CAF8D;IAA/B,CAAD,CAAP;EAKlB,CAXxB;AAAA;AAYA;AACA;AACA;AACA;AACA;;;AACA,MAAMiC,aAAN,CAAoB;;AAEpBA,aAAa,CAAC9B,IAAd;EAAA,iBAA0G8B,aAA1G;AAAA;;AACAA,aAAa,CAACT,IAAd,kBApNiGjC,EAoNjG;EAAA,MAA8F0C,aAA9F;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MApNiG1C,EAoNjG;MApNiGA,EAoN+G,gBAAhN;MApNiGA,EAoNwL,4BAAzR;MApNiGA,EAoNgO,mBAAjU;MApNiGA,EAoNuY,eAAxe;MApNiGA,EAoN+Y,mBAAhf;IAAA;EAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDArNiGA,EAqNjG,mBAA2F0C,aAA3F,EAAsH,CAAC;IAC3G5B,IAAI,EAAEX,SADqG;IAE3GY,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,iBAAZ;MAA+BkB,aAAa,EAAE9B,iBAAiB,CAAC+B,IAAhE;MAAsEC,eAAe,EAAE/B,uBAAuB,CAACgC,MAA/G;MAAuHpB,IAAI,EAAE;QAAE,SAAS;MAAX,CAA7H;MAA6JqB,QAAQ,EAAE;IAAvK,CAAD;EAFqG,CAAD,CAAtH;AAAA;AAIA;AACA;AACA;AACA;AACA;;;AACA,MAAMK,iBAAN,CAAwB;;AAExBA,iBAAiB,CAAC/B,IAAlB;EAAA,iBAA8G+B,iBAA9G;AAAA;;AACAA,iBAAiB,CAACV,IAAlB,kBAjOiGjC,EAiOjG;EAAA,MAAkG2C,iBAAlG;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAjOiG3C,EAiOjG;MAjOiGA,EAiOiI,yBAAlO;MAjOiGA,EAiO0I,gBAA3O;MAjOiGA,EAiOiT,eAAlZ;MAjOiGA,EAiOyT,mBAA1Z;MAjOiGA,EAiOmW,mBAApc;IAAA;EAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDAlOiGA,EAkOjG,mBAA2F2C,iBAA3F,EAA0H,CAAC;IAC/G7B,IAAI,EAAEX,SADyG;IAE/GY,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,sBAAZ;MAAoCkB,aAAa,EAAE9B,iBAAiB,CAAC+B,IAArE;MAA2EC,eAAe,EAAE/B,uBAAuB,CAACgC,MAApH;MAA4HpB,IAAI,EAAE;QAAE,SAAS;MAAX,CAAlI;MAAuKqB,QAAQ,EAAE;IAAjL,CAAD;EAFyG,CAAD,CAA1H;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMM,aAAN,CAAoB;;AAEpBA,aAAa,CAAChC,IAAd;EAAA,iBAA0GgC,aAA1G;AAAA;;AACAA,aAAa,CAACC,IAAd,kBAjPiG7C,EAiPjG;EAAA,MAA2G4C,aAA3G;EAAA,eAAyIb,OAAzI,EACQW,aADR,EAEQC,iBAFR,EAGQhC,cAHR,EAIQO,YAJR,EAKQC,eALR,EAMQC,cANR,EAOQI,aAPR,EAQQE,cARR,EASQC,cATR,EAUQC,cAVR,EAWQH,YAXR,EAYQI,cAZR,EAaQC,aAbR;EAAA,UAakCpB,eAblC;EAAA,UAa8DqB,OAb9D,EAcQW,aAdR,EAeQC,iBAfR,EAgBQhC,cAhBR,EAiBQO,YAjBR,EAkBQC,eAlBR,EAmBQC,cAnBR,EAoBQI,aApBR,EAqBQE,cArBR,EAsBQC,cAtBR,EAuBQC,cAvBR,EAwBQH,YAxBR,EAyBQI,cAzBR,EA0BQC,aA1BR,EA2BQpB,eA3BR;AAAA;AA4BAkC,aAAa,CAACE,IAAd,kBA7QiG9C,EA6QjG;EAAA,UAAoIU,eAApI,EAAqJA,eAArJ;AAAA;;AACA;EAAA,mDA9QiGV,EA8QjG,mBAA2F4C,aAA3F,EAAsH,CAAC;IAC3G9B,IAAI,EAAEN,QADqG;IAE3GO,IAAI,EAAE,CAAC;MACCgC,OAAO,EAAE,CAACrC,eAAD,CADV;MAECsC,OAAO,EAAE,CACLjB,OADK,EAELW,aAFK,EAGLC,iBAHK,EAILhC,cAJK,EAKLO,YALK,EAMLC,eANK,EAOLC,cAPK,EAQLI,aARK,EASLE,cATK,EAULC,cAVK,EAWLC,cAXK,EAYLH,YAZK,EAaLI,cAbK,EAcLC,aAdK,EAeLpB,eAfK,CAFV;MAmBCuC,YAAY,EAAE,CACVlB,OADU,EAEVW,aAFU,EAGVC,iBAHU,EAIVhC,cAJU,EAKVO,YALU,EAMVC,eANU,EAOVC,cAPU,EAQVI,aARU,EASVE,cATU,EAUVC,cAVU,EAWVC,cAXU,EAYVH,YAZU,EAaVI,cAbU,EAcVC,aAdU;IAnBf,CAAD;EAFqG,CAAD,CAAtH;AAAA;AAwCA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASC,OAAT,EAAkBX,cAAlB,EAAkCU,aAAlC,EAAiDnB,cAAjD,EAAiEa,aAAjE,EAAgFkB,aAAhF,EAA+FjB,YAA/F,EAA6GG,cAA7G,EAA6HD,cAA7H,EAA6IiB,aAA7I,EAA4JlB,cAA5J,EAA4KP,eAA5K,EAA6LD,YAA7L,EAA2MyB,iBAA3M,EAA8Nd,cAA9N"}, "metadata": {}, "sourceType": "module"}