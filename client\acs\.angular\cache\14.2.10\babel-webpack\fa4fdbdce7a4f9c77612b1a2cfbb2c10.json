{"ast": null, "code": "export const isArray = (() => Array.isArray || (x => x && typeof x.length === 'number'))();", "map": {"version": 3, "names": ["isArray", "Array", "x", "length"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/isArray.js"], "sourcesContent": ["export const isArray = (() => Array.isArray || ((x) => x && typeof x.length === 'number'))();\n"], "mappings": "AAAA,OAAO,MAAMA,OAAO,GAAG,CAAC,MAAMC,KAAK,CAACD,OAAN,KAAmBE,CAAD,IAAOA,CAAC,IAAI,OAAOA,CAAC,CAACC,MAAT,KAAoB,QAAlD,CAAP,GAAhB"}, "metadata": {}, "sourceType": "module"}