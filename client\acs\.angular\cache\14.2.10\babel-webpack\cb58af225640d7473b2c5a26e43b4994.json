{"ast": null, "code": ";\n\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * ANSI X.923 padding strategy.\n   */\n  CryptoJS.pad.AnsiX923 = {\n    pad: function (data, blockSize) {\n      // Shortcuts\n      var dataSigBytes = data.sigBytes;\n      var blockSizeBytes = blockSize * 4; // Count padding bytes\n\n      var nPaddingBytes = blockSizeBytes - dataSigBytes % blockSizeBytes; // Compute last byte position\n\n      var lastBytePos = dataSigBytes + nPaddingBytes - 1; // Pad\n\n      data.clamp();\n      data.words[lastBytePos >>> 2] |= nPaddingBytes << 24 - lastBytePos % 4 * 8;\n      data.sigBytes += nPaddingBytes;\n    },\n    unpad: function (data) {\n      // Get number of padding bytes from last byte\n      var nPaddingBytes = data.words[data.sigBytes - 1 >>> 2] & 0xff; // Remove padding\n\n      data.sigBytes -= nPaddingBytes;\n    }\n  };\n  return CryptoJS.pad.Ansix923;\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "pad", "AnsiX923", "data", "blockSize", "dataSigBytes", "sigBytes", "blockSizeBytes", "nPaddingBytes", "lastBytePos", "clamp", "words", "unpad", "Ansix923"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/crypto-js/pad-ansix923.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./cipher-core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t/**\n\t * ANSI X.923 padding strategy.\n\t */\n\tCryptoJS.pad.AnsiX923 = {\n\t    pad: function (data, blockSize) {\n\t        // Shortcuts\n\t        var dataSigBytes = data.sigBytes;\n\t        var blockSizeBytes = blockSize * 4;\n\n\t        // Count padding bytes\n\t        var nPaddingBytes = blockSizeBytes - dataSigBytes % blockSizeBytes;\n\n\t        // Compute last byte position\n\t        var lastBytePos = dataSigBytes + nPaddingBytes - 1;\n\n\t        // Pad\n\t        data.clamp();\n\t        data.words[lastBytePos >>> 2] |= nPaddingBytes << (24 - (lastBytePos % 4) * 8);\n\t        data.sigBytes += nPaddingBytes;\n\t    },\n\n\t    unpad: function (data) {\n\t        // Get number of padding bytes from last byte\n\t        var nPaddingBytes = data.words[(data.sigBytes - 1) >>> 2] & 0xff;\n\n\t        // Remove padding\n\t        data.sigBytes -= nPaddingBytes;\n\t    }\n\t};\n\n\n\treturn CryptoJS.pad.Ansix923;\n\n}));"], "mappings": "AAAA;;AAAE,WAAUA,IAAV,EAAgBC,OAAhB,EAAyBC,KAAzB,EAAgC;EACjC,IAAI,OAAOC,OAAP,KAAmB,QAAvB,EAAiC;IAChC;IACAC,MAAM,CAACD,OAAP,GAAiBA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAD,CAAR,EAAoBA,OAAO,CAAC,eAAD,CAA3B,CAAlC;EACA,CAHD,MAIK,IAAI,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAA3C,EAAgD;IACpD;IACAD,MAAM,CAAC,CAAC,QAAD,EAAW,eAAX,CAAD,EAA8BL,OAA9B,CAAN;EACA,CAHI,MAIA;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAN,CAAP;EACA;AACD,CAbC,EAaA,IAbA,EAaM,UAAUA,QAAV,EAAoB;EAE3B;AACD;AACA;EACCA,QAAQ,CAACC,GAAT,CAAaC,QAAb,GAAwB;IACpBD,GAAG,EAAE,UAAUE,IAAV,EAAgBC,SAAhB,EAA2B;MAC5B;MACA,IAAIC,YAAY,GAAGF,IAAI,CAACG,QAAxB;MACA,IAAIC,cAAc,GAAGH,SAAS,GAAG,CAAjC,CAH4B,CAK5B;;MACA,IAAII,aAAa,GAAGD,cAAc,GAAGF,YAAY,GAAGE,cAApD,CAN4B,CAQ5B;;MACA,IAAIE,WAAW,GAAGJ,YAAY,GAAGG,aAAf,GAA+B,CAAjD,CAT4B,CAW5B;;MACAL,IAAI,CAACO,KAAL;MACAP,IAAI,CAACQ,KAAL,CAAWF,WAAW,KAAK,CAA3B,KAAiCD,aAAa,IAAK,KAAMC,WAAW,GAAG,CAAf,GAAoB,CAA5E;MACAN,IAAI,CAACG,QAAL,IAAiBE,aAAjB;IACH,CAhBmB;IAkBpBI,KAAK,EAAE,UAAUT,IAAV,EAAgB;MACnB;MACA,IAAIK,aAAa,GAAGL,IAAI,CAACQ,KAAL,CAAYR,IAAI,CAACG,QAAL,GAAgB,CAAjB,KAAwB,CAAnC,IAAwC,IAA5D,CAFmB,CAInB;;MACAH,IAAI,CAACG,QAAL,IAAiBE,aAAjB;IACH;EAxBmB,CAAxB;EA4BA,OAAOR,QAAQ,CAACC,GAAT,CAAaY,QAApB;AAEA,CAhDC,CAAD"}, "metadata": {}, "sourceType": "script"}