{"ast": null, "code": "export const subscribeToArray = array => subscriber => {\n  for (let i = 0, len = array.length; i < len && !subscriber.closed; i++) {\n    subscriber.next(array[i]);\n  }\n\n  subscriber.complete();\n};", "map": {"version": 3, "names": ["subscribeToArray", "array", "subscriber", "i", "len", "length", "closed", "next", "complete"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/subscribeToArray.js"], "sourcesContent": ["export const subscribeToArray = (array) => (subscriber) => {\n    for (let i = 0, len = array.length; i < len && !subscriber.closed; i++) {\n        subscriber.next(array[i]);\n    }\n    subscriber.complete();\n};\n"], "mappings": "AAAA,OAAO,MAAMA,gBAAgB,GAAIC,KAAD,IAAYC,UAAD,IAAgB;EACvD,KAAK,IAAIC,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGH,KAAK,CAACI,MAA5B,EAAoCF,CAAC,GAAGC,GAAJ,IAAW,CAACF,UAAU,CAACI,MAA3D,EAAmEH,CAAC,EAApE,EAAwE;IACpED,UAAU,CAACK,IAAX,CAAgBN,KAAK,CAACE,CAAD,CAArB;EACH;;EACDD,UAAU,CAACM,QAAX;AACH,CALM"}, "metadata": {}, "sourceType": "module"}