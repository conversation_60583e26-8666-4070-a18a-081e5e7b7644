{"ast": null, "code": "import { subscribeToArray } from './subscribeToArray';\nimport { subscribeToPromise } from './subscribeToPromise';\nimport { subscribeToIterable } from './subscribeToIterable';\nimport { subscribeToObservable } from './subscribeToObservable';\nimport { isArrayLike } from './isArrayLike';\nimport { isPromise } from './isPromise';\nimport { isObject } from './isObject';\nimport { iterator as Symbol_iterator } from '../symbol/iterator';\nimport { observable as Symbol_observable } from '../symbol/observable';\nexport const subscribeTo = result => {\n  if (!!result && typeof result[Symbol_observable] === 'function') {\n    return subscribeToObservable(result);\n  } else if (isArrayLike(result)) {\n    return subscribeToArray(result);\n  } else if (isPromise(result)) {\n    return subscribeToPromise(result);\n  } else if (!!result && typeof result[Symbol_iterator] === 'function') {\n    return subscribeToIterable(result);\n  } else {\n    const value = isObject(result) ? 'an invalid object' : `'${result}'`;\n    const msg = `You provided ${value} where a stream was expected.` + ' You can provide an Observable, Promise, Array, or Iterable.';\n    throw new TypeError(msg);\n  }\n};", "map": {"version": 3, "names": ["subscribeToArray", "subscribeToPromise", "subscribeToIterable", "subscribeToObservable", "isArrayLike", "isPromise", "isObject", "iterator", "Symbol_iterator", "observable", "Symbol_observable", "subscribeTo", "result", "value", "msg", "TypeError"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/subscribeTo.js"], "sourcesContent": ["import { subscribeToArray } from './subscribeToArray';\nimport { subscribeToPromise } from './subscribeToPromise';\nimport { subscribeToIterable } from './subscribeToIterable';\nimport { subscribeToObservable } from './subscribeToObservable';\nimport { isArrayLike } from './isArrayLike';\nimport { isPromise } from './isPromise';\nimport { isObject } from './isObject';\nimport { iterator as Symbol_iterator } from '../symbol/iterator';\nimport { observable as Symbol_observable } from '../symbol/observable';\nexport const subscribeTo = (result) => {\n    if (!!result && typeof result[Symbol_observable] === 'function') {\n        return subscribeToObservable(result);\n    }\n    else if (isArrayLike(result)) {\n        return subscribeToArray(result);\n    }\n    else if (isPromise(result)) {\n        return subscribeToPromise(result);\n    }\n    else if (!!result && typeof result[Symbol_iterator] === 'function') {\n        return subscribeToIterable(result);\n    }\n    else {\n        const value = isObject(result) ? 'an invalid object' : `'${result}'`;\n        const msg = `You provided ${value} where a stream was expected.`\n            + ' You can provide an Observable, Promise, Array, or Iterable.';\n        throw new TypeError(msg);\n    }\n};\n"], "mappings": "AAAA,SAASA,gBAAT,QAAiC,oBAAjC;AACA,SAASC,kBAAT,QAAmC,sBAAnC;AACA,SAASC,mBAAT,QAAoC,uBAApC;AACA,SAASC,qBAAT,QAAsC,yBAAtC;AACA,SAASC,WAAT,QAA4B,eAA5B;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,SAASC,QAAT,QAAyB,YAAzB;AACA,SAASC,QAAQ,IAAIC,eAArB,QAA4C,oBAA5C;AACA,SAASC,UAAU,IAAIC,iBAAvB,QAAgD,sBAAhD;AACA,OAAO,MAAMC,WAAW,GAAIC,MAAD,IAAY;EACnC,IAAI,CAAC,CAACA,MAAF,IAAY,OAAOA,MAAM,CAACF,iBAAD,CAAb,KAAqC,UAArD,EAAiE;IAC7D,OAAOP,qBAAqB,CAACS,MAAD,CAA5B;EACH,<PERSON>FD,MAGK,IAAIR,WAAW,CAACQ,MAAD,CAAf,EAAyB;IAC1B,OAAOZ,gBAAgB,CAACY,MAAD,CAAvB;EACH,CAFI,MAGA,IAAIP,SAAS,CAACO,MAAD,CAAb,EAAuB;IACxB,OAAOX,kBAAkB,CAACW,MAAD,CAAzB;EACH,CAFI,MAGA,IAAI,CAAC,CAACA,MAAF,IAAY,OAAOA,MAAM,CAACJ,eAAD,CAAb,KAAmC,UAAnD,EAA+D;IAChE,OAAON,mBAAmB,CAACU,MAAD,CAA1B;EACH,CAFI,MAGA;IACD,MAAMC,KAAK,GAAGP,QAAQ,CAACM,MAAD,CAAR,GAAmB,mBAAnB,GAA0C,IAAGA,MAAO,GAAlE;IACA,MAAME,GAAG,GAAI,gBAAeD,KAAM,+BAAtB,GACN,8DADN;IAEA,MAAM,IAAIE,SAAJ,CAAcD,GAAd,CAAN;EACH;AACJ,CAnBM"}, "metadata": {}, "sourceType": "module"}