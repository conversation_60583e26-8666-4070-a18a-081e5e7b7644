{"ast": null, "code": "import { AsyncSubject } from '../AsyncSubject';\nimport { multicast } from './multicast';\nexport function publishLast() {\n  return source => multicast(new AsyncSubject())(source);\n}", "map": {"version": 3, "names": ["AsyncSubject", "multicast", "publishLast", "source"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/publishLast.js"], "sourcesContent": ["import { AsyncSubject } from '../AsyncSubject';\nimport { multicast } from './multicast';\nexport function publishLast() {\n    return (source) => multicast(new AsyncSubject())(source);\n}\n"], "mappings": "AAAA,SAASA,YAAT,QAA6B,iBAA7B;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,OAAO,SAASC,WAAT,GAAuB;EAC1B,OAAQC,MAAD,IAAYF,SAAS,CAAC,IAAID,YAAJ,EAAD,CAAT,CAA8BG,MAA9B,CAAnB;AACH"}, "metadata": {}, "sourceType": "module"}