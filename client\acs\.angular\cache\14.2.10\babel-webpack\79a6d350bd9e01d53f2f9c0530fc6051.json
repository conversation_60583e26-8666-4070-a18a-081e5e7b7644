{"ast": null, "code": "'use strict';\n/* eslint no-invalid-this: 1 */\n\nvar ERROR_MESSAGE = 'Function.prototype.bind called on incompatible ';\nvar slice = Array.prototype.slice;\nvar toStr = Object.prototype.toString;\nvar funcType = '[object Function]';\n\nmodule.exports = function bind(that) {\n  var target = this;\n\n  if (typeof target !== 'function' || toStr.call(target) !== funcType) {\n    throw new TypeError(ERROR_MESSAGE + target);\n  }\n\n  var args = slice.call(arguments, 1);\n  var bound;\n\n  var binder = function () {\n    if (this instanceof bound) {\n      var result = target.apply(this, args.concat(slice.call(arguments)));\n\n      if (Object(result) === result) {\n        return result;\n      }\n\n      return this;\n    } else {\n      return target.apply(that, args.concat(slice.call(arguments)));\n    }\n  };\n\n  var boundLength = Math.max(0, target.length - args.length);\n  var boundArgs = [];\n\n  for (var i = 0; i < boundLength; i++) {\n    boundArgs.push('$' + i);\n  }\n\n  bound = Function('binder', 'return function (' + boundArgs.join(',') + '){ return binder.apply(this,arguments); }')(binder);\n\n  if (target.prototype) {\n    var Empty = function Empty() {};\n\n    Empty.prototype = target.prototype;\n    bound.prototype = new Empty();\n    Empty.prototype = null;\n  }\n\n  return bound;\n};", "map": {"version": 3, "names": ["ERROR_MESSAGE", "slice", "Array", "prototype", "toStr", "Object", "toString", "funcType", "module", "exports", "bind", "that", "target", "call", "TypeError", "args", "arguments", "bound", "binder", "result", "apply", "concat", "<PERSON><PERSON><PERSON><PERSON>", "Math", "max", "length", "boundArgs", "i", "push", "Function", "join", "Empty"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/function-bind/implementation.js"], "sourcesContent": ["'use strict';\n\n/* eslint no-invalid-this: 1 */\n\nvar ERROR_MESSAGE = 'Function.prototype.bind called on incompatible ';\nvar slice = Array.prototype.slice;\nvar toStr = Object.prototype.toString;\nvar funcType = '[object Function]';\n\nmodule.exports = function bind(that) {\n    var target = this;\n    if (typeof target !== 'function' || toStr.call(target) !== funcType) {\n        throw new TypeError(ERROR_MESSAGE + target);\n    }\n    var args = slice.call(arguments, 1);\n\n    var bound;\n    var binder = function () {\n        if (this instanceof bound) {\n            var result = target.apply(\n                this,\n                args.concat(slice.call(arguments))\n            );\n            if (Object(result) === result) {\n                return result;\n            }\n            return this;\n        } else {\n            return target.apply(\n                that,\n                args.concat(slice.call(arguments))\n            );\n        }\n    };\n\n    var boundLength = Math.max(0, target.length - args.length);\n    var boundArgs = [];\n    for (var i = 0; i < boundLength; i++) {\n        boundArgs.push('$' + i);\n    }\n\n    bound = Function('binder', 'return function (' + boundArgs.join(',') + '){ return binder.apply(this,arguments); }')(binder);\n\n    if (target.prototype) {\n        var Empty = function Empty() {};\n        Empty.prototype = target.prototype;\n        bound.prototype = new Empty();\n        Empty.prototype = null;\n    }\n\n    return bound;\n};\n"], "mappings": "AAAA;AAEA;;AAEA,IAAIA,aAAa,GAAG,iDAApB;AACA,IAAIC,KAAK,GAAGC,KAAK,CAACC,SAAN,CAAgBF,KAA5B;AACA,IAAIG,KAAK,GAAGC,MAAM,CAACF,SAAP,CAAiBG,QAA7B;AACA,IAAIC,QAAQ,GAAG,mBAAf;;AAEAC,MAAM,CAACC,OAAP,GAAiB,SAASC,IAAT,CAAcC,IAAd,EAAoB;EACjC,IAAIC,MAAM,GAAG,IAAb;;EACA,IAAI,OAAOA,MAAP,KAAkB,UAAlB,IAAgCR,KAAK,CAACS,IAAN,CAAWD,MAAX,MAAuBL,QAA3D,EAAqE;IACjE,MAAM,IAAIO,SAAJ,CAAcd,aAAa,GAAGY,MAA9B,CAAN;EACH;;EACD,IAAIG,IAAI,GAAGd,KAAK,CAACY,IAAN,CAAWG,SAAX,EAAsB,CAAtB,CAAX;EAEA,IAAIC,KAAJ;;EACA,IAAIC,MAAM,GAAG,YAAY;IACrB,IAAI,gBAAgBD,KAApB,EAA2B;MACvB,IAAIE,MAAM,GAAGP,MAAM,CAACQ,KAAP,CACT,IADS,EAETL,IAAI,CAACM,MAAL,CAAYpB,KAAK,CAACY,IAAN,CAAWG,SAAX,CAAZ,CAFS,CAAb;;MAIA,IAAIX,MAAM,CAACc,MAAD,CAAN,KAAmBA,MAAvB,EAA+B;QAC3B,OAAOA,MAAP;MACH;;MACD,OAAO,IAAP;IACH,CATD,MASO;MACH,OAAOP,MAAM,CAACQ,KAAP,CACHT,IADG,EAEHI,IAAI,CAACM,MAAL,CAAYpB,KAAK,CAACY,IAAN,CAAWG,SAAX,CAAZ,CAFG,CAAP;IAIH;EACJ,CAhBD;;EAkBA,IAAIM,WAAW,GAAGC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYZ,MAAM,CAACa,MAAP,GAAgBV,IAAI,CAACU,MAAjC,CAAlB;EACA,IAAIC,SAAS,GAAG,EAAhB;;EACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,WAApB,EAAiCK,CAAC,EAAlC,EAAsC;IAClCD,SAAS,CAACE,IAAV,CAAe,MAAMD,CAArB;EACH;;EAEDV,KAAK,GAAGY,QAAQ,CAAC,QAAD,EAAW,sBAAsBH,SAAS,CAACI,IAAV,CAAe,GAAf,CAAtB,GAA4C,2CAAvD,CAAR,CAA4GZ,MAA5G,CAAR;;EAEA,IAAIN,MAAM,CAACT,SAAX,EAAsB;IAClB,IAAI4B,KAAK,GAAG,SAASA,KAAT,GAAiB,CAAE,CAA/B;;IACAA,KAAK,CAAC5B,SAAN,GAAkBS,MAAM,CAACT,SAAzB;IACAc,KAAK,CAACd,SAAN,GAAkB,IAAI4B,KAAJ,EAAlB;IACAA,KAAK,CAAC5B,SAAN,GAAkB,IAAlB;EACH;;EAED,OAAOc,KAAP;AACH,CA1CD"}, "metadata": {}, "sourceType": "script"}