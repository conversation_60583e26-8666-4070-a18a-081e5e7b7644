{"ast": null, "code": "/**\n * @license Angular v14.3.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\nimport { CompilerConfig, ResourceLoader } from '@angular/compiler';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, PACKAGE_ROOT_URL, Compiler, ViewEncapsulation, MissingTranslationStrategy, Injector, isDevMode, createPlatformFactory, platformCore, COMPILER_OPTIONS, CompilerFactory, Injectable, PLATFORM_ID, ɵglobal, Version } from '@angular/core';\nimport { ɵPLATFORM_BROWSER_ID } from '@angular/common';\nimport { ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS } from '@angular/platform-browser';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nconst ERROR_COLLECTOR_TOKEN = new InjectionToken('ErrorCollector');\n/**\n * A default provider for {@link PACKAGE_ROOT_URL} that maps to '/'.\n */\n\nconst DEFAULT_PACKAGE_URL_PROVIDER = {\n  provide: PACKAGE_ROOT_URL,\n  useValue: '/'\n};\nconst COMPILER_PROVIDERS = [{\n  provide: Compiler,\n  useFactory: () => new Compiler()\n}];\n/**\n * @publicApi\n *\n * @deprecated\n * Ivy JIT mode doesn't require accessing this symbol.\n * See [JIT API changes due to ViewEngine deprecation](guide/deprecations#jit-api-changes) for\n * additional context.\n */\n\nclass JitCompilerFactory {\n  /* @internal */\n  constructor(defaultOptions) {\n    const compilerOptions = {\n      useJit: true,\n      defaultEncapsulation: ViewEncapsulation.Emulated,\n      missingTranslation: MissingTranslationStrategy.Warning\n    };\n    this._defaultOptions = [compilerOptions, ...defaultOptions];\n  }\n\n  createCompiler(options = []) {\n    const opts = _mergeOptions(this._defaultOptions.concat(options));\n\n    const injector = Injector.create([COMPILER_PROVIDERS, {\n      provide: CompilerConfig,\n      useFactory: () => {\n        return new CompilerConfig({\n          // let explicit values from the compiler options overwrite options\n          // from the app providers\n          useJit: opts.useJit,\n          jitDevMode: isDevMode(),\n          // let explicit values from the compiler options overwrite options\n          // from the app providers\n          defaultEncapsulation: opts.defaultEncapsulation,\n          missingTranslation: opts.missingTranslation,\n          preserveWhitespaces: opts.preserveWhitespaces\n        });\n      },\n      deps: []\n    }, opts.providers]);\n    return injector.get(Compiler);\n  }\n\n}\n\nfunction _mergeOptions(optionsArr) {\n  return {\n    useJit: _lastDefined(optionsArr.map(options => options.useJit)),\n    defaultEncapsulation: _lastDefined(optionsArr.map(options => options.defaultEncapsulation)),\n    providers: _mergeArrays(optionsArr.map(options => options.providers)),\n    missingTranslation: _lastDefined(optionsArr.map(options => options.missingTranslation)),\n    preserveWhitespaces: _lastDefined(optionsArr.map(options => options.preserveWhitespaces))\n  };\n}\n\nfunction _lastDefined(args) {\n  for (let i = args.length - 1; i >= 0; i--) {\n    if (args[i] !== undefined) {\n      return args[i];\n    }\n  }\n\n  return undefined;\n}\n\nfunction _mergeArrays(parts) {\n  const result = [];\n  parts.forEach(part => part && result.push(...part));\n  return result;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A platform that included corePlatform and the compiler.\n *\n * @publicApi\n */\n\n\nconst platformCoreDynamic = createPlatformFactory(platformCore, 'coreDynamic', [{\n  provide: COMPILER_OPTIONS,\n  useValue: {},\n  multi: true\n}, {\n  provide: CompilerFactory,\n  useClass: JitCompilerFactory,\n  deps: [COMPILER_OPTIONS]\n}]);\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nclass ResourceLoaderImpl extends ResourceLoader {\n  get(url) {\n    let resolve;\n    let reject;\n    const promise = new Promise((res, rej) => {\n      resolve = res;\n      reject = rej;\n    });\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', url, true);\n    xhr.responseType = 'text';\n\n    xhr.onload = function () {\n      // responseText is the old-school way of retrieving response (supported by IE8 & 9)\n      // response/responseType properties were introduced in ResourceLoader Level2 spec (supported\n      // by IE10)\n      const response = xhr.response || xhr.responseText; // normalize IE9 bug (https://bugs.jquery.com/ticket/1450)\n\n      let status = xhr.status === 1223 ? 204 : xhr.status; // fix status code when it is 0 (0 status is undocumented).\n      // Occurs when accessing file resources or on Android 4.1 stock browser\n      // while retrieving files from application cache.\n\n      if (status === 0) {\n        status = response ? 200 : 0;\n      }\n\n      if (200 <= status && status <= 300) {\n        resolve(response);\n      } else {\n        reject(`Failed to load ${url}`);\n      }\n    };\n\n    xhr.onerror = function () {\n      reject(`Failed to load ${url}`);\n    };\n\n    xhr.send();\n    return promise;\n  }\n\n}\n\nResourceLoaderImpl.ɵfac = /* @__PURE__ */function () {\n  let ɵResourceLoaderImpl_BaseFactory;\n  return function ResourceLoaderImpl_Factory(t) {\n    return (ɵResourceLoaderImpl_BaseFactory || (ɵResourceLoaderImpl_BaseFactory = i0.ɵɵgetInheritedFactory(ResourceLoaderImpl)))(t || ResourceLoaderImpl);\n  };\n}();\n\nResourceLoaderImpl.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ResourceLoaderImpl,\n  factory: ResourceLoaderImpl.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ResourceLoaderImpl, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @publicApi\n */\n\n\nconst INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS = [ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, {\n  provide: COMPILER_OPTIONS,\n  useValue: {\n    providers: [{\n      provide: ResourceLoader,\n      useClass: ResourceLoaderImpl,\n      deps: []\n    }]\n  },\n  multi: true\n}, {\n  provide: PLATFORM_ID,\n  useValue: ɵPLATFORM_BROWSER_ID\n}];\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * An implementation of ResourceLoader that uses a template cache to avoid doing an actual\n * ResourceLoader.\n *\n * The template cache needs to be built and loaded into window.$templateCache\n * via a separate mechanism.\n *\n * @publicApi\n *\n * @deprecated This was previously necessary in some cases to test AOT-compiled components with View\n *     Engine, but is no longer since Ivy.\n */\n\nclass CachedResourceLoader extends ResourceLoader {\n  constructor() {\n    super();\n    this._cache = ɵglobal.$templateCache;\n\n    if (this._cache == null) {\n      throw new Error('CachedResourceLoader: Template cache was not found in $templateCache.');\n    }\n  }\n\n  get(url) {\n    if (this._cache.hasOwnProperty(url)) {\n      return Promise.resolve(this._cache[url]);\n    } else {\n      return Promise.reject('CachedResourceLoader: Did not find cached template for ' + url);\n    }\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @publicApi\n */\n\n\nconst VERSION = new Version('14.3.0');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @publicApi\n *\n * @deprecated This was previously necessary in some cases to test AOT-compiled components with View\n *     Engine, but is no longer since Ivy.\n\n */\n\nconst RESOURCE_CACHE_PROVIDER = [{\n  provide: ResourceLoader,\n  useClass: CachedResourceLoader,\n  deps: []\n}];\n/**\n * @publicApi\n */\n\nconst platformBrowserDynamic = createPlatformFactory(platformCoreDynamic, 'browserDynamic', INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { JitCompilerFactory, RESOURCE_CACHE_PROVIDER, VERSION, platformBrowserDynamic, INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS, platformCoreDynamic as ɵplatformCoreDynamic };", "map": {"version": 3, "names": ["CompilerConfig", "Resource<PERSON><PERSON>der", "i0", "InjectionToken", "PACKAGE_ROOT_URL", "Compiler", "ViewEncapsulation", "MissingTranslationStrategy", "Injector", "isDevMode", "createPlatformFactory", "platformCore", "COMPILER_OPTIONS", "CompilerFactory", "Injectable", "PLATFORM_ID", "ɵglobal", "Version", "ɵPLATFORM_BROWSER_ID", "ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS", "ERROR_COLLECTOR_TOKEN", "DEFAULT_PACKAGE_URL_PROVIDER", "provide", "useValue", "COMPILER_PROVIDERS", "useFactory", "JitCompilerFactory", "constructor", "defaultOptions", "compilerOptions", "useJit", "defaultEncapsulation", "Emulated", "missingTranslation", "Warning", "_defaultOptions", "createCompiler", "options", "opts", "_mergeOptions", "concat", "injector", "create", "jit<PERSON>ev<PERSON><PERSON>", "preserveWhitespaces", "deps", "providers", "get", "optionsArr", "_lastDefined", "map", "_mergeArrays", "args", "i", "length", "undefined", "parts", "result", "for<PERSON>ach", "part", "push", "platformCoreDynamic", "multi", "useClass", "ResourceLoaderImpl", "url", "resolve", "reject", "promise", "Promise", "res", "rej", "xhr", "XMLHttpRequest", "open", "responseType", "onload", "response", "responseText", "status", "onerror", "send", "ɵfac", "ɵprov", "type", "INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS", "CachedResourceLoader", "_cache", "$templateCache", "Error", "hasOwnProperty", "VERSION", "RESOURCE_CACHE_PROVIDER", "platformBrowserDynamic", "ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS", "ɵplatformCoreDynamic"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/platform-browser-dynamic/fesm2020/platform-browser-dynamic.mjs"], "sourcesContent": ["/**\n * @license Angular v14.3.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { CompilerConfig, ResourceLoader } from '@angular/compiler';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, PACKAGE_ROOT_URL, Compiler, ViewEncapsulation, MissingTranslationStrategy, Injector, isDevMode, createPlatformFactory, platformCore, COMPILER_OPTIONS, CompilerFactory, Injectable, PLATFORM_ID, ɵglobal, Version } from '@angular/core';\nimport { ɵPLATFORM_BROWSER_ID } from '@angular/common';\nimport { ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS } from '@angular/platform-browser';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst ERROR_COLLECTOR_TOKEN = new InjectionToken('ErrorCollector');\n/**\n * A default provider for {@link PACKAGE_ROOT_URL} that maps to '/'.\n */\nconst DEFAULT_PACKAGE_URL_PROVIDER = {\n    provide: PACKAGE_ROOT_URL,\n    useValue: '/'\n};\nconst COMPILER_PROVIDERS = [{ provide: Compiler, useFactory: () => new Compiler() }];\n/**\n * @publicApi\n *\n * @deprecated\n * Ivy JIT mode doesn't require accessing this symbol.\n * See [JIT API changes due to ViewEngine deprecation](guide/deprecations#jit-api-changes) for\n * additional context.\n */\nclass JitCompilerFactory {\n    /* @internal */\n    constructor(defaultOptions) {\n        const compilerOptions = {\n            useJit: true,\n            defaultEncapsulation: ViewEncapsulation.Emulated,\n            missingTranslation: MissingTranslationStrategy.Warning,\n        };\n        this._defaultOptions = [compilerOptions, ...defaultOptions];\n    }\n    createCompiler(options = []) {\n        const opts = _mergeOptions(this._defaultOptions.concat(options));\n        const injector = Injector.create([\n            COMPILER_PROVIDERS, {\n                provide: CompilerConfig,\n                useFactory: () => {\n                    return new CompilerConfig({\n                        // let explicit values from the compiler options overwrite options\n                        // from the app providers\n                        useJit: opts.useJit,\n                        jitDevMode: isDevMode(),\n                        // let explicit values from the compiler options overwrite options\n                        // from the app providers\n                        defaultEncapsulation: opts.defaultEncapsulation,\n                        missingTranslation: opts.missingTranslation,\n                        preserveWhitespaces: opts.preserveWhitespaces,\n                    });\n                },\n                deps: []\n            },\n            opts.providers\n        ]);\n        return injector.get(Compiler);\n    }\n}\nfunction _mergeOptions(optionsArr) {\n    return {\n        useJit: _lastDefined(optionsArr.map(options => options.useJit)),\n        defaultEncapsulation: _lastDefined(optionsArr.map(options => options.defaultEncapsulation)),\n        providers: _mergeArrays(optionsArr.map(options => options.providers)),\n        missingTranslation: _lastDefined(optionsArr.map(options => options.missingTranslation)),\n        preserveWhitespaces: _lastDefined(optionsArr.map(options => options.preserveWhitespaces)),\n    };\n}\nfunction _lastDefined(args) {\n    for (let i = args.length - 1; i >= 0; i--) {\n        if (args[i] !== undefined) {\n            return args[i];\n        }\n    }\n    return undefined;\n}\nfunction _mergeArrays(parts) {\n    const result = [];\n    parts.forEach((part) => part && result.push(...part));\n    return result;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * A platform that included corePlatform and the compiler.\n *\n * @publicApi\n */\nconst platformCoreDynamic = createPlatformFactory(platformCore, 'coreDynamic', [\n    { provide: COMPILER_OPTIONS, useValue: {}, multi: true },\n    { provide: CompilerFactory, useClass: JitCompilerFactory, deps: [COMPILER_OPTIONS] },\n]);\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass ResourceLoaderImpl extends ResourceLoader {\n    get(url) {\n        let resolve;\n        let reject;\n        const promise = new Promise((res, rej) => {\n            resolve = res;\n            reject = rej;\n        });\n        const xhr = new XMLHttpRequest();\n        xhr.open('GET', url, true);\n        xhr.responseType = 'text';\n        xhr.onload = function () {\n            // responseText is the old-school way of retrieving response (supported by IE8 & 9)\n            // response/responseType properties were introduced in ResourceLoader Level2 spec (supported\n            // by IE10)\n            const response = xhr.response || xhr.responseText;\n            // normalize IE9 bug (https://bugs.jquery.com/ticket/1450)\n            let status = xhr.status === 1223 ? 204 : xhr.status;\n            // fix status code when it is 0 (0 status is undocumented).\n            // Occurs when accessing file resources or on Android 4.1 stock browser\n            // while retrieving files from application cache.\n            if (status === 0) {\n                status = response ? 200 : 0;\n            }\n            if (200 <= status && status <= 300) {\n                resolve(response);\n            }\n            else {\n                reject(`Failed to load ${url}`);\n            }\n        };\n        xhr.onerror = function () {\n            reject(`Failed to load ${url}`);\n        };\n        xhr.send();\n        return promise;\n    }\n}\nResourceLoaderImpl.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: ResourceLoaderImpl, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\nResourceLoaderImpl.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: ResourceLoaderImpl });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: ResourceLoaderImpl, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @publicApi\n */\nconst INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS = [\n    ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS,\n    {\n        provide: COMPILER_OPTIONS,\n        useValue: { providers: [{ provide: ResourceLoader, useClass: ResourceLoaderImpl, deps: [] }] },\n        multi: true\n    },\n    { provide: PLATFORM_ID, useValue: ɵPLATFORM_BROWSER_ID },\n];\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * An implementation of ResourceLoader that uses a template cache to avoid doing an actual\n * ResourceLoader.\n *\n * The template cache needs to be built and loaded into window.$templateCache\n * via a separate mechanism.\n *\n * @publicApi\n *\n * @deprecated This was previously necessary in some cases to test AOT-compiled components with View\n *     Engine, but is no longer since Ivy.\n */\nclass CachedResourceLoader extends ResourceLoader {\n    constructor() {\n        super();\n        this._cache = ɵglobal.$templateCache;\n        if (this._cache == null) {\n            throw new Error('CachedResourceLoader: Template cache was not found in $templateCache.');\n        }\n    }\n    get(url) {\n        if (this._cache.hasOwnProperty(url)) {\n            return Promise.resolve(this._cache[url]);\n        }\n        else {\n            return Promise.reject('CachedResourceLoader: Did not find cached template for ' + url);\n        }\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('14.3.0');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @publicApi\n *\n * @deprecated This was previously necessary in some cases to test AOT-compiled components with View\n *     Engine, but is no longer since Ivy.\n\n */\nconst RESOURCE_CACHE_PROVIDER = [{ provide: ResourceLoader, useClass: CachedResourceLoader, deps: [] }];\n/**\n * @publicApi\n */\nconst platformBrowserDynamic = createPlatformFactory(platformCoreDynamic, 'browserDynamic', INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { JitCompilerFactory, RESOURCE_CACHE_PROVIDER, VERSION, platformBrowserDynamic, INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS, platformCoreDynamic as ɵplatformCoreDynamic };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AAEA,SAASA,cAAT,EAAyBC,cAAzB,QAA+C,mBAA/C;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,gBAAzB,EAA2CC,QAA3C,EAAqDC,iBAArD,EAAwEC,0BAAxE,EAAoGC,QAApG,EAA8GC,SAA9G,EAAyHC,qBAAzH,EAAgJC,YAAhJ,EAA8JC,gBAA9J,EAAgLC,eAAhL,EAAiMC,UAAjM,EAA6MC,WAA7M,EAA0NC,OAA1N,EAAmOC,OAAnO,QAAkP,eAAlP;AACA,SAASC,oBAAT,QAAqC,iBAArC;AACA,SAASC,oCAAT,QAAqD,2BAArD;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,qBAAqB,GAAG,IAAIjB,cAAJ,CAAmB,gBAAnB,CAA9B;AACA;AACA;AACA;;AACA,MAAMkB,4BAA4B,GAAG;EACjCC,OAAO,EAAElB,gBADwB;EAEjCmB,QAAQ,EAAE;AAFuB,CAArC;AAIA,MAAMC,kBAAkB,GAAG,CAAC;EAAEF,OAAO,EAAEjB,QAAX;EAAqBoB,UAAU,EAAE,MAAM,IAAIpB,QAAJ;AAAvC,CAAD,CAA3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMqB,kBAAN,CAAyB;EACrB;EACAC,WAAW,CAACC,cAAD,EAAiB;IACxB,MAAMC,eAAe,GAAG;MACpBC,MAAM,EAAE,IADY;MAEpBC,oBAAoB,EAAEzB,iBAAiB,CAAC0B,QAFpB;MAGpBC,kBAAkB,EAAE1B,0BAA0B,CAAC2B;IAH3B,CAAxB;IAKA,KAAKC,eAAL,GAAuB,CAACN,eAAD,EAAkB,GAAGD,cAArB,CAAvB;EACH;;EACDQ,cAAc,CAACC,OAAO,GAAG,EAAX,EAAe;IACzB,MAAMC,IAAI,GAAGC,aAAa,CAAC,KAAKJ,eAAL,CAAqBK,MAArB,CAA4BH,OAA5B,CAAD,CAA1B;;IACA,MAAMI,QAAQ,GAAGjC,QAAQ,CAACkC,MAAT,CAAgB,CAC7BlB,kBAD6B,EACT;MAChBF,OAAO,EAAEtB,cADO;MAEhByB,UAAU,EAAE,MAAM;QACd,OAAO,IAAIzB,cAAJ,CAAmB;UACtB;UACA;UACA8B,MAAM,EAAEQ,IAAI,CAACR,MAHS;UAItBa,UAAU,EAAElC,SAAS,EAJC;UAKtB;UACA;UACAsB,oBAAoB,EAAEO,IAAI,CAACP,oBAPL;UAQtBE,kBAAkB,EAAEK,IAAI,CAACL,kBARH;UAStBW,mBAAmB,EAAEN,IAAI,CAACM;QATJ,CAAnB,CAAP;MAWH,CAde;MAehBC,IAAI,EAAE;IAfU,CADS,EAkB7BP,IAAI,CAACQ,SAlBwB,CAAhB,CAAjB;IAoBA,OAAOL,QAAQ,CAACM,GAAT,CAAa1C,QAAb,CAAP;EACH;;AAjCoB;;AAmCzB,SAASkC,aAAT,CAAuBS,UAAvB,EAAmC;EAC/B,OAAO;IACHlB,MAAM,EAAEmB,YAAY,CAACD,UAAU,CAACE,GAAX,CAAeb,OAAO,IAAIA,OAAO,CAACP,MAAlC,CAAD,CADjB;IAEHC,oBAAoB,EAAEkB,YAAY,CAACD,UAAU,CAACE,GAAX,CAAeb,OAAO,IAAIA,OAAO,CAACN,oBAAlC,CAAD,CAF/B;IAGHe,SAAS,EAAEK,YAAY,CAACH,UAAU,CAACE,GAAX,CAAeb,OAAO,IAAIA,OAAO,CAACS,SAAlC,CAAD,CAHpB;IAIHb,kBAAkB,EAAEgB,YAAY,CAACD,UAAU,CAACE,GAAX,CAAeb,OAAO,IAAIA,OAAO,CAACJ,kBAAlC,CAAD,CAJ7B;IAKHW,mBAAmB,EAAEK,YAAY,CAACD,UAAU,CAACE,GAAX,CAAeb,OAAO,IAAIA,OAAO,CAACO,mBAAlC,CAAD;EAL9B,CAAP;AAOH;;AACD,SAASK,YAAT,CAAsBG,IAAtB,EAA4B;EACxB,KAAK,IAAIC,CAAC,GAAGD,IAAI,CAACE,MAAL,GAAc,CAA3B,EAA8BD,CAAC,IAAI,CAAnC,EAAsCA,CAAC,EAAvC,EAA2C;IACvC,IAAID,IAAI,CAACC,CAAD,CAAJ,KAAYE,SAAhB,EAA2B;MACvB,OAAOH,IAAI,CAACC,CAAD,CAAX;IACH;EACJ;;EACD,OAAOE,SAAP;AACH;;AACD,SAASJ,YAAT,CAAsBK,KAAtB,EAA6B;EACzB,MAAMC,MAAM,GAAG,EAAf;EACAD,KAAK,CAACE,OAAN,CAAeC,IAAD,IAAUA,IAAI,IAAIF,MAAM,CAACG,IAAP,CAAY,GAAGD,IAAf,CAAhC;EACA,OAAOF,MAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMI,mBAAmB,GAAGnD,qBAAqB,CAACC,YAAD,EAAe,aAAf,EAA8B,CAC3E;EAAEW,OAAO,EAAEV,gBAAX;EAA6BW,QAAQ,EAAE,EAAvC;EAA2CuC,KAAK,EAAE;AAAlD,CAD2E,EAE3E;EAAExC,OAAO,EAAET,eAAX;EAA4BkD,QAAQ,EAAErC,kBAAtC;EAA0DmB,IAAI,EAAE,CAACjC,gBAAD;AAAhE,CAF2E,CAA9B,CAAjD;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMoD,kBAAN,SAAiC/D,cAAjC,CAAgD;EAC5C8C,GAAG,CAACkB,GAAD,EAAM;IACL,IAAIC,OAAJ;IACA,IAAIC,MAAJ;IACA,MAAMC,OAAO,GAAG,IAAIC,OAAJ,CAAY,CAACC,GAAD,EAAMC,GAAN,KAAc;MACtCL,OAAO,GAAGI,GAAV;MACAH,MAAM,GAAGI,GAAT;IACH,CAHe,CAAhB;IAIA,MAAMC,GAAG,GAAG,IAAIC,cAAJ,EAAZ;IACAD,GAAG,CAACE,IAAJ,CAAS,KAAT,EAAgBT,GAAhB,EAAqB,IAArB;IACAO,GAAG,CAACG,YAAJ,GAAmB,MAAnB;;IACAH,GAAG,CAACI,MAAJ,GAAa,YAAY;MACrB;MACA;MACA;MACA,MAAMC,QAAQ,GAAGL,GAAG,CAACK,QAAJ,IAAgBL,GAAG,CAACM,YAArC,CAJqB,CAKrB;;MACA,IAAIC,MAAM,GAAGP,GAAG,CAACO,MAAJ,KAAe,IAAf,GAAsB,GAAtB,GAA4BP,GAAG,CAACO,MAA7C,CANqB,CAOrB;MACA;MACA;;MACA,IAAIA,MAAM,KAAK,CAAf,EAAkB;QACdA,MAAM,GAAGF,QAAQ,GAAG,GAAH,GAAS,CAA1B;MACH;;MACD,IAAI,OAAOE,MAAP,IAAiBA,MAAM,IAAI,GAA/B,EAAoC;QAChCb,OAAO,CAACW,QAAD,CAAP;MACH,CAFD,MAGK;QACDV,MAAM,CAAE,kBAAiBF,GAAI,EAAvB,CAAN;MACH;IACJ,CAnBD;;IAoBAO,GAAG,CAACQ,OAAJ,GAAc,YAAY;MACtBb,MAAM,CAAE,kBAAiBF,GAAI,EAAvB,CAAN;IACH,CAFD;;IAGAO,GAAG,CAACS,IAAJ;IACA,OAAOb,OAAP;EACH;;AApC2C;;AAsChDJ,kBAAkB,CAACkB,IAAnB;EAAA;EAAA;IAAA,8EAAqGhF,EAArG,uBAA+G8D,kBAA/G,SAA+GA,kBAA/G;EAAA;AAAA;;AACAA,kBAAkB,CAACmB,KAAnB,kBADqGjF,EACrG;EAAA,OAAmH8D,kBAAnH;EAAA,SAAmHA,kBAAnH;AAAA;;AACA;EAAA,mDAFqG9D,EAErG,mBAA2F8D,kBAA3F,EAA2H,CAAC;IAChHoB,IAAI,EAAEtE;EAD0G,CAAD,CAA3H;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMuE,2CAA2C,GAAG,CAChDlE,oCADgD,EAEhD;EACIG,OAAO,EAAEV,gBADb;EAEIW,QAAQ,EAAE;IAAEuB,SAAS,EAAE,CAAC;MAAExB,OAAO,EAAErB,cAAX;MAA2B8D,QAAQ,EAAEC,kBAArC;MAAyDnB,IAAI,EAAE;IAA/D,CAAD;EAAb,CAFd;EAGIiB,KAAK,EAAE;AAHX,CAFgD,EAOhD;EAAExC,OAAO,EAAEP,WAAX;EAAwBQ,QAAQ,EAAEL;AAAlC,CAPgD,CAApD;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMoE,oBAAN,SAAmCrF,cAAnC,CAAkD;EAC9C0B,WAAW,GAAG;IACV;IACA,KAAK4D,MAAL,GAAcvE,OAAO,CAACwE,cAAtB;;IACA,IAAI,KAAKD,MAAL,IAAe,IAAnB,EAAyB;MACrB,MAAM,IAAIE,KAAJ,CAAU,uEAAV,CAAN;IACH;EACJ;;EACD1C,GAAG,CAACkB,GAAD,EAAM;IACL,IAAI,KAAKsB,MAAL,CAAYG,cAAZ,CAA2BzB,GAA3B,CAAJ,EAAqC;MACjC,OAAOI,OAAO,CAACH,OAAR,CAAgB,KAAKqB,MAAL,CAAYtB,GAAZ,CAAhB,CAAP;IACH,CAFD,MAGK;MACD,OAAOI,OAAO,CAACF,MAAR,CAAe,4DAA4DF,GAA3E,CAAP;IACH;EACJ;;AAf6C;AAkBlD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAM0B,OAAO,GAAG,IAAI1E,OAAJ,CAAY,QAAZ,CAAhB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAM2E,uBAAuB,GAAG,CAAC;EAAEtE,OAAO,EAAErB,cAAX;EAA2B8D,QAAQ,EAAEuB,oBAArC;EAA2DzC,IAAI,EAAE;AAAjE,CAAD,CAAhC;AACA;AACA;AACA;;AACA,MAAMgD,sBAAsB,GAAGnF,qBAAqB,CAACmD,mBAAD,EAAsB,gBAAtB,EAAwCwB,2CAAxC,CAApD;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAS3D,kBAAT,EAA6BkE,uBAA7B,EAAsDD,OAAtD,EAA+DE,sBAA/D,EAAuFR,2CAA2C,IAAIS,4CAAtI,EAAoLjC,mBAAmB,IAAIkC,oBAA3M"}, "metadata": {}, "sourceType": "module"}