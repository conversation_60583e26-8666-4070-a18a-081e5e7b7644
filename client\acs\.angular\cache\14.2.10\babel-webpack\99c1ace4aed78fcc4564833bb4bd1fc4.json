{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { scan } from './scan';\nimport { defer } from '../observable/defer';\nimport { map } from './map';\nexport function timeInterval(scheduler = async) {\n  return source => defer(() => {\n    return source.pipe(scan(({\n      current\n    }, value) => ({\n      value,\n      current: scheduler.now(),\n      last: current\n    }), {\n      current: scheduler.now(),\n      value: undefined,\n      last: undefined\n    }), map(({\n      current,\n      last,\n      value\n    }) => new TimeInterval(value, current - last)));\n  });\n}\nexport class TimeInterval {\n  constructor(value, interval) {\n    this.value = value;\n    this.interval = interval;\n  }\n\n}", "map": {"version": 3, "names": ["async", "scan", "defer", "map", "timeInterval", "scheduler", "source", "pipe", "current", "value", "now", "last", "undefined", "TimeInterval", "constructor", "interval"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/timeInterval.js"], "sourcesContent": ["import { async } from '../scheduler/async';\nimport { scan } from './scan';\nimport { defer } from '../observable/defer';\nimport { map } from './map';\nexport function timeInterval(scheduler = async) {\n    return (source) => defer(() => {\n        return source.pipe(scan(({ current }, value) => ({ value, current: scheduler.now(), last: current }), { current: scheduler.now(), value: undefined, last: undefined }), map(({ current, last, value }) => new TimeInterval(value, current - last)));\n    });\n}\nexport class TimeInterval {\n    constructor(value, interval) {\n        this.value = value;\n        this.interval = interval;\n    }\n}\n"], "mappings": "AAAA,SAASA,KAAT,QAAsB,oBAAtB;AACA,SAASC,IAAT,QAAqB,QAArB;AACA,SAASC,KAAT,QAAsB,qBAAtB;AACA,SAASC,GAAT,QAAoB,OAApB;AACA,OAAO,SAASC,YAAT,CAAsBC,SAAS,GAAGL,KAAlC,EAAyC;EAC5C,OAAQM,MAAD,IAAYJ,KAAK,CAAC,MAAM;IAC3B,OAAOI,MAAM,CAACC,IAAP,CAAYN,IAAI,CAAC,CAAC;MAAEO;IAAF,CAAD,EAAcC,KAAd,MAAyB;MAAEA,KAAF;MAASD,OAAO,EAAEH,SAAS,CAACK,GAAV,EAAlB;MAAmCC,IAAI,EAAEH;IAAzC,CAAzB,CAAD,EAA+E;MAAEA,OAAO,EAAEH,SAAS,CAACK,GAAV,EAAX;MAA4BD,KAAK,EAAEG,SAAnC;MAA8CD,IAAI,EAAEC;IAApD,CAA/E,CAAhB,EAAiKT,GAAG,CAAC,CAAC;MAAEK,OAAF;MAAWG,IAAX;MAAiBF;IAAjB,CAAD,KAA8B,IAAII,YAAJ,CAAiBJ,KAAjB,EAAwBD,OAAO,GAAGG,IAAlC,CAA/B,CAApK,CAAP;EACH,CAFuB,CAAxB;AAGH;AACD,OAAO,MAAME,YAAN,CAAmB;EACtBC,WAAW,CAACL,KAAD,EAAQM,QAAR,EAAkB;IACzB,KAAKN,KAAL,GAAaA,KAAb;IACA,KAAKM,QAAL,GAAgBA,QAAhB;EACH;;AAJqB"}, "metadata": {}, "sourceType": "module"}