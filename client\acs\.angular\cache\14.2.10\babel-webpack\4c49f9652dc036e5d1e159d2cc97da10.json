{"ast": null, "code": "import { not } from '../util/not';\nimport { subscribeTo } from '../util/subscribeTo';\nimport { filter } from '../operators/filter';\nimport { Observable } from '../Observable';\nexport function partition(source, predicate, thisArg) {\n  return [filter(predicate, thisArg)(new Observable(subscribeTo(source))), filter(not(predicate, thisArg))(new Observable(subscribeTo(source)))];\n}", "map": {"version": 3, "names": ["not", "subscribeTo", "filter", "Observable", "partition", "source", "predicate", "thisArg"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/partition.js"], "sourcesContent": ["import { not } from '../util/not';\nimport { subscribeTo } from '../util/subscribeTo';\nimport { filter } from '../operators/filter';\nimport { Observable } from '../Observable';\nexport function partition(source, predicate, thisArg) {\n    return [\n        filter(predicate, thisArg)(new Observable(subscribeTo(source))),\n        filter(not(predicate, thisArg))(new Observable(subscribeTo(source)))\n    ];\n}\n"], "mappings": "AAAA,SAASA,GAAT,QAAoB,aAApB;AACA,SAASC,WAAT,QAA4B,qBAA5B;AACA,SAASC,MAAT,QAAuB,qBAAvB;AACA,SAASC,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,SAAT,CAAmBC,MAAnB,EAA2BC,SAA3B,EAAsCC,OAAtC,EAA+C;EAClD,OAAO,CACHL,MAAM,CAACI,SAAD,EAAYC,OAAZ,CAAN,CAA2B,IAAIJ,UAAJ,CAAeF,WAAW,CAACI,MAAD,CAA1B,CAA3B,CADG,EAEHH,MAAM,CAACF,GAAG,CAACM,SAAD,EAAYC,OAAZ,CAAJ,CAAN,CAAgC,IAAIJ,UAAJ,CAAeF,WAAW,CAACI,MAAD,CAA1B,CAAhC,CAFG,CAAP;AAIH"}, "metadata": {}, "sourceType": "module"}