{"ast": null, "code": "import { innerSubscribe, SimpleInnerSubscriber, SimpleOuterSubscriber } from '../innerSubscribe';\nexport function takeUntil(notifier) {\n  return source => source.lift(new TakeUntilOperator(notifier));\n}\n\nclass TakeUntilOperator {\n  constructor(notifier) {\n    this.notifier = notifier;\n  }\n\n  call(subscriber, source) {\n    const takeUntilSubscriber = new TakeUntilSubscriber(subscriber);\n    const notifierSubscription = innerSubscribe(this.notifier, new SimpleInnerSubscriber(takeUntilSubscriber));\n\n    if (notifierSubscription && !takeUntilSubscriber.seenValue) {\n      takeUntilSubscriber.add(notifierSubscription);\n      return source.subscribe(takeUntilSubscriber);\n    }\n\n    return takeUntilSubscriber;\n  }\n\n}\n\nclass TakeUntilSubscriber extends SimpleOuterSubscriber {\n  constructor(destination) {\n    super(destination);\n    this.seenValue = false;\n  }\n\n  notifyNext() {\n    this.seenValue = true;\n    this.complete();\n  }\n\n  notifyComplete() {}\n\n}", "map": {"version": 3, "names": ["innerSubscribe", "SimpleInnerSubscriber", "SimpleOuterSubscriber", "takeUntil", "notifier", "source", "lift", "TakeUntilOperator", "constructor", "call", "subscriber", "takeUntilSubscriber", "TakeUntilSubscriber", "notifierSubscription", "seenValue", "add", "subscribe", "destination", "notifyNext", "complete", "notifyComplete"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/takeUntil.js"], "sourcesContent": ["import { innerSubscribe, SimpleInnerSubscriber, SimpleOuterSubscriber } from '../innerSubscribe';\nexport function takeUntil(notifier) {\n    return (source) => source.lift(new TakeUntilOperator(notifier));\n}\nclass TakeUntilOperator {\n    constructor(notifier) {\n        this.notifier = notifier;\n    }\n    call(subscriber, source) {\n        const takeUntilSubscriber = new TakeUntilSubscriber(subscriber);\n        const notifierSubscription = innerSubscribe(this.notifier, new SimpleInnerSubscriber(takeUntilSubscriber));\n        if (notifierSubscription && !takeUntilSubscriber.seenValue) {\n            takeUntilSubscriber.add(notifierSubscription);\n            return source.subscribe(takeUntilSubscriber);\n        }\n        return takeUntilSubscriber;\n    }\n}\nclass TakeUntilSubscriber extends SimpleOuterSubscriber {\n    constructor(destination) {\n        super(destination);\n        this.seenValue = false;\n    }\n    notifyNext() {\n        this.seenValue = true;\n        this.complete();\n    }\n    notifyComplete() {\n    }\n}\n"], "mappings": "AAAA,SAASA,cAAT,EAAyBC,qBAAzB,EAAgDC,qBAAhD,QAA6E,mBAA7E;AACA,OAAO,SAASC,SAAT,CAAmBC,QAAnB,EAA6B;EAChC,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,iBAAJ,CAAsBH,QAAtB,CAAZ,CAAnB;AACH;;AACD,MAAMG,iBAAN,CAAwB;EACpBC,WAAW,CAACJ,QAAD,EAAW;IAClB,KAAKA,QAAL,GAAgBA,QAAhB;EACH;;EACDK,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,MAAMM,mBAAmB,GAAG,IAAIC,mBAAJ,CAAwBF,UAAxB,CAA5B;IACA,MAAMG,oBAAoB,GAAGb,cAAc,CAAC,KAAKI,QAAN,EAAgB,IAAIH,qBAAJ,CAA0BU,mBAA1B,CAAhB,CAA3C;;IACA,IAAIE,oBAAoB,IAAI,CAACF,mBAAmB,CAACG,SAAjD,EAA4D;MACxDH,mBAAmB,CAACI,GAApB,CAAwBF,oBAAxB;MACA,OAAOR,MAAM,CAACW,SAAP,CAAiBL,mBAAjB,CAAP;IACH;;IACD,OAAOA,mBAAP;EACH;;AAZmB;;AAcxB,MAAMC,mBAAN,SAAkCV,qBAAlC,CAAwD;EACpDM,WAAW,CAACS,WAAD,EAAc;IACrB,MAAMA,WAAN;IACA,KAAKH,SAAL,GAAiB,KAAjB;EACH;;EACDI,UAAU,GAAG;IACT,KAAKJ,SAAL,GAAiB,IAAjB;IACA,KAAKK,QAAL;EACH;;EACDC,cAAc,GAAG,CAChB;;AAVmD"}, "metadata": {}, "sourceType": "module"}