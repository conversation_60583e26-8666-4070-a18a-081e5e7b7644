{"ast": null, "code": "import { Observable } from '../Observable';\nimport { async } from '../scheduler/async';\nimport { isNumeric } from '../util/isNumeric';\nimport { isScheduler } from '../util/isScheduler';\nexport function timer(dueTime = 0, periodOrScheduler, scheduler) {\n  let period = -1;\n\n  if (isNumeric(periodOrScheduler)) {\n    period = Number(periodOrScheduler) < 1 && 1 || Number(periodOrScheduler);\n  } else if (isScheduler(periodOrScheduler)) {\n    scheduler = periodOrScheduler;\n  }\n\n  if (!isScheduler(scheduler)) {\n    scheduler = async;\n  }\n\n  return new Observable(subscriber => {\n    const due = isNumeric(dueTime) ? dueTime : +dueTime - scheduler.now();\n    return scheduler.schedule(dispatch, due, {\n      index: 0,\n      period,\n      subscriber\n    });\n  });\n}\n\nfunction dispatch(state) {\n  const {\n    index,\n    period,\n    subscriber\n  } = state;\n  subscriber.next(index);\n\n  if (subscriber.closed) {\n    return;\n  } else if (period === -1) {\n    return subscriber.complete();\n  }\n\n  state.index = index + 1;\n  this.schedule(state, period);\n}", "map": {"version": 3, "names": ["Observable", "async", "isNumeric", "isScheduler", "timer", "dueTime", "periodOrScheduler", "scheduler", "period", "Number", "subscriber", "due", "now", "schedule", "dispatch", "index", "state", "next", "closed", "complete"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/timer.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { async } from '../scheduler/async';\nimport { isNumeric } from '../util/isNumeric';\nimport { isScheduler } from '../util/isScheduler';\nexport function timer(dueTime = 0, periodOrScheduler, scheduler) {\n    let period = -1;\n    if (isNumeric(periodOrScheduler)) {\n        period = Number(periodOrScheduler) < 1 && 1 || Number(periodOrScheduler);\n    }\n    else if (isScheduler(periodOrScheduler)) {\n        scheduler = periodOrScheduler;\n    }\n    if (!isScheduler(scheduler)) {\n        scheduler = async;\n    }\n    return new Observable(subscriber => {\n        const due = isNumeric(dueTime)\n            ? dueTime\n            : (+dueTime - scheduler.now());\n        return scheduler.schedule(dispatch, due, {\n            index: 0, period, subscriber\n        });\n    });\n}\nfunction dispatch(state) {\n    const { index, period, subscriber } = state;\n    subscriber.next(index);\n    if (subscriber.closed) {\n        return;\n    }\n    else if (period === -1) {\n        return subscriber.complete();\n    }\n    state.index = index + 1;\n    this.schedule(state, period);\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,KAAT,QAAsB,oBAAtB;AACA,SAASC,SAAT,QAA0B,mBAA1B;AACA,SAASC,WAAT,QAA4B,qBAA5B;AACA,OAAO,SAASC,KAAT,CAAeC,OAAO,GAAG,CAAzB,EAA4BC,iBAA5B,EAA+CC,SAA/C,EAA0D;EAC7D,IAAIC,MAAM,GAAG,CAAC,CAAd;;EACA,IAAIN,SAAS,CAACI,iBAAD,CAAb,EAAkC;IAC9BE,MAAM,GAAGC,MAAM,CAACH,iBAAD,CAAN,GAA4B,CAA5B,IAAiC,CAAjC,IAAsCG,MAAM,CAACH,iBAAD,CAArD;EACH,CAFD,MAGK,IAAIH,WAAW,CAACG,iBAAD,CAAf,EAAoC;IACrCC,SAAS,GAAGD,iBAAZ;EACH;;EACD,IAAI,CAACH,WAAW,CAACI,SAAD,CAAhB,EAA6B;IACzBA,SAAS,GAAGN,KAAZ;EACH;;EACD,OAAO,IAAID,UAAJ,CAAeU,UAAU,IAAI;IAChC,MAAMC,GAAG,GAAGT,SAAS,CAACG,OAAD,CAAT,GACNA,OADM,GAEL,CAACA,OAAD,GAAWE,SAAS,CAACK,GAAV,EAFlB;IAGA,OAAOL,SAAS,CAACM,QAAV,CAAmBC,QAAnB,EAA6BH,GAA7B,EAAkC;MACrCI,KAAK,EAAE,CAD8B;MAC3BP,MAD2B;MACnBE;IADmB,CAAlC,CAAP;EAGH,CAPM,CAAP;AAQH;;AACD,SAASI,QAAT,CAAkBE,KAAlB,EAAyB;EACrB,MAAM;IAAED,KAAF;IAASP,MAAT;IAAiBE;EAAjB,IAAgCM,KAAtC;EACAN,UAAU,CAACO,IAAX,CAAgBF,KAAhB;;EACA,IAAIL,UAAU,CAACQ,MAAf,EAAuB;IACnB;EACH,CAFD,MAGK,IAAIV,MAAM,KAAK,CAAC,CAAhB,EAAmB;IACpB,OAAOE,UAAU,CAACS,QAAX,EAAP;EACH;;EACDH,KAAK,CAACD,KAAN,GAAcA,KAAK,GAAG,CAAtB;EACA,KAAKF,QAAL,CAAcG,KAAd,EAAqBR,MAArB;AACH"}, "metadata": {}, "sourceType": "module"}