{"ast": null, "code": "import { ReplaySubject } from '../ReplaySubject';\nimport { multicast } from './multicast';\nexport function publishReplay(bufferSize, windowTime, selectorOrScheduler, scheduler) {\n  if (selectorOrScheduler && typeof selectorOrScheduler !== 'function') {\n    scheduler = selectorOrScheduler;\n  }\n\n  const selector = typeof selectorOrScheduler === 'function' ? selectorOrScheduler : undefined;\n  const subject = new ReplaySubject(bufferSize, windowTime, scheduler);\n  return source => multicast(() => subject, selector)(source);\n}", "map": {"version": 3, "names": ["ReplaySubject", "multicast", "publishReplay", "bufferSize", "windowTime", "selectorOrScheduler", "scheduler", "selector", "undefined", "subject", "source"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/publishReplay.js"], "sourcesContent": ["import { ReplaySubject } from '../ReplaySubject';\nimport { multicast } from './multicast';\nexport function publishReplay(bufferSize, windowTime, selectorOrScheduler, scheduler) {\n    if (selectorOrScheduler && typeof selectorOrScheduler !== 'function') {\n        scheduler = selectorOrScheduler;\n    }\n    const selector = typeof selectorOrScheduler === 'function' ? selectorOrScheduler : undefined;\n    const subject = new ReplaySubject(bufferSize, windowTime, scheduler);\n    return (source) => multicast(() => subject, selector)(source);\n}\n"], "mappings": "AAAA,SAASA,aAAT,QAA8B,kBAA9B;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,OAAO,SAASC,aAAT,CAAuBC,UAAvB,EAAmCC,UAAnC,EAA+CC,mBAA/C,EAAoEC,SAApE,EAA+E;EAClF,IAAID,mBAAmB,IAAI,OAAOA,mBAAP,KAA+B,UAA1D,EAAsE;IAClEC,SAAS,GAAGD,mBAAZ;EACH;;EACD,MAAME,QAAQ,GAAG,OAAOF,mBAAP,KAA+B,UAA/B,GAA4CA,mBAA5C,GAAkEG,SAAnF;EACA,MAAMC,OAAO,GAAG,IAAIT,aAAJ,CAAkBG,UAAlB,EAA8BC,UAA9B,EAA0CE,SAA1C,CAAhB;EACA,OAAQI,MAAD,IAAYT,SAAS,CAAC,MAAMQ,OAAP,EAAgBF,QAAhB,CAAT,CAAmCG,MAAnC,CAAnB;AACH"}, "metadata": {}, "sourceType": "module"}