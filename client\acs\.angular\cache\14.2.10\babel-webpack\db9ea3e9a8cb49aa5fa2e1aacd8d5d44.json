{"ast": null, "code": "import { Subscription } from './Subscription';\nexport class SubjectSubscription extends Subscription {\n  constructor(subject, subscriber) {\n    super();\n    this.subject = subject;\n    this.subscriber = subscriber;\n    this.closed = false;\n  }\n\n  unsubscribe() {\n    if (this.closed) {\n      return;\n    }\n\n    this.closed = true;\n    const subject = this.subject;\n    const observers = subject.observers;\n    this.subject = null;\n\n    if (!observers || observers.length === 0 || subject.isStopped || subject.closed) {\n      return;\n    }\n\n    const subscriberIndex = observers.indexOf(this.subscriber);\n\n    if (subscriberIndex !== -1) {\n      observers.splice(subscriberIndex, 1);\n    }\n  }\n\n}", "map": {"version": 3, "names": ["Subscription", "SubjectSubscription", "constructor", "subject", "subscriber", "closed", "unsubscribe", "observers", "length", "isStopped", "subscriberIndex", "indexOf", "splice"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/SubjectSubscription.js"], "sourcesContent": ["import { Subscription } from './Subscription';\nexport class SubjectSubscription extends Subscription {\n    constructor(subject, subscriber) {\n        super();\n        this.subject = subject;\n        this.subscriber = subscriber;\n        this.closed = false;\n    }\n    unsubscribe() {\n        if (this.closed) {\n            return;\n        }\n        this.closed = true;\n        const subject = this.subject;\n        const observers = subject.observers;\n        this.subject = null;\n        if (!observers || observers.length === 0 || subject.isStopped || subject.closed) {\n            return;\n        }\n        const subscriberIndex = observers.indexOf(this.subscriber);\n        if (subscriberIndex !== -1) {\n            observers.splice(subscriberIndex, 1);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAT,QAA6B,gBAA7B;AACA,OAAO,MAAMC,mBAAN,SAAkCD,YAAlC,CAA+C;EAClDE,WAAW,CAACC,OAAD,EAAUC,UAAV,EAAsB;IAC7B;IACA,KAAKD,OAAL,GAAeA,OAAf;IACA,KAAKC,UAAL,GAAkBA,UAAlB;IACA,KAAKC,MAAL,GAAc,KAAd;EACH;;EACDC,WAAW,GAAG;IACV,IAAI,KAAKD,MAAT,EAAiB;MACb;IACH;;IACD,KAAKA,MAAL,GAAc,IAAd;IACA,MAAMF,OAAO,GAAG,KAAKA,OAArB;IACA,MAAMI,SAAS,GAAGJ,OAAO,CAACI,SAA1B;IACA,KAAKJ,OAAL,GAAe,IAAf;;IACA,IAAI,CAACI,SAAD,IAAcA,SAAS,CAACC,MAAV,KAAqB,CAAnC,IAAwCL,OAAO,CAACM,SAAhD,IAA6DN,OAAO,CAACE,MAAzE,EAAiF;MAC7E;IACH;;IACD,MAAMK,eAAe,GAAGH,SAAS,CAACI,OAAV,CAAkB,KAAKP,UAAvB,CAAxB;;IACA,IAAIM,eAAe,KAAK,CAAC,CAAzB,EAA4B;MACxBH,SAAS,CAACK,MAAV,CAAiBF,eAAjB,EAAkC,CAAlC;IACH;EACJ;;AAtBiD"}, "metadata": {}, "sourceType": "module"}