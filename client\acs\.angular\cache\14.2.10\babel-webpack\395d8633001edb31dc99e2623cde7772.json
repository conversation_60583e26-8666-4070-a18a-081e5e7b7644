{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { async } from '../scheduler/async';\nexport function sampleTime(period, scheduler = async) {\n  return source => source.lift(new SampleTimeOperator(period, scheduler));\n}\n\nclass SampleTimeOperator {\n  constructor(period, scheduler) {\n    this.period = period;\n    this.scheduler = scheduler;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new SampleTimeSubscriber(subscriber, this.period, this.scheduler));\n  }\n\n}\n\nclass SampleTimeSubscriber extends Subscriber {\n  constructor(destination, period, scheduler) {\n    super(destination);\n    this.period = period;\n    this.scheduler = scheduler;\n    this.hasValue = false;\n    this.add(scheduler.schedule(dispatchNotification, period, {\n      subscriber: this,\n      period\n    }));\n  }\n\n  _next(value) {\n    this.lastValue = value;\n    this.hasValue = true;\n  }\n\n  notifyNext() {\n    if (this.hasValue) {\n      this.hasValue = false;\n      this.destination.next(this.lastValue);\n    }\n  }\n\n}\n\nfunction dispatchNotification(state) {\n  let {\n    subscriber,\n    period\n  } = state;\n  subscriber.notifyNext();\n  this.schedule(state, period);\n}", "map": {"version": 3, "names": ["Subscriber", "async", "sampleTime", "period", "scheduler", "source", "lift", "SampleTimeOperator", "constructor", "call", "subscriber", "subscribe", "SampleTimeSubscriber", "destination", "hasValue", "add", "schedule", "dispatchNotification", "_next", "value", "lastValue", "notifyNext", "next", "state"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/sampleTime.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { async } from '../scheduler/async';\nexport function sampleTime(period, scheduler = async) {\n    return (source) => source.lift(new SampleTimeOperator(period, scheduler));\n}\nclass SampleTimeOperator {\n    constructor(period, scheduler) {\n        this.period = period;\n        this.scheduler = scheduler;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new SampleTimeSubscriber(subscriber, this.period, this.scheduler));\n    }\n}\nclass SampleTimeSubscriber extends Subscriber {\n    constructor(destination, period, scheduler) {\n        super(destination);\n        this.period = period;\n        this.scheduler = scheduler;\n        this.hasValue = false;\n        this.add(scheduler.schedule(dispatchNotification, period, { subscriber: this, period }));\n    }\n    _next(value) {\n        this.lastValue = value;\n        this.hasValue = true;\n    }\n    notifyNext() {\n        if (this.hasValue) {\n            this.hasValue = false;\n            this.destination.next(this.lastValue);\n        }\n    }\n}\nfunction dispatchNotification(state) {\n    let { subscriber, period } = state;\n    subscriber.notifyNext();\n    this.schedule(state, period);\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,KAAT,QAAsB,oBAAtB;AACA,OAAO,SAASC,UAAT,CAAoBC,MAApB,EAA4BC,SAAS,GAAGH,KAAxC,EAA+C;EAClD,OAAQI,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,kBAAJ,CAAuBJ,MAAvB,EAA+BC,SAA/B,CAAZ,CAAnB;AACH;;AACD,MAAMG,kBAAN,CAAyB;EACrBC,WAAW,CAACL,MAAD,EAASC,SAAT,EAAoB;IAC3B,KAAKD,MAAL,GAAcA,MAAd;IACA,KAAKC,SAAL,GAAiBA,SAAjB;EACH;;EACDK,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,oBAAJ,CAAyBF,UAAzB,EAAqC,KAAKP,MAA1C,EAAkD,KAAKC,SAAvD,CAAjB,CAAP;EACH;;AAPoB;;AASzB,MAAMQ,oBAAN,SAAmCZ,UAAnC,CAA8C;EAC1CQ,WAAW,CAACK,WAAD,EAAcV,MAAd,EAAsBC,SAAtB,EAAiC;IACxC,MAAMS,WAAN;IACA,KAAKV,MAAL,GAAcA,MAAd;IACA,KAAKC,SAAL,GAAiBA,SAAjB;IACA,KAAKU,QAAL,GAAgB,KAAhB;IACA,KAAKC,GAAL,CAASX,SAAS,CAACY,QAAV,CAAmBC,oBAAnB,EAAyCd,MAAzC,EAAiD;MAAEO,UAAU,EAAE,IAAd;MAAoBP;IAApB,CAAjD,CAAT;EACH;;EACDe,KAAK,CAACC,KAAD,EAAQ;IACT,KAAKC,SAAL,GAAiBD,KAAjB;IACA,KAAKL,QAAL,GAAgB,IAAhB;EACH;;EACDO,UAAU,GAAG;IACT,IAAI,KAAKP,QAAT,EAAmB;MACf,KAAKA,QAAL,GAAgB,KAAhB;MACA,KAAKD,WAAL,CAAiBS,IAAjB,CAAsB,KAAKF,SAA3B;IACH;EACJ;;AAjByC;;AAmB9C,SAASH,oBAAT,CAA8BM,KAA9B,EAAqC;EACjC,IAAI;IAAEb,UAAF;IAAcP;EAAd,IAAyBoB,KAA7B;EACAb,UAAU,CAACW,UAAX;EACA,KAAKL,QAAL,CAAcO,KAAd,EAAqBpB,MAArB;AACH"}, "metadata": {}, "sourceType": "module"}