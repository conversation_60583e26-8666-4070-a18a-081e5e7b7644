{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { Subscription } from '../Subscription';\nimport { Observable } from '../Observable';\nimport { Subject } from '../Subject';\nexport function groupBy(keySelector, elementSelector, durationSelector, subjectSelector) {\n  return source => source.lift(new GroupByOperator(keySelector, elementSelector, durationSelector, subjectSelector));\n}\n\nclass GroupByOperator {\n  constructor(keySelector, elementSelector, durationSelector, subjectSelector) {\n    this.keySelector = keySelector;\n    this.elementSelector = elementSelector;\n    this.durationSelector = durationSelector;\n    this.subjectSelector = subjectSelector;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new GroupBySubscriber(subscriber, this.keySelector, this.elementSelector, this.durationSelector, this.subjectSelector));\n  }\n\n}\n\nclass GroupBySubscriber extends Subscriber {\n  constructor(destination, keySelector, elementSelector, durationSelector, subjectSelector) {\n    super(destination);\n    this.keySelector = keySelector;\n    this.elementSelector = elementSelector;\n    this.durationSelector = durationSelector;\n    this.subjectSelector = subjectSelector;\n    this.groups = null;\n    this.attemptedToUnsubscribe = false;\n    this.count = 0;\n  }\n\n  _next(value) {\n    let key;\n\n    try {\n      key = this.keySelector(value);\n    } catch (err) {\n      this.error(err);\n      return;\n    }\n\n    this._group(value, key);\n  }\n\n  _group(value, key) {\n    let groups = this.groups;\n\n    if (!groups) {\n      groups = this.groups = new Map();\n    }\n\n    let group = groups.get(key);\n    let element;\n\n    if (this.elementSelector) {\n      try {\n        element = this.elementSelector(value);\n      } catch (err) {\n        this.error(err);\n      }\n    } else {\n      element = value;\n    }\n\n    if (!group) {\n      group = this.subjectSelector ? this.subjectSelector() : new Subject();\n      groups.set(key, group);\n      const groupedObservable = new GroupedObservable(key, group, this);\n      this.destination.next(groupedObservable);\n\n      if (this.durationSelector) {\n        let duration;\n\n        try {\n          duration = this.durationSelector(new GroupedObservable(key, group));\n        } catch (err) {\n          this.error(err);\n          return;\n        }\n\n        this.add(duration.subscribe(new GroupDurationSubscriber(key, group, this)));\n      }\n    }\n\n    if (!group.closed) {\n      group.next(element);\n    }\n  }\n\n  _error(err) {\n    const groups = this.groups;\n\n    if (groups) {\n      groups.forEach((group, key) => {\n        group.error(err);\n      });\n      groups.clear();\n    }\n\n    this.destination.error(err);\n  }\n\n  _complete() {\n    const groups = this.groups;\n\n    if (groups) {\n      groups.forEach((group, key) => {\n        group.complete();\n      });\n      groups.clear();\n    }\n\n    this.destination.complete();\n  }\n\n  removeGroup(key) {\n    this.groups.delete(key);\n  }\n\n  unsubscribe() {\n    if (!this.closed) {\n      this.attemptedToUnsubscribe = true;\n\n      if (this.count === 0) {\n        super.unsubscribe();\n      }\n    }\n  }\n\n}\n\nclass GroupDurationSubscriber extends Subscriber {\n  constructor(key, group, parent) {\n    super(group);\n    this.key = key;\n    this.group = group;\n    this.parent = parent;\n  }\n\n  _next(value) {\n    this.complete();\n  }\n\n  _unsubscribe() {\n    const {\n      parent,\n      key\n    } = this;\n    this.key = this.parent = null;\n\n    if (parent) {\n      parent.removeGroup(key);\n    }\n  }\n\n}\n\nexport class GroupedObservable extends Observable {\n  constructor(key, groupSubject, refCountSubscription) {\n    super();\n    this.key = key;\n    this.groupSubject = groupSubject;\n    this.refCountSubscription = refCountSubscription;\n  }\n\n  _subscribe(subscriber) {\n    const subscription = new Subscription();\n    const {\n      refCountSubscription,\n      groupSubject\n    } = this;\n\n    if (refCountSubscription && !refCountSubscription.closed) {\n      subscription.add(new InnerRefCountSubscription(refCountSubscription));\n    }\n\n    subscription.add(groupSubject.subscribe(subscriber));\n    return subscription;\n  }\n\n}\n\nclass InnerRefCountSubscription extends Subscription {\n  constructor(parent) {\n    super();\n    this.parent = parent;\n    parent.count++;\n  }\n\n  unsubscribe() {\n    const parent = this.parent;\n\n    if (!parent.closed && !this.closed) {\n      super.unsubscribe();\n      parent.count -= 1;\n\n      if (parent.count === 0 && parent.attemptedToUnsubscribe) {\n        parent.unsubscribe();\n      }\n    }\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "Subscription", "Observable", "Subject", "groupBy", "keySelector", "elementSelector", "durationSelector", "subjectSelector", "source", "lift", "GroupByOperator", "constructor", "call", "subscriber", "subscribe", "GroupBySubscriber", "destination", "groups", "attemptedToUnsubscribe", "count", "_next", "value", "key", "err", "error", "_group", "Map", "group", "get", "element", "set", "groupedObservable", "GroupedObservable", "next", "duration", "add", "GroupDurationSubscriber", "closed", "_error", "for<PERSON>ach", "clear", "_complete", "complete", "removeGroup", "delete", "unsubscribe", "parent", "_unsubscribe", "groupSubject", "refCountSubscription", "_subscribe", "subscription", "InnerRefCountSubscription"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/groupBy.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { Subscription } from '../Subscription';\nimport { Observable } from '../Observable';\nimport { Subject } from '../Subject';\nexport function groupBy(keySelector, elementSelector, durationSelector, subjectSelector) {\n    return (source) => source.lift(new GroupByOperator(keySelector, elementSelector, durationSelector, subjectSelector));\n}\nclass GroupByOperator {\n    constructor(keySelector, elementSelector, durationSelector, subjectSelector) {\n        this.keySelector = keySelector;\n        this.elementSelector = elementSelector;\n        this.durationSelector = durationSelector;\n        this.subjectSelector = subjectSelector;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new GroupBySubscriber(subscriber, this.keySelector, this.elementSelector, this.durationSelector, this.subjectSelector));\n    }\n}\nclass GroupBySubscriber extends Subscriber {\n    constructor(destination, keySelector, elementSelector, durationSelector, subjectSelector) {\n        super(destination);\n        this.keySelector = keySelector;\n        this.elementSelector = elementSelector;\n        this.durationSelector = durationSelector;\n        this.subjectSelector = subjectSelector;\n        this.groups = null;\n        this.attemptedToUnsubscribe = false;\n        this.count = 0;\n    }\n    _next(value) {\n        let key;\n        try {\n            key = this.keySelector(value);\n        }\n        catch (err) {\n            this.error(err);\n            return;\n        }\n        this._group(value, key);\n    }\n    _group(value, key) {\n        let groups = this.groups;\n        if (!groups) {\n            groups = this.groups = new Map();\n        }\n        let group = groups.get(key);\n        let element;\n        if (this.elementSelector) {\n            try {\n                element = this.elementSelector(value);\n            }\n            catch (err) {\n                this.error(err);\n            }\n        }\n        else {\n            element = value;\n        }\n        if (!group) {\n            group = (this.subjectSelector ? this.subjectSelector() : new Subject());\n            groups.set(key, group);\n            const groupedObservable = new GroupedObservable(key, group, this);\n            this.destination.next(groupedObservable);\n            if (this.durationSelector) {\n                let duration;\n                try {\n                    duration = this.durationSelector(new GroupedObservable(key, group));\n                }\n                catch (err) {\n                    this.error(err);\n                    return;\n                }\n                this.add(duration.subscribe(new GroupDurationSubscriber(key, group, this)));\n            }\n        }\n        if (!group.closed) {\n            group.next(element);\n        }\n    }\n    _error(err) {\n        const groups = this.groups;\n        if (groups) {\n            groups.forEach((group, key) => {\n                group.error(err);\n            });\n            groups.clear();\n        }\n        this.destination.error(err);\n    }\n    _complete() {\n        const groups = this.groups;\n        if (groups) {\n            groups.forEach((group, key) => {\n                group.complete();\n            });\n            groups.clear();\n        }\n        this.destination.complete();\n    }\n    removeGroup(key) {\n        this.groups.delete(key);\n    }\n    unsubscribe() {\n        if (!this.closed) {\n            this.attemptedToUnsubscribe = true;\n            if (this.count === 0) {\n                super.unsubscribe();\n            }\n        }\n    }\n}\nclass GroupDurationSubscriber extends Subscriber {\n    constructor(key, group, parent) {\n        super(group);\n        this.key = key;\n        this.group = group;\n        this.parent = parent;\n    }\n    _next(value) {\n        this.complete();\n    }\n    _unsubscribe() {\n        const { parent, key } = this;\n        this.key = this.parent = null;\n        if (parent) {\n            parent.removeGroup(key);\n        }\n    }\n}\nexport class GroupedObservable extends Observable {\n    constructor(key, groupSubject, refCountSubscription) {\n        super();\n        this.key = key;\n        this.groupSubject = groupSubject;\n        this.refCountSubscription = refCountSubscription;\n    }\n    _subscribe(subscriber) {\n        const subscription = new Subscription();\n        const { refCountSubscription, groupSubject } = this;\n        if (refCountSubscription && !refCountSubscription.closed) {\n            subscription.add(new InnerRefCountSubscription(refCountSubscription));\n        }\n        subscription.add(groupSubject.subscribe(subscriber));\n        return subscription;\n    }\n}\nclass InnerRefCountSubscription extends Subscription {\n    constructor(parent) {\n        super();\n        this.parent = parent;\n        parent.count++;\n    }\n    unsubscribe() {\n        const parent = this.parent;\n        if (!parent.closed && !this.closed) {\n            super.unsubscribe();\n            parent.count -= 1;\n            if (parent.count === 0 && parent.attemptedToUnsubscribe) {\n                parent.unsubscribe();\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,UAAT,QAA2B,eAA3B;AACA,SAASC,OAAT,QAAwB,YAAxB;AACA,OAAO,SAASC,OAAT,CAAiBC,WAAjB,EAA8BC,eAA9B,EAA+CC,gBAA/C,EAAiEC,eAAjE,EAAkF;EACrF,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,eAAJ,CAAoBN,WAApB,EAAiCC,eAAjC,EAAkDC,gBAAlD,EAAoEC,eAApE,CAAZ,CAAnB;AACH;;AACD,MAAMG,eAAN,CAAsB;EAClBC,WAAW,CAACP,WAAD,EAAcC,eAAd,EAA+BC,gBAA/B,EAAiDC,eAAjD,EAAkE;IACzE,KAAKH,WAAL,GAAmBA,WAAnB;IACA,KAAKC,eAAL,GAAuBA,eAAvB;IACA,KAAKC,gBAAL,GAAwBA,gBAAxB;IACA,KAAKC,eAAL,GAAuBA,eAAvB;EACH;;EACDK,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,iBAAJ,CAAsBF,UAAtB,EAAkC,KAAKT,WAAvC,EAAoD,KAAKC,eAAzD,EAA0E,KAAKC,gBAA/E,EAAiG,KAAKC,eAAtG,CAAjB,CAAP;EACH;;AATiB;;AAWtB,MAAMQ,iBAAN,SAAgChB,UAAhC,CAA2C;EACvCY,WAAW,CAACK,WAAD,EAAcZ,WAAd,EAA2BC,eAA3B,EAA4CC,gBAA5C,EAA8DC,eAA9D,EAA+E;IACtF,MAAMS,WAAN;IACA,KAAKZ,WAAL,GAAmBA,WAAnB;IACA,KAAKC,eAAL,GAAuBA,eAAvB;IACA,KAAKC,gBAAL,GAAwBA,gBAAxB;IACA,KAAKC,eAAL,GAAuBA,eAAvB;IACA,KAAKU,MAAL,GAAc,IAAd;IACA,KAAKC,sBAAL,GAA8B,KAA9B;IACA,KAAKC,KAAL,GAAa,CAAb;EACH;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACT,IAAIC,GAAJ;;IACA,IAAI;MACAA,GAAG,GAAG,KAAKlB,WAAL,CAAiBiB,KAAjB,CAAN;IACH,CAFD,CAGA,OAAOE,GAAP,EAAY;MACR,KAAKC,KAAL,CAAWD,GAAX;MACA;IACH;;IACD,KAAKE,MAAL,CAAYJ,KAAZ,EAAmBC,GAAnB;EACH;;EACDG,MAAM,CAACJ,KAAD,EAAQC,GAAR,EAAa;IACf,IAAIL,MAAM,GAAG,KAAKA,MAAlB;;IACA,IAAI,CAACA,MAAL,EAAa;MACTA,MAAM,GAAG,KAAKA,MAAL,GAAc,IAAIS,GAAJ,EAAvB;IACH;;IACD,IAAIC,KAAK,GAAGV,MAAM,CAACW,GAAP,CAAWN,GAAX,CAAZ;IACA,IAAIO,OAAJ;;IACA,IAAI,KAAKxB,eAAT,EAA0B;MACtB,IAAI;QACAwB,OAAO,GAAG,KAAKxB,eAAL,CAAqBgB,KAArB,CAAV;MACH,CAFD,CAGA,OAAOE,GAAP,EAAY;QACR,KAAKC,KAAL,CAAWD,GAAX;MACH;IACJ,CAPD,MAQK;MACDM,OAAO,GAAGR,KAAV;IACH;;IACD,IAAI,CAACM,KAAL,EAAY;MACRA,KAAK,GAAI,KAAKpB,eAAL,GAAuB,KAAKA,eAAL,EAAvB,GAAgD,IAAIL,OAAJ,EAAzD;MACAe,MAAM,CAACa,GAAP,CAAWR,GAAX,EAAgBK,KAAhB;MACA,MAAMI,iBAAiB,GAAG,IAAIC,iBAAJ,CAAsBV,GAAtB,EAA2BK,KAA3B,EAAkC,IAAlC,CAA1B;MACA,KAAKX,WAAL,CAAiBiB,IAAjB,CAAsBF,iBAAtB;;MACA,IAAI,KAAKzB,gBAAT,EAA2B;QACvB,IAAI4B,QAAJ;;QACA,IAAI;UACAA,QAAQ,GAAG,KAAK5B,gBAAL,CAAsB,IAAI0B,iBAAJ,CAAsBV,GAAtB,EAA2BK,KAA3B,CAAtB,CAAX;QACH,CAFD,CAGA,OAAOJ,GAAP,EAAY;UACR,KAAKC,KAAL,CAAWD,GAAX;UACA;QACH;;QACD,KAAKY,GAAL,CAASD,QAAQ,CAACpB,SAAT,CAAmB,IAAIsB,uBAAJ,CAA4Bd,GAA5B,EAAiCK,KAAjC,EAAwC,IAAxC,CAAnB,CAAT;MACH;IACJ;;IACD,IAAI,CAACA,KAAK,CAACU,MAAX,EAAmB;MACfV,KAAK,CAACM,IAAN,CAAWJ,OAAX;IACH;EACJ;;EACDS,MAAM,CAACf,GAAD,EAAM;IACR,MAAMN,MAAM,GAAG,KAAKA,MAApB;;IACA,IAAIA,MAAJ,EAAY;MACRA,MAAM,CAACsB,OAAP,CAAe,CAACZ,KAAD,EAAQL,GAAR,KAAgB;QAC3BK,KAAK,CAACH,KAAN,CAAYD,GAAZ;MACH,CAFD;MAGAN,MAAM,CAACuB,KAAP;IACH;;IACD,KAAKxB,WAAL,CAAiBQ,KAAjB,CAAuBD,GAAvB;EACH;;EACDkB,SAAS,GAAG;IACR,MAAMxB,MAAM,GAAG,KAAKA,MAApB;;IACA,IAAIA,MAAJ,EAAY;MACRA,MAAM,CAACsB,OAAP,CAAe,CAACZ,KAAD,EAAQL,GAAR,KAAgB;QAC3BK,KAAK,CAACe,QAAN;MACH,CAFD;MAGAzB,MAAM,CAACuB,KAAP;IACH;;IACD,KAAKxB,WAAL,CAAiB0B,QAAjB;EACH;;EACDC,WAAW,CAACrB,GAAD,EAAM;IACb,KAAKL,MAAL,CAAY2B,MAAZ,CAAmBtB,GAAnB;EACH;;EACDuB,WAAW,GAAG;IACV,IAAI,CAAC,KAAKR,MAAV,EAAkB;MACd,KAAKnB,sBAAL,GAA8B,IAA9B;;MACA,IAAI,KAAKC,KAAL,KAAe,CAAnB,EAAsB;QAClB,MAAM0B,WAAN;MACH;IACJ;EACJ;;AA3FsC;;AA6F3C,MAAMT,uBAAN,SAAsCrC,UAAtC,CAAiD;EAC7CY,WAAW,CAACW,GAAD,EAAMK,KAAN,EAAamB,MAAb,EAAqB;IAC5B,MAAMnB,KAAN;IACA,KAAKL,GAAL,GAAWA,GAAX;IACA,KAAKK,KAAL,GAAaA,KAAb;IACA,KAAKmB,MAAL,GAAcA,MAAd;EACH;;EACD1B,KAAK,CAACC,KAAD,EAAQ;IACT,KAAKqB,QAAL;EACH;;EACDK,YAAY,GAAG;IACX,MAAM;MAAED,MAAF;MAAUxB;IAAV,IAAkB,IAAxB;IACA,KAAKA,GAAL,GAAW,KAAKwB,MAAL,GAAc,IAAzB;;IACA,IAAIA,MAAJ,EAAY;MACRA,MAAM,CAACH,WAAP,CAAmBrB,GAAnB;IACH;EACJ;;AAhB4C;;AAkBjD,OAAO,MAAMU,iBAAN,SAAgC/B,UAAhC,CAA2C;EAC9CU,WAAW,CAACW,GAAD,EAAM0B,YAAN,EAAoBC,oBAApB,EAA0C;IACjD;IACA,KAAK3B,GAAL,GAAWA,GAAX;IACA,KAAK0B,YAAL,GAAoBA,YAApB;IACA,KAAKC,oBAAL,GAA4BA,oBAA5B;EACH;;EACDC,UAAU,CAACrC,UAAD,EAAa;IACnB,MAAMsC,YAAY,GAAG,IAAInD,YAAJ,EAArB;IACA,MAAM;MAAEiD,oBAAF;MAAwBD;IAAxB,IAAyC,IAA/C;;IACA,IAAIC,oBAAoB,IAAI,CAACA,oBAAoB,CAACZ,MAAlD,EAA0D;MACtDc,YAAY,CAAChB,GAAb,CAAiB,IAAIiB,yBAAJ,CAA8BH,oBAA9B,CAAjB;IACH;;IACDE,YAAY,CAAChB,GAAb,CAAiBa,YAAY,CAAClC,SAAb,CAAuBD,UAAvB,CAAjB;IACA,OAAOsC,YAAP;EACH;;AAf6C;;AAiBlD,MAAMC,yBAAN,SAAwCpD,YAAxC,CAAqD;EACjDW,WAAW,CAACmC,MAAD,EAAS;IAChB;IACA,KAAKA,MAAL,GAAcA,MAAd;IACAA,MAAM,CAAC3B,KAAP;EACH;;EACD0B,WAAW,GAAG;IACV,MAAMC,MAAM,GAAG,KAAKA,MAApB;;IACA,IAAI,CAACA,MAAM,CAACT,MAAR,IAAkB,CAAC,KAAKA,MAA5B,EAAoC;MAChC,MAAMQ,WAAN;MACAC,MAAM,CAAC3B,KAAP,IAAgB,CAAhB;;MACA,IAAI2B,MAAM,CAAC3B,KAAP,KAAiB,CAAjB,IAAsB2B,MAAM,CAAC5B,sBAAjC,EAAyD;QACrD4B,MAAM,CAACD,WAAP;MACH;IACJ;EACJ;;AAfgD"}, "metadata": {}, "sourceType": "module"}