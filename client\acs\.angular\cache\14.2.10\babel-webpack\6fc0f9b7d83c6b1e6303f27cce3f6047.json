{"ast": null, "code": "import { Subscription } from '../Subscription';\nimport { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function bufferWhen(closingSelector) {\n  return function (source) {\n    return source.lift(new <PERSON><PERSON>er<PERSON>henOperator(closingSelector));\n  };\n}\n\nclass BufferWhenOperator {\n  constructor(closingSelector) {\n    this.closingSelector = closingSelector;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new BufferWhenSubscriber(subscriber, this.closingSelector));\n  }\n\n}\n\nclass Buffer<PERSON>henSubscriber extends SimpleOuterSubscriber {\n  constructor(destination, closingSelector) {\n    super(destination);\n    this.closingSelector = closingSelector;\n    this.subscribing = false;\n    this.openBuffer();\n  }\n\n  _next(value) {\n    this.buffer.push(value);\n  }\n\n  _complete() {\n    const buffer = this.buffer;\n\n    if (buffer) {\n      this.destination.next(buffer);\n    }\n\n    super._complete();\n  }\n\n  _unsubscribe() {\n    this.buffer = undefined;\n    this.subscribing = false;\n  }\n\n  notifyNext() {\n    this.openBuffer();\n  }\n\n  notifyComplete() {\n    if (this.subscribing) {\n      this.complete();\n    } else {\n      this.openBuffer();\n    }\n  }\n\n  openBuffer() {\n    let {\n      closingSubscription\n    } = this;\n\n    if (closingSubscription) {\n      this.remove(closingSubscription);\n      closingSubscription.unsubscribe();\n    }\n\n    const buffer = this.buffer;\n\n    if (this.buffer) {\n      this.destination.next(buffer);\n    }\n\n    this.buffer = [];\n    let closingNotifier;\n\n    try {\n      const {\n        closingSelector\n      } = this;\n      closingNotifier = closingSelector();\n    } catch (err) {\n      return this.error(err);\n    }\n\n    closingSubscription = new Subscription();\n    this.closingSubscription = closingSubscription;\n    this.add(closingSubscription);\n    this.subscribing = true;\n    closingSubscription.add(innerSubscribe(closingNotifier, new SimpleInnerSubscriber(this)));\n    this.subscribing = false;\n  }\n\n}", "map": {"version": 3, "names": ["Subscription", "SimpleOuterSubscriber", "innerSubscribe", "SimpleInnerSubscriber", "bufferWhen", "closingSelector", "source", "lift", "BufferWhenOperator", "constructor", "call", "subscriber", "subscribe", "BufferWhenSubscriber", "destination", "subscribing", "openBuffer", "_next", "value", "buffer", "push", "_complete", "next", "_unsubscribe", "undefined", "notifyNext", "notifyComplete", "complete", "closingSubscription", "remove", "unsubscribe", "closingNotifier", "err", "error", "add"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/bufferWhen.js"], "sourcesContent": ["import { Subscription } from '../Subscription';\nimport { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function bufferWhen(closingSelector) {\n    return function (source) {\n        return source.lift(new <PERSON><PERSON>er<PERSON>henOperator(closingSelector));\n    };\n}\nclass BufferWhenOperator {\n    constructor(closingSelector) {\n        this.closingSelector = closingSelector;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new BufferWhenSubscriber(subscriber, this.closingSelector));\n    }\n}\nclass Buffer<PERSON>henSubscriber extends SimpleOuterSubscriber {\n    constructor(destination, closingSelector) {\n        super(destination);\n        this.closingSelector = closingSelector;\n        this.subscribing = false;\n        this.openBuffer();\n    }\n    _next(value) {\n        this.buffer.push(value);\n    }\n    _complete() {\n        const buffer = this.buffer;\n        if (buffer) {\n            this.destination.next(buffer);\n        }\n        super._complete();\n    }\n    _unsubscribe() {\n        this.buffer = undefined;\n        this.subscribing = false;\n    }\n    notifyNext() {\n        this.openBuffer();\n    }\n    notifyComplete() {\n        if (this.subscribing) {\n            this.complete();\n        }\n        else {\n            this.openBuffer();\n        }\n    }\n    openBuffer() {\n        let { closingSubscription } = this;\n        if (closingSubscription) {\n            this.remove(closingSubscription);\n            closingSubscription.unsubscribe();\n        }\n        const buffer = this.buffer;\n        if (this.buffer) {\n            this.destination.next(buffer);\n        }\n        this.buffer = [];\n        let closingNotifier;\n        try {\n            const { closingSelector } = this;\n            closingNotifier = closingSelector();\n        }\n        catch (err) {\n            return this.error(err);\n        }\n        closingSubscription = new Subscription();\n        this.closingSubscription = closingSubscription;\n        this.add(closingSubscription);\n        this.subscribing = true;\n        closingSubscription.add(innerSubscribe(closingNotifier, new SimpleInnerSubscriber(this)));\n        this.subscribing = false;\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAT,QAA6B,iBAA7B;AACA,SAASC,qBAAT,EAAgCC,cAAhC,EAAgDC,qBAAhD,QAA6E,mBAA7E;AACA,OAAO,SAASC,UAAT,CAAoBC,eAApB,EAAqC;EACxC,OAAO,UAAUC,MAAV,EAAkB;IACrB,OAAOA,MAAM,CAACC,IAAP,CAAY,IAAIC,kBAAJ,CAAuBH,eAAvB,CAAZ,CAAP;EACH,CAFD;AAGH;;AACD,MAAMG,kBAAN,CAAyB;EACrBC,WAAW,CAACJ,eAAD,EAAkB;IACzB,KAAKA,eAAL,GAAuBA,eAAvB;EACH;;EACDK,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,oBAAJ,CAAyBF,UAAzB,EAAqC,KAAKN,eAA1C,CAAjB,CAAP;EACH;;AANoB;;AAQzB,MAAMQ,oBAAN,SAAmCZ,qBAAnC,CAAyD;EACrDQ,WAAW,CAACK,WAAD,EAAcT,eAAd,EAA+B;IACtC,MAAMS,WAAN;IACA,KAAKT,eAAL,GAAuBA,eAAvB;IACA,KAAKU,WAAL,GAAmB,KAAnB;IACA,KAAKC,UAAL;EACH;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACT,KAAKC,MAAL,CAAYC,IAAZ,CAAiBF,KAAjB;EACH;;EACDG,SAAS,GAAG;IACR,MAAMF,MAAM,GAAG,KAAKA,MAApB;;IACA,IAAIA,MAAJ,EAAY;MACR,KAAKL,WAAL,CAAiBQ,IAAjB,CAAsBH,MAAtB;IACH;;IACD,MAAME,SAAN;EACH;;EACDE,YAAY,GAAG;IACX,KAAKJ,MAAL,GAAcK,SAAd;IACA,KAAKT,WAAL,GAAmB,KAAnB;EACH;;EACDU,UAAU,GAAG;IACT,KAAKT,UAAL;EACH;;EACDU,cAAc,GAAG;IACb,IAAI,KAAKX,WAAT,EAAsB;MAClB,KAAKY,QAAL;IACH,CAFD,MAGK;MACD,KAAKX,UAAL;IACH;EACJ;;EACDA,UAAU,GAAG;IACT,IAAI;MAAEY;IAAF,IAA0B,IAA9B;;IACA,IAAIA,mBAAJ,EAAyB;MACrB,KAAKC,MAAL,CAAYD,mBAAZ;MACAA,mBAAmB,CAACE,WAApB;IACH;;IACD,MAAMX,MAAM,GAAG,KAAKA,MAApB;;IACA,IAAI,KAAKA,MAAT,EAAiB;MACb,KAAKL,WAAL,CAAiBQ,IAAjB,CAAsBH,MAAtB;IACH;;IACD,KAAKA,MAAL,GAAc,EAAd;IACA,IAAIY,eAAJ;;IACA,IAAI;MACA,MAAM;QAAE1B;MAAF,IAAsB,IAA5B;MACA0B,eAAe,GAAG1B,eAAe,EAAjC;IACH,CAHD,CAIA,OAAO2B,GAAP,EAAY;MACR,OAAO,KAAKC,KAAL,CAAWD,GAAX,CAAP;IACH;;IACDJ,mBAAmB,GAAG,IAAI5B,YAAJ,EAAtB;IACA,KAAK4B,mBAAL,GAA2BA,mBAA3B;IACA,KAAKM,GAAL,CAASN,mBAAT;IACA,KAAKb,WAAL,GAAmB,IAAnB;IACAa,mBAAmB,CAACM,GAApB,CAAwBhC,cAAc,CAAC6B,eAAD,EAAkB,IAAI5B,qBAAJ,CAA0B,IAA1B,CAAlB,CAAtC;IACA,KAAKY,WAAL,GAAmB,KAAnB;EACH;;AAzDoD"}, "metadata": {}, "sourceType": "module"}