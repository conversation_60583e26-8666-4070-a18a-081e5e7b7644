{"ast": null, "code": "const ObjectUnsubscribedErrorImpl = (() => {\n  function ObjectUnsubscribedErrorImpl() {\n    Error.call(this);\n    this.message = 'object unsubscribed';\n    this.name = 'ObjectUnsubscribedError';\n    return this;\n  }\n\n  ObjectUnsubscribedErrorImpl.prototype = Object.create(Error.prototype);\n  return ObjectUnsubscribedErrorImpl;\n})();\n\nexport const ObjectUnsubscribedError = ObjectUnsubscribedErrorImpl;", "map": {"version": 3, "names": ["ObjectUnsubscribedErrorImpl", "Error", "call", "message", "name", "prototype", "Object", "create", "ObjectUnsubscribedError"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/ObjectUnsubscribedError.js"], "sourcesContent": ["const ObjectUnsubscribedErrorImpl = (() => {\n    function ObjectUnsubscribedErrorImpl() {\n        Error.call(this);\n        this.message = 'object unsubscribed';\n        this.name = 'ObjectUnsubscribedError';\n        return this;\n    }\n    ObjectUnsubscribedErrorImpl.prototype = Object.create(Error.prototype);\n    return ObjectUnsubscribedErrorImpl;\n})();\nexport const ObjectUnsubscribedError = ObjectUnsubscribedErrorImpl;\n"], "mappings": "AAAA,MAAMA,2BAA2B,GAAG,CAAC,MAAM;EACvC,SAASA,2BAAT,GAAuC;IACnCC,KAAK,CAACC,IAAN,CAAW,IAAX;IACA,KAAKC,OAAL,GAAe,qBAAf;IACA,KAAKC,IAAL,GAAY,yBAAZ;IACA,OAAO,IAAP;EACH;;EACDJ,2BAA2B,CAACK,SAA5B,GAAwCC,MAAM,CAACC,MAAP,CAAcN,KAAK,CAACI,SAApB,CAAxC;EACA,OAAOL,2BAAP;AACH,CATmC,GAApC;;AAUA,OAAO,MAAMQ,uBAAuB,GAAGR,2BAAhC"}, "metadata": {}, "sourceType": "module"}