{"ast": null, "code": "import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "map": {"version": 3, "names": ["getWindow", "isElement", "node", "OwnElement", "Element", "isHTMLElement", "HTMLElement", "isShadowRoot", "ShadowRoot"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/dom-utils/instanceOf.js"], "sourcesContent": ["import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,gBAAtB;;AAEA,SAASC,SAAT,CAAmBC,IAAnB,EAAyB;EACvB,IAAIC,UAAU,GAAGH,SAAS,CAACE,IAAD,CAAT,CAAgBE,OAAjC;EACA,OAAOF,IAAI,YAAYC,UAAhB,IAA8BD,IAAI,YAAYE,OAArD;AACD;;AAED,SAASC,aAAT,CAAuBH,IAAvB,EAA6B;EAC3B,IAAIC,UAAU,GAAGH,SAAS,CAACE,IAAD,CAAT,CAAgBI,WAAjC;EACA,OAAOJ,IAAI,YAAYC,UAAhB,IAA8BD,IAAI,YAAYI,WAArD;AACD;;AAED,SAASC,YAAT,CAAsBL,IAAtB,EAA4B;EAC1B;EACA,IAAI,OAAOM,UAAP,KAAsB,WAA1B,EAAuC;IACrC,OAAO,KAAP;EACD;;EAED,IAAIL,UAAU,GAAGH,SAAS,CAACE,IAAD,CAAT,CAAgBM,UAAjC;EACA,OAAON,IAAI,YAAYC,UAAhB,IAA8BD,IAAI,YAAYM,UAArD;AACD;;AAED,SAASP,SAAT,EAAoBI,aAApB,EAAmCE,YAAnC"}, "metadata": {}, "sourceType": "module"}