{"ast": null, "code": "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "map": {"version": 3, "names": ["rectToClientRect", "rect", "Object", "assign", "left", "x", "top", "y", "right", "width", "bottom", "height"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/utils/rectToClientRect.js"], "sourcesContent": ["export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}"], "mappings": "AAAA,eAAe,SAASA,gBAAT,CAA0BC,IAA1B,EAAgC;EAC7C,OAAOC,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBF,IAAlB,EAAwB;IAC7BG,IAAI,EAAEH,IAAI,CAACI,CADkB;IAE7BC,GAAG,EAAEL,IAAI,CAACM,CAFmB;IAG7BC,KAAK,EAAEP,IAAI,CAACI,CAAL,GAASJ,IAAI,CAACQ,KAHQ;IAI7BC,MAAM,EAAET,IAAI,CAACM,CAAL,GAASN,IAAI,CAACU;EAJO,CAAxB,CAAP;AAMD"}, "metadata": {}, "sourceType": "module"}