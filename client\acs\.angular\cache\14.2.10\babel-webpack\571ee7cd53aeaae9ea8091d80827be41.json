{"ast": null, "code": "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "map": {"version": 3, "names": ["max", "Math", "min", "round"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/utils/math.js"], "sourcesContent": ["export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;"], "mappings": "AAAA,OAAO,IAAIA,GAAG,GAAGC,IAAI,CAACD,GAAf;AACP,OAAO,IAAIE,GAAG,GAAGD,IAAI,CAACC,GAAf;AACP,OAAO,IAAIC,KAAK,GAAGF,IAAI,CAACE,KAAjB"}, "metadata": {}, "sourceType": "module"}