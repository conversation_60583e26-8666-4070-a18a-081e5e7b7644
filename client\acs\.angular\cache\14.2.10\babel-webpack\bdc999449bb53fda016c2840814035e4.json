{"ast": null, "code": "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "map": {"version": 3, "names": ["getFreshSideObject", "mergePaddingObject", "paddingObject", "Object", "assign"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/utils/mergePaddingObject.js"], "sourcesContent": ["import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}"], "mappings": "AAAA,OAAOA,kBAAP,MAA+B,yBAA/B;AACA,eAAe,SAASC,kBAAT,CAA4BC,aAA5B,EAA2C;EACxD,OAAOC,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBJ,kBAAkB,EAApC,EAAwCE,aAAxC,CAAP;AACD"}, "metadata": {}, "sourceType": "module"}