{"ast": null, "code": "import { AsyncAction } from './AsyncAction';\nimport { AsyncScheduler } from './AsyncScheduler';\nexport class VirtualTimeScheduler extends AsyncScheduler {\n  constructor(SchedulerAction = VirtualAction, maxFrames = Number.POSITIVE_INFINITY) {\n    super(SchedulerAction, () => this.frame);\n    this.maxFrames = maxFrames;\n    this.frame = 0;\n    this.index = -1;\n  }\n\n  flush() {\n    const {\n      actions,\n      maxFrames\n    } = this;\n    let error, action;\n\n    while ((action = actions[0]) && action.delay <= maxFrames) {\n      actions.shift();\n      this.frame = action.delay;\n\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    }\n\n    if (error) {\n      while (action = actions.shift()) {\n        action.unsubscribe();\n      }\n\n      throw error;\n    }\n  }\n\n}\nVirtualTimeScheduler.frameTimeFactor = 10;\nexport class VirtualAction extends AsyncAction {\n  constructor(scheduler, work, index = scheduler.index += 1) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n    this.index = index;\n    this.active = true;\n    this.index = scheduler.index = index;\n  }\n\n  schedule(state, delay = 0) {\n    if (!this.id) {\n      return super.schedule(state, delay);\n    }\n\n    this.active = false;\n    const action = new VirtualAction(this.scheduler, this.work);\n    this.add(action);\n    return action.schedule(state, delay);\n  }\n\n  requestAsyncId(scheduler, id, delay = 0) {\n    this.delay = scheduler.frame + delay;\n    const {\n      actions\n    } = scheduler;\n    actions.push(this);\n    actions.sort(VirtualAction.sortActions);\n    return true;\n  }\n\n  recycleAsyncId(scheduler, id, delay = 0) {\n    return undefined;\n  }\n\n  _execute(state, delay) {\n    if (this.active === true) {\n      return super._execute(state, delay);\n    }\n  }\n\n  static sortActions(a, b) {\n    if (a.delay === b.delay) {\n      if (a.index === b.index) {\n        return 0;\n      } else if (a.index > b.index) {\n        return 1;\n      } else {\n        return -1;\n      }\n    } else if (a.delay > b.delay) {\n      return 1;\n    } else {\n      return -1;\n    }\n  }\n\n}", "map": {"version": 3, "names": ["AsyncAction", "AsyncScheduler", "VirtualTimeScheduler", "constructor", "SchedulerAction", "VirtualAction", "maxFrames", "Number", "POSITIVE_INFINITY", "frame", "index", "flush", "actions", "error", "action", "delay", "shift", "execute", "state", "unsubscribe", "frameTimeFactor", "scheduler", "work", "active", "schedule", "id", "add", "requestAsyncId", "push", "sort", "sortActions", "recycleAsyncId", "undefined", "_execute", "a", "b"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/scheduler/VirtualTimeScheduler.js"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nimport { AsyncScheduler } from './AsyncScheduler';\nexport class VirtualTimeScheduler extends AsyncScheduler {\n    constructor(SchedulerAction = VirtualAction, maxFrames = Number.POSITIVE_INFINITY) {\n        super(SchedulerAction, () => this.frame);\n        this.maxFrames = maxFrames;\n        this.frame = 0;\n        this.index = -1;\n    }\n    flush() {\n        const { actions, maxFrames } = this;\n        let error, action;\n        while ((action = actions[0]) && action.delay <= maxFrames) {\n            actions.shift();\n            this.frame = action.delay;\n            if (error = action.execute(action.state, action.delay)) {\n                break;\n            }\n        }\n        if (error) {\n            while (action = actions.shift()) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    }\n}\nVirtualTimeScheduler.frameTimeFactor = 10;\nexport class VirtualAction extends AsyncAction {\n    constructor(scheduler, work, index = scheduler.index += 1) {\n        super(scheduler, work);\n        this.scheduler = scheduler;\n        this.work = work;\n        this.index = index;\n        this.active = true;\n        this.index = scheduler.index = index;\n    }\n    schedule(state, delay = 0) {\n        if (!this.id) {\n            return super.schedule(state, delay);\n        }\n        this.active = false;\n        const action = new VirtualAction(this.scheduler, this.work);\n        this.add(action);\n        return action.schedule(state, delay);\n    }\n    requestAsyncId(scheduler, id, delay = 0) {\n        this.delay = scheduler.frame + delay;\n        const { actions } = scheduler;\n        actions.push(this);\n        actions.sort(VirtualAction.sortActions);\n        return true;\n    }\n    recycleAsyncId(scheduler, id, delay = 0) {\n        return undefined;\n    }\n    _execute(state, delay) {\n        if (this.active === true) {\n            return super._execute(state, delay);\n        }\n    }\n    static sortActions(a, b) {\n        if (a.delay === b.delay) {\n            if (a.index === b.index) {\n                return 0;\n            }\n            else if (a.index > b.index) {\n                return 1;\n            }\n            else {\n                return -1;\n            }\n        }\n        else if (a.delay > b.delay) {\n            return 1;\n        }\n        else {\n            return -1;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAT,QAA4B,eAA5B;AACA,SAASC,cAAT,QAA+B,kBAA/B;AACA,OAAO,MAAMC,oBAAN,SAAmCD,cAAnC,CAAkD;EACrDE,WAAW,CAACC,eAAe,GAAGC,aAAnB,EAAkCC,SAAS,GAAGC,MAAM,CAACC,iBAArD,EAAwE;IAC/E,MAAMJ,eAAN,EAAuB,MAAM,KAAKK,KAAlC;IACA,KAAKH,SAAL,GAAiBA,SAAjB;IACA,KAAKG,KAAL,GAAa,CAAb;IACA,KAAKC,KAAL,GAAa,CAAC,CAAd;EACH;;EACDC,KAAK,GAAG;IACJ,MAAM;MAAEC,OAAF;MAAWN;IAAX,IAAyB,IAA/B;IACA,IAAIO,KAAJ,EAAWC,MAAX;;IACA,OAAO,CAACA,MAAM,GAAGF,OAAO,CAAC,CAAD,CAAjB,KAAyBE,MAAM,CAACC,KAAP,IAAgBT,SAAhD,EAA2D;MACvDM,OAAO,CAACI,KAAR;MACA,KAAKP,KAAL,GAAaK,MAAM,CAACC,KAApB;;MACA,IAAIF,KAAK,GAAGC,MAAM,CAACG,OAAP,CAAeH,MAAM,CAACI,KAAtB,EAA6BJ,MAAM,CAACC,KAApC,CAAZ,EAAwD;QACpD;MACH;IACJ;;IACD,IAAIF,KAAJ,EAAW;MACP,OAAOC,MAAM,GAAGF,OAAO,CAACI,KAAR,EAAhB,EAAiC;QAC7BF,MAAM,CAACK,WAAP;MACH;;MACD,MAAMN,KAAN;IACH;EACJ;;AAvBoD;AAyBzDX,oBAAoB,CAACkB,eAArB,GAAuC,EAAvC;AACA,OAAO,MAAMf,aAAN,SAA4BL,WAA5B,CAAwC;EAC3CG,WAAW,CAACkB,SAAD,EAAYC,IAAZ,EAAkBZ,KAAK,GAAGW,SAAS,CAACX,KAAV,IAAmB,CAA7C,EAAgD;IACvD,MAAMW,SAAN,EAAiBC,IAAjB;IACA,KAAKD,SAAL,GAAiBA,SAAjB;IACA,KAAKC,IAAL,GAAYA,IAAZ;IACA,KAAKZ,KAAL,GAAaA,KAAb;IACA,KAAKa,MAAL,GAAc,IAAd;IACA,KAAKb,KAAL,GAAaW,SAAS,CAACX,KAAV,GAAkBA,KAA/B;EACH;;EACDc,QAAQ,CAACN,KAAD,EAAQH,KAAK,GAAG,CAAhB,EAAmB;IACvB,IAAI,CAAC,KAAKU,EAAV,EAAc;MACV,OAAO,MAAMD,QAAN,CAAeN,KAAf,EAAsBH,KAAtB,CAAP;IACH;;IACD,KAAKQ,MAAL,GAAc,KAAd;IACA,MAAMT,MAAM,GAAG,IAAIT,aAAJ,CAAkB,KAAKgB,SAAvB,EAAkC,KAAKC,IAAvC,CAAf;IACA,KAAKI,GAAL,CAASZ,MAAT;IACA,OAAOA,MAAM,CAACU,QAAP,CAAgBN,KAAhB,EAAuBH,KAAvB,CAAP;EACH;;EACDY,cAAc,CAACN,SAAD,EAAYI,EAAZ,EAAgBV,KAAK,GAAG,CAAxB,EAA2B;IACrC,KAAKA,KAAL,GAAaM,SAAS,CAACZ,KAAV,GAAkBM,KAA/B;IACA,MAAM;MAAEH;IAAF,IAAcS,SAApB;IACAT,OAAO,CAACgB,IAAR,CAAa,IAAb;IACAhB,OAAO,CAACiB,IAAR,CAAaxB,aAAa,CAACyB,WAA3B;IACA,OAAO,IAAP;EACH;;EACDC,cAAc,CAACV,SAAD,EAAYI,EAAZ,EAAgBV,KAAK,GAAG,CAAxB,EAA2B;IACrC,OAAOiB,SAAP;EACH;;EACDC,QAAQ,CAACf,KAAD,EAAQH,KAAR,EAAe;IACnB,IAAI,KAAKQ,MAAL,KAAgB,IAApB,EAA0B;MACtB,OAAO,MAAMU,QAAN,CAAef,KAAf,EAAsBH,KAAtB,CAAP;IACH;EACJ;;EACiB,OAAXe,WAAW,CAACI,CAAD,EAAIC,CAAJ,EAAO;IACrB,IAAID,CAAC,CAACnB,KAAF,KAAYoB,CAAC,CAACpB,KAAlB,EAAyB;MACrB,IAAImB,CAAC,CAACxB,KAAF,KAAYyB,CAAC,CAACzB,KAAlB,EAAyB;QACrB,OAAO,CAAP;MACH,CAFD,MAGK,IAAIwB,CAAC,CAACxB,KAAF,GAAUyB,CAAC,CAACzB,KAAhB,EAAuB;QACxB,OAAO,CAAP;MACH,CAFI,MAGA;QACD,OAAO,CAAC,CAAR;MACH;IACJ,CAVD,MAWK,IAAIwB,CAAC,CAACnB,KAAF,GAAUoB,CAAC,CAACpB,KAAhB,EAAuB;MACxB,OAAO,CAAP;IACH,CAFI,MAGA;MACD,OAAO,CAAC,CAAR;IACH;EACJ;;AAnD0C"}, "metadata": {}, "sourceType": "module"}