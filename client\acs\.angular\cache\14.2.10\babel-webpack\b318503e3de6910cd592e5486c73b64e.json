{"ast": null, "code": "import { mergeMap } from './mergeMap';\nexport function mergeMapTo(innerObservable, resultSelector, concurrent = Number.POSITIVE_INFINITY) {\n  if (typeof resultSelector === 'function') {\n    return mergeMap(() => innerObservable, resultSelector, concurrent);\n  }\n\n  if (typeof resultSelector === 'number') {\n    concurrent = resultSelector;\n  }\n\n  return mergeMap(() => innerObservable, concurrent);\n}", "map": {"version": 3, "names": ["mergeMap", "mergeMapTo", "innerObservable", "resultSelector", "concurrent", "Number", "POSITIVE_INFINITY"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/mergeMapTo.js"], "sourcesContent": ["import { mergeMap } from './mergeMap';\nexport function mergeMapTo(innerObservable, resultSelector, concurrent = Number.POSITIVE_INFINITY) {\n    if (typeof resultSelector === 'function') {\n        return mergeMap(() => innerObservable, resultSelector, concurrent);\n    }\n    if (typeof resultSelector === 'number') {\n        concurrent = resultSelector;\n    }\n    return mergeMap(() => innerObservable, concurrent);\n}\n"], "mappings": "AAAA,SAASA,QAAT,QAAyB,YAAzB;AACA,OAAO,SAASC,UAAT,CAAoBC,eAApB,EAAqCC,cAArC,EAAqDC,UAAU,GAAGC,MAAM,CAACC,iBAAzE,EAA4F;EAC/F,IAAI,OAAOH,cAAP,KAA0B,UAA9B,EAA0C;IACtC,OAAOH,QAAQ,CAAC,MAAME,eAAP,EAAwBC,cAAxB,EAAwCC,UAAxC,CAAf;EACH;;EACD,IAAI,OAAOD,cAAP,KAA0B,QAA9B,EAAwC;IACpCC,UAAU,GAAGD,cAAb;EACH;;EACD,OAAOH,QAAQ,CAAC,MAAME,eAAP,EAAwBE,UAAxB,CAAf;AACH"}, "metadata": {}, "sourceType": "module"}