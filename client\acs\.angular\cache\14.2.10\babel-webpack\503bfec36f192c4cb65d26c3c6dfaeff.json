{"ast": null, "code": "import { Observable } from '../Observable';\nimport { from } from './from';\nimport { EMPTY } from './empty';\nexport function using(resourceFactory, observableFactory) {\n  return new Observable(subscriber => {\n    let resource;\n\n    try {\n      resource = resourceFactory();\n    } catch (err) {\n      subscriber.error(err);\n      return undefined;\n    }\n\n    let result;\n\n    try {\n      result = observableFactory(resource);\n    } catch (err) {\n      subscriber.error(err);\n      return undefined;\n    }\n\n    const source = result ? from(result) : EMPTY;\n    const subscription = source.subscribe(subscriber);\n    return () => {\n      subscription.unsubscribe();\n\n      if (resource) {\n        resource.unsubscribe();\n      }\n    };\n  });\n}", "map": {"version": 3, "names": ["Observable", "from", "EMPTY", "using", "resourceFactory", "observableFactory", "subscriber", "resource", "err", "error", "undefined", "result", "source", "subscription", "subscribe", "unsubscribe"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/using.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { from } from './from';\nimport { EMPTY } from './empty';\nexport function using(resourceFactory, observableFactory) {\n    return new Observable(subscriber => {\n        let resource;\n        try {\n            resource = resourceFactory();\n        }\n        catch (err) {\n            subscriber.error(err);\n            return undefined;\n        }\n        let result;\n        try {\n            result = observableFactory(resource);\n        }\n        catch (err) {\n            subscriber.error(err);\n            return undefined;\n        }\n        const source = result ? from(result) : EMPTY;\n        const subscription = source.subscribe(subscriber);\n        return () => {\n            subscription.unsubscribe();\n            if (resource) {\n                resource.unsubscribe();\n            }\n        };\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,IAAT,QAAqB,QAArB;AACA,SAASC,KAAT,QAAsB,SAAtB;AACA,OAAO,SAASC,KAAT,CAAeC,eAAf,EAAgCC,iBAAhC,EAAmD;EACtD,OAAO,IAAIL,UAAJ,CAAeM,UAAU,IAAI;IAChC,IAAIC,QAAJ;;IACA,IAAI;MACAA,QAAQ,GAAGH,eAAe,EAA1B;IACH,CAFD,CAGA,OAAOI,GAAP,EAAY;MACRF,UAAU,CAACG,KAAX,CAAiBD,GAAjB;MACA,OAAOE,SAAP;IACH;;IACD,IAAIC,MAAJ;;IACA,IAAI;MACAA,MAAM,GAAGN,iBAAiB,CAACE,QAAD,CAA1B;IACH,CAFD,CAGA,OAAOC,GAAP,EAAY;MACRF,UAAU,CAACG,KAAX,CAAiBD,GAAjB;MACA,OAAOE,SAAP;IACH;;IACD,MAAME,MAAM,GAAGD,MAAM,GAAGV,IAAI,CAACU,MAAD,CAAP,GAAkBT,KAAvC;IACA,MAAMW,YAAY,GAAGD,MAAM,CAACE,SAAP,CAAiBR,UAAjB,CAArB;IACA,OAAO,MAAM;MACTO,YAAY,CAACE,WAAb;;MACA,IAAIR,QAAJ,EAAc;QACVA,QAAQ,CAACQ,WAAT;MACH;IACJ,CALD;EAMH,CAzBM,CAAP;AA0BH"}, "metadata": {}, "sourceType": "module"}