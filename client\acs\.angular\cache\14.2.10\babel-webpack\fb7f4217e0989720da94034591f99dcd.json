{"ast": null, "code": "import { Subject } from '../Subject';\nimport { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function repeatWhen(notifier) {\n  return source => source.lift(new RepeatWhenOperator(notifier));\n}\n\nclass RepeatWhenOperator {\n  constructor(notifier) {\n    this.notifier = notifier;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new RepeatWhenSubscriber(subscriber, this.notifier, source));\n  }\n\n}\n\nclass RepeatWhenSubscriber extends SimpleOuterSubscriber {\n  constructor(destination, notifier, source) {\n    super(destination);\n    this.notifier = notifier;\n    this.source = source;\n    this.sourceIsBeingSubscribedTo = true;\n  }\n\n  notifyNext() {\n    this.sourceIsBeingSubscribedTo = true;\n    this.source.subscribe(this);\n  }\n\n  notifyComplete() {\n    if (this.sourceIsBeingSubscribedTo === false) {\n      return super.complete();\n    }\n  }\n\n  complete() {\n    this.sourceIsBeingSubscribedTo = false;\n\n    if (!this.isStopped) {\n      if (!this.retries) {\n        this.subscribeToRetries();\n      }\n\n      if (!this.retriesSubscription || this.retriesSubscription.closed) {\n        return super.complete();\n      }\n\n      this._unsubscribeAndRecycle();\n\n      this.notifications.next(undefined);\n    }\n  }\n\n  _unsubscribe() {\n    const {\n      notifications,\n      retriesSubscription\n    } = this;\n\n    if (notifications) {\n      notifications.unsubscribe();\n      this.notifications = undefined;\n    }\n\n    if (retriesSubscription) {\n      retriesSubscription.unsubscribe();\n      this.retriesSubscription = undefined;\n    }\n\n    this.retries = undefined;\n  }\n\n  _unsubscribeAndRecycle() {\n    const {\n      _unsubscribe\n    } = this;\n    this._unsubscribe = null;\n\n    super._unsubscribeAndRecycle();\n\n    this._unsubscribe = _unsubscribe;\n    return this;\n  }\n\n  subscribeToRetries() {\n    this.notifications = new Subject();\n    let retries;\n\n    try {\n      const {\n        notifier\n      } = this;\n      retries = notifier(this.notifications);\n    } catch (e) {\n      return super.complete();\n    }\n\n    this.retries = retries;\n    this.retriesSubscription = innerSubscribe(retries, new SimpleInnerSubscriber(this));\n  }\n\n}", "map": {"version": 3, "names": ["Subject", "SimpleOuterSubscriber", "innerSubscribe", "SimpleInnerSubscriber", "repeatWhen", "notifier", "source", "lift", "RepeatWhenOperator", "constructor", "call", "subscriber", "subscribe", "RepeatWhenSubscriber", "destination", "sourceIsBeingSubscribedTo", "notifyNext", "notifyComplete", "complete", "isStopped", "retries", "subscribeToRetries", "retriesSubscription", "closed", "_unsubscribeAndRecycle", "notifications", "next", "undefined", "_unsubscribe", "unsubscribe", "e"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/repeatWhen.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function repeatWhen(notifier) {\n    return (source) => source.lift(new RepeatWhenOperator(notifier));\n}\nclass RepeatWhenOperator {\n    constructor(notifier) {\n        this.notifier = notifier;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new RepeatWhenSubscriber(subscriber, this.notifier, source));\n    }\n}\nclass RepeatWhenSubscriber extends SimpleOuterSubscriber {\n    constructor(destination, notifier, source) {\n        super(destination);\n        this.notifier = notifier;\n        this.source = source;\n        this.sourceIsBeingSubscribedTo = true;\n    }\n    notifyNext() {\n        this.sourceIsBeingSubscribedTo = true;\n        this.source.subscribe(this);\n    }\n    notifyComplete() {\n        if (this.sourceIsBeingSubscribedTo === false) {\n            return super.complete();\n        }\n    }\n    complete() {\n        this.sourceIsBeingSubscribedTo = false;\n        if (!this.isStopped) {\n            if (!this.retries) {\n                this.subscribeToRetries();\n            }\n            if (!this.retriesSubscription || this.retriesSubscription.closed) {\n                return super.complete();\n            }\n            this._unsubscribeAndRecycle();\n            this.notifications.next(undefined);\n        }\n    }\n    _unsubscribe() {\n        const { notifications, retriesSubscription } = this;\n        if (notifications) {\n            notifications.unsubscribe();\n            this.notifications = undefined;\n        }\n        if (retriesSubscription) {\n            retriesSubscription.unsubscribe();\n            this.retriesSubscription = undefined;\n        }\n        this.retries = undefined;\n    }\n    _unsubscribeAndRecycle() {\n        const { _unsubscribe } = this;\n        this._unsubscribe = null;\n        super._unsubscribeAndRecycle();\n        this._unsubscribe = _unsubscribe;\n        return this;\n    }\n    subscribeToRetries() {\n        this.notifications = new Subject();\n        let retries;\n        try {\n            const { notifier } = this;\n            retries = notifier(this.notifications);\n        }\n        catch (e) {\n            return super.complete();\n        }\n        this.retries = retries;\n        this.retriesSubscription = innerSubscribe(retries, new SimpleInnerSubscriber(this));\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,YAAxB;AACA,SAASC,qBAAT,EAAgCC,cAAhC,EAAgDC,qBAAhD,QAA6E,mBAA7E;AACA,OAAO,SAASC,UAAT,CAAoBC,QAApB,EAA8B;EACjC,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,kBAAJ,CAAuBH,QAAvB,CAAZ,CAAnB;AACH;;AACD,MAAMG,kBAAN,CAAyB;EACrBC,WAAW,CAACJ,QAAD,EAAW;IAClB,KAAKA,QAAL,GAAgBA,QAAhB;EACH;;EACDK,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,oBAAJ,CAAyBF,UAAzB,EAAqC,KAAKN,QAA1C,EAAoDC,MAApD,CAAjB,CAAP;EACH;;AANoB;;AAQzB,MAAMO,oBAAN,SAAmCZ,qBAAnC,CAAyD;EACrDQ,WAAW,CAACK,WAAD,EAAcT,QAAd,EAAwBC,MAAxB,EAAgC;IACvC,MAAMQ,WAAN;IACA,KAAKT,QAAL,GAAgBA,QAAhB;IACA,KAAKC,MAAL,GAAcA,MAAd;IACA,KAAKS,yBAAL,GAAiC,IAAjC;EACH;;EACDC,UAAU,GAAG;IACT,KAAKD,yBAAL,GAAiC,IAAjC;IACA,KAAKT,MAAL,CAAYM,SAAZ,CAAsB,IAAtB;EACH;;EACDK,cAAc,GAAG;IACb,IAAI,KAAKF,yBAAL,KAAmC,KAAvC,EAA8C;MAC1C,OAAO,MAAMG,QAAN,EAAP;IACH;EACJ;;EACDA,QAAQ,GAAG;IACP,KAAKH,yBAAL,GAAiC,KAAjC;;IACA,IAAI,CAAC,KAAKI,SAAV,EAAqB;MACjB,IAAI,CAAC,KAAKC,OAAV,EAAmB;QACf,KAAKC,kBAAL;MACH;;MACD,IAAI,CAAC,KAAKC,mBAAN,IAA6B,KAAKA,mBAAL,CAAyBC,MAA1D,EAAkE;QAC9D,OAAO,MAAML,QAAN,EAAP;MACH;;MACD,KAAKM,sBAAL;;MACA,KAAKC,aAAL,CAAmBC,IAAnB,CAAwBC,SAAxB;IACH;EACJ;;EACDC,YAAY,GAAG;IACX,MAAM;MAAEH,aAAF;MAAiBH;IAAjB,IAAyC,IAA/C;;IACA,IAAIG,aAAJ,EAAmB;MACfA,aAAa,CAACI,WAAd;MACA,KAAKJ,aAAL,GAAqBE,SAArB;IACH;;IACD,IAAIL,mBAAJ,EAAyB;MACrBA,mBAAmB,CAACO,WAApB;MACA,KAAKP,mBAAL,GAA2BK,SAA3B;IACH;;IACD,KAAKP,OAAL,GAAeO,SAAf;EACH;;EACDH,sBAAsB,GAAG;IACrB,MAAM;MAAEI;IAAF,IAAmB,IAAzB;IACA,KAAKA,YAAL,GAAoB,IAApB;;IACA,MAAMJ,sBAAN;;IACA,KAAKI,YAAL,GAAoBA,YAApB;IACA,OAAO,IAAP;EACH;;EACDP,kBAAkB,GAAG;IACjB,KAAKI,aAAL,GAAqB,IAAIzB,OAAJ,EAArB;IACA,IAAIoB,OAAJ;;IACA,IAAI;MACA,MAAM;QAAEf;MAAF,IAAe,IAArB;MACAe,OAAO,GAAGf,QAAQ,CAAC,KAAKoB,aAAN,CAAlB;IACH,CAHD,CAIA,OAAOK,CAAP,EAAU;MACN,OAAO,MAAMZ,QAAN,EAAP;IACH;;IACD,KAAKE,OAAL,GAAeA,OAAf;IACA,KAAKE,mBAAL,GAA2BpB,cAAc,CAACkB,OAAD,EAAU,IAAIjB,qBAAJ,CAA0B,IAA1B,CAAV,CAAzC;EACH;;AA5DoD"}, "metadata": {}, "sourceType": "module"}