{"ast": null, "code": "import { AnimationFrameAction } from './AnimationFrameAction';\nimport { AnimationFrameScheduler } from './AnimationFrameScheduler';\nexport const animationFrameScheduler = new AnimationFrameScheduler(AnimationFrameAction);\nexport const animationFrame = animationFrameScheduler;", "map": {"version": 3, "names": ["AnimationFrameAction", "AnimationFrameScheduler", "animationFrameScheduler", "animationFrame"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/scheduler/animationFrame.js"], "sourcesContent": ["import { AnimationFrameAction } from './AnimationFrameAction';\nimport { AnimationFrameScheduler } from './AnimationFrameScheduler';\nexport const animationFrameScheduler = new AnimationFrameScheduler(AnimationFrameAction);\nexport const animationFrame = animationFrameScheduler;\n"], "mappings": "AAAA,SAASA,oBAAT,QAAqC,wBAArC;AACA,SAASC,uBAAT,QAAwC,2BAAxC;AACA,OAAO,MAAMC,uBAAuB,GAAG,IAAID,uBAAJ,CAA4BD,oBAA5B,CAAhC;AACP,OAAO,MAAMG,cAAc,GAAGD,uBAAvB"}, "metadata": {}, "sourceType": "module"}