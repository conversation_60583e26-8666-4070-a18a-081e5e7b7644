{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./employer-identification-number.component.html?ngResource\";\nimport { Component, ViewChild, Input } from \"@angular/core\";\nimport { Validators, FormGroup, FormControl } from \"@angular/forms\";\nimport { FilingApiService } from \"../../Services/FilingApiService\";\nimport { StateSelectorComponent } from \"src/app/Modules/Shared/Components/formComponents/stateSelector/state-selector.component\";\nimport { CorporationTypeSelectorComponent } from \"src/app/Modules/Shared/Components/formComponents/corporation-type-selector/corporation-type-selector.component\";\nimport { forkJoin } from \"rxjs\";\nimport { EmployerIdnetificationNumber } from \"../../Models/EmployerIdentificationNumber\";\nimport { ActivatedRoute } from \"@angular/router\";\nimport { LoadFilingService } from \"../../Functions/filing-service-load.func\";\nimport { FilingServiceResponseObject } from \"../../Enums/filing-service-enum\";\nimport { FormModeService } from \"src/app/Modules/Shared/Services/Common/FormModeService\";\nimport { CustomSharedValidations } from \"src/app/Modules/Shared/functions/custom-validations\";\nimport { FilingInfoService } from \"../../Services/filing-price.service\";\nimport { OrganizationTypeSelectorComponent } from \"src/app/Modules/Shared/Components/formComponents/organization-type-selector/organization-type-selector.component\";\nimport { PageTitleService } from \"src/app/Modules/Shared/Services/Common/page-title.service\";\nlet EmployerIdentificationNumberComponent = class EmployerIdentificationNumberComponent {\n  constructor(filingPriceService, Api, formMode, activatedRoute, pageTitleService) {\n    this.filingPriceService = filingPriceService;\n    this.Api = Api;\n    this.formMode = formMode;\n    this.activatedRoute = activatedRoute;\n    this.pageTitleService = pageTitleService;\n    this.EntityTypeData = [];\n    this.LLC = \"LC\";\n    this.MemberMarried = [];\n    this.Types = [];\n    this.EmployerIdentificationNo = new EmployerIdnetificationNumber();\n    this.States = [];\n    this.IsUpSelling = false;\n    this.Form = new FormGroup({\n      FormationState: new FormControl(null),\n      Date: new FormControl(null),\n      PrintedName: new FormControl(null),\n      CorporateType: new FormControl(null),\n      EntityType: new FormControl(null),\n      MemberCount: new FormControl(null),\n      IsMembersMarried: new FormControl(null),\n      PrincipalOfficerName: new FormControl(null),\n      SSN_TIN: new FormControl(null),\n      Phone: new FormControl(null, CustomSharedValidations.phoneLength),\n      PhysicalAddress: new FormControl(null),\n      City: new FormControl(null),\n      State: new FormControl(null),\n      Zip: new FormControl(null, [CustomSharedValidations.zipLength]),\n      LegalName: new FormControl(null),\n      TradeName_DBA: new FormControl(null),\n      ClosingMonth: new FormControl(null, [CustomSharedValidations.validateMonth]),\n      IsHighwayMotorTaxable: new FormControl(null),\n      IsFileReturns: new FormControl(null),\n      IsAlcholSeller: new FormControl(null),\n      IsFormW2: new FormControl(null),\n      AgricultureEmployeeCount: new FormControl(null),\n      OtherEmployeeCount: new FormControl(null),\n      PrimaryBusinessActivity: new FormControl(null),\n      IsGambling: new FormControl(null),\n      IsTotalWagesPay: new FormControl(null),\n      Remarks: new FormControl(null),\n      file_date_y: new FormControl(null, [CustomSharedValidations.validateFormationDate(\"file_date_d\", \"file_date_m\", \"file_date_y\")]),\n      file_date_m: new FormControl(null, [CustomSharedValidations.validateFormationDate(\"file_date_d\", \"file_date_m\", \"file_date_y\")]),\n      file_date_d: new FormControl(null, [CustomSharedValidations.validateFormationDate(\"file_date_d\", \"file_date_m\", \"file_date_y\")]),\n      IsThirdPartyAgree: new FormControl(null),\n      DateAnnutiesPaidM: new FormControl(null, [Validators.required, CustomSharedValidations.validateMonth]),\n      DateAnnutiesPaidY: new FormControl(null, [Validators.required, CustomSharedValidations.validateYear]),\n      IsOnlyTwoMember: new FormControl(null),\n      SingleOrMultiMemberValue: new FormControl(null),\n      HowManyMember: new FormControl(null),\n      AreTheyMarried: new FormControl(null)\n    });\n  }\n\n  set _FormRef(FormRef) {\n    this.FormRef = FormRef;\n  }\n\n  getUpSellingData() {\n    setTimeout(() => {\n      this.FormRef.nativeElement.click();\n    }, 10);\n    return new Promise(res => this.UpSellingResolver = res);\n  }\n\n  ngOnInit() {\n    this.filingPriceService.getLabel(this.filingPriceService.SubCatCode.employeeidntificationnumber, 'FOT').subscribe(label => {\n      this.pageTitleService.setPageTitle(label);\n    });\n    this.CorpTypeComponent.Caption = \"Select Type of Entity\";\n    this.Api.FCGetStates().subscribe(States => {\n      this.States = States;\n      this.StateComponent.States = States;\n    });\n    const fillData = forkJoin([this.Api.GetEmployerIdentificationEntity(), this.Api.GetEmployerIdentificationMembers(), this.Api.GetFormationType(\"907\")]);\n    fillData.subscribe(response => {\n      this.MemberMarried = response[1];\n      this.Types = response[2] || [];\n      this.StartLoad();\n    });\n\n    if (!this.IsUpSelling) {\n      this.Form.controls.IsThirdPartyAgree.setValue(false);\n    }\n\n    this.Form.controls.EntityType.valueChanges.subscribe(value => {\n      if (this.IsUpSelling) {\n        this.Form.controls.EntityType.value;\n      }\n    });\n    this.Form.controls.HowManyMember.valueChanges.subscribe(value => {\n      if (this.Form.controls.HowManyMember.value != null && this.Form.controls.HowManyMember.value != 2) {\n        this.Form.controls.AreTheyMarried.setValue(null);\n        this.Form.controls.IsOnlyTwoMember.setValue(null);\n        this.Form.controls.SingleOrMultiMemberValue.setValue(null);\n      }\n    });\n    this.Form.controls.IsThirdPartyAgree.valueChanges.subscribe(value => {\n      this.OrganizationTypeComponent.Caption = \"Select Type\";\n      this.OrganizationTypeComponent.OrganizationTypes = this.Types;\n    });\n    this.Form.controls.CorporateType.valueChanges.subscribe(value => {\n      this.onCorporationTypeUpdate(value);\n    });\n  }\n\n  onCorporationTypeUpdate(corporationTypeCode) {\n    this.Api.GetFormationTypes(\"FS\", \"FOT\", \"907\", corporationTypeCode).subscribe(data => {\n      this.CorpTypeComponent.CorporationTypes = data;\n      this.EntityTypeData = data;\n\n      if (data.length == 1) {\n        this.Form.controls.EntityType.setValue(data[0].corporationTypeCode);\n        console.log(this.Form.controls.EntityType);\n        this.CorpTypeComponent.Caption = \"\";\n      } else if (this.EntityTypeData.length == 0) {\n        this.Form.controls.EntityType.setValue(\"\");\n        this.CorpTypeComponent.Caption = \"\";\n      } else {\n        this.CorpTypeComponent.Caption = \"Select Type of Entity\";\n      }\n    });\n  }\n\n  onSave(event) {\n    try {\n      if (this.Form.controls.IsFormW2.value == \"No\") {\n        this.Form.controls.AgricultureEmployeeCount.clearValidators();\n        this.Form.controls.DateAnnutiesPaidY.clearValidators();\n        this.Form.controls.DateAnnutiesPaidM.clearValidators();\n        this.Form.controls.OtherEmployeeCount.clearValidators();\n        this.Form.updateValueAndValidity();\n      }\n\n      if (this.Form.valid) {\n        this.Save();\n      }\n\n      return false;\n    } catch (ex) {\n      console.error(ex);\n    }\n  }\n\n  StartLoad() {\n    var _this = this;\n\n    this.activatedRoute.queryParams.subscribe( /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (queryString) {\n        LoadFilingService(queryString, _this.Api, FilingServiceResponseObject.EmployerIdentificationNumber, _this.formMode).then(serviceData => {\n          _this.EmployerIdentificationNo = serviceData;\n\n          _this.Load();\n        }).catch(e => console.log(e));\n      });\n\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  }\n\n  Save() {\n    this.EmployerIdentificationNo.FormationState = this.Form.controls.FormationState.value;\n    this.EmployerIdentificationNo.Date = this.Form.controls.Date.value;\n    this.EmployerIdentificationNo.PrintedName = this.Form.controls.PrintedName.value;\n    this.EmployerIdentificationNo.CorporationType = this.Form.controls.CorporateType.value;\n    this.EmployerIdentificationNo.EntityType = this.Form.controls.EntityType.value;\n    this.EmployerIdentificationNo.MemberCount = this.Form.controls.MemberCount.value;\n    this.EmployerIdentificationNo.IsMembersMarried = this.Form.controls.IsMembersMarried.value;\n    this.EmployerIdentificationNo.PrincipalOfficerName = this.Form.controls.PrincipalOfficerName.value;\n    this.EmployerIdentificationNo.SSN_TIN = this.Form.controls.SSN_TIN.value;\n    this.EmployerIdentificationNo.Phone = this.Form.controls.Phone.value;\n    this.EmployerIdentificationNo.PhysicalAddress = this.Form.controls.PhysicalAddress.value;\n    this.EmployerIdentificationNo.City = this.Form.controls.City.value;\n    this.EmployerIdentificationNo.State = this.Form.controls.State.value;\n    this.EmployerIdentificationNo.Zip = this.Form.controls.Zip.value;\n    this.EmployerIdentificationNo.LegalName = this.Form.controls.LegalName.value;\n    this.EmployerIdentificationNo.TradeName_DBA = this.Form.controls.TradeName_DBA.value;\n    this.EmployerIdentificationNo.TradeState = this.Form.controls.FormationState.value;\n    this.EmployerIdentificationNo.ClosingMonth = this.Form.controls.ClosingMonth.value;\n    this.EmployerIdentificationNo.IsHighwayMotorTaxable = this.Form.controls.IsHighwayMotorTaxable.value;\n    this.EmployerIdentificationNo.IsFileReturns = this.Form.controls.IsFileReturns.value;\n    this.EmployerIdentificationNo.IsAlcholSeller = this.Form.controls.IsAlcholSeller.value;\n    this.EmployerIdentificationNo.IsFormW2 = this.Form.controls.IsFormW2.value;\n    this.EmployerIdentificationNo.AgricultureEmployeeCount = this.Form.controls.AgricultureEmployeeCount.value;\n    this.EmployerIdentificationNo.OtherEmployeeCount = this.Form.controls.OtherEmployeeCount.value;\n    this.EmployerIdentificationNo.PrimaryBusinessActivity = this.Form.controls.PrimaryBusinessActivity.value;\n    this.EmployerIdentificationNo.Remarks = this.Form.controls.Remarks.value;\n    this.EmployerIdentificationNo.file_date_y = this.Form.controls.file_date_y.value;\n    this.EmployerIdentificationNo.file_date_m = this.Form.controls.file_date_m.value;\n    this.EmployerIdentificationNo.file_date_d = this.Form.controls.file_date_d.value;\n    this.EmployerIdentificationNo.IsGambling = this.Form.controls.IsGambling.value;\n    this.EmployerIdentificationNo.IsTotalWagesPay = this.Form.controls.IsTotalWagesPay.value;\n    this.EmployerIdentificationNo.DateAnnutiesPaidY = this.Form.controls.DateAnnutiesPaidY.value;\n    this.EmployerIdentificationNo.DateAnnutiesPaidM = this.Form.controls.DateAnnutiesPaidM.value;\n    this.EmployerIdentificationNo.IsOnlyTwoMember = this.Form.controls.IsOnlyTwoMember.value;\n    this.EmployerIdentificationNo.SingleOrMultiMemberValue = this.Form.controls.SingleOrMultiMemberValue.value;\n    this.EmployerIdentificationNo.HowManyMember = this.Form.controls.HowManyMember.value;\n    this.EmployerIdentificationNo.AreTheyMarried = this.Form.controls.AreTheyMarried.value;\n\n    if (this.IsUpSelling) {\n      this.UpSellingResolver(Object.assign({}, this.EmployerIdentificationNo));\n      return;\n    }\n\n    this.Api.SaveFilingService({\n      employerIdentificationNumber: this.EmployerIdentificationNo\n    }).subscribe(x => {\n      this.Form.reset();\n    });\n  }\n\n  Load() {\n    this.EmployerIdentificationNo.SSN_TIN = this.EmployerIdentificationNo[\"SsN_TIN\"];\n    this.EmployerIdentificationNo.file_date_m = this.EmployerIdentificationNo[\"File_date_m\"];\n    this.EmployerIdentificationNo.file_date_d = this.EmployerIdentificationNo[\"File_date_d\"];\n    this.EmployerIdentificationNo.file_date_y = this.EmployerIdentificationNo[\"File_date_y\"];\n    this.Form.controls.FormationState.setValue(this.EmployerIdentificationNo.FormationState);\n\n    if (this.EmployerIdentificationNo.Date) {\n      this.Form.controls.Date.setValue(new Date(this.EmployerIdentificationNo.Date));\n    }\n\n    this.Form.controls.PrintedName.setValue(this.EmployerIdentificationNo.PrintedName);\n    this.Form.controls.CorporateType.setValue(this.EmployerIdentificationNo.CorporationType);\n    this.Form.controls.EntityType.setValue(this.EmployerIdentificationNo.EntityType);\n    this.Form.controls.MemberCount.setValue(this.EmployerIdentificationNo.MemberCount);\n    this.Form.controls.IsMembersMarried.setValue(this.EmployerIdentificationNo.IsMembersMarried);\n    this.Form.controls.PrincipalOfficerName.setValue(this.EmployerIdentificationNo.PrincipalOfficerName);\n    this.Form.controls.SSN_TIN.setValue(this.EmployerIdentificationNo.SSN_TIN);\n    this.Form.controls.Phone.setValue(this.EmployerIdentificationNo.Phone);\n    this.Form.controls.PhysicalAddress.setValue(this.EmployerIdentificationNo.PhysicalAddress);\n    this.Form.controls.City.setValue(this.EmployerIdentificationNo.City);\n    this.Form.controls.State.setValue(this.EmployerIdentificationNo.State);\n    this.Form.controls.Zip.setValue(this.EmployerIdentificationNo.Zip);\n    this.Form.controls.LegalName.setValue(this.EmployerIdentificationNo.LegalName);\n    this.Form.controls.TradeName_DBA.setValue(this.EmployerIdentificationNo.TradeName_DBA);\n    this.Form.controls.Remarks.setValue(this.EmployerIdentificationNo.Remarks);\n    this.Form.controls.file_date_y.setValue(this.EmployerIdentificationNo.file_date_y);\n    this.Form.controls.file_date_d.setValue(this.EmployerIdentificationNo.file_date_d);\n    this.Form.controls.file_date_m.setValue(this.EmployerIdentificationNo.file_date_m);\n    this.Form.controls.ClosingMonth.setValue(this.EmployerIdentificationNo.ClosingMonth);\n    this.Form.controls.IsHighwayMotorTaxable.setValue(this.EmployerIdentificationNo.IsHighwayMotorTaxable);\n    this.Form.controls.IsFileReturns.setValue(this.EmployerIdentificationNo.IsFileReturns);\n    this.Form.controls.IsAlcholSeller.setValue(this.EmployerIdentificationNo.IsAlcholSeller);\n    this.Form.controls.IsFormW2.setValue(this.EmployerIdentificationNo.IsFormW2);\n    this.Form.controls.AgricultureEmployeeCount.setValue(this.EmployerIdentificationNo.AgricultureEmployeeCount);\n    this.Form.controls.OtherEmployeeCount.setValue(this.EmployerIdentificationNo.OtherEmployeeCount);\n    this.Form.controls.PrimaryBusinessActivity.setValue(this.EmployerIdentificationNo.PrimaryBusinessActivity);\n    this.Form.controls.Remarks.setValue(this.EmployerIdentificationNo.Remarks);\n    this.Form.controls.IsGambling.setValue(this.EmployerIdentificationNo.IsGambling);\n    this.Form.controls.IsTotalWagesPay.setValue(this.EmployerIdentificationNo.IsTotalWagesPay);\n    this.Form.controls.DateAnnutiesPaidY.setValue(this.EmployerIdentificationNo.DateAnnutiesPaidY);\n    this.Form.controls.DateAnnutiesPaidM.setValue(this.EmployerIdentificationNo.DateAnnutiesPaidM);\n    this.Form.controls.IsOnlyTwoMember.setValue(this.EmployerIdentificationNo.IsOnlyTwoMember);\n    this.Form.controls.SingleOrMultiMemberValue.setValue(this.EmployerIdentificationNo.SingleOrMultiMemberValue);\n    this.Form.controls.HowManyMember.setValue(this.EmployerIdentificationNo.HowManyMember);\n    this.Form.controls.AreTheyMarried.setValue(this.EmployerIdentificationNo.AreTheyMarried);\n    this.Form.controls.IsThirdPartyAgree.setValue(true);\n  }\n\n};\n\nEmployerIdentificationNumberComponent.ctorParameters = () => [{\n  type: FilingInfoService\n}, {\n  type: FilingApiService\n}, {\n  type: FormModeService\n}, {\n  type: ActivatedRoute\n}, {\n  type: PageTitleService\n}];\n\nEmployerIdentificationNumberComponent.propDecorators = {\n  _FormRef: [{\n    type: ViewChild,\n    args: [\"submitButton\", {\n      static: true\n    }]\n  }],\n  StateComponent: [{\n    type: ViewChild,\n    args: [StateSelectorComponent, {\n      static: true\n    }]\n  }],\n  CorpTypeComponent: [{\n    type: ViewChild,\n    args: [CorporationTypeSelectorComponent, {\n      static: true\n    }]\n  }],\n  OrganizationTypeComponent: [{\n    type: ViewChild,\n    args: [OrganizationTypeSelectorComponent, {\n      static: true\n    }]\n  }],\n  IsUpSelling: [{\n    type: Input\n  }]\n};\nEmployerIdentificationNumberComponent = __decorate([Component({\n  selector: \"employer-identification-number\",\n  template: __NG_CLI_RESOURCE__0\n})], EmployerIdentificationNumberComponent);\nexport { EmployerIdentificationNumberComponent };", "map": {"version": 3, "mappings": ";;;AAAA,SAASA,SAAT,EAA4BC,SAA5B,EAAuCC,KAAvC,QAAgE,eAAhE;AACA,SACEC,UADF,EAEEC,SAFF,EAGEC,WAHF,QAIO,gBAJP;AAKA,SAASC,gBAAT,QAAiC,iCAAjC;AACA,SAASC,sBAAT,QAAuC,yFAAvC;AACA,SAASC,gCAAT,QAAiD,gHAAjD;AACA,SAASC,QAAT,QAAyB,MAAzB;AACA,SAASC,4BAAT,QAA6C,2CAA7C;AACA,SAASC,cAAT,QAA+B,iBAA/B;AACA,SAASC,iBAAT,QAAkC,0CAAlC;AACA,SAASC,2BAAT,QAA4C,iCAA5C;AACA,SAASC,eAAT,QAAgC,wDAAhC;AAEA,SAASC,uBAAT,QAAwC,qDAAxC;AACA,SAASC,iBAAT,QAAkC,qCAAlC;AACA,SAASC,iCAAT,QAAkD,kHAAlD;AACA,SAASC,gBAAT,QAAiC,2DAAjC;IAMaC,qCAAqC,SAArCA,qCAAqC;EAGhDC,YACSC,kBADT,EAEUC,GAFV,EAGSC,QAHT,EAIUC,cAJV,EAKUC,gBALV,EAK4C;IAJnC;IACC;IACD;IACC;IACA;IANV,sBAAwB,EAAxB;IAuBA,WAAM,IAAN;IACA,qBAAuB,EAAvB;IACA,aAAe,EAAf;IACA,gCACE,IAAIf,4BAAJ,EADF;IAEA,cAAgB,EAAhB;IAES,mBAAuB,KAAvB;IAkFT,YAAO,IAAIN,SAAJ,CAAc;MACnBsB,cAAc,EAAE,IAAIrB,WAAJ,CAA+B,IAA/B,CADG;MAEnBsB,IAAI,EAAE,IAAItB,WAAJ,CAA4B,IAA5B,CAFa;MAGnBuB,WAAW,EAAE,IAAIvB,WAAJ,CAA+B,IAA/B,CAHM;MAInBwB,aAAa,EAAE,IAAIxB,WAAJ,CAA4B,IAA5B,CAJI;MAKnByB,UAAU,EAAE,IAAIzB,WAAJ,CAA+B,IAA/B,CALO;MAMnB0B,WAAW,EAAE,IAAI1B,WAAJ,CAA+B,IAA/B,CANM;MAOnB2B,gBAAgB,EAAE,IAAI3B,WAAJ,CAA+B,IAA/B,CAPC;MAQnB4B,oBAAoB,EAAE,IAAI5B,WAAJ,CAA+B,IAA/B,CARH;MASnB6B,OAAO,EAAE,IAAI7B,WAAJ,CAA+B,IAA/B,CATU;MAUnB8B,KAAK,EAAE,IAAI9B,WAAJ,CAA+B,IAA/B,EAAqCU,uBAAuB,CAACqB,WAA7D,CAVY;MAWnBC,eAAe,EAAE,IAAIhC,WAAJ,CAA+B,IAA/B,CAXE;MAYnBiC,IAAI,EAAE,IAAIjC,WAAJ,CAA+B,IAA/B,CAZa;MAanBkC,KAAK,EAAE,IAAIlC,WAAJ,CAA+B,IAA/B,CAbY;MAcnBmC,GAAG,EAAE,IAAInC,WAAJ,CAA+B,IAA/B,EAAqC,CAACU,uBAAuB,CAAC0B,SAAzB,CAArC,CAdc;MAenBC,SAAS,EAAE,IAAIrC,WAAJ,CAA+B,IAA/B,CAfQ;MAgBnBsC,aAAa,EAAE,IAAItC,WAAJ,CAA+B,IAA/B,CAhBI;MAkBnBuC,YAAY,EAAE,IAAIvC,WAAJ,CAA+B,IAA/B,EAAqC,CACjDU,uBAAuB,CAAC8B,aADyB,CAArC,CAlBK;MAqBnBC,qBAAqB,EAAE,IAAIzC,WAAJ,CAA+B,IAA/B,CArBJ;MAsBnB0C,aAAa,EAAE,IAAI1C,WAAJ,CAA+B,IAA/B,CAtBI;MAuBnB2C,cAAc,EAAE,IAAI3C,WAAJ,CAA+B,IAA/B,CAvBG;MAwBnB4C,QAAQ,EAAE,IAAI5C,WAAJ,CAA+B,IAA/B,CAxBS;MAyBnB6C,wBAAwB,EAAE,IAAI7C,WAAJ,CAA+B,IAA/B,CAzBP;MA0BnB8C,kBAAkB,EAAE,IAAI9C,WAAJ,CAA+B,IAA/B,CA1BD;MA2BnB+C,uBAAuB,EAAE,IAAI/C,WAAJ,CAA+B,IAA/B,CA3BN;MA4BnBgD,UAAU,EAAE,IAAIhD,WAAJ,CAA+B,IAA/B,CA5BO;MA6BnBiD,eAAe,EAAE,IAAIjD,WAAJ,CAA+B,IAA/B,CA7BE;MA8BnBkD,OAAO,EAAE,IAAIlD,WAAJ,CAA+B,IAA/B,CA9BU;MA+BnBmD,WAAW,EAAE,IAAInD,WAAJ,CAA4B,IAA5B,EAAkC,CAC7CU,uBAAuB,CAAC0C,qBAAxB,CACE,aADF,EAEE,aAFF,EAGE,aAHF,CAD6C,CAAlC,CA/BM;MAsCnBC,WAAW,EAAE,IAAIrD,WAAJ,CAA4B,IAA5B,EAAkC,CAC7CU,uBAAuB,CAAC0C,qBAAxB,CACE,aADF,EAEE,aAFF,EAGE,aAHF,CAD6C,CAAlC,CAtCM;MA6CnBE,WAAW,EAAE,IAAItD,WAAJ,CAA4B,IAA5B,EAAkC,CAC7CU,uBAAuB,CAAC0C,qBAAxB,CACE,aADF,EAEE,aAFF,EAGE,aAHF,CAD6C,CAAlC,CA7CM;MAoDnBG,iBAAiB,EAAE,IAAIvD,WAAJ,CAA4B,IAA5B,CApDA;MAqDnBwD,iBAAiB,EAAE,IAAIxD,WAAJ,CAA+B,IAA/B,EAAqC,CACtDF,UAAU,CAAC2D,QAD2C,EAEtD/C,uBAAuB,CAAC8B,aAF8B,CAArC,CArDA;MAyDnBkB,iBAAiB,EAAE,IAAI1D,WAAJ,CAA+B,IAA/B,EAAqC,CACtDF,UAAU,CAAC2D,QAD2C,EAEtD/C,uBAAuB,CAACiD,YAF8B,CAArC,CAzDA;MA6DnBC,eAAe,EAAE,IAAI5D,WAAJ,CAA4B,IAA5B,CA7DE;MA8DnB6D,wBAAwB,EAAE,IAAI7D,WAAJ,CAA+B,IAA/B,CA9DP;MA+DnB8D,aAAa,EAAE,IAAI9D,WAAJ,CAA4B,IAA5B,CA/DI;MAgEnB+D,cAAc,EAAE,IAAI/D,WAAJ,CAA4B,IAA5B;IAhEG,CAAd,CAAP;EAzGK;;EAGoD,IAARgE,QAAQ,CACvDC,OADuD,EACpC;IAEnB,KAAKA,OAAL,GAAeA,OAAf;EACD;;EAiBDC,gBAAgB;IACdC,UAAU,CAAC,MAAK;MACd,KAAKF,OAAL,CAAaG,aAAb,CAA2BC,KAA3B;IACD,CAFS,EAEP,EAFO,CAAV;IAGA,OAAO,IAAIC,OAAJ,CAAaC,GAAD,IAAU,KAAKC,iBAAL,GAAyBD,GAA/C,CAAP;EACD;;EACDE,QAAQ;IACN,KAAKzD,kBAAL,CACG0D,QADH,CACY,KAAK1D,kBAAL,CAAwB2D,UAAxB,CAAmCC,2BAD/C,EAC4E,KAD5E,EAEGC,SAFH,CAEaC,KAAK,IAAG;MACjB,KAAK1D,gBAAL,CAAsB2D,YAAtB,CAAmCD,KAAnC;IACD,CAJH;IAMA,KAAKE,iBAAL,CAAuBC,OAAvB,GAAiC,uBAAjC;IACA,KAAKhE,GAAL,CAASiE,WAAT,GAAuBL,SAAvB,CAAkCM,MAAD,IAAW;MAC1C,KAAKA,MAAL,GAAcA,MAAd;MACA,KAAKC,cAAL,CAAoBD,MAApB,GAA6BA,MAA7B;IACD,CAHD;IAMA,MAAME,QAAQ,GAAGjF,QAAQ,CAAC,CACxB,KAAKa,GAAL,CAASqE,+BAAT,EADwB,EAExB,KAAKrE,GAAL,CAASsE,gCAAT,EAFwB,EAGxB,KAAKtE,GAAL,CAASuE,gBAAT,CAA0B,KAA1B,CAHwB,CAAD,CAAzB;IAKAH,QAAQ,CAACR,SAAT,CAAoBY,QAAD,IAAa;MAC9B,KAAKC,aAAL,GAAqBD,QAAQ,CAAC,CAAD,CAA7B;MACA,KAAKE,KAAL,GAAaF,QAAQ,CAAC,CAAD,CAAR,IAAe,EAA5B;MACA,KAAKG,SAAL;IACD,CAJD;;IAKA,IAAI,CAAC,KAAKC,WAAV,EAAuB;MACrB,KAAKC,IAAL,CAAUC,QAAV,CAAmBxC,iBAAnB,CAAqCyC,QAArC,CAA8C,KAA9C;IACD;;IAED,KAAKF,IAAL,CAAUC,QAAV,CAAmBtE,UAAnB,CAA8BwE,YAA9B,CAA2CpB,SAA3C,CAAsDqB,KAAD,IAAU;MAC7D,IAAI,KAAKL,WAAT,EAAsB;QACpB,KAAKC,IAAL,CAAUC,QAAV,CAAmBtE,UAAnB,CAA8ByE,KAA9B;MACD;IACF,CAJD;IAKA,KAAKJ,IAAL,CAAUC,QAAV,CAAmBjC,aAAnB,CAAiCmC,YAAjC,CAA8CpB,SAA9C,CAAyDqB,KAAD,IAAU;MAChE,IAAI,KAAKJ,IAAL,CAAUC,QAAV,CAAmBjC,aAAnB,CAAiCoC,KAAjC,IAA0C,IAA1C,IAAkD,KAAKJ,IAAL,CAAUC,QAAV,CAAmBjC,aAAnB,CAAiCoC,KAAjC,IAA0C,CAAhG,EAAmG;QACjG,KAAKJ,IAAL,CAAUC,QAAV,CAAmBhC,cAAnB,CAAkCiC,QAAlC,CAA2C,IAA3C;QACA,KAAKF,IAAL,CAAUC,QAAV,CAAmBnC,eAAnB,CAAmCoC,QAAnC,CAA4C,IAA5C;QACA,KAAKF,IAAL,CAAUC,QAAV,CAAmBlC,wBAAnB,CAA4CmC,QAA5C,CAAqD,IAArD;MACD;IAEF,CAPD;IASA,KAAKF,IAAL,CAAUC,QAAV,CAAmBxC,iBAAnB,CAAqC0C,YAArC,CAAkDpB,SAAlD,CAA6DqB,KAAD,IAAU;MACpE,KAAKC,yBAAL,CAA+BlB,OAA/B,GAAyC,aAAzC;MACA,KAAKkB,yBAAL,CAA+BC,iBAA/B,GAAmD,KAAKT,KAAxD;IACD,CAHD;IAKA,KAAKG,IAAL,CAAUC,QAAV,CAAmBvE,aAAnB,CAAiCyE,YAAjC,CAA8CpB,SAA9C,CAAyDqB,KAAD,IAAU;MAChE,KAAKG,uBAAL,CAA6BH,KAA7B;IACD,CAFD;EAGD;;EAEDG,uBAAuB,CAACC,mBAAD,EAAoB;IACzC,KAAKrF,GAAL,CAASsF,iBAAT,CACE,IADF,EAEE,KAFF,EAGE,KAHF,EAIED,mBAJF,EAKEzB,SALF,CAKa2B,IAAD,IAAS;MACnB,KAAKxB,iBAAL,CAAuByB,gBAAvB,GAA0CD,IAA1C;MACA,KAAKE,cAAL,GAAsBF,IAAtB;;MAEA,IAAIA,IAAI,CAACG,MAAL,IAAe,CAAnB,EAAsB;QACpB,KAAKb,IAAL,CAAUC,QAAV,CAAmBtE,UAAnB,CAA8BuE,QAA9B,CAAuCQ,IAAI,CAAC,CAAD,CAAJ,CAAQF,mBAA/C;QACAM,OAAO,CAACC,GAAR,CAAY,KAAKf,IAAL,CAAUC,QAAV,CAAmBtE,UAA/B;QACA,KAAKuD,iBAAL,CAAuBC,OAAvB,GAAiC,EAAjC;MACD,CAJD,MAKK,IAAI,KAAKyB,cAAL,CAAoBC,MAApB,IAA8B,CAAlC,EAAqC;QACxC,KAAKb,IAAL,CAAUC,QAAV,CAAmBtE,UAAnB,CAA8BuE,QAA9B,CAAuC,EAAvC;QACA,KAAKhB,iBAAL,CAAuBC,OAAvB,GAAiC,EAAjC;MACD,CAHI,MAGE;QACL,KAAKD,iBAAL,CAAuBC,OAAvB,GAAiC,uBAAjC;MACD;IACF,CApBD;EAqBD;;EAoED6B,MAAM,CAACC,KAAD,EAAM;IACV,IAAI;MACF,IAAI,KAAKjB,IAAL,CAAUC,QAAV,CAAmBnD,QAAnB,CAA4BsD,KAA5B,IAAqC,IAAzC,EAA+C;QAC7C,KAAKJ,IAAL,CAAUC,QAAV,CAAmBlD,wBAAnB,CAA4CmE,eAA5C;QACA,KAAKlB,IAAL,CAAUC,QAAV,CAAmBrC,iBAAnB,CAAqCsD,eAArC;QACA,KAAKlB,IAAL,CAAUC,QAAV,CAAmBvC,iBAAnB,CAAqCwD,eAArC;QACA,KAAKlB,IAAL,CAAUC,QAAV,CAAmBjD,kBAAnB,CAAsCkE,eAAtC;QACA,KAAKlB,IAAL,CAAUmB,sBAAV;MACD;;MAED,IAAI,KAAKnB,IAAL,CAAUoB,KAAd,EAAqB;QACnB,KAAKC,IAAL;MACD;;MAED,OAAO,KAAP;IACD,CAdD,CAcE,OAAOC,EAAP,EAAW;MACXR,OAAO,CAACS,KAAR,CAAcD,EAAd;IACD;EACF;;EAEDxB,SAAS;IAAA;;IACP,KAAKzE,cAAL,CAAoBmG,WAApB,CAAgCzC,SAAhC;MAAA,6BAA0C,WAAO0C,WAAP,EAAsB;QAC9DhH,iBAAiB,CACfgH,WADe,EAEf,KAAI,CAACtG,GAFU,EAGfT,2BAA2B,CAACgH,4BAHb,EAIf,KAAI,CAACtG,QAJU,CAAjB,CAMGuG,IANH,CAMSC,WAAD,IAAgB;UACpB,KAAI,CAACC,wBAAL,GAAgCD,WAAhC;;UACA,KAAI,CAACE,IAAL;QACD,CATH,EAUGC,KAVH,CAUUC,CAAD,IAAOlB,OAAO,CAACC,GAAR,CAAYiB,CAAZ,CAVhB;MAWD,CAZD;;MAAA;QAAA;MAAA;IAAA;EAaD;;EAEDX,IAAI;IACF,KAAKQ,wBAAL,CAA8BtG,cAA9B,GACE,KAAKyE,IAAL,CAAUC,QAAV,CAAmB1E,cAAnB,CAAkC6E,KADpC;IAEA,KAAKyB,wBAAL,CAA8BrG,IAA9B,GAAqC,KAAKwE,IAAL,CAAUC,QAAV,CAAmBzE,IAAnB,CAAwB4E,KAA7D;IACA,KAAKyB,wBAAL,CAA8BpG,WAA9B,GACE,KAAKuE,IAAL,CAAUC,QAAV,CAAmBxE,WAAnB,CAA+B2E,KADjC;IAEA,KAAKyB,wBAAL,CAA8BI,eAA9B,GACE,KAAKjC,IAAL,CAAUC,QAAV,CAAmBvE,aAAnB,CAAiC0E,KADnC;IAEA,KAAKyB,wBAAL,CAA8BlG,UAA9B,GACE,KAAKqE,IAAL,CAAUC,QAAV,CAAmBtE,UAAnB,CAA8ByE,KADhC;IAEA,KAAKyB,wBAAL,CAA8BjG,WAA9B,GACE,KAAKoE,IAAL,CAAUC,QAAV,CAAmBrE,WAAnB,CAA+BwE,KADjC;IAEA,KAAKyB,wBAAL,CAA8BhG,gBAA9B,GACE,KAAKmE,IAAL,CAAUC,QAAV,CAAmBpE,gBAAnB,CAAoCuE,KADtC;IAEA,KAAKyB,wBAAL,CAA8B/F,oBAA9B,GACE,KAAKkE,IAAL,CAAUC,QAAV,CAAmBnE,oBAAnB,CAAwCsE,KAD1C;IAEA,KAAKyB,wBAAL,CAA8B9F,OAA9B,GAAwC,KAAKiE,IAAL,CAAUC,QAAV,CAAmBlE,OAAnB,CAA2BqE,KAAnE;IACA,KAAKyB,wBAAL,CAA8B7F,KAA9B,GAAsC,KAAKgE,IAAL,CAAUC,QAAV,CAAmBjE,KAAnB,CAAyBoE,KAA/D;IACA,KAAKyB,wBAAL,CAA8B3F,eAA9B,GACE,KAAK8D,IAAL,CAAUC,QAAV,CAAmB/D,eAAnB,CAAmCkE,KADrC;IAEA,KAAKyB,wBAAL,CAA8B1F,IAA9B,GAAqC,KAAK6D,IAAL,CAAUC,QAAV,CAAmB9D,IAAnB,CAAwBiE,KAA7D;IACA,KAAKyB,wBAAL,CAA8BzF,KAA9B,GAAsC,KAAK4D,IAAL,CAAUC,QAAV,CAAmB7D,KAAnB,CAAyBgE,KAA/D;IACA,KAAKyB,wBAAL,CAA8BxF,GAA9B,GAAoC,KAAK2D,IAAL,CAAUC,QAAV,CAAmB5D,GAAnB,CAAuB+D,KAA3D;IACA,KAAKyB,wBAAL,CAA8BtF,SAA9B,GACE,KAAKyD,IAAL,CAAUC,QAAV,CAAmB1D,SAAnB,CAA6B6D,KAD/B;IAEA,KAAKyB,wBAAL,CAA8BrF,aAA9B,GACE,KAAKwD,IAAL,CAAUC,QAAV,CAAmBzD,aAAnB,CAAiC4D,KADnC;IAEA,KAAKyB,wBAAL,CAA8BK,UAA9B,GACE,KAAKlC,IAAL,CAAUC,QAAV,CAAmB1E,cAAnB,CAAkC6E,KADpC;IAEA,KAAKyB,wBAAL,CAA8BpF,YAA9B,GACE,KAAKuD,IAAL,CAAUC,QAAV,CAAmBxD,YAAnB,CAAgC2D,KADlC;IAEA,KAAKyB,wBAAL,CAA8BlF,qBAA9B,GACE,KAAKqD,IAAL,CAAUC,QAAV,CAAmBtD,qBAAnB,CAAyCyD,KAD3C;IAEA,KAAKyB,wBAAL,CAA8BjF,aAA9B,GACE,KAAKoD,IAAL,CAAUC,QAAV,CAAmBrD,aAAnB,CAAiCwD,KADnC;IAEA,KAAKyB,wBAAL,CAA8BhF,cAA9B,GACE,KAAKmD,IAAL,CAAUC,QAAV,CAAmBpD,cAAnB,CAAkCuD,KADpC;IAEA,KAAKyB,wBAAL,CAA8B/E,QAA9B,GAAyC,KAAKkD,IAAL,CAAUC,QAAV,CAAmBnD,QAAnB,CAA4BsD,KAArE;IACA,KAAKyB,wBAAL,CAA8B9E,wBAA9B,GACE,KAAKiD,IAAL,CAAUC,QAAV,CAAmBlD,wBAAnB,CAA4CqD,KAD9C;IAEA,KAAKyB,wBAAL,CAA8B7E,kBAA9B,GACE,KAAKgD,IAAL,CAAUC,QAAV,CAAmBjD,kBAAnB,CAAsCoD,KADxC;IAEA,KAAKyB,wBAAL,CAA8B5E,uBAA9B,GACE,KAAK+C,IAAL,CAAUC,QAAV,CAAmBhD,uBAAnB,CAA2CmD,KAD7C;IAEA,KAAKyB,wBAAL,CAA8BzE,OAA9B,GAAwC,KAAK4C,IAAL,CAAUC,QAAV,CAAmB7C,OAAnB,CAA2BgD,KAAnE;IACA,KAAKyB,wBAAL,CAA8BxE,WAA9B,GACE,KAAK2C,IAAL,CAAUC,QAAV,CAAmB5C,WAAnB,CAA+B+C,KADjC;IAEA,KAAKyB,wBAAL,CAA8BtE,WAA9B,GACE,KAAKyC,IAAL,CAAUC,QAAV,CAAmB1C,WAAnB,CAA+B6C,KADjC;IAEA,KAAKyB,wBAAL,CAA8BrE,WAA9B,GACE,KAAKwC,IAAL,CAAUC,QAAV,CAAmBzC,WAAnB,CAA+B4C,KADjC;IAEA,KAAKyB,wBAAL,CAA8B3E,UAA9B,GACE,KAAK8C,IAAL,CAAUC,QAAV,CAAmB/C,UAAnB,CAA8BkD,KADhC;IAEA,KAAKyB,wBAAL,CAA8B1E,eAA9B,GACE,KAAK6C,IAAL,CAAUC,QAAV,CAAmB9C,eAAnB,CAAmCiD,KADrC;IAEA,KAAKyB,wBAAL,CAA8BjE,iBAA9B,GACE,KAAKoC,IAAL,CAAUC,QAAV,CAAmBrC,iBAAnB,CAAqCwC,KADvC;IAEA,KAAKyB,wBAAL,CAA8BnE,iBAA9B,GACE,KAAKsC,IAAL,CAAUC,QAAV,CAAmBvC,iBAAnB,CAAqC0C,KADvC;IAEA,KAAKyB,wBAAL,CAA8B/D,eAA9B,GACE,KAAKkC,IAAL,CAAUC,QAAV,CAAmBnC,eAAnB,CAAmCsC,KADrC;IAEA,KAAKyB,wBAAL,CAA8B9D,wBAA9B,GACE,KAAKiC,IAAL,CAAUC,QAAV,CAAmBlC,wBAAnB,CAA4CqC,KAD9C;IAEA,KAAKyB,wBAAL,CAA8B7D,aAA9B,GACE,KAAKgC,IAAL,CAAUC,QAAV,CAAmBjC,aAAnB,CAAiCoC,KADnC;IAEA,KAAKyB,wBAAL,CAA8B5D,cAA9B,GACE,KAAK+B,IAAL,CAAUC,QAAV,CAAmBhC,cAAnB,CAAkCmC,KADpC;;IAGA,IAAI,KAAKL,WAAT,EAAsB;MACpB,KAAKrB,iBAAL,CAAuByD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,KAAKP,wBAAvB,CAAvB;MACA;IACD;;IAED,KAAK1G,GAAL,CAASkH,iBAAT,CAA2B;MACzBC,4BAA4B,EAAE,KAAKT;IADV,CAA3B,EAEG9C,SAFH,CAEcwD,CAAD,IAAM;MACjB,KAAKvC,IAAL,CAAUwC,KAAV;IACD,CAJD;EAKD;;EAEDV,IAAI;IACF,KAAKD,wBAAL,CAA8B9F,OAA9B,GACE,KAAK8F,wBAAL,CAA8B,SAA9B,CADF;IAEA,KAAKA,wBAAL,CAA8BtE,WAA9B,GACE,KAAKsE,wBAAL,CAA8B,aAA9B,CADF;IAEA,KAAKA,wBAAL,CAA8BrE,WAA9B,GACE,KAAKqE,wBAAL,CAA8B,aAA9B,CADF;IAEA,KAAKA,wBAAL,CAA8BxE,WAA9B,GACE,KAAKwE,wBAAL,CAA8B,aAA9B,CADF;IAEA,KAAK7B,IAAL,CAAUC,QAAV,CAAmB1E,cAAnB,CAAkC2E,QAAlC,CACE,KAAK2B,wBAAL,CAA8BtG,cADhC;;IAGA,IAAI,KAAKsG,wBAAL,CAA8BrG,IAAlC,EAAwC;MACtC,KAAKwE,IAAL,CAAUC,QAAV,CAAmBzE,IAAnB,CAAwB0E,QAAxB,CACE,IAAI1E,IAAJ,CAAS,KAAKqG,wBAAL,CAA8BrG,IAAvC,CADF;IAGD;;IAED,KAAKwE,IAAL,CAAUC,QAAV,CAAmBxE,WAAnB,CAA+ByE,QAA/B,CACE,KAAK2B,wBAAL,CAA8BpG,WADhC;IAGA,KAAKuE,IAAL,CAAUC,QAAV,CAAmBvE,aAAnB,CAAiCwE,QAAjC,CACE,KAAK2B,wBAAL,CAA8BI,eADhC;IAGA,KAAKjC,IAAL,CAAUC,QAAV,CAAmBtE,UAAnB,CAA8BuE,QAA9B,CACE,KAAK2B,wBAAL,CAA8BlG,UADhC;IAGA,KAAKqE,IAAL,CAAUC,QAAV,CAAmBrE,WAAnB,CAA+BsE,QAA/B,CACE,KAAK2B,wBAAL,CAA8BjG,WADhC;IAGA,KAAKoE,IAAL,CAAUC,QAAV,CAAmBpE,gBAAnB,CAAoCqE,QAApC,CACE,KAAK2B,wBAAL,CAA8BhG,gBADhC;IAGA,KAAKmE,IAAL,CAAUC,QAAV,CAAmBnE,oBAAnB,CAAwCoE,QAAxC,CACE,KAAK2B,wBAAL,CAA8B/F,oBADhC;IAGA,KAAKkE,IAAL,CAAUC,QAAV,CAAmBlE,OAAnB,CAA2BmE,QAA3B,CAAoC,KAAK2B,wBAAL,CAA8B9F,OAAlE;IACA,KAAKiE,IAAL,CAAUC,QAAV,CAAmBjE,KAAnB,CAAyBkE,QAAzB,CAAkC,KAAK2B,wBAAL,CAA8B7F,KAAhE;IACA,KAAKgE,IAAL,CAAUC,QAAV,CAAmB/D,eAAnB,CAAmCgE,QAAnC,CACE,KAAK2B,wBAAL,CAA8B3F,eADhC;IAGA,KAAK8D,IAAL,CAAUC,QAAV,CAAmB9D,IAAnB,CAAwB+D,QAAxB,CAAiC,KAAK2B,wBAAL,CAA8B1F,IAA/D;IACA,KAAK6D,IAAL,CAAUC,QAAV,CAAmB7D,KAAnB,CAAyB8D,QAAzB,CAAkC,KAAK2B,wBAAL,CAA8BzF,KAAhE;IACA,KAAK4D,IAAL,CAAUC,QAAV,CAAmB5D,GAAnB,CAAuB6D,QAAvB,CAAgC,KAAK2B,wBAAL,CAA8BxF,GAA9D;IACA,KAAK2D,IAAL,CAAUC,QAAV,CAAmB1D,SAAnB,CAA6B2D,QAA7B,CACE,KAAK2B,wBAAL,CAA8BtF,SADhC;IAGA,KAAKyD,IAAL,CAAUC,QAAV,CAAmBzD,aAAnB,CAAiC0D,QAAjC,CACE,KAAK2B,wBAAL,CAA8BrF,aADhC;IAGA,KAAKwD,IAAL,CAAUC,QAAV,CAAmB7C,OAAnB,CAA2B8C,QAA3B,CAAoC,KAAK2B,wBAAL,CAA8BzE,OAAlE;IAEA,KAAK4C,IAAL,CAAUC,QAAV,CAAmB5C,WAAnB,CAA+B6C,QAA/B,CACE,KAAK2B,wBAAL,CAA8BxE,WADhC;IAGA,KAAK2C,IAAL,CAAUC,QAAV,CAAmBzC,WAAnB,CAA+B0C,QAA/B,CACE,KAAK2B,wBAAL,CAA8BrE,WADhC;IAGA,KAAKwC,IAAL,CAAUC,QAAV,CAAmB1C,WAAnB,CAA+B2C,QAA/B,CACE,KAAK2B,wBAAL,CAA8BtE,WADhC;IAGA,KAAKyC,IAAL,CAAUC,QAAV,CAAmBxD,YAAnB,CAAgCyD,QAAhC,CACE,KAAK2B,wBAAL,CAA8BpF,YADhC;IAGA,KAAKuD,IAAL,CAAUC,QAAV,CAAmBtD,qBAAnB,CAAyCuD,QAAzC,CACE,KAAK2B,wBAAL,CAA8BlF,qBADhC;IAGA,KAAKqD,IAAL,CAAUC,QAAV,CAAmBrD,aAAnB,CAAiCsD,QAAjC,CACE,KAAK2B,wBAAL,CAA8BjF,aADhC;IAGA,KAAKoD,IAAL,CAAUC,QAAV,CAAmBpD,cAAnB,CAAkCqD,QAAlC,CACE,KAAK2B,wBAAL,CAA8BhF,cADhC;IAGA,KAAKmD,IAAL,CAAUC,QAAV,CAAmBnD,QAAnB,CAA4BoD,QAA5B,CACE,KAAK2B,wBAAL,CAA8B/E,QADhC;IAGA,KAAKkD,IAAL,CAAUC,QAAV,CAAmBlD,wBAAnB,CAA4CmD,QAA5C,CACE,KAAK2B,wBAAL,CAA8B9E,wBADhC;IAGA,KAAKiD,IAAL,CAAUC,QAAV,CAAmBjD,kBAAnB,CAAsCkD,QAAtC,CACE,KAAK2B,wBAAL,CAA8B7E,kBADhC;IAGA,KAAKgD,IAAL,CAAUC,QAAV,CAAmBhD,uBAAnB,CAA2CiD,QAA3C,CACE,KAAK2B,wBAAL,CAA8B5E,uBADhC;IAGA,KAAK+C,IAAL,CAAUC,QAAV,CAAmB7C,OAAnB,CAA2B8C,QAA3B,CAAoC,KAAK2B,wBAAL,CAA8BzE,OAAlE;IACA,KAAK4C,IAAL,CAAUC,QAAV,CAAmB/C,UAAnB,CAA8BgD,QAA9B,CACE,KAAK2B,wBAAL,CAA8B3E,UADhC;IAGA,KAAK8C,IAAL,CAAUC,QAAV,CAAmB9C,eAAnB,CAAmC+C,QAAnC,CACE,KAAK2B,wBAAL,CAA8B1E,eADhC;IAGA,KAAK6C,IAAL,CAAUC,QAAV,CAAmBrC,iBAAnB,CAAqCsC,QAArC,CACE,KAAK2B,wBAAL,CAA8BjE,iBADhC;IAGA,KAAKoC,IAAL,CAAUC,QAAV,CAAmBvC,iBAAnB,CAAqCwC,QAArC,CACE,KAAK2B,wBAAL,CAA8BnE,iBADhC;IAGA,KAAKsC,IAAL,CAAUC,QAAV,CAAmBnC,eAAnB,CAAmCoC,QAAnC,CACE,KAAK2B,wBAAL,CAA8B/D,eADhC;IAGA,KAAKkC,IAAL,CAAUC,QAAV,CAAmBlC,wBAAnB,CAA4CmC,QAA5C,CACE,KAAK2B,wBAAL,CAA8B9D,wBADhC;IAGA,KAAKiC,IAAL,CAAUC,QAAV,CAAmBjC,aAAnB,CAAiCkC,QAAjC,CACE,KAAK2B,wBAAL,CAA8B7D,aADhC;IAGA,KAAKgC,IAAL,CAAUC,QAAV,CAAmBhC,cAAnB,CAAkCiC,QAAlC,CACE,KAAK2B,wBAAL,CAA8B5D,cADhC;IAGA,KAAK+B,IAAL,CAAUC,QAAV,CAAmBxC,iBAAnB,CAAqCyC,QAArC,CAA8C,IAA9C;EACD;;AAxZ+C;;;;;;;;;;;;;;;;UAY/CpG;IAAS2I,OAAC,cAAD,EAAiB;MAAEC,MAAM,EAAE;IAAV,CAAjB;;;UAKT5I;IAAS2I,OAACrI,sBAAD,EAAyB;MAAEsI,MAAM,EAAE;IAAV,CAAzB;;;UAET5I;IAAS2I,OAACpI,gCAAD,EAAmC;MAAEqI,MAAM,EAAE;IAAV,CAAnC;;;UAGT5I;IAAS2I,OAAC3H,iCAAD,EAAoC;MAAE4H,MAAM,EAAE;IAAV,CAApC;;;UAUT3I;;;AAhCUiB,qCAAqC,eAJjDnB,SAAS,CAAC;EACT8I,QAAQ,EAAE,gCADD;EAETC;AAFS,CAAD,CAIwC,GAArC5H,qCAAqC,CAArC;SAAAA", "names": ["Component", "ViewChild", "Input", "Validators", "FormGroup", "FormControl", "FilingApiService", "StateSelectorComponent", "CorporationTypeSelectorComponent", "fork<PERSON><PERSON>n", "EmployerIdnetificationNumber", "ActivatedRoute", "LoadFilingService", "FilingServiceResponseObject", "FormModeService", "CustomSharedValidations", "FilingInfoService", "OrganizationTypeSelectorComponent", "PageTitleService", "EmployerIdentificationNumberComponent", "constructor", "filingPriceService", "Api", "formMode", "activatedRoute", "pageTitleService", "FormationState", "Date", "PrintedName", "CorporateType", "EntityType", "MemberCount", "IsMembersMarried", "PrincipalOfficerName", "SSN_TIN", "Phone", "phoneLength", "<PERSON><PERSON><PERSON><PERSON>", "City", "State", "Zip", "zipLength", "LegalName", "TradeName_DBA", "ClosingMonth", "validate<PERSON><PERSON><PERSON>", "IsHighwayMotorTaxable", "IsFileReturns", "IsAlcholSeller", "IsFormW2", "AgricultureEmployeeCount", "OtherEmployeeCount", "PrimaryBusinessActivity", "IsGambling", "IsTotalWagesPay", "Remarks", "file_date_y", "validateFormationDate", "file_date_m", "file_date_d", "IsThirdPartyAgree", "DateAnnutiesPaidM", "required", "DateAnnutiesPaidY", "validateYear", "IsOnlyTwoMember", "SingleOrMultiMemberValue", "HowManyMember", "AreTheyMarried", "_FormRef", "FormRef", "getUpSellingData", "setTimeout", "nativeElement", "click", "Promise", "res", "UpSellingResolver", "ngOnInit", "get<PERSON><PERSON><PERSON>", "SubCatCode", "employeeidntificationnumber", "subscribe", "label", "setPageTitle", "CorpTypeComponent", "Caption", "FCGetStates", "States", "StateComponent", "fillData", "GetEmployerIdentificationEntity", "GetEmployerIdentificationMembers", "GetFormationType", "response", "Member<PERSON><PERSON><PERSON>", "Types", "StartLoad", "IsUpSelling", "Form", "controls", "setValue", "valueChanges", "value", "OrganizationTypeComponent", "OrganizationTypes", "onCorporationTypeUpdate", "corporationTypeCode", "GetFormationTypes", "data", "CorporationTypes", "EntityTypeData", "length", "console", "log", "onSave", "event", "clearValidators", "updateValueAndValidity", "valid", "Save", "ex", "error", "queryParams", "queryString", "EmployerIdentificationNumber", "then", "serviceData", "EmployerIdentificationNo", "Load", "catch", "e", "CorporationType", "TradeState", "Object", "assign", "SaveFilingService", "employerIdentificationNumber", "x", "reset", "args", "static", "selector", "template"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\viatech\\Ecom\\client\\acs\\src\\app\\Modules\\FilingService\\Pages\\employer-identification-number\\employer-identification-number.component.ts"], "sourcesContent": ["import { Component, OnInit, ViewChild, Input, ElementRef } from \"@angular/core\";\r\nimport {\r\n  Validators,\r\n  FormGroup,\r\n  FormControl,\r\n} from \"@angular/forms\";\r\nimport { FilingApiService } from \"../../Services/FilingApiService\";\r\nimport { StateSelectorComponent } from \"src/app/Modules/Shared/Components/formComponents/stateSelector/state-selector.component\";\r\nimport { CorporationTypeSelectorComponent } from \"src/app/Modules/Shared/Components/formComponents/corporation-type-selector/corporation-type-selector.component\";\r\nimport { forkJoin } from \"rxjs\";\r\nimport { EmployerIdnetificationNumber } from \"../../Models/EmployerIdentificationNumber\";\r\nimport { ActivatedRoute } from \"@angular/router\";\r\nimport { LoadFilingService } from \"../../Functions/filing-service-load.func\";\r\nimport { FilingServiceResponseObject } from \"../../Enums/filing-service-enum\";\r\nimport { FormModeService } from \"src/app/Modules/Shared/Services/Common/FormModeService\";\r\n\r\nimport { CustomSharedValidations } from \"src/app/Modules/Shared/functions/custom-validations\";\r\nimport { FilingInfoService } from \"../../Services/filing-price.service\";\r\nimport { OrganizationTypeSelectorComponent } from \"src/app/Modules/Shared/Components/formComponents/organization-type-selector/organization-type-selector.component\";\r\nimport { PageTitleService } from \"src/app/Modules/Shared/Services/Common/page-title.service\";\r\n\r\n@Component({\r\n  selector: \"employer-identification-number\",\r\n  templateUrl: \"employer-identification-number.component.html\",\r\n})\r\nexport class EmployerIdentificationNumberComponent implements OnInit {\r\n  additionalEntityType: any;\r\n  EntityTypeData: any[] = [];\r\n  constructor(\r\n    public filingPriceService: FilingInfoService,\r\n    private Api: FilingApiService,\r\n    public formMode: FormModeService,\r\n    private activatedRoute: ActivatedRoute,\r\n    private pageTitleService: PageTitleService\r\n  ) { }\r\n\r\n  FormRef: ElementRef;\r\n  @ViewChild(\"submitButton\", { static: true }) set _FormRef(\r\n    FormRef: ElementRef\r\n  ) {\r\n    this.FormRef = FormRef;\r\n  }\r\n  @ViewChild(StateSelectorComponent, { static: true })\r\n  StateComponent: StateSelectorComponent;\r\n  @ViewChild(CorporationTypeSelectorComponent, { static: true })\r\n  CorpTypeComponent: CorporationTypeSelectorComponent;\r\n\r\n  @ViewChild(OrganizationTypeSelectorComponent, { static: true })\r\n  OrganizationTypeComponent: OrganizationTypeSelectorComponent;\r\n\r\n  LLC = \"LC\";\r\n  MemberMarried: any[] = [];\r\n  Types: any[] = [];\r\n  EmployerIdentificationNo: EmployerIdnetificationNumber =\r\n    new EmployerIdnetificationNumber();\r\n  States: any[] = [];\r\n  UpSellingResolver;\r\n  @Input() IsUpSelling: boolean = false;\r\n  getUpSellingData(): Promise<any> {\r\n    setTimeout(() => {\r\n      this.FormRef.nativeElement.click();\r\n    }, 10);\r\n    return new Promise((res) => (this.UpSellingResolver = res));\r\n  }\r\n  ngOnInit() {\r\n    this.filingPriceService\r\n      .getLabel(this.filingPriceService.SubCatCode.employeeidntificationnumber, 'FOT')\r\n      .subscribe(label => {\r\n        this.pageTitleService.setPageTitle(label);\r\n      });\r\n\r\n    this.CorpTypeComponent.Caption = \"Select Type of Entity\";\r\n    this.Api.FCGetStates().subscribe((States) => {\r\n      this.States = States;\r\n      this.StateComponent.States = States;\r\n    });\r\n\r\n\r\n    const fillData = forkJoin([\r\n      this.Api.GetEmployerIdentificationEntity(),\r\n      this.Api.GetEmployerIdentificationMembers(),\r\n      this.Api.GetFormationType(\"907\"),\r\n    ]);\r\n    fillData.subscribe((response) => {\r\n      this.MemberMarried = response[1];\r\n      this.Types = response[2] || [];\r\n      this.StartLoad();\r\n    });\r\n    if (!this.IsUpSelling) {\r\n      this.Form.controls.IsThirdPartyAgree.setValue(false);\r\n    }\r\n\r\n    this.Form.controls.EntityType.valueChanges.subscribe((value) => {\r\n      if (this.IsUpSelling) {\r\n        this.Form.controls.EntityType.value;\r\n      }\r\n    });\r\n    this.Form.controls.HowManyMember.valueChanges.subscribe((value) => {\r\n      if (this.Form.controls.HowManyMember.value != null && this.Form.controls.HowManyMember.value != 2) {\r\n        this.Form.controls.AreTheyMarried.setValue(null);\r\n        this.Form.controls.IsOnlyTwoMember.setValue(null);\r\n        this.Form.controls.SingleOrMultiMemberValue.setValue(null);\r\n      }\r\n\r\n    });\r\n\r\n    this.Form.controls.IsThirdPartyAgree.valueChanges.subscribe((value) => {\r\n      this.OrganizationTypeComponent.Caption = \"Select Type\"\r\n      this.OrganizationTypeComponent.OrganizationTypes = this.Types;\r\n    })\r\n\r\n    this.Form.controls.CorporateType.valueChanges.subscribe((value) => {\r\n      this.onCorporationTypeUpdate(value);\r\n    })\r\n  }\r\n\r\n  onCorporationTypeUpdate(corporationTypeCode) {\r\n    this.Api.GetFormationTypes(\r\n      \"FS\",\r\n      \"FOT\",\r\n      \"907\",\r\n      corporationTypeCode\r\n    ).subscribe((data) => {\r\n      this.CorpTypeComponent.CorporationTypes = data;\r\n      this.EntityTypeData = data;\r\n\r\n      if (data.length == 1) {\r\n        this.Form.controls.EntityType.setValue(data[0].corporationTypeCode);\r\n        console.log(this.Form.controls.EntityType);\r\n        this.CorpTypeComponent.Caption = \"\";\r\n      }\r\n      else if (this.EntityTypeData.length == 0) {\r\n        this.Form.controls.EntityType.setValue(\"\");\r\n        this.CorpTypeComponent.Caption = \"\";\r\n      } else {\r\n        this.CorpTypeComponent.Caption = \"Select Type of Entity\";\r\n      }\r\n    });\r\n  }\r\n  Form = new FormGroup({\r\n    FormationState: new FormControl<string | null>(null),\r\n    Date: new FormControl<any | null>(null),\r\n    PrintedName: new FormControl<string | null>(null),\r\n    CorporateType: new FormControl<any | null>(null),\r\n    EntityType: new FormControl<string | null>(null),\r\n    MemberCount: new FormControl<number | null>(null),\r\n    IsMembersMarried: new FormControl<string | null>(null),\r\n    PrincipalOfficerName: new FormControl<string | null>(null),\r\n    SSN_TIN: new FormControl<number | null>(null),\r\n    Phone: new FormControl<string | null>(null, CustomSharedValidations.phoneLength),\r\n    PhysicalAddress: new FormControl<string | null>(null),\r\n    City: new FormControl<string | null>(null),\r\n    State: new FormControl<string | null>(null),\r\n    Zip: new FormControl<string | null>(null, [CustomSharedValidations.zipLength]),\r\n    LegalName: new FormControl<string | null>(null),\r\n    TradeName_DBA: new FormControl<string | null>(null),\r\n\r\n    ClosingMonth: new FormControl<string | null>(null, [\r\n      CustomSharedValidations.validateMonth,\r\n    ]),\r\n    IsHighwayMotorTaxable: new FormControl<string | null>(null),\r\n    IsFileReturns: new FormControl<string | null>(null),\r\n    IsAlcholSeller: new FormControl<string | null>(null),\r\n    IsFormW2: new FormControl<string | null>(null),\r\n    AgricultureEmployeeCount: new FormControl<number | null>(null),\r\n    OtherEmployeeCount: new FormControl<number | null>(null),\r\n    PrimaryBusinessActivity: new FormControl<string | null>(null),\r\n    IsGambling: new FormControl<string | null>(null),\r\n    IsTotalWagesPay: new FormControl<string | null>(null),\r\n    Remarks: new FormControl<string | null>(null),\r\n    file_date_y: new FormControl<any | null>(null, [\r\n      CustomSharedValidations.validateFormationDate(\r\n        \"file_date_d\",\r\n        \"file_date_m\",\r\n        \"file_date_y\"\r\n      ),\r\n    ]),\r\n    file_date_m: new FormControl<any | null>(null, [\r\n      CustomSharedValidations.validateFormationDate(\r\n        \"file_date_d\",\r\n        \"file_date_m\",\r\n        \"file_date_y\"\r\n      ),\r\n    ]),\r\n    file_date_d: new FormControl<any | null>(null, [\r\n      CustomSharedValidations.validateFormationDate(\r\n        \"file_date_d\",\r\n        \"file_date_m\",\r\n        \"file_date_y\"\r\n      ),\r\n    ]),\r\n    IsThirdPartyAgree: new FormControl<any | null>(null),\r\n    DateAnnutiesPaidM: new FormControl<string | null>(null, [\r\n      Validators.required,\r\n      CustomSharedValidations.validateMonth,\r\n    ]),\r\n    DateAnnutiesPaidY: new FormControl<string | null>(null, [\r\n      Validators.required,\r\n      CustomSharedValidations.validateYear,\r\n    ]),\r\n    IsOnlyTwoMember: new FormControl<any | null>(null),\r\n    SingleOrMultiMemberValue: new FormControl<string | null>(null),\r\n    HowManyMember: new FormControl<any | null>(null),\r\n    AreTheyMarried: new FormControl<any | null>(null),\r\n  });\r\n\r\n  onSave(event) {\r\n    try {\r\n      if (this.Form.controls.IsFormW2.value == \"No\") {\r\n        this.Form.controls.AgricultureEmployeeCount.clearValidators();\r\n        this.Form.controls.DateAnnutiesPaidY.clearValidators();\r\n        this.Form.controls.DateAnnutiesPaidM.clearValidators();\r\n        this.Form.controls.OtherEmployeeCount.clearValidators();\r\n        this.Form.updateValueAndValidity();\r\n      }\r\n\r\n      if (this.Form.valid) {\r\n        this.Save();\r\n      }\r\n\r\n      return false;\r\n    } catch (ex) {\r\n      console.error(ex);\r\n    }\r\n  }\r\n\r\n  StartLoad() {\r\n    this.activatedRoute.queryParams.subscribe(async (queryString) => {\r\n      LoadFilingService<EmployerIdnetificationNumber>(\r\n        queryString,\r\n        this.Api,\r\n        FilingServiceResponseObject.EmployerIdentificationNumber,\r\n        this.formMode\r\n      )\r\n        .then((serviceData) => {\r\n          this.EmployerIdentificationNo = serviceData;\r\n          this.Load();\r\n        })\r\n        .catch((e) => console.log(e));\r\n    });\r\n  }\r\n\r\n  Save() {\r\n    this.EmployerIdentificationNo.FormationState =\r\n      this.Form.controls.FormationState.value;\r\n    this.EmployerIdentificationNo.Date = this.Form.controls.Date.value;\r\n    this.EmployerIdentificationNo.PrintedName =\r\n      this.Form.controls.PrintedName.value;\r\n    this.EmployerIdentificationNo.CorporationType =\r\n      this.Form.controls.CorporateType.value;\r\n    this.EmployerIdentificationNo.EntityType =\r\n      this.Form.controls.EntityType.value;\r\n    this.EmployerIdentificationNo.MemberCount =\r\n      this.Form.controls.MemberCount.value;\r\n    this.EmployerIdentificationNo.IsMembersMarried =\r\n      this.Form.controls.IsMembersMarried.value;\r\n    this.EmployerIdentificationNo.PrincipalOfficerName =\r\n      this.Form.controls.PrincipalOfficerName.value;\r\n    this.EmployerIdentificationNo.SSN_TIN = this.Form.controls.SSN_TIN.value;\r\n    this.EmployerIdentificationNo.Phone = this.Form.controls.Phone.value;\r\n    this.EmployerIdentificationNo.PhysicalAddress =\r\n      this.Form.controls.PhysicalAddress.value;\r\n    this.EmployerIdentificationNo.City = this.Form.controls.City.value;\r\n    this.EmployerIdentificationNo.State = this.Form.controls.State.value;\r\n    this.EmployerIdentificationNo.Zip = this.Form.controls.Zip.value;\r\n    this.EmployerIdentificationNo.LegalName =\r\n      this.Form.controls.LegalName.value;\r\n    this.EmployerIdentificationNo.TradeName_DBA =\r\n      this.Form.controls.TradeName_DBA.value;\r\n    this.EmployerIdentificationNo.TradeState =\r\n      this.Form.controls.FormationState.value;\r\n    this.EmployerIdentificationNo.ClosingMonth =\r\n      this.Form.controls.ClosingMonth.value;\r\n    this.EmployerIdentificationNo.IsHighwayMotorTaxable =\r\n      this.Form.controls.IsHighwayMotorTaxable.value;\r\n    this.EmployerIdentificationNo.IsFileReturns =\r\n      this.Form.controls.IsFileReturns.value;\r\n    this.EmployerIdentificationNo.IsAlcholSeller =\r\n      this.Form.controls.IsAlcholSeller.value;\r\n    this.EmployerIdentificationNo.IsFormW2 = this.Form.controls.IsFormW2.value;\r\n    this.EmployerIdentificationNo.AgricultureEmployeeCount =\r\n      this.Form.controls.AgricultureEmployeeCount.value;\r\n    this.EmployerIdentificationNo.OtherEmployeeCount =\r\n      this.Form.controls.OtherEmployeeCount.value;\r\n    this.EmployerIdentificationNo.PrimaryBusinessActivity =\r\n      this.Form.controls.PrimaryBusinessActivity.value;\r\n    this.EmployerIdentificationNo.Remarks = this.Form.controls.Remarks.value;\r\n    this.EmployerIdentificationNo.file_date_y =\r\n      this.Form.controls.file_date_y.value;\r\n    this.EmployerIdentificationNo.file_date_m =\r\n      this.Form.controls.file_date_m.value;\r\n    this.EmployerIdentificationNo.file_date_d =\r\n      this.Form.controls.file_date_d.value;\r\n    this.EmployerIdentificationNo.IsGambling =\r\n      this.Form.controls.IsGambling.value;\r\n    this.EmployerIdentificationNo.IsTotalWagesPay =\r\n      this.Form.controls.IsTotalWagesPay.value;\r\n    this.EmployerIdentificationNo.DateAnnutiesPaidY =\r\n      this.Form.controls.DateAnnutiesPaidY.value;\r\n    this.EmployerIdentificationNo.DateAnnutiesPaidM =\r\n      this.Form.controls.DateAnnutiesPaidM.value;\r\n    this.EmployerIdentificationNo.IsOnlyTwoMember =\r\n      this.Form.controls.IsOnlyTwoMember.value;\r\n    this.EmployerIdentificationNo.SingleOrMultiMemberValue =\r\n      this.Form.controls.SingleOrMultiMemberValue.value;\r\n    this.EmployerIdentificationNo.HowManyMember =\r\n      this.Form.controls.HowManyMember.value;\r\n    this.EmployerIdentificationNo.AreTheyMarried =\r\n      this.Form.controls.AreTheyMarried.value;\r\n\r\n    if (this.IsUpSelling) {\r\n      this.UpSellingResolver(Object.assign({}, this.EmployerIdentificationNo));\r\n      return;\r\n    }\r\n\r\n    this.Api.SaveFilingService({\r\n      employerIdentificationNumber: this.EmployerIdentificationNo,\r\n    }).subscribe((x) => {\r\n      this.Form.reset();\r\n    });\r\n  }\r\n\r\n  Load() {\r\n    this.EmployerIdentificationNo.SSN_TIN =\r\n      this.EmployerIdentificationNo[\"SsN_TIN\"];\r\n    this.EmployerIdentificationNo.file_date_m =\r\n      this.EmployerIdentificationNo[\"File_date_m\"];\r\n    this.EmployerIdentificationNo.file_date_d =\r\n      this.EmployerIdentificationNo[\"File_date_d\"];\r\n    this.EmployerIdentificationNo.file_date_y =\r\n      this.EmployerIdentificationNo[\"File_date_y\"];\r\n    this.Form.controls.FormationState.setValue(\r\n      this.EmployerIdentificationNo.FormationState\r\n    );\r\n    if (this.EmployerIdentificationNo.Date) {\r\n      this.Form.controls.Date.setValue(\r\n        new Date(this.EmployerIdentificationNo.Date)\r\n      );\r\n    }\r\n\r\n    this.Form.controls.PrintedName.setValue(\r\n      this.EmployerIdentificationNo.PrintedName\r\n    );\r\n    this.Form.controls.CorporateType.setValue(\r\n      this.EmployerIdentificationNo.CorporationType\r\n    );\r\n    this.Form.controls.EntityType.setValue(\r\n      this.EmployerIdentificationNo.EntityType\r\n    );\r\n    this.Form.controls.MemberCount.setValue(\r\n      this.EmployerIdentificationNo.MemberCount\r\n    );\r\n    this.Form.controls.IsMembersMarried.setValue(\r\n      this.EmployerIdentificationNo.IsMembersMarried\r\n    );\r\n    this.Form.controls.PrincipalOfficerName.setValue(\r\n      this.EmployerIdentificationNo.PrincipalOfficerName\r\n    );\r\n    this.Form.controls.SSN_TIN.setValue(this.EmployerIdentificationNo.SSN_TIN);\r\n    this.Form.controls.Phone.setValue(this.EmployerIdentificationNo.Phone);\r\n    this.Form.controls.PhysicalAddress.setValue(\r\n      this.EmployerIdentificationNo.PhysicalAddress\r\n    );\r\n    this.Form.controls.City.setValue(this.EmployerIdentificationNo.City);\r\n    this.Form.controls.State.setValue(this.EmployerIdentificationNo.State);\r\n    this.Form.controls.Zip.setValue(this.EmployerIdentificationNo.Zip);\r\n    this.Form.controls.LegalName.setValue(\r\n      this.EmployerIdentificationNo.LegalName\r\n    );\r\n    this.Form.controls.TradeName_DBA.setValue(\r\n      this.EmployerIdentificationNo.TradeName_DBA\r\n    );\r\n    this.Form.controls.Remarks.setValue(this.EmployerIdentificationNo.Remarks);\r\n\r\n    this.Form.controls.file_date_y.setValue(\r\n      this.EmployerIdentificationNo.file_date_y\r\n    );\r\n    this.Form.controls.file_date_d.setValue(\r\n      this.EmployerIdentificationNo.file_date_d\r\n    );\r\n    this.Form.controls.file_date_m.setValue(\r\n      this.EmployerIdentificationNo.file_date_m\r\n    );\r\n    this.Form.controls.ClosingMonth.setValue(\r\n      this.EmployerIdentificationNo.ClosingMonth\r\n    );\r\n    this.Form.controls.IsHighwayMotorTaxable.setValue(\r\n      this.EmployerIdentificationNo.IsHighwayMotorTaxable\r\n    );\r\n    this.Form.controls.IsFileReturns.setValue(\r\n      this.EmployerIdentificationNo.IsFileReturns\r\n    );\r\n    this.Form.controls.IsAlcholSeller.setValue(\r\n      this.EmployerIdentificationNo.IsAlcholSeller\r\n    );\r\n    this.Form.controls.IsFormW2.setValue(\r\n      this.EmployerIdentificationNo.IsFormW2\r\n    );\r\n    this.Form.controls.AgricultureEmployeeCount.setValue(\r\n      this.EmployerIdentificationNo.AgricultureEmployeeCount\r\n    );\r\n    this.Form.controls.OtherEmployeeCount.setValue(\r\n      this.EmployerIdentificationNo.OtherEmployeeCount\r\n    );\r\n    this.Form.controls.PrimaryBusinessActivity.setValue(\r\n      this.EmployerIdentificationNo.PrimaryBusinessActivity\r\n    );\r\n    this.Form.controls.Remarks.setValue(this.EmployerIdentificationNo.Remarks);\r\n    this.Form.controls.IsGambling.setValue(\r\n      this.EmployerIdentificationNo.IsGambling\r\n    );\r\n    this.Form.controls.IsTotalWagesPay.setValue(\r\n      this.EmployerIdentificationNo.IsTotalWagesPay\r\n    );\r\n    this.Form.controls.DateAnnutiesPaidY.setValue(\r\n      this.EmployerIdentificationNo.DateAnnutiesPaidY\r\n    );\r\n    this.Form.controls.DateAnnutiesPaidM.setValue(\r\n      this.EmployerIdentificationNo.DateAnnutiesPaidM\r\n    );\r\n    this.Form.controls.IsOnlyTwoMember.setValue(\r\n      this.EmployerIdentificationNo.IsOnlyTwoMember\r\n    );\r\n    this.Form.controls.SingleOrMultiMemberValue.setValue(\r\n      this.EmployerIdentificationNo.SingleOrMultiMemberValue\r\n    );\r\n    this.Form.controls.HowManyMember.setValue(\r\n      this.EmployerIdentificationNo.HowManyMember\r\n    );\r\n    this.Form.controls.AreTheyMarried.setValue(\r\n      this.EmployerIdentificationNo.AreTheyMarried\r\n    );\r\n    this.Form.controls.IsThirdPartyAgree.setValue(true);\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}