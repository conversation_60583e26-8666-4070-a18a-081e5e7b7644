{"ast": null, "code": "import { ReplaySubject } from '../ReplaySubject';\nexport function shareReplay(configOrBufferSize, windowTime, scheduler) {\n  let config;\n\n  if (configOrBufferSize && typeof configOrBufferSize === 'object') {\n    config = configOrBufferSize;\n  } else {\n    config = {\n      bufferSize: configOrBufferSize,\n      windowTime,\n      refCount: false,\n      scheduler\n    };\n  }\n\n  return source => source.lift(shareReplayOperator(config));\n}\n\nfunction shareReplayOperator({\n  bufferSize = Number.POSITIVE_INFINITY,\n  windowTime = Number.POSITIVE_INFINITY,\n  refCount: useRefCount,\n  scheduler\n}) {\n  let subject;\n  let refCount = 0;\n  let subscription;\n  let hasError = false;\n  let isComplete = false;\n  return function shareReplayOperation(source) {\n    refCount++;\n    let innerSub;\n\n    if (!subject || hasError) {\n      hasError = false;\n      subject = new ReplaySubject(bufferSize, windowTime, scheduler);\n      innerSub = subject.subscribe(this);\n      subscription = source.subscribe({\n        next(value) {\n          subject.next(value);\n        },\n\n        error(err) {\n          hasError = true;\n          subject.error(err);\n        },\n\n        complete() {\n          isComplete = true;\n          subscription = undefined;\n          subject.complete();\n        }\n\n      });\n\n      if (isComplete) {\n        subscription = undefined;\n      }\n    } else {\n      innerSub = subject.subscribe(this);\n    }\n\n    this.add(() => {\n      refCount--;\n      innerSub.unsubscribe();\n      innerSub = undefined;\n\n      if (subscription && !isComplete && useRefCount && refCount === 0) {\n        subscription.unsubscribe();\n        subscription = undefined;\n        subject = undefined;\n      }\n    });\n  };\n}", "map": {"version": 3, "names": ["ReplaySubject", "shareReplay", "configOrBufferSize", "windowTime", "scheduler", "config", "bufferSize", "refCount", "source", "lift", "shareReplayOperator", "Number", "POSITIVE_INFINITY", "useRefCount", "subject", "subscription", "<PERSON><PERSON><PERSON><PERSON>", "isComplete", "shareReplayOperation", "innerSub", "subscribe", "next", "value", "error", "err", "complete", "undefined", "add", "unsubscribe"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/shareReplay.js"], "sourcesContent": ["import { ReplaySubject } from '../ReplaySubject';\nexport function shareReplay(configOrBufferSize, windowTime, scheduler) {\n    let config;\n    if (configOrBufferSize && typeof configOrBufferSize === 'object') {\n        config = configOrBufferSize;\n    }\n    else {\n        config = {\n            bufferSize: configOrBufferSize,\n            windowTime,\n            refCount: false,\n            scheduler,\n        };\n    }\n    return (source) => source.lift(shareReplayOperator(config));\n}\nfunction shareReplayOperator({ bufferSize = Number.POSITIVE_INFINITY, windowTime = Number.POSITIVE_INFINITY, refCount: useRefCount, scheduler, }) {\n    let subject;\n    let refCount = 0;\n    let subscription;\n    let hasError = false;\n    let isComplete = false;\n    return function shareReplayOperation(source) {\n        refCount++;\n        let innerSub;\n        if (!subject || hasError) {\n            hasError = false;\n            subject = new ReplaySubject(bufferSize, windowTime, scheduler);\n            innerSub = subject.subscribe(this);\n            subscription = source.subscribe({\n                next(value) {\n                    subject.next(value);\n                },\n                error(err) {\n                    hasError = true;\n                    subject.error(err);\n                },\n                complete() {\n                    isComplete = true;\n                    subscription = undefined;\n                    subject.complete();\n                },\n            });\n            if (isComplete) {\n                subscription = undefined;\n            }\n        }\n        else {\n            innerSub = subject.subscribe(this);\n        }\n        this.add(() => {\n            refCount--;\n            innerSub.unsubscribe();\n            innerSub = undefined;\n            if (subscription && !isComplete && useRefCount && refCount === 0) {\n                subscription.unsubscribe();\n                subscription = undefined;\n                subject = undefined;\n            }\n        });\n    };\n}\n"], "mappings": "AAAA,SAASA,aAAT,QAA8B,kBAA9B;AACA,OAAO,SAASC,WAAT,CAAqBC,kBAArB,EAAyCC,UAAzC,EAAqDC,SAArD,EAAgE;EACnE,IAAIC,MAAJ;;EACA,IAAIH,kBAAkB,IAAI,OAAOA,kBAAP,KAA8B,QAAxD,EAAkE;IAC9DG,MAAM,GAAGH,kBAAT;EACH,CAFD,MAGK;IACDG,MAAM,GAAG;MACLC,UAAU,EAAEJ,kBADP;MAELC,UAFK;MAGLI,QAAQ,EAAE,KAHL;MAILH;IAJK,CAAT;EAMH;;EACD,OAAQI,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAYC,mBAAmB,CAACL,MAAD,CAA/B,CAAnB;AACH;;AACD,SAASK,mBAAT,CAA6B;EAAEJ,UAAU,GAAGK,MAAM,CAACC,iBAAtB;EAAyCT,UAAU,GAAGQ,MAAM,CAACC,iBAA7D;EAAgFL,QAAQ,EAAEM,WAA1F;EAAuGT;AAAvG,CAA7B,EAAkJ;EAC9I,IAAIU,OAAJ;EACA,IAAIP,QAAQ,GAAG,CAAf;EACA,IAAIQ,YAAJ;EACA,IAAIC,QAAQ,GAAG,KAAf;EACA,IAAIC,UAAU,GAAG,KAAjB;EACA,OAAO,SAASC,oBAAT,CAA8BV,MAA9B,EAAsC;IACzCD,QAAQ;IACR,IAAIY,QAAJ;;IACA,IAAI,CAACL,OAAD,IAAYE,QAAhB,EAA0B;MACtBA,QAAQ,GAAG,KAAX;MACAF,OAAO,GAAG,IAAId,aAAJ,CAAkBM,UAAlB,EAA8BH,UAA9B,EAA0CC,SAA1C,CAAV;MACAe,QAAQ,GAAGL,OAAO,CAACM,SAAR,CAAkB,IAAlB,CAAX;MACAL,YAAY,GAAGP,MAAM,CAACY,SAAP,CAAiB;QAC5BC,IAAI,CAACC,KAAD,EAAQ;UACRR,OAAO,CAACO,IAAR,CAAaC,KAAb;QACH,CAH2B;;QAI5BC,KAAK,CAACC,GAAD,EAAM;UACPR,QAAQ,GAAG,IAAX;UACAF,OAAO,CAACS,KAAR,CAAcC,GAAd;QACH,CAP2B;;QAQ5BC,QAAQ,GAAG;UACPR,UAAU,GAAG,IAAb;UACAF,YAAY,GAAGW,SAAf;UACAZ,OAAO,CAACW,QAAR;QACH;;MAZ2B,CAAjB,CAAf;;MAcA,IAAIR,UAAJ,EAAgB;QACZF,YAAY,GAAGW,SAAf;MACH;IACJ,CArBD,MAsBK;MACDP,QAAQ,GAAGL,OAAO,CAACM,SAAR,CAAkB,IAAlB,CAAX;IACH;;IACD,KAAKO,GAAL,CAAS,MAAM;MACXpB,QAAQ;MACRY,QAAQ,CAACS,WAAT;MACAT,QAAQ,GAAGO,SAAX;;MACA,IAAIX,YAAY,IAAI,CAACE,UAAjB,IAA+BJ,WAA/B,IAA8CN,QAAQ,KAAK,CAA/D,EAAkE;QAC9DQ,YAAY,CAACa,WAAb;QACAb,YAAY,GAAGW,SAAf;QACAZ,OAAO,GAAGY,SAAV;MACH;IACJ,CATD;EAUH,CAtCD;AAuCH"}, "metadata": {}, "sourceType": "module"}