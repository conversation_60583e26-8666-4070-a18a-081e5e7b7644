{"ast": null, "code": "const UnsubscriptionErrorImpl = (() => {\n  function UnsubscriptionErrorImpl(errors) {\n    Error.call(this);\n    this.message = errors ? `${errors.length} errors occurred during unsubscription:\n${errors.map((err, i) => `${i + 1}) ${err.toString()}`).join('\\n  ')}` : '';\n    this.name = 'UnsubscriptionError';\n    this.errors = errors;\n    return this;\n  }\n\n  UnsubscriptionErrorImpl.prototype = Object.create(Error.prototype);\n  return UnsubscriptionErrorImpl;\n})();\n\nexport const UnsubscriptionError = UnsubscriptionErrorImpl;", "map": {"version": 3, "names": ["UnsubscriptionErrorImpl", "errors", "Error", "call", "message", "length", "map", "err", "i", "toString", "join", "name", "prototype", "Object", "create", "UnsubscriptionError"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/UnsubscriptionError.js"], "sourcesContent": ["const UnsubscriptionErrorImpl = (() => {\n    function UnsubscriptionErrorImpl(errors) {\n        Error.call(this);\n        this.message = errors ?\n            `${errors.length} errors occurred during unsubscription:\n${errors.map((err, i) => `${i + 1}) ${err.toString()}`).join('\\n  ')}` : '';\n        this.name = 'UnsubscriptionError';\n        this.errors = errors;\n        return this;\n    }\n    UnsubscriptionErrorImpl.prototype = Object.create(Error.prototype);\n    return UnsubscriptionErrorImpl;\n})();\nexport const UnsubscriptionError = UnsubscriptionErrorImpl;\n"], "mappings": "AAAA,MAAMA,uBAAuB,GAAG,CAAC,MAAM;EACnC,SAASA,uBAAT,CAAiCC,MAAjC,EAAyC;IACrCC,KAAK,CAACC,IAAN,CAAW,IAAX;IACA,KAAKC,OAAL,GAAeH,MAAM,GAChB,GAAEA,MAAM,CAACI,MAAO;AAC7B,EAAEJ,MAAM,CAACK,GAAP,CAAW,CAACC,GAAD,EAAMC,CAAN,KAAa,GAAEA,CAAC,GAAG,CAAE,KAAID,GAAG,CAACE,QAAJ,EAAe,EAAnD,EAAsDC,IAAtD,CAA2D,MAA3D,CAAmE,EAFxC,GAE4C,EAFjE;IAGA,KAAKC,IAAL,GAAY,qBAAZ;IACA,KAAKV,MAAL,GAAcA,MAAd;IACA,OAAO,IAAP;EACH;;EACDD,uBAAuB,CAACY,SAAxB,GAAoCC,MAAM,CAACC,MAAP,CAAcZ,KAAK,CAACU,SAApB,CAApC;EACA,OAAOZ,uBAAP;AACH,CAZ+B,GAAhC;;AAaA,OAAO,MAAMe,mBAAmB,GAAGf,uBAA5B"}, "metadata": {}, "sourceType": "module"}