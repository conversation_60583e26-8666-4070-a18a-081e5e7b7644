{"ast": null, "code": "import { createSelector } from '@ngrx/store';\nimport { isDevMode } from '@angular/core';\n\nfunction getInitialEntityState() {\n  return {\n    ids: [],\n    entities: {}\n  };\n}\n\nfunction createInitialStateFactory() {\n  function getInitialState(additionalState = {}) {\n    return Object.assign(getInitialEntityState(), additionalState);\n  }\n\n  return {\n    getInitialState\n  };\n}\n\nfunction createSelectorsFactory() {\n  function getSelectors(selectState) {\n    const selectIds = state => state.ids;\n\n    const selectEntities = state => state.entities;\n\n    const selectAll = createSelector(selectIds, selectEntities, (ids, entities) => ids.map(id => entities[id]));\n    const selectTotal = createSelector(selectIds, ids => ids.length);\n\n    if (!selectState) {\n      return {\n        selectIds,\n        selectEntities,\n        selectAll,\n        selectTotal\n      };\n    }\n\n    return {\n      selectIds: createSelector(selectState, selectIds),\n      selectEntities: createSelector(selectState, selectEntities),\n      selectAll: createSelector(selectState, selectAll),\n      selectTotal: createSelector(selectState, selectTotal)\n    };\n  }\n\n  return {\n    getSelectors\n  };\n}\n\nvar DidMutate;\n\n(function (DidMutate) {\n  DidMutate[DidMutate[\"EntitiesOnly\"] = 0] = \"EntitiesOnly\";\n  DidMutate[DidMutate[\"Both\"] = 1] = \"Both\";\n  DidMutate[DidMutate[\"None\"] = 2] = \"None\";\n})(DidMutate || (DidMutate = {}));\n\nfunction createStateOperator(mutator) {\n  return function operation(arg, state) {\n    const clonedEntityState = {\n      ids: [...state.ids],\n      entities: { ...state.entities\n      }\n    };\n    const didMutate = mutator(arg, clonedEntityState);\n\n    if (didMutate === DidMutate.Both) {\n      return Object.assign({}, state, clonedEntityState);\n    }\n\n    if (didMutate === DidMutate.EntitiesOnly) {\n      return { ...state,\n        entities: clonedEntityState.entities\n      };\n    }\n\n    return state;\n  };\n}\n\nfunction selectIdValue(entity, selectId) {\n  const key = selectId(entity);\n\n  if (isDevMode() && key === undefined) {\n    console.warn('@ngrx/entity: The entity passed to the `selectId` implementation returned undefined.', 'You should probably provide your own `selectId` implementation.', 'The entity that was passed:', entity, 'The `selectId` implementation:', selectId.toString());\n  }\n\n  return key;\n}\n\nfunction createUnsortedStateAdapter(selectId) {\n  function addOneMutably(entity, state) {\n    const key = selectIdValue(entity, selectId);\n\n    if (key in state.entities) {\n      return DidMutate.None;\n    }\n\n    state.ids.push(key);\n    state.entities[key] = entity;\n    return DidMutate.Both;\n  }\n\n  function addManyMutably(entities, state) {\n    let didMutate = false;\n\n    for (const entity of entities) {\n      didMutate = addOneMutably(entity, state) !== DidMutate.None || didMutate;\n    }\n\n    return didMutate ? DidMutate.Both : DidMutate.None;\n  }\n\n  function setAllMutably(entities, state) {\n    state.ids = [];\n    state.entities = {};\n    addManyMutably(entities, state);\n    return DidMutate.Both;\n  }\n\n  function setOneMutably(entity, state) {\n    const key = selectIdValue(entity, selectId);\n\n    if (key in state.entities) {\n      state.entities[key] = entity;\n      return DidMutate.EntitiesOnly;\n    }\n\n    state.ids.push(key);\n    state.entities[key] = entity;\n    return DidMutate.Both;\n  }\n\n  function setManyMutably(entities, state) {\n    const didMutateSetOne = entities.map(entity => setOneMutably(entity, state));\n\n    switch (true) {\n      case didMutateSetOne.some(didMutate => didMutate === DidMutate.Both):\n        return DidMutate.Both;\n\n      case didMutateSetOne.some(didMutate => didMutate === DidMutate.EntitiesOnly):\n        return DidMutate.EntitiesOnly;\n\n      default:\n        return DidMutate.None;\n    }\n  }\n\n  function removeOneMutably(key, state) {\n    return removeManyMutably([key], state);\n  }\n\n  function removeManyMutably(keysOrPredicate, state) {\n    const keys = keysOrPredicate instanceof Array ? keysOrPredicate : state.ids.filter(key => keysOrPredicate(state.entities[key]));\n    const didMutate = keys.filter(key => key in state.entities).map(key => delete state.entities[key]).length > 0;\n\n    if (didMutate) {\n      state.ids = state.ids.filter(id => id in state.entities);\n    }\n\n    return didMutate ? DidMutate.Both : DidMutate.None;\n  }\n\n  function removeAll(state) {\n    return Object.assign({}, state, {\n      ids: [],\n      entities: {}\n    });\n  }\n\n  function takeNewKey(keys, update, state) {\n    const original = state.entities[update.id];\n    const updated = Object.assign({}, original, update.changes);\n    const newKey = selectIdValue(updated, selectId);\n    const hasNewKey = newKey !== update.id;\n\n    if (hasNewKey) {\n      keys[update.id] = newKey;\n      delete state.entities[update.id];\n    }\n\n    state.entities[newKey] = updated;\n    return hasNewKey;\n  }\n\n  function updateOneMutably(update, state) {\n    return updateManyMutably([update], state);\n  }\n\n  function updateManyMutably(updates, state) {\n    const newKeys = {};\n    updates = updates.filter(update => update.id in state.entities);\n    const didMutateEntities = updates.length > 0;\n\n    if (didMutateEntities) {\n      const didMutateIds = updates.filter(update => takeNewKey(newKeys, update, state)).length > 0;\n\n      if (didMutateIds) {\n        state.ids = state.ids.map(id => newKeys[id] || id);\n        return DidMutate.Both;\n      } else {\n        return DidMutate.EntitiesOnly;\n      }\n    }\n\n    return DidMutate.None;\n  }\n\n  function mapMutably(map, state) {\n    const changes = state.ids.reduce((changes, id) => {\n      const change = map(state.entities[id]);\n\n      if (change !== state.entities[id]) {\n        changes.push({\n          id,\n          changes: change\n        });\n      }\n\n      return changes;\n    }, []);\n    const updates = changes.filter(({\n      id\n    }) => id in state.entities);\n    return updateManyMutably(updates, state);\n  }\n\n  function mapOneMutably({\n    map,\n    id\n  }, state) {\n    const entity = state.entities[id];\n\n    if (!entity) {\n      return DidMutate.None;\n    }\n\n    const updatedEntity = map(entity);\n    return updateOneMutably({\n      id: id,\n      changes: updatedEntity\n    }, state);\n  }\n\n  function upsertOneMutably(entity, state) {\n    return upsertManyMutably([entity], state);\n  }\n\n  function upsertManyMutably(entities, state) {\n    const added = [];\n    const updated = [];\n\n    for (const entity of entities) {\n      const id = selectIdValue(entity, selectId);\n\n      if (id in state.entities) {\n        updated.push({\n          id,\n          changes: entity\n        });\n      } else {\n        added.push(entity);\n      }\n    }\n\n    const didMutateByUpdated = updateManyMutably(updated, state);\n    const didMutateByAdded = addManyMutably(added, state);\n\n    switch (true) {\n      case didMutateByAdded === DidMutate.None && didMutateByUpdated === DidMutate.None:\n        return DidMutate.None;\n\n      case didMutateByAdded === DidMutate.Both || didMutateByUpdated === DidMutate.Both:\n        return DidMutate.Both;\n\n      default:\n        return DidMutate.EntitiesOnly;\n    }\n  }\n\n  return {\n    removeAll,\n    addOne: createStateOperator(addOneMutably),\n    addMany: createStateOperator(addManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    upsertMany: createStateOperator(upsertManyMutably),\n    removeOne: createStateOperator(removeOneMutably),\n    removeMany: createStateOperator(removeManyMutably),\n    map: createStateOperator(mapMutably),\n    mapOne: createStateOperator(mapOneMutably)\n  };\n}\n\nfunction createSortedStateAdapter(selectId, sort) {\n  const {\n    removeOne,\n    removeMany,\n    removeAll\n  } = createUnsortedStateAdapter(selectId);\n\n  function addOneMutably(entity, state) {\n    return addManyMutably([entity], state);\n  }\n\n  function addManyMutably(newModels, state) {\n    const models = newModels.filter(model => !(selectIdValue(model, selectId) in state.entities));\n\n    if (models.length === 0) {\n      return DidMutate.None;\n    } else {\n      merge(models, state);\n      return DidMutate.Both;\n    }\n  }\n\n  function setAllMutably(models, state) {\n    state.entities = {};\n    state.ids = [];\n    addManyMutably(models, state);\n    return DidMutate.Both;\n  }\n\n  function setOneMutably(entity, state) {\n    const id = selectIdValue(entity, selectId);\n\n    if (id in state.entities) {\n      state.ids = state.ids.filter(val => val !== id);\n      merge([entity], state);\n      return DidMutate.Both;\n    } else {\n      return addOneMutably(entity, state);\n    }\n  }\n\n  function setManyMutably(entities, state) {\n    const didMutateSetOne = entities.map(entity => setOneMutably(entity, state));\n\n    switch (true) {\n      case didMutateSetOne.some(didMutate => didMutate === DidMutate.Both):\n        return DidMutate.Both;\n\n      case didMutateSetOne.some(didMutate => didMutate === DidMutate.EntitiesOnly):\n        return DidMutate.EntitiesOnly;\n\n      default:\n        return DidMutate.None;\n    }\n  }\n\n  function updateOneMutably(update, state) {\n    return updateManyMutably([update], state);\n  }\n\n  function takeUpdatedModel(models, update, state) {\n    if (!(update.id in state.entities)) {\n      return false;\n    }\n\n    const original = state.entities[update.id];\n    const updated = Object.assign({}, original, update.changes);\n    const newKey = selectIdValue(updated, selectId);\n    delete state.entities[update.id];\n    models.push(updated);\n    return newKey !== update.id;\n  }\n\n  function updateManyMutably(updates, state) {\n    const models = [];\n    const didMutateIds = updates.filter(update => takeUpdatedModel(models, update, state)).length > 0;\n\n    if (models.length === 0) {\n      return DidMutate.None;\n    } else {\n      const originalIds = state.ids;\n      const updatedIndexes = [];\n      state.ids = state.ids.filter((id, index) => {\n        if (id in state.entities) {\n          return true;\n        } else {\n          updatedIndexes.push(index);\n          return false;\n        }\n      });\n      merge(models, state);\n\n      if (!didMutateIds && updatedIndexes.every(i => state.ids[i] === originalIds[i])) {\n        return DidMutate.EntitiesOnly;\n      } else {\n        return DidMutate.Both;\n      }\n    }\n  }\n\n  function mapMutably(updatesOrMap, state) {\n    const updates = state.ids.reduce((changes, id) => {\n      const change = updatesOrMap(state.entities[id]);\n\n      if (change !== state.entities[id]) {\n        changes.push({\n          id,\n          changes: change\n        });\n      }\n\n      return changes;\n    }, []);\n    return updateManyMutably(updates, state);\n  }\n\n  function mapOneMutably({\n    map,\n    id\n  }, state) {\n    const entity = state.entities[id];\n\n    if (!entity) {\n      return DidMutate.None;\n    }\n\n    const updatedEntity = map(entity);\n    return updateOneMutably({\n      id: id,\n      changes: updatedEntity\n    }, state);\n  }\n\n  function upsertOneMutably(entity, state) {\n    return upsertManyMutably([entity], state);\n  }\n\n  function upsertManyMutably(entities, state) {\n    const added = [];\n    const updated = [];\n\n    for (const entity of entities) {\n      const id = selectIdValue(entity, selectId);\n\n      if (id in state.entities) {\n        updated.push({\n          id,\n          changes: entity\n        });\n      } else {\n        added.push(entity);\n      }\n    }\n\n    const didMutateByUpdated = updateManyMutably(updated, state);\n    const didMutateByAdded = addManyMutably(added, state);\n\n    switch (true) {\n      case didMutateByAdded === DidMutate.None && didMutateByUpdated === DidMutate.None:\n        return DidMutate.None;\n\n      case didMutateByAdded === DidMutate.Both || didMutateByUpdated === DidMutate.Both:\n        return DidMutate.Both;\n\n      default:\n        return DidMutate.EntitiesOnly;\n    }\n  }\n\n  function merge(models, state) {\n    models.sort(sort);\n    const ids = [];\n    let i = 0;\n    let j = 0;\n\n    while (i < models.length && j < state.ids.length) {\n      const model = models[i];\n      const modelId = selectIdValue(model, selectId);\n      const entityId = state.ids[j];\n      const entity = state.entities[entityId];\n\n      if (sort(model, entity) <= 0) {\n        ids.push(modelId);\n        i++;\n      } else {\n        ids.push(entityId);\n        j++;\n      }\n    }\n\n    if (i < models.length) {\n      state.ids = ids.concat(models.slice(i).map(selectId));\n    } else {\n      state.ids = ids.concat(state.ids.slice(j));\n    }\n\n    models.forEach((model, i) => {\n      state.entities[selectId(model)] = model;\n    });\n  }\n\n  return {\n    removeOne,\n    removeMany,\n    removeAll,\n    addOne: createStateOperator(addOneMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    setAll: createStateOperator(setAllMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    addMany: createStateOperator(addManyMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertMany: createStateOperator(upsertManyMutably),\n    map: createStateOperator(mapMutably),\n    mapOne: createStateOperator(mapOneMutably)\n  };\n}\n\nfunction createEntityAdapter(options = {}) {\n  const {\n    selectId,\n    sortComparer\n  } = {\n    selectId: options.selectId ?? (entity => entity.id),\n    sortComparer: options.sortComparer ?? false\n  };\n  const stateFactory = createInitialStateFactory();\n  const selectorsFactory = createSelectorsFactory();\n  const stateAdapter = sortComparer ? createSortedStateAdapter(selectId, sortComparer) : createUnsortedStateAdapter(selectId);\n  return {\n    selectId,\n    sortComparer,\n    ...stateFactory,\n    ...selectorsFactory,\n    ...stateAdapter\n  };\n}\n\nclass Dictionary {}\n/**\n * DO NOT EDIT\n *\n * This file is automatically generated at build\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Dictionary, createEntityAdapter };", "map": {"version": 3, "names": ["createSelector", "isDevMode", "getInitialEntityState", "ids", "entities", "createInitialStateFactory", "getInitialState", "additionalState", "Object", "assign", "createSelectorsFactory", "getSelectors", "selectState", "selectIds", "state", "selectEntities", "selectAll", "map", "id", "selectTotal", "length", "DidMutate", "createStateOperator", "mutator", "operation", "arg", "clonedEntityState", "didMutate", "Both", "EntitiesOnly", "selectIdValue", "entity", "selectId", "key", "undefined", "console", "warn", "toString", "createUnsortedStateAdapter", "addOneMutably", "None", "push", "addManyMutably", "setAllMutably", "setOneMutably", "setManyMutably", "didMutateSetOne", "some", "removeOneMutably", "removeManyMutably", "keysOrPredicate", "keys", "Array", "filter", "removeAll", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "update", "original", "updated", "changes", "new<PERSON>ey", "has<PERSON>ew<PERSON><PERSON>", "updateOneMutably", "updateManyMutably", "updates", "newKeys", "didMutateEntities", "didMutateIds", "mapMutably", "reduce", "change", "mapOneMutably", "updatedEntity", "upsertOneMutably", "upsertManyMutably", "added", "didMutateByUpdated", "didMutateByAdded", "addOne", "addMany", "setAll", "setOne", "setMany", "updateOne", "updateMany", "upsertOne", "upsertMany", "removeOne", "remove<PERSON>any", "mapOne", "createSortedStateAdapter", "sort", "newModels", "models", "model", "merge", "val", "takeUpdatedModel", "originalIds", "updatedIndexes", "index", "every", "i", "updatesOrMap", "j", "modelId", "entityId", "concat", "slice", "for<PERSON>ach", "createEntityAdapter", "options", "sortComparer", "stateFactory", "selectorsFactory", "stateAdapter", "Dictionary"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@ngrx/entity/fesm2020/ngrx-entity.mjs"], "sourcesContent": ["import { createSelector } from '@ngrx/store';\nimport { isDevMode } from '@angular/core';\n\nfunction getInitialEntityState() {\n    return {\n        ids: [],\n        entities: {},\n    };\n}\nfunction createInitialStateFactory() {\n    function getInitialState(additionalState = {}) {\n        return Object.assign(getInitialEntityState(), additionalState);\n    }\n    return { getInitialState };\n}\n\nfunction createSelectorsFactory() {\n    function getSelectors(selectState) {\n        const selectIds = (state) => state.ids;\n        const selectEntities = (state) => state.entities;\n        const selectAll = createSelector(selectIds, selectEntities, (ids, entities) => ids.map((id) => entities[id]));\n        const selectTotal = createSelector(selectIds, (ids) => ids.length);\n        if (!selectState) {\n            return {\n                selectIds,\n                selectEntities,\n                selectAll,\n                selectTotal,\n            };\n        }\n        return {\n            selectIds: createSelector(selectState, selectIds),\n            selectEntities: createSelector(selectState, selectEntities),\n            selectAll: createSelector(selectState, selectAll),\n            selectTotal: createSelector(selectState, selectTotal),\n        };\n    }\n    return { getSelectors };\n}\n\nvar DidMutate;\n(function (DidMutate) {\n    DidMutate[DidMutate[\"EntitiesOnly\"] = 0] = \"EntitiesOnly\";\n    DidMutate[DidMutate[\"Both\"] = 1] = \"Both\";\n    DidMutate[DidMutate[\"None\"] = 2] = \"None\";\n})(DidMutate || (DidMutate = {}));\nfunction createStateOperator(mutator) {\n    return function operation(arg, state) {\n        const clonedEntityState = {\n            ids: [...state.ids],\n            entities: { ...state.entities },\n        };\n        const didMutate = mutator(arg, clonedEntityState);\n        if (didMutate === DidMutate.Both) {\n            return Object.assign({}, state, clonedEntityState);\n        }\n        if (didMutate === DidMutate.EntitiesOnly) {\n            return {\n                ...state,\n                entities: clonedEntityState.entities,\n            };\n        }\n        return state;\n    };\n}\n\nfunction selectIdValue(entity, selectId) {\n    const key = selectId(entity);\n    if (isDevMode() && key === undefined) {\n        console.warn('@ngrx/entity: The entity passed to the `selectId` implementation returned undefined.', 'You should probably provide your own `selectId` implementation.', 'The entity that was passed:', entity, 'The `selectId` implementation:', selectId.toString());\n    }\n    return key;\n}\n\nfunction createUnsortedStateAdapter(selectId) {\n    function addOneMutably(entity, state) {\n        const key = selectIdValue(entity, selectId);\n        if (key in state.entities) {\n            return DidMutate.None;\n        }\n        state.ids.push(key);\n        state.entities[key] = entity;\n        return DidMutate.Both;\n    }\n    function addManyMutably(entities, state) {\n        let didMutate = false;\n        for (const entity of entities) {\n            didMutate = addOneMutably(entity, state) !== DidMutate.None || didMutate;\n        }\n        return didMutate ? DidMutate.Both : DidMutate.None;\n    }\n    function setAllMutably(entities, state) {\n        state.ids = [];\n        state.entities = {};\n        addManyMutably(entities, state);\n        return DidMutate.Both;\n    }\n    function setOneMutably(entity, state) {\n        const key = selectIdValue(entity, selectId);\n        if (key in state.entities) {\n            state.entities[key] = entity;\n            return DidMutate.EntitiesOnly;\n        }\n        state.ids.push(key);\n        state.entities[key] = entity;\n        return DidMutate.Both;\n    }\n    function setManyMutably(entities, state) {\n        const didMutateSetOne = entities.map((entity) => setOneMutably(entity, state));\n        switch (true) {\n            case didMutateSetOne.some((didMutate) => didMutate === DidMutate.Both):\n                return DidMutate.Both;\n            case didMutateSetOne.some((didMutate) => didMutate === DidMutate.EntitiesOnly):\n                return DidMutate.EntitiesOnly;\n            default:\n                return DidMutate.None;\n        }\n    }\n    function removeOneMutably(key, state) {\n        return removeManyMutably([key], state);\n    }\n    function removeManyMutably(keysOrPredicate, state) {\n        const keys = keysOrPredicate instanceof Array\n            ? keysOrPredicate\n            : state.ids.filter((key) => keysOrPredicate(state.entities[key]));\n        const didMutate = keys\n            .filter((key) => key in state.entities)\n            .map((key) => delete state.entities[key]).length > 0;\n        if (didMutate) {\n            state.ids = state.ids.filter((id) => id in state.entities);\n        }\n        return didMutate ? DidMutate.Both : DidMutate.None;\n    }\n    function removeAll(state) {\n        return Object.assign({}, state, {\n            ids: [],\n            entities: {},\n        });\n    }\n    function takeNewKey(keys, update, state) {\n        const original = state.entities[update.id];\n        const updated = Object.assign({}, original, update.changes);\n        const newKey = selectIdValue(updated, selectId);\n        const hasNewKey = newKey !== update.id;\n        if (hasNewKey) {\n            keys[update.id] = newKey;\n            delete state.entities[update.id];\n        }\n        state.entities[newKey] = updated;\n        return hasNewKey;\n    }\n    function updateOneMutably(update, state) {\n        return updateManyMutably([update], state);\n    }\n    function updateManyMutably(updates, state) {\n        const newKeys = {};\n        updates = updates.filter((update) => update.id in state.entities);\n        const didMutateEntities = updates.length > 0;\n        if (didMutateEntities) {\n            const didMutateIds = updates.filter((update) => takeNewKey(newKeys, update, state)).length >\n                0;\n            if (didMutateIds) {\n                state.ids = state.ids.map((id) => newKeys[id] || id);\n                return DidMutate.Both;\n            }\n            else {\n                return DidMutate.EntitiesOnly;\n            }\n        }\n        return DidMutate.None;\n    }\n    function mapMutably(map, state) {\n        const changes = state.ids.reduce((changes, id) => {\n            const change = map(state.entities[id]);\n            if (change !== state.entities[id]) {\n                changes.push({ id, changes: change });\n            }\n            return changes;\n        }, []);\n        const updates = changes.filter(({ id }) => id in state.entities);\n        return updateManyMutably(updates, state);\n    }\n    function mapOneMutably({ map, id }, state) {\n        const entity = state.entities[id];\n        if (!entity) {\n            return DidMutate.None;\n        }\n        const updatedEntity = map(entity);\n        return updateOneMutably({\n            id: id,\n            changes: updatedEntity,\n        }, state);\n    }\n    function upsertOneMutably(entity, state) {\n        return upsertManyMutably([entity], state);\n    }\n    function upsertManyMutably(entities, state) {\n        const added = [];\n        const updated = [];\n        for (const entity of entities) {\n            const id = selectIdValue(entity, selectId);\n            if (id in state.entities) {\n                updated.push({ id, changes: entity });\n            }\n            else {\n                added.push(entity);\n            }\n        }\n        const didMutateByUpdated = updateManyMutably(updated, state);\n        const didMutateByAdded = addManyMutably(added, state);\n        switch (true) {\n            case didMutateByAdded === DidMutate.None &&\n                didMutateByUpdated === DidMutate.None:\n                return DidMutate.None;\n            case didMutateByAdded === DidMutate.Both ||\n                didMutateByUpdated === DidMutate.Both:\n                return DidMutate.Both;\n            default:\n                return DidMutate.EntitiesOnly;\n        }\n    }\n    return {\n        removeAll,\n        addOne: createStateOperator(addOneMutably),\n        addMany: createStateOperator(addManyMutably),\n        setAll: createStateOperator(setAllMutably),\n        setOne: createStateOperator(setOneMutably),\n        setMany: createStateOperator(setManyMutably),\n        updateOne: createStateOperator(updateOneMutably),\n        updateMany: createStateOperator(updateManyMutably),\n        upsertOne: createStateOperator(upsertOneMutably),\n        upsertMany: createStateOperator(upsertManyMutably),\n        removeOne: createStateOperator(removeOneMutably),\n        removeMany: createStateOperator(removeManyMutably),\n        map: createStateOperator(mapMutably),\n        mapOne: createStateOperator(mapOneMutably),\n    };\n}\n\nfunction createSortedStateAdapter(selectId, sort) {\n    const { removeOne, removeMany, removeAll } = createUnsortedStateAdapter(selectId);\n    function addOneMutably(entity, state) {\n        return addManyMutably([entity], state);\n    }\n    function addManyMutably(newModels, state) {\n        const models = newModels.filter((model) => !(selectIdValue(model, selectId) in state.entities));\n        if (models.length === 0) {\n            return DidMutate.None;\n        }\n        else {\n            merge(models, state);\n            return DidMutate.Both;\n        }\n    }\n    function setAllMutably(models, state) {\n        state.entities = {};\n        state.ids = [];\n        addManyMutably(models, state);\n        return DidMutate.Both;\n    }\n    function setOneMutably(entity, state) {\n        const id = selectIdValue(entity, selectId);\n        if (id in state.entities) {\n            state.ids = state.ids.filter((val) => val !== id);\n            merge([entity], state);\n            return DidMutate.Both;\n        }\n        else {\n            return addOneMutably(entity, state);\n        }\n    }\n    function setManyMutably(entities, state) {\n        const didMutateSetOne = entities.map((entity) => setOneMutably(entity, state));\n        switch (true) {\n            case didMutateSetOne.some((didMutate) => didMutate === DidMutate.Both):\n                return DidMutate.Both;\n            case didMutateSetOne.some((didMutate) => didMutate === DidMutate.EntitiesOnly):\n                return DidMutate.EntitiesOnly;\n            default:\n                return DidMutate.None;\n        }\n    }\n    function updateOneMutably(update, state) {\n        return updateManyMutably([update], state);\n    }\n    function takeUpdatedModel(models, update, state) {\n        if (!(update.id in state.entities)) {\n            return false;\n        }\n        const original = state.entities[update.id];\n        const updated = Object.assign({}, original, update.changes);\n        const newKey = selectIdValue(updated, selectId);\n        delete state.entities[update.id];\n        models.push(updated);\n        return newKey !== update.id;\n    }\n    function updateManyMutably(updates, state) {\n        const models = [];\n        const didMutateIds = updates.filter((update) => takeUpdatedModel(models, update, state))\n            .length > 0;\n        if (models.length === 0) {\n            return DidMutate.None;\n        }\n        else {\n            const originalIds = state.ids;\n            const updatedIndexes = [];\n            state.ids = state.ids.filter((id, index) => {\n                if (id in state.entities) {\n                    return true;\n                }\n                else {\n                    updatedIndexes.push(index);\n                    return false;\n                }\n            });\n            merge(models, state);\n            if (!didMutateIds &&\n                updatedIndexes.every((i) => state.ids[i] === originalIds[i])) {\n                return DidMutate.EntitiesOnly;\n            }\n            else {\n                return DidMutate.Both;\n            }\n        }\n    }\n    function mapMutably(updatesOrMap, state) {\n        const updates = state.ids.reduce((changes, id) => {\n            const change = updatesOrMap(state.entities[id]);\n            if (change !== state.entities[id]) {\n                changes.push({ id, changes: change });\n            }\n            return changes;\n        }, []);\n        return updateManyMutably(updates, state);\n    }\n    function mapOneMutably({ map, id }, state) {\n        const entity = state.entities[id];\n        if (!entity) {\n            return DidMutate.None;\n        }\n        const updatedEntity = map(entity);\n        return updateOneMutably({\n            id: id,\n            changes: updatedEntity,\n        }, state);\n    }\n    function upsertOneMutably(entity, state) {\n        return upsertManyMutably([entity], state);\n    }\n    function upsertManyMutably(entities, state) {\n        const added = [];\n        const updated = [];\n        for (const entity of entities) {\n            const id = selectIdValue(entity, selectId);\n            if (id in state.entities) {\n                updated.push({ id, changes: entity });\n            }\n            else {\n                added.push(entity);\n            }\n        }\n        const didMutateByUpdated = updateManyMutably(updated, state);\n        const didMutateByAdded = addManyMutably(added, state);\n        switch (true) {\n            case didMutateByAdded === DidMutate.None &&\n                didMutateByUpdated === DidMutate.None:\n                return DidMutate.None;\n            case didMutateByAdded === DidMutate.Both ||\n                didMutateByUpdated === DidMutate.Both:\n                return DidMutate.Both;\n            default:\n                return DidMutate.EntitiesOnly;\n        }\n    }\n    function merge(models, state) {\n        models.sort(sort);\n        const ids = [];\n        let i = 0;\n        let j = 0;\n        while (i < models.length && j < state.ids.length) {\n            const model = models[i];\n            const modelId = selectIdValue(model, selectId);\n            const entityId = state.ids[j];\n            const entity = state.entities[entityId];\n            if (sort(model, entity) <= 0) {\n                ids.push(modelId);\n                i++;\n            }\n            else {\n                ids.push(entityId);\n                j++;\n            }\n        }\n        if (i < models.length) {\n            state.ids = ids.concat(models.slice(i).map(selectId));\n        }\n        else {\n            state.ids = ids.concat(state.ids.slice(j));\n        }\n        models.forEach((model, i) => {\n            state.entities[selectId(model)] = model;\n        });\n    }\n    return {\n        removeOne,\n        removeMany,\n        removeAll,\n        addOne: createStateOperator(addOneMutably),\n        updateOne: createStateOperator(updateOneMutably),\n        upsertOne: createStateOperator(upsertOneMutably),\n        setAll: createStateOperator(setAllMutably),\n        setOne: createStateOperator(setOneMutably),\n        setMany: createStateOperator(setManyMutably),\n        addMany: createStateOperator(addManyMutably),\n        updateMany: createStateOperator(updateManyMutably),\n        upsertMany: createStateOperator(upsertManyMutably),\n        map: createStateOperator(mapMutably),\n        mapOne: createStateOperator(mapOneMutably),\n    };\n}\n\nfunction createEntityAdapter(options = {}) {\n    const { selectId, sortComparer } = {\n        selectId: options.selectId ?? ((entity) => entity.id),\n        sortComparer: options.sortComparer ?? false,\n    };\n    const stateFactory = createInitialStateFactory();\n    const selectorsFactory = createSelectorsFactory();\n    const stateAdapter = sortComparer\n        ? createSortedStateAdapter(selectId, sortComparer)\n        : createUnsortedStateAdapter(selectId);\n    return {\n        selectId,\n        sortComparer,\n        ...stateFactory,\n        ...selectorsFactory,\n        ...stateAdapter,\n    };\n}\n\nclass Dictionary {\n}\n\n/**\n * DO NOT EDIT\n *\n * This file is automatically generated at build\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Dictionary, createEntityAdapter };\n"], "mappings": "AAAA,SAASA,cAAT,QAA+B,aAA/B;AACA,SAASC,SAAT,QAA0B,eAA1B;;AAEA,SAASC,qBAAT,GAAiC;EAC7B,OAAO;IACHC,GAAG,EAAE,EADF;IAEHC,QAAQ,EAAE;EAFP,CAAP;AAIH;;AACD,SAASC,yBAAT,GAAqC;EACjC,SAASC,eAAT,CAAyBC,eAAe,GAAG,EAA3C,EAA+C;IAC3C,OAAOC,MAAM,CAACC,MAAP,CAAcP,qBAAqB,EAAnC,EAAuCK,eAAvC,CAAP;EACH;;EACD,OAAO;IAAED;EAAF,CAAP;AACH;;AAED,SAASI,sBAAT,GAAkC;EAC9B,SAASC,YAAT,CAAsBC,WAAtB,EAAmC;IAC/B,MAAMC,SAAS,GAAIC,KAAD,IAAWA,KAAK,CAACX,GAAnC;;IACA,MAAMY,cAAc,GAAID,KAAD,IAAWA,KAAK,CAACV,QAAxC;;IACA,MAAMY,SAAS,GAAGhB,cAAc,CAACa,SAAD,EAAYE,cAAZ,EAA4B,CAACZ,GAAD,EAAMC,QAAN,KAAmBD,GAAG,CAACc,GAAJ,CAASC,EAAD,IAAQd,QAAQ,CAACc,EAAD,CAAxB,CAA/C,CAAhC;IACA,MAAMC,WAAW,GAAGnB,cAAc,CAACa,SAAD,EAAaV,GAAD,IAASA,GAAG,CAACiB,MAAzB,CAAlC;;IACA,IAAI,CAACR,WAAL,EAAkB;MACd,OAAO;QACHC,SADG;QAEHE,cAFG;QAGHC,SAHG;QAIHG;MAJG,CAAP;IAMH;;IACD,OAAO;MACHN,SAAS,EAAEb,cAAc,CAACY,WAAD,EAAcC,SAAd,CADtB;MAEHE,cAAc,EAAEf,cAAc,CAACY,WAAD,EAAcG,cAAd,CAF3B;MAGHC,SAAS,EAAEhB,cAAc,CAACY,WAAD,EAAcI,SAAd,CAHtB;MAIHG,WAAW,EAAEnB,cAAc,CAACY,WAAD,EAAcO,WAAd;IAJxB,CAAP;EAMH;;EACD,OAAO;IAAER;EAAF,CAAP;AACH;;AAED,IAAIU,SAAJ;;AACA,CAAC,UAAUA,SAAV,EAAqB;EAClBA,SAAS,CAACA,SAAS,CAAC,cAAD,CAAT,GAA4B,CAA7B,CAAT,GAA2C,cAA3C;EACAA,SAAS,CAACA,SAAS,CAAC,MAAD,CAAT,GAAoB,CAArB,CAAT,GAAmC,MAAnC;EACAA,SAAS,CAACA,SAAS,CAAC,MAAD,CAAT,GAAoB,CAArB,CAAT,GAAmC,MAAnC;AACH,CAJD,EAIGA,SAAS,KAAKA,SAAS,GAAG,EAAjB,CAJZ;;AAKA,SAASC,mBAAT,CAA6BC,OAA7B,EAAsC;EAClC,OAAO,SAASC,SAAT,CAAmBC,GAAnB,EAAwBX,KAAxB,EAA+B;IAClC,MAAMY,iBAAiB,GAAG;MACtBvB,GAAG,EAAE,CAAC,GAAGW,KAAK,CAACX,GAAV,CADiB;MAEtBC,QAAQ,EAAE,EAAE,GAAGU,KAAK,CAACV;MAAX;IAFY,CAA1B;IAIA,MAAMuB,SAAS,GAAGJ,OAAO,CAACE,GAAD,EAAMC,iBAAN,CAAzB;;IACA,IAAIC,SAAS,KAAKN,SAAS,CAACO,IAA5B,EAAkC;MAC9B,OAAOpB,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBK,KAAlB,EAAyBY,iBAAzB,CAAP;IACH;;IACD,IAAIC,SAAS,KAAKN,SAAS,CAACQ,YAA5B,EAA0C;MACtC,OAAO,EACH,GAAGf,KADA;QAEHV,QAAQ,EAAEsB,iBAAiB,CAACtB;MAFzB,CAAP;IAIH;;IACD,OAAOU,KAAP;EACH,CAhBD;AAiBH;;AAED,SAASgB,aAAT,CAAuBC,MAAvB,EAA+BC,QAA/B,EAAyC;EACrC,MAAMC,GAAG,GAAGD,QAAQ,CAACD,MAAD,CAApB;;EACA,IAAI9B,SAAS,MAAMgC,GAAG,KAAKC,SAA3B,EAAsC;IAClCC,OAAO,CAACC,IAAR,CAAa,sFAAb,EAAqG,iEAArG,EAAwK,6BAAxK,EAAuML,MAAvM,EAA+M,gCAA/M,EAAiPC,QAAQ,CAACK,QAAT,EAAjP;EACH;;EACD,OAAOJ,GAAP;AACH;;AAED,SAASK,0BAAT,CAAoCN,QAApC,EAA8C;EAC1C,SAASO,aAAT,CAAuBR,MAAvB,EAA+BjB,KAA/B,EAAsC;IAClC,MAAMmB,GAAG,GAAGH,aAAa,CAACC,MAAD,EAASC,QAAT,CAAzB;;IACA,IAAIC,GAAG,IAAInB,KAAK,CAACV,QAAjB,EAA2B;MACvB,OAAOiB,SAAS,CAACmB,IAAjB;IACH;;IACD1B,KAAK,CAACX,GAAN,CAAUsC,IAAV,CAAeR,GAAf;IACAnB,KAAK,CAACV,QAAN,CAAe6B,GAAf,IAAsBF,MAAtB;IACA,OAAOV,SAAS,CAACO,IAAjB;EACH;;EACD,SAASc,cAAT,CAAwBtC,QAAxB,EAAkCU,KAAlC,EAAyC;IACrC,IAAIa,SAAS,GAAG,KAAhB;;IACA,KAAK,MAAMI,MAAX,IAAqB3B,QAArB,EAA+B;MAC3BuB,SAAS,GAAGY,aAAa,CAACR,MAAD,EAASjB,KAAT,CAAb,KAAiCO,SAAS,CAACmB,IAA3C,IAAmDb,SAA/D;IACH;;IACD,OAAOA,SAAS,GAAGN,SAAS,CAACO,IAAb,GAAoBP,SAAS,CAACmB,IAA9C;EACH;;EACD,SAASG,aAAT,CAAuBvC,QAAvB,EAAiCU,KAAjC,EAAwC;IACpCA,KAAK,CAACX,GAAN,GAAY,EAAZ;IACAW,KAAK,CAACV,QAAN,GAAiB,EAAjB;IACAsC,cAAc,CAACtC,QAAD,EAAWU,KAAX,CAAd;IACA,OAAOO,SAAS,CAACO,IAAjB;EACH;;EACD,SAASgB,aAAT,CAAuBb,MAAvB,EAA+BjB,KAA/B,EAAsC;IAClC,MAAMmB,GAAG,GAAGH,aAAa,CAACC,MAAD,EAASC,QAAT,CAAzB;;IACA,IAAIC,GAAG,IAAInB,KAAK,CAACV,QAAjB,EAA2B;MACvBU,KAAK,CAACV,QAAN,CAAe6B,GAAf,IAAsBF,MAAtB;MACA,OAAOV,SAAS,CAACQ,YAAjB;IACH;;IACDf,KAAK,CAACX,GAAN,CAAUsC,IAAV,CAAeR,GAAf;IACAnB,KAAK,CAACV,QAAN,CAAe6B,GAAf,IAAsBF,MAAtB;IACA,OAAOV,SAAS,CAACO,IAAjB;EACH;;EACD,SAASiB,cAAT,CAAwBzC,QAAxB,EAAkCU,KAAlC,EAAyC;IACrC,MAAMgC,eAAe,GAAG1C,QAAQ,CAACa,GAAT,CAAcc,MAAD,IAAYa,aAAa,CAACb,MAAD,EAASjB,KAAT,CAAtC,CAAxB;;IACA,QAAQ,IAAR;MACI,KAAKgC,eAAe,CAACC,IAAhB,CAAsBpB,SAAD,IAAeA,SAAS,KAAKN,SAAS,CAACO,IAA5D,CAAL;QACI,OAAOP,SAAS,CAACO,IAAjB;;MACJ,KAAKkB,eAAe,CAACC,IAAhB,CAAsBpB,SAAD,IAAeA,SAAS,KAAKN,SAAS,CAACQ,YAA5D,CAAL;QACI,OAAOR,SAAS,CAACQ,YAAjB;;MACJ;QACI,OAAOR,SAAS,CAACmB,IAAjB;IANR;EAQH;;EACD,SAASQ,gBAAT,CAA0Bf,GAA1B,EAA+BnB,KAA/B,EAAsC;IAClC,OAAOmC,iBAAiB,CAAC,CAAChB,GAAD,CAAD,EAAQnB,KAAR,CAAxB;EACH;;EACD,SAASmC,iBAAT,CAA2BC,eAA3B,EAA4CpC,KAA5C,EAAmD;IAC/C,MAAMqC,IAAI,GAAGD,eAAe,YAAYE,KAA3B,GACPF,eADO,GAEPpC,KAAK,CAACX,GAAN,CAAUkD,MAAV,CAAkBpB,GAAD,IAASiB,eAAe,CAACpC,KAAK,CAACV,QAAN,CAAe6B,GAAf,CAAD,CAAzC,CAFN;IAGA,MAAMN,SAAS,GAAGwB,IAAI,CACjBE,MADa,CACLpB,GAAD,IAASA,GAAG,IAAInB,KAAK,CAACV,QADhB,EAEba,GAFa,CAERgB,GAAD,IAAS,OAAOnB,KAAK,CAACV,QAAN,CAAe6B,GAAf,CAFP,EAE4Bb,MAF5B,GAEqC,CAFvD;;IAGA,IAAIO,SAAJ,EAAe;MACXb,KAAK,CAACX,GAAN,GAAYW,KAAK,CAACX,GAAN,CAAUkD,MAAV,CAAkBnC,EAAD,IAAQA,EAAE,IAAIJ,KAAK,CAACV,QAArC,CAAZ;IACH;;IACD,OAAOuB,SAAS,GAAGN,SAAS,CAACO,IAAb,GAAoBP,SAAS,CAACmB,IAA9C;EACH;;EACD,SAASc,SAAT,CAAmBxC,KAAnB,EAA0B;IACtB,OAAON,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBK,KAAlB,EAAyB;MAC5BX,GAAG,EAAE,EADuB;MAE5BC,QAAQ,EAAE;IAFkB,CAAzB,CAAP;EAIH;;EACD,SAASmD,UAAT,CAAoBJ,IAApB,EAA0BK,MAA1B,EAAkC1C,KAAlC,EAAyC;IACrC,MAAM2C,QAAQ,GAAG3C,KAAK,CAACV,QAAN,CAAeoD,MAAM,CAACtC,EAAtB,CAAjB;IACA,MAAMwC,OAAO,GAAGlD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBgD,QAAlB,EAA4BD,MAAM,CAACG,OAAnC,CAAhB;IACA,MAAMC,MAAM,GAAG9B,aAAa,CAAC4B,OAAD,EAAU1B,QAAV,CAA5B;IACA,MAAM6B,SAAS,GAAGD,MAAM,KAAKJ,MAAM,CAACtC,EAApC;;IACA,IAAI2C,SAAJ,EAAe;MACXV,IAAI,CAACK,MAAM,CAACtC,EAAR,CAAJ,GAAkB0C,MAAlB;MACA,OAAO9C,KAAK,CAACV,QAAN,CAAeoD,MAAM,CAACtC,EAAtB,CAAP;IACH;;IACDJ,KAAK,CAACV,QAAN,CAAewD,MAAf,IAAyBF,OAAzB;IACA,OAAOG,SAAP;EACH;;EACD,SAASC,gBAAT,CAA0BN,MAA1B,EAAkC1C,KAAlC,EAAyC;IACrC,OAAOiD,iBAAiB,CAAC,CAACP,MAAD,CAAD,EAAW1C,KAAX,CAAxB;EACH;;EACD,SAASiD,iBAAT,CAA2BC,OAA3B,EAAoClD,KAApC,EAA2C;IACvC,MAAMmD,OAAO,GAAG,EAAhB;IACAD,OAAO,GAAGA,OAAO,CAACX,MAAR,CAAgBG,MAAD,IAAYA,MAAM,CAACtC,EAAP,IAAaJ,KAAK,CAACV,QAA9C,CAAV;IACA,MAAM8D,iBAAiB,GAAGF,OAAO,CAAC5C,MAAR,GAAiB,CAA3C;;IACA,IAAI8C,iBAAJ,EAAuB;MACnB,MAAMC,YAAY,GAAGH,OAAO,CAACX,MAAR,CAAgBG,MAAD,IAAYD,UAAU,CAACU,OAAD,EAAUT,MAAV,EAAkB1C,KAAlB,CAArC,EAA+DM,MAA/D,GACjB,CADJ;;MAEA,IAAI+C,YAAJ,EAAkB;QACdrD,KAAK,CAACX,GAAN,GAAYW,KAAK,CAACX,GAAN,CAAUc,GAAV,CAAeC,EAAD,IAAQ+C,OAAO,CAAC/C,EAAD,CAAP,IAAeA,EAArC,CAAZ;QACA,OAAOG,SAAS,CAACO,IAAjB;MACH,CAHD,MAIK;QACD,OAAOP,SAAS,CAACQ,YAAjB;MACH;IACJ;;IACD,OAAOR,SAAS,CAACmB,IAAjB;EACH;;EACD,SAAS4B,UAAT,CAAoBnD,GAApB,EAAyBH,KAAzB,EAAgC;IAC5B,MAAM6C,OAAO,GAAG7C,KAAK,CAACX,GAAN,CAAUkE,MAAV,CAAiB,CAACV,OAAD,EAAUzC,EAAV,KAAiB;MAC9C,MAAMoD,MAAM,GAAGrD,GAAG,CAACH,KAAK,CAACV,QAAN,CAAec,EAAf,CAAD,CAAlB;;MACA,IAAIoD,MAAM,KAAKxD,KAAK,CAACV,QAAN,CAAec,EAAf,CAAf,EAAmC;QAC/ByC,OAAO,CAAClB,IAAR,CAAa;UAAEvB,EAAF;UAAMyC,OAAO,EAAEW;QAAf,CAAb;MACH;;MACD,OAAOX,OAAP;IACH,CANe,EAMb,EANa,CAAhB;IAOA,MAAMK,OAAO,GAAGL,OAAO,CAACN,MAAR,CAAe,CAAC;MAAEnC;IAAF,CAAD,KAAYA,EAAE,IAAIJ,KAAK,CAACV,QAAvC,CAAhB;IACA,OAAO2D,iBAAiB,CAACC,OAAD,EAAUlD,KAAV,CAAxB;EACH;;EACD,SAASyD,aAAT,CAAuB;IAAEtD,GAAF;IAAOC;EAAP,CAAvB,EAAoCJ,KAApC,EAA2C;IACvC,MAAMiB,MAAM,GAAGjB,KAAK,CAACV,QAAN,CAAec,EAAf,CAAf;;IACA,IAAI,CAACa,MAAL,EAAa;MACT,OAAOV,SAAS,CAACmB,IAAjB;IACH;;IACD,MAAMgC,aAAa,GAAGvD,GAAG,CAACc,MAAD,CAAzB;IACA,OAAO+B,gBAAgB,CAAC;MACpB5C,EAAE,EAAEA,EADgB;MAEpByC,OAAO,EAAEa;IAFW,CAAD,EAGpB1D,KAHoB,CAAvB;EAIH;;EACD,SAAS2D,gBAAT,CAA0B1C,MAA1B,EAAkCjB,KAAlC,EAAyC;IACrC,OAAO4D,iBAAiB,CAAC,CAAC3C,MAAD,CAAD,EAAWjB,KAAX,CAAxB;EACH;;EACD,SAAS4D,iBAAT,CAA2BtE,QAA3B,EAAqCU,KAArC,EAA4C;IACxC,MAAM6D,KAAK,GAAG,EAAd;IACA,MAAMjB,OAAO,GAAG,EAAhB;;IACA,KAAK,MAAM3B,MAAX,IAAqB3B,QAArB,EAA+B;MAC3B,MAAMc,EAAE,GAAGY,aAAa,CAACC,MAAD,EAASC,QAAT,CAAxB;;MACA,IAAId,EAAE,IAAIJ,KAAK,CAACV,QAAhB,EAA0B;QACtBsD,OAAO,CAACjB,IAAR,CAAa;UAAEvB,EAAF;UAAMyC,OAAO,EAAE5B;QAAf,CAAb;MACH,CAFD,MAGK;QACD4C,KAAK,CAAClC,IAAN,CAAWV,MAAX;MACH;IACJ;;IACD,MAAM6C,kBAAkB,GAAGb,iBAAiB,CAACL,OAAD,EAAU5C,KAAV,CAA5C;IACA,MAAM+D,gBAAgB,GAAGnC,cAAc,CAACiC,KAAD,EAAQ7D,KAAR,CAAvC;;IACA,QAAQ,IAAR;MACI,KAAK+D,gBAAgB,KAAKxD,SAAS,CAACmB,IAA/B,IACDoC,kBAAkB,KAAKvD,SAAS,CAACmB,IADrC;QAEI,OAAOnB,SAAS,CAACmB,IAAjB;;MACJ,KAAKqC,gBAAgB,KAAKxD,SAAS,CAACO,IAA/B,IACDgD,kBAAkB,KAAKvD,SAAS,CAACO,IADrC;QAEI,OAAOP,SAAS,CAACO,IAAjB;;MACJ;QACI,OAAOP,SAAS,CAACQ,YAAjB;IARR;EAUH;;EACD,OAAO;IACHyB,SADG;IAEHwB,MAAM,EAAExD,mBAAmB,CAACiB,aAAD,CAFxB;IAGHwC,OAAO,EAAEzD,mBAAmB,CAACoB,cAAD,CAHzB;IAIHsC,MAAM,EAAE1D,mBAAmB,CAACqB,aAAD,CAJxB;IAKHsC,MAAM,EAAE3D,mBAAmB,CAACsB,aAAD,CALxB;IAMHsC,OAAO,EAAE5D,mBAAmB,CAACuB,cAAD,CANzB;IAOHsC,SAAS,EAAE7D,mBAAmB,CAACwC,gBAAD,CAP3B;IAQHsB,UAAU,EAAE9D,mBAAmB,CAACyC,iBAAD,CAR5B;IASHsB,SAAS,EAAE/D,mBAAmB,CAACmD,gBAAD,CAT3B;IAUHa,UAAU,EAAEhE,mBAAmB,CAACoD,iBAAD,CAV5B;IAWHa,SAAS,EAAEjE,mBAAmB,CAAC0B,gBAAD,CAX3B;IAYHwC,UAAU,EAAElE,mBAAmB,CAAC2B,iBAAD,CAZ5B;IAaHhC,GAAG,EAAEK,mBAAmB,CAAC8C,UAAD,CAbrB;IAcHqB,MAAM,EAAEnE,mBAAmB,CAACiD,aAAD;EAdxB,CAAP;AAgBH;;AAED,SAASmB,wBAAT,CAAkC1D,QAAlC,EAA4C2D,IAA5C,EAAkD;EAC9C,MAAM;IAAEJ,SAAF;IAAaC,UAAb;IAAyBlC;EAAzB,IAAuChB,0BAA0B,CAACN,QAAD,CAAvE;;EACA,SAASO,aAAT,CAAuBR,MAAvB,EAA+BjB,KAA/B,EAAsC;IAClC,OAAO4B,cAAc,CAAC,CAACX,MAAD,CAAD,EAAWjB,KAAX,CAArB;EACH;;EACD,SAAS4B,cAAT,CAAwBkD,SAAxB,EAAmC9E,KAAnC,EAA0C;IACtC,MAAM+E,MAAM,GAAGD,SAAS,CAACvC,MAAV,CAAkByC,KAAD,IAAW,EAAEhE,aAAa,CAACgE,KAAD,EAAQ9D,QAAR,CAAb,IAAkClB,KAAK,CAACV,QAA1C,CAA5B,CAAf;;IACA,IAAIyF,MAAM,CAACzE,MAAP,KAAkB,CAAtB,EAAyB;MACrB,OAAOC,SAAS,CAACmB,IAAjB;IACH,CAFD,MAGK;MACDuD,KAAK,CAACF,MAAD,EAAS/E,KAAT,CAAL;MACA,OAAOO,SAAS,CAACO,IAAjB;IACH;EACJ;;EACD,SAASe,aAAT,CAAuBkD,MAAvB,EAA+B/E,KAA/B,EAAsC;IAClCA,KAAK,CAACV,QAAN,GAAiB,EAAjB;IACAU,KAAK,CAACX,GAAN,GAAY,EAAZ;IACAuC,cAAc,CAACmD,MAAD,EAAS/E,KAAT,CAAd;IACA,OAAOO,SAAS,CAACO,IAAjB;EACH;;EACD,SAASgB,aAAT,CAAuBb,MAAvB,EAA+BjB,KAA/B,EAAsC;IAClC,MAAMI,EAAE,GAAGY,aAAa,CAACC,MAAD,EAASC,QAAT,CAAxB;;IACA,IAAId,EAAE,IAAIJ,KAAK,CAACV,QAAhB,EAA0B;MACtBU,KAAK,CAACX,GAAN,GAAYW,KAAK,CAACX,GAAN,CAAUkD,MAAV,CAAkB2C,GAAD,IAASA,GAAG,KAAK9E,EAAlC,CAAZ;MACA6E,KAAK,CAAC,CAAChE,MAAD,CAAD,EAAWjB,KAAX,CAAL;MACA,OAAOO,SAAS,CAACO,IAAjB;IACH,CAJD,MAKK;MACD,OAAOW,aAAa,CAACR,MAAD,EAASjB,KAAT,CAApB;IACH;EACJ;;EACD,SAAS+B,cAAT,CAAwBzC,QAAxB,EAAkCU,KAAlC,EAAyC;IACrC,MAAMgC,eAAe,GAAG1C,QAAQ,CAACa,GAAT,CAAcc,MAAD,IAAYa,aAAa,CAACb,MAAD,EAASjB,KAAT,CAAtC,CAAxB;;IACA,QAAQ,IAAR;MACI,KAAKgC,eAAe,CAACC,IAAhB,CAAsBpB,SAAD,IAAeA,SAAS,KAAKN,SAAS,CAACO,IAA5D,CAAL;QACI,OAAOP,SAAS,CAACO,IAAjB;;MACJ,KAAKkB,eAAe,CAACC,IAAhB,CAAsBpB,SAAD,IAAeA,SAAS,KAAKN,SAAS,CAACQ,YAA5D,CAAL;QACI,OAAOR,SAAS,CAACQ,YAAjB;;MACJ;QACI,OAAOR,SAAS,CAACmB,IAAjB;IANR;EAQH;;EACD,SAASsB,gBAAT,CAA0BN,MAA1B,EAAkC1C,KAAlC,EAAyC;IACrC,OAAOiD,iBAAiB,CAAC,CAACP,MAAD,CAAD,EAAW1C,KAAX,CAAxB;EACH;;EACD,SAASmF,gBAAT,CAA0BJ,MAA1B,EAAkCrC,MAAlC,EAA0C1C,KAA1C,EAAiD;IAC7C,IAAI,EAAE0C,MAAM,CAACtC,EAAP,IAAaJ,KAAK,CAACV,QAArB,CAAJ,EAAoC;MAChC,OAAO,KAAP;IACH;;IACD,MAAMqD,QAAQ,GAAG3C,KAAK,CAACV,QAAN,CAAeoD,MAAM,CAACtC,EAAtB,CAAjB;IACA,MAAMwC,OAAO,GAAGlD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBgD,QAAlB,EAA4BD,MAAM,CAACG,OAAnC,CAAhB;IACA,MAAMC,MAAM,GAAG9B,aAAa,CAAC4B,OAAD,EAAU1B,QAAV,CAA5B;IACA,OAAOlB,KAAK,CAACV,QAAN,CAAeoD,MAAM,CAACtC,EAAtB,CAAP;IACA2E,MAAM,CAACpD,IAAP,CAAYiB,OAAZ;IACA,OAAOE,MAAM,KAAKJ,MAAM,CAACtC,EAAzB;EACH;;EACD,SAAS6C,iBAAT,CAA2BC,OAA3B,EAAoClD,KAApC,EAA2C;IACvC,MAAM+E,MAAM,GAAG,EAAf;IACA,MAAM1B,YAAY,GAAGH,OAAO,CAACX,MAAR,CAAgBG,MAAD,IAAYyC,gBAAgB,CAACJ,MAAD,EAASrC,MAAT,EAAiB1C,KAAjB,CAA3C,EAChBM,MADgB,GACP,CADd;;IAEA,IAAIyE,MAAM,CAACzE,MAAP,KAAkB,CAAtB,EAAyB;MACrB,OAAOC,SAAS,CAACmB,IAAjB;IACH,CAFD,MAGK;MACD,MAAM0D,WAAW,GAAGpF,KAAK,CAACX,GAA1B;MACA,MAAMgG,cAAc,GAAG,EAAvB;MACArF,KAAK,CAACX,GAAN,GAAYW,KAAK,CAACX,GAAN,CAAUkD,MAAV,CAAiB,CAACnC,EAAD,EAAKkF,KAAL,KAAe;QACxC,IAAIlF,EAAE,IAAIJ,KAAK,CAACV,QAAhB,EAA0B;UACtB,OAAO,IAAP;QACH,CAFD,MAGK;UACD+F,cAAc,CAAC1D,IAAf,CAAoB2D,KAApB;UACA,OAAO,KAAP;QACH;MACJ,CARW,CAAZ;MASAL,KAAK,CAACF,MAAD,EAAS/E,KAAT,CAAL;;MACA,IAAI,CAACqD,YAAD,IACAgC,cAAc,CAACE,KAAf,CAAsBC,CAAD,IAAOxF,KAAK,CAACX,GAAN,CAAUmG,CAAV,MAAiBJ,WAAW,CAACI,CAAD,CAAxD,CADJ,EACkE;QAC9D,OAAOjF,SAAS,CAACQ,YAAjB;MACH,CAHD,MAIK;QACD,OAAOR,SAAS,CAACO,IAAjB;MACH;IACJ;EACJ;;EACD,SAASwC,UAAT,CAAoBmC,YAApB,EAAkCzF,KAAlC,EAAyC;IACrC,MAAMkD,OAAO,GAAGlD,KAAK,CAACX,GAAN,CAAUkE,MAAV,CAAiB,CAACV,OAAD,EAAUzC,EAAV,KAAiB;MAC9C,MAAMoD,MAAM,GAAGiC,YAAY,CAACzF,KAAK,CAACV,QAAN,CAAec,EAAf,CAAD,CAA3B;;MACA,IAAIoD,MAAM,KAAKxD,KAAK,CAACV,QAAN,CAAec,EAAf,CAAf,EAAmC;QAC/ByC,OAAO,CAAClB,IAAR,CAAa;UAAEvB,EAAF;UAAMyC,OAAO,EAAEW;QAAf,CAAb;MACH;;MACD,OAAOX,OAAP;IACH,CANe,EAMb,EANa,CAAhB;IAOA,OAAOI,iBAAiB,CAACC,OAAD,EAAUlD,KAAV,CAAxB;EACH;;EACD,SAASyD,aAAT,CAAuB;IAAEtD,GAAF;IAAOC;EAAP,CAAvB,EAAoCJ,KAApC,EAA2C;IACvC,MAAMiB,MAAM,GAAGjB,KAAK,CAACV,QAAN,CAAec,EAAf,CAAf;;IACA,IAAI,CAACa,MAAL,EAAa;MACT,OAAOV,SAAS,CAACmB,IAAjB;IACH;;IACD,MAAMgC,aAAa,GAAGvD,GAAG,CAACc,MAAD,CAAzB;IACA,OAAO+B,gBAAgB,CAAC;MACpB5C,EAAE,EAAEA,EADgB;MAEpByC,OAAO,EAAEa;IAFW,CAAD,EAGpB1D,KAHoB,CAAvB;EAIH;;EACD,SAAS2D,gBAAT,CAA0B1C,MAA1B,EAAkCjB,KAAlC,EAAyC;IACrC,OAAO4D,iBAAiB,CAAC,CAAC3C,MAAD,CAAD,EAAWjB,KAAX,CAAxB;EACH;;EACD,SAAS4D,iBAAT,CAA2BtE,QAA3B,EAAqCU,KAArC,EAA4C;IACxC,MAAM6D,KAAK,GAAG,EAAd;IACA,MAAMjB,OAAO,GAAG,EAAhB;;IACA,KAAK,MAAM3B,MAAX,IAAqB3B,QAArB,EAA+B;MAC3B,MAAMc,EAAE,GAAGY,aAAa,CAACC,MAAD,EAASC,QAAT,CAAxB;;MACA,IAAId,EAAE,IAAIJ,KAAK,CAACV,QAAhB,EAA0B;QACtBsD,OAAO,CAACjB,IAAR,CAAa;UAAEvB,EAAF;UAAMyC,OAAO,EAAE5B;QAAf,CAAb;MACH,CAFD,MAGK;QACD4C,KAAK,CAAClC,IAAN,CAAWV,MAAX;MACH;IACJ;;IACD,MAAM6C,kBAAkB,GAAGb,iBAAiB,CAACL,OAAD,EAAU5C,KAAV,CAA5C;IACA,MAAM+D,gBAAgB,GAAGnC,cAAc,CAACiC,KAAD,EAAQ7D,KAAR,CAAvC;;IACA,QAAQ,IAAR;MACI,KAAK+D,gBAAgB,KAAKxD,SAAS,CAACmB,IAA/B,IACDoC,kBAAkB,KAAKvD,SAAS,CAACmB,IADrC;QAEI,OAAOnB,SAAS,CAACmB,IAAjB;;MACJ,KAAKqC,gBAAgB,KAAKxD,SAAS,CAACO,IAA/B,IACDgD,kBAAkB,KAAKvD,SAAS,CAACO,IADrC;QAEI,OAAOP,SAAS,CAACO,IAAjB;;MACJ;QACI,OAAOP,SAAS,CAACQ,YAAjB;IARR;EAUH;;EACD,SAASkE,KAAT,CAAeF,MAAf,EAAuB/E,KAAvB,EAA8B;IAC1B+E,MAAM,CAACF,IAAP,CAAYA,IAAZ;IACA,MAAMxF,GAAG,GAAG,EAAZ;IACA,IAAImG,CAAC,GAAG,CAAR;IACA,IAAIE,CAAC,GAAG,CAAR;;IACA,OAAOF,CAAC,GAAGT,MAAM,CAACzE,MAAX,IAAqBoF,CAAC,GAAG1F,KAAK,CAACX,GAAN,CAAUiB,MAA1C,EAAkD;MAC9C,MAAM0E,KAAK,GAAGD,MAAM,CAACS,CAAD,CAApB;MACA,MAAMG,OAAO,GAAG3E,aAAa,CAACgE,KAAD,EAAQ9D,QAAR,CAA7B;MACA,MAAM0E,QAAQ,GAAG5F,KAAK,CAACX,GAAN,CAAUqG,CAAV,CAAjB;MACA,MAAMzE,MAAM,GAAGjB,KAAK,CAACV,QAAN,CAAesG,QAAf,CAAf;;MACA,IAAIf,IAAI,CAACG,KAAD,EAAQ/D,MAAR,CAAJ,IAAuB,CAA3B,EAA8B;QAC1B5B,GAAG,CAACsC,IAAJ,CAASgE,OAAT;QACAH,CAAC;MACJ,CAHD,MAIK;QACDnG,GAAG,CAACsC,IAAJ,CAASiE,QAAT;QACAF,CAAC;MACJ;IACJ;;IACD,IAAIF,CAAC,GAAGT,MAAM,CAACzE,MAAf,EAAuB;MACnBN,KAAK,CAACX,GAAN,GAAYA,GAAG,CAACwG,MAAJ,CAAWd,MAAM,CAACe,KAAP,CAAaN,CAAb,EAAgBrF,GAAhB,CAAoBe,QAApB,CAAX,CAAZ;IACH,CAFD,MAGK;MACDlB,KAAK,CAACX,GAAN,GAAYA,GAAG,CAACwG,MAAJ,CAAW7F,KAAK,CAACX,GAAN,CAAUyG,KAAV,CAAgBJ,CAAhB,CAAX,CAAZ;IACH;;IACDX,MAAM,CAACgB,OAAP,CAAe,CAACf,KAAD,EAAQQ,CAAR,KAAc;MACzBxF,KAAK,CAACV,QAAN,CAAe4B,QAAQ,CAAC8D,KAAD,CAAvB,IAAkCA,KAAlC;IACH,CAFD;EAGH;;EACD,OAAO;IACHP,SADG;IAEHC,UAFG;IAGHlC,SAHG;IAIHwB,MAAM,EAAExD,mBAAmB,CAACiB,aAAD,CAJxB;IAKH4C,SAAS,EAAE7D,mBAAmB,CAACwC,gBAAD,CAL3B;IAMHuB,SAAS,EAAE/D,mBAAmB,CAACmD,gBAAD,CAN3B;IAOHO,MAAM,EAAE1D,mBAAmB,CAACqB,aAAD,CAPxB;IAQHsC,MAAM,EAAE3D,mBAAmB,CAACsB,aAAD,CARxB;IASHsC,OAAO,EAAE5D,mBAAmB,CAACuB,cAAD,CATzB;IAUHkC,OAAO,EAAEzD,mBAAmB,CAACoB,cAAD,CAVzB;IAWH0C,UAAU,EAAE9D,mBAAmB,CAACyC,iBAAD,CAX5B;IAYHuB,UAAU,EAAEhE,mBAAmB,CAACoD,iBAAD,CAZ5B;IAaHzD,GAAG,EAAEK,mBAAmB,CAAC8C,UAAD,CAbrB;IAcHqB,MAAM,EAAEnE,mBAAmB,CAACiD,aAAD;EAdxB,CAAP;AAgBH;;AAED,SAASuC,mBAAT,CAA6BC,OAAO,GAAG,EAAvC,EAA2C;EACvC,MAAM;IAAE/E,QAAF;IAAYgF;EAAZ,IAA6B;IAC/BhF,QAAQ,EAAE+E,OAAO,CAAC/E,QAAR,KAAsBD,MAAD,IAAYA,MAAM,CAACb,EAAxC,CADqB;IAE/B8F,YAAY,EAAED,OAAO,CAACC,YAAR,IAAwB;EAFP,CAAnC;EAIA,MAAMC,YAAY,GAAG5G,yBAAyB,EAA9C;EACA,MAAM6G,gBAAgB,GAAGxG,sBAAsB,EAA/C;EACA,MAAMyG,YAAY,GAAGH,YAAY,GAC3BtB,wBAAwB,CAAC1D,QAAD,EAAWgF,YAAX,CADG,GAE3B1E,0BAA0B,CAACN,QAAD,CAFhC;EAGA,OAAO;IACHA,QADG;IAEHgF,YAFG;IAGH,GAAGC,YAHA;IAIH,GAAGC,gBAJA;IAKH,GAAGC;EALA,CAAP;AAOH;;AAED,MAAMC,UAAN,CAAiB;AAGjB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASA,UAAT,EAAqBN,mBAArB"}, "metadata": {}, "sourceType": "module"}