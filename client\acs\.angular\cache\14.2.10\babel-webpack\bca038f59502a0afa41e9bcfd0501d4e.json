{"ast": null, "code": "import { Subject } from '../Subject';\nimport { async } from '../scheduler/async';\nimport { Subscriber } from '../Subscriber';\nimport { isNumeric } from '../util/isNumeric';\nimport { isScheduler } from '../util/isScheduler';\nexport function windowTime(windowTimeSpan) {\n  let scheduler = async;\n  let windowCreationInterval = null;\n  let maxWindowSize = Number.POSITIVE_INFINITY;\n\n  if (isScheduler(arguments[3])) {\n    scheduler = arguments[3];\n  }\n\n  if (isScheduler(arguments[2])) {\n    scheduler = arguments[2];\n  } else if (isNumeric(arguments[2])) {\n    maxWindowSize = Number(arguments[2]);\n  }\n\n  if (isScheduler(arguments[1])) {\n    scheduler = arguments[1];\n  } else if (isNumeric(arguments[1])) {\n    windowCreationInterval = Number(arguments[1]);\n  }\n\n  return function windowTimeOperatorFunction(source) {\n    return source.lift(new WindowTimeOperator(windowTimeSpan, windowCreationInterval, maxWindowSize, scheduler));\n  };\n}\n\nclass WindowTimeOperator {\n  constructor(windowTimeSpan, windowCreationInterval, maxWindowSize, scheduler) {\n    this.windowTimeSpan = windowTimeSpan;\n    this.windowCreationInterval = windowCreationInterval;\n    this.maxWindowSize = maxWindowSize;\n    this.scheduler = scheduler;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new WindowTimeSubscriber(subscriber, this.windowTimeSpan, this.windowCreationInterval, this.maxWindowSize, this.scheduler));\n  }\n\n}\n\nclass CountedSubject extends Subject {\n  constructor() {\n    super(...arguments);\n    this._numberOfNextedValues = 0;\n  }\n\n  next(value) {\n    this._numberOfNextedValues++;\n    super.next(value);\n  }\n\n  get numberOfNextedValues() {\n    return this._numberOfNextedValues;\n  }\n\n}\n\nclass WindowTimeSubscriber extends Subscriber {\n  constructor(destination, windowTimeSpan, windowCreationInterval, maxWindowSize, scheduler) {\n    super(destination);\n    this.destination = destination;\n    this.windowTimeSpan = windowTimeSpan;\n    this.windowCreationInterval = windowCreationInterval;\n    this.maxWindowSize = maxWindowSize;\n    this.scheduler = scheduler;\n    this.windows = [];\n    const window = this.openWindow();\n\n    if (windowCreationInterval !== null && windowCreationInterval >= 0) {\n      const closeState = {\n        subscriber: this,\n        window,\n        context: null\n      };\n      const creationState = {\n        windowTimeSpan,\n        windowCreationInterval,\n        subscriber: this,\n        scheduler\n      };\n      this.add(scheduler.schedule(dispatchWindowClose, windowTimeSpan, closeState));\n      this.add(scheduler.schedule(dispatchWindowCreation, windowCreationInterval, creationState));\n    } else {\n      const timeSpanOnlyState = {\n        subscriber: this,\n        window,\n        windowTimeSpan\n      };\n      this.add(scheduler.schedule(dispatchWindowTimeSpanOnly, windowTimeSpan, timeSpanOnlyState));\n    }\n  }\n\n  _next(value) {\n    const windows = this.windows;\n    const len = windows.length;\n\n    for (let i = 0; i < len; i++) {\n      const window = windows[i];\n\n      if (!window.closed) {\n        window.next(value);\n\n        if (window.numberOfNextedValues >= this.maxWindowSize) {\n          this.closeWindow(window);\n        }\n      }\n    }\n  }\n\n  _error(err) {\n    const windows = this.windows;\n\n    while (windows.length > 0) {\n      windows.shift().error(err);\n    }\n\n    this.destination.error(err);\n  }\n\n  _complete() {\n    const windows = this.windows;\n\n    while (windows.length > 0) {\n      const window = windows.shift();\n\n      if (!window.closed) {\n        window.complete();\n      }\n    }\n\n    this.destination.complete();\n  }\n\n  openWindow() {\n    const window = new CountedSubject();\n    this.windows.push(window);\n    const destination = this.destination;\n    destination.next(window);\n    return window;\n  }\n\n  closeWindow(window) {\n    window.complete();\n    const windows = this.windows;\n    windows.splice(windows.indexOf(window), 1);\n  }\n\n}\n\nfunction dispatchWindowTimeSpanOnly(state) {\n  const {\n    subscriber,\n    windowTimeSpan,\n    window\n  } = state;\n\n  if (window) {\n    subscriber.closeWindow(window);\n  }\n\n  state.window = subscriber.openWindow();\n  this.schedule(state, windowTimeSpan);\n}\n\nfunction dispatchWindowCreation(state) {\n  const {\n    windowTimeSpan,\n    subscriber,\n    scheduler,\n    windowCreationInterval\n  } = state;\n  const window = subscriber.openWindow();\n  const action = this;\n  let context = {\n    action,\n    subscription: null\n  };\n  const timeSpanState = {\n    subscriber,\n    window,\n    context\n  };\n  context.subscription = scheduler.schedule(dispatchWindowClose, windowTimeSpan, timeSpanState);\n  action.add(context.subscription);\n  action.schedule(state, windowCreationInterval);\n}\n\nfunction dispatchWindowClose(state) {\n  const {\n    subscriber,\n    window,\n    context\n  } = state;\n\n  if (context && context.action && context.subscription) {\n    context.action.remove(context.subscription);\n  }\n\n  subscriber.closeWindow(window);\n}", "map": {"version": 3, "names": ["Subject", "async", "Subscriber", "isNumeric", "isScheduler", "windowTime", "windowTimeSpan", "scheduler", "windowCreationInterval", "maxWindowSize", "Number", "POSITIVE_INFINITY", "arguments", "windowTimeOperatorFunction", "source", "lift", "WindowTimeOperator", "constructor", "call", "subscriber", "subscribe", "WindowTimeSubscriber", "CountedSubject", "_numberOfNextedValues", "next", "value", "numberOfNextedValues", "destination", "windows", "window", "openWindow", "closeState", "context", "creationState", "add", "schedule", "dispatchWindowClose", "dispatchWindowCreation", "timeSpanOnlyState", "dispatchWindowTimeSpanOnly", "_next", "len", "length", "i", "closed", "closeWindow", "_error", "err", "shift", "error", "_complete", "complete", "push", "splice", "indexOf", "state", "action", "subscription", "timeSpanState", "remove"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/windowTime.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { async } from '../scheduler/async';\nimport { Subscriber } from '../Subscriber';\nimport { isNumeric } from '../util/isNumeric';\nimport { isScheduler } from '../util/isScheduler';\nexport function windowTime(windowTimeSpan) {\n    let scheduler = async;\n    let windowCreationInterval = null;\n    let maxWindowSize = Number.POSITIVE_INFINITY;\n    if (isScheduler(arguments[3])) {\n        scheduler = arguments[3];\n    }\n    if (isScheduler(arguments[2])) {\n        scheduler = arguments[2];\n    }\n    else if (isNumeric(arguments[2])) {\n        maxWindowSize = Number(arguments[2]);\n    }\n    if (isScheduler(arguments[1])) {\n        scheduler = arguments[1];\n    }\n    else if (isNumeric(arguments[1])) {\n        windowCreationInterval = Number(arguments[1]);\n    }\n    return function windowTimeOperatorFunction(source) {\n        return source.lift(new WindowTimeOperator(windowTimeSpan, windowCreationInterval, maxWindowSize, scheduler));\n    };\n}\nclass WindowTimeOperator {\n    constructor(windowTimeSpan, windowCreationInterval, maxWindowSize, scheduler) {\n        this.windowTimeSpan = windowTimeSpan;\n        this.windowCreationInterval = windowCreationInterval;\n        this.maxWindowSize = maxWindowSize;\n        this.scheduler = scheduler;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new WindowTimeSubscriber(subscriber, this.windowTimeSpan, this.windowCreationInterval, this.maxWindowSize, this.scheduler));\n    }\n}\nclass CountedSubject extends Subject {\n    constructor() {\n        super(...arguments);\n        this._numberOfNextedValues = 0;\n    }\n    next(value) {\n        this._numberOfNextedValues++;\n        super.next(value);\n    }\n    get numberOfNextedValues() {\n        return this._numberOfNextedValues;\n    }\n}\nclass WindowTimeSubscriber extends Subscriber {\n    constructor(destination, windowTimeSpan, windowCreationInterval, maxWindowSize, scheduler) {\n        super(destination);\n        this.destination = destination;\n        this.windowTimeSpan = windowTimeSpan;\n        this.windowCreationInterval = windowCreationInterval;\n        this.maxWindowSize = maxWindowSize;\n        this.scheduler = scheduler;\n        this.windows = [];\n        const window = this.openWindow();\n        if (windowCreationInterval !== null && windowCreationInterval >= 0) {\n            const closeState = { subscriber: this, window, context: null };\n            const creationState = { windowTimeSpan, windowCreationInterval, subscriber: this, scheduler };\n            this.add(scheduler.schedule(dispatchWindowClose, windowTimeSpan, closeState));\n            this.add(scheduler.schedule(dispatchWindowCreation, windowCreationInterval, creationState));\n        }\n        else {\n            const timeSpanOnlyState = { subscriber: this, window, windowTimeSpan };\n            this.add(scheduler.schedule(dispatchWindowTimeSpanOnly, windowTimeSpan, timeSpanOnlyState));\n        }\n    }\n    _next(value) {\n        const windows = this.windows;\n        const len = windows.length;\n        for (let i = 0; i < len; i++) {\n            const window = windows[i];\n            if (!window.closed) {\n                window.next(value);\n                if (window.numberOfNextedValues >= this.maxWindowSize) {\n                    this.closeWindow(window);\n                }\n            }\n        }\n    }\n    _error(err) {\n        const windows = this.windows;\n        while (windows.length > 0) {\n            windows.shift().error(err);\n        }\n        this.destination.error(err);\n    }\n    _complete() {\n        const windows = this.windows;\n        while (windows.length > 0) {\n            const window = windows.shift();\n            if (!window.closed) {\n                window.complete();\n            }\n        }\n        this.destination.complete();\n    }\n    openWindow() {\n        const window = new CountedSubject();\n        this.windows.push(window);\n        const destination = this.destination;\n        destination.next(window);\n        return window;\n    }\n    closeWindow(window) {\n        window.complete();\n        const windows = this.windows;\n        windows.splice(windows.indexOf(window), 1);\n    }\n}\nfunction dispatchWindowTimeSpanOnly(state) {\n    const { subscriber, windowTimeSpan, window } = state;\n    if (window) {\n        subscriber.closeWindow(window);\n    }\n    state.window = subscriber.openWindow();\n    this.schedule(state, windowTimeSpan);\n}\nfunction dispatchWindowCreation(state) {\n    const { windowTimeSpan, subscriber, scheduler, windowCreationInterval } = state;\n    const window = subscriber.openWindow();\n    const action = this;\n    let context = { action, subscription: null };\n    const timeSpanState = { subscriber, window, context };\n    context.subscription = scheduler.schedule(dispatchWindowClose, windowTimeSpan, timeSpanState);\n    action.add(context.subscription);\n    action.schedule(state, windowCreationInterval);\n}\nfunction dispatchWindowClose(state) {\n    const { subscriber, window, context } = state;\n    if (context && context.action && context.subscription) {\n        context.action.remove(context.subscription);\n    }\n    subscriber.closeWindow(window);\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,YAAxB;AACA,SAASC,KAAT,QAAsB,oBAAtB;AACA,SAASC,UAAT,QAA2B,eAA3B;AACA,SAASC,SAAT,QAA0B,mBAA1B;AACA,SAASC,WAAT,QAA4B,qBAA5B;AACA,OAAO,SAASC,UAAT,CAAoBC,cAApB,EAAoC;EACvC,IAAIC,SAAS,GAAGN,KAAhB;EACA,IAAIO,sBAAsB,GAAG,IAA7B;EACA,IAAIC,aAAa,GAAGC,MAAM,CAACC,iBAA3B;;EACA,IAAIP,WAAW,CAACQ,SAAS,CAAC,CAAD,CAAV,CAAf,EAA+B;IAC3BL,SAAS,GAAGK,SAAS,CAAC,CAAD,CAArB;EACH;;EACD,IAAIR,WAAW,CAACQ,SAAS,CAAC,CAAD,CAAV,CAAf,EAA+B;IAC3BL,SAAS,GAAGK,SAAS,CAAC,CAAD,CAArB;EACH,CAFD,MAGK,IAAIT,SAAS,CAACS,SAAS,CAAC,CAAD,CAAV,CAAb,EAA6B;IAC9BH,aAAa,GAAGC,MAAM,CAACE,SAAS,CAAC,CAAD,CAAV,CAAtB;EACH;;EACD,IAAIR,WAAW,CAACQ,SAAS,CAAC,CAAD,CAAV,CAAf,EAA+B;IAC3BL,SAAS,GAAGK,SAAS,CAAC,CAAD,CAArB;EACH,CAFD,MAGK,IAAIT,SAAS,CAACS,SAAS,CAAC,CAAD,CAAV,CAAb,EAA6B;IAC9BJ,sBAAsB,GAAGE,MAAM,CAACE,SAAS,CAAC,CAAD,CAAV,CAA/B;EACH;;EACD,OAAO,SAASC,0BAAT,CAAoCC,MAApC,EAA4C;IAC/C,OAAOA,MAAM,CAACC,IAAP,CAAY,IAAIC,kBAAJ,CAAuBV,cAAvB,EAAuCE,sBAAvC,EAA+DC,aAA/D,EAA8EF,SAA9E,CAAZ,CAAP;EACH,CAFD;AAGH;;AACD,MAAMS,kBAAN,CAAyB;EACrBC,WAAW,CAACX,cAAD,EAAiBE,sBAAjB,EAAyCC,aAAzC,EAAwDF,SAAxD,EAAmE;IAC1E,KAAKD,cAAL,GAAsBA,cAAtB;IACA,KAAKE,sBAAL,GAA8BA,sBAA9B;IACA,KAAKC,aAAL,GAAqBA,aAArB;IACA,KAAKF,SAAL,GAAiBA,SAAjB;EACH;;EACDW,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,oBAAJ,CAAyBF,UAAzB,EAAqC,KAAKb,cAA1C,EAA0D,KAAKE,sBAA/D,EAAuF,KAAKC,aAA5F,EAA2G,KAAKF,SAAhH,CAAjB,CAAP;EACH;;AAToB;;AAWzB,MAAMe,cAAN,SAA6BtB,OAA7B,CAAqC;EACjCiB,WAAW,GAAG;IACV,MAAM,GAAGL,SAAT;IACA,KAAKW,qBAAL,GAA6B,CAA7B;EACH;;EACDC,IAAI,CAACC,KAAD,EAAQ;IACR,KAAKF,qBAAL;IACA,MAAMC,IAAN,CAAWC,KAAX;EACH;;EACuB,IAApBC,oBAAoB,GAAG;IACvB,OAAO,KAAKH,qBAAZ;EACH;;AAXgC;;AAarC,MAAMF,oBAAN,SAAmCnB,UAAnC,CAA8C;EAC1Ce,WAAW,CAACU,WAAD,EAAcrB,cAAd,EAA8BE,sBAA9B,EAAsDC,aAAtD,EAAqEF,SAArE,EAAgF;IACvF,MAAMoB,WAAN;IACA,KAAKA,WAAL,GAAmBA,WAAnB;IACA,KAAKrB,cAAL,GAAsBA,cAAtB;IACA,KAAKE,sBAAL,GAA8BA,sBAA9B;IACA,KAAKC,aAAL,GAAqBA,aAArB;IACA,KAAKF,SAAL,GAAiBA,SAAjB;IACA,KAAKqB,OAAL,GAAe,EAAf;IACA,MAAMC,MAAM,GAAG,KAAKC,UAAL,EAAf;;IACA,IAAItB,sBAAsB,KAAK,IAA3B,IAAmCA,sBAAsB,IAAI,CAAjE,EAAoE;MAChE,MAAMuB,UAAU,GAAG;QAAEZ,UAAU,EAAE,IAAd;QAAoBU,MAApB;QAA4BG,OAAO,EAAE;MAArC,CAAnB;MACA,MAAMC,aAAa,GAAG;QAAE3B,cAAF;QAAkBE,sBAAlB;QAA0CW,UAAU,EAAE,IAAtD;QAA4DZ;MAA5D,CAAtB;MACA,KAAK2B,GAAL,CAAS3B,SAAS,CAAC4B,QAAV,CAAmBC,mBAAnB,EAAwC9B,cAAxC,EAAwDyB,UAAxD,CAAT;MACA,KAAKG,GAAL,CAAS3B,SAAS,CAAC4B,QAAV,CAAmBE,sBAAnB,EAA2C7B,sBAA3C,EAAmEyB,aAAnE,CAAT;IACH,CALD,MAMK;MACD,MAAMK,iBAAiB,GAAG;QAAEnB,UAAU,EAAE,IAAd;QAAoBU,MAApB;QAA4BvB;MAA5B,CAA1B;MACA,KAAK4B,GAAL,CAAS3B,SAAS,CAAC4B,QAAV,CAAmBI,0BAAnB,EAA+CjC,cAA/C,EAA+DgC,iBAA/D,CAAT;IACH;EACJ;;EACDE,KAAK,CAACf,KAAD,EAAQ;IACT,MAAMG,OAAO,GAAG,KAAKA,OAArB;IACA,MAAMa,GAAG,GAAGb,OAAO,CAACc,MAApB;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,GAApB,EAAyBE,CAAC,EAA1B,EAA8B;MAC1B,MAAMd,MAAM,GAAGD,OAAO,CAACe,CAAD,CAAtB;;MACA,IAAI,CAACd,MAAM,CAACe,MAAZ,EAAoB;QAChBf,MAAM,CAACL,IAAP,CAAYC,KAAZ;;QACA,IAAII,MAAM,CAACH,oBAAP,IAA+B,KAAKjB,aAAxC,EAAuD;UACnD,KAAKoC,WAAL,CAAiBhB,MAAjB;QACH;MACJ;IACJ;EACJ;;EACDiB,MAAM,CAACC,GAAD,EAAM;IACR,MAAMnB,OAAO,GAAG,KAAKA,OAArB;;IACA,OAAOA,OAAO,CAACc,MAAR,GAAiB,CAAxB,EAA2B;MACvBd,OAAO,CAACoB,KAAR,GAAgBC,KAAhB,CAAsBF,GAAtB;IACH;;IACD,KAAKpB,WAAL,CAAiBsB,KAAjB,CAAuBF,GAAvB;EACH;;EACDG,SAAS,GAAG;IACR,MAAMtB,OAAO,GAAG,KAAKA,OAArB;;IACA,OAAOA,OAAO,CAACc,MAAR,GAAiB,CAAxB,EAA2B;MACvB,MAAMb,MAAM,GAAGD,OAAO,CAACoB,KAAR,EAAf;;MACA,IAAI,CAACnB,MAAM,CAACe,MAAZ,EAAoB;QAChBf,MAAM,CAACsB,QAAP;MACH;IACJ;;IACD,KAAKxB,WAAL,CAAiBwB,QAAjB;EACH;;EACDrB,UAAU,GAAG;IACT,MAAMD,MAAM,GAAG,IAAIP,cAAJ,EAAf;IACA,KAAKM,OAAL,CAAawB,IAAb,CAAkBvB,MAAlB;IACA,MAAMF,WAAW,GAAG,KAAKA,WAAzB;IACAA,WAAW,CAACH,IAAZ,CAAiBK,MAAjB;IACA,OAAOA,MAAP;EACH;;EACDgB,WAAW,CAAChB,MAAD,EAAS;IAChBA,MAAM,CAACsB,QAAP;IACA,MAAMvB,OAAO,GAAG,KAAKA,OAArB;IACAA,OAAO,CAACyB,MAAR,CAAezB,OAAO,CAAC0B,OAAR,CAAgBzB,MAAhB,CAAf,EAAwC,CAAxC;EACH;;AA9DyC;;AAgE9C,SAASU,0BAAT,CAAoCgB,KAApC,EAA2C;EACvC,MAAM;IAAEpC,UAAF;IAAcb,cAAd;IAA8BuB;EAA9B,IAAyC0B,KAA/C;;EACA,IAAI1B,MAAJ,EAAY;IACRV,UAAU,CAAC0B,WAAX,CAAuBhB,MAAvB;EACH;;EACD0B,KAAK,CAAC1B,MAAN,GAAeV,UAAU,CAACW,UAAX,EAAf;EACA,KAAKK,QAAL,CAAcoB,KAAd,EAAqBjD,cAArB;AACH;;AACD,SAAS+B,sBAAT,CAAgCkB,KAAhC,EAAuC;EACnC,MAAM;IAAEjD,cAAF;IAAkBa,UAAlB;IAA8BZ,SAA9B;IAAyCC;EAAzC,IAAoE+C,KAA1E;EACA,MAAM1B,MAAM,GAAGV,UAAU,CAACW,UAAX,EAAf;EACA,MAAM0B,MAAM,GAAG,IAAf;EACA,IAAIxB,OAAO,GAAG;IAAEwB,MAAF;IAAUC,YAAY,EAAE;EAAxB,CAAd;EACA,MAAMC,aAAa,GAAG;IAAEvC,UAAF;IAAcU,MAAd;IAAsBG;EAAtB,CAAtB;EACAA,OAAO,CAACyB,YAAR,GAAuBlD,SAAS,CAAC4B,QAAV,CAAmBC,mBAAnB,EAAwC9B,cAAxC,EAAwDoD,aAAxD,CAAvB;EACAF,MAAM,CAACtB,GAAP,CAAWF,OAAO,CAACyB,YAAnB;EACAD,MAAM,CAACrB,QAAP,CAAgBoB,KAAhB,EAAuB/C,sBAAvB;AACH;;AACD,SAAS4B,mBAAT,CAA6BmB,KAA7B,EAAoC;EAChC,MAAM;IAAEpC,UAAF;IAAcU,MAAd;IAAsBG;EAAtB,IAAkCuB,KAAxC;;EACA,IAAIvB,OAAO,IAAIA,OAAO,CAACwB,MAAnB,IAA6BxB,OAAO,CAACyB,YAAzC,EAAuD;IACnDzB,OAAO,CAACwB,MAAR,CAAeG,MAAf,CAAsB3B,OAAO,CAACyB,YAA9B;EACH;;EACDtC,UAAU,CAAC0B,WAAX,CAAuBhB,MAAvB;AACH"}, "metadata": {}, "sourceType": "module"}