{"ast": null, "code": "import { BehaviorSubject } from '../BehaviorSubject';\nimport { multicast } from './multicast';\nexport function publishBehavior(value) {\n  return source => multicast(new BehaviorSubject(value))(source);\n}", "map": {"version": 3, "names": ["BehaviorSubject", "multicast", "publish<PERSON>eh<PERSON>or", "value", "source"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/publishBehavior.js"], "sourcesContent": ["import { BehaviorSubject } from '../BehaviorSubject';\nimport { multicast } from './multicast';\nexport function publishBehavior(value) {\n    return (source) => multicast(new BehaviorSubject(value))(source);\n}\n"], "mappings": "AAAA,SAASA,eAAT,QAAgC,oBAAhC;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,OAAO,SAASC,eAAT,CAAyBC,KAAzB,EAAgC;EACnC,OAAQC,MAAD,IAAYH,SAAS,CAAC,IAAID,eAAJ,CAAoBG,KAApB,CAAD,CAAT,CAAsCC,MAAtC,CAAnB;AACH"}, "metadata": {}, "sourceType": "module"}