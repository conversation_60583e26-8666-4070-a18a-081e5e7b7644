{"ast": null, "code": "import { Observable } from '../Observable';\nimport { identity } from '../util/identity';\nimport { isScheduler } from '../util/isScheduler';\nexport function generate(initialStateOrOptions, condition, iterate, resultSelectorOrObservable, scheduler) {\n  let resultSelector;\n  let initialState;\n\n  if (arguments.length == 1) {\n    const options = initialStateOrOptions;\n    initialState = options.initialState;\n    condition = options.condition;\n    iterate = options.iterate;\n    resultSelector = options.resultSelector || identity;\n    scheduler = options.scheduler;\n  } else if (resultSelectorOrObservable === undefined || isScheduler(resultSelectorOrObservable)) {\n    initialState = initialStateOrOptions;\n    resultSelector = identity;\n    scheduler = resultSelectorOrObservable;\n  } else {\n    initialState = initialStateOrOptions;\n    resultSelector = resultSelectorOrObservable;\n  }\n\n  return new Observable(subscriber => {\n    let state = initialState;\n\n    if (scheduler) {\n      return scheduler.schedule(dispatch, 0, {\n        subscriber,\n        iterate,\n        condition,\n        resultSelector,\n        state\n      });\n    }\n\n    do {\n      if (condition) {\n        let conditionResult;\n\n        try {\n          conditionResult = condition(state);\n        } catch (err) {\n          subscriber.error(err);\n          return undefined;\n        }\n\n        if (!conditionResult) {\n          subscriber.complete();\n          break;\n        }\n      }\n\n      let value;\n\n      try {\n        value = resultSelector(state);\n      } catch (err) {\n        subscriber.error(err);\n        return undefined;\n      }\n\n      subscriber.next(value);\n\n      if (subscriber.closed) {\n        break;\n      }\n\n      try {\n        state = iterate(state);\n      } catch (err) {\n        subscriber.error(err);\n        return undefined;\n      }\n    } while (true);\n\n    return undefined;\n  });\n}\n\nfunction dispatch(state) {\n  const {\n    subscriber,\n    condition\n  } = state;\n\n  if (subscriber.closed) {\n    return undefined;\n  }\n\n  if (state.needIterate) {\n    try {\n      state.state = state.iterate(state.state);\n    } catch (err) {\n      subscriber.error(err);\n      return undefined;\n    }\n  } else {\n    state.needIterate = true;\n  }\n\n  if (condition) {\n    let conditionResult;\n\n    try {\n      conditionResult = condition(state.state);\n    } catch (err) {\n      subscriber.error(err);\n      return undefined;\n    }\n\n    if (!conditionResult) {\n      subscriber.complete();\n      return undefined;\n    }\n\n    if (subscriber.closed) {\n      return undefined;\n    }\n  }\n\n  let value;\n\n  try {\n    value = state.resultSelector(state.state);\n  } catch (err) {\n    subscriber.error(err);\n    return undefined;\n  }\n\n  if (subscriber.closed) {\n    return undefined;\n  }\n\n  subscriber.next(value);\n\n  if (subscriber.closed) {\n    return undefined;\n  }\n\n  return this.schedule(state);\n}", "map": {"version": 3, "names": ["Observable", "identity", "isScheduler", "generate", "initialStateOrOptions", "condition", "iterate", "resultSelectorOrObservable", "scheduler", "resultSelector", "initialState", "arguments", "length", "options", "undefined", "subscriber", "state", "schedule", "dispatch", "conditionResult", "err", "error", "complete", "value", "next", "closed", "needIterate"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/generate.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { identity } from '../util/identity';\nimport { isScheduler } from '../util/isScheduler';\nexport function generate(initialStateOrOptions, condition, iterate, resultSelectorOrObservable, scheduler) {\n    let resultSelector;\n    let initialState;\n    if (arguments.length == 1) {\n        const options = initialStateOrOptions;\n        initialState = options.initialState;\n        condition = options.condition;\n        iterate = options.iterate;\n        resultSelector = options.resultSelector || identity;\n        scheduler = options.scheduler;\n    }\n    else if (resultSelectorOrObservable === undefined || isScheduler(resultSelectorOrObservable)) {\n        initialState = initialStateOrOptions;\n        resultSelector = identity;\n        scheduler = resultSelectorOrObservable;\n    }\n    else {\n        initialState = initialStateOrOptions;\n        resultSelector = resultSelectorOrObservable;\n    }\n    return new Observable(subscriber => {\n        let state = initialState;\n        if (scheduler) {\n            return scheduler.schedule(dispatch, 0, {\n                subscriber,\n                iterate,\n                condition,\n                resultSelector,\n                state\n            });\n        }\n        do {\n            if (condition) {\n                let conditionResult;\n                try {\n                    conditionResult = condition(state);\n                }\n                catch (err) {\n                    subscriber.error(err);\n                    return undefined;\n                }\n                if (!conditionResult) {\n                    subscriber.complete();\n                    break;\n                }\n            }\n            let value;\n            try {\n                value = resultSelector(state);\n            }\n            catch (err) {\n                subscriber.error(err);\n                return undefined;\n            }\n            subscriber.next(value);\n            if (subscriber.closed) {\n                break;\n            }\n            try {\n                state = iterate(state);\n            }\n            catch (err) {\n                subscriber.error(err);\n                return undefined;\n            }\n        } while (true);\n        return undefined;\n    });\n}\nfunction dispatch(state) {\n    const { subscriber, condition } = state;\n    if (subscriber.closed) {\n        return undefined;\n    }\n    if (state.needIterate) {\n        try {\n            state.state = state.iterate(state.state);\n        }\n        catch (err) {\n            subscriber.error(err);\n            return undefined;\n        }\n    }\n    else {\n        state.needIterate = true;\n    }\n    if (condition) {\n        let conditionResult;\n        try {\n            conditionResult = condition(state.state);\n        }\n        catch (err) {\n            subscriber.error(err);\n            return undefined;\n        }\n        if (!conditionResult) {\n            subscriber.complete();\n            return undefined;\n        }\n        if (subscriber.closed) {\n            return undefined;\n        }\n    }\n    let value;\n    try {\n        value = state.resultSelector(state.state);\n    }\n    catch (err) {\n        subscriber.error(err);\n        return undefined;\n    }\n    if (subscriber.closed) {\n        return undefined;\n    }\n    subscriber.next(value);\n    if (subscriber.closed) {\n        return undefined;\n    }\n    return this.schedule(state);\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,QAAT,QAAyB,kBAAzB;AACA,SAASC,WAAT,QAA4B,qBAA5B;AACA,OAAO,SAASC,QAAT,CAAkBC,qBAAlB,EAAyCC,SAAzC,EAAoDC,OAApD,EAA6DC,0BAA7D,EAAyFC,SAAzF,EAAoG;EACvG,IAAIC,cAAJ;EACA,IAAIC,YAAJ;;EACA,IAAIC,SAAS,CAACC,MAAV,IAAoB,CAAxB,EAA2B;IACvB,MAAMC,OAAO,GAAGT,qBAAhB;IACAM,YAAY,GAAGG,OAAO,CAACH,YAAvB;IACAL,SAAS,GAAGQ,OAAO,CAACR,SAApB;IACAC,OAAO,GAAGO,OAAO,CAACP,OAAlB;IACAG,cAAc,GAAGI,OAAO,CAACJ,cAAR,IAA0BR,QAA3C;IACAO,SAAS,GAAGK,OAAO,CAACL,SAApB;EACH,CAPD,MAQK,IAAID,0BAA0B,KAAKO,SAA/B,IAA4CZ,WAAW,CAACK,0BAAD,CAA3D,EAAyF;IAC1FG,YAAY,GAAGN,qBAAf;IACAK,cAAc,GAAGR,QAAjB;IACAO,SAAS,GAAGD,0BAAZ;EACH,CAJI,MAKA;IACDG,YAAY,GAAGN,qBAAf;IACAK,cAAc,GAAGF,0BAAjB;EACH;;EACD,OAAO,IAAIP,UAAJ,CAAee,UAAU,IAAI;IAChC,IAAIC,KAAK,GAAGN,YAAZ;;IACA,IAAIF,SAAJ,EAAe;MACX,OAAOA,SAAS,CAACS,QAAV,CAAmBC,QAAnB,EAA6B,CAA7B,EAAgC;QACnCH,UADmC;QAEnCT,OAFmC;QAGnCD,SAHmC;QAInCI,cAJmC;QAKnCO;MALmC,CAAhC,CAAP;IAOH;;IACD,GAAG;MACC,IAAIX,SAAJ,EAAe;QACX,IAAIc,eAAJ;;QACA,IAAI;UACAA,eAAe,GAAGd,SAAS,CAACW,KAAD,CAA3B;QACH,CAFD,CAGA,OAAOI,GAAP,EAAY;UACRL,UAAU,CAACM,KAAX,CAAiBD,GAAjB;UACA,OAAON,SAAP;QACH;;QACD,IAAI,CAACK,eAAL,EAAsB;UAClBJ,UAAU,CAACO,QAAX;UACA;QACH;MACJ;;MACD,IAAIC,KAAJ;;MACA,IAAI;QACAA,KAAK,GAAGd,cAAc,CAACO,KAAD,CAAtB;MACH,CAFD,CAGA,OAAOI,GAAP,EAAY;QACRL,UAAU,CAACM,KAAX,CAAiBD,GAAjB;QACA,OAAON,SAAP;MACH;;MACDC,UAAU,CAACS,IAAX,CAAgBD,KAAhB;;MACA,IAAIR,UAAU,CAACU,MAAf,EAAuB;QACnB;MACH;;MACD,IAAI;QACAT,KAAK,GAAGV,OAAO,CAACU,KAAD,CAAf;MACH,CAFD,CAGA,OAAOI,GAAP,EAAY;QACRL,UAAU,CAACM,KAAX,CAAiBD,GAAjB;QACA,OAAON,SAAP;MACH;IACJ,CAlCD,QAkCS,IAlCT;;IAmCA,OAAOA,SAAP;EACH,CA/CM,CAAP;AAgDH;;AACD,SAASI,QAAT,CAAkBF,KAAlB,EAAyB;EACrB,MAAM;IAAED,UAAF;IAAcV;EAAd,IAA4BW,KAAlC;;EACA,IAAID,UAAU,CAACU,MAAf,EAAuB;IACnB,OAAOX,SAAP;EACH;;EACD,IAAIE,KAAK,CAACU,WAAV,EAAuB;IACnB,IAAI;MACAV,KAAK,CAACA,KAAN,GAAcA,KAAK,CAACV,OAAN,CAAcU,KAAK,CAACA,KAApB,CAAd;IACH,CAFD,CAGA,OAAOI,GAAP,EAAY;MACRL,UAAU,CAACM,KAAX,CAAiBD,GAAjB;MACA,OAAON,SAAP;IACH;EACJ,CARD,MASK;IACDE,KAAK,CAACU,WAAN,GAAoB,IAApB;EACH;;EACD,IAAIrB,SAAJ,EAAe;IACX,IAAIc,eAAJ;;IACA,IAAI;MACAA,eAAe,GAAGd,SAAS,CAACW,KAAK,CAACA,KAAP,CAA3B;IACH,CAFD,CAGA,OAAOI,GAAP,EAAY;MACRL,UAAU,CAACM,KAAX,CAAiBD,GAAjB;MACA,OAAON,SAAP;IACH;;IACD,IAAI,CAACK,eAAL,EAAsB;MAClBJ,UAAU,CAACO,QAAX;MACA,OAAOR,SAAP;IACH;;IACD,IAAIC,UAAU,CAACU,MAAf,EAAuB;MACnB,OAAOX,SAAP;IACH;EACJ;;EACD,IAAIS,KAAJ;;EACA,IAAI;IACAA,KAAK,GAAGP,KAAK,CAACP,cAAN,CAAqBO,KAAK,CAACA,KAA3B,CAAR;EACH,CAFD,CAGA,OAAOI,GAAP,EAAY;IACRL,UAAU,CAACM,KAAX,CAAiBD,GAAjB;IACA,OAAON,SAAP;EACH;;EACD,IAAIC,UAAU,CAACU,MAAf,EAAuB;IACnB,OAAOX,SAAP;EACH;;EACDC,UAAU,CAACS,IAAX,CAAgBD,KAAhB;;EACA,IAAIR,UAAU,CAACU,MAAf,EAAuB;IACnB,OAAOX,SAAP;EACH;;EACD,OAAO,KAAKG,QAAL,CAAcD,KAAd,CAAP;AACH"}, "metadata": {}, "sourceType": "module"}