{"ast": null, "code": "'use strict';\n\nvar fnToStr = Function.prototype.toString;\nvar reflectApply = typeof Reflect === 'object' && Reflect !== null && Reflect.apply;\nvar badArrayLike;\nvar isCallableMarker;\n\nif (typeof reflectApply === 'function' && typeof Object.defineProperty === 'function') {\n  try {\n    badArrayLike = Object.defineProperty({}, 'length', {\n      get: function () {\n        throw isCallableMarker;\n      }\n    });\n    isCallableMarker = {}; // eslint-disable-next-line no-throw-literal\n\n    reflectApply(function () {\n      throw 42;\n    }, null, badArrayLike);\n  } catch (_) {\n    if (_ !== isCallableMarker) {\n      reflectApply = null;\n    }\n  }\n} else {\n  reflectApply = null;\n}\n\nvar constructorRegex = /^\\s*class\\b/;\n\nvar isES6ClassFn = function isES6ClassFunction(value) {\n  try {\n    var fnStr = fnToStr.call(value);\n    return constructorRegex.test(fnStr);\n  } catch (e) {\n    return false; // not a function\n  }\n};\n\nvar tryFunctionObject = function tryFunctionToStr(value) {\n  try {\n    if (isES6ClassFn(value)) {\n      return false;\n    }\n\n    fnToStr.call(value);\n    return true;\n  } catch (e) {\n    return false;\n  }\n};\n\nvar toStr = Object.prototype.toString;\nvar objectClass = '[object Object]';\nvar fnClass = '[object Function]';\nvar genClass = '[object GeneratorFunction]';\nvar ddaClass = '[object HTMLAllCollection]'; // IE 11\n\nvar ddaClass2 = '[object HTML document.all class]';\nvar ddaClass3 = '[object HTMLCollection]'; // IE 9-10\n\nvar hasToStringTag = typeof Symbol === 'function' && !!Symbol.toStringTag; // better: use `has-tostringtag`\n\nvar isIE68 = !(0 in [,]); // eslint-disable-line no-sparse-arrays, comma-spacing\n\nvar isDDA = function isDocumentDotAll() {\n  return false;\n};\n\nif (typeof document === 'object') {\n  // Firefox 3 canonicalizes DDA to undefined when it's not accessed directly\n  var all = document.all;\n\n  if (toStr.call(all) === toStr.call(document.all)) {\n    isDDA = function isDocumentDotAll(value) {\n      /* globals document: false */\n      // in IE 6-8, typeof document.all is \"object\" and it's truthy\n      if ((isIE68 || !value) && (typeof value === 'undefined' || typeof value === 'object')) {\n        try {\n          var str = toStr.call(value);\n          return (str === ddaClass || str === ddaClass2 || str === ddaClass3 // opera 12.16\n          || str === objectClass // IE 6-8\n          ) && value('') == null; // eslint-disable-line eqeqeq\n        } catch (e) {\n          /**/\n        }\n      }\n\n      return false;\n    };\n  }\n}\n\nmodule.exports = reflectApply ? function isCallable(value) {\n  if (isDDA(value)) {\n    return true;\n  }\n\n  if (!value) {\n    return false;\n  }\n\n  if (typeof value !== 'function' && typeof value !== 'object') {\n    return false;\n  }\n\n  try {\n    reflectApply(value, null, badArrayLike);\n  } catch (e) {\n    if (e !== isCallableMarker) {\n      return false;\n    }\n  }\n\n  return !isES6ClassFn(value) && tryFunctionObject(value);\n} : function isCallable(value) {\n  if (isDDA(value)) {\n    return true;\n  }\n\n  if (!value) {\n    return false;\n  }\n\n  if (typeof value !== 'function' && typeof value !== 'object') {\n    return false;\n  }\n\n  if (hasToStringTag) {\n    return tryFunctionObject(value);\n  }\n\n  if (isES6ClassFn(value)) {\n    return false;\n  }\n\n  var strClass = toStr.call(value);\n\n  if (strClass !== fnClass && strClass !== genClass && !/^\\[object HTML/.test(strClass)) {\n    return false;\n  }\n\n  return tryFunctionObject(value);\n};", "map": {"version": 3, "names": ["fnToStr", "Function", "prototype", "toString", "reflectApply", "Reflect", "apply", "badArrayLike", "isCallableMarker", "Object", "defineProperty", "get", "_", "constructorRegex", "isES6ClassFn", "isES6ClassFunction", "value", "fnStr", "call", "test", "e", "tryFunctionObject", "tryFunctionToStr", "toStr", "objectClass", "fnClass", "genClass", "ddaClass", "ddaClass2", "ddaClass3", "hasToStringTag", "Symbol", "toStringTag", "isIE68", "isDDA", "isDocumentDotAll", "document", "all", "str", "module", "exports", "isCallable", "strClass"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/is-callable/index.js"], "sourcesContent": ["'use strict';\n\nvar fnToStr = Function.prototype.toString;\nvar reflectApply = typeof Reflect === 'object' && Reflect !== null && Reflect.apply;\nvar badArrayLike;\nvar isCallableMarker;\nif (typeof reflectApply === 'function' && typeof Object.defineProperty === 'function') {\n\ttry {\n\t\tbadArrayLike = Object.defineProperty({}, 'length', {\n\t\t\tget: function () {\n\t\t\t\tthrow isCallableMarker;\n\t\t\t}\n\t\t});\n\t\tisCallableMarker = {};\n\t\t// eslint-disable-next-line no-throw-literal\n\t\treflectApply(function () { throw 42; }, null, badArrayLike);\n\t} catch (_) {\n\t\tif (_ !== isCallableMarker) {\n\t\t\treflectApply = null;\n\t\t}\n\t}\n} else {\n\treflectApply = null;\n}\n\nvar constructorRegex = /^\\s*class\\b/;\nvar isES6ClassFn = function isES6ClassFunction(value) {\n\ttry {\n\t\tvar fnStr = fnToStr.call(value);\n\t\treturn constructorRegex.test(fnStr);\n\t} catch (e) {\n\t\treturn false; // not a function\n\t}\n};\n\nvar tryFunctionObject = function tryFunctionToStr(value) {\n\ttry {\n\t\tif (isES6ClassFn(value)) { return false; }\n\t\tfnToStr.call(value);\n\t\treturn true;\n\t} catch (e) {\n\t\treturn false;\n\t}\n};\nvar toStr = Object.prototype.toString;\nvar objectClass = '[object Object]';\nvar fnClass = '[object Function]';\nvar genClass = '[object GeneratorFunction]';\nvar ddaClass = '[object HTMLAllCollection]'; // IE 11\nvar ddaClass2 = '[object HTML document.all class]';\nvar ddaClass3 = '[object HTMLCollection]'; // IE 9-10\nvar hasToStringTag = typeof Symbol === 'function' && !!Symbol.toStringTag; // better: use `has-tostringtag`\n\nvar isIE68 = !(0 in [,]); // eslint-disable-line no-sparse-arrays, comma-spacing\n\nvar isDDA = function isDocumentDotAll() { return false; };\nif (typeof document === 'object') {\n\t// Firefox 3 canonicalizes DDA to undefined when it's not accessed directly\n\tvar all = document.all;\n\tif (toStr.call(all) === toStr.call(document.all)) {\n\t\tisDDA = function isDocumentDotAll(value) {\n\t\t\t/* globals document: false */\n\t\t\t// in IE 6-8, typeof document.all is \"object\" and it's truthy\n\t\t\tif ((isIE68 || !value) && (typeof value === 'undefined' || typeof value === 'object')) {\n\t\t\t\ttry {\n\t\t\t\t\tvar str = toStr.call(value);\n\t\t\t\t\treturn (\n\t\t\t\t\t\tstr === ddaClass\n\t\t\t\t\t\t|| str === ddaClass2\n\t\t\t\t\t\t|| str === ddaClass3 // opera 12.16\n\t\t\t\t\t\t|| str === objectClass // IE 6-8\n\t\t\t\t\t) && value('') == null; // eslint-disable-line eqeqeq\n\t\t\t\t} catch (e) { /**/ }\n\t\t\t}\n\t\t\treturn false;\n\t\t};\n\t}\n}\n\nmodule.exports = reflectApply\n\t? function isCallable(value) {\n\t\tif (isDDA(value)) { return true; }\n\t\tif (!value) { return false; }\n\t\tif (typeof value !== 'function' && typeof value !== 'object') { return false; }\n\t\ttry {\n\t\t\treflectApply(value, null, badArrayLike);\n\t\t} catch (e) {\n\t\t\tif (e !== isCallableMarker) { return false; }\n\t\t}\n\t\treturn !isES6ClassFn(value) && tryFunctionObject(value);\n\t}\n\t: function isCallable(value) {\n\t\tif (isDDA(value)) { return true; }\n\t\tif (!value) { return false; }\n\t\tif (typeof value !== 'function' && typeof value !== 'object') { return false; }\n\t\tif (hasToStringTag) { return tryFunctionObject(value); }\n\t\tif (isES6ClassFn(value)) { return false; }\n\t\tvar strClass = toStr.call(value);\n\t\tif (strClass !== fnClass && strClass !== genClass && !(/^\\[object HTML/).test(strClass)) { return false; }\n\t\treturn tryFunctionObject(value);\n\t};\n"], "mappings": "AAAA;;AAEA,IAAIA,OAAO,GAAGC,QAAQ,CAACC,SAAT,CAAmBC,QAAjC;AACA,IAAIC,YAAY,GAAG,OAAOC,OAAP,KAAmB,QAAnB,IAA+BA,OAAO,KAAK,IAA3C,IAAmDA,OAAO,CAACC,KAA9E;AACA,IAAIC,YAAJ;AACA,IAAIC,gBAAJ;;AACA,IAAI,OAAOJ,YAAP,KAAwB,UAAxB,IAAsC,OAAOK,MAAM,CAACC,cAAd,KAAiC,UAA3E,EAAuF;EACtF,IAAI;IACHH,YAAY,GAAGE,MAAM,CAACC,cAAP,CAAsB,EAAtB,EAA0B,QAA1B,EAAoC;MAClDC,GAAG,EAAE,YAAY;QAChB,MAAMH,gBAAN;MACA;IAHiD,CAApC,CAAf;IAKAA,gBAAgB,GAAG,EAAnB,CANG,CAOH;;IACAJ,YAAY,CAAC,YAAY;MAAE,MAAM,EAAN;IAAW,CAA1B,EAA4B,IAA5B,EAAkCG,YAAlC,CAAZ;EACA,CATD,CASE,OAAOK,CAAP,EAAU;IACX,IAAIA,CAAC,KAAKJ,gBAAV,EAA4B;MAC3BJ,YAAY,GAAG,IAAf;IACA;EACD;AACD,CAfD,MAeO;EACNA,YAAY,GAAG,IAAf;AACA;;AAED,IAAIS,gBAAgB,GAAG,aAAvB;;AACA,IAAIC,YAAY,GAAG,SAASC,kBAAT,CAA4BC,KAA5B,EAAmC;EACrD,IAAI;IACH,IAAIC,KAAK,GAAGjB,OAAO,CAACkB,IAAR,CAAaF,KAAb,CAAZ;IACA,OAAOH,gBAAgB,CAACM,IAAjB,CAAsBF,KAAtB,CAAP;EACA,CAHD,CAGE,OAAOG,CAAP,EAAU;IACX,OAAO,KAAP,CADW,CACG;EACd;AACD,CAPD;;AASA,IAAIC,iBAAiB,GAAG,SAASC,gBAAT,CAA0BN,KAA1B,EAAiC;EACxD,IAAI;IACH,IAAIF,YAAY,CAACE,KAAD,CAAhB,EAAyB;MAAE,OAAO,KAAP;IAAe;;IAC1ChB,OAAO,CAACkB,IAAR,CAAaF,KAAb;IACA,OAAO,IAAP;EACA,CAJD,CAIE,OAAOI,CAAP,EAAU;IACX,OAAO,KAAP;EACA;AACD,CARD;;AASA,IAAIG,KAAK,GAAGd,MAAM,CAACP,SAAP,CAAiBC,QAA7B;AACA,IAAIqB,WAAW,GAAG,iBAAlB;AACA,IAAIC,OAAO,GAAG,mBAAd;AACA,IAAIC,QAAQ,GAAG,4BAAf;AACA,IAAIC,QAAQ,GAAG,4BAAf,C,CAA6C;;AAC7C,IAAIC,SAAS,GAAG,kCAAhB;AACA,IAAIC,SAAS,GAAG,yBAAhB,C,CAA2C;;AAC3C,IAAIC,cAAc,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgC,CAAC,CAACA,MAAM,CAACC,WAA9D,C,CAA2E;;AAE3E,IAAIC,MAAM,GAAG,EAAE,KAAK,GAAP,CAAb,C,CAA0B;;AAE1B,IAAIC,KAAK,GAAG,SAASC,gBAAT,GAA4B;EAAE,OAAO,KAAP;AAAe,CAAzD;;AACA,IAAI,OAAOC,QAAP,KAAoB,QAAxB,EAAkC;EACjC;EACA,IAAIC,GAAG,GAAGD,QAAQ,CAACC,GAAnB;;EACA,IAAId,KAAK,CAACL,IAAN,CAAWmB,GAAX,MAAoBd,KAAK,CAACL,IAAN,CAAWkB,QAAQ,CAACC,GAApB,CAAxB,EAAkD;IACjDH,KAAK,GAAG,SAASC,gBAAT,CAA0BnB,KAA1B,EAAiC;MACxC;MACA;MACA,IAAI,CAACiB,MAAM,IAAI,CAACjB,KAAZ,MAAuB,OAAOA,KAAP,KAAiB,WAAjB,IAAgC,OAAOA,KAAP,KAAiB,QAAxE,CAAJ,EAAuF;QACtF,IAAI;UACH,IAAIsB,GAAG,GAAGf,KAAK,CAACL,IAAN,CAAWF,KAAX,CAAV;UACA,OAAO,CACNsB,GAAG,KAAKX,QAAR,IACGW,GAAG,KAAKV,SADX,IAEGU,GAAG,KAAKT,SAFX,CAEqB;UAFrB,GAGGS,GAAG,KAAKd,WAJL,CAIiB;UAJjB,KAKFR,KAAK,CAAC,EAAD,CAAL,IAAa,IALlB,CAFG,CAOqB;QACxB,CARD,CAQE,OAAOI,CAAP,EAAU;UAAE;QAAM;MACpB;;MACD,OAAO,KAAP;IACA,CAfD;EAgBA;AACD;;AAEDmB,MAAM,CAACC,OAAP,GAAiBpC,YAAY,GAC1B,SAASqC,UAAT,CAAoBzB,KAApB,EAA2B;EAC5B,IAAIkB,KAAK,CAAClB,KAAD,CAAT,EAAkB;IAAE,OAAO,IAAP;EAAc;;EAClC,IAAI,CAACA,KAAL,EAAY;IAAE,OAAO,KAAP;EAAe;;EAC7B,IAAI,OAAOA,KAAP,KAAiB,UAAjB,IAA+B,OAAOA,KAAP,KAAiB,QAApD,EAA8D;IAAE,OAAO,KAAP;EAAe;;EAC/E,IAAI;IACHZ,YAAY,CAACY,KAAD,EAAQ,IAAR,EAAcT,YAAd,CAAZ;EACA,CAFD,CAEE,OAAOa,CAAP,EAAU;IACX,IAAIA,CAAC,KAAKZ,gBAAV,EAA4B;MAAE,OAAO,KAAP;IAAe;EAC7C;;EACD,OAAO,CAACM,YAAY,CAACE,KAAD,CAAb,IAAwBK,iBAAiB,CAACL,KAAD,CAAhD;AACA,CAX2B,GAY1B,SAASyB,UAAT,CAAoBzB,KAApB,EAA2B;EAC5B,IAAIkB,KAAK,CAAClB,KAAD,CAAT,EAAkB;IAAE,OAAO,IAAP;EAAc;;EAClC,IAAI,CAACA,KAAL,EAAY;IAAE,OAAO,KAAP;EAAe;;EAC7B,IAAI,OAAOA,KAAP,KAAiB,UAAjB,IAA+B,OAAOA,KAAP,KAAiB,QAApD,EAA8D;IAAE,OAAO,KAAP;EAAe;;EAC/E,IAAIc,cAAJ,EAAoB;IAAE,OAAOT,iBAAiB,CAACL,KAAD,CAAxB;EAAkC;;EACxD,IAAIF,YAAY,CAACE,KAAD,CAAhB,EAAyB;IAAE,OAAO,KAAP;EAAe;;EAC1C,IAAI0B,QAAQ,GAAGnB,KAAK,CAACL,IAAN,CAAWF,KAAX,CAAf;;EACA,IAAI0B,QAAQ,KAAKjB,OAAb,IAAwBiB,QAAQ,KAAKhB,QAArC,IAAiD,CAAE,gBAAD,CAAmBP,IAAnB,CAAwBuB,QAAxB,CAAtD,EAAyF;IAAE,OAAO,KAAP;EAAe;;EAC1G,OAAOrB,iBAAiB,CAACL,KAAD,CAAxB;AACA,CArBF"}, "metadata": {}, "sourceType": "script"}