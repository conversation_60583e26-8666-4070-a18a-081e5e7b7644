{"ast": null, "code": "import { ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty, coerceStringArray } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/platform';\nimport { _getEventTarget } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, TemplateRef, Directive, Inject, ViewChild, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, forwardRef, Optional, Host, NgModule } from '@angular/core';\nimport { mixinDisableRipple, MAT_OPTION_PARENT_COMPONENT, MAT_OPTGROUP, MatOption, MatOptionSelectionChange, _countGroupLabelsBeforeOption, _getOptionScrollPosition, MatOptionModule, MatCommonModule } from '@angular/material/core';\nimport { Subscription, Subject, defer, merge, of, fromEvent } from 'rxjs';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport * as i3 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i2$1 from '@angular/cdk/bidi';\nimport { hasModifierKey, ESCAPE, ENTER, UP_ARROW, DOWN_ARROW, TAB } from '@angular/cdk/keycodes';\nimport { TemplatePortal } from '@angular/cdk/portal';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i4 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD } from '@angular/material/form-field';\nimport { startWith, switchMap, take, filter, map, tap, delay } from 'rxjs/operators';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Autocomplete IDs need to be unique across components, so this counter exists outside of\n * the component definition.\n */\n\nconst _c0 = [\"panel\"];\n\nfunction MatAutocomplete_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0, 1);\n    i0.ɵɵprojection(2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const formFieldId_r1 = ctx.id;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"id\", ctx_r0.id)(\"ngClass\", ctx_r0._classList);\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.ariaLabel || null)(\"aria-labelledby\", ctx_r0._getPanelAriaLabelledby(formFieldId_r1));\n  }\n}\n\nconst _c1 = [\"*\"];\nlet _uniqueAutocompleteIdCounter = 0;\n/** Event object that is emitted when an autocomplete option is selected. */\n\nclass MatAutocompleteSelectedEvent {\n  constructor(\n  /** Reference to the autocomplete panel that emitted the event. */\n  source,\n  /** Option that was selected. */\n  option) {\n    this.source = source;\n    this.option = option;\n  }\n\n} // Boilerplate for applying mixins to MatAutocomplete.\n\n/** @docs-private */\n\n\nconst _MatAutocompleteMixinBase = mixinDisableRipple(class {});\n/** Injection token to be used to override the default options for `mat-autocomplete`. */\n\n\nconst MAT_AUTOCOMPLETE_DEFAULT_OPTIONS = new InjectionToken('mat-autocomplete-default-options', {\n  providedIn: 'root',\n  factory: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\n\nfunction MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    autoActiveFirstOption: false,\n    autoSelectActiveOption: false\n  };\n}\n/** Base class with all of the `MatAutocomplete` functionality. */\n\n\nclass _MatAutocompleteBase extends _MatAutocompleteMixinBase {\n  constructor(_changeDetectorRef, _elementRef, defaults, platform) {\n    super();\n    this._changeDetectorRef = _changeDetectorRef;\n    this._elementRef = _elementRef;\n    this._activeOptionChanges = Subscription.EMPTY;\n    /** Whether the autocomplete panel should be visible, depending on option length. */\n\n    this.showPanel = false;\n    this._isOpen = false;\n    /** Function that maps an option's control value to its display value in the trigger. */\n\n    this.displayWith = null;\n    /** Event that is emitted whenever an option from the list is selected. */\n\n    this.optionSelected = new EventEmitter();\n    /** Event that is emitted when the autocomplete panel is opened. */\n\n    this.opened = new EventEmitter();\n    /** Event that is emitted when the autocomplete panel is closed. */\n\n    this.closed = new EventEmitter();\n    /** Emits whenever an option is activated. */\n\n    this.optionActivated = new EventEmitter();\n    this._classList = {};\n    /** Unique ID to be used by autocomplete trigger's \"aria-owns\" property. */\n\n    this.id = `mat-autocomplete-${_uniqueAutocompleteIdCounter++}`; // TODO(crisbeto): the problem that the `inertGroups` option resolves is only present on\n    // Safari using VoiceOver. We should occasionally check back to see whether the bug\n    // wasn't resolved in VoiceOver, and if it has, we can remove this and the `inertGroups`\n    // option altogether.\n\n    this.inertGroups = platform?.SAFARI || false;\n    this._autoActiveFirstOption = !!defaults.autoActiveFirstOption;\n    this._autoSelectActiveOption = !!defaults.autoSelectActiveOption;\n  }\n  /** Whether the autocomplete panel is open. */\n\n\n  get isOpen() {\n    return this._isOpen && this.showPanel;\n  }\n  /**\n   * Whether the first option should be highlighted when the autocomplete panel is opened.\n   * Can be configured globally through the `MAT_AUTOCOMPLETE_DEFAULT_OPTIONS` token.\n   */\n\n\n  get autoActiveFirstOption() {\n    return this._autoActiveFirstOption;\n  }\n\n  set autoActiveFirstOption(value) {\n    this._autoActiveFirstOption = coerceBooleanProperty(value);\n  }\n  /** Whether the active option should be selected as the user is navigating. */\n\n\n  get autoSelectActiveOption() {\n    return this._autoSelectActiveOption;\n  }\n\n  set autoSelectActiveOption(value) {\n    this._autoSelectActiveOption = coerceBooleanProperty(value);\n  }\n  /**\n   * Takes classes set on the host mat-autocomplete element and applies them to the panel\n   * inside the overlay container to allow for easy styling.\n   */\n\n\n  set classList(value) {\n    if (value && value.length) {\n      this._classList = coerceStringArray(value).reduce((classList, className) => {\n        classList[className] = true;\n        return classList;\n      }, {});\n    } else {\n      this._classList = {};\n    }\n\n    this._setVisibilityClasses(this._classList);\n\n    this._elementRef.nativeElement.className = '';\n  }\n\n  ngAfterContentInit() {\n    this._keyManager = new ActiveDescendantKeyManager(this.options).withWrap();\n    this._activeOptionChanges = this._keyManager.change.subscribe(index => {\n      if (this.isOpen) {\n        this.optionActivated.emit({\n          source: this,\n          option: this.options.toArray()[index] || null\n        });\n      }\n    }); // Set the initial visibility state.\n\n    this._setVisibility();\n  }\n\n  ngOnDestroy() {\n    this._activeOptionChanges.unsubscribe();\n  }\n  /**\n   * Sets the panel scrollTop. This allows us to manually scroll to display options\n   * above or below the fold, as they are not actually being focused when active.\n   */\n\n\n  _setScrollTop(scrollTop) {\n    if (this.panel) {\n      this.panel.nativeElement.scrollTop = scrollTop;\n    }\n  }\n  /** Returns the panel's scrollTop. */\n\n\n  _getScrollTop() {\n    return this.panel ? this.panel.nativeElement.scrollTop : 0;\n  }\n  /** Panel should hide itself when the option list is empty. */\n\n\n  _setVisibility() {\n    this.showPanel = !!this.options.length;\n\n    this._setVisibilityClasses(this._classList);\n\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Emits the `select` event. */\n\n\n  _emitSelectEvent(option) {\n    const event = new MatAutocompleteSelectedEvent(this, option);\n    this.optionSelected.emit(event);\n  }\n  /** Gets the aria-labelledby for the autocomplete panel. */\n\n\n  _getPanelAriaLabelledby(labelId) {\n    if (this.ariaLabel) {\n      return null;\n    }\n\n    const labelExpression = labelId ? labelId + ' ' : '';\n    return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n  }\n  /** Sets the autocomplete visibility classes on a classlist based on the panel is visible. */\n\n\n  _setVisibilityClasses(classList) {\n    classList[this._visibleClass] = this.showPanel;\n    classList[this._hiddenClass] = !this.showPanel;\n  }\n\n}\n\n_MatAutocompleteBase.ɵfac = function _MatAutocompleteBase_Factory(t) {\n  return new (t || _MatAutocompleteBase)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_AUTOCOMPLETE_DEFAULT_OPTIONS), i0.ɵɵdirectiveInject(i1.Platform));\n};\n\n_MatAutocompleteBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatAutocompleteBase,\n  viewQuery: function _MatAutocompleteBase_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(TemplateRef, 7);\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.panel = _t.first);\n    }\n  },\n  inputs: {\n    ariaLabel: [\"aria-label\", \"ariaLabel\"],\n    ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n    displayWith: \"displayWith\",\n    autoActiveFirstOption: \"autoActiveFirstOption\",\n    autoSelectActiveOption: \"autoSelectActiveOption\",\n    panelWidth: \"panelWidth\",\n    classList: [\"class\", \"classList\"]\n  },\n  outputs: {\n    optionSelected: \"optionSelected\",\n    opened: \"opened\",\n    closed: \"closed\",\n    optionActivated: \"optionActivated\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatAutocompleteBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_AUTOCOMPLETE_DEFAULT_OPTIONS]\n      }]\n    }, {\n      type: i1.Platform\n    }];\n  }, {\n    template: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: true\n      }]\n    }],\n    panel: [{\n      type: ViewChild,\n      args: ['panel']\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    displayWith: [{\n      type: Input\n    }],\n    autoActiveFirstOption: [{\n      type: Input\n    }],\n    autoSelectActiveOption: [{\n      type: Input\n    }],\n    panelWidth: [{\n      type: Input\n    }],\n    optionSelected: [{\n      type: Output\n    }],\n    opened: [{\n      type: Output\n    }],\n    closed: [{\n      type: Output\n    }],\n    optionActivated: [{\n      type: Output\n    }],\n    classList: [{\n      type: Input,\n      args: ['class']\n    }]\n  });\n})();\n\nclass MatAutocomplete extends _MatAutocompleteBase {\n  constructor() {\n    super(...arguments);\n    this._visibleClass = 'mat-autocomplete-visible';\n    this._hiddenClass = 'mat-autocomplete-hidden';\n  }\n\n}\n\nMatAutocomplete.ɵfac = /* @__PURE__ */function () {\n  let ɵMatAutocomplete_BaseFactory;\n  return function MatAutocomplete_Factory(t) {\n    return (ɵMatAutocomplete_BaseFactory || (ɵMatAutocomplete_BaseFactory = i0.ɵɵgetInheritedFactory(MatAutocomplete)))(t || MatAutocomplete);\n  };\n}();\n\nMatAutocomplete.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatAutocomplete,\n  selectors: [[\"mat-autocomplete\"]],\n  contentQueries: function MatAutocomplete_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MAT_OPTGROUP, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatOption, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionGroups = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.options = _t);\n    }\n  },\n  hostAttrs: [1, \"mat-autocomplete\"],\n  inputs: {\n    disableRipple: \"disableRipple\"\n  },\n  exportAs: [\"matAutocomplete\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_OPTION_PARENT_COMPONENT,\n    useExisting: MatAutocomplete\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c1,\n  decls: 1,\n  vars: 0,\n  consts: [[\"role\", \"listbox\", 1, \"mat-autocomplete-panel\", 3, \"id\", \"ngClass\"], [\"panel\", \"\"]],\n  template: function MatAutocomplete_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵtemplate(0, MatAutocomplete_ng_template_0_Template, 3, 4, \"ng-template\");\n    }\n  },\n  dependencies: [i2.NgClass],\n  styles: [\".mat-autocomplete-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;visibility:hidden;max-width:none;max-height:256px;position:relative;width:100%;border-bottom-left-radius:4px;border-bottom-right-radius:4px}.mat-autocomplete-panel.mat-autocomplete-visible{visibility:visible}.mat-autocomplete-panel.mat-autocomplete-hidden{visibility:hidden}.mat-autocomplete-panel-above .mat-autocomplete-panel{border-radius:0;border-top-left-radius:4px;border-top-right-radius:4px}.mat-autocomplete-panel .mat-divider-horizontal{margin-top:-1px}.cdk-high-contrast-active .mat-autocomplete-panel{outline:solid 1px}mat-autocomplete{display:none}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAutocomplete, [{\n    type: Component,\n    args: [{\n      selector: 'mat-autocomplete',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      exportAs: 'matAutocomplete',\n      inputs: ['disableRipple'],\n      host: {\n        'class': 'mat-autocomplete'\n      },\n      providers: [{\n        provide: MAT_OPTION_PARENT_COMPONENT,\n        useExisting: MatAutocomplete\n      }],\n      template: \"<ng-template let-formFieldId=\\\"id\\\">\\n  <div class=\\\"mat-autocomplete-panel\\\"\\n       role=\\\"listbox\\\"\\n       [id]=\\\"id\\\"\\n       [attr.aria-label]=\\\"ariaLabel || null\\\"\\n       [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby(formFieldId)\\\"\\n       [ngClass]=\\\"_classList\\\"\\n       #panel>\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\",\n      styles: [\".mat-autocomplete-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;visibility:hidden;max-width:none;max-height:256px;position:relative;width:100%;border-bottom-left-radius:4px;border-bottom-right-radius:4px}.mat-autocomplete-panel.mat-autocomplete-visible{visibility:visible}.mat-autocomplete-panel.mat-autocomplete-hidden{visibility:hidden}.mat-autocomplete-panel-above .mat-autocomplete-panel{border-radius:0;border-top-left-radius:4px;border-top-right-radius:4px}.mat-autocomplete-panel .mat-divider-horizontal{margin-top:-1px}.cdk-high-contrast-active .mat-autocomplete-panel{outline:solid 1px}mat-autocomplete{display:none}\"]\n    }]\n  }], null, {\n    optionGroups: [{\n      type: ContentChildren,\n      args: [MAT_OPTGROUP, {\n        descendants: true\n      }]\n    }],\n    options: [{\n      type: ContentChildren,\n      args: [MatOption, {\n        descendants: true\n      }]\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Base class containing all of the functionality for `MatAutocompleteOrigin`. */\n\n\nclass _MatAutocompleteOriginBase {\n  constructor(\n  /** Reference to the element on which the directive is applied. */\n  elementRef) {\n    this.elementRef = elementRef;\n  }\n\n}\n\n_MatAutocompleteOriginBase.ɵfac = function _MatAutocompleteOriginBase_Factory(t) {\n  return new (t || _MatAutocompleteOriginBase)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\n_MatAutocompleteOriginBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatAutocompleteOriginBase\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatAutocompleteOriginBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\n/**\n * Directive applied to an element to make it usable\n * as a connection point for an autocomplete panel.\n */\n\n\nclass MatAutocompleteOrigin extends _MatAutocompleteOriginBase {}\n\nMatAutocompleteOrigin.ɵfac = /* @__PURE__ */function () {\n  let ɵMatAutocompleteOrigin_BaseFactory;\n  return function MatAutocompleteOrigin_Factory(t) {\n    return (ɵMatAutocompleteOrigin_BaseFactory || (ɵMatAutocompleteOrigin_BaseFactory = i0.ɵɵgetInheritedFactory(MatAutocompleteOrigin)))(t || MatAutocompleteOrigin);\n  };\n}();\n\nMatAutocompleteOrigin.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatAutocompleteOrigin,\n  selectors: [[\"\", \"matAutocompleteOrigin\", \"\"]],\n  exportAs: [\"matAutocompleteOrigin\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAutocompleteOrigin, [{\n    type: Directive,\n    args: [{\n      selector: '[matAutocompleteOrigin]',\n      exportAs: 'matAutocompleteOrigin'\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injection token that determines the scroll handling while the autocomplete panel is open. */\n\n\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY = new InjectionToken('mat-autocomplete-scroll-strategy');\n/** @docs-private */\n\nfunction MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\n\n\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_AUTOCOMPLETE_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY\n};\n/**\n * Provider that allows the autocomplete to register as a ControlValueAccessor.\n * @docs-private\n */\n\nconst MAT_AUTOCOMPLETE_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatAutocompleteTrigger),\n  multi: true\n};\n/**\n * Creates an error to be thrown when attempting to use an autocomplete trigger without a panel.\n * @docs-private\n */\n\nfunction getMatAutocompleteMissingPanelError() {\n  return Error('Attempting to open an undefined instance of `mat-autocomplete`. ' + 'Make sure that the id passed to the `matAutocomplete` is correct and that ' + \"you're attempting to open it after the ngAfterContentInit hook.\");\n}\n/** Base class with all of the `MatAutocompleteTrigger` functionality. */\n\n\nclass _MatAutocompleteTriggerBase {\n  constructor(_element, _overlay, _viewContainerRef, _zone, _changeDetectorRef, scrollStrategy, _dir, _formField, _document, _viewportRuler, _defaults) {\n    this._element = _element;\n    this._overlay = _overlay;\n    this._viewContainerRef = _viewContainerRef;\n    this._zone = _zone;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._dir = _dir;\n    this._formField = _formField;\n    this._document = _document;\n    this._viewportRuler = _viewportRuler;\n    this._defaults = _defaults;\n    this._componentDestroyed = false;\n    this._autocompleteDisabled = false;\n    /** Whether or not the label state is being overridden. */\n\n    this._manuallyFloatingLabel = false;\n    /** Subscription to viewport size changes. */\n\n    this._viewportSubscription = Subscription.EMPTY;\n    /**\n     * Whether the autocomplete can open the next time it is focused. Used to prevent a focused,\n     * closed autocomplete from being reopened if the user switches to another browser tab and then\n     * comes back.\n     */\n\n    this._canOpenOnNextFocus = true;\n    /** Stream of keyboard events that can close the panel. */\n\n    this._closeKeyEventStream = new Subject();\n    /**\n     * Event handler for when the window is blurred. Needs to be an\n     * arrow function in order to preserve the context.\n     */\n\n    this._windowBlurHandler = () => {\n      // If the user blurred the window while the autocomplete is focused, it means that it'll be\n      // refocused when they come back. In this case we want to skip the first focus event, if the\n      // pane was closed, in order to avoid reopening it unintentionally.\n      this._canOpenOnNextFocus = this._document.activeElement !== this._element.nativeElement || this.panelOpen;\n    };\n    /** `View -> model callback called when value changes` */\n\n\n    this._onChange = () => {};\n    /** `View -> model callback called when autocomplete has been touched` */\n\n\n    this._onTouched = () => {};\n    /**\n     * Position of the autocomplete panel relative to the trigger element. A position of `auto`\n     * will render the panel underneath the trigger if there is enough space for it to fit in\n     * the viewport, otherwise the panel will be shown above it. If the position is set to\n     * `above` or `below`, the panel will always be shown above or below the trigger. no matter\n     * whether it fits completely in the viewport.\n     */\n\n\n    this.position = 'auto';\n    /**\n     * `autocomplete` attribute to be set on the input element.\n     * @docs-private\n     */\n\n    this.autocompleteAttribute = 'off';\n    this._overlayAttached = false;\n    /** Stream of changes to the selection state of the autocomplete options. */\n\n    this.optionSelections = defer(() => {\n      const options = this.autocomplete ? this.autocomplete.options : null;\n\n      if (options) {\n        return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n      } // If there are any subscribers before `ngAfterViewInit`, the `autocomplete` will be undefined.\n      // Return a stream that we'll replace with the real one once everything is in place.\n\n\n      return this._zone.onStable.pipe(take(1), switchMap(() => this.optionSelections));\n    });\n    this._scrollStrategy = scrollStrategy;\n  }\n  /**\n   * Whether the autocomplete is disabled. When disabled, the element will\n   * act as a regular input and the user won't be able to open the panel.\n   */\n\n\n  get autocompleteDisabled() {\n    return this._autocompleteDisabled;\n  }\n\n  set autocompleteDisabled(value) {\n    this._autocompleteDisabled = coerceBooleanProperty(value);\n  }\n\n  ngAfterViewInit() {\n    const window = this._getWindow();\n\n    if (typeof window !== 'undefined') {\n      this._zone.runOutsideAngular(() => window.addEventListener('blur', this._windowBlurHandler));\n    }\n  }\n\n  ngOnChanges(changes) {\n    if (changes['position'] && this._positionStrategy) {\n      this._setStrategyPositions(this._positionStrategy);\n\n      if (this.panelOpen) {\n        this._overlayRef.updatePosition();\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    const window = this._getWindow();\n\n    if (typeof window !== 'undefined') {\n      window.removeEventListener('blur', this._windowBlurHandler);\n    }\n\n    this._viewportSubscription.unsubscribe();\n\n    this._componentDestroyed = true;\n\n    this._destroyPanel();\n\n    this._closeKeyEventStream.complete();\n  }\n  /** Whether or not the autocomplete panel is open. */\n\n\n  get panelOpen() {\n    return this._overlayAttached && this.autocomplete.showPanel;\n  }\n  /** Opens the autocomplete suggestion panel. */\n\n\n  openPanel() {\n    this._attachOverlay();\n\n    this._floatLabel();\n  }\n  /** Closes the autocomplete suggestion panel. */\n\n\n  closePanel() {\n    this._resetLabel();\n\n    if (!this._overlayAttached) {\n      return;\n    }\n\n    if (this.panelOpen) {\n      // Only emit if the panel was visible.\n      // The `NgZone.onStable` always emits outside of the Angular zone,\n      // so all the subscriptions from `_subscribeToClosingActions()` are also outside of the Angular zone.\n      // We should manually run in Angular zone to update UI after panel closing.\n      this._zone.run(() => {\n        this.autocomplete.closed.emit();\n      });\n    }\n\n    this.autocomplete._isOpen = this._overlayAttached = false;\n    this._pendingAutoselectedOption = null;\n\n    if (this._overlayRef && this._overlayRef.hasAttached()) {\n      this._overlayRef.detach();\n\n      this._closingActionsSubscription.unsubscribe();\n    } // Note that in some cases this can end up being called after the component is destroyed.\n    // Add a check to ensure that we don't try to run change detection on a destroyed view.\n\n\n    if (!this._componentDestroyed) {\n      // We need to trigger change detection manually, because\n      // `fromEvent` doesn't seem to do it at the proper time.\n      // This ensures that the label is reset when the\n      // user clicks outside.\n      this._changeDetectorRef.detectChanges();\n    }\n  }\n  /**\n   * Updates the position of the autocomplete suggestion panel to ensure that it fits all options\n   * within the viewport.\n   */\n\n\n  updatePosition() {\n    if (this._overlayAttached) {\n      this._overlayRef.updatePosition();\n    }\n  }\n  /**\n   * A stream of actions that should close the autocomplete panel, including\n   * when an option is selected, on blur, and when TAB is pressed.\n   */\n\n\n  get panelClosingActions() {\n    return merge(this.optionSelections, this.autocomplete._keyManager.tabOut.pipe(filter(() => this._overlayAttached)), this._closeKeyEventStream, this._getOutsideClickStream(), this._overlayRef ? this._overlayRef.detachments().pipe(filter(() => this._overlayAttached)) : of()).pipe( // Normalize the output so we return a consistent type.\n    map(event => event instanceof MatOptionSelectionChange ? event : null));\n  }\n  /** The currently active option, coerced to MatOption type. */\n\n\n  get activeOption() {\n    if (this.autocomplete && this.autocomplete._keyManager) {\n      return this.autocomplete._keyManager.activeItem;\n    }\n\n    return null;\n  }\n  /** Stream of clicks outside of the autocomplete panel. */\n\n\n  _getOutsideClickStream() {\n    return merge(fromEvent(this._document, 'click'), fromEvent(this._document, 'auxclick'), fromEvent(this._document, 'touchend')).pipe(filter(event => {\n      // If we're in the Shadow DOM, the event target will be the shadow root, so we have to\n      // fall back to check the first element in the path of the click event.\n      const clickTarget = _getEventTarget(event);\n\n      const formField = this._formField ? this._formField._elementRef.nativeElement : null;\n      const customOrigin = this.connectedTo ? this.connectedTo.elementRef.nativeElement : null;\n      return this._overlayAttached && clickTarget !== this._element.nativeElement && // Normally focus moves inside `mousedown` so this condition will almost always be\n      // true. Its main purpose is to handle the case where the input is focused from an\n      // outside click which propagates up to the `body` listener within the same sequence\n      // and causes the panel to close immediately (see #3106).\n      this._document.activeElement !== this._element.nativeElement && (!formField || !formField.contains(clickTarget)) && (!customOrigin || !customOrigin.contains(clickTarget)) && !!this._overlayRef && !this._overlayRef.overlayElement.contains(clickTarget);\n    }));\n  } // Implemented as part of ControlValueAccessor.\n\n\n  writeValue(value) {\n    Promise.resolve(null).then(() => this._assignOptionValue(value));\n  } // Implemented as part of ControlValueAccessor.\n\n\n  registerOnChange(fn) {\n    this._onChange = fn;\n  } // Implemented as part of ControlValueAccessor.\n\n\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  } // Implemented as part of ControlValueAccessor.\n\n\n  setDisabledState(isDisabled) {\n    this._element.nativeElement.disabled = isDisabled;\n  }\n\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    const hasModifier = hasModifierKey(event); // Prevent the default action on all escape key presses. This is here primarily to bring IE\n    // in line with other browsers. By default, pressing escape on IE will cause it to revert\n    // the input value to the one that it had on focus, however it won't dispatch any events\n    // which means that the model value will be out of sync with the view.\n\n    if (keyCode === ESCAPE && !hasModifier) {\n      event.preventDefault();\n    }\n\n    if (this.activeOption && keyCode === ENTER && this.panelOpen && !hasModifier) {\n      this.activeOption._selectViaInteraction();\n\n      this._resetActiveItem();\n\n      event.preventDefault();\n    } else if (this.autocomplete) {\n      const prevActiveItem = this.autocomplete._keyManager.activeItem;\n      const isArrowKey = keyCode === UP_ARROW || keyCode === DOWN_ARROW;\n\n      if (keyCode === TAB || isArrowKey && !hasModifier && this.panelOpen) {\n        this.autocomplete._keyManager.onKeydown(event);\n      } else if (isArrowKey && this._canOpen()) {\n        this.openPanel();\n      }\n\n      if (isArrowKey || this.autocomplete._keyManager.activeItem !== prevActiveItem) {\n        this._scrollToOption(this.autocomplete._keyManager.activeItemIndex || 0);\n\n        if (this.autocomplete.autoSelectActiveOption && this.activeOption) {\n          if (!this._pendingAutoselectedOption) {\n            this._valueBeforeAutoSelection = this._element.nativeElement.value;\n          }\n\n          this._pendingAutoselectedOption = this.activeOption;\n\n          this._assignOptionValue(this.activeOption.value);\n        }\n      }\n    }\n  }\n\n  _handleInput(event) {\n    let target = event.target;\n    let value = target.value; // Based on `NumberValueAccessor` from forms.\n\n    if (target.type === 'number') {\n      value = value == '' ? null : parseFloat(value);\n    } // If the input has a placeholder, IE will fire the `input` event on page load,\n    // focus and blur, in addition to when the user actually changed the value. To\n    // filter out all of the extra events, we save the value on focus and between\n    // `input` events, and we check whether it changed.\n    // See: https://connect.microsoft.com/IE/feedback/details/885747/\n\n\n    if (this._previousValue !== value) {\n      this._previousValue = value;\n      this._pendingAutoselectedOption = null;\n\n      this._onChange(value);\n\n      if (this._canOpen() && this._document.activeElement === event.target) {\n        this.openPanel();\n      }\n    }\n  }\n\n  _handleFocus() {\n    if (!this._canOpenOnNextFocus) {\n      this._canOpenOnNextFocus = true;\n    } else if (this._canOpen()) {\n      this._previousValue = this._element.nativeElement.value;\n\n      this._attachOverlay();\n\n      this._floatLabel(true);\n    }\n  }\n\n  _handleClick() {\n    if (this._canOpen() && !this.panelOpen) {\n      this.openPanel();\n    }\n  }\n  /**\n   * In \"auto\" mode, the label will animate down as soon as focus is lost.\n   * This causes the value to jump when selecting an option with the mouse.\n   * This method manually floats the label until the panel can be closed.\n   * @param shouldAnimate Whether the label should be animated when it is floated.\n   */\n\n\n  _floatLabel(shouldAnimate = false) {\n    if (this._formField && this._formField.floatLabel === 'auto') {\n      if (shouldAnimate) {\n        this._formField._animateAndLockLabel();\n      } else {\n        this._formField.floatLabel = 'always';\n      }\n\n      this._manuallyFloatingLabel = true;\n    }\n  }\n  /** If the label has been manually elevated, return it to its normal state. */\n\n\n  _resetLabel() {\n    if (this._manuallyFloatingLabel) {\n      this._formField.floatLabel = 'auto';\n      this._manuallyFloatingLabel = false;\n    }\n  }\n  /**\n   * This method listens to a stream of panel closing actions and resets the\n   * stream every time the option list changes.\n   */\n\n\n  _subscribeToClosingActions() {\n    const firstStable = this._zone.onStable.pipe(take(1));\n\n    const optionChanges = this.autocomplete.options.changes.pipe(tap(() => this._positionStrategy.reapplyLastPosition()), // Defer emitting to the stream until the next tick, because changing\n    // bindings in here will cause \"changed after checked\" errors.\n    delay(0)); // When the zone is stable initially, and when the option list changes...\n\n    return merge(firstStable, optionChanges).pipe( // create a new stream of panelClosingActions, replacing any previous streams\n    // that were created, and flatten it so our stream only emits closing events...\n    switchMap(() => {\n      // The `NgZone.onStable` always emits outside of the Angular zone, thus we have to re-enter\n      // the Angular zone. This will lead to change detection being called outside of the Angular\n      // zone and the `autocomplete.opened` will also emit outside of the Angular.\n      this._zone.run(() => {\n        const wasOpen = this.panelOpen;\n\n        this._resetActiveItem();\n\n        this.autocomplete._setVisibility();\n\n        this._changeDetectorRef.detectChanges();\n\n        if (this.panelOpen) {\n          this._overlayRef.updatePosition();\n        }\n\n        if (wasOpen !== this.panelOpen) {\n          // If the `panelOpen` state changed, we need to make sure to emit the `opened` or\n          // `closed` event, because we may not have emitted it. This can happen\n          // - if the users opens the panel and there are no options, but the\n          //   options come in slightly later or as a result of the value changing,\n          // - if the panel is closed after the user entered a string that did not match any\n          //   of the available options,\n          // - if a valid string is entered after an invalid one.\n          if (this.panelOpen) {\n            this.autocomplete.opened.emit();\n          } else {\n            this.autocomplete.closed.emit();\n          }\n        }\n      });\n\n      return this.panelClosingActions;\n    }), // when the first closing event occurs...\n    take(1)) // set the value, close the panel, and complete.\n    .subscribe(event => this._setValueAndClose(event));\n  }\n  /** Destroys the autocomplete suggestion panel. */\n\n\n  _destroyPanel() {\n    if (this._overlayRef) {\n      this.closePanel();\n\n      this._overlayRef.dispose();\n\n      this._overlayRef = null;\n    }\n  }\n\n  _assignOptionValue(value) {\n    const toDisplay = this.autocomplete && this.autocomplete.displayWith ? this.autocomplete.displayWith(value) : value; // Simply falling back to an empty string if the display value is falsy does not work properly.\n    // The display value can also be the number zero and shouldn't fall back to an empty string.\n\n    this._updateNativeInputValue(toDisplay != null ? toDisplay : '');\n  }\n\n  _updateNativeInputValue(value) {\n    // If it's used within a `MatFormField`, we should set it through the property so it can go\n    // through change detection.\n    if (this._formField) {\n      this._formField._control.value = value;\n    } else {\n      this._element.nativeElement.value = value;\n    }\n\n    this._previousValue = value;\n  }\n  /**\n   * This method closes the panel, and if a value is specified, also sets the associated\n   * control to that value. It will also mark the control as dirty if this interaction\n   * stemmed from the user.\n   */\n\n\n  _setValueAndClose(event) {\n    const toSelect = event ? event.source : this._pendingAutoselectedOption;\n\n    if (toSelect) {\n      this._clearPreviousSelectedOption(toSelect);\n\n      this._assignOptionValue(toSelect.value);\n\n      this._onChange(toSelect.value);\n\n      this.autocomplete._emitSelectEvent(toSelect);\n\n      this._element.nativeElement.focus();\n    }\n\n    this.closePanel();\n  }\n  /**\n   * Clear any previous selected option and emit a selection change event for this option\n   */\n\n\n  _clearPreviousSelectedOption(skip) {\n    this.autocomplete.options.forEach(option => {\n      if (option !== skip && option.selected) {\n        option.deselect();\n      }\n    });\n  }\n\n  _attachOverlay() {\n    if (!this.autocomplete && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatAutocompleteMissingPanelError();\n    }\n\n    let overlayRef = this._overlayRef;\n\n    if (!overlayRef) {\n      this._portal = new TemplatePortal(this.autocomplete.template, this._viewContainerRef, {\n        id: this._formField?.getLabelId()\n      });\n      overlayRef = this._overlay.create(this._getOverlayConfig());\n      this._overlayRef = overlayRef;\n\n      this._handleOverlayEvents(overlayRef);\n\n      this._viewportSubscription = this._viewportRuler.change().subscribe(() => {\n        if (this.panelOpen && overlayRef) {\n          overlayRef.updateSize({\n            width: this._getPanelWidth()\n          });\n        }\n      });\n    } else {\n      // Update the trigger, panel width and direction, in case anything has changed.\n      this._positionStrategy.setOrigin(this._getConnectedElement());\n\n      overlayRef.updateSize({\n        width: this._getPanelWidth()\n      });\n    }\n\n    if (overlayRef && !overlayRef.hasAttached()) {\n      overlayRef.attach(this._portal);\n      this._closingActionsSubscription = this._subscribeToClosingActions();\n    }\n\n    const wasOpen = this.panelOpen;\n\n    this.autocomplete._setVisibility();\n\n    this.autocomplete._isOpen = this._overlayAttached = true; // We need to do an extra `panelOpen` check in here, because the\n    // autocomplete won't be shown if there are no options.\n\n    if (this.panelOpen && wasOpen !== this.panelOpen) {\n      this.autocomplete.opened.emit();\n    }\n  }\n\n  _getOverlayConfig() {\n    return new OverlayConfig({\n      positionStrategy: this._getOverlayPosition(),\n      scrollStrategy: this._scrollStrategy(),\n      width: this._getPanelWidth(),\n      direction: this._dir,\n      panelClass: this._defaults?.overlayPanelClass\n    });\n  }\n\n  _getOverlayPosition() {\n    const strategy = this._overlay.position().flexibleConnectedTo(this._getConnectedElement()).withFlexibleDimensions(false).withPush(false);\n\n    this._setStrategyPositions(strategy);\n\n    this._positionStrategy = strategy;\n    return strategy;\n  }\n  /** Sets the positions on a position strategy based on the directive's input state. */\n\n\n  _setStrategyPositions(positionStrategy) {\n    // Note that we provide horizontal fallback positions, even though by default the dropdown\n    // width matches the input, because consumers can override the width. See #18854.\n    const belowPositions = [{\n      originX: 'start',\n      originY: 'bottom',\n      overlayX: 'start',\n      overlayY: 'top'\n    }, {\n      originX: 'end',\n      originY: 'bottom',\n      overlayX: 'end',\n      overlayY: 'top'\n    }]; // The overlay edge connected to the trigger should have squared corners, while\n    // the opposite end has rounded corners. We apply a CSS class to swap the\n    // border-radius based on the overlay position.\n\n    const panelClass = this._aboveClass;\n    const abovePositions = [{\n      originX: 'start',\n      originY: 'top',\n      overlayX: 'start',\n      overlayY: 'bottom',\n      panelClass\n    }, {\n      originX: 'end',\n      originY: 'top',\n      overlayX: 'end',\n      overlayY: 'bottom',\n      panelClass\n    }];\n    let positions;\n\n    if (this.position === 'above') {\n      positions = abovePositions;\n    } else if (this.position === 'below') {\n      positions = belowPositions;\n    } else {\n      positions = [...belowPositions, ...abovePositions];\n    }\n\n    positionStrategy.withPositions(positions);\n  }\n\n  _getConnectedElement() {\n    if (this.connectedTo) {\n      return this.connectedTo.elementRef;\n    }\n\n    return this._formField ? this._formField.getConnectedOverlayOrigin() : this._element;\n  }\n\n  _getPanelWidth() {\n    return this.autocomplete.panelWidth || this._getHostWidth();\n  }\n  /** Returns the width of the input element, so the panel width can match it. */\n\n\n  _getHostWidth() {\n    return this._getConnectedElement().nativeElement.getBoundingClientRect().width;\n  }\n  /**\n   * Resets the active item to -1 so arrow events will activate the\n   * correct options, or to 0 if the consumer opted into it.\n   */\n\n\n  _resetActiveItem() {\n    const autocomplete = this.autocomplete;\n\n    if (autocomplete.autoActiveFirstOption) {\n      // Note that we go through `setFirstItemActive`, rather than `setActiveItem(0)`, because\n      // the former will find the next enabled option, if the first one is disabled.\n      autocomplete._keyManager.setFirstItemActive();\n    } else {\n      autocomplete._keyManager.setActiveItem(-1);\n    }\n  }\n  /** Determines whether the panel can be opened. */\n\n\n  _canOpen() {\n    const element = this._element.nativeElement;\n    return !element.readOnly && !element.disabled && !this._autocompleteDisabled;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n\n\n  _getWindow() {\n    return this._document?.defaultView || window;\n  }\n  /** Scrolls to a particular option in the list. */\n\n\n  _scrollToOption(index) {\n    // Given that we are not actually focusing active options, we must manually adjust scroll\n    // to reveal options below the fold. First, we find the offset of the option from the top\n    // of the panel. If that offset is below the fold, the new scrollTop will be the offset -\n    // the panel height + the option height, so the active option will be just visible at the\n    // bottom of the panel. If that offset is above the top of the visible panel, the new scrollTop\n    // will become the offset. If that offset is visible within the panel already, the scrollTop is\n    // not adjusted.\n    const autocomplete = this.autocomplete;\n\n    const labelCount = _countGroupLabelsBeforeOption(index, autocomplete.options, autocomplete.optionGroups);\n\n    if (index === 0 && labelCount === 1) {\n      // If we've got one group label before the option and we're at the top option,\n      // scroll the list to the top. This is better UX than scrolling the list to the\n      // top of the option, because it allows the user to read the top group's label.\n      autocomplete._setScrollTop(0);\n    } else if (autocomplete.panel) {\n      const option = autocomplete.options.toArray()[index];\n\n      if (option) {\n        const element = option._getHostElement();\n\n        const newScrollPosition = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, autocomplete._getScrollTop(), autocomplete.panel.nativeElement.offsetHeight);\n\n        autocomplete._setScrollTop(newScrollPosition);\n      }\n    }\n  }\n  /** Handles keyboard events coming from the overlay panel. */\n\n\n  _handleOverlayEvents(overlayRef) {\n    // Use the `keydownEvents` in order to take advantage of\n    // the overlay event targeting provided by the CDK overlay.\n    overlayRef.keydownEvents().subscribe(event => {\n      // Close when pressing ESCAPE or ALT + UP_ARROW, based on the a11y guidelines.\n      // See: https://www.w3.org/TR/wai-aria-practices-1.1/#textbox-keyboard-interaction\n      if (event.keyCode === ESCAPE && !hasModifierKey(event) || event.keyCode === UP_ARROW && hasModifierKey(event, 'altKey')) {\n        // If the user had typed something in before we autoselected an option, and they decided\n        // to cancel the selection, restore the input value to the one they had typed in.\n        if (this._pendingAutoselectedOption) {\n          this._updateNativeInputValue(this._valueBeforeAutoSelection ?? '');\n\n          this._pendingAutoselectedOption = null;\n        }\n\n        this._closeKeyEventStream.next();\n\n        this._resetActiveItem(); // We need to stop propagation, otherwise the event will eventually\n        // reach the input itself and cause the overlay to be reopened.\n\n\n        event.stopPropagation();\n        event.preventDefault();\n      }\n    }); // Subscribe to the pointer events stream so that it doesn't get picked up by other overlays.\n    // TODO(crisbeto): we should switch `_getOutsideClickStream` eventually to use this stream,\n    // but the behvior isn't exactly the same and it ends up breaking some internal tests.\n\n    overlayRef.outsidePointerEvents().subscribe();\n  }\n\n}\n\n_MatAutocompleteTriggerBase.ɵfac = function _MatAutocompleteTriggerBase_Factory(t) {\n  return new (t || _MatAutocompleteTriggerBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1$1.Overlay), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_AUTOCOMPLETE_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i2$1.Directionality, 8), i0.ɵɵdirectiveInject(MAT_FORM_FIELD, 9), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(i3.ViewportRuler), i0.ɵɵdirectiveInject(MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, 8));\n};\n\n_MatAutocompleteTriggerBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatAutocompleteTriggerBase,\n  inputs: {\n    autocomplete: [\"matAutocomplete\", \"autocomplete\"],\n    position: [\"matAutocompletePosition\", \"position\"],\n    connectedTo: [\"matAutocompleteConnectedTo\", \"connectedTo\"],\n    autocompleteAttribute: [\"autocomplete\", \"autocompleteAttribute\"],\n    autocompleteDisabled: [\"matAutocompleteDisabled\", \"autocompleteDisabled\"]\n  },\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatAutocompleteTriggerBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1$1.Overlay\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: i2$1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i4.MatFormField,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_FORM_FIELD]\n      }, {\n        type: Host\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i3.ViewportRuler\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_AUTOCOMPLETE_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, {\n    autocomplete: [{\n      type: Input,\n      args: ['matAutocomplete']\n    }],\n    position: [{\n      type: Input,\n      args: ['matAutocompletePosition']\n    }],\n    connectedTo: [{\n      type: Input,\n      args: ['matAutocompleteConnectedTo']\n    }],\n    autocompleteAttribute: [{\n      type: Input,\n      args: ['autocomplete']\n    }],\n    autocompleteDisabled: [{\n      type: Input,\n      args: ['matAutocompleteDisabled']\n    }]\n  });\n})();\n\nclass MatAutocompleteTrigger extends _MatAutocompleteTriggerBase {\n  constructor() {\n    super(...arguments);\n    this._aboveClass = 'mat-autocomplete-panel-above';\n  }\n\n}\n\nMatAutocompleteTrigger.ɵfac = /* @__PURE__ */function () {\n  let ɵMatAutocompleteTrigger_BaseFactory;\n  return function MatAutocompleteTrigger_Factory(t) {\n    return (ɵMatAutocompleteTrigger_BaseFactory || (ɵMatAutocompleteTrigger_BaseFactory = i0.ɵɵgetInheritedFactory(MatAutocompleteTrigger)))(t || MatAutocompleteTrigger);\n  };\n}();\n\nMatAutocompleteTrigger.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatAutocompleteTrigger,\n  selectors: [[\"input\", \"matAutocomplete\", \"\"], [\"textarea\", \"matAutocomplete\", \"\"]],\n  hostAttrs: [1, \"mat-autocomplete-trigger\"],\n  hostVars: 7,\n  hostBindings: function MatAutocompleteTrigger_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"focusin\", function MatAutocompleteTrigger_focusin_HostBindingHandler() {\n        return ctx._handleFocus();\n      })(\"blur\", function MatAutocompleteTrigger_blur_HostBindingHandler() {\n        return ctx._onTouched();\n      })(\"input\", function MatAutocompleteTrigger_input_HostBindingHandler($event) {\n        return ctx._handleInput($event);\n      })(\"keydown\", function MatAutocompleteTrigger_keydown_HostBindingHandler($event) {\n        return ctx._handleKeydown($event);\n      })(\"click\", function MatAutocompleteTrigger_click_HostBindingHandler() {\n        return ctx._handleClick();\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵattribute(\"autocomplete\", ctx.autocompleteAttribute)(\"role\", ctx.autocompleteDisabled ? null : \"combobox\")(\"aria-autocomplete\", ctx.autocompleteDisabled ? null : \"list\")(\"aria-activedescendant\", ctx.panelOpen && ctx.activeOption ? ctx.activeOption.id : null)(\"aria-expanded\", ctx.autocompleteDisabled ? null : ctx.panelOpen.toString())(\"aria-owns\", ctx.autocompleteDisabled || !ctx.panelOpen ? null : ctx.autocomplete == null ? null : ctx.autocomplete.id)(\"aria-haspopup\", ctx.autocompleteDisabled ? null : \"listbox\");\n    }\n  },\n  exportAs: [\"matAutocompleteTrigger\"],\n  features: [i0.ɵɵProvidersFeature([MAT_AUTOCOMPLETE_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAutocompleteTrigger, [{\n    type: Directive,\n    args: [{\n      selector: `input[matAutocomplete], textarea[matAutocomplete]`,\n      host: {\n        'class': 'mat-autocomplete-trigger',\n        '[attr.autocomplete]': 'autocompleteAttribute',\n        '[attr.role]': 'autocompleteDisabled ? null : \"combobox\"',\n        '[attr.aria-autocomplete]': 'autocompleteDisabled ? null : \"list\"',\n        '[attr.aria-activedescendant]': '(panelOpen && activeOption) ? activeOption.id : null',\n        '[attr.aria-expanded]': 'autocompleteDisabled ? null : panelOpen.toString()',\n        '[attr.aria-owns]': '(autocompleteDisabled || !panelOpen) ? null : autocomplete?.id',\n        '[attr.aria-haspopup]': 'autocompleteDisabled ? null : \"listbox\"',\n        // Note: we use `focusin`, as opposed to `focus`, in order to open the panel\n        // a little earlier. This avoids issues where IE delays the focusing of the input.\n        '(focusin)': '_handleFocus()',\n        '(blur)': '_onTouched()',\n        '(input)': '_handleInput($event)',\n        '(keydown)': '_handleKeydown($event)',\n        '(click)': '_handleClick()'\n      },\n      exportAs: 'matAutocompleteTrigger',\n      providers: [MAT_AUTOCOMPLETE_VALUE_ACCESSOR]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatAutocompleteModule {}\n\nMatAutocompleteModule.ɵfac = function MatAutocompleteModule_Factory(t) {\n  return new (t || MatAutocompleteModule)();\n};\n\nMatAutocompleteModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatAutocompleteModule,\n  declarations: [MatAutocomplete, MatAutocompleteTrigger, MatAutocompleteOrigin],\n  imports: [OverlayModule, MatOptionModule, MatCommonModule, CommonModule],\n  exports: [MatAutocomplete, MatAutocompleteTrigger, MatAutocompleteOrigin, CdkScrollableModule, MatOptionModule, MatCommonModule]\n});\nMatAutocompleteModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER],\n  imports: [OverlayModule, MatOptionModule, MatCommonModule, CommonModule, CdkScrollableModule, MatOptionModule, MatCommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAutocompleteModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, MatOptionModule, MatCommonModule, CommonModule],\n      exports: [MatAutocomplete, MatAutocompleteTrigger, MatAutocompleteOrigin, CdkScrollableModule, MatOptionModule, MatCommonModule],\n      declarations: [MatAutocomplete, MatAutocompleteTrigger, MatAutocompleteOrigin],\n      providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER, MAT_AUTOCOMPLETE_VALUE_ACCESSOR, MatAutocomplete, MatAutocompleteModule, MatAutocompleteOrigin, MatAutocompleteSelectedEvent, MatAutocompleteTrigger, _MatAutocompleteBase, _MatAutocompleteOriginBase, _MatAutocompleteTriggerBase, getMatAutocompleteMissingPanelError };", "map": {"version": 3, "names": ["ActiveDescendantKeyManager", "coerceBooleanProperty", "coerce<PERSON><PERSON><PERSON><PERSON><PERSON>", "i1", "_getEventTarget", "i0", "InjectionToken", "EventEmitter", "TemplateRef", "Directive", "Inject", "ViewChild", "Input", "Output", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ContentChildren", "forwardRef", "Optional", "Host", "NgModule", "mixinDisableRipple", "MAT_OPTION_PARENT_COMPONENT", "MAT_OPTGROUP", "MatOption", "MatOptionSelectionChange", "_countGroupLabelsBeforeOption", "_getOptionScrollPosition", "MatOptionModule", "MatCommonModule", "Subscription", "Subject", "defer", "merge", "of", "fromEvent", "i2", "DOCUMENT", "CommonModule", "i1$1", "Overlay", "OverlayConfig", "OverlayModule", "i3", "CdkScrollableModule", "i2$1", "hasModifierKey", "ESCAPE", "ENTER", "UP_ARROW", "DOWN_ARROW", "TAB", "TemplatePortal", "NG_VALUE_ACCESSOR", "i4", "MAT_FORM_FIELD", "startWith", "switchMap", "take", "filter", "map", "tap", "delay", "_uniqueAutocompleteIdCounter", "MatAutocompleteSelectedEvent", "constructor", "source", "option", "_MatAutocompleteMixinBase", "MAT_AUTOCOMPLETE_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY", "autoActiveFirstOption", "autoSelectActiveOption", "_MatAutocompleteBase", "_changeDetectorRef", "_elementRef", "defaults", "platform", "_activeOptionChanges", "EMPTY", "showPanel", "_isOpen", "displayWith", "optionSelected", "opened", "closed", "optionActivated", "_classList", "id", "inertGroups", "SAFARI", "_autoActiveFirstOption", "_autoSelectActiveOption", "isOpen", "value", "classList", "length", "reduce", "className", "_setVisibilityClasses", "nativeElement", "ngAfterContentInit", "_keyManager", "options", "withWrap", "change", "subscribe", "index", "emit", "toArray", "_setVisibility", "ngOnDestroy", "unsubscribe", "_setScrollTop", "scrollTop", "panel", "_getScrollTop", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_emitSelectEvent", "event", "_getPanelAriaLabe<PERSON>by", "labelId", "aria<PERSON><PERSON><PERSON>", "labelExpression", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_visibleClass", "_hiddenClass", "ɵfac", "ChangeDetectorRef", "ElementRef", "Platform", "ɵdir", "type", "undefined", "decorators", "args", "template", "static", "panelWidth", "MatAutocomplete", "arguments", "ɵcmp", "provide", "useExisting", "Ng<PERSON><PERSON>", "selector", "encapsulation", "None", "changeDetection", "OnPush", "exportAs", "inputs", "host", "providers", "styles", "optionGroups", "descendants", "_MatAutocompleteOriginBase", "elementRef", "MatAutocompleteOrigin", "MAT_AUTOCOMPLETE_SCROLL_STRATEGY", "MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY", "overlay", "scrollStrategies", "reposition", "MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER", "deps", "useFactory", "MAT_AUTOCOMPLETE_VALUE_ACCESSOR", "MatAutocompleteTrigger", "multi", "getMatAutocompleteMissingPanelError", "Error", "_MatAutocompleteTriggerBase", "_element", "_overlay", "_viewContainerRef", "_zone", "scrollStrategy", "_dir", "_formField", "_document", "_viewportRuler", "_defaults", "_componentDestroyed", "_autocompleteDisabled", "_manuallyFloatingLabel", "_viewportSubscription", "_canOpenOnNextFocus", "_closeKeyEventStream", "_windowBlurHandler", "activeElement", "panelOpen", "_onChange", "_onTouched", "position", "autocompleteAttribute", "_overlayAttached", "optionSelections", "autocomplete", "changes", "pipe", "onSelectionChange", "onStable", "_scrollStrategy", "autocompleteDisabled", "ngAfterViewInit", "window", "_getWindow", "runOutsideAngular", "addEventListener", "ngOnChanges", "_positionStrategy", "_setStrategyPositions", "_overlayRef", "updatePosition", "removeEventListener", "_destroyPanel", "complete", "openPanel", "_attachOverlay", "_floatLabel", "closePanel", "_resetLabel", "run", "_pendingAutoselectedOption", "has<PERSON>tta<PERSON>", "detach", "_closingActionsSubscription", "detectChanges", "panelClosingActions", "tabOut", "_getOutsideClickStream", "detachments", "activeOption", "activeItem", "clickTarget", "formField", "customOrigin", "connectedTo", "contains", "overlayElement", "writeValue", "Promise", "resolve", "then", "_assignOptionValue", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "disabled", "_handleKeydown", "keyCode", "hasModifier", "preventDefault", "_selectViaInteraction", "_resetActiveItem", "prevActiveItem", "isArrowKey", "onKeydown", "_canOpen", "_scrollToOption", "activeItemIndex", "_valueBeforeAutoSelection", "_handleInput", "target", "parseFloat", "_previousValue", "_handleFocus", "_handleClick", "shouldAnimate", "floatLabel", "_animateAndLockLabel", "_subscribeToClosingActions", "firstStable", "optionChanges", "reapplyLastPosition", "was<PERSON><PERSON>", "_setValueAndClose", "dispose", "toDisplay", "_updateNativeInputValue", "_control", "toSelect", "_clearPreviousSelectedOption", "focus", "skip", "for<PERSON>ach", "selected", "deselect", "ngDevMode", "overlayRef", "_portal", "getLabelId", "create", "_getOverlayConfig", "_handleOverlayEvents", "updateSize", "width", "_get<PERSON><PERSON><PERSON><PERSON>idth", "<PERSON><PERSON><PERSON><PERSON>", "_getConnectedElement", "attach", "positionStrategy", "_getOverlayPosition", "direction", "panelClass", "overlayPanelClass", "strategy", "flexibleConnectedTo", "withFlexibleDimensions", "with<PERSON><PERSON>", "belowPositions", "originX", "originY", "overlayX", "overlayY", "_aboveClass", "abovePositions", "positions", "withPositions", "getConnectedOverlayOrigin", "_getHostWidth", "getBoundingClientRect", "setFirstItemActive", "setActiveItem", "element", "readOnly", "defaultView", "labelCount", "_getHostElement", "newScrollPosition", "offsetTop", "offsetHeight", "keydownEvents", "next", "stopPropagation", "outsidePointerEvents", "ViewContainerRef", "NgZone", "Directionality", "ViewportRuler", "MatFormField", "MatAutocompleteModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/material/fesm2020/autocomplete.mjs"], "sourcesContent": ["import { ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty, coerceStringArray } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/platform';\nimport { _getEventTarget } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, TemplateRef, Directive, Inject, ViewChild, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, forwardRef, Optional, Host, NgModule } from '@angular/core';\nimport { mixinDisableRipple, MAT_OPTION_PARENT_COMPONENT, MAT_OPTGROUP, MatOption, MatOptionSelectionChange, _countGroupLabelsBeforeOption, _getOptionScrollPosition, MatOptionModule, MatCommonModule } from '@angular/material/core';\nimport { Subscription, Subject, defer, merge, of, fromEvent } from 'rxjs';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport * as i3 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i2$1 from '@angular/cdk/bidi';\nimport { hasModifierKey, ESCAPE, ENTER, UP_ARROW, DOWN_ARROW, TAB } from '@angular/cdk/keycodes';\nimport { TemplatePortal } from '@angular/cdk/portal';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i4 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD } from '@angular/material/form-field';\nimport { startWith, switchMap, take, filter, map, tap, delay } from 'rxjs/operators';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Autocomplete IDs need to be unique across components, so this counter exists outside of\n * the component definition.\n */\nlet _uniqueAutocompleteIdCounter = 0;\n/** Event object that is emitted when an autocomplete option is selected. */\nclass MatAutocompleteSelectedEvent {\n    constructor(\n    /** Reference to the autocomplete panel that emitted the event. */\n    source, \n    /** Option that was selected. */\n    option) {\n        this.source = source;\n        this.option = option;\n    }\n}\n// Boilerplate for applying mixins to MatAutocomplete.\n/** @docs-private */\nconst _MatAutocompleteMixinBase = mixinDisableRipple(class {\n});\n/** Injection token to be used to override the default options for `mat-autocomplete`. */\nconst MAT_AUTOCOMPLETE_DEFAULT_OPTIONS = new InjectionToken('mat-autocomplete-default-options', {\n    providedIn: 'root',\n    factory: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY,\n});\n/** @docs-private */\nfunction MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY() {\n    return { autoActiveFirstOption: false, autoSelectActiveOption: false };\n}\n/** Base class with all of the `MatAutocomplete` functionality. */\nclass _MatAutocompleteBase extends _MatAutocompleteMixinBase {\n    constructor(_changeDetectorRef, _elementRef, defaults, platform) {\n        super();\n        this._changeDetectorRef = _changeDetectorRef;\n        this._elementRef = _elementRef;\n        this._activeOptionChanges = Subscription.EMPTY;\n        /** Whether the autocomplete panel should be visible, depending on option length. */\n        this.showPanel = false;\n        this._isOpen = false;\n        /** Function that maps an option's control value to its display value in the trigger. */\n        this.displayWith = null;\n        /** Event that is emitted whenever an option from the list is selected. */\n        this.optionSelected = new EventEmitter();\n        /** Event that is emitted when the autocomplete panel is opened. */\n        this.opened = new EventEmitter();\n        /** Event that is emitted when the autocomplete panel is closed. */\n        this.closed = new EventEmitter();\n        /** Emits whenever an option is activated. */\n        this.optionActivated = new EventEmitter();\n        this._classList = {};\n        /** Unique ID to be used by autocomplete trigger's \"aria-owns\" property. */\n        this.id = `mat-autocomplete-${_uniqueAutocompleteIdCounter++}`;\n        // TODO(crisbeto): the problem that the `inertGroups` option resolves is only present on\n        // Safari using VoiceOver. We should occasionally check back to see whether the bug\n        // wasn't resolved in VoiceOver, and if it has, we can remove this and the `inertGroups`\n        // option altogether.\n        this.inertGroups = platform?.SAFARI || false;\n        this._autoActiveFirstOption = !!defaults.autoActiveFirstOption;\n        this._autoSelectActiveOption = !!defaults.autoSelectActiveOption;\n    }\n    /** Whether the autocomplete panel is open. */\n    get isOpen() {\n        return this._isOpen && this.showPanel;\n    }\n    /**\n     * Whether the first option should be highlighted when the autocomplete panel is opened.\n     * Can be configured globally through the `MAT_AUTOCOMPLETE_DEFAULT_OPTIONS` token.\n     */\n    get autoActiveFirstOption() {\n        return this._autoActiveFirstOption;\n    }\n    set autoActiveFirstOption(value) {\n        this._autoActiveFirstOption = coerceBooleanProperty(value);\n    }\n    /** Whether the active option should be selected as the user is navigating. */\n    get autoSelectActiveOption() {\n        return this._autoSelectActiveOption;\n    }\n    set autoSelectActiveOption(value) {\n        this._autoSelectActiveOption = coerceBooleanProperty(value);\n    }\n    /**\n     * Takes classes set on the host mat-autocomplete element and applies them to the panel\n     * inside the overlay container to allow for easy styling.\n     */\n    set classList(value) {\n        if (value && value.length) {\n            this._classList = coerceStringArray(value).reduce((classList, className) => {\n                classList[className] = true;\n                return classList;\n            }, {});\n        }\n        else {\n            this._classList = {};\n        }\n        this._setVisibilityClasses(this._classList);\n        this._elementRef.nativeElement.className = '';\n    }\n    ngAfterContentInit() {\n        this._keyManager = new ActiveDescendantKeyManager(this.options).withWrap();\n        this._activeOptionChanges = this._keyManager.change.subscribe(index => {\n            if (this.isOpen) {\n                this.optionActivated.emit({ source: this, option: this.options.toArray()[index] || null });\n            }\n        });\n        // Set the initial visibility state.\n        this._setVisibility();\n    }\n    ngOnDestroy() {\n        this._activeOptionChanges.unsubscribe();\n    }\n    /**\n     * Sets the panel scrollTop. This allows us to manually scroll to display options\n     * above or below the fold, as they are not actually being focused when active.\n     */\n    _setScrollTop(scrollTop) {\n        if (this.panel) {\n            this.panel.nativeElement.scrollTop = scrollTop;\n        }\n    }\n    /** Returns the panel's scrollTop. */\n    _getScrollTop() {\n        return this.panel ? this.panel.nativeElement.scrollTop : 0;\n    }\n    /** Panel should hide itself when the option list is empty. */\n    _setVisibility() {\n        this.showPanel = !!this.options.length;\n        this._setVisibilityClasses(this._classList);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Emits the `select` event. */\n    _emitSelectEvent(option) {\n        const event = new MatAutocompleteSelectedEvent(this, option);\n        this.optionSelected.emit(event);\n    }\n    /** Gets the aria-labelledby for the autocomplete panel. */\n    _getPanelAriaLabelledby(labelId) {\n        if (this.ariaLabel) {\n            return null;\n        }\n        const labelExpression = labelId ? labelId + ' ' : '';\n        return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n    }\n    /** Sets the autocomplete visibility classes on a classlist based on the panel is visible. */\n    _setVisibilityClasses(classList) {\n        classList[this._visibleClass] = this.showPanel;\n        classList[this._hiddenClass] = !this.showPanel;\n    }\n}\n_MatAutocompleteBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatAutocompleteBase, deps: [{ token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS }, { token: i1.Platform }], target: i0.ɵɵFactoryTarget.Directive });\n_MatAutocompleteBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: _MatAutocompleteBase, inputs: { ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], displayWith: \"displayWith\", autoActiveFirstOption: \"autoActiveFirstOption\", autoSelectActiveOption: \"autoSelectActiveOption\", panelWidth: \"panelWidth\", classList: [\"class\", \"classList\"] }, outputs: { optionSelected: \"optionSelected\", opened: \"opened\", closed: \"closed\", optionActivated: \"optionActivated\" }, viewQueries: [{ propertyName: \"template\", first: true, predicate: TemplateRef, descendants: true, static: true }, { propertyName: \"panel\", first: true, predicate: [\"panel\"], descendants: true }], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatAutocompleteBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_AUTOCOMPLETE_DEFAULT_OPTIONS]\n                }] }, { type: i1.Platform }]; }, propDecorators: { template: [{\n                type: ViewChild,\n                args: [TemplateRef, { static: true }]\n            }], panel: [{\n                type: ViewChild,\n                args: ['panel']\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], displayWith: [{\n                type: Input\n            }], autoActiveFirstOption: [{\n                type: Input\n            }], autoSelectActiveOption: [{\n                type: Input\n            }], panelWidth: [{\n                type: Input\n            }], optionSelected: [{\n                type: Output\n            }], opened: [{\n                type: Output\n            }], closed: [{\n                type: Output\n            }], optionActivated: [{\n                type: Output\n            }], classList: [{\n                type: Input,\n                args: ['class']\n            }] } });\nclass MatAutocomplete extends _MatAutocompleteBase {\n    constructor() {\n        super(...arguments);\n        this._visibleClass = 'mat-autocomplete-visible';\n        this._hiddenClass = 'mat-autocomplete-hidden';\n    }\n}\nMatAutocomplete.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatAutocomplete, deps: null, target: i0.ɵɵFactoryTarget.Component });\nMatAutocomplete.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatAutocomplete, selector: \"mat-autocomplete\", inputs: { disableRipple: \"disableRipple\" }, host: { classAttribute: \"mat-autocomplete\" }, providers: [{ provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatAutocomplete }], queries: [{ propertyName: \"optionGroups\", predicate: MAT_OPTGROUP, descendants: true }, { propertyName: \"options\", predicate: MatOption, descendants: true }], exportAs: [\"matAutocomplete\"], usesInheritance: true, ngImport: i0, template: \"<ng-template let-formFieldId=\\\"id\\\">\\n  <div class=\\\"mat-autocomplete-panel\\\"\\n       role=\\\"listbox\\\"\\n       [id]=\\\"id\\\"\\n       [attr.aria-label]=\\\"ariaLabel || null\\\"\\n       [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby(formFieldId)\\\"\\n       [ngClass]=\\\"_classList\\\"\\n       #panel>\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\".mat-autocomplete-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;visibility:hidden;max-width:none;max-height:256px;position:relative;width:100%;border-bottom-left-radius:4px;border-bottom-right-radius:4px}.mat-autocomplete-panel.mat-autocomplete-visible{visibility:visible}.mat-autocomplete-panel.mat-autocomplete-hidden{visibility:hidden}.mat-autocomplete-panel-above .mat-autocomplete-panel{border-radius:0;border-top-left-radius:4px;border-top-right-radius:4px}.mat-autocomplete-panel .mat-divider-horizontal{margin-top:-1px}.cdk-high-contrast-active .mat-autocomplete-panel{outline:solid 1px}mat-autocomplete{display:none}\"], dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatAutocomplete, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-autocomplete', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, exportAs: 'matAutocomplete', inputs: ['disableRipple'], host: {\n                        'class': 'mat-autocomplete',\n                    }, providers: [{ provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatAutocomplete }], template: \"<ng-template let-formFieldId=\\\"id\\\">\\n  <div class=\\\"mat-autocomplete-panel\\\"\\n       role=\\\"listbox\\\"\\n       [id]=\\\"id\\\"\\n       [attr.aria-label]=\\\"ariaLabel || null\\\"\\n       [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby(formFieldId)\\\"\\n       [ngClass]=\\\"_classList\\\"\\n       #panel>\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\".mat-autocomplete-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;visibility:hidden;max-width:none;max-height:256px;position:relative;width:100%;border-bottom-left-radius:4px;border-bottom-right-radius:4px}.mat-autocomplete-panel.mat-autocomplete-visible{visibility:visible}.mat-autocomplete-panel.mat-autocomplete-hidden{visibility:hidden}.mat-autocomplete-panel-above .mat-autocomplete-panel{border-radius:0;border-top-left-radius:4px;border-top-right-radius:4px}.mat-autocomplete-panel .mat-divider-horizontal{margin-top:-1px}.cdk-high-contrast-active .mat-autocomplete-panel{outline:solid 1px}mat-autocomplete{display:none}\"] }]\n        }], propDecorators: { optionGroups: [{\n                type: ContentChildren,\n                args: [MAT_OPTGROUP, { descendants: true }]\n            }], options: [{\n                type: ContentChildren,\n                args: [MatOption, { descendants: true }]\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Base class containing all of the functionality for `MatAutocompleteOrigin`. */\nclass _MatAutocompleteOriginBase {\n    constructor(\n    /** Reference to the element on which the directive is applied. */\n    elementRef) {\n        this.elementRef = elementRef;\n    }\n}\n_MatAutocompleteOriginBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatAutocompleteOriginBase, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\n_MatAutocompleteOriginBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: _MatAutocompleteOriginBase, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatAutocompleteOriginBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; } });\n/**\n * Directive applied to an element to make it usable\n * as a connection point for an autocomplete panel.\n */\nclass MatAutocompleteOrigin extends _MatAutocompleteOriginBase {\n}\nMatAutocompleteOrigin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatAutocompleteOrigin, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatAutocompleteOrigin.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatAutocompleteOrigin, selector: \"[matAutocompleteOrigin]\", exportAs: [\"matAutocompleteOrigin\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatAutocompleteOrigin, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matAutocompleteOrigin]',\n                    exportAs: 'matAutocompleteOrigin',\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token that determines the scroll handling while the autocomplete panel is open. */\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY = new InjectionToken('mat-autocomplete-scroll-strategy');\n/** @docs-private */\nfunction MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n    provide: MAT_AUTOCOMPLETE_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY,\n};\n/**\n * Provider that allows the autocomplete to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_AUTOCOMPLETE_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatAutocompleteTrigger),\n    multi: true,\n};\n/**\n * Creates an error to be thrown when attempting to use an autocomplete trigger without a panel.\n * @docs-private\n */\nfunction getMatAutocompleteMissingPanelError() {\n    return Error('Attempting to open an undefined instance of `mat-autocomplete`. ' +\n        'Make sure that the id passed to the `matAutocomplete` is correct and that ' +\n        \"you're attempting to open it after the ngAfterContentInit hook.\");\n}\n/** Base class with all of the `MatAutocompleteTrigger` functionality. */\nclass _MatAutocompleteTriggerBase {\n    constructor(_element, _overlay, _viewContainerRef, _zone, _changeDetectorRef, scrollStrategy, _dir, _formField, _document, _viewportRuler, _defaults) {\n        this._element = _element;\n        this._overlay = _overlay;\n        this._viewContainerRef = _viewContainerRef;\n        this._zone = _zone;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._dir = _dir;\n        this._formField = _formField;\n        this._document = _document;\n        this._viewportRuler = _viewportRuler;\n        this._defaults = _defaults;\n        this._componentDestroyed = false;\n        this._autocompleteDisabled = false;\n        /** Whether or not the label state is being overridden. */\n        this._manuallyFloatingLabel = false;\n        /** Subscription to viewport size changes. */\n        this._viewportSubscription = Subscription.EMPTY;\n        /**\n         * Whether the autocomplete can open the next time it is focused. Used to prevent a focused,\n         * closed autocomplete from being reopened if the user switches to another browser tab and then\n         * comes back.\n         */\n        this._canOpenOnNextFocus = true;\n        /** Stream of keyboard events that can close the panel. */\n        this._closeKeyEventStream = new Subject();\n        /**\n         * Event handler for when the window is blurred. Needs to be an\n         * arrow function in order to preserve the context.\n         */\n        this._windowBlurHandler = () => {\n            // If the user blurred the window while the autocomplete is focused, it means that it'll be\n            // refocused when they come back. In this case we want to skip the first focus event, if the\n            // pane was closed, in order to avoid reopening it unintentionally.\n            this._canOpenOnNextFocus =\n                this._document.activeElement !== this._element.nativeElement || this.panelOpen;\n        };\n        /** `View -> model callback called when value changes` */\n        this._onChange = () => { };\n        /** `View -> model callback called when autocomplete has been touched` */\n        this._onTouched = () => { };\n        /**\n         * Position of the autocomplete panel relative to the trigger element. A position of `auto`\n         * will render the panel underneath the trigger if there is enough space for it to fit in\n         * the viewport, otherwise the panel will be shown above it. If the position is set to\n         * `above` or `below`, the panel will always be shown above or below the trigger. no matter\n         * whether it fits completely in the viewport.\n         */\n        this.position = 'auto';\n        /**\n         * `autocomplete` attribute to be set on the input element.\n         * @docs-private\n         */\n        this.autocompleteAttribute = 'off';\n        this._overlayAttached = false;\n        /** Stream of changes to the selection state of the autocomplete options. */\n        this.optionSelections = defer(() => {\n            const options = this.autocomplete ? this.autocomplete.options : null;\n            if (options) {\n                return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n            }\n            // If there are any subscribers before `ngAfterViewInit`, the `autocomplete` will be undefined.\n            // Return a stream that we'll replace with the real one once everything is in place.\n            return this._zone.onStable.pipe(take(1), switchMap(() => this.optionSelections));\n        });\n        this._scrollStrategy = scrollStrategy;\n    }\n    /**\n     * Whether the autocomplete is disabled. When disabled, the element will\n     * act as a regular input and the user won't be able to open the panel.\n     */\n    get autocompleteDisabled() {\n        return this._autocompleteDisabled;\n    }\n    set autocompleteDisabled(value) {\n        this._autocompleteDisabled = coerceBooleanProperty(value);\n    }\n    ngAfterViewInit() {\n        const window = this._getWindow();\n        if (typeof window !== 'undefined') {\n            this._zone.runOutsideAngular(() => window.addEventListener('blur', this._windowBlurHandler));\n        }\n    }\n    ngOnChanges(changes) {\n        if (changes['position'] && this._positionStrategy) {\n            this._setStrategyPositions(this._positionStrategy);\n            if (this.panelOpen) {\n                this._overlayRef.updatePosition();\n            }\n        }\n    }\n    ngOnDestroy() {\n        const window = this._getWindow();\n        if (typeof window !== 'undefined') {\n            window.removeEventListener('blur', this._windowBlurHandler);\n        }\n        this._viewportSubscription.unsubscribe();\n        this._componentDestroyed = true;\n        this._destroyPanel();\n        this._closeKeyEventStream.complete();\n    }\n    /** Whether or not the autocomplete panel is open. */\n    get panelOpen() {\n        return this._overlayAttached && this.autocomplete.showPanel;\n    }\n    /** Opens the autocomplete suggestion panel. */\n    openPanel() {\n        this._attachOverlay();\n        this._floatLabel();\n    }\n    /** Closes the autocomplete suggestion panel. */\n    closePanel() {\n        this._resetLabel();\n        if (!this._overlayAttached) {\n            return;\n        }\n        if (this.panelOpen) {\n            // Only emit if the panel was visible.\n            // The `NgZone.onStable` always emits outside of the Angular zone,\n            // so all the subscriptions from `_subscribeToClosingActions()` are also outside of the Angular zone.\n            // We should manually run in Angular zone to update UI after panel closing.\n            this._zone.run(() => {\n                this.autocomplete.closed.emit();\n            });\n        }\n        this.autocomplete._isOpen = this._overlayAttached = false;\n        this._pendingAutoselectedOption = null;\n        if (this._overlayRef && this._overlayRef.hasAttached()) {\n            this._overlayRef.detach();\n            this._closingActionsSubscription.unsubscribe();\n        }\n        // Note that in some cases this can end up being called after the component is destroyed.\n        // Add a check to ensure that we don't try to run change detection on a destroyed view.\n        if (!this._componentDestroyed) {\n            // We need to trigger change detection manually, because\n            // `fromEvent` doesn't seem to do it at the proper time.\n            // This ensures that the label is reset when the\n            // user clicks outside.\n            this._changeDetectorRef.detectChanges();\n        }\n    }\n    /**\n     * Updates the position of the autocomplete suggestion panel to ensure that it fits all options\n     * within the viewport.\n     */\n    updatePosition() {\n        if (this._overlayAttached) {\n            this._overlayRef.updatePosition();\n        }\n    }\n    /**\n     * A stream of actions that should close the autocomplete panel, including\n     * when an option is selected, on blur, and when TAB is pressed.\n     */\n    get panelClosingActions() {\n        return merge(this.optionSelections, this.autocomplete._keyManager.tabOut.pipe(filter(() => this._overlayAttached)), this._closeKeyEventStream, this._getOutsideClickStream(), this._overlayRef\n            ? this._overlayRef.detachments().pipe(filter(() => this._overlayAttached))\n            : of()).pipe(\n        // Normalize the output so we return a consistent type.\n        map(event => (event instanceof MatOptionSelectionChange ? event : null)));\n    }\n    /** The currently active option, coerced to MatOption type. */\n    get activeOption() {\n        if (this.autocomplete && this.autocomplete._keyManager) {\n            return this.autocomplete._keyManager.activeItem;\n        }\n        return null;\n    }\n    /** Stream of clicks outside of the autocomplete panel. */\n    _getOutsideClickStream() {\n        return merge(fromEvent(this._document, 'click'), fromEvent(this._document, 'auxclick'), fromEvent(this._document, 'touchend')).pipe(filter(event => {\n            // If we're in the Shadow DOM, the event target will be the shadow root, so we have to\n            // fall back to check the first element in the path of the click event.\n            const clickTarget = _getEventTarget(event);\n            const formField = this._formField ? this._formField._elementRef.nativeElement : null;\n            const customOrigin = this.connectedTo ? this.connectedTo.elementRef.nativeElement : null;\n            return (this._overlayAttached &&\n                clickTarget !== this._element.nativeElement &&\n                // Normally focus moves inside `mousedown` so this condition will almost always be\n                // true. Its main purpose is to handle the case where the input is focused from an\n                // outside click which propagates up to the `body` listener within the same sequence\n                // and causes the panel to close immediately (see #3106).\n                this._document.activeElement !== this._element.nativeElement &&\n                (!formField || !formField.contains(clickTarget)) &&\n                (!customOrigin || !customOrigin.contains(clickTarget)) &&\n                !!this._overlayRef &&\n                !this._overlayRef.overlayElement.contains(clickTarget));\n        }));\n    }\n    // Implemented as part of ControlValueAccessor.\n    writeValue(value) {\n        Promise.resolve(null).then(() => this._assignOptionValue(value));\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    setDisabledState(isDisabled) {\n        this._element.nativeElement.disabled = isDisabled;\n    }\n    _handleKeydown(event) {\n        const keyCode = event.keyCode;\n        const hasModifier = hasModifierKey(event);\n        // Prevent the default action on all escape key presses. This is here primarily to bring IE\n        // in line with other browsers. By default, pressing escape on IE will cause it to revert\n        // the input value to the one that it had on focus, however it won't dispatch any events\n        // which means that the model value will be out of sync with the view.\n        if (keyCode === ESCAPE && !hasModifier) {\n            event.preventDefault();\n        }\n        if (this.activeOption && keyCode === ENTER && this.panelOpen && !hasModifier) {\n            this.activeOption._selectViaInteraction();\n            this._resetActiveItem();\n            event.preventDefault();\n        }\n        else if (this.autocomplete) {\n            const prevActiveItem = this.autocomplete._keyManager.activeItem;\n            const isArrowKey = keyCode === UP_ARROW || keyCode === DOWN_ARROW;\n            if (keyCode === TAB || (isArrowKey && !hasModifier && this.panelOpen)) {\n                this.autocomplete._keyManager.onKeydown(event);\n            }\n            else if (isArrowKey && this._canOpen()) {\n                this.openPanel();\n            }\n            if (isArrowKey || this.autocomplete._keyManager.activeItem !== prevActiveItem) {\n                this._scrollToOption(this.autocomplete._keyManager.activeItemIndex || 0);\n                if (this.autocomplete.autoSelectActiveOption && this.activeOption) {\n                    if (!this._pendingAutoselectedOption) {\n                        this._valueBeforeAutoSelection = this._element.nativeElement.value;\n                    }\n                    this._pendingAutoselectedOption = this.activeOption;\n                    this._assignOptionValue(this.activeOption.value);\n                }\n            }\n        }\n    }\n    _handleInput(event) {\n        let target = event.target;\n        let value = target.value;\n        // Based on `NumberValueAccessor` from forms.\n        if (target.type === 'number') {\n            value = value == '' ? null : parseFloat(value);\n        }\n        // If the input has a placeholder, IE will fire the `input` event on page load,\n        // focus and blur, in addition to when the user actually changed the value. To\n        // filter out all of the extra events, we save the value on focus and between\n        // `input` events, and we check whether it changed.\n        // See: https://connect.microsoft.com/IE/feedback/details/885747/\n        if (this._previousValue !== value) {\n            this._previousValue = value;\n            this._pendingAutoselectedOption = null;\n            this._onChange(value);\n            if (this._canOpen() && this._document.activeElement === event.target) {\n                this.openPanel();\n            }\n        }\n    }\n    _handleFocus() {\n        if (!this._canOpenOnNextFocus) {\n            this._canOpenOnNextFocus = true;\n        }\n        else if (this._canOpen()) {\n            this._previousValue = this._element.nativeElement.value;\n            this._attachOverlay();\n            this._floatLabel(true);\n        }\n    }\n    _handleClick() {\n        if (this._canOpen() && !this.panelOpen) {\n            this.openPanel();\n        }\n    }\n    /**\n     * In \"auto\" mode, the label will animate down as soon as focus is lost.\n     * This causes the value to jump when selecting an option with the mouse.\n     * This method manually floats the label until the panel can be closed.\n     * @param shouldAnimate Whether the label should be animated when it is floated.\n     */\n    _floatLabel(shouldAnimate = false) {\n        if (this._formField && this._formField.floatLabel === 'auto') {\n            if (shouldAnimate) {\n                this._formField._animateAndLockLabel();\n            }\n            else {\n                this._formField.floatLabel = 'always';\n            }\n            this._manuallyFloatingLabel = true;\n        }\n    }\n    /** If the label has been manually elevated, return it to its normal state. */\n    _resetLabel() {\n        if (this._manuallyFloatingLabel) {\n            this._formField.floatLabel = 'auto';\n            this._manuallyFloatingLabel = false;\n        }\n    }\n    /**\n     * This method listens to a stream of panel closing actions and resets the\n     * stream every time the option list changes.\n     */\n    _subscribeToClosingActions() {\n        const firstStable = this._zone.onStable.pipe(take(1));\n        const optionChanges = this.autocomplete.options.changes.pipe(tap(() => this._positionStrategy.reapplyLastPosition()), \n        // Defer emitting to the stream until the next tick, because changing\n        // bindings in here will cause \"changed after checked\" errors.\n        delay(0));\n        // When the zone is stable initially, and when the option list changes...\n        return (merge(firstStable, optionChanges)\n            .pipe(\n        // create a new stream of panelClosingActions, replacing any previous streams\n        // that were created, and flatten it so our stream only emits closing events...\n        switchMap(() => {\n            // The `NgZone.onStable` always emits outside of the Angular zone, thus we have to re-enter\n            // the Angular zone. This will lead to change detection being called outside of the Angular\n            // zone and the `autocomplete.opened` will also emit outside of the Angular.\n            this._zone.run(() => {\n                const wasOpen = this.panelOpen;\n                this._resetActiveItem();\n                this.autocomplete._setVisibility();\n                this._changeDetectorRef.detectChanges();\n                if (this.panelOpen) {\n                    this._overlayRef.updatePosition();\n                }\n                if (wasOpen !== this.panelOpen) {\n                    // If the `panelOpen` state changed, we need to make sure to emit the `opened` or\n                    // `closed` event, because we may not have emitted it. This can happen\n                    // - if the users opens the panel and there are no options, but the\n                    //   options come in slightly later or as a result of the value changing,\n                    // - if the panel is closed after the user entered a string that did not match any\n                    //   of the available options,\n                    // - if a valid string is entered after an invalid one.\n                    if (this.panelOpen) {\n                        this.autocomplete.opened.emit();\n                    }\n                    else {\n                        this.autocomplete.closed.emit();\n                    }\n                }\n            });\n            return this.panelClosingActions;\n        }), \n        // when the first closing event occurs...\n        take(1))\n            // set the value, close the panel, and complete.\n            .subscribe(event => this._setValueAndClose(event)));\n    }\n    /** Destroys the autocomplete suggestion panel. */\n    _destroyPanel() {\n        if (this._overlayRef) {\n            this.closePanel();\n            this._overlayRef.dispose();\n            this._overlayRef = null;\n        }\n    }\n    _assignOptionValue(value) {\n        const toDisplay = this.autocomplete && this.autocomplete.displayWith\n            ? this.autocomplete.displayWith(value)\n            : value;\n        // Simply falling back to an empty string if the display value is falsy does not work properly.\n        // The display value can also be the number zero and shouldn't fall back to an empty string.\n        this._updateNativeInputValue(toDisplay != null ? toDisplay : '');\n    }\n    _updateNativeInputValue(value) {\n        // If it's used within a `MatFormField`, we should set it through the property so it can go\n        // through change detection.\n        if (this._formField) {\n            this._formField._control.value = value;\n        }\n        else {\n            this._element.nativeElement.value = value;\n        }\n        this._previousValue = value;\n    }\n    /**\n     * This method closes the panel, and if a value is specified, also sets the associated\n     * control to that value. It will also mark the control as dirty if this interaction\n     * stemmed from the user.\n     */\n    _setValueAndClose(event) {\n        const toSelect = event ? event.source : this._pendingAutoselectedOption;\n        if (toSelect) {\n            this._clearPreviousSelectedOption(toSelect);\n            this._assignOptionValue(toSelect.value);\n            this._onChange(toSelect.value);\n            this.autocomplete._emitSelectEvent(toSelect);\n            this._element.nativeElement.focus();\n        }\n        this.closePanel();\n    }\n    /**\n     * Clear any previous selected option and emit a selection change event for this option\n     */\n    _clearPreviousSelectedOption(skip) {\n        this.autocomplete.options.forEach(option => {\n            if (option !== skip && option.selected) {\n                option.deselect();\n            }\n        });\n    }\n    _attachOverlay() {\n        if (!this.autocomplete && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatAutocompleteMissingPanelError();\n        }\n        let overlayRef = this._overlayRef;\n        if (!overlayRef) {\n            this._portal = new TemplatePortal(this.autocomplete.template, this._viewContainerRef, {\n                id: this._formField?.getLabelId(),\n            });\n            overlayRef = this._overlay.create(this._getOverlayConfig());\n            this._overlayRef = overlayRef;\n            this._handleOverlayEvents(overlayRef);\n            this._viewportSubscription = this._viewportRuler.change().subscribe(() => {\n                if (this.panelOpen && overlayRef) {\n                    overlayRef.updateSize({ width: this._getPanelWidth() });\n                }\n            });\n        }\n        else {\n            // Update the trigger, panel width and direction, in case anything has changed.\n            this._positionStrategy.setOrigin(this._getConnectedElement());\n            overlayRef.updateSize({ width: this._getPanelWidth() });\n        }\n        if (overlayRef && !overlayRef.hasAttached()) {\n            overlayRef.attach(this._portal);\n            this._closingActionsSubscription = this._subscribeToClosingActions();\n        }\n        const wasOpen = this.panelOpen;\n        this.autocomplete._setVisibility();\n        this.autocomplete._isOpen = this._overlayAttached = true;\n        // We need to do an extra `panelOpen` check in here, because the\n        // autocomplete won't be shown if there are no options.\n        if (this.panelOpen && wasOpen !== this.panelOpen) {\n            this.autocomplete.opened.emit();\n        }\n    }\n    _getOverlayConfig() {\n        return new OverlayConfig({\n            positionStrategy: this._getOverlayPosition(),\n            scrollStrategy: this._scrollStrategy(),\n            width: this._getPanelWidth(),\n            direction: this._dir,\n            panelClass: this._defaults?.overlayPanelClass,\n        });\n    }\n    _getOverlayPosition() {\n        const strategy = this._overlay\n            .position()\n            .flexibleConnectedTo(this._getConnectedElement())\n            .withFlexibleDimensions(false)\n            .withPush(false);\n        this._setStrategyPositions(strategy);\n        this._positionStrategy = strategy;\n        return strategy;\n    }\n    /** Sets the positions on a position strategy based on the directive's input state. */\n    _setStrategyPositions(positionStrategy) {\n        // Note that we provide horizontal fallback positions, even though by default the dropdown\n        // width matches the input, because consumers can override the width. See #18854.\n        const belowPositions = [\n            { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top' },\n            { originX: 'end', originY: 'bottom', overlayX: 'end', overlayY: 'top' },\n        ];\n        // The overlay edge connected to the trigger should have squared corners, while\n        // the opposite end has rounded corners. We apply a CSS class to swap the\n        // border-radius based on the overlay position.\n        const panelClass = this._aboveClass;\n        const abovePositions = [\n            { originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom', panelClass },\n            { originX: 'end', originY: 'top', overlayX: 'end', overlayY: 'bottom', panelClass },\n        ];\n        let positions;\n        if (this.position === 'above') {\n            positions = abovePositions;\n        }\n        else if (this.position === 'below') {\n            positions = belowPositions;\n        }\n        else {\n            positions = [...belowPositions, ...abovePositions];\n        }\n        positionStrategy.withPositions(positions);\n    }\n    _getConnectedElement() {\n        if (this.connectedTo) {\n            return this.connectedTo.elementRef;\n        }\n        return this._formField ? this._formField.getConnectedOverlayOrigin() : this._element;\n    }\n    _getPanelWidth() {\n        return this.autocomplete.panelWidth || this._getHostWidth();\n    }\n    /** Returns the width of the input element, so the panel width can match it. */\n    _getHostWidth() {\n        return this._getConnectedElement().nativeElement.getBoundingClientRect().width;\n    }\n    /**\n     * Resets the active item to -1 so arrow events will activate the\n     * correct options, or to 0 if the consumer opted into it.\n     */\n    _resetActiveItem() {\n        const autocomplete = this.autocomplete;\n        if (autocomplete.autoActiveFirstOption) {\n            // Note that we go through `setFirstItemActive`, rather than `setActiveItem(0)`, because\n            // the former will find the next enabled option, if the first one is disabled.\n            autocomplete._keyManager.setFirstItemActive();\n        }\n        else {\n            autocomplete._keyManager.setActiveItem(-1);\n        }\n    }\n    /** Determines whether the panel can be opened. */\n    _canOpen() {\n        const element = this._element.nativeElement;\n        return !element.readOnly && !element.disabled && !this._autocompleteDisabled;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        return this._document?.defaultView || window;\n    }\n    /** Scrolls to a particular option in the list. */\n    _scrollToOption(index) {\n        // Given that we are not actually focusing active options, we must manually adjust scroll\n        // to reveal options below the fold. First, we find the offset of the option from the top\n        // of the panel. If that offset is below the fold, the new scrollTop will be the offset -\n        // the panel height + the option height, so the active option will be just visible at the\n        // bottom of the panel. If that offset is above the top of the visible panel, the new scrollTop\n        // will become the offset. If that offset is visible within the panel already, the scrollTop is\n        // not adjusted.\n        const autocomplete = this.autocomplete;\n        const labelCount = _countGroupLabelsBeforeOption(index, autocomplete.options, autocomplete.optionGroups);\n        if (index === 0 && labelCount === 1) {\n            // If we've got one group label before the option and we're at the top option,\n            // scroll the list to the top. This is better UX than scrolling the list to the\n            // top of the option, because it allows the user to read the top group's label.\n            autocomplete._setScrollTop(0);\n        }\n        else if (autocomplete.panel) {\n            const option = autocomplete.options.toArray()[index];\n            if (option) {\n                const element = option._getHostElement();\n                const newScrollPosition = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, autocomplete._getScrollTop(), autocomplete.panel.nativeElement.offsetHeight);\n                autocomplete._setScrollTop(newScrollPosition);\n            }\n        }\n    }\n    /** Handles keyboard events coming from the overlay panel. */\n    _handleOverlayEvents(overlayRef) {\n        // Use the `keydownEvents` in order to take advantage of\n        // the overlay event targeting provided by the CDK overlay.\n        overlayRef.keydownEvents().subscribe(event => {\n            // Close when pressing ESCAPE or ALT + UP_ARROW, based on the a11y guidelines.\n            // See: https://www.w3.org/TR/wai-aria-practices-1.1/#textbox-keyboard-interaction\n            if ((event.keyCode === ESCAPE && !hasModifierKey(event)) ||\n                (event.keyCode === UP_ARROW && hasModifierKey(event, 'altKey'))) {\n                // If the user had typed something in before we autoselected an option, and they decided\n                // to cancel the selection, restore the input value to the one they had typed in.\n                if (this._pendingAutoselectedOption) {\n                    this._updateNativeInputValue(this._valueBeforeAutoSelection ?? '');\n                    this._pendingAutoselectedOption = null;\n                }\n                this._closeKeyEventStream.next();\n                this._resetActiveItem();\n                // We need to stop propagation, otherwise the event will eventually\n                // reach the input itself and cause the overlay to be reopened.\n                event.stopPropagation();\n                event.preventDefault();\n            }\n        });\n        // Subscribe to the pointer events stream so that it doesn't get picked up by other overlays.\n        // TODO(crisbeto): we should switch `_getOutsideClickStream` eventually to use this stream,\n        // but the behvior isn't exactly the same and it ends up breaking some internal tests.\n        overlayRef.outsidePointerEvents().subscribe();\n    }\n}\n_MatAutocompleteTriggerBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatAutocompleteTriggerBase, deps: [{ token: i0.ElementRef }, { token: i1$1.Overlay }, { token: i0.ViewContainerRef }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: MAT_AUTOCOMPLETE_SCROLL_STRATEGY }, { token: i2$1.Directionality, optional: true }, { token: MAT_FORM_FIELD, host: true, optional: true }, { token: DOCUMENT, optional: true }, { token: i3.ViewportRuler }, { token: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n_MatAutocompleteTriggerBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: _MatAutocompleteTriggerBase, inputs: { autocomplete: [\"matAutocomplete\", \"autocomplete\"], position: [\"matAutocompletePosition\", \"position\"], connectedTo: [\"matAutocompleteConnectedTo\", \"connectedTo\"], autocompleteAttribute: [\"autocomplete\", \"autocompleteAttribute\"], autocompleteDisabled: [\"matAutocompleteDisabled\", \"autocompleteDisabled\"] }, usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatAutocompleteTriggerBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1$1.Overlay }, { type: i0.ViewContainerRef }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY]\n                }] }, { type: i2$1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i4.MatFormField, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_FORM_FIELD]\n                }, {\n                    type: Host\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i3.ViewportRuler }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_AUTOCOMPLETE_DEFAULT_OPTIONS]\n                }] }]; }, propDecorators: { autocomplete: [{\n                type: Input,\n                args: ['matAutocomplete']\n            }], position: [{\n                type: Input,\n                args: ['matAutocompletePosition']\n            }], connectedTo: [{\n                type: Input,\n                args: ['matAutocompleteConnectedTo']\n            }], autocompleteAttribute: [{\n                type: Input,\n                args: ['autocomplete']\n            }], autocompleteDisabled: [{\n                type: Input,\n                args: ['matAutocompleteDisabled']\n            }] } });\nclass MatAutocompleteTrigger extends _MatAutocompleteTriggerBase {\n    constructor() {\n        super(...arguments);\n        this._aboveClass = 'mat-autocomplete-panel-above';\n    }\n}\nMatAutocompleteTrigger.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatAutocompleteTrigger, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatAutocompleteTrigger.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatAutocompleteTrigger, selector: \"input[matAutocomplete], textarea[matAutocomplete]\", host: { listeners: { \"focusin\": \"_handleFocus()\", \"blur\": \"_onTouched()\", \"input\": \"_handleInput($event)\", \"keydown\": \"_handleKeydown($event)\", \"click\": \"_handleClick()\" }, properties: { \"attr.autocomplete\": \"autocompleteAttribute\", \"attr.role\": \"autocompleteDisabled ? null : \\\"combobox\\\"\", \"attr.aria-autocomplete\": \"autocompleteDisabled ? null : \\\"list\\\"\", \"attr.aria-activedescendant\": \"(panelOpen && activeOption) ? activeOption.id : null\", \"attr.aria-expanded\": \"autocompleteDisabled ? null : panelOpen.toString()\", \"attr.aria-owns\": \"(autocompleteDisabled || !panelOpen) ? null : autocomplete?.id\", \"attr.aria-haspopup\": \"autocompleteDisabled ? null : \\\"listbox\\\"\" }, classAttribute: \"mat-autocomplete-trigger\" }, providers: [MAT_AUTOCOMPLETE_VALUE_ACCESSOR], exportAs: [\"matAutocompleteTrigger\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatAutocompleteTrigger, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `input[matAutocomplete], textarea[matAutocomplete]`,\n                    host: {\n                        'class': 'mat-autocomplete-trigger',\n                        '[attr.autocomplete]': 'autocompleteAttribute',\n                        '[attr.role]': 'autocompleteDisabled ? null : \"combobox\"',\n                        '[attr.aria-autocomplete]': 'autocompleteDisabled ? null : \"list\"',\n                        '[attr.aria-activedescendant]': '(panelOpen && activeOption) ? activeOption.id : null',\n                        '[attr.aria-expanded]': 'autocompleteDisabled ? null : panelOpen.toString()',\n                        '[attr.aria-owns]': '(autocompleteDisabled || !panelOpen) ? null : autocomplete?.id',\n                        '[attr.aria-haspopup]': 'autocompleteDisabled ? null : \"listbox\"',\n                        // Note: we use `focusin`, as opposed to `focus`, in order to open the panel\n                        // a little earlier. This avoids issues where IE delays the focusing of the input.\n                        '(focusin)': '_handleFocus()',\n                        '(blur)': '_onTouched()',\n                        '(input)': '_handleInput($event)',\n                        '(keydown)': '_handleKeydown($event)',\n                        '(click)': '_handleClick()',\n                    },\n                    exportAs: 'matAutocompleteTrigger',\n                    providers: [MAT_AUTOCOMPLETE_VALUE_ACCESSOR],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatAutocompleteModule {\n}\nMatAutocompleteModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatAutocompleteModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatAutocompleteModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: MatAutocompleteModule, declarations: [MatAutocomplete, MatAutocompleteTrigger, MatAutocompleteOrigin], imports: [OverlayModule, MatOptionModule, MatCommonModule, CommonModule], exports: [MatAutocomplete,\n        MatAutocompleteTrigger,\n        MatAutocompleteOrigin,\n        CdkScrollableModule,\n        MatOptionModule,\n        MatCommonModule] });\nMatAutocompleteModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatAutocompleteModule, providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER], imports: [OverlayModule, MatOptionModule, MatCommonModule, CommonModule, CdkScrollableModule,\n        MatOptionModule,\n        MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatAutocompleteModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [OverlayModule, MatOptionModule, MatCommonModule, CommonModule],\n                    exports: [\n                        MatAutocomplete,\n                        MatAutocompleteTrigger,\n                        MatAutocompleteOrigin,\n                        CdkScrollableModule,\n                        MatOptionModule,\n                        MatCommonModule,\n                    ],\n                    declarations: [MatAutocomplete, MatAutocompleteTrigger, MatAutocompleteOrigin],\n                    providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER, MAT_AUTOCOMPLETE_VALUE_ACCESSOR, MatAutocomplete, MatAutocompleteModule, MatAutocompleteOrigin, MatAutocompleteSelectedEvent, MatAutocompleteTrigger, _MatAutocompleteBase, _MatAutocompleteOriginBase, _MatAutocompleteTriggerBase, getMatAutocompleteMissingPanelError };\n"], "mappings": "AAAA,SAASA,0BAAT,QAA2C,mBAA3C;AACA,SAASC,qBAAT,EAAgCC,iBAAhC,QAAyD,uBAAzD;AACA,OAAO,KAAKC,EAAZ,MAAoB,uBAApB;AACA,SAASC,eAAT,QAAgC,uBAAhC;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,YAAzB,EAAuCC,WAAvC,EAAoDC,SAApD,EAA+DC,MAA/D,EAAuEC,SAAvE,EAAkFC,KAAlF,EAAyFC,MAAzF,EAAiGC,SAAjG,EAA4GC,iBAA5G,EAA+HC,uBAA/H,EAAwJC,eAAxJ,EAAyKC,UAAzK,EAAqLC,QAArL,EAA+LC,IAA/L,EAAqMC,QAArM,QAAqN,eAArN;AACA,SAASC,kBAAT,EAA6BC,2BAA7B,EAA0DC,YAA1D,EAAwEC,SAAxE,EAAmFC,wBAAnF,EAA6GC,6BAA7G,EAA4IC,wBAA5I,EAAsKC,eAAtK,EAAuLC,eAAvL,QAA8M,wBAA9M;AACA,SAASC,YAAT,EAAuBC,OAAvB,EAAgCC,KAAhC,EAAuCC,KAAvC,EAA8CC,EAA9C,EAAkDC,SAAlD,QAAmE,MAAnE;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,QAAT,EAAmBC,YAAnB,QAAuC,iBAAvC;AACA,OAAO,KAAKC,IAAZ,MAAsB,sBAAtB;AACA,SAASC,OAAT,EAAkBC,aAAlB,EAAiCC,aAAjC,QAAsD,sBAAtD;AACA,OAAO,KAAKC,EAAZ,MAAoB,wBAApB;AACA,SAASC,mBAAT,QAAoC,wBAApC;AACA,OAAO,KAAKC,IAAZ,MAAsB,mBAAtB;AACA,SAASC,cAAT,EAAyBC,MAAzB,EAAiCC,KAAjC,EAAwCC,QAAxC,EAAkDC,UAAlD,EAA8DC,GAA9D,QAAyE,uBAAzE;AACA,SAASC,cAAT,QAA+B,qBAA/B;AACA,SAASC,iBAAT,QAAkC,gBAAlC;AACA,OAAO,KAAKC,EAAZ,MAAoB,8BAApB;AACA,SAASC,cAAT,QAA+B,8BAA/B;AACA,SAASC,SAAT,EAAoBC,SAApB,EAA+BC,IAA/B,EAAqCC,MAArC,EAA6CC,GAA7C,EAAkDC,GAAlD,EAAuDC,KAAvD,QAAoE,gBAApE;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;;;;IAkJuG1D,EA+C6e,+B;IA/C7eA,EA+C8uB,gB;IA/C9uBA,EA+C2wB,e;;;;;mBA/C3wBA,E;IAAAA,EA+CojB,0D;IA/CpjBA,EA+CwkB,uH;;;;;AAhM/qB,IAAI2D,4BAA4B,GAAG,CAAnC;AACA;;AACA,MAAMC,4BAAN,CAAmC;EAC/BC,WAAW;EACX;EACAC,MAFW;EAGX;EACAC,MAJW,EAIH;IACJ,KAAKD,MAAL,GAAcA,MAAd;IACA,KAAKC,MAAL,GAAcA,MAAd;EACH;;AAR8B,C,CAUnC;;AACA;;;AACA,MAAMC,yBAAyB,GAAG/C,kBAAkB,CAAC,MAAM,EAAP,CAApD;AAEA;;;AACA,MAAMgD,gCAAgC,GAAG,IAAIhE,cAAJ,CAAmB,kCAAnB,EAAuD;EAC5FiE,UAAU,EAAE,MADgF;EAE5FC,OAAO,EAAEC;AAFmF,CAAvD,CAAzC;AAIA;;AACA,SAASA,wCAAT,GAAoD;EAChD,OAAO;IAAEC,qBAAqB,EAAE,KAAzB;IAAgCC,sBAAsB,EAAE;EAAxD,CAAP;AACH;AACD;;;AACA,MAAMC,oBAAN,SAAmCP,yBAAnC,CAA6D;EACzDH,WAAW,CAACW,kBAAD,EAAqBC,WAArB,EAAkCC,QAAlC,EAA4CC,QAA5C,EAAsD;IAC7D;IACA,KAAKH,kBAAL,GAA0BA,kBAA1B;IACA,KAAKC,WAAL,GAAmBA,WAAnB;IACA,KAAKG,oBAAL,GAA4BlD,YAAY,CAACmD,KAAzC;IACA;;IACA,KAAKC,SAAL,GAAiB,KAAjB;IACA,KAAKC,OAAL,GAAe,KAAf;IACA;;IACA,KAAKC,WAAL,GAAmB,IAAnB;IACA;;IACA,KAAKC,cAAL,GAAsB,IAAI/E,YAAJ,EAAtB;IACA;;IACA,KAAKgF,MAAL,GAAc,IAAIhF,YAAJ,EAAd;IACA;;IACA,KAAKiF,MAAL,GAAc,IAAIjF,YAAJ,EAAd;IACA;;IACA,KAAKkF,eAAL,GAAuB,IAAIlF,YAAJ,EAAvB;IACA,KAAKmF,UAAL,GAAkB,EAAlB;IACA;;IACA,KAAKC,EAAL,GAAW,oBAAmB3B,4BAA4B,EAAG,EAA7D,CApB6D,CAqB7D;IACA;IACA;IACA;;IACA,KAAK4B,WAAL,GAAmBZ,QAAQ,EAAEa,MAAV,IAAoB,KAAvC;IACA,KAAKC,sBAAL,GAA8B,CAAC,CAACf,QAAQ,CAACL,qBAAzC;IACA,KAAKqB,uBAAL,GAA+B,CAAC,CAAChB,QAAQ,CAACJ,sBAA1C;EACH;EACD;;;EACU,IAANqB,MAAM,GAAG;IACT,OAAO,KAAKZ,OAAL,IAAgB,KAAKD,SAA5B;EACH;EACD;AACJ;AACA;AACA;;;EAC6B,IAArBT,qBAAqB,GAAG;IACxB,OAAO,KAAKoB,sBAAZ;EACH;;EACwB,IAArBpB,qBAAqB,CAACuB,KAAD,EAAQ;IAC7B,KAAKH,sBAAL,GAA8B7F,qBAAqB,CAACgG,KAAD,CAAnD;EACH;EACD;;;EAC0B,IAAtBtB,sBAAsB,GAAG;IACzB,OAAO,KAAKoB,uBAAZ;EACH;;EACyB,IAAtBpB,sBAAsB,CAACsB,KAAD,EAAQ;IAC9B,KAAKF,uBAAL,GAA+B9F,qBAAqB,CAACgG,KAAD,CAApD;EACH;EACD;AACJ;AACA;AACA;;;EACiB,IAATC,SAAS,CAACD,KAAD,EAAQ;IACjB,IAAIA,KAAK,IAAIA,KAAK,CAACE,MAAnB,EAA2B;MACvB,KAAKT,UAAL,GAAkBxF,iBAAiB,CAAC+F,KAAD,CAAjB,CAAyBG,MAAzB,CAAgC,CAACF,SAAD,EAAYG,SAAZ,KAA0B;QACxEH,SAAS,CAACG,SAAD,CAAT,GAAuB,IAAvB;QACA,OAAOH,SAAP;MACH,CAHiB,EAGf,EAHe,CAAlB;IAIH,CALD,MAMK;MACD,KAAKR,UAAL,GAAkB,EAAlB;IACH;;IACD,KAAKY,qBAAL,CAA2B,KAAKZ,UAAhC;;IACA,KAAKZ,WAAL,CAAiByB,aAAjB,CAA+BF,SAA/B,GAA2C,EAA3C;EACH;;EACDG,kBAAkB,GAAG;IACjB,KAAKC,WAAL,GAAmB,IAAIzG,0BAAJ,CAA+B,KAAK0G,OAApC,EAA6CC,QAA7C,EAAnB;IACA,KAAK1B,oBAAL,GAA4B,KAAKwB,WAAL,CAAiBG,MAAjB,CAAwBC,SAAxB,CAAkCC,KAAK,IAAI;MACnE,IAAI,KAAKd,MAAT,EAAiB;QACb,KAAKP,eAAL,CAAqBsB,IAArB,CAA0B;UAAE5C,MAAM,EAAE,IAAV;UAAgBC,MAAM,EAAE,KAAKsC,OAAL,CAAaM,OAAb,GAAuBF,KAAvB,KAAiC;QAAzD,CAA1B;MACH;IACJ,CAJ2B,CAA5B,CAFiB,CAOjB;;IACA,KAAKG,cAAL;EACH;;EACDC,WAAW,GAAG;IACV,KAAKjC,oBAAL,CAA0BkC,WAA1B;EACH;EACD;AACJ;AACA;AACA;;;EACIC,aAAa,CAACC,SAAD,EAAY;IACrB,IAAI,KAAKC,KAAT,EAAgB;MACZ,KAAKA,KAAL,CAAWf,aAAX,CAAyBc,SAAzB,GAAqCA,SAArC;IACH;EACJ;EACD;;;EACAE,aAAa,GAAG;IACZ,OAAO,KAAKD,KAAL,GAAa,KAAKA,KAAL,CAAWf,aAAX,CAAyBc,SAAtC,GAAkD,CAAzD;EACH;EACD;;;EACAJ,cAAc,GAAG;IACb,KAAK9B,SAAL,GAAiB,CAAC,CAAC,KAAKuB,OAAL,CAAaP,MAAhC;;IACA,KAAKG,qBAAL,CAA2B,KAAKZ,UAAhC;;IACA,KAAKb,kBAAL,CAAwB2C,YAAxB;EACH;EACD;;;EACAC,gBAAgB,CAACrD,MAAD,EAAS;IACrB,MAAMsD,KAAK,GAAG,IAAIzD,4BAAJ,CAAiC,IAAjC,EAAuCG,MAAvC,CAAd;IACA,KAAKkB,cAAL,CAAoByB,IAApB,CAAyBW,KAAzB;EACH;EACD;;;EACAC,uBAAuB,CAACC,OAAD,EAAU;IAC7B,IAAI,KAAKC,SAAT,EAAoB;MAChB,OAAO,IAAP;IACH;;IACD,MAAMC,eAAe,GAAGF,OAAO,GAAGA,OAAO,GAAG,GAAb,GAAmB,EAAlD;IACA,OAAO,KAAKG,cAAL,GAAsBD,eAAe,GAAG,KAAKC,cAA7C,GAA8DH,OAArE;EACH;EACD;;;EACAtB,qBAAqB,CAACJ,SAAD,EAAY;IAC7BA,SAAS,CAAC,KAAK8B,aAAN,CAAT,GAAgC,KAAK7C,SAArC;IACAe,SAAS,CAAC,KAAK+B,YAAN,CAAT,GAA+B,CAAC,KAAK9C,SAArC;EACH;;AArHwD;;AAuH7DP,oBAAoB,CAACsD,IAArB;EAAA,iBAAiHtD,oBAAjH,EAAuGvE,EAAvG,mBAAuJA,EAAE,CAAC8H,iBAA1J,GAAuG9H,EAAvG,mBAAwLA,EAAE,CAAC+H,UAA3L,GAAuG/H,EAAvG,mBAAkNiE,gCAAlN,GAAuGjE,EAAvG,mBAA+PF,EAAE,CAACkI,QAAlQ;AAAA;;AACAzD,oBAAoB,CAAC0D,IAArB,kBADuGjI,EACvG;EAAA,MAAqGuE,oBAArG;EAAA;IAAA;MADuGvE,EACvG,aAA0lBG,WAA1lB;MADuGH,EACvG;IAAA;;IAAA;MAAA;;MADuGA,EACvG,qBADuGA,EACvG;MADuGA,EACvG,qBADuGA,EACvG;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WADuGA,EACvG;AAAA;;AACA;EAAA,mDAFuGA,EAEvG,mBAA2FuE,oBAA3F,EAA6H,CAAC;IAClH2D,IAAI,EAAE9H;EAD4G,CAAD,CAA7H,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAE8H,IAAI,EAAElI,EAAE,CAAC8H;IAAX,CAAD,EAAiC;MAAEI,IAAI,EAAElI,EAAE,CAAC+H;IAAX,CAAjC,EAA0D;MAAEG,IAAI,EAAEC,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACvHF,IAAI,EAAE7H,MADiH;QAEvHgI,IAAI,EAAE,CAACpE,gCAAD;MAFiH,CAAD;IAA/B,CAA1D,EAG3B;MAAEiE,IAAI,EAAEpI,EAAE,CAACkI;IAAX,CAH2B,CAAP;EAGK,CAL/C,EAKiE;IAAEM,QAAQ,EAAE,CAAC;MAC9DJ,IAAI,EAAE5H,SADwD;MAE9D+H,IAAI,EAAE,CAAClI,WAAD,EAAc;QAAEoI,MAAM,EAAE;MAAV,CAAd;IAFwD,CAAD,CAAZ;IAGjDtB,KAAK,EAAE,CAAC;MACRiB,IAAI,EAAE5H,SADE;MAER+H,IAAI,EAAE,CAAC,OAAD;IAFE,CAAD,CAH0C;IAMjDb,SAAS,EAAE,CAAC;MACZU,IAAI,EAAE3H,KADM;MAEZ8H,IAAI,EAAE,CAAC,YAAD;IAFM,CAAD,CANsC;IASjDX,cAAc,EAAE,CAAC;MACjBQ,IAAI,EAAE3H,KADW;MAEjB8H,IAAI,EAAE,CAAC,iBAAD;IAFW,CAAD,CATiC;IAYjDrD,WAAW,EAAE,CAAC;MACdkD,IAAI,EAAE3H;IADQ,CAAD,CAZoC;IAcjD8D,qBAAqB,EAAE,CAAC;MACxB6D,IAAI,EAAE3H;IADkB,CAAD,CAd0B;IAgBjD+D,sBAAsB,EAAE,CAAC;MACzB4D,IAAI,EAAE3H;IADmB,CAAD,CAhByB;IAkBjDiI,UAAU,EAAE,CAAC;MACbN,IAAI,EAAE3H;IADO,CAAD,CAlBqC;IAoBjD0E,cAAc,EAAE,CAAC;MACjBiD,IAAI,EAAE1H;IADW,CAAD,CApBiC;IAsBjD0E,MAAM,EAAE,CAAC;MACTgD,IAAI,EAAE1H;IADG,CAAD,CAtByC;IAwBjD2E,MAAM,EAAE,CAAC;MACT+C,IAAI,EAAE1H;IADG,CAAD,CAxByC;IA0BjD4E,eAAe,EAAE,CAAC;MAClB8C,IAAI,EAAE1H;IADY,CAAD,CA1BgC;IA4BjDqF,SAAS,EAAE,CAAC;MACZqC,IAAI,EAAE3H,KADM;MAEZ8H,IAAI,EAAE,CAAC,OAAD;IAFM,CAAD;EA5BsC,CALjE;AAAA;;AAqCA,MAAMI,eAAN,SAA8BlE,oBAA9B,CAAmD;EAC/CV,WAAW,GAAG;IACV,MAAM,GAAG6E,SAAT;IACA,KAAKf,aAAL,GAAqB,0BAArB;IACA,KAAKC,YAAL,GAAoB,yBAApB;EACH;;AAL8C;;AAOnDa,eAAe,CAACZ,IAAhB;EAAA;EAAA;IAAA,wEA9CuG7H,EA8CvG,uBAA4GyI,eAA5G,SAA4GA,eAA5G;EAAA;AAAA;;AACAA,eAAe,CAACE,IAAhB,kBA/CuG3I,EA+CvG;EAAA,MAAgGyI,eAAhG;EAAA;EAAA;IAAA;MA/CuGzI,EA+CvG,0BAAmXmB,YAAnX;MA/CuGnB,EA+CvG,0BAA4boB,SAA5b;IAAA;;IAAA;MAAA;;MA/CuGpB,EA+CvG,qBA/CuGA,EA+CvG;MA/CuGA,EA+CvG,qBA/CuGA,EA+CvG;IAAA;EAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA,WA/CuGA,EA+CvG,oBAAoP,CAAC;IAAE4I,OAAO,EAAE1H,2BAAX;IAAwC2H,WAAW,EAAEJ;EAArD,CAAD,CAApP,GA/CuGzI,EA+CvG;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA/CuGA,EA+CvG;MA/CuGA,EA+Cqc,2EAA5iB;IAAA;EAAA;EAAA,eAA6lDgC,EAAE,CAAC8G,OAAhmD;EAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDAhDuG9I,EAgDvG,mBAA2FyI,eAA3F,EAAwH,CAAC;IAC7GP,IAAI,EAAEzH,SADuG;IAE7G4H,IAAI,EAAE,CAAC;MAAEU,QAAQ,EAAE,kBAAZ;MAAgCC,aAAa,EAAEtI,iBAAiB,CAACuI,IAAjE;MAAuEC,eAAe,EAAEvI,uBAAuB,CAACwI,MAAhH;MAAwHC,QAAQ,EAAE,iBAAlI;MAAqJC,MAAM,EAAE,CAAC,eAAD,CAA7J;MAAgLC,IAAI,EAAE;QACjL,SAAS;MADwK,CAAtL;MAEIC,SAAS,EAAE,CAAC;QAAEX,OAAO,EAAE1H,2BAAX;QAAwC2H,WAAW,EAAEJ;MAArD,CAAD,CAFf;MAEyFH,QAAQ,EAAE,gWAFnG;MAEqckB,MAAM,EAAE,CAAC,0pBAAD;IAF7c,CAAD;EAFuG,CAAD,CAAxH,QAK4B;IAAEC,YAAY,EAAE,CAAC;MAC7BvB,IAAI,EAAEtH,eADuB;MAE7ByH,IAAI,EAAE,CAAClH,YAAD,EAAe;QAAEuI,WAAW,EAAE;MAAf,CAAf;IAFuB,CAAD,CAAhB;IAGZrD,OAAO,EAAE,CAAC;MACV6B,IAAI,EAAEtH,eADI;MAEVyH,IAAI,EAAE,CAACjH,SAAD,EAAY;QAAEsI,WAAW,EAAE;MAAf,CAAZ;IAFI,CAAD;EAHG,CAL5B;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMC,0BAAN,CAAiC;EAC7B9F,WAAW;EACX;EACA+F,UAFW,EAEC;IACR,KAAKA,UAAL,GAAkBA,UAAlB;EACH;;AAL4B;;AAOjCD,0BAA0B,CAAC9B,IAA3B;EAAA,iBAAuH8B,0BAAvH,EA5EuG3J,EA4EvG,mBAAmKA,EAAE,CAAC+H,UAAtK;AAAA;;AACA4B,0BAA0B,CAAC1B,IAA3B,kBA7EuGjI,EA6EvG;EAAA,MAA2G2J;AAA3G;;AACA;EAAA,mDA9EuG3J,EA8EvG,mBAA2F2J,0BAA3F,EAAmI,CAAC;IACxHzB,IAAI,EAAE9H;EADkH,CAAD,CAAnI,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAE8H,IAAI,EAAElI,EAAE,CAAC+H;IAAX,CAAD,CAAP;EAAmC,CAF7E;AAAA;AAGA;AACA;AACA;AACA;;;AACA,MAAM8B,qBAAN,SAAoCF,0BAApC,CAA+D;;AAE/DE,qBAAqB,CAAChC,IAAtB;EAAA;EAAA;IAAA,oFAvFuG7H,EAuFvG,uBAAkH6J,qBAAlH,SAAkHA,qBAAlH;EAAA;AAAA;;AACAA,qBAAqB,CAAC5B,IAAtB,kBAxFuGjI,EAwFvG;EAAA,MAAsG6J,qBAAtG;EAAA;EAAA;EAAA,WAxFuG7J,EAwFvG;AAAA;;AACA;EAAA,mDAzFuGA,EAyFvG,mBAA2F6J,qBAA3F,EAA8H,CAAC;IACnH3B,IAAI,EAAE9H,SAD6G;IAEnHiI,IAAI,EAAE,CAAC;MACCU,QAAQ,EAAE,yBADX;MAECK,QAAQ,EAAE;IAFX,CAAD;EAF6G,CAAD,CAA9H;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMU,gCAAgC,GAAG,IAAI7J,cAAJ,CAAmB,kCAAnB,CAAzC;AACA;;AACA,SAAS8J,wCAAT,CAAkDC,OAAlD,EAA2D;EACvD,OAAO,MAAMA,OAAO,CAACC,gBAAR,CAAyBC,UAAzB,EAAb;AACH;AACD;;;AACA,MAAMC,iDAAiD,GAAG;EACtDvB,OAAO,EAAEkB,gCAD6C;EAEtDM,IAAI,EAAE,CAAChI,OAAD,CAFgD;EAGtDiI,UAAU,EAAEN;AAH0C,CAA1D;AAKA;AACA;AACA;AACA;;AACA,MAAMO,+BAA+B,GAAG;EACpC1B,OAAO,EAAE3F,iBAD2B;EAEpC4F,WAAW,EAAEhI,UAAU,CAAC,MAAM0J,sBAAP,CAFa;EAGpCC,KAAK,EAAE;AAH6B,CAAxC;AAKA;AACA;AACA;AACA;;AACA,SAASC,mCAAT,GAA+C;EAC3C,OAAOC,KAAK,CAAC,qEACT,4EADS,GAET,iEAFQ,CAAZ;AAGH;AACD;;;AACA,MAAMC,2BAAN,CAAkC;EAC9B9G,WAAW,CAAC+G,QAAD,EAAWC,QAAX,EAAqBC,iBAArB,EAAwCC,KAAxC,EAA+CvG,kBAA/C,EAAmEwG,cAAnE,EAAmFC,IAAnF,EAAyFC,UAAzF,EAAqGC,SAArG,EAAgHC,cAAhH,EAAgIC,SAAhI,EAA2I;IAClJ,KAAKT,QAAL,GAAgBA,QAAhB;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,iBAAL,GAAyBA,iBAAzB;IACA,KAAKC,KAAL,GAAaA,KAAb;IACA,KAAKvG,kBAAL,GAA0BA,kBAA1B;IACA,KAAKyG,IAAL,GAAYA,IAAZ;IACA,KAAKC,UAAL,GAAkBA,UAAlB;IACA,KAAKC,SAAL,GAAiBA,SAAjB;IACA,KAAKC,cAAL,GAAsBA,cAAtB;IACA,KAAKC,SAAL,GAAiBA,SAAjB;IACA,KAAKC,mBAAL,GAA2B,KAA3B;IACA,KAAKC,qBAAL,GAA6B,KAA7B;IACA;;IACA,KAAKC,sBAAL,GAA8B,KAA9B;IACA;;IACA,KAAKC,qBAAL,GAA6B/J,YAAY,CAACmD,KAA1C;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAK6G,mBAAL,GAA2B,IAA3B;IACA;;IACA,KAAKC,oBAAL,GAA4B,IAAIhK,OAAJ,EAA5B;IACA;AACR;AACA;AACA;;IACQ,KAAKiK,kBAAL,GAA0B,MAAM;MAC5B;MACA;MACA;MACA,KAAKF,mBAAL,GACI,KAAKP,SAAL,CAAeU,aAAf,KAAiC,KAAKjB,QAAL,CAAc1E,aAA/C,IAAgE,KAAK4F,SADzE;IAEH,CAND;IAOA;;;IACA,KAAKC,SAAL,GAAiB,MAAM,CAAG,CAA1B;IACA;;;IACA,KAAKC,UAAL,GAAkB,MAAM,CAAG,CAA3B;IACA;AACR;AACA;AACA;AACA;AACA;AACA;;;IACQ,KAAKC,QAAL,GAAgB,MAAhB;IACA;AACR;AACA;AACA;;IACQ,KAAKC,qBAAL,GAA6B,KAA7B;IACA,KAAKC,gBAAL,GAAwB,KAAxB;IACA;;IACA,KAAKC,gBAAL,GAAwBxK,KAAK,CAAC,MAAM;MAChC,MAAMyE,OAAO,GAAG,KAAKgG,YAAL,GAAoB,KAAKA,YAAL,CAAkBhG,OAAtC,GAAgD,IAAhE;;MACA,IAAIA,OAAJ,EAAa;QACT,OAAOA,OAAO,CAACiG,OAAR,CAAgBC,IAAhB,CAAqBnJ,SAAS,CAACiD,OAAD,CAA9B,EAAyChD,SAAS,CAAC,MAAMxB,KAAK,CAAC,GAAGwE,OAAO,CAAC7C,GAAR,CAAYO,MAAM,IAAIA,MAAM,CAACyI,iBAA7B,CAAJ,CAAZ,CAAlD,CAAP;MACH,CAJ+B,CAKhC;MACA;;;MACA,OAAO,KAAKzB,KAAL,CAAW0B,QAAX,CAAoBF,IAApB,CAAyBjJ,IAAI,CAAC,CAAD,CAA7B,EAAkCD,SAAS,CAAC,MAAM,KAAK+I,gBAAZ,CAA3C,CAAP;IACH,CAR4B,CAA7B;IASA,KAAKM,eAAL,GAAuB1B,cAAvB;EACH;EACD;AACJ;AACA;AACA;;;EAC4B,IAApB2B,oBAAoB,GAAG;IACvB,OAAO,KAAKpB,qBAAZ;EACH;;EACuB,IAApBoB,oBAAoB,CAAC/G,KAAD,EAAQ;IAC5B,KAAK2F,qBAAL,GAA6B3L,qBAAqB,CAACgG,KAAD,CAAlD;EACH;;EACDgH,eAAe,GAAG;IACd,MAAMC,MAAM,GAAG,KAAKC,UAAL,EAAf;;IACA,IAAI,OAAOD,MAAP,KAAkB,WAAtB,EAAmC;MAC/B,KAAK9B,KAAL,CAAWgC,iBAAX,CAA6B,MAAMF,MAAM,CAACG,gBAAP,CAAwB,MAAxB,EAAgC,KAAKpB,kBAArC,CAAnC;IACH;EACJ;;EACDqB,WAAW,CAACX,OAAD,EAAU;IACjB,IAAIA,OAAO,CAAC,UAAD,CAAP,IAAuB,KAAKY,iBAAhC,EAAmD;MAC/C,KAAKC,qBAAL,CAA2B,KAAKD,iBAAhC;;MACA,IAAI,KAAKpB,SAAT,EAAoB;QAChB,KAAKsB,WAAL,CAAiBC,cAAjB;MACH;IACJ;EACJ;;EACDxG,WAAW,GAAG;IACV,MAAMgG,MAAM,GAAG,KAAKC,UAAL,EAAf;;IACA,IAAI,OAAOD,MAAP,KAAkB,WAAtB,EAAmC;MAC/BA,MAAM,CAACS,mBAAP,CAA2B,MAA3B,EAAmC,KAAK1B,kBAAxC;IACH;;IACD,KAAKH,qBAAL,CAA2B3E,WAA3B;;IACA,KAAKwE,mBAAL,GAA2B,IAA3B;;IACA,KAAKiC,aAAL;;IACA,KAAK5B,oBAAL,CAA0B6B,QAA1B;EACH;EACD;;;EACa,IAAT1B,SAAS,GAAG;IACZ,OAAO,KAAKK,gBAAL,IAAyB,KAAKE,YAAL,CAAkBvH,SAAlD;EACH;EACD;;;EACA2I,SAAS,GAAG;IACR,KAAKC,cAAL;;IACA,KAAKC,WAAL;EACH;EACD;;;EACAC,UAAU,GAAG;IACT,KAAKC,WAAL;;IACA,IAAI,CAAC,KAAK1B,gBAAV,EAA4B;MACxB;IACH;;IACD,IAAI,KAAKL,SAAT,EAAoB;MAChB;MACA;MACA;MACA;MACA,KAAKf,KAAL,CAAW+C,GAAX,CAAe,MAAM;QACjB,KAAKzB,YAAL,CAAkBlH,MAAlB,CAAyBuB,IAAzB;MACH,CAFD;IAGH;;IACD,KAAK2F,YAAL,CAAkBtH,OAAlB,GAA4B,KAAKoH,gBAAL,GAAwB,KAApD;IACA,KAAK4B,0BAAL,GAAkC,IAAlC;;IACA,IAAI,KAAKX,WAAL,IAAoB,KAAKA,WAAL,CAAiBY,WAAjB,EAAxB,EAAwD;MACpD,KAAKZ,WAAL,CAAiBa,MAAjB;;MACA,KAAKC,2BAAL,CAAiCpH,WAAjC;IACH,CAnBQ,CAoBT;IACA;;;IACA,IAAI,CAAC,KAAKwE,mBAAV,EAA+B;MAC3B;MACA;MACA;MACA;MACA,KAAK9G,kBAAL,CAAwB2J,aAAxB;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACId,cAAc,GAAG;IACb,IAAI,KAAKlB,gBAAT,EAA2B;MACvB,KAAKiB,WAAL,CAAiBC,cAAjB;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EAC2B,IAAnBe,mBAAmB,GAAG;IACtB,OAAOvM,KAAK,CAAC,KAAKuK,gBAAN,EAAwB,KAAKC,YAAL,CAAkBjG,WAAlB,CAA8BiI,MAA9B,CAAqC9B,IAArC,CAA0ChJ,MAAM,CAAC,MAAM,KAAK4I,gBAAZ,CAAhD,CAAxB,EAAwG,KAAKR,oBAA7G,EAAmI,KAAK2C,sBAAL,EAAnI,EAAkK,KAAKlB,WAAL,GACxK,KAAKA,WAAL,CAAiBmB,WAAjB,GAA+BhC,IAA/B,CAAoChJ,MAAM,CAAC,MAAM,KAAK4I,gBAAZ,CAA1C,CADwK,GAExKrK,EAAE,EAFI,CAAL,CAEKyK,IAFL,EAGP;IACA/I,GAAG,CAAC6D,KAAK,IAAKA,KAAK,YAAYhG,wBAAjB,GAA4CgG,KAA5C,GAAoD,IAA/D,CAJI,CAAP;EAKH;EACD;;;EACgB,IAAZmH,YAAY,GAAG;IACf,IAAI,KAAKnC,YAAL,IAAqB,KAAKA,YAAL,CAAkBjG,WAA3C,EAAwD;MACpD,OAAO,KAAKiG,YAAL,CAAkBjG,WAAlB,CAA8BqI,UAArC;IACH;;IACD,OAAO,IAAP;EACH;EACD;;;EACAH,sBAAsB,GAAG;IACrB,OAAOzM,KAAK,CAACE,SAAS,CAAC,KAAKoJ,SAAN,EAAiB,OAAjB,CAAV,EAAqCpJ,SAAS,CAAC,KAAKoJ,SAAN,EAAiB,UAAjB,CAA9C,EAA4EpJ,SAAS,CAAC,KAAKoJ,SAAN,EAAiB,UAAjB,CAArF,CAAL,CAAwHoB,IAAxH,CAA6HhJ,MAAM,CAAC8D,KAAK,IAAI;MAChJ;MACA;MACA,MAAMqH,WAAW,GAAG3O,eAAe,CAACsH,KAAD,CAAnC;;MACA,MAAMsH,SAAS,GAAG,KAAKzD,UAAL,GAAkB,KAAKA,UAAL,CAAgBzG,WAAhB,CAA4ByB,aAA9C,GAA8D,IAAhF;MACA,MAAM0I,YAAY,GAAG,KAAKC,WAAL,GAAmB,KAAKA,WAAL,CAAiBjF,UAAjB,CAA4B1D,aAA/C,GAA+D,IAApF;MACA,OAAQ,KAAKiG,gBAAL,IACJuC,WAAW,KAAK,KAAK9D,QAAL,CAAc1E,aAD1B,IAEJ;MACA;MACA;MACA;MACA,KAAKiF,SAAL,CAAeU,aAAf,KAAiC,KAAKjB,QAAL,CAAc1E,aAN3C,KAOH,CAACyI,SAAD,IAAc,CAACA,SAAS,CAACG,QAAV,CAAmBJ,WAAnB,CAPZ,MAQH,CAACE,YAAD,IAAiB,CAACA,YAAY,CAACE,QAAb,CAAsBJ,WAAtB,CARf,KASJ,CAAC,CAAC,KAAKtB,WATH,IAUJ,CAAC,KAAKA,WAAL,CAAiB2B,cAAjB,CAAgCD,QAAhC,CAAyCJ,WAAzC,CAVL;IAWH,CAjByI,CAAnI,CAAP;EAkBH,CA5L6B,CA6L9B;;;EACAM,UAAU,CAACpJ,KAAD,EAAQ;IACdqJ,OAAO,CAACC,OAAR,CAAgB,IAAhB,EAAsBC,IAAtB,CAA2B,MAAM,KAAKC,kBAAL,CAAwBxJ,KAAxB,CAAjC;EACH,CAhM6B,CAiM9B;;;EACAyJ,gBAAgB,CAACC,EAAD,EAAK;IACjB,KAAKvD,SAAL,GAAiBuD,EAAjB;EACH,CApM6B,CAqM9B;;;EACAC,iBAAiB,CAACD,EAAD,EAAK;IAClB,KAAKtD,UAAL,GAAkBsD,EAAlB;EACH,CAxM6B,CAyM9B;;;EACAE,gBAAgB,CAACC,UAAD,EAAa;IACzB,KAAK7E,QAAL,CAAc1E,aAAd,CAA4BwJ,QAA5B,GAAuCD,UAAvC;EACH;;EACDE,cAAc,CAACtI,KAAD,EAAQ;IAClB,MAAMuI,OAAO,GAAGvI,KAAK,CAACuI,OAAtB;IACA,MAAMC,WAAW,GAAGnN,cAAc,CAAC2E,KAAD,CAAlC,CAFkB,CAGlB;IACA;IACA;IACA;;IACA,IAAIuI,OAAO,KAAKjN,MAAZ,IAAsB,CAACkN,WAA3B,EAAwC;MACpCxI,KAAK,CAACyI,cAAN;IACH;;IACD,IAAI,KAAKtB,YAAL,IAAqBoB,OAAO,KAAKhN,KAAjC,IAA0C,KAAKkJ,SAA/C,IAA4D,CAAC+D,WAAjE,EAA8E;MAC1E,KAAKrB,YAAL,CAAkBuB,qBAAlB;;MACA,KAAKC,gBAAL;;MACA3I,KAAK,CAACyI,cAAN;IACH,CAJD,MAKK,IAAI,KAAKzD,YAAT,EAAuB;MACxB,MAAM4D,cAAc,GAAG,KAAK5D,YAAL,CAAkBjG,WAAlB,CAA8BqI,UAArD;MACA,MAAMyB,UAAU,GAAGN,OAAO,KAAK/M,QAAZ,IAAwB+M,OAAO,KAAK9M,UAAvD;;MACA,IAAI8M,OAAO,KAAK7M,GAAZ,IAAoBmN,UAAU,IAAI,CAACL,WAAf,IAA8B,KAAK/D,SAA3D,EAAuE;QACnE,KAAKO,YAAL,CAAkBjG,WAAlB,CAA8B+J,SAA9B,CAAwC9I,KAAxC;MACH,CAFD,MAGK,IAAI6I,UAAU,IAAI,KAAKE,QAAL,EAAlB,EAAmC;QACpC,KAAK3C,SAAL;MACH;;MACD,IAAIyC,UAAU,IAAI,KAAK7D,YAAL,CAAkBjG,WAAlB,CAA8BqI,UAA9B,KAA6CwB,cAA/D,EAA+E;QAC3E,KAAKI,eAAL,CAAqB,KAAKhE,YAAL,CAAkBjG,WAAlB,CAA8BkK,eAA9B,IAAiD,CAAtE;;QACA,IAAI,KAAKjE,YAAL,CAAkB/H,sBAAlB,IAA4C,KAAKkK,YAArD,EAAmE;UAC/D,IAAI,CAAC,KAAKT,0BAAV,EAAsC;YAClC,KAAKwC,yBAAL,GAAiC,KAAK3F,QAAL,CAAc1E,aAAd,CAA4BN,KAA7D;UACH;;UACD,KAAKmI,0BAAL,GAAkC,KAAKS,YAAvC;;UACA,KAAKY,kBAAL,CAAwB,KAAKZ,YAAL,CAAkB5I,KAA1C;QACH;MACJ;IACJ;EACJ;;EACD4K,YAAY,CAACnJ,KAAD,EAAQ;IAChB,IAAIoJ,MAAM,GAAGpJ,KAAK,CAACoJ,MAAnB;IACA,IAAI7K,KAAK,GAAG6K,MAAM,CAAC7K,KAAnB,CAFgB,CAGhB;;IACA,IAAI6K,MAAM,CAACvI,IAAP,KAAgB,QAApB,EAA8B;MAC1BtC,KAAK,GAAGA,KAAK,IAAI,EAAT,GAAc,IAAd,GAAqB8K,UAAU,CAAC9K,KAAD,CAAvC;IACH,CANe,CAOhB;IACA;IACA;IACA;IACA;;;IACA,IAAI,KAAK+K,cAAL,KAAwB/K,KAA5B,EAAmC;MAC/B,KAAK+K,cAAL,GAAsB/K,KAAtB;MACA,KAAKmI,0BAAL,GAAkC,IAAlC;;MACA,KAAKhC,SAAL,CAAenG,KAAf;;MACA,IAAI,KAAKwK,QAAL,MAAmB,KAAKjF,SAAL,CAAeU,aAAf,KAAiCxE,KAAK,CAACoJ,MAA9D,EAAsE;QAClE,KAAKhD,SAAL;MACH;IACJ;EACJ;;EACDmD,YAAY,GAAG;IACX,IAAI,CAAC,KAAKlF,mBAAV,EAA+B;MAC3B,KAAKA,mBAAL,GAA2B,IAA3B;IACH,CAFD,MAGK,IAAI,KAAK0E,QAAL,EAAJ,EAAqB;MACtB,KAAKO,cAAL,GAAsB,KAAK/F,QAAL,CAAc1E,aAAd,CAA4BN,KAAlD;;MACA,KAAK8H,cAAL;;MACA,KAAKC,WAAL,CAAiB,IAAjB;IACH;EACJ;;EACDkD,YAAY,GAAG;IACX,IAAI,KAAKT,QAAL,MAAmB,CAAC,KAAKtE,SAA7B,EAAwC;MACpC,KAAK2B,SAAL;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIE,WAAW,CAACmD,aAAa,GAAG,KAAjB,EAAwB;IAC/B,IAAI,KAAK5F,UAAL,IAAmB,KAAKA,UAAL,CAAgB6F,UAAhB,KAA+B,MAAtD,EAA8D;MAC1D,IAAID,aAAJ,EAAmB;QACf,KAAK5F,UAAL,CAAgB8F,oBAAhB;MACH,CAFD,MAGK;QACD,KAAK9F,UAAL,CAAgB6F,UAAhB,GAA6B,QAA7B;MACH;;MACD,KAAKvF,sBAAL,GAA8B,IAA9B;IACH;EACJ;EACD;;;EACAqC,WAAW,GAAG;IACV,IAAI,KAAKrC,sBAAT,EAAiC;MAC7B,KAAKN,UAAL,CAAgB6F,UAAhB,GAA6B,MAA7B;MACA,KAAKvF,sBAAL,GAA8B,KAA9B;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACIyF,0BAA0B,GAAG;IACzB,MAAMC,WAAW,GAAG,KAAKnG,KAAL,CAAW0B,QAAX,CAAoBF,IAApB,CAAyBjJ,IAAI,CAAC,CAAD,CAA7B,CAApB;;IACA,MAAM6N,aAAa,GAAG,KAAK9E,YAAL,CAAkBhG,OAAlB,CAA0BiG,OAA1B,CAAkCC,IAAlC,CAAuC9I,GAAG,CAAC,MAAM,KAAKyJ,iBAAL,CAAuBkE,mBAAvB,EAAP,CAA1C,EACtB;IACA;IACA1N,KAAK,CAAC,CAAD,CAHiB,CAAtB,CAFyB,CAMzB;;IACA,OAAQ7B,KAAK,CAACqP,WAAD,EAAcC,aAAd,CAAL,CACH5E,IADG,EAER;IACA;IACAlJ,SAAS,CAAC,MAAM;MACZ;MACA;MACA;MACA,KAAK0H,KAAL,CAAW+C,GAAX,CAAe,MAAM;QACjB,MAAMuD,OAAO,GAAG,KAAKvF,SAArB;;QACA,KAAKkE,gBAAL;;QACA,KAAK3D,YAAL,CAAkBzF,cAAlB;;QACA,KAAKpC,kBAAL,CAAwB2J,aAAxB;;QACA,IAAI,KAAKrC,SAAT,EAAoB;UAChB,KAAKsB,WAAL,CAAiBC,cAAjB;QACH;;QACD,IAAIgE,OAAO,KAAK,KAAKvF,SAArB,EAAgC;UAC5B;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,KAAKA,SAAT,EAAoB;YAChB,KAAKO,YAAL,CAAkBnH,MAAlB,CAAyBwB,IAAzB;UACH,CAFD,MAGK;YACD,KAAK2F,YAAL,CAAkBlH,MAAlB,CAAyBuB,IAAzB;UACH;QACJ;MACJ,CAvBD;;MAwBA,OAAO,KAAK0H,mBAAZ;IACH,CA7BQ,CAJD,EAkCR;IACA9K,IAAI,CAAC,CAAD,CAnCI,EAoCJ;IApCI,CAqCHkD,SArCG,CAqCOa,KAAK,IAAI,KAAKiK,iBAAL,CAAuBjK,KAAvB,CArChB,CAAR;EAsCH;EACD;;;EACAkG,aAAa,GAAG;IACZ,IAAI,KAAKH,WAAT,EAAsB;MAClB,KAAKQ,UAAL;;MACA,KAAKR,WAAL,CAAiBmE,OAAjB;;MACA,KAAKnE,WAAL,GAAmB,IAAnB;IACH;EACJ;;EACDgC,kBAAkB,CAACxJ,KAAD,EAAQ;IACtB,MAAM4L,SAAS,GAAG,KAAKnF,YAAL,IAAqB,KAAKA,YAAL,CAAkBrH,WAAvC,GACZ,KAAKqH,YAAL,CAAkBrH,WAAlB,CAA8BY,KAA9B,CADY,GAEZA,KAFN,CADsB,CAItB;IACA;;IACA,KAAK6L,uBAAL,CAA6BD,SAAS,IAAI,IAAb,GAAoBA,SAApB,GAAgC,EAA7D;EACH;;EACDC,uBAAuB,CAAC7L,KAAD,EAAQ;IAC3B;IACA;IACA,IAAI,KAAKsF,UAAT,EAAqB;MACjB,KAAKA,UAAL,CAAgBwG,QAAhB,CAAyB9L,KAAzB,GAAiCA,KAAjC;IACH,CAFD,MAGK;MACD,KAAKgF,QAAL,CAAc1E,aAAd,CAA4BN,KAA5B,GAAoCA,KAApC;IACH;;IACD,KAAK+K,cAAL,GAAsB/K,KAAtB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACI0L,iBAAiB,CAACjK,KAAD,EAAQ;IACrB,MAAMsK,QAAQ,GAAGtK,KAAK,GAAGA,KAAK,CAACvD,MAAT,GAAkB,KAAKiK,0BAA7C;;IACA,IAAI4D,QAAJ,EAAc;MACV,KAAKC,4BAAL,CAAkCD,QAAlC;;MACA,KAAKvC,kBAAL,CAAwBuC,QAAQ,CAAC/L,KAAjC;;MACA,KAAKmG,SAAL,CAAe4F,QAAQ,CAAC/L,KAAxB;;MACA,KAAKyG,YAAL,CAAkBjF,gBAAlB,CAAmCuK,QAAnC;;MACA,KAAK/G,QAAL,CAAc1E,aAAd,CAA4B2L,KAA5B;IACH;;IACD,KAAKjE,UAAL;EACH;EACD;AACJ;AACA;;;EACIgE,4BAA4B,CAACE,IAAD,EAAO;IAC/B,KAAKzF,YAAL,CAAkBhG,OAAlB,CAA0B0L,OAA1B,CAAkChO,MAAM,IAAI;MACxC,IAAIA,MAAM,KAAK+N,IAAX,IAAmB/N,MAAM,CAACiO,QAA9B,EAAwC;QACpCjO,MAAM,CAACkO,QAAP;MACH;IACJ,CAJD;EAKH;;EACDvE,cAAc,GAAG;IACb,IAAI,CAAC,KAAKrB,YAAN,KAAuB,OAAO6F,SAAP,KAAqB,WAArB,IAAoCA,SAA3D,CAAJ,EAA2E;MACvE,MAAMzH,mCAAmC,EAAzC;IACH;;IACD,IAAI0H,UAAU,GAAG,KAAK/E,WAAtB;;IACA,IAAI,CAAC+E,UAAL,EAAiB;MACb,KAAKC,OAAL,GAAe,IAAIpP,cAAJ,CAAmB,KAAKqJ,YAAL,CAAkB/D,QAArC,EAA+C,KAAKwC,iBAApD,EAAuE;QAClFxF,EAAE,EAAE,KAAK4F,UAAL,EAAiBmH,UAAjB;MAD8E,CAAvE,CAAf;MAGAF,UAAU,GAAG,KAAKtH,QAAL,CAAcyH,MAAd,CAAqB,KAAKC,iBAAL,EAArB,CAAb;MACA,KAAKnF,WAAL,GAAmB+E,UAAnB;;MACA,KAAKK,oBAAL,CAA0BL,UAA1B;;MACA,KAAK1G,qBAAL,GAA6B,KAAKL,cAAL,CAAoB7E,MAApB,GAA6BC,SAA7B,CAAuC,MAAM;QACtE,IAAI,KAAKsF,SAAL,IAAkBqG,UAAtB,EAAkC;UAC9BA,UAAU,CAACM,UAAX,CAAsB;YAAEC,KAAK,EAAE,KAAKC,cAAL;UAAT,CAAtB;QACH;MACJ,CAJ4B,CAA7B;IAKH,CAZD,MAaK;MACD;MACA,KAAKzF,iBAAL,CAAuB0F,SAAvB,CAAiC,KAAKC,oBAAL,EAAjC;;MACAV,UAAU,CAACM,UAAX,CAAsB;QAAEC,KAAK,EAAE,KAAKC,cAAL;MAAT,CAAtB;IACH;;IACD,IAAIR,UAAU,IAAI,CAACA,UAAU,CAACnE,WAAX,EAAnB,EAA6C;MACzCmE,UAAU,CAACW,MAAX,CAAkB,KAAKV,OAAvB;MACA,KAAKlE,2BAAL,GAAmC,KAAK+C,0BAAL,EAAnC;IACH;;IACD,MAAMI,OAAO,GAAG,KAAKvF,SAArB;;IACA,KAAKO,YAAL,CAAkBzF,cAAlB;;IACA,KAAKyF,YAAL,CAAkBtH,OAAlB,GAA4B,KAAKoH,gBAAL,GAAwB,IAApD,CA7Ba,CA8Bb;IACA;;IACA,IAAI,KAAKL,SAAL,IAAkBuF,OAAO,KAAK,KAAKvF,SAAvC,EAAkD;MAC9C,KAAKO,YAAL,CAAkBnH,MAAlB,CAAyBwB,IAAzB;IACH;EACJ;;EACD6L,iBAAiB,GAAG;IAChB,OAAO,IAAIlQ,aAAJ,CAAkB;MACrB0Q,gBAAgB,EAAE,KAAKC,mBAAL,EADG;MAErBhI,cAAc,EAAE,KAAK0B,eAAL,EAFK;MAGrBgG,KAAK,EAAE,KAAKC,cAAL,EAHc;MAIrBM,SAAS,EAAE,KAAKhI,IAJK;MAKrBiI,UAAU,EAAE,KAAK7H,SAAL,EAAgB8H;IALP,CAAlB,CAAP;EAOH;;EACDH,mBAAmB,GAAG;IAClB,MAAMI,QAAQ,GAAG,KAAKvI,QAAL,CACZoB,QADY,GAEZoH,mBAFY,CAEQ,KAAKR,oBAAL,EAFR,EAGZS,sBAHY,CAGW,KAHX,EAIZC,QAJY,CAIH,KAJG,CAAjB;;IAKA,KAAKpG,qBAAL,CAA2BiG,QAA3B;;IACA,KAAKlG,iBAAL,GAAyBkG,QAAzB;IACA,OAAOA,QAAP;EACH;EACD;;;EACAjG,qBAAqB,CAAC4F,gBAAD,EAAmB;IACpC;IACA;IACA,MAAMS,cAAc,GAAG,CACnB;MAAEC,OAAO,EAAE,OAAX;MAAoBC,OAAO,EAAE,QAA7B;MAAuCC,QAAQ,EAAE,OAAjD;MAA0DC,QAAQ,EAAE;IAApE,CADmB,EAEnB;MAAEH,OAAO,EAAE,KAAX;MAAkBC,OAAO,EAAE,QAA3B;MAAqCC,QAAQ,EAAE,KAA/C;MAAsDC,QAAQ,EAAE;IAAhE,CAFmB,CAAvB,CAHoC,CAOpC;IACA;IACA;;IACA,MAAMV,UAAU,GAAG,KAAKW,WAAxB;IACA,MAAMC,cAAc,GAAG,CACnB;MAAEL,OAAO,EAAE,OAAX;MAAoBC,OAAO,EAAE,KAA7B;MAAoCC,QAAQ,EAAE,OAA9C;MAAuDC,QAAQ,EAAE,QAAjE;MAA2EV;IAA3E,CADmB,EAEnB;MAAEO,OAAO,EAAE,KAAX;MAAkBC,OAAO,EAAE,KAA3B;MAAkCC,QAAQ,EAAE,KAA5C;MAAmDC,QAAQ,EAAE,QAA7D;MAAuEV;IAAvE,CAFmB,CAAvB;IAIA,IAAIa,SAAJ;;IACA,IAAI,KAAK9H,QAAL,KAAkB,OAAtB,EAA+B;MAC3B8H,SAAS,GAAGD,cAAZ;IACH,CAFD,MAGK,IAAI,KAAK7H,QAAL,KAAkB,OAAtB,EAA+B;MAChC8H,SAAS,GAAGP,cAAZ;IACH,CAFI,MAGA;MACDO,SAAS,GAAG,CAAC,GAAGP,cAAJ,EAAoB,GAAGM,cAAvB,CAAZ;IACH;;IACDf,gBAAgB,CAACiB,aAAjB,CAA+BD,SAA/B;EACH;;EACDlB,oBAAoB,GAAG;IACnB,IAAI,KAAKhE,WAAT,EAAsB;MAClB,OAAO,KAAKA,WAAL,CAAiBjF,UAAxB;IACH;;IACD,OAAO,KAAKsB,UAAL,GAAkB,KAAKA,UAAL,CAAgB+I,yBAAhB,EAAlB,GAAgE,KAAKrJ,QAA5E;EACH;;EACD+H,cAAc,GAAG;IACb,OAAO,KAAKtG,YAAL,CAAkB7D,UAAlB,IAAgC,KAAK0L,aAAL,EAAvC;EACH;EACD;;;EACAA,aAAa,GAAG;IACZ,OAAO,KAAKrB,oBAAL,GAA4B3M,aAA5B,CAA0CiO,qBAA1C,GAAkEzB,KAAzE;EACH;EACD;AACJ;AACA;AACA;;;EACI1C,gBAAgB,GAAG;IACf,MAAM3D,YAAY,GAAG,KAAKA,YAA1B;;IACA,IAAIA,YAAY,CAAChI,qBAAjB,EAAwC;MACpC;MACA;MACAgI,YAAY,CAACjG,WAAb,CAAyBgO,kBAAzB;IACH,CAJD,MAKK;MACD/H,YAAY,CAACjG,WAAb,CAAyBiO,aAAzB,CAAuC,CAAC,CAAxC;IACH;EACJ;EACD;;;EACAjE,QAAQ,GAAG;IACP,MAAMkE,OAAO,GAAG,KAAK1J,QAAL,CAAc1E,aAA9B;IACA,OAAO,CAACoO,OAAO,CAACC,QAAT,IAAqB,CAACD,OAAO,CAAC5E,QAA9B,IAA0C,CAAC,KAAKnE,qBAAvD;EACH;EACD;;;EACAuB,UAAU,GAAG;IACT,OAAO,KAAK3B,SAAL,EAAgBqJ,WAAhB,IAA+B3H,MAAtC;EACH;EACD;;;EACAwD,eAAe,CAAC5J,KAAD,EAAQ;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM4F,YAAY,GAAG,KAAKA,YAA1B;;IACA,MAAMoI,UAAU,GAAGnT,6BAA6B,CAACmF,KAAD,EAAQ4F,YAAY,CAAChG,OAArB,EAA8BgG,YAAY,CAAC5C,YAA3C,CAAhD;;IACA,IAAIhD,KAAK,KAAK,CAAV,IAAegO,UAAU,KAAK,CAAlC,EAAqC;MACjC;MACA;MACA;MACApI,YAAY,CAACtF,aAAb,CAA2B,CAA3B;IACH,CALD,MAMK,IAAIsF,YAAY,CAACpF,KAAjB,EAAwB;MACzB,MAAMlD,MAAM,GAAGsI,YAAY,CAAChG,OAAb,CAAqBM,OAArB,GAA+BF,KAA/B,CAAf;;MACA,IAAI1C,MAAJ,EAAY;QACR,MAAMuQ,OAAO,GAAGvQ,MAAM,CAAC2Q,eAAP,EAAhB;;QACA,MAAMC,iBAAiB,GAAGpT,wBAAwB,CAAC+S,OAAO,CAACM,SAAT,EAAoBN,OAAO,CAACO,YAA5B,EAA0CxI,YAAY,CAACnF,aAAb,EAA1C,EAAwEmF,YAAY,CAACpF,KAAb,CAAmBf,aAAnB,CAAiC2O,YAAzG,CAAlD;;QACAxI,YAAY,CAACtF,aAAb,CAA2B4N,iBAA3B;MACH;IACJ;EACJ;EACD;;;EACAnC,oBAAoB,CAACL,UAAD,EAAa;IAC7B;IACA;IACAA,UAAU,CAAC2C,aAAX,GAA2BtO,SAA3B,CAAqCa,KAAK,IAAI;MAC1C;MACA;MACA,IAAKA,KAAK,CAACuI,OAAN,KAAkBjN,MAAlB,IAA4B,CAACD,cAAc,CAAC2E,KAAD,CAA5C,IACCA,KAAK,CAACuI,OAAN,KAAkB/M,QAAlB,IAA8BH,cAAc,CAAC2E,KAAD,EAAQ,QAAR,CADjD,EACqE;QACjE;QACA;QACA,IAAI,KAAK0G,0BAAT,EAAqC;UACjC,KAAK0D,uBAAL,CAA6B,KAAKlB,yBAAL,IAAkC,EAA/D;;UACA,KAAKxC,0BAAL,GAAkC,IAAlC;QACH;;QACD,KAAKpC,oBAAL,CAA0BoJ,IAA1B;;QACA,KAAK/E,gBAAL,GARiE,CASjE;QACA;;;QACA3I,KAAK,CAAC2N,eAAN;QACA3N,KAAK,CAACyI,cAAN;MACH;IACJ,CAlBD,EAH6B,CAsB7B;IACA;IACA;;IACAqC,UAAU,CAAC8C,oBAAX,GAAkCzO,SAAlC;EACH;;AAjkB6B;;AAmkBlCmE,2BAA2B,CAAC9C,IAA5B;EAAA,iBAAwH8C,2BAAxH,EA1sBuG3K,EA0sBvG,mBAAqKA,EAAE,CAAC+H,UAAxK,GA1sBuG/H,EA0sBvG,mBAA+LmC,IAAI,CAACC,OAApM,GA1sBuGpC,EA0sBvG,mBAAwNA,EAAE,CAACkV,gBAA3N,GA1sBuGlV,EA0sBvG,mBAAwPA,EAAE,CAACmV,MAA3P,GA1sBuGnV,EA0sBvG,mBAA8QA,EAAE,CAAC8H,iBAAjR,GA1sBuG9H,EA0sBvG,mBAA+S8J,gCAA/S,GA1sBuG9J,EA0sBvG,mBAA4VyC,IAAI,CAAC2S,cAAjW,MA1sBuGpV,EA0sBvG,mBAA4YmD,cAA5Y,MA1sBuGnD,EA0sBvG,mBAAmciC,QAAnc,MA1sBuGjC,EA0sBvG,mBAAweuC,EAAE,CAAC8S,aAA3e,GA1sBuGrV,EA0sBvG,mBAAqgBiE,gCAArgB;AAAA;;AACA0G,2BAA2B,CAAC1C,IAA5B,kBA3sBuGjI,EA2sBvG;EAAA,MAA4G2K,2BAA5G;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WA3sBuG3K,EA2sBvG;AAAA;;AACA;EAAA,mDA5sBuGA,EA4sBvG,mBAA2F2K,2BAA3F,EAAoI,CAAC;IACzHzC,IAAI,EAAE9H;EADmH,CAAD,CAApI,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAE8H,IAAI,EAAElI,EAAE,CAAC+H;IAAX,CAAD,EAA0B;MAAEG,IAAI,EAAE/F,IAAI,CAACC;IAAb,CAA1B,EAAkD;MAAE8F,IAAI,EAAElI,EAAE,CAACkV;IAAX,CAAlD,EAAiF;MAAEhN,IAAI,EAAElI,EAAE,CAACmV;IAAX,CAAjF,EAAsG;MAAEjN,IAAI,EAAElI,EAAE,CAAC8H;IAAX,CAAtG,EAAsI;MAAEI,IAAI,EAAEC,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACnMF,IAAI,EAAE7H,MAD6L;QAEnMgI,IAAI,EAAE,CAACyB,gCAAD;MAF6L,CAAD;IAA/B,CAAtI,EAG3B;MAAE5B,IAAI,EAAEzF,IAAI,CAAC2S,cAAb;MAA6BhN,UAAU,EAAE,CAAC;QAC5CF,IAAI,EAAEpH;MADsC,CAAD;IAAzC,CAH2B,EAK3B;MAAEoH,IAAI,EAAEhF,EAAE,CAACoS,YAAX;MAAyBlN,UAAU,EAAE,CAAC;QACxCF,IAAI,EAAEpH;MADkC,CAAD,EAExC;QACCoH,IAAI,EAAE7H,MADP;QAECgI,IAAI,EAAE,CAAClF,cAAD;MAFP,CAFwC,EAKxC;QACC+E,IAAI,EAAEnH;MADP,CALwC;IAArC,CAL2B,EAY3B;MAAEmH,IAAI,EAAEC,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClCF,IAAI,EAAEpH;MAD4B,CAAD,EAElC;QACCoH,IAAI,EAAE7H,MADP;QAECgI,IAAI,EAAE,CAACpG,QAAD;MAFP,CAFkC;IAA/B,CAZ2B,EAiB3B;MAAEiG,IAAI,EAAE3F,EAAE,CAAC8S;IAAX,CAjB2B,EAiBC;MAAEnN,IAAI,EAAEC,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9DF,IAAI,EAAEpH;MADwD,CAAD,EAE9D;QACCoH,IAAI,EAAE7H,MADP;QAECgI,IAAI,EAAE,CAACpE,gCAAD;MAFP,CAF8D;IAA/B,CAjBD,CAAP;EAsBlB,CAxBxB,EAwB0C;IAAEoI,YAAY,EAAE,CAAC;MAC3CnE,IAAI,EAAE3H,KADqC;MAE3C8H,IAAI,EAAE,CAAC,iBAAD;IAFqC,CAAD,CAAhB;IAG1B4D,QAAQ,EAAE,CAAC;MACX/D,IAAI,EAAE3H,KADK;MAEX8H,IAAI,EAAE,CAAC,yBAAD;IAFK,CAAD,CAHgB;IAM1BwG,WAAW,EAAE,CAAC;MACd3G,IAAI,EAAE3H,KADQ;MAEd8H,IAAI,EAAE,CAAC,4BAAD;IAFQ,CAAD,CANa;IAS1B6D,qBAAqB,EAAE,CAAC;MACxBhE,IAAI,EAAE3H,KADkB;MAExB8H,IAAI,EAAE,CAAC,cAAD;IAFkB,CAAD,CATG;IAY1BsE,oBAAoB,EAAE,CAAC;MACvBzE,IAAI,EAAE3H,KADiB;MAEvB8H,IAAI,EAAE,CAAC,yBAAD;IAFiB,CAAD;EAZI,CAxB1C;AAAA;;AAwCA,MAAMkC,sBAAN,SAAqCI,2BAArC,CAAiE;EAC7D9G,WAAW,GAAG;IACV,MAAM,GAAG6E,SAAT;IACA,KAAKmL,WAAL,GAAmB,8BAAnB;EACH;;AAJ4D;;AAMjEtJ,sBAAsB,CAAC1C,IAAvB;EAAA;EAAA;IAAA,sFA1vBuG7H,EA0vBvG,uBAAmHuK,sBAAnH,SAAmHA,sBAAnH;EAAA;AAAA;;AACAA,sBAAsB,CAACtC,IAAvB,kBA3vBuGjI,EA2vBvG;EAAA,MAAuGuK,sBAAvG;EAAA;EAAA;EAAA;EAAA;IAAA;MA3vBuGvK,EA2vBvG;QAAA,OAAuG,kBAAvG;MAAA;QAAA,OAAuG,gBAAvG;MAAA;QAAA,OAAuG,wBAAvG;MAAA;QAAA,OAAuG,0BAAvG;MAAA;QAAA,OAAuG,kBAAvG;MAAA;IAAA;;IAAA;MA3vBuGA,EA2vBvG;IAAA;EAAA;EAAA;EAAA,WA3vBuGA,EA2vBvG,oBAA05B,CAACsK,+BAAD,CAA15B,GA3vBuGtK,EA2vBvG;AAAA;;AACA;EAAA,mDA5vBuGA,EA4vBvG,mBAA2FuK,sBAA3F,EAA+H,CAAC;IACpHrC,IAAI,EAAE9H,SAD8G;IAEpHiI,IAAI,EAAE,CAAC;MACCU,QAAQ,EAAG,mDADZ;MAECO,IAAI,EAAE;QACF,SAAS,0BADP;QAEF,uBAAuB,uBAFrB;QAGF,eAAe,0CAHb;QAIF,4BAA4B,sCAJ1B;QAKF,gCAAgC,sDAL9B;QAMF,wBAAwB,oDANtB;QAOF,oBAAoB,gEAPlB;QAQF,wBAAwB,yCARtB;QASF;QACA;QACA,aAAa,gBAXX;QAYF,UAAU,cAZR;QAaF,WAAW,sBAbT;QAcF,aAAa,wBAdX;QAeF,WAAW;MAfT,CAFP;MAmBCF,QAAQ,EAAE,wBAnBX;MAoBCG,SAAS,EAAE,CAACe,+BAAD;IApBZ,CAAD;EAF8G,CAAD,CAA/H;AAAA;AA0BA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMiL,qBAAN,CAA4B;;AAE5BA,qBAAqB,CAAC1N,IAAtB;EAAA,iBAAkH0N,qBAAlH;AAAA;;AACAA,qBAAqB,CAACC,IAAtB,kBAhyBuGxV,EAgyBvG;EAAA,MAAmHuV,qBAAnH;EAAA,eAAyJ9M,eAAzJ,EAA0K8B,sBAA1K,EAAkMV,qBAAlM;EAAA,UAAoOvH,aAApO,EAAmPd,eAAnP,EAAoQC,eAApQ,EAAqRS,YAArR;EAAA,UAA8SuG,eAA9S,EACQ8B,sBADR,EAEQV,qBAFR,EAGQrH,mBAHR,EAIQhB,eAJR,EAKQC,eALR;AAAA;AAMA8T,qBAAqB,CAACE,IAAtB,kBAtyBuGzV,EAsyBvG;EAAA,WAAqJ,CAACmK,iDAAD,CAArJ;EAAA,UAAoN7H,aAApN,EAAmOd,eAAnO,EAAoPC,eAApP,EAAqQS,YAArQ,EAAmRM,mBAAnR,EACQhB,eADR,EAEQC,eAFR;AAAA;;AAGA;EAAA,mDAzyBuGzB,EAyyBvG,mBAA2FuV,qBAA3F,EAA8H,CAAC;IACnHrN,IAAI,EAAElH,QAD6G;IAEnHqH,IAAI,EAAE,CAAC;MACCqN,OAAO,EAAE,CAACpT,aAAD,EAAgBd,eAAhB,EAAiCC,eAAjC,EAAkDS,YAAlD,CADV;MAECyT,OAAO,EAAE,CACLlN,eADK,EAEL8B,sBAFK,EAGLV,qBAHK,EAILrH,mBAJK,EAKLhB,eALK,EAMLC,eANK,CAFV;MAUCmU,YAAY,EAAE,CAACnN,eAAD,EAAkB8B,sBAAlB,EAA0CV,qBAA1C,CAVf;MAWCN,SAAS,EAAE,CAACY,iDAAD;IAXZ,CAAD;EAF6G,CAAD,CAA9H;AAAA;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASlG,gCAAT,EAA2CG,wCAA3C,EAAqF0F,gCAArF,EAAuHC,wCAAvH,EAAiKI,iDAAjK,EAAoNG,+BAApN,EAAqP7B,eAArP,EAAsQ8M,qBAAtQ,EAA6R1L,qBAA7R,EAAoTjG,4BAApT,EAAkV2G,sBAAlV,EAA0WhG,oBAA1W,EAAgYoF,0BAAhY,EAA4ZgB,2BAA5Z,EAAybF,mCAAzb"}, "metadata": {}, "sourceType": "module"}