{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./retrive-field-documents.component.html?ngResource\";\nimport { Component, ViewChild } from '@angular/core';\nimport { Validators, FormGroup, FormControl } from '@angular/forms';\nimport { FilingApiService } from '../../Services/FilingApiService';\nimport { StateSelectorComponent } from 'src/app/Modules/Shared/Components/formComponents/stateSelector/state-selector.component';\nimport { CorporationTypeSelectorComponent } from 'src/app/Modules/Shared/Components/formComponents/corporation-type-selector/corporation-type-selector.component';\nimport { RetriveFieldDocument } from '../../Models/RetriveFieldDocument';\nimport { forkJoin } from 'rxjs';\nimport { ProductCode } from 'src/app/Modules/Shared/Enums/products-code.enum';\nimport { CategoryCode } from 'src/app/Modules/Shared/Enums/category-code.enum';\nimport { SubCategoryCode } from 'src/app/Modules/Shared/Enums/sub-category-code.enum';\nimport { FilingServiceResponseObject } from '../../Enums/filing-service-enum';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { LoadFilingService } from '../../Functions/filing-service-load.func';\nimport { FormModeService } from 'src/app/Modules/Shared/Services/Common/FormModeService';\nimport { PDFValidate } from '../../Models/PDFValidate';\nimport { FilingInfoService } from '../../Services/filing-price.service';\nimport { CustomSharedValidations } from 'src/app/Modules/Shared/functions/custom-validations';\nimport { StatePriceRequest } from 'src/app/Modules/Shared/Models/Common/state-price-request.model';\nimport { PageTitleService } from 'src/app/Modules/Shared/Services/Common/page-title.service';\nlet RetriveFieldDocumentsComponent = class RetriveFieldDocumentsComponent {\n  constructor(filingInfoService, router, Api, formMode, activatedRoute, pageTitleService) {\n    this.filingInfoService = filingInfoService;\n    this.router = router;\n    this.Api = Api;\n    this.formMode = formMode;\n    this.activatedRoute = activatedRoute;\n    this.pageTitleService = pageTitleService;\n    this.RetriveFieldDocument = new RetriveFieldDocument();\n    this.California = \"CA\";\n    this.TypeOfDocuments = [];\n    this.pdfValidate = new PDFValidate();\n    this.isPDFRequestComplete = false;\n    this.Form = new FormGroup({\n      FormationState: new FormControl(null, []),\n      FormationType: new FormControl(null, []),\n      DocumentType: new FormControl(null, [Validators.required, Validators.nullValidator]),\n      CompanyName: new FormControl(null, [Validators.required]),\n      ContactName: new FormControl(null),\n      LLCNumber: new FormControl(null),\n      FormationDate: new FormControl(null),\n      CertifiedCopiesNo: new FormControl(null, [Validators.required, CustomSharedValidations.noDecimal, CustomSharedValidations.maxNumberOfCopies]),\n      CertifiedPlainNo: new FormControl(null, [Validators.required, CustomSharedValidations.noDecimal, CustomSharedValidations.maxNumberOfCopies]),\n      Remarks: new FormControl(null, [CustomSharedValidations.specialInstructions])\n    });\n  }\n\n  ngOnInit() {\n    this.filingInfoService.getLabel(this.filingInfoService.SubCatCode.retrievefielddocs, 'FOT').subscribe(label => {\n      this.pageTitleService.setPageTitle(label);\n    });\n    this.filingInfoService.getPrice(this.filingInfoService.SubCatCode.retrievefielddocs).subscribe(res => {\n      this.price = res;\n      this.basePrice = res;\n    });\n    const fillData = forkJoin([this.Api.FCGetStates(), this.Api.GetDocumentTypes(), this.Api.GetFormationType(\"910\")]);\n    fillData.subscribe(response => {\n      this.StateComponent.States = response[0];\n      this.TypeOfDocuments = response[1];\n      this.CorpTypeComponent.CorporationTypes = response[2] || [];\n      this.StartLoad();\n    });\n    this.Form.controls.FormationType.valueChanges.subscribe(selectedEntity => {\n      this.IsPDFAvailable(true, selectedEntity);\n    });\n    this.StateComponent.$OnStateSelection.subscribe(selectedState => {\n      if (selectedState) {\n        var request = new StatePriceRequest();\n        request.ProductCode = 'FS';\n        request.CategoryCode = 'FOT';\n        request.SubCategoryCode = '910';\n        request.State = selectedState;\n        this.Api.GetStateWisePrice(request).subscribe(res => {\n          this.price = res.price > 0 ? res.price : this.basePrice;\n        });\n      }\n    });\n  }\n\n  getStatePrice(isEntityType, value) {\n    var corpType = isEntityType ? value : this.Form.controls.FormationType.value;\n    var documentType = this.Form.controls.DocumentType.value;\n\n    if (corpType && documentType) {\n      var request = new StatePriceRequest();\n      request.ProductCode = 'FS';\n      request.CategoryCode = 'FOT';\n      request.SubCategoryCode = '910';\n      request.State = this.Form.controls.FormationState.value;\n      request.EntityCode = corpType || '';\n      request.OptionCode = documentType || '';\n      this.Api.GetStateWisePrice(request).subscribe(res => {\n        this.price = res.price > 0 ? res.price : this.basePrice;\n      });\n    }\n  }\n\n  clickOnDownload() {\n    this.selectedState = this.Form.controls.FormationState.value;\n    this.Api.GetPDFUrl(ProductCode.FS, this.selectedState, CategoryCode.otherfiling, SubCategoryCode.retrievefielddocs).subscribe(data => {\n      if (data.downloadUrl != \"\") {\n        window.open(data.downloadUrl);\n      } else {\n        this.router.navigate(['/filingservice/error-page']);\n      }\n    });\n  }\n\n  StartLoad() {\n    var _this = this;\n\n    this.activatedRoute.queryParams.subscribe( /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (queryString) {\n        LoadFilingService(queryString, _this.Api, FilingServiceResponseObject.RetrieveFieldDocuments, _this.formMode).then(serviceData => {\n          _this.RetriveFieldDocument = serviceData;\n\n          _this.Load();\n        }).catch(e => console.log(e));\n      });\n\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  }\n\n  urlValidator(control) {\n    if (control.parent) {\n      if (this.Form.value.FormationState) {\n        if (this.Form.value.FormationState == 'California') {\n          let error = !this.Form.value.CompanyName ? {\n            required: \"Company Name Required\"\n          } : null;\n          return error;\n        }\n      }\n    }\n\n    return null;\n  }\n\n  IsPDFAvailable(isEntityType, value) {\n    this.getStatePrice(isEntityType, value);\n    this.isPDFRequestComplete = false;\n    this.pdfValidate.IsPDFDownloadable = false;\n    this.pdfValidate.ProductCode = 'FS';\n    this.pdfValidate.CategoryCode = '910';\n    this.pdfValidate.EntityType = this.Form.controls.FormationType.value;\n    this.pdfValidate.DocumentType = this.Form.controls.DocumentType.value;\n\n    if (this.pdfValidate.EntityType && this.pdfValidate.DocumentType) {\n      this.Api.CheckPDFDownloadable(this.pdfValidate).subscribe(validatePdf => {\n        this.pdfValidate.IsPDFDownloadable = validatePdf.isPDFDownloadable;\n        this.isPDFRequestComplete = true;\n      });\n    }\n  }\n\n  OnSave() {\n    try {\n      if (this.Form.valid) {\n        this.Save();\n      }\n    } catch (ex) {\n      console.error(ex);\n    }\n  }\n\n  Save() {\n    this.RetriveFieldDocument.FormationState = this.Form.controls.FormationState.value;\n    this.RetriveFieldDocument.FormationType = this.Form.controls.FormationType.value.trim();\n    this.RetriveFieldDocument.DocumentType = this.Form.controls.DocumentType.value;\n    this.RetriveFieldDocument.CompanyName = this.Form.controls.CompanyName.value;\n    this.RetriveFieldDocument.ContactName = this.Form.controls.ContactName.value;\n    this.RetriveFieldDocument.LLCNumber = this.Form.controls.LLCNumber.value;\n    this.RetriveFieldDocument.FormationDate = this.Form.controls.FormationDate.value;\n    this.RetriveFieldDocument.CertifiedCopiesNo = this.Form.controls.CertifiedCopiesNo.value;\n    this.RetriveFieldDocument.CertifiedPlainNo = this.Form.controls.CertifiedPlainNo.value;\n    this.RetriveFieldDocument.Remarks = this.Form.controls.Remarks.value;\n\n    if (this.Form.controls.FormationDate.value) {\n      this.RetriveFieldDocument.Formation_date_m = this.Form.controls.FormationDate.value.getMonth() + 1;\n      this.RetriveFieldDocument.Formation_date_d = this.Form.controls.FormationDate.value.getDate();\n      this.RetriveFieldDocument.Formation_date_y = this.Form.controls.FormationDate.value.getFullYear();\n    }\n\n    this.Api.SaveFilingService({\n      retrieveFieldDocs: this.RetriveFieldDocument\n    }).subscribe(x => {\n      this.Form.reset();\n    });\n  }\n\n  Load() {\n    this.Form.controls.FormationState.setValue(this.RetriveFieldDocument.FormationState);\n    this.Form.controls.FormationType.setValue(this.RetriveFieldDocument.FormationType.trim());\n    this.Form.controls.DocumentType.setValue(this.RetriveFieldDocument.DocumentType);\n    this.Form.controls.CompanyName.setValue(this.RetriveFieldDocument.CompanyName); // this.Form.controls.ContactName.setValue(this.RetriveFieldDocument.ContactName)\n    // this.Form.controls.Phone.setValue(this.RetriveFieldDocument.Phone)\n    // this.Form.controls.Email.setValue(this.RetriveFieldDocument.Email)\n\n    this.Form.controls.LLCNumber.setValue(this.RetriveFieldDocument[\"LlcNumber\"]);\n    let fromationDate = this.RetriveFieldDocument.Formation_date_m + \"-\" + this.RetriveFieldDocument.Formation_date_d + \"-\" + this.RetriveFieldDocument.Formation_date_y;\n    fromationDate = fromationDate.split('-').filter(x => x.length > 0).length > 0 ? fromationDate : null;\n    this.Form.controls.FormationDate.setValue(fromationDate ? new Date(fromationDate) : null);\n    this.Form.controls.CertifiedCopiesNo.setValue(this.RetriveFieldDocument.CertifiedCopiesNo);\n    this.Form.controls.CertifiedPlainNo.setValue(this.RetriveFieldDocument.CertifiedPlainNo);\n    this.Form.controls.Remarks.setValue(this.RetriveFieldDocument.Remarks);\n    this.isPDFRequestComplete = true;\n    this.getStatePrice(false, '');\n  }\n\n  recalculatesValidator() {\n    for (let s in this.Form.controls) {\n      this.Form.controls[s].updateValueAndValidity();\n    }\n  }\n\n  emailConditionallyRequiredValidator(formGroup) {\n    if (this.RetriveFieldDocument.FormationState) {\n      if (this.RetriveFieldDocument.FormationState == 'California') {\n        return this.Form.value.CompanyName ? {\n          required: \"Company Name Required\"\n        } : null;\n      }\n    }\n\n    return null;\n  }\n\n};\n\nRetriveFieldDocumentsComponent.ctorParameters = () => [{\n  type: FilingInfoService\n}, {\n  type: Router\n}, {\n  type: FilingApiService\n}, {\n  type: FormModeService\n}, {\n  type: ActivatedRoute\n}, {\n  type: PageTitleService\n}];\n\nRetriveFieldDocumentsComponent.propDecorators = {\n  StateComponent: [{\n    type: ViewChild,\n    args: [StateSelectorComponent, {\n      static: true\n    }]\n  }],\n  CorpTypeComponent: [{\n    type: ViewChild,\n    args: [CorporationTypeSelectorComponent, {\n      static: true\n    }]\n  }]\n};\nRetriveFieldDocumentsComponent = __decorate([Component({\n  selector: 'retrive-field-document',\n  template: __NG_CLI_RESOURCE__0\n})], RetriveFieldDocumentsComponent);\nexport { RetriveFieldDocumentsComponent };", "map": {"version": 3, "mappings": ";;;AAAA,SAASA,SAAT,EAA4BC,SAA5B,QAA6C,eAA7C;AACA,SAASC,UAAT,EAAsCC,SAAtC,EAAiDC,WAAjD,QAAoE,gBAApE;AACA,SAASC,gBAAT,QAAiC,iCAAjC;AACA,SAASC,sBAAT,QAAuC,yFAAvC;AACA,SAASC,gCAAT,QAAiD,gHAAjD;AACA,SAASC,oBAAT,QAAqC,mCAArC;AACA,SAASC,QAAT,QAAyB,MAAzB;AACA,SAASC,WAAT,QAA4B,iDAA5B;AAEA,SAASC,YAAT,QAA6B,iDAA7B;AACA,SAASC,eAAT,QAAgC,qDAAhC;AACA,SAASC,2BAAT,QAA4C,iCAA5C;AACA,SAASC,cAAT,EAAyBC,MAAzB,QAAuC,iBAAvC;AACA,SAASC,iBAAT,QAAkC,0CAAlC;AACA,SAASC,eAAT,QAAgC,wDAAhC;AACA,SAASC,WAAT,QAA4B,0BAA5B;AACA,SAASC,iBAAT,QAAkC,qCAAlC;AACA,SAASC,uBAAT,QAAwC,qDAAxC;AACA,SAASC,iBAAT,QAAkC,gEAAlC;AACA,SAASC,gBAAT,QAAiC,2DAAjC;IAMaC,8BAA8B,SAA9BA,8BAA8B;EAEvCC,YAAmBC,iBAAnB,EAAiEC,MAAjE,EAAyFC,GAAzF,EAAuHC,QAAvH,EAA0JC,cAA1J,EAAkMC,gBAAlM,EAAoO;IAAjN;IAA8C;IAAwB;IAA8B;IAAmC;IAAwC;IAGlM,4BAA6C,IAAItB,oBAAJ,EAA7C;IACA,kBAAa,IAAb;IACA,uBAAyB,EAAzB;IACA,mBAAc,IAAIU,WAAJ,EAAd;IACA,4BAAuB,KAAvB;IAQA,YAAO,IAAIf,SAAJ,CAAc;MAEjB4B,cAAc,EAAE,IAAI3B,WAAJ,CAA+B,IAA/B,EAAqC,EAArC,CAFC;MAGjB4B,aAAa,EAAE,IAAI5B,WAAJ,CAA+B,IAA/B,EAAqC,EAArC,CAHE;MAIjB6B,YAAY,EAAE,IAAI7B,WAAJ,CAA+B,IAA/B,EAAqC,CAACF,UAAU,CAACgC,QAAZ,EAAsBhC,UAAU,CAACiC,aAAjC,CAArC,CAJG;MAKjBC,WAAW,EAAE,IAAIhC,WAAJ,CAA+B,IAA/B,EAAqC,CAACF,UAAU,CAACgC,QAAZ,CAArC,CALI;MAMjBG,WAAW,EAAE,IAAIjC,WAAJ,CAA+B,IAA/B,CANI;MAOjBkC,SAAS,EAAE,IAAIlC,WAAJ,CAA+B,IAA/B,CAPM;MAQjBmC,aAAa,EAAE,IAAInC,WAAJ,CAA4B,IAA5B,CARE;MASjBoC,iBAAiB,EAAE,IAAIpC,WAAJ,CAA+B,IAA/B,EAAqC,CAACF,UAAU,CAACgC,QAAZ,EAAsBd,uBAAuB,CAACqB,SAA9C,EAAyDrB,uBAAuB,CAACsB,iBAAjF,CAArC,CATF;MAUjBC,gBAAgB,EAAE,IAAIvC,WAAJ,CAA4B,IAA5B,EAAkC,CAACF,UAAU,CAACgC,QAAZ,EAAsBd,uBAAuB,CAACqB,SAA9C,EAAyDrB,uBAAuB,CAACsB,iBAAjF,CAAlC,CAVD;MAWjBE,OAAO,EAAE,IAAIxC,WAAJ,CAA+B,IAA/B,EAAqC,CAACgB,uBAAuB,CAACyB,mBAAzB,CAArC;IAXQ,CAAd,CAAP;EAfyO;;EA+BzOC,QAAQ;IACJ,KAAKrB,iBAAL,CAAuBsB,QAAvB,CAAgC,KAAKtB,iBAAL,CAAuBuB,UAAvB,CAAkCC,iBAAlE,EAAqF,KAArF,EAA4FC,SAA5F,CAAsGC,KAAK,IAAG;MAC1G,KAAKrB,gBAAL,CAAsBsB,YAAtB,CAAmCD,KAAnC;IACH,CAFD;IAGA,KAAK1B,iBAAL,CAAuB4B,QAAvB,CAAgC,KAAK5B,iBAAL,CAAuBuB,UAAvB,CAAkCC,iBAAlE,EAAqFC,SAArF,CAA+FI,GAAG,IAAG;MACjG,KAAKC,KAAL,GAAaD,GAAb;MACA,KAAKE,SAAL,GAAiBF,GAAjB;IACH,CAHD;IAIA,MAAMG,QAAQ,GAAGhD,QAAQ,CAAC,CACtB,KAAKkB,GAAL,CAAS+B,WAAT,EADsB,EAEtB,KAAK/B,GAAL,CAASgC,gBAAT,EAFsB,EAGtB,KAAKhC,GAAL,CAASiC,gBAAT,CAA0B,KAA1B,CAHsB,CAAD,CAAzB;IAQAH,QAAQ,CAACP,SAAT,CAAmBW,QAAQ,IAAG;MAG1B,KAAKC,cAAL,CAAoBC,MAApB,GAA6BF,QAAQ,CAAC,CAAD,CAArC;MACA,KAAKG,eAAL,GAAuBH,QAAQ,CAAC,CAAD,CAA/B;MACA,KAAKI,iBAAL,CAAuBC,gBAAvB,GAA0CL,QAAQ,CAAC,CAAD,CAAR,IAAe,EAAzD;MACA,KAAKM,SAAL;IACH,CAPD;IAUA,KAAKC,IAAL,CAAUC,QAAV,CAAmBrC,aAAnB,CAAiCsC,YAAjC,CAA8CpB,SAA9C,CAAwDqB,cAAc,IAAG;MAErE,KAAKC,cAAL,CAAoB,IAApB,EAA0BD,cAA1B;IAEH,CAJD;IAMA,KAAKT,cAAL,CAAoBW,iBAApB,CAAsCvB,SAAtC,CAAgDwB,aAAa,IAAG;MAC5D,IAAIA,aAAJ,EAAmB;QACf,IAAIC,OAAO,GAAG,IAAItD,iBAAJ,EAAd;QACAsD,OAAO,CAACjE,WAAR,GAAsB,IAAtB;QACAiE,OAAO,CAAChE,YAAR,GAAuB,KAAvB;QACAgE,OAAO,CAAC/D,eAAR,GAA0B,KAA1B;QACA+D,OAAO,CAACC,KAAR,GAAgBF,aAAhB;QACA,KAAK/C,GAAL,CAASkD,iBAAT,CAA2BF,OAA3B,EAAoCzB,SAApC,CAA8CI,GAAG,IAAG;UAChD,KAAKC,KAAL,GAAaD,GAAG,CAACC,KAAJ,GAAY,CAAZ,GAAgBD,GAAG,CAACC,KAApB,GAA4B,KAAKC,SAA9C;QACH,CAFD;MAGH;IACJ,CAXD;EAaH;;EAEDsB,aAAa,CAACC,YAAD,EAAeC,KAAf,EAAoB;IAC7B,IAAIC,QAAQ,GAAGF,YAAY,GAAGC,KAAH,GAAW,KAAKZ,IAAL,CAAUC,QAAV,CAAmBrC,aAAnB,CAAiCgD,KAAvE;IACA,IAAIE,YAAY,GAAG,KAAKd,IAAL,CAAUC,QAAV,CAAmBpC,YAAnB,CAAgC+C,KAAnD;;IACA,IAAIC,QAAQ,IAAIC,YAAhB,EAA8B;MAC1B,IAAIP,OAAO,GAAG,IAAItD,iBAAJ,EAAd;MACAsD,OAAO,CAACjE,WAAR,GAAsB,IAAtB;MACAiE,OAAO,CAAChE,YAAR,GAAuB,KAAvB;MACAgE,OAAO,CAAC/D,eAAR,GAA0B,KAA1B;MACA+D,OAAO,CAACC,KAAR,GAAgB,KAAKR,IAAL,CAAUC,QAAV,CAAmBtC,cAAnB,CAAkCiD,KAAlD;MACAL,OAAO,CAACQ,UAAR,GAAqBF,QAAQ,IAAI,EAAjC;MACAN,OAAO,CAACS,UAAR,GAAqBF,YAAY,IAAI,EAArC;MAEA,KAAKvD,GAAL,CAASkD,iBAAT,CAA2BF,OAA3B,EAAoCzB,SAApC,CAA8CI,GAAG,IAAG;QAChD,KAAKC,KAAL,GAAaD,GAAG,CAACC,KAAJ,GAAY,CAAZ,GAAgBD,GAAG,CAACC,KAApB,GAA4B,KAAKC,SAA9C;MACH,CAFD;IAGH;EACJ;;EAED6B,eAAe;IACX,KAAKX,aAAL,GAAqB,KAAKN,IAAL,CAAUC,QAAV,CAAmBtC,cAAnB,CAAkCiD,KAAvD;IACA,KAAKrD,GAAL,CAAS2D,SAAT,CAAmB5E,WAAW,CAAC6E,EAA/B,EAAmC,KAAKb,aAAxC,EAAuD/D,YAAY,CAAC6E,WAApE,EAAiF5E,eAAe,CAACqC,iBAAjG,EAAoHC,SAApH,CAA+HuC,IAAD,IAA8B;MACxJ,IAAIA,IAAI,CAACC,WAAL,IAAoB,EAAxB,EAA4B;QACxBC,MAAM,CAACC,IAAP,CAAYH,IAAI,CAACC,WAAjB;MACH,CAFD,MAGK;QACD,KAAKhE,MAAL,CAAYmE,QAAZ,CAAqB,CAAC,2BAAD,CAArB;MACH;IACJ,CAPD;EAQH;;EAED1B,SAAS;IAAA;;IACL,KAAKtC,cAAL,CAAoBiE,WAApB,CAAgC5C,SAAhC;MAAA,6BAA0C,WAAM6C,WAAN,EAAoB;QAE1D/E,iBAAiB,CAAuB+E,WAAvB,EAAoC,KAAI,CAACpE,GAAzC,EAA8Cd,2BAA2B,CAACmF,sBAA1E,EAAkG,KAAI,CAACpE,QAAvG,CAAjB,CAAkIqE,IAAlI,CAAuIC,WAAW,IAAG;UAGjJ,KAAI,CAAC1F,oBAAL,GAA4B0F,WAA5B;;UACA,KAAI,CAACC,IAAL;QACH,CALD,EAKGC,KALH,CAKSC,CAAC,IAAIC,OAAO,CAACC,GAAR,CAAYF,CAAZ,CALd;MAQH,CAVD;;MAAA;QAAA;MAAA;IAAA;EAWH;;EAGDG,YAAY,CAACC,OAAD,EAAyB;IAEjC,IAAIA,OAAO,CAACC,MAAZ,EAAoB;MAEhB,IAAI,KAAKtC,IAAL,CAAUY,KAAV,CAAgBjD,cAApB,EAAoC;QAChC,IAAI,KAAKqC,IAAL,CAAUY,KAAV,CAAgBjD,cAAhB,IAAkC,YAAtC,EAAoD;UAChD,IAAI4E,KAAK,GAAG,CAAC,KAAKvC,IAAL,CAAUY,KAAV,CAAgB5C,WAAjB,GAA+B;YACvCF,QAAQ,EAAE;UAD6B,CAA/B,GAER,IAFJ;UAIA,OAAOyE,KAAP;QACH;MACJ;IACJ;;IAED,OAAO,IAAP;EAEH;;EAGDnC,cAAc,CAACO,YAAD,EAAeC,KAAf,EAAoB;IAC9B,KAAKF,aAAL,CAAmBC,YAAnB,EAAiCC,KAAjC;IAEA,KAAK4B,oBAAL,GAA4B,KAA5B;IACA,KAAKC,WAAL,CAAiBC,iBAAjB,GAAqC,KAArC;IACA,KAAKD,WAAL,CAAiBnG,WAAjB,GAA+B,IAA/B;IACA,KAAKmG,WAAL,CAAiBlG,YAAjB,GAAgC,KAAhC;IAEA,KAAKkG,WAAL,CAAiBE,UAAjB,GAA8B,KAAK3C,IAAL,CAAUC,QAAV,CAAmBrC,aAAnB,CAAiCgD,KAA/D;IACA,KAAK6B,WAAL,CAAiB5E,YAAjB,GAAgC,KAAKmC,IAAL,CAAUC,QAAV,CAAmBpC,YAAnB,CAAgC+C,KAAhE;;IAMA,IAAI,KAAK6B,WAAL,CAAiBE,UAAjB,IAA+B,KAAKF,WAAL,CAAiB5E,YAApD,EAAkE;MAE9D,KAAKN,GAAL,CAASqF,oBAAT,CAA8B,KAAKH,WAAnC,EAAgD3D,SAAhD,CAA0D+D,WAAW,IAAG;QACpE,KAAKJ,WAAL,CAAiBC,iBAAjB,GAAqCG,WAAW,CAACC,iBAAjD;QACA,KAAKN,oBAAL,GAA4B,IAA5B;MACH,CAHD;IAIH;EACJ;;EAEDO,MAAM;IACF,IAAI;MAEA,IAAI,KAAK/C,IAAL,CAAUgD,KAAd,EAAqB;QACjB,KAAKC,IAAL;MACH;IACJ,CALD,CAMA,OAAOC,EAAP,EAAW;MACPhB,OAAO,CAACK,KAAR,CAAcW,EAAd;IACH;EACJ;;EAEDD,IAAI;IACA,KAAK7G,oBAAL,CAA0BuB,cAA1B,GAA2C,KAAKqC,IAAL,CAAUC,QAAV,CAAmBtC,cAAnB,CAAkCiD,KAA7E;IACA,KAAKxE,oBAAL,CAA0BwB,aAA1B,GAA0C,KAAKoC,IAAL,CAAUC,QAAV,CAAmBrC,aAAnB,CAAiCgD,KAAjC,CAAuCuC,IAAvC,EAA1C;IACA,KAAK/G,oBAAL,CAA0ByB,YAA1B,GAAyC,KAAKmC,IAAL,CAAUC,QAAV,CAAmBpC,YAAnB,CAAgC+C,KAAzE;IACA,KAAKxE,oBAAL,CAA0B4B,WAA1B,GAAwC,KAAKgC,IAAL,CAAUC,QAAV,CAAmBjC,WAAnB,CAA+B4C,KAAvE;IACA,KAAKxE,oBAAL,CAA0B6B,WAA1B,GAAwC,KAAK+B,IAAL,CAAUC,QAAV,CAAmBhC,WAAnB,CAA+B2C,KAAvE;IACA,KAAKxE,oBAAL,CAA0B8B,SAA1B,GAAsC,KAAK8B,IAAL,CAAUC,QAAV,CAAmB/B,SAAnB,CAA6B0C,KAAnE;IACA,KAAKxE,oBAAL,CAA0B+B,aAA1B,GAA0C,KAAK6B,IAAL,CAAUC,QAAV,CAAmB9B,aAAnB,CAAiCyC,KAA3E;IACA,KAAKxE,oBAAL,CAA0BgC,iBAA1B,GAA8C,KAAK4B,IAAL,CAAUC,QAAV,CAAmB7B,iBAAnB,CAAqCwC,KAAnF;IACA,KAAKxE,oBAAL,CAA0BmC,gBAA1B,GAA6C,KAAKyB,IAAL,CAAUC,QAAV,CAAmB1B,gBAAnB,CAAoCqC,KAAjF;IACA,KAAKxE,oBAAL,CAA0BoC,OAA1B,GAAoC,KAAKwB,IAAL,CAAUC,QAAV,CAAmBzB,OAAnB,CAA2BoC,KAA/D;;IACA,IAAI,KAAKZ,IAAL,CAAUC,QAAV,CAAmB9B,aAAnB,CAAiCyC,KAArC,EAA4C;MACxC,KAAKxE,oBAAL,CAA0BgH,gBAA1B,GAA6C,KAAKpD,IAAL,CAAUC,QAAV,CAAmB9B,aAAnB,CAAiCyC,KAAjC,CAAuCyC,QAAvC,KAAoD,CAAjG;MACA,KAAKjH,oBAAL,CAA0BkH,gBAA1B,GAA6C,KAAKtD,IAAL,CAAUC,QAAV,CAAmB9B,aAAnB,CAAiCyC,KAAjC,CAAuC2C,OAAvC,EAA7C;MACA,KAAKnH,oBAAL,CAA0BoH,gBAA1B,GAA6C,KAAKxD,IAAL,CAAUC,QAAV,CAAmB9B,aAAnB,CAAiCyC,KAAjC,CAAuC6C,WAAvC,EAA7C;IACH;;IAGD,KAAKlG,GAAL,CAASmG,iBAAT,CAA2B;MAAEC,iBAAiB,EAAE,KAAKvH;IAA1B,CAA3B,EAA6E0C,SAA7E,CAAuF8E,CAAC,IAAG;MAEvF,KAAK5D,IAAL,CAAU6D,KAAV;IAEH,CAJD;EAKH;;EAGD9B,IAAI;IACA,KAAK/B,IAAL,CAAUC,QAAV,CAAmBtC,cAAnB,CAAkCmG,QAAlC,CAA2C,KAAK1H,oBAAL,CAA0BuB,cAArE;IACA,KAAKqC,IAAL,CAAUC,QAAV,CAAmBrC,aAAnB,CAAiCkG,QAAjC,CAA0C,KAAK1H,oBAAL,CAA0BwB,aAA1B,CAAwCuF,IAAxC,EAA1C;IACA,KAAKnD,IAAL,CAAUC,QAAV,CAAmBpC,YAAnB,CAAgCiG,QAAhC,CAAyC,KAAK1H,oBAAL,CAA0ByB,YAAnE;IACA,KAAKmC,IAAL,CAAUC,QAAV,CAAmBjC,WAAnB,CAA+B8F,QAA/B,CAAwC,KAAK1H,oBAAL,CAA0B4B,WAAlE,EAJA,CAKA;IACA;IACA;;IACA,KAAKgC,IAAL,CAAUC,QAAV,CAAmB/B,SAAnB,CAA6B4F,QAA7B,CAAsC,KAAK1H,oBAAL,CAA0B,WAA1B,CAAtC;IAEA,IAAI2H,aAAa,GAAG,KAAK3H,oBAAL,CAA0BgH,gBAA1B,GAA6C,GAA7C,GAAmD,KAAKhH,oBAAL,CAA0BkH,gBAA7E,GAAgG,GAAhG,GAAsG,KAAKlH,oBAAL,CAA0BoH,gBAApJ;IACAO,aAAa,GAAGA,aAAa,CAACC,KAAd,CAAoB,GAApB,EAAyBC,MAAzB,CAAgCL,CAAC,IAAIA,CAAC,CAACM,MAAF,GAAW,CAAhD,EAAmDA,MAAnD,GAA4D,CAA5D,GAAgEH,aAAhE,GAAgF,IAAhG;IACA,KAAK/D,IAAL,CAAUC,QAAV,CAAmB9B,aAAnB,CAAiC2F,QAAjC,CAA0CC,aAAa,GAAG,IAAII,IAAJ,CAASJ,aAAT,CAAH,GAA6B,IAApF;IACA,KAAK/D,IAAL,CAAUC,QAAV,CAAmB7B,iBAAnB,CAAqC0F,QAArC,CAA8C,KAAK1H,oBAAL,CAA0BgC,iBAAxE;IACA,KAAK4B,IAAL,CAAUC,QAAV,CAAmB1B,gBAAnB,CAAoCuF,QAApC,CAA6C,KAAK1H,oBAAL,CAA0BmC,gBAAvE;IACA,KAAKyB,IAAL,CAAUC,QAAV,CAAmBzB,OAAnB,CAA2BsF,QAA3B,CAAoC,KAAK1H,oBAAL,CAA0BoC,OAA9D;IACA,KAAKgE,oBAAL,GAA4B,IAA5B;IAEA,KAAK9B,aAAL,CAAmB,KAAnB,EAA0B,EAA1B;EACH;;EAED0D,qBAAqB;IAEjB,KAAK,IAAIC,CAAT,IAAc,KAAKrE,IAAL,CAAUC,QAAxB,EAAkC;MAC9B,KAAKD,IAAL,CAAUC,QAAV,CAAmBoE,CAAnB,EAAsBC,sBAAtB;IACH;EACJ;;EAEDC,mCAAmC,CAACC,SAAD,EAAqB;IACpD,IAAI,KAAKpI,oBAAL,CAA0BuB,cAA9B,EAA8C;MAC1C,IAAI,KAAKvB,oBAAL,CAA0BuB,cAA1B,IAA4C,YAAhD,EAA8D;QAC1D,OAAO,KAAKqC,IAAL,CAAUY,KAAV,CAAgB5C,WAAhB,GAA8B;UACjCF,QAAQ,EAAE;QADuB,CAA9B,GAEH,IAFJ;MAGH;IAEJ;;IACD,OAAO,IAAP;EACH;;AArPsC;;;;;;;;;;;;;;;;;;UAatCjC;IAAS4I,OAACvI,sBAAD,EAAyB;MAAEwI,MAAM,EAAE;IAAV,CAAzB;;;UACT7I;IAAS4I,OAACtI,gCAAD,EAAmC;MAAEuI,MAAM,EAAE;IAAV,CAAnC;;;AAdDvH,8BAA8B,eAJ1CvB,SAAS,CAAC;EACP+I,QAAQ,EAAE,wBADH;EAEPC;AAFO,CAAD,CAIiC,GAA9BzH,8BAA8B,CAA9B;SAAAA", "names": ["Component", "ViewChild", "Validators", "FormGroup", "FormControl", "FilingApiService", "StateSelectorComponent", "CorporationTypeSelectorComponent", "RetriveFieldDocument", "fork<PERSON><PERSON>n", "ProductCode", "CategoryCode", "SubCategoryCode", "FilingServiceResponseObject", "ActivatedRoute", "Router", "LoadFilingService", "FormModeService", "PDFValidate", "FilingInfoService", "CustomSharedValidations", "StatePriceRequest", "PageTitleService", "RetriveFieldDocumentsComponent", "constructor", "filingInfoService", "router", "Api", "formMode", "activatedRoute", "pageTitleService", "FormationState", "FormationType", "DocumentType", "required", "nullValidator", "CompanyName", "ContactName", "LLCNumber", "FormationDate", "CertifiedCopiesNo", "noDecimal", "maxNumberOfCopies", "CertifiedPlainNo", "Remarks", "specialInstructions", "ngOnInit", "get<PERSON><PERSON><PERSON>", "SubCatCode", "retrievefielddocs", "subscribe", "label", "setPageTitle", "getPrice", "res", "price", "basePrice", "fillData", "FCGetStates", "GetDocumentTypes", "GetFormationType", "response", "StateComponent", "States", "TypeOfDocuments", "CorpTypeComponent", "CorporationTypes", "StartLoad", "Form", "controls", "valueChanges", "selected<PERSON><PERSON><PERSON>", "IsPDFAvailable", "$OnStateSelection", "selectedState", "request", "State", "GetStateWisePrice", "getStatePrice", "isEntityType", "value", "corpType", "documentType", "EntityCode", "OptionCode", "clickOnDownload", "GetPDFUrl", "FS", "otherfiling", "data", "downloadUrl", "window", "open", "navigate", "queryParams", "queryString", "RetrieveFieldDocuments", "then", "serviceData", "Load", "catch", "e", "console", "log", "urlValidator", "control", "parent", "error", "isPDFRequestComplete", "pdfValidate", "IsPDFDownloadable", "EntityType", "CheckPDFDownloadable", "validatePdf", "isPDFDownloadable", "OnSave", "valid", "Save", "ex", "trim", "Formation_date_m", "getMonth", "Formation_date_d", "getDate", "Formation_date_y", "getFullYear", "SaveFilingService", "retrieveFieldDocs", "x", "reset", "setValue", "fromationDate", "split", "filter", "length", "Date", "recalculatesValidator", "s", "updateValueAndValidity", "emailConditionallyRequiredValidator", "formGroup", "args", "static", "selector", "template"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\viatech\\Ecom\\client\\acs\\src\\app\\Modules\\FilingService\\Pages\\retrive-field-documents\\retrive-field-documents.component.ts"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { Validators, AbstractControl, FormGroup, FormControl } from '@angular/forms';\r\nimport { FilingApiService } from '../../Services/FilingApiService';\r\nimport { StateSelectorComponent } from 'src/app/Modules/Shared/Components/formComponents/stateSelector/state-selector.component';\r\nimport { CorporationTypeSelectorComponent } from 'src/app/Modules/Shared/Components/formComponents/corporation-type-selector/corporation-type-selector.component';\r\nimport { RetriveFieldDocument } from '../../Models/RetriveFieldDocument';\r\nimport { forkJoin } from 'rxjs';\r\nimport { ProductCode } from 'src/app/Modules/Shared/Enums/products-code.enum';\r\nimport { PDFDownloadURLModel } from 'src/app/Modules/Shared/Models/FilingService/PDFDownloadURLModel';\r\nimport { CategoryCode } from 'src/app/Modules/Shared/Enums/category-code.enum';\r\nimport { SubCategoryCode } from 'src/app/Modules/Shared/Enums/sub-category-code.enum';\r\nimport { FilingServiceResponseObject } from '../../Enums/filing-service-enum';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { LoadFilingService } from '../../Functions/filing-service-load.func';\r\nimport { FormModeService } from 'src/app/Modules/Shared/Services/Common/FormModeService';\r\nimport { PDFValidate } from '../../Models/PDFValidate';\r\nimport { FilingInfoService } from '../../Services/filing-price.service';\r\nimport { CustomSharedValidations } from 'src/app/Modules/Shared/functions/custom-validations';\r\nimport { StatePriceRequest } from 'src/app/Modules/Shared/Models/Common/state-price-request.model';\r\nimport { PageTitleService } from 'src/app/Modules/Shared/Services/Common/page-title.service';\r\n\r\n@Component({\r\n    selector: 'retrive-field-document',\r\n    templateUrl: 'retrive-field-documents.component.html'\r\n})\r\nexport class RetriveFieldDocumentsComponent implements OnInit {\r\n\r\n    constructor(public filingInfoService: FilingInfoService, private router: Router, private Api: FilingApiService, public formMode: FormModeService, private activatedRoute: ActivatedRoute, private pageTitleService: PageTitleService) { }\r\n\r\n    selectedState: string;\r\n    RetriveFieldDocument: RetriveFieldDocument = new RetriveFieldDocument();\r\n    California = \"CA\"\r\n    TypeOfDocuments: any[] = [];\r\n    pdfValidate = new PDFValidate();\r\n    isPDFRequestComplete = false;\r\n    price: number;\r\n    basePrice: number;\r\n\r\n    @ViewChild(StateSelectorComponent, { static: true }) StateComponent: StateSelectorComponent;\r\n    @ViewChild(CorporationTypeSelectorComponent, { static: true }) CorpTypeComponent: CorporationTypeSelectorComponent;\r\n\r\n\r\n    Form = new FormGroup({\r\n\r\n        FormationState: new FormControl<string | null>(null, []),\r\n        FormationType: new FormControl<string | null>(null, []),\r\n        DocumentType: new FormControl<string | null>(null, [Validators.required, Validators.nullValidator]),\r\n        CompanyName: new FormControl<string | null>(null, [Validators.required]),\r\n        ContactName: new FormControl<string | null>(null),\r\n        LLCNumber: new FormControl<string | null>(null),\r\n        FormationDate: new FormControl<any | null>(null),\r\n        CertifiedCopiesNo: new FormControl<number | null>(null, [Validators.required, CustomSharedValidations.noDecimal, CustomSharedValidations.maxNumberOfCopies]),\r\n        CertifiedPlainNo: new FormControl<any | null>(null, [Validators.required, CustomSharedValidations.noDecimal, CustomSharedValidations.maxNumberOfCopies]),\r\n        Remarks: new FormControl<string | null>(null, [CustomSharedValidations.specialInstructions]),\r\n    })\r\n\r\n\r\n\r\n    ngOnInit() {\r\n        this.filingInfoService.getLabel(this.filingInfoService.SubCatCode.retrievefielddocs, 'FOT').subscribe(label => {\r\n            this.pageTitleService.setPageTitle(label);\r\n        });\r\n        this.filingInfoService.getPrice(this.filingInfoService.SubCatCode.retrievefielddocs).subscribe(res => {\r\n            this.price = res;\r\n            this.basePrice = res;\r\n        });\r\n        const fillData = forkJoin([\r\n            this.Api.FCGetStates(),\r\n            this.Api.GetDocumentTypes(),\r\n            this.Api.GetFormationType(\"910\")\r\n\r\n        ]\r\n        )\r\n\r\n        fillData.subscribe(response => {\r\n\r\n\r\n            this.StateComponent.States = response[0];\r\n            this.TypeOfDocuments = response[1];\r\n            this.CorpTypeComponent.CorporationTypes = response[2] || [];\r\n            this.StartLoad();\r\n        })\r\n\r\n\r\n        this.Form.controls.FormationType.valueChanges.subscribe(selectedEntity => {\r\n\r\n            this.IsPDFAvailable(true, selectedEntity)\r\n\r\n        })\r\n\r\n        this.StateComponent.$OnStateSelection.subscribe(selectedState => {\r\n            if (selectedState) {\r\n                var request = new StatePriceRequest();\r\n                request.ProductCode = 'FS';\r\n                request.CategoryCode = 'FOT';\r\n                request.SubCategoryCode = '910';\r\n                request.State = selectedState;\r\n                this.Api.GetStateWisePrice(request).subscribe(res => {\r\n                    this.price = res.price > 0 ? res.price : this.basePrice;\r\n                });\r\n            }\r\n        })\r\n\r\n    }\r\n\r\n    getStatePrice(isEntityType, value) {\r\n        var corpType = isEntityType ? value : this.Form.controls.FormationType.value;\r\n        var documentType = this.Form.controls.DocumentType.value;\r\n        if (corpType && documentType) {\r\n            var request = new StatePriceRequest();\r\n            request.ProductCode = 'FS';\r\n            request.CategoryCode = 'FOT';\r\n            request.SubCategoryCode = '910';\r\n            request.State = this.Form.controls.FormationState.value;\r\n            request.EntityCode = corpType || '';\r\n            request.OptionCode = documentType || '';\r\n\r\n            this.Api.GetStateWisePrice(request).subscribe(res => {\r\n                this.price = res.price > 0 ? res.price : this.basePrice;\r\n            });\r\n        }\r\n    }\r\n\r\n    clickOnDownload() {\r\n        this.selectedState = this.Form.controls.FormationState.value\r\n        this.Api.GetPDFUrl(ProductCode.FS, this.selectedState, CategoryCode.otherfiling, SubCategoryCode.retrievefielddocs).subscribe((data: PDFDownloadURLModel) => {\r\n            if (data.downloadUrl != \"\") {\r\n                window.open(data.downloadUrl)\r\n            }\r\n            else {\r\n                this.router.navigate(['/filingservice/error-page'])\r\n            }\r\n        })\r\n    }\r\n\r\n    StartLoad() {\r\n        this.activatedRoute.queryParams.subscribe(async queryString => {\r\n\r\n            LoadFilingService<RetriveFieldDocument>(queryString, this.Api, FilingServiceResponseObject.RetrieveFieldDocuments, this.formMode).then(serviceData => {\r\n\r\n\r\n                this.RetriveFieldDocument = serviceData;\r\n                this.Load();\r\n            }).catch(e => console.log(e))\r\n\r\n\r\n        })\r\n    }\r\n\r\n\r\n    urlValidator(control: AbstractControl) {\r\n\r\n        if (control.parent) {\r\n\r\n            if (this.Form.value.FormationState) {\r\n                if (this.Form.value.FormationState == 'California') {\r\n                    let error = !this.Form.value.CompanyName ? {\r\n                        required: \"Company Name Required\",\r\n                    } : null;\r\n\r\n                    return error;\r\n                }\r\n            }\r\n        }\r\n\r\n        return null;\r\n\r\n    }\r\n\r\n\r\n    IsPDFAvailable(isEntityType, value) {\r\n        this.getStatePrice(isEntityType, value);\r\n\r\n        this.isPDFRequestComplete = false\r\n        this.pdfValidate.IsPDFDownloadable = false;\r\n        this.pdfValidate.ProductCode = 'FS'\r\n        this.pdfValidate.CategoryCode = '910'\r\n\r\n        this.pdfValidate.EntityType = this.Form.controls.FormationType.value\r\n        this.pdfValidate.DocumentType = this.Form.controls.DocumentType.value\r\n\r\n\r\n\r\n\r\n\r\n        if (this.pdfValidate.EntityType && this.pdfValidate.DocumentType) {\r\n\r\n            this.Api.CheckPDFDownloadable(this.pdfValidate).subscribe(validatePdf => {\r\n                this.pdfValidate.IsPDFDownloadable = validatePdf.isPDFDownloadable;\r\n                this.isPDFRequestComplete = true;\r\n            })\r\n        }\r\n    }\r\n\r\n    OnSave() {\r\n        try {\r\n\r\n            if (this.Form.valid) {\r\n                this.Save();\r\n            }\r\n        }\r\n        catch (ex) {\r\n            console.error(ex)\r\n        }\r\n    }\r\n\r\n    Save() {\r\n        this.RetriveFieldDocument.FormationState = this.Form.controls.FormationState.value;\r\n        this.RetriveFieldDocument.FormationType = this.Form.controls.FormationType.value.trim();\r\n        this.RetriveFieldDocument.DocumentType = this.Form.controls.DocumentType.value;\r\n        this.RetriveFieldDocument.CompanyName = this.Form.controls.CompanyName.value;\r\n        this.RetriveFieldDocument.ContactName = this.Form.controls.ContactName.value;\r\n        this.RetriveFieldDocument.LLCNumber = this.Form.controls.LLCNumber.value;\r\n        this.RetriveFieldDocument.FormationDate = this.Form.controls.FormationDate.value;\r\n        this.RetriveFieldDocument.CertifiedCopiesNo = this.Form.controls.CertifiedCopiesNo.value;\r\n        this.RetriveFieldDocument.CertifiedPlainNo = this.Form.controls.CertifiedPlainNo.value;\r\n        this.RetriveFieldDocument.Remarks = this.Form.controls.Remarks.value;\r\n        if (this.Form.controls.FormationDate.value) {\r\n            this.RetriveFieldDocument.Formation_date_m = this.Form.controls.FormationDate.value.getMonth() + 1;\r\n            this.RetriveFieldDocument.Formation_date_d = this.Form.controls.FormationDate.value.getDate();\r\n            this.RetriveFieldDocument.Formation_date_y = this.Form.controls.FormationDate.value.getFullYear();\r\n        }\r\n\r\n\r\n        this.Api.SaveFilingService({ retrieveFieldDocs: this.RetriveFieldDocument }).subscribe(x => {\r\n\r\n            this.Form.reset();\r\n\r\n        })\r\n    }\r\n\r\n\r\n    Load() {\r\n        this.Form.controls.FormationState.setValue(this.RetriveFieldDocument.FormationState)\r\n        this.Form.controls.FormationType.setValue(this.RetriveFieldDocument.FormationType.trim())\r\n        this.Form.controls.DocumentType.setValue(this.RetriveFieldDocument.DocumentType)\r\n        this.Form.controls.CompanyName.setValue(this.RetriveFieldDocument.CompanyName)\r\n        // this.Form.controls.ContactName.setValue(this.RetriveFieldDocument.ContactName)\r\n        // this.Form.controls.Phone.setValue(this.RetriveFieldDocument.Phone)\r\n        // this.Form.controls.Email.setValue(this.RetriveFieldDocument.Email)\r\n        this.Form.controls.LLCNumber.setValue(this.RetriveFieldDocument[\"LlcNumber\"])\r\n\r\n        let fromationDate = this.RetriveFieldDocument.Formation_date_m + \"-\" + this.RetriveFieldDocument.Formation_date_d + \"-\" + this.RetriveFieldDocument.Formation_date_y;\r\n        fromationDate = fromationDate.split('-').filter(x => x.length > 0).length > 0 ? fromationDate : null;\r\n        this.Form.controls.FormationDate.setValue(fromationDate ? new Date(fromationDate) : null)\r\n        this.Form.controls.CertifiedCopiesNo.setValue(this.RetriveFieldDocument.CertifiedCopiesNo)\r\n        this.Form.controls.CertifiedPlainNo.setValue(this.RetriveFieldDocument.CertifiedPlainNo)\r\n        this.Form.controls.Remarks.setValue(this.RetriveFieldDocument.Remarks)\r\n        this.isPDFRequestComplete = true;\r\n\r\n        this.getStatePrice(false, '');\r\n    }\r\n\r\n    recalculatesValidator() {\r\n\r\n        for (let s in this.Form.controls) {\r\n            this.Form.controls[s].updateValueAndValidity();\r\n        }\r\n    }\r\n\r\n    emailConditionallyRequiredValidator(formGroup: FormGroup) {\r\n        if (this.RetriveFieldDocument.FormationState) {\r\n            if (this.RetriveFieldDocument.FormationState == 'California') {\r\n                return this.Form.value.CompanyName ? {\r\n                    required: \"Company Name Required\",\r\n                } : null;\r\n            }\r\n\r\n        }\r\n        return null;\r\n    }\r\n}\r\n\r\n"]}, "metadata": {}, "sourceType": "module"}