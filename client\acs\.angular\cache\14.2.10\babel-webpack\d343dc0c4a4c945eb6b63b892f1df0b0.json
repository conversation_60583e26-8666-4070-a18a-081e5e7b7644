{"ast": null, "code": "import { defer } from './defer';\nimport { EMPTY } from './empty';\nexport function iif(condition, trueResult = EMPTY, falseResult = EMPTY) {\n  return defer(() => condition() ? trueResult : falseResult);\n}", "map": {"version": 3, "names": ["defer", "EMPTY", "iif", "condition", "trueResult", "falseResult"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/iif.js"], "sourcesContent": ["import { defer } from './defer';\nimport { EMPTY } from './empty';\nexport function iif(condition, trueResult = EMPTY, falseResult = EMPTY) {\n    return defer(() => condition() ? trueResult : falseResult);\n}\n"], "mappings": "AAAA,SAASA,KAAT,QAAsB,SAAtB;AACA,SAASC,KAAT,QAAsB,SAAtB;AACA,OAAO,SAASC,GAAT,CAAaC,SAAb,EAAwBC,UAAU,GAAGH,KAArC,EAA4CI,WAAW,GAAGJ,KAA1D,EAAiE;EACpE,OAAOD,KAAK,CAAC,MAAMG,SAAS,KAAKC,UAAL,GAAkBC,WAAlC,CAAZ;AACH"}, "metadata": {}, "sourceType": "module"}