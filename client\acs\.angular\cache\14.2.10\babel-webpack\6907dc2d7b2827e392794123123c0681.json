{"ast": null, "code": "import { SimpleOuterSubscriber, SimpleInnerSubscriber, innerSubscribe } from '../innerSubscribe';\nexport function catchError(selector) {\n  return function catchErrorOperatorFunction(source) {\n    const operator = new CatchOperator(selector);\n    const caught = source.lift(operator);\n    return operator.caught = caught;\n  };\n}\n\nclass CatchOperator {\n  constructor(selector) {\n    this.selector = selector;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new CatchSubscriber(subscriber, this.selector, this.caught));\n  }\n\n}\n\nclass CatchSubscriber extends SimpleOuterSubscriber {\n  constructor(destination, selector, caught) {\n    super(destination);\n    this.selector = selector;\n    this.caught = caught;\n  }\n\n  error(err) {\n    if (!this.isStopped) {\n      let result;\n\n      try {\n        result = this.selector(err, this.caught);\n      } catch (err2) {\n        super.error(err2);\n        return;\n      }\n\n      this._unsubscribeAndRecycle();\n\n      const innerSubscriber = new SimpleInnerSubscriber(this);\n      this.add(innerSubscriber);\n      const innerSubscription = innerSubscribe(result, innerSubscriber);\n\n      if (innerSubscription !== innerSubscriber) {\n        this.add(innerSubscription);\n      }\n    }\n  }\n\n}", "map": {"version": 3, "names": ["SimpleOuterSubscriber", "SimpleInnerSubscriber", "innerSubscribe", "catchError", "selector", "catchErrorOperatorFunction", "source", "operator", "CatchOperator", "caught", "lift", "constructor", "call", "subscriber", "subscribe", "CatchSubscriber", "destination", "error", "err", "isStopped", "result", "err2", "_unsubscribeAndRecycle", "innerSubscriber", "add", "innerSubscription"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/catchError.js"], "sourcesContent": ["import { SimpleOuterSubscriber, SimpleInnerSubscriber, innerSubscribe } from '../innerSubscribe';\nexport function catchError(selector) {\n    return function catchErrorOperatorFunction(source) {\n        const operator = new CatchOperator(selector);\n        const caught = source.lift(operator);\n        return (operator.caught = caught);\n    };\n}\nclass CatchOperator {\n    constructor(selector) {\n        this.selector = selector;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new CatchSubscriber(subscriber, this.selector, this.caught));\n    }\n}\nclass CatchSubscriber extends SimpleOuterSubscriber {\n    constructor(destination, selector, caught) {\n        super(destination);\n        this.selector = selector;\n        this.caught = caught;\n    }\n    error(err) {\n        if (!this.isStopped) {\n            let result;\n            try {\n                result = this.selector(err, this.caught);\n            }\n            catch (err2) {\n                super.error(err2);\n                return;\n            }\n            this._unsubscribeAndRecycle();\n            const innerSubscriber = new SimpleInnerSubscriber(this);\n            this.add(innerSubscriber);\n            const innerSubscription = innerSubscribe(result, innerSubscriber);\n            if (innerSubscription !== innerSubscriber) {\n                this.add(innerSubscription);\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,qBAAT,EAAgCC,qBAAhC,EAAuDC,cAAvD,QAA6E,mBAA7E;AACA,OAAO,SAASC,UAAT,CAAoBC,QAApB,EAA8B;EACjC,OAAO,SAASC,0BAAT,CAAoCC,MAApC,EAA4C;IAC/C,MAAMC,QAAQ,GAAG,IAAIC,aAAJ,CAAkBJ,QAAlB,CAAjB;IACA,MAAMK,MAAM,GAAGH,MAAM,CAACI,IAAP,CAAYH,QAAZ,CAAf;IACA,OAAQA,QAAQ,CAACE,MAAT,GAAkBA,MAA1B;EACH,CAJD;AAKH;;AACD,MAAMD,aAAN,CAAoB;EAChBG,WAAW,CAACP,QAAD,EAAW;IAClB,KAAKA,QAAL,GAAgBA,QAAhB;EACH;;EACDQ,IAAI,CAACC,UAAD,EAAaP,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACQ,SAAP,CAAiB,IAAIC,eAAJ,CAAoBF,UAApB,EAAgC,KAAKT,QAArC,EAA+C,KAAKK,MAApD,CAAjB,CAAP;EACH;;AANe;;AAQpB,MAAMM,eAAN,SAA8Bf,qBAA9B,CAAoD;EAChDW,WAAW,CAACK,WAAD,EAAcZ,QAAd,EAAwBK,MAAxB,EAAgC;IACvC,MAAMO,WAAN;IACA,KAAKZ,QAAL,GAAgBA,QAAhB;IACA,KAAKK,MAAL,GAAcA,MAAd;EACH;;EACDQ,KAAK,CAACC,GAAD,EAAM;IACP,IAAI,CAAC,KAAKC,SAAV,EAAqB;MACjB,IAAIC,MAAJ;;MACA,IAAI;QACAA,MAAM,GAAG,KAAKhB,QAAL,CAAcc,GAAd,EAAmB,KAAKT,MAAxB,CAAT;MACH,CAFD,CAGA,OAAOY,IAAP,EAAa;QACT,MAAMJ,KAAN,CAAYI,IAAZ;QACA;MACH;;MACD,KAAKC,sBAAL;;MACA,MAAMC,eAAe,GAAG,IAAItB,qBAAJ,CAA0B,IAA1B,CAAxB;MACA,KAAKuB,GAAL,CAASD,eAAT;MACA,MAAME,iBAAiB,GAAGvB,cAAc,CAACkB,MAAD,EAASG,eAAT,CAAxC;;MACA,IAAIE,iBAAiB,KAAKF,eAA1B,EAA2C;QACvC,KAAKC,GAAL,CAASC,iBAAT;MACH;IACJ;EACJ;;AAxB+C"}, "metadata": {}, "sourceType": "module"}