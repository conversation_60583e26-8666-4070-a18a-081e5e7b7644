{"ast": null, "code": "import { Observable } from '../Observable';\nimport { isArray } from '../util/isArray';\nimport { isFunction } from '../util/isFunction';\nimport { map } from '../operators/map';\nexport function fromEventPattern(add<PERSON><PERSON><PERSON>, removeHandler, resultSelector) {\n  if (resultSelector) {\n    return fromEventPattern(addHand<PERSON>, removeHandler).pipe(map(args => isArray(args) ? resultSelector(...args) : resultSelector(args)));\n  }\n\n  return new Observable(subscriber => {\n    const handler = (...e) => subscriber.next(e.length === 1 ? e[0] : e);\n\n    let retValue;\n\n    try {\n      retValue = addHandler(handler);\n    } catch (err) {\n      subscriber.error(err);\n      return undefined;\n    }\n\n    if (!isFunction(removeHandler)) {\n      return undefined;\n    }\n\n    return () => removeHandler(handler, retValue);\n  });\n}", "map": {"version": 3, "names": ["Observable", "isArray", "isFunction", "map", "fromEventPattern", "add<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "resultSelector", "pipe", "args", "subscriber", "handler", "e", "next", "length", "retValue", "err", "error", "undefined"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/fromEventPattern.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { isArray } from '../util/isArray';\nimport { isFunction } from '../util/isFunction';\nimport { map } from '../operators/map';\nexport function fromEventPattern(add<PERSON><PERSON><PERSON>, removeHandler, resultSelector) {\n    if (resultSelector) {\n        return fromEventPattern(addHand<PERSON>, removeHandler).pipe(map(args => isArray(args) ? resultSelector(...args) : resultSelector(args)));\n    }\n    return new Observable(subscriber => {\n        const handler = (...e) => subscriber.next(e.length === 1 ? e[0] : e);\n        let retValue;\n        try {\n            retValue = addHandler(handler);\n        }\n        catch (err) {\n            subscriber.error(err);\n            return undefined;\n        }\n        if (!isFunction(removeHandler)) {\n            return undefined;\n        }\n        return () => removeHandler(handler, retValue);\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,OAAT,QAAwB,iBAAxB;AACA,SAASC,UAAT,QAA2B,oBAA3B;AACA,SAASC,GAAT,QAAoB,kBAApB;AACA,OAAO,SAASC,gBAAT,CAA0BC,UAA1B,EAAsCC,aAAtC,EAAqDC,cAArD,EAAqE;EACxE,IAAIA,cAAJ,EAAoB;IAChB,OAAOH,gBAAgB,CAACC,UAAD,EAAaC,aAAb,CAAhB,CAA4CE,IAA5C,CAAiDL,GAAG,CAACM,IAAI,IAAIR,OAAO,CAACQ,IAAD,CAAP,GAAgBF,cAAc,CAAC,GAAGE,IAAJ,CAA9B,GAA0CF,cAAc,CAACE,IAAD,CAAjE,CAApD,CAAP;EACH;;EACD,OAAO,IAAIT,UAAJ,CAAeU,UAAU,IAAI;IAChC,MAAMC,OAAO,GAAG,CAAC,GAAGC,CAAJ,KAAUF,UAAU,CAACG,IAAX,CAAgBD,CAAC,CAACE,MAAF,KAAa,CAAb,GAAiBF,CAAC,CAAC,CAAD,CAAlB,GAAwBA,CAAxC,CAA1B;;IACA,IAAIG,QAAJ;;IACA,IAAI;MACAA,QAAQ,GAAGV,UAAU,CAACM,OAAD,CAArB;IACH,CAFD,CAGA,OAAOK,GAAP,EAAY;MACRN,UAAU,CAACO,KAAX,CAAiBD,GAAjB;MACA,OAAOE,SAAP;IACH;;IACD,IAAI,CAAChB,UAAU,CAACI,aAAD,CAAf,EAAgC;MAC5B,OAAOY,SAAP;IACH;;IACD,OAAO,MAAMZ,aAAa,CAACK,OAAD,EAAUI,QAAV,CAA1B;EACH,CAdM,CAAP;AAeH"}, "metadata": {}, "sourceType": "module"}