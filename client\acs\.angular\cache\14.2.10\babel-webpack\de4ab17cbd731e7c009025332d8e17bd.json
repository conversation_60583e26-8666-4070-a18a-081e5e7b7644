{"ast": null, "code": "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "map": {"version": 3, "names": ["getMainAxisFromPlacement", "placement", "indexOf"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js"], "sourcesContent": ["export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}"], "mappings": "AAAA,eAAe,SAASA,wBAAT,CAAkCC,SAAlC,EAA6C;EAC1D,OAAO,CAAC,KAAD,EAAQ,QAAR,EAAkBC,OAAlB,CAA0BD,SAA1B,KAAwC,CAAxC,GAA4C,GAA5C,GAAkD,GAAzD;AACD"}, "metadata": {}, "sourceType": "module"}