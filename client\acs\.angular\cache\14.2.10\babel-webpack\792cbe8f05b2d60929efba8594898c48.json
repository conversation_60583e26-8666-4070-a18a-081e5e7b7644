{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./ucc-filing.component.html?ngResource\";\nimport { Component } from '@angular/core';\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { FilingApiService } from '../../Services/FilingApiService';\nimport { UCCFiling } from '../../Models/UCCFiling';\nimport { ActivatedRoute } from '@angular/router';\nimport { LoadFilingService } from '../../Functions/filing-service-load.func';\nimport { FilingServiceResponseObject } from '../../Enums/filing-service-enum';\nimport { FormModeService } from 'src/app/Modules/Shared/Services/Common/FormModeService';\nimport { CustomSharedValidations } from 'src/app/Modules/Shared/functions/custom-validations';\nimport { FilingInfoService } from '../../Services/filing-price.service';\nimport { StatePriceRequest } from 'src/app/Modules/Shared/Models/Common/state-price-request.model';\nimport { PageTitleService } from 'src/app/Modules/Shared/Services/Common/page-title.service';\nlet UCCFilingComponent = class UCCFilingComponent {\n  constructor(filingInfoService, Api, formMode, activatedRoute, pageTitleService) {\n    this.filingInfoService = filingInfoService;\n    this.Api = Api;\n    this.formMode = formMode;\n    this.activatedRoute = activatedRoute;\n    this.pageTitleService = pageTitleService;\n    this.SeceratoryOfStates = [];\n    this.UCCFiling = new UCCFiling();\n    this.IsLoad = false;\n    this.States = [];\n    this.Form = new FormGroup({\n      State: new FormControl(null, [Validators.required]),\n      Seceratory: new FormControl(null, [Validators.required]),\n      DebtorName: new FormControl(null, [Validators.required, CustomSharedValidations.alphaNumeric]),\n      ContactName: new FormControl(null, [Validators.required, CustomSharedValidations.alphaNumeric]),\n      DebtorFillingNumber: new FormControl(null),\n      PhoneNo: new FormControl(null, [Validators.required, CustomSharedValidations.phoneLength]),\n      Email: new FormControl(null, [Validators.required, Validators.email]),\n      CertifiedCopies: new FormControl(null, [Validators.required,, CustomSharedValidations.noDecimal, CustomSharedValidations.maxNumberOfCopies]),\n      PlainCopies: new FormControl(null, [Validators.required, CustomSharedValidations.noDecimal, CustomSharedValidations.maxNumberOfCopies]),\n      Remarks: new FormControl(null, [CustomSharedValidations.specialInstructions])\n    });\n  }\n\n  ngOnInit() {\n    this.filingInfoService.getLabel(this.filingInfoService.SubCatCode.uccfiling, 'FOT').subscribe(label => {\n      this.pageTitleService.setPageTitle(label);\n    });\n    this.filingInfoService.getPrice(this.filingInfoService.SubCatCode.uccfiling).subscribe(res => {\n      this.price = res;\n      this.basePrice = res;\n    });\n    this.Api.GetSeceratoryOfState().subscribe(response => {\n      this.SeceratoryOfStates = response;\n      this.StartLoad();\n    }); //Get all States from store if store is empty call states API\n\n    this.Api.FCGetStates().subscribe(States => {\n      this.States = States;\n    });\n  }\n\n  OnSave() {\n    try {\n      if (this.Form.valid) {\n        this.Save();\n      }\n    } catch (ex) {\n      console.error(ex);\n    }\n  }\n\n  Save() {\n    this.UCCFiling.State = this.Form.controls.State.value;\n    this.UCCFiling.Seceratory = this.Form.controls.Seceratory.value;\n    this.UCCFiling.DebtorName = this.Form.controls.DebtorName.value;\n    this.UCCFiling.ContactName = this.Form.controls.ContactName.value;\n    this.UCCFiling.PhoneNo = this.Form.controls.PhoneNo.value;\n    this.UCCFiling.Email = this.Form.controls.Email.value;\n    this.UCCFiling.CertifiedCopies = this.Form.controls.CertifiedCopies.value;\n    this.UCCFiling.PlainCopies = this.Form.controls.PlainCopies.value;\n    this.UCCFiling.Remarks = this.Form.controls.Remarks.value;\n    this.UCCFiling.DebtorFillingNumber = this.Form.controls.DebtorFillingNumber.value;\n    this.Api.SaveFilingService({\n      uCCFiling: this.UCCFiling\n    }).subscribe(x => {\n      this.Form.reset();\n    });\n  }\n\n  StartLoad() {\n    var _this = this;\n\n    this.activatedRoute.queryParams.subscribe( /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (queryString) {\n        LoadFilingService(queryString, _this.Api, FilingServiceResponseObject.UCCFiling, _this.formMode).then(serviceData => {\n          _this.UCCFiling = serviceData;\n\n          _this.Load();\n        }).catch(e => console.log(e));\n      });\n\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  }\n\n  Load() {\n    this.onStateChange(this.UCCFiling.State);\n    this.Form.controls.State.setValue(this.UCCFiling.State);\n    this.Form.controls.Seceratory.setValue(this.UCCFiling.Seceratory);\n    this.Form.controls.DebtorName.setValue(this.UCCFiling.DebtorName);\n    this.Form.controls.ContactName.setValue(this.UCCFiling.ContactName);\n    this.Form.controls.PhoneNo.setValue(this.UCCFiling.PhoneNo);\n    this.Form.controls.Email.setValue(this.UCCFiling.Email);\n    this.Form.controls.CertifiedCopies.setValue(this.UCCFiling.CertifiedCopies);\n    this.Form.controls.PlainCopies.setValue(this.UCCFiling.PlainCopies);\n    this.Form.controls.Remarks.setValue(this.UCCFiling.Remarks);\n    this.Form.controls.DebtorFillingNumber.setValue(this.UCCFiling.DebtorFillingNumber);\n    this.IsLoad = true;\n  }\n\n  onStateChange(selectedState) {\n    if (selectedState) {\n      var request = new StatePriceRequest();\n      request.ProductCode = 'FS';\n      request.CategoryCode = 'FOT';\n      request.SubCategoryCode = '906';\n      request.State = selectedState;\n      this.Api.GetStateWisePrice(request).subscribe(res => {\n        this.price = res.price > 0 ? res.price : this.basePrice;\n      });\n    }\n  }\n\n};\n\nUCCFilingComponent.ctorParameters = () => [{\n  type: FilingInfoService\n}, {\n  type: FilingApiService\n}, {\n  type: FormModeService\n}, {\n  type: ActivatedRoute\n}, {\n  type: PageTitleService\n}];\n\nUCCFilingComponent = __decorate([Component({\n  selector: 'app-ucc-filing',\n  template: __NG_CLI_RESOURCE__0\n})], UCCFilingComponent);\nexport { UCCFilingComponent };", "map": {"version": 3, "mappings": ";;;AAAA,SAASA,SAAT,QAAkC,eAAlC;AACA,SAASC,WAAT,EAAsBC,SAAtB,EAAiCC,UAAjC,QAAmD,gBAAnD;AACA,SAASC,gBAAT,QAAiC,iCAAjC;AACA,SAASC,SAAT,QAA0B,wBAA1B;AACA,SAASC,cAAT,QAA+B,iBAA/B;AACA,SAASC,iBAAT,QAAkC,0CAAlC;AACA,SAASC,2BAAT,QAA4C,iCAA5C;AACA,SAASC,eAAT,QAAgC,wDAAhC;AACA,SAASC,uBAAT,QAAwC,qDAAxC;AACA,SAASC,iBAAT,QAAkC,qCAAlC;AACA,SAASC,iBAAT,QAAkC,gEAAlC;AACA,SAASC,gBAAT,QAAiC,2DAAjC;IAMaC,kBAAkB,SAAlBA,kBAAkB;EAE3BC,YAAmBC,iBAAnB,EAAiEC,GAAjE,EAA+FC,QAA/F,EAAkIC,cAAlI,EAA0KC,gBAA1K,EAA4M;IAAzL;IAA8C;IAA8B;IAAmC;IAAwC;IAC1K,0BAA4B,EAA5B;IACA,iBAAuB,IAAIf,SAAJ,EAAvB;IACA,cAAkB,KAAlB;IACA,cAAgB,EAAhB;IA8BA,YAAO,IAAIH,SAAJ,CAAc;MACjBmB,KAAK,EAAE,IAAIpB,WAAJ,CAA+B,IAA/B,EAAqC,CAACE,UAAU,CAACmB,QAAZ,CAArC,CADU;MAEjBC,UAAU,EAAE,IAAItB,WAAJ,CAA+B,IAA/B,EAAqC,CAACE,UAAU,CAACmB,QAAZ,CAArC,CAFK;MAGjBE,UAAU,EAAE,IAAIvB,WAAJ,CAA+B,IAA/B,EAAqC,CAACE,UAAU,CAACmB,QAAZ,EAAsBZ,uBAAuB,CAACe,YAA9C,CAArC,CAHK;MAIjBC,WAAW,EAAE,IAAIzB,WAAJ,CAA+B,IAA/B,EAAqC,CAACE,UAAU,CAACmB,QAAZ,EAAsBZ,uBAAuB,CAACe,YAA9C,CAArC,CAJI;MAKjBE,mBAAmB,EAAE,IAAI1B,WAAJ,CAA4B,IAA5B,CALJ;MAMjB2B,OAAO,EAAE,IAAI3B,WAAJ,CAA+B,IAA/B,EAAqC,CAACE,UAAU,CAACmB,QAAZ,EAAsBZ,uBAAuB,CAACmB,WAA9C,CAArC,CANQ;MAOjBC,KAAK,EAAE,IAAI7B,WAAJ,CAA+B,IAA/B,EAAqC,CAACE,UAAU,CAACmB,QAAZ,EAAsBnB,UAAU,CAAC4B,KAAjC,CAArC,CAPU;MAQjBC,eAAe,EAAE,IAAI/B,WAAJ,CAA+B,IAA/B,EAAqC,CAACE,UAAU,CAACmB,QAAZ,GAAwBZ,uBAAuB,CAACuB,SAAhD,EAA2DvB,uBAAuB,CAACwB,iBAAnF,CAArC,CARA;MASjBC,WAAW,EAAE,IAAIlC,WAAJ,CAA+B,IAA/B,EAAqC,CAACE,UAAU,CAACmB,QAAZ,EAAsBZ,uBAAuB,CAACuB,SAA9C,EAAyDvB,uBAAuB,CAACwB,iBAAjF,CAArC,CATI;MAUjBE,OAAO,EAAE,IAAInC,WAAJ,CAA+B,IAA/B,EAAqC,CAACS,uBAAuB,CAAC2B,mBAAzB,CAArC;IAVQ,CAAd,CAAP;EAlCiN;;EAQjNC,QAAQ;IACJ,KAAKtB,iBAAL,CAAuBuB,QAAvB,CAAgC,KAAKvB,iBAAL,CAAuBwB,UAAvB,CAAkCC,SAAlE,EAA6E,KAA7E,EAAoFC,SAApF,CAA8FC,KAAK,IAAG;MAClG,KAAKvB,gBAAL,CAAsBwB,YAAtB,CAAmCD,KAAnC;IACH,CAFD;IAGA,KAAK3B,iBAAL,CAAuB6B,QAAvB,CAAgC,KAAK7B,iBAAL,CAAuBwB,UAAvB,CAAkCC,SAAlE,EAA6EC,SAA7E,CAAuFI,GAAG,IAAG;MACzF,KAAKC,KAAL,GAAaD,GAAb;MACA,KAAKE,SAAL,GAAiBF,GAAjB;IACH,CAHD;IAKA,KAAK7B,GAAL,CAASgC,oBAAT,GAAgCP,SAAhC,CAA0CQ,QAAQ,IAAG;MAEjD,KAAKC,kBAAL,GAA0BD,QAA1B;MAEA,KAAKE,SAAL;IACH,CALD,EATI,CAgBJ;;IAEA,KAAKnC,GAAL,CAASoC,WAAT,GAAuBX,SAAvB,CAAkCY,MAAD,IAAW;MACxC,KAAKA,MAAL,GAAcA,MAAd;IACH,CAFD;EAMH;;EAcDC,MAAM;IACF,IAAI;MAEA,IAAI,KAAKC,IAAL,CAAUC,KAAd,EAAqB;QACjB,KAAKC,IAAL;MACH;IACJ,CALD,CAMA,OAAOC,EAAP,EAAW;MACPC,OAAO,CAACC,KAAR,CAAcF,EAAd;IACH;EACJ;;EACDD,IAAI;IACA,KAAKrD,SAAL,CAAegB,KAAf,GAAuB,KAAKmC,IAAL,CAAUM,QAAV,CAAmBzC,KAAnB,CAAyB0C,KAAhD;IACA,KAAK1D,SAAL,CAAekB,UAAf,GAA4B,KAAKiC,IAAL,CAAUM,QAAV,CAAmBvC,UAAnB,CAA8BwC,KAA1D;IACA,KAAK1D,SAAL,CAAemB,UAAf,GAA4B,KAAKgC,IAAL,CAAUM,QAAV,CAAmBtC,UAAnB,CAA8BuC,KAA1D;IACA,KAAK1D,SAAL,CAAeqB,WAAf,GAA6B,KAAK8B,IAAL,CAAUM,QAAV,CAAmBpC,WAAnB,CAA+BqC,KAA5D;IACA,KAAK1D,SAAL,CAAeuB,OAAf,GAAyB,KAAK4B,IAAL,CAAUM,QAAV,CAAmBlC,OAAnB,CAA2BmC,KAApD;IACA,KAAK1D,SAAL,CAAeyB,KAAf,GAAuB,KAAK0B,IAAL,CAAUM,QAAV,CAAmBhC,KAAnB,CAAyBiC,KAAhD;IACA,KAAK1D,SAAL,CAAe2B,eAAf,GAAiC,KAAKwB,IAAL,CAAUM,QAAV,CAAmB9B,eAAnB,CAAmC+B,KAApE;IACA,KAAK1D,SAAL,CAAe8B,WAAf,GAA6B,KAAKqB,IAAL,CAAUM,QAAV,CAAmB3B,WAAnB,CAA+B4B,KAA5D;IACA,KAAK1D,SAAL,CAAe+B,OAAf,GAAyB,KAAKoB,IAAL,CAAUM,QAAV,CAAmB1B,OAAnB,CAA2B2B,KAApD;IACA,KAAK1D,SAAL,CAAesB,mBAAf,GAAqC,KAAK6B,IAAL,CAAUM,QAAV,CAAmBnC,mBAAnB,CAAuCoC,KAA5E;IAEA,KAAK9C,GAAL,CAAS+C,iBAAT,CAA2B;MAAEC,SAAS,EAAE,KAAK5D;IAAlB,CAA3B,EAA0DqC,SAA1D,CAAoEwB,CAAC,IAAG;MAEpE,KAAKV,IAAL,CAAUW,KAAV;IAEH,CAJD;EAKH;;EAGDf,SAAS;IAAA;;IACL,KAAKjC,cAAL,CAAoBiD,WAApB,CAAgC1B,SAAhC;MAAA,6BAA0C,WAAM2B,WAAN,EAAoB;QAE1D9D,iBAAiB,CAAY8D,WAAZ,EAAyB,KAAI,CAACpD,GAA9B,EAAmCT,2BAA2B,CAACH,SAA/D,EAA0E,KAAI,CAACa,QAA/E,CAAjB,CAA0GoD,IAA1G,CAA+GC,WAAW,IAAG;UAGzH,KAAI,CAAClE,SAAL,GAAiBkE,WAAjB;;UACA,KAAI,CAACC,IAAL;QACH,CALD,EAKGC,KALH,CAKSC,CAAC,IAAId,OAAO,CAACe,GAAR,CAAYD,CAAZ,CALd;MAQH,CAVD;;MAAA;QAAA;MAAA;IAAA;EAWH;;EAEDF,IAAI;IACA,KAAKI,aAAL,CAAmB,KAAKvE,SAAL,CAAegB,KAAlC;IACA,KAAKmC,IAAL,CAAUM,QAAV,CAAmBzC,KAAnB,CAAyBwD,QAAzB,CAAkC,KAAKxE,SAAL,CAAegB,KAAjD;IACA,KAAKmC,IAAL,CAAUM,QAAV,CAAmBvC,UAAnB,CAA8BsD,QAA9B,CAAuC,KAAKxE,SAAL,CAAekB,UAAtD;IACA,KAAKiC,IAAL,CAAUM,QAAV,CAAmBtC,UAAnB,CAA8BqD,QAA9B,CAAuC,KAAKxE,SAAL,CAAemB,UAAtD;IACA,KAAKgC,IAAL,CAAUM,QAAV,CAAmBpC,WAAnB,CAA+BmD,QAA/B,CAAwC,KAAKxE,SAAL,CAAeqB,WAAvD;IACA,KAAK8B,IAAL,CAAUM,QAAV,CAAmBlC,OAAnB,CAA2BiD,QAA3B,CAAoC,KAAKxE,SAAL,CAAeuB,OAAnD;IACA,KAAK4B,IAAL,CAAUM,QAAV,CAAmBhC,KAAnB,CAAyB+C,QAAzB,CAAkC,KAAKxE,SAAL,CAAeyB,KAAjD;IACA,KAAK0B,IAAL,CAAUM,QAAV,CAAmB9B,eAAnB,CAAmC6C,QAAnC,CAA4C,KAAKxE,SAAL,CAAe2B,eAA3D;IACA,KAAKwB,IAAL,CAAUM,QAAV,CAAmB3B,WAAnB,CAA+B0C,QAA/B,CAAwC,KAAKxE,SAAL,CAAe8B,WAAvD;IACA,KAAKqB,IAAL,CAAUM,QAAV,CAAmB1B,OAAnB,CAA2ByC,QAA3B,CAAoC,KAAKxE,SAAL,CAAe+B,OAAnD;IACA,KAAKoB,IAAL,CAAUM,QAAV,CAAmBnC,mBAAnB,CAAuCkD,QAAvC,CAAgD,KAAKxE,SAAL,CAAesB,mBAA/D;IAEA,KAAKmD,MAAL,GAAc,IAAd;EACH;;EAEDF,aAAa,CAACG,aAAD,EAAc;IACvB,IAAIA,aAAJ,EAAmB;MACf,IAAIC,OAAO,GAAG,IAAIpE,iBAAJ,EAAd;MACAoE,OAAO,CAACC,WAAR,GAAsB,IAAtB;MACAD,OAAO,CAACE,YAAR,GAAuB,KAAvB;MACAF,OAAO,CAACG,eAAR,GAA0B,KAA1B;MACAH,OAAO,CAAC3D,KAAR,GAAgB0D,aAAhB;MACA,KAAK9D,GAAL,CAASmE,iBAAT,CAA2BJ,OAA3B,EAAoCtC,SAApC,CAA8CI,GAAG,IAAG;QAChD,KAAKC,KAAL,GAAaD,GAAG,CAACC,KAAJ,GAAY,CAAZ,GAAgBD,GAAG,CAACC,KAApB,GAA4B,KAAKC,SAA9C;MACH,CAFD;IAGH;EACJ;;AAxH0B;;;;;;;;;;;;;;AAAlBlC,kBAAkB,eAJ9Bd,SAAS,CAAC;EACPqF,QAAQ,EAAE,gBADH;EAEPC;AAFO,CAAD,CAIqB,GAAlBxE,kBAAkB,CAAlB;SAAAA", "names": ["Component", "FormControl", "FormGroup", "Validators", "FilingApiService", "UCCFiling", "ActivatedRoute", "LoadFilingService", "FilingServiceResponseObject", "FormModeService", "CustomSharedValidations", "FilingInfoService", "StatePriceRequest", "PageTitleService", "UCCFilingComponent", "constructor", "filingInfoService", "Api", "formMode", "activatedRoute", "pageTitleService", "State", "required", "Seceratory", "Debtor<PERSON>ame", "alphaNumeric", "ContactName", "DebtorFillingNumber", "PhoneNo", "phoneLength", "Email", "email", "CertifiedCopies", "noDecimal", "maxNumberOfCopies", "PlainCopies", "Remarks", "specialInstructions", "ngOnInit", "get<PERSON><PERSON><PERSON>", "SubCatCode", "uccfiling", "subscribe", "label", "setPageTitle", "getPrice", "res", "price", "basePrice", "GetSeceratoryOfState", "response", "SeceratoryOfStates", "StartLoad", "FCGetStates", "States", "OnSave", "Form", "valid", "Save", "ex", "console", "error", "controls", "value", "SaveFilingService", "uCCFiling", "x", "reset", "queryParams", "queryString", "then", "serviceData", "Load", "catch", "e", "log", "onStateChange", "setValue", "IsLoad", "selectedState", "request", "ProductCode", "CategoryCode", "SubCategoryCode", "GetStateWisePrice", "selector", "template"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\viatech\\Ecom\\client\\acs\\src\\app\\Modules\\FilingService\\Pages\\ucc-filing\\ucc-filing.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport { FilingApiService } from '../../Services/FilingApiService';\r\nimport { UCCFiling } from '../../Models/UCCFiling';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { LoadFilingService } from '../../Functions/filing-service-load.func';\r\nimport { FilingServiceResponseObject } from '../../Enums/filing-service-enum';\r\nimport { FormModeService } from 'src/app/Modules/Shared/Services/Common/FormModeService';\r\nimport { CustomSharedValidations } from 'src/app/Modules/Shared/functions/custom-validations';\r\nimport { FilingInfoService } from '../../Services/filing-price.service';\r\nimport { StatePriceRequest } from 'src/app/Modules/Shared/Models/Common/state-price-request.model';\r\nimport { PageTitleService } from 'src/app/Modules/Shared/Services/Common/page-title.service';\r\n\r\n@Component({\r\n    selector: 'app-ucc-filing',\r\n    templateUrl: 'ucc-filing.component.html'\r\n})\r\nexport class UCCFilingComponent implements OnInit {\r\n\r\n    constructor(public filingInfoService: FilingInfoService, private Api: FilingApiService, public formMode: FormModeService, private activatedRoute: ActivatedRoute, private pageTitleService: PageTitleService) { }\r\n    SeceratoryOfStates: any[] = []\r\n    UCCFiling: UCCFiling = new UCCFiling();\r\n    IsLoad: boolean = false;\r\n    States: any[] = [];\r\n    price: number;\r\n    basePrice: number;\r\n\r\n    ngOnInit() {\r\n        this.filingInfoService.getLabel(this.filingInfoService.SubCatCode.uccfiling, 'FOT').subscribe(label => {\r\n            this.pageTitleService.setPageTitle(label);\r\n        });\r\n        this.filingInfoService.getPrice(this.filingInfoService.SubCatCode.uccfiling).subscribe(res => {\r\n            this.price = res;\r\n            this.basePrice = res;\r\n        });\r\n\r\n        this.Api.GetSeceratoryOfState().subscribe(response => {\r\n\r\n            this.SeceratoryOfStates = response;\r\n\r\n            this.StartLoad();\r\n        })\r\n\r\n        //Get all States from store if store is empty call states API\r\n\r\n        this.Api.FCGetStates().subscribe((States) => {\r\n            this.States = States;\r\n        });\r\n\r\n\r\n\r\n    }\r\n\r\n    Form = new FormGroup({\r\n        State: new FormControl<string | null>(null, [Validators.required]),\r\n        Seceratory: new FormControl<string | null>(null, [Validators.required]),\r\n        DebtorName: new FormControl<string | null>(null, [Validators.required, CustomSharedValidations.alphaNumeric]),\r\n        ContactName: new FormControl<string | null>(null, [Validators.required, CustomSharedValidations.alphaNumeric]),\r\n        DebtorFillingNumber: new FormControl<any | null>(null),\r\n        PhoneNo: new FormControl<string | null>(null, [Validators.required, CustomSharedValidations.phoneLength]),\r\n        Email: new FormControl<string | null>(null, [Validators.required, Validators.email]),\r\n        CertifiedCopies: new FormControl<string | null>(null, [Validators.required, , CustomSharedValidations.noDecimal, CustomSharedValidations.maxNumberOfCopies]),\r\n        PlainCopies: new FormControl<string | null>(null, [Validators.required, CustomSharedValidations.noDecimal, CustomSharedValidations.maxNumberOfCopies]),\r\n        Remarks: new FormControl<string | null>(null, [CustomSharedValidations.specialInstructions]),\r\n    })\r\n    OnSave() {\r\n        try {\r\n\r\n            if (this.Form.valid) {\r\n                this.Save();\r\n            }\r\n        }\r\n        catch (ex) {\r\n            console.error(ex)\r\n        }\r\n    }\r\n    Save() {\r\n        this.UCCFiling.State = this.Form.controls.State.value;\r\n        this.UCCFiling.Seceratory = this.Form.controls.Seceratory.value;\r\n        this.UCCFiling.DebtorName = this.Form.controls.DebtorName.value;\r\n        this.UCCFiling.ContactName = this.Form.controls.ContactName.value;\r\n        this.UCCFiling.PhoneNo = this.Form.controls.PhoneNo.value;\r\n        this.UCCFiling.Email = this.Form.controls.Email.value;\r\n        this.UCCFiling.CertifiedCopies = this.Form.controls.CertifiedCopies.value;\r\n        this.UCCFiling.PlainCopies = this.Form.controls.PlainCopies.value;\r\n        this.UCCFiling.Remarks = this.Form.controls.Remarks.value;\r\n        this.UCCFiling.DebtorFillingNumber = this.Form.controls.DebtorFillingNumber.value;\r\n\r\n        this.Api.SaveFilingService({ uCCFiling: this.UCCFiling }).subscribe(x => {\r\n\r\n            this.Form.reset();\r\n\r\n        })\r\n    }\r\n\r\n\r\n    StartLoad() {\r\n        this.activatedRoute.queryParams.subscribe(async queryString => {\r\n\r\n            LoadFilingService<UCCFiling>(queryString, this.Api, FilingServiceResponseObject.UCCFiling, this.formMode).then(serviceData => {\r\n\r\n\r\n                this.UCCFiling = serviceData;\r\n                this.Load();\r\n            }).catch(e => console.log(e))\r\n\r\n\r\n        })\r\n    }\r\n\r\n    Load() {\r\n        this.onStateChange(this.UCCFiling.State);\r\n        this.Form.controls.State.setValue(this.UCCFiling.State)\r\n        this.Form.controls.Seceratory.setValue(this.UCCFiling.Seceratory)\r\n        this.Form.controls.DebtorName.setValue(this.UCCFiling.DebtorName)\r\n        this.Form.controls.ContactName.setValue(this.UCCFiling.ContactName)\r\n        this.Form.controls.PhoneNo.setValue(this.UCCFiling.PhoneNo)\r\n        this.Form.controls.Email.setValue(this.UCCFiling.Email)\r\n        this.Form.controls.CertifiedCopies.setValue(this.UCCFiling.CertifiedCopies)\r\n        this.Form.controls.PlainCopies.setValue(this.UCCFiling.PlainCopies)\r\n        this.Form.controls.Remarks.setValue(this.UCCFiling.Remarks)\r\n        this.Form.controls.DebtorFillingNumber.setValue(this.UCCFiling.DebtorFillingNumber)\r\n\r\n        this.IsLoad = true;\r\n    }\r\n\r\n    onStateChange(selectedState) {\r\n        if (selectedState) {\r\n            var request = new StatePriceRequest();\r\n            request.ProductCode = 'FS';\r\n            request.CategoryCode = 'FOT';\r\n            request.SubCategoryCode = '906';\r\n            request.State = selectedState;\r\n            this.Api.GetStateWisePrice(request).subscribe(res => {\r\n                this.price = res.price > 0 ? res.price : this.basePrice;\r\n            });\r\n        }\r\n    }\r\n}"]}, "metadata": {}, "sourceType": "module"}