{"ast": null, "code": "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\n\nimport { log } from \"../utils/log.js\";\n\nvar WebSocketClient = /*#__PURE__*/function () {\n  /**\n   * @param {string} url\n   */\n  function WebSocketClient(url) {\n    _classCallCheck(this, WebSocketClient);\n\n    this.client = new WebSocket(url);\n\n    this.client.onerror = function (error) {\n      log.error(error);\n    };\n  }\n  /**\n   * @param {(...args: any[]) => void} f\n   */\n\n\n  _createClass(WebSocketClient, [{\n    key: \"onOpen\",\n    value: function onOpen(f) {\n      this.client.onopen = f;\n    }\n    /**\n     * @param {(...args: any[]) => void} f\n     */\n\n  }, {\n    key: \"onClose\",\n    value: function onClose(f) {\n      this.client.onclose = f;\n    } // call f with the message string as the first argument\n\n    /**\n     * @param {(...args: any[]) => void} f\n     */\n\n  }, {\n    key: \"onMessage\",\n    value: function onMessage(f) {\n      this.client.onmessage = function (e) {\n        f(e.data);\n      };\n    }\n  }]);\n\n  return WebSocketClient;\n}();\n\nexport { WebSocketClient as default };", "map": {"version": 3, "names": ["_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "prototype", "log", "WebSocketClient", "url", "client", "WebSocket", "onerror", "error", "value", "onOpen", "f", "onopen", "onClose", "onclose", "onMessage", "onmessage", "e", "data", "default"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/webpack-dev-server/client/clients/WebSocketClient.js"], "sourcesContent": ["function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\nimport { log } from \"../utils/log.js\";\n\nvar WebSocketClient = /*#__PURE__*/function () {\n  /**\n   * @param {string} url\n   */\n  function WebSocketClient(url) {\n    _classCallCheck(this, WebSocketClient);\n\n    this.client = new WebSocket(url);\n\n    this.client.onerror = function (error) {\n      log.error(error);\n    };\n  }\n  /**\n   * @param {(...args: any[]) => void} f\n   */\n\n\n  _createClass(WebSocketClient, [{\n    key: \"onOpen\",\n    value: function onOpen(f) {\n      this.client.onopen = f;\n    }\n    /**\n     * @param {(...args: any[]) => void} f\n     */\n\n  }, {\n    key: \"onClose\",\n    value: function onClose(f) {\n      this.client.onclose = f;\n    } // call f with the message string as the first argument\n\n    /**\n     * @param {(...args: any[]) => void} f\n     */\n\n  }, {\n    key: \"onMessage\",\n    value: function onMessage(f) {\n      this.client.onmessage = function (e) {\n        f(e.data);\n      };\n    }\n  }]);\n\n  return WebSocketClient;\n}();\n\nexport { WebSocketClient as default };"], "mappings": "AAAA,SAASA,eAAT,CAAyBC,QAAzB,EAAmCC,WAAnC,EAAgD;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAtB,CAAJ,EAAwC;IAAE,MAAM,IAAIC,SAAJ,CAAc,mCAAd,CAAN;EAA2D;AAAE;;AAEzJ,SAASC,iBAAT,CAA2BC,MAA3B,EAAmCC,KAAnC,EAA0C;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,KAAK,CAACE,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;IAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAD,CAAtB;IAA2BE,UAAU,CAACC,UAAX,GAAwBD,UAAU,CAACC,UAAX,IAAyB,KAAjD;IAAwDD,UAAU,CAACE,YAAX,GAA0B,IAA1B;IAAgC,IAAI,WAAWF,UAAf,EAA2BA,UAAU,CAACG,QAAX,GAAsB,IAAtB;IAA4BC,MAAM,CAACC,cAAP,CAAsBT,MAAtB,EAA8BI,UAAU,CAACM,GAAzC,EAA8CN,UAA9C;EAA4D;AAAE;;AAE7T,SAASO,YAAT,CAAsBd,WAAtB,EAAmCe,UAAnC,EAA+CC,WAA/C,EAA4D;EAAE,IAAID,UAAJ,EAAgBb,iBAAiB,CAACF,WAAW,CAACiB,SAAb,EAAwBF,UAAxB,CAAjB;EAAsD,IAAIC,WAAJ,EAAiBd,iBAAiB,CAACF,WAAD,EAAcgB,WAAd,CAAjB;EAA6CL,MAAM,CAACC,cAAP,CAAsBZ,WAAtB,EAAmC,WAAnC,EAAgD;IAAEU,QAAQ,EAAE;EAAZ,CAAhD;EAAsE,OAAOV,WAAP;AAAqB;;AAE7R,SAASkB,GAAT,QAAoB,iBAApB;;AAEA,IAAIC,eAAe,GAAG,aAAa,YAAY;EAC7C;AACF;AACA;EACE,SAASA,eAAT,CAAyBC,GAAzB,EAA8B;IAC5BtB,eAAe,CAAC,IAAD,EAAOqB,eAAP,CAAf;;IAEA,KAAKE,MAAL,GAAc,IAAIC,SAAJ,CAAcF,GAAd,CAAd;;IAEA,KAAKC,MAAL,CAAYE,OAAZ,GAAsB,UAAUC,KAAV,EAAiB;MACrCN,GAAG,CAACM,KAAJ,CAAUA,KAAV;IACD,CAFD;EAGD;EACD;AACF;AACA;;;EAGEV,YAAY,CAACK,eAAD,EAAkB,CAAC;IAC7BN,GAAG,EAAE,QADwB;IAE7BY,KAAK,EAAE,SAASC,MAAT,CAAgBC,CAAhB,EAAmB;MACxB,KAAKN,MAAL,CAAYO,MAAZ,GAAqBD,CAArB;IACD;IACD;AACJ;AACA;;EAPiC,CAAD,EAS3B;IACDd,GAAG,EAAE,SADJ;IAEDY,KAAK,EAAE,SAASI,OAAT,CAAiBF,CAAjB,EAAoB;MACzB,KAAKN,MAAL,CAAYS,OAAZ,GAAsBH,CAAtB;IACD,CAJA,CAIC;;IAEF;AACJ;AACA;;EARK,CAT2B,EAmB3B;IACDd,GAAG,EAAE,WADJ;IAEDY,KAAK,EAAE,SAASM,SAAT,CAAmBJ,CAAnB,EAAsB;MAC3B,KAAKN,MAAL,CAAYW,SAAZ,GAAwB,UAAUC,CAAV,EAAa;QACnCN,CAAC,CAACM,CAAC,CAACC,IAAH,CAAD;MACD,CAFD;IAGD;EANA,CAnB2B,CAAlB,CAAZ;;EA4BA,OAAOf,eAAP;AACD,CA/CkC,EAAnC;;AAiDA,SAASA,eAAe,IAAIgB,OAA5B"}, "metadata": {}, "sourceType": "module"}