{"ast": null, "code": "import * as i1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayModule } from '@angular/cdk/overlay';\nimport * as i4 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport * as i7 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Input, Optional, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i5 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i6 from '@angular/cdk/layout';\nimport { Breakpoints } from '@angular/cdk/layout';\nimport * as i3 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { ComponentPortal } from '@angular/cdk/portal';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { Subject } from 'rxjs';\nimport { takeUntil, take } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate, keyframes } from '@angular/animations';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Time in ms to throttle repositioning after scroll events. */\n\nconst _c0 = [\"tooltip\"];\nconst SCROLL_THROTTLE_MS = 20;\n/**\n * CSS class that will be attached to the overlay panel.\n * @deprecated\n * @breaking-change 13.0.0 remove this variable\n */\n\nconst TOOLTIP_PANEL_CLASS = 'mat-tooltip-panel';\nconst PANEL_CLASS = 'tooltip-panel';\n/** Options used to bind passive event listeners. */\n\nconst passiveListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/**\n * Time between the user putting the pointer on a tooltip\n * trigger and the long press event being fired.\n */\n\nconst LONGPRESS_DELAY = 500;\n/**\n * Creates an error to be thrown if the user supplied an invalid tooltip position.\n * @docs-private\n */\n\nfunction getMatTooltipInvalidPositionError(position) {\n  return Error(`Tooltip position \"${position}\" is invalid.`);\n}\n/** Injection token that determines the scroll handling while a tooltip is visible. */\n\n\nconst MAT_TOOLTIP_SCROLL_STRATEGY = new InjectionToken('mat-tooltip-scroll-strategy');\n/** @docs-private */\n\nfunction MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition({\n    scrollThrottle: SCROLL_THROTTLE_MS\n  });\n}\n/** @docs-private */\n\n\nconst MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_TOOLTIP_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY\n};\n/** Injection token to be used to override the default options for `matTooltip`. */\n\nconst MAT_TOOLTIP_DEFAULT_OPTIONS = new InjectionToken('mat-tooltip-default-options', {\n  providedIn: 'root',\n  factory: MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\n\nfunction MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    showDelay: 0,\n    hideDelay: 0,\n    touchendHideDelay: 1500\n  };\n}\n\nclass _MatTooltipBase {\n  constructor(_overlay, _elementRef, _scrollDispatcher, _viewContainerRef, _ngZone, _platform, _ariaDescriber, _focusMonitor, scrollStrategy, _dir, _defaultOptions, _document) {\n    this._overlay = _overlay;\n    this._elementRef = _elementRef;\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewContainerRef = _viewContainerRef;\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    this._ariaDescriber = _ariaDescriber;\n    this._focusMonitor = _focusMonitor;\n    this._dir = _dir;\n    this._defaultOptions = _defaultOptions;\n    this._position = 'below';\n    this._disabled = false;\n    this._viewInitialized = false;\n    this._pointerExitEventsInitialized = false;\n    this._viewportMargin = 8;\n    this._cssClassPrefix = 'mat';\n    this._showDelay = this._defaultOptions.showDelay;\n    this._hideDelay = this._defaultOptions.hideDelay;\n    /**\n     * How touch gestures should be handled by the tooltip. On touch devices the tooltip directive\n     * uses a long press gesture to show and hide, however it can conflict with the native browser\n     * gestures. To work around the conflict, Angular Material disables native gestures on the\n     * trigger, but that might not be desirable on particular elements (e.g. inputs and draggable\n     * elements). The different values for this option configure the touch event handling as follows:\n     * - `auto` - Enables touch gestures for all elements, but tries to avoid conflicts with native\n     *   browser gestures on particular elements. In particular, it allows text selection on inputs\n     *   and textareas, and preserves the native browser dragging on elements marked as `draggable`.\n     * - `on` - Enables touch gestures for all elements and disables native\n     *   browser gestures with no exceptions.\n     * - `off` - Disables touch gestures. Note that this will prevent the tooltip from\n     *   showing on touch devices.\n     */\n\n    this.touchGestures = 'auto';\n    this._message = '';\n    /** Manually-bound passive event listeners. */\n\n    this._passiveListeners = [];\n    /** Emits when the component is destroyed. */\n\n    this._destroyed = new Subject();\n    this._scrollStrategy = scrollStrategy;\n    this._document = _document;\n\n    if (_defaultOptions) {\n      if (_defaultOptions.position) {\n        this.position = _defaultOptions.position;\n      }\n\n      if (_defaultOptions.touchGestures) {\n        this.touchGestures = _defaultOptions.touchGestures;\n      }\n    }\n\n    _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n      if (this._overlayRef) {\n        this._updatePosition(this._overlayRef);\n      }\n    });\n  }\n  /** Allows the user to define the position of the tooltip relative to the parent element */\n\n\n  get position() {\n    return this._position;\n  }\n\n  set position(value) {\n    if (value !== this._position) {\n      this._position = value;\n\n      if (this._overlayRef) {\n        this._updatePosition(this._overlayRef);\n\n        this._tooltipInstance?.show(0);\n\n        this._overlayRef.updatePosition();\n      }\n    }\n  }\n  /** Disables the display of the tooltip. */\n\n\n  get disabled() {\n    return this._disabled;\n  }\n\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value); // If tooltip is disabled, hide immediately.\n\n    if (this._disabled) {\n      this.hide(0);\n    } else {\n      this._setupPointerEnterEventsIfNeeded();\n    }\n  }\n  /** The default delay in ms before showing the tooltip after show is called */\n\n\n  get showDelay() {\n    return this._showDelay;\n  }\n\n  set showDelay(value) {\n    this._showDelay = coerceNumberProperty(value);\n  }\n  /** The default delay in ms before hiding the tooltip after hide is called */\n\n\n  get hideDelay() {\n    return this._hideDelay;\n  }\n\n  set hideDelay(value) {\n    this._hideDelay = coerceNumberProperty(value);\n\n    if (this._tooltipInstance) {\n      this._tooltipInstance._mouseLeaveHideDelay = this._hideDelay;\n    }\n  }\n  /** The message to be displayed in the tooltip */\n\n\n  get message() {\n    return this._message;\n  }\n\n  set message(value) {\n    this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this._message, 'tooltip'); // If the message is not a string (e.g. number), convert it to a string and trim it.\n    // Must convert with `String(value)`, not `${value}`, otherwise Closure Compiler optimises\n    // away the string-conversion: https://github.com/angular/components/issues/20684\n\n\n    this._message = value != null ? String(value).trim() : '';\n\n    if (!this._message && this._isTooltipVisible()) {\n      this.hide(0);\n    } else {\n      this._setupPointerEnterEventsIfNeeded();\n\n      this._updateTooltipMessage();\n\n      this._ngZone.runOutsideAngular(() => {\n        // The `AriaDescriber` has some functionality that avoids adding a description if it's the\n        // same as the `aria-label` of an element, however we can't know whether the tooltip trigger\n        // has a data-bound `aria-label` or when it'll be set for the first time. We can avoid the\n        // issue by deferring the description by a tick so Angular has time to set the `aria-label`.\n        Promise.resolve().then(() => {\n          this._ariaDescriber.describe(this._elementRef.nativeElement, this.message, 'tooltip');\n        });\n      });\n    }\n  }\n  /** Classes to be passed to the tooltip. Supports the same syntax as `ngClass`. */\n\n\n  get tooltipClass() {\n    return this._tooltipClass;\n  }\n\n  set tooltipClass(value) {\n    this._tooltipClass = value;\n\n    if (this._tooltipInstance) {\n      this._setTooltipClass(this._tooltipClass);\n    }\n  }\n\n  ngAfterViewInit() {\n    // This needs to happen after view init so the initial values for all inputs have been set.\n    this._viewInitialized = true;\n\n    this._setupPointerEnterEventsIfNeeded();\n\n    this._focusMonitor.monitor(this._elementRef).pipe(takeUntil(this._destroyed)).subscribe(origin => {\n      // Note that the focus monitor runs outside the Angular zone.\n      if (!origin) {\n        this._ngZone.run(() => this.hide(0));\n      } else if (origin === 'keyboard') {\n        this._ngZone.run(() => this.show());\n      }\n    });\n  }\n  /**\n   * Dispose the tooltip when destroyed.\n   */\n\n\n  ngOnDestroy() {\n    const nativeElement = this._elementRef.nativeElement;\n    clearTimeout(this._touchstartTimeout);\n\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n\n      this._tooltipInstance = null;\n    } // Clean up the event listeners set in the constructor\n\n\n    this._passiveListeners.forEach(([event, listener]) => {\n      nativeElement.removeEventListener(event, listener, passiveListenerOptions);\n    });\n\n    this._passiveListeners.length = 0;\n\n    this._destroyed.next();\n\n    this._destroyed.complete();\n\n    this._ariaDescriber.removeDescription(nativeElement, this.message, 'tooltip');\n\n    this._focusMonitor.stopMonitoring(nativeElement);\n  }\n  /** Shows the tooltip after the delay in ms, defaults to tooltip-delay-show or 0ms if no input */\n\n\n  show(delay = this.showDelay) {\n    if (this.disabled || !this.message || this._isTooltipVisible()) {\n      this._tooltipInstance?._cancelPendingAnimations();\n      return;\n    }\n\n    const overlayRef = this._createOverlay();\n\n    this._detach();\n\n    this._portal = this._portal || new ComponentPortal(this._tooltipComponent, this._viewContainerRef);\n    const instance = this._tooltipInstance = overlayRef.attach(this._portal).instance;\n    instance._triggerElement = this._elementRef.nativeElement;\n    instance._mouseLeaveHideDelay = this._hideDelay;\n    instance.afterHidden().pipe(takeUntil(this._destroyed)).subscribe(() => this._detach());\n\n    this._setTooltipClass(this._tooltipClass);\n\n    this._updateTooltipMessage();\n\n    instance.show(delay);\n  }\n  /** Hides the tooltip after the delay in ms, defaults to tooltip-delay-hide or 0ms if no input */\n\n\n  hide(delay = this.hideDelay) {\n    const instance = this._tooltipInstance;\n\n    if (instance) {\n      if (instance.isVisible()) {\n        instance.hide(delay);\n      } else {\n        instance._cancelPendingAnimations();\n\n        this._detach();\n      }\n    }\n  }\n  /** Shows/hides the tooltip */\n\n\n  toggle() {\n    this._isTooltipVisible() ? this.hide() : this.show();\n  }\n  /** Returns true if the tooltip is currently visible to the user */\n\n\n  _isTooltipVisible() {\n    return !!this._tooltipInstance && this._tooltipInstance.isVisible();\n  }\n  /** Create the overlay config and position strategy */\n\n\n  _createOverlay() {\n    if (this._overlayRef) {\n      return this._overlayRef;\n    }\n\n    const scrollableAncestors = this._scrollDispatcher.getAncestorScrollContainers(this._elementRef); // Create connected position strategy that listens for scroll events to reposition.\n\n\n    const strategy = this._overlay.position().flexibleConnectedTo(this._elementRef).withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`).withFlexibleDimensions(false).withViewportMargin(this._viewportMargin).withScrollableContainers(scrollableAncestors);\n\n    strategy.positionChanges.pipe(takeUntil(this._destroyed)).subscribe(change => {\n      this._updateCurrentPositionClass(change.connectionPair);\n\n      if (this._tooltipInstance) {\n        if (change.scrollableViewProperties.isOverlayClipped && this._tooltipInstance.isVisible()) {\n          // After position changes occur and the overlay is clipped by\n          // a parent scrollable then close the tooltip.\n          this._ngZone.run(() => this.hide(0));\n        }\n      }\n    });\n    this._overlayRef = this._overlay.create({\n      direction: this._dir,\n      positionStrategy: strategy,\n      panelClass: `${this._cssClassPrefix}-${PANEL_CLASS}`,\n      scrollStrategy: this._scrollStrategy()\n    });\n\n    this._updatePosition(this._overlayRef);\n\n    this._overlayRef.detachments().pipe(takeUntil(this._destroyed)).subscribe(() => this._detach());\n\n    this._overlayRef.outsidePointerEvents().pipe(takeUntil(this._destroyed)).subscribe(() => this._tooltipInstance?._handleBodyInteraction());\n\n    this._overlayRef.keydownEvents().pipe(takeUntil(this._destroyed)).subscribe(event => {\n      if (this._isTooltipVisible() && event.keyCode === ESCAPE && !hasModifierKey(event)) {\n        event.preventDefault();\n        event.stopPropagation();\n\n        this._ngZone.run(() => this.hide(0));\n      }\n    });\n\n    if (this._defaultOptions?.disableTooltipInteractivity) {\n      this._overlayRef.addPanelClass(`${this._cssClassPrefix}-tooltip-panel-non-interactive`);\n    }\n\n    return this._overlayRef;\n  }\n  /** Detaches the currently-attached tooltip. */\n\n\n  _detach() {\n    if (this._overlayRef && this._overlayRef.hasAttached()) {\n      this._overlayRef.detach();\n    }\n\n    this._tooltipInstance = null;\n  }\n  /** Updates the position of the current tooltip. */\n\n\n  _updatePosition(overlayRef) {\n    const position = overlayRef.getConfig().positionStrategy;\n\n    const origin = this._getOrigin();\n\n    const overlay = this._getOverlayPosition();\n\n    position.withPositions([this._addOffset({ ...origin.main,\n      ...overlay.main\n    }), this._addOffset({ ...origin.fallback,\n      ...overlay.fallback\n    })]);\n  }\n  /** Adds the configured offset to a position. Used as a hook for child classes. */\n\n\n  _addOffset(position) {\n    return position;\n  }\n  /**\n   * Returns the origin position and a fallback position based on the user's position preference.\n   * The fallback position is the inverse of the origin (e.g. `'below' -> 'above'`).\n   */\n\n\n  _getOrigin() {\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    const position = this.position;\n    let originPosition;\n\n    if (position == 'above' || position == 'below') {\n      originPosition = {\n        originX: 'center',\n        originY: position == 'above' ? 'top' : 'bottom'\n      };\n    } else if (position == 'before' || position == 'left' && isLtr || position == 'right' && !isLtr) {\n      originPosition = {\n        originX: 'start',\n        originY: 'center'\n      };\n    } else if (position == 'after' || position == 'right' && isLtr || position == 'left' && !isLtr) {\n      originPosition = {\n        originX: 'end',\n        originY: 'center'\n      };\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getMatTooltipInvalidPositionError(position);\n    }\n\n    const {\n      x,\n      y\n    } = this._invertPosition(originPosition.originX, originPosition.originY);\n\n    return {\n      main: originPosition,\n      fallback: {\n        originX: x,\n        originY: y\n      }\n    };\n  }\n  /** Returns the overlay position and a fallback position based on the user's preference */\n\n\n  _getOverlayPosition() {\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    const position = this.position;\n    let overlayPosition;\n\n    if (position == 'above') {\n      overlayPosition = {\n        overlayX: 'center',\n        overlayY: 'bottom'\n      };\n    } else if (position == 'below') {\n      overlayPosition = {\n        overlayX: 'center',\n        overlayY: 'top'\n      };\n    } else if (position == 'before' || position == 'left' && isLtr || position == 'right' && !isLtr) {\n      overlayPosition = {\n        overlayX: 'end',\n        overlayY: 'center'\n      };\n    } else if (position == 'after' || position == 'right' && isLtr || position == 'left' && !isLtr) {\n      overlayPosition = {\n        overlayX: 'start',\n        overlayY: 'center'\n      };\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getMatTooltipInvalidPositionError(position);\n    }\n\n    const {\n      x,\n      y\n    } = this._invertPosition(overlayPosition.overlayX, overlayPosition.overlayY);\n\n    return {\n      main: overlayPosition,\n      fallback: {\n        overlayX: x,\n        overlayY: y\n      }\n    };\n  }\n  /** Updates the tooltip message and repositions the overlay according to the new message length */\n\n\n  _updateTooltipMessage() {\n    // Must wait for the message to be painted to the tooltip so that the overlay can properly\n    // calculate the correct positioning based on the size of the text.\n    if (this._tooltipInstance) {\n      this._tooltipInstance.message = this.message;\n\n      this._tooltipInstance._markForCheck();\n\n      this._ngZone.onMicrotaskEmpty.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n        if (this._tooltipInstance) {\n          this._overlayRef.updatePosition();\n        }\n      });\n    }\n  }\n  /** Updates the tooltip class */\n\n\n  _setTooltipClass(tooltipClass) {\n    if (this._tooltipInstance) {\n      this._tooltipInstance.tooltipClass = tooltipClass;\n\n      this._tooltipInstance._markForCheck();\n    }\n  }\n  /** Inverts an overlay position. */\n\n\n  _invertPosition(x, y) {\n    if (this.position === 'above' || this.position === 'below') {\n      if (y === 'top') {\n        y = 'bottom';\n      } else if (y === 'bottom') {\n        y = 'top';\n      }\n    } else {\n      if (x === 'end') {\n        x = 'start';\n      } else if (x === 'start') {\n        x = 'end';\n      }\n    }\n\n    return {\n      x,\n      y\n    };\n  }\n  /** Updates the class on the overlay panel based on the current position of the tooltip. */\n\n\n  _updateCurrentPositionClass(connectionPair) {\n    const {\n      overlayY,\n      originX,\n      originY\n    } = connectionPair;\n    let newPosition; // If the overlay is in the middle along the Y axis,\n    // it means that it's either before or after.\n\n    if (overlayY === 'center') {\n      // Note that since this information is used for styling, we want to\n      // resolve `start` and `end` to their real values, otherwise consumers\n      // would have to remember to do it themselves on each consumption.\n      if (this._dir && this._dir.value === 'rtl') {\n        newPosition = originX === 'end' ? 'left' : 'right';\n      } else {\n        newPosition = originX === 'start' ? 'left' : 'right';\n      }\n    } else {\n      newPosition = overlayY === 'bottom' && originY === 'top' ? 'above' : 'below';\n    }\n\n    if (newPosition !== this._currentPosition) {\n      const overlayRef = this._overlayRef;\n\n      if (overlayRef) {\n        const classPrefix = `${this._cssClassPrefix}-${PANEL_CLASS}-`;\n        overlayRef.removePanelClass(classPrefix + this._currentPosition);\n        overlayRef.addPanelClass(classPrefix + newPosition);\n      }\n\n      this._currentPosition = newPosition;\n    }\n  }\n  /** Binds the pointer events to the tooltip trigger. */\n\n\n  _setupPointerEnterEventsIfNeeded() {\n    // Optimization: Defer hooking up events if there's no message or the tooltip is disabled.\n    if (this._disabled || !this.message || !this._viewInitialized || this._passiveListeners.length) {\n      return;\n    } // The mouse events shouldn't be bound on mobile devices, because they can prevent the\n    // first tap from firing its click event or can cause the tooltip to open for clicks.\n\n\n    if (this._platformSupportsMouseEvents()) {\n      this._passiveListeners.push(['mouseenter', () => {\n        this._setupPointerExitEventsIfNeeded();\n\n        this.show();\n      }]);\n    } else if (this.touchGestures !== 'off') {\n      this._disableNativeGesturesIfNecessary();\n\n      this._passiveListeners.push(['touchstart', () => {\n        // Note that it's important that we don't `preventDefault` here,\n        // because it can prevent click events from firing on the element.\n        this._setupPointerExitEventsIfNeeded();\n\n        clearTimeout(this._touchstartTimeout);\n        this._touchstartTimeout = setTimeout(() => this.show(), LONGPRESS_DELAY);\n      }]);\n    }\n\n    this._addListeners(this._passiveListeners);\n  }\n\n  _setupPointerExitEventsIfNeeded() {\n    if (this._pointerExitEventsInitialized) {\n      return;\n    }\n\n    this._pointerExitEventsInitialized = true;\n    const exitListeners = [];\n\n    if (this._platformSupportsMouseEvents()) {\n      exitListeners.push(['mouseleave', event => {\n        const newTarget = event.relatedTarget;\n\n        if (!newTarget || !this._overlayRef?.overlayElement.contains(newTarget)) {\n          this.hide();\n        }\n      }], ['wheel', event => this._wheelListener(event)]);\n    } else if (this.touchGestures !== 'off') {\n      this._disableNativeGesturesIfNecessary();\n\n      const touchendListener = () => {\n        clearTimeout(this._touchstartTimeout);\n        this.hide(this._defaultOptions.touchendHideDelay);\n      };\n\n      exitListeners.push(['touchend', touchendListener], ['touchcancel', touchendListener]);\n    }\n\n    this._addListeners(exitListeners);\n\n    this._passiveListeners.push(...exitListeners);\n  }\n\n  _addListeners(listeners) {\n    listeners.forEach(([event, listener]) => {\n      this._elementRef.nativeElement.addEventListener(event, listener, passiveListenerOptions);\n    });\n  }\n\n  _platformSupportsMouseEvents() {\n    return !this._platform.IOS && !this._platform.ANDROID;\n  }\n  /** Listener for the `wheel` event on the element. */\n\n\n  _wheelListener(event) {\n    if (this._isTooltipVisible()) {\n      const elementUnderPointer = this._document.elementFromPoint(event.clientX, event.clientY);\n\n      const element = this._elementRef.nativeElement; // On non-touch devices we depend on the `mouseleave` event to close the tooltip, but it\n      // won't fire if the user scrolls away using the wheel without moving their cursor. We\n      // work around it by finding the element under the user's cursor and closing the tooltip\n      // if it's not the trigger.\n\n      if (elementUnderPointer !== element && !element.contains(elementUnderPointer)) {\n        this.hide();\n      }\n    }\n  }\n  /** Disables the native browser gestures, based on how the tooltip has been configured. */\n\n\n  _disableNativeGesturesIfNecessary() {\n    const gestures = this.touchGestures;\n\n    if (gestures !== 'off') {\n      const element = this._elementRef.nativeElement;\n      const style = element.style; // If gestures are set to `auto`, we don't disable text selection on inputs and\n      // textareas, because it prevents the user from typing into them on iOS Safari.\n\n      if (gestures === 'on' || element.nodeName !== 'INPUT' && element.nodeName !== 'TEXTAREA') {\n        style.userSelect = style.msUserSelect = style.webkitUserSelect = style.MozUserSelect = 'none';\n      } // If we have `auto` gestures and the element uses native HTML dragging,\n      // we don't set `-webkit-user-drag` because it prevents the native behavior.\n\n\n      if (gestures === 'on' || !element.draggable) {\n        style.webkitUserDrag = 'none';\n      }\n\n      style.touchAction = 'none';\n      style.webkitTapHighlightColor = 'transparent';\n    }\n  }\n\n}\n\n_MatTooltipBase.ɵfac = function _MatTooltipBase_Factory(t) {\n  i0.ɵɵinvalidFactory();\n};\n\n_MatTooltipBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTooltipBase,\n  inputs: {\n    position: [\"matTooltipPosition\", \"position\"],\n    disabled: [\"matTooltipDisabled\", \"disabled\"],\n    showDelay: [\"matTooltipShowDelay\", \"showDelay\"],\n    hideDelay: [\"matTooltipHideDelay\", \"hideDelay\"],\n    touchGestures: [\"matTooltipTouchGestures\", \"touchGestures\"],\n    message: [\"matTooltip\", \"message\"],\n    tooltipClass: [\"matTooltipClass\", \"tooltipClass\"]\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTooltipBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i1.Overlay\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i2.ScrollDispatcher\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i3.Platform\n    }, {\n      type: i4.AriaDescriber\n    }, {\n      type: i4.FocusMonitor\n    }, {\n      type: undefined\n    }, {\n      type: i5.Directionality\n    }, {\n      type: undefined\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, {\n    position: [{\n      type: Input,\n      args: ['matTooltipPosition']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['matTooltipDisabled']\n    }],\n    showDelay: [{\n      type: Input,\n      args: ['matTooltipShowDelay']\n    }],\n    hideDelay: [{\n      type: Input,\n      args: ['matTooltipHideDelay']\n    }],\n    touchGestures: [{\n      type: Input,\n      args: ['matTooltipTouchGestures']\n    }],\n    message: [{\n      type: Input,\n      args: ['matTooltip']\n    }],\n    tooltipClass: [{\n      type: Input,\n      args: ['matTooltipClass']\n    }]\n  });\n})();\n/**\n * Directive that attaches a material design tooltip to the host element. Animates the showing and\n * hiding of a tooltip provided position (defaults to below the element).\n *\n * https://material.io/design/components/tooltips.html\n */\n\n\nclass MatTooltip extends _MatTooltipBase {\n  constructor(overlay, elementRef, scrollDispatcher, viewContainerRef, ngZone, platform, ariaDescriber, focusMonitor, scrollStrategy, dir, defaultOptions, _document) {\n    super(overlay, elementRef, scrollDispatcher, viewContainerRef, ngZone, platform, ariaDescriber, focusMonitor, scrollStrategy, dir, defaultOptions, _document);\n    this._tooltipComponent = TooltipComponent;\n  }\n\n}\n\nMatTooltip.ɵfac = function MatTooltip_Factory(t) {\n  return new (t || MatTooltip)(i0.ɵɵdirectiveInject(i1.Overlay), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.ScrollDispatcher), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(i4.AriaDescriber), i0.ɵɵdirectiveInject(i4.FocusMonitor), i0.ɵɵdirectiveInject(MAT_TOOLTIP_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i5.Directionality, 8), i0.ɵɵdirectiveInject(MAT_TOOLTIP_DEFAULT_OPTIONS, 8), i0.ɵɵdirectiveInject(DOCUMENT));\n};\n\nMatTooltip.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatTooltip,\n  selectors: [[\"\", \"matTooltip\", \"\"]],\n  hostAttrs: [1, \"mat-tooltip-trigger\"],\n  exportAs: [\"matTooltip\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTooltip, [{\n    type: Directive,\n    args: [{\n      selector: '[matTooltip]',\n      exportAs: 'matTooltip',\n      host: {\n        'class': 'mat-tooltip-trigger'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i1.Overlay\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i2.ScrollDispatcher\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i3.Platform\n    }, {\n      type: i4.AriaDescriber\n    }, {\n      type: i4.FocusMonitor\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_TOOLTIP_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: i5.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_TOOLTIP_DEFAULT_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\nclass _TooltipComponentBase {\n  constructor(_changeDetectorRef, animationMode) {\n    this._changeDetectorRef = _changeDetectorRef;\n    /** Whether interactions on the page should close the tooltip */\n\n    this._closeOnInteraction = false;\n    /** Whether the tooltip is currently visible. */\n\n    this._isVisible = false;\n    /** Subject for notifying that the tooltip has been hidden from the view */\n\n    this._onHide = new Subject();\n    this._animationsDisabled = animationMode === 'NoopAnimations';\n  }\n  /**\n   * Shows the tooltip with an animation originating from the provided origin\n   * @param delay Amount of milliseconds to the delay showing the tooltip.\n   */\n\n\n  show(delay) {\n    // Cancel the delayed hide if it is scheduled\n    clearTimeout(this._hideTimeoutId);\n    this._showTimeoutId = setTimeout(() => {\n      this._toggleVisibility(true);\n\n      this._showTimeoutId = undefined;\n    }, delay);\n  }\n  /**\n   * Begins the animation to hide the tooltip after the provided delay in ms.\n   * @param delay Amount of milliseconds to delay showing the tooltip.\n   */\n\n\n  hide(delay) {\n    // Cancel the delayed show if it is scheduled\n    clearTimeout(this._showTimeoutId);\n    this._hideTimeoutId = setTimeout(() => {\n      this._toggleVisibility(false);\n\n      this._hideTimeoutId = undefined;\n    }, delay);\n  }\n  /** Returns an observable that notifies when the tooltip has been hidden from view. */\n\n\n  afterHidden() {\n    return this._onHide;\n  }\n  /** Whether the tooltip is being displayed. */\n\n\n  isVisible() {\n    return this._isVisible;\n  }\n\n  ngOnDestroy() {\n    this._cancelPendingAnimations();\n\n    this._onHide.complete();\n\n    this._triggerElement = null;\n  }\n  /**\n   * Interactions on the HTML body should close the tooltip immediately as defined in the\n   * material design spec.\n   * https://material.io/design/components/tooltips.html#behavior\n   */\n\n\n  _handleBodyInteraction() {\n    if (this._closeOnInteraction) {\n      this.hide(0);\n    }\n  }\n  /**\n   * Marks that the tooltip needs to be checked in the next change detection run.\n   * Mainly used for rendering the initial text before positioning a tooltip, which\n   * can be problematic in components with OnPush change detection.\n   */\n\n\n  _markForCheck() {\n    this._changeDetectorRef.markForCheck();\n  }\n\n  _handleMouseLeave({\n    relatedTarget\n  }) {\n    if (!relatedTarget || !this._triggerElement.contains(relatedTarget)) {\n      if (this.isVisible()) {\n        this.hide(this._mouseLeaveHideDelay);\n      } else {\n        this._finalizeAnimation(false);\n      }\n    }\n  }\n  /**\n   * Callback for when the timeout in this.show() gets completed.\n   * This method is only needed by the mdc-tooltip, and so it is only implemented\n   * in the mdc-tooltip, not here.\n   */\n\n\n  _onShow() {}\n  /** Event listener dispatched when an animation on the tooltip finishes. */\n\n\n  _handleAnimationEnd({\n    animationName\n  }) {\n    if (animationName === this._showAnimation || animationName === this._hideAnimation) {\n      this._finalizeAnimation(animationName === this._showAnimation);\n    }\n  }\n  /** Cancels any pending animation sequences. */\n\n\n  _cancelPendingAnimations() {\n    clearTimeout(this._showTimeoutId);\n    clearTimeout(this._hideTimeoutId);\n    this._showTimeoutId = this._hideTimeoutId = undefined;\n  }\n  /** Handles the cleanup after an animation has finished. */\n\n\n  _finalizeAnimation(toVisible) {\n    if (toVisible) {\n      this._closeOnInteraction = true;\n    } else if (!this.isVisible()) {\n      this._onHide.next();\n    }\n  }\n  /** Toggles the visibility of the tooltip element. */\n\n\n  _toggleVisibility(isVisible) {\n    // We set the classes directly here ourselves so that toggling the tooltip state\n    // isn't bound by change detection. This allows us to hide it even if the\n    // view ref has been detached from the CD tree.\n    const tooltip = this._tooltip.nativeElement;\n    const showClass = this._showAnimation;\n    const hideClass = this._hideAnimation;\n    tooltip.classList.remove(isVisible ? hideClass : showClass);\n    tooltip.classList.add(isVisible ? showClass : hideClass);\n    this._isVisible = isVisible; // It's common for internal apps to disable animations using `* { animation: none !important }`\n    // which can break the opening sequence. Try to detect such cases and work around them.\n\n    if (isVisible && !this._animationsDisabled && typeof getComputedStyle === 'function') {\n      const styles = getComputedStyle(tooltip); // Use `getPropertyValue` to avoid issues with property renaming.\n\n      if (styles.getPropertyValue('animation-duration') === '0s' || styles.getPropertyValue('animation-name') === 'none') {\n        this._animationsDisabled = true;\n      }\n    }\n\n    if (isVisible) {\n      this._onShow();\n    }\n\n    if (this._animationsDisabled) {\n      tooltip.classList.add('_mat-animation-noopable');\n\n      this._finalizeAnimation(isVisible);\n    }\n  }\n\n}\n\n_TooltipComponentBase.ɵfac = function _TooltipComponentBase_Factory(t) {\n  return new (t || _TooltipComponentBase)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n\n_TooltipComponentBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _TooltipComponentBase\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_TooltipComponentBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Internal component that wraps the tooltip's content.\n * @docs-private\n */\n\n\nclass TooltipComponent extends _TooltipComponentBase {\n  constructor(changeDetectorRef, _breakpointObserver, animationMode) {\n    super(changeDetectorRef, animationMode);\n    this._breakpointObserver = _breakpointObserver;\n    /** Stream that emits whether the user has a handset-sized display.  */\n\n    this._isHandset = this._breakpointObserver.observe(Breakpoints.Handset);\n    this._showAnimation = 'mat-tooltip-show';\n    this._hideAnimation = 'mat-tooltip-hide';\n  }\n\n}\n\nTooltipComponent.ɵfac = function TooltipComponent_Factory(t) {\n  return new (t || TooltipComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i6.BreakpointObserver), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n\nTooltipComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TooltipComponent,\n  selectors: [[\"mat-tooltip-component\"]],\n  viewQuery: function TooltipComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tooltip = _t.first);\n    }\n  },\n  hostAttrs: [\"aria-hidden\", \"true\"],\n  hostVars: 2,\n  hostBindings: function TooltipComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"mouseleave\", function TooltipComponent_mouseleave_HostBindingHandler($event) {\n        return ctx._handleMouseLeave($event);\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵstyleProp(\"zoom\", ctx.isVisible() ? 1 : null);\n    }\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 4,\n  vars: 6,\n  consts: [[1, \"mat-tooltip\", 3, \"ngClass\", \"animationend\"], [\"tooltip\", \"\"]],\n  template: function TooltipComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵlistener(\"animationend\", function TooltipComponent_Template_div_animationend_0_listener($event) {\n        return ctx._handleAnimationEnd($event);\n      });\n      i0.ɵɵpipe(2, \"async\");\n      i0.ɵɵtext(3);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      let tmp_0_0;\n      i0.ɵɵclassProp(\"mat-tooltip-handset\", (tmp_0_0 = i0.ɵɵpipeBind1(2, 4, ctx._isHandset)) == null ? null : tmp_0_0.matches);\n      i0.ɵɵproperty(\"ngClass\", ctx.tooltipClass);\n      i0.ɵɵadvance(3);\n      i0.ɵɵtextInterpolate(ctx.message);\n    }\n  },\n  dependencies: [i7.NgClass, i7.AsyncPipe],\n  styles: [\".mat-tooltip{color:#fff;border-radius:4px;margin:14px;max-width:250px;padding-left:8px;padding-right:8px;overflow:hidden;text-overflow:ellipsis;transform:scale(0)}.mat-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.cdk-high-contrast-active .mat-tooltip{outline:solid 1px}.mat-tooltip-handset{margin:24px;padding-left:16px;padding-right:16px}.mat-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-tooltip-show{0%{opacity:0;transform:scale(0)}50%{opacity:.5;transform:scale(0.99)}100%{opacity:1;transform:scale(1)}}@keyframes mat-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(1)}}.mat-tooltip-show{animation:mat-tooltip-show 200ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-tooltip-hide{animation:mat-tooltip-hide 100ms cubic-bezier(0, 0, 0.2, 1) forwards}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TooltipComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tooltip-component',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        // Forces the element to have a layout in IE and Edge. This fixes issues where the element\n        // won't be rendered if the animations are disabled or there is no web animations polyfill.\n        '[style.zoom]': 'isVisible() ? 1 : null',\n        '(mouseleave)': '_handleMouseLeave($event)',\n        'aria-hidden': 'true'\n      },\n      template: \"<div #tooltip\\n     class=\\\"mat-tooltip\\\"\\n     (animationend)=\\\"_handleAnimationEnd($event)\\\"\\n     [ngClass]=\\\"tooltipClass\\\"\\n     [class.mat-tooltip-handset]=\\\"(_isHandset | async)?.matches\\\">{{message}}</div>\\n\",\n      styles: [\".mat-tooltip{color:#fff;border-radius:4px;margin:14px;max-width:250px;padding-left:8px;padding-right:8px;overflow:hidden;text-overflow:ellipsis;transform:scale(0)}.mat-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.cdk-high-contrast-active .mat-tooltip{outline:solid 1px}.mat-tooltip-handset{margin:24px;padding-left:16px;padding-right:16px}.mat-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-tooltip-show{0%{opacity:0;transform:scale(0)}50%{opacity:.5;transform:scale(0.99)}100%{opacity:1;transform:scale(1)}}@keyframes mat-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(1)}}.mat-tooltip-show{animation:mat-tooltip-show 200ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-tooltip-hide{animation:mat-tooltip-hide 100ms cubic-bezier(0, 0, 0.2, 1) forwards}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i6.BreakpointObserver\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    _tooltip: [{\n      type: ViewChild,\n      args: ['tooltip', {\n        // Use a static query here since we interact directly with\n        // the DOM which can happen before `ngAfterViewInit`.\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatTooltipModule {}\n\nMatTooltipModule.ɵfac = function MatTooltipModule_Factory(t) {\n  return new (t || MatTooltipModule)();\n};\n\nMatTooltipModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatTooltipModule,\n  declarations: [MatTooltip, TooltipComponent],\n  imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule],\n  exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule]\n});\nMatTooltipModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER],\n  imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule, MatCommonModule, CdkScrollableModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTooltipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule],\n      exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule],\n      declarations: [MatTooltip, TooltipComponent],\n      providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Animations used by MatTooltip.\n * @docs-private\n */\n\n\nconst matTooltipAnimations = {\n  /** Animation that transitions a tooltip in and out. */\n  tooltipState: trigger('state', [state('initial, void, hidden', style({\n    opacity: 0,\n    transform: 'scale(0)'\n  })), state('visible', style({\n    transform: 'scale(1)'\n  })), transition('* => visible', animate('200ms cubic-bezier(0, 0, 0.2, 1)', keyframes([style({\n    opacity: 0,\n    transform: 'scale(0)',\n    offset: 0\n  }), style({\n    opacity: 0.5,\n    transform: 'scale(0.99)',\n    offset: 0.5\n  }), style({\n    opacity: 1,\n    transform: 'scale(1)',\n    offset: 1\n  })]))), transition('* => hidden', animate('100ms cubic-bezier(0, 0, 0.2, 1)', style({\n    opacity: 0\n  })))])\n};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_TOOLTIP_DEFAULT_OPTIONS, MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER, MatTooltip, MatTooltipModule, SCROLL_THROTTLE_MS, TOOLTIP_PANEL_CLASS, TooltipComponent, _MatTooltipBase, _TooltipComponentBase, getMatTooltipInvalidPositionError, matTooltipAnimations };", "map": {"version": 3, "names": ["i1", "Overlay", "OverlayModule", "i4", "A11yModule", "i7", "DOCUMENT", "CommonModule", "i0", "InjectionToken", "Directive", "Inject", "Input", "Optional", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ViewChild", "NgModule", "MatCommonModule", "i2", "CdkScrollableModule", "i5", "coerceBooleanProperty", "coerceNumberProperty", "ESCAPE", "hasModifierKey", "i6", "Breakpoints", "i3", "normalizePassiveListenerOptions", "ComponentPortal", "ANIMATION_MODULE_TYPE", "Subject", "takeUntil", "take", "trigger", "state", "style", "transition", "animate", "keyframes", "SCROLL_THROTTLE_MS", "TOOLTIP_PANEL_CLASS", "PANEL_CLASS", "passiveListenerOptions", "passive", "LONGPRESS_DELAY", "getMatTooltipInvalidPositionError", "position", "Error", "MAT_TOOLTIP_SCROLL_STRATEGY", "MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY", "overlay", "scrollStrategies", "reposition", "scrollThrottle", "MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER", "provide", "deps", "useFactory", "MAT_TOOLTIP_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY", "showDelay", "<PERSON><PERSON><PERSON><PERSON>", "touchendHideDelay", "_MatTooltipBase", "constructor", "_overlay", "_elementRef", "_scrollDispatcher", "_viewContainerRef", "_ngZone", "_platform", "_ariaDescriber", "_focusMonitor", "scrollStrategy", "_dir", "_defaultOptions", "_document", "_position", "_disabled", "_viewInitialized", "_pointerExitEventsInitialized", "_viewportMargin", "_cssClassPrefix", "_showDelay", "_hideDelay", "touchGestures", "_message", "_passiveListeners", "_destroyed", "_scrollStrategy", "change", "pipe", "subscribe", "_overlayRef", "_updatePosition", "value", "_tooltipInstance", "show", "updatePosition", "disabled", "hide", "_setupPointerEnterEventsIfNeeded", "_mouseLeaveHideDelay", "message", "removeDescription", "nativeElement", "String", "trim", "_isTooltipVisible", "_updateTooltipMessage", "runOutsideAngular", "Promise", "resolve", "then", "describe", "tooltipClass", "_tooltipClass", "_setTooltipClass", "ngAfterViewInit", "monitor", "origin", "run", "ngOnDestroy", "clearTimeout", "_touchstartTimeout", "dispose", "for<PERSON>ach", "event", "listener", "removeEventListener", "length", "next", "complete", "stopMonitoring", "delay", "_cancelPendingAnimations", "overlayRef", "_createOverlay", "_detach", "_portal", "_tooltipComponent", "instance", "attach", "_triggerElement", "afterHidden", "isVisible", "toggle", "scrollableAncestors", "getAncestorScrollContainers", "strategy", "flexibleConnectedTo", "withTransformOriginOn", "withFlexibleDimensions", "withViewportMargin", "withScrollableContainers", "position<PERSON><PERSON>es", "_updateCurrentPositionClass", "connectionPair", "scrollableViewProperties", "isOverlayClipped", "create", "direction", "positionStrategy", "panelClass", "detachments", "outsidePointerEvents", "_handleBodyInteraction", "keydownEvents", "keyCode", "preventDefault", "stopPropagation", "disableTooltipInteractivity", "addPanelClass", "has<PERSON>tta<PERSON>", "detach", "getConfig", "_get<PERSON><PERSON>in", "_getOverlayPosition", "withPositions", "_addOffset", "main", "fallback", "isLtr", "originPosition", "originX", "originY", "ngDevMode", "x", "y", "_invertPosition", "overlayPosition", "overlayX", "overlayY", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "onMicrotaskEmpty", "newPosition", "_currentPosition", "classPrefix", "removePanelClass", "_platformSupportsMouseEvents", "push", "_setupPointerExitEventsIfNeeded", "_disableNativeGesturesIfNecessary", "setTimeout", "_addListeners", "exitListeners", "newTarget", "relatedTarget", "overlayElement", "contains", "_wheelListener", "touchendListener", "listeners", "addEventListener", "IOS", "ANDROID", "elementUnderPointer", "elementFromPoint", "clientX", "clientY", "element", "gestures", "nodeName", "userSelect", "msUserSelect", "webkitUserSelect", "MozUserSelect", "draggable", "webkitUserDrag", "touchAction", "webkitTapHighlightColor", "ɵfac", "ɵdir", "type", "ElementRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewContainerRef", "NgZone", "Platform", "AriaDescriber", "FocusMonitor", "undefined", "Directionality", "decorators", "args", "MatTooltip", "elementRef", "scroll<PERSON><PERSON><PERSON>tcher", "viewContainerRef", "ngZone", "platform", "ariaDescriber", "focusMonitor", "dir", "defaultOptions", "TooltipComponent", "selector", "exportAs", "host", "_TooltipComponentBase", "_changeDetectorRef", "animationMode", "_closeOnInteraction", "_isVisible", "_onHide", "_animationsDisabled", "_hideTimeoutId", "_showTimeoutId", "_toggleVisibility", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_handleMouseLeave", "_finalizeAnimation", "_onShow", "_handleAnimationEnd", "animationName", "_showAnimation", "_hideAnimation", "toVisible", "tooltip", "_tooltip", "showClass", "hideClass", "classList", "remove", "add", "getComputedStyle", "styles", "getPropertyValue", "ChangeDetectorRef", "changeDetectorRef", "_breakpointObserver", "_isHandset", "observe", "Handset", "BreakpointObserver", "ɵcmp", "Ng<PERSON><PERSON>", "AsyncPipe", "encapsulation", "None", "changeDetection", "OnPush", "template", "static", "MatTooltipModule", "ɵmod", "ɵinj", "imports", "exports", "declarations", "providers", "matTooltipAnimations", "tooltipState", "opacity", "transform", "offset"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/material/fesm2020/tooltip.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayModule } from '@angular/cdk/overlay';\nimport * as i4 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport * as i7 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Input, Optional, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i5 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i6 from '@angular/cdk/layout';\nimport { Breakpoints } from '@angular/cdk/layout';\nimport * as i3 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { ComponentPortal } from '@angular/cdk/portal';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { Subject } from 'rxjs';\nimport { takeUntil, take } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate, keyframes } from '@angular/animations';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Time in ms to throttle repositioning after scroll events. */\nconst SCROLL_THROTTLE_MS = 20;\n/**\n * CSS class that will be attached to the overlay panel.\n * @deprecated\n * @breaking-change 13.0.0 remove this variable\n */\nconst TOOLTIP_PANEL_CLASS = 'mat-tooltip-panel';\nconst PANEL_CLASS = 'tooltip-panel';\n/** Options used to bind passive event listeners. */\nconst passiveListenerOptions = normalizePassiveListenerOptions({ passive: true });\n/**\n * Time between the user putting the pointer on a tooltip\n * trigger and the long press event being fired.\n */\nconst LONGPRESS_DELAY = 500;\n/**\n * Creates an error to be thrown if the user supplied an invalid tooltip position.\n * @docs-private\n */\nfunction getMatTooltipInvalidPositionError(position) {\n    return Error(`Tooltip position \"${position}\" is invalid.`);\n}\n/** Injection token that determines the scroll handling while a tooltip is visible. */\nconst MAT_TOOLTIP_SCROLL_STRATEGY = new InjectionToken('mat-tooltip-scroll-strategy');\n/** @docs-private */\nfunction MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition({ scrollThrottle: SCROLL_THROTTLE_MS });\n}\n/** @docs-private */\nconst MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n    provide: MAT_TOOLTIP_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY,\n};\n/** Injection token to be used to override the default options for `matTooltip`. */\nconst MAT_TOOLTIP_DEFAULT_OPTIONS = new InjectionToken('mat-tooltip-default-options', {\n    providedIn: 'root',\n    factory: MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY,\n});\n/** @docs-private */\nfunction MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        showDelay: 0,\n        hideDelay: 0,\n        touchendHideDelay: 1500,\n    };\n}\nclass _MatTooltipBase {\n    constructor(_overlay, _elementRef, _scrollDispatcher, _viewContainerRef, _ngZone, _platform, _ariaDescriber, _focusMonitor, scrollStrategy, _dir, _defaultOptions, _document) {\n        this._overlay = _overlay;\n        this._elementRef = _elementRef;\n        this._scrollDispatcher = _scrollDispatcher;\n        this._viewContainerRef = _viewContainerRef;\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        this._ariaDescriber = _ariaDescriber;\n        this._focusMonitor = _focusMonitor;\n        this._dir = _dir;\n        this._defaultOptions = _defaultOptions;\n        this._position = 'below';\n        this._disabled = false;\n        this._viewInitialized = false;\n        this._pointerExitEventsInitialized = false;\n        this._viewportMargin = 8;\n        this._cssClassPrefix = 'mat';\n        this._showDelay = this._defaultOptions.showDelay;\n        this._hideDelay = this._defaultOptions.hideDelay;\n        /**\n         * How touch gestures should be handled by the tooltip. On touch devices the tooltip directive\n         * uses a long press gesture to show and hide, however it can conflict with the native browser\n         * gestures. To work around the conflict, Angular Material disables native gestures on the\n         * trigger, but that might not be desirable on particular elements (e.g. inputs and draggable\n         * elements). The different values for this option configure the touch event handling as follows:\n         * - `auto` - Enables touch gestures for all elements, but tries to avoid conflicts with native\n         *   browser gestures on particular elements. In particular, it allows text selection on inputs\n         *   and textareas, and preserves the native browser dragging on elements marked as `draggable`.\n         * - `on` - Enables touch gestures for all elements and disables native\n         *   browser gestures with no exceptions.\n         * - `off` - Disables touch gestures. Note that this will prevent the tooltip from\n         *   showing on touch devices.\n         */\n        this.touchGestures = 'auto';\n        this._message = '';\n        /** Manually-bound passive event listeners. */\n        this._passiveListeners = [];\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        this._scrollStrategy = scrollStrategy;\n        this._document = _document;\n        if (_defaultOptions) {\n            if (_defaultOptions.position) {\n                this.position = _defaultOptions.position;\n            }\n            if (_defaultOptions.touchGestures) {\n                this.touchGestures = _defaultOptions.touchGestures;\n            }\n        }\n        _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n            if (this._overlayRef) {\n                this._updatePosition(this._overlayRef);\n            }\n        });\n    }\n    /** Allows the user to define the position of the tooltip relative to the parent element */\n    get position() {\n        return this._position;\n    }\n    set position(value) {\n        if (value !== this._position) {\n            this._position = value;\n            if (this._overlayRef) {\n                this._updatePosition(this._overlayRef);\n                this._tooltipInstance?.show(0);\n                this._overlayRef.updatePosition();\n            }\n        }\n    }\n    /** Disables the display of the tooltip. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        // If tooltip is disabled, hide immediately.\n        if (this._disabled) {\n            this.hide(0);\n        }\n        else {\n            this._setupPointerEnterEventsIfNeeded();\n        }\n    }\n    /** The default delay in ms before showing the tooltip after show is called */\n    get showDelay() {\n        return this._showDelay;\n    }\n    set showDelay(value) {\n        this._showDelay = coerceNumberProperty(value);\n    }\n    /** The default delay in ms before hiding the tooltip after hide is called */\n    get hideDelay() {\n        return this._hideDelay;\n    }\n    set hideDelay(value) {\n        this._hideDelay = coerceNumberProperty(value);\n        if (this._tooltipInstance) {\n            this._tooltipInstance._mouseLeaveHideDelay = this._hideDelay;\n        }\n    }\n    /** The message to be displayed in the tooltip */\n    get message() {\n        return this._message;\n    }\n    set message(value) {\n        this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this._message, 'tooltip');\n        // If the message is not a string (e.g. number), convert it to a string and trim it.\n        // Must convert with `String(value)`, not `${value}`, otherwise Closure Compiler optimises\n        // away the string-conversion: https://github.com/angular/components/issues/20684\n        this._message = value != null ? String(value).trim() : '';\n        if (!this._message && this._isTooltipVisible()) {\n            this.hide(0);\n        }\n        else {\n            this._setupPointerEnterEventsIfNeeded();\n            this._updateTooltipMessage();\n            this._ngZone.runOutsideAngular(() => {\n                // The `AriaDescriber` has some functionality that avoids adding a description if it's the\n                // same as the `aria-label` of an element, however we can't know whether the tooltip trigger\n                // has a data-bound `aria-label` or when it'll be set for the first time. We can avoid the\n                // issue by deferring the description by a tick so Angular has time to set the `aria-label`.\n                Promise.resolve().then(() => {\n                    this._ariaDescriber.describe(this._elementRef.nativeElement, this.message, 'tooltip');\n                });\n            });\n        }\n    }\n    /** Classes to be passed to the tooltip. Supports the same syntax as `ngClass`. */\n    get tooltipClass() {\n        return this._tooltipClass;\n    }\n    set tooltipClass(value) {\n        this._tooltipClass = value;\n        if (this._tooltipInstance) {\n            this._setTooltipClass(this._tooltipClass);\n        }\n    }\n    ngAfterViewInit() {\n        // This needs to happen after view init so the initial values for all inputs have been set.\n        this._viewInitialized = true;\n        this._setupPointerEnterEventsIfNeeded();\n        this._focusMonitor\n            .monitor(this._elementRef)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(origin => {\n            // Note that the focus monitor runs outside the Angular zone.\n            if (!origin) {\n                this._ngZone.run(() => this.hide(0));\n            }\n            else if (origin === 'keyboard') {\n                this._ngZone.run(() => this.show());\n            }\n        });\n    }\n    /**\n     * Dispose the tooltip when destroyed.\n     */\n    ngOnDestroy() {\n        const nativeElement = this._elementRef.nativeElement;\n        clearTimeout(this._touchstartTimeout);\n        if (this._overlayRef) {\n            this._overlayRef.dispose();\n            this._tooltipInstance = null;\n        }\n        // Clean up the event listeners set in the constructor\n        this._passiveListeners.forEach(([event, listener]) => {\n            nativeElement.removeEventListener(event, listener, passiveListenerOptions);\n        });\n        this._passiveListeners.length = 0;\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._ariaDescriber.removeDescription(nativeElement, this.message, 'tooltip');\n        this._focusMonitor.stopMonitoring(nativeElement);\n    }\n    /** Shows the tooltip after the delay in ms, defaults to tooltip-delay-show or 0ms if no input */\n    show(delay = this.showDelay) {\n        if (this.disabled || !this.message || this._isTooltipVisible()) {\n            this._tooltipInstance?._cancelPendingAnimations();\n            return;\n        }\n        const overlayRef = this._createOverlay();\n        this._detach();\n        this._portal =\n            this._portal || new ComponentPortal(this._tooltipComponent, this._viewContainerRef);\n        const instance = (this._tooltipInstance = overlayRef.attach(this._portal).instance);\n        instance._triggerElement = this._elementRef.nativeElement;\n        instance._mouseLeaveHideDelay = this._hideDelay;\n        instance\n            .afterHidden()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._detach());\n        this._setTooltipClass(this._tooltipClass);\n        this._updateTooltipMessage();\n        instance.show(delay);\n    }\n    /** Hides the tooltip after the delay in ms, defaults to tooltip-delay-hide or 0ms if no input */\n    hide(delay = this.hideDelay) {\n        const instance = this._tooltipInstance;\n        if (instance) {\n            if (instance.isVisible()) {\n                instance.hide(delay);\n            }\n            else {\n                instance._cancelPendingAnimations();\n                this._detach();\n            }\n        }\n    }\n    /** Shows/hides the tooltip */\n    toggle() {\n        this._isTooltipVisible() ? this.hide() : this.show();\n    }\n    /** Returns true if the tooltip is currently visible to the user */\n    _isTooltipVisible() {\n        return !!this._tooltipInstance && this._tooltipInstance.isVisible();\n    }\n    /** Create the overlay config and position strategy */\n    _createOverlay() {\n        if (this._overlayRef) {\n            return this._overlayRef;\n        }\n        const scrollableAncestors = this._scrollDispatcher.getAncestorScrollContainers(this._elementRef);\n        // Create connected position strategy that listens for scroll events to reposition.\n        const strategy = this._overlay\n            .position()\n            .flexibleConnectedTo(this._elementRef)\n            .withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`)\n            .withFlexibleDimensions(false)\n            .withViewportMargin(this._viewportMargin)\n            .withScrollableContainers(scrollableAncestors);\n        strategy.positionChanges.pipe(takeUntil(this._destroyed)).subscribe(change => {\n            this._updateCurrentPositionClass(change.connectionPair);\n            if (this._tooltipInstance) {\n                if (change.scrollableViewProperties.isOverlayClipped && this._tooltipInstance.isVisible()) {\n                    // After position changes occur and the overlay is clipped by\n                    // a parent scrollable then close the tooltip.\n                    this._ngZone.run(() => this.hide(0));\n                }\n            }\n        });\n        this._overlayRef = this._overlay.create({\n            direction: this._dir,\n            positionStrategy: strategy,\n            panelClass: `${this._cssClassPrefix}-${PANEL_CLASS}`,\n            scrollStrategy: this._scrollStrategy(),\n        });\n        this._updatePosition(this._overlayRef);\n        this._overlayRef\n            .detachments()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._detach());\n        this._overlayRef\n            .outsidePointerEvents()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._tooltipInstance?._handleBodyInteraction());\n        this._overlayRef\n            .keydownEvents()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(event => {\n            if (this._isTooltipVisible() && event.keyCode === ESCAPE && !hasModifierKey(event)) {\n                event.preventDefault();\n                event.stopPropagation();\n                this._ngZone.run(() => this.hide(0));\n            }\n        });\n        if (this._defaultOptions?.disableTooltipInteractivity) {\n            this._overlayRef.addPanelClass(`${this._cssClassPrefix}-tooltip-panel-non-interactive`);\n        }\n        return this._overlayRef;\n    }\n    /** Detaches the currently-attached tooltip. */\n    _detach() {\n        if (this._overlayRef && this._overlayRef.hasAttached()) {\n            this._overlayRef.detach();\n        }\n        this._tooltipInstance = null;\n    }\n    /** Updates the position of the current tooltip. */\n    _updatePosition(overlayRef) {\n        const position = overlayRef.getConfig().positionStrategy;\n        const origin = this._getOrigin();\n        const overlay = this._getOverlayPosition();\n        position.withPositions([\n            this._addOffset({ ...origin.main, ...overlay.main }),\n            this._addOffset({ ...origin.fallback, ...overlay.fallback }),\n        ]);\n    }\n    /** Adds the configured offset to a position. Used as a hook for child classes. */\n    _addOffset(position) {\n        return position;\n    }\n    /**\n     * Returns the origin position and a fallback position based on the user's position preference.\n     * The fallback position is the inverse of the origin (e.g. `'below' -> 'above'`).\n     */\n    _getOrigin() {\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        const position = this.position;\n        let originPosition;\n        if (position == 'above' || position == 'below') {\n            originPosition = { originX: 'center', originY: position == 'above' ? 'top' : 'bottom' };\n        }\n        else if (position == 'before' ||\n            (position == 'left' && isLtr) ||\n            (position == 'right' && !isLtr)) {\n            originPosition = { originX: 'start', originY: 'center' };\n        }\n        else if (position == 'after' ||\n            (position == 'right' && isLtr) ||\n            (position == 'left' && !isLtr)) {\n            originPosition = { originX: 'end', originY: 'center' };\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getMatTooltipInvalidPositionError(position);\n        }\n        const { x, y } = this._invertPosition(originPosition.originX, originPosition.originY);\n        return {\n            main: originPosition,\n            fallback: { originX: x, originY: y },\n        };\n    }\n    /** Returns the overlay position and a fallback position based on the user's preference */\n    _getOverlayPosition() {\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        const position = this.position;\n        let overlayPosition;\n        if (position == 'above') {\n            overlayPosition = { overlayX: 'center', overlayY: 'bottom' };\n        }\n        else if (position == 'below') {\n            overlayPosition = { overlayX: 'center', overlayY: 'top' };\n        }\n        else if (position == 'before' ||\n            (position == 'left' && isLtr) ||\n            (position == 'right' && !isLtr)) {\n            overlayPosition = { overlayX: 'end', overlayY: 'center' };\n        }\n        else if (position == 'after' ||\n            (position == 'right' && isLtr) ||\n            (position == 'left' && !isLtr)) {\n            overlayPosition = { overlayX: 'start', overlayY: 'center' };\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getMatTooltipInvalidPositionError(position);\n        }\n        const { x, y } = this._invertPosition(overlayPosition.overlayX, overlayPosition.overlayY);\n        return {\n            main: overlayPosition,\n            fallback: { overlayX: x, overlayY: y },\n        };\n    }\n    /** Updates the tooltip message and repositions the overlay according to the new message length */\n    _updateTooltipMessage() {\n        // Must wait for the message to be painted to the tooltip so that the overlay can properly\n        // calculate the correct positioning based on the size of the text.\n        if (this._tooltipInstance) {\n            this._tooltipInstance.message = this.message;\n            this._tooltipInstance._markForCheck();\n            this._ngZone.onMicrotaskEmpty.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n                if (this._tooltipInstance) {\n                    this._overlayRef.updatePosition();\n                }\n            });\n        }\n    }\n    /** Updates the tooltip class */\n    _setTooltipClass(tooltipClass) {\n        if (this._tooltipInstance) {\n            this._tooltipInstance.tooltipClass = tooltipClass;\n            this._tooltipInstance._markForCheck();\n        }\n    }\n    /** Inverts an overlay position. */\n    _invertPosition(x, y) {\n        if (this.position === 'above' || this.position === 'below') {\n            if (y === 'top') {\n                y = 'bottom';\n            }\n            else if (y === 'bottom') {\n                y = 'top';\n            }\n        }\n        else {\n            if (x === 'end') {\n                x = 'start';\n            }\n            else if (x === 'start') {\n                x = 'end';\n            }\n        }\n        return { x, y };\n    }\n    /** Updates the class on the overlay panel based on the current position of the tooltip. */\n    _updateCurrentPositionClass(connectionPair) {\n        const { overlayY, originX, originY } = connectionPair;\n        let newPosition;\n        // If the overlay is in the middle along the Y axis,\n        // it means that it's either before or after.\n        if (overlayY === 'center') {\n            // Note that since this information is used for styling, we want to\n            // resolve `start` and `end` to their real values, otherwise consumers\n            // would have to remember to do it themselves on each consumption.\n            if (this._dir && this._dir.value === 'rtl') {\n                newPosition = originX === 'end' ? 'left' : 'right';\n            }\n            else {\n                newPosition = originX === 'start' ? 'left' : 'right';\n            }\n        }\n        else {\n            newPosition = overlayY === 'bottom' && originY === 'top' ? 'above' : 'below';\n        }\n        if (newPosition !== this._currentPosition) {\n            const overlayRef = this._overlayRef;\n            if (overlayRef) {\n                const classPrefix = `${this._cssClassPrefix}-${PANEL_CLASS}-`;\n                overlayRef.removePanelClass(classPrefix + this._currentPosition);\n                overlayRef.addPanelClass(classPrefix + newPosition);\n            }\n            this._currentPosition = newPosition;\n        }\n    }\n    /** Binds the pointer events to the tooltip trigger. */\n    _setupPointerEnterEventsIfNeeded() {\n        // Optimization: Defer hooking up events if there's no message or the tooltip is disabled.\n        if (this._disabled ||\n            !this.message ||\n            !this._viewInitialized ||\n            this._passiveListeners.length) {\n            return;\n        }\n        // The mouse events shouldn't be bound on mobile devices, because they can prevent the\n        // first tap from firing its click event or can cause the tooltip to open for clicks.\n        if (this._platformSupportsMouseEvents()) {\n            this._passiveListeners.push([\n                'mouseenter',\n                () => {\n                    this._setupPointerExitEventsIfNeeded();\n                    this.show();\n                },\n            ]);\n        }\n        else if (this.touchGestures !== 'off') {\n            this._disableNativeGesturesIfNecessary();\n            this._passiveListeners.push([\n                'touchstart',\n                () => {\n                    // Note that it's important that we don't `preventDefault` here,\n                    // because it can prevent click events from firing on the element.\n                    this._setupPointerExitEventsIfNeeded();\n                    clearTimeout(this._touchstartTimeout);\n                    this._touchstartTimeout = setTimeout(() => this.show(), LONGPRESS_DELAY);\n                },\n            ]);\n        }\n        this._addListeners(this._passiveListeners);\n    }\n    _setupPointerExitEventsIfNeeded() {\n        if (this._pointerExitEventsInitialized) {\n            return;\n        }\n        this._pointerExitEventsInitialized = true;\n        const exitListeners = [];\n        if (this._platformSupportsMouseEvents()) {\n            exitListeners.push([\n                'mouseleave',\n                event => {\n                    const newTarget = event.relatedTarget;\n                    if (!newTarget || !this._overlayRef?.overlayElement.contains(newTarget)) {\n                        this.hide();\n                    }\n                },\n            ], ['wheel', event => this._wheelListener(event)]);\n        }\n        else if (this.touchGestures !== 'off') {\n            this._disableNativeGesturesIfNecessary();\n            const touchendListener = () => {\n                clearTimeout(this._touchstartTimeout);\n                this.hide(this._defaultOptions.touchendHideDelay);\n            };\n            exitListeners.push(['touchend', touchendListener], ['touchcancel', touchendListener]);\n        }\n        this._addListeners(exitListeners);\n        this._passiveListeners.push(...exitListeners);\n    }\n    _addListeners(listeners) {\n        listeners.forEach(([event, listener]) => {\n            this._elementRef.nativeElement.addEventListener(event, listener, passiveListenerOptions);\n        });\n    }\n    _platformSupportsMouseEvents() {\n        return !this._platform.IOS && !this._platform.ANDROID;\n    }\n    /** Listener for the `wheel` event on the element. */\n    _wheelListener(event) {\n        if (this._isTooltipVisible()) {\n            const elementUnderPointer = this._document.elementFromPoint(event.clientX, event.clientY);\n            const element = this._elementRef.nativeElement;\n            // On non-touch devices we depend on the `mouseleave` event to close the tooltip, but it\n            // won't fire if the user scrolls away using the wheel without moving their cursor. We\n            // work around it by finding the element under the user's cursor and closing the tooltip\n            // if it's not the trigger.\n            if (elementUnderPointer !== element && !element.contains(elementUnderPointer)) {\n                this.hide();\n            }\n        }\n    }\n    /** Disables the native browser gestures, based on how the tooltip has been configured. */\n    _disableNativeGesturesIfNecessary() {\n        const gestures = this.touchGestures;\n        if (gestures !== 'off') {\n            const element = this._elementRef.nativeElement;\n            const style = element.style;\n            // If gestures are set to `auto`, we don't disable text selection on inputs and\n            // textareas, because it prevents the user from typing into them on iOS Safari.\n            if (gestures === 'on' || (element.nodeName !== 'INPUT' && element.nodeName !== 'TEXTAREA')) {\n                style.userSelect =\n                    style.msUserSelect =\n                        style.webkitUserSelect =\n                            style.MozUserSelect =\n                                'none';\n            }\n            // If we have `auto` gestures and the element uses native HTML dragging,\n            // we don't set `-webkit-user-drag` because it prevents the native behavior.\n            if (gestures === 'on' || !element.draggable) {\n                style.webkitUserDrag = 'none';\n            }\n            style.touchAction = 'none';\n            style.webkitTapHighlightColor = 'transparent';\n        }\n    }\n}\n_MatTooltipBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatTooltipBase, deps: \"invalid\", target: i0.ɵɵFactoryTarget.Directive });\n_MatTooltipBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: _MatTooltipBase, inputs: { position: [\"matTooltipPosition\", \"position\"], disabled: [\"matTooltipDisabled\", \"disabled\"], showDelay: [\"matTooltipShowDelay\", \"showDelay\"], hideDelay: [\"matTooltipHideDelay\", \"hideDelay\"], touchGestures: [\"matTooltipTouchGestures\", \"touchGestures\"], message: [\"matTooltip\", \"message\"], tooltipClass: [\"matTooltipClass\", \"tooltipClass\"] }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatTooltipBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i1.Overlay }, { type: i0.ElementRef }, { type: i2.ScrollDispatcher }, { type: i0.ViewContainerRef }, { type: i0.NgZone }, { type: i3.Platform }, { type: i4.AriaDescriber }, { type: i4.FocusMonitor }, { type: undefined }, { type: i5.Directionality }, { type: undefined }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; }, propDecorators: { position: [{\n                type: Input,\n                args: ['matTooltipPosition']\n            }], disabled: [{\n                type: Input,\n                args: ['matTooltipDisabled']\n            }], showDelay: [{\n                type: Input,\n                args: ['matTooltipShowDelay']\n            }], hideDelay: [{\n                type: Input,\n                args: ['matTooltipHideDelay']\n            }], touchGestures: [{\n                type: Input,\n                args: ['matTooltipTouchGestures']\n            }], message: [{\n                type: Input,\n                args: ['matTooltip']\n            }], tooltipClass: [{\n                type: Input,\n                args: ['matTooltipClass']\n            }] } });\n/**\n * Directive that attaches a material design tooltip to the host element. Animates the showing and\n * hiding of a tooltip provided position (defaults to below the element).\n *\n * https://material.io/design/components/tooltips.html\n */\nclass MatTooltip extends _MatTooltipBase {\n    constructor(overlay, elementRef, scrollDispatcher, viewContainerRef, ngZone, platform, ariaDescriber, focusMonitor, scrollStrategy, dir, defaultOptions, _document) {\n        super(overlay, elementRef, scrollDispatcher, viewContainerRef, ngZone, platform, ariaDescriber, focusMonitor, scrollStrategy, dir, defaultOptions, _document);\n        this._tooltipComponent = TooltipComponent;\n    }\n}\nMatTooltip.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTooltip, deps: [{ token: i1.Overlay }, { token: i0.ElementRef }, { token: i2.ScrollDispatcher }, { token: i0.ViewContainerRef }, { token: i0.NgZone }, { token: i3.Platform }, { token: i4.AriaDescriber }, { token: i4.FocusMonitor }, { token: MAT_TOOLTIP_SCROLL_STRATEGY }, { token: i5.Directionality, optional: true }, { token: MAT_TOOLTIP_DEFAULT_OPTIONS, optional: true }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive });\nMatTooltip.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatTooltip, selector: \"[matTooltip]\", host: { classAttribute: \"mat-tooltip-trigger\" }, exportAs: [\"matTooltip\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTooltip, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTooltip]',\n                    exportAs: 'matTooltip',\n                    host: {\n                        'class': 'mat-tooltip-trigger',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i1.Overlay }, { type: i0.ElementRef }, { type: i2.ScrollDispatcher }, { type: i0.ViewContainerRef }, { type: i0.NgZone }, { type: i3.Platform }, { type: i4.AriaDescriber }, { type: i4.FocusMonitor }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TOOLTIP_SCROLL_STRATEGY]\n                }] }, { type: i5.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_TOOLTIP_DEFAULT_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\nclass _TooltipComponentBase {\n    constructor(_changeDetectorRef, animationMode) {\n        this._changeDetectorRef = _changeDetectorRef;\n        /** Whether interactions on the page should close the tooltip */\n        this._closeOnInteraction = false;\n        /** Whether the tooltip is currently visible. */\n        this._isVisible = false;\n        /** Subject for notifying that the tooltip has been hidden from the view */\n        this._onHide = new Subject();\n        this._animationsDisabled = animationMode === 'NoopAnimations';\n    }\n    /**\n     * Shows the tooltip with an animation originating from the provided origin\n     * @param delay Amount of milliseconds to the delay showing the tooltip.\n     */\n    show(delay) {\n        // Cancel the delayed hide if it is scheduled\n        clearTimeout(this._hideTimeoutId);\n        this._showTimeoutId = setTimeout(() => {\n            this._toggleVisibility(true);\n            this._showTimeoutId = undefined;\n        }, delay);\n    }\n    /**\n     * Begins the animation to hide the tooltip after the provided delay in ms.\n     * @param delay Amount of milliseconds to delay showing the tooltip.\n     */\n    hide(delay) {\n        // Cancel the delayed show if it is scheduled\n        clearTimeout(this._showTimeoutId);\n        this._hideTimeoutId = setTimeout(() => {\n            this._toggleVisibility(false);\n            this._hideTimeoutId = undefined;\n        }, delay);\n    }\n    /** Returns an observable that notifies when the tooltip has been hidden from view. */\n    afterHidden() {\n        return this._onHide;\n    }\n    /** Whether the tooltip is being displayed. */\n    isVisible() {\n        return this._isVisible;\n    }\n    ngOnDestroy() {\n        this._cancelPendingAnimations();\n        this._onHide.complete();\n        this._triggerElement = null;\n    }\n    /**\n     * Interactions on the HTML body should close the tooltip immediately as defined in the\n     * material design spec.\n     * https://material.io/design/components/tooltips.html#behavior\n     */\n    _handleBodyInteraction() {\n        if (this._closeOnInteraction) {\n            this.hide(0);\n        }\n    }\n    /**\n     * Marks that the tooltip needs to be checked in the next change detection run.\n     * Mainly used for rendering the initial text before positioning a tooltip, which\n     * can be problematic in components with OnPush change detection.\n     */\n    _markForCheck() {\n        this._changeDetectorRef.markForCheck();\n    }\n    _handleMouseLeave({ relatedTarget }) {\n        if (!relatedTarget || !this._triggerElement.contains(relatedTarget)) {\n            if (this.isVisible()) {\n                this.hide(this._mouseLeaveHideDelay);\n            }\n            else {\n                this._finalizeAnimation(false);\n            }\n        }\n    }\n    /**\n     * Callback for when the timeout in this.show() gets completed.\n     * This method is only needed by the mdc-tooltip, and so it is only implemented\n     * in the mdc-tooltip, not here.\n     */\n    _onShow() { }\n    /** Event listener dispatched when an animation on the tooltip finishes. */\n    _handleAnimationEnd({ animationName }) {\n        if (animationName === this._showAnimation || animationName === this._hideAnimation) {\n            this._finalizeAnimation(animationName === this._showAnimation);\n        }\n    }\n    /** Cancels any pending animation sequences. */\n    _cancelPendingAnimations() {\n        clearTimeout(this._showTimeoutId);\n        clearTimeout(this._hideTimeoutId);\n        this._showTimeoutId = this._hideTimeoutId = undefined;\n    }\n    /** Handles the cleanup after an animation has finished. */\n    _finalizeAnimation(toVisible) {\n        if (toVisible) {\n            this._closeOnInteraction = true;\n        }\n        else if (!this.isVisible()) {\n            this._onHide.next();\n        }\n    }\n    /** Toggles the visibility of the tooltip element. */\n    _toggleVisibility(isVisible) {\n        // We set the classes directly here ourselves so that toggling the tooltip state\n        // isn't bound by change detection. This allows us to hide it even if the\n        // view ref has been detached from the CD tree.\n        const tooltip = this._tooltip.nativeElement;\n        const showClass = this._showAnimation;\n        const hideClass = this._hideAnimation;\n        tooltip.classList.remove(isVisible ? hideClass : showClass);\n        tooltip.classList.add(isVisible ? showClass : hideClass);\n        this._isVisible = isVisible;\n        // It's common for internal apps to disable animations using `* { animation: none !important }`\n        // which can break the opening sequence. Try to detect such cases and work around them.\n        if (isVisible && !this._animationsDisabled && typeof getComputedStyle === 'function') {\n            const styles = getComputedStyle(tooltip);\n            // Use `getPropertyValue` to avoid issues with property renaming.\n            if (styles.getPropertyValue('animation-duration') === '0s' ||\n                styles.getPropertyValue('animation-name') === 'none') {\n                this._animationsDisabled = true;\n            }\n        }\n        if (isVisible) {\n            this._onShow();\n        }\n        if (this._animationsDisabled) {\n            tooltip.classList.add('_mat-animation-noopable');\n            this._finalizeAnimation(isVisible);\n        }\n    }\n}\n_TooltipComponentBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _TooltipComponentBase, deps: [{ token: i0.ChangeDetectorRef }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n_TooltipComponentBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: _TooltipComponentBase, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _TooltipComponentBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; } });\n/**\n * Internal component that wraps the tooltip's content.\n * @docs-private\n */\nclass TooltipComponent extends _TooltipComponentBase {\n    constructor(changeDetectorRef, _breakpointObserver, animationMode) {\n        super(changeDetectorRef, animationMode);\n        this._breakpointObserver = _breakpointObserver;\n        /** Stream that emits whether the user has a handset-sized display.  */\n        this._isHandset = this._breakpointObserver.observe(Breakpoints.Handset);\n        this._showAnimation = 'mat-tooltip-show';\n        this._hideAnimation = 'mat-tooltip-hide';\n    }\n}\nTooltipComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: TooltipComponent, deps: [{ token: i0.ChangeDetectorRef }, { token: i6.BreakpointObserver }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nTooltipComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: TooltipComponent, selector: \"mat-tooltip-component\", host: { attributes: { \"aria-hidden\": \"true\" }, listeners: { \"mouseleave\": \"_handleMouseLeave($event)\" }, properties: { \"style.zoom\": \"isVisible() ? 1 : null\" } }, viewQueries: [{ propertyName: \"_tooltip\", first: true, predicate: [\"tooltip\"], descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<div #tooltip\\n     class=\\\"mat-tooltip\\\"\\n     (animationend)=\\\"_handleAnimationEnd($event)\\\"\\n     [ngClass]=\\\"tooltipClass\\\"\\n     [class.mat-tooltip-handset]=\\\"(_isHandset | async)?.matches\\\">{{message}}</div>\\n\", styles: [\".mat-tooltip{color:#fff;border-radius:4px;margin:14px;max-width:250px;padding-left:8px;padding-right:8px;overflow:hidden;text-overflow:ellipsis;transform:scale(0)}.mat-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.cdk-high-contrast-active .mat-tooltip{outline:solid 1px}.mat-tooltip-handset{margin:24px;padding-left:16px;padding-right:16px}.mat-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-tooltip-show{0%{opacity:0;transform:scale(0)}50%{opacity:.5;transform:scale(0.99)}100%{opacity:1;transform:scale(1)}}@keyframes mat-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(1)}}.mat-tooltip-show{animation:mat-tooltip-show 200ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-tooltip-hide{animation:mat-tooltip-hide 100ms cubic-bezier(0, 0, 0.2, 1) forwards}\"], dependencies: [{ kind: \"directive\", type: i7.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"pipe\", type: i7.AsyncPipe, name: \"async\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: TooltipComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tooltip-component', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        // Forces the element to have a layout in IE and Edge. This fixes issues where the element\n                        // won't be rendered if the animations are disabled or there is no web animations polyfill.\n                        '[style.zoom]': 'isVisible() ? 1 : null',\n                        '(mouseleave)': '_handleMouseLeave($event)',\n                        'aria-hidden': 'true',\n                    }, template: \"<div #tooltip\\n     class=\\\"mat-tooltip\\\"\\n     (animationend)=\\\"_handleAnimationEnd($event)\\\"\\n     [ngClass]=\\\"tooltipClass\\\"\\n     [class.mat-tooltip-handset]=\\\"(_isHandset | async)?.matches\\\">{{message}}</div>\\n\", styles: [\".mat-tooltip{color:#fff;border-radius:4px;margin:14px;max-width:250px;padding-left:8px;padding-right:8px;overflow:hidden;text-overflow:ellipsis;transform:scale(0)}.mat-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.cdk-high-contrast-active .mat-tooltip{outline:solid 1px}.mat-tooltip-handset{margin:24px;padding-left:16px;padding-right:16px}.mat-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-tooltip-show{0%{opacity:0;transform:scale(0)}50%{opacity:.5;transform:scale(0.99)}100%{opacity:1;transform:scale(1)}}@keyframes mat-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(1)}}.mat-tooltip-show{animation:mat-tooltip-show 200ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-tooltip-hide{animation:mat-tooltip-hide 100ms cubic-bezier(0, 0, 0.2, 1) forwards}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: i6.BreakpointObserver }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { _tooltip: [{\n                type: ViewChild,\n                args: ['tooltip', {\n                        // Use a static query here since we interact directly with\n                        // the DOM which can happen before `ngAfterViewInit`.\n                        static: true,\n                    }]\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatTooltipModule {\n}\nMatTooltipModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTooltipModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatTooltipModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTooltipModule, declarations: [MatTooltip, TooltipComponent], imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule], exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule] });\nMatTooltipModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTooltipModule, providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER], imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule, MatCommonModule, CdkScrollableModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTooltipModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule],\n                    exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule],\n                    declarations: [MatTooltip, TooltipComponent],\n                    providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Animations used by MatTooltip.\n * @docs-private\n */\nconst matTooltipAnimations = {\n    /** Animation that transitions a tooltip in and out. */\n    tooltipState: trigger('state', [\n        state('initial, void, hidden', style({ opacity: 0, transform: 'scale(0)' })),\n        state('visible', style({ transform: 'scale(1)' })),\n        transition('* => visible', animate('200ms cubic-bezier(0, 0, 0.2, 1)', keyframes([\n            style({ opacity: 0, transform: 'scale(0)', offset: 0 }),\n            style({ opacity: 0.5, transform: 'scale(0.99)', offset: 0.5 }),\n            style({ opacity: 1, transform: 'scale(1)', offset: 1 }),\n        ]))),\n        transition('* => hidden', animate('100ms cubic-bezier(0, 0, 0.2, 1)', style({ opacity: 0 }))),\n    ]),\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_TOOLTIP_DEFAULT_OPTIONS, MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER, MatTooltip, MatTooltipModule, SCROLL_THROTTLE_MS, TOOLTIP_PANEL_CLASS, TooltipComponent, _MatTooltipBase, _TooltipComponentBase, getMatTooltipInvalidPositionError, matTooltipAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,sBAApB;AACA,SAASC,OAAT,EAAkBC,aAAlB,QAAuC,sBAAvC;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,SAASC,UAAT,QAA2B,mBAA3B;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,QAAT,EAAmBC,YAAnB,QAAuC,iBAAvC;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,SAAzB,EAAoCC,MAApC,EAA4CC,KAA5C,EAAmDC,QAAnD,EAA6DC,SAA7D,EAAwEC,iBAAxE,EAA2FC,uBAA3F,EAAoHC,SAApH,EAA+HC,QAA/H,QAA+I,eAA/I;AACA,SAASC,eAAT,QAAgC,wBAAhC;AACA,OAAO,KAAKC,EAAZ,MAAoB,wBAApB;AACA,SAASC,mBAAT,QAAoC,wBAApC;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,SAASC,qBAAT,EAAgCC,oBAAhC,QAA4D,uBAA5D;AACA,SAASC,MAAT,EAAiBC,cAAjB,QAAuC,uBAAvC;AACA,OAAO,KAAKC,EAAZ,MAAoB,qBAApB;AACA,SAASC,WAAT,QAA4B,qBAA5B;AACA,OAAO,KAAKC,EAAZ,MAAoB,uBAApB;AACA,SAASC,+BAAT,QAAgD,uBAAhD;AACA,SAASC,eAAT,QAAgC,qBAAhC;AACA,SAASC,qBAAT,QAAsC,sCAAtC;AACA,SAASC,OAAT,QAAwB,MAAxB;AACA,SAASC,SAAT,EAAoBC,IAApB,QAAgC,gBAAhC;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,KAAzB,EAAgCC,UAAhC,EAA4CC,OAA5C,EAAqDC,SAArD,QAAsE,qBAAtE;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMC,kBAAkB,GAAG,EAA3B;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,mBAAmB,GAAG,mBAA5B;AACA,MAAMC,WAAW,GAAG,eAApB;AACA;;AACA,MAAMC,sBAAsB,GAAGf,+BAA+B,CAAC;EAAEgB,OAAO,EAAE;AAAX,CAAD,CAA9D;AACA;AACA;AACA;AACA;;AACA,MAAMC,eAAe,GAAG,GAAxB;AACA;AACA;AACA;AACA;;AACA,SAASC,iCAAT,CAA2CC,QAA3C,EAAqD;EACjD,OAAOC,KAAK,CAAE,qBAAoBD,QAAS,eAA/B,CAAZ;AACH;AACD;;;AACA,MAAME,2BAA2B,GAAG,IAAI1C,cAAJ,CAAmB,6BAAnB,CAApC;AACA;;AACA,SAAS2C,mCAAT,CAA6CC,OAA7C,EAAsD;EAClD,OAAO,MAAMA,OAAO,CAACC,gBAAR,CAAyBC,UAAzB,CAAoC;IAAEC,cAAc,EAAEd;EAAlB,CAApC,CAAb;AACH;AACD;;;AACA,MAAMe,4CAA4C,GAAG;EACjDC,OAAO,EAAEP,2BADwC;EAEjDQ,IAAI,EAAE,CAAC1D,OAAD,CAF2C;EAGjD2D,UAAU,EAAER;AAHqC,CAArD;AAKA;;AACA,MAAMS,2BAA2B,GAAG,IAAIpD,cAAJ,CAAmB,6BAAnB,EAAkD;EAClFqD,UAAU,EAAE,MADsE;EAElFC,OAAO,EAAEC;AAFyE,CAAlD,CAApC;AAIA;;AACA,SAASA,mCAAT,GAA+C;EAC3C,OAAO;IACHC,SAAS,EAAE,CADR;IAEHC,SAAS,EAAE,CAFR;IAGHC,iBAAiB,EAAE;EAHhB,CAAP;AAKH;;AACD,MAAMC,eAAN,CAAsB;EAClBC,WAAW,CAACC,QAAD,EAAWC,WAAX,EAAwBC,iBAAxB,EAA2CC,iBAA3C,EAA8DC,OAA9D,EAAuEC,SAAvE,EAAkFC,cAAlF,EAAkGC,aAAlG,EAAiHC,cAAjH,EAAiIC,IAAjI,EAAuIC,eAAvI,EAAwJC,SAAxJ,EAAmK;IAC1K,KAAKX,QAAL,GAAgBA,QAAhB;IACA,KAAKC,WAAL,GAAmBA,WAAnB;IACA,KAAKC,iBAAL,GAAyBA,iBAAzB;IACA,KAAKC,iBAAL,GAAyBA,iBAAzB;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKC,SAAL,GAAiBA,SAAjB;IACA,KAAKC,cAAL,GAAsBA,cAAtB;IACA,KAAKC,aAAL,GAAqBA,aAArB;IACA,KAAKE,IAAL,GAAYA,IAAZ;IACA,KAAKC,eAAL,GAAuBA,eAAvB;IACA,KAAKE,SAAL,GAAiB,OAAjB;IACA,KAAKC,SAAL,GAAiB,KAAjB;IACA,KAAKC,gBAAL,GAAwB,KAAxB;IACA,KAAKC,6BAAL,GAAqC,KAArC;IACA,KAAKC,eAAL,GAAuB,CAAvB;IACA,KAAKC,eAAL,GAAuB,KAAvB;IACA,KAAKC,UAAL,GAAkB,KAAKR,eAAL,CAAqBf,SAAvC;IACA,KAAKwB,UAAL,GAAkB,KAAKT,eAAL,CAAqBd,SAAvC;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IACQ,KAAKwB,aAAL,GAAqB,MAArB;IACA,KAAKC,QAAL,GAAgB,EAAhB;IACA;;IACA,KAAKC,iBAAL,GAAyB,EAAzB;IACA;;IACA,KAAKC,UAAL,GAAkB,IAAI5D,OAAJ,EAAlB;IACA,KAAK6D,eAAL,GAAuBhB,cAAvB;IACA,KAAKG,SAAL,GAAiBA,SAAjB;;IACA,IAAID,eAAJ,EAAqB;MACjB,IAAIA,eAAe,CAAC/B,QAApB,EAA8B;QAC1B,KAAKA,QAAL,GAAgB+B,eAAe,CAAC/B,QAAhC;MACH;;MACD,IAAI+B,eAAe,CAACU,aAApB,EAAmC;QAC/B,KAAKA,aAAL,GAAqBV,eAAe,CAACU,aAArC;MACH;IACJ;;IACDX,IAAI,CAACgB,MAAL,CAAYC,IAAZ,CAAiB9D,SAAS,CAAC,KAAK2D,UAAN,CAA1B,EAA6CI,SAA7C,CAAuD,MAAM;MACzD,IAAI,KAAKC,WAAT,EAAsB;QAClB,KAAKC,eAAL,CAAqB,KAAKD,WAA1B;MACH;IACJ,CAJD;EAKH;EACD;;;EACY,IAARjD,QAAQ,GAAG;IACX,OAAO,KAAKiC,SAAZ;EACH;;EACW,IAARjC,QAAQ,CAACmD,KAAD,EAAQ;IAChB,IAAIA,KAAK,KAAK,KAAKlB,SAAnB,EAA8B;MAC1B,KAAKA,SAAL,GAAiBkB,KAAjB;;MACA,IAAI,KAAKF,WAAT,EAAsB;QAClB,KAAKC,eAAL,CAAqB,KAAKD,WAA1B;;QACA,KAAKG,gBAAL,EAAuBC,IAAvB,CAA4B,CAA5B;;QACA,KAAKJ,WAAL,CAAiBK,cAAjB;MACH;IACJ;EACJ;EACD;;;EACY,IAARC,QAAQ,GAAG;IACX,OAAO,KAAKrB,SAAZ;EACH;;EACW,IAARqB,QAAQ,CAACJ,KAAD,EAAQ;IAChB,KAAKjB,SAAL,GAAiB5D,qBAAqB,CAAC6E,KAAD,CAAtC,CADgB,CAEhB;;IACA,IAAI,KAAKjB,SAAT,EAAoB;MAChB,KAAKsB,IAAL,CAAU,CAAV;IACH,CAFD,MAGK;MACD,KAAKC,gCAAL;IACH;EACJ;EACD;;;EACa,IAATzC,SAAS,GAAG;IACZ,OAAO,KAAKuB,UAAZ;EACH;;EACY,IAATvB,SAAS,CAACmC,KAAD,EAAQ;IACjB,KAAKZ,UAAL,GAAkBhE,oBAAoB,CAAC4E,KAAD,CAAtC;EACH;EACD;;;EACa,IAATlC,SAAS,GAAG;IACZ,OAAO,KAAKuB,UAAZ;EACH;;EACY,IAATvB,SAAS,CAACkC,KAAD,EAAQ;IACjB,KAAKX,UAAL,GAAkBjE,oBAAoB,CAAC4E,KAAD,CAAtC;;IACA,IAAI,KAAKC,gBAAT,EAA2B;MACvB,KAAKA,gBAAL,CAAsBM,oBAAtB,GAA6C,KAAKlB,UAAlD;IACH;EACJ;EACD;;;EACW,IAAPmB,OAAO,GAAG;IACV,OAAO,KAAKjB,QAAZ;EACH;;EACU,IAAPiB,OAAO,CAACR,KAAD,EAAQ;IACf,KAAKxB,cAAL,CAAoBiC,iBAApB,CAAsC,KAAKtC,WAAL,CAAiBuC,aAAvD,EAAsE,KAAKnB,QAA3E,EAAqF,SAArF,EADe,CAEf;IACA;IACA;;;IACA,KAAKA,QAAL,GAAgBS,KAAK,IAAI,IAAT,GAAgBW,MAAM,CAACX,KAAD,CAAN,CAAcY,IAAd,EAAhB,GAAuC,EAAvD;;IACA,IAAI,CAAC,KAAKrB,QAAN,IAAkB,KAAKsB,iBAAL,EAAtB,EAAgD;MAC5C,KAAKR,IAAL,CAAU,CAAV;IACH,CAFD,MAGK;MACD,KAAKC,gCAAL;;MACA,KAAKQ,qBAAL;;MACA,KAAKxC,OAAL,CAAayC,iBAAb,CAA+B,MAAM;QACjC;QACA;QACA;QACA;QACAC,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;UACzB,KAAK1C,cAAL,CAAoB2C,QAApB,CAA6B,KAAKhD,WAAL,CAAiBuC,aAA9C,EAA6D,KAAKF,OAAlE,EAA2E,SAA3E;QACH,CAFD;MAGH,CARD;IASH;EACJ;EACD;;;EACgB,IAAZY,YAAY,GAAG;IACf,OAAO,KAAKC,aAAZ;EACH;;EACe,IAAZD,YAAY,CAACpB,KAAD,EAAQ;IACpB,KAAKqB,aAAL,GAAqBrB,KAArB;;IACA,IAAI,KAAKC,gBAAT,EAA2B;MACvB,KAAKqB,gBAAL,CAAsB,KAAKD,aAA3B;IACH;EACJ;;EACDE,eAAe,GAAG;IACd;IACA,KAAKvC,gBAAL,GAAwB,IAAxB;;IACA,KAAKsB,gCAAL;;IACA,KAAK7B,aAAL,CACK+C,OADL,CACa,KAAKrD,WADlB,EAEKyB,IAFL,CAEU9D,SAAS,CAAC,KAAK2D,UAAN,CAFnB,EAGKI,SAHL,CAGe4B,MAAM,IAAI;MACrB;MACA,IAAI,CAACA,MAAL,EAAa;QACT,KAAKnD,OAAL,CAAaoD,GAAb,CAAiB,MAAM,KAAKrB,IAAL,CAAU,CAAV,CAAvB;MACH,CAFD,MAGK,IAAIoB,MAAM,KAAK,UAAf,EAA2B;QAC5B,KAAKnD,OAAL,CAAaoD,GAAb,CAAiB,MAAM,KAAKxB,IAAL,EAAvB;MACH;IACJ,CAXD;EAYH;EACD;AACJ;AACA;;;EACIyB,WAAW,GAAG;IACV,MAAMjB,aAAa,GAAG,KAAKvC,WAAL,CAAiBuC,aAAvC;IACAkB,YAAY,CAAC,KAAKC,kBAAN,CAAZ;;IACA,IAAI,KAAK/B,WAAT,EAAsB;MAClB,KAAKA,WAAL,CAAiBgC,OAAjB;;MACA,KAAK7B,gBAAL,GAAwB,IAAxB;IACH,CANS,CAOV;;;IACA,KAAKT,iBAAL,CAAuBuC,OAAvB,CAA+B,CAAC,CAACC,KAAD,EAAQC,QAAR,CAAD,KAAuB;MAClDvB,aAAa,CAACwB,mBAAd,CAAkCF,KAAlC,EAAyCC,QAAzC,EAAmDxF,sBAAnD;IACH,CAFD;;IAGA,KAAK+C,iBAAL,CAAuB2C,MAAvB,GAAgC,CAAhC;;IACA,KAAK1C,UAAL,CAAgB2C,IAAhB;;IACA,KAAK3C,UAAL,CAAgB4C,QAAhB;;IACA,KAAK7D,cAAL,CAAoBiC,iBAApB,CAAsCC,aAAtC,EAAqD,KAAKF,OAA1D,EAAmE,SAAnE;;IACA,KAAK/B,aAAL,CAAmB6D,cAAnB,CAAkC5B,aAAlC;EACH;EACD;;;EACAR,IAAI,CAACqC,KAAK,GAAG,KAAK1E,SAAd,EAAyB;IACzB,IAAI,KAAKuC,QAAL,IAAiB,CAAC,KAAKI,OAAvB,IAAkC,KAAKK,iBAAL,EAAtC,EAAgE;MAC5D,KAAKZ,gBAAL,EAAuBuC,wBAAvB;MACA;IACH;;IACD,MAAMC,UAAU,GAAG,KAAKC,cAAL,EAAnB;;IACA,KAAKC,OAAL;;IACA,KAAKC,OAAL,GACI,KAAKA,OAAL,IAAgB,IAAIjH,eAAJ,CAAoB,KAAKkH,iBAAzB,EAA4C,KAAKxE,iBAAjD,CADpB;IAEA,MAAMyE,QAAQ,GAAI,KAAK7C,gBAAL,GAAwBwC,UAAU,CAACM,MAAX,CAAkB,KAAKH,OAAvB,EAAgCE,QAA1E;IACAA,QAAQ,CAACE,eAAT,GAA2B,KAAK7E,WAAL,CAAiBuC,aAA5C;IACAoC,QAAQ,CAACvC,oBAAT,GAAgC,KAAKlB,UAArC;IACAyD,QAAQ,CACHG,WADL,GAEKrD,IAFL,CAEU9D,SAAS,CAAC,KAAK2D,UAAN,CAFnB,EAGKI,SAHL,CAGe,MAAM,KAAK8C,OAAL,EAHrB;;IAIA,KAAKrB,gBAAL,CAAsB,KAAKD,aAA3B;;IACA,KAAKP,qBAAL;;IACAgC,QAAQ,CAAC5C,IAAT,CAAcqC,KAAd;EACH;EACD;;;EACAlC,IAAI,CAACkC,KAAK,GAAG,KAAKzE,SAAd,EAAyB;IACzB,MAAMgF,QAAQ,GAAG,KAAK7C,gBAAtB;;IACA,IAAI6C,QAAJ,EAAc;MACV,IAAIA,QAAQ,CAACI,SAAT,EAAJ,EAA0B;QACtBJ,QAAQ,CAACzC,IAAT,CAAckC,KAAd;MACH,CAFD,MAGK;QACDO,QAAQ,CAACN,wBAAT;;QACA,KAAKG,OAAL;MACH;IACJ;EACJ;EACD;;;EACAQ,MAAM,GAAG;IACL,KAAKtC,iBAAL,KAA2B,KAAKR,IAAL,EAA3B,GAAyC,KAAKH,IAAL,EAAzC;EACH;EACD;;;EACAW,iBAAiB,GAAG;IAChB,OAAO,CAAC,CAAC,KAAKZ,gBAAP,IAA2B,KAAKA,gBAAL,CAAsBiD,SAAtB,EAAlC;EACH;EACD;;;EACAR,cAAc,GAAG;IACb,IAAI,KAAK5C,WAAT,EAAsB;MAClB,OAAO,KAAKA,WAAZ;IACH;;IACD,MAAMsD,mBAAmB,GAAG,KAAKhF,iBAAL,CAAuBiF,2BAAvB,CAAmD,KAAKlF,WAAxD,CAA5B,CAJa,CAKb;;;IACA,MAAMmF,QAAQ,GAAG,KAAKpF,QAAL,CACZrB,QADY,GAEZ0G,mBAFY,CAEQ,KAAKpF,WAFb,EAGZqF,qBAHY,CAGW,IAAG,KAAKrE,eAAgB,UAHnC,EAIZsE,sBAJY,CAIW,KAJX,EAKZC,kBALY,CAKO,KAAKxE,eALZ,EAMZyE,wBANY,CAMaP,mBANb,CAAjB;;IAOAE,QAAQ,CAACM,eAAT,CAAyBhE,IAAzB,CAA8B9D,SAAS,CAAC,KAAK2D,UAAN,CAAvC,EAA0DI,SAA1D,CAAoEF,MAAM,IAAI;MAC1E,KAAKkE,2BAAL,CAAiClE,MAAM,CAACmE,cAAxC;;MACA,IAAI,KAAK7D,gBAAT,EAA2B;QACvB,IAAIN,MAAM,CAACoE,wBAAP,CAAgCC,gBAAhC,IAAoD,KAAK/D,gBAAL,CAAsBiD,SAAtB,EAAxD,EAA2F;UACvF;UACA;UACA,KAAK5E,OAAL,CAAaoD,GAAb,CAAiB,MAAM,KAAKrB,IAAL,CAAU,CAAV,CAAvB;QACH;MACJ;IACJ,CATD;IAUA,KAAKP,WAAL,GAAmB,KAAK5B,QAAL,CAAc+F,MAAd,CAAqB;MACpCC,SAAS,EAAE,KAAKvF,IADoB;MAEpCwF,gBAAgB,EAAEb,QAFkB;MAGpCc,UAAU,EAAG,GAAE,KAAKjF,eAAgB,IAAG3C,WAAY,EAHf;MAIpCkC,cAAc,EAAE,KAAKgB,eAAL;IAJoB,CAArB,CAAnB;;IAMA,KAAKK,eAAL,CAAqB,KAAKD,WAA1B;;IACA,KAAKA,WAAL,CACKuE,WADL,GAEKzE,IAFL,CAEU9D,SAAS,CAAC,KAAK2D,UAAN,CAFnB,EAGKI,SAHL,CAGe,MAAM,KAAK8C,OAAL,EAHrB;;IAIA,KAAK7C,WAAL,CACKwE,oBADL,GAEK1E,IAFL,CAEU9D,SAAS,CAAC,KAAK2D,UAAN,CAFnB,EAGKI,SAHL,CAGe,MAAM,KAAKI,gBAAL,EAAuBsE,sBAAvB,EAHrB;;IAIA,KAAKzE,WAAL,CACK0E,aADL,GAEK5E,IAFL,CAEU9D,SAAS,CAAC,KAAK2D,UAAN,CAFnB,EAGKI,SAHL,CAGemC,KAAK,IAAI;MACpB,IAAI,KAAKnB,iBAAL,MAA4BmB,KAAK,CAACyC,OAAN,KAAkBpJ,MAA9C,IAAwD,CAACC,cAAc,CAAC0G,KAAD,CAA3E,EAAoF;QAChFA,KAAK,CAAC0C,cAAN;QACA1C,KAAK,CAAC2C,eAAN;;QACA,KAAKrG,OAAL,CAAaoD,GAAb,CAAiB,MAAM,KAAKrB,IAAL,CAAU,CAAV,CAAvB;MACH;IACJ,CATD;;IAUA,IAAI,KAAKzB,eAAL,EAAsBgG,2BAA1B,EAAuD;MACnD,KAAK9E,WAAL,CAAiB+E,aAAjB,CAAgC,GAAE,KAAK1F,eAAgB,gCAAvD;IACH;;IACD,OAAO,KAAKW,WAAZ;EACH;EACD;;;EACA6C,OAAO,GAAG;IACN,IAAI,KAAK7C,WAAL,IAAoB,KAAKA,WAAL,CAAiBgF,WAAjB,EAAxB,EAAwD;MACpD,KAAKhF,WAAL,CAAiBiF,MAAjB;IACH;;IACD,KAAK9E,gBAAL,GAAwB,IAAxB;EACH;EACD;;;EACAF,eAAe,CAAC0C,UAAD,EAAa;IACxB,MAAM5F,QAAQ,GAAG4F,UAAU,CAACuC,SAAX,GAAuBb,gBAAxC;;IACA,MAAM1C,MAAM,GAAG,KAAKwD,UAAL,EAAf;;IACA,MAAMhI,OAAO,GAAG,KAAKiI,mBAAL,EAAhB;;IACArI,QAAQ,CAACsI,aAAT,CAAuB,CACnB,KAAKC,UAAL,CAAgB,EAAE,GAAG3D,MAAM,CAAC4D,IAAZ;MAAkB,GAAGpI,OAAO,CAACoI;IAA7B,CAAhB,CADmB,EAEnB,KAAKD,UAAL,CAAgB,EAAE,GAAG3D,MAAM,CAAC6D,QAAZ;MAAsB,GAAGrI,OAAO,CAACqI;IAAjC,CAAhB,CAFmB,CAAvB;EAIH;EACD;;;EACAF,UAAU,CAACvI,QAAD,EAAW;IACjB,OAAOA,QAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIoI,UAAU,GAAG;IACT,MAAMM,KAAK,GAAG,CAAC,KAAK5G,IAAN,IAAc,KAAKA,IAAL,CAAUqB,KAAV,IAAmB,KAA/C;IACA,MAAMnD,QAAQ,GAAG,KAAKA,QAAtB;IACA,IAAI2I,cAAJ;;IACA,IAAI3I,QAAQ,IAAI,OAAZ,IAAuBA,QAAQ,IAAI,OAAvC,EAAgD;MAC5C2I,cAAc,GAAG;QAAEC,OAAO,EAAE,QAAX;QAAqBC,OAAO,EAAE7I,QAAQ,IAAI,OAAZ,GAAsB,KAAtB,GAA8B;MAA5D,CAAjB;IACH,CAFD,MAGK,IAAIA,QAAQ,IAAI,QAAZ,IACJA,QAAQ,IAAI,MAAZ,IAAsB0I,KADlB,IAEJ1I,QAAQ,IAAI,OAAZ,IAAuB,CAAC0I,KAFxB,EAEgC;MACjCC,cAAc,GAAG;QAAEC,OAAO,EAAE,OAAX;QAAoBC,OAAO,EAAE;MAA7B,CAAjB;IACH,CAJI,MAKA,IAAI7I,QAAQ,IAAI,OAAZ,IACJA,QAAQ,IAAI,OAAZ,IAAuB0I,KADnB,IAEJ1I,QAAQ,IAAI,MAAZ,IAAsB,CAAC0I,KAFvB,EAE+B;MAChCC,cAAc,GAAG;QAAEC,OAAO,EAAE,KAAX;QAAkBC,OAAO,EAAE;MAA3B,CAAjB;IACH,CAJI,MAKA,IAAI,OAAOC,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;MACpD,MAAM/I,iCAAiC,CAACC,QAAD,CAAvC;IACH;;IACD,MAAM;MAAE+I,CAAF;MAAKC;IAAL,IAAW,KAAKC,eAAL,CAAqBN,cAAc,CAACC,OAApC,EAA6CD,cAAc,CAACE,OAA5D,CAAjB;;IACA,OAAO;MACHL,IAAI,EAAEG,cADH;MAEHF,QAAQ,EAAE;QAAEG,OAAO,EAAEG,CAAX;QAAcF,OAAO,EAAEG;MAAvB;IAFP,CAAP;EAIH;EACD;;;EACAX,mBAAmB,GAAG;IAClB,MAAMK,KAAK,GAAG,CAAC,KAAK5G,IAAN,IAAc,KAAKA,IAAL,CAAUqB,KAAV,IAAmB,KAA/C;IACA,MAAMnD,QAAQ,GAAG,KAAKA,QAAtB;IACA,IAAIkJ,eAAJ;;IACA,IAAIlJ,QAAQ,IAAI,OAAhB,EAAyB;MACrBkJ,eAAe,GAAG;QAAEC,QAAQ,EAAE,QAAZ;QAAsBC,QAAQ,EAAE;MAAhC,CAAlB;IACH,CAFD,MAGK,IAAIpJ,QAAQ,IAAI,OAAhB,EAAyB;MAC1BkJ,eAAe,GAAG;QAAEC,QAAQ,EAAE,QAAZ;QAAsBC,QAAQ,EAAE;MAAhC,CAAlB;IACH,CAFI,MAGA,IAAIpJ,QAAQ,IAAI,QAAZ,IACJA,QAAQ,IAAI,MAAZ,IAAsB0I,KADlB,IAEJ1I,QAAQ,IAAI,OAAZ,IAAuB,CAAC0I,KAFxB,EAEgC;MACjCQ,eAAe,GAAG;QAAEC,QAAQ,EAAE,KAAZ;QAAmBC,QAAQ,EAAE;MAA7B,CAAlB;IACH,CAJI,MAKA,IAAIpJ,QAAQ,IAAI,OAAZ,IACJA,QAAQ,IAAI,OAAZ,IAAuB0I,KADnB,IAEJ1I,QAAQ,IAAI,MAAZ,IAAsB,CAAC0I,KAFvB,EAE+B;MAChCQ,eAAe,GAAG;QAAEC,QAAQ,EAAE,OAAZ;QAAqBC,QAAQ,EAAE;MAA/B,CAAlB;IACH,CAJI,MAKA,IAAI,OAAON,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;MACpD,MAAM/I,iCAAiC,CAACC,QAAD,CAAvC;IACH;;IACD,MAAM;MAAE+I,CAAF;MAAKC;IAAL,IAAW,KAAKC,eAAL,CAAqBC,eAAe,CAACC,QAArC,EAA+CD,eAAe,CAACE,QAA/D,CAAjB;;IACA,OAAO;MACHZ,IAAI,EAAEU,eADH;MAEHT,QAAQ,EAAE;QAAEU,QAAQ,EAAEJ,CAAZ;QAAeK,QAAQ,EAAEJ;MAAzB;IAFP,CAAP;EAIH;EACD;;;EACA/E,qBAAqB,GAAG;IACpB;IACA;IACA,IAAI,KAAKb,gBAAT,EAA2B;MACvB,KAAKA,gBAAL,CAAsBO,OAAtB,GAAgC,KAAKA,OAArC;;MACA,KAAKP,gBAAL,CAAsBiG,aAAtB;;MACA,KAAK5H,OAAL,CAAa6H,gBAAb,CAA8BvG,IAA9B,CAAmC7D,IAAI,CAAC,CAAD,CAAvC,EAA4CD,SAAS,CAAC,KAAK2D,UAAN,CAArD,EAAwEI,SAAxE,CAAkF,MAAM;QACpF,IAAI,KAAKI,gBAAT,EAA2B;UACvB,KAAKH,WAAL,CAAiBK,cAAjB;QACH;MACJ,CAJD;IAKH;EACJ;EACD;;;EACAmB,gBAAgB,CAACF,YAAD,EAAe;IAC3B,IAAI,KAAKnB,gBAAT,EAA2B;MACvB,KAAKA,gBAAL,CAAsBmB,YAAtB,GAAqCA,YAArC;;MACA,KAAKnB,gBAAL,CAAsBiG,aAAtB;IACH;EACJ;EACD;;;EACAJ,eAAe,CAACF,CAAD,EAAIC,CAAJ,EAAO;IAClB,IAAI,KAAKhJ,QAAL,KAAkB,OAAlB,IAA6B,KAAKA,QAAL,KAAkB,OAAnD,EAA4D;MACxD,IAAIgJ,CAAC,KAAK,KAAV,EAAiB;QACbA,CAAC,GAAG,QAAJ;MACH,CAFD,MAGK,IAAIA,CAAC,KAAK,QAAV,EAAoB;QACrBA,CAAC,GAAG,KAAJ;MACH;IACJ,CAPD,MAQK;MACD,IAAID,CAAC,KAAK,KAAV,EAAiB;QACbA,CAAC,GAAG,OAAJ;MACH,CAFD,MAGK,IAAIA,CAAC,KAAK,OAAV,EAAmB;QACpBA,CAAC,GAAG,KAAJ;MACH;IACJ;;IACD,OAAO;MAAEA,CAAF;MAAKC;IAAL,CAAP;EACH;EACD;;;EACAhC,2BAA2B,CAACC,cAAD,EAAiB;IACxC,MAAM;MAAEmC,QAAF;MAAYR,OAAZ;MAAqBC;IAArB,IAAiC5B,cAAvC;IACA,IAAIsC,WAAJ,CAFwC,CAGxC;IACA;;IACA,IAAIH,QAAQ,KAAK,QAAjB,EAA2B;MACvB;MACA;MACA;MACA,IAAI,KAAKtH,IAAL,IAAa,KAAKA,IAAL,CAAUqB,KAAV,KAAoB,KAArC,EAA4C;QACxCoG,WAAW,GAAGX,OAAO,KAAK,KAAZ,GAAoB,MAApB,GAA6B,OAA3C;MACH,CAFD,MAGK;QACDW,WAAW,GAAGX,OAAO,KAAK,OAAZ,GAAsB,MAAtB,GAA+B,OAA7C;MACH;IACJ,CAVD,MAWK;MACDW,WAAW,GAAGH,QAAQ,KAAK,QAAb,IAAyBP,OAAO,KAAK,KAArC,GAA6C,OAA7C,GAAuD,OAArE;IACH;;IACD,IAAIU,WAAW,KAAK,KAAKC,gBAAzB,EAA2C;MACvC,MAAM5D,UAAU,GAAG,KAAK3C,WAAxB;;MACA,IAAI2C,UAAJ,EAAgB;QACZ,MAAM6D,WAAW,GAAI,GAAE,KAAKnH,eAAgB,IAAG3C,WAAY,GAA3D;QACAiG,UAAU,CAAC8D,gBAAX,CAA4BD,WAAW,GAAG,KAAKD,gBAA/C;QACA5D,UAAU,CAACoC,aAAX,CAAyByB,WAAW,GAAGF,WAAvC;MACH;;MACD,KAAKC,gBAAL,GAAwBD,WAAxB;IACH;EACJ;EACD;;;EACA9F,gCAAgC,GAAG;IAC/B;IACA,IAAI,KAAKvB,SAAL,IACA,CAAC,KAAKyB,OADN,IAEA,CAAC,KAAKxB,gBAFN,IAGA,KAAKQ,iBAAL,CAAuB2C,MAH3B,EAGmC;MAC/B;IACH,CAP8B,CAQ/B;IACA;;;IACA,IAAI,KAAKqE,4BAAL,EAAJ,EAAyC;MACrC,KAAKhH,iBAAL,CAAuBiH,IAAvB,CAA4B,CACxB,YADwB,EAExB,MAAM;QACF,KAAKC,+BAAL;;QACA,KAAKxG,IAAL;MACH,CALuB,CAA5B;IAOH,CARD,MASK,IAAI,KAAKZ,aAAL,KAAuB,KAA3B,EAAkC;MACnC,KAAKqH,iCAAL;;MACA,KAAKnH,iBAAL,CAAuBiH,IAAvB,CAA4B,CACxB,YADwB,EAExB,MAAM;QACF;QACA;QACA,KAAKC,+BAAL;;QACA9E,YAAY,CAAC,KAAKC,kBAAN,CAAZ;QACA,KAAKA,kBAAL,GAA0B+E,UAAU,CAAC,MAAM,KAAK1G,IAAL,EAAP,EAAoBvD,eAApB,CAApC;MACH,CARuB,CAA5B;IAUH;;IACD,KAAKkK,aAAL,CAAmB,KAAKrH,iBAAxB;EACH;;EACDkH,+BAA+B,GAAG;IAC9B,IAAI,KAAKzH,6BAAT,EAAwC;MACpC;IACH;;IACD,KAAKA,6BAAL,GAAqC,IAArC;IACA,MAAM6H,aAAa,GAAG,EAAtB;;IACA,IAAI,KAAKN,4BAAL,EAAJ,EAAyC;MACrCM,aAAa,CAACL,IAAd,CAAmB,CACf,YADe,EAEfzE,KAAK,IAAI;QACL,MAAM+E,SAAS,GAAG/E,KAAK,CAACgF,aAAxB;;QACA,IAAI,CAACD,SAAD,IAAc,CAAC,KAAKjH,WAAL,EAAkBmH,cAAlB,CAAiCC,QAAjC,CAA0CH,SAA1C,CAAnB,EAAyE;UACrE,KAAK1G,IAAL;QACH;MACJ,CAPc,CAAnB,EAQG,CAAC,OAAD,EAAU2B,KAAK,IAAI,KAAKmF,cAAL,CAAoBnF,KAApB,CAAnB,CARH;IASH,CAVD,MAWK,IAAI,KAAK1C,aAAL,KAAuB,KAA3B,EAAkC;MACnC,KAAKqH,iCAAL;;MACA,MAAMS,gBAAgB,GAAG,MAAM;QAC3BxF,YAAY,CAAC,KAAKC,kBAAN,CAAZ;QACA,KAAKxB,IAAL,CAAU,KAAKzB,eAAL,CAAqBb,iBAA/B;MACH,CAHD;;MAIA+I,aAAa,CAACL,IAAd,CAAmB,CAAC,UAAD,EAAaW,gBAAb,CAAnB,EAAmD,CAAC,aAAD,EAAgBA,gBAAhB,CAAnD;IACH;;IACD,KAAKP,aAAL,CAAmBC,aAAnB;;IACA,KAAKtH,iBAAL,CAAuBiH,IAAvB,CAA4B,GAAGK,aAA/B;EACH;;EACDD,aAAa,CAACQ,SAAD,EAAY;IACrBA,SAAS,CAACtF,OAAV,CAAkB,CAAC,CAACC,KAAD,EAAQC,QAAR,CAAD,KAAuB;MACrC,KAAK9D,WAAL,CAAiBuC,aAAjB,CAA+B4G,gBAA/B,CAAgDtF,KAAhD,EAAuDC,QAAvD,EAAiExF,sBAAjE;IACH,CAFD;EAGH;;EACD+J,4BAA4B,GAAG;IAC3B,OAAO,CAAC,KAAKjI,SAAL,CAAegJ,GAAhB,IAAuB,CAAC,KAAKhJ,SAAL,CAAeiJ,OAA9C;EACH;EACD;;;EACAL,cAAc,CAACnF,KAAD,EAAQ;IAClB,IAAI,KAAKnB,iBAAL,EAAJ,EAA8B;MAC1B,MAAM4G,mBAAmB,GAAG,KAAK5I,SAAL,CAAe6I,gBAAf,CAAgC1F,KAAK,CAAC2F,OAAtC,EAA+C3F,KAAK,CAAC4F,OAArD,CAA5B;;MACA,MAAMC,OAAO,GAAG,KAAK1J,WAAL,CAAiBuC,aAAjC,CAF0B,CAG1B;MACA;MACA;MACA;;MACA,IAAI+G,mBAAmB,KAAKI,OAAxB,IAAmC,CAACA,OAAO,CAACX,QAAR,CAAiBO,mBAAjB,CAAxC,EAA+E;QAC3E,KAAKpH,IAAL;MACH;IACJ;EACJ;EACD;;;EACAsG,iCAAiC,GAAG;IAChC,MAAMmB,QAAQ,GAAG,KAAKxI,aAAtB;;IACA,IAAIwI,QAAQ,KAAK,KAAjB,EAAwB;MACpB,MAAMD,OAAO,GAAG,KAAK1J,WAAL,CAAiBuC,aAAjC;MACA,MAAMxE,KAAK,GAAG2L,OAAO,CAAC3L,KAAtB,CAFoB,CAGpB;MACA;;MACA,IAAI4L,QAAQ,KAAK,IAAb,IAAsBD,OAAO,CAACE,QAAR,KAAqB,OAArB,IAAgCF,OAAO,CAACE,QAAR,KAAqB,UAA/E,EAA4F;QACxF7L,KAAK,CAAC8L,UAAN,GACI9L,KAAK,CAAC+L,YAAN,GACI/L,KAAK,CAACgM,gBAAN,GACIhM,KAAK,CAACiM,aAAN,GACI,MAJhB;MAKH,CAXmB,CAYpB;MACA;;;MACA,IAAIL,QAAQ,KAAK,IAAb,IAAqB,CAACD,OAAO,CAACO,SAAlC,EAA6C;QACzClM,KAAK,CAACmM,cAAN,GAAuB,MAAvB;MACH;;MACDnM,KAAK,CAACoM,WAAN,GAAoB,MAApB;MACApM,KAAK,CAACqM,uBAAN,GAAgC,aAAhC;IACH;EACJ;;AAnhBiB;;AAqhBtBvK,eAAe,CAACwK,IAAhB;EAAkGpO,EAAlG;AAAA;;AACA4D,eAAe,CAACyK,IAAhB,kBADkGrO,EAClG;EAAA,MAAgG4D,eAAhG;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;AAAA;;AACA;EAAA,mDAFkG5D,EAElG,mBAA2F4D,eAA3F,EAAwH,CAAC;IAC7G0K,IAAI,EAAEpO;EADuG,CAAD,CAAxH,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAEoO,IAAI,EAAE9O,EAAE,CAACC;IAAX,CAAD,EAAuB;MAAE6O,IAAI,EAAEtO,EAAE,CAACuO;IAAX,CAAvB,EAAgD;MAAED,IAAI,EAAE1N,EAAE,CAAC4N;IAAX,CAAhD,EAA+E;MAAEF,IAAI,EAAEtO,EAAE,CAACyO;IAAX,CAA/E,EAA8G;MAAEH,IAAI,EAAEtO,EAAE,CAAC0O;IAAX,CAA9G,EAAmI;MAAEJ,IAAI,EAAEjN,EAAE,CAACsN;IAAX,CAAnI,EAA0J;MAAEL,IAAI,EAAE3O,EAAE,CAACiP;IAAX,CAA1J,EAAsL;MAAEN,IAAI,EAAE3O,EAAE,CAACkP;IAAX,CAAtL,EAAiN;MAAEP,IAAI,EAAEQ;IAAR,CAAjN,EAAsO;MAAER,IAAI,EAAExN,EAAE,CAACiO;IAAX,CAAtO,EAAmQ;MAAET,IAAI,EAAEQ;IAAR,CAAnQ,EAAwR;MAAER,IAAI,EAAEQ,SAAR;MAAmBE,UAAU,EAAE,CAAC;QACrVV,IAAI,EAAEnO,MAD+U;QAErV8O,IAAI,EAAE,CAACnP,QAAD;MAF+U,CAAD;IAA/B,CAAxR,CAAP;EAGlB,CALxB,EAK0C;IAAE2C,QAAQ,EAAE,CAAC;MACvC6L,IAAI,EAAElO,KADiC;MAEvC6O,IAAI,EAAE,CAAC,oBAAD;IAFiC,CAAD,CAAZ;IAG1BjJ,QAAQ,EAAE,CAAC;MACXsI,IAAI,EAAElO,KADK;MAEX6O,IAAI,EAAE,CAAC,oBAAD;IAFK,CAAD,CAHgB;IAM1BxL,SAAS,EAAE,CAAC;MACZ6K,IAAI,EAAElO,KADM;MAEZ6O,IAAI,EAAE,CAAC,qBAAD;IAFM,CAAD,CANe;IAS1BvL,SAAS,EAAE,CAAC;MACZ4K,IAAI,EAAElO,KADM;MAEZ6O,IAAI,EAAE,CAAC,qBAAD;IAFM,CAAD,CATe;IAY1B/J,aAAa,EAAE,CAAC;MAChBoJ,IAAI,EAAElO,KADU;MAEhB6O,IAAI,EAAE,CAAC,yBAAD;IAFU,CAAD,CAZW;IAe1B7I,OAAO,EAAE,CAAC;MACVkI,IAAI,EAAElO,KADI;MAEV6O,IAAI,EAAE,CAAC,YAAD;IAFI,CAAD,CAfiB;IAkB1BjI,YAAY,EAAE,CAAC;MACfsH,IAAI,EAAElO,KADS;MAEf6O,IAAI,EAAE,CAAC,iBAAD;IAFS,CAAD;EAlBY,CAL1C;AAAA;AA2BA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,UAAN,SAAyBtL,eAAzB,CAAyC;EACrCC,WAAW,CAAChB,OAAD,EAAUsM,UAAV,EAAsBC,gBAAtB,EAAwCC,gBAAxC,EAA0DC,MAA1D,EAAkEC,QAAlE,EAA4EC,aAA5E,EAA2FC,YAA3F,EAAyGnL,cAAzG,EAAyHoL,GAAzH,EAA8HC,cAA9H,EAA8IlL,SAA9I,EAAyJ;IAChK,MAAM5B,OAAN,EAAesM,UAAf,EAA2BC,gBAA3B,EAA6CC,gBAA7C,EAA+DC,MAA/D,EAAuEC,QAAvE,EAAiFC,aAAjF,EAAgGC,YAAhG,EAA8GnL,cAA9G,EAA8HoL,GAA9H,EAAmIC,cAAnI,EAAmJlL,SAAnJ;IACA,KAAKgE,iBAAL,GAAyBmH,gBAAzB;EACH;;AAJoC;;AAMzCV,UAAU,CAACd,IAAX;EAAA,iBAAuGc,UAAvG,EAzCkGlP,EAyClG,mBAAmIR,EAAE,CAACC,OAAtI,GAzCkGO,EAyClG,mBAA0JA,EAAE,CAACuO,UAA7J,GAzCkGvO,EAyClG,mBAAoLY,EAAE,CAAC4N,gBAAvL,GAzCkGxO,EAyClG,mBAAoNA,EAAE,CAACyO,gBAAvN,GAzCkGzO,EAyClG,mBAAoPA,EAAE,CAAC0O,MAAvP,GAzCkG1O,EAyClG,mBAA0QqB,EAAE,CAACsN,QAA7Q,GAzCkG3O,EAyClG,mBAAkSL,EAAE,CAACiP,aAArS,GAzCkG5O,EAyClG,mBAA+TL,EAAE,CAACkP,YAAlU,GAzCkG7O,EAyClG,mBAA2V2C,2BAA3V,GAzCkG3C,EAyClG,mBAAmYc,EAAE,CAACiO,cAAtY,MAzCkG/O,EAyClG,mBAAibqD,2BAAjb,MAzCkGrD,EAyClG,mBAAyeF,QAAze;AAAA;;AACAoP,UAAU,CAACb,IAAX,kBA1CkGrO,EA0ClG;EAAA,MAA2FkP,UAA3F;EAAA;EAAA;EAAA;EAAA,WA1CkGlP,EA0ClG;AAAA;;AACA;EAAA,mDA3CkGA,EA2ClG,mBAA2FkP,UAA3F,EAAmH,CAAC;IACxGZ,IAAI,EAAEpO,SADkG;IAExG+O,IAAI,EAAE,CAAC;MACCY,QAAQ,EAAE,cADX;MAECC,QAAQ,EAAE,YAFX;MAGCC,IAAI,EAAE;QACF,SAAS;MADP;IAHP,CAAD;EAFkG,CAAD,CAAnH,EAS4B,YAAY;IAAE,OAAO,CAAC;MAAEzB,IAAI,EAAE9O,EAAE,CAACC;IAAX,CAAD,EAAuB;MAAE6O,IAAI,EAAEtO,EAAE,CAACuO;IAAX,CAAvB,EAAgD;MAAED,IAAI,EAAE1N,EAAE,CAAC4N;IAAX,CAAhD,EAA+E;MAAEF,IAAI,EAAEtO,EAAE,CAACyO;IAAX,CAA/E,EAA8G;MAAEH,IAAI,EAAEtO,EAAE,CAAC0O;IAAX,CAA9G,EAAmI;MAAEJ,IAAI,EAAEjN,EAAE,CAACsN;IAAX,CAAnI,EAA0J;MAAEL,IAAI,EAAE3O,EAAE,CAACiP;IAAX,CAA1J,EAAsL;MAAEN,IAAI,EAAE3O,EAAE,CAACkP;IAAX,CAAtL,EAAiN;MAAEP,IAAI,EAAEQ,SAAR;MAAmBE,UAAU,EAAE,CAAC;QAC9QV,IAAI,EAAEnO,MADwQ;QAE9Q8O,IAAI,EAAE,CAACtM,2BAAD;MAFwQ,CAAD;IAA/B,CAAjN,EAG3B;MAAE2L,IAAI,EAAExN,EAAE,CAACiO,cAAX;MAA2BC,UAAU,EAAE,CAAC;QAC1CV,IAAI,EAAEjO;MADoC,CAAD;IAAvC,CAH2B,EAK3B;MAAEiO,IAAI,EAAEQ,SAAR;MAAmBE,UAAU,EAAE,CAAC;QAClCV,IAAI,EAAEjO;MAD4B,CAAD,EAElC;QACCiO,IAAI,EAAEnO,MADP;QAEC8O,IAAI,EAAE,CAAC5L,2BAAD;MAFP,CAFkC;IAA/B,CAL2B,EAU3B;MAAEiL,IAAI,EAAEQ,SAAR;MAAmBE,UAAU,EAAE,CAAC;QAClCV,IAAI,EAAEnO,MAD4B;QAElC8O,IAAI,EAAE,CAACnP,QAAD;MAF4B,CAAD;IAA/B,CAV2B,CAAP;EAalB,CAtBxB;AAAA;;AAuBA,MAAMkQ,qBAAN,CAA4B;EACxBnM,WAAW,CAACoM,kBAAD,EAAqBC,aAArB,EAAoC;IAC3C,KAAKD,kBAAL,GAA0BA,kBAA1B;IACA;;IACA,KAAKE,mBAAL,GAA2B,KAA3B;IACA;;IACA,KAAKC,UAAL,GAAkB,KAAlB;IACA;;IACA,KAAKC,OAAL,GAAe,IAAI5O,OAAJ,EAAf;IACA,KAAK6O,mBAAL,GAA2BJ,aAAa,KAAK,gBAA7C;EACH;EACD;AACJ;AACA;AACA;;;EACIpK,IAAI,CAACqC,KAAD,EAAQ;IACR;IACAX,YAAY,CAAC,KAAK+I,cAAN,CAAZ;IACA,KAAKC,cAAL,GAAsBhE,UAAU,CAAC,MAAM;MACnC,KAAKiE,iBAAL,CAAuB,IAAvB;;MACA,KAAKD,cAAL,GAAsB1B,SAAtB;IACH,CAH+B,EAG7B3G,KAH6B,CAAhC;EAIH;EACD;AACJ;AACA;AACA;;;EACIlC,IAAI,CAACkC,KAAD,EAAQ;IACR;IACAX,YAAY,CAAC,KAAKgJ,cAAN,CAAZ;IACA,KAAKD,cAAL,GAAsB/D,UAAU,CAAC,MAAM;MACnC,KAAKiE,iBAAL,CAAuB,KAAvB;;MACA,KAAKF,cAAL,GAAsBzB,SAAtB;IACH,CAH+B,EAG7B3G,KAH6B,CAAhC;EAIH;EACD;;;EACAU,WAAW,GAAG;IACV,OAAO,KAAKwH,OAAZ;EACH;EACD;;;EACAvH,SAAS,GAAG;IACR,OAAO,KAAKsH,UAAZ;EACH;;EACD7I,WAAW,GAAG;IACV,KAAKa,wBAAL;;IACA,KAAKiI,OAAL,CAAapI,QAAb;;IACA,KAAKW,eAAL,GAAuB,IAAvB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIuB,sBAAsB,GAAG;IACrB,IAAI,KAAKgG,mBAAT,EAA8B;MAC1B,KAAKlK,IAAL,CAAU,CAAV;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;;;EACI6F,aAAa,GAAG;IACZ,KAAKmE,kBAAL,CAAwBS,YAAxB;EACH;;EACDC,iBAAiB,CAAC;IAAE/D;EAAF,CAAD,EAAoB;IACjC,IAAI,CAACA,aAAD,IAAkB,CAAC,KAAKhE,eAAL,CAAqBkE,QAArB,CAA8BF,aAA9B,CAAvB,EAAqE;MACjE,IAAI,KAAK9D,SAAL,EAAJ,EAAsB;QAClB,KAAK7C,IAAL,CAAU,KAAKE,oBAAf;MACH,CAFD,MAGK;QACD,KAAKyK,kBAAL,CAAwB,KAAxB;MACH;IACJ;EACJ;EACD;AACJ;AACA;AACA;AACA;;;EACIC,OAAO,GAAG,CAAG;EACb;;;EACAC,mBAAmB,CAAC;IAAEC;EAAF,CAAD,EAAoB;IACnC,IAAIA,aAAa,KAAK,KAAKC,cAAvB,IAAyCD,aAAa,KAAK,KAAKE,cAApE,EAAoF;MAChF,KAAKL,kBAAL,CAAwBG,aAAa,KAAK,KAAKC,cAA/C;IACH;EACJ;EACD;;;EACA5I,wBAAwB,GAAG;IACvBZ,YAAY,CAAC,KAAKgJ,cAAN,CAAZ;IACAhJ,YAAY,CAAC,KAAK+I,cAAN,CAAZ;IACA,KAAKC,cAAL,GAAsB,KAAKD,cAAL,GAAsBzB,SAA5C;EACH;EACD;;;EACA8B,kBAAkB,CAACM,SAAD,EAAY;IAC1B,IAAIA,SAAJ,EAAe;MACX,KAAKf,mBAAL,GAA2B,IAA3B;IACH,CAFD,MAGK,IAAI,CAAC,KAAKrH,SAAL,EAAL,EAAuB;MACxB,KAAKuH,OAAL,CAAarI,IAAb;IACH;EACJ;EACD;;;EACAyI,iBAAiB,CAAC3H,SAAD,EAAY;IACzB;IACA;IACA;IACA,MAAMqI,OAAO,GAAG,KAAKC,QAAL,CAAc9K,aAA9B;IACA,MAAM+K,SAAS,GAAG,KAAKL,cAAvB;IACA,MAAMM,SAAS,GAAG,KAAKL,cAAvB;IACAE,OAAO,CAACI,SAAR,CAAkBC,MAAlB,CAAyB1I,SAAS,GAAGwI,SAAH,GAAeD,SAAjD;IACAF,OAAO,CAACI,SAAR,CAAkBE,GAAlB,CAAsB3I,SAAS,GAAGuI,SAAH,GAAeC,SAA9C;IACA,KAAKlB,UAAL,GAAkBtH,SAAlB,CATyB,CAUzB;IACA;;IACA,IAAIA,SAAS,IAAI,CAAC,KAAKwH,mBAAnB,IAA0C,OAAOoB,gBAAP,KAA4B,UAA1E,EAAsF;MAClF,MAAMC,MAAM,GAAGD,gBAAgB,CAACP,OAAD,CAA/B,CADkF,CAElF;;MACA,IAAIQ,MAAM,CAACC,gBAAP,CAAwB,oBAAxB,MAAkD,IAAlD,IACAD,MAAM,CAACC,gBAAP,CAAwB,gBAAxB,MAA8C,MADlD,EAC0D;QACtD,KAAKtB,mBAAL,GAA2B,IAA3B;MACH;IACJ;;IACD,IAAIxH,SAAJ,EAAe;MACX,KAAK+H,OAAL;IACH;;IACD,IAAI,KAAKP,mBAAT,EAA8B;MAC1Ba,OAAO,CAACI,SAAR,CAAkBE,GAAlB,CAAsB,yBAAtB;;MACA,KAAKb,kBAAL,CAAwB9H,SAAxB;IACH;EACJ;;AAnIuB;;AAqI5BkH,qBAAqB,CAAC5B,IAAtB;EAAA,iBAAkH4B,qBAAlH,EAvMkGhQ,EAuMlG,mBAAyJA,EAAE,CAAC6R,iBAA5J,GAvMkG7R,EAuMlG,mBAA0LwB,qBAA1L;AAAA;;AACAwO,qBAAqB,CAAC3B,IAAtB,kBAxMkGrO,EAwMlG;EAAA,MAAsGgQ;AAAtG;;AACA;EAAA,mDAzMkGhQ,EAyMlG,mBAA2FgQ,qBAA3F,EAA8H,CAAC;IACnH1B,IAAI,EAAEpO;EAD6G,CAAD,CAA9H,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAEoO,IAAI,EAAEtO,EAAE,CAAC6R;IAAX,CAAD,EAAiC;MAAEvD,IAAI,EAAEQ,SAAR;MAAmBE,UAAU,EAAE,CAAC;QAC9FV,IAAI,EAAEjO;MADwF,CAAD,EAE9F;QACCiO,IAAI,EAAEnO,MADP;QAEC8O,IAAI,EAAE,CAACzN,qBAAD;MAFP,CAF8F;IAA/B,CAAjC,CAAP;EAKlB,CAPxB;AAAA;AAQA;AACA;AACA;AACA;;;AACA,MAAMoO,gBAAN,SAA+BI,qBAA/B,CAAqD;EACjDnM,WAAW,CAACiO,iBAAD,EAAoBC,mBAApB,EAAyC7B,aAAzC,EAAwD;IAC/D,MAAM4B,iBAAN,EAAyB5B,aAAzB;IACA,KAAK6B,mBAAL,GAA2BA,mBAA3B;IACA;;IACA,KAAKC,UAAL,GAAkB,KAAKD,mBAAL,CAAyBE,OAAzB,CAAiC7Q,WAAW,CAAC8Q,OAA7C,CAAlB;IACA,KAAKlB,cAAL,GAAsB,kBAAtB;IACA,KAAKC,cAAL,GAAsB,kBAAtB;EACH;;AARgD;;AAUrDrB,gBAAgB,CAACxB,IAAjB;EAAA,iBAA6GwB,gBAA7G,EA/NkG5P,EA+NlG,mBAA+IA,EAAE,CAAC6R,iBAAlJ,GA/NkG7R,EA+NlG,mBAAgLmB,EAAE,CAACgR,kBAAnL,GA/NkGnS,EA+NlG,mBAAkNwB,qBAAlN;AAAA;;AACAoO,gBAAgB,CAACwC,IAAjB,kBAhOkGpS,EAgOlG;EAAA,MAAiG4P,gBAAjG;EAAA;EAAA;IAAA;MAhOkG5P,EAgOlG;IAAA;;IAAA;MAAA;;MAhOkGA,EAgOlG,qBAhOkGA,EAgOlG;IAAA;EAAA;EAAA,2BAA2L,MAA3L;EAAA;EAAA;IAAA;MAhOkGA,EAgOlG;QAAA,OAAiG,6BAAjG;MAAA;IAAA;;IAAA;MAhOkGA,EAgOlG;IAAA;EAAA;EAAA,WAhOkGA,EAgOlG;EAAA;EAAA;EAAA;EAAA;IAAA;MAhOkGA,EAgO0X,+BAA5d;MAhOkGA,EAgO0a;QAAA,OAAiB,+BAAjB;MAAA,EAA5gB;MAhOkGA,EAgOlG;MAhOkGA,EAgO8jB,UAAhqB;MAhOkGA,EAgOykB,eAA3qB;IAAA;;IAAA;MAAA;MAhOkGA,EAgOggB,+CAhOhgBA,EAgOggB,qEAAlmB;MAhOkGA,EAgO+d,wCAAjkB;MAhOkGA,EAgO8jB,aAAhqB;MAhOkGA,EAgO8jB,+BAAhqB;IAAA;EAAA;EAAA,eAA+hDH,EAAE,CAACwS,OAAliD,EAAwnDxS,EAAE,CAACyS,SAA3nD;EAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDAjOkGtS,EAiOlG,mBAA2F4P,gBAA3F,EAAyH,CAAC;IAC9GtB,IAAI,EAAEhO,SADwG;IAE9G2O,IAAI,EAAE,CAAC;MAAEY,QAAQ,EAAE,uBAAZ;MAAqC0C,aAAa,EAAEhS,iBAAiB,CAACiS,IAAtE;MAA4EC,eAAe,EAAEjS,uBAAuB,CAACkS,MAArH;MAA6H3C,IAAI,EAAE;QAC9H;QACA;QACA,gBAAgB,wBAH8G;QAI9H,gBAAgB,2BAJ8G;QAK9H,eAAe;MAL+G,CAAnI;MAMI4C,QAAQ,EAAE,yNANd;MAMyOhB,MAAM,EAAE,CAAC,mzBAAD;IANjP,CAAD;EAFwG,CAAD,CAAzH,EAS4B,YAAY;IAAE,OAAO,CAAC;MAAErD,IAAI,EAAEtO,EAAE,CAAC6R;IAAX,CAAD,EAAiC;MAAEvD,IAAI,EAAEnN,EAAE,CAACgR;IAAX,CAAjC,EAAkE;MAAE7D,IAAI,EAAEQ,SAAR;MAAmBE,UAAU,EAAE,CAAC;QAC/HV,IAAI,EAAEjO;MADyH,CAAD,EAE/H;QACCiO,IAAI,EAAEnO,MADP;QAEC8O,IAAI,EAAE,CAACzN,qBAAD;MAFP,CAF+H;IAA/B,CAAlE,CAAP;EAKlB,CAdxB,EAc0C;IAAE4P,QAAQ,EAAE,CAAC;MACvC9C,IAAI,EAAE7N,SADiC;MAEvCwO,IAAI,EAAE,CAAC,SAAD,EAAY;QACV;QACA;QACA2D,MAAM,EAAE;MAHE,CAAZ;IAFiC,CAAD;EAAZ,CAd1C;AAAA;AAuBA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,gBAAN,CAAuB;;AAEvBA,gBAAgB,CAACzE,IAAjB;EAAA,iBAA6GyE,gBAA7G;AAAA;;AACAA,gBAAgB,CAACC,IAAjB,kBAlQkG9S,EAkQlG;EAAA,MAA8G6S,gBAA9G;EAAA,eAA+I3D,UAA/I,EAA2JU,gBAA3J;EAAA,UAAwLhQ,UAAxL,EAAoMG,YAApM,EAAkNL,aAAlN,EAAiOiB,eAAjO;EAAA,UAA6PuO,UAA7P,EAAyQU,gBAAzQ,EAA2RjP,eAA3R,EAA4SE,mBAA5S;AAAA;AACAgS,gBAAgB,CAACE,IAAjB,kBAnQkG/S,EAmQlG;EAAA,WAA2I,CAACiD,4CAAD,CAA3I;EAAA,UAAqMrD,UAArM,EAAiNG,YAAjN,EAA+NL,aAA/N,EAA8OiB,eAA9O,EAA+PA,eAA/P,EAAgRE,mBAAhR;AAAA;;AACA;EAAA,mDApQkGb,EAoQlG,mBAA2F6S,gBAA3F,EAAyH,CAAC;IAC9GvE,IAAI,EAAE5N,QADwG;IAE9GuO,IAAI,EAAE,CAAC;MACC+D,OAAO,EAAE,CAACpT,UAAD,EAAaG,YAAb,EAA2BL,aAA3B,EAA0CiB,eAA1C,CADV;MAECsS,OAAO,EAAE,CAAC/D,UAAD,EAAaU,gBAAb,EAA+BjP,eAA/B,EAAgDE,mBAAhD,CAFV;MAGCqS,YAAY,EAAE,CAAChE,UAAD,EAAaU,gBAAb,CAHf;MAICuD,SAAS,EAAE,CAAClQ,4CAAD;IAJZ,CAAD;EAFwG,CAAD,CAAzH;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMmQ,oBAAoB,GAAG;EACzB;EACAC,YAAY,EAAEzR,OAAO,CAAC,OAAD,EAAU,CAC3BC,KAAK,CAAC,uBAAD,EAA0BC,KAAK,CAAC;IAAEwR,OAAO,EAAE,CAAX;IAAcC,SAAS,EAAE;EAAzB,CAAD,CAA/B,CADsB,EAE3B1R,KAAK,CAAC,SAAD,EAAYC,KAAK,CAAC;IAAEyR,SAAS,EAAE;EAAb,CAAD,CAAjB,CAFsB,EAG3BxR,UAAU,CAAC,cAAD,EAAiBC,OAAO,CAAC,kCAAD,EAAqCC,SAAS,CAAC,CAC7EH,KAAK,CAAC;IAAEwR,OAAO,EAAE,CAAX;IAAcC,SAAS,EAAE,UAAzB;IAAqCC,MAAM,EAAE;EAA7C,CAAD,CADwE,EAE7E1R,KAAK,CAAC;IAAEwR,OAAO,EAAE,GAAX;IAAgBC,SAAS,EAAE,aAA3B;IAA0CC,MAAM,EAAE;EAAlD,CAAD,CAFwE,EAG7E1R,KAAK,CAAC;IAAEwR,OAAO,EAAE,CAAX;IAAcC,SAAS,EAAE,UAAzB;IAAqCC,MAAM,EAAE;EAA7C,CAAD,CAHwE,CAAD,CAA9C,CAAxB,CAHiB,EAQ3BzR,UAAU,CAAC,aAAD,EAAgBC,OAAO,CAAC,kCAAD,EAAqCF,KAAK,CAAC;IAAEwR,OAAO,EAAE;EAAX,CAAD,CAA1C,CAAvB,CARiB,CAAV;AAFI,CAA7B;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASjQ,2BAAT,EAAsCG,mCAAtC,EAA2Eb,2BAA3E,EAAwGC,mCAAxG,EAA6IK,4CAA7I,EAA2LiM,UAA3L,EAAuM2D,gBAAvM,EAAyN3Q,kBAAzN,EAA6OC,mBAA7O,EAAkQyN,gBAAlQ,EAAoRhM,eAApR,EAAqSoM,qBAArS,EAA4TxN,iCAA5T,EAA+V4Q,oBAA/V"}, "metadata": {}, "sourceType": "module"}