{"ast": null, "code": "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "map": {"version": 3, "names": ["getVariation", "placement", "split"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/utils/getVariation.js"], "sourcesContent": ["export default function getVariation(placement) {\n  return placement.split('-')[1];\n}"], "mappings": "AAAA,eAAe,SAASA,YAAT,CAAsBC,SAAtB,EAAiC;EAC9C,OAAOA,SAAS,CAACC,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAP;AACD"}, "metadata": {}, "sourceType": "module"}