{"ast": null, "code": "import { Observable } from '../Observable';\nimport { noop } from '../util/noop';\nexport const NEVER = new Observable(noop);\nexport function never() {\n  return NEVER;\n}", "map": {"version": 3, "names": ["Observable", "noop", "NEVER", "never"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/never.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { noop } from '../util/noop';\nexport const NEVER = new Observable(noop);\nexport function never() {\n    return NEVER;\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,IAAT,QAAqB,cAArB;AACA,OAAO,MAAMC,KAAK,GAAG,IAAIF,UAAJ,CAAeC,IAAf,CAAd;AACP,OAAO,SAASE,KAAT,GAAiB;EACpB,OAAOD,KAAP;AACH"}, "metadata": {}, "sourceType": "module"}