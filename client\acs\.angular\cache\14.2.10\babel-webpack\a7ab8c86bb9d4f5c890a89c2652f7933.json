{"ast": null, "code": "import { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function expand(project, concurrent = Number.POSITIVE_INFINITY, scheduler) {\n  concurrent = (concurrent || 0) < 1 ? Number.POSITIVE_INFINITY : concurrent;\n  return source => source.lift(new ExpandOperator(project, concurrent, scheduler));\n}\nexport class ExpandOperator {\n  constructor(project, concurrent, scheduler) {\n    this.project = project;\n    this.concurrent = concurrent;\n    this.scheduler = scheduler;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new ExpandSubscriber(subscriber, this.project, this.concurrent, this.scheduler));\n  }\n\n}\nexport class ExpandSubscriber extends SimpleOuterSubscriber {\n  constructor(destination, project, concurrent, scheduler) {\n    super(destination);\n    this.project = project;\n    this.concurrent = concurrent;\n    this.scheduler = scheduler;\n    this.index = 0;\n    this.active = 0;\n    this.hasCompleted = false;\n\n    if (concurrent < Number.POSITIVE_INFINITY) {\n      this.buffer = [];\n    }\n  }\n\n  static dispatch(arg) {\n    const {\n      subscriber,\n      result,\n      value,\n      index\n    } = arg;\n    subscriber.subscribeToProjection(result, value, index);\n  }\n\n  _next(value) {\n    const destination = this.destination;\n\n    if (destination.closed) {\n      this._complete();\n\n      return;\n    }\n\n    const index = this.index++;\n\n    if (this.active < this.concurrent) {\n      destination.next(value);\n\n      try {\n        const {\n          project\n        } = this;\n        const result = project(value, index);\n\n        if (!this.scheduler) {\n          this.subscribeToProjection(result, value, index);\n        } else {\n          const state = {\n            subscriber: this,\n            result,\n            value,\n            index\n          };\n          const destination = this.destination;\n          destination.add(this.scheduler.schedule(ExpandSubscriber.dispatch, 0, state));\n        }\n      } catch (e) {\n        destination.error(e);\n      }\n    } else {\n      this.buffer.push(value);\n    }\n  }\n\n  subscribeToProjection(result, value, index) {\n    this.active++;\n    const destination = this.destination;\n    destination.add(innerSubscribe(result, new SimpleInnerSubscriber(this)));\n  }\n\n  _complete() {\n    this.hasCompleted = true;\n\n    if (this.hasCompleted && this.active === 0) {\n      this.destination.complete();\n    }\n\n    this.unsubscribe();\n  }\n\n  notifyNext(innerValue) {\n    this._next(innerValue);\n  }\n\n  notifyComplete() {\n    const buffer = this.buffer;\n    this.active--;\n\n    if (buffer && buffer.length > 0) {\n      this._next(buffer.shift());\n    }\n\n    if (this.hasCompleted && this.active === 0) {\n      this.destination.complete();\n    }\n  }\n\n}", "map": {"version": 3, "names": ["SimpleOuterSubscriber", "innerSubscribe", "SimpleInnerSubscriber", "expand", "project", "concurrent", "Number", "POSITIVE_INFINITY", "scheduler", "source", "lift", "ExpandOperator", "constructor", "call", "subscriber", "subscribe", "ExpandSubscriber", "destination", "index", "active", "hasCompleted", "buffer", "dispatch", "arg", "result", "value", "subscribeToProjection", "_next", "closed", "_complete", "next", "state", "add", "schedule", "e", "error", "push", "complete", "unsubscribe", "notifyNext", "innerValue", "notifyComplete", "length", "shift"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/expand.js"], "sourcesContent": ["import { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function expand(project, concurrent = Number.POSITIVE_INFINITY, scheduler) {\n    concurrent = (concurrent || 0) < 1 ? Number.POSITIVE_INFINITY : concurrent;\n    return (source) => source.lift(new ExpandOperator(project, concurrent, scheduler));\n}\nexport class ExpandOperator {\n    constructor(project, concurrent, scheduler) {\n        this.project = project;\n        this.concurrent = concurrent;\n        this.scheduler = scheduler;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new ExpandSubscriber(subscriber, this.project, this.concurrent, this.scheduler));\n    }\n}\nexport class ExpandSubscriber extends SimpleOuterSubscriber {\n    constructor(destination, project, concurrent, scheduler) {\n        super(destination);\n        this.project = project;\n        this.concurrent = concurrent;\n        this.scheduler = scheduler;\n        this.index = 0;\n        this.active = 0;\n        this.hasCompleted = false;\n        if (concurrent < Number.POSITIVE_INFINITY) {\n            this.buffer = [];\n        }\n    }\n    static dispatch(arg) {\n        const { subscriber, result, value, index } = arg;\n        subscriber.subscribeToProjection(result, value, index);\n    }\n    _next(value) {\n        const destination = this.destination;\n        if (destination.closed) {\n            this._complete();\n            return;\n        }\n        const index = this.index++;\n        if (this.active < this.concurrent) {\n            destination.next(value);\n            try {\n                const { project } = this;\n                const result = project(value, index);\n                if (!this.scheduler) {\n                    this.subscribeToProjection(result, value, index);\n                }\n                else {\n                    const state = { subscriber: this, result, value, index };\n                    const destination = this.destination;\n                    destination.add(this.scheduler.schedule(ExpandSubscriber.dispatch, 0, state));\n                }\n            }\n            catch (e) {\n                destination.error(e);\n            }\n        }\n        else {\n            this.buffer.push(value);\n        }\n    }\n    subscribeToProjection(result, value, index) {\n        this.active++;\n        const destination = this.destination;\n        destination.add(innerSubscribe(result, new SimpleInnerSubscriber(this)));\n    }\n    _complete() {\n        this.hasCompleted = true;\n        if (this.hasCompleted && this.active === 0) {\n            this.destination.complete();\n        }\n        this.unsubscribe();\n    }\n    notifyNext(innerValue) {\n        this._next(innerValue);\n    }\n    notifyComplete() {\n        const buffer = this.buffer;\n        this.active--;\n        if (buffer && buffer.length > 0) {\n            this._next(buffer.shift());\n        }\n        if (this.hasCompleted && this.active === 0) {\n            this.destination.complete();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,qBAAT,EAAgCC,cAAhC,EAAgDC,qBAAhD,QAA6E,mBAA7E;AACA,OAAO,SAASC,MAAT,CAAgBC,OAAhB,EAAyBC,UAAU,GAAGC,MAAM,CAACC,iBAA7C,EAAgEC,SAAhE,EAA2E;EAC9EH,UAAU,GAAG,CAACA,UAAU,IAAI,CAAf,IAAoB,CAApB,GAAwBC,MAAM,CAACC,iBAA/B,GAAmDF,UAAhE;EACA,OAAQI,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,cAAJ,CAAmBP,OAAnB,EAA4BC,UAA5B,EAAwCG,SAAxC,CAAZ,CAAnB;AACH;AACD,OAAO,MAAMG,cAAN,CAAqB;EACxBC,WAAW,CAACR,OAAD,EAAUC,UAAV,EAAsBG,SAAtB,EAAiC;IACxC,KAAKJ,OAAL,GAAeA,OAAf;IACA,KAAKC,UAAL,GAAkBA,UAAlB;IACA,KAAKG,SAAL,GAAiBA,SAAjB;EACH;;EACDK,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,gBAAJ,CAAqBF,UAArB,EAAiC,KAAKV,OAAtC,EAA+C,KAAKC,UAApD,EAAgE,KAAKG,SAArE,CAAjB,CAAP;EACH;;AARuB;AAU5B,OAAO,MAAMQ,gBAAN,SAA+BhB,qBAA/B,CAAqD;EACxDY,WAAW,CAACK,WAAD,EAAcb,OAAd,EAAuBC,UAAvB,EAAmCG,SAAnC,EAA8C;IACrD,MAAMS,WAAN;IACA,KAAKb,OAAL,GAAeA,OAAf;IACA,KAAKC,UAAL,GAAkBA,UAAlB;IACA,KAAKG,SAAL,GAAiBA,SAAjB;IACA,KAAKU,KAAL,GAAa,CAAb;IACA,KAAKC,MAAL,GAAc,CAAd;IACA,KAAKC,YAAL,GAAoB,KAApB;;IACA,IAAIf,UAAU,GAAGC,MAAM,CAACC,iBAAxB,EAA2C;MACvC,KAAKc,MAAL,GAAc,EAAd;IACH;EACJ;;EACc,OAARC,QAAQ,CAACC,GAAD,EAAM;IACjB,MAAM;MAAET,UAAF;MAAcU,MAAd;MAAsBC,KAAtB;MAA6BP;IAA7B,IAAuCK,GAA7C;IACAT,UAAU,CAACY,qBAAX,CAAiCF,MAAjC,EAAyCC,KAAzC,EAAgDP,KAAhD;EACH;;EACDS,KAAK,CAACF,KAAD,EAAQ;IACT,MAAMR,WAAW,GAAG,KAAKA,WAAzB;;IACA,IAAIA,WAAW,CAACW,MAAhB,EAAwB;MACpB,KAAKC,SAAL;;MACA;IACH;;IACD,MAAMX,KAAK,GAAG,KAAKA,KAAL,EAAd;;IACA,IAAI,KAAKC,MAAL,GAAc,KAAKd,UAAvB,EAAmC;MAC/BY,WAAW,CAACa,IAAZ,CAAiBL,KAAjB;;MACA,IAAI;QACA,MAAM;UAAErB;QAAF,IAAc,IAApB;QACA,MAAMoB,MAAM,GAAGpB,OAAO,CAACqB,KAAD,EAAQP,KAAR,CAAtB;;QACA,IAAI,CAAC,KAAKV,SAAV,EAAqB;UACjB,KAAKkB,qBAAL,CAA2BF,MAA3B,EAAmCC,KAAnC,EAA0CP,KAA1C;QACH,CAFD,MAGK;UACD,MAAMa,KAAK,GAAG;YAAEjB,UAAU,EAAE,IAAd;YAAoBU,MAApB;YAA4BC,KAA5B;YAAmCP;UAAnC,CAAd;UACA,MAAMD,WAAW,GAAG,KAAKA,WAAzB;UACAA,WAAW,CAACe,GAAZ,CAAgB,KAAKxB,SAAL,CAAeyB,QAAf,CAAwBjB,gBAAgB,CAACM,QAAzC,EAAmD,CAAnD,EAAsDS,KAAtD,CAAhB;QACH;MACJ,CAXD,CAYA,OAAOG,CAAP,EAAU;QACNjB,WAAW,CAACkB,KAAZ,CAAkBD,CAAlB;MACH;IACJ,CAjBD,MAkBK;MACD,KAAKb,MAAL,CAAYe,IAAZ,CAAiBX,KAAjB;IACH;EACJ;;EACDC,qBAAqB,CAACF,MAAD,EAASC,KAAT,EAAgBP,KAAhB,EAAuB;IACxC,KAAKC,MAAL;IACA,MAAMF,WAAW,GAAG,KAAKA,WAAzB;IACAA,WAAW,CAACe,GAAZ,CAAgB/B,cAAc,CAACuB,MAAD,EAAS,IAAItB,qBAAJ,CAA0B,IAA1B,CAAT,CAA9B;EACH;;EACD2B,SAAS,GAAG;IACR,KAAKT,YAAL,GAAoB,IAApB;;IACA,IAAI,KAAKA,YAAL,IAAqB,KAAKD,MAAL,KAAgB,CAAzC,EAA4C;MACxC,KAAKF,WAAL,CAAiBoB,QAAjB;IACH;;IACD,KAAKC,WAAL;EACH;;EACDC,UAAU,CAACC,UAAD,EAAa;IACnB,KAAKb,KAAL,CAAWa,UAAX;EACH;;EACDC,cAAc,GAAG;IACb,MAAMpB,MAAM,GAAG,KAAKA,MAApB;IACA,KAAKF,MAAL;;IACA,IAAIE,MAAM,IAAIA,MAAM,CAACqB,MAAP,GAAgB,CAA9B,EAAiC;MAC7B,KAAKf,KAAL,CAAWN,MAAM,CAACsB,KAAP,EAAX;IACH;;IACD,IAAI,KAAKvB,YAAL,IAAqB,KAAKD,MAAL,KAAgB,CAAzC,EAA4C;MACxC,KAAKF,WAAL,CAAiBoB,QAAjB;IACH;EACJ;;AAtEuD"}, "metadata": {}, "sourceType": "module"}