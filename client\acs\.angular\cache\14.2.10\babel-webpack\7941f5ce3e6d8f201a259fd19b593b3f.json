{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable, Inject, QueryList, Directive, Input, InjectionToken, Optional, EventEmitter, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/platform';\nimport { _getFocusedElementPierceShadowDom, normalizePassiveListenerOptions, _getEventTarget, _getShadowRoot } from '@angular/cdk/platform';\nimport { Subject, Subscription, BehaviorSubject, of } from 'rxjs';\nimport { hasModifierKey, A, Z, ZERO, NINE, END, HOME, LEFT_ARROW, RIGHT_ARROW, UP_ARROW, DOWN_ARROW, TAB, ALT, CONTROL, MAC_META, META, SHIFT } from '@angular/cdk/keycodes';\nimport { tap, debounceTime, filter, map, take, skip, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { coerceBooleanProperty, coerceElement } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport { BreakpointObserver } from '@angular/cdk/layout';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** IDs are delimited by an empty space, as per the spec. */\n\nconst ID_DELIMITER = ' ';\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\n\nfunction addAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n\n  if (ids.some(existingId => existingId.trim() == id.trim())) {\n    return;\n  }\n\n  ids.push(id.trim());\n  el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\n\n\nfunction removeAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  const filteredIds = ids.filter(val => val != id.trim());\n\n  if (filteredIds.length) {\n    el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n  } else {\n    el.removeAttribute(attr);\n  }\n}\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\n\n\nfunction getAriaReferenceIds(el, attr) {\n  // Get string array of all individual ids (whitespace delimited) in the attribute value\n  return (el.getAttribute(attr) || '').match(/\\S+/g) || [];\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\n\n\nconst MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\n\nconst CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\n\nconst CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n/** Global incremental identifier for each registered message element. */\n\nlet nextId = 0;\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\n\nclass AriaDescriber {\n  constructor(_document,\n  /**\n   * @deprecated To be turned into a required parameter.\n   * @breaking-change 14.0.0\n   */\n  _platform) {\n    this._platform = _platform;\n    /** Map of all registered message elements that have been placed into the document. */\n\n    this._messageRegistry = new Map();\n    /** Container for all registered messages. */\n\n    this._messagesContainer = null;\n    /** Unique ID for the service. */\n\n    this._id = `${nextId++}`;\n    this._document = _document;\n    this._id = inject(APP_ID) + '-' + nextId++;\n  }\n\n  describe(hostElement, message, role) {\n    if (!this._canBeDescribed(hostElement, message)) {\n      return;\n    }\n\n    const key = getKey(message, role);\n\n    if (typeof message !== 'string') {\n      // We need to ensure that the element has an ID.\n      setMessageId(message, this._id);\n\n      this._messageRegistry.set(key, {\n        messageElement: message,\n        referenceCount: 0\n      });\n    } else if (!this._messageRegistry.has(key)) {\n      this._createMessageElement(message, role);\n    }\n\n    if (!this._isElementDescribedByMessage(hostElement, key)) {\n      this._addMessageReference(hostElement, key);\n    }\n  }\n\n  removeDescription(hostElement, message, role) {\n    if (!message || !this._isElementNode(hostElement)) {\n      return;\n    }\n\n    const key = getKey(message, role);\n\n    if (this._isElementDescribedByMessage(hostElement, key)) {\n      this._removeMessageReference(hostElement, key);\n    } // If the message is a string, it means that it's one that we created for the\n    // consumer so we can remove it safely, otherwise we should leave it in place.\n\n\n    if (typeof message === 'string') {\n      const registeredMessage = this._messageRegistry.get(key);\n\n      if (registeredMessage && registeredMessage.referenceCount === 0) {\n        this._deleteMessageElement(key);\n      }\n    }\n\n    if (this._messagesContainer?.childNodes.length === 0) {\n      this._messagesContainer.remove();\n\n      this._messagesContainer = null;\n    }\n  }\n  /** Unregisters all created message elements and removes the message container. */\n\n\n  ngOnDestroy() {\n    const describedElements = this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`);\n\n    for (let i = 0; i < describedElements.length; i++) {\n      this._removeCdkDescribedByReferenceIds(describedElements[i]);\n\n      describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n\n    this._messagesContainer?.remove();\n    this._messagesContainer = null;\n\n    this._messageRegistry.clear();\n  }\n  /**\n   * Creates a new element in the visually hidden message container element with the message\n   * as its content and adds it to the message registry.\n   */\n\n\n  _createMessageElement(message, role) {\n    const messageElement = this._document.createElement('div');\n\n    setMessageId(messageElement, this._id);\n    messageElement.textContent = message;\n\n    if (role) {\n      messageElement.setAttribute('role', role);\n    }\n\n    this._createMessagesContainer();\n\n    this._messagesContainer.appendChild(messageElement);\n\n    this._messageRegistry.set(getKey(message, role), {\n      messageElement,\n      referenceCount: 0\n    });\n  }\n  /** Deletes the message element from the global messages container. */\n\n\n  _deleteMessageElement(key) {\n    this._messageRegistry.get(key)?.messageElement?.remove();\n\n    this._messageRegistry.delete(key);\n  }\n  /** Creates the global container for all aria-describedby messages. */\n\n\n  _createMessagesContainer() {\n    if (this._messagesContainer) {\n      return;\n    }\n\n    const containerClassName = 'cdk-describedby-message-container';\n\n    const serverContainers = this._document.querySelectorAll(`.${containerClassName}[platform=\"server\"]`);\n\n    for (let i = 0; i < serverContainers.length; i++) {\n      // When going from the server to the client, we may end up in a situation where there's\n      // already a container on the page, but we don't have a reference to it. Clear the\n      // old container so we don't get duplicates. Doing this, instead of emptying the previous\n      // container, should be slightly faster.\n      serverContainers[i].remove();\n    }\n\n    const messagesContainer = this._document.createElement('div'); // We add `visibility: hidden` in order to prevent text in this container from\n    // being searchable by the browser's Ctrl + F functionality.\n    // Screen-readers will still read the description for elements with aria-describedby even\n    // when the description element is not visible.\n\n\n    messagesContainer.style.visibility = 'hidden'; // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n    // the description element doesn't impact page layout.\n\n    messagesContainer.classList.add(containerClassName);\n    messagesContainer.classList.add('cdk-visually-hidden'); // @breaking-change 14.0.0 Remove null check for `_platform`.\n\n    if (this._platform && !this._platform.isBrowser) {\n      messagesContainer.setAttribute('platform', 'server');\n    }\n\n    this._document.body.appendChild(messagesContainer);\n\n    this._messagesContainer = messagesContainer;\n  }\n  /** Removes all cdk-describedby messages that are hosted through the element. */\n\n\n  _removeCdkDescribedByReferenceIds(element) {\n    // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n    const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n    element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n  }\n  /**\n   * Adds a message reference to the element using aria-describedby and increments the registered\n   * message's reference count.\n   */\n\n\n  _addMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key); // Add the aria-describedby reference and set the\n    // describedby_host attribute to mark the element.\n\n\n    addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n    registeredMessage.referenceCount++;\n  }\n  /**\n   * Removes a message reference from the element using aria-describedby\n   * and decrements the registered message's reference count.\n   */\n\n\n  _removeMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key);\n\n    registeredMessage.referenceCount--;\n    removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n  }\n  /** Returns true if the element has been described by the provided message ID. */\n\n\n  _isElementDescribedByMessage(element, key) {\n    const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n\n    const registeredMessage = this._messageRegistry.get(key);\n\n    const messageId = registeredMessage && registeredMessage.messageElement.id;\n    return !!messageId && referenceIds.indexOf(messageId) != -1;\n  }\n  /** Determines whether a message can be described on a particular element. */\n\n\n  _canBeDescribed(element, message) {\n    if (!this._isElementNode(element)) {\n      return false;\n    }\n\n    if (message && typeof message === 'object') {\n      // We'd have to make some assumptions about the description element's text, if the consumer\n      // passed in an element. Assume that if an element is passed in, the consumer has verified\n      // that it can be used as a description.\n      return true;\n    }\n\n    const trimmedMessage = message == null ? '' : `${message}`.trim();\n    const ariaLabel = element.getAttribute('aria-label'); // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n    // element, because screen readers will end up reading out the same text twice in a row.\n\n    return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n  }\n  /** Checks whether a node is an Element node. */\n\n\n  _isElementNode(element) {\n    return element.nodeType === this._document.ELEMENT_NODE;\n  }\n\n}\n\nAriaDescriber.ɵfac = function AriaDescriber_Factory(t) {\n  return new (t || AriaDescriber)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1.Platform));\n};\n\nAriaDescriber.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: AriaDescriber,\n  factory: AriaDescriber.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AriaDescriber, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1.Platform\n    }];\n  }, null);\n})();\n/** Gets a key that can be used to look messages up in the registry. */\n\n\nfunction getKey(message, role) {\n  return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n/** Assigns a unique ID to an element, if it doesn't have one already. */\n\n\nfunction setMessageId(element, serviceId) {\n  if (!element.id) {\n    element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${serviceId}-${nextId++}`;\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\n\n\nclass ListKeyManager {\n  constructor(_items) {\n    this._items = _items;\n    this._activeItemIndex = -1;\n    this._activeItem = null;\n    this._wrap = false;\n    this._letterKeyStream = new Subject();\n    this._typeaheadSubscription = Subscription.EMPTY;\n    this._vertical = true;\n    this._allowedModifierKeys = [];\n    this._homeAndEnd = false;\n    /**\n     * Predicate function that can be used to check whether an item should be skipped\n     * by the key manager. By default, disabled items are skipped.\n     */\n\n    this._skipPredicateFn = item => item.disabled; // Buffer for the letters that the user has pressed when the typeahead option is turned on.\n\n\n    this._pressedLetters = [];\n    /**\n     * Stream that emits any time the TAB key is pressed, so components can react\n     * when focus is shifted off of the list.\n     */\n\n    this.tabOut = new Subject();\n    /** Stream that emits whenever the active item of the list manager changes. */\n\n    this.change = new Subject(); // We allow for the items to be an array because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n\n    if (_items instanceof QueryList) {\n      _items.changes.subscribe(newItems => {\n        if (this._activeItem) {\n          const itemArray = newItems.toArray();\n          const newIndex = itemArray.indexOf(this._activeItem);\n\n          if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n            this._activeItemIndex = newIndex;\n          }\n        }\n      });\n    }\n  }\n  /**\n   * Sets the predicate function that determines which items should be skipped by the\n   * list key manager.\n   * @param predicate Function that determines whether the given item should be skipped.\n   */\n\n\n  skipPredicate(predicate) {\n    this._skipPredicateFn = predicate;\n    return this;\n  }\n  /**\n   * Configures wrapping mode, which determines whether the active item will wrap to\n   * the other end of list when there are no more items in the given direction.\n   * @param shouldWrap Whether the list should wrap when reaching the end.\n   */\n\n\n  withWrap(shouldWrap = true) {\n    this._wrap = shouldWrap;\n    return this;\n  }\n  /**\n   * Configures whether the key manager should be able to move the selection vertically.\n   * @param enabled Whether vertical selection should be enabled.\n   */\n\n\n  withVerticalOrientation(enabled = true) {\n    this._vertical = enabled;\n    return this;\n  }\n  /**\n   * Configures the key manager to move the selection horizontally.\n   * Passing in `null` will disable horizontal movement.\n   * @param direction Direction in which the selection can be moved.\n   */\n\n\n  withHorizontalOrientation(direction) {\n    this._horizontal = direction;\n    return this;\n  }\n  /**\n   * Modifier keys which are allowed to be held down and whose default actions will be prevented\n   * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n   */\n\n\n  withAllowedModifierKeys(keys) {\n    this._allowedModifierKeys = keys;\n    return this;\n  }\n  /**\n   * Turns on typeahead mode which allows users to set the active item by typing.\n   * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n   */\n\n\n  withTypeAhead(debounceInterval = 200) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && this._items.length && this._items.some(item => typeof item.getLabel !== 'function')) {\n      throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n    }\n\n    this._typeaheadSubscription.unsubscribe(); // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n    // and convert those letters back into a string. Afterwards find the first item that starts\n    // with that string and select it.\n\n\n    this._typeaheadSubscription = this._letterKeyStream.pipe(tap(letter => this._pressedLetters.push(letter)), debounceTime(debounceInterval), filter(() => this._pressedLetters.length > 0), map(() => this._pressedLetters.join(''))).subscribe(inputString => {\n      const items = this._getItemsArray(); // Start at 1 because we want to start searching at the item immediately\n      // following the current active item.\n\n\n      for (let i = 1; i < items.length + 1; i++) {\n        const index = (this._activeItemIndex + i) % items.length;\n        const item = items[index];\n\n        if (!this._skipPredicateFn(item) && item.getLabel().toUpperCase().trim().indexOf(inputString) === 0) {\n          this.setActiveItem(index);\n          break;\n        }\n      }\n\n      this._pressedLetters = [];\n    });\n    return this;\n  }\n  /**\n   * Configures the key manager to activate the first and last items\n   * respectively when the Home or End key is pressed.\n   * @param enabled Whether pressing the Home or End key activates the first/last item.\n   */\n\n\n  withHomeAndEnd(enabled = true) {\n    this._homeAndEnd = enabled;\n    return this;\n  }\n\n  setActiveItem(item) {\n    const previousActiveItem = this._activeItem;\n    this.updateActiveItem(item);\n\n    if (this._activeItem !== previousActiveItem) {\n      this.change.next(this._activeItemIndex);\n    }\n  }\n  /**\n   * Sets the active item depending on the key event passed in.\n   * @param event Keyboard event to be used for determining which element should be active.\n   */\n\n\n  onKeydown(event) {\n    const keyCode = event.keyCode;\n    const modifiers = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n    const isModifierAllowed = modifiers.every(modifier => {\n      return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n    });\n\n    switch (keyCode) {\n      case TAB:\n        this.tabOut.next();\n        return;\n\n      case DOWN_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case UP_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case RIGHT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case LEFT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case HOME:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setFirstItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case END:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setLastItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      default:\n        if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n          // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n          // otherwise fall back to resolving alphanumeric characters via the keyCode.\n          if (event.key && event.key.length === 1) {\n            this._letterKeyStream.next(event.key.toLocaleUpperCase());\n          } else if (keyCode >= A && keyCode <= Z || keyCode >= ZERO && keyCode <= NINE) {\n            this._letterKeyStream.next(String.fromCharCode(keyCode));\n          }\n        } // Note that we return here, in order to avoid preventing\n        // the default action of non-navigational keys.\n\n\n        return;\n    }\n\n    this._pressedLetters = [];\n    event.preventDefault();\n  }\n  /** Index of the currently active item. */\n\n\n  get activeItemIndex() {\n    return this._activeItemIndex;\n  }\n  /** The active item. */\n\n\n  get activeItem() {\n    return this._activeItem;\n  }\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n\n\n  isTyping() {\n    return this._pressedLetters.length > 0;\n  }\n  /** Sets the active item to the first enabled item in the list. */\n\n\n  setFirstItemActive() {\n    this._setActiveItemByIndex(0, 1);\n  }\n  /** Sets the active item to the last enabled item in the list. */\n\n\n  setLastItemActive() {\n    this._setActiveItemByIndex(this._items.length - 1, -1);\n  }\n  /** Sets the active item to the next enabled item in the list. */\n\n\n  setNextItemActive() {\n    this._activeItemIndex < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n  }\n  /** Sets the active item to a previous enabled item in the list. */\n\n\n  setPreviousItemActive() {\n    this._activeItemIndex < 0 && this._wrap ? this.setLastItemActive() : this._setActiveItemByDelta(-1);\n  }\n\n  updateActiveItem(item) {\n    const itemArray = this._getItemsArray();\n\n    const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n    const activeItem = itemArray[index]; // Explicitly check for `null` and `undefined` because other falsy values are valid.\n\n    this._activeItem = activeItem == null ? null : activeItem;\n    this._activeItemIndex = index;\n  }\n  /**\n   * This method sets the active item, given a list of items and the delta between the\n   * currently active item and the new active item. It will calculate differently\n   * depending on whether wrap mode is turned on.\n   */\n\n\n  _setActiveItemByDelta(delta) {\n    this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n  }\n  /**\n   * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n   * down the list until it finds an item that is not disabled, and it will wrap if it\n   * encounters either end of the list.\n   */\n\n\n  _setActiveInWrapMode(delta) {\n    const items = this._getItemsArray();\n\n    for (let i = 1; i <= items.length; i++) {\n      const index = (this._activeItemIndex + delta * i + items.length) % items.length;\n      const item = items[index];\n\n      if (!this._skipPredicateFn(item)) {\n        this.setActiveItem(index);\n        return;\n      }\n    }\n  }\n  /**\n   * Sets the active item properly given the default mode. In other words, it will\n   * continue to move down the list until it finds an item that is not disabled. If\n   * it encounters either end of the list, it will stop and not wrap.\n   */\n\n\n  _setActiveInDefaultMode(delta) {\n    this._setActiveItemByIndex(this._activeItemIndex + delta, delta);\n  }\n  /**\n   * Sets the active item to the first enabled item starting at the index specified. If the\n   * item is disabled, it will move in the fallbackDelta direction until it either\n   * finds an enabled item or encounters the end of the list.\n   */\n\n\n  _setActiveItemByIndex(index, fallbackDelta) {\n    const items = this._getItemsArray();\n\n    if (!items[index]) {\n      return;\n    }\n\n    while (this._skipPredicateFn(items[index])) {\n      index += fallbackDelta;\n\n      if (!items[index]) {\n        return;\n      }\n    }\n\n    this.setActiveItem(index);\n  }\n  /** Returns the items as an array. */\n\n\n  _getItemsArray() {\n    return this._items instanceof QueryList ? this._items.toArray() : this._items;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass ActiveDescendantKeyManager extends ListKeyManager {\n  setActiveItem(index) {\n    if (this.activeItem) {\n      this.activeItem.setInactiveStyles();\n    }\n\n    super.setActiveItem(index);\n\n    if (this.activeItem) {\n      this.activeItem.setActiveStyles();\n    }\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass FocusKeyManager extends ListKeyManager {\n  constructor() {\n    super(...arguments);\n    this._origin = 'program';\n  }\n  /**\n   * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n   * @param origin Focus origin to be used when focusing items.\n   */\n\n\n  setFocusOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n\n  setActiveItem(item) {\n    super.setActiveItem(item);\n\n    if (this.activeItem) {\n      this.activeItem.focus(this._origin);\n    }\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Configuration for the isFocusable method.\n */\n\n\nclass IsFocusableConfig {\n  constructor() {\n    /**\n     * Whether to count an element as focusable even if it is not currently visible.\n     */\n    this.ignoreVisibility = false;\n  }\n\n} // The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n\n/**\n * Utility for checking the interactivity of an element, such as whether is is focusable or\n * tabbable.\n */\n\n\nclass InteractivityChecker {\n  constructor(_platform) {\n    this._platform = _platform;\n  }\n  /**\n   * Gets whether an element is disabled.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is disabled.\n   */\n\n\n  isDisabled(element) {\n    // This does not capture some cases, such as a non-form control with a disabled attribute or\n    // a form control inside of a disabled form, but should capture the most common cases.\n    return element.hasAttribute('disabled');\n  }\n  /**\n   * Gets whether an element is visible for the purposes of interactivity.\n   *\n   * This will capture states like `display: none` and `visibility: hidden`, but not things like\n   * being clipped by an `overflow: hidden` parent or being outside the viewport.\n   *\n   * @returns Whether the element is visible.\n   */\n\n\n  isVisible(element) {\n    return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n  }\n  /**\n   * Gets whether an element can be reached via Tab key.\n   * Assumes that the element has already been checked with isFocusable.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is tabbable.\n   */\n\n\n  isTabbable(element) {\n    // Nothing is tabbable on the server 😎\n    if (!this._platform.isBrowser) {\n      return false;\n    }\n\n    const frameElement = getFrameElement(getWindow(element));\n\n    if (frameElement) {\n      // Frame elements inherit their tabindex onto all child elements.\n      if (getTabIndexValue(frameElement) === -1) {\n        return false;\n      } // Browsers disable tabbing to an element inside of an invisible frame.\n\n\n      if (!this.isVisible(frameElement)) {\n        return false;\n      }\n    }\n\n    let nodeName = element.nodeName.toLowerCase();\n    let tabIndexValue = getTabIndexValue(element);\n\n    if (element.hasAttribute('contenteditable')) {\n      return tabIndexValue !== -1;\n    }\n\n    if (nodeName === 'iframe' || nodeName === 'object') {\n      // The frame or object's content may be tabbable depending on the content, but it's\n      // not possibly to reliably detect the content of the frames. We always consider such\n      // elements as non-tabbable.\n      return false;\n    } // In iOS, the browser only considers some specific elements as tabbable.\n\n\n    if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n      return false;\n    }\n\n    if (nodeName === 'audio') {\n      // Audio elements without controls enabled are never tabbable, regardless\n      // of the tabindex attribute explicitly being set.\n      if (!element.hasAttribute('controls')) {\n        return false;\n      } // Audio elements with controls are by default tabbable unless the\n      // tabindex attribute is set to `-1` explicitly.\n\n\n      return tabIndexValue !== -1;\n    }\n\n    if (nodeName === 'video') {\n      // For all video elements, if the tabindex attribute is set to `-1`, the video\n      // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n      // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n      // tabindex attribute is the source of truth here.\n      if (tabIndexValue === -1) {\n        return false;\n      } // If the tabindex is explicitly set, and not `-1` (as per check before), the\n      // video element is always tabbable (regardless of whether it has controls or not).\n\n\n      if (tabIndexValue !== null) {\n        return true;\n      } // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n      // has controls enabled. Firefox is special as videos are always tabbable regardless\n      // of whether there are controls or not.\n\n\n      return this._platform.FIREFOX || element.hasAttribute('controls');\n    }\n\n    return element.tabIndex >= 0;\n  }\n  /**\n   * Gets whether an element can be focused by the user.\n   *\n   * @param element Element to be checked.\n   * @param config The config object with options to customize this method's behavior\n   * @returns Whether the element is focusable.\n   */\n\n\n  isFocusable(element, config) {\n    // Perform checks in order of left to most expensive.\n    // Again, naive approach that does not capture many edge cases and browser quirks.\n    return isPotentiallyFocusable(element) && !this.isDisabled(element) && (config?.ignoreVisibility || this.isVisible(element));\n  }\n\n}\n\nInteractivityChecker.ɵfac = function InteractivityChecker_Factory(t) {\n  return new (t || InteractivityChecker)(i0.ɵɵinject(i1.Platform));\n};\n\nInteractivityChecker.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: InteractivityChecker,\n  factory: InteractivityChecker.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InteractivityChecker, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.Platform\n    }];\n  }, null);\n})();\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\n\n\nfunction getFrameElement(window) {\n  try {\n    return window.frameElement;\n  } catch {\n    return null;\n  }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\n\n\nfunction hasGeometry(element) {\n  // Use logic from jQuery to check for an invisible element.\n  // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n  return !!(element.offsetWidth || element.offsetHeight || typeof element.getClientRects === 'function' && element.getClientRects().length);\n}\n/** Gets whether an element's  */\n\n\nfunction isNativeFormElement(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  return nodeName === 'input' || nodeName === 'select' || nodeName === 'button' || nodeName === 'textarea';\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\n\n\nfunction isHiddenInput(element) {\n  return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\n\n\nfunction isAnchorWithHref(element) {\n  return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\n\n\nfunction isInputElement(element) {\n  return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\n\n\nfunction isAnchorElement(element) {\n  return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\n\n\nfunction hasValidTabIndex(element) {\n  if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n    return false;\n  }\n\n  let tabIndex = element.getAttribute('tabindex');\n  return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\n\n\nfunction getTabIndexValue(element) {\n  if (!hasValidTabIndex(element)) {\n    return null;\n  } // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n\n\n  const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n  return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\n\n\nfunction isPotentiallyTabbableIOS(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  let inputType = nodeName === 'input' && element.type;\n  return inputType === 'text' || inputType === 'password' || nodeName === 'select' || nodeName === 'textarea';\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\n\n\nfunction isPotentiallyFocusable(element) {\n  // Inputs are potentially focusable *unless* they're type=\"hidden\".\n  if (isHiddenInput(element)) {\n    return false;\n  }\n\n  return isNativeFormElement(element) || isAnchorWithHref(element) || element.hasAttribute('contenteditable') || hasValidTabIndex(element);\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\n\n\nfunction getWindow(node) {\n  // ownerDocument is null if `node` itself *is* a document.\n  return node.ownerDocument && node.ownerDocument.defaultView || window;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n *\n * @deprecated Use `ConfigurableFocusTrap` instead.\n * @breaking-change 11.0.0\n */\n\n\nclass FocusTrap {\n  constructor(_element, _checker, _ngZone, _document, deferAnchors = false) {\n    this._element = _element;\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._hasAttached = false; // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n\n    this.startAnchorListener = () => this.focusLastTabbableElement();\n\n    this.endAnchorListener = () => this.focusFirstTabbableElement();\n\n    this._enabled = true;\n\n    if (!deferAnchors) {\n      this.attachAnchors();\n    }\n  }\n  /** Whether the focus trap is active. */\n\n\n  get enabled() {\n    return this._enabled;\n  }\n\n  set enabled(value) {\n    this._enabled = value;\n\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(value, this._startAnchor);\n\n      this._toggleAnchorTabIndex(value, this._endAnchor);\n    }\n  }\n  /** Destroys the focus trap by cleaning up the anchors. */\n\n\n  destroy() {\n    const startAnchor = this._startAnchor;\n    const endAnchor = this._endAnchor;\n\n    if (startAnchor) {\n      startAnchor.removeEventListener('focus', this.startAnchorListener);\n      startAnchor.remove();\n    }\n\n    if (endAnchor) {\n      endAnchor.removeEventListener('focus', this.endAnchorListener);\n      endAnchor.remove();\n    }\n\n    this._startAnchor = this._endAnchor = null;\n    this._hasAttached = false;\n  }\n  /**\n   * Inserts the anchors into the DOM. This is usually done automatically\n   * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n   * @returns Whether the focus trap managed to attach successfully. This may not be the case\n   * if the target element isn't currently in the DOM.\n   */\n\n\n  attachAnchors() {\n    // If we're not on the browser, there can be no focus to trap.\n    if (this._hasAttached) {\n      return true;\n    }\n\n    this._ngZone.runOutsideAngular(() => {\n      if (!this._startAnchor) {\n        this._startAnchor = this._createAnchor();\n\n        this._startAnchor.addEventListener('focus', this.startAnchorListener);\n      }\n\n      if (!this._endAnchor) {\n        this._endAnchor = this._createAnchor();\n\n        this._endAnchor.addEventListener('focus', this.endAnchorListener);\n      }\n    });\n\n    if (this._element.parentNode) {\n      this._element.parentNode.insertBefore(this._startAnchor, this._element);\n\n      this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n\n      this._hasAttached = true;\n    }\n\n    return this._hasAttached;\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses the first tabbable element.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n\n\n  focusInitialElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the first tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n\n\n  focusFirstTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the last tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n\n\n  focusLastTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n    });\n  }\n  /**\n   * Get the specified boundary element of the trapped region.\n   * @param bound The boundary to get (start or end of trapped region).\n   * @returns The boundary element.\n   */\n\n\n  _getRegionBoundary(bound) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      for (let i = 0; i < markers.length; i++) {\n        // @breaking-change 8.0.0\n        if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated ` + `attribute will be removed in 8.0.0.`, markers[i]);\n        } else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` + `will be removed in 8.0.0.`, markers[i]);\n        }\n      }\n    }\n\n    if (bound == 'start') {\n      return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n    }\n\n    return markers.length ? markers[markers.length - 1] : this._getLastTabbableElement(this._element);\n  }\n  /**\n   * Focuses the element that should be focused when the focus trap is initialized.\n   * @returns Whether focus was moved successfully.\n   */\n\n\n  focusInitialElement(options) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n\n    if (redirectToElement) {\n      // @breaking-change 8.0.0\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` + `use 'cdkFocusInitial' instead. The deprecated attribute ` + `will be removed in 8.0.0`, redirectToElement);\n      } // Warn the consumer if the element they've pointed to\n      // isn't focusable, when not in production mode.\n\n\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._checker.isFocusable(redirectToElement)) {\n        console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n      }\n\n      if (!this._checker.isFocusable(redirectToElement)) {\n        const focusableChild = this._getFirstTabbableElement(redirectToElement);\n\n        focusableChild?.focus(options);\n        return !!focusableChild;\n      }\n\n      redirectToElement.focus(options);\n      return true;\n    }\n\n    return this.focusFirstTabbableElement(options);\n  }\n  /**\n   * Focuses the first tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n\n\n  focusFirstTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('start');\n\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n\n    return !!redirectToElement;\n  }\n  /**\n   * Focuses the last tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n\n\n  focusLastTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('end');\n\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n\n    return !!redirectToElement;\n  }\n  /**\n   * Checks whether the focus trap has successfully been attached.\n   */\n\n\n  hasAttached() {\n    return this._hasAttached;\n  }\n  /** Get the first tabbable element from a DOM subtree (inclusive). */\n\n\n  _getFirstTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n\n    const children = root.children;\n\n    for (let i = 0; i < children.length; i++) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getFirstTabbableElement(children[i]) : null;\n\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n\n    return null;\n  }\n  /** Get the last tabbable element from a DOM subtree (inclusive). */\n\n\n  _getLastTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    } // Iterate in reverse DOM order.\n\n\n    const children = root.children;\n\n    for (let i = children.length - 1; i >= 0; i--) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getLastTabbableElement(children[i]) : null;\n\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n\n    return null;\n  }\n  /** Creates an anchor element. */\n\n\n  _createAnchor() {\n    const anchor = this._document.createElement('div');\n\n    this._toggleAnchorTabIndex(this._enabled, anchor);\n\n    anchor.classList.add('cdk-visually-hidden');\n    anchor.classList.add('cdk-focus-trap-anchor');\n    anchor.setAttribute('aria-hidden', 'true');\n    return anchor;\n  }\n  /**\n   * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n   * @param isEnabled Whether the focus trap is enabled.\n   * @param anchor Anchor on which to toggle the tabindex.\n   */\n\n\n  _toggleAnchorTabIndex(isEnabled, anchor) {\n    // Remove the tabindex completely, rather than setting it to -1, because if the\n    // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n    isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n  }\n  /**\n   * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n   * @param enabled: Whether the anchors should trap Tab.\n   */\n\n\n  toggleAnchors(enabled) {\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(enabled, this._startAnchor);\n\n      this._toggleAnchorTabIndex(enabled, this._endAnchor);\n    }\n  }\n  /** Executes a function when the zone is stable. */\n\n\n  _executeOnStable(fn) {\n    if (this._ngZone.isStable) {\n      fn();\n    } else {\n      this._ngZone.onStable.pipe(take(1)).subscribe(fn);\n    }\n  }\n\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n * @deprecated Use `ConfigurableFocusTrapFactory` instead.\n * @breaking-change 11.0.0\n */\n\n\nclass FocusTrapFactory {\n  constructor(_checker, _ngZone, _document) {\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._document = _document;\n  }\n  /**\n   * Creates a focus-trapped region around the given element.\n   * @param element The element around which focus will be trapped.\n   * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n   *     manually by the user.\n   * @returns The created focus trap instance.\n   */\n\n\n  create(element, deferCaptureElements = false) {\n    return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements);\n  }\n\n}\n\nFocusTrapFactory.ɵfac = function FocusTrapFactory_Factory(t) {\n  return new (t || FocusTrapFactory)(i0.ɵɵinject(InteractivityChecker), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n};\n\nFocusTrapFactory.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: FocusTrapFactory,\n  factory: FocusTrapFactory.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: InteractivityChecker\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/** Directive for trapping focus within a region. */\n\n\nclass CdkTrapFocus {\n  constructor(_elementRef, _focusTrapFactory,\n  /**\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 13.0.0\n   */\n  _document) {\n    this._elementRef = _elementRef;\n    this._focusTrapFactory = _focusTrapFactory;\n    /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n\n    this._previouslyFocusedElement = null;\n    this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n  }\n  /** Whether the focus trap is active. */\n\n\n  get enabled() {\n    return this.focusTrap.enabled;\n  }\n\n  set enabled(value) {\n    this.focusTrap.enabled = coerceBooleanProperty(value);\n  }\n  /**\n   * Whether the directive should automatically move focus into the trapped region upon\n   * initialization and return focus to the previous activeElement upon destruction.\n   */\n\n\n  get autoCapture() {\n    return this._autoCapture;\n  }\n\n  set autoCapture(value) {\n    this._autoCapture = coerceBooleanProperty(value);\n  }\n\n  ngOnDestroy() {\n    this.focusTrap.destroy(); // If we stored a previously focused element when using autoCapture, return focus to that\n    // element now that the trapped region is being destroyed.\n\n    if (this._previouslyFocusedElement) {\n      this._previouslyFocusedElement.focus();\n\n      this._previouslyFocusedElement = null;\n    }\n  }\n\n  ngAfterContentInit() {\n    this.focusTrap.attachAnchors();\n\n    if (this.autoCapture) {\n      this._captureFocus();\n    }\n  }\n\n  ngDoCheck() {\n    if (!this.focusTrap.hasAttached()) {\n      this.focusTrap.attachAnchors();\n    }\n  }\n\n  ngOnChanges(changes) {\n    const autoCaptureChange = changes['autoCapture'];\n\n    if (autoCaptureChange && !autoCaptureChange.firstChange && this.autoCapture && this.focusTrap.hasAttached()) {\n      this._captureFocus();\n    }\n  }\n\n  _captureFocus() {\n    this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n    this.focusTrap.focusInitialElementWhenReady();\n  }\n\n}\n\nCdkTrapFocus.ɵfac = function CdkTrapFocus_Factory(t) {\n  return new (t || CdkTrapFocus)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(FocusTrapFactory), i0.ɵɵdirectiveInject(DOCUMENT));\n};\n\nCdkTrapFocus.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkTrapFocus,\n  selectors: [[\"\", \"cdkTrapFocus\", \"\"]],\n  inputs: {\n    enabled: [\"cdkTrapFocus\", \"enabled\"],\n    autoCapture: [\"cdkTrapFocusAutoCapture\", \"autoCapture\"]\n  },\n  exportAs: [\"cdkTrapFocus\"],\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTrapFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTrapFocus]',\n      exportAs: 'cdkTrapFocus'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: FocusTrapFactory\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, {\n    enabled: [{\n      type: Input,\n      args: ['cdkTrapFocus']\n    }],\n    autoCapture: [{\n      type: Input,\n      args: ['cdkTrapFocusAutoCapture']\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\n\n\nclass ConfigurableFocusTrap extends FocusTrap {\n  constructor(_element, _checker, _ngZone, _document, _focusTrapManager, _inertStrategy, config) {\n    super(_element, _checker, _ngZone, _document, config.defer);\n    this._focusTrapManager = _focusTrapManager;\n    this._inertStrategy = _inertStrategy;\n\n    this._focusTrapManager.register(this);\n  }\n  /** Whether the FocusTrap is enabled. */\n\n\n  get enabled() {\n    return this._enabled;\n  }\n\n  set enabled(value) {\n    this._enabled = value;\n\n    if (this._enabled) {\n      this._focusTrapManager.register(this);\n    } else {\n      this._focusTrapManager.deregister(this);\n    }\n  }\n  /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n\n\n  destroy() {\n    this._focusTrapManager.deregister(this);\n\n    super.destroy();\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n\n\n  _enable() {\n    this._inertStrategy.preventFocus(this);\n\n    this.toggleAnchors(true);\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n\n\n  _disable() {\n    this._inertStrategy.allowFocus(this);\n\n    this.toggleAnchors(false);\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** The injection token used to specify the inert strategy. */\n\n\nconst FOCUS_TRAP_INERT_STRATEGY = new InjectionToken('FOCUS_TRAP_INERT_STRATEGY');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\n\nclass EventListenerFocusTrapInertStrategy {\n  constructor() {\n    /** Focus event handler. */\n    this._listener = null;\n  }\n  /** Adds a document event listener that keeps focus inside the FocusTrap. */\n\n\n  preventFocus(focusTrap) {\n    // Ensure there's only one listener per document\n    if (this._listener) {\n      focusTrap._document.removeEventListener('focus', this._listener, true);\n    }\n\n    this._listener = e => this._trapFocus(focusTrap, e);\n\n    focusTrap._ngZone.runOutsideAngular(() => {\n      focusTrap._document.addEventListener('focus', this._listener, true);\n    });\n  }\n  /** Removes the event listener added in preventFocus. */\n\n\n  allowFocus(focusTrap) {\n    if (!this._listener) {\n      return;\n    }\n\n    focusTrap._document.removeEventListener('focus', this._listener, true);\n\n    this._listener = null;\n  }\n  /**\n   * Refocuses the first element in the FocusTrap if the focus event target was outside\n   * the FocusTrap.\n   *\n   * This is an event listener callback. The event listener is added in runOutsideAngular,\n   * so all this code runs outside Angular as well.\n   */\n\n\n  _trapFocus(focusTrap, event) {\n    const target = event.target;\n    const focusTrapRoot = focusTrap._element; // Don't refocus if target was in an overlay, because the overlay might be associated\n    // with an element inside the FocusTrap, ex. mat-select.\n\n    if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n      // Some legacy FocusTrap usages have logic that focuses some element on the page\n      // just before FocusTrap is destroyed. For backwards compatibility, wait\n      // to be sure FocusTrap is still enabled before refocusing.\n      setTimeout(() => {\n        // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n        if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n          focusTrap.focusFirstTabbableElement();\n        }\n      });\n    }\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\n\n\nclass FocusTrapManager {\n  constructor() {\n    // A stack of the FocusTraps on the page. Only the FocusTrap at the\n    // top of the stack is active.\n    this._focusTrapStack = [];\n  }\n  /**\n   * Disables the FocusTrap at the top of the stack, and then pushes\n   * the new FocusTrap onto the stack.\n   */\n\n\n  register(focusTrap) {\n    // Dedupe focusTraps that register multiple times.\n    this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n    let stack = this._focusTrapStack;\n\n    if (stack.length) {\n      stack[stack.length - 1]._disable();\n    }\n\n    stack.push(focusTrap);\n\n    focusTrap._enable();\n  }\n  /**\n   * Removes the FocusTrap from the stack, and activates the\n   * FocusTrap that is the new top of the stack.\n   */\n\n\n  deregister(focusTrap) {\n    focusTrap._disable();\n\n    const stack = this._focusTrapStack;\n    const i = stack.indexOf(focusTrap);\n\n    if (i !== -1) {\n      stack.splice(i, 1);\n\n      if (stack.length) {\n        stack[stack.length - 1]._enable();\n      }\n    }\n  }\n\n}\n\nFocusTrapManager.ɵfac = function FocusTrapManager_Factory(t) {\n  return new (t || FocusTrapManager)();\n};\n\nFocusTrapManager.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: FocusTrapManager,\n  factory: FocusTrapManager.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapManager, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Factory that allows easy instantiation of configurable focus traps. */\n\n\nclass ConfigurableFocusTrapFactory {\n  constructor(_checker, _ngZone, _focusTrapManager, _document, _inertStrategy) {\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._focusTrapManager = _focusTrapManager;\n    this._document = _document; // TODO split up the strategies into different modules, similar to DateAdapter.\n\n    this._inertStrategy = _inertStrategy || new EventListenerFocusTrapInertStrategy();\n  }\n\n  create(element, config = {\n    defer: false\n  }) {\n    let configObject;\n\n    if (typeof config === 'boolean') {\n      configObject = {\n        defer: config\n      };\n    } else {\n      configObject = config;\n    }\n\n    return new ConfigurableFocusTrap(element, this._checker, this._ngZone, this._document, this._focusTrapManager, this._inertStrategy, configObject);\n  }\n\n}\n\nConfigurableFocusTrapFactory.ɵfac = function ConfigurableFocusTrapFactory_Factory(t) {\n  return new (t || ConfigurableFocusTrapFactory)(i0.ɵɵinject(InteractivityChecker), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(FocusTrapManager), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(FOCUS_TRAP_INERT_STRATEGY, 8));\n};\n\nConfigurableFocusTrapFactory.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ConfigurableFocusTrapFactory,\n  factory: ConfigurableFocusTrapFactory.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ConfigurableFocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: InteractivityChecker\n    }, {\n      type: i0.NgZone\n    }, {\n      type: FocusTrapManager\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [FOCUS_TRAP_INERT_STRATEGY]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */\n\n\nfunction isFakeMousedownFromScreenReader(event) {\n  // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on\n  // a clickable element. We can distinguish these events when both `offsetX` and `offsetY` are\n  // zero or `event.buttons` is zero, depending on the browser:\n  // - `event.buttons` works on Firefox, but fails on Chrome.\n  // - `offsetX` and `offsetY` work on Chrome, but fail on Firefox.\n  // Note that there's an edge case where the user could click the 0x0 spot of the\n  // screen themselves, but that is unlikely to contain interactive elements.\n  return event.buttons === 0 || event.offsetX === 0 && event.offsetY === 0;\n}\n/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */\n\n\nfunction isFakeTouchstartFromScreenReader(event) {\n  const touch = event.touches && event.touches[0] || event.changedTouches && event.changedTouches[0]; // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`\n  // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,\n  // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10\n  // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.\n\n  return !!touch && touch.identifier === -1 && (touch.radiusX == null || touch.radiusX === 1) && (touch.radiusY == null || touch.radiusY === 1);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\n\n\nconst INPUT_MODALITY_DETECTOR_OPTIONS = new InjectionToken('cdk-input-modality-detector-options');\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\n\nconst INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS = {\n  ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT]\n};\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\n\nconst TOUCH_BUFFER_MS = 650;\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\n\nconst modalityEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\n\nclass InputModalityDetector {\n  constructor(_platform, ngZone, document, options) {\n    this._platform = _platform;\n    /**\n     * The most recently detected input modality event target. Is null if no input modality has been\n     * detected or if the associated event target is null for some unknown reason.\n     */\n\n    this._mostRecentTarget = null;\n    /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n\n    this._modality = new BehaviorSubject(null);\n    /**\n     * The timestamp of the last touch input modality. Used to determine whether mousedown events\n     * should be attributed to mouse or touch.\n     */\n\n    this._lastTouchMs = 0;\n    /**\n     * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n     * bound.\n     */\n\n    this._onKeydown = event => {\n      // If this is one of the keys we should ignore, then ignore it and don't update the input\n      // modality to keyboard.\n      if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n        return;\n      }\n\n      this._modality.next('keyboard');\n\n      this._mostRecentTarget = _getEventTarget(event);\n    };\n    /**\n     * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n     * gets bound.\n     */\n\n\n    this._onMousedown = event => {\n      // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n      // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n      // after the previous touch event.\n      if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n        return;\n      } // Fake mousedown events are fired by some screen readers when controls are activated by the\n      // screen reader. Attribute them to keyboard input modality.\n\n\n      this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n\n      this._mostRecentTarget = _getEventTarget(event);\n    };\n    /**\n     * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n     * gets bound.\n     */\n\n\n    this._onTouchstart = event => {\n      // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n      // events are fired. Again, attribute to keyboard input modality.\n      if (isFakeTouchstartFromScreenReader(event)) {\n        this._modality.next('keyboard');\n\n        return;\n      } // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n      // triggered via mouse vs touch.\n\n\n      this._lastTouchMs = Date.now();\n\n      this._modality.next('touch');\n\n      this._mostRecentTarget = _getEventTarget(event);\n    };\n\n    this._options = { ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n      ...options\n    }; // Skip the first emission as it's null.\n\n    this.modalityDetected = this._modality.pipe(skip(1));\n    this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged()); // If we're not in a browser, this service should do nothing, as there's no relevant input\n    // modality to detect.\n\n    if (_platform.isBrowser) {\n      ngZone.runOutsideAngular(() => {\n        document.addEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n        document.addEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n        document.addEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n      });\n    }\n  }\n  /** The most recently detected input modality. */\n\n\n  get mostRecentModality() {\n    return this._modality.value;\n  }\n\n  ngOnDestroy() {\n    this._modality.complete();\n\n    if (this._platform.isBrowser) {\n      document.removeEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n      document.removeEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n      document.removeEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n    }\n  }\n\n}\n\nInputModalityDetector.ɵfac = function InputModalityDetector_Factory(t) {\n  return new (t || InputModalityDetector)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(INPUT_MODALITY_DETECTOR_OPTIONS, 8));\n};\n\nInputModalityDetector.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: InputModalityDetector,\n  factory: InputModalityDetector.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputModalityDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.Platform\n    }, {\n      type: i0.NgZone\n    }, {\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [INPUT_MODALITY_DETECTOR_OPTIONS]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken('liveAnnouncerElement', {\n  providedIn: 'root',\n  factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY\n});\n/** @docs-private */\n\nfunction LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {\n  return null;\n}\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\n\n\nconst LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nclass LiveAnnouncer {\n  constructor(elementToken, _ngZone, _document, _defaultOptions) {\n    this._ngZone = _ngZone;\n    this._defaultOptions = _defaultOptions; // We inject the live element and document as `any` because the constructor signature cannot\n    // reference browser globals (HTMLElement, Document) on non-browser environments, since having\n    // a class decorator causes TypeScript to preserve the constructor signature types.\n\n    this._document = _document;\n    this._liveElement = elementToken || this._createLiveElement();\n  }\n\n  announce(message, ...args) {\n    const defaultOptions = this._defaultOptions;\n    let politeness;\n    let duration;\n\n    if (args.length === 1 && typeof args[0] === 'number') {\n      duration = args[0];\n    } else {\n      [politeness, duration] = args;\n    }\n\n    this.clear();\n    clearTimeout(this._previousTimeout);\n\n    if (!politeness) {\n      politeness = defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n    }\n\n    if (duration == null && defaultOptions) {\n      duration = defaultOptions.duration;\n    } // TODO: ensure changing the politeness works on all environments we support.\n\n\n    this._liveElement.setAttribute('aria-live', politeness); // This 100ms timeout is necessary for some browser + screen-reader combinations:\n    // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n    // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n    //   second time without clearing and then using a non-zero delay.\n    // (using JAWS 17 at time of this writing).\n\n\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._currentPromise) {\n        this._currentPromise = new Promise(resolve => this._currentResolve = resolve);\n      }\n\n      clearTimeout(this._previousTimeout);\n      this._previousTimeout = setTimeout(() => {\n        this._liveElement.textContent = message;\n\n        if (typeof duration === 'number') {\n          this._previousTimeout = setTimeout(() => this.clear(), duration);\n        }\n\n        this._currentResolve();\n\n        this._currentPromise = this._currentResolve = undefined;\n      }, 100);\n      return this._currentPromise;\n    });\n  }\n  /**\n   * Clears the current text from the announcer element. Can be used to prevent\n   * screen readers from reading the text out again while the user is going\n   * through the page landmarks.\n   */\n\n\n  clear() {\n    if (this._liveElement) {\n      this._liveElement.textContent = '';\n    }\n  }\n\n  ngOnDestroy() {\n    clearTimeout(this._previousTimeout);\n    this._liveElement?.remove();\n    this._liveElement = null;\n    this._currentResolve?.();\n    this._currentPromise = this._currentResolve = undefined;\n  }\n\n  _createLiveElement() {\n    const elementClass = 'cdk-live-announcer-element';\n\n    const previousElements = this._document.getElementsByClassName(elementClass);\n\n    const liveEl = this._document.createElement('div'); // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n\n\n    for (let i = 0; i < previousElements.length; i++) {\n      previousElements[i].remove();\n    }\n\n    liveEl.classList.add(elementClass);\n    liveEl.classList.add('cdk-visually-hidden');\n    liveEl.setAttribute('aria-atomic', 'true');\n    liveEl.setAttribute('aria-live', 'polite');\n\n    this._document.body.appendChild(liveEl);\n\n    return liveEl;\n  }\n\n}\n\nLiveAnnouncer.ɵfac = function LiveAnnouncer_Factory(t) {\n  return new (t || LiveAnnouncer)(i0.ɵɵinject(LIVE_ANNOUNCER_ELEMENT_TOKEN, 8), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(LIVE_ANNOUNCER_DEFAULT_OPTIONS, 8));\n};\n\nLiveAnnouncer.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: LiveAnnouncer,\n  factory: LiveAnnouncer.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LiveAnnouncer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [LIVE_ANNOUNCER_ELEMENT_TOKEN]\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [LIVE_ANNOUNCER_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, null);\n})();\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\n\n\nclass CdkAriaLive {\n  constructor(_elementRef, _liveAnnouncer, _contentObserver, _ngZone) {\n    this._elementRef = _elementRef;\n    this._liveAnnouncer = _liveAnnouncer;\n    this._contentObserver = _contentObserver;\n    this._ngZone = _ngZone;\n    this._politeness = 'polite';\n  }\n  /** The aria-live politeness level to use when announcing messages. */\n\n\n  get politeness() {\n    return this._politeness;\n  }\n\n  set politeness(value) {\n    this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n\n    if (this._politeness === 'off') {\n      if (this._subscription) {\n        this._subscription.unsubscribe();\n\n        this._subscription = null;\n      }\n    } else if (!this._subscription) {\n      this._subscription = this._ngZone.runOutsideAngular(() => {\n        return this._contentObserver.observe(this._elementRef).subscribe(() => {\n          // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n          const elementText = this._elementRef.nativeElement.textContent; // The `MutationObserver` fires also for attribute\n          // changes which we don't want to announce.\n\n          if (elementText !== this._previousAnnouncedText) {\n            this._liveAnnouncer.announce(elementText, this._politeness, this.duration);\n\n            this._previousAnnouncedText = elementText;\n          }\n        });\n      });\n    }\n  }\n\n  ngOnDestroy() {\n    if (this._subscription) {\n      this._subscription.unsubscribe();\n    }\n  }\n\n}\n\nCdkAriaLive.ɵfac = function CdkAriaLive_Factory(t) {\n  return new (t || CdkAriaLive)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(LiveAnnouncer), i0.ɵɵdirectiveInject(i1$1.ContentObserver), i0.ɵɵdirectiveInject(i0.NgZone));\n};\n\nCdkAriaLive.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkAriaLive,\n  selectors: [[\"\", \"cdkAriaLive\", \"\"]],\n  inputs: {\n    politeness: [\"cdkAriaLive\", \"politeness\"],\n    duration: [\"cdkAriaLiveDuration\", \"duration\"]\n  },\n  exportAs: [\"cdkAriaLive\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAriaLive, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkAriaLive]',\n      exportAs: 'cdkAriaLive'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: LiveAnnouncer\n    }, {\n      type: i1$1.ContentObserver\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    politeness: [{\n      type: Input,\n      args: ['cdkAriaLive']\n    }],\n    duration: [{\n      type: Input,\n      args: ['cdkAriaLiveDuration']\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** InjectionToken for FocusMonitorOptions. */\n\n\nconst FOCUS_MONITOR_DEFAULT_OPTIONS = new InjectionToken('cdk-focus-monitor-default-options');\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\n\nconst captureEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\n\nclass FocusMonitor {\n  constructor(_ngZone, _platform, _inputModalityDetector,\n  /** @breaking-change 11.0.0 make document required */\n  document, options) {\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    this._inputModalityDetector = _inputModalityDetector;\n    /** The focus origin that the next focus event is a result of. */\n\n    this._origin = null;\n    /** Whether the window has just been focused. */\n\n    this._windowFocused = false;\n    /**\n     * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n     * focus events to touch interactions requires special logic.\n     */\n\n    this._originFromTouchInteraction = false;\n    /** Map of elements being monitored to their info. */\n\n    this._elementInfo = new Map();\n    /** The number of elements currently being monitored. */\n\n    this._monitoredElementCount = 0;\n    /**\n     * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n     * as well as the number of monitored elements that they contain. We have to treat focus/blur\n     * handlers differently from the rest of the events, because the browser won't emit events\n     * to the document when focus moves inside of a shadow root.\n     */\n\n    this._rootNodeFocusListenerCount = new Map();\n    /**\n     * Event listener for `focus` events on the window.\n     * Needs to be an arrow function in order to preserve the context when it gets bound.\n     */\n\n    this._windowFocusListener = () => {\n      // Make a note of when the window regains focus, so we can\n      // restore the origin info for the focused element.\n      this._windowFocused = true;\n      this._windowFocusTimeoutId = window.setTimeout(() => this._windowFocused = false);\n    };\n    /** Subject for stopping our InputModalityDetector subscription. */\n\n\n    this._stopInputModalityDetector = new Subject();\n    /**\n     * Event listener for `focus` and 'blur' events on the document.\n     * Needs to be an arrow function in order to preserve the context when it gets bound.\n     */\n\n    this._rootNodeFocusAndBlurListener = event => {\n      const target = _getEventTarget(event); // We need to walk up the ancestor chain in order to support `checkChildren`.\n\n\n      for (let element = target; element; element = element.parentElement) {\n        if (event.type === 'focus') {\n          this._onFocus(event, element);\n        } else {\n          this._onBlur(event, element);\n        }\n      }\n    };\n\n    this._document = document;\n    this._detectionMode = options?.detectionMode || 0\n    /* FocusMonitorDetectionMode.IMMEDIATE */\n    ;\n  }\n\n  monitor(element, checkChildren = false) {\n    const nativeElement = coerceElement(element); // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n\n    if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n      return of(null);\n    } // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n    // the shadow root, rather than the `document`, because the browser won't emit focus events\n    // to the `document`, if focus is moving within the same shadow root.\n\n\n    const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n\n    const cachedInfo = this._elementInfo.get(nativeElement); // Check if we're already monitoring this element.\n\n\n    if (cachedInfo) {\n      if (checkChildren) {\n        // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n        // observers into ones that behave as if `checkChildren` was turned on. We need a more\n        // robust solution.\n        cachedInfo.checkChildren = true;\n      }\n\n      return cachedInfo.subject;\n    } // Create monitored element info.\n\n\n    const info = {\n      checkChildren: checkChildren,\n      subject: new Subject(),\n      rootNode\n    };\n\n    this._elementInfo.set(nativeElement, info);\n\n    this._registerGlobalListeners(info);\n\n    return info.subject;\n  }\n\n  stopMonitoring(element) {\n    const nativeElement = coerceElement(element);\n\n    const elementInfo = this._elementInfo.get(nativeElement);\n\n    if (elementInfo) {\n      elementInfo.subject.complete();\n\n      this._setClasses(nativeElement);\n\n      this._elementInfo.delete(nativeElement);\n\n      this._removeGlobalListeners(elementInfo);\n    }\n  }\n\n  focusVia(element, origin, options) {\n    const nativeElement = coerceElement(element);\n\n    const focusedElement = this._getDocument().activeElement; // If the element is focused already, calling `focus` again won't trigger the event listener\n    // which means that the focus classes won't be updated. If that's the case, update the classes\n    // directly without waiting for an event.\n\n\n    if (nativeElement === focusedElement) {\n      this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) => this._originChanged(currentElement, origin, info));\n    } else {\n      this._setOrigin(origin); // `focus` isn't available on the server\n\n\n      if (typeof nativeElement.focus === 'function') {\n        nativeElement.focus(options);\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n  }\n  /** Access injected document if available or fallback to global document reference */\n\n\n  _getDocument() {\n    return this._document || document;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n\n\n  _getWindow() {\n    const doc = this._getDocument();\n\n    return doc.defaultView || window;\n  }\n\n  _getFocusOrigin(focusEventTarget) {\n    if (this._origin) {\n      // If the origin was realized via a touch interaction, we need to perform additional checks\n      // to determine whether the focus origin should be attributed to touch or program.\n      if (this._originFromTouchInteraction) {\n        return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n      } else {\n        return this._origin;\n      }\n    } // If the window has just regained focus, we can restore the most recent origin from before the\n    // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n    // focus. This typically means one of two things happened:\n    //\n    // 1) The element was programmatically focused, or\n    // 2) The element was focused via screen reader navigation (which generally doesn't fire\n    //    events).\n    //\n    // Because we can't distinguish between these two cases, we default to setting `program`.\n\n\n    if (this._windowFocused && this._lastFocusOrigin) {\n      return this._lastFocusOrigin;\n    } // If the interaction is coming from an input label, we consider it a mouse interactions.\n    // This is a special case where focus moves on `click`, rather than `mousedown` which breaks\n    // our detection, because all our assumptions are for `mousedown`. We need to handle this\n    // special case, because it's very common for checkboxes and radio buttons.\n\n\n    if (focusEventTarget && this._isLastInteractionFromInputLabel(focusEventTarget)) {\n      return 'mouse';\n    }\n\n    return 'program';\n  }\n  /**\n   * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n   * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n   * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n   * event was directly caused by the touch interaction or (2) the focus event was caused by a\n   * subsequent programmatic focus call triggered by the touch interaction.\n   * @param focusEventTarget The target of the focus event under examination.\n   */\n\n\n  _shouldBeAttributedToTouch(focusEventTarget) {\n    // Please note that this check is not perfect. Consider the following edge case:\n    //\n    // <div #parent tabindex=\"0\">\n    //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n    // </div>\n    //\n    // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n    // #child, #parent is programmatically focused. This code will attribute the focus to touch\n    // instead of program. This is a relatively minor edge-case that can be worked around by using\n    // focusVia(parent, 'program') to focus #parent.\n    return this._detectionMode === 1\n    /* FocusMonitorDetectionMode.EVENTUAL */\n    || !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget);\n  }\n  /**\n   * Sets the focus classes on the element based on the given focus origin.\n   * @param element The element to update the classes on.\n   * @param origin The focus origin.\n   */\n\n\n  _setClasses(element, origin) {\n    element.classList.toggle('cdk-focused', !!origin);\n    element.classList.toggle('cdk-touch-focused', origin === 'touch');\n    element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n    element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n    element.classList.toggle('cdk-program-focused', origin === 'program');\n  }\n  /**\n   * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n   * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n   * the origin being set.\n   * @param origin The origin to set.\n   * @param isFromInteraction Whether we are setting the origin from an interaction event.\n   */\n\n\n  _setOrigin(origin, isFromInteraction = false) {\n    this._ngZone.runOutsideAngular(() => {\n      this._origin = origin;\n      this._originFromTouchInteraction = origin === 'touch' && isFromInteraction; // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n      // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n      // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n      // a touch event because when a touch event is fired, the associated focus event isn't yet in\n      // the event queue. Before doing so, clear any pending timeouts.\n\n      if (this._detectionMode === 0\n      /* FocusMonitorDetectionMode.IMMEDIATE */\n      ) {\n        clearTimeout(this._originTimeoutId);\n        const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n        this._originTimeoutId = setTimeout(() => this._origin = null, ms);\n      }\n    });\n  }\n  /**\n   * Handles focus events on a registered element.\n   * @param event The focus event.\n   * @param element The monitored element.\n   */\n\n\n  _onFocus(event, element) {\n    // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n    // focus event affecting the monitored element. If we want to use the origin of the first event\n    // instead we should check for the cdk-focused class here and return if the element already has\n    // it. (This only matters for elements that have includesChildren = true).\n    // If we are not counting child-element-focus as focused, make sure that the event target is the\n    // monitored element itself.\n    const elementInfo = this._elementInfo.get(element);\n\n    const focusEventTarget = _getEventTarget(event);\n\n    if (!elementInfo || !elementInfo.checkChildren && element !== focusEventTarget) {\n      return;\n    }\n\n    this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n  }\n  /**\n   * Handles blur events on a registered element.\n   * @param event The blur event.\n   * @param element The monitored element.\n   */\n\n\n  _onBlur(event, element) {\n    // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n    // order to focus another child of the monitored element.\n    const elementInfo = this._elementInfo.get(element);\n\n    if (!elementInfo || elementInfo.checkChildren && event.relatedTarget instanceof Node && element.contains(event.relatedTarget)) {\n      return;\n    }\n\n    this._setClasses(element);\n\n    this._emitOrigin(elementInfo, null);\n  }\n\n  _emitOrigin(info, origin) {\n    if (info.subject.observers.length) {\n      this._ngZone.run(() => info.subject.next(origin));\n    }\n  }\n\n  _registerGlobalListeners(elementInfo) {\n    if (!this._platform.isBrowser) {\n      return;\n    }\n\n    const rootNode = elementInfo.rootNode;\n    const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n\n    if (!rootNodeFocusListeners) {\n      this._ngZone.runOutsideAngular(() => {\n        rootNode.addEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.addEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n      });\n    }\n\n    this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1); // Register global listeners when first element is monitored.\n\n\n    if (++this._monitoredElementCount === 1) {\n      // Note: we listen to events in the capture phase so we\n      // can detect them even if the user stops propagation.\n      this._ngZone.runOutsideAngular(() => {\n        const window = this._getWindow();\n\n        window.addEventListener('focus', this._windowFocusListener);\n      }); // The InputModalityDetector is also just a collection of global listeners.\n\n\n      this._inputModalityDetector.modalityDetected.pipe(takeUntil(this._stopInputModalityDetector)).subscribe(modality => {\n        this._setOrigin(modality, true\n        /* isFromInteraction */\n        );\n      });\n    }\n  }\n\n  _removeGlobalListeners(elementInfo) {\n    const rootNode = elementInfo.rootNode;\n\n    if (this._rootNodeFocusListenerCount.has(rootNode)) {\n      const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode);\n\n      if (rootNodeFocusListeners > 1) {\n        this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n      } else {\n        rootNode.removeEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.removeEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n\n        this._rootNodeFocusListenerCount.delete(rootNode);\n      }\n    } // Unregister global listeners when last element is unmonitored.\n\n\n    if (! --this._monitoredElementCount) {\n      const window = this._getWindow();\n\n      window.removeEventListener('focus', this._windowFocusListener); // Equivalently, stop our InputModalityDetector subscription.\n\n      this._stopInputModalityDetector.next(); // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n\n\n      clearTimeout(this._windowFocusTimeoutId);\n      clearTimeout(this._originTimeoutId);\n    }\n  }\n  /** Updates all the state on an element once its focus origin has changed. */\n\n\n  _originChanged(element, origin, elementInfo) {\n    this._setClasses(element, origin);\n\n    this._emitOrigin(elementInfo, origin);\n\n    this._lastFocusOrigin = origin;\n  }\n  /**\n   * Collects the `MonitoredElementInfo` of a particular element and\n   * all of its ancestors that have enabled `checkChildren`.\n   * @param element Element from which to start the search.\n   */\n\n\n  _getClosestElementsInfo(element) {\n    const results = [];\n\n    this._elementInfo.forEach((info, currentElement) => {\n      if (currentElement === element || info.checkChildren && currentElement.contains(element)) {\n        results.push([currentElement, info]);\n      }\n    });\n\n    return results;\n  }\n  /**\n   * Returns whether an interaction is likely to have come from the user clicking the `label` of\n   * an `input` or `textarea` in order to focus it.\n   * @param focusEventTarget Target currently receiving focus.\n   */\n\n\n  _isLastInteractionFromInputLabel(focusEventTarget) {\n    const {\n      _mostRecentTarget: mostRecentTarget,\n      mostRecentModality\n    } = this._inputModalityDetector; // If the last interaction used the mouse on an element contained by one of the labels\n    // of an `input`/`textarea` that is currently focused, it is very likely that the\n    // user redirected focus using the label.\n\n    if (mostRecentModality !== 'mouse' || !mostRecentTarget || mostRecentTarget === focusEventTarget || focusEventTarget.nodeName !== 'INPUT' && focusEventTarget.nodeName !== 'TEXTAREA' || focusEventTarget.disabled) {\n      return false;\n    }\n\n    const labels = focusEventTarget.labels;\n\n    if (labels) {\n      for (let i = 0; i < labels.length; i++) {\n        if (labels[i].contains(mostRecentTarget)) {\n          return true;\n        }\n      }\n    }\n\n    return false;\n  }\n\n}\n\nFocusMonitor.ɵfac = function FocusMonitor_Factory(t) {\n  return new (t || FocusMonitor)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.Platform), i0.ɵɵinject(InputModalityDetector), i0.ɵɵinject(DOCUMENT, 8), i0.ɵɵinject(FOCUS_MONITOR_DEFAULT_OPTIONS, 8));\n};\n\nFocusMonitor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: FocusMonitor,\n  factory: FocusMonitor.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusMonitor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }, {\n      type: i1.Platform\n    }, {\n      type: InputModalityDetector\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [FOCUS_MONITOR_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\n\n\nclass CdkMonitorFocus {\n  constructor(_elementRef, _focusMonitor) {\n    this._elementRef = _elementRef;\n    this._focusMonitor = _focusMonitor;\n    this._focusOrigin = null;\n    this.cdkFocusChange = new EventEmitter();\n  }\n\n  get focusOrigin() {\n    return this._focusOrigin;\n  }\n\n  ngAfterViewInit() {\n    const element = this._elementRef.nativeElement;\n    this._monitorSubscription = this._focusMonitor.monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus')).subscribe(origin => {\n      this._focusOrigin = origin;\n      this.cdkFocusChange.emit(origin);\n    });\n  }\n\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n\n    if (this._monitorSubscription) {\n      this._monitorSubscription.unsubscribe();\n    }\n  }\n\n}\n\nCdkMonitorFocus.ɵfac = function CdkMonitorFocus_Factory(t) {\n  return new (t || CdkMonitorFocus)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(FocusMonitor));\n};\n\nCdkMonitorFocus.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkMonitorFocus,\n  selectors: [[\"\", \"cdkMonitorElementFocus\", \"\"], [\"\", \"cdkMonitorSubtreeFocus\", \"\"]],\n  outputs: {\n    cdkFocusChange: \"cdkFocusChange\"\n  },\n  exportAs: [\"cdkMonitorFocus\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMonitorFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n      exportAs: 'cdkMonitorFocus'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: FocusMonitor\n    }];\n  }, {\n    cdkFocusChange: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\n\n\nconst BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\n\nconst WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n/** CSS class applied to the document body when in high-contrast mode. */\n\nconst HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\n\nclass HighContrastModeDetector {\n  constructor(_platform, document) {\n    this._platform = _platform;\n    this._document = document;\n    this._breakpointSubscription = inject(BreakpointObserver).observe('(forced-colors: active)').subscribe(() => {\n      if (this._hasCheckedHighContrastMode) {\n        this._hasCheckedHighContrastMode = false;\n\n        this._applyBodyHighContrastModeCssClasses();\n      }\n    });\n  }\n  /** Gets the current high-contrast-mode for the page. */\n\n\n  getHighContrastMode() {\n    if (!this._platform.isBrowser) {\n      return 0\n      /* HighContrastMode.NONE */\n      ;\n    } // Create a test element with an arbitrary background-color that is neither black nor\n    // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n    // appending the test element to the DOM does not affect layout by absolutely positioning it\n\n\n    const testElement = this._document.createElement('div');\n\n    testElement.style.backgroundColor = 'rgb(1,2,3)';\n    testElement.style.position = 'absolute';\n\n    this._document.body.appendChild(testElement); // Get the computed style for the background color, collapsing spaces to normalize between\n    // browsers. Once we get this color, we no longer need the test element. Access the `window`\n    // via the document so we can fake it in tests. Note that we have extra null checks, because\n    // this logic will likely run during app bootstrap and throwing can break the entire app.\n\n\n    const documentWindow = this._document.defaultView || window;\n    const computedStyle = documentWindow && documentWindow.getComputedStyle ? documentWindow.getComputedStyle(testElement) : null;\n    const computedColor = (computedStyle && computedStyle.backgroundColor || '').replace(/ /g, '');\n    testElement.remove();\n\n    switch (computedColor) {\n      // Pre Windows 11 dark theme.\n      case 'rgb(0,0,0)': // Windows 11 dark themes.\n\n      case 'rgb(45,50,54)':\n      case 'rgb(32,32,32)':\n        return 2\n        /* HighContrastMode.WHITE_ON_BLACK */\n        ;\n      // Pre Windows 11 light theme.\n\n      case 'rgb(255,255,255)': // Windows 11 light theme.\n\n      case 'rgb(255,250,239)':\n        return 1\n        /* HighContrastMode.BLACK_ON_WHITE */\n        ;\n    }\n\n    return 0\n    /* HighContrastMode.NONE */\n    ;\n  }\n\n  ngOnDestroy() {\n    this._breakpointSubscription.unsubscribe();\n  }\n  /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n\n\n  _applyBodyHighContrastModeCssClasses() {\n    if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n      const bodyClasses = this._document.body.classList;\n      bodyClasses.remove(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      this._hasCheckedHighContrastMode = true;\n      const mode = this.getHighContrastMode();\n\n      if (mode === 1\n      /* HighContrastMode.BLACK_ON_WHITE */\n      ) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS);\n      } else if (mode === 2\n      /* HighContrastMode.WHITE_ON_BLACK */\n      ) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      }\n    }\n  }\n\n}\n\nHighContrastModeDetector.ɵfac = function HighContrastModeDetector_Factory(t) {\n  return new (t || HighContrastModeDetector)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(DOCUMENT));\n};\n\nHighContrastModeDetector.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HighContrastModeDetector,\n  factory: HighContrastModeDetector.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HighContrastModeDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass A11yModule {\n  constructor(highContrastModeDetector) {\n    highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n  }\n\n}\n\nA11yModule.ɵfac = function A11yModule_Factory(t) {\n  return new (t || A11yModule)(i0.ɵɵinject(HighContrastModeDetector));\n};\n\nA11yModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: A11yModule,\n  declarations: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n  imports: [ObserversModule],\n  exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus]\n});\nA11yModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [ObserversModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(A11yModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ObserversModule],\n      declarations: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n      exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus]\n    }]\n  }], function () {\n    return [{\n      type: HighContrastModeDetector\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { A11yModule, ActiveDescendantKeyManager, AriaDescriber, CDK_DESCRIBEDBY_HOST_ATTRIBUTE, CDK_DESCRIBEDBY_ID_PREFIX, CdkAriaLive, CdkMonitorFocus, CdkTrapFocus, ConfigurableFocusTrap, ConfigurableFocusTrapFactory, EventListenerFocusTrapInertStrategy, FOCUS_MONITOR_DEFAULT_OPTIONS, FOCUS_TRAP_INERT_STRATEGY, FocusKeyManager, FocusMonitor, FocusTrap, FocusTrapFactory, HighContrastModeDetector, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, INPUT_MODALITY_DETECTOR_OPTIONS, InputModalityDetector, InteractivityChecker, IsFocusableConfig, LIVE_ANNOUNCER_DEFAULT_OPTIONS, LIVE_ANNOUNCER_ELEMENT_TOKEN, LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY, ListKeyManager, LiveAnnouncer, MESSAGES_CONTAINER_ID, isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader };", "map": {"version": 3, "names": ["DOCUMENT", "i0", "inject", "APP_ID", "Injectable", "Inject", "QueryList", "Directive", "Input", "InjectionToken", "Optional", "EventEmitter", "Output", "NgModule", "i1", "_getFocusedElementPierceShadowDom", "normalizePassiveListenerOptions", "_getEventTarget", "_getShadowRoot", "Subject", "Subscription", "BehaviorSubject", "of", "hasModifierKey", "A", "Z", "ZERO", "NINE", "END", "HOME", "LEFT_ARROW", "RIGHT_ARROW", "UP_ARROW", "DOWN_ARROW", "TAB", "ALT", "CONTROL", "MAC_META", "META", "SHIFT", "tap", "debounceTime", "filter", "map", "take", "skip", "distinctUntilChanged", "takeUntil", "coerceBooleanProperty", "coerceElement", "i1$1", "ObserversModule", "BreakpointObserver", "ID_DELIMITER", "addAriaReferencedId", "el", "attr", "id", "ids", "getAriaReferenceIds", "some", "existingId", "trim", "push", "setAttribute", "join", "removeAriaReferencedId", "filteredIds", "val", "length", "removeAttribute", "getAttribute", "match", "MESSAGES_CONTAINER_ID", "CDK_DESCRIBEDBY_ID_PREFIX", "CDK_DESCRIBEDBY_HOST_ATTRIBUTE", "nextId", "AriaDescriber", "constructor", "_document", "_platform", "_messageRegistry", "Map", "_messagesContainer", "_id", "describe", "hostElement", "message", "role", "_canBeDescribed", "key", "<PERSON><PERSON><PERSON>", "setMessageId", "set", "messageElement", "referenceCount", "has", "_createMessageElement", "_isElementDescribedByMessage", "_addMessageReference", "removeDescription", "_isElementNode", "_removeMessageReference", "registeredMessage", "get", "_deleteMessageElement", "childNodes", "remove", "ngOnDestroy", "describedE<PERSON>s", "querySelectorAll", "i", "_removeCdkDescribedByReferenceIds", "clear", "createElement", "textContent", "_createMessagesContainer", "append<PERSON><PERSON><PERSON>", "delete", "containerClassName", "serverContainers", "messagesContainer", "style", "visibility", "classList", "add", "<PERSON><PERSON><PERSON><PERSON>", "body", "element", "originalReferenceIds", "indexOf", "referenceIds", "messageId", "trimmedMessage", "aria<PERSON><PERSON><PERSON>", "nodeType", "ELEMENT_NODE", "ɵfac", "Platform", "ɵprov", "type", "args", "providedIn", "undefined", "decorators", "serviceId", "ListKeyManager", "_items", "_activeItemIndex", "_activeItem", "_wrap", "_letterKeyStream", "_typeaheadSubscription", "EMPTY", "_vertical", "_allowedModifierKeys", "_homeAndEnd", "_skipPredicateFn", "item", "disabled", "_pressedLetters", "tabOut", "change", "changes", "subscribe", "newItems", "itemArray", "toArray", "newIndex", "skipPredicate", "predicate", "withWrap", "shouldWrap", "withVerticalOrientation", "enabled", "withHorizontalOrientation", "direction", "_horizontal", "withAllowedModifierKeys", "keys", "withTypeAhead", "debounceInterval", "ngDevMode", "get<PERSON><PERSON><PERSON>", "Error", "unsubscribe", "pipe", "letter", "inputString", "items", "_getItemsArray", "index", "toUpperCase", "setActiveItem", "withHomeAndEnd", "previousActiveItem", "updateActiveItem", "next", "onKeydown", "event", "keyCode", "modifiers", "isModifierAllowed", "every", "modifier", "setNextItemActive", "setPreviousItemActive", "setFirstItemActive", "setLastItemActive", "toLocaleUpperCase", "String", "fromCharCode", "preventDefault", "activeItemIndex", "activeItem", "isTyping", "_setActiveItemByIndex", "_setActiveItemByDelta", "delta", "_setActiveInWrapMode", "_setActiveInDefaultMode", "fallback<PERSON><PERSON><PERSON>", "ActiveDescendantKeyManager", "setInactiveStyles", "setActiveStyles", "FocusKeyManager", "arguments", "_origin", "setFocusOrigin", "origin", "focus", "IsFocusableConfig", "ignoreVisibility", "InteractivityChecker", "isDisabled", "hasAttribute", "isVisible", "hasGeometry", "getComputedStyle", "isTabbable", "frameElement", "getFrameElement", "getWindow", "getTabIndexValue", "nodeName", "toLowerCase", "tabIndexValue", "WEBKIT", "IOS", "isPotentiallyTabbableIOS", "FIREFOX", "tabIndex", "isFocusable", "config", "isPotentiallyFocusable", "window", "offsetWidth", "offsetHeight", "getClientRects", "isNativeFormElement", "isHiddenInput", "isInputElement", "isAnchorWithHref", "isAnchorElement", "hasValidTabIndex", "isNaN", "parseInt", "inputType", "node", "ownerDocument", "defaultView", "FocusTrap", "_element", "_checker", "_ngZone", "deferAnchors", "_hasAttached", "startAnchorListener", "focusLastTabbableElement", "endAnchorListener", "focusFirstTabbableElement", "_enabled", "attachAnchors", "value", "_startAnchor", "_endAnchor", "_toggleAnchorTabIndex", "destroy", "startAnchor", "endAnchor", "removeEventListener", "runOutsideAngular", "_createAnchor", "addEventListener", "parentNode", "insertBefore", "nextS<PERSON>ling", "focusInitialElementWhenReady", "options", "Promise", "resolve", "_executeOnStable", "focusInitialElement", "focusFirstTabbableElementWhenReady", "focusLastTabbableElementWhenReady", "_getRegionBoundary", "bound", "markers", "console", "warn", "_getFirstTabbableElement", "_getLastTabbableElement", "redirectToElement", "querySelector", "focus<PERSON><PERSON><PERSON><PERSON>", "has<PERSON>tta<PERSON>", "root", "children", "tabbable<PERSON><PERSON><PERSON>", "anchor", "isEnabled", "toggleAnchors", "fn", "isStable", "onStable", "FocusTrapFactory", "create", "deferCaptureElements", "NgZone", "CdkTrapFocus", "_elementRef", "_focusTrapFactory", "_previouslyFocusedElement", "focusTrap", "nativeElement", "autoCapture", "_autoCapture", "ngAfterContentInit", "_captureFocus", "ngDoCheck", "ngOnChanges", "autoCaptureChange", "firstChange", "ElementRef", "ɵdir", "selector", "exportAs", "ConfigurableFocusTrap", "_focusTrapManager", "_inertStrategy", "defer", "register", "deregister", "_enable", "preventFocus", "_disable", "allowFocus", "FOCUS_TRAP_INERT_STRATEGY", "EventListenerFocusTrapInertStrategy", "_listener", "e", "_trapFocus", "target", "focusTrapRoot", "contains", "closest", "setTimeout", "activeElement", "FocusTrapManager", "_focusTrapStack", "ft", "stack", "splice", "ConfigurableFocusTrapFactory", "configObject", "isFakeMousedownFromScreenReader", "buttons", "offsetX", "offsetY", "isFakeTouchstartFromScreenReader", "touch", "touches", "changedTouches", "identifier", "radiusX", "radiusY", "INPUT_MODALITY_DETECTOR_OPTIONS", "INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS", "<PERSON><PERSON><PERSON><PERSON>", "TOUCH_BUFFER_MS", "modalityEventListenerOptions", "passive", "capture", "InputModalityDetector", "ngZone", "document", "_mostRecentTarget", "_modality", "_lastTouchMs", "_onKeydown", "_options", "_onMousedown", "Date", "now", "_onTouchstart", "modalityDetected", "modalityChanged", "mostRecentModality", "complete", "Document", "LIVE_ANNOUNCER_ELEMENT_TOKEN", "factory", "LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY", "LIVE_ANNOUNCER_DEFAULT_OPTIONS", "LiveAnnouncer", "elementToken", "_defaultOptions", "_liveElement", "_createLiveElement", "announce", "defaultOptions", "politeness", "duration", "clearTimeout", "_previousTimeout", "_currentPromise", "_currentResolve", "elementClass", "previousElements", "getElementsByClassName", "liveEl", "CdkAriaLive", "_liveAnnouncer", "_contentObserver", "_politeness", "_subscription", "observe", "elementText", "_previousAnnouncedText", "ContentObserver", "FOCUS_MONITOR_DEFAULT_OPTIONS", "captureEventListenerOptions", "FocusMonitor", "_inputModalityDetector", "_windowFocused", "_originFromTouchInteraction", "_elementInfo", "_monitoredElementCount", "_rootNodeFocusListenerCount", "_windowFocusListener", "_windowFocusTimeoutId", "_stopInputModalityDetector", "_rootNodeFocusAndBlurListener", "parentElement", "_onFocus", "_onBlur", "_detectionMode", "detectionMode", "monitor", "check<PERSON><PERSON><PERSON><PERSON>", "rootNode", "_getDocument", "cachedInfo", "subject", "info", "_registerGlobalListeners", "stopMonitoring", "elementInfo", "_setClasses", "_removeGlobalListeners", "focusVia", "focusedElement", "_getClosestElementsInfo", "for<PERSON>ach", "currentElement", "_originChanged", "_set<PERSON><PERSON><PERSON>", "_info", "_getWindow", "doc", "_getFocus<PERSON><PERSON>in", "focusEventTarget", "_shouldBeAttributedToTouch", "_last<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_isLastInteractionFromInputLabel", "toggle", "isFromInteraction", "_originTimeoutId", "ms", "relatedTarget", "Node", "_emit<PERSON><PERSON>in", "observers", "run", "rootNodeFocusListeners", "modality", "results", "mostRecentTarget", "labels", "CdkMonitorFocus", "_focusMonitor", "_focus<PERSON><PERSON>in", "cdkFocusChange", "<PERSON><PERSON><PERSON><PERSON>", "ngAfterViewInit", "_monitorSubscription", "emit", "BLACK_ON_WHITE_CSS_CLASS", "WHITE_ON_BLACK_CSS_CLASS", "HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS", "HighContrastModeDetector", "_breakpointSubscription", "_hasCheckedHighContrastMode", "_applyBodyHighContrastModeCssClasses", "getHighContrastMode", "testElement", "backgroundColor", "position", "documentWindow", "computedStyle", "computedColor", "replace", "bodyClasses", "mode", "A11yModule", "highContrastModeDetector", "ɵmod", "ɵinj", "imports", "declarations", "exports"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/cdk/fesm2020/a11y.mjs"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable, Inject, QueryList, Directive, Input, InjectionToken, Optional, EventEmitter, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/platform';\nimport { _getFocusedElementPierceShadowDom, normalizePassiveListenerOptions, _getEventTarget, _getShadowRoot } from '@angular/cdk/platform';\nimport { Subject, Subscription, BehaviorSubject, of } from 'rxjs';\nimport { hasModifierKey, A, Z, ZERO, NINE, END, HOME, LEFT_ARROW, RIGHT_ARROW, UP_ARROW, DOWN_ARROW, TAB, ALT, CONTROL, MAC_META, META, SHIFT } from '@angular/cdk/keycodes';\nimport { tap, debounceTime, filter, map, take, skip, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { coerceBooleanProperty, coerceElement } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport { BreakpointObserver } from '@angular/cdk/layout';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** IDs are delimited by an empty space, as per the spec. */\nconst ID_DELIMITER = ' ';\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction addAriaReferencedId(el, attr, id) {\n    const ids = getAriaReferenceIds(el, attr);\n    if (ids.some(existingId => existingId.trim() == id.trim())) {\n        return;\n    }\n    ids.push(id.trim());\n    el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction removeAriaReferencedId(el, attr, id) {\n    const ids = getAriaReferenceIds(el, attr);\n    const filteredIds = ids.filter(val => val != id.trim());\n    if (filteredIds.length) {\n        el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n    }\n    else {\n        el.removeAttribute(attr);\n    }\n}\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction getAriaReferenceIds(el, attr) {\n    // Get string array of all individual ids (whitespace delimited) in the attribute value\n    return (el.getAttribute(attr) || '').match(/\\S+/g) || [];\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\nconst MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\nclass AriaDescriber {\n    constructor(_document, \n    /**\n     * @deprecated To be turned into a required parameter.\n     * @breaking-change 14.0.0\n     */\n    _platform) {\n        this._platform = _platform;\n        /** Map of all registered message elements that have been placed into the document. */\n        this._messageRegistry = new Map();\n        /** Container for all registered messages. */\n        this._messagesContainer = null;\n        /** Unique ID for the service. */\n        this._id = `${nextId++}`;\n        this._document = _document;\n        this._id = inject(APP_ID) + '-' + nextId++;\n    }\n    describe(hostElement, message, role) {\n        if (!this._canBeDescribed(hostElement, message)) {\n            return;\n        }\n        const key = getKey(message, role);\n        if (typeof message !== 'string') {\n            // We need to ensure that the element has an ID.\n            setMessageId(message, this._id);\n            this._messageRegistry.set(key, { messageElement: message, referenceCount: 0 });\n        }\n        else if (!this._messageRegistry.has(key)) {\n            this._createMessageElement(message, role);\n        }\n        if (!this._isElementDescribedByMessage(hostElement, key)) {\n            this._addMessageReference(hostElement, key);\n        }\n    }\n    removeDescription(hostElement, message, role) {\n        if (!message || !this._isElementNode(hostElement)) {\n            return;\n        }\n        const key = getKey(message, role);\n        if (this._isElementDescribedByMessage(hostElement, key)) {\n            this._removeMessageReference(hostElement, key);\n        }\n        // If the message is a string, it means that it's one that we created for the\n        // consumer so we can remove it safely, otherwise we should leave it in place.\n        if (typeof message === 'string') {\n            const registeredMessage = this._messageRegistry.get(key);\n            if (registeredMessage && registeredMessage.referenceCount === 0) {\n                this._deleteMessageElement(key);\n            }\n        }\n        if (this._messagesContainer?.childNodes.length === 0) {\n            this._messagesContainer.remove();\n            this._messagesContainer = null;\n        }\n    }\n    /** Unregisters all created message elements and removes the message container. */\n    ngOnDestroy() {\n        const describedElements = this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`);\n        for (let i = 0; i < describedElements.length; i++) {\n            this._removeCdkDescribedByReferenceIds(describedElements[i]);\n            describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n        }\n        this._messagesContainer?.remove();\n        this._messagesContainer = null;\n        this._messageRegistry.clear();\n    }\n    /**\n     * Creates a new element in the visually hidden message container element with the message\n     * as its content and adds it to the message registry.\n     */\n    _createMessageElement(message, role) {\n        const messageElement = this._document.createElement('div');\n        setMessageId(messageElement, this._id);\n        messageElement.textContent = message;\n        if (role) {\n            messageElement.setAttribute('role', role);\n        }\n        this._createMessagesContainer();\n        this._messagesContainer.appendChild(messageElement);\n        this._messageRegistry.set(getKey(message, role), { messageElement, referenceCount: 0 });\n    }\n    /** Deletes the message element from the global messages container. */\n    _deleteMessageElement(key) {\n        this._messageRegistry.get(key)?.messageElement?.remove();\n        this._messageRegistry.delete(key);\n    }\n    /** Creates the global container for all aria-describedby messages. */\n    _createMessagesContainer() {\n        if (this._messagesContainer) {\n            return;\n        }\n        const containerClassName = 'cdk-describedby-message-container';\n        const serverContainers = this._document.querySelectorAll(`.${containerClassName}[platform=\"server\"]`);\n        for (let i = 0; i < serverContainers.length; i++) {\n            // When going from the server to the client, we may end up in a situation where there's\n            // already a container on the page, but we don't have a reference to it. Clear the\n            // old container so we don't get duplicates. Doing this, instead of emptying the previous\n            // container, should be slightly faster.\n            serverContainers[i].remove();\n        }\n        const messagesContainer = this._document.createElement('div');\n        // We add `visibility: hidden` in order to prevent text in this container from\n        // being searchable by the browser's Ctrl + F functionality.\n        // Screen-readers will still read the description for elements with aria-describedby even\n        // when the description element is not visible.\n        messagesContainer.style.visibility = 'hidden';\n        // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n        // the description element doesn't impact page layout.\n        messagesContainer.classList.add(containerClassName);\n        messagesContainer.classList.add('cdk-visually-hidden');\n        // @breaking-change 14.0.0 Remove null check for `_platform`.\n        if (this._platform && !this._platform.isBrowser) {\n            messagesContainer.setAttribute('platform', 'server');\n        }\n        this._document.body.appendChild(messagesContainer);\n        this._messagesContainer = messagesContainer;\n    }\n    /** Removes all cdk-describedby messages that are hosted through the element. */\n    _removeCdkDescribedByReferenceIds(element) {\n        // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n        const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n        element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n    }\n    /**\n     * Adds a message reference to the element using aria-describedby and increments the registered\n     * message's reference count.\n     */\n    _addMessageReference(element, key) {\n        const registeredMessage = this._messageRegistry.get(key);\n        // Add the aria-describedby reference and set the\n        // describedby_host attribute to mark the element.\n        addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n        element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n        registeredMessage.referenceCount++;\n    }\n    /**\n     * Removes a message reference from the element using aria-describedby\n     * and decrements the registered message's reference count.\n     */\n    _removeMessageReference(element, key) {\n        const registeredMessage = this._messageRegistry.get(key);\n        registeredMessage.referenceCount--;\n        removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n        element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n    /** Returns true if the element has been described by the provided message ID. */\n    _isElementDescribedByMessage(element, key) {\n        const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n        const registeredMessage = this._messageRegistry.get(key);\n        const messageId = registeredMessage && registeredMessage.messageElement.id;\n        return !!messageId && referenceIds.indexOf(messageId) != -1;\n    }\n    /** Determines whether a message can be described on a particular element. */\n    _canBeDescribed(element, message) {\n        if (!this._isElementNode(element)) {\n            return false;\n        }\n        if (message && typeof message === 'object') {\n            // We'd have to make some assumptions about the description element's text, if the consumer\n            // passed in an element. Assume that if an element is passed in, the consumer has verified\n            // that it can be used as a description.\n            return true;\n        }\n        const trimmedMessage = message == null ? '' : `${message}`.trim();\n        const ariaLabel = element.getAttribute('aria-label');\n        // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n        // element, because screen readers will end up reading out the same text twice in a row.\n        return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n    }\n    /** Checks whether a node is an Element node. */\n    _isElementNode(element) {\n        return element.nodeType === this._document.ELEMENT_NODE;\n    }\n}\nAriaDescriber.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: AriaDescriber, deps: [{ token: DOCUMENT }, { token: i1.Platform }], target: i0.ɵɵFactoryTarget.Injectable });\nAriaDescriber.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: AriaDescriber, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: AriaDescriber, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1.Platform }]; } });\n/** Gets a key that can be used to look messages up in the registry. */\nfunction getKey(message, role) {\n    return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n/** Assigns a unique ID to an element, if it doesn't have one already. */\nfunction setMessageId(element, serviceId) {\n    if (!element.id) {\n        element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${serviceId}-${nextId++}`;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nclass ListKeyManager {\n    constructor(_items) {\n        this._items = _items;\n        this._activeItemIndex = -1;\n        this._activeItem = null;\n        this._wrap = false;\n        this._letterKeyStream = new Subject();\n        this._typeaheadSubscription = Subscription.EMPTY;\n        this._vertical = true;\n        this._allowedModifierKeys = [];\n        this._homeAndEnd = false;\n        /**\n         * Predicate function that can be used to check whether an item should be skipped\n         * by the key manager. By default, disabled items are skipped.\n         */\n        this._skipPredicateFn = (item) => item.disabled;\n        // Buffer for the letters that the user has pressed when the typeahead option is turned on.\n        this._pressedLetters = [];\n        /**\n         * Stream that emits any time the TAB key is pressed, so components can react\n         * when focus is shifted off of the list.\n         */\n        this.tabOut = new Subject();\n        /** Stream that emits whenever the active item of the list manager changes. */\n        this.change = new Subject();\n        // We allow for the items to be an array because, in some cases, the consumer may\n        // not have access to a QueryList of the items they want to manage (e.g. when the\n        // items aren't being collected via `ViewChildren` or `ContentChildren`).\n        if (_items instanceof QueryList) {\n            _items.changes.subscribe((newItems) => {\n                if (this._activeItem) {\n                    const itemArray = newItems.toArray();\n                    const newIndex = itemArray.indexOf(this._activeItem);\n                    if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n                        this._activeItemIndex = newIndex;\n                    }\n                }\n            });\n        }\n    }\n    /**\n     * Sets the predicate function that determines which items should be skipped by the\n     * list key manager.\n     * @param predicate Function that determines whether the given item should be skipped.\n     */\n    skipPredicate(predicate) {\n        this._skipPredicateFn = predicate;\n        return this;\n    }\n    /**\n     * Configures wrapping mode, which determines whether the active item will wrap to\n     * the other end of list when there are no more items in the given direction.\n     * @param shouldWrap Whether the list should wrap when reaching the end.\n     */\n    withWrap(shouldWrap = true) {\n        this._wrap = shouldWrap;\n        return this;\n    }\n    /**\n     * Configures whether the key manager should be able to move the selection vertically.\n     * @param enabled Whether vertical selection should be enabled.\n     */\n    withVerticalOrientation(enabled = true) {\n        this._vertical = enabled;\n        return this;\n    }\n    /**\n     * Configures the key manager to move the selection horizontally.\n     * Passing in `null` will disable horizontal movement.\n     * @param direction Direction in which the selection can be moved.\n     */\n    withHorizontalOrientation(direction) {\n        this._horizontal = direction;\n        return this;\n    }\n    /**\n     * Modifier keys which are allowed to be held down and whose default actions will be prevented\n     * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n     */\n    withAllowedModifierKeys(keys) {\n        this._allowedModifierKeys = keys;\n        return this;\n    }\n    /**\n     * Turns on typeahead mode which allows users to set the active item by typing.\n     * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n     */\n    withTypeAhead(debounceInterval = 200) {\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n            this._items.length &&\n            this._items.some(item => typeof item.getLabel !== 'function')) {\n            throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n        }\n        this._typeaheadSubscription.unsubscribe();\n        // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n        // and convert those letters back into a string. Afterwards find the first item that starts\n        // with that string and select it.\n        this._typeaheadSubscription = this._letterKeyStream\n            .pipe(tap(letter => this._pressedLetters.push(letter)), debounceTime(debounceInterval), filter(() => this._pressedLetters.length > 0), map(() => this._pressedLetters.join('')))\n            .subscribe(inputString => {\n            const items = this._getItemsArray();\n            // Start at 1 because we want to start searching at the item immediately\n            // following the current active item.\n            for (let i = 1; i < items.length + 1; i++) {\n                const index = (this._activeItemIndex + i) % items.length;\n                const item = items[index];\n                if (!this._skipPredicateFn(item) &&\n                    item.getLabel().toUpperCase().trim().indexOf(inputString) === 0) {\n                    this.setActiveItem(index);\n                    break;\n                }\n            }\n            this._pressedLetters = [];\n        });\n        return this;\n    }\n    /**\n     * Configures the key manager to activate the first and last items\n     * respectively when the Home or End key is pressed.\n     * @param enabled Whether pressing the Home or End key activates the first/last item.\n     */\n    withHomeAndEnd(enabled = true) {\n        this._homeAndEnd = enabled;\n        return this;\n    }\n    setActiveItem(item) {\n        const previousActiveItem = this._activeItem;\n        this.updateActiveItem(item);\n        if (this._activeItem !== previousActiveItem) {\n            this.change.next(this._activeItemIndex);\n        }\n    }\n    /**\n     * Sets the active item depending on the key event passed in.\n     * @param event Keyboard event to be used for determining which element should be active.\n     */\n    onKeydown(event) {\n        const keyCode = event.keyCode;\n        const modifiers = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n        const isModifierAllowed = modifiers.every(modifier => {\n            return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n        });\n        switch (keyCode) {\n            case TAB:\n                this.tabOut.next();\n                return;\n            case DOWN_ARROW:\n                if (this._vertical && isModifierAllowed) {\n                    this.setNextItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case UP_ARROW:\n                if (this._vertical && isModifierAllowed) {\n                    this.setPreviousItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case RIGHT_ARROW:\n                if (this._horizontal && isModifierAllowed) {\n                    this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case LEFT_ARROW:\n                if (this._horizontal && isModifierAllowed) {\n                    this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case HOME:\n                if (this._homeAndEnd && isModifierAllowed) {\n                    this.setFirstItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case END:\n                if (this._homeAndEnd && isModifierAllowed) {\n                    this.setLastItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            default:\n                if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n                    // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n                    // otherwise fall back to resolving alphanumeric characters via the keyCode.\n                    if (event.key && event.key.length === 1) {\n                        this._letterKeyStream.next(event.key.toLocaleUpperCase());\n                    }\n                    else if ((keyCode >= A && keyCode <= Z) || (keyCode >= ZERO && keyCode <= NINE)) {\n                        this._letterKeyStream.next(String.fromCharCode(keyCode));\n                    }\n                }\n                // Note that we return here, in order to avoid preventing\n                // the default action of non-navigational keys.\n                return;\n        }\n        this._pressedLetters = [];\n        event.preventDefault();\n    }\n    /** Index of the currently active item. */\n    get activeItemIndex() {\n        return this._activeItemIndex;\n    }\n    /** The active item. */\n    get activeItem() {\n        return this._activeItem;\n    }\n    /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n    isTyping() {\n        return this._pressedLetters.length > 0;\n    }\n    /** Sets the active item to the first enabled item in the list. */\n    setFirstItemActive() {\n        this._setActiveItemByIndex(0, 1);\n    }\n    /** Sets the active item to the last enabled item in the list. */\n    setLastItemActive() {\n        this._setActiveItemByIndex(this._items.length - 1, -1);\n    }\n    /** Sets the active item to the next enabled item in the list. */\n    setNextItemActive() {\n        this._activeItemIndex < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n    }\n    /** Sets the active item to a previous enabled item in the list. */\n    setPreviousItemActive() {\n        this._activeItemIndex < 0 && this._wrap\n            ? this.setLastItemActive()\n            : this._setActiveItemByDelta(-1);\n    }\n    updateActiveItem(item) {\n        const itemArray = this._getItemsArray();\n        const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n        const activeItem = itemArray[index];\n        // Explicitly check for `null` and `undefined` because other falsy values are valid.\n        this._activeItem = activeItem == null ? null : activeItem;\n        this._activeItemIndex = index;\n    }\n    /**\n     * This method sets the active item, given a list of items and the delta between the\n     * currently active item and the new active item. It will calculate differently\n     * depending on whether wrap mode is turned on.\n     */\n    _setActiveItemByDelta(delta) {\n        this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n    }\n    /**\n     * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n     * down the list until it finds an item that is not disabled, and it will wrap if it\n     * encounters either end of the list.\n     */\n    _setActiveInWrapMode(delta) {\n        const items = this._getItemsArray();\n        for (let i = 1; i <= items.length; i++) {\n            const index = (this._activeItemIndex + delta * i + items.length) % items.length;\n            const item = items[index];\n            if (!this._skipPredicateFn(item)) {\n                this.setActiveItem(index);\n                return;\n            }\n        }\n    }\n    /**\n     * Sets the active item properly given the default mode. In other words, it will\n     * continue to move down the list until it finds an item that is not disabled. If\n     * it encounters either end of the list, it will stop and not wrap.\n     */\n    _setActiveInDefaultMode(delta) {\n        this._setActiveItemByIndex(this._activeItemIndex + delta, delta);\n    }\n    /**\n     * Sets the active item to the first enabled item starting at the index specified. If the\n     * item is disabled, it will move in the fallbackDelta direction until it either\n     * finds an enabled item or encounters the end of the list.\n     */\n    _setActiveItemByIndex(index, fallbackDelta) {\n        const items = this._getItemsArray();\n        if (!items[index]) {\n            return;\n        }\n        while (this._skipPredicateFn(items[index])) {\n            index += fallbackDelta;\n            if (!items[index]) {\n                return;\n            }\n        }\n        this.setActiveItem(index);\n    }\n    /** Returns the items as an array. */\n    _getItemsArray() {\n        return this._items instanceof QueryList ? this._items.toArray() : this._items;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass ActiveDescendantKeyManager extends ListKeyManager {\n    setActiveItem(index) {\n        if (this.activeItem) {\n            this.activeItem.setInactiveStyles();\n        }\n        super.setActiveItem(index);\n        if (this.activeItem) {\n            this.activeItem.setActiveStyles();\n        }\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass FocusKeyManager extends ListKeyManager {\n    constructor() {\n        super(...arguments);\n        this._origin = 'program';\n    }\n    /**\n     * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n     * @param origin Focus origin to be used when focusing items.\n     */\n    setFocusOrigin(origin) {\n        this._origin = origin;\n        return this;\n    }\n    setActiveItem(item) {\n        super.setActiveItem(item);\n        if (this.activeItem) {\n            this.activeItem.focus(this._origin);\n        }\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Configuration for the isFocusable method.\n */\nclass IsFocusableConfig {\n    constructor() {\n        /**\n         * Whether to count an element as focusable even if it is not currently visible.\n         */\n        this.ignoreVisibility = false;\n    }\n}\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n/**\n * Utility for checking the interactivity of an element, such as whether is is focusable or\n * tabbable.\n */\nclass InteractivityChecker {\n    constructor(_platform) {\n        this._platform = _platform;\n    }\n    /**\n     * Gets whether an element is disabled.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is disabled.\n     */\n    isDisabled(element) {\n        // This does not capture some cases, such as a non-form control with a disabled attribute or\n        // a form control inside of a disabled form, but should capture the most common cases.\n        return element.hasAttribute('disabled');\n    }\n    /**\n     * Gets whether an element is visible for the purposes of interactivity.\n     *\n     * This will capture states like `display: none` and `visibility: hidden`, but not things like\n     * being clipped by an `overflow: hidden` parent or being outside the viewport.\n     *\n     * @returns Whether the element is visible.\n     */\n    isVisible(element) {\n        return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n    }\n    /**\n     * Gets whether an element can be reached via Tab key.\n     * Assumes that the element has already been checked with isFocusable.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is tabbable.\n     */\n    isTabbable(element) {\n        // Nothing is tabbable on the server 😎\n        if (!this._platform.isBrowser) {\n            return false;\n        }\n        const frameElement = getFrameElement(getWindow(element));\n        if (frameElement) {\n            // Frame elements inherit their tabindex onto all child elements.\n            if (getTabIndexValue(frameElement) === -1) {\n                return false;\n            }\n            // Browsers disable tabbing to an element inside of an invisible frame.\n            if (!this.isVisible(frameElement)) {\n                return false;\n            }\n        }\n        let nodeName = element.nodeName.toLowerCase();\n        let tabIndexValue = getTabIndexValue(element);\n        if (element.hasAttribute('contenteditable')) {\n            return tabIndexValue !== -1;\n        }\n        if (nodeName === 'iframe' || nodeName === 'object') {\n            // The frame or object's content may be tabbable depending on the content, but it's\n            // not possibly to reliably detect the content of the frames. We always consider such\n            // elements as non-tabbable.\n            return false;\n        }\n        // In iOS, the browser only considers some specific elements as tabbable.\n        if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n            return false;\n        }\n        if (nodeName === 'audio') {\n            // Audio elements without controls enabled are never tabbable, regardless\n            // of the tabindex attribute explicitly being set.\n            if (!element.hasAttribute('controls')) {\n                return false;\n            }\n            // Audio elements with controls are by default tabbable unless the\n            // tabindex attribute is set to `-1` explicitly.\n            return tabIndexValue !== -1;\n        }\n        if (nodeName === 'video') {\n            // For all video elements, if the tabindex attribute is set to `-1`, the video\n            // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n            // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n            // tabindex attribute is the source of truth here.\n            if (tabIndexValue === -1) {\n                return false;\n            }\n            // If the tabindex is explicitly set, and not `-1` (as per check before), the\n            // video element is always tabbable (regardless of whether it has controls or not).\n            if (tabIndexValue !== null) {\n                return true;\n            }\n            // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n            // has controls enabled. Firefox is special as videos are always tabbable regardless\n            // of whether there are controls or not.\n            return this._platform.FIREFOX || element.hasAttribute('controls');\n        }\n        return element.tabIndex >= 0;\n    }\n    /**\n     * Gets whether an element can be focused by the user.\n     *\n     * @param element Element to be checked.\n     * @param config The config object with options to customize this method's behavior\n     * @returns Whether the element is focusable.\n     */\n    isFocusable(element, config) {\n        // Perform checks in order of left to most expensive.\n        // Again, naive approach that does not capture many edge cases and browser quirks.\n        return (isPotentiallyFocusable(element) &&\n            !this.isDisabled(element) &&\n            (config?.ignoreVisibility || this.isVisible(element)));\n    }\n}\nInteractivityChecker.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: InteractivityChecker, deps: [{ token: i1.Platform }], target: i0.ɵɵFactoryTarget.Injectable });\nInteractivityChecker.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: InteractivityChecker, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: InteractivityChecker, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.Platform }]; } });\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window) {\n    try {\n        return window.frameElement;\n    }\n    catch {\n        return null;\n    }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element) {\n    // Use logic from jQuery to check for an invisible element.\n    // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n    return !!(element.offsetWidth ||\n        element.offsetHeight ||\n        (typeof element.getClientRects === 'function' && element.getClientRects().length));\n}\n/** Gets whether an element's  */\nfunction isNativeFormElement(element) {\n    let nodeName = element.nodeName.toLowerCase();\n    return (nodeName === 'input' ||\n        nodeName === 'select' ||\n        nodeName === 'button' ||\n        nodeName === 'textarea');\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element) {\n    return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element) {\n    return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\nfunction isInputElement(element) {\n    return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element) {\n    return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element) {\n    if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n        return false;\n    }\n    let tabIndex = element.getAttribute('tabindex');\n    return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element) {\n    if (!hasValidTabIndex(element)) {\n        return null;\n    }\n    // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n    const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n    return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element) {\n    let nodeName = element.nodeName.toLowerCase();\n    let inputType = nodeName === 'input' && element.type;\n    return (inputType === 'text' ||\n        inputType === 'password' ||\n        nodeName === 'select' ||\n        nodeName === 'textarea');\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element) {\n    // Inputs are potentially focusable *unless* they're type=\"hidden\".\n    if (isHiddenInput(element)) {\n        return false;\n    }\n    return (isNativeFormElement(element) ||\n        isAnchorWithHref(element) ||\n        element.hasAttribute('contenteditable') ||\n        hasValidTabIndex(element));\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node) {\n    // ownerDocument is null if `node` itself *is* a document.\n    return (node.ownerDocument && node.ownerDocument.defaultView) || window;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n *\n * @deprecated Use `ConfigurableFocusTrap` instead.\n * @breaking-change 11.0.0\n */\nclass FocusTrap {\n    constructor(_element, _checker, _ngZone, _document, deferAnchors = false) {\n        this._element = _element;\n        this._checker = _checker;\n        this._ngZone = _ngZone;\n        this._document = _document;\n        this._hasAttached = false;\n        // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n        this.startAnchorListener = () => this.focusLastTabbableElement();\n        this.endAnchorListener = () => this.focusFirstTabbableElement();\n        this._enabled = true;\n        if (!deferAnchors) {\n            this.attachAnchors();\n        }\n    }\n    /** Whether the focus trap is active. */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        this._enabled = value;\n        if (this._startAnchor && this._endAnchor) {\n            this._toggleAnchorTabIndex(value, this._startAnchor);\n            this._toggleAnchorTabIndex(value, this._endAnchor);\n        }\n    }\n    /** Destroys the focus trap by cleaning up the anchors. */\n    destroy() {\n        const startAnchor = this._startAnchor;\n        const endAnchor = this._endAnchor;\n        if (startAnchor) {\n            startAnchor.removeEventListener('focus', this.startAnchorListener);\n            startAnchor.remove();\n        }\n        if (endAnchor) {\n            endAnchor.removeEventListener('focus', this.endAnchorListener);\n            endAnchor.remove();\n        }\n        this._startAnchor = this._endAnchor = null;\n        this._hasAttached = false;\n    }\n    /**\n     * Inserts the anchors into the DOM. This is usually done automatically\n     * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n     * @returns Whether the focus trap managed to attach successfully. This may not be the case\n     * if the target element isn't currently in the DOM.\n     */\n    attachAnchors() {\n        // If we're not on the browser, there can be no focus to trap.\n        if (this._hasAttached) {\n            return true;\n        }\n        this._ngZone.runOutsideAngular(() => {\n            if (!this._startAnchor) {\n                this._startAnchor = this._createAnchor();\n                this._startAnchor.addEventListener('focus', this.startAnchorListener);\n            }\n            if (!this._endAnchor) {\n                this._endAnchor = this._createAnchor();\n                this._endAnchor.addEventListener('focus', this.endAnchorListener);\n            }\n        });\n        if (this._element.parentNode) {\n            this._element.parentNode.insertBefore(this._startAnchor, this._element);\n            this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n            this._hasAttached = true;\n        }\n        return this._hasAttached;\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses the first tabbable element.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusInitialElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n        });\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses\n     * the first tabbable element within the focus trap region.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusFirstTabbableElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n        });\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses\n     * the last tabbable element within the focus trap region.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusLastTabbableElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n        });\n    }\n    /**\n     * Get the specified boundary element of the trapped region.\n     * @param bound The boundary to get (start or end of trapped region).\n     * @returns The boundary element.\n     */\n    _getRegionBoundary(bound) {\n        // Contains the deprecated version of selector, for temporary backwards comparability.\n        const markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            for (let i = 0; i < markers.length; i++) {\n                // @breaking-change 8.0.0\n                if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n                    console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` +\n                        `use 'cdkFocusRegion${bound}' instead. The deprecated ` +\n                        `attribute will be removed in 8.0.0.`, markers[i]);\n                }\n                else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n                    console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` +\n                        `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` +\n                        `will be removed in 8.0.0.`, markers[i]);\n                }\n            }\n        }\n        if (bound == 'start') {\n            return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n        }\n        return markers.length\n            ? markers[markers.length - 1]\n            : this._getLastTabbableElement(this._element);\n    }\n    /**\n     * Focuses the element that should be focused when the focus trap is initialized.\n     * @returns Whether focus was moved successfully.\n     */\n    focusInitialElement(options) {\n        // Contains the deprecated version of selector, for temporary backwards comparability.\n        const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n        if (redirectToElement) {\n            // @breaking-change 8.0.0\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n                redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n                console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` +\n                    `use 'cdkFocusInitial' instead. The deprecated attribute ` +\n                    `will be removed in 8.0.0`, redirectToElement);\n            }\n            // Warn the consumer if the element they've pointed to\n            // isn't focusable, when not in production mode.\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n                !this._checker.isFocusable(redirectToElement)) {\n                console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n            }\n            if (!this._checker.isFocusable(redirectToElement)) {\n                const focusableChild = this._getFirstTabbableElement(redirectToElement);\n                focusableChild?.focus(options);\n                return !!focusableChild;\n            }\n            redirectToElement.focus(options);\n            return true;\n        }\n        return this.focusFirstTabbableElement(options);\n    }\n    /**\n     * Focuses the first tabbable element within the focus trap region.\n     * @returns Whether focus was moved successfully.\n     */\n    focusFirstTabbableElement(options) {\n        const redirectToElement = this._getRegionBoundary('start');\n        if (redirectToElement) {\n            redirectToElement.focus(options);\n        }\n        return !!redirectToElement;\n    }\n    /**\n     * Focuses the last tabbable element within the focus trap region.\n     * @returns Whether focus was moved successfully.\n     */\n    focusLastTabbableElement(options) {\n        const redirectToElement = this._getRegionBoundary('end');\n        if (redirectToElement) {\n            redirectToElement.focus(options);\n        }\n        return !!redirectToElement;\n    }\n    /**\n     * Checks whether the focus trap has successfully been attached.\n     */\n    hasAttached() {\n        return this._hasAttached;\n    }\n    /** Get the first tabbable element from a DOM subtree (inclusive). */\n    _getFirstTabbableElement(root) {\n        if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n            return root;\n        }\n        const children = root.children;\n        for (let i = 0; i < children.length; i++) {\n            const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE\n                ? this._getFirstTabbableElement(children[i])\n                : null;\n            if (tabbableChild) {\n                return tabbableChild;\n            }\n        }\n        return null;\n    }\n    /** Get the last tabbable element from a DOM subtree (inclusive). */\n    _getLastTabbableElement(root) {\n        if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n            return root;\n        }\n        // Iterate in reverse DOM order.\n        const children = root.children;\n        for (let i = children.length - 1; i >= 0; i--) {\n            const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE\n                ? this._getLastTabbableElement(children[i])\n                : null;\n            if (tabbableChild) {\n                return tabbableChild;\n            }\n        }\n        return null;\n    }\n    /** Creates an anchor element. */\n    _createAnchor() {\n        const anchor = this._document.createElement('div');\n        this._toggleAnchorTabIndex(this._enabled, anchor);\n        anchor.classList.add('cdk-visually-hidden');\n        anchor.classList.add('cdk-focus-trap-anchor');\n        anchor.setAttribute('aria-hidden', 'true');\n        return anchor;\n    }\n    /**\n     * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n     * @param isEnabled Whether the focus trap is enabled.\n     * @param anchor Anchor on which to toggle the tabindex.\n     */\n    _toggleAnchorTabIndex(isEnabled, anchor) {\n        // Remove the tabindex completely, rather than setting it to -1, because if the\n        // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n        isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n    }\n    /**\n     * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n     * @param enabled: Whether the anchors should trap Tab.\n     */\n    toggleAnchors(enabled) {\n        if (this._startAnchor && this._endAnchor) {\n            this._toggleAnchorTabIndex(enabled, this._startAnchor);\n            this._toggleAnchorTabIndex(enabled, this._endAnchor);\n        }\n    }\n    /** Executes a function when the zone is stable. */\n    _executeOnStable(fn) {\n        if (this._ngZone.isStable) {\n            fn();\n        }\n        else {\n            this._ngZone.onStable.pipe(take(1)).subscribe(fn);\n        }\n    }\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n * @deprecated Use `ConfigurableFocusTrapFactory` instead.\n * @breaking-change 11.0.0\n */\nclass FocusTrapFactory {\n    constructor(_checker, _ngZone, _document) {\n        this._checker = _checker;\n        this._ngZone = _ngZone;\n        this._document = _document;\n    }\n    /**\n     * Creates a focus-trapped region around the given element.\n     * @param element The element around which focus will be trapped.\n     * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n     *     manually by the user.\n     * @returns The created focus trap instance.\n     */\n    create(element, deferCaptureElements = false) {\n        return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements);\n    }\n}\nFocusTrapFactory.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: FocusTrapFactory, deps: [{ token: InteractivityChecker }, { token: i0.NgZone }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nFocusTrapFactory.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: FocusTrapFactory, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: FocusTrapFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: InteractivityChecker }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n/** Directive for trapping focus within a region. */\nclass CdkTrapFocus {\n    constructor(_elementRef, _focusTrapFactory, \n    /**\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 13.0.0\n     */\n    _document) {\n        this._elementRef = _elementRef;\n        this._focusTrapFactory = _focusTrapFactory;\n        /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n        this._previouslyFocusedElement = null;\n        this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n    }\n    /** Whether the focus trap is active. */\n    get enabled() {\n        return this.focusTrap.enabled;\n    }\n    set enabled(value) {\n        this.focusTrap.enabled = coerceBooleanProperty(value);\n    }\n    /**\n     * Whether the directive should automatically move focus into the trapped region upon\n     * initialization and return focus to the previous activeElement upon destruction.\n     */\n    get autoCapture() {\n        return this._autoCapture;\n    }\n    set autoCapture(value) {\n        this._autoCapture = coerceBooleanProperty(value);\n    }\n    ngOnDestroy() {\n        this.focusTrap.destroy();\n        // If we stored a previously focused element when using autoCapture, return focus to that\n        // element now that the trapped region is being destroyed.\n        if (this._previouslyFocusedElement) {\n            this._previouslyFocusedElement.focus();\n            this._previouslyFocusedElement = null;\n        }\n    }\n    ngAfterContentInit() {\n        this.focusTrap.attachAnchors();\n        if (this.autoCapture) {\n            this._captureFocus();\n        }\n    }\n    ngDoCheck() {\n        if (!this.focusTrap.hasAttached()) {\n            this.focusTrap.attachAnchors();\n        }\n    }\n    ngOnChanges(changes) {\n        const autoCaptureChange = changes['autoCapture'];\n        if (autoCaptureChange &&\n            !autoCaptureChange.firstChange &&\n            this.autoCapture &&\n            this.focusTrap.hasAttached()) {\n            this._captureFocus();\n        }\n    }\n    _captureFocus() {\n        this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n        this.focusTrap.focusInitialElementWhenReady();\n    }\n}\nCdkTrapFocus.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkTrapFocus, deps: [{ token: i0.ElementRef }, { token: FocusTrapFactory }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive });\nCdkTrapFocus.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: CdkTrapFocus, selector: \"[cdkTrapFocus]\", inputs: { enabled: [\"cdkTrapFocus\", \"enabled\"], autoCapture: [\"cdkTrapFocusAutoCapture\", \"autoCapture\"] }, exportAs: [\"cdkTrapFocus\"], usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkTrapFocus, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkTrapFocus]',\n                    exportAs: 'cdkTrapFocus',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: FocusTrapFactory }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; }, propDecorators: { enabled: [{\n                type: Input,\n                args: ['cdkTrapFocus']\n            }], autoCapture: [{\n                type: Input,\n                args: ['cdkTrapFocusAutoCapture']\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\nclass ConfigurableFocusTrap extends FocusTrap {\n    constructor(_element, _checker, _ngZone, _document, _focusTrapManager, _inertStrategy, config) {\n        super(_element, _checker, _ngZone, _document, config.defer);\n        this._focusTrapManager = _focusTrapManager;\n        this._inertStrategy = _inertStrategy;\n        this._focusTrapManager.register(this);\n    }\n    /** Whether the FocusTrap is enabled. */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        this._enabled = value;\n        if (this._enabled) {\n            this._focusTrapManager.register(this);\n        }\n        else {\n            this._focusTrapManager.deregister(this);\n        }\n    }\n    /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n    destroy() {\n        this._focusTrapManager.deregister(this);\n        super.destroy();\n    }\n    /** @docs-private Implemented as part of ManagedFocusTrap. */\n    _enable() {\n        this._inertStrategy.preventFocus(this);\n        this.toggleAnchors(true);\n    }\n    /** @docs-private Implemented as part of ManagedFocusTrap. */\n    _disable() {\n        this._inertStrategy.allowFocus(this);\n        this.toggleAnchors(false);\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** The injection token used to specify the inert strategy. */\nconst FOCUS_TRAP_INERT_STRATEGY = new InjectionToken('FOCUS_TRAP_INERT_STRATEGY');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\nclass EventListenerFocusTrapInertStrategy {\n    constructor() {\n        /** Focus event handler. */\n        this._listener = null;\n    }\n    /** Adds a document event listener that keeps focus inside the FocusTrap. */\n    preventFocus(focusTrap) {\n        // Ensure there's only one listener per document\n        if (this._listener) {\n            focusTrap._document.removeEventListener('focus', this._listener, true);\n        }\n        this._listener = (e) => this._trapFocus(focusTrap, e);\n        focusTrap._ngZone.runOutsideAngular(() => {\n            focusTrap._document.addEventListener('focus', this._listener, true);\n        });\n    }\n    /** Removes the event listener added in preventFocus. */\n    allowFocus(focusTrap) {\n        if (!this._listener) {\n            return;\n        }\n        focusTrap._document.removeEventListener('focus', this._listener, true);\n        this._listener = null;\n    }\n    /**\n     * Refocuses the first element in the FocusTrap if the focus event target was outside\n     * the FocusTrap.\n     *\n     * This is an event listener callback. The event listener is added in runOutsideAngular,\n     * so all this code runs outside Angular as well.\n     */\n    _trapFocus(focusTrap, event) {\n        const target = event.target;\n        const focusTrapRoot = focusTrap._element;\n        // Don't refocus if target was in an overlay, because the overlay might be associated\n        // with an element inside the FocusTrap, ex. mat-select.\n        if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n            // Some legacy FocusTrap usages have logic that focuses some element on the page\n            // just before FocusTrap is destroyed. For backwards compatibility, wait\n            // to be sure FocusTrap is still enabled before refocusing.\n            setTimeout(() => {\n                // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n                if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n                    focusTrap.focusFirstTabbableElement();\n                }\n            });\n        }\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\nclass FocusTrapManager {\n    constructor() {\n        // A stack of the FocusTraps on the page. Only the FocusTrap at the\n        // top of the stack is active.\n        this._focusTrapStack = [];\n    }\n    /**\n     * Disables the FocusTrap at the top of the stack, and then pushes\n     * the new FocusTrap onto the stack.\n     */\n    register(focusTrap) {\n        // Dedupe focusTraps that register multiple times.\n        this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n        let stack = this._focusTrapStack;\n        if (stack.length) {\n            stack[stack.length - 1]._disable();\n        }\n        stack.push(focusTrap);\n        focusTrap._enable();\n    }\n    /**\n     * Removes the FocusTrap from the stack, and activates the\n     * FocusTrap that is the new top of the stack.\n     */\n    deregister(focusTrap) {\n        focusTrap._disable();\n        const stack = this._focusTrapStack;\n        const i = stack.indexOf(focusTrap);\n        if (i !== -1) {\n            stack.splice(i, 1);\n            if (stack.length) {\n                stack[stack.length - 1]._enable();\n            }\n        }\n    }\n}\nFocusTrapManager.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: FocusTrapManager, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nFocusTrapManager.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: FocusTrapManager, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: FocusTrapManager, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Factory that allows easy instantiation of configurable focus traps. */\nclass ConfigurableFocusTrapFactory {\n    constructor(_checker, _ngZone, _focusTrapManager, _document, _inertStrategy) {\n        this._checker = _checker;\n        this._ngZone = _ngZone;\n        this._focusTrapManager = _focusTrapManager;\n        this._document = _document;\n        // TODO split up the strategies into different modules, similar to DateAdapter.\n        this._inertStrategy = _inertStrategy || new EventListenerFocusTrapInertStrategy();\n    }\n    create(element, config = { defer: false }) {\n        let configObject;\n        if (typeof config === 'boolean') {\n            configObject = { defer: config };\n        }\n        else {\n            configObject = config;\n        }\n        return new ConfigurableFocusTrap(element, this._checker, this._ngZone, this._document, this._focusTrapManager, this._inertStrategy, configObject);\n    }\n}\nConfigurableFocusTrapFactory.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: ConfigurableFocusTrapFactory, deps: [{ token: InteractivityChecker }, { token: i0.NgZone }, { token: FocusTrapManager }, { token: DOCUMENT }, { token: FOCUS_TRAP_INERT_STRATEGY, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nConfigurableFocusTrapFactory.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: ConfigurableFocusTrapFactory, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: ConfigurableFocusTrapFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: InteractivityChecker }, { type: i0.NgZone }, { type: FocusTrapManager }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [FOCUS_TRAP_INERT_STRATEGY]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */\nfunction isFakeMousedownFromScreenReader(event) {\n    // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on\n    // a clickable element. We can distinguish these events when both `offsetX` and `offsetY` are\n    // zero or `event.buttons` is zero, depending on the browser:\n    // - `event.buttons` works on Firefox, but fails on Chrome.\n    // - `offsetX` and `offsetY` work on Chrome, but fail on Firefox.\n    // Note that there's an edge case where the user could click the 0x0 spot of the\n    // screen themselves, but that is unlikely to contain interactive elements.\n    return event.buttons === 0 || (event.offsetX === 0 && event.offsetY === 0);\n}\n/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */\nfunction isFakeTouchstartFromScreenReader(event) {\n    const touch = (event.touches && event.touches[0]) || (event.changedTouches && event.changedTouches[0]);\n    // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`\n    // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,\n    // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10\n    // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.\n    return (!!touch &&\n        touch.identifier === -1 &&\n        (touch.radiusX == null || touch.radiusX === 1) &&\n        (touch.radiusY == null || touch.radiusY === 1));\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\nconst INPUT_MODALITY_DETECTOR_OPTIONS = new InjectionToken('cdk-input-modality-detector-options');\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\nconst INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS = {\n    ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT],\n};\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\nconst TOUCH_BUFFER_MS = 650;\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\nconst modalityEventListenerOptions = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\nclass InputModalityDetector {\n    constructor(_platform, ngZone, document, options) {\n        this._platform = _platform;\n        /**\n         * The most recently detected input modality event target. Is null if no input modality has been\n         * detected or if the associated event target is null for some unknown reason.\n         */\n        this._mostRecentTarget = null;\n        /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n        this._modality = new BehaviorSubject(null);\n        /**\n         * The timestamp of the last touch input modality. Used to determine whether mousedown events\n         * should be attributed to mouse or touch.\n         */\n        this._lastTouchMs = 0;\n        /**\n         * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n         * bound.\n         */\n        this._onKeydown = (event) => {\n            // If this is one of the keys we should ignore, then ignore it and don't update the input\n            // modality to keyboard.\n            if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n                return;\n            }\n            this._modality.next('keyboard');\n            this._mostRecentTarget = _getEventTarget(event);\n        };\n        /**\n         * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n         * gets bound.\n         */\n        this._onMousedown = (event) => {\n            // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n            // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n            // after the previous touch event.\n            if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n                return;\n            }\n            // Fake mousedown events are fired by some screen readers when controls are activated by the\n            // screen reader. Attribute them to keyboard input modality.\n            this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n            this._mostRecentTarget = _getEventTarget(event);\n        };\n        /**\n         * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n         * gets bound.\n         */\n        this._onTouchstart = (event) => {\n            // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n            // events are fired. Again, attribute to keyboard input modality.\n            if (isFakeTouchstartFromScreenReader(event)) {\n                this._modality.next('keyboard');\n                return;\n            }\n            // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n            // triggered via mouse vs touch.\n            this._lastTouchMs = Date.now();\n            this._modality.next('touch');\n            this._mostRecentTarget = _getEventTarget(event);\n        };\n        this._options = {\n            ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n            ...options,\n        };\n        // Skip the first emission as it's null.\n        this.modalityDetected = this._modality.pipe(skip(1));\n        this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged());\n        // If we're not in a browser, this service should do nothing, as there's no relevant input\n        // modality to detect.\n        if (_platform.isBrowser) {\n            ngZone.runOutsideAngular(() => {\n                document.addEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n                document.addEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n                document.addEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n            });\n        }\n    }\n    /** The most recently detected input modality. */\n    get mostRecentModality() {\n        return this._modality.value;\n    }\n    ngOnDestroy() {\n        this._modality.complete();\n        if (this._platform.isBrowser) {\n            document.removeEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n            document.removeEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n            document.removeEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n        }\n    }\n}\nInputModalityDetector.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: InputModalityDetector, deps: [{ token: i1.Platform }, { token: i0.NgZone }, { token: DOCUMENT }, { token: INPUT_MODALITY_DETECTOR_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nInputModalityDetector.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: InputModalityDetector, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: InputModalityDetector, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.Platform }, { type: i0.NgZone }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [INPUT_MODALITY_DETECTOR_OPTIONS]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken('liveAnnouncerElement', {\n    providedIn: 'root',\n    factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY,\n});\n/** @docs-private */\nfunction LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {\n    return null;\n}\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\nconst LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass LiveAnnouncer {\n    constructor(elementToken, _ngZone, _document, _defaultOptions) {\n        this._ngZone = _ngZone;\n        this._defaultOptions = _defaultOptions;\n        // We inject the live element and document as `any` because the constructor signature cannot\n        // reference browser globals (HTMLElement, Document) on non-browser environments, since having\n        // a class decorator causes TypeScript to preserve the constructor signature types.\n        this._document = _document;\n        this._liveElement = elementToken || this._createLiveElement();\n    }\n    announce(message, ...args) {\n        const defaultOptions = this._defaultOptions;\n        let politeness;\n        let duration;\n        if (args.length === 1 && typeof args[0] === 'number') {\n            duration = args[0];\n        }\n        else {\n            [politeness, duration] = args;\n        }\n        this.clear();\n        clearTimeout(this._previousTimeout);\n        if (!politeness) {\n            politeness =\n                defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n        }\n        if (duration == null && defaultOptions) {\n            duration = defaultOptions.duration;\n        }\n        // TODO: ensure changing the politeness works on all environments we support.\n        this._liveElement.setAttribute('aria-live', politeness);\n        // This 100ms timeout is necessary for some browser + screen-reader combinations:\n        // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n        // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n        //   second time without clearing and then using a non-zero delay.\n        // (using JAWS 17 at time of this writing).\n        return this._ngZone.runOutsideAngular(() => {\n            if (!this._currentPromise) {\n                this._currentPromise = new Promise(resolve => (this._currentResolve = resolve));\n            }\n            clearTimeout(this._previousTimeout);\n            this._previousTimeout = setTimeout(() => {\n                this._liveElement.textContent = message;\n                if (typeof duration === 'number') {\n                    this._previousTimeout = setTimeout(() => this.clear(), duration);\n                }\n                this._currentResolve();\n                this._currentPromise = this._currentResolve = undefined;\n            }, 100);\n            return this._currentPromise;\n        });\n    }\n    /**\n     * Clears the current text from the announcer element. Can be used to prevent\n     * screen readers from reading the text out again while the user is going\n     * through the page landmarks.\n     */\n    clear() {\n        if (this._liveElement) {\n            this._liveElement.textContent = '';\n        }\n    }\n    ngOnDestroy() {\n        clearTimeout(this._previousTimeout);\n        this._liveElement?.remove();\n        this._liveElement = null;\n        this._currentResolve?.();\n        this._currentPromise = this._currentResolve = undefined;\n    }\n    _createLiveElement() {\n        const elementClass = 'cdk-live-announcer-element';\n        const previousElements = this._document.getElementsByClassName(elementClass);\n        const liveEl = this._document.createElement('div');\n        // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n        for (let i = 0; i < previousElements.length; i++) {\n            previousElements[i].remove();\n        }\n        liveEl.classList.add(elementClass);\n        liveEl.classList.add('cdk-visually-hidden');\n        liveEl.setAttribute('aria-atomic', 'true');\n        liveEl.setAttribute('aria-live', 'polite');\n        this._document.body.appendChild(liveEl);\n        return liveEl;\n    }\n}\nLiveAnnouncer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: LiveAnnouncer, deps: [{ token: LIVE_ANNOUNCER_ELEMENT_TOKEN, optional: true }, { token: i0.NgZone }, { token: DOCUMENT }, { token: LIVE_ANNOUNCER_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nLiveAnnouncer.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: LiveAnnouncer, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: LiveAnnouncer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [LIVE_ANNOUNCER_ELEMENT_TOKEN]\n                }] }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [LIVE_ANNOUNCER_DEFAULT_OPTIONS]\n                }] }]; } });\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\nclass CdkAriaLive {\n    constructor(_elementRef, _liveAnnouncer, _contentObserver, _ngZone) {\n        this._elementRef = _elementRef;\n        this._liveAnnouncer = _liveAnnouncer;\n        this._contentObserver = _contentObserver;\n        this._ngZone = _ngZone;\n        this._politeness = 'polite';\n    }\n    /** The aria-live politeness level to use when announcing messages. */\n    get politeness() {\n        return this._politeness;\n    }\n    set politeness(value) {\n        this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n        if (this._politeness === 'off') {\n            if (this._subscription) {\n                this._subscription.unsubscribe();\n                this._subscription = null;\n            }\n        }\n        else if (!this._subscription) {\n            this._subscription = this._ngZone.runOutsideAngular(() => {\n                return this._contentObserver.observe(this._elementRef).subscribe(() => {\n                    // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n                    const elementText = this._elementRef.nativeElement.textContent;\n                    // The `MutationObserver` fires also for attribute\n                    // changes which we don't want to announce.\n                    if (elementText !== this._previousAnnouncedText) {\n                        this._liveAnnouncer.announce(elementText, this._politeness, this.duration);\n                        this._previousAnnouncedText = elementText;\n                    }\n                });\n            });\n        }\n    }\n    ngOnDestroy() {\n        if (this._subscription) {\n            this._subscription.unsubscribe();\n        }\n    }\n}\nCdkAriaLive.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkAriaLive, deps: [{ token: i0.ElementRef }, { token: LiveAnnouncer }, { token: i1$1.ContentObserver }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive });\nCdkAriaLive.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: CdkAriaLive, selector: \"[cdkAriaLive]\", inputs: { politeness: [\"cdkAriaLive\", \"politeness\"], duration: [\"cdkAriaLiveDuration\", \"duration\"] }, exportAs: [\"cdkAriaLive\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkAriaLive, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkAriaLive]',\n                    exportAs: 'cdkAriaLive',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: LiveAnnouncer }, { type: i1$1.ContentObserver }, { type: i0.NgZone }]; }, propDecorators: { politeness: [{\n                type: Input,\n                args: ['cdkAriaLive']\n            }], duration: [{\n                type: Input,\n                args: ['cdkAriaLiveDuration']\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** InjectionToken for FocusMonitorOptions. */\nconst FOCUS_MONITOR_DEFAULT_OPTIONS = new InjectionToken('cdk-focus-monitor-default-options');\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\nconst captureEventListenerOptions = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\nclass FocusMonitor {\n    constructor(_ngZone, _platform, _inputModalityDetector, \n    /** @breaking-change 11.0.0 make document required */\n    document, options) {\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        this._inputModalityDetector = _inputModalityDetector;\n        /** The focus origin that the next focus event is a result of. */\n        this._origin = null;\n        /** Whether the window has just been focused. */\n        this._windowFocused = false;\n        /**\n         * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n         * focus events to touch interactions requires special logic.\n         */\n        this._originFromTouchInteraction = false;\n        /** Map of elements being monitored to their info. */\n        this._elementInfo = new Map();\n        /** The number of elements currently being monitored. */\n        this._monitoredElementCount = 0;\n        /**\n         * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n         * as well as the number of monitored elements that they contain. We have to treat focus/blur\n         * handlers differently from the rest of the events, because the browser won't emit events\n         * to the document when focus moves inside of a shadow root.\n         */\n        this._rootNodeFocusListenerCount = new Map();\n        /**\n         * Event listener for `focus` events on the window.\n         * Needs to be an arrow function in order to preserve the context when it gets bound.\n         */\n        this._windowFocusListener = () => {\n            // Make a note of when the window regains focus, so we can\n            // restore the origin info for the focused element.\n            this._windowFocused = true;\n            this._windowFocusTimeoutId = window.setTimeout(() => (this._windowFocused = false));\n        };\n        /** Subject for stopping our InputModalityDetector subscription. */\n        this._stopInputModalityDetector = new Subject();\n        /**\n         * Event listener for `focus` and 'blur' events on the document.\n         * Needs to be an arrow function in order to preserve the context when it gets bound.\n         */\n        this._rootNodeFocusAndBlurListener = (event) => {\n            const target = _getEventTarget(event);\n            // We need to walk up the ancestor chain in order to support `checkChildren`.\n            for (let element = target; element; element = element.parentElement) {\n                if (event.type === 'focus') {\n                    this._onFocus(event, element);\n                }\n                else {\n                    this._onBlur(event, element);\n                }\n            }\n        };\n        this._document = document;\n        this._detectionMode = options?.detectionMode || 0 /* FocusMonitorDetectionMode.IMMEDIATE */;\n    }\n    monitor(element, checkChildren = false) {\n        const nativeElement = coerceElement(element);\n        // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n        if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n            return of(null);\n        }\n        // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n        // the shadow root, rather than the `document`, because the browser won't emit focus events\n        // to the `document`, if focus is moving within the same shadow root.\n        const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n        const cachedInfo = this._elementInfo.get(nativeElement);\n        // Check if we're already monitoring this element.\n        if (cachedInfo) {\n            if (checkChildren) {\n                // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n                // observers into ones that behave as if `checkChildren` was turned on. We need a more\n                // robust solution.\n                cachedInfo.checkChildren = true;\n            }\n            return cachedInfo.subject;\n        }\n        // Create monitored element info.\n        const info = {\n            checkChildren: checkChildren,\n            subject: new Subject(),\n            rootNode,\n        };\n        this._elementInfo.set(nativeElement, info);\n        this._registerGlobalListeners(info);\n        return info.subject;\n    }\n    stopMonitoring(element) {\n        const nativeElement = coerceElement(element);\n        const elementInfo = this._elementInfo.get(nativeElement);\n        if (elementInfo) {\n            elementInfo.subject.complete();\n            this._setClasses(nativeElement);\n            this._elementInfo.delete(nativeElement);\n            this._removeGlobalListeners(elementInfo);\n        }\n    }\n    focusVia(element, origin, options) {\n        const nativeElement = coerceElement(element);\n        const focusedElement = this._getDocument().activeElement;\n        // If the element is focused already, calling `focus` again won't trigger the event listener\n        // which means that the focus classes won't be updated. If that's the case, update the classes\n        // directly without waiting for an event.\n        if (nativeElement === focusedElement) {\n            this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) => this._originChanged(currentElement, origin, info));\n        }\n        else {\n            this._setOrigin(origin);\n            // `focus` isn't available on the server\n            if (typeof nativeElement.focus === 'function') {\n                nativeElement.focus(options);\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n    }\n    /** Access injected document if available or fallback to global document reference */\n    _getDocument() {\n        return this._document || document;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        const doc = this._getDocument();\n        return doc.defaultView || window;\n    }\n    _getFocusOrigin(focusEventTarget) {\n        if (this._origin) {\n            // If the origin was realized via a touch interaction, we need to perform additional checks\n            // to determine whether the focus origin should be attributed to touch or program.\n            if (this._originFromTouchInteraction) {\n                return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n            }\n            else {\n                return this._origin;\n            }\n        }\n        // If the window has just regained focus, we can restore the most recent origin from before the\n        // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n        // focus. This typically means one of two things happened:\n        //\n        // 1) The element was programmatically focused, or\n        // 2) The element was focused via screen reader navigation (which generally doesn't fire\n        //    events).\n        //\n        // Because we can't distinguish between these two cases, we default to setting `program`.\n        if (this._windowFocused && this._lastFocusOrigin) {\n            return this._lastFocusOrigin;\n        }\n        // If the interaction is coming from an input label, we consider it a mouse interactions.\n        // This is a special case where focus moves on `click`, rather than `mousedown` which breaks\n        // our detection, because all our assumptions are for `mousedown`. We need to handle this\n        // special case, because it's very common for checkboxes and radio buttons.\n        if (focusEventTarget && this._isLastInteractionFromInputLabel(focusEventTarget)) {\n            return 'mouse';\n        }\n        return 'program';\n    }\n    /**\n     * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n     * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n     * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n     * event was directly caused by the touch interaction or (2) the focus event was caused by a\n     * subsequent programmatic focus call triggered by the touch interaction.\n     * @param focusEventTarget The target of the focus event under examination.\n     */\n    _shouldBeAttributedToTouch(focusEventTarget) {\n        // Please note that this check is not perfect. Consider the following edge case:\n        //\n        // <div #parent tabindex=\"0\">\n        //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n        // </div>\n        //\n        // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n        // #child, #parent is programmatically focused. This code will attribute the focus to touch\n        // instead of program. This is a relatively minor edge-case that can be worked around by using\n        // focusVia(parent, 'program') to focus #parent.\n        return (this._detectionMode === 1 /* FocusMonitorDetectionMode.EVENTUAL */ ||\n            !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget));\n    }\n    /**\n     * Sets the focus classes on the element based on the given focus origin.\n     * @param element The element to update the classes on.\n     * @param origin The focus origin.\n     */\n    _setClasses(element, origin) {\n        element.classList.toggle('cdk-focused', !!origin);\n        element.classList.toggle('cdk-touch-focused', origin === 'touch');\n        element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n        element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n        element.classList.toggle('cdk-program-focused', origin === 'program');\n    }\n    /**\n     * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n     * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n     * the origin being set.\n     * @param origin The origin to set.\n     * @param isFromInteraction Whether we are setting the origin from an interaction event.\n     */\n    _setOrigin(origin, isFromInteraction = false) {\n        this._ngZone.runOutsideAngular(() => {\n            this._origin = origin;\n            this._originFromTouchInteraction = origin === 'touch' && isFromInteraction;\n            // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n            // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n            // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n            // a touch event because when a touch event is fired, the associated focus event isn't yet in\n            // the event queue. Before doing so, clear any pending timeouts.\n            if (this._detectionMode === 0 /* FocusMonitorDetectionMode.IMMEDIATE */) {\n                clearTimeout(this._originTimeoutId);\n                const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n                this._originTimeoutId = setTimeout(() => (this._origin = null), ms);\n            }\n        });\n    }\n    /**\n     * Handles focus events on a registered element.\n     * @param event The focus event.\n     * @param element The monitored element.\n     */\n    _onFocus(event, element) {\n        // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n        // focus event affecting the monitored element. If we want to use the origin of the first event\n        // instead we should check for the cdk-focused class here and return if the element already has\n        // it. (This only matters for elements that have includesChildren = true).\n        // If we are not counting child-element-focus as focused, make sure that the event target is the\n        // monitored element itself.\n        const elementInfo = this._elementInfo.get(element);\n        const focusEventTarget = _getEventTarget(event);\n        if (!elementInfo || (!elementInfo.checkChildren && element !== focusEventTarget)) {\n            return;\n        }\n        this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n    }\n    /**\n     * Handles blur events on a registered element.\n     * @param event The blur event.\n     * @param element The monitored element.\n     */\n    _onBlur(event, element) {\n        // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n        // order to focus another child of the monitored element.\n        const elementInfo = this._elementInfo.get(element);\n        if (!elementInfo ||\n            (elementInfo.checkChildren &&\n                event.relatedTarget instanceof Node &&\n                element.contains(event.relatedTarget))) {\n            return;\n        }\n        this._setClasses(element);\n        this._emitOrigin(elementInfo, null);\n    }\n    _emitOrigin(info, origin) {\n        if (info.subject.observers.length) {\n            this._ngZone.run(() => info.subject.next(origin));\n        }\n    }\n    _registerGlobalListeners(elementInfo) {\n        if (!this._platform.isBrowser) {\n            return;\n        }\n        const rootNode = elementInfo.rootNode;\n        const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n        if (!rootNodeFocusListeners) {\n            this._ngZone.runOutsideAngular(() => {\n                rootNode.addEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                rootNode.addEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n            });\n        }\n        this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1);\n        // Register global listeners when first element is monitored.\n        if (++this._monitoredElementCount === 1) {\n            // Note: we listen to events in the capture phase so we\n            // can detect them even if the user stops propagation.\n            this._ngZone.runOutsideAngular(() => {\n                const window = this._getWindow();\n                window.addEventListener('focus', this._windowFocusListener);\n            });\n            // The InputModalityDetector is also just a collection of global listeners.\n            this._inputModalityDetector.modalityDetected\n                .pipe(takeUntil(this._stopInputModalityDetector))\n                .subscribe(modality => {\n                this._setOrigin(modality, true /* isFromInteraction */);\n            });\n        }\n    }\n    _removeGlobalListeners(elementInfo) {\n        const rootNode = elementInfo.rootNode;\n        if (this._rootNodeFocusListenerCount.has(rootNode)) {\n            const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode);\n            if (rootNodeFocusListeners > 1) {\n                this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n            }\n            else {\n                rootNode.removeEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                rootNode.removeEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                this._rootNodeFocusListenerCount.delete(rootNode);\n            }\n        }\n        // Unregister global listeners when last element is unmonitored.\n        if (!--this._monitoredElementCount) {\n            const window = this._getWindow();\n            window.removeEventListener('focus', this._windowFocusListener);\n            // Equivalently, stop our InputModalityDetector subscription.\n            this._stopInputModalityDetector.next();\n            // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n            clearTimeout(this._windowFocusTimeoutId);\n            clearTimeout(this._originTimeoutId);\n        }\n    }\n    /** Updates all the state on an element once its focus origin has changed. */\n    _originChanged(element, origin, elementInfo) {\n        this._setClasses(element, origin);\n        this._emitOrigin(elementInfo, origin);\n        this._lastFocusOrigin = origin;\n    }\n    /**\n     * Collects the `MonitoredElementInfo` of a particular element and\n     * all of its ancestors that have enabled `checkChildren`.\n     * @param element Element from which to start the search.\n     */\n    _getClosestElementsInfo(element) {\n        const results = [];\n        this._elementInfo.forEach((info, currentElement) => {\n            if (currentElement === element || (info.checkChildren && currentElement.contains(element))) {\n                results.push([currentElement, info]);\n            }\n        });\n        return results;\n    }\n    /**\n     * Returns whether an interaction is likely to have come from the user clicking the `label` of\n     * an `input` or `textarea` in order to focus it.\n     * @param focusEventTarget Target currently receiving focus.\n     */\n    _isLastInteractionFromInputLabel(focusEventTarget) {\n        const { _mostRecentTarget: mostRecentTarget, mostRecentModality } = this._inputModalityDetector;\n        // If the last interaction used the mouse on an element contained by one of the labels\n        // of an `input`/`textarea` that is currently focused, it is very likely that the\n        // user redirected focus using the label.\n        if (mostRecentModality !== 'mouse' ||\n            !mostRecentTarget ||\n            mostRecentTarget === focusEventTarget ||\n            (focusEventTarget.nodeName !== 'INPUT' && focusEventTarget.nodeName !== 'TEXTAREA') ||\n            focusEventTarget.disabled) {\n            return false;\n        }\n        const labels = focusEventTarget.labels;\n        if (labels) {\n            for (let i = 0; i < labels.length; i++) {\n                if (labels[i].contains(mostRecentTarget)) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n}\nFocusMonitor.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: FocusMonitor, deps: [{ token: i0.NgZone }, { token: i1.Platform }, { token: InputModalityDetector }, { token: DOCUMENT, optional: true }, { token: FOCUS_MONITOR_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nFocusMonitor.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: FocusMonitor, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: FocusMonitor, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i0.NgZone }, { type: i1.Platform }, { type: InputModalityDetector }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [FOCUS_MONITOR_DEFAULT_OPTIONS]\n                }] }]; } });\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\nclass CdkMonitorFocus {\n    constructor(_elementRef, _focusMonitor) {\n        this._elementRef = _elementRef;\n        this._focusMonitor = _focusMonitor;\n        this._focusOrigin = null;\n        this.cdkFocusChange = new EventEmitter();\n    }\n    get focusOrigin() {\n        return this._focusOrigin;\n    }\n    ngAfterViewInit() {\n        const element = this._elementRef.nativeElement;\n        this._monitorSubscription = this._focusMonitor\n            .monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus'))\n            .subscribe(origin => {\n            this._focusOrigin = origin;\n            this.cdkFocusChange.emit(origin);\n        });\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        if (this._monitorSubscription) {\n            this._monitorSubscription.unsubscribe();\n        }\n    }\n}\nCdkMonitorFocus.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkMonitorFocus, deps: [{ token: i0.ElementRef }, { token: FocusMonitor }], target: i0.ɵɵFactoryTarget.Directive });\nCdkMonitorFocus.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: CdkMonitorFocus, selector: \"[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]\", outputs: { cdkFocusChange: \"cdkFocusChange\" }, exportAs: [\"cdkMonitorFocus\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkMonitorFocus, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n                    exportAs: 'cdkMonitorFocus',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: FocusMonitor }]; }, propDecorators: { cdkFocusChange: [{\n                type: Output\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\nconst BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\nconst WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n/** CSS class applied to the document body when in high-contrast mode. */\nconst HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\nclass HighContrastModeDetector {\n    constructor(_platform, document) {\n        this._platform = _platform;\n        this._document = document;\n        this._breakpointSubscription = inject(BreakpointObserver)\n            .observe('(forced-colors: active)')\n            .subscribe(() => {\n            if (this._hasCheckedHighContrastMode) {\n                this._hasCheckedHighContrastMode = false;\n                this._applyBodyHighContrastModeCssClasses();\n            }\n        });\n    }\n    /** Gets the current high-contrast-mode for the page. */\n    getHighContrastMode() {\n        if (!this._platform.isBrowser) {\n            return 0 /* HighContrastMode.NONE */;\n        }\n        // Create a test element with an arbitrary background-color that is neither black nor\n        // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n        // appending the test element to the DOM does not affect layout by absolutely positioning it\n        const testElement = this._document.createElement('div');\n        testElement.style.backgroundColor = 'rgb(1,2,3)';\n        testElement.style.position = 'absolute';\n        this._document.body.appendChild(testElement);\n        // Get the computed style for the background color, collapsing spaces to normalize between\n        // browsers. Once we get this color, we no longer need the test element. Access the `window`\n        // via the document so we can fake it in tests. Note that we have extra null checks, because\n        // this logic will likely run during app bootstrap and throwing can break the entire app.\n        const documentWindow = this._document.defaultView || window;\n        const computedStyle = documentWindow && documentWindow.getComputedStyle\n            ? documentWindow.getComputedStyle(testElement)\n            : null;\n        const computedColor = ((computedStyle && computedStyle.backgroundColor) || '').replace(/ /g, '');\n        testElement.remove();\n        switch (computedColor) {\n            // Pre Windows 11 dark theme.\n            case 'rgb(0,0,0)':\n            // Windows 11 dark themes.\n            case 'rgb(45,50,54)':\n            case 'rgb(32,32,32)':\n                return 2 /* HighContrastMode.WHITE_ON_BLACK */;\n            // Pre Windows 11 light theme.\n            case 'rgb(255,255,255)':\n            // Windows 11 light theme.\n            case 'rgb(255,250,239)':\n                return 1 /* HighContrastMode.BLACK_ON_WHITE */;\n        }\n        return 0 /* HighContrastMode.NONE */;\n    }\n    ngOnDestroy() {\n        this._breakpointSubscription.unsubscribe();\n    }\n    /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n    _applyBodyHighContrastModeCssClasses() {\n        if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n            const bodyClasses = this._document.body.classList;\n            bodyClasses.remove(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n            this._hasCheckedHighContrastMode = true;\n            const mode = this.getHighContrastMode();\n            if (mode === 1 /* HighContrastMode.BLACK_ON_WHITE */) {\n                bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS);\n            }\n            else if (mode === 2 /* HighContrastMode.WHITE_ON_BLACK */) {\n                bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n            }\n        }\n    }\n}\nHighContrastModeDetector.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: HighContrastModeDetector, deps: [{ token: i1.Platform }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nHighContrastModeDetector.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: HighContrastModeDetector, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: HighContrastModeDetector, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.Platform }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass A11yModule {\n    constructor(highContrastModeDetector) {\n        highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n    }\n}\nA11yModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: A11yModule, deps: [{ token: HighContrastModeDetector }], target: i0.ɵɵFactoryTarget.NgModule });\nA11yModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: A11yModule, declarations: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus], imports: [ObserversModule], exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus] });\nA11yModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: A11yModule, imports: [ObserversModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: A11yModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [ObserversModule],\n                    declarations: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n                    exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n                }]\n        }], ctorParameters: function () { return [{ type: HighContrastModeDetector }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { A11yModule, ActiveDescendantKeyManager, AriaDescriber, CDK_DESCRIBEDBY_HOST_ATTRIBUTE, CDK_DESCRIBEDBY_ID_PREFIX, CdkAriaLive, CdkMonitorFocus, CdkTrapFocus, ConfigurableFocusTrap, ConfigurableFocusTrapFactory, EventListenerFocusTrapInertStrategy, FOCUS_MONITOR_DEFAULT_OPTIONS, FOCUS_TRAP_INERT_STRATEGY, FocusKeyManager, FocusMonitor, FocusTrap, FocusTrapFactory, HighContrastModeDetector, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, INPUT_MODALITY_DETECTOR_OPTIONS, InputModalityDetector, InteractivityChecker, IsFocusableConfig, LIVE_ANNOUNCER_DEFAULT_OPTIONS, LIVE_ANNOUNCER_ELEMENT_TOKEN, LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY, ListKeyManager, LiveAnnouncer, MESSAGES_CONTAINER_ID, isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader };\n"], "mappings": "AAAA,SAASA,QAAT,QAAyB,iBAAzB;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,MAAT,EAAiBC,MAAjB,EAAyBC,UAAzB,EAAqCC,MAArC,EAA6CC,SAA7C,EAAwDC,SAAxD,EAAmEC,KAAnE,EAA0EC,cAA1E,EAA0FC,QAA1F,EAAoGC,YAApG,EAAkHC,MAAlH,EAA0HC,QAA1H,QAA0I,eAA1I;AACA,OAAO,KAAKC,EAAZ,MAAoB,uBAApB;AACA,SAASC,iCAAT,EAA4CC,+BAA5C,EAA6EC,eAA7E,EAA8FC,cAA9F,QAAoH,uBAApH;AACA,SAASC,OAAT,EAAkBC,YAAlB,EAAgCC,eAAhC,EAAiDC,EAAjD,QAA2D,MAA3D;AACA,SAASC,cAAT,EAAyBC,CAAzB,EAA4BC,CAA5B,EAA+BC,IAA/B,EAAqCC,IAArC,EAA2CC,GAA3C,EAAgDC,IAAhD,EAAsDC,UAAtD,EAAkEC,WAAlE,EAA+EC,QAA/E,EAAyFC,UAAzF,EAAqGC,GAArG,EAA0GC,GAA1G,EAA+GC,OAA/G,EAAwHC,QAAxH,EAAkIC,IAAlI,EAAwIC,KAAxI,QAAqJ,uBAArJ;AACA,SAASC,GAAT,EAAcC,YAAd,EAA4BC,MAA5B,EAAoCC,GAApC,EAAyCC,IAAzC,EAA+CC,IAA/C,EAAqDC,oBAArD,EAA2EC,SAA3E,QAA4F,gBAA5F;AACA,SAASC,qBAAT,EAAgCC,aAAhC,QAAqD,uBAArD;AACA,OAAO,KAAKC,IAAZ,MAAsB,wBAAtB;AACA,SAASC,eAAT,QAAgC,wBAAhC;AACA,SAASC,kBAAT,QAAmC,qBAAnC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;AACA,MAAMC,YAAY,GAAG,GAArB;AACA;AACA;AACA;AACA;;AACA,SAASC,mBAAT,CAA6BC,EAA7B,EAAiCC,IAAjC,EAAuCC,EAAvC,EAA2C;EACvC,MAAMC,GAAG,GAAGC,mBAAmB,CAACJ,EAAD,EAAKC,IAAL,CAA/B;;EACA,IAAIE,GAAG,CAACE,IAAJ,CAASC,UAAU,IAAIA,UAAU,CAACC,IAAX,MAAqBL,EAAE,CAACK,IAAH,EAA5C,CAAJ,EAA4D;IACxD;EACH;;EACDJ,GAAG,CAACK,IAAJ,CAASN,EAAE,CAACK,IAAH,EAAT;EACAP,EAAE,CAACS,YAAH,CAAgBR,IAAhB,EAAsBE,GAAG,CAACO,IAAJ,CAASZ,YAAT,CAAtB;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASa,sBAAT,CAAgCX,EAAhC,EAAoCC,IAApC,EAA0CC,EAA1C,EAA8C;EAC1C,MAAMC,GAAG,GAAGC,mBAAmB,CAACJ,EAAD,EAAKC,IAAL,CAA/B;EACA,MAAMW,WAAW,GAAGT,GAAG,CAAChB,MAAJ,CAAW0B,GAAG,IAAIA,GAAG,IAAIX,EAAE,CAACK,IAAH,EAAzB,CAApB;;EACA,IAAIK,WAAW,CAACE,MAAhB,EAAwB;IACpBd,EAAE,CAACS,YAAH,CAAgBR,IAAhB,EAAsBW,WAAW,CAACF,IAAZ,CAAiBZ,YAAjB,CAAtB;EACH,CAFD,MAGK;IACDE,EAAE,CAACe,eAAH,CAAmBd,IAAnB;EACH;AACJ;AACD;AACA;AACA;AACA;;;AACA,SAASG,mBAAT,CAA6BJ,EAA7B,EAAiCC,IAAjC,EAAuC;EACnC;EACA,OAAO,CAACD,EAAE,CAACgB,YAAH,CAAgBf,IAAhB,KAAyB,EAA1B,EAA8BgB,KAA9B,CAAoC,MAApC,KAA+C,EAAtD;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,qBAAqB,GAAG,mCAA9B;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,yBAAyB,GAAG,yBAAlC;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,8BAA8B,GAAG,sBAAvC;AACA;;AACA,IAAIC,MAAM,GAAG,CAAb;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,aAAN,CAAoB;EAChBC,WAAW,CAACC,SAAD;EACX;AACJ;AACA;AACA;EACIC,SALW,EAKA;IACP,KAAKA,SAAL,GAAiBA,SAAjB;IACA;;IACA,KAAKC,gBAAL,GAAwB,IAAIC,GAAJ,EAAxB;IACA;;IACA,KAAKC,kBAAL,GAA0B,IAA1B;IACA;;IACA,KAAKC,GAAL,GAAY,GAAER,MAAM,EAAG,EAAvB;IACA,KAAKG,SAAL,GAAiBA,SAAjB;IACA,KAAKK,GAAL,GAAWlF,MAAM,CAACC,MAAD,CAAN,GAAiB,GAAjB,GAAuByE,MAAM,EAAxC;EACH;;EACDS,QAAQ,CAACC,WAAD,EAAcC,OAAd,EAAuBC,IAAvB,EAA6B;IACjC,IAAI,CAAC,KAAKC,eAAL,CAAqBH,WAArB,EAAkCC,OAAlC,CAAL,EAAiD;MAC7C;IACH;;IACD,MAAMG,GAAG,GAAGC,MAAM,CAACJ,OAAD,EAAUC,IAAV,CAAlB;;IACA,IAAI,OAAOD,OAAP,KAAmB,QAAvB,EAAiC;MAC7B;MACAK,YAAY,CAACL,OAAD,EAAU,KAAKH,GAAf,CAAZ;;MACA,KAAKH,gBAAL,CAAsBY,GAAtB,CAA0BH,GAA1B,EAA+B;QAAEI,cAAc,EAAEP,OAAlB;QAA2BQ,cAAc,EAAE;MAA3C,CAA/B;IACH,CAJD,MAKK,IAAI,CAAC,KAAKd,gBAAL,CAAsBe,GAAtB,CAA0BN,GAA1B,CAAL,EAAqC;MACtC,KAAKO,qBAAL,CAA2BV,OAA3B,EAAoCC,IAApC;IACH;;IACD,IAAI,CAAC,KAAKU,4BAAL,CAAkCZ,WAAlC,EAA+CI,GAA/C,CAAL,EAA0D;MACtD,KAAKS,oBAAL,CAA0Bb,WAA1B,EAAuCI,GAAvC;IACH;EACJ;;EACDU,iBAAiB,CAACd,WAAD,EAAcC,OAAd,EAAuBC,IAAvB,EAA6B;IAC1C,IAAI,CAACD,OAAD,IAAY,CAAC,KAAKc,cAAL,CAAoBf,WAApB,CAAjB,EAAmD;MAC/C;IACH;;IACD,MAAMI,GAAG,GAAGC,MAAM,CAACJ,OAAD,EAAUC,IAAV,CAAlB;;IACA,IAAI,KAAKU,4BAAL,CAAkCZ,WAAlC,EAA+CI,GAA/C,CAAJ,EAAyD;MACrD,KAAKY,uBAAL,CAA6BhB,WAA7B,EAA0CI,GAA1C;IACH,CAPyC,CAQ1C;IACA;;;IACA,IAAI,OAAOH,OAAP,KAAmB,QAAvB,EAAiC;MAC7B,MAAMgB,iBAAiB,GAAG,KAAKtB,gBAAL,CAAsBuB,GAAtB,CAA0Bd,GAA1B,CAA1B;;MACA,IAAIa,iBAAiB,IAAIA,iBAAiB,CAACR,cAAlB,KAAqC,CAA9D,EAAiE;QAC7D,KAAKU,qBAAL,CAA2Bf,GAA3B;MACH;IACJ;;IACD,IAAI,KAAKP,kBAAL,EAAyBuB,UAAzB,CAAoCrC,MAApC,KAA+C,CAAnD,EAAsD;MAClD,KAAKc,kBAAL,CAAwBwB,MAAxB;;MACA,KAAKxB,kBAAL,GAA0B,IAA1B;IACH;EACJ;EACD;;;EACAyB,WAAW,GAAG;IACV,MAAMC,iBAAiB,GAAG,KAAK9B,SAAL,CAAe+B,gBAAf,CAAiC,IAAGnC,8BAA+B,KAAI,KAAKS,GAAI,IAAhF,CAA1B;;IACA,KAAK,IAAI2B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,iBAAiB,CAACxC,MAAtC,EAA8C0C,CAAC,EAA/C,EAAmD;MAC/C,KAAKC,iCAAL,CAAuCH,iBAAiB,CAACE,CAAD,CAAxD;;MACAF,iBAAiB,CAACE,CAAD,CAAjB,CAAqBzC,eAArB,CAAqCK,8BAArC;IACH;;IACD,KAAKQ,kBAAL,EAAyBwB,MAAzB;IACA,KAAKxB,kBAAL,GAA0B,IAA1B;;IACA,KAAKF,gBAAL,CAAsBgC,KAAtB;EACH;EACD;AACJ;AACA;AACA;;;EACIhB,qBAAqB,CAACV,OAAD,EAAUC,IAAV,EAAgB;IACjC,MAAMM,cAAc,GAAG,KAAKf,SAAL,CAAemC,aAAf,CAA6B,KAA7B,CAAvB;;IACAtB,YAAY,CAACE,cAAD,EAAiB,KAAKV,GAAtB,CAAZ;IACAU,cAAc,CAACqB,WAAf,GAA6B5B,OAA7B;;IACA,IAAIC,IAAJ,EAAU;MACNM,cAAc,CAAC9B,YAAf,CAA4B,MAA5B,EAAoCwB,IAApC;IACH;;IACD,KAAK4B,wBAAL;;IACA,KAAKjC,kBAAL,CAAwBkC,WAAxB,CAAoCvB,cAApC;;IACA,KAAKb,gBAAL,CAAsBY,GAAtB,CAA0BF,MAAM,CAACJ,OAAD,EAAUC,IAAV,CAAhC,EAAiD;MAAEM,cAAF;MAAkBC,cAAc,EAAE;IAAlC,CAAjD;EACH;EACD;;;EACAU,qBAAqB,CAACf,GAAD,EAAM;IACvB,KAAKT,gBAAL,CAAsBuB,GAAtB,CAA0Bd,GAA1B,GAAgCI,cAAhC,EAAgDa,MAAhD;;IACA,KAAK1B,gBAAL,CAAsBqC,MAAtB,CAA6B5B,GAA7B;EACH;EACD;;;EACA0B,wBAAwB,GAAG;IACvB,IAAI,KAAKjC,kBAAT,EAA6B;MACzB;IACH;;IACD,MAAMoC,kBAAkB,GAAG,mCAA3B;;IACA,MAAMC,gBAAgB,GAAG,KAAKzC,SAAL,CAAe+B,gBAAf,CAAiC,IAAGS,kBAAmB,qBAAvD,CAAzB;;IACA,KAAK,IAAIR,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGS,gBAAgB,CAACnD,MAArC,EAA6C0C,CAAC,EAA9C,EAAkD;MAC9C;MACA;MACA;MACA;MACAS,gBAAgB,CAACT,CAAD,CAAhB,CAAoBJ,MAApB;IACH;;IACD,MAAMc,iBAAiB,GAAG,KAAK1C,SAAL,CAAemC,aAAf,CAA6B,KAA7B,CAA1B,CAbuB,CAcvB;IACA;IACA;IACA;;;IACAO,iBAAiB,CAACC,KAAlB,CAAwBC,UAAxB,GAAqC,QAArC,CAlBuB,CAmBvB;IACA;;IACAF,iBAAiB,CAACG,SAAlB,CAA4BC,GAA5B,CAAgCN,kBAAhC;IACAE,iBAAiB,CAACG,SAAlB,CAA4BC,GAA5B,CAAgC,qBAAhC,EAtBuB,CAuBvB;;IACA,IAAI,KAAK7C,SAAL,IAAkB,CAAC,KAAKA,SAAL,CAAe8C,SAAtC,EAAiD;MAC7CL,iBAAiB,CAACzD,YAAlB,CAA+B,UAA/B,EAA2C,QAA3C;IACH;;IACD,KAAKe,SAAL,CAAegD,IAAf,CAAoBV,WAApB,CAAgCI,iBAAhC;;IACA,KAAKtC,kBAAL,GAA0BsC,iBAA1B;EACH;EACD;;;EACAT,iCAAiC,CAACgB,OAAD,EAAU;IACvC;IACA,MAAMC,oBAAoB,GAAGtE,mBAAmB,CAACqE,OAAD,EAAU,kBAAV,CAAnB,CAAiDtF,MAAjD,CAAwDe,EAAE,IAAIA,EAAE,CAACyE,OAAH,CAAWxD,yBAAX,KAAyC,CAAvG,CAA7B;IACAsD,OAAO,CAAChE,YAAR,CAAqB,kBAArB,EAAyCiE,oBAAoB,CAAChE,IAArB,CAA0B,GAA1B,CAAzC;EACH;EACD;AACJ;AACA;AACA;;;EACIkC,oBAAoB,CAAC6B,OAAD,EAAUtC,GAAV,EAAe;IAC/B,MAAMa,iBAAiB,GAAG,KAAKtB,gBAAL,CAAsBuB,GAAtB,CAA0Bd,GAA1B,CAA1B,CAD+B,CAE/B;IACA;;;IACApC,mBAAmB,CAAC0E,OAAD,EAAU,kBAAV,EAA8BzB,iBAAiB,CAACT,cAAlB,CAAiCrC,EAA/D,CAAnB;IACAuE,OAAO,CAAChE,YAAR,CAAqBW,8BAArB,EAAqD,KAAKS,GAA1D;IACAmB,iBAAiB,CAACR,cAAlB;EACH;EACD;AACJ;AACA;AACA;;;EACIO,uBAAuB,CAAC0B,OAAD,EAAUtC,GAAV,EAAe;IAClC,MAAMa,iBAAiB,GAAG,KAAKtB,gBAAL,CAAsBuB,GAAtB,CAA0Bd,GAA1B,CAA1B;;IACAa,iBAAiB,CAACR,cAAlB;IACA7B,sBAAsB,CAAC8D,OAAD,EAAU,kBAAV,EAA8BzB,iBAAiB,CAACT,cAAlB,CAAiCrC,EAA/D,CAAtB;IACAuE,OAAO,CAAC1D,eAAR,CAAwBK,8BAAxB;EACH;EACD;;;EACAuB,4BAA4B,CAAC8B,OAAD,EAAUtC,GAAV,EAAe;IACvC,MAAMyC,YAAY,GAAGxE,mBAAmB,CAACqE,OAAD,EAAU,kBAAV,CAAxC;;IACA,MAAMzB,iBAAiB,GAAG,KAAKtB,gBAAL,CAAsBuB,GAAtB,CAA0Bd,GAA1B,CAA1B;;IACA,MAAM0C,SAAS,GAAG7B,iBAAiB,IAAIA,iBAAiB,CAACT,cAAlB,CAAiCrC,EAAxE;IACA,OAAO,CAAC,CAAC2E,SAAF,IAAeD,YAAY,CAACD,OAAb,CAAqBE,SAArB,KAAmC,CAAC,CAA1D;EACH;EACD;;;EACA3C,eAAe,CAACuC,OAAD,EAAUzC,OAAV,EAAmB;IAC9B,IAAI,CAAC,KAAKc,cAAL,CAAoB2B,OAApB,CAAL,EAAmC;MAC/B,OAAO,KAAP;IACH;;IACD,IAAIzC,OAAO,IAAI,OAAOA,OAAP,KAAmB,QAAlC,EAA4C;MACxC;MACA;MACA;MACA,OAAO,IAAP;IACH;;IACD,MAAM8C,cAAc,GAAG9C,OAAO,IAAI,IAAX,GAAkB,EAAlB,GAAwB,GAAEA,OAAQ,EAAX,CAAazB,IAAb,EAA9C;IACA,MAAMwE,SAAS,GAAGN,OAAO,CAACzD,YAAR,CAAqB,YAArB,CAAlB,CAX8B,CAY9B;IACA;;IACA,OAAO8D,cAAc,GAAG,CAACC,SAAD,IAAcA,SAAS,CAACxE,IAAV,OAAqBuE,cAAtC,GAAuD,KAA5E;EACH;EACD;;;EACAhC,cAAc,CAAC2B,OAAD,EAAU;IACpB,OAAOA,OAAO,CAACO,QAAR,KAAqB,KAAKxD,SAAL,CAAeyD,YAA3C;EACH;;AA5Ke;;AA8KpB3D,aAAa,CAAC4D,IAAd;EAAA,iBAA0G5D,aAA1G,EAAgG5E,EAAhG,UAAyID,QAAzI,GAAgGC,EAAhG,UAA8Ja,EAAE,CAAC4H,QAAjK;AAAA;;AACA7D,aAAa,CAAC8D,KAAd,kBADgG1I,EAChG;EAAA,OAA8G4E,aAA9G;EAAA,SAA8GA,aAA9G;EAAA,YAAyI;AAAzI;;AACA;EAAA,mDAFgG5E,EAEhG,mBAA2F4E,aAA3F,EAAsH,CAAC;IAC3G+D,IAAI,EAAExI,UADqG;IAE3GyI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAFqG,CAAD,CAAtH,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9DJ,IAAI,EAAEvI,MADwD;QAE9DwI,IAAI,EAAE,CAAC7I,QAAD;MAFwD,CAAD;IAA/B,CAAD,EAG3B;MAAE4I,IAAI,EAAE9H,EAAE,CAAC4H;IAAX,CAH2B,CAAP;EAGK,CAN/C;AAAA;AAOA;;;AACA,SAAS/C,MAAT,CAAgBJ,OAAhB,EAAyBC,IAAzB,EAA+B;EAC3B,OAAO,OAAOD,OAAP,KAAmB,QAAnB,GAA+B,GAAEC,IAAI,IAAI,EAAG,IAAGD,OAAQ,EAAvD,GAA2DA,OAAlE;AACH;AACD;;;AACA,SAASK,YAAT,CAAsBoC,OAAtB,EAA+BiB,SAA/B,EAA0C;EACtC,IAAI,CAACjB,OAAO,CAACvE,EAAb,EAAiB;IACbuE,OAAO,CAACvE,EAAR,GAAc,GAAEiB,yBAA0B,IAAGuE,SAAU,IAAGrE,MAAM,EAAG,EAAnE;EACH;AACJ;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMsE,cAAN,CAAqB;EACjBpE,WAAW,CAACqE,MAAD,EAAS;IAChB,KAAKA,MAAL,GAAcA,MAAd;IACA,KAAKC,gBAAL,GAAwB,CAAC,CAAzB;IACA,KAAKC,WAAL,GAAmB,IAAnB;IACA,KAAKC,KAAL,GAAa,KAAb;IACA,KAAKC,gBAAL,GAAwB,IAAIpI,OAAJ,EAAxB;IACA,KAAKqI,sBAAL,GAA8BpI,YAAY,CAACqI,KAA3C;IACA,KAAKC,SAAL,GAAiB,IAAjB;IACA,KAAKC,oBAAL,GAA4B,EAA5B;IACA,KAAKC,WAAL,GAAmB,KAAnB;IACA;AACR;AACA;AACA;;IACQ,KAAKC,gBAAL,GAAyBC,IAAD,IAAUA,IAAI,CAACC,QAAvC,CAdgB,CAehB;;;IACA,KAAKC,eAAL,GAAuB,EAAvB;IACA;AACR;AACA;AACA;;IACQ,KAAKC,MAAL,GAAc,IAAI9I,OAAJ,EAAd;IACA;;IACA,KAAK+I,MAAL,GAAc,IAAI/I,OAAJ,EAAd,CAvBgB,CAwBhB;IACA;IACA;;IACA,IAAIgI,MAAM,YAAY7I,SAAtB,EAAiC;MAC7B6I,MAAM,CAACgB,OAAP,CAAeC,SAAf,CAA0BC,QAAD,IAAc;QACnC,IAAI,KAAKhB,WAAT,EAAsB;UAClB,MAAMiB,SAAS,GAAGD,QAAQ,CAACE,OAAT,EAAlB;UACA,MAAMC,QAAQ,GAAGF,SAAS,CAACpC,OAAV,CAAkB,KAAKmB,WAAvB,CAAjB;;UACA,IAAImB,QAAQ,GAAG,CAAC,CAAZ,IAAiBA,QAAQ,KAAK,KAAKpB,gBAAvC,EAAyD;YACrD,KAAKA,gBAAL,GAAwBoB,QAAxB;UACH;QACJ;MACJ,CARD;IASH;EACJ;EACD;AACJ;AACA;AACA;AACA;;;EACIC,aAAa,CAACC,SAAD,EAAY;IACrB,KAAKb,gBAAL,GAAwBa,SAAxB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIC,QAAQ,CAACC,UAAU,GAAG,IAAd,EAAoB;IACxB,KAAKtB,KAAL,GAAasB,UAAb;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIC,uBAAuB,CAACC,OAAO,GAAG,IAAX,EAAiB;IACpC,KAAKpB,SAAL,GAAiBoB,OAAjB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIC,yBAAyB,CAACC,SAAD,EAAY;IACjC,KAAKC,WAAL,GAAmBD,SAAnB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIE,uBAAuB,CAACC,IAAD,EAAO;IAC1B,KAAKxB,oBAAL,GAA4BwB,IAA5B;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIC,aAAa,CAACC,gBAAgB,GAAG,GAApB,EAAyB;IAClC,IAAI,CAAC,OAAOC,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KACA,KAAKnC,MAAL,CAAY9E,MADZ,IAEA,KAAK8E,MAAL,CAAYvF,IAAZ,CAAiBkG,IAAI,IAAI,OAAOA,IAAI,CAACyB,QAAZ,KAAyB,UAAlD,CAFJ,EAEmE;MAC/D,MAAMC,KAAK,CAAC,8EAAD,CAAX;IACH;;IACD,KAAKhC,sBAAL,CAA4BiC,WAA5B,GANkC,CAOlC;IACA;IACA;;;IACA,KAAKjC,sBAAL,GAA8B,KAAKD,gBAAL,CACzBmC,IADyB,CACpBlJ,GAAG,CAACmJ,MAAM,IAAI,KAAK3B,eAAL,CAAqBjG,IAArB,CAA0B4H,MAA1B,CAAX,CADiB,EAC8BlJ,YAAY,CAAC4I,gBAAD,CAD1C,EAC8D3I,MAAM,CAAC,MAAM,KAAKsH,eAAL,CAAqB3F,MAArB,GAA8B,CAArC,CADpE,EAC6G1B,GAAG,CAAC,MAAM,KAAKqH,eAAL,CAAqB/F,IAArB,CAA0B,EAA1B,CAAP,CADhH,EAEzBmG,SAFyB,CAEfwB,WAAW,IAAI;MAC1B,MAAMC,KAAK,GAAG,KAAKC,cAAL,EAAd,CAD0B,CAE1B;MACA;;;MACA,KAAK,IAAI/E,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8E,KAAK,CAACxH,MAAN,GAAe,CAAnC,EAAsC0C,CAAC,EAAvC,EAA2C;QACvC,MAAMgF,KAAK,GAAG,CAAC,KAAK3C,gBAAL,GAAwBrC,CAAzB,IAA8B8E,KAAK,CAACxH,MAAlD;QACA,MAAMyF,IAAI,GAAG+B,KAAK,CAACE,KAAD,CAAlB;;QACA,IAAI,CAAC,KAAKlC,gBAAL,CAAsBC,IAAtB,CAAD,IACAA,IAAI,CAACyB,QAAL,GAAgBS,WAAhB,GAA8BlI,IAA9B,GAAqCoE,OAArC,CAA6C0D,WAA7C,MAA8D,CADlE,EACqE;UACjE,KAAKK,aAAL,CAAmBF,KAAnB;UACA;QACH;MACJ;;MACD,KAAK/B,eAAL,GAAuB,EAAvB;IACH,CAhB6B,CAA9B;IAiBA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIkC,cAAc,CAACpB,OAAO,GAAG,IAAX,EAAiB;IAC3B,KAAKlB,WAAL,GAAmBkB,OAAnB;IACA,OAAO,IAAP;EACH;;EACDmB,aAAa,CAACnC,IAAD,EAAO;IAChB,MAAMqC,kBAAkB,GAAG,KAAK9C,WAAhC;IACA,KAAK+C,gBAAL,CAAsBtC,IAAtB;;IACA,IAAI,KAAKT,WAAL,KAAqB8C,kBAAzB,EAA6C;MACzC,KAAKjC,MAAL,CAAYmC,IAAZ,CAAiB,KAAKjD,gBAAtB;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACIkD,SAAS,CAACC,KAAD,EAAQ;IACb,MAAMC,OAAO,GAAGD,KAAK,CAACC,OAAtB;IACA,MAAMC,SAAS,GAAG,CAAC,QAAD,EAAW,SAAX,EAAsB,SAAtB,EAAiC,UAAjC,CAAlB;IACA,MAAMC,iBAAiB,GAAGD,SAAS,CAACE,KAAV,CAAgBC,QAAQ,IAAI;MAClD,OAAO,CAACL,KAAK,CAACK,QAAD,CAAN,IAAoB,KAAKjD,oBAAL,CAA0BzB,OAA1B,CAAkC0E,QAAlC,IAA8C,CAAC,CAA1E;IACH,CAFyB,CAA1B;;IAGA,QAAQJ,OAAR;MACI,KAAKtK,GAAL;QACI,KAAK+H,MAAL,CAAYoC,IAAZ;QACA;;MACJ,KAAKpK,UAAL;QACI,IAAI,KAAKyH,SAAL,IAAkBgD,iBAAtB,EAAyC;UACrC,KAAKG,iBAAL;UACA;QACH,CAHD,MAIK;UACD;QACH;;MACL,KAAK7K,QAAL;QACI,IAAI,KAAK0H,SAAL,IAAkBgD,iBAAtB,EAAyC;UACrC,KAAKI,qBAAL;UACA;QACH,CAHD,MAIK;UACD;QACH;;MACL,KAAK/K,WAAL;QACI,IAAI,KAAKkJ,WAAL,IAAoByB,iBAAxB,EAA2C;UACvC,KAAKzB,WAAL,KAAqB,KAArB,GAA6B,KAAK6B,qBAAL,EAA7B,GAA4D,KAAKD,iBAAL,EAA5D;UACA;QACH,CAHD,MAIK;UACD;QACH;;MACL,KAAK/K,UAAL;QACI,IAAI,KAAKmJ,WAAL,IAAoByB,iBAAxB,EAA2C;UACvC,KAAKzB,WAAL,KAAqB,KAArB,GAA6B,KAAK4B,iBAAL,EAA7B,GAAwD,KAAKC,qBAAL,EAAxD;UACA;QACH,CAHD,MAIK;UACD;QACH;;MACL,KAAKjL,IAAL;QACI,IAAI,KAAK+H,WAAL,IAAoB8C,iBAAxB,EAA2C;UACvC,KAAKK,kBAAL;UACA;QACH,CAHD,MAIK;UACD;QACH;;MACL,KAAKnL,GAAL;QACI,IAAI,KAAKgI,WAAL,IAAoB8C,iBAAxB,EAA2C;UACvC,KAAKM,iBAAL;UACA;QACH,CAHD,MAIK;UACD;QACH;;MACL;QACI,IAAIN,iBAAiB,IAAInL,cAAc,CAACgL,KAAD,EAAQ,UAAR,CAAvC,EAA4D;UACxD;UACA;UACA,IAAIA,KAAK,CAAC7G,GAAN,IAAa6G,KAAK,CAAC7G,GAAN,CAAUrB,MAAV,KAAqB,CAAtC,EAAyC;YACrC,KAAKkF,gBAAL,CAAsB8C,IAAtB,CAA2BE,KAAK,CAAC7G,GAAN,CAAUuH,iBAAV,EAA3B;UACH,CAFD,MAGK,IAAKT,OAAO,IAAIhL,CAAX,IAAgBgL,OAAO,IAAI/K,CAA5B,IAAmC+K,OAAO,IAAI9K,IAAX,IAAmB8K,OAAO,IAAI7K,IAArE,EAA4E;YAC7E,KAAK4H,gBAAL,CAAsB8C,IAAtB,CAA2Ba,MAAM,CAACC,YAAP,CAAoBX,OAApB,CAA3B;UACH;QACJ,CAVL,CAWI;QACA;;;QACA;IAjER;;IAmEA,KAAKxC,eAAL,GAAuB,EAAvB;IACAuC,KAAK,CAACa,cAAN;EACH;EACD;;;EACmB,IAAfC,eAAe,GAAG;IAClB,OAAO,KAAKjE,gBAAZ;EACH;EACD;;;EACc,IAAVkE,UAAU,GAAG;IACb,OAAO,KAAKjE,WAAZ;EACH;EACD;;;EACAkE,QAAQ,GAAG;IACP,OAAO,KAAKvD,eAAL,CAAqB3F,MAArB,GAA8B,CAArC;EACH;EACD;;;EACA0I,kBAAkB,GAAG;IACjB,KAAKS,qBAAL,CAA2B,CAA3B,EAA8B,CAA9B;EACH;EACD;;;EACAR,iBAAiB,GAAG;IAChB,KAAKQ,qBAAL,CAA2B,KAAKrE,MAAL,CAAY9E,MAAZ,GAAqB,CAAhD,EAAmD,CAAC,CAApD;EACH;EACD;;;EACAwI,iBAAiB,GAAG;IAChB,KAAKzD,gBAAL,GAAwB,CAAxB,GAA4B,KAAK2D,kBAAL,EAA5B,GAAwD,KAAKU,qBAAL,CAA2B,CAA3B,CAAxD;EACH;EACD;;;EACAX,qBAAqB,GAAG;IACpB,KAAK1D,gBAAL,GAAwB,CAAxB,IAA6B,KAAKE,KAAlC,GACM,KAAK0D,iBAAL,EADN,GAEM,KAAKS,qBAAL,CAA2B,CAAC,CAA5B,CAFN;EAGH;;EACDrB,gBAAgB,CAACtC,IAAD,EAAO;IACnB,MAAMQ,SAAS,GAAG,KAAKwB,cAAL,EAAlB;;IACA,MAAMC,KAAK,GAAG,OAAOjC,IAAP,KAAgB,QAAhB,GAA2BA,IAA3B,GAAkCQ,SAAS,CAACpC,OAAV,CAAkB4B,IAAlB,CAAhD;IACA,MAAMwD,UAAU,GAAGhD,SAAS,CAACyB,KAAD,CAA5B,CAHmB,CAInB;;IACA,KAAK1C,WAAL,GAAmBiE,UAAU,IAAI,IAAd,GAAqB,IAArB,GAA4BA,UAA/C;IACA,KAAKlE,gBAAL,GAAwB2C,KAAxB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACI0B,qBAAqB,CAACC,KAAD,EAAQ;IACzB,KAAKpE,KAAL,GAAa,KAAKqE,oBAAL,CAA0BD,KAA1B,CAAb,GAAgD,KAAKE,uBAAL,CAA6BF,KAA7B,CAAhD;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIC,oBAAoB,CAACD,KAAD,EAAQ;IACxB,MAAM7B,KAAK,GAAG,KAAKC,cAAL,EAAd;;IACA,KAAK,IAAI/E,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI8E,KAAK,CAACxH,MAA3B,EAAmC0C,CAAC,EAApC,EAAwC;MACpC,MAAMgF,KAAK,GAAG,CAAC,KAAK3C,gBAAL,GAAwBsE,KAAK,GAAG3G,CAAhC,GAAoC8E,KAAK,CAACxH,MAA3C,IAAqDwH,KAAK,CAACxH,MAAzE;MACA,MAAMyF,IAAI,GAAG+B,KAAK,CAACE,KAAD,CAAlB;;MACA,IAAI,CAAC,KAAKlC,gBAAL,CAAsBC,IAAtB,CAAL,EAAkC;QAC9B,KAAKmC,aAAL,CAAmBF,KAAnB;QACA;MACH;IACJ;EACJ;EACD;AACJ;AACA;AACA;AACA;;;EACI6B,uBAAuB,CAACF,KAAD,EAAQ;IAC3B,KAAKF,qBAAL,CAA2B,KAAKpE,gBAAL,GAAwBsE,KAAnD,EAA0DA,KAA1D;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIF,qBAAqB,CAACzB,KAAD,EAAQ8B,aAAR,EAAuB;IACxC,MAAMhC,KAAK,GAAG,KAAKC,cAAL,EAAd;;IACA,IAAI,CAACD,KAAK,CAACE,KAAD,CAAV,EAAmB;MACf;IACH;;IACD,OAAO,KAAKlC,gBAAL,CAAsBgC,KAAK,CAACE,KAAD,CAA3B,CAAP,EAA4C;MACxCA,KAAK,IAAI8B,aAAT;;MACA,IAAI,CAAChC,KAAK,CAACE,KAAD,CAAV,EAAmB;QACf;MACH;IACJ;;IACD,KAAKE,aAAL,CAAmBF,KAAnB;EACH;EACD;;;EACAD,cAAc,GAAG;IACb,OAAO,KAAK3C,MAAL,YAAuB7I,SAAvB,GAAmC,KAAK6I,MAAL,CAAYoB,OAAZ,EAAnC,GAA2D,KAAKpB,MAAvE;EACH;;AA/SgB;AAkTrB;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM2E,0BAAN,SAAyC5E,cAAzC,CAAwD;EACpD+C,aAAa,CAACF,KAAD,EAAQ;IACjB,IAAI,KAAKuB,UAAT,EAAqB;MACjB,KAAKA,UAAL,CAAgBS,iBAAhB;IACH;;IACD,MAAM9B,aAAN,CAAoBF,KAApB;;IACA,IAAI,KAAKuB,UAAT,EAAqB;MACjB,KAAKA,UAAL,CAAgBU,eAAhB;IACH;EACJ;;AATmD;AAYxD;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,eAAN,SAA8B/E,cAA9B,CAA6C;EACzCpE,WAAW,GAAG;IACV,MAAM,GAAGoJ,SAAT;IACA,KAAKC,OAAL,GAAe,SAAf;EACH;EACD;AACJ;AACA;AACA;;;EACIC,cAAc,CAACC,MAAD,EAAS;IACnB,KAAKF,OAAL,GAAeE,MAAf;IACA,OAAO,IAAP;EACH;;EACDpC,aAAa,CAACnC,IAAD,EAAO;IAChB,MAAMmC,aAAN,CAAoBnC,IAApB;;IACA,IAAI,KAAKwD,UAAT,EAAqB;MACjB,KAAKA,UAAL,CAAgBgB,KAAhB,CAAsB,KAAKH,OAA3B;IACH;EACJ;;AAlBwC;AAqB7C;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMI,iBAAN,CAAwB;EACpBzJ,WAAW,GAAG;IACV;AACR;AACA;IACQ,KAAK0J,gBAAL,GAAwB,KAAxB;EACH;;AANmB,C,CAQxB;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMC,oBAAN,CAA2B;EACvB3J,WAAW,CAACE,SAAD,EAAY;IACnB,KAAKA,SAAL,GAAiBA,SAAjB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACI0J,UAAU,CAAC1G,OAAD,EAAU;IAChB;IACA;IACA,OAAOA,OAAO,CAAC2G,YAAR,CAAqB,UAArB,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIC,SAAS,CAAC5G,OAAD,EAAU;IACf,OAAO6G,WAAW,CAAC7G,OAAD,CAAX,IAAwB8G,gBAAgB,CAAC9G,OAAD,CAAhB,CAA0BL,UAA1B,KAAyC,SAAxE;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACIoH,UAAU,CAAC/G,OAAD,EAAU;IAChB;IACA,IAAI,CAAC,KAAKhD,SAAL,CAAe8C,SAApB,EAA+B;MAC3B,OAAO,KAAP;IACH;;IACD,MAAMkH,YAAY,GAAGC,eAAe,CAACC,SAAS,CAAClH,OAAD,CAAV,CAApC;;IACA,IAAIgH,YAAJ,EAAkB;MACd;MACA,IAAIG,gBAAgB,CAACH,YAAD,CAAhB,KAAmC,CAAC,CAAxC,EAA2C;QACvC,OAAO,KAAP;MACH,CAJa,CAKd;;;MACA,IAAI,CAAC,KAAKJ,SAAL,CAAeI,YAAf,CAAL,EAAmC;QAC/B,OAAO,KAAP;MACH;IACJ;;IACD,IAAII,QAAQ,GAAGpH,OAAO,CAACoH,QAAR,CAAiBC,WAAjB,EAAf;IACA,IAAIC,aAAa,GAAGH,gBAAgB,CAACnH,OAAD,CAApC;;IACA,IAAIA,OAAO,CAAC2G,YAAR,CAAqB,iBAArB,CAAJ,EAA6C;MACzC,OAAOW,aAAa,KAAK,CAAC,CAA1B;IACH;;IACD,IAAIF,QAAQ,KAAK,QAAb,IAAyBA,QAAQ,KAAK,QAA1C,EAAoD;MAChD;MACA;MACA;MACA,OAAO,KAAP;IACH,CA1Be,CA2BhB;;;IACA,IAAI,KAAKpK,SAAL,CAAeuK,MAAf,IAAyB,KAAKvK,SAAL,CAAewK,GAAxC,IAA+C,CAACC,wBAAwB,CAACzH,OAAD,CAA5E,EAAuF;MACnF,OAAO,KAAP;IACH;;IACD,IAAIoH,QAAQ,KAAK,OAAjB,EAA0B;MACtB;MACA;MACA,IAAI,CAACpH,OAAO,CAAC2G,YAAR,CAAqB,UAArB,CAAL,EAAuC;QACnC,OAAO,KAAP;MACH,CALqB,CAMtB;MACA;;;MACA,OAAOW,aAAa,KAAK,CAAC,CAA1B;IACH;;IACD,IAAIF,QAAQ,KAAK,OAAjB,EAA0B;MACtB;MACA;MACA;MACA;MACA,IAAIE,aAAa,KAAK,CAAC,CAAvB,EAA0B;QACtB,OAAO,KAAP;MACH,CAPqB,CAQtB;MACA;;;MACA,IAAIA,aAAa,KAAK,IAAtB,EAA4B;QACxB,OAAO,IAAP;MACH,CAZqB,CAatB;MACA;MACA;;;MACA,OAAO,KAAKtK,SAAL,CAAe0K,OAAf,IAA0B1H,OAAO,CAAC2G,YAAR,CAAqB,UAArB,CAAjC;IACH;;IACD,OAAO3G,OAAO,CAAC2H,QAAR,IAAoB,CAA3B;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACIC,WAAW,CAAC5H,OAAD,EAAU6H,MAAV,EAAkB;IACzB;IACA;IACA,OAAQC,sBAAsB,CAAC9H,OAAD,CAAtB,IACJ,CAAC,KAAK0G,UAAL,CAAgB1G,OAAhB,CADG,KAEH6H,MAAM,EAAErB,gBAAR,IAA4B,KAAKI,SAAL,CAAe5G,OAAf,CAFzB,CAAR;EAGH;;AA3GsB;;AA6G3ByG,oBAAoB,CAAChG,IAArB;EAAA,iBAAiHgG,oBAAjH,EAtgBgGxO,EAsgBhG,UAAuJa,EAAE,CAAC4H,QAA1J;AAAA;;AACA+F,oBAAoB,CAAC9F,KAArB,kBAvgBgG1I,EAugBhG;EAAA,OAAqHwO,oBAArH;EAAA,SAAqHA,oBAArH;EAAA,YAAuJ;AAAvJ;;AACA;EAAA,mDAxgBgGxO,EAwgBhG,mBAA2FwO,oBAA3F,EAA6H,CAAC;IAClH7F,IAAI,EAAExI,UAD4G;IAElHyI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAF4G,CAAD,CAA7H,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAE9H,EAAE,CAAC4H;IAAX,CAAD,CAAP;EAAiC,CAH3E;AAAA;AAIA;AACA;AACA;AACA;AACA;;;AACA,SAASuG,eAAT,CAAyBc,MAAzB,EAAiC;EAC7B,IAAI;IACA,OAAOA,MAAM,CAACf,YAAd;EACH,CAFD,CAGA,MAAM;IACF,OAAO,IAAP;EACH;AACJ;AACD;;;AACA,SAASH,WAAT,CAAqB7G,OAArB,EAA8B;EAC1B;EACA;EACA,OAAO,CAAC,EAAEA,OAAO,CAACgI,WAAR,IACNhI,OAAO,CAACiI,YADF,IAEL,OAAOjI,OAAO,CAACkI,cAAf,KAAkC,UAAlC,IAAgDlI,OAAO,CAACkI,cAAR,GAAyB7L,MAFtE,CAAR;AAGH;AACD;;;AACA,SAAS8L,mBAAT,CAA6BnI,OAA7B,EAAsC;EAClC,IAAIoH,QAAQ,GAAGpH,OAAO,CAACoH,QAAR,CAAiBC,WAAjB,EAAf;EACA,OAAQD,QAAQ,KAAK,OAAb,IACJA,QAAQ,KAAK,QADT,IAEJA,QAAQ,KAAK,QAFT,IAGJA,QAAQ,KAAK,UAHjB;AAIH;AACD;;;AACA,SAASgB,aAAT,CAAuBpI,OAAvB,EAAgC;EAC5B,OAAOqI,cAAc,CAACrI,OAAD,CAAd,IAA2BA,OAAO,CAACY,IAAR,IAAgB,QAAlD;AACH;AACD;;;AACA,SAAS0H,gBAAT,CAA0BtI,OAA1B,EAAmC;EAC/B,OAAOuI,eAAe,CAACvI,OAAD,CAAf,IAA4BA,OAAO,CAAC2G,YAAR,CAAqB,MAArB,CAAnC;AACH;AACD;;;AACA,SAAS0B,cAAT,CAAwBrI,OAAxB,EAAiC;EAC7B,OAAOA,OAAO,CAACoH,QAAR,CAAiBC,WAAjB,MAAkC,OAAzC;AACH;AACD;;;AACA,SAASkB,eAAT,CAAyBvI,OAAzB,EAAkC;EAC9B,OAAOA,OAAO,CAACoH,QAAR,CAAiBC,WAAjB,MAAkC,GAAzC;AACH;AACD;;;AACA,SAASmB,gBAAT,CAA0BxI,OAA1B,EAAmC;EAC/B,IAAI,CAACA,OAAO,CAAC2G,YAAR,CAAqB,UAArB,CAAD,IAAqC3G,OAAO,CAAC2H,QAAR,KAAqB5G,SAA9D,EAAyE;IACrE,OAAO,KAAP;EACH;;EACD,IAAI4G,QAAQ,GAAG3H,OAAO,CAACzD,YAAR,CAAqB,UAArB,CAAf;EACA,OAAO,CAAC,EAAEoL,QAAQ,IAAI,CAACc,KAAK,CAACC,QAAQ,CAACf,QAAD,EAAW,EAAX,CAAT,CAApB,CAAR;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASR,gBAAT,CAA0BnH,OAA1B,EAAmC;EAC/B,IAAI,CAACwI,gBAAgB,CAACxI,OAAD,CAArB,EAAgC;IAC5B,OAAO,IAAP;EACH,CAH8B,CAI/B;;;EACA,MAAM2H,QAAQ,GAAGe,QAAQ,CAAC1I,OAAO,CAACzD,YAAR,CAAqB,UAArB,KAAoC,EAArC,EAAyC,EAAzC,CAAzB;EACA,OAAOkM,KAAK,CAACd,QAAD,CAAL,GAAkB,CAAC,CAAnB,GAAuBA,QAA9B;AACH;AACD;;;AACA,SAASF,wBAAT,CAAkCzH,OAAlC,EAA2C;EACvC,IAAIoH,QAAQ,GAAGpH,OAAO,CAACoH,QAAR,CAAiBC,WAAjB,EAAf;EACA,IAAIsB,SAAS,GAAGvB,QAAQ,KAAK,OAAb,IAAwBpH,OAAO,CAACY,IAAhD;EACA,OAAQ+H,SAAS,KAAK,MAAd,IACJA,SAAS,KAAK,UADV,IAEJvB,QAAQ,KAAK,QAFT,IAGJA,QAAQ,KAAK,UAHjB;AAIH;AACD;AACA;AACA;AACA;;;AACA,SAASU,sBAAT,CAAgC9H,OAAhC,EAAyC;EACrC;EACA,IAAIoI,aAAa,CAACpI,OAAD,CAAjB,EAA4B;IACxB,OAAO,KAAP;EACH;;EACD,OAAQmI,mBAAmB,CAACnI,OAAD,CAAnB,IACJsI,gBAAgB,CAACtI,OAAD,CADZ,IAEJA,OAAO,CAAC2G,YAAR,CAAqB,iBAArB,CAFI,IAGJ6B,gBAAgB,CAACxI,OAAD,CAHpB;AAIH;AACD;;;AACA,SAASkH,SAAT,CAAmB0B,IAAnB,EAAyB;EACrB;EACA,OAAQA,IAAI,CAACC,aAAL,IAAsBD,IAAI,CAACC,aAAL,CAAmBC,WAA1C,IAA0Df,MAAjE;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMgB,SAAN,CAAgB;EACZjM,WAAW,CAACkM,QAAD,EAAWC,QAAX,EAAqBC,OAArB,EAA8BnM,SAA9B,EAAyCoM,YAAY,GAAG,KAAxD,EAA+D;IACtE,KAAKH,QAAL,GAAgBA,QAAhB;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKnM,SAAL,GAAiBA,SAAjB;IACA,KAAKqM,YAAL,GAAoB,KAApB,CALsE,CAMtE;;IACA,KAAKC,mBAAL,GAA2B,MAAM,KAAKC,wBAAL,EAAjC;;IACA,KAAKC,iBAAL,GAAyB,MAAM,KAAKC,yBAAL,EAA/B;;IACA,KAAKC,QAAL,GAAgB,IAAhB;;IACA,IAAI,CAACN,YAAL,EAAmB;MACf,KAAKO,aAAL;IACH;EACJ;EACD;;;EACW,IAAP5G,OAAO,GAAG;IACV,OAAO,KAAK2G,QAAZ;EACH;;EACU,IAAP3G,OAAO,CAAC6G,KAAD,EAAQ;IACf,KAAKF,QAAL,GAAgBE,KAAhB;;IACA,IAAI,KAAKC,YAAL,IAAqB,KAAKC,UAA9B,EAA0C;MACtC,KAAKC,qBAAL,CAA2BH,KAA3B,EAAkC,KAAKC,YAAvC;;MACA,KAAKE,qBAAL,CAA2BH,KAA3B,EAAkC,KAAKE,UAAvC;IACH;EACJ;EACD;;;EACAE,OAAO,GAAG;IACN,MAAMC,WAAW,GAAG,KAAKJ,YAAzB;IACA,MAAMK,SAAS,GAAG,KAAKJ,UAAvB;;IACA,IAAIG,WAAJ,EAAiB;MACbA,WAAW,CAACE,mBAAZ,CAAgC,OAAhC,EAAyC,KAAKb,mBAA9C;MACAW,WAAW,CAACrL,MAAZ;IACH;;IACD,IAAIsL,SAAJ,EAAe;MACXA,SAAS,CAACC,mBAAV,CAA8B,OAA9B,EAAuC,KAAKX,iBAA5C;MACAU,SAAS,CAACtL,MAAV;IACH;;IACD,KAAKiL,YAAL,GAAoB,KAAKC,UAAL,GAAkB,IAAtC;IACA,KAAKT,YAAL,GAAoB,KAApB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIM,aAAa,GAAG;IACZ;IACA,IAAI,KAAKN,YAAT,EAAuB;MACnB,OAAO,IAAP;IACH;;IACD,KAAKF,OAAL,CAAaiB,iBAAb,CAA+B,MAAM;MACjC,IAAI,CAAC,KAAKP,YAAV,EAAwB;QACpB,KAAKA,YAAL,GAAoB,KAAKQ,aAAL,EAApB;;QACA,KAAKR,YAAL,CAAkBS,gBAAlB,CAAmC,OAAnC,EAA4C,KAAKhB,mBAAjD;MACH;;MACD,IAAI,CAAC,KAAKQ,UAAV,EAAsB;QAClB,KAAKA,UAAL,GAAkB,KAAKO,aAAL,EAAlB;;QACA,KAAKP,UAAL,CAAgBQ,gBAAhB,CAAiC,OAAjC,EAA0C,KAAKd,iBAA/C;MACH;IACJ,CATD;;IAUA,IAAI,KAAKP,QAAL,CAAcsB,UAAlB,EAA8B;MAC1B,KAAKtB,QAAL,CAAcsB,UAAd,CAAyBC,YAAzB,CAAsC,KAAKX,YAA3C,EAAyD,KAAKZ,QAA9D;;MACA,KAAKA,QAAL,CAAcsB,UAAd,CAAyBC,YAAzB,CAAsC,KAAKV,UAA3C,EAAuD,KAAKb,QAAL,CAAcwB,WAArE;;MACA,KAAKpB,YAAL,GAAoB,IAApB;IACH;;IACD,OAAO,KAAKA,YAAZ;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIqB,4BAA4B,CAACC,OAAD,EAAU;IAClC,OAAO,IAAIC,OAAJ,CAAYC,OAAO,IAAI;MAC1B,KAAKC,gBAAL,CAAsB,MAAMD,OAAO,CAAC,KAAKE,mBAAL,CAAyBJ,OAAzB,CAAD,CAAnC;IACH,CAFM,CAAP;EAGH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIK,kCAAkC,CAACL,OAAD,EAAU;IACxC,OAAO,IAAIC,OAAJ,CAAYC,OAAO,IAAI;MAC1B,KAAKC,gBAAL,CAAsB,MAAMD,OAAO,CAAC,KAAKpB,yBAAL,CAA+BkB,OAA/B,CAAD,CAAnC;IACH,CAFM,CAAP;EAGH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIM,iCAAiC,CAACN,OAAD,EAAU;IACvC,OAAO,IAAIC,OAAJ,CAAYC,OAAO,IAAI;MAC1B,KAAKC,gBAAL,CAAsB,MAAMD,OAAO,CAAC,KAAKtB,wBAAL,CAA8BoB,OAA9B,CAAD,CAAnC;IACH,CAFM,CAAP;EAGH;EACD;AACJ;AACA;AACA;AACA;;;EACIO,kBAAkB,CAACC,KAAD,EAAQ;IACtB;IACA,MAAMC,OAAO,GAAG,KAAKnC,QAAL,CAAclK,gBAAd,CAAgC,qBAAoBoM,KAAM,KAA3B,GAAmC,kBAAiBA,KAAM,KAA1D,GAAkE,cAAaA,KAAM,GAApH,CAAhB;;IACA,IAAI,OAAO5H,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;MAC/C,KAAK,IAAIvE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoM,OAAO,CAAC9O,MAA5B,EAAoC0C,CAAC,EAArC,EAAyC;QACrC;QACA,IAAIoM,OAAO,CAACpM,CAAD,CAAP,CAAW4H,YAAX,CAAyB,aAAYuE,KAAM,EAA3C,CAAJ,EAAmD;UAC/CE,OAAO,CAACC,IAAR,CAAc,gDAA+CH,KAAM,KAAtD,GACR,sBAAqBA,KAAM,4BADnB,GAER,qCAFL,EAE2CC,OAAO,CAACpM,CAAD,CAFlD;QAGH,CAJD,MAKK,IAAIoM,OAAO,CAACpM,CAAD,CAAP,CAAW4H,YAAX,CAAyB,oBAAmBuE,KAAM,EAAlD,CAAJ,EAA0D;UAC3DE,OAAO,CAACC,IAAR,CAAc,uDAAsDH,KAAM,KAA7D,GACR,sBAAqBA,KAAM,sCADnB,GAER,2BAFL,EAEiCC,OAAO,CAACpM,CAAD,CAFxC;QAGH;MACJ;IACJ;;IACD,IAAImM,KAAK,IAAI,OAAb,EAAsB;MAClB,OAAOC,OAAO,CAAC9O,MAAR,GAAiB8O,OAAO,CAAC,CAAD,CAAxB,GAA8B,KAAKG,wBAAL,CAA8B,KAAKtC,QAAnC,CAArC;IACH;;IACD,OAAOmC,OAAO,CAAC9O,MAAR,GACD8O,OAAO,CAACA,OAAO,CAAC9O,MAAR,GAAiB,CAAlB,CADN,GAED,KAAKkP,uBAAL,CAA6B,KAAKvC,QAAlC,CAFN;EAGH;EACD;AACJ;AACA;AACA;;;EACI8B,mBAAmB,CAACJ,OAAD,EAAU;IACzB;IACA,MAAMc,iBAAiB,GAAG,KAAKxC,QAAL,CAAcyC,aAAd,CAA6B,uBAAD,GAA2B,mBAAvD,CAA1B;;IACA,IAAID,iBAAJ,EAAuB;MACnB;MACA,IAAI,CAAC,OAAOlI,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KACAkI,iBAAiB,CAAC7E,YAAlB,CAAgC,mBAAhC,CADJ,EACyD;QACrDyE,OAAO,CAACC,IAAR,CAAc,yDAAD,GACR,0DADQ,GAER,0BAFL,EAEgCG,iBAFhC;MAGH,CAPkB,CAQnB;MACA;;;MACA,IAAI,CAAC,OAAOlI,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KACA,CAAC,KAAK2F,QAAL,CAAcrB,WAAd,CAA0B4D,iBAA1B,CADL,EACmD;QAC/CJ,OAAO,CAACC,IAAR,CAAc,wDAAd,EAAuEG,iBAAvE;MACH;;MACD,IAAI,CAAC,KAAKvC,QAAL,CAAcrB,WAAd,CAA0B4D,iBAA1B,CAAL,EAAmD;QAC/C,MAAME,cAAc,GAAG,KAAKJ,wBAAL,CAA8BE,iBAA9B,CAAvB;;QACAE,cAAc,EAAEpF,KAAhB,CAAsBoE,OAAtB;QACA,OAAO,CAAC,CAACgB,cAAT;MACH;;MACDF,iBAAiB,CAAClF,KAAlB,CAAwBoE,OAAxB;MACA,OAAO,IAAP;IACH;;IACD,OAAO,KAAKlB,yBAAL,CAA+BkB,OAA/B,CAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIlB,yBAAyB,CAACkB,OAAD,EAAU;IAC/B,MAAMc,iBAAiB,GAAG,KAAKP,kBAAL,CAAwB,OAAxB,CAA1B;;IACA,IAAIO,iBAAJ,EAAuB;MACnBA,iBAAiB,CAAClF,KAAlB,CAAwBoE,OAAxB;IACH;;IACD,OAAO,CAAC,CAACc,iBAAT;EACH;EACD;AACJ;AACA;AACA;;;EACIlC,wBAAwB,CAACoB,OAAD,EAAU;IAC9B,MAAMc,iBAAiB,GAAG,KAAKP,kBAAL,CAAwB,KAAxB,CAA1B;;IACA,IAAIO,iBAAJ,EAAuB;MACnBA,iBAAiB,CAAClF,KAAlB,CAAwBoE,OAAxB;IACH;;IACD,OAAO,CAAC,CAACc,iBAAT;EACH;EACD;AACJ;AACA;;;EACIG,WAAW,GAAG;IACV,OAAO,KAAKvC,YAAZ;EACH;EACD;;;EACAkC,wBAAwB,CAACM,IAAD,EAAO;IAC3B,IAAI,KAAK3C,QAAL,CAAcrB,WAAd,CAA0BgE,IAA1B,KAAmC,KAAK3C,QAAL,CAAclC,UAAd,CAAyB6E,IAAzB,CAAvC,EAAuE;MACnE,OAAOA,IAAP;IACH;;IACD,MAAMC,QAAQ,GAAGD,IAAI,CAACC,QAAtB;;IACA,KAAK,IAAI9M,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8M,QAAQ,CAACxP,MAA7B,EAAqC0C,CAAC,EAAtC,EAA0C;MACtC,MAAM+M,aAAa,GAAGD,QAAQ,CAAC9M,CAAD,CAAR,CAAYwB,QAAZ,KAAyB,KAAKxD,SAAL,CAAeyD,YAAxC,GAChB,KAAK8K,wBAAL,CAA8BO,QAAQ,CAAC9M,CAAD,CAAtC,CADgB,GAEhB,IAFN;;MAGA,IAAI+M,aAAJ,EAAmB;QACf,OAAOA,aAAP;MACH;IACJ;;IACD,OAAO,IAAP;EACH;EACD;;;EACAP,uBAAuB,CAACK,IAAD,EAAO;IAC1B,IAAI,KAAK3C,QAAL,CAAcrB,WAAd,CAA0BgE,IAA1B,KAAmC,KAAK3C,QAAL,CAAclC,UAAd,CAAyB6E,IAAzB,CAAvC,EAAuE;MACnE,OAAOA,IAAP;IACH,CAHyB,CAI1B;;;IACA,MAAMC,QAAQ,GAAGD,IAAI,CAACC,QAAtB;;IACA,KAAK,IAAI9M,CAAC,GAAG8M,QAAQ,CAACxP,MAAT,GAAkB,CAA/B,EAAkC0C,CAAC,IAAI,CAAvC,EAA0CA,CAAC,EAA3C,EAA+C;MAC3C,MAAM+M,aAAa,GAAGD,QAAQ,CAAC9M,CAAD,CAAR,CAAYwB,QAAZ,KAAyB,KAAKxD,SAAL,CAAeyD,YAAxC,GAChB,KAAK+K,uBAAL,CAA6BM,QAAQ,CAAC9M,CAAD,CAArC,CADgB,GAEhB,IAFN;;MAGA,IAAI+M,aAAJ,EAAmB;QACf,OAAOA,aAAP;MACH;IACJ;;IACD,OAAO,IAAP;EACH;EACD;;;EACA1B,aAAa,GAAG;IACZ,MAAM2B,MAAM,GAAG,KAAKhP,SAAL,CAAemC,aAAf,CAA6B,KAA7B,CAAf;;IACA,KAAK4K,qBAAL,CAA2B,KAAKL,QAAhC,EAA0CsC,MAA1C;;IACAA,MAAM,CAACnM,SAAP,CAAiBC,GAAjB,CAAqB,qBAArB;IACAkM,MAAM,CAACnM,SAAP,CAAiBC,GAAjB,CAAqB,uBAArB;IACAkM,MAAM,CAAC/P,YAAP,CAAoB,aAApB,EAAmC,MAAnC;IACA,OAAO+P,MAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIjC,qBAAqB,CAACkC,SAAD,EAAYD,MAAZ,EAAoB;IACrC;IACA;IACAC,SAAS,GAAGD,MAAM,CAAC/P,YAAP,CAAoB,UAApB,EAAgC,GAAhC,CAAH,GAA0C+P,MAAM,CAACzP,eAAP,CAAuB,UAAvB,CAAnD;EACH;EACD;AACJ;AACA;AACA;;;EACI2P,aAAa,CAACnJ,OAAD,EAAU;IACnB,IAAI,KAAK8G,YAAL,IAAqB,KAAKC,UAA9B,EAA0C;MACtC,KAAKC,qBAAL,CAA2BhH,OAA3B,EAAoC,KAAK8G,YAAzC;;MACA,KAAKE,qBAAL,CAA2BhH,OAA3B,EAAoC,KAAK+G,UAAzC;IACH;EACJ;EACD;;;EACAgB,gBAAgB,CAACqB,EAAD,EAAK;IACjB,IAAI,KAAKhD,OAAL,CAAaiD,QAAjB,EAA2B;MACvBD,EAAE;IACL,CAFD,MAGK;MACD,KAAKhD,OAAL,CAAakD,QAAb,CAAsB1I,IAAtB,CAA2B9I,IAAI,CAAC,CAAD,CAA/B,EAAoCwH,SAApC,CAA8C8J,EAA9C;IACH;EACJ;;AApQW;AAsQhB;AACA;AACA;AACA;AACA;;;AACA,MAAMG,gBAAN,CAAuB;EACnBvP,WAAW,CAACmM,QAAD,EAAWC,OAAX,EAAoBnM,SAApB,EAA+B;IACtC,KAAKkM,QAAL,GAAgBA,QAAhB;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKnM,SAAL,GAAiBA,SAAjB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACIuP,MAAM,CAACtM,OAAD,EAAUuM,oBAAoB,GAAG,KAAjC,EAAwC;IAC1C,OAAO,IAAIxD,SAAJ,CAAc/I,OAAd,EAAuB,KAAKiJ,QAA5B,EAAsC,KAAKC,OAA3C,EAAoD,KAAKnM,SAAzD,EAAoEwP,oBAApE,CAAP;EACH;;AAfkB;;AAiBvBF,gBAAgB,CAAC5L,IAAjB;EAAA,iBAA6G4L,gBAA7G,EAv5BgGpU,EAu5BhG,UAA+IwO,oBAA/I,GAv5BgGxO,EAu5BhG,UAAgLA,EAAE,CAACuU,MAAnL,GAv5BgGvU,EAu5BhG,UAAsMD,QAAtM;AAAA;;AACAqU,gBAAgB,CAAC1L,KAAjB,kBAx5BgG1I,EAw5BhG;EAAA,OAAiHoU,gBAAjH;EAAA,SAAiHA,gBAAjH;EAAA,YAA+I;AAA/I;;AACA;EAAA,mDAz5BgGpU,EAy5BhG,mBAA2FoU,gBAA3F,EAAyH,CAAC;IAC9GzL,IAAI,EAAExI,UADwG;IAE9GyI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAFwG,CAAD,CAAzH,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAE6F;IAAR,CAAD,EAAiC;MAAE7F,IAAI,EAAE3I,EAAE,CAACuU;IAAX,CAAjC,EAAsD;MAAE5L,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACnHJ,IAAI,EAAEvI,MAD6G;QAEnHwI,IAAI,EAAE,CAAC7I,QAAD;MAF6G,CAAD;IAA/B,CAAtD,CAAP;EAGlB,CANxB;AAAA;AAOA;;;AACA,MAAMyU,YAAN,CAAmB;EACf3P,WAAW,CAAC4P,WAAD,EAAcC,iBAAd;EACX;AACJ;AACA;AACA;EACI5P,SALW,EAKA;IACP,KAAK2P,WAAL,GAAmBA,WAAnB;IACA,KAAKC,iBAAL,GAAyBA,iBAAzB;IACA;;IACA,KAAKC,yBAAL,GAAiC,IAAjC;IACA,KAAKC,SAAL,GAAiB,KAAKF,iBAAL,CAAuBL,MAAvB,CAA8B,KAAKI,WAAL,CAAiBI,aAA/C,EAA8D,IAA9D,CAAjB;EACH;EACD;;;EACW,IAAPhK,OAAO,GAAG;IACV,OAAO,KAAK+J,SAAL,CAAe/J,OAAtB;EACH;;EACU,IAAPA,OAAO,CAAC6G,KAAD,EAAQ;IACf,KAAKkD,SAAL,CAAe/J,OAAf,GAAyB9H,qBAAqB,CAAC2O,KAAD,CAA9C;EACH;EACD;AACJ;AACA;AACA;;;EACmB,IAAXoD,WAAW,GAAG;IACd,OAAO,KAAKC,YAAZ;EACH;;EACc,IAAXD,WAAW,CAACpD,KAAD,EAAQ;IACnB,KAAKqD,YAAL,GAAoBhS,qBAAqB,CAAC2O,KAAD,CAAzC;EACH;;EACD/K,WAAW,GAAG;IACV,KAAKiO,SAAL,CAAe9C,OAAf,GADU,CAEV;IACA;;IACA,IAAI,KAAK6C,yBAAT,EAAoC;MAChC,KAAKA,yBAAL,CAA+BtG,KAA/B;;MACA,KAAKsG,yBAAL,GAAiC,IAAjC;IACH;EACJ;;EACDK,kBAAkB,GAAG;IACjB,KAAKJ,SAAL,CAAenD,aAAf;;IACA,IAAI,KAAKqD,WAAT,EAAsB;MAClB,KAAKG,aAAL;IACH;EACJ;;EACDC,SAAS,GAAG;IACR,IAAI,CAAC,KAAKN,SAAL,CAAelB,WAAf,EAAL,EAAmC;MAC/B,KAAKkB,SAAL,CAAenD,aAAf;IACH;EACJ;;EACD0D,WAAW,CAACjL,OAAD,EAAU;IACjB,MAAMkL,iBAAiB,GAAGlL,OAAO,CAAC,aAAD,CAAjC;;IACA,IAAIkL,iBAAiB,IACjB,CAACA,iBAAiB,CAACC,WADnB,IAEA,KAAKP,WAFL,IAGA,KAAKF,SAAL,CAAelB,WAAf,EAHJ,EAGkC;MAC9B,KAAKuB,aAAL;IACH;EACJ;;EACDA,aAAa,GAAG;IACZ,KAAKN,yBAAL,GAAiC7T,iCAAiC,EAAlE;IACA,KAAK8T,SAAL,CAAepC,4BAAf;EACH;;AA9Dc;;AAgEnBgC,YAAY,CAAChM,IAAb;EAAA,iBAAyGgM,YAAzG,EAj+BgGxU,EAi+BhG,mBAAuIA,EAAE,CAACsV,UAA1I,GAj+BgGtV,EAi+BhG,mBAAiKoU,gBAAjK,GAj+BgGpU,EAi+BhG,mBAA8LD,QAA9L;AAAA;;AACAyU,YAAY,CAACe,IAAb,kBAl+BgGvV,EAk+BhG;EAAA,MAA6FwU,YAA7F;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAl+BgGxU,EAk+BhG;AAAA;;AACA;EAAA,mDAn+BgGA,EAm+BhG,mBAA2FwU,YAA3F,EAAqH,CAAC;IAC1G7L,IAAI,EAAErI,SADoG;IAE1GsI,IAAI,EAAE,CAAC;MACC4M,QAAQ,EAAE,gBADX;MAECC,QAAQ,EAAE;IAFX,CAAD;EAFoG,CAAD,CAArH,EAM4B,YAAY;IAAE,OAAO,CAAC;MAAE9M,IAAI,EAAE3I,EAAE,CAACsV;IAAX,CAAD,EAA0B;MAAE3M,IAAI,EAAEyL;IAAR,CAA1B,EAAsD;MAAEzL,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACnHJ,IAAI,EAAEvI,MAD6G;QAEnHwI,IAAI,EAAE,CAAC7I,QAAD;MAF6G,CAAD;IAA/B,CAAtD,CAAP;EAGlB,CATxB,EAS0C;IAAE8K,OAAO,EAAE,CAAC;MACtClC,IAAI,EAAEpI,KADgC;MAEtCqI,IAAI,EAAE,CAAC,cAAD;IAFgC,CAAD,CAAX;IAG1BkM,WAAW,EAAE,CAAC;MACdnM,IAAI,EAAEpI,KADQ;MAEdqI,IAAI,EAAE,CAAC,yBAAD;IAFQ,CAAD;EAHa,CAT1C;AAAA;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM8M,qBAAN,SAAoC5E,SAApC,CAA8C;EAC1CjM,WAAW,CAACkM,QAAD,EAAWC,QAAX,EAAqBC,OAArB,EAA8BnM,SAA9B,EAAyC6Q,iBAAzC,EAA4DC,cAA5D,EAA4EhG,MAA5E,EAAoF;IAC3F,MAAMmB,QAAN,EAAgBC,QAAhB,EAA0BC,OAA1B,EAAmCnM,SAAnC,EAA8C8K,MAAM,CAACiG,KAArD;IACA,KAAKF,iBAAL,GAAyBA,iBAAzB;IACA,KAAKC,cAAL,GAAsBA,cAAtB;;IACA,KAAKD,iBAAL,CAAuBG,QAAvB,CAAgC,IAAhC;EACH;EACD;;;EACW,IAAPjL,OAAO,GAAG;IACV,OAAO,KAAK2G,QAAZ;EACH;;EACU,IAAP3G,OAAO,CAAC6G,KAAD,EAAQ;IACf,KAAKF,QAAL,GAAgBE,KAAhB;;IACA,IAAI,KAAKF,QAAT,EAAmB;MACf,KAAKmE,iBAAL,CAAuBG,QAAvB,CAAgC,IAAhC;IACH,CAFD,MAGK;MACD,KAAKH,iBAAL,CAAuBI,UAAvB,CAAkC,IAAlC;IACH;EACJ;EACD;;;EACAjE,OAAO,GAAG;IACN,KAAK6D,iBAAL,CAAuBI,UAAvB,CAAkC,IAAlC;;IACA,MAAMjE,OAAN;EACH;EACD;;;EACAkE,OAAO,GAAG;IACN,KAAKJ,cAAL,CAAoBK,YAApB,CAAiC,IAAjC;;IACA,KAAKjC,aAAL,CAAmB,IAAnB;EACH;EACD;;;EACAkC,QAAQ,GAAG;IACP,KAAKN,cAAL,CAAoBO,UAApB,CAA+B,IAA/B;;IACA,KAAKnC,aAAL,CAAmB,KAAnB;EACH;;AAlCyC;AAqC9C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMoC,yBAAyB,GAAG,IAAI5V,cAAJ,CAAmB,2BAAnB,CAAlC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;AACA,MAAM6V,mCAAN,CAA0C;EACtCxR,WAAW,GAAG;IACV;IACA,KAAKyR,SAAL,GAAiB,IAAjB;EACH;EACD;;;EACAL,YAAY,CAACrB,SAAD,EAAY;IACpB;IACA,IAAI,KAAK0B,SAAT,EAAoB;MAChB1B,SAAS,CAAC9P,SAAV,CAAoBmN,mBAApB,CAAwC,OAAxC,EAAiD,KAAKqE,SAAtD,EAAiE,IAAjE;IACH;;IACD,KAAKA,SAAL,GAAkBC,CAAD,IAAO,KAAKC,UAAL,CAAgB5B,SAAhB,EAA2B2B,CAA3B,CAAxB;;IACA3B,SAAS,CAAC3D,OAAV,CAAkBiB,iBAAlB,CAAoC,MAAM;MACtC0C,SAAS,CAAC9P,SAAV,CAAoBsN,gBAApB,CAAqC,OAArC,EAA8C,KAAKkE,SAAnD,EAA8D,IAA9D;IACH,CAFD;EAGH;EACD;;;EACAH,UAAU,CAACvB,SAAD,EAAY;IAClB,IAAI,CAAC,KAAK0B,SAAV,EAAqB;MACjB;IACH;;IACD1B,SAAS,CAAC9P,SAAV,CAAoBmN,mBAApB,CAAwC,OAAxC,EAAiD,KAAKqE,SAAtD,EAAiE,IAAjE;;IACA,KAAKA,SAAL,GAAiB,IAAjB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACIE,UAAU,CAAC5B,SAAD,EAAYtI,KAAZ,EAAmB;IACzB,MAAMmK,MAAM,GAAGnK,KAAK,CAACmK,MAArB;IACA,MAAMC,aAAa,GAAG9B,SAAS,CAAC7D,QAAhC,CAFyB,CAGzB;IACA;;IACA,IAAI0F,MAAM,IAAI,CAACC,aAAa,CAACC,QAAd,CAAuBF,MAAvB,CAAX,IAA6C,CAACA,MAAM,CAACG,OAAP,GAAiB,sBAAjB,CAAlD,EAA4F;MACxF;MACA;MACA;MACAC,UAAU,CAAC,MAAM;QACb;QACA,IAAIjC,SAAS,CAAC/J,OAAV,IAAqB,CAAC6L,aAAa,CAACC,QAAd,CAAuB/B,SAAS,CAAC9P,SAAV,CAAoBgS,aAA3C,CAA1B,EAAqF;UACjFlC,SAAS,CAACrD,yBAAV;QACH;MACJ,CALS,CAAV;IAMH;EACJ;;AA/CqC;AAkD1C;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMwF,gBAAN,CAAuB;EACnBlS,WAAW,GAAG;IACV;IACA;IACA,KAAKmS,eAAL,GAAuB,EAAvB;EACH;EACD;AACJ;AACA;AACA;;;EACIlB,QAAQ,CAAClB,SAAD,EAAY;IAChB;IACA,KAAKoC,eAAL,GAAuB,KAAKA,eAAL,CAAqBvU,MAArB,CAA4BwU,EAAE,IAAIA,EAAE,KAAKrC,SAAzC,CAAvB;IACA,IAAIsC,KAAK,GAAG,KAAKF,eAAjB;;IACA,IAAIE,KAAK,CAAC9S,MAAV,EAAkB;MACd8S,KAAK,CAACA,KAAK,CAAC9S,MAAN,GAAe,CAAhB,CAAL,CAAwB8R,QAAxB;IACH;;IACDgB,KAAK,CAACpT,IAAN,CAAW8Q,SAAX;;IACAA,SAAS,CAACoB,OAAV;EACH;EACD;AACJ;AACA;AACA;;;EACID,UAAU,CAACnB,SAAD,EAAY;IAClBA,SAAS,CAACsB,QAAV;;IACA,MAAMgB,KAAK,GAAG,KAAKF,eAAnB;IACA,MAAMlQ,CAAC,GAAGoQ,KAAK,CAACjP,OAAN,CAAc2M,SAAd,CAAV;;IACA,IAAI9N,CAAC,KAAK,CAAC,CAAX,EAAc;MACVoQ,KAAK,CAACC,MAAN,CAAarQ,CAAb,EAAgB,CAAhB;;MACA,IAAIoQ,KAAK,CAAC9S,MAAV,EAAkB;QACd8S,KAAK,CAACA,KAAK,CAAC9S,MAAN,GAAe,CAAhB,CAAL,CAAwB4R,OAAxB;MACH;IACJ;EACJ;;AAlCkB;;AAoCvBe,gBAAgB,CAACvO,IAAjB;EAAA,iBAA6GuO,gBAA7G;AAAA;;AACAA,gBAAgB,CAACrO,KAAjB,kBAlqCgG1I,EAkqChG;EAAA,OAAiH+W,gBAAjH;EAAA,SAAiHA,gBAAjH;EAAA,YAA+I;AAA/I;;AACA;EAAA,mDAnqCgG/W,EAmqChG,mBAA2F+W,gBAA3F,EAAyH,CAAC;IAC9GpO,IAAI,EAAExI,UADwG;IAE9GyI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAFwG,CAAD,CAAzH;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMuO,4BAAN,CAAmC;EAC/BvS,WAAW,CAACmM,QAAD,EAAWC,OAAX,EAAoB0E,iBAApB,EAAuC7Q,SAAvC,EAAkD8Q,cAAlD,EAAkE;IACzE,KAAK5E,QAAL,GAAgBA,QAAhB;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAK0E,iBAAL,GAAyBA,iBAAzB;IACA,KAAK7Q,SAAL,GAAiBA,SAAjB,CAJyE,CAKzE;;IACA,KAAK8Q,cAAL,GAAsBA,cAAc,IAAI,IAAIS,mCAAJ,EAAxC;EACH;;EACDhC,MAAM,CAACtM,OAAD,EAAU6H,MAAM,GAAG;IAAEiG,KAAK,EAAE;EAAT,CAAnB,EAAqC;IACvC,IAAIwB,YAAJ;;IACA,IAAI,OAAOzH,MAAP,KAAkB,SAAtB,EAAiC;MAC7ByH,YAAY,GAAG;QAAExB,KAAK,EAAEjG;MAAT,CAAf;IACH,CAFD,MAGK;MACDyH,YAAY,GAAGzH,MAAf;IACH;;IACD,OAAO,IAAI8F,qBAAJ,CAA0B3N,OAA1B,EAAmC,KAAKiJ,QAAxC,EAAkD,KAAKC,OAAvD,EAAgE,KAAKnM,SAArE,EAAgF,KAAK6Q,iBAArF,EAAwG,KAAKC,cAA7G,EAA6HyB,YAA7H,CAAP;EACH;;AAlB8B;;AAoBnCD,4BAA4B,CAAC5O,IAA7B;EAAA,iBAAyH4O,4BAAzH,EApsCgGpX,EAosChG,UAAuKwO,oBAAvK,GApsCgGxO,EAosChG,UAAwMA,EAAE,CAACuU,MAA3M,GApsCgGvU,EAosChG,UAA8N+W,gBAA9N,GApsCgG/W,EAosChG,UAA2PD,QAA3P,GApsCgGC,EAosChG,UAAgRoW,yBAAhR;AAAA;;AACAgB,4BAA4B,CAAC1O,KAA7B,kBArsCgG1I,EAqsChG;EAAA,OAA6HoX,4BAA7H;EAAA,SAA6HA,4BAA7H;EAAA,YAAuK;AAAvK;;AACA;EAAA,mDAtsCgGpX,EAssChG,mBAA2FoX,4BAA3F,EAAqI,CAAC;IAC1HzO,IAAI,EAAExI,UADoH;IAE1HyI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAFoH,CAAD,CAArI,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAE6F;IAAR,CAAD,EAAiC;MAAE7F,IAAI,EAAE3I,EAAE,CAACuU;IAAX,CAAjC,EAAsD;MAAE5L,IAAI,EAAEoO;IAAR,CAAtD,EAAkF;MAAEpO,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC/IJ,IAAI,EAAEvI,MADyI;QAE/IwI,IAAI,EAAE,CAAC7I,QAAD;MAFyI,CAAD;IAA/B,CAAlF,EAG3B;MAAE4I,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClCJ,IAAI,EAAElI;MAD4B,CAAD,EAElC;QACCkI,IAAI,EAAEvI,MADP;QAECwI,IAAI,EAAE,CAACwN,yBAAD;MAFP,CAFkC;IAA/B,CAH2B,CAAP;EAQlB,CAXxB;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,SAASkB,+BAAT,CAAyChL,KAAzC,EAAgD;EAC5C;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OAAOA,KAAK,CAACiL,OAAN,KAAkB,CAAlB,IAAwBjL,KAAK,CAACkL,OAAN,KAAkB,CAAlB,IAAuBlL,KAAK,CAACmL,OAAN,KAAkB,CAAxE;AACH;AACD;;;AACA,SAASC,gCAAT,CAA0CpL,KAA1C,EAAiD;EAC7C,MAAMqL,KAAK,GAAIrL,KAAK,CAACsL,OAAN,IAAiBtL,KAAK,CAACsL,OAAN,CAAc,CAAd,CAAlB,IAAwCtL,KAAK,CAACuL,cAAN,IAAwBvL,KAAK,CAACuL,cAAN,CAAqB,CAArB,CAA9E,CAD6C,CAE7C;EACA;EACA;EACA;;EACA,OAAQ,CAAC,CAACF,KAAF,IACJA,KAAK,CAACG,UAAN,KAAqB,CAAC,CADlB,KAEHH,KAAK,CAACI,OAAN,IAAiB,IAAjB,IAAyBJ,KAAK,CAACI,OAAN,KAAkB,CAFxC,MAGHJ,KAAK,CAACK,OAAN,IAAiB,IAAjB,IAAyBL,KAAK,CAACK,OAAN,KAAkB,CAHxC,CAAR;AAIH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMC,+BAA+B,GAAG,IAAIzX,cAAJ,CAAmB,qCAAnB,CAAxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAM0X,uCAAuC,GAAG;EAC5CC,UAAU,EAAE,CAACjW,GAAD,EAAMC,OAAN,EAAeC,QAAf,EAAyBC,IAAzB,EAA+BC,KAA/B;AADgC,CAAhD;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAM8V,eAAe,GAAG,GAAxB;AACA;AACA;AACA;AACA;;AACA,MAAMC,4BAA4B,GAAGtX,+BAA+B,CAAC;EACjEuX,OAAO,EAAE,IADwD;EAEjEC,OAAO,EAAE;AAFwD,CAAD,CAApE;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,qBAAN,CAA4B;EACxB3T,WAAW,CAACE,SAAD,EAAY0T,MAAZ,EAAoBC,QAApB,EAA8BjG,OAA9B,EAAuC;IAC9C,KAAK1N,SAAL,GAAiBA,SAAjB;IACA;AACR;AACA;AACA;;IACQ,KAAK4T,iBAAL,GAAyB,IAAzB;IACA;;IACA,KAAKC,SAAL,GAAiB,IAAIxX,eAAJ,CAAoB,IAApB,CAAjB;IACA;AACR;AACA;AACA;;IACQ,KAAKyX,YAAL,GAAoB,CAApB;IACA;AACR;AACA;AACA;;IACQ,KAAKC,UAAL,GAAmBxM,KAAD,IAAW;MACzB;MACA;MACA,IAAI,KAAKyM,QAAL,EAAeZ,UAAf,EAA2BxU,IAA3B,CAAgC4I,OAAO,IAAIA,OAAO,KAAKD,KAAK,CAACC,OAA7D,CAAJ,EAA2E;QACvE;MACH;;MACD,KAAKqM,SAAL,CAAexM,IAAf,CAAoB,UAApB;;MACA,KAAKuM,iBAAL,GAAyB3X,eAAe,CAACsL,KAAD,CAAxC;IACH,CARD;IASA;AACR;AACA;AACA;;;IACQ,KAAK0M,YAAL,GAAqB1M,KAAD,IAAW;MAC3B;MACA;MACA;MACA,IAAI2M,IAAI,CAACC,GAAL,KAAa,KAAKL,YAAlB,GAAiCT,eAArC,EAAsD;QAClD;MACH,CAN0B,CAO3B;MACA;;;MACA,KAAKQ,SAAL,CAAexM,IAAf,CAAoBkL,+BAA+B,CAAChL,KAAD,CAA/B,GAAyC,UAAzC,GAAsD,OAA1E;;MACA,KAAKqM,iBAAL,GAAyB3X,eAAe,CAACsL,KAAD,CAAxC;IACH,CAXD;IAYA;AACR;AACA;AACA;;;IACQ,KAAK6M,aAAL,GAAsB7M,KAAD,IAAW;MAC5B;MACA;MACA,IAAIoL,gCAAgC,CAACpL,KAAD,CAApC,EAA6C;QACzC,KAAKsM,SAAL,CAAexM,IAAf,CAAoB,UAApB;;QACA;MACH,CAN2B,CAO5B;MACA;;;MACA,KAAKyM,YAAL,GAAoBI,IAAI,CAACC,GAAL,EAApB;;MACA,KAAKN,SAAL,CAAexM,IAAf,CAAoB,OAApB;;MACA,KAAKuM,iBAAL,GAAyB3X,eAAe,CAACsL,KAAD,CAAxC;IACH,CAZD;;IAaA,KAAKyM,QAAL,GAAgB,EACZ,GAAGb,uCADS;MAEZ,GAAGzF;IAFS,CAAhB,CA5D8C,CAgE9C;;IACA,KAAK2G,gBAAL,GAAwB,KAAKR,SAAL,CAAenN,IAAf,CAAoB7I,IAAI,CAAC,CAAD,CAAxB,CAAxB;IACA,KAAKyW,eAAL,GAAuB,KAAKD,gBAAL,CAAsB3N,IAAtB,CAA2B5I,oBAAoB,EAA/C,CAAvB,CAlE8C,CAmE9C;IACA;;IACA,IAAIkC,SAAS,CAAC8C,SAAd,EAAyB;MACrB4Q,MAAM,CAACvG,iBAAP,CAAyB,MAAM;QAC3BwG,QAAQ,CAACtG,gBAAT,CAA0B,SAA1B,EAAqC,KAAK0G,UAA1C,EAAsDT,4BAAtD;QACAK,QAAQ,CAACtG,gBAAT,CAA0B,WAA1B,EAAuC,KAAK4G,YAA5C,EAA0DX,4BAA1D;QACAK,QAAQ,CAACtG,gBAAT,CAA0B,YAA1B,EAAwC,KAAK+G,aAA7C,EAA4Dd,4BAA5D;MACH,CAJD;IAKH;EACJ;EACD;;;EACsB,IAAlBiB,kBAAkB,GAAG;IACrB,OAAO,KAAKV,SAAL,CAAelH,KAAtB;EACH;;EACD/K,WAAW,GAAG;IACV,KAAKiS,SAAL,CAAeW,QAAf;;IACA,IAAI,KAAKxU,SAAL,CAAe8C,SAAnB,EAA8B;MAC1B6Q,QAAQ,CAACzG,mBAAT,CAA6B,SAA7B,EAAwC,KAAK6G,UAA7C,EAAyDT,4BAAzD;MACAK,QAAQ,CAACzG,mBAAT,CAA6B,WAA7B,EAA0C,KAAK+G,YAA/C,EAA6DX,4BAA7D;MACAK,QAAQ,CAACzG,mBAAT,CAA6B,YAA7B,EAA2C,KAAKkH,aAAhD,EAA+Dd,4BAA/D;IACH;EACJ;;AAzFuB;;AA2F5BG,qBAAqB,CAAChQ,IAAtB;EAAA,iBAAkHgQ,qBAAlH,EA14CgGxY,EA04ChG,UAAyJa,EAAE,CAAC4H,QAA5J,GA14CgGzI,EA04ChG,UAAiLA,EAAE,CAACuU,MAApL,GA14CgGvU,EA04ChG,UAAuMD,QAAvM,GA14CgGC,EA04ChG,UAA4NiY,+BAA5N;AAAA;;AACAO,qBAAqB,CAAC9P,KAAtB,kBA34CgG1I,EA24ChG;EAAA,OAAsHwY,qBAAtH;EAAA,SAAsHA,qBAAtH;EAAA,YAAyJ;AAAzJ;;AACA;EAAA,mDA54CgGxY,EA44ChG,mBAA2FwY,qBAA3F,EAA8H,CAAC;IACnH7P,IAAI,EAAExI,UAD6G;IAEnHyI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAF6G,CAAD,CAA9H,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAE9H,EAAE,CAAC4H;IAAX,CAAD,EAAwB;MAAEE,IAAI,EAAE3I,EAAE,CAACuU;IAAX,CAAxB,EAA6C;MAAE5L,IAAI,EAAE6Q,QAAR;MAAkBzQ,UAAU,EAAE,CAAC;QACzGJ,IAAI,EAAEvI,MADmG;QAEzGwI,IAAI,EAAE,CAAC7I,QAAD;MAFmG,CAAD;IAA9B,CAA7C,EAG3B;MAAE4I,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClCJ,IAAI,EAAElI;MAD4B,CAAD,EAElC;QACCkI,IAAI,EAAEvI,MADP;QAECwI,IAAI,EAAE,CAACqP,+BAAD;MAFP,CAFkC;IAA/B,CAH2B,CAAP;EAQlB,CAXxB;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMwB,4BAA4B,GAAG,IAAIjZ,cAAJ,CAAmB,sBAAnB,EAA2C;EAC5EqI,UAAU,EAAE,MADgE;EAE5E6Q,OAAO,EAAEC;AAFmE,CAA3C,CAArC;AAIA;;AACA,SAASA,oCAAT,GAAgD;EAC5C,OAAO,IAAP;AACH;AACD;;;AACA,MAAMC,8BAA8B,GAAG,IAAIpZ,cAAJ,CAAmB,gCAAnB,CAAvC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMqZ,aAAN,CAAoB;EAChBhV,WAAW,CAACiV,YAAD,EAAe7I,OAAf,EAAwBnM,SAAxB,EAAmCiV,eAAnC,EAAoD;IAC3D,KAAK9I,OAAL,GAAeA,OAAf;IACA,KAAK8I,eAAL,GAAuBA,eAAvB,CAF2D,CAG3D;IACA;IACA;;IACA,KAAKjV,SAAL,GAAiBA,SAAjB;IACA,KAAKkV,YAAL,GAAoBF,YAAY,IAAI,KAAKG,kBAAL,EAApC;EACH;;EACDC,QAAQ,CAAC5U,OAAD,EAAU,GAAGsD,IAAb,EAAmB;IACvB,MAAMuR,cAAc,GAAG,KAAKJ,eAA5B;IACA,IAAIK,UAAJ;IACA,IAAIC,QAAJ;;IACA,IAAIzR,IAAI,CAACxE,MAAL,KAAgB,CAAhB,IAAqB,OAAOwE,IAAI,CAAC,CAAD,CAAX,KAAmB,QAA5C,EAAsD;MAClDyR,QAAQ,GAAGzR,IAAI,CAAC,CAAD,CAAf;IACH,CAFD,MAGK;MACD,CAACwR,UAAD,EAAaC,QAAb,IAAyBzR,IAAzB;IACH;;IACD,KAAK5B,KAAL;IACAsT,YAAY,CAAC,KAAKC,gBAAN,CAAZ;;IACA,IAAI,CAACH,UAAL,EAAiB;MACbA,UAAU,GACND,cAAc,IAAIA,cAAc,CAACC,UAAjC,GAA8CD,cAAc,CAACC,UAA7D,GAA0E,QAD9E;IAEH;;IACD,IAAIC,QAAQ,IAAI,IAAZ,IAAoBF,cAAxB,EAAwC;MACpCE,QAAQ,GAAGF,cAAc,CAACE,QAA1B;IACH,CAlBsB,CAmBvB;;;IACA,KAAKL,YAAL,CAAkBjW,YAAlB,CAA+B,WAA/B,EAA4CqW,UAA5C,EApBuB,CAqBvB;IACA;IACA;IACA;IACA;;;IACA,OAAO,KAAKnJ,OAAL,CAAaiB,iBAAb,CAA+B,MAAM;MACxC,IAAI,CAAC,KAAKsI,eAAV,EAA2B;QACvB,KAAKA,eAAL,GAAuB,IAAI9H,OAAJ,CAAYC,OAAO,IAAK,KAAK8H,eAAL,GAAuB9H,OAA/C,CAAvB;MACH;;MACD2H,YAAY,CAAC,KAAKC,gBAAN,CAAZ;MACA,KAAKA,gBAAL,GAAwB1D,UAAU,CAAC,MAAM;QACrC,KAAKmD,YAAL,CAAkB9S,WAAlB,GAAgC5B,OAAhC;;QACA,IAAI,OAAO+U,QAAP,KAAoB,QAAxB,EAAkC;UAC9B,KAAKE,gBAAL,GAAwB1D,UAAU,CAAC,MAAM,KAAK7P,KAAL,EAAP,EAAqBqT,QAArB,CAAlC;QACH;;QACD,KAAKI,eAAL;;QACA,KAAKD,eAAL,GAAuB,KAAKC,eAAL,GAAuB3R,SAA9C;MACH,CAPiC,EAO/B,GAP+B,CAAlC;MAQA,OAAO,KAAK0R,eAAZ;IACH,CAdM,CAAP;EAeH;EACD;AACJ;AACA;AACA;AACA;;;EACIxT,KAAK,GAAG;IACJ,IAAI,KAAKgT,YAAT,EAAuB;MACnB,KAAKA,YAAL,CAAkB9S,WAAlB,GAAgC,EAAhC;IACH;EACJ;;EACDP,WAAW,GAAG;IACV2T,YAAY,CAAC,KAAKC,gBAAN,CAAZ;IACA,KAAKP,YAAL,EAAmBtT,MAAnB;IACA,KAAKsT,YAAL,GAAoB,IAApB;IACA,KAAKS,eAAL;IACA,KAAKD,eAAL,GAAuB,KAAKC,eAAL,GAAuB3R,SAA9C;EACH;;EACDmR,kBAAkB,GAAG;IACjB,MAAMS,YAAY,GAAG,4BAArB;;IACA,MAAMC,gBAAgB,GAAG,KAAK7V,SAAL,CAAe8V,sBAAf,CAAsCF,YAAtC,CAAzB;;IACA,MAAMG,MAAM,GAAG,KAAK/V,SAAL,CAAemC,aAAf,CAA6B,KAA7B,CAAf,CAHiB,CAIjB;;;IACA,KAAK,IAAIH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6T,gBAAgB,CAACvW,MAArC,EAA6C0C,CAAC,EAA9C,EAAkD;MAC9C6T,gBAAgB,CAAC7T,CAAD,CAAhB,CAAoBJ,MAApB;IACH;;IACDmU,MAAM,CAAClT,SAAP,CAAiBC,GAAjB,CAAqB8S,YAArB;IACAG,MAAM,CAAClT,SAAP,CAAiBC,GAAjB,CAAqB,qBAArB;IACAiT,MAAM,CAAC9W,YAAP,CAAoB,aAApB,EAAmC,MAAnC;IACA8W,MAAM,CAAC9W,YAAP,CAAoB,WAApB,EAAiC,QAAjC;;IACA,KAAKe,SAAL,CAAegD,IAAf,CAAoBV,WAApB,CAAgCyT,MAAhC;;IACA,OAAOA,MAAP;EACH;;AAnFe;;AAqFpBhB,aAAa,CAACrR,IAAd;EAAA,iBAA0GqR,aAA1G,EAvgDgG7Z,EAugDhG,UAAyIyZ,4BAAzI,MAvgDgGzZ,EAugDhG,UAAkMA,EAAE,CAACuU,MAArM,GAvgDgGvU,EAugDhG,UAAwND,QAAxN,GAvgDgGC,EAugDhG,UAA6O4Z,8BAA7O;AAAA;;AACAC,aAAa,CAACnR,KAAd,kBAxgDgG1I,EAwgDhG;EAAA,OAA8G6Z,aAA9G;EAAA,SAA8GA,aAA9G;EAAA,YAAyI;AAAzI;;AACA;EAAA,mDAzgDgG7Z,EAygDhG,mBAA2F6Z,aAA3F,EAAsH,CAAC;IAC3GlR,IAAI,EAAExI,UADqG;IAE3GyI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAFqG,CAAD,CAAtH,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9DJ,IAAI,EAAElI;MADwD,CAAD,EAE9D;QACCkI,IAAI,EAAEvI,MADP;QAECwI,IAAI,EAAE,CAAC6Q,4BAAD;MAFP,CAF8D;IAA/B,CAAD,EAK3B;MAAE9Q,IAAI,EAAE3I,EAAE,CAACuU;IAAX,CAL2B,EAKN;MAAE5L,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACvDJ,IAAI,EAAEvI,MADiD;QAEvDwI,IAAI,EAAE,CAAC7I,QAAD;MAFiD,CAAD;IAA/B,CALM,EAQ3B;MAAE4I,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClCJ,IAAI,EAAElI;MAD4B,CAAD,EAElC;QACCkI,IAAI,EAAEvI,MADP;QAECwI,IAAI,EAAE,CAACgR,8BAAD;MAFP,CAFkC;IAA/B,CAR2B,CAAP;EAalB,CAhBxB;AAAA;AAiBA;AACA;AACA;AACA;;;AACA,MAAMkB,WAAN,CAAkB;EACdjW,WAAW,CAAC4P,WAAD,EAAcsG,cAAd,EAA8BC,gBAA9B,EAAgD/J,OAAhD,EAAyD;IAChE,KAAKwD,WAAL,GAAmBA,WAAnB;IACA,KAAKsG,cAAL,GAAsBA,cAAtB;IACA,KAAKC,gBAAL,GAAwBA,gBAAxB;IACA,KAAK/J,OAAL,GAAeA,OAAf;IACA,KAAKgK,WAAL,GAAmB,QAAnB;EACH;EACD;;;EACc,IAAVb,UAAU,GAAG;IACb,OAAO,KAAKa,WAAZ;EACH;;EACa,IAAVb,UAAU,CAAC1I,KAAD,EAAQ;IAClB,KAAKuJ,WAAL,GAAmBvJ,KAAK,KAAK,KAAV,IAAmBA,KAAK,KAAK,WAA7B,GAA2CA,KAA3C,GAAmD,QAAtE;;IACA,IAAI,KAAKuJ,WAAL,KAAqB,KAAzB,EAAgC;MAC5B,IAAI,KAAKC,aAAT,EAAwB;QACpB,KAAKA,aAAL,CAAmB1P,WAAnB;;QACA,KAAK0P,aAAL,GAAqB,IAArB;MACH;IACJ,CALD,MAMK,IAAI,CAAC,KAAKA,aAAV,EAAyB;MAC1B,KAAKA,aAAL,GAAqB,KAAKjK,OAAL,CAAaiB,iBAAb,CAA+B,MAAM;QACtD,OAAO,KAAK8I,gBAAL,CAAsBG,OAAtB,CAA8B,KAAK1G,WAAnC,EAAgDtK,SAAhD,CAA0D,MAAM;UACnE;UACA,MAAMiR,WAAW,GAAG,KAAK3G,WAAL,CAAiBI,aAAjB,CAA+B3N,WAAnD,CAFmE,CAGnE;UACA;;UACA,IAAIkU,WAAW,KAAK,KAAKC,sBAAzB,EAAiD;YAC7C,KAAKN,cAAL,CAAoBb,QAApB,CAA6BkB,WAA7B,EAA0C,KAAKH,WAA/C,EAA4D,KAAKZ,QAAjE;;YACA,KAAKgB,sBAAL,GAA8BD,WAA9B;UACH;QACJ,CATM,CAAP;MAUH,CAXoB,CAArB;IAYH;EACJ;;EACDzU,WAAW,GAAG;IACV,IAAI,KAAKuU,aAAT,EAAwB;MACpB,KAAKA,aAAL,CAAmB1P,WAAnB;IACH;EACJ;;AAvCa;;AAyClBsP,WAAW,CAACtS,IAAZ;EAAA,iBAAwGsS,WAAxG,EAvkDgG9a,EAukDhG,mBAAqIA,EAAE,CAACsV,UAAxI,GAvkDgGtV,EAukDhG,mBAA+J6Z,aAA/J,GAvkDgG7Z,EAukDhG,mBAAyLiD,IAAI,CAACqY,eAA9L,GAvkDgGtb,EAukDhG,mBAA0NA,EAAE,CAACuU,MAA7N;AAAA;;AACAuG,WAAW,CAACvF,IAAZ,kBAxkDgGvV,EAwkDhG;EAAA,MAA4F8a,WAA5F;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDAzkDgG9a,EAykDhG,mBAA2F8a,WAA3F,EAAoH,CAAC;IACzGnS,IAAI,EAAErI,SADmG;IAEzGsI,IAAI,EAAE,CAAC;MACC4M,QAAQ,EAAE,eADX;MAECC,QAAQ,EAAE;IAFX,CAAD;EAFmG,CAAD,CAApH,EAM4B,YAAY;IAAE,OAAO,CAAC;MAAE9M,IAAI,EAAE3I,EAAE,CAACsV;IAAX,CAAD,EAA0B;MAAE3M,IAAI,EAAEkR;IAAR,CAA1B,EAAmD;MAAElR,IAAI,EAAE1F,IAAI,CAACqY;IAAb,CAAnD,EAAmF;MAAE3S,IAAI,EAAE3I,EAAE,CAACuU;IAAX,CAAnF,CAAP;EAAiH,CAN3J,EAM6K;IAAE6F,UAAU,EAAE,CAAC;MAC5KzR,IAAI,EAAEpI,KADsK;MAE5KqI,IAAI,EAAE,CAAC,aAAD;IAFsK,CAAD,CAAd;IAG7JyR,QAAQ,EAAE,CAAC;MACX1R,IAAI,EAAEpI,KADK;MAEXqI,IAAI,EAAE,CAAC,qBAAD;IAFK,CAAD;EAHmJ,CAN7K;AAAA;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAM2S,6BAA6B,GAAG,IAAI/a,cAAJ,CAAmB,mCAAnB,CAAtC;AACA;AACA;AACA;AACA;;AACA,MAAMgb,2BAA2B,GAAGza,+BAA+B,CAAC;EAChEuX,OAAO,EAAE,IADuD;EAEhEC,OAAO,EAAE;AAFuD,CAAD,CAAnE;AAIA;;AACA,MAAMkD,YAAN,CAAmB;EACf5W,WAAW,CAACoM,OAAD,EAAUlM,SAAV,EAAqB2W,sBAArB;EACX;EACAhD,QAFW,EAEDjG,OAFC,EAEQ;IACf,KAAKxB,OAAL,GAAeA,OAAf;IACA,KAAKlM,SAAL,GAAiBA,SAAjB;IACA,KAAK2W,sBAAL,GAA8BA,sBAA9B;IACA;;IACA,KAAKxN,OAAL,GAAe,IAAf;IACA;;IACA,KAAKyN,cAAL,GAAsB,KAAtB;IACA;AACR;AACA;AACA;;IACQ,KAAKC,2BAAL,GAAmC,KAAnC;IACA;;IACA,KAAKC,YAAL,GAAoB,IAAI5W,GAAJ,EAApB;IACA;;IACA,KAAK6W,sBAAL,GAA8B,CAA9B;IACA;AACR;AACA;AACA;AACA;AACA;;IACQ,KAAKC,2BAAL,GAAmC,IAAI9W,GAAJ,EAAnC;IACA;AACR;AACA;AACA;;IACQ,KAAK+W,oBAAL,GAA4B,MAAM;MAC9B;MACA;MACA,KAAKL,cAAL,GAAsB,IAAtB;MACA,KAAKM,qBAAL,GAA6BnM,MAAM,CAAC+G,UAAP,CAAkB,MAAO,KAAK8E,cAAL,GAAsB,KAA/C,CAA7B;IACH,CALD;IAMA;;;IACA,KAAKO,0BAAL,GAAkC,IAAIhb,OAAJ,EAAlC;IACA;AACR;AACA;AACA;;IACQ,KAAKib,6BAAL,GAAsC7P,KAAD,IAAW;MAC5C,MAAMmK,MAAM,GAAGzV,eAAe,CAACsL,KAAD,CAA9B,CAD4C,CAE5C;;;MACA,KAAK,IAAIvE,OAAO,GAAG0O,MAAnB,EAA2B1O,OAA3B,EAAoCA,OAAO,GAAGA,OAAO,CAACqU,aAAtD,EAAqE;QACjE,IAAI9P,KAAK,CAAC3D,IAAN,KAAe,OAAnB,EAA4B;UACxB,KAAK0T,QAAL,CAAc/P,KAAd,EAAqBvE,OAArB;QACH,CAFD,MAGK;UACD,KAAKuU,OAAL,CAAahQ,KAAb,EAAoBvE,OAApB;QACH;MACJ;IACJ,CAXD;;IAYA,KAAKjD,SAAL,GAAiB4T,QAAjB;IACA,KAAK6D,cAAL,GAAsB9J,OAAO,EAAE+J,aAAT,IAA0B;IAAE;IAAlD;EACH;;EACDC,OAAO,CAAC1U,OAAD,EAAU2U,aAAa,GAAG,KAA1B,EAAiC;IACpC,MAAM7H,aAAa,GAAG7R,aAAa,CAAC+E,OAAD,CAAnC,CADoC,CAEpC;;IACA,IAAI,CAAC,KAAKhD,SAAL,CAAe8C,SAAhB,IAA6BgN,aAAa,CAACvM,QAAd,KAA2B,CAA5D,EAA+D;MAC3D,OAAOjH,EAAE,CAAC,IAAD,CAAT;IACH,CALmC,CAMpC;IACA;IACA;;;IACA,MAAMsb,QAAQ,GAAG1b,cAAc,CAAC4T,aAAD,CAAd,IAAiC,KAAK+H,YAAL,EAAlD;;IACA,MAAMC,UAAU,GAAG,KAAKhB,YAAL,CAAkBtV,GAAlB,CAAsBsO,aAAtB,CAAnB,CAVoC,CAWpC;;;IACA,IAAIgI,UAAJ,EAAgB;MACZ,IAAIH,aAAJ,EAAmB;QACf;QACA;QACA;QACAG,UAAU,CAACH,aAAX,GAA2B,IAA3B;MACH;;MACD,OAAOG,UAAU,CAACC,OAAlB;IACH,CApBmC,CAqBpC;;;IACA,MAAMC,IAAI,GAAG;MACTL,aAAa,EAAEA,aADN;MAETI,OAAO,EAAE,IAAI5b,OAAJ,EAFA;MAGTyb;IAHS,CAAb;;IAKA,KAAKd,YAAL,CAAkBjW,GAAlB,CAAsBiP,aAAtB,EAAqCkI,IAArC;;IACA,KAAKC,wBAAL,CAA8BD,IAA9B;;IACA,OAAOA,IAAI,CAACD,OAAZ;EACH;;EACDG,cAAc,CAAClV,OAAD,EAAU;IACpB,MAAM8M,aAAa,GAAG7R,aAAa,CAAC+E,OAAD,CAAnC;;IACA,MAAMmV,WAAW,GAAG,KAAKrB,YAAL,CAAkBtV,GAAlB,CAAsBsO,aAAtB,CAApB;;IACA,IAAIqI,WAAJ,EAAiB;MACbA,WAAW,CAACJ,OAAZ,CAAoBvD,QAApB;;MACA,KAAK4D,WAAL,CAAiBtI,aAAjB;;MACA,KAAKgH,YAAL,CAAkBxU,MAAlB,CAAyBwN,aAAzB;;MACA,KAAKuI,sBAAL,CAA4BF,WAA5B;IACH;EACJ;;EACDG,QAAQ,CAACtV,OAAD,EAAUqG,MAAV,EAAkBqE,OAAlB,EAA2B;IAC/B,MAAMoC,aAAa,GAAG7R,aAAa,CAAC+E,OAAD,CAAnC;;IACA,MAAMuV,cAAc,GAAG,KAAKV,YAAL,GAAoB9F,aAA3C,CAF+B,CAG/B;IACA;IACA;;;IACA,IAAIjC,aAAa,KAAKyI,cAAtB,EAAsC;MAClC,KAAKC,uBAAL,CAA6B1I,aAA7B,EAA4C2I,OAA5C,CAAoD,CAAC,CAACC,cAAD,EAAiBV,IAAjB,CAAD,KAA4B,KAAKW,cAAL,CAAoBD,cAApB,EAAoCrP,MAApC,EAA4C2O,IAA5C,CAAhF;IACH,CAFD,MAGK;MACD,KAAKY,UAAL,CAAgBvP,MAAhB,EADC,CAED;;;MACA,IAAI,OAAOyG,aAAa,CAACxG,KAArB,KAA+B,UAAnC,EAA+C;QAC3CwG,aAAa,CAACxG,KAAd,CAAoBoE,OAApB;MACH;IACJ;EACJ;;EACD9L,WAAW,GAAG;IACV,KAAKkV,YAAL,CAAkB2B,OAAlB,CAA0B,CAACI,KAAD,EAAQ7V,OAAR,KAAoB,KAAKkV,cAAL,CAAoBlV,OAApB,CAA9C;EACH;EACD;;;EACA6U,YAAY,GAAG;IACX,OAAO,KAAK9X,SAAL,IAAkB4T,QAAzB;EACH;EACD;;;EACAmF,UAAU,GAAG;IACT,MAAMC,GAAG,GAAG,KAAKlB,YAAL,EAAZ;;IACA,OAAOkB,GAAG,CAACjN,WAAJ,IAAmBf,MAA1B;EACH;;EACDiO,eAAe,CAACC,gBAAD,EAAmB;IAC9B,IAAI,KAAK9P,OAAT,EAAkB;MACd;MACA;MACA,IAAI,KAAK0N,2BAAT,EAAsC;QAClC,OAAO,KAAKqC,0BAAL,CAAgCD,gBAAhC,IAAoD,OAApD,GAA8D,SAArE;MACH,CAFD,MAGK;QACD,OAAO,KAAK9P,OAAZ;MACH;IACJ,CAV6B,CAW9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,IAAI,KAAKyN,cAAL,IAAuB,KAAKuC,gBAAhC,EAAkD;MAC9C,OAAO,KAAKA,gBAAZ;IACH,CAtB6B,CAuB9B;IACA;IACA;IACA;;;IACA,IAAIF,gBAAgB,IAAI,KAAKG,gCAAL,CAAsCH,gBAAtC,CAAxB,EAAiF;MAC7E,OAAO,OAAP;IACH;;IACD,OAAO,SAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIC,0BAA0B,CAACD,gBAAD,EAAmB;IACzC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAQ,KAAKzB,cAAL,KAAwB;IAAE;IAA1B,GACJ,CAAC,CAACyB,gBAAgB,EAAErH,QAAlB,CAA2B,KAAK+E,sBAAL,CAA4B/C,iBAAvD,CADN;EAEH;EACD;AACJ;AACA;AACA;AACA;;;EACIwE,WAAW,CAACpV,OAAD,EAAUqG,MAAV,EAAkB;IACzBrG,OAAO,CAACJ,SAAR,CAAkByW,MAAlB,CAAyB,aAAzB,EAAwC,CAAC,CAAChQ,MAA1C;IACArG,OAAO,CAACJ,SAAR,CAAkByW,MAAlB,CAAyB,mBAAzB,EAA8ChQ,MAAM,KAAK,OAAzD;IACArG,OAAO,CAACJ,SAAR,CAAkByW,MAAlB,CAAyB,sBAAzB,EAAiDhQ,MAAM,KAAK,UAA5D;IACArG,OAAO,CAACJ,SAAR,CAAkByW,MAAlB,CAAyB,mBAAzB,EAA8ChQ,MAAM,KAAK,OAAzD;IACArG,OAAO,CAACJ,SAAR,CAAkByW,MAAlB,CAAyB,qBAAzB,EAAgDhQ,MAAM,KAAK,SAA3D;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACIuP,UAAU,CAACvP,MAAD,EAASiQ,iBAAiB,GAAG,KAA7B,EAAoC;IAC1C,KAAKpN,OAAL,CAAaiB,iBAAb,CAA+B,MAAM;MACjC,KAAKhE,OAAL,GAAeE,MAAf;MACA,KAAKwN,2BAAL,GAAmCxN,MAAM,KAAK,OAAX,IAAsBiQ,iBAAzD,CAFiC,CAGjC;MACA;MACA;MACA;MACA;;MACA,IAAI,KAAK9B,cAAL,KAAwB;MAAE;MAA9B,EAAyE;QACrEjC,YAAY,CAAC,KAAKgE,gBAAN,CAAZ;QACA,MAAMC,EAAE,GAAG,KAAK3C,2BAAL,GAAmCxD,eAAnC,GAAqD,CAAhE;QACA,KAAKkG,gBAAL,GAAwBzH,UAAU,CAAC,MAAO,KAAK3I,OAAL,GAAe,IAAvB,EAA8BqQ,EAA9B,CAAlC;MACH;IACJ,CAbD;EAcH;EACD;AACJ;AACA;AACA;AACA;;;EACIlC,QAAQ,CAAC/P,KAAD,EAAQvE,OAAR,EAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA,MAAMmV,WAAW,GAAG,KAAKrB,YAAL,CAAkBtV,GAAlB,CAAsBwB,OAAtB,CAApB;;IACA,MAAMiW,gBAAgB,GAAGhd,eAAe,CAACsL,KAAD,CAAxC;;IACA,IAAI,CAAC4Q,WAAD,IAAiB,CAACA,WAAW,CAACR,aAAb,IAA8B3U,OAAO,KAAKiW,gBAA/D,EAAkF;MAC9E;IACH;;IACD,KAAKN,cAAL,CAAoB3V,OAApB,EAA6B,KAAKgW,eAAL,CAAqBC,gBAArB,CAA7B,EAAqEd,WAArE;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIZ,OAAO,CAAChQ,KAAD,EAAQvE,OAAR,EAAiB;IACpB;IACA;IACA,MAAMmV,WAAW,GAAG,KAAKrB,YAAL,CAAkBtV,GAAlB,CAAsBwB,OAAtB,CAApB;;IACA,IAAI,CAACmV,WAAD,IACCA,WAAW,CAACR,aAAZ,IACGpQ,KAAK,CAACkS,aAAN,YAA+BC,IADlC,IAEG1W,OAAO,CAAC4O,QAAR,CAAiBrK,KAAK,CAACkS,aAAvB,CAHR,EAGgD;MAC5C;IACH;;IACD,KAAKrB,WAAL,CAAiBpV,OAAjB;;IACA,KAAK2W,WAAL,CAAiBxB,WAAjB,EAA8B,IAA9B;EACH;;EACDwB,WAAW,CAAC3B,IAAD,EAAO3O,MAAP,EAAe;IACtB,IAAI2O,IAAI,CAACD,OAAL,CAAa6B,SAAb,CAAuBva,MAA3B,EAAmC;MAC/B,KAAK6M,OAAL,CAAa2N,GAAb,CAAiB,MAAM7B,IAAI,CAACD,OAAL,CAAa1Q,IAAb,CAAkBgC,MAAlB,CAAvB;IACH;EACJ;;EACD4O,wBAAwB,CAACE,WAAD,EAAc;IAClC,IAAI,CAAC,KAAKnY,SAAL,CAAe8C,SAApB,EAA+B;MAC3B;IACH;;IACD,MAAM8U,QAAQ,GAAGO,WAAW,CAACP,QAA7B;IACA,MAAMkC,sBAAsB,GAAG,KAAK9C,2BAAL,CAAiCxV,GAAjC,CAAqCoW,QAArC,KAAkD,CAAjF;;IACA,IAAI,CAACkC,sBAAL,EAA6B;MACzB,KAAK5N,OAAL,CAAaiB,iBAAb,CAA+B,MAAM;QACjCyK,QAAQ,CAACvK,gBAAT,CAA0B,OAA1B,EAAmC,KAAK+J,6BAAxC,EAAuEX,2BAAvE;QACAmB,QAAQ,CAACvK,gBAAT,CAA0B,MAA1B,EAAkC,KAAK+J,6BAAvC,EAAsEX,2BAAtE;MACH,CAHD;IAIH;;IACD,KAAKO,2BAAL,CAAiCnW,GAAjC,CAAqC+W,QAArC,EAA+CkC,sBAAsB,GAAG,CAAxE,EAZkC,CAalC;;;IACA,IAAI,EAAE,KAAK/C,sBAAP,KAAkC,CAAtC,EAAyC;MACrC;MACA;MACA,KAAK7K,OAAL,CAAaiB,iBAAb,CAA+B,MAAM;QACjC,MAAMpC,MAAM,GAAG,KAAK+N,UAAL,EAAf;;QACA/N,MAAM,CAACsC,gBAAP,CAAwB,OAAxB,EAAiC,KAAK4J,oBAAtC;MACH,CAHD,EAHqC,CAOrC;;;MACA,KAAKN,sBAAL,CAA4BtC,gBAA5B,CACK3N,IADL,CACU3I,SAAS,CAAC,KAAKoZ,0BAAN,CADnB,EAEK/R,SAFL,CAEe2U,QAAQ,IAAI;QACvB,KAAKnB,UAAL,CAAgBmB,QAAhB,EAA0B;QAAK;QAA/B;MACH,CAJD;IAKH;EACJ;;EACD1B,sBAAsB,CAACF,WAAD,EAAc;IAChC,MAAMP,QAAQ,GAAGO,WAAW,CAACP,QAA7B;;IACA,IAAI,KAAKZ,2BAAL,CAAiChW,GAAjC,CAAqC4W,QAArC,CAAJ,EAAoD;MAChD,MAAMkC,sBAAsB,GAAG,KAAK9C,2BAAL,CAAiCxV,GAAjC,CAAqCoW,QAArC,CAA/B;;MACA,IAAIkC,sBAAsB,GAAG,CAA7B,EAAgC;QAC5B,KAAK9C,2BAAL,CAAiCnW,GAAjC,CAAqC+W,QAArC,EAA+CkC,sBAAsB,GAAG,CAAxE;MACH,CAFD,MAGK;QACDlC,QAAQ,CAAC1K,mBAAT,CAA6B,OAA7B,EAAsC,KAAKkK,6BAA3C,EAA0EX,2BAA1E;QACAmB,QAAQ,CAAC1K,mBAAT,CAA6B,MAA7B,EAAqC,KAAKkK,6BAA1C,EAAyEX,2BAAzE;;QACA,KAAKO,2BAAL,CAAiC1U,MAAjC,CAAwCsV,QAAxC;MACH;IACJ,CAZ+B,CAahC;;;IACA,IAAI,CAAC,GAAE,KAAKb,sBAAZ,EAAoC;MAChC,MAAMhM,MAAM,GAAG,KAAK+N,UAAL,EAAf;;MACA/N,MAAM,CAACmC,mBAAP,CAA2B,OAA3B,EAAoC,KAAK+J,oBAAzC,EAFgC,CAGhC;;MACA,KAAKE,0BAAL,CAAgC9P,IAAhC,GAJgC,CAKhC;;;MACAkO,YAAY,CAAC,KAAK2B,qBAAN,CAAZ;MACA3B,YAAY,CAAC,KAAKgE,gBAAN,CAAZ;IACH;EACJ;EACD;;;EACAZ,cAAc,CAAC3V,OAAD,EAAUqG,MAAV,EAAkB8O,WAAlB,EAA+B;IACzC,KAAKC,WAAL,CAAiBpV,OAAjB,EAA0BqG,MAA1B;;IACA,KAAKsQ,WAAL,CAAiBxB,WAAjB,EAA8B9O,MAA9B;;IACA,KAAK8P,gBAAL,GAAwB9P,MAAxB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACImP,uBAAuB,CAACxV,OAAD,EAAU;IAC7B,MAAMgX,OAAO,GAAG,EAAhB;;IACA,KAAKlD,YAAL,CAAkB2B,OAAlB,CAA0B,CAACT,IAAD,EAAOU,cAAP,KAA0B;MAChD,IAAIA,cAAc,KAAK1V,OAAnB,IAA+BgV,IAAI,CAACL,aAAL,IAAsBe,cAAc,CAAC9G,QAAf,CAAwB5O,OAAxB,CAAzD,EAA4F;QACxFgX,OAAO,CAACjb,IAAR,CAAa,CAAC2Z,cAAD,EAAiBV,IAAjB,CAAb;MACH;IACJ,CAJD;;IAKA,OAAOgC,OAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIZ,gCAAgC,CAACH,gBAAD,EAAmB;IAC/C,MAAM;MAAErF,iBAAiB,EAAEqG,gBAArB;MAAuC1F;IAAvC,IAA8D,KAAKoC,sBAAzE,CAD+C,CAE/C;IACA;IACA;;IACA,IAAIpC,kBAAkB,KAAK,OAAvB,IACA,CAAC0F,gBADD,IAEAA,gBAAgB,KAAKhB,gBAFrB,IAGCA,gBAAgB,CAAC7O,QAAjB,KAA8B,OAA9B,IAAyC6O,gBAAgB,CAAC7O,QAAjB,KAA8B,UAHxE,IAIA6O,gBAAgB,CAAClU,QAJrB,EAI+B;MAC3B,OAAO,KAAP;IACH;;IACD,MAAMmV,MAAM,GAAGjB,gBAAgB,CAACiB,MAAhC;;IACA,IAAIA,MAAJ,EAAY;MACR,KAAK,IAAInY,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmY,MAAM,CAAC7a,MAA3B,EAAmC0C,CAAC,EAApC,EAAwC;QACpC,IAAImY,MAAM,CAACnY,CAAD,CAAN,CAAU6P,QAAV,CAAmBqI,gBAAnB,CAAJ,EAA0C;UACtC,OAAO,IAAP;QACH;MACJ;IACJ;;IACD,OAAO,KAAP;EACH;;AAtWc;;AAwWnBvD,YAAY,CAACjT,IAAb;EAAA,iBAAyGiT,YAAzG,EAj9DgGzb,EAi9DhG,UAAuIA,EAAE,CAACuU,MAA1I,GAj9DgGvU,EAi9DhG,UAA6Ja,EAAE,CAAC4H,QAAhK,GAj9DgGzI,EAi9DhG,UAAqLwY,qBAArL,GAj9DgGxY,EAi9DhG,UAAuND,QAAvN,MAj9DgGC,EAi9DhG,UAA4Pub,6BAA5P;AAAA;;AACAE,YAAY,CAAC/S,KAAb,kBAl9DgG1I,EAk9DhG;EAAA,OAA6Gyb,YAA7G;EAAA,SAA6GA,YAA7G;EAAA,YAAuI;AAAvI;;AACA;EAAA,mDAn9DgGzb,EAm9DhG,mBAA2Fyb,YAA3F,EAAqH,CAAC;IAC1G9S,IAAI,EAAExI,UADoG;IAE1GyI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAFoG,CAAD,CAArH,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAE3I,EAAE,CAACuU;IAAX,CAAD,EAAsB;MAAE5L,IAAI,EAAE9H,EAAE,CAAC4H;IAAX,CAAtB,EAA6C;MAAEE,IAAI,EAAE6P;IAAR,CAA7C,EAA8E;MAAE7P,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC3IJ,IAAI,EAAElI;MADqI,CAAD,EAE3I;QACCkI,IAAI,EAAEvI,MADP;QAECwI,IAAI,EAAE,CAAC7I,QAAD;MAFP,CAF2I;IAA/B,CAA9E,EAK3B;MAAE4I,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClCJ,IAAI,EAAElI;MAD4B,CAAD,EAElC;QACCkI,IAAI,EAAEvI,MADP;QAECwI,IAAI,EAAE,CAAC2S,6BAAD;MAFP,CAFkC;IAA/B,CAL2B,CAAP;EAUlB,CAbxB;AAAA;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM2D,eAAN,CAAsB;EAClBra,WAAW,CAAC4P,WAAD,EAAc0K,aAAd,EAA6B;IACpC,KAAK1K,WAAL,GAAmBA,WAAnB;IACA,KAAK0K,aAAL,GAAqBA,aAArB;IACA,KAAKC,YAAL,GAAoB,IAApB;IACA,KAAKC,cAAL,GAAsB,IAAI3e,YAAJ,EAAtB;EACH;;EACc,IAAX4e,WAAW,GAAG;IACd,OAAO,KAAKF,YAAZ;EACH;;EACDG,eAAe,GAAG;IACd,MAAMxX,OAAO,GAAG,KAAK0M,WAAL,CAAiBI,aAAjC;IACA,KAAK2K,oBAAL,GAA4B,KAAKL,aAAL,CACvB1C,OADuB,CACf1U,OADe,EACNA,OAAO,CAACO,QAAR,KAAqB,CAArB,IAA0BP,OAAO,CAAC2G,YAAR,CAAqB,wBAArB,CADpB,EAEvBvE,SAFuB,CAEbiE,MAAM,IAAI;MACrB,KAAKgR,YAAL,GAAoBhR,MAApB;MACA,KAAKiR,cAAL,CAAoBI,IAApB,CAAyBrR,MAAzB;IACH,CAL2B,CAA5B;EAMH;;EACDzH,WAAW,GAAG;IACV,KAAKwY,aAAL,CAAmBlC,cAAnB,CAAkC,KAAKxI,WAAvC;;IACA,IAAI,KAAK+K,oBAAT,EAA+B;MAC3B,KAAKA,oBAAL,CAA0BhU,WAA1B;IACH;EACJ;;AAxBiB;;AA0BtB0T,eAAe,CAAC1W,IAAhB;EAAA,iBAA4G0W,eAA5G,EApgEgGlf,EAogEhG,mBAA6IA,EAAE,CAACsV,UAAhJ,GApgEgGtV,EAogEhG,mBAAuKyb,YAAvK;AAAA;;AACAyD,eAAe,CAAC3J,IAAhB,kBArgEgGvV,EAqgEhG;EAAA,MAAgGkf,eAAhG;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDAtgEgGlf,EAsgEhG,mBAA2Fkf,eAA3F,EAAwH,CAAC;IAC7GvW,IAAI,EAAErI,SADuG;IAE7GsI,IAAI,EAAE,CAAC;MACC4M,QAAQ,EAAE,oDADX;MAECC,QAAQ,EAAE;IAFX,CAAD;EAFuG,CAAD,CAAxH,EAM4B,YAAY;IAAE,OAAO,CAAC;MAAE9M,IAAI,EAAE3I,EAAE,CAACsV;IAAX,CAAD,EAA0B;MAAE3M,IAAI,EAAE8S;IAAR,CAA1B,CAAP;EAA2D,CANrG,EAMuH;IAAE4D,cAAc,EAAE,CAAC;MAC1H1W,IAAI,EAAEhI;IADoH,CAAD;EAAlB,CANvH;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAM+e,wBAAwB,GAAG,kCAAjC;AACA;;AACA,MAAMC,wBAAwB,GAAG,kCAAjC;AACA;;AACA,MAAMC,mCAAmC,GAAG,0BAA5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,wBAAN,CAA+B;EAC3Bhb,WAAW,CAACE,SAAD,EAAY2T,QAAZ,EAAsB;IAC7B,KAAK3T,SAAL,GAAiBA,SAAjB;IACA,KAAKD,SAAL,GAAiB4T,QAAjB;IACA,KAAKoH,uBAAL,GAA+B7f,MAAM,CAACkD,kBAAD,CAAN,CAC1BgY,OAD0B,CAClB,yBADkB,EAE1BhR,SAF0B,CAEhB,MAAM;MACjB,IAAI,KAAK4V,2BAAT,EAAsC;QAClC,KAAKA,2BAAL,GAAmC,KAAnC;;QACA,KAAKC,oCAAL;MACH;IACJ,CAP8B,CAA/B;EAQH;EACD;;;EACAC,mBAAmB,GAAG;IAClB,IAAI,CAAC,KAAKlb,SAAL,CAAe8C,SAApB,EAA+B;MAC3B,OAAO;MAAE;MAAT;IACH,CAHiB,CAIlB;IACA;IACA;;;IACA,MAAMqY,WAAW,GAAG,KAAKpb,SAAL,CAAemC,aAAf,CAA6B,KAA7B,CAApB;;IACAiZ,WAAW,CAACzY,KAAZ,CAAkB0Y,eAAlB,GAAoC,YAApC;IACAD,WAAW,CAACzY,KAAZ,CAAkB2Y,QAAlB,GAA6B,UAA7B;;IACA,KAAKtb,SAAL,CAAegD,IAAf,CAAoBV,WAApB,CAAgC8Y,WAAhC,EAVkB,CAWlB;IACA;IACA;IACA;;;IACA,MAAMG,cAAc,GAAG,KAAKvb,SAAL,CAAe+L,WAAf,IAA8Bf,MAArD;IACA,MAAMwQ,aAAa,GAAGD,cAAc,IAAIA,cAAc,CAACxR,gBAAjC,GAChBwR,cAAc,CAACxR,gBAAf,CAAgCqR,WAAhC,CADgB,GAEhB,IAFN;IAGA,MAAMK,aAAa,GAAG,CAAED,aAAa,IAAIA,aAAa,CAACH,eAAhC,IAAoD,EAArD,EAAyDK,OAAzD,CAAiE,IAAjE,EAAuE,EAAvE,CAAtB;IACAN,WAAW,CAACxZ,MAAZ;;IACA,QAAQ6Z,aAAR;MACI;MACA,KAAK,YAAL,CAFJ,CAGI;;MACA,KAAK,eAAL;MACA,KAAK,eAAL;QACI,OAAO;QAAE;QAAT;MACJ;;MACA,KAAK,kBAAL,CARJ,CASI;;MACA,KAAK,kBAAL;QACI,OAAO;QAAE;QAAT;IAXR;;IAaA,OAAO;IAAE;IAAT;EACH;;EACD5Z,WAAW,GAAG;IACV,KAAKmZ,uBAAL,CAA6BtU,WAA7B;EACH;EACD;;;EACAwU,oCAAoC,GAAG;IACnC,IAAI,CAAC,KAAKD,2BAAN,IAAqC,KAAKhb,SAAL,CAAe8C,SAApD,IAAiE,KAAK/C,SAAL,CAAegD,IAApF,EAA0F;MACtF,MAAM2Y,WAAW,GAAG,KAAK3b,SAAL,CAAegD,IAAf,CAAoBH,SAAxC;MACA8Y,WAAW,CAAC/Z,MAAZ,CAAmBkZ,mCAAnB,EAAwDF,wBAAxD,EAAkFC,wBAAlF;MACA,KAAKI,2BAAL,GAAmC,IAAnC;MACA,MAAMW,IAAI,GAAG,KAAKT,mBAAL,EAAb;;MACA,IAAIS,IAAI,KAAK;MAAE;MAAf,EAAsD;QAClDD,WAAW,CAAC7Y,GAAZ,CAAgBgY,mCAAhB,EAAqDF,wBAArD;MACH,CAFD,MAGK,IAAIgB,IAAI,KAAK;MAAE;MAAf,EAAsD;QACvDD,WAAW,CAAC7Y,GAAZ,CAAgBgY,mCAAhB,EAAqDD,wBAArD;MACH;IACJ;EACJ;;AAnE0B;;AAqE/BE,wBAAwB,CAACrX,IAAzB;EAAA,iBAAqHqX,wBAArH,EA7mEgG7f,EA6mEhG,UAA+Ja,EAAE,CAAC4H,QAAlK,GA7mEgGzI,EA6mEhG,UAAuLD,QAAvL;AAAA;;AACA8f,wBAAwB,CAACnX,KAAzB,kBA9mEgG1I,EA8mEhG;EAAA,OAAyH6f,wBAAzH;EAAA,SAAyHA,wBAAzH;EAAA,YAA+J;AAA/J;;AACA;EAAA,mDA/mEgG7f,EA+mEhG,mBAA2F6f,wBAA3F,EAAiI,CAAC;IACtHlX,IAAI,EAAExI,UADgH;IAEtHyI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAFgH,CAAD,CAAjI,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAE9H,EAAE,CAAC4H;IAAX,CAAD,EAAwB;MAAEE,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACrFJ,IAAI,EAAEvI,MAD+E;QAErFwI,IAAI,EAAE,CAAC7I,QAAD;MAF+E,CAAD;IAA/B,CAAxB,CAAP;EAGlB,CANxB;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM4gB,UAAN,CAAiB;EACb9b,WAAW,CAAC+b,wBAAD,EAA2B;IAClCA,wBAAwB,CAACZ,oCAAzB;EACH;;AAHY;;AAKjBW,UAAU,CAACnY,IAAX;EAAA,iBAAuGmY,UAAvG,EAnoEgG3gB,EAmoEhG,UAAmI6f,wBAAnI;AAAA;;AACAc,UAAU,CAACE,IAAX,kBApoEgG7gB,EAooEhG;EAAA,MAAwG2gB,UAAxG;EAAA,eAAmI7F,WAAnI,EAAgJtG,YAAhJ,EAA8J0K,eAA9J;EAAA,UAA0Lhc,eAA1L;EAAA,UAAsN4X,WAAtN,EAAmOtG,YAAnO,EAAiP0K,eAAjP;AAAA;AACAyB,UAAU,CAACG,IAAX,kBAroEgG9gB,EAqoEhG;EAAA,UAA8HkD,eAA9H;AAAA;;AACA;EAAA,mDAtoEgGlD,EAsoEhG,mBAA2F2gB,UAA3F,EAAmH,CAAC;IACxGhY,IAAI,EAAE/H,QADkG;IAExGgI,IAAI,EAAE,CAAC;MACCmY,OAAO,EAAE,CAAC7d,eAAD,CADV;MAEC8d,YAAY,EAAE,CAAClG,WAAD,EAActG,YAAd,EAA4B0K,eAA5B,CAFf;MAGC+B,OAAO,EAAE,CAACnG,WAAD,EAActG,YAAd,EAA4B0K,eAA5B;IAHV,CAAD;EAFkG,CAAD,CAAnH,EAO4B,YAAY;IAAE,OAAO,CAAC;MAAEvW,IAAI,EAAEkX;IAAR,CAAD,CAAP;EAA8C,CAPxF;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASc,UAAT,EAAqB9S,0BAArB,EAAiDjJ,aAAjD,EAAgEF,8BAAhE,EAAgGD,yBAAhG,EAA2HqW,WAA3H,EAAwIoE,eAAxI,EAAyJ1K,YAAzJ,EAAuKkB,qBAAvK,EAA8L0B,4BAA9L,EAA4Nf,mCAA5N,EAAiQkF,6BAAjQ,EAAgSnF,yBAAhS,EAA2TpI,eAA3T,EAA4UyN,YAA5U,EAA0V3K,SAA1V,EAAqWsD,gBAArW,EAAuXyL,wBAAvX,EAAiZ3H,uCAAjZ,EAA0bD,+BAA1b,EAA2dO,qBAA3d,EAAkfhK,oBAAlf,EAAwgBF,iBAAxgB,EAA2hBsL,8BAA3hB,EAA2jBH,4BAA3jB,EAAylBE,oCAAzlB,EAA+nB1Q,cAA/nB,EAA+oB4Q,aAA/oB,EAA8pBrV,qBAA9pB,EAAqrB8S,+BAArrB,EAAstBI,gCAAttB"}, "metadata": {}, "sourceType": "module"}