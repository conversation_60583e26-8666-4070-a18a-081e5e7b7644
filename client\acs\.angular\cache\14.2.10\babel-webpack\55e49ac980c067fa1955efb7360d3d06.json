{"ast": null, "code": "let nextHandle = 1;\n\nconst RESOLVED = (() => Promise.resolve())();\n\nconst activeHandles = {};\n\nfunction findAndClearHandle(handle) {\n  if (handle in activeHandles) {\n    delete activeHandles[handle];\n    return true;\n  }\n\n  return false;\n}\n\nexport const Immediate = {\n  setImmediate(cb) {\n    const handle = nextHandle++;\n    activeHandles[handle] = true;\n    RESOLVED.then(() => findAndClearHandle(handle) && cb());\n    return handle;\n  },\n\n  clearImmediate(handle) {\n    findAndClearHandle(handle);\n  }\n\n};\nexport const TestTools = {\n  pending() {\n    return Object.keys(activeHandles).length;\n  }\n\n};", "map": {"version": 3, "names": ["nextH<PERSON>le", "RESOLVED", "Promise", "resolve", "active<PERSON><PERSON><PERSON>", "findAndClearHandle", "handle", "Immediate", "setImmediate", "cb", "then", "clearImmediate", "TestTools", "pending", "Object", "keys", "length"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/Immediate.js"], "sourcesContent": ["let nextHandle = 1;\nconst RESOLVED = (() => Promise.resolve())();\nconst activeHandles = {};\nfunction findAndClearHandle(handle) {\n    if (handle in activeHandles) {\n        delete activeHandles[handle];\n        return true;\n    }\n    return false;\n}\nexport const Immediate = {\n    setImmediate(cb) {\n        const handle = nextHandle++;\n        activeHandles[handle] = true;\n        RESOLVED.then(() => findAndClearHandle(handle) && cb());\n        return handle;\n    },\n    clearImmediate(handle) {\n        findAndClearHandle(handle);\n    },\n};\nexport const TestTools = {\n    pending() {\n        return Object.keys(activeHandles).length;\n    }\n};\n"], "mappings": "AAAA,IAAIA,UAAU,GAAG,CAAjB;;AACA,MAAMC,QAAQ,GAAG,CAAC,MAAMC,OAAO,CAACC,OAAR,EAAP,GAAjB;;AACA,MAAMC,aAAa,GAAG,EAAtB;;AACA,SAASC,kBAAT,CAA4BC,MAA5B,EAAoC;EAChC,IAAIA,MAAM,IAAIF,aAAd,EAA6B;IACzB,OAAOA,aAAa,CAACE,MAAD,CAApB;IACA,OAAO,IAAP;EACH;;EACD,OAAO,KAAP;AACH;;AACD,OAAO,MAAMC,SAAS,GAAG;EACrBC,YAAY,CAACC,EAAD,EAAK;IACb,MAAMH,MAAM,GAAGN,UAAU,EAAzB;IACAI,aAAa,CAACE,MAAD,CAAb,GAAwB,IAAxB;IACAL,QAAQ,CAACS,IAAT,CAAc,MAAML,kBAAkB,CAACC,MAAD,CAAlB,IAA8BG,EAAE,EAApD;IACA,OAAOH,MAAP;EACH,CANoB;;EAOrBK,cAAc,CAACL,MAAD,EAAS;IACnBD,kBAAkB,CAACC,MAAD,CAAlB;EACH;;AAToB,CAAlB;AAWP,OAAO,MAAMM,SAAS,GAAG;EACrBC,OAAO,GAAG;IACN,OAAOC,MAAM,CAACC,IAAP,CAAYX,aAAZ,EAA2BY,MAAlC;EACH;;AAHoB,CAAlB"}, "metadata": {}, "sourceType": "module"}