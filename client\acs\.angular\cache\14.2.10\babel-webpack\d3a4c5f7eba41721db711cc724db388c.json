{"ast": null, "code": "'use strict';\n\nvar forEach = require('for-each');\n\nvar availableTypedArrays = require('available-typed-arrays');\n\nvar callBound = require('call-bind/callBound');\n\nvar gOPD = require('gopd');\n\nvar $toString = callBound('Object.prototype.toString');\n\nvar hasToStringTag = require('has-tostringtag/shams')();\n\nvar g = typeof globalThis === 'undefined' ? global : globalThis;\nvar typedArrays = availableTypedArrays();\nvar $slice = callBound('String.prototype.slice');\nvar toStrTags = {};\nvar getPrototypeOf = Object.getPrototypeOf; // require('getprototypeof');\n\nif (hasToStringTag && gOPD && getPrototypeOf) {\n  forEach(typedArrays, function (typedArray) {\n    if (typeof g[typedArray] === 'function') {\n      var arr = new g[typedArray]();\n\n      if (Symbol.toStringTag in arr) {\n        var proto = getPrototypeOf(arr);\n        var descriptor = gOPD(proto, Symbol.toStringTag);\n\n        if (!descriptor) {\n          var superProto = getPrototypeOf(proto);\n          descriptor = gOPD(superProto, Symbol.toStringTag);\n        }\n\n        toStrTags[typedArray] = descriptor.get;\n      }\n    }\n  });\n}\n\nvar tryTypedArrays = function tryAllTypedArrays(value) {\n  var foundName = false;\n  forEach(toStrTags, function (getter, typedArray) {\n    if (!foundName) {\n      try {\n        var name = getter.call(value);\n\n        if (name === typedArray) {\n          foundName = name;\n        }\n      } catch (e) {}\n    }\n  });\n  return foundName;\n};\n\nvar isTypedArray = require('is-typed-array');\n\nmodule.exports = function whichTypedArray(value) {\n  if (!isTypedArray(value)) {\n    return false;\n  }\n\n  if (!hasToStringTag || !(Symbol.toStringTag in value)) {\n    return $slice($toString(value), 8, -1);\n  }\n\n  return tryTypedArrays(value);\n};", "map": {"version": 3, "names": ["for<PERSON>ach", "require", "availableTypedArrays", "callBound", "gOPD", "$toString", "hasToStringTag", "g", "globalThis", "global", "typedArrays", "$slice", "toStrTags", "getPrototypeOf", "Object", "typedArray", "arr", "Symbol", "toStringTag", "proto", "descriptor", "superProto", "get", "tryTypedArrays", "tryAllTypedArrays", "value", "<PERSON><PERSON><PERSON>", "getter", "name", "call", "e", "isTypedArray", "module", "exports", "whichTypedArray"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/which-typed-array/index.js"], "sourcesContent": ["'use strict';\n\nvar forEach = require('for-each');\nvar availableTypedArrays = require('available-typed-arrays');\nvar callBound = require('call-bind/callBound');\nvar gOPD = require('gopd');\n\nvar $toString = callBound('Object.prototype.toString');\nvar hasToStringTag = require('has-tostringtag/shams')();\n\nvar g = typeof globalThis === 'undefined' ? global : globalThis;\nvar typedArrays = availableTypedArrays();\n\nvar $slice = callBound('String.prototype.slice');\nvar toStrTags = {};\nvar getPrototypeOf = Object.getPrototypeOf; // require('getprototypeof');\nif (hasToStringTag && gOPD && getPrototypeOf) {\n\tforEach(typedArrays, function (typedArray) {\n\t\tif (typeof g[typedArray] === 'function') {\n\t\t\tvar arr = new g[typedArray]();\n\t\t\tif (Symbol.toStringTag in arr) {\n\t\t\t\tvar proto = getPrototypeOf(arr);\n\t\t\t\tvar descriptor = gOPD(proto, Symbol.toStringTag);\n\t\t\t\tif (!descriptor) {\n\t\t\t\t\tvar superProto = getPrototypeOf(proto);\n\t\t\t\t\tdescriptor = gOPD(superProto, Symbol.toStringTag);\n\t\t\t\t}\n\t\t\t\ttoStrTags[typedArray] = descriptor.get;\n\t\t\t}\n\t\t}\n\t});\n}\n\nvar tryTypedArrays = function tryAllTypedArrays(value) {\n\tvar foundName = false;\n\tforEach(toStrTags, function (getter, typedArray) {\n\t\tif (!foundName) {\n\t\t\ttry {\n\t\t\t\tvar name = getter.call(value);\n\t\t\t\tif (name === typedArray) {\n\t\t\t\t\tfoundName = name;\n\t\t\t\t}\n\t\t\t} catch (e) {}\n\t\t}\n\t});\n\treturn foundName;\n};\n\nvar isTypedArray = require('is-typed-array');\n\nmodule.exports = function whichTypedArray(value) {\n\tif (!isTypedArray(value)) { return false; }\n\tif (!hasToStringTag || !(Symbol.toStringTag in value)) { return $slice($toString(value), 8, -1); }\n\treturn tryTypedArrays(value);\n};\n"], "mappings": "AAAA;;AAEA,IAAIA,OAAO,GAAGC,OAAO,CAAC,UAAD,CAArB;;AACA,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,wBAAD,CAAlC;;AACA,IAAIE,SAAS,GAAGF,OAAO,CAAC,qBAAD,CAAvB;;AACA,IAAIG,IAAI,GAAGH,OAAO,CAAC,MAAD,CAAlB;;AAEA,IAAII,SAAS,GAAGF,SAAS,CAAC,2BAAD,CAAzB;;AACA,IAAIG,cAAc,GAAGL,OAAO,CAAC,uBAAD,CAAP,EAArB;;AAEA,IAAIM,CAAC,GAAG,OAAOC,UAAP,KAAsB,WAAtB,GAAoCC,MAApC,GAA6CD,UAArD;AACA,IAAIE,WAAW,GAAGR,oBAAoB,EAAtC;AAEA,IAAIS,MAAM,GAAGR,SAAS,CAAC,wBAAD,CAAtB;AACA,IAAIS,SAAS,GAAG,EAAhB;AACA,IAAIC,cAAc,GAAGC,MAAM,CAACD,cAA5B,C,CAA4C;;AAC5C,IAAIP,cAAc,IAAIF,IAAlB,IAA0BS,cAA9B,EAA8C;EAC7Cb,OAAO,CAACU,WAAD,EAAc,UAAUK,UAAV,EAAsB;IAC1C,IAAI,OAAOR,CAAC,CAACQ,UAAD,CAAR,KAAyB,UAA7B,EAAyC;MACxC,IAAIC,GAAG,GAAG,IAAIT,CAAC,CAACQ,UAAD,CAAL,EAAV;;MACA,IAAIE,MAAM,CAACC,WAAP,IAAsBF,GAA1B,EAA+B;QAC9B,IAAIG,KAAK,GAAGN,cAAc,CAACG,GAAD,CAA1B;QACA,IAAII,UAAU,GAAGhB,IAAI,CAACe,KAAD,EAAQF,MAAM,CAACC,WAAf,CAArB;;QACA,IAAI,CAACE,UAAL,EAAiB;UAChB,IAAIC,UAAU,GAAGR,cAAc,CAACM,KAAD,CAA/B;UACAC,UAAU,GAAGhB,IAAI,CAACiB,UAAD,EAAaJ,MAAM,CAACC,WAApB,CAAjB;QACA;;QACDN,SAAS,CAACG,UAAD,CAAT,GAAwBK,UAAU,CAACE,GAAnC;MACA;IACD;EACD,CAbM,CAAP;AAcA;;AAED,IAAIC,cAAc,GAAG,SAASC,iBAAT,CAA2BC,KAA3B,EAAkC;EACtD,IAAIC,SAAS,GAAG,KAAhB;EACA1B,OAAO,CAACY,SAAD,EAAY,UAAUe,MAAV,EAAkBZ,UAAlB,EAA8B;IAChD,IAAI,CAACW,SAAL,EAAgB;MACf,IAAI;QACH,IAAIE,IAAI,GAAGD,MAAM,CAACE,IAAP,CAAYJ,KAAZ,CAAX;;QACA,IAAIG,IAAI,KAAKb,UAAb,EAAyB;UACxBW,SAAS,GAAGE,IAAZ;QACA;MACD,CALD,CAKE,OAAOE,CAAP,EAAU,CAAE;IACd;EACD,CATM,CAAP;EAUA,OAAOJ,SAAP;AACA,CAbD;;AAeA,IAAIK,YAAY,GAAG9B,OAAO,CAAC,gBAAD,CAA1B;;AAEA+B,MAAM,CAACC,OAAP,GAAiB,SAASC,eAAT,CAAyBT,KAAzB,EAAgC;EAChD,IAAI,CAACM,YAAY,CAACN,KAAD,CAAjB,EAA0B;IAAE,OAAO,KAAP;EAAe;;EAC3C,IAAI,CAACnB,cAAD,IAAmB,EAAEW,MAAM,CAACC,WAAP,IAAsBO,KAAxB,CAAvB,EAAuD;IAAE,OAAOd,MAAM,CAACN,SAAS,CAACoB,KAAD,CAAV,EAAmB,CAAnB,EAAsB,CAAC,CAAvB,CAAb;EAAyC;;EAClG,OAAOF,cAAc,CAACE,KAAD,CAArB;AACA,CAJD"}, "metadata": {}, "sourceType": "script"}