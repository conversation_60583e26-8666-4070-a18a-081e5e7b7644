{"ast": null, "code": "/**\n * @license Angular v14.3.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\nimport { ɵDomAdapter, ɵsetRootDomAdapter, ɵparseCookieValue, ɵgetDOM, DOCUMENT, ɵPLATFORM_BROWSER_ID, XhrFactory, CommonModule } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, ApplicationInitStatus, APP_INITIALIZER, Injector, ɵglobal, Injectable, Inject, ViewEncapsulation, APP_ID, RendererStyleFlags2, ɵinternalCreateApplication, ErrorHandler, ɵsetDocument, PLATFORM_ID, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, ɵTESTABILITY_GETTER, ɵTESTABILITY, Testability, NgZone, TestabilityRegistry, ɵINJECTOR_SCOPE, RendererFactory2, ApplicationModule, NgModule, Optional, SkipSelf, ɵɵinject, ApplicationRef, inject, ɵConsole, forwardRef, SecurityContext, ɵallowSanitizationBypassAndThrow, ɵunwrapSafeValue, ɵ_sanitizeUrl, ɵ_sanitizeHtml, ɵbypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl, Version } from '@angular/core';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Provides DOM operations in any browser environment.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n\nclass GenericBrowserDomAdapter extends ɵDomAdapter {\n  constructor() {\n    super(...arguments);\n    this.supportsDOMEvents = true;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n\n/* tslint:disable:requireParameterType no-console */\n\n\nclass BrowserDomAdapter extends GenericBrowserDomAdapter {\n  static makeCurrent() {\n    ɵsetRootDomAdapter(new BrowserDomAdapter());\n  }\n\n  onAndCancel(el, evt, listener) {\n    el.addEventListener(evt, listener, false); // Needed to follow Dart's subscription semantic, until fix of\n    // https://code.google.com/p/dart/issues/detail?id=17406\n\n    return () => {\n      el.removeEventListener(evt, listener, false);\n    };\n  }\n\n  dispatchEvent(el, evt) {\n    el.dispatchEvent(evt);\n  }\n\n  remove(node) {\n    if (node.parentNode) {\n      node.parentNode.removeChild(node);\n    }\n  }\n\n  createElement(tagName, doc) {\n    doc = doc || this.getDefaultDocument();\n    return doc.createElement(tagName);\n  }\n\n  createHtmlDocument() {\n    return document.implementation.createHTMLDocument('fakeTitle');\n  }\n\n  getDefaultDocument() {\n    return document;\n  }\n\n  isElementNode(node) {\n    return node.nodeType === Node.ELEMENT_NODE;\n  }\n\n  isShadowRoot(node) {\n    return node instanceof DocumentFragment;\n  }\n  /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n\n\n  getGlobalEventTarget(doc, target) {\n    if (target === 'window') {\n      return window;\n    }\n\n    if (target === 'document') {\n      return doc;\n    }\n\n    if (target === 'body') {\n      return doc.body;\n    }\n\n    return null;\n  }\n\n  getBaseHref(doc) {\n    const href = getBaseElementHref();\n    return href == null ? null : relativePath(href);\n  }\n\n  resetBaseElement() {\n    baseElement = null;\n  }\n\n  getUserAgent() {\n    return window.navigator.userAgent;\n  }\n\n  getCookie(name) {\n    return ɵparseCookieValue(document.cookie, name);\n  }\n\n}\n\nlet baseElement = null;\n\nfunction getBaseElementHref() {\n  baseElement = baseElement || document.querySelector('base');\n  return baseElement ? baseElement.getAttribute('href') : null;\n} // based on urlUtils.js in AngularJS 1\n\n\nlet urlParsingNode;\n\nfunction relativePath(url) {\n  urlParsingNode = urlParsingNode || document.createElement('a');\n  urlParsingNode.setAttribute('href', url);\n  const pathName = urlParsingNode.pathname;\n  return pathName.charAt(0) === '/' ? pathName : `/${pathName}`;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * An id that identifies a particular application being bootstrapped, that should\n * match across the client/server boundary.\n */\n\n\nconst TRANSITION_ID = new InjectionToken('TRANSITION_ID');\n\nfunction appInitializerFactory(transitionId, document, injector) {\n  return () => {\n    // Wait for all application initializers to be completed before removing the styles set by\n    // the server.\n    injector.get(ApplicationInitStatus).donePromise.then(() => {\n      const dom = ɵgetDOM();\n      const styles = document.querySelectorAll(`style[ng-transition=\"${transitionId}\"]`);\n\n      for (let i = 0; i < styles.length; i++) {\n        dom.remove(styles[i]);\n      }\n    });\n  };\n}\n\nconst SERVER_TRANSITION_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: appInitializerFactory,\n  deps: [TRANSITION_ID, DOCUMENT, Injector],\n  multi: true\n}];\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nclass BrowserGetTestability {\n  addToWindow(registry) {\n    ɵglobal['getAngularTestability'] = (elem, findInAncestors = true) => {\n      const testability = registry.findTestabilityInTree(elem, findInAncestors);\n\n      if (testability == null) {\n        throw new Error('Could not find testability for element.');\n      }\n\n      return testability;\n    };\n\n    ɵglobal['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n\n    ɵglobal['getAllAngularRootElements'] = () => registry.getAllRootElements();\n\n    const whenAllStable = (callback\n    /** TODO #9100 */\n    ) => {\n      const testabilities = ɵglobal['getAllAngularTestabilities']();\n      let count = testabilities.length;\n      let didWork = false;\n\n      const decrement = function (didWork_\n      /** TODO #9100 */\n      ) {\n        didWork = didWork || didWork_;\n        count--;\n\n        if (count == 0) {\n          callback(didWork);\n        }\n      };\n\n      testabilities.forEach(function (testability\n      /** TODO #9100 */\n      ) {\n        testability.whenStable(decrement);\n      });\n    };\n\n    if (!ɵglobal['frameworkStabilizers']) {\n      ɵglobal['frameworkStabilizers'] = [];\n    }\n\n    ɵglobal['frameworkStabilizers'].push(whenAllStable);\n  }\n\n  findTestabilityInTree(registry, elem, findInAncestors) {\n    if (elem == null) {\n      return null;\n    }\n\n    const t = registry.getTestability(elem);\n\n    if (t != null) {\n      return t;\n    } else if (!findInAncestors) {\n      return null;\n    }\n\n    if (ɵgetDOM().isShadowRoot(elem)) {\n      return this.findTestabilityInTree(registry, elem.host, true);\n    }\n\n    return this.findTestabilityInTree(registry, elem.parentElement, true);\n  }\n\n}\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\n\n\nclass BrowserXhr {\n  build() {\n    return new XMLHttpRequest();\n  }\n\n}\n\nBrowserXhr.ɵfac = function BrowserXhr_Factory(t) {\n  return new (t || BrowserXhr)();\n};\n\nBrowserXhr.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: BrowserXhr,\n  factory: BrowserXhr.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserXhr, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * The injection token for the event-manager plug-in service.\n *\n * @publicApi\n */\n\n\nconst EVENT_MANAGER_PLUGINS = new InjectionToken('EventManagerPlugins');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\n\nclass EventManager {\n  /**\n   * Initializes an instance of the event-manager service.\n   */\n  constructor(plugins, _zone) {\n    this._zone = _zone;\n    this._eventNameToPlugin = new Map();\n    plugins.forEach(p => p.manager = this);\n    this._plugins = plugins.slice().reverse();\n  }\n  /**\n   * Registers a handler for a specific element and event.\n   *\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns  A callback function that can be used to remove the handler.\n   */\n\n\n  addEventListener(element, eventName, handler) {\n    const plugin = this._findPluginFor(eventName);\n\n    return plugin.addEventListener(element, eventName, handler);\n  }\n  /**\n   * Registers a global handler for an event in a target view.\n   *\n   * @param target A target for global event notifications. One of \"window\", \"document\", or \"body\".\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns A callback function that can be used to remove the handler.\n   * @deprecated No longer being used in Ivy code. To be removed in version 14.\n   */\n\n\n  addGlobalEventListener(target, eventName, handler) {\n    const plugin = this._findPluginFor(eventName);\n\n    return plugin.addGlobalEventListener(target, eventName, handler);\n  }\n  /**\n   * Retrieves the compilation zone in which event listeners are registered.\n   */\n\n\n  getZone() {\n    return this._zone;\n  }\n  /** @internal */\n\n\n  _findPluginFor(eventName) {\n    const plugin = this._eventNameToPlugin.get(eventName);\n\n    if (plugin) {\n      return plugin;\n    }\n\n    const plugins = this._plugins;\n\n    for (let i = 0; i < plugins.length; i++) {\n      const plugin = plugins[i];\n\n      if (plugin.supports(eventName)) {\n        this._eventNameToPlugin.set(eventName, plugin);\n\n        return plugin;\n      }\n    }\n\n    throw new Error(`No event manager plugin found for event ${eventName}`);\n  }\n\n}\n\nEventManager.ɵfac = function EventManager_Factory(t) {\n  return new (t || EventManager)(i0.ɵɵinject(EVENT_MANAGER_PLUGINS), i0.ɵɵinject(i0.NgZone));\n};\n\nEventManager.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: EventManager,\n  factory: EventManager.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EventManager, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [EVENT_MANAGER_PLUGINS]\n      }]\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\n\nclass EventManagerPlugin {\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n\n  addGlobalEventListener(element, eventName, handler) {\n    const target = ɵgetDOM().getGlobalEventTarget(this._doc, element);\n\n    if (!target) {\n      throw new Error(`Unsupported event target ${target} for event ${eventName}`);\n    }\n\n    return this.addEventListener(target, eventName, handler);\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass SharedStylesHost {\n  constructor() {\n    /** @internal */\n    this._stylesSet = new Set();\n  }\n\n  addStyles(styles) {\n    const additions = new Set();\n    styles.forEach(style => {\n      if (!this._stylesSet.has(style)) {\n        this._stylesSet.add(style);\n\n        additions.add(style);\n      }\n    });\n    this.onStylesAdded(additions);\n  }\n\n  onStylesAdded(additions) {}\n\n  getAllStyles() {\n    return Array.from(this._stylesSet);\n  }\n\n}\n\nSharedStylesHost.ɵfac = function SharedStylesHost_Factory(t) {\n  return new (t || SharedStylesHost)();\n};\n\nSharedStylesHost.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: SharedStylesHost,\n  factory: SharedStylesHost.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SharedStylesHost, [{\n    type: Injectable\n  }], null, null);\n})();\n\nclass DomSharedStylesHost extends SharedStylesHost {\n  constructor(_doc) {\n    super();\n    this._doc = _doc; // Maps all registered host nodes to a list of style nodes that have been added to the host node.\n\n    this._hostNodes = new Map();\n\n    this._hostNodes.set(_doc.head, []);\n  }\n\n  _addStylesToHost(styles, host, styleNodes) {\n    styles.forEach(style => {\n      const styleEl = this._doc.createElement('style');\n\n      styleEl.textContent = style;\n      styleNodes.push(host.appendChild(styleEl));\n    });\n  }\n\n  addHost(hostNode) {\n    const styleNodes = [];\n\n    this._addStylesToHost(this._stylesSet, hostNode, styleNodes);\n\n    this._hostNodes.set(hostNode, styleNodes);\n  }\n\n  removeHost(hostNode) {\n    const styleNodes = this._hostNodes.get(hostNode);\n\n    if (styleNodes) {\n      styleNodes.forEach(removeStyle);\n    }\n\n    this._hostNodes.delete(hostNode);\n  }\n\n  onStylesAdded(additions) {\n    this._hostNodes.forEach((styleNodes, hostNode) => {\n      this._addStylesToHost(additions, hostNode, styleNodes);\n    });\n  }\n\n  ngOnDestroy() {\n    this._hostNodes.forEach(styleNodes => styleNodes.forEach(removeStyle));\n  }\n\n}\n\nDomSharedStylesHost.ɵfac = function DomSharedStylesHost_Factory(t) {\n  return new (t || DomSharedStylesHost)(i0.ɵɵinject(DOCUMENT));\n};\n\nDomSharedStylesHost.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomSharedStylesHost,\n  factory: DomSharedStylesHost.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSharedStylesHost, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\nfunction removeStyle(styleNode) {\n  ɵgetDOM().remove(styleNode);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst NAMESPACE_URIS = {\n  'svg': 'http://www.w3.org/2000/svg',\n  'xhtml': 'http://www.w3.org/1999/xhtml',\n  'xlink': 'http://www.w3.org/1999/xlink',\n  'xml': 'http://www.w3.org/XML/1998/namespace',\n  'xmlns': 'http://www.w3.org/2000/xmlns/',\n  'math': 'http://www.w3.org/1998/MathML/'\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst NG_DEV_MODE$1 = typeof ngDevMode === 'undefined' || !!ngDevMode;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n\nfunction shimContentAttribute(componentShortId) {\n  return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\n\nfunction shimHostAttribute(componentShortId) {\n  return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\n\nfunction flattenStyles(compId, styles, target) {\n  for (let i = 0; i < styles.length; i++) {\n    let style = styles[i];\n\n    if (Array.isArray(style)) {\n      flattenStyles(compId, style, target);\n    } else {\n      style = style.replace(COMPONENT_REGEX, compId);\n      target.push(style);\n    }\n  }\n\n  return target;\n}\n\nfunction decoratePreventDefault(eventHandler) {\n  // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n  // decoratePreventDefault or is a listener added outside the Angular context so it can handle the\n  // two differently. In the first case, the special '__ngUnwrap__' token is passed to the unwrap\n  // the listener (see below).\n  return event => {\n    // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n    // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The debug_node\n    // can inspect the listener toString contents for the existence of this special token. Because\n    // the token is a string literal, it is ensured to not be modified by compiled code.\n    if (event === '__ngUnwrap__') {\n      return eventHandler;\n    }\n\n    const allowDefaultBehavior = eventHandler(event);\n\n    if (allowDefaultBehavior === false) {\n      // TODO(tbosch): move preventDefault into event plugins...\n      event.preventDefault();\n      event.returnValue = false;\n    }\n\n    return undefined;\n  };\n}\n\nlet hasLoggedNativeEncapsulationWarning = false;\n\nclass DomRendererFactory2 {\n  constructor(eventManager, sharedStylesHost, appId) {\n    this.eventManager = eventManager;\n    this.sharedStylesHost = sharedStylesHost;\n    this.appId = appId;\n    this.rendererByCompId = new Map();\n    this.defaultRenderer = new DefaultDomRenderer2(eventManager);\n  }\n\n  createRenderer(element, type) {\n    if (!element || !type) {\n      return this.defaultRenderer;\n    }\n\n    switch (type.encapsulation) {\n      case ViewEncapsulation.Emulated:\n        {\n          let renderer = this.rendererByCompId.get(type.id);\n\n          if (!renderer) {\n            renderer = new EmulatedEncapsulationDomRenderer2(this.eventManager, this.sharedStylesHost, type, this.appId);\n            this.rendererByCompId.set(type.id, renderer);\n          }\n\n          renderer.applyToHost(element);\n          return renderer;\n        }\n      // @ts-ignore TODO: Remove as part of FW-2290. TS complains about us dealing with an enum\n      // value that is not known (but previously was the value for ViewEncapsulation.Native)\n\n      case 1:\n      case ViewEncapsulation.ShadowDom:\n        // TODO(FW-2290): remove the `case 1:` fallback logic and the warning in v12.\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && // @ts-ignore TODO: Remove as part of FW-2290. TS complains about us dealing with an\n        // enum value that is not known (but previously was the value for\n        // ViewEncapsulation.Native)\n        !hasLoggedNativeEncapsulationWarning && type.encapsulation === 1) {\n          hasLoggedNativeEncapsulationWarning = true;\n          console.warn('ViewEncapsulation.Native is no longer supported. Falling back to ViewEncapsulation.ShadowDom. The fallback will be removed in v12.');\n        }\n\n        return new ShadowDomRenderer(this.eventManager, this.sharedStylesHost, element, type);\n\n      default:\n        {\n          if (!this.rendererByCompId.has(type.id)) {\n            const styles = flattenStyles(type.id, type.styles, []);\n            this.sharedStylesHost.addStyles(styles);\n            this.rendererByCompId.set(type.id, this.defaultRenderer);\n          }\n\n          return this.defaultRenderer;\n        }\n    }\n  }\n\n  begin() {}\n\n  end() {}\n\n}\n\nDomRendererFactory2.ɵfac = function DomRendererFactory2_Factory(t) {\n  return new (t || DomRendererFactory2)(i0.ɵɵinject(EventManager), i0.ɵɵinject(DomSharedStylesHost), i0.ɵɵinject(APP_ID));\n};\n\nDomRendererFactory2.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomRendererFactory2,\n  factory: DomRendererFactory2.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomRendererFactory2, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: EventManager\n    }, {\n      type: DomSharedStylesHost\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [APP_ID]\n      }]\n    }];\n  }, null);\n})();\n\nclass DefaultDomRenderer2 {\n  constructor(eventManager) {\n    this.eventManager = eventManager;\n    this.data = Object.create(null);\n    this.destroyNode = null;\n  }\n\n  destroy() {}\n\n  createElement(name, namespace) {\n    if (namespace) {\n      // TODO: `|| namespace` was added in\n      // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n      // support how Ivy passed around the namespace URI rather than short name at the time. It did\n      // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n      // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n      // namespaces should be and make it consistent.\n      // Related issues:\n      // https://github.com/angular/angular/issues/44028\n      // https://github.com/angular/angular/issues/44883\n      return document.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n    }\n\n    return document.createElement(name);\n  }\n\n  createComment(value) {\n    return document.createComment(value);\n  }\n\n  createText(value) {\n    return document.createTextNode(value);\n  }\n\n  appendChild(parent, newChild) {\n    const targetParent = isTemplateNode(parent) ? parent.content : parent;\n    targetParent.appendChild(newChild);\n  }\n\n  insertBefore(parent, newChild, refChild) {\n    if (parent) {\n      const targetParent = isTemplateNode(parent) ? parent.content : parent;\n      targetParent.insertBefore(newChild, refChild);\n    }\n  }\n\n  removeChild(parent, oldChild) {\n    if (parent) {\n      parent.removeChild(oldChild);\n    }\n  }\n\n  selectRootElement(selectorOrNode, preserveContent) {\n    let el = typeof selectorOrNode === 'string' ? document.querySelector(selectorOrNode) : selectorOrNode;\n\n    if (!el) {\n      throw new Error(`The selector \"${selectorOrNode}\" did not match any elements`);\n    }\n\n    if (!preserveContent) {\n      el.textContent = '';\n    }\n\n    return el;\n  }\n\n  parentNode(node) {\n    return node.parentNode;\n  }\n\n  nextSibling(node) {\n    return node.nextSibling;\n  }\n\n  setAttribute(el, name, value, namespace) {\n    if (namespace) {\n      name = namespace + ':' + name;\n      const namespaceUri = NAMESPACE_URIS[namespace];\n\n      if (namespaceUri) {\n        el.setAttributeNS(namespaceUri, name, value);\n      } else {\n        el.setAttribute(name, value);\n      }\n    } else {\n      el.setAttribute(name, value);\n    }\n  }\n\n  removeAttribute(el, name, namespace) {\n    if (namespace) {\n      const namespaceUri = NAMESPACE_URIS[namespace];\n\n      if (namespaceUri) {\n        el.removeAttributeNS(namespaceUri, name);\n      } else {\n        el.removeAttribute(`${namespace}:${name}`);\n      }\n    } else {\n      el.removeAttribute(name);\n    }\n  }\n\n  addClass(el, name) {\n    el.classList.add(name);\n  }\n\n  removeClass(el, name) {\n    el.classList.remove(name);\n  }\n\n  setStyle(el, style, value, flags) {\n    if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n      el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n    } else {\n      el.style[style] = value;\n    }\n  }\n\n  removeStyle(el, style, flags) {\n    if (flags & RendererStyleFlags2.DashCase) {\n      el.style.removeProperty(style);\n    } else {\n      // IE requires '' instead of null\n      // see https://github.com/angular/angular/issues/7916\n      el.style[style] = '';\n    }\n  }\n\n  setProperty(el, name, value) {\n    NG_DEV_MODE$1 && checkNoSyntheticProp(name, 'property');\n    el[name] = value;\n  }\n\n  setValue(node, value) {\n    node.nodeValue = value;\n  }\n\n  listen(target, event, callback) {\n    NG_DEV_MODE$1 && checkNoSyntheticProp(event, 'listener');\n\n    if (typeof target === 'string') {\n      return this.eventManager.addGlobalEventListener(target, event, decoratePreventDefault(callback));\n    }\n\n    return this.eventManager.addEventListener(target, event, decoratePreventDefault(callback));\n  }\n\n}\n\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\n\nfunction checkNoSyntheticProp(name, nameKind) {\n  if (name.charCodeAt(0) === AT_CHARCODE) {\n    throw new Error(`Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Either \\`BrowserAnimationsModule\\` or \\`NoopAnimationsModule\\` are imported in your application.\n  - There is corresponding configuration for the animation named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.io/api/core/Component#animations).`);\n  }\n}\n\nfunction isTemplateNode(node) {\n  return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\n\nclass EmulatedEncapsulationDomRenderer2 extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, component, appId) {\n    super(eventManager);\n    this.component = component;\n    const styles = flattenStyles(appId + '-' + component.id, component.styles, []);\n    sharedStylesHost.addStyles(styles);\n    this.contentAttr = shimContentAttribute(appId + '-' + component.id);\n    this.hostAttr = shimHostAttribute(appId + '-' + component.id);\n  }\n\n  applyToHost(element) {\n    super.setAttribute(element, this.hostAttr, '');\n  }\n\n  createElement(parent, name) {\n    const el = super.createElement(parent, name);\n    super.setAttribute(el, this.contentAttr, '');\n    return el;\n  }\n\n}\n\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, hostEl, component) {\n    super(eventManager);\n    this.sharedStylesHost = sharedStylesHost;\n    this.hostEl = hostEl;\n    this.shadowRoot = hostEl.attachShadow({\n      mode: 'open'\n    });\n    this.sharedStylesHost.addHost(this.shadowRoot);\n    const styles = flattenStyles(component.id, component.styles, []);\n\n    for (let i = 0; i < styles.length; i++) {\n      const styleEl = document.createElement('style');\n      styleEl.textContent = styles[i];\n      this.shadowRoot.appendChild(styleEl);\n    }\n  }\n\n  nodeOrShadowRoot(node) {\n    return node === this.hostEl ? this.shadowRoot : node;\n  }\n\n  destroy() {\n    this.sharedStylesHost.removeHost(this.shadowRoot);\n  }\n\n  appendChild(parent, newChild) {\n    return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n  }\n\n  insertBefore(parent, newChild, refChild) {\n    return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n  }\n\n  removeChild(parent, oldChild) {\n    return super.removeChild(this.nodeOrShadowRoot(parent), oldChild);\n  }\n\n  parentNode(node) {\n    return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass DomEventsPlugin extends EventManagerPlugin {\n  constructor(doc) {\n    super(doc);\n  } // This plugin should come last in the list of plugins, because it accepts all\n  // events.\n\n\n  supports(eventName) {\n    return true;\n  }\n\n  addEventListener(element, eventName, handler) {\n    element.addEventListener(eventName, handler, false);\n    return () => this.removeEventListener(element, eventName, handler);\n  }\n\n  removeEventListener(target, eventName, callback) {\n    return target.removeEventListener(eventName, callback);\n  }\n\n}\n\nDomEventsPlugin.ɵfac = function DomEventsPlugin_Factory(t) {\n  return new (t || DomEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n};\n\nDomEventsPlugin.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomEventsPlugin,\n  factory: DomEventsPlugin.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomEventsPlugin, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Defines supported modifiers for key events.\n */\n\n\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift']; // The following values are here for cross-browser compatibility and to match the W3C standard\n// cf https://www.w3.org/TR/DOM-Level-3-Events-key/\n\nconst _keyMap = {\n  '\\b': 'Backspace',\n  '\\t': 'Tab',\n  '\\x7F': 'Delete',\n  '\\x1B': 'Escape',\n  'Del': 'Delete',\n  'Esc': 'Escape',\n  'Left': 'ArrowLeft',\n  'Right': 'ArrowRight',\n  'Up': 'ArrowUp',\n  'Down': 'ArrowDown',\n  'Menu': 'ContextMenu',\n  'Scroll': 'ScrollLock',\n  'Win': 'OS'\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\n\nconst MODIFIER_KEY_GETTERS = {\n  'alt': event => event.altKey,\n  'control': event => event.ctrlKey,\n  'meta': event => event.metaKey,\n  'shift': event => event.shiftKey\n};\n/**\n * @publicApi\n * A browser plug-in that provides support for handling of key events in Angular.\n */\n\nclass KeyEventsPlugin extends EventManagerPlugin {\n  /**\n   * Initializes an instance of the browser plug-in.\n   * @param doc The document in which key events will be detected.\n   */\n  constructor(doc) {\n    super(doc);\n  }\n  /**\n   * Reports whether a named key event is supported.\n   * @param eventName The event name to query.\n   * @return True if the named key event is supported.\n   */\n\n\n  supports(eventName) {\n    return KeyEventsPlugin.parseEventName(eventName) != null;\n  }\n  /**\n   * Registers a handler for a specific element and key event.\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the key event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns The key event that was registered.\n   */\n\n\n  addEventListener(element, eventName, handler) {\n    const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n    const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n    return this.manager.getZone().runOutsideAngular(() => {\n      return ɵgetDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n    });\n  }\n  /**\n   * Parses the user provided full keyboard event definition and normalizes it for\n   * later internal use. It ensures the string is all lowercase, converts special\n   * characters to a standard spelling, and orders all the values consistently.\n   *\n   * @param eventName The name of the key event to listen for.\n   * @returns an object with the full, normalized string, and the dom event name\n   * or null in the case when the event doesn't match a keyboard event.\n   */\n\n\n  static parseEventName(eventName) {\n    const parts = eventName.toLowerCase().split('.');\n    const domEventName = parts.shift();\n\n    if (parts.length === 0 || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n      return null;\n    }\n\n    const key = KeyEventsPlugin._normalizeKey(parts.pop());\n\n    let fullKey = '';\n    let codeIX = parts.indexOf('code');\n\n    if (codeIX > -1) {\n      parts.splice(codeIX, 1);\n      fullKey = 'code.';\n    }\n\n    MODIFIER_KEYS.forEach(modifierName => {\n      const index = parts.indexOf(modifierName);\n\n      if (index > -1) {\n        parts.splice(index, 1);\n        fullKey += modifierName + '.';\n      }\n    });\n    fullKey += key;\n\n    if (parts.length != 0 || key.length === 0) {\n      // returning null instead of throwing to let another plugin process the event\n      return null;\n    } // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n    //       The code must remain in the `result['domEventName']` form.\n    // return {domEventName, fullKey};\n\n\n    const result = {};\n    result['domEventName'] = domEventName;\n    result['fullKey'] = fullKey;\n    return result;\n  }\n  /**\n   * Determines whether the actual keys pressed match the configured key code string.\n   * The `fullKeyCode` event is normalized in the `parseEventName` method when the\n   * event is attached to the DOM during the `addEventListener` call. This is unseen\n   * by the end user and is normalized for internal consistency and parsing.\n   *\n   * @param event The keyboard event.\n   * @param fullKeyCode The normalized user defined expected key event string\n   * @returns boolean.\n   */\n\n\n  static matchEventFullKeyCode(event, fullKeyCode) {\n    let keycode = _keyMap[event.key] || event.key;\n    let key = '';\n\n    if (fullKeyCode.indexOf('code.') > -1) {\n      keycode = event.code;\n      key = 'code.';\n    } // the keycode could be unidentified so we have to check here\n\n\n    if (keycode == null || !keycode) return false;\n    keycode = keycode.toLowerCase();\n\n    if (keycode === ' ') {\n      keycode = 'space'; // for readability\n    } else if (keycode === '.') {\n      keycode = 'dot'; // because '.' is used as a separator in event names\n    }\n\n    MODIFIER_KEYS.forEach(modifierName => {\n      if (modifierName !== keycode) {\n        const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n\n        if (modifierGetter(event)) {\n          key += modifierName + '.';\n        }\n      }\n    });\n    key += keycode;\n    return key === fullKeyCode;\n  }\n  /**\n   * Configures a handler callback for a key event.\n   * @param fullKey The event name that combines all simultaneous keystrokes.\n   * @param handler The function that responds to the key event.\n   * @param zone The zone in which the event occurred.\n   * @returns A callback function.\n   */\n\n\n  static eventCallback(fullKey, handler, zone) {\n    return event => {\n      if (KeyEventsPlugin.matchEventFullKeyCode(event, fullKey)) {\n        zone.runGuarded(() => handler(event));\n      }\n    };\n  }\n  /** @internal */\n\n\n  static _normalizeKey(keyName) {\n    // TODO: switch to a Map if the mapping grows too much\n    switch (keyName) {\n      case 'esc':\n        return 'escape';\n\n      default:\n        return keyName;\n    }\n  }\n\n}\n\nKeyEventsPlugin.ɵfac = function KeyEventsPlugin_Factory(t) {\n  return new (t || KeyEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n};\n\nKeyEventsPlugin.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: KeyEventsPlugin,\n  factory: KeyEventsPlugin.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeyEventsPlugin, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || !!ngDevMode;\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/standalone-components).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```typescript\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```typescript\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n * @developerPreview\n */\n\nfunction bootstrapApplication(rootComponent, options) {\n  return ɵinternalCreateApplication({\n    rootComponent,\n    ...createProvidersConfig(options)\n  });\n}\n/**\n * Create an instance of an Angular application without bootstrapping any components. This is useful\n * for the situation where one wants to decouple application environment creation (a platform and\n * associated injectors) from rendering components on a screen. Components can be subsequently\n * bootstrapped on the returned `ApplicationRef`.\n *\n * @param options Extra configuration for the application environment, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n * @developerPreview\n */\n\n\nfunction createApplication(options) {\n  return ɵinternalCreateApplication(createProvidersConfig(options));\n}\n\nfunction createProvidersConfig(options) {\n  return {\n    appProviders: [...BROWSER_MODULE_PROVIDERS, ...(options?.providers ?? [])],\n    platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS\n  };\n}\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @developerPreview\n * @publicApi\n */\n\n\nfunction provideProtractorTestingSupport() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideProtractorTestingSupport` call results in app code.\n  return [...TESTABILITY_PROVIDERS];\n}\n\nfunction initDomAdapter() {\n  BrowserDomAdapter.makeCurrent();\n}\n\nfunction errorHandler() {\n  return new ErrorHandler();\n}\n\nfunction _document() {\n  // Tell ivy about the global document\n  ɵsetDocument(document);\n  return document;\n}\n\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [{\n  provide: PLATFORM_ID,\n  useValue: ɵPLATFORM_BROWSER_ID\n}, {\n  provide: PLATFORM_INITIALIZER,\n  useValue: initDomAdapter,\n  multi: true\n}, {\n  provide: DOCUMENT,\n  useFactory: _document,\n  deps: []\n}];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\n\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\n\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken(NG_DEV_MODE ? 'BrowserModule Providers Marker' : '');\nconst TESTABILITY_PROVIDERS = [{\n  provide: ɵTESTABILITY_GETTER,\n  useClass: BrowserGetTestability,\n  deps: []\n}, {\n  provide: ɵTESTABILITY,\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n}, {\n  provide: Testability,\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n}];\nconst BROWSER_MODULE_PROVIDERS = [{\n  provide: ɵINJECTOR_SCOPE,\n  useValue: 'root'\n}, {\n  provide: ErrorHandler,\n  useFactory: errorHandler,\n  deps: []\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: DomEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT, NgZone, PLATFORM_ID]\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: KeyEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT]\n}, {\n  provide: DomRendererFactory2,\n  useClass: DomRendererFactory2,\n  deps: [EventManager, DomSharedStylesHost, APP_ID]\n}, {\n  provide: RendererFactory2,\n  useExisting: DomRendererFactory2\n}, {\n  provide: SharedStylesHost,\n  useExisting: DomSharedStylesHost\n}, {\n  provide: DomSharedStylesHost,\n  useClass: DomSharedStylesHost,\n  deps: [DOCUMENT]\n}, {\n  provide: EventManager,\n  useClass: EventManager,\n  deps: [EVENT_MANAGER_PLUGINS, NgZone]\n}, {\n  provide: XhrFactory,\n  useClass: BrowserXhr,\n  deps: []\n}, NG_DEV_MODE ? {\n  provide: BROWSER_MODULE_PROVIDERS_MARKER,\n  useValue: true\n} : []];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\n\nclass BrowserModule {\n  constructor(providersAlreadyPresent) {\n    if (NG_DEV_MODE && providersAlreadyPresent) {\n      throw new Error(`Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` + `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n    }\n  }\n  /**\n   * Configures a browser-based app to transition from a server-rendered app, if\n   * one is present on the page.\n   *\n   * @param params An object containing an identifier for the app to transition.\n   * The ID must match between the client and server versions of the app.\n   * @returns The reconfigured `BrowserModule` to import into the app's root `AppModule`.\n   */\n\n\n  static withServerTransition(params) {\n    return {\n      ngModule: BrowserModule,\n      providers: [{\n        provide: APP_ID,\n        useValue: params.appId\n      }, {\n        provide: TRANSITION_ID,\n        useExisting: APP_ID\n      }, SERVER_TRANSITION_PROVIDERS]\n    };\n  }\n\n}\n\nBrowserModule.ɵfac = function BrowserModule_Factory(t) {\n  return new (t || BrowserModule)(i0.ɵɵinject(BROWSER_MODULE_PROVIDERS_MARKER, 12));\n};\n\nBrowserModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BrowserModule,\n  exports: [CommonModule, ApplicationModule]\n});\nBrowserModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n  imports: [CommonModule, ApplicationModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserModule, [{\n    type: NgModule,\n    args: [{\n      providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n      exports: [CommonModule, ApplicationModule]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }, {\n        type: Inject,\n        args: [BROWSER_MODULE_PROVIDERS_MARKER]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Factory to create a `Meta` service instance for the current DOM document.\n */\n\n\nfunction createMeta() {\n  return new Meta(ɵɵinject(DOCUMENT));\n}\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\n\n\nclass Meta {\n  constructor(_doc) {\n    this._doc = _doc;\n    this._dom = ɵgetDOM();\n  }\n  /**\n   * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * If an existing element is found, it is returned and is not modified in any way.\n   * @param tag The definition of a `<meta>` element to match or create.\n   * @param forceCreation True to create a new element without checking whether one already exists.\n   * @returns The existing element with the same attributes and values if found,\n   * the new element if no match is found, or `null` if the tag parameter is not defined.\n   */\n\n\n  addTag(tag, forceCreation = false) {\n    if (!tag) return null;\n    return this._getOrCreateElement(tag, forceCreation);\n  }\n  /**\n   * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * @param tags An array of tag definitions to match or create.\n   * @param forceCreation True to create new elements without checking whether they already exist.\n   * @returns The matching elements if found, or the new elements.\n   */\n\n\n  addTags(tags, forceCreation = false) {\n    if (!tags) return [];\n    return tags.reduce((result, tag) => {\n      if (tag) {\n        result.push(this._getOrCreateElement(tag, forceCreation));\n      }\n\n      return result;\n    }, []);\n  }\n  /**\n   * Retrieves a `<meta>` tag element in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching element, if any.\n   */\n\n\n  getTag(attrSelector) {\n    if (!attrSelector) return null;\n    return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n  }\n  /**\n   * Retrieves a set of `<meta>` tag elements in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching elements, if any.\n   */\n\n\n  getTags(attrSelector) {\n    if (!attrSelector) return [];\n\n    const list\n    /*NodeList*/\n    = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n\n    return list ? [].slice.call(list) : [];\n  }\n  /**\n   * Modifies an existing `<meta>` tag element in the current HTML document.\n   * @param tag The tag description with which to replace the existing tag content.\n   * @param selector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n   * replacement tag.\n   * @return The modified element.\n   */\n\n\n  updateTag(tag, selector) {\n    if (!tag) return null;\n    selector = selector || this._parseSelector(tag);\n    const meta = this.getTag(selector);\n\n    if (meta) {\n      return this._setMetaElementAttributes(tag, meta);\n    }\n\n    return this._getOrCreateElement(tag, true);\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param attrSelector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   */\n\n\n  removeTag(attrSelector) {\n    this.removeTagElement(this.getTag(attrSelector));\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param meta The tag definition to match against to identify an existing tag.\n   */\n\n\n  removeTagElement(meta) {\n    if (meta) {\n      this._dom.remove(meta);\n    }\n  }\n\n  _getOrCreateElement(meta, forceCreation = false) {\n    if (!forceCreation) {\n      const selector = this._parseSelector(meta); // It's allowed to have multiple elements with the same name so it's not enough to\n      // just check that element with the same name already present on the page. We also need to\n      // check if element has tag attributes\n\n\n      const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n      if (elem !== undefined) return elem;\n    }\n\n    const element = this._dom.createElement('meta');\n\n    this._setMetaElementAttributes(meta, element);\n\n    const head = this._doc.getElementsByTagName('head')[0];\n\n    head.appendChild(element);\n    return element;\n  }\n\n  _setMetaElementAttributes(tag, el) {\n    Object.keys(tag).forEach(prop => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n    return el;\n  }\n\n  _parseSelector(tag) {\n    const attr = tag.name ? 'name' : 'property';\n    return `${attr}=\"${tag[attr]}\"`;\n  }\n\n  _containsAttributes(tag, elem) {\n    return Object.keys(tag).every(key => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n  }\n\n  _getMetaKeyMap(prop) {\n    return META_KEYS_MAP[prop] || prop;\n  }\n\n}\n\nMeta.ɵfac = function Meta_Factory(t) {\n  return new (t || Meta)(i0.ɵɵinject(DOCUMENT));\n};\n\nMeta.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Meta,\n  factory: function Meta_Factory(t) {\n    let r = null;\n\n    if (t) {\n      r = new t();\n    } else {\n      r = createMeta();\n    }\n\n    return r;\n  },\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Meta, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: createMeta,\n      deps: []\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\n\n\nconst META_KEYS_MAP = {\n  httpEquiv: 'http-equiv'\n};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Factory to create Title service.\n */\n\nfunction createTitle() {\n  return new Title(ɵɵinject(DOCUMENT));\n}\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\n\n\nclass Title {\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n  /**\n   * Get the title of the current HTML document.\n   */\n\n\n  getTitle() {\n    return this._doc.title;\n  }\n  /**\n   * Set the title of the current HTML document.\n   * @param newTitle\n   */\n\n\n  setTitle(newTitle) {\n    this._doc.title = newTitle || '';\n  }\n\n}\n\nTitle.ɵfac = function Title_Factory(t) {\n  return new (t || Title)(i0.ɵɵinject(DOCUMENT));\n};\n\nTitle.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Title,\n  factory: function Title_Factory(t) {\n    let r = null;\n\n    if (t) {\n      r = new t();\n    } else {\n      r = createTitle();\n    }\n\n    return r;\n  },\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Title, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: createTitle,\n      deps: []\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst CAMEL_CASE_REGEXP = /([A-Z])/g;\nconst DASH_CASE_REGEXP = /-([a-z])/g;\n\nfunction camelCaseToDashCase(input) {\n  return input.replace(CAMEL_CASE_REGEXP, (...m) => '-' + m[1].toLowerCase());\n}\n\nfunction dashCaseToCamelCase(input) {\n  return input.replace(DASH_CASE_REGEXP, (...m) => m[1].toUpperCase());\n}\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\n\n\nfunction exportNgVar(name, value) {\n  if (typeof COMPILED === 'undefined' || !COMPILED) {\n    // Note: we can't export `ng` when using closure enhanced optimization as:\n    // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n    // - we can't declare a closure extern as the namespace `ng` is already used within Google\n    //   for typings for angularJS (via `goog.provide('ng....')`).\n    const ng = ɵglobal['ng'] = ɵglobal['ng'] || {};\n    ng[name] = value;\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst win = typeof window !== 'undefined' && window || {};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nclass ChangeDetectionPerfRecord {\n  constructor(msPerTick, numTicks) {\n    this.msPerTick = msPerTick;\n    this.numTicks = numTicks;\n  }\n\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\n\n\nclass AngularProfiler {\n  constructor(ref) {\n    this.appRef = ref.injector.get(ApplicationRef);\n  } // tslint:disable:no-console\n\n  /**\n   * Exercises change detection in a loop and then prints the average amount of\n   * time in milliseconds how long a single round of change detection takes for\n   * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n   * of 500 milliseconds.\n   *\n   * Optionally, a user may pass a `config` parameter containing a map of\n   * options. Supported options are:\n   *\n   * `record` (boolean) - causes the profiler to record a CPU profile while\n   * it exercises the change detector. Example:\n   *\n   * ```\n   * ng.profiler.timeChangeDetection({record: true})\n   * ```\n   */\n\n\n  timeChangeDetection(config) {\n    const record = config && config['record'];\n    const profileName = 'Change Detection'; // Profiler is not available in Android browsers without dev tools opened\n\n    const isProfilerAvailable = win.console.profile != null;\n\n    if (record && isProfilerAvailable) {\n      win.console.profile(profileName);\n    }\n\n    const start = performanceNow();\n    let numTicks = 0;\n\n    while (numTicks < 5 || performanceNow() - start < 500) {\n      this.appRef.tick();\n      numTicks++;\n    }\n\n    const end = performanceNow();\n\n    if (record && isProfilerAvailable) {\n      win.console.profileEnd(profileName);\n    }\n\n    const msPerTick = (end - start) / numTicks;\n    win.console.log(`ran ${numTicks} change detection cycles`);\n    win.console.log(`${msPerTick.toFixed(2)} ms per check`);\n    return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n  }\n\n}\n\nfunction performanceNow() {\n  return win.performance && win.performance.now ? win.performance.now() : new Date().getTime();\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\n\nfunction enableDebugTools(ref) {\n  exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n  return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\n\n\nfunction disableDebugTools() {\n  exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction escapeHtml(text) {\n  const escapedText = {\n    '&': '&a;',\n    '\"': '&q;',\n    '\\'': '&s;',\n    '<': '&l;',\n    '>': '&g;'\n  };\n  return text.replace(/[&\"'<>]/g, s => escapedText[s]);\n}\n\nfunction unescapeHtml(text) {\n  const unescapedText = {\n    '&a;': '&',\n    '&q;': '\"',\n    '&s;': '\\'',\n    '&l;': '<',\n    '&g;': '>'\n  };\n  return text.replace(/&[^;]+;/g, s => unescapedText[s]);\n}\n/**\n * Create a `StateKey<T>` that can be used to store value of type T with `TransferState`.\n *\n * Example:\n *\n * ```\n * const COUNTER_KEY = makeStateKey<number>('counter');\n * let value = 10;\n *\n * transferState.set(COUNTER_KEY, value);\n * ```\n *\n * @publicApi\n */\n\n\nfunction makeStateKey(key) {\n  return key;\n}\n/**\n * A key value store that is transferred from the application on the server side to the application\n * on the client side.\n *\n * The `TransferState` is available as an injectable token.\n * On the client, just inject this token using DI and use it, it will be lazily initialized.\n * On the server it's already included if `renderApplication` function is used. Otherwise, import\n * the `ServerTransferStateModule` module to make the `TransferState` available.\n *\n * The values in the store are serialized/deserialized using JSON.stringify/JSON.parse. So only\n * boolean, number, string, null and non-class objects will be serialized and deserialized in a\n * non-lossy manner.\n *\n * @publicApi\n */\n\n\nclass TransferState {\n  constructor() {\n    this.store = {};\n    this.onSerializeCallbacks = {};\n  }\n  /**\n   * Get the value corresponding to a key. Return `defaultValue` if key is not found.\n   */\n\n\n  get(key, defaultValue) {\n    return this.store[key] !== undefined ? this.store[key] : defaultValue;\n  }\n  /**\n   * Set the value corresponding to a key.\n   */\n\n\n  set(key, value) {\n    this.store[key] = value;\n  }\n  /**\n   * Remove a key from the store.\n   */\n\n\n  remove(key) {\n    delete this.store[key];\n  }\n  /**\n   * Test whether a key exists in the store.\n   */\n\n\n  hasKey(key) {\n    return this.store.hasOwnProperty(key);\n  }\n  /**\n   * Indicates whether the state is empty.\n   */\n\n\n  get isEmpty() {\n    return Object.keys(this.store).length === 0;\n  }\n  /**\n   * Register a callback to provide the value for a key when `toJson` is called.\n   */\n\n\n  onSerialize(key, callback) {\n    this.onSerializeCallbacks[key] = callback;\n  }\n  /**\n   * Serialize the current state of the store to JSON.\n   */\n\n\n  toJson() {\n    // Call the onSerialize callbacks and put those values into the store.\n    for (const key in this.onSerializeCallbacks) {\n      if (this.onSerializeCallbacks.hasOwnProperty(key)) {\n        try {\n          this.store[key] = this.onSerializeCallbacks[key]();\n        } catch (e) {\n          console.warn('Exception in onSerialize callback: ', e);\n        }\n      }\n    }\n\n    return JSON.stringify(this.store);\n  }\n\n}\n\nTransferState.ɵfac = function TransferState_Factory(t) {\n  return new (t || TransferState)();\n};\n\nTransferState.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: TransferState,\n  factory: function () {\n    return (() => {\n      const doc = inject(DOCUMENT);\n      const appId = inject(APP_ID);\n      const state = new TransferState();\n      state.store = retrieveTransferredState(doc, appId);\n      return state;\n    })();\n  },\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TransferState, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: () => {\n        const doc = inject(DOCUMENT);\n        const appId = inject(APP_ID);\n        const state = new TransferState();\n        state.store = retrieveTransferredState(doc, appId);\n        return state;\n      }\n    }]\n  }], null, null);\n})();\n\nfunction retrieveTransferredState(doc, appId) {\n  // Locate the script tag with the JSON data transferred from the server.\n  // The id of the script tag is set to the Angular appId + 'state'.\n  const script = doc.getElementById(appId + '-state');\n  let initialState = {};\n\n  if (script && script.textContent) {\n    try {\n      // Avoid using any here as it triggers lint errors in google3 (any is not allowed).\n      initialState = JSON.parse(unescapeHtml(script.textContent));\n    } catch (e) {\n      console.warn('Exception while restoring TransferState for app ' + appId, e);\n    }\n  }\n\n  return initialState;\n}\n/**\n * NgModule to install on the client side while using the `TransferState` to transfer state from\n * server to client.\n *\n * @publicApi\n * @deprecated no longer needed, you can inject the `TransferState` in an app without providing\n *     this module.\n */\n\n\nclass BrowserTransferStateModule {}\n\nBrowserTransferStateModule.ɵfac = function BrowserTransferStateModule_Factory(t) {\n  return new (t || BrowserTransferStateModule)();\n};\n\nBrowserTransferStateModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BrowserTransferStateModule\n});\nBrowserTransferStateModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserTransferStateModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\n\n\nclass By {\n  /**\n   * Match all nodes.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n   */\n  static all() {\n    return () => true;\n  }\n  /**\n   * Match elements by the given CSS selector.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n   */\n\n\n  static css(selector) {\n    return debugElement => {\n      return debugElement.nativeElement != null ? elementMatches(debugElement.nativeElement, selector) : false;\n    };\n  }\n  /**\n   * Match nodes that have the given directive present.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n   */\n\n\n  static directive(type) {\n    return debugNode => debugNode.providerTokens.indexOf(type) !== -1;\n  }\n\n}\n\nfunction elementMatches(n, selector) {\n  if (ɵgetDOM().isElementNode(n)) {\n    return n.matches && n.matches(selector) || n.msMatchesSelector && n.msMatchesSelector(selector) || n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n  }\n\n  return false;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Supported HammerJS recognizer event names.\n */\n\n\nconst EVENT_NAMES = {\n  // pan\n  'pan': true,\n  'panstart': true,\n  'panmove': true,\n  'panend': true,\n  'pancancel': true,\n  'panleft': true,\n  'panright': true,\n  'panup': true,\n  'pandown': true,\n  // pinch\n  'pinch': true,\n  'pinchstart': true,\n  'pinchmove': true,\n  'pinchend': true,\n  'pinchcancel': true,\n  'pinchin': true,\n  'pinchout': true,\n  // press\n  'press': true,\n  'pressup': true,\n  // rotate\n  'rotate': true,\n  'rotatestart': true,\n  'rotatemove': true,\n  'rotateend': true,\n  'rotatecancel': true,\n  // swipe\n  'swipe': true,\n  'swipeleft': true,\n  'swiperight': true,\n  'swipeup': true,\n  'swipedown': true,\n  // tap\n  'tap': true,\n  'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see `HammerGestureConfig`\n *\n * @ngModule HammerModule\n * @publicApi\n */\n\nconst HAMMER_GESTURE_CONFIG = new InjectionToken('HammerGestureConfig');\n/**\n * Injection token used to provide a {@link HammerLoader} to Angular.\n *\n * @publicApi\n */\n\nconst HAMMER_LOADER = new InjectionToken('HammerLoader');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\n\nclass HammerGestureConfig {\n  constructor() {\n    /**\n     * A set of supported event names for gestures to be used in Angular.\n     * Angular supports all built-in recognizers, as listed in\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     */\n    this.events = [];\n    /**\n     * Maps gesture event names to a set of configuration options\n     * that specify overrides to the default values for specific properties.\n     *\n     * The key is a supported event name to be configured,\n     * and the options object contains a set of properties, with override values\n     * to be applied to the named recognizer event.\n     * For example, to disable recognition of the rotate event, specify\n     *  `{\"rotate\": {\"enable\": false}}`.\n     *\n     * Properties that are not present take the HammerJS default values.\n     * For information about which properties are supported for which events,\n     * and their allowed and default values, see\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     *\n     */\n\n    this.overrides = {};\n  }\n  /**\n   * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n   * and attaches it to a given HTML element.\n   * @param element The element that will recognize gestures.\n   * @returns A HammerJS event-manager object.\n   */\n\n\n  buildHammer(element) {\n    const mc = new Hammer(element, this.options);\n    mc.get('pinch').set({\n      enable: true\n    });\n    mc.get('rotate').set({\n      enable: true\n    });\n\n    for (const eventName in this.overrides) {\n      mc.get(eventName).set(this.overrides[eventName]);\n    }\n\n    return mc;\n  }\n\n}\n\nHammerGestureConfig.ɵfac = function HammerGestureConfig_Factory(t) {\n  return new (t || HammerGestureConfig)();\n};\n\nHammerGestureConfig.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HammerGestureConfig,\n  factory: HammerGestureConfig.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGestureConfig, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\n\n\nclass HammerGesturesPlugin extends EventManagerPlugin {\n  constructor(doc, _config, console, loader) {\n    super(doc);\n    this._config = _config;\n    this.console = console;\n    this.loader = loader;\n    this._loaderPromise = null;\n  }\n\n  supports(eventName) {\n    if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n      return false;\n    }\n\n    if (!window.Hammer && !this.loader) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        this.console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` + `loaded and no custom loader has been specified.`);\n      }\n\n      return false;\n    }\n\n    return true;\n  }\n\n  addEventListener(element, eventName, handler) {\n    const zone = this.manager.getZone();\n    eventName = eventName.toLowerCase(); // If Hammer is not present but a loader is specified, we defer adding the event listener\n    // until Hammer is loaded.\n\n    if (!window.Hammer && this.loader) {\n      this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader()); // This `addEventListener` method returns a function to remove the added listener.\n      // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n      // than remove anything.\n\n      let cancelRegistration = false;\n\n      let deregister = () => {\n        cancelRegistration = true;\n      };\n\n      zone.runOutsideAngular(() => this._loaderPromise.then(() => {\n        // If Hammer isn't actually loaded when the custom loader resolves, give up.\n        if (!window.Hammer) {\n          if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            this.console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n          }\n\n          deregister = () => {};\n\n          return;\n        }\n\n        if (!cancelRegistration) {\n          // Now that Hammer is loaded and the listener is being loaded for real,\n          // the deregistration function changes from canceling registration to\n          // removal.\n          deregister = this.addEventListener(element, eventName, handler);\n        }\n      }).catch(() => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          this.console.warn(`The \"${eventName}\" event cannot be bound because the custom ` + `Hammer.JS loader failed.`);\n        }\n\n        deregister = () => {};\n      })); // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n      // can change the behavior of `deregister` once the listener is added. Using a closure in\n      // this way allows us to avoid any additional data structures to track listener removal.\n\n      return () => {\n        deregister();\n      };\n    }\n\n    return zone.runOutsideAngular(() => {\n      // Creating the manager bind events, must be done outside of angular\n      const mc = this._config.buildHammer(element);\n\n      const callback = function (eventObj) {\n        zone.runGuarded(function () {\n          handler(eventObj);\n        });\n      };\n\n      mc.on(eventName, callback);\n      return () => {\n        mc.off(eventName, callback); // destroy mc to prevent memory leak\n\n        if (typeof mc.destroy === 'function') {\n          mc.destroy();\n        }\n      };\n    });\n  }\n\n  isCustomEvent(eventName) {\n    return this._config.events.indexOf(eventName) > -1;\n  }\n\n}\n\nHammerGesturesPlugin.ɵfac = function HammerGesturesPlugin_Factory(t) {\n  return new (t || HammerGesturesPlugin)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(HAMMER_GESTURE_CONFIG), i0.ɵɵinject(i0.ɵConsole), i0.ɵɵinject(HAMMER_LOADER, 8));\n};\n\nHammerGesturesPlugin.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HammerGesturesPlugin,\n  factory: HammerGesturesPlugin.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGesturesPlugin, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: HammerGestureConfig,\n      decorators: [{\n        type: Inject,\n        args: [HAMMER_GESTURE_CONFIG]\n      }]\n    }, {\n      type: i0.ɵConsole\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [HAMMER_LOADER]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's EventManager.\n *\n * @publicApi\n */\n\n\nclass HammerModule {}\n\nHammerModule.ɵfac = function HammerModule_Factory(t) {\n  return new (t || HammerModule)();\n};\n\nHammerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: HammerModule\n});\nHammerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [{\n    provide: EVENT_MANAGER_PLUGINS,\n    useClass: HammerGesturesPlugin,\n    multi: true,\n    deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n  }, {\n    provide: HAMMER_GESTURE_CONFIG,\n    useClass: HammerGestureConfig,\n    deps: []\n  }]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: HammerGesturesPlugin,\n        multi: true,\n        deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n      }, {\n        provide: HAMMER_GESTURE_CONFIG,\n        useClass: HammerGestureConfig,\n        deps: []\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\n\n\nclass DomSanitizer {}\n\nDomSanitizer.ɵfac = function DomSanitizer_Factory(t) {\n  return new (t || DomSanitizer)();\n};\n\nDomSanitizer.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomSanitizer,\n  factory: function DomSanitizer_Factory(t) {\n    let r = null;\n\n    if (t) {\n      r = new (t || DomSanitizer)();\n    } else {\n      r = i0.ɵɵinject(DomSanitizerImpl);\n    }\n\n    return r;\n  },\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useExisting: forwardRef(() => DomSanitizerImpl)\n    }]\n  }], null, null);\n})();\n\nfunction domSanitizerImplFactory(injector) {\n  return new DomSanitizerImpl(injector.get(DOCUMENT));\n}\n\nclass DomSanitizerImpl extends DomSanitizer {\n  constructor(_doc) {\n    super();\n    this._doc = _doc;\n  }\n\n  sanitize(ctx, value) {\n    if (value == null) return null;\n\n    switch (ctx) {\n      case SecurityContext.NONE:\n        return value;\n\n      case SecurityContext.HTML:\n        if (ɵallowSanitizationBypassAndThrow(value, \"HTML\"\n        /* BypassType.Html */\n        )) {\n          return ɵunwrapSafeValue(value);\n        }\n\n        return ɵ_sanitizeHtml(this._doc, String(value)).toString();\n\n      case SecurityContext.STYLE:\n        if (ɵallowSanitizationBypassAndThrow(value, \"Style\"\n        /* BypassType.Style */\n        )) {\n          return ɵunwrapSafeValue(value);\n        }\n\n        return value;\n\n      case SecurityContext.SCRIPT:\n        if (ɵallowSanitizationBypassAndThrow(value, \"Script\"\n        /* BypassType.Script */\n        )) {\n          return ɵunwrapSafeValue(value);\n        }\n\n        throw new Error('unsafe value used in a script context');\n\n      case SecurityContext.URL:\n        if (ɵallowSanitizationBypassAndThrow(value, \"URL\"\n        /* BypassType.Url */\n        )) {\n          return ɵunwrapSafeValue(value);\n        }\n\n        return ɵ_sanitizeUrl(String(value));\n\n      case SecurityContext.RESOURCE_URL:\n        if (ɵallowSanitizationBypassAndThrow(value, \"ResourceURL\"\n        /* BypassType.ResourceUrl */\n        )) {\n          return ɵunwrapSafeValue(value);\n        }\n\n        throw new Error('unsafe value used in a resource URL context (see https://g.co/ng/security#xss)');\n\n      default:\n        throw new Error(`Unexpected SecurityContext ${ctx} (see https://g.co/ng/security#xss)`);\n    }\n  }\n\n  bypassSecurityTrustHtml(value) {\n    return ɵbypassSanitizationTrustHtml(value);\n  }\n\n  bypassSecurityTrustStyle(value) {\n    return ɵbypassSanitizationTrustStyle(value);\n  }\n\n  bypassSecurityTrustScript(value) {\n    return ɵbypassSanitizationTrustScript(value);\n  }\n\n  bypassSecurityTrustUrl(value) {\n    return ɵbypassSanitizationTrustUrl(value);\n  }\n\n  bypassSecurityTrustResourceUrl(value) {\n    return ɵbypassSanitizationTrustResourceUrl(value);\n  }\n\n}\n\nDomSanitizerImpl.ɵfac = function DomSanitizerImpl_Factory(t) {\n  return new (t || DomSanitizerImpl)(i0.ɵɵinject(DOCUMENT));\n};\n\nDomSanitizerImpl.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomSanitizerImpl,\n  factory: function DomSanitizerImpl_Factory(t) {\n    let r = null;\n\n    if (t) {\n      r = new t();\n    } else {\n      r = domSanitizerImplFactory(i0.ɵɵinject(Injector));\n    }\n\n    return r;\n  },\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizerImpl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: domSanitizerImplFactory,\n      deps: [Injector]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @publicApi\n */\n\n\nconst VERSION = new Version('14.3.0');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserModule, BrowserTransferStateModule, By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManager, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, Meta, Title, TransferState, VERSION, bootstrapApplication, createApplication, disableDebugTools, enableDebugTools, makeStateKey, platformBrowser, provideProtractorTestingSupport, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, DomRendererFactory2 as ɵDomRendererFactory2, DomSanitizerImpl as ɵDomSanitizerImpl, DomSharedStylesHost as ɵDomSharedStylesHost, HammerGesturesPlugin as ɵHammerGesturesPlugin, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, KeyEventsPlugin as ɵKeyEventsPlugin, NAMESPACE_URIS as ɵNAMESPACE_URIS, SharedStylesHost as ɵSharedStylesHost, TRANSITION_ID as ɵTRANSITION_ID, escapeHtml as ɵescapeHtml, flattenStyles as ɵflattenStyles, initDomAdapter as ɵinitDomAdapter, shimContentAttribute as ɵshimContentAttribute, shimHostAttribute as ɵshimHostAttribute };", "map": {"version": 3, "names": ["ɵDomAdapter", "ɵsetRootDomAdapter", "ɵparseCookieValue", "ɵgetDOM", "DOCUMENT", "ɵPLATFORM_BROWSER_ID", "XhrFactory", "CommonModule", "i0", "InjectionToken", "ApplicationInitStatus", "APP_INITIALIZER", "Injector", "ɵglobal", "Injectable", "Inject", "ViewEncapsulation", "APP_ID", "RendererStyleFlags2", "ɵinternalCreateApplication", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵsetDocument", "PLATFORM_ID", "PLATFORM_INITIALIZER", "createPlatformFactory", "platformCore", "ɵTESTABILITY_GETTER", "ɵTESTABILITY", "Testability", "NgZone", "TestabilityRegistry", "ɵINJECTOR_SCOPE", "RendererFactory2", "ApplicationModule", "NgModule", "Optional", "SkipSelf", "ɵɵinject", "ApplicationRef", "inject", "ɵConsole", "forwardRef", "SecurityContext", "ɵallowSanitizationBypassAndThrow", "ɵunwrapSafeValue", "ɵ_sanitizeUrl", "ɵ_sanitizeHtml", "ɵbypassSanitizationTrustHtml", "ɵbypassSanitizationTrustStyle", "ɵbypassSanitizationTrustScript", "ɵbypassSanitizationTrustUrl", "ɵbypassSanitizationTrustResourceUrl", "Version", "GenericBrowserDomAdapter", "constructor", "arguments", "supportsDOMEvents", "BrowserDomAdapter", "makeCurrent", "onAndCancel", "el", "evt", "listener", "addEventListener", "removeEventListener", "dispatchEvent", "remove", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "tagName", "doc", "getDefaultDocument", "createHtmlDocument", "document", "implementation", "createHTMLDocument", "isElementNode", "nodeType", "Node", "ELEMENT_NODE", "isShadowRoot", "DocumentFragment", "getGlobalEventTarget", "target", "window", "body", "getBaseHref", "href", "getBaseElementHref", "relativePath", "resetBaseElement", "baseElement", "getUserAgent", "navigator", "userAgent", "<PERSON><PERSON><PERSON><PERSON>", "name", "cookie", "querySelector", "getAttribute", "urlParsingNode", "url", "setAttribute", "pathName", "pathname", "char<PERSON>t", "TRANSITION_ID", "appInitializerFactory", "transitionId", "injector", "get", "donePromise", "then", "dom", "styles", "querySelectorAll", "i", "length", "SERVER_TRANSITION_PROVIDERS", "provide", "useFactory", "deps", "multi", "BrowserGetTestability", "addToWindow", "registry", "elem", "findInAncestors", "testability", "findTestabilityInTree", "Error", "getAllTestabilities", "getAllRootElements", "whenAllStable", "callback", "testabilities", "count", "didWork", "decrement", "didWork_", "for<PERSON>ach", "whenStable", "push", "t", "getTestability", "host", "parentElement", "BrowserXhr", "build", "XMLHttpRequest", "ɵfac", "ɵprov", "type", "EVENT_MANAGER_PLUGINS", "EventManager", "plugins", "_zone", "_eventNameToPlugin", "Map", "p", "manager", "_plugins", "slice", "reverse", "element", "eventName", "handler", "plugin", "_findPluginFor", "addGlobalEventListener", "getZone", "supports", "set", "undefined", "decorators", "args", "EventManagerPlugin", "_doc", "SharedStylesHost", "_stylesSet", "Set", "addStyles", "additions", "style", "has", "add", "onStylesAdded", "getAllStyles", "Array", "from", "DomSharedStylesHost", "_hostNodes", "head", "_addStylesToHost", "styleNodes", "styleEl", "textContent", "append<PERSON><PERSON><PERSON>", "addHost", "hostNode", "removeHost", "removeStyle", "delete", "ngOnDestroy", "styleNode", "NAMESPACE_URIS", "COMPONENT_REGEX", "NG_DEV_MODE$1", "ngDevMode", "COMPONENT_VARIABLE", "HOST_ATTR", "CONTENT_ATTR", "shimContentAttribute", "componentShortId", "replace", "shimHostAttribute", "flattenStyles", "compId", "isArray", "decoratePreventDefault", "<PERSON><PERSON><PERSON><PERSON>", "event", "allowDefaultBehavior", "preventDefault", "returnValue", "hasLoggedNativeEncapsulationWarning", "DomRendererFactory2", "eventManager", "sharedStylesHost", "appId", "rendererByCompId", "defaultRenderer", "DefaultDomRenderer2", "<PERSON><PERSON><PERSON><PERSON>", "encapsulation", "Emulated", "renderer", "id", "EmulatedEncapsulationDomRenderer2", "applyToHost", "ShadowDom", "console", "warn", "ShadowDom<PERSON><PERSON><PERSON>", "begin", "end", "data", "Object", "create", "destroyNode", "destroy", "namespace", "createElementNS", "createComment", "value", "createText", "createTextNode", "parent", "<PERSON><PERSON><PERSON><PERSON>", "targetParent", "isTemplateNode", "content", "insertBefore", "refChild", "<PERSON><PERSON><PERSON><PERSON>", "selectRootElement", "selectorOrNode", "preserve<PERSON><PERSON>nt", "nextS<PERSON>ling", "namespaceUri", "setAttributeNS", "removeAttribute", "removeAttributeNS", "addClass", "classList", "removeClass", "setStyle", "flags", "DashCase", "Important", "setProperty", "removeProperty", "checkNoSyntheticProp", "setValue", "nodeValue", "listen", "AT_CHARCODE", "charCodeAt", "<PERSON><PERSON><PERSON>", "component", "contentAttr", "hostAttr", "hostEl", "shadowRoot", "attachShadow", "mode", "nodeOrShadowRoot", "DomEventsPlugin", "MODIFIER_KEYS", "_keyMap", "MODIFIER_KEY_GETTERS", "altKey", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "KeyEventsPlugin", "parseEventName", "parsedEvent", "outsideH<PERSON>ler", "eventCallback", "runOutsideAngular", "parts", "toLowerCase", "split", "domEventName", "shift", "key", "_normalizeKey", "pop", "<PERSON><PERSON><PERSON>", "codeIX", "indexOf", "splice", "modifierName", "index", "result", "matchEventFullKeyCode", "fullKeyCode", "keycode", "code", "modifierGetter", "zone", "runGuarded", "keyName", "NG_DEV_MODE", "bootstrapApplication", "rootComponent", "options", "createProvidersConfig", "createApplication", "appProviders", "BROWSER_MODULE_PROVIDERS", "providers", "platformProviders", "INTERNAL_BROWSER_PLATFORM_PROVIDERS", "provideProtractorTestingSupport", "TESTABILITY_PROVIDERS", "initDomAdapter", "<PERSON><PERSON><PERSON><PERSON>", "_document", "useValue", "platformBrowser", "BROWSER_MODULE_PROVIDERS_MARKER", "useClass", "useExisting", "BrowserModule", "providersAlreadyPresent", "withServerTransition", "params", "ngModule", "ɵmod", "ɵinj", "exports", "createMeta", "Meta", "_dom", "addTag", "tag", "forceCreation", "_getOrCreateElement", "addTags", "tags", "reduce", "getTag", "attrSelector", "getTags", "list", "call", "updateTag", "selector", "_parseSelector", "meta", "_setMetaElementAttributes", "removeTag", "removeTagElement", "filter", "_containsAttributes", "getElementsByTagName", "keys", "prop", "_getMetaKeyMap", "attr", "every", "META_KEYS_MAP", "providedIn", "httpEquiv", "createTitle", "Title", "getTitle", "title", "setTitle", "newTitle", "CAMEL_CASE_REGEXP", "DASH_CASE_REGEXP", "camelCaseToDashCase", "input", "m", "dashCaseToCamelCase", "toUpperCase", "exportNgVar", "COMPILED", "ng", "win", "ChangeDetectionPerfRecord", "msPerTick", "numTicks", "AngularProfiler", "ref", "appRef", "timeChangeDetection", "config", "record", "profileName", "isProfilerAvailable", "profile", "start", "performanceNow", "tick", "profileEnd", "log", "toFixed", "performance", "now", "Date", "getTime", "PROFILER_GLOBAL_NAME", "enableDebugTools", "disableDebugTools", "escapeHtml", "text", "escapedText", "s", "unescapeHtml", "unescapedText", "makeStateKey", "TransferState", "store", "onSerializeCallbacks", "defaultValue", "<PERSON><PERSON><PERSON>", "hasOwnProperty", "isEmpty", "onSerialize", "to<PERSON><PERSON>", "e", "JSON", "stringify", "state", "retrieveTransferredState", "script", "getElementById", "initialState", "parse", "BrowserTransferStateModule", "By", "all", "css", "debugElement", "nativeElement", "elementMatches", "directive", "debugNode", "providerTokens", "n", "matches", "msMatchesSelector", "webkitMatchesSelector", "EVENT_NAMES", "HAMMER_GESTURE_CONFIG", "HAMMER_LOADER", "HammerGestureConfig", "events", "overrides", "buildHammer", "mc", "Hammer", "enable", "HammerGesturesPlugin", "_config", "loader", "_loaderPromise", "isCustomEvent", "cancelRegistration", "deregister", "catch", "eventObj", "on", "off", "HammerModule", "Dom<PERSON><PERSON><PERSON>zer", "DomSanitizerImpl", "domSanitizerImplFactory", "sanitize", "ctx", "NONE", "HTML", "String", "toString", "STYLE", "SCRIPT", "URL", "RESOURCE_URL", "bypassSecurityTrustHtml", "bypassSecurityTrustStyle", "bypassSecurityTrustScript", "bypassSecurityTrustUrl", "bypassSecurityTrustResourceUrl", "VERSION", "ɵBrowserDomAdapter", "ɵBrowserGetTestability", "ɵDomEventsPlugin", "ɵDomRendererFactory2", "ɵDomSanitizerImpl", "ɵDomSharedStylesHost", "ɵHammerGesturesPlugin", "ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS", "ɵKeyEventsPlugin", "ɵNAMESPACE_URIS", "ɵSharedStylesHost", "ɵTRANSITION_ID", "ɵescapeHtml", "ɵflattenStyles", "ɵinitDomAdapter", "ɵshimContentAttribute", "ɵshimHostAttribute"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/platform-browser/fesm2020/platform-browser.mjs"], "sourcesContent": ["/**\n * @license Angular v14.3.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵDomAdapter, ɵsetRootDomAdapter, ɵparseCookieValue, ɵgetDOM, DOCUMENT, ɵPLATFORM_BROWSER_ID, XhrFactory, CommonModule } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, ApplicationInitStatus, APP_INITIALIZER, Injector, ɵglobal, Injectable, Inject, ViewEncapsulation, APP_ID, RendererStyleFlags2, ɵinternalCreateApplication, ErrorHandler, ɵsetDocument, PLATFORM_ID, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, ɵTESTABILITY_GETTER, ɵTESTABILITY, Testability, NgZone, TestabilityRegistry, ɵINJECTOR_SCOPE, RendererFactory2, ApplicationModule, NgModule, Optional, SkipSelf, ɵɵinject, ApplicationRef, inject, ɵConsole, forwardRef, SecurityContext, ɵallowSanitizationBypassAndThrow, ɵunwrapSafeValue, ɵ_sanitizeUrl, ɵ_sanitizeHtml, ɵbypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl, Version } from '@angular/core';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Provides DOM operations in any browser environment.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass GenericBrowserDomAdapter extends ɵDomAdapter {\n    constructor() {\n        super(...arguments);\n        this.supportsDOMEvents = true;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n/* tslint:disable:requireParameterType no-console */\nclass BrowserDomAdapter extends GenericBrowserDomAdapter {\n    static makeCurrent() {\n        ɵsetRootDomAdapter(new BrowserDomAdapter());\n    }\n    onAndCancel(el, evt, listener) {\n        el.addEventListener(evt, listener, false);\n        // Needed to follow Dart's subscription semantic, until fix of\n        // https://code.google.com/p/dart/issues/detail?id=17406\n        return () => {\n            el.removeEventListener(evt, listener, false);\n        };\n    }\n    dispatchEvent(el, evt) {\n        el.dispatchEvent(evt);\n    }\n    remove(node) {\n        if (node.parentNode) {\n            node.parentNode.removeChild(node);\n        }\n    }\n    createElement(tagName, doc) {\n        doc = doc || this.getDefaultDocument();\n        return doc.createElement(tagName);\n    }\n    createHtmlDocument() {\n        return document.implementation.createHTMLDocument('fakeTitle');\n    }\n    getDefaultDocument() {\n        return document;\n    }\n    isElementNode(node) {\n        return node.nodeType === Node.ELEMENT_NODE;\n    }\n    isShadowRoot(node) {\n        return node instanceof DocumentFragment;\n    }\n    /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n    getGlobalEventTarget(doc, target) {\n        if (target === 'window') {\n            return window;\n        }\n        if (target === 'document') {\n            return doc;\n        }\n        if (target === 'body') {\n            return doc.body;\n        }\n        return null;\n    }\n    getBaseHref(doc) {\n        const href = getBaseElementHref();\n        return href == null ? null : relativePath(href);\n    }\n    resetBaseElement() {\n        baseElement = null;\n    }\n    getUserAgent() {\n        return window.navigator.userAgent;\n    }\n    getCookie(name) {\n        return ɵparseCookieValue(document.cookie, name);\n    }\n}\nlet baseElement = null;\nfunction getBaseElementHref() {\n    baseElement = baseElement || document.querySelector('base');\n    return baseElement ? baseElement.getAttribute('href') : null;\n}\n// based on urlUtils.js in AngularJS 1\nlet urlParsingNode;\nfunction relativePath(url) {\n    urlParsingNode = urlParsingNode || document.createElement('a');\n    urlParsingNode.setAttribute('href', url);\n    const pathName = urlParsingNode.pathname;\n    return pathName.charAt(0) === '/' ? pathName : `/${pathName}`;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * An id that identifies a particular application being bootstrapped, that should\n * match across the client/server boundary.\n */\nconst TRANSITION_ID = new InjectionToken('TRANSITION_ID');\nfunction appInitializerFactory(transitionId, document, injector) {\n    return () => {\n        // Wait for all application initializers to be completed before removing the styles set by\n        // the server.\n        injector.get(ApplicationInitStatus).donePromise.then(() => {\n            const dom = ɵgetDOM();\n            const styles = document.querySelectorAll(`style[ng-transition=\"${transitionId}\"]`);\n            for (let i = 0; i < styles.length; i++) {\n                dom.remove(styles[i]);\n            }\n        });\n    };\n}\nconst SERVER_TRANSITION_PROVIDERS = [\n    {\n        provide: APP_INITIALIZER,\n        useFactory: appInitializerFactory,\n        deps: [TRANSITION_ID, DOCUMENT, Injector],\n        multi: true\n    },\n];\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass BrowserGetTestability {\n    addToWindow(registry) {\n        ɵglobal['getAngularTestability'] = (elem, findInAncestors = true) => {\n            const testability = registry.findTestabilityInTree(elem, findInAncestors);\n            if (testability == null) {\n                throw new Error('Could not find testability for element.');\n            }\n            return testability;\n        };\n        ɵglobal['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n        ɵglobal['getAllAngularRootElements'] = () => registry.getAllRootElements();\n        const whenAllStable = (callback /** TODO #9100 */) => {\n            const testabilities = ɵglobal['getAllAngularTestabilities']();\n            let count = testabilities.length;\n            let didWork = false;\n            const decrement = function (didWork_ /** TODO #9100 */) {\n                didWork = didWork || didWork_;\n                count--;\n                if (count == 0) {\n                    callback(didWork);\n                }\n            };\n            testabilities.forEach(function (testability /** TODO #9100 */) {\n                testability.whenStable(decrement);\n            });\n        };\n        if (!ɵglobal['frameworkStabilizers']) {\n            ɵglobal['frameworkStabilizers'] = [];\n        }\n        ɵglobal['frameworkStabilizers'].push(whenAllStable);\n    }\n    findTestabilityInTree(registry, elem, findInAncestors) {\n        if (elem == null) {\n            return null;\n        }\n        const t = registry.getTestability(elem);\n        if (t != null) {\n            return t;\n        }\n        else if (!findInAncestors) {\n            return null;\n        }\n        if (ɵgetDOM().isShadowRoot(elem)) {\n            return this.findTestabilityInTree(registry, elem.host, true);\n        }\n        return this.findTestabilityInTree(registry, elem.parentElement, true);\n    }\n}\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\nclass BrowserXhr {\n    build() {\n        return new XMLHttpRequest();\n    }\n}\nBrowserXhr.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: BrowserXhr, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nBrowserXhr.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: BrowserXhr });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: BrowserXhr, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * The injection token for the event-manager plug-in service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken('EventManagerPlugins');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n    /**\n     * Initializes an instance of the event-manager service.\n     */\n    constructor(plugins, _zone) {\n        this._zone = _zone;\n        this._eventNameToPlugin = new Map();\n        plugins.forEach(p => p.manager = this);\n        this._plugins = plugins.slice().reverse();\n    }\n    /**\n     * Registers a handler for a specific element and event.\n     *\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns  A callback function that can be used to remove the handler.\n     */\n    addEventListener(element, eventName, handler) {\n        const plugin = this._findPluginFor(eventName);\n        return plugin.addEventListener(element, eventName, handler);\n    }\n    /**\n     * Registers a global handler for an event in a target view.\n     *\n     * @param target A target for global event notifications. One of \"window\", \"document\", or \"body\".\n     * @param eventName The name of the event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns A callback function that can be used to remove the handler.\n     * @deprecated No longer being used in Ivy code. To be removed in version 14.\n     */\n    addGlobalEventListener(target, eventName, handler) {\n        const plugin = this._findPluginFor(eventName);\n        return plugin.addGlobalEventListener(target, eventName, handler);\n    }\n    /**\n     * Retrieves the compilation zone in which event listeners are registered.\n     */\n    getZone() {\n        return this._zone;\n    }\n    /** @internal */\n    _findPluginFor(eventName) {\n        const plugin = this._eventNameToPlugin.get(eventName);\n        if (plugin) {\n            return plugin;\n        }\n        const plugins = this._plugins;\n        for (let i = 0; i < plugins.length; i++) {\n            const plugin = plugins[i];\n            if (plugin.supports(eventName)) {\n                this._eventNameToPlugin.set(eventName, plugin);\n                return plugin;\n            }\n        }\n        throw new Error(`No event manager plugin found for event ${eventName}`);\n    }\n}\nEventManager.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: EventManager, deps: [{ token: EVENT_MANAGER_PLUGINS }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\nEventManager.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: EventManager });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: EventManager, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [EVENT_MANAGER_PLUGINS]\n                }] }, { type: i0.NgZone }]; } });\nclass EventManagerPlugin {\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n    addGlobalEventListener(element, eventName, handler) {\n        const target = ɵgetDOM().getGlobalEventTarget(this._doc, element);\n        if (!target) {\n            throw new Error(`Unsupported event target ${target} for event ${eventName}`);\n        }\n        return this.addEventListener(target, eventName, handler);\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass SharedStylesHost {\n    constructor() {\n        /** @internal */\n        this._stylesSet = new Set();\n    }\n    addStyles(styles) {\n        const additions = new Set();\n        styles.forEach(style => {\n            if (!this._stylesSet.has(style)) {\n                this._stylesSet.add(style);\n                additions.add(style);\n            }\n        });\n        this.onStylesAdded(additions);\n    }\n    onStylesAdded(additions) { }\n    getAllStyles() {\n        return Array.from(this._stylesSet);\n    }\n}\nSharedStylesHost.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: SharedStylesHost, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nSharedStylesHost.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: SharedStylesHost });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: SharedStylesHost, decorators: [{\n            type: Injectable\n        }] });\nclass DomSharedStylesHost extends SharedStylesHost {\n    constructor(_doc) {\n        super();\n        this._doc = _doc;\n        // Maps all registered host nodes to a list of style nodes that have been added to the host node.\n        this._hostNodes = new Map();\n        this._hostNodes.set(_doc.head, []);\n    }\n    _addStylesToHost(styles, host, styleNodes) {\n        styles.forEach((style) => {\n            const styleEl = this._doc.createElement('style');\n            styleEl.textContent = style;\n            styleNodes.push(host.appendChild(styleEl));\n        });\n    }\n    addHost(hostNode) {\n        const styleNodes = [];\n        this._addStylesToHost(this._stylesSet, hostNode, styleNodes);\n        this._hostNodes.set(hostNode, styleNodes);\n    }\n    removeHost(hostNode) {\n        const styleNodes = this._hostNodes.get(hostNode);\n        if (styleNodes) {\n            styleNodes.forEach(removeStyle);\n        }\n        this._hostNodes.delete(hostNode);\n    }\n    onStylesAdded(additions) {\n        this._hostNodes.forEach((styleNodes, hostNode) => {\n            this._addStylesToHost(additions, hostNode, styleNodes);\n        });\n    }\n    ngOnDestroy() {\n        this._hostNodes.forEach(styleNodes => styleNodes.forEach(removeStyle));\n    }\n}\nDomSharedStylesHost.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: DomSharedStylesHost, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nDomSharedStylesHost.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: DomSharedStylesHost });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: DomSharedStylesHost, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\nfunction removeStyle(styleNode) {\n    ɵgetDOM().remove(styleNode);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst NAMESPACE_URIS = {\n    'svg': 'http://www.w3.org/2000/svg',\n    'xhtml': 'http://www.w3.org/1999/xhtml',\n    'xlink': 'http://www.w3.org/1999/xlink',\n    'xml': 'http://www.w3.org/XML/1998/namespace',\n    'xmlns': 'http://www.w3.org/2000/xmlns/',\n    'math': 'http://www.w3.org/1998/MathML/',\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst NG_DEV_MODE$1 = typeof ngDevMode === 'undefined' || !!ngDevMode;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\nfunction shimContentAttribute(componentShortId) {\n    return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n    return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction flattenStyles(compId, styles, target) {\n    for (let i = 0; i < styles.length; i++) {\n        let style = styles[i];\n        if (Array.isArray(style)) {\n            flattenStyles(compId, style, target);\n        }\n        else {\n            style = style.replace(COMPONENT_REGEX, compId);\n            target.push(style);\n        }\n    }\n    return target;\n}\nfunction decoratePreventDefault(eventHandler) {\n    // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n    // decoratePreventDefault or is a listener added outside the Angular context so it can handle the\n    // two differently. In the first case, the special '__ngUnwrap__' token is passed to the unwrap\n    // the listener (see below).\n    return (event) => {\n        // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n        // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The debug_node\n        // can inspect the listener toString contents for the existence of this special token. Because\n        // the token is a string literal, it is ensured to not be modified by compiled code.\n        if (event === '__ngUnwrap__') {\n            return eventHandler;\n        }\n        const allowDefaultBehavior = eventHandler(event);\n        if (allowDefaultBehavior === false) {\n            // TODO(tbosch): move preventDefault into event plugins...\n            event.preventDefault();\n            event.returnValue = false;\n        }\n        return undefined;\n    };\n}\nlet hasLoggedNativeEncapsulationWarning = false;\nclass DomRendererFactory2 {\n    constructor(eventManager, sharedStylesHost, appId) {\n        this.eventManager = eventManager;\n        this.sharedStylesHost = sharedStylesHost;\n        this.appId = appId;\n        this.rendererByCompId = new Map();\n        this.defaultRenderer = new DefaultDomRenderer2(eventManager);\n    }\n    createRenderer(element, type) {\n        if (!element || !type) {\n            return this.defaultRenderer;\n        }\n        switch (type.encapsulation) {\n            case ViewEncapsulation.Emulated: {\n                let renderer = this.rendererByCompId.get(type.id);\n                if (!renderer) {\n                    renderer = new EmulatedEncapsulationDomRenderer2(this.eventManager, this.sharedStylesHost, type, this.appId);\n                    this.rendererByCompId.set(type.id, renderer);\n                }\n                renderer.applyToHost(element);\n                return renderer;\n            }\n            // @ts-ignore TODO: Remove as part of FW-2290. TS complains about us dealing with an enum\n            // value that is not known (but previously was the value for ViewEncapsulation.Native)\n            case 1:\n            case ViewEncapsulation.ShadowDom:\n                // TODO(FW-2290): remove the `case 1:` fallback logic and the warning in v12.\n                if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    // @ts-ignore TODO: Remove as part of FW-2290. TS complains about us dealing with an\n                    // enum value that is not known (but previously was the value for\n                    // ViewEncapsulation.Native)\n                    !hasLoggedNativeEncapsulationWarning && type.encapsulation === 1) {\n                    hasLoggedNativeEncapsulationWarning = true;\n                    console.warn('ViewEncapsulation.Native is no longer supported. Falling back to ViewEncapsulation.ShadowDom. The fallback will be removed in v12.');\n                }\n                return new ShadowDomRenderer(this.eventManager, this.sharedStylesHost, element, type);\n            default: {\n                if (!this.rendererByCompId.has(type.id)) {\n                    const styles = flattenStyles(type.id, type.styles, []);\n                    this.sharedStylesHost.addStyles(styles);\n                    this.rendererByCompId.set(type.id, this.defaultRenderer);\n                }\n                return this.defaultRenderer;\n            }\n        }\n    }\n    begin() { }\n    end() { }\n}\nDomRendererFactory2.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: DomRendererFactory2, deps: [{ token: EventManager }, { token: DomSharedStylesHost }, { token: APP_ID }], target: i0.ɵɵFactoryTarget.Injectable });\nDomRendererFactory2.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: DomRendererFactory2 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: DomRendererFactory2, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: EventManager }, { type: DomSharedStylesHost }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [APP_ID]\n                }] }]; } });\nclass DefaultDomRenderer2 {\n    constructor(eventManager) {\n        this.eventManager = eventManager;\n        this.data = Object.create(null);\n        this.destroyNode = null;\n    }\n    destroy() { }\n    createElement(name, namespace) {\n        if (namespace) {\n            // TODO: `|| namespace` was added in\n            // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n            // support how Ivy passed around the namespace URI rather than short name at the time. It did\n            // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n            // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n            // namespaces should be and make it consistent.\n            // Related issues:\n            // https://github.com/angular/angular/issues/44028\n            // https://github.com/angular/angular/issues/44883\n            return document.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n        }\n        return document.createElement(name);\n    }\n    createComment(value) {\n        return document.createComment(value);\n    }\n    createText(value) {\n        return document.createTextNode(value);\n    }\n    appendChild(parent, newChild) {\n        const targetParent = isTemplateNode(parent) ? parent.content : parent;\n        targetParent.appendChild(newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        if (parent) {\n            const targetParent = isTemplateNode(parent) ? parent.content : parent;\n            targetParent.insertBefore(newChild, refChild);\n        }\n    }\n    removeChild(parent, oldChild) {\n        if (parent) {\n            parent.removeChild(oldChild);\n        }\n    }\n    selectRootElement(selectorOrNode, preserveContent) {\n        let el = typeof selectorOrNode === 'string' ? document.querySelector(selectorOrNode) :\n            selectorOrNode;\n        if (!el) {\n            throw new Error(`The selector \"${selectorOrNode}\" did not match any elements`);\n        }\n        if (!preserveContent) {\n            el.textContent = '';\n        }\n        return el;\n    }\n    parentNode(node) {\n        return node.parentNode;\n    }\n    nextSibling(node) {\n        return node.nextSibling;\n    }\n    setAttribute(el, name, value, namespace) {\n        if (namespace) {\n            name = namespace + ':' + name;\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.setAttributeNS(namespaceUri, name, value);\n            }\n            else {\n                el.setAttribute(name, value);\n            }\n        }\n        else {\n            el.setAttribute(name, value);\n        }\n    }\n    removeAttribute(el, name, namespace) {\n        if (namespace) {\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.removeAttributeNS(namespaceUri, name);\n            }\n            else {\n                el.removeAttribute(`${namespace}:${name}`);\n            }\n        }\n        else {\n            el.removeAttribute(name);\n        }\n    }\n    addClass(el, name) {\n        el.classList.add(name);\n    }\n    removeClass(el, name) {\n        el.classList.remove(name);\n    }\n    setStyle(el, style, value, flags) {\n        if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n            el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n        }\n        else {\n            el.style[style] = value;\n        }\n    }\n    removeStyle(el, style, flags) {\n        if (flags & RendererStyleFlags2.DashCase) {\n            el.style.removeProperty(style);\n        }\n        else {\n            // IE requires '' instead of null\n            // see https://github.com/angular/angular/issues/7916\n            el.style[style] = '';\n        }\n    }\n    setProperty(el, name, value) {\n        NG_DEV_MODE$1 && checkNoSyntheticProp(name, 'property');\n        el[name] = value;\n    }\n    setValue(node, value) {\n        node.nodeValue = value;\n    }\n    listen(target, event, callback) {\n        NG_DEV_MODE$1 && checkNoSyntheticProp(event, 'listener');\n        if (typeof target === 'string') {\n            return this.eventManager.addGlobalEventListener(target, event, decoratePreventDefault(callback));\n        }\n        return this.eventManager.addEventListener(target, event, decoratePreventDefault(callback));\n    }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n    if (name.charCodeAt(0) === AT_CHARCODE) {\n        throw new Error(`Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Either \\`BrowserAnimationsModule\\` or \\`NoopAnimationsModule\\` are imported in your application.\n  - There is corresponding configuration for the animation named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.io/api/core/Component#animations).`);\n    }\n}\nfunction isTemplateNode(node) {\n    return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass EmulatedEncapsulationDomRenderer2 extends DefaultDomRenderer2 {\n    constructor(eventManager, sharedStylesHost, component, appId) {\n        super(eventManager);\n        this.component = component;\n        const styles = flattenStyles(appId + '-' + component.id, component.styles, []);\n        sharedStylesHost.addStyles(styles);\n        this.contentAttr = shimContentAttribute(appId + '-' + component.id);\n        this.hostAttr = shimHostAttribute(appId + '-' + component.id);\n    }\n    applyToHost(element) {\n        super.setAttribute(element, this.hostAttr, '');\n    }\n    createElement(parent, name) {\n        const el = super.createElement(parent, name);\n        super.setAttribute(el, this.contentAttr, '');\n        return el;\n    }\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n    constructor(eventManager, sharedStylesHost, hostEl, component) {\n        super(eventManager);\n        this.sharedStylesHost = sharedStylesHost;\n        this.hostEl = hostEl;\n        this.shadowRoot = hostEl.attachShadow({ mode: 'open' });\n        this.sharedStylesHost.addHost(this.shadowRoot);\n        const styles = flattenStyles(component.id, component.styles, []);\n        for (let i = 0; i < styles.length; i++) {\n            const styleEl = document.createElement('style');\n            styleEl.textContent = styles[i];\n            this.shadowRoot.appendChild(styleEl);\n        }\n    }\n    nodeOrShadowRoot(node) {\n        return node === this.hostEl ? this.shadowRoot : node;\n    }\n    destroy() {\n        this.sharedStylesHost.removeHost(this.shadowRoot);\n    }\n    appendChild(parent, newChild) {\n        return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n    }\n    removeChild(parent, oldChild) {\n        return super.removeChild(this.nodeOrShadowRoot(parent), oldChild);\n    }\n    parentNode(node) {\n        return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass DomEventsPlugin extends EventManagerPlugin {\n    constructor(doc) {\n        super(doc);\n    }\n    // This plugin should come last in the list of plugins, because it accepts all\n    // events.\n    supports(eventName) {\n        return true;\n    }\n    addEventListener(element, eventName, handler) {\n        element.addEventListener(eventName, handler, false);\n        return () => this.removeEventListener(element, eventName, handler);\n    }\n    removeEventListener(target, eventName, callback) {\n        return target.removeEventListener(eventName, callback);\n    }\n}\nDomEventsPlugin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: DomEventsPlugin, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nDomEventsPlugin.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: DomEventsPlugin });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: DomEventsPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\n// The following values are here for cross-browser compatibility and to match the W3C standard\n// cf https://www.w3.org/TR/DOM-Level-3-Events-key/\nconst _keyMap = {\n    '\\b': 'Backspace',\n    '\\t': 'Tab',\n    '\\x7F': 'Delete',\n    '\\x1B': 'Escape',\n    'Del': 'Delete',\n    'Esc': 'Escape',\n    'Left': 'ArrowLeft',\n    'Right': 'ArrowRight',\n    'Up': 'ArrowUp',\n    'Down': 'ArrowDown',\n    'Menu': 'ContextMenu',\n    'Scroll': 'ScrollLock',\n    'Win': 'OS'\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS = {\n    'alt': (event) => event.altKey,\n    'control': (event) => event.ctrlKey,\n    'meta': (event) => event.metaKey,\n    'shift': (event) => event.shiftKey\n};\n/**\n * @publicApi\n * A browser plug-in that provides support for handling of key events in Angular.\n */\nclass KeyEventsPlugin extends EventManagerPlugin {\n    /**\n     * Initializes an instance of the browser plug-in.\n     * @param doc The document in which key events will be detected.\n     */\n    constructor(doc) {\n        super(doc);\n    }\n    /**\n     * Reports whether a named key event is supported.\n     * @param eventName The event name to query.\n     * @return True if the named key event is supported.\n     */\n    supports(eventName) {\n        return KeyEventsPlugin.parseEventName(eventName) != null;\n    }\n    /**\n     * Registers a handler for a specific element and key event.\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the key event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns The key event that was registered.\n     */\n    addEventListener(element, eventName, handler) {\n        const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n        const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n        return this.manager.getZone().runOutsideAngular(() => {\n            return ɵgetDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n        });\n    }\n    /**\n     * Parses the user provided full keyboard event definition and normalizes it for\n     * later internal use. It ensures the string is all lowercase, converts special\n     * characters to a standard spelling, and orders all the values consistently.\n     *\n     * @param eventName The name of the key event to listen for.\n     * @returns an object with the full, normalized string, and the dom event name\n     * or null in the case when the event doesn't match a keyboard event.\n     */\n    static parseEventName(eventName) {\n        const parts = eventName.toLowerCase().split('.');\n        const domEventName = parts.shift();\n        if ((parts.length === 0) || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n            return null;\n        }\n        const key = KeyEventsPlugin._normalizeKey(parts.pop());\n        let fullKey = '';\n        let codeIX = parts.indexOf('code');\n        if (codeIX > -1) {\n            parts.splice(codeIX, 1);\n            fullKey = 'code.';\n        }\n        MODIFIER_KEYS.forEach(modifierName => {\n            const index = parts.indexOf(modifierName);\n            if (index > -1) {\n                parts.splice(index, 1);\n                fullKey += modifierName + '.';\n            }\n        });\n        fullKey += key;\n        if (parts.length != 0 || key.length === 0) {\n            // returning null instead of throwing to let another plugin process the event\n            return null;\n        }\n        // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n        //       The code must remain in the `result['domEventName']` form.\n        // return {domEventName, fullKey};\n        const result = {};\n        result['domEventName'] = domEventName;\n        result['fullKey'] = fullKey;\n        return result;\n    }\n    /**\n     * Determines whether the actual keys pressed match the configured key code string.\n     * The `fullKeyCode` event is normalized in the `parseEventName` method when the\n     * event is attached to the DOM during the `addEventListener` call. This is unseen\n     * by the end user and is normalized for internal consistency and parsing.\n     *\n     * @param event The keyboard event.\n     * @param fullKeyCode The normalized user defined expected key event string\n     * @returns boolean.\n     */\n    static matchEventFullKeyCode(event, fullKeyCode) {\n        let keycode = _keyMap[event.key] || event.key;\n        let key = '';\n        if (fullKeyCode.indexOf('code.') > -1) {\n            keycode = event.code;\n            key = 'code.';\n        }\n        // the keycode could be unidentified so we have to check here\n        if (keycode == null || !keycode)\n            return false;\n        keycode = keycode.toLowerCase();\n        if (keycode === ' ') {\n            keycode = 'space'; // for readability\n        }\n        else if (keycode === '.') {\n            keycode = 'dot'; // because '.' is used as a separator in event names\n        }\n        MODIFIER_KEYS.forEach(modifierName => {\n            if (modifierName !== keycode) {\n                const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n                if (modifierGetter(event)) {\n                    key += modifierName + '.';\n                }\n            }\n        });\n        key += keycode;\n        return key === fullKeyCode;\n    }\n    /**\n     * Configures a handler callback for a key event.\n     * @param fullKey The event name that combines all simultaneous keystrokes.\n     * @param handler The function that responds to the key event.\n     * @param zone The zone in which the event occurred.\n     * @returns A callback function.\n     */\n    static eventCallback(fullKey, handler, zone) {\n        return (event) => {\n            if (KeyEventsPlugin.matchEventFullKeyCode(event, fullKey)) {\n                zone.runGuarded(() => handler(event));\n            }\n        };\n    }\n    /** @internal */\n    static _normalizeKey(keyName) {\n        // TODO: switch to a Map if the mapping grows too much\n        switch (keyName) {\n            case 'esc':\n                return 'escape';\n            default:\n                return keyName;\n        }\n    }\n}\nKeyEventsPlugin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: KeyEventsPlugin, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nKeyEventsPlugin.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: KeyEventsPlugin });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: KeyEventsPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || !!ngDevMode;\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/standalone-components).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```typescript\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```typescript\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n * @developerPreview\n */\nfunction bootstrapApplication(rootComponent, options) {\n    return ɵinternalCreateApplication({ rootComponent, ...createProvidersConfig(options) });\n}\n/**\n * Create an instance of an Angular application without bootstrapping any components. This is useful\n * for the situation where one wants to decouple application environment creation (a platform and\n * associated injectors) from rendering components on a screen. Components can be subsequently\n * bootstrapped on the returned `ApplicationRef`.\n *\n * @param options Extra configuration for the application environment, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n * @developerPreview\n */\nfunction createApplication(options) {\n    return ɵinternalCreateApplication(createProvidersConfig(options));\n}\nfunction createProvidersConfig(options) {\n    return {\n        appProviders: [\n            ...BROWSER_MODULE_PROVIDERS,\n            ...(options?.providers ?? []),\n        ],\n        platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS\n    };\n}\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @developerPreview\n * @publicApi\n */\nfunction provideProtractorTestingSupport() {\n    // Return a copy to prevent changes to the original array in case any in-place\n    // alterations are performed to the `provideProtractorTestingSupport` call results in app code.\n    return [...TESTABILITY_PROVIDERS];\n}\nfunction initDomAdapter() {\n    BrowserDomAdapter.makeCurrent();\n}\nfunction errorHandler() {\n    return new ErrorHandler();\n}\nfunction _document() {\n    // Tell ivy about the global document\n    ɵsetDocument(document);\n    return document;\n}\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [\n    { provide: PLATFORM_ID, useValue: ɵPLATFORM_BROWSER_ID },\n    { provide: PLATFORM_INITIALIZER, useValue: initDomAdapter, multi: true },\n    { provide: DOCUMENT, useFactory: _document, deps: [] },\n];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken(NG_DEV_MODE ? 'BrowserModule Providers Marker' : '');\nconst TESTABILITY_PROVIDERS = [\n    {\n        provide: ɵTESTABILITY_GETTER,\n        useClass: BrowserGetTestability,\n        deps: [],\n    },\n    {\n        provide: ɵTESTABILITY,\n        useClass: Testability,\n        deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n    },\n    {\n        provide: Testability,\n        useClass: Testability,\n        deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n    }\n];\nconst BROWSER_MODULE_PROVIDERS = [\n    { provide: ɵINJECTOR_SCOPE, useValue: 'root' },\n    { provide: ErrorHandler, useFactory: errorHandler, deps: [] }, {\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: DomEventsPlugin,\n        multi: true,\n        deps: [DOCUMENT, NgZone, PLATFORM_ID]\n    },\n    { provide: EVENT_MANAGER_PLUGINS, useClass: KeyEventsPlugin, multi: true, deps: [DOCUMENT] }, {\n        provide: DomRendererFactory2,\n        useClass: DomRendererFactory2,\n        deps: [EventManager, DomSharedStylesHost, APP_ID]\n    },\n    { provide: RendererFactory2, useExisting: DomRendererFactory2 },\n    { provide: SharedStylesHost, useExisting: DomSharedStylesHost },\n    { provide: DomSharedStylesHost, useClass: DomSharedStylesHost, deps: [DOCUMENT] },\n    { provide: EventManager, useClass: EventManager, deps: [EVENT_MANAGER_PLUGINS, NgZone] },\n    { provide: XhrFactory, useClass: BrowserXhr, deps: [] },\n    NG_DEV_MODE ? { provide: BROWSER_MODULE_PROVIDERS_MARKER, useValue: true } : []\n];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\nclass BrowserModule {\n    constructor(providersAlreadyPresent) {\n        if (NG_DEV_MODE && providersAlreadyPresent) {\n            throw new Error(`Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` +\n                `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n        }\n    }\n    /**\n     * Configures a browser-based app to transition from a server-rendered app, if\n     * one is present on the page.\n     *\n     * @param params An object containing an identifier for the app to transition.\n     * The ID must match between the client and server versions of the app.\n     * @returns The reconfigured `BrowserModule` to import into the app's root `AppModule`.\n     */\n    static withServerTransition(params) {\n        return {\n            ngModule: BrowserModule,\n            providers: [\n                { provide: APP_ID, useValue: params.appId },\n                { provide: TRANSITION_ID, useExisting: APP_ID },\n                SERVER_TRANSITION_PROVIDERS,\n            ],\n        };\n    }\n}\nBrowserModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: BrowserModule, deps: [{ token: BROWSER_MODULE_PROVIDERS_MARKER, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.NgModule });\nBrowserModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.3.0\", ngImport: i0, type: BrowserModule, exports: [CommonModule, ApplicationModule] });\nBrowserModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: BrowserModule, providers: [\n        ...BROWSER_MODULE_PROVIDERS,\n        ...TESTABILITY_PROVIDERS\n    ], imports: [CommonModule, ApplicationModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: BrowserModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        ...BROWSER_MODULE_PROVIDERS,\n                        ...TESTABILITY_PROVIDERS\n                    ],\n                    exports: [CommonModule, ApplicationModule],\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }, {\n                    type: Inject,\n                    args: [BROWSER_MODULE_PROVIDERS_MARKER]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Factory to create a `Meta` service instance for the current DOM document.\n */\nfunction createMeta() {\n    return new Meta(ɵɵinject(DOCUMENT));\n}\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n    constructor(_doc) {\n        this._doc = _doc;\n        this._dom = ɵgetDOM();\n    }\n    /**\n     * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * If an existing element is found, it is returned and is not modified in any way.\n     * @param tag The definition of a `<meta>` element to match or create.\n     * @param forceCreation True to create a new element without checking whether one already exists.\n     * @returns The existing element with the same attributes and values if found,\n     * the new element if no match is found, or `null` if the tag parameter is not defined.\n     */\n    addTag(tag, forceCreation = false) {\n        if (!tag)\n            return null;\n        return this._getOrCreateElement(tag, forceCreation);\n    }\n    /**\n     * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * @param tags An array of tag definitions to match or create.\n     * @param forceCreation True to create new elements without checking whether they already exist.\n     * @returns The matching elements if found, or the new elements.\n     */\n    addTags(tags, forceCreation = false) {\n        if (!tags)\n            return [];\n        return tags.reduce((result, tag) => {\n            if (tag) {\n                result.push(this._getOrCreateElement(tag, forceCreation));\n            }\n            return result;\n        }, []);\n    }\n    /**\n     * Retrieves a `<meta>` tag element in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching element, if any.\n     */\n    getTag(attrSelector) {\n        if (!attrSelector)\n            return null;\n        return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n    }\n    /**\n     * Retrieves a set of `<meta>` tag elements in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching elements, if any.\n     */\n    getTags(attrSelector) {\n        if (!attrSelector)\n            return [];\n        const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n        return list ? [].slice.call(list) : [];\n    }\n    /**\n     * Modifies an existing `<meta>` tag element in the current HTML document.\n     * @param tag The tag description with which to replace the existing tag content.\n     * @param selector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n     * replacement tag.\n     * @return The modified element.\n     */\n    updateTag(tag, selector) {\n        if (!tag)\n            return null;\n        selector = selector || this._parseSelector(tag);\n        const meta = this.getTag(selector);\n        if (meta) {\n            return this._setMetaElementAttributes(tag, meta);\n        }\n        return this._getOrCreateElement(tag, true);\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param attrSelector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     */\n    removeTag(attrSelector) {\n        this.removeTagElement(this.getTag(attrSelector));\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param meta The tag definition to match against to identify an existing tag.\n     */\n    removeTagElement(meta) {\n        if (meta) {\n            this._dom.remove(meta);\n        }\n    }\n    _getOrCreateElement(meta, forceCreation = false) {\n        if (!forceCreation) {\n            const selector = this._parseSelector(meta);\n            // It's allowed to have multiple elements with the same name so it's not enough to\n            // just check that element with the same name already present on the page. We also need to\n            // check if element has tag attributes\n            const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n            if (elem !== undefined)\n                return elem;\n        }\n        const element = this._dom.createElement('meta');\n        this._setMetaElementAttributes(meta, element);\n        const head = this._doc.getElementsByTagName('head')[0];\n        head.appendChild(element);\n        return element;\n    }\n    _setMetaElementAttributes(tag, el) {\n        Object.keys(tag).forEach((prop) => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n        return el;\n    }\n    _parseSelector(tag) {\n        const attr = tag.name ? 'name' : 'property';\n        return `${attr}=\"${tag[attr]}\"`;\n    }\n    _containsAttributes(tag, elem) {\n        return Object.keys(tag).every((key) => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n    }\n    _getMetaKeyMap(prop) {\n        return META_KEYS_MAP[prop] || prop;\n    }\n}\nMeta.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: Meta, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nMeta.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: Meta, providedIn: 'root', useFactory: createMeta, deps: [] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: Meta, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: createMeta, deps: [] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n    httpEquiv: 'http-equiv'\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Factory to create Title service.\n */\nfunction createTitle() {\n    return new Title(ɵɵinject(DOCUMENT));\n}\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n    /**\n     * Get the title of the current HTML document.\n     */\n    getTitle() {\n        return this._doc.title;\n    }\n    /**\n     * Set the title of the current HTML document.\n     * @param newTitle\n     */\n    setTitle(newTitle) {\n        this._doc.title = newTitle || '';\n    }\n}\nTitle.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: Title, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nTitle.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: Title, providedIn: 'root', useFactory: createTitle, deps: [] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: Title, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: createTitle, deps: [] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst CAMEL_CASE_REGEXP = /([A-Z])/g;\nconst DASH_CASE_REGEXP = /-([a-z])/g;\nfunction camelCaseToDashCase(input) {\n    return input.replace(CAMEL_CASE_REGEXP, (...m) => '-' + m[1].toLowerCase());\n}\nfunction dashCaseToCamelCase(input) {\n    return input.replace(DASH_CASE_REGEXP, (...m) => m[1].toUpperCase());\n}\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n    if (typeof COMPILED === 'undefined' || !COMPILED) {\n        // Note: we can't export `ng` when using closure enhanced optimization as:\n        // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n        // - we can't declare a closure extern as the namespace `ng` is already used within Google\n        //   for typings for angularJS (via `goog.provide('ng....')`).\n        const ng = ɵglobal['ng'] = ɵglobal['ng'] || {};\n        ng[name] = value;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst win = typeof window !== 'undefined' && window || {};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass ChangeDetectionPerfRecord {\n    constructor(msPerTick, numTicks) {\n        this.msPerTick = msPerTick;\n        this.numTicks = numTicks;\n    }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n    constructor(ref) {\n        this.appRef = ref.injector.get(ApplicationRef);\n    }\n    // tslint:disable:no-console\n    /**\n     * Exercises change detection in a loop and then prints the average amount of\n     * time in milliseconds how long a single round of change detection takes for\n     * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n     * of 500 milliseconds.\n     *\n     * Optionally, a user may pass a `config` parameter containing a map of\n     * options. Supported options are:\n     *\n     * `record` (boolean) - causes the profiler to record a CPU profile while\n     * it exercises the change detector. Example:\n     *\n     * ```\n     * ng.profiler.timeChangeDetection({record: true})\n     * ```\n     */\n    timeChangeDetection(config) {\n        const record = config && config['record'];\n        const profileName = 'Change Detection';\n        // Profiler is not available in Android browsers without dev tools opened\n        const isProfilerAvailable = win.console.profile != null;\n        if (record && isProfilerAvailable) {\n            win.console.profile(profileName);\n        }\n        const start = performanceNow();\n        let numTicks = 0;\n        while (numTicks < 5 || (performanceNow() - start) < 500) {\n            this.appRef.tick();\n            numTicks++;\n        }\n        const end = performanceNow();\n        if (record && isProfilerAvailable) {\n            win.console.profileEnd(profileName);\n        }\n        const msPerTick = (end - start) / numTicks;\n        win.console.log(`ran ${numTicks} change detection cycles`);\n        win.console.log(`${msPerTick.toFixed(2)} ms per check`);\n        return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n    }\n}\nfunction performanceNow() {\n    return win.performance && win.performance.now ? win.performance.now() :\n        new Date().getTime();\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n    exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n    return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n    exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction escapeHtml(text) {\n    const escapedText = {\n        '&': '&a;',\n        '\"': '&q;',\n        '\\'': '&s;',\n        '<': '&l;',\n        '>': '&g;',\n    };\n    return text.replace(/[&\"'<>]/g, s => escapedText[s]);\n}\nfunction unescapeHtml(text) {\n    const unescapedText = {\n        '&a;': '&',\n        '&q;': '\"',\n        '&s;': '\\'',\n        '&l;': '<',\n        '&g;': '>',\n    };\n    return text.replace(/&[^;]+;/g, s => unescapedText[s]);\n}\n/**\n * Create a `StateKey<T>` that can be used to store value of type T with `TransferState`.\n *\n * Example:\n *\n * ```\n * const COUNTER_KEY = makeStateKey<number>('counter');\n * let value = 10;\n *\n * transferState.set(COUNTER_KEY, value);\n * ```\n *\n * @publicApi\n */\nfunction makeStateKey(key) {\n    return key;\n}\n/**\n * A key value store that is transferred from the application on the server side to the application\n * on the client side.\n *\n * The `TransferState` is available as an injectable token.\n * On the client, just inject this token using DI and use it, it will be lazily initialized.\n * On the server it's already included if `renderApplication` function is used. Otherwise, import\n * the `ServerTransferStateModule` module to make the `TransferState` available.\n *\n * The values in the store are serialized/deserialized using JSON.stringify/JSON.parse. So only\n * boolean, number, string, null and non-class objects will be serialized and deserialized in a\n * non-lossy manner.\n *\n * @publicApi\n */\nclass TransferState {\n    constructor() {\n        this.store = {};\n        this.onSerializeCallbacks = {};\n    }\n    /**\n     * Get the value corresponding to a key. Return `defaultValue` if key is not found.\n     */\n    get(key, defaultValue) {\n        return this.store[key] !== undefined ? this.store[key] : defaultValue;\n    }\n    /**\n     * Set the value corresponding to a key.\n     */\n    set(key, value) {\n        this.store[key] = value;\n    }\n    /**\n     * Remove a key from the store.\n     */\n    remove(key) {\n        delete this.store[key];\n    }\n    /**\n     * Test whether a key exists in the store.\n     */\n    hasKey(key) {\n        return this.store.hasOwnProperty(key);\n    }\n    /**\n     * Indicates whether the state is empty.\n     */\n    get isEmpty() {\n        return Object.keys(this.store).length === 0;\n    }\n    /**\n     * Register a callback to provide the value for a key when `toJson` is called.\n     */\n    onSerialize(key, callback) {\n        this.onSerializeCallbacks[key] = callback;\n    }\n    /**\n     * Serialize the current state of the store to JSON.\n     */\n    toJson() {\n        // Call the onSerialize callbacks and put those values into the store.\n        for (const key in this.onSerializeCallbacks) {\n            if (this.onSerializeCallbacks.hasOwnProperty(key)) {\n                try {\n                    this.store[key] = this.onSerializeCallbacks[key]();\n                }\n                catch (e) {\n                    console.warn('Exception in onSerialize callback: ', e);\n                }\n            }\n        }\n        return JSON.stringify(this.store);\n    }\n}\nTransferState.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: TransferState, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nTransferState.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: TransferState, providedIn: 'root', useFactory: () => {\n        const doc = inject(DOCUMENT);\n        const appId = inject(APP_ID);\n        const state = new TransferState();\n        state.store = retrieveTransferredState(doc, appId);\n        return state;\n    } });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: TransferState, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                    useFactory: () => {\n                        const doc = inject(DOCUMENT);\n                        const appId = inject(APP_ID);\n                        const state = new TransferState();\n                        state.store = retrieveTransferredState(doc, appId);\n                        return state;\n                    }\n                }]\n        }] });\nfunction retrieveTransferredState(doc, appId) {\n    // Locate the script tag with the JSON data transferred from the server.\n    // The id of the script tag is set to the Angular appId + 'state'.\n    const script = doc.getElementById(appId + '-state');\n    let initialState = {};\n    if (script && script.textContent) {\n        try {\n            // Avoid using any here as it triggers lint errors in google3 (any is not allowed).\n            initialState = JSON.parse(unescapeHtml(script.textContent));\n        }\n        catch (e) {\n            console.warn('Exception while restoring TransferState for app ' + appId, e);\n        }\n    }\n    return initialState;\n}\n/**\n * NgModule to install on the client side while using the `TransferState` to transfer state from\n * server to client.\n *\n * @publicApi\n * @deprecated no longer needed, you can inject the `TransferState` in an app without providing\n *     this module.\n */\nclass BrowserTransferStateModule {\n}\nBrowserTransferStateModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: BrowserTransferStateModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nBrowserTransferStateModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.3.0\", ngImport: i0, type: BrowserTransferStateModule });\nBrowserTransferStateModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: BrowserTransferStateModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: BrowserTransferStateModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n    /**\n     * Match all nodes.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n     */\n    static all() {\n        return () => true;\n    }\n    /**\n     * Match elements by the given CSS selector.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n     */\n    static css(selector) {\n        return (debugElement) => {\n            return debugElement.nativeElement != null ?\n                elementMatches(debugElement.nativeElement, selector) :\n                false;\n        };\n    }\n    /**\n     * Match nodes that have the given directive present.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n     */\n    static directive(type) {\n        return (debugNode) => debugNode.providerTokens.indexOf(type) !== -1;\n    }\n}\nfunction elementMatches(n, selector) {\n    if (ɵgetDOM().isElementNode(n)) {\n        return n.matches && n.matches(selector) ||\n            n.msMatchesSelector && n.msMatchesSelector(selector) ||\n            n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n    }\n    return false;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n    // pan\n    'pan': true,\n    'panstart': true,\n    'panmove': true,\n    'panend': true,\n    'pancancel': true,\n    'panleft': true,\n    'panright': true,\n    'panup': true,\n    'pandown': true,\n    // pinch\n    'pinch': true,\n    'pinchstart': true,\n    'pinchmove': true,\n    'pinchend': true,\n    'pinchcancel': true,\n    'pinchin': true,\n    'pinchout': true,\n    // press\n    'press': true,\n    'pressup': true,\n    // rotate\n    'rotate': true,\n    'rotatestart': true,\n    'rotatemove': true,\n    'rotateend': true,\n    'rotatecancel': true,\n    // swipe\n    'swipe': true,\n    'swipeleft': true,\n    'swiperight': true,\n    'swipeup': true,\n    'swipedown': true,\n    // tap\n    'tap': true,\n    'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see `HammerGestureConfig`\n *\n * @ngModule HammerModule\n * @publicApi\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken('HammerGestureConfig');\n/**\n * Injection token used to provide a {@link HammerLoader} to Angular.\n *\n * @publicApi\n */\nconst HAMMER_LOADER = new InjectionToken('HammerLoader');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\nclass HammerGestureConfig {\n    constructor() {\n        /**\n         * A set of supported event names for gestures to be used in Angular.\n         * Angular supports all built-in recognizers, as listed in\n         * [HammerJS documentation](https://hammerjs.github.io/).\n         */\n        this.events = [];\n        /**\n         * Maps gesture event names to a set of configuration options\n         * that specify overrides to the default values for specific properties.\n         *\n         * The key is a supported event name to be configured,\n         * and the options object contains a set of properties, with override values\n         * to be applied to the named recognizer event.\n         * For example, to disable recognition of the rotate event, specify\n         *  `{\"rotate\": {\"enable\": false}}`.\n         *\n         * Properties that are not present take the HammerJS default values.\n         * For information about which properties are supported for which events,\n         * and their allowed and default values, see\n         * [HammerJS documentation](https://hammerjs.github.io/).\n         *\n         */\n        this.overrides = {};\n    }\n    /**\n     * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n     * and attaches it to a given HTML element.\n     * @param element The element that will recognize gestures.\n     * @returns A HammerJS event-manager object.\n     */\n    buildHammer(element) {\n        const mc = new Hammer(element, this.options);\n        mc.get('pinch').set({ enable: true });\n        mc.get('rotate').set({ enable: true });\n        for (const eventName in this.overrides) {\n            mc.get(eventName).set(this.overrides[eventName]);\n        }\n        return mc;\n    }\n}\nHammerGestureConfig.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HammerGestureConfig, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nHammerGestureConfig.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HammerGestureConfig });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HammerGestureConfig, decorators: [{\n            type: Injectable\n        }] });\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n    constructor(doc, _config, console, loader) {\n        super(doc);\n        this._config = _config;\n        this.console = console;\n        this.loader = loader;\n        this._loaderPromise = null;\n    }\n    supports(eventName) {\n        if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n            return false;\n        }\n        if (!window.Hammer && !this.loader) {\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                this.console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` +\n                    `loaded and no custom loader has been specified.`);\n            }\n            return false;\n        }\n        return true;\n    }\n    addEventListener(element, eventName, handler) {\n        const zone = this.manager.getZone();\n        eventName = eventName.toLowerCase();\n        // If Hammer is not present but a loader is specified, we defer adding the event listener\n        // until Hammer is loaded.\n        if (!window.Hammer && this.loader) {\n            this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader());\n            // This `addEventListener` method returns a function to remove the added listener.\n            // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n            // than remove anything.\n            let cancelRegistration = false;\n            let deregister = () => {\n                cancelRegistration = true;\n            };\n            zone.runOutsideAngular(() => this._loaderPromise\n                .then(() => {\n                // If Hammer isn't actually loaded when the custom loader resolves, give up.\n                if (!window.Hammer) {\n                    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                        this.console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n                    }\n                    deregister = () => { };\n                    return;\n                }\n                if (!cancelRegistration) {\n                    // Now that Hammer is loaded and the listener is being loaded for real,\n                    // the deregistration function changes from canceling registration to\n                    // removal.\n                    deregister = this.addEventListener(element, eventName, handler);\n                }\n            })\n                .catch(() => {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    this.console.warn(`The \"${eventName}\" event cannot be bound because the custom ` +\n                        `Hammer.JS loader failed.`);\n                }\n                deregister = () => { };\n            }));\n            // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n            // can change the behavior of `deregister` once the listener is added. Using a closure in\n            // this way allows us to avoid any additional data structures to track listener removal.\n            return () => {\n                deregister();\n            };\n        }\n        return zone.runOutsideAngular(() => {\n            // Creating the manager bind events, must be done outside of angular\n            const mc = this._config.buildHammer(element);\n            const callback = function (eventObj) {\n                zone.runGuarded(function () {\n                    handler(eventObj);\n                });\n            };\n            mc.on(eventName, callback);\n            return () => {\n                mc.off(eventName, callback);\n                // destroy mc to prevent memory leak\n                if (typeof mc.destroy === 'function') {\n                    mc.destroy();\n                }\n            };\n        });\n    }\n    isCustomEvent(eventName) {\n        return this._config.events.indexOf(eventName) > -1;\n    }\n}\nHammerGesturesPlugin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HammerGesturesPlugin, deps: [{ token: DOCUMENT }, { token: HAMMER_GESTURE_CONFIG }, { token: i0.ɵConsole }, { token: HAMMER_LOADER, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nHammerGesturesPlugin.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HammerGesturesPlugin });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HammerGesturesPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: HammerGestureConfig, decorators: [{\n                    type: Inject,\n                    args: [HAMMER_GESTURE_CONFIG]\n                }] }, { type: i0.ɵConsole }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [HAMMER_LOADER]\n                }] }]; } });\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's EventManager.\n *\n * @publicApi\n */\nclass HammerModule {\n}\nHammerModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HammerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nHammerModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.3.0\", ngImport: i0, type: HammerModule });\nHammerModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HammerModule, providers: [\n        {\n            provide: EVENT_MANAGER_PLUGINS,\n            useClass: HammerGesturesPlugin,\n            multi: true,\n            deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n        },\n        { provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig, deps: [] },\n    ] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HammerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        {\n                            provide: EVENT_MANAGER_PLUGINS,\n                            useClass: HammerGesturesPlugin,\n                            multi: true,\n                            deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n                        },\n                        { provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig, deps: [] },\n                    ]\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {\n}\nDomSanitizer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: DomSanitizer, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nDomSanitizer.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: DomSanitizer, providedIn: 'root', useExisting: i0.forwardRef(function () { return DomSanitizerImpl; }) });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: DomSanitizer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useExisting: forwardRef(() => DomSanitizerImpl) }]\n        }] });\nfunction domSanitizerImplFactory(injector) {\n    return new DomSanitizerImpl(injector.get(DOCUMENT));\n}\nclass DomSanitizerImpl extends DomSanitizer {\n    constructor(_doc) {\n        super();\n        this._doc = _doc;\n    }\n    sanitize(ctx, value) {\n        if (value == null)\n            return null;\n        switch (ctx) {\n            case SecurityContext.NONE:\n                return value;\n            case SecurityContext.HTML:\n                if (ɵallowSanitizationBypassAndThrow(value, \"HTML\" /* BypassType.Html */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return ɵ_sanitizeHtml(this._doc, String(value)).toString();\n            case SecurityContext.STYLE:\n                if (ɵallowSanitizationBypassAndThrow(value, \"Style\" /* BypassType.Style */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return value;\n            case SecurityContext.SCRIPT:\n                if (ɵallowSanitizationBypassAndThrow(value, \"Script\" /* BypassType.Script */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                throw new Error('unsafe value used in a script context');\n            case SecurityContext.URL:\n                if (ɵallowSanitizationBypassAndThrow(value, \"URL\" /* BypassType.Url */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return ɵ_sanitizeUrl(String(value));\n            case SecurityContext.RESOURCE_URL:\n                if (ɵallowSanitizationBypassAndThrow(value, \"ResourceURL\" /* BypassType.ResourceUrl */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                throw new Error('unsafe value used in a resource URL context (see https://g.co/ng/security#xss)');\n            default:\n                throw new Error(`Unexpected SecurityContext ${ctx} (see https://g.co/ng/security#xss)`);\n        }\n    }\n    bypassSecurityTrustHtml(value) {\n        return ɵbypassSanitizationTrustHtml(value);\n    }\n    bypassSecurityTrustStyle(value) {\n        return ɵbypassSanitizationTrustStyle(value);\n    }\n    bypassSecurityTrustScript(value) {\n        return ɵbypassSanitizationTrustScript(value);\n    }\n    bypassSecurityTrustUrl(value) {\n        return ɵbypassSanitizationTrustUrl(value);\n    }\n    bypassSecurityTrustResourceUrl(value) {\n        return ɵbypassSanitizationTrustResourceUrl(value);\n    }\n}\nDomSanitizerImpl.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: DomSanitizerImpl, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nDomSanitizerImpl.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: DomSanitizerImpl, providedIn: 'root', useFactory: domSanitizerImplFactory, deps: [{ token: Injector }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: DomSanitizerImpl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: domSanitizerImplFactory, deps: [Injector] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('14.3.0');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserModule, BrowserTransferStateModule, By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManager, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, Meta, Title, TransferState, VERSION, bootstrapApplication, createApplication, disableDebugTools, enableDebugTools, makeStateKey, platformBrowser, provideProtractorTestingSupport, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, DomRendererFactory2 as ɵDomRendererFactory2, DomSanitizerImpl as ɵDomSanitizerImpl, DomSharedStylesHost as ɵDomSharedStylesHost, HammerGesturesPlugin as ɵHammerGesturesPlugin, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, KeyEventsPlugin as ɵKeyEventsPlugin, NAMESPACE_URIS as ɵNAMESPACE_URIS, SharedStylesHost as ɵSharedStylesHost, TRANSITION_ID as ɵTRANSITION_ID, escapeHtml as ɵescapeHtml, flattenStyles as ɵflattenStyles, initDomAdapter as ɵinitDomAdapter, shimContentAttribute as ɵshimContentAttribute, shimHostAttribute as ɵshimHostAttribute };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AAEA,SAASA,WAAT,EAAsBC,kBAAtB,EAA0CC,iBAA1C,EAA6DC,OAA7D,EAAsEC,QAAtE,EAAgFC,oBAAhF,EAAsGC,UAAtG,EAAkHC,YAAlH,QAAsI,iBAAtI;AACA,SAASJ,OAAT,QAAwB,iBAAxB;AACA,OAAO,KAAKK,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,qBAAzB,EAAgDC,eAAhD,EAAiEC,QAAjE,EAA2EC,OAA3E,EAAoFC,UAApF,EAAgGC,MAAhG,EAAwGC,iBAAxG,EAA2HC,MAA3H,EAAmIC,mBAAnI,EAAwJC,0BAAxJ,EAAoLC,YAApL,EAAkMC,YAAlM,EAAgNC,WAAhN,EAA6NC,oBAA7N,EAAmPC,qBAAnP,EAA0QC,YAA1Q,EAAwRC,mBAAxR,EAA6SC,YAA7S,EAA2TC,WAA3T,EAAwUC,MAAxU,EAAgVC,mBAAhV,EAAqWC,eAArW,EAAsXC,gBAAtX,EAAwYC,iBAAxY,EAA2ZC,QAA3Z,EAAqaC,QAAra,EAA+aC,QAA/a,EAAybC,QAAzb,EAAmcC,cAAnc,EAAmdC,MAAnd,EAA2dC,QAA3d,EAAqeC,UAAre,EAAifC,eAAjf,EAAkgBC,gCAAlgB,EAAoiBC,gBAApiB,EAAsjBC,aAAtjB,EAAqkBC,cAArkB,EAAqlBC,4BAArlB,EAAmnBC,6BAAnnB,EAAkpBC,8BAAlpB,EAAkrBC,2BAAlrB,EAA+sBC,mCAA/sB,EAAovBC,OAApvB,QAAmwB,eAAnwB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,wBAAN,SAAuCrD,WAAvC,CAAmD;EAC/CsD,WAAW,GAAG;IACV,MAAM,GAAGC,SAAT;IACA,KAAKC,iBAAL,GAAyB,IAAzB;EACH;;AAJ8C;AAOnD;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMC,iBAAN,SAAgCJ,wBAAhC,CAAyD;EACnC,OAAXK,WAAW,GAAG;IACjBzD,kBAAkB,CAAC,IAAIwD,iBAAJ,EAAD,CAAlB;EACH;;EACDE,WAAW,CAACC,EAAD,EAAKC,GAAL,EAAUC,QAAV,EAAoB;IAC3BF,EAAE,CAACG,gBAAH,CAAoBF,GAApB,EAAyBC,QAAzB,EAAmC,KAAnC,EAD2B,CAE3B;IACA;;IACA,OAAO,MAAM;MACTF,EAAE,CAACI,mBAAH,CAAuBH,GAAvB,EAA4BC,QAA5B,EAAsC,KAAtC;IACH,CAFD;EAGH;;EACDG,aAAa,CAACL,EAAD,EAAKC,GAAL,EAAU;IACnBD,EAAE,CAACK,aAAH,CAAiBJ,GAAjB;EACH;;EACDK,MAAM,CAACC,IAAD,EAAO;IACT,IAAIA,IAAI,CAACC,UAAT,EAAqB;MACjBD,IAAI,CAACC,UAAL,CAAgBC,WAAhB,CAA4BF,IAA5B;IACH;EACJ;;EACDG,aAAa,CAACC,OAAD,EAAUC,GAAV,EAAe;IACxBA,GAAG,GAAGA,GAAG,IAAI,KAAKC,kBAAL,EAAb;IACA,OAAOD,GAAG,CAACF,aAAJ,CAAkBC,OAAlB,CAAP;EACH;;EACDG,kBAAkB,GAAG;IACjB,OAAOC,QAAQ,CAACC,cAAT,CAAwBC,kBAAxB,CAA2C,WAA3C,CAAP;EACH;;EACDJ,kBAAkB,GAAG;IACjB,OAAOE,QAAP;EACH;;EACDG,aAAa,CAACX,IAAD,EAAO;IAChB,OAAOA,IAAI,CAACY,QAAL,KAAkBC,IAAI,CAACC,YAA9B;EACH;;EACDC,YAAY,CAACf,IAAD,EAAO;IACf,OAAOA,IAAI,YAAYgB,gBAAvB;EACH;EACD;;;EACAC,oBAAoB,CAACZ,GAAD,EAAMa,MAAN,EAAc;IAC9B,IAAIA,MAAM,KAAK,QAAf,EAAyB;MACrB,OAAOC,MAAP;IACH;;IACD,IAAID,MAAM,KAAK,UAAf,EAA2B;MACvB,OAAOb,GAAP;IACH;;IACD,IAAIa,MAAM,KAAK,MAAf,EAAuB;MACnB,OAAOb,GAAG,CAACe,IAAX;IACH;;IACD,OAAO,IAAP;EACH;;EACDC,WAAW,CAAChB,GAAD,EAAM;IACb,MAAMiB,IAAI,GAAGC,kBAAkB,EAA/B;IACA,OAAOD,IAAI,IAAI,IAAR,GAAe,IAAf,GAAsBE,YAAY,CAACF,IAAD,CAAzC;EACH;;EACDG,gBAAgB,GAAG;IACfC,WAAW,GAAG,IAAd;EACH;;EACDC,YAAY,GAAG;IACX,OAAOR,MAAM,CAACS,SAAP,CAAiBC,SAAxB;EACH;;EACDC,SAAS,CAACC,IAAD,EAAO;IACZ,OAAOhG,iBAAiB,CAACyE,QAAQ,CAACwB,MAAV,EAAkBD,IAAlB,CAAxB;EACH;;AA7DoD;;AA+DzD,IAAIL,WAAW,GAAG,IAAlB;;AACA,SAASH,kBAAT,GAA8B;EAC1BG,WAAW,GAAGA,WAAW,IAAIlB,QAAQ,CAACyB,aAAT,CAAuB,MAAvB,CAA7B;EACA,OAAOP,WAAW,GAAGA,WAAW,CAACQ,YAAZ,CAAyB,MAAzB,CAAH,GAAsC,IAAxD;AACH,C,CACD;;;AACA,IAAIC,cAAJ;;AACA,SAASX,YAAT,CAAsBY,GAAtB,EAA2B;EACvBD,cAAc,GAAGA,cAAc,IAAI3B,QAAQ,CAACL,aAAT,CAAuB,GAAvB,CAAnC;EACAgC,cAAc,CAACE,YAAf,CAA4B,MAA5B,EAAoCD,GAApC;EACA,MAAME,QAAQ,GAAGH,cAAc,CAACI,QAAhC;EACA,OAAOD,QAAQ,CAACE,MAAT,CAAgB,CAAhB,MAAuB,GAAvB,GAA6BF,QAA7B,GAAyC,IAAGA,QAAS,EAA5D;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMG,aAAa,GAAG,IAAInG,cAAJ,CAAmB,eAAnB,CAAtB;;AACA,SAASoG,qBAAT,CAA+BC,YAA/B,EAA6CnC,QAA7C,EAAuDoC,QAAvD,EAAiE;EAC7D,OAAO,MAAM;IACT;IACA;IACAA,QAAQ,CAACC,GAAT,CAAatG,qBAAb,EAAoCuG,WAApC,CAAgDC,IAAhD,CAAqD,MAAM;MACvD,MAAMC,GAAG,GAAGhH,OAAO,EAAnB;MACA,MAAMiH,MAAM,GAAGzC,QAAQ,CAAC0C,gBAAT,CAA2B,wBAAuBP,YAAa,IAA/D,CAAf;;MACA,KAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,MAAM,CAACG,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;QACpCH,GAAG,CAACjD,MAAJ,CAAWkD,MAAM,CAACE,CAAD,CAAjB;MACH;IACJ,CAND;EAOH,CAVD;AAWH;;AACD,MAAME,2BAA2B,GAAG,CAChC;EACIC,OAAO,EAAE9G,eADb;EAEI+G,UAAU,EAAEb,qBAFhB;EAGIc,IAAI,EAAE,CAACf,aAAD,EAAgBxG,QAAhB,EAA0BQ,QAA1B,CAHV;EAIIgH,KAAK,EAAE;AAJX,CADgC,CAApC;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,qBAAN,CAA4B;EACxBC,WAAW,CAACC,QAAD,EAAW;IAClBlH,OAAO,CAAC,uBAAD,CAAP,GAAmC,CAACmH,IAAD,EAAOC,eAAe,GAAG,IAAzB,KAAkC;MACjE,MAAMC,WAAW,GAAGH,QAAQ,CAACI,qBAAT,CAA+BH,IAA/B,EAAqCC,eAArC,CAApB;;MACA,IAAIC,WAAW,IAAI,IAAnB,EAAyB;QACrB,MAAM,IAAIE,KAAJ,CAAU,yCAAV,CAAN;MACH;;MACD,OAAOF,WAAP;IACH,CAND;;IAOArH,OAAO,CAAC,4BAAD,CAAP,GAAwC,MAAMkH,QAAQ,CAACM,mBAAT,EAA9C;;IACAxH,OAAO,CAAC,2BAAD,CAAP,GAAuC,MAAMkH,QAAQ,CAACO,kBAAT,EAA7C;;IACA,MAAMC,aAAa,GAAG,CAACC;IAAS;IAAV,KAAgC;MAClD,MAAMC,aAAa,GAAG5H,OAAO,CAAC,4BAAD,CAAP,EAAtB;MACA,IAAI6H,KAAK,GAAGD,aAAa,CAAClB,MAA1B;MACA,IAAIoB,OAAO,GAAG,KAAd;;MACA,MAAMC,SAAS,GAAG,UAAUC;MAAS;MAAnB,EAAsC;QACpDF,OAAO,GAAGA,OAAO,IAAIE,QAArB;QACAH,KAAK;;QACL,IAAIA,KAAK,IAAI,CAAb,EAAgB;UACZF,QAAQ,CAACG,OAAD,CAAR;QACH;MACJ,CAND;;MAOAF,aAAa,CAACK,OAAd,CAAsB,UAAUZ;MAAY;MAAtB,EAAyC;QAC3DA,WAAW,CAACa,UAAZ,CAAuBH,SAAvB;MACH,CAFD;IAGH,CAdD;;IAeA,IAAI,CAAC/H,OAAO,CAAC,sBAAD,CAAZ,EAAsC;MAClCA,OAAO,CAAC,sBAAD,CAAP,GAAkC,EAAlC;IACH;;IACDA,OAAO,CAAC,sBAAD,CAAP,CAAgCmI,IAAhC,CAAqCT,aAArC;EACH;;EACDJ,qBAAqB,CAACJ,QAAD,EAAWC,IAAX,EAAiBC,eAAjB,EAAkC;IACnD,IAAID,IAAI,IAAI,IAAZ,EAAkB;MACd,OAAO,IAAP;IACH;;IACD,MAAMiB,CAAC,GAAGlB,QAAQ,CAACmB,cAAT,CAAwBlB,IAAxB,CAAV;;IACA,IAAIiB,CAAC,IAAI,IAAT,EAAe;MACX,OAAOA,CAAP;IACH,CAFD,MAGK,IAAI,CAAChB,eAAL,EAAsB;MACvB,OAAO,IAAP;IACH;;IACD,IAAI9H,OAAO,GAAG+E,YAAV,CAAuB8C,IAAvB,CAAJ,EAAkC;MAC9B,OAAO,KAAKG,qBAAL,CAA2BJ,QAA3B,EAAqCC,IAAI,CAACmB,IAA1C,EAAgD,IAAhD,CAAP;IACH;;IACD,OAAO,KAAKhB,qBAAL,CAA2BJ,QAA3B,EAAqCC,IAAI,CAACoB,aAA1C,EAAyD,IAAzD,CAAP;EACH;;AA9CuB;AAiD5B;AACA;AACA;;;AACA,MAAMC,UAAN,CAAiB;EACbC,KAAK,GAAG;IACJ,OAAO,IAAIC,cAAJ,EAAP;EACH;;AAHY;;AAKjBF,UAAU,CAACG,IAAX;EAAA,iBAAuGH,UAAvG;AAAA;;AACAA,UAAU,CAACI,KAAX,kBAD6FjJ,EAC7F;EAAA,OAA2G6I,UAA3G;EAAA,SAA2GA,UAA3G;AAAA;;AACA;EAAA,mDAF6F7I,EAE7F,mBAA2F6I,UAA3F,EAAmH,CAAC;IACxGK,IAAI,EAAE5I;EADkG,CAAD,CAAnH;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM6I,qBAAqB,GAAG,IAAIlJ,cAAJ,CAAmB,qBAAnB,CAA9B;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMmJ,YAAN,CAAmB;EACf;AACJ;AACA;EACItG,WAAW,CAACuG,OAAD,EAAUC,KAAV,EAAiB;IACxB,KAAKA,KAAL,GAAaA,KAAb;IACA,KAAKC,kBAAL,GAA0B,IAAIC,GAAJ,EAA1B;IACAH,OAAO,CAACf,OAAR,CAAgBmB,CAAC,IAAIA,CAAC,CAACC,OAAF,GAAY,IAAjC;IACA,KAAKC,QAAL,GAAgBN,OAAO,CAACO,KAAR,GAAgBC,OAAhB,EAAhB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACItG,gBAAgB,CAACuG,OAAD,EAAUC,SAAV,EAAqBC,OAArB,EAA8B;IAC1C,MAAMC,MAAM,GAAG,KAAKC,cAAL,CAAoBH,SAApB,CAAf;;IACA,OAAOE,MAAM,CAAC1G,gBAAP,CAAwBuG,OAAxB,EAAiCC,SAAjC,EAA4CC,OAA5C,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIG,sBAAsB,CAACtF,MAAD,EAASkF,SAAT,EAAoBC,OAApB,EAA6B;IAC/C,MAAMC,MAAM,GAAG,KAAKC,cAAL,CAAoBH,SAApB,CAAf;;IACA,OAAOE,MAAM,CAACE,sBAAP,CAA8BtF,MAA9B,EAAsCkF,SAAtC,EAAiDC,OAAjD,CAAP;EACH;EACD;AACJ;AACA;;;EACII,OAAO,GAAG;IACN,OAAO,KAAKd,KAAZ;EACH;EACD;;;EACAY,cAAc,CAACH,SAAD,EAAY;IACtB,MAAME,MAAM,GAAG,KAAKV,kBAAL,CAAwB/C,GAAxB,CAA4BuD,SAA5B,CAAf;;IACA,IAAIE,MAAJ,EAAY;MACR,OAAOA,MAAP;IACH;;IACD,MAAMZ,OAAO,GAAG,KAAKM,QAArB;;IACA,KAAK,IAAI7C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuC,OAAO,CAACtC,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;MACrC,MAAMmD,MAAM,GAAGZ,OAAO,CAACvC,CAAD,CAAtB;;MACA,IAAImD,MAAM,CAACI,QAAP,CAAgBN,SAAhB,CAAJ,EAAgC;QAC5B,KAAKR,kBAAL,CAAwBe,GAAxB,CAA4BP,SAA5B,EAAuCE,MAAvC;;QACA,OAAOA,MAAP;MACH;IACJ;;IACD,MAAM,IAAIrC,KAAJ,CAAW,2CAA0CmC,SAAU,EAA/D,CAAN;EACH;;AA1Dc;;AA4DnBX,YAAY,CAACJ,IAAb;EAAA,iBAAyGI,YAAzG,EArF6FpJ,EAqF7F,UAAuImJ,qBAAvI,GArF6FnJ,EAqF7F,UAAyKA,EAAE,CAACqB,MAA5K;AAAA;;AACA+H,YAAY,CAACH,KAAb,kBAtF6FjJ,EAsF7F;EAAA,OAA6GoJ,YAA7G;EAAA,SAA6GA,YAA7G;AAAA;;AACA;EAAA,mDAvF6FpJ,EAuF7F,mBAA2FoJ,YAA3F,EAAqH,CAAC;IAC1GF,IAAI,EAAE5I;EADoG,CAAD,CAArH,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAE4I,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9DtB,IAAI,EAAE3I,MADwD;QAE9DkK,IAAI,EAAE,CAACtB,qBAAD;MAFwD,CAAD;IAA/B,CAAD,EAG3B;MAAED,IAAI,EAAElJ,EAAE,CAACqB;IAAX,CAH2B,CAAP;EAGG,CAL7C;AAAA;;AAMA,MAAMqJ,kBAAN,CAAyB;EACrB5H,WAAW,CAAC6H,IAAD,EAAO;IACd,KAAKA,IAAL,GAAYA,IAAZ;EACH;;EACDR,sBAAsB,CAACL,OAAD,EAAUC,SAAV,EAAqBC,OAArB,EAA8B;IAChD,MAAMnF,MAAM,GAAGlF,OAAO,GAAGiF,oBAAV,CAA+B,KAAK+F,IAApC,EAA0Cb,OAA1C,CAAf;;IACA,IAAI,CAACjF,MAAL,EAAa;MACT,MAAM,IAAI+C,KAAJ,CAAW,4BAA2B/C,MAAO,cAAakF,SAAU,EAApE,CAAN;IACH;;IACD,OAAO,KAAKxG,gBAAL,CAAsBsB,MAAtB,EAA8BkF,SAA9B,EAAyCC,OAAzC,CAAP;EACH;;AAVoB;AAazB;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMY,gBAAN,CAAuB;EACnB9H,WAAW,GAAG;IACV;IACA,KAAK+H,UAAL,GAAkB,IAAIC,GAAJ,EAAlB;EACH;;EACDC,SAAS,CAACnE,MAAD,EAAS;IACd,MAAMoE,SAAS,GAAG,IAAIF,GAAJ,EAAlB;IACAlE,MAAM,CAAC0B,OAAP,CAAe2C,KAAK,IAAI;MACpB,IAAI,CAAC,KAAKJ,UAAL,CAAgBK,GAAhB,CAAoBD,KAApB,CAAL,EAAiC;QAC7B,KAAKJ,UAAL,CAAgBM,GAAhB,CAAoBF,KAApB;;QACAD,SAAS,CAACG,GAAV,CAAcF,KAAd;MACH;IACJ,CALD;IAMA,KAAKG,aAAL,CAAmBJ,SAAnB;EACH;;EACDI,aAAa,CAACJ,SAAD,EAAY,CAAG;;EAC5BK,YAAY,GAAG;IACX,OAAOC,KAAK,CAACC,IAAN,CAAW,KAAKV,UAAhB,CAAP;EACH;;AAlBkB;;AAoBvBD,gBAAgB,CAAC5B,IAAjB;EAAA,iBAA6G4B,gBAA7G;AAAA;;AACAA,gBAAgB,CAAC3B,KAAjB,kBAtI6FjJ,EAsI7F;EAAA,OAAiH4K,gBAAjH;EAAA,SAAiHA,gBAAjH;AAAA;;AACA;EAAA,mDAvI6F5K,EAuI7F,mBAA2F4K,gBAA3F,EAAyH,CAAC;IAC9G1B,IAAI,EAAE5I;EADwG,CAAD,CAAzH;AAAA;;AAGA,MAAMkL,mBAAN,SAAkCZ,gBAAlC,CAAmD;EAC/C9H,WAAW,CAAC6H,IAAD,EAAO;IACd;IACA,KAAKA,IAAL,GAAYA,IAAZ,CAFc,CAGd;;IACA,KAAKc,UAAL,GAAkB,IAAIjC,GAAJ,EAAlB;;IACA,KAAKiC,UAAL,CAAgBnB,GAAhB,CAAoBK,IAAI,CAACe,IAAzB,EAA+B,EAA/B;EACH;;EACDC,gBAAgB,CAAC/E,MAAD,EAAS+B,IAAT,EAAeiD,UAAf,EAA2B;IACvChF,MAAM,CAAC0B,OAAP,CAAgB2C,KAAD,IAAW;MACtB,MAAMY,OAAO,GAAG,KAAKlB,IAAL,CAAU7G,aAAV,CAAwB,OAAxB,CAAhB;;MACA+H,OAAO,CAACC,WAAR,GAAsBb,KAAtB;MACAW,UAAU,CAACpD,IAAX,CAAgBG,IAAI,CAACoD,WAAL,CAAiBF,OAAjB,CAAhB;IACH,CAJD;EAKH;;EACDG,OAAO,CAACC,QAAD,EAAW;IACd,MAAML,UAAU,GAAG,EAAnB;;IACA,KAAKD,gBAAL,CAAsB,KAAKd,UAA3B,EAAuCoB,QAAvC,EAAiDL,UAAjD;;IACA,KAAKH,UAAL,CAAgBnB,GAAhB,CAAoB2B,QAApB,EAA8BL,UAA9B;EACH;;EACDM,UAAU,CAACD,QAAD,EAAW;IACjB,MAAML,UAAU,GAAG,KAAKH,UAAL,CAAgBjF,GAAhB,CAAoByF,QAApB,CAAnB;;IACA,IAAIL,UAAJ,EAAgB;MACZA,UAAU,CAACtD,OAAX,CAAmB6D,WAAnB;IACH;;IACD,KAAKV,UAAL,CAAgBW,MAAhB,CAAuBH,QAAvB;EACH;;EACDb,aAAa,CAACJ,SAAD,EAAY;IACrB,KAAKS,UAAL,CAAgBnD,OAAhB,CAAwB,CAACsD,UAAD,EAAaK,QAAb,KAA0B;MAC9C,KAAKN,gBAAL,CAAsBX,SAAtB,EAAiCiB,QAAjC,EAA2CL,UAA3C;IACH,CAFD;EAGH;;EACDS,WAAW,GAAG;IACV,KAAKZ,UAAL,CAAgBnD,OAAhB,CAAwBsD,UAAU,IAAIA,UAAU,CAACtD,OAAX,CAAmB6D,WAAnB,CAAtC;EACH;;AAlC8C;;AAoCnDX,mBAAmB,CAACxC,IAApB;EAAA,iBAAgHwC,mBAAhH,EA9K6FxL,EA8K7F,UAAqJJ,QAArJ;AAAA;;AACA4L,mBAAmB,CAACvC,KAApB,kBA/K6FjJ,EA+K7F;EAAA,OAAoHwL,mBAApH;EAAA,SAAoHA,mBAApH;AAAA;;AACA;EAAA,mDAhL6FxL,EAgL7F,mBAA2FwL,mBAA3F,EAA4H,CAAC;IACjHtC,IAAI,EAAE5I;EAD2G,CAAD,CAA5H,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAE4I,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9DtB,IAAI,EAAE3I,MADwD;QAE9DkK,IAAI,EAAE,CAAC7K,QAAD;MAFwD,CAAD;IAA/B,CAAD,CAAP;EAGlB,CALxB;AAAA;;AAMA,SAASuM,WAAT,CAAqBG,SAArB,EAAgC;EAC5B3M,OAAO,GAAG+D,MAAV,CAAiB4I,SAAjB;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,cAAc,GAAG;EACnB,OAAO,4BADY;EAEnB,SAAS,8BAFU;EAGnB,SAAS,8BAHU;EAInB,OAAO,sCAJY;EAKnB,SAAS,+BALU;EAMnB,QAAQ;AANW,CAAvB;AAQA,MAAMC,eAAe,GAAG,SAAxB;AACA,MAAMC,aAAa,GAAG,OAAOC,SAAP,KAAqB,WAArB,IAAoC,CAAC,CAACA,SAA5D;AACA,MAAMC,kBAAkB,GAAG,QAA3B;AACA,MAAMC,SAAS,GAAI,WAAUD,kBAAmB,EAAhD;AACA,MAAME,YAAY,GAAI,cAAaF,kBAAmB,EAAtD;;AACA,SAASG,oBAAT,CAA8BC,gBAA9B,EAAgD;EAC5C,OAAOF,YAAY,CAACG,OAAb,CAAqBR,eAArB,EAAsCO,gBAAtC,CAAP;AACH;;AACD,SAASE,iBAAT,CAA2BF,gBAA3B,EAA6C;EACzC,OAAOH,SAAS,CAACI,OAAV,CAAkBR,eAAlB,EAAmCO,gBAAnC,CAAP;AACH;;AACD,SAASG,aAAT,CAAuBC,MAAvB,EAA+BvG,MAA/B,EAAuC/B,MAAvC,EAA+C;EAC3C,KAAK,IAAIiC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,MAAM,CAACG,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;IACpC,IAAImE,KAAK,GAAGrE,MAAM,CAACE,CAAD,CAAlB;;IACA,IAAIwE,KAAK,CAAC8B,OAAN,CAAcnC,KAAd,CAAJ,EAA0B;MACtBiC,aAAa,CAACC,MAAD,EAASlC,KAAT,EAAgBpG,MAAhB,CAAb;IACH,CAFD,MAGK;MACDoG,KAAK,GAAGA,KAAK,CAAC+B,OAAN,CAAcR,eAAd,EAA+BW,MAA/B,CAAR;MACAtI,MAAM,CAAC2D,IAAP,CAAYyC,KAAZ;IACH;EACJ;;EACD,OAAOpG,MAAP;AACH;;AACD,SAASwI,sBAAT,CAAgCC,YAAhC,EAA8C;EAC1C;EACA;EACA;EACA;EACA,OAAQC,KAAD,IAAW;IACd;IACA;IACA;IACA;IACA,IAAIA,KAAK,KAAK,cAAd,EAA8B;MAC1B,OAAOD,YAAP;IACH;;IACD,MAAME,oBAAoB,GAAGF,YAAY,CAACC,KAAD,CAAzC;;IACA,IAAIC,oBAAoB,KAAK,KAA7B,EAAoC;MAChC;MACAD,KAAK,CAACE,cAAN;MACAF,KAAK,CAACG,WAAN,GAAoB,KAApB;IACH;;IACD,OAAOnD,SAAP;EACH,CAfD;AAgBH;;AACD,IAAIoD,mCAAmC,GAAG,KAA1C;;AACA,MAAMC,mBAAN,CAA0B;EACtB9K,WAAW,CAAC+K,YAAD,EAAeC,gBAAf,EAAiCC,KAAjC,EAAwC;IAC/C,KAAKF,YAAL,GAAoBA,YAApB;IACA,KAAKC,gBAAL,GAAwBA,gBAAxB;IACA,KAAKC,KAAL,GAAaA,KAAb;IACA,KAAKC,gBAAL,GAAwB,IAAIxE,GAAJ,EAAxB;IACA,KAAKyE,eAAL,GAAuB,IAAIC,mBAAJ,CAAwBL,YAAxB,CAAvB;EACH;;EACDM,cAAc,CAACrE,OAAD,EAAUZ,IAAV,EAAgB;IAC1B,IAAI,CAACY,OAAD,IAAY,CAACZ,IAAjB,EAAuB;MACnB,OAAO,KAAK+E,eAAZ;IACH;;IACD,QAAQ/E,IAAI,CAACkF,aAAb;MACI,KAAK5N,iBAAiB,CAAC6N,QAAvB;QAAiC;UAC7B,IAAIC,QAAQ,GAAG,KAAKN,gBAAL,CAAsBxH,GAAtB,CAA0B0C,IAAI,CAACqF,EAA/B,CAAf;;UACA,IAAI,CAACD,QAAL,EAAe;YACXA,QAAQ,GAAG,IAAIE,iCAAJ,CAAsC,KAAKX,YAA3C,EAAyD,KAAKC,gBAA9D,EAAgF5E,IAAhF,EAAsF,KAAK6E,KAA3F,CAAX;YACA,KAAKC,gBAAL,CAAsB1D,GAAtB,CAA0BpB,IAAI,CAACqF,EAA/B,EAAmCD,QAAnC;UACH;;UACDA,QAAQ,CAACG,WAAT,CAAqB3E,OAArB;UACA,OAAOwE,QAAP;QACH;MACD;MACA;;MACA,KAAK,CAAL;MACA,KAAK9N,iBAAiB,CAACkO,SAAvB;QACI;QACA,IAAI,CAAC,OAAOhC,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KACA;QACA;QACA;QACA,CAACiB,mCAJD,IAIwCzE,IAAI,CAACkF,aAAL,KAAuB,CAJnE,EAIsE;UAClET,mCAAmC,GAAG,IAAtC;UACAgB,OAAO,CAACC,IAAR,CAAa,oIAAb;QACH;;QACD,OAAO,IAAIC,iBAAJ,CAAsB,KAAKhB,YAA3B,EAAyC,KAAKC,gBAA9C,EAAgEhE,OAAhE,EAAyEZ,IAAzE,CAAP;;MACJ;QAAS;UACL,IAAI,CAAC,KAAK8E,gBAAL,CAAsB9C,GAAtB,CAA0BhC,IAAI,CAACqF,EAA/B,CAAL,EAAyC;YACrC,MAAM3H,MAAM,GAAGsG,aAAa,CAAChE,IAAI,CAACqF,EAAN,EAAUrF,IAAI,CAACtC,MAAf,EAAuB,EAAvB,CAA5B;YACA,KAAKkH,gBAAL,CAAsB/C,SAAtB,CAAgCnE,MAAhC;YACA,KAAKoH,gBAAL,CAAsB1D,GAAtB,CAA0BpB,IAAI,CAACqF,EAA/B,EAAmC,KAAKN,eAAxC;UACH;;UACD,OAAO,KAAKA,eAAZ;QACH;IA/BL;EAiCH;;EACDa,KAAK,GAAG,CAAG;;EACXC,GAAG,GAAG,CAAG;;AA/Ca;;AAiD1BnB,mBAAmB,CAAC5E,IAApB;EAAA,iBAAgH4E,mBAAhH,EAzS6F5N,EAyS7F,UAAqJoJ,YAArJ,GAzS6FpJ,EAyS7F,UAA8KwL,mBAA9K,GAzS6FxL,EAyS7F,UAA8MS,MAA9M;AAAA;;AACAmN,mBAAmB,CAAC3E,KAApB,kBA1S6FjJ,EA0S7F;EAAA,OAAoH4N,mBAApH;EAAA,SAAoHA,mBAApH;AAAA;;AACA;EAAA,mDA3S6F5N,EA2S7F,mBAA2F4N,mBAA3F,EAA4H,CAAC;IACjH1E,IAAI,EAAE5I;EAD2G,CAAD,CAA5H,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAE4I,IAAI,EAAEE;IAAR,CAAD,EAAyB;MAAEF,IAAI,EAAEsC;IAAR,CAAzB,EAAwD;MAAEtC,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACrHtB,IAAI,EAAE3I,MAD+G;QAErHkK,IAAI,EAAE,CAAChK,MAAD;MAF+G,CAAD;IAA/B,CAAxD,CAAP;EAGlB,CALxB;AAAA;;AAMA,MAAMyN,mBAAN,CAA0B;EACtBpL,WAAW,CAAC+K,YAAD,EAAe;IACtB,KAAKA,YAAL,GAAoBA,YAApB;IACA,KAAKmB,IAAL,GAAYC,MAAM,CAACC,MAAP,CAAc,IAAd,CAAZ;IACA,KAAKC,WAAL,GAAmB,IAAnB;EACH;;EACDC,OAAO,GAAG,CAAG;;EACbtL,aAAa,CAAC4B,IAAD,EAAO2J,SAAP,EAAkB;IAC3B,IAAIA,SAAJ,EAAe;MACX;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAOlL,QAAQ,CAACmL,eAAT,CAAyB/C,cAAc,CAAC8C,SAAD,CAAd,IAA6BA,SAAtD,EAAiE3J,IAAjE,CAAP;IACH;;IACD,OAAOvB,QAAQ,CAACL,aAAT,CAAuB4B,IAAvB,CAAP;EACH;;EACD6J,aAAa,CAACC,KAAD,EAAQ;IACjB,OAAOrL,QAAQ,CAACoL,aAAT,CAAuBC,KAAvB,CAAP;EACH;;EACDC,UAAU,CAACD,KAAD,EAAQ;IACd,OAAOrL,QAAQ,CAACuL,cAAT,CAAwBF,KAAxB,CAAP;EACH;;EACDzD,WAAW,CAAC4D,MAAD,EAASC,QAAT,EAAmB;IAC1B,MAAMC,YAAY,GAAGC,cAAc,CAACH,MAAD,CAAd,GAAyBA,MAAM,CAACI,OAAhC,GAA0CJ,MAA/D;IACAE,YAAY,CAAC9D,WAAb,CAAyB6D,QAAzB;EACH;;EACDI,YAAY,CAACL,MAAD,EAASC,QAAT,EAAmBK,QAAnB,EAA6B;IACrC,IAAIN,MAAJ,EAAY;MACR,MAAME,YAAY,GAAGC,cAAc,CAACH,MAAD,CAAd,GAAyBA,MAAM,CAACI,OAAhC,GAA0CJ,MAA/D;MACAE,YAAY,CAACG,YAAb,CAA0BJ,QAA1B,EAAoCK,QAApC;IACH;EACJ;;EACDpM,WAAW,CAAC8L,MAAD,EAASO,QAAT,EAAmB;IAC1B,IAAIP,MAAJ,EAAY;MACRA,MAAM,CAAC9L,WAAP,CAAmBqM,QAAnB;IACH;EACJ;;EACDC,iBAAiB,CAACC,cAAD,EAAiBC,eAAjB,EAAkC;IAC/C,IAAIjN,EAAE,GAAG,OAAOgN,cAAP,KAA0B,QAA1B,GAAqCjM,QAAQ,CAACyB,aAAT,CAAuBwK,cAAvB,CAArC,GACLA,cADJ;;IAEA,IAAI,CAAChN,EAAL,EAAS;MACL,MAAM,IAAIwE,KAAJ,CAAW,iBAAgBwI,cAAe,8BAA1C,CAAN;IACH;;IACD,IAAI,CAACC,eAAL,EAAsB;MAClBjN,EAAE,CAAC0I,WAAH,GAAiB,EAAjB;IACH;;IACD,OAAO1I,EAAP;EACH;;EACDQ,UAAU,CAACD,IAAD,EAAO;IACb,OAAOA,IAAI,CAACC,UAAZ;EACH;;EACD0M,WAAW,CAAC3M,IAAD,EAAO;IACd,OAAOA,IAAI,CAAC2M,WAAZ;EACH;;EACDtK,YAAY,CAAC5C,EAAD,EAAKsC,IAAL,EAAW8J,KAAX,EAAkBH,SAAlB,EAA6B;IACrC,IAAIA,SAAJ,EAAe;MACX3J,IAAI,GAAG2J,SAAS,GAAG,GAAZ,GAAkB3J,IAAzB;MACA,MAAM6K,YAAY,GAAGhE,cAAc,CAAC8C,SAAD,CAAnC;;MACA,IAAIkB,YAAJ,EAAkB;QACdnN,EAAE,CAACoN,cAAH,CAAkBD,YAAlB,EAAgC7K,IAAhC,EAAsC8J,KAAtC;MACH,CAFD,MAGK;QACDpM,EAAE,CAAC4C,YAAH,CAAgBN,IAAhB,EAAsB8J,KAAtB;MACH;IACJ,CATD,MAUK;MACDpM,EAAE,CAAC4C,YAAH,CAAgBN,IAAhB,EAAsB8J,KAAtB;IACH;EACJ;;EACDiB,eAAe,CAACrN,EAAD,EAAKsC,IAAL,EAAW2J,SAAX,EAAsB;IACjC,IAAIA,SAAJ,EAAe;MACX,MAAMkB,YAAY,GAAGhE,cAAc,CAAC8C,SAAD,CAAnC;;MACA,IAAIkB,YAAJ,EAAkB;QACdnN,EAAE,CAACsN,iBAAH,CAAqBH,YAArB,EAAmC7K,IAAnC;MACH,CAFD,MAGK;QACDtC,EAAE,CAACqN,eAAH,CAAoB,GAAEpB,SAAU,IAAG3J,IAAK,EAAxC;MACH;IACJ,CARD,MASK;MACDtC,EAAE,CAACqN,eAAH,CAAmB/K,IAAnB;IACH;EACJ;;EACDiL,QAAQ,CAACvN,EAAD,EAAKsC,IAAL,EAAW;IACftC,EAAE,CAACwN,SAAH,CAAazF,GAAb,CAAiBzF,IAAjB;EACH;;EACDmL,WAAW,CAACzN,EAAD,EAAKsC,IAAL,EAAW;IAClBtC,EAAE,CAACwN,SAAH,CAAalN,MAAb,CAAoBgC,IAApB;EACH;;EACDoL,QAAQ,CAAC1N,EAAD,EAAK6H,KAAL,EAAYuE,KAAZ,EAAmBuB,KAAnB,EAA0B;IAC9B,IAAIA,KAAK,IAAIrQ,mBAAmB,CAACsQ,QAApB,GAA+BtQ,mBAAmB,CAACuQ,SAAvD,CAAT,EAA4E;MACxE7N,EAAE,CAAC6H,KAAH,CAASiG,WAAT,CAAqBjG,KAArB,EAA4BuE,KAA5B,EAAmCuB,KAAK,GAAGrQ,mBAAmB,CAACuQ,SAA5B,GAAwC,WAAxC,GAAsD,EAAzF;IACH,CAFD,MAGK;MACD7N,EAAE,CAAC6H,KAAH,CAASA,KAAT,IAAkBuE,KAAlB;IACH;EACJ;;EACDrD,WAAW,CAAC/I,EAAD,EAAK6H,KAAL,EAAY8F,KAAZ,EAAmB;IAC1B,IAAIA,KAAK,GAAGrQ,mBAAmB,CAACsQ,QAAhC,EAA0C;MACtC5N,EAAE,CAAC6H,KAAH,CAASkG,cAAT,CAAwBlG,KAAxB;IACH,CAFD,MAGK;MACD;MACA;MACA7H,EAAE,CAAC6H,KAAH,CAASA,KAAT,IAAkB,EAAlB;IACH;EACJ;;EACDiG,WAAW,CAAC9N,EAAD,EAAKsC,IAAL,EAAW8J,KAAX,EAAkB;IACzB/C,aAAa,IAAI2E,oBAAoB,CAAC1L,IAAD,EAAO,UAAP,CAArC;IACAtC,EAAE,CAACsC,IAAD,CAAF,GAAW8J,KAAX;EACH;;EACD6B,QAAQ,CAAC1N,IAAD,EAAO6L,KAAP,EAAc;IAClB7L,IAAI,CAAC2N,SAAL,GAAiB9B,KAAjB;EACH;;EACD+B,MAAM,CAAC1M,MAAD,EAAS0I,KAAT,EAAgBvF,QAAhB,EAA0B;IAC5ByE,aAAa,IAAI2E,oBAAoB,CAAC7D,KAAD,EAAQ,UAAR,CAArC;;IACA,IAAI,OAAO1I,MAAP,KAAkB,QAAtB,EAAgC;MAC5B,OAAO,KAAKgJ,YAAL,CAAkB1D,sBAAlB,CAAyCtF,MAAzC,EAAiD0I,KAAjD,EAAwDF,sBAAsB,CAACrF,QAAD,CAA9E,CAAP;IACH;;IACD,OAAO,KAAK6F,YAAL,CAAkBtK,gBAAlB,CAAmCsB,MAAnC,EAA2C0I,KAA3C,EAAkDF,sBAAsB,CAACrF,QAAD,CAAxE,CAAP;EACH;;AA9HqB;;AAgI1B,MAAMwJ,WAAW,GAAG,CAAC,MAAM,IAAIC,UAAJ,CAAe,CAAf,CAAP,GAApB;;AACA,SAASL,oBAAT,CAA8B1L,IAA9B,EAAoCgM,QAApC,EAA8C;EAC1C,IAAIhM,IAAI,CAAC+L,UAAL,CAAgB,CAAhB,MAAuBD,WAA3B,EAAwC;IACpC,MAAM,IAAI5J,KAAJ,CAAW,wBAAuB8J,QAAS,IAAGhM,IAAK;AACjE;AACA,qEAAqEA,IAAK,gIAF5D,CAAN;EAGH;AACJ;;AACD,SAASoK,cAAT,CAAwBnM,IAAxB,EAA8B;EAC1B,OAAOA,IAAI,CAACI,OAAL,KAAiB,UAAjB,IAA+BJ,IAAI,CAACoM,OAAL,KAAiBxF,SAAvD;AACH;;AACD,MAAMiE,iCAAN,SAAgDN,mBAAhD,CAAoE;EAChEpL,WAAW,CAAC+K,YAAD,EAAeC,gBAAf,EAAiC6D,SAAjC,EAA4C5D,KAA5C,EAAmD;IAC1D,MAAMF,YAAN;IACA,KAAK8D,SAAL,GAAiBA,SAAjB;IACA,MAAM/K,MAAM,GAAGsG,aAAa,CAACa,KAAK,GAAG,GAAR,GAAc4D,SAAS,CAACpD,EAAzB,EAA6BoD,SAAS,CAAC/K,MAAvC,EAA+C,EAA/C,CAA5B;IACAkH,gBAAgB,CAAC/C,SAAjB,CAA2BnE,MAA3B;IACA,KAAKgL,WAAL,GAAmB9E,oBAAoB,CAACiB,KAAK,GAAG,GAAR,GAAc4D,SAAS,CAACpD,EAAzB,CAAvC;IACA,KAAKsD,QAAL,GAAgB5E,iBAAiB,CAACc,KAAK,GAAG,GAAR,GAAc4D,SAAS,CAACpD,EAAzB,CAAjC;EACH;;EACDE,WAAW,CAAC3E,OAAD,EAAU;IACjB,MAAM9D,YAAN,CAAmB8D,OAAnB,EAA4B,KAAK+H,QAAjC,EAA2C,EAA3C;EACH;;EACD/N,aAAa,CAAC6L,MAAD,EAASjK,IAAT,EAAe;IACxB,MAAMtC,EAAE,GAAG,MAAMU,aAAN,CAAoB6L,MAApB,EAA4BjK,IAA5B,CAAX;IACA,MAAMM,YAAN,CAAmB5C,EAAnB,EAAuB,KAAKwO,WAA5B,EAAyC,EAAzC;IACA,OAAOxO,EAAP;EACH;;AAhB+D;;AAkBpE,MAAMyL,iBAAN,SAAgCX,mBAAhC,CAAoD;EAChDpL,WAAW,CAAC+K,YAAD,EAAeC,gBAAf,EAAiCgE,MAAjC,EAAyCH,SAAzC,EAAoD;IAC3D,MAAM9D,YAAN;IACA,KAAKC,gBAAL,GAAwBA,gBAAxB;IACA,KAAKgE,MAAL,GAAcA,MAAd;IACA,KAAKC,UAAL,GAAkBD,MAAM,CAACE,YAAP,CAAoB;MAAEC,IAAI,EAAE;IAAR,CAApB,CAAlB;IACA,KAAKnE,gBAAL,CAAsB9B,OAAtB,CAA8B,KAAK+F,UAAnC;IACA,MAAMnL,MAAM,GAAGsG,aAAa,CAACyE,SAAS,CAACpD,EAAX,EAAeoD,SAAS,CAAC/K,MAAzB,EAAiC,EAAjC,CAA5B;;IACA,KAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,MAAM,CAACG,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;MACpC,MAAM+E,OAAO,GAAG1H,QAAQ,CAACL,aAAT,CAAuB,OAAvB,CAAhB;MACA+H,OAAO,CAACC,WAAR,GAAsBlF,MAAM,CAACE,CAAD,CAA5B;MACA,KAAKiL,UAAL,CAAgBhG,WAAhB,CAA4BF,OAA5B;IACH;EACJ;;EACDqG,gBAAgB,CAACvO,IAAD,EAAO;IACnB,OAAOA,IAAI,KAAK,KAAKmO,MAAd,GAAuB,KAAKC,UAA5B,GAAyCpO,IAAhD;EACH;;EACDyL,OAAO,GAAG;IACN,KAAKtB,gBAAL,CAAsB5B,UAAtB,CAAiC,KAAK6F,UAAtC;EACH;;EACDhG,WAAW,CAAC4D,MAAD,EAASC,QAAT,EAAmB;IAC1B,OAAO,MAAM7D,WAAN,CAAkB,KAAKmG,gBAAL,CAAsBvC,MAAtB,CAAlB,EAAiDC,QAAjD,CAAP;EACH;;EACDI,YAAY,CAACL,MAAD,EAASC,QAAT,EAAmBK,QAAnB,EAA6B;IACrC,OAAO,MAAMD,YAAN,CAAmB,KAAKkC,gBAAL,CAAsBvC,MAAtB,CAAnB,EAAkDC,QAAlD,EAA4DK,QAA5D,CAAP;EACH;;EACDpM,WAAW,CAAC8L,MAAD,EAASO,QAAT,EAAmB;IAC1B,OAAO,MAAMrM,WAAN,CAAkB,KAAKqO,gBAAL,CAAsBvC,MAAtB,CAAlB,EAAiDO,QAAjD,CAAP;EACH;;EACDtM,UAAU,CAACD,IAAD,EAAO;IACb,OAAO,KAAKuO,gBAAL,CAAsB,MAAMtO,UAAN,CAAiB,KAAKsO,gBAAL,CAAsBvO,IAAtB,CAAjB,CAAtB,CAAP;EACH;;AA/B+C;AAkCpD;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMwO,eAAN,SAA8BzH,kBAA9B,CAAiD;EAC7C5H,WAAW,CAACkB,GAAD,EAAM;IACb,MAAMA,GAAN;EACH,CAH4C,CAI7C;EACA;;;EACAqG,QAAQ,CAACN,SAAD,EAAY;IAChB,OAAO,IAAP;EACH;;EACDxG,gBAAgB,CAACuG,OAAD,EAAUC,SAAV,EAAqBC,OAArB,EAA8B;IAC1CF,OAAO,CAACvG,gBAAR,CAAyBwG,SAAzB,EAAoCC,OAApC,EAA6C,KAA7C;IACA,OAAO,MAAM,KAAKxG,mBAAL,CAAyBsG,OAAzB,EAAkCC,SAAlC,EAA6CC,OAA7C,CAAb;EACH;;EACDxG,mBAAmB,CAACqB,MAAD,EAASkF,SAAT,EAAoB/B,QAApB,EAA8B;IAC7C,OAAOnD,MAAM,CAACrB,mBAAP,CAA2BuG,SAA3B,EAAsC/B,QAAtC,CAAP;EACH;;AAf4C;;AAiBjDmK,eAAe,CAACnJ,IAAhB;EAAA,iBAA4GmJ,eAA5G,EAxgB6FnS,EAwgB7F,UAA6IJ,QAA7I;AAAA;;AACAuS,eAAe,CAAClJ,KAAhB,kBAzgB6FjJ,EAygB7F;EAAA,OAAgHmS,eAAhH;EAAA,SAAgHA,eAAhH;AAAA;;AACA;EAAA,mDA1gB6FnS,EA0gB7F,mBAA2FmS,eAA3F,EAAwH,CAAC;IAC7GjJ,IAAI,EAAE5I;EADuG,CAAD,CAAxH,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAE4I,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9DtB,IAAI,EAAE3I,MADwD;QAE9DkK,IAAI,EAAE,CAAC7K,QAAD;MAFwD,CAAD;IAA/B,CAAD,CAAP;EAGlB,CALxB;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMwS,aAAa,GAAG,CAAC,KAAD,EAAQ,SAAR,EAAmB,MAAnB,EAA2B,OAA3B,CAAtB,C,CACA;AACA;;AACA,MAAMC,OAAO,GAAG;EACZ,MAAM,WADM;EAEZ,MAAM,KAFM;EAGZ,QAAQ,QAHI;EAIZ,QAAQ,QAJI;EAKZ,OAAO,QALK;EAMZ,OAAO,QANK;EAOZ,QAAQ,WAPI;EAQZ,SAAS,YARG;EASZ,MAAM,SATM;EAUZ,QAAQ,WAVI;EAWZ,QAAQ,aAXI;EAYZ,UAAU,YAZE;EAaZ,OAAO;AAbK,CAAhB;AAeA;AACA;AACA;;AACA,MAAMC,oBAAoB,GAAG;EACzB,OAAQ/E,KAAD,IAAWA,KAAK,CAACgF,MADC;EAEzB,WAAYhF,KAAD,IAAWA,KAAK,CAACiF,OAFH;EAGzB,QAASjF,KAAD,IAAWA,KAAK,CAACkF,OAHA;EAIzB,SAAUlF,KAAD,IAAWA,KAAK,CAACmF;AAJD,CAA7B;AAMA;AACA;AACA;AACA;;AACA,MAAMC,eAAN,SAA8BjI,kBAA9B,CAAiD;EAC7C;AACJ;AACA;AACA;EACI5H,WAAW,CAACkB,GAAD,EAAM;IACb,MAAMA,GAAN;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIqG,QAAQ,CAACN,SAAD,EAAY;IAChB,OAAO4I,eAAe,CAACC,cAAhB,CAA+B7I,SAA/B,KAA6C,IAApD;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIxG,gBAAgB,CAACuG,OAAD,EAAUC,SAAV,EAAqBC,OAArB,EAA8B;IAC1C,MAAM6I,WAAW,GAAGF,eAAe,CAACC,cAAhB,CAA+B7I,SAA/B,CAApB;IACA,MAAM+I,cAAc,GAAGH,eAAe,CAACI,aAAhB,CAA8BF,WAAW,CAAC,SAAD,CAAzC,EAAsD7I,OAAtD,EAA+D,KAAKN,OAAL,CAAaU,OAAb,EAA/D,CAAvB;IACA,OAAO,KAAKV,OAAL,CAAaU,OAAb,GAAuB4I,iBAAvB,CAAyC,MAAM;MAClD,OAAOrT,OAAO,GAAGwD,WAAV,CAAsB2G,OAAtB,EAA+B+I,WAAW,CAAC,cAAD,CAA1C,EAA4DC,cAA5D,CAAP;IACH,CAFM,CAAP;EAGH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACyB,OAAdF,cAAc,CAAC7I,SAAD,EAAY;IAC7B,MAAMkJ,KAAK,GAAGlJ,SAAS,CAACmJ,WAAV,GAAwBC,KAAxB,CAA8B,GAA9B,CAAd;IACA,MAAMC,YAAY,GAAGH,KAAK,CAACI,KAAN,EAArB;;IACA,IAAKJ,KAAK,CAAClM,MAAN,KAAiB,CAAlB,IAAwB,EAAEqM,YAAY,KAAK,SAAjB,IAA8BA,YAAY,KAAK,OAAjD,CAA5B,EAAuF;MACnF,OAAO,IAAP;IACH;;IACD,MAAME,GAAG,GAAGX,eAAe,CAACY,aAAhB,CAA8BN,KAAK,CAACO,GAAN,EAA9B,CAAZ;;IACA,IAAIC,OAAO,GAAG,EAAd;IACA,IAAIC,MAAM,GAAGT,KAAK,CAACU,OAAN,CAAc,MAAd,CAAb;;IACA,IAAID,MAAM,GAAG,CAAC,CAAd,EAAiB;MACbT,KAAK,CAACW,MAAN,CAAaF,MAAb,EAAqB,CAArB;MACAD,OAAO,GAAG,OAAV;IACH;;IACDrB,aAAa,CAAC9J,OAAd,CAAsBuL,YAAY,IAAI;MAClC,MAAMC,KAAK,GAAGb,KAAK,CAACU,OAAN,CAAcE,YAAd,CAAd;;MACA,IAAIC,KAAK,GAAG,CAAC,CAAb,EAAgB;QACZb,KAAK,CAACW,MAAN,CAAaE,KAAb,EAAoB,CAApB;QACAL,OAAO,IAAII,YAAY,GAAG,GAA1B;MACH;IACJ,CAND;IAOAJ,OAAO,IAAIH,GAAX;;IACA,IAAIL,KAAK,CAAClM,MAAN,IAAgB,CAAhB,IAAqBuM,GAAG,CAACvM,MAAJ,KAAe,CAAxC,EAA2C;MACvC;MACA,OAAO,IAAP;IACH,CAxB4B,CAyB7B;IACA;IACA;;;IACA,MAAMgN,MAAM,GAAG,EAAf;IACAA,MAAM,CAAC,cAAD,CAAN,GAAyBX,YAAzB;IACAW,MAAM,CAAC,SAAD,CAAN,GAAoBN,OAApB;IACA,OAAOM,MAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACgC,OAArBC,qBAAqB,CAACzG,KAAD,EAAQ0G,WAAR,EAAqB;IAC7C,IAAIC,OAAO,GAAG7B,OAAO,CAAC9E,KAAK,CAAC+F,GAAP,CAAP,IAAsB/F,KAAK,CAAC+F,GAA1C;IACA,IAAIA,GAAG,GAAG,EAAV;;IACA,IAAIW,WAAW,CAACN,OAAZ,CAAoB,OAApB,IAA+B,CAAC,CAApC,EAAuC;MACnCO,OAAO,GAAG3G,KAAK,CAAC4G,IAAhB;MACAb,GAAG,GAAG,OAAN;IACH,CAN4C,CAO7C;;;IACA,IAAIY,OAAO,IAAI,IAAX,IAAmB,CAACA,OAAxB,EACI,OAAO,KAAP;IACJA,OAAO,GAAGA,OAAO,CAAChB,WAAR,EAAV;;IACA,IAAIgB,OAAO,KAAK,GAAhB,EAAqB;MACjBA,OAAO,GAAG,OAAV,CADiB,CACE;IACtB,CAFD,MAGK,IAAIA,OAAO,KAAK,GAAhB,EAAqB;MACtBA,OAAO,GAAG,KAAV,CADsB,CACL;IACpB;;IACD9B,aAAa,CAAC9J,OAAd,CAAsBuL,YAAY,IAAI;MAClC,IAAIA,YAAY,KAAKK,OAArB,EAA8B;QAC1B,MAAME,cAAc,GAAG9B,oBAAoB,CAACuB,YAAD,CAA3C;;QACA,IAAIO,cAAc,CAAC7G,KAAD,CAAlB,EAA2B;UACvB+F,GAAG,IAAIO,YAAY,GAAG,GAAtB;QACH;MACJ;IACJ,CAPD;IAQAP,GAAG,IAAIY,OAAP;IACA,OAAOZ,GAAG,KAAKW,WAAf;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACwB,OAAblB,aAAa,CAACU,OAAD,EAAUzJ,OAAV,EAAmBqK,IAAnB,EAAyB;IACzC,OAAQ9G,KAAD,IAAW;MACd,IAAIoF,eAAe,CAACqB,qBAAhB,CAAsCzG,KAAtC,EAA6CkG,OAA7C,CAAJ,EAA2D;QACvDY,IAAI,CAACC,UAAL,CAAgB,MAAMtK,OAAO,CAACuD,KAAD,CAA7B;MACH;IACJ,CAJD;EAKH;EACD;;;EACoB,OAAbgG,aAAa,CAACgB,OAAD,EAAU;IAC1B;IACA,QAAQA,OAAR;MACI,KAAK,KAAL;QACI,OAAO,QAAP;;MACJ;QACI,OAAOA,OAAP;IAJR;EAMH;;AAtI4C;;AAwIjD5B,eAAe,CAAC3J,IAAhB;EAAA,iBAA4G2J,eAA5G,EAlsB6F3S,EAksB7F,UAA6IJ,QAA7I;AAAA;;AACA+S,eAAe,CAAC1J,KAAhB,kBAnsB6FjJ,EAmsB7F;EAAA,OAAgH2S,eAAhH;EAAA,SAAgHA,eAAhH;AAAA;;AACA;EAAA,mDApsB6F3S,EAosB7F,mBAA2F2S,eAA3F,EAAwH,CAAC;IAC7GzJ,IAAI,EAAE5I;EADuG,CAAD,CAAxH,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAE4I,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9DtB,IAAI,EAAE3I,MADwD;QAE9DkK,IAAI,EAAE,CAAC7K,QAAD;MAFwD,CAAD;IAA/B,CAAD,CAAP;EAGlB,CALxB;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM4U,WAAW,GAAG,OAAO9H,SAAP,KAAqB,WAArB,IAAoC,CAAC,CAACA,SAA1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAAS+H,oBAAT,CAA8BC,aAA9B,EAA6CC,OAA7C,EAAsD;EAClD,OAAOhU,0BAA0B,CAAC;IAAE+T,aAAF;IAAiB,GAAGE,qBAAqB,CAACD,OAAD;EAAzC,CAAD,CAAjC;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASE,iBAAT,CAA2BF,OAA3B,EAAoC;EAChC,OAAOhU,0BAA0B,CAACiU,qBAAqB,CAACD,OAAD,CAAtB,CAAjC;AACH;;AACD,SAASC,qBAAT,CAA+BD,OAA/B,EAAwC;EACpC,OAAO;IACHG,YAAY,EAAE,CACV,GAAGC,wBADO,EAEV,IAAIJ,OAAO,EAAEK,SAAT,IAAsB,EAA1B,CAFU,CADX;IAKHC,iBAAiB,EAAEC;EALhB,CAAP;AAOH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,+BAAT,GAA2C;EACvC;EACA;EACA,OAAO,CAAC,GAAGC,qBAAJ,CAAP;AACH;;AACD,SAASC,cAAT,GAA0B;EACtBpS,iBAAiB,CAACC,WAAlB;AACH;;AACD,SAASoS,YAAT,GAAwB;EACpB,OAAO,IAAI1U,YAAJ,EAAP;AACH;;AACD,SAAS2U,SAAT,GAAqB;EACjB;EACA1U,YAAY,CAACsD,QAAD,CAAZ;EACA,OAAOA,QAAP;AACH;;AACD,MAAM+Q,mCAAmC,GAAG,CACxC;EAAEjO,OAAO,EAAEnG,WAAX;EAAwB0U,QAAQ,EAAE3V;AAAlC,CADwC,EAExC;EAAEoH,OAAO,EAAElG,oBAAX;EAAiCyU,QAAQ,EAAEH,cAA3C;EAA2DjO,KAAK,EAAE;AAAlE,CAFwC,EAGxC;EAAEH,OAAO,EAAErH,QAAX;EAAqBsH,UAAU,EAAEqO,SAAjC;EAA4CpO,IAAI,EAAE;AAAlD,CAHwC,CAA5C;AAKA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMsO,eAAe,GAAGzU,qBAAqB,CAACC,YAAD,EAAe,SAAf,EAA0BiU,mCAA1B,CAA7C;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMQ,+BAA+B,GAAG,IAAIzV,cAAJ,CAAmBuU,WAAW,GAAG,gCAAH,GAAsC,EAApE,CAAxC;AACA,MAAMY,qBAAqB,GAAG,CAC1B;EACInO,OAAO,EAAE/F,mBADb;EAEIyU,QAAQ,EAAEtO,qBAFd;EAGIF,IAAI,EAAE;AAHV,CAD0B,EAM1B;EACIF,OAAO,EAAE9F,YADb;EAEIwU,QAAQ,EAAEvU,WAFd;EAGI+F,IAAI,EAAE,CAAC9F,MAAD,EAASC,mBAAT,EAA8BJ,mBAA9B;AAHV,CAN0B,EAW1B;EACI+F,OAAO,EAAE7F,WADb;EAEIuU,QAAQ,EAAEvU,WAFd;EAGI+F,IAAI,EAAE,CAAC9F,MAAD,EAASC,mBAAT,EAA8BJ,mBAA9B;AAHV,CAX0B,CAA9B;AAiBA,MAAM6T,wBAAwB,GAAG,CAC7B;EAAE9N,OAAO,EAAE1F,eAAX;EAA4BiU,QAAQ,EAAE;AAAtC,CAD6B,EAE7B;EAAEvO,OAAO,EAAErG,YAAX;EAAyBsG,UAAU,EAAEoO,YAArC;EAAmDnO,IAAI,EAAE;AAAzD,CAF6B,EAEkC;EAC3DF,OAAO,EAAEkC,qBADkD;EAE3DwM,QAAQ,EAAExD,eAFiD;EAG3D/K,KAAK,EAAE,IAHoD;EAI3DD,IAAI,EAAE,CAACvH,QAAD,EAAWyB,MAAX,EAAmBP,WAAnB;AAJqD,CAFlC,EAQ7B;EAAEmG,OAAO,EAAEkC,qBAAX;EAAkCwM,QAAQ,EAAEhD,eAA5C;EAA6DvL,KAAK,EAAE,IAApE;EAA0ED,IAAI,EAAE,CAACvH,QAAD;AAAhF,CAR6B,EAQiE;EAC1FqH,OAAO,EAAE2G,mBADiF;EAE1F+H,QAAQ,EAAE/H,mBAFgF;EAG1FzG,IAAI,EAAE,CAACiC,YAAD,EAAeoC,mBAAf,EAAoC/K,MAApC;AAHoF,CARjE,EAa7B;EAAEwG,OAAO,EAAEzF,gBAAX;EAA6BoU,WAAW,EAAEhI;AAA1C,CAb6B,EAc7B;EAAE3G,OAAO,EAAE2D,gBAAX;EAA6BgL,WAAW,EAAEpK;AAA1C,CAd6B,EAe7B;EAAEvE,OAAO,EAAEuE,mBAAX;EAAgCmK,QAAQ,EAAEnK,mBAA1C;EAA+DrE,IAAI,EAAE,CAACvH,QAAD;AAArE,CAf6B,EAgB7B;EAAEqH,OAAO,EAAEmC,YAAX;EAAyBuM,QAAQ,EAAEvM,YAAnC;EAAiDjC,IAAI,EAAE,CAACgC,qBAAD,EAAwB9H,MAAxB;AAAvD,CAhB6B,EAiB7B;EAAE4F,OAAO,EAAEnH,UAAX;EAAuB6V,QAAQ,EAAE9M,UAAjC;EAA6C1B,IAAI,EAAE;AAAnD,CAjB6B,EAkB7BqN,WAAW,GAAG;EAAEvN,OAAO,EAAEyO,+BAAX;EAA4CF,QAAQ,EAAE;AAAtD,CAAH,GAAkE,EAlBhD,CAAjC;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMK,aAAN,CAAoB;EAChB/S,WAAW,CAACgT,uBAAD,EAA0B;IACjC,IAAItB,WAAW,IAAIsB,uBAAnB,EAA4C;MACxC,MAAM,IAAIlO,KAAJ,CAAW,oFAAD,GACX,mFADC,CAAN;IAEH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EAC+B,OAApBmO,oBAAoB,CAACC,MAAD,EAAS;IAChC,OAAO;MACHC,QAAQ,EAAEJ,aADP;MAEHb,SAAS,EAAE,CACP;QAAE/N,OAAO,EAAExG,MAAX;QAAmB+U,QAAQ,EAAEQ,MAAM,CAACjI;MAApC,CADO,EAEP;QAAE9G,OAAO,EAAEb,aAAX;QAA0BwP,WAAW,EAAEnV;MAAvC,CAFO,EAGPuG,2BAHO;IAFR,CAAP;EAQH;;AAxBe;;AA0BpB6O,aAAa,CAAC7M,IAAd;EAAA,iBAA0G6M,aAA1G,EAl6B6F7V,EAk6B7F,UAAyI0V,+BAAzI;AAAA;;AACAG,aAAa,CAACK,IAAd,kBAn6B6FlW,EAm6B7F;EAAA,MAA2G6V,aAA3G;EAAA,UAAoI9V,YAApI,EAAkJ0B,iBAAlJ;AAAA;AACAoU,aAAa,CAACM,IAAd,kBAp6B6FnW,EAo6B7F;EAAA,WAAqI,CAC7H,GAAG+U,wBAD0H,EAE7H,GAAGK,qBAF0H,CAArI;EAAA,UAGiBrV,YAHjB,EAG+B0B,iBAH/B;AAAA;;AAIA;EAAA,mDAx6B6FzB,EAw6B7F,mBAA2F6V,aAA3F,EAAsH,CAAC;IAC3G3M,IAAI,EAAExH,QADqG;IAE3G+I,IAAI,EAAE,CAAC;MACCuK,SAAS,EAAE,CACP,GAAGD,wBADI,EAEP,GAAGK,qBAFI,CADZ;MAKCgB,OAAO,EAAE,CAACrW,YAAD,EAAe0B,iBAAf;IALV,CAAD;EAFqG,CAAD,CAAtH,EAS4B,YAAY;IAAE,OAAO,CAAC;MAAEyH,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9DtB,IAAI,EAAEvH;MADwD,CAAD,EAE9D;QACCuH,IAAI,EAAEtH;MADP,CAF8D,EAI9D;QACCsH,IAAI,EAAE3I,MADP;QAECkK,IAAI,EAAE,CAACiL,+BAAD;MAFP,CAJ8D;IAA/B,CAAD,CAAP;EAOlB,CAhBxB;AAAA;AAkBA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,SAASW,UAAT,GAAsB;EAClB,OAAO,IAAIC,IAAJ,CAASzU,QAAQ,CAACjC,QAAD,CAAjB,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM0W,IAAN,CAAW;EACPxT,WAAW,CAAC6H,IAAD,EAAO;IACd,KAAKA,IAAL,GAAYA,IAAZ;IACA,KAAK4L,IAAL,GAAY5W,OAAO,EAAnB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACI6W,MAAM,CAACC,GAAD,EAAMC,aAAa,GAAG,KAAtB,EAA6B;IAC/B,IAAI,CAACD,GAAL,EACI,OAAO,IAAP;IACJ,OAAO,KAAKE,mBAAL,CAAyBF,GAAzB,EAA8BC,aAA9B,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIE,OAAO,CAACC,IAAD,EAAOH,aAAa,GAAG,KAAvB,EAA8B;IACjC,IAAI,CAACG,IAAL,EACI,OAAO,EAAP;IACJ,OAAOA,IAAI,CAACC,MAAL,CAAY,CAAC/C,MAAD,EAAS0C,GAAT,KAAiB;MAChC,IAAIA,GAAJ,EAAS;QACL1C,MAAM,CAACvL,IAAP,CAAY,KAAKmO,mBAAL,CAAyBF,GAAzB,EAA8BC,aAA9B,CAAZ;MACH;;MACD,OAAO3C,MAAP;IACH,CALM,EAKJ,EALI,CAAP;EAMH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIgD,MAAM,CAACC,YAAD,EAAe;IACjB,IAAI,CAACA,YAAL,EACI,OAAO,IAAP;IACJ,OAAO,KAAKrM,IAAL,CAAU/E,aAAV,CAAyB,QAAOoR,YAAa,GAA7C,KAAoD,IAA3D;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIC,OAAO,CAACD,YAAD,EAAe;IAClB,IAAI,CAACA,YAAL,EACI,OAAO,EAAP;;IACJ,MAAME;IAAK;IAAD,EAAgB,KAAKvM,IAAL,CAAU9D,gBAAV,CAA4B,QAAOmQ,YAAa,GAAhD,CAA1B;;IACA,OAAOE,IAAI,GAAG,GAAGtN,KAAH,CAASuN,IAAT,CAAcD,IAAd,CAAH,GAAyB,EAApC;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIE,SAAS,CAACX,GAAD,EAAMY,QAAN,EAAgB;IACrB,IAAI,CAACZ,GAAL,EACI,OAAO,IAAP;IACJY,QAAQ,GAAGA,QAAQ,IAAI,KAAKC,cAAL,CAAoBb,GAApB,CAAvB;IACA,MAAMc,IAAI,GAAG,KAAKR,MAAL,CAAYM,QAAZ,CAAb;;IACA,IAAIE,IAAJ,EAAU;MACN,OAAO,KAAKC,yBAAL,CAA+Bf,GAA/B,EAAoCc,IAApC,CAAP;IACH;;IACD,OAAO,KAAKZ,mBAAL,CAAyBF,GAAzB,EAA8B,IAA9B,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIgB,SAAS,CAACT,YAAD,EAAe;IACpB,KAAKU,gBAAL,CAAsB,KAAKX,MAAL,CAAYC,YAAZ,CAAtB;EACH;EACD;AACJ;AACA;AACA;;;EACIU,gBAAgB,CAACH,IAAD,EAAO;IACnB,IAAIA,IAAJ,EAAU;MACN,KAAKhB,IAAL,CAAU7S,MAAV,CAAiB6T,IAAjB;IACH;EACJ;;EACDZ,mBAAmB,CAACY,IAAD,EAAOb,aAAa,GAAG,KAAvB,EAA8B;IAC7C,IAAI,CAACA,aAAL,EAAoB;MAChB,MAAMW,QAAQ,GAAG,KAAKC,cAAL,CAAoBC,IAApB,CAAjB,CADgB,CAEhB;MACA;MACA;;;MACA,MAAM/P,IAAI,GAAG,KAAKyP,OAAL,CAAaI,QAAb,EAAuBM,MAAvB,CAA8BnQ,IAAI,IAAI,KAAKoQ,mBAAL,CAAyBL,IAAzB,EAA+B/P,IAA/B,CAAtC,EAA4E,CAA5E,CAAb;MACA,IAAIA,IAAI,KAAK+C,SAAb,EACI,OAAO/C,IAAP;IACP;;IACD,MAAMsC,OAAO,GAAG,KAAKyM,IAAL,CAAUzS,aAAV,CAAwB,MAAxB,CAAhB;;IACA,KAAK0T,yBAAL,CAA+BD,IAA/B,EAAqCzN,OAArC;;IACA,MAAM4B,IAAI,GAAG,KAAKf,IAAL,CAAUkN,oBAAV,CAA+B,MAA/B,EAAuC,CAAvC,CAAb;;IACAnM,IAAI,CAACK,WAAL,CAAiBjC,OAAjB;IACA,OAAOA,OAAP;EACH;;EACD0N,yBAAyB,CAACf,GAAD,EAAMrT,EAAN,EAAU;IAC/B6L,MAAM,CAAC6I,IAAP,CAAYrB,GAAZ,EAAiBnO,OAAjB,CAA0ByP,IAAD,IAAU3U,EAAE,CAAC4C,YAAH,CAAgB,KAAKgS,cAAL,CAAoBD,IAApB,CAAhB,EAA2CtB,GAAG,CAACsB,IAAD,CAA9C,CAAnC;IACA,OAAO3U,EAAP;EACH;;EACDkU,cAAc,CAACb,GAAD,EAAM;IAChB,MAAMwB,IAAI,GAAGxB,GAAG,CAAC/Q,IAAJ,GAAW,MAAX,GAAoB,UAAjC;IACA,OAAQ,GAAEuS,IAAK,KAAIxB,GAAG,CAACwB,IAAD,CAAO,GAA7B;EACH;;EACDL,mBAAmB,CAACnB,GAAD,EAAMjP,IAAN,EAAY;IAC3B,OAAOyH,MAAM,CAAC6I,IAAP,CAAYrB,GAAZ,EAAiByB,KAAjB,CAAwB5E,GAAD,IAAS9L,IAAI,CAAC3B,YAAL,CAAkB,KAAKmS,cAAL,CAAoB1E,GAApB,CAAlB,MAAgDmD,GAAG,CAACnD,GAAD,CAAnF,CAAP;EACH;;EACD0E,cAAc,CAACD,IAAD,EAAO;IACjB,OAAOI,aAAa,CAACJ,IAAD,CAAb,IAAuBA,IAA9B;EACH;;AA9HM;;AAgIXzB,IAAI,CAACtN,IAAL;EAAA,iBAAiGsN,IAAjG,EA7lC6FtW,EA6lC7F,UAAuHJ,QAAvH;AAAA;;AACA0W,IAAI,CAACrN,KAAL,kBA9lC6FjJ,EA8lC7F;EAAA,OAAqGsW,IAArG;EAAA;IAAA;;IAAA;MAAA;IAAA;MAAA,IAA2ID,UAA3I;IAAA;;IAAA;EAAA;EAAA,YAAuH;AAAvH;;AACA;EAAA,mDA/lC6FrW,EA+lC7F,mBAA2FsW,IAA3F,EAA6G,CAAC;IAClGpN,IAAI,EAAE5I,UAD4F;IAElGmK,IAAI,EAAE,CAAC;MAAE2N,UAAU,EAAE,MAAd;MAAsBlR,UAAU,EAAEmP,UAAlC;MAA8ClP,IAAI,EAAE;IAApD,CAAD;EAF4F,CAAD,CAA7G,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAE+B,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9DtB,IAAI,EAAE3I,MADwD;QAE9DkK,IAAI,EAAE,CAAC7K,QAAD;MAFwD,CAAD;IAA/B,CAAD,CAAP;EAGlB,CANxB;AAAA;AAOA;AACA;AACA;;;AACA,MAAMuY,aAAa,GAAG;EAClBE,SAAS,EAAE;AADO,CAAtB;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;AACA,SAASC,WAAT,GAAuB;EACnB,OAAO,IAAIC,KAAJ,CAAU1W,QAAQ,CAACjC,QAAD,CAAlB,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM2Y,KAAN,CAAY;EACRzV,WAAW,CAAC6H,IAAD,EAAO;IACd,KAAKA,IAAL,GAAYA,IAAZ;EACH;EACD;AACJ;AACA;;;EACI6N,QAAQ,GAAG;IACP,OAAO,KAAK7N,IAAL,CAAU8N,KAAjB;EACH;EACD;AACJ;AACA;AACA;;;EACIC,QAAQ,CAACC,QAAD,EAAW;IACf,KAAKhO,IAAL,CAAU8N,KAAV,GAAkBE,QAAQ,IAAI,EAA9B;EACH;;AAhBO;;AAkBZJ,KAAK,CAACvP,IAAN;EAAA,iBAAkGuP,KAAlG,EAtpC6FvY,EAspC7F,UAAyHJ,QAAzH;AAAA;;AACA2Y,KAAK,CAACtP,KAAN,kBAvpC6FjJ,EAupC7F;EAAA,OAAsGuY,KAAtG;EAAA;IAAA;;IAAA;MAAA;IAAA;MAAA,IAA6ID,WAA7I;IAAA;;IAAA;EAAA;EAAA,YAAyH;AAAzH;;AACA;EAAA,mDAxpC6FtY,EAwpC7F,mBAA2FuY,KAA3F,EAA8G,CAAC;IACnGrP,IAAI,EAAE5I,UAD6F;IAEnGmK,IAAI,EAAE,CAAC;MAAE2N,UAAU,EAAE,MAAd;MAAsBlR,UAAU,EAAEoR,WAAlC;MAA+CnR,IAAI,EAAE;IAArD,CAAD;EAF6F,CAAD,CAA9G,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAE+B,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9DtB,IAAI,EAAE3I,MADwD;QAE9DkK,IAAI,EAAE,CAAC7K,QAAD;MAFwD,CAAD;IAA/B,CAAD,CAAP;EAGlB,CANxB;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMgZ,iBAAiB,GAAG,UAA1B;AACA,MAAMC,gBAAgB,GAAG,WAAzB;;AACA,SAASC,mBAAT,CAA6BC,KAA7B,EAAoC;EAChC,OAAOA,KAAK,CAAC/L,OAAN,CAAc4L,iBAAd,EAAiC,CAAC,GAAGI,CAAJ,KAAU,MAAMA,CAAC,CAAC,CAAD,CAAD,CAAK9F,WAAL,EAAjD,CAAP;AACH;;AACD,SAAS+F,mBAAT,CAA6BF,KAA7B,EAAoC;EAChC,OAAOA,KAAK,CAAC/L,OAAN,CAAc6L,gBAAd,EAAgC,CAAC,GAAGG,CAAJ,KAAUA,CAAC,CAAC,CAAD,CAAD,CAAKE,WAAL,EAA1C,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,WAAT,CAAqBzT,IAArB,EAA2B8J,KAA3B,EAAkC;EAC9B,IAAI,OAAO4J,QAAP,KAAoB,WAApB,IAAmC,CAACA,QAAxC,EAAkD;IAC9C;IACA;IACA;IACA;IACA,MAAMC,EAAE,GAAGhZ,OAAO,CAAC,IAAD,CAAP,GAAgBA,OAAO,CAAC,IAAD,CAAP,IAAiB,EAA5C;IACAgZ,EAAE,CAAC3T,IAAD,CAAF,GAAW8J,KAAX;EACH;AACJ;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM8J,GAAG,GAAG,OAAOxU,MAAP,KAAkB,WAAlB,IAAiCA,MAAjC,IAA2C,EAAvD;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMyU,yBAAN,CAAgC;EAC5BzW,WAAW,CAAC0W,SAAD,EAAYC,QAAZ,EAAsB;IAC7B,KAAKD,SAAL,GAAiBA,SAAjB;IACA,KAAKC,QAAL,GAAgBA,QAAhB;EACH;;AAJ2B;AAMhC;AACA;AACA;AACA;;;AACA,MAAMC,eAAN,CAAsB;EAClB5W,WAAW,CAAC6W,GAAD,EAAM;IACb,KAAKC,MAAL,GAAcD,GAAG,CAACpT,QAAJ,CAAaC,GAAb,CAAiB1E,cAAjB,CAAd;EACH,CAHiB,CAIlB;;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACI+X,mBAAmB,CAACC,MAAD,EAAS;IACxB,MAAMC,MAAM,GAAGD,MAAM,IAAIA,MAAM,CAAC,QAAD,CAA/B;IACA,MAAME,WAAW,GAAG,kBAApB,CAFwB,CAGxB;;IACA,MAAMC,mBAAmB,GAAGX,GAAG,CAAC3K,OAAJ,CAAYuL,OAAZ,IAAuB,IAAnD;;IACA,IAAIH,MAAM,IAAIE,mBAAd,EAAmC;MAC/BX,GAAG,CAAC3K,OAAJ,CAAYuL,OAAZ,CAAoBF,WAApB;IACH;;IACD,MAAMG,KAAK,GAAGC,cAAc,EAA5B;IACA,IAAIX,QAAQ,GAAG,CAAf;;IACA,OAAOA,QAAQ,GAAG,CAAX,IAAiBW,cAAc,KAAKD,KAApB,GAA6B,GAApD,EAAyD;MACrD,KAAKP,MAAL,CAAYS,IAAZ;MACAZ,QAAQ;IACX;;IACD,MAAM1K,GAAG,GAAGqL,cAAc,EAA1B;;IACA,IAAIL,MAAM,IAAIE,mBAAd,EAAmC;MAC/BX,GAAG,CAAC3K,OAAJ,CAAY2L,UAAZ,CAAuBN,WAAvB;IACH;;IACD,MAAMR,SAAS,GAAG,CAACzK,GAAG,GAAGoL,KAAP,IAAgBV,QAAlC;IACAH,GAAG,CAAC3K,OAAJ,CAAY4L,GAAZ,CAAiB,OAAMd,QAAS,0BAAhC;IACAH,GAAG,CAAC3K,OAAJ,CAAY4L,GAAZ,CAAiB,GAAEf,SAAS,CAACgB,OAAV,CAAkB,CAAlB,CAAqB,eAAxC;IACA,OAAO,IAAIjB,yBAAJ,CAA8BC,SAA9B,EAAyCC,QAAzC,CAAP;EACH;;AA3CiB;;AA6CtB,SAASW,cAAT,GAA0B;EACtB,OAAOd,GAAG,CAACmB,WAAJ,IAAmBnB,GAAG,CAACmB,WAAJ,CAAgBC,GAAnC,GAAyCpB,GAAG,CAACmB,WAAJ,CAAgBC,GAAhB,EAAzC,GACH,IAAIC,IAAJ,GAAWC,OAAX,EADJ;AAEH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,oBAAoB,GAAG,UAA7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,gBAAT,CAA0BnB,GAA1B,EAA+B;EAC3BR,WAAW,CAAC0B,oBAAD,EAAuB,IAAInB,eAAJ,CAAoBC,GAApB,CAAvB,CAAX;EACA,OAAOA,GAAP;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASoB,iBAAT,GAA6B;EACzB5B,WAAW,CAAC0B,oBAAD,EAAuB,IAAvB,CAAX;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASG,UAAT,CAAoBC,IAApB,EAA0B;EACtB,MAAMC,WAAW,GAAG;IAChB,KAAK,KADW;IAEhB,KAAK,KAFW;IAGhB,MAAM,KAHU;IAIhB,KAAK,KAJW;IAKhB,KAAK;EALW,CAApB;EAOA,OAAOD,IAAI,CAACjO,OAAL,CAAa,UAAb,EAAyBmO,CAAC,IAAID,WAAW,CAACC,CAAD,CAAzC,CAAP;AACH;;AACD,SAASC,YAAT,CAAsBH,IAAtB,EAA4B;EACxB,MAAMI,aAAa,GAAG;IAClB,OAAO,GADW;IAElB,OAAO,GAFW;IAGlB,OAAO,IAHW;IAIlB,OAAO,GAJW;IAKlB,OAAO;EALW,CAAtB;EAOA,OAAOJ,IAAI,CAACjO,OAAL,CAAa,UAAb,EAAyBmO,CAAC,IAAIE,aAAa,CAACF,CAAD,CAA3C,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASG,YAAT,CAAsBhI,GAAtB,EAA2B;EACvB,OAAOA,GAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMiI,aAAN,CAAoB;EAChBzY,WAAW,GAAG;IACV,KAAK0Y,KAAL,GAAa,EAAb;IACA,KAAKC,oBAAL,GAA4B,EAA5B;EACH;EACD;AACJ;AACA;;;EACIjV,GAAG,CAAC8M,GAAD,EAAMoI,YAAN,EAAoB;IACnB,OAAO,KAAKF,KAAL,CAAWlI,GAAX,MAAoB/I,SAApB,GAAgC,KAAKiR,KAAL,CAAWlI,GAAX,CAAhC,GAAkDoI,YAAzD;EACH;EACD;AACJ;AACA;;;EACIpR,GAAG,CAACgJ,GAAD,EAAM9D,KAAN,EAAa;IACZ,KAAKgM,KAAL,CAAWlI,GAAX,IAAkB9D,KAAlB;EACH;EACD;AACJ;AACA;;;EACI9L,MAAM,CAAC4P,GAAD,EAAM;IACR,OAAO,KAAKkI,KAAL,CAAWlI,GAAX,CAAP;EACH;EACD;AACJ;AACA;;;EACIqI,MAAM,CAACrI,GAAD,EAAM;IACR,OAAO,KAAKkI,KAAL,CAAWI,cAAX,CAA0BtI,GAA1B,CAAP;EACH;EACD;AACJ;AACA;;;EACe,IAAPuI,OAAO,GAAG;IACV,OAAO5M,MAAM,CAAC6I,IAAP,CAAY,KAAK0D,KAAjB,EAAwBzU,MAAxB,KAAmC,CAA1C;EACH;EACD;AACJ;AACA;;;EACI+U,WAAW,CAACxI,GAAD,EAAMtL,QAAN,EAAgB;IACvB,KAAKyT,oBAAL,CAA0BnI,GAA1B,IAAiCtL,QAAjC;EACH;EACD;AACJ;AACA;;;EACI+T,MAAM,GAAG;IACL;IACA,KAAK,MAAMzI,GAAX,IAAkB,KAAKmI,oBAAvB,EAA6C;MACzC,IAAI,KAAKA,oBAAL,CAA0BG,cAA1B,CAAyCtI,GAAzC,CAAJ,EAAmD;QAC/C,IAAI;UACA,KAAKkI,KAAL,CAAWlI,GAAX,IAAkB,KAAKmI,oBAAL,CAA0BnI,GAA1B,GAAlB;QACH,CAFD,CAGA,OAAO0I,CAAP,EAAU;UACNrN,OAAO,CAACC,IAAR,CAAa,qCAAb,EAAoDoN,CAApD;QACH;MACJ;IACJ;;IACD,OAAOC,IAAI,CAACC,SAAL,CAAe,KAAKV,KAApB,CAAP;EACH;;AAzDe;;AA2DpBD,aAAa,CAACvS,IAAd;EAAA,iBAA0GuS,aAA1G;AAAA;;AACAA,aAAa,CAACtS,KAAd,kBAt6C6FjJ,EAs6C7F;EAAA,OAA8Gub,aAA9G;EAAA;IAAA,QAA6J,MAAM;MAC3J,MAAMvX,GAAG,GAAGjC,MAAM,CAACnC,QAAD,CAAlB;MACA,MAAMmO,KAAK,GAAGhM,MAAM,CAACtB,MAAD,CAApB;MACA,MAAM0b,KAAK,GAAG,IAAIZ,aAAJ,EAAd;MACAY,KAAK,CAACX,KAAN,GAAcY,wBAAwB,CAACpY,GAAD,EAAM+J,KAAN,CAAtC;MACA,OAAOoO,KAAP;IACH,CANL;EAAA;EAAA,YAAyI;AAAzI;;AAOA;EAAA,mDA76C6Fnc,EA66C7F,mBAA2Fub,aAA3F,EAAsH,CAAC;IAC3GrS,IAAI,EAAE5I,UADqG;IAE3GmK,IAAI,EAAE,CAAC;MACC2N,UAAU,EAAE,MADb;MAEClR,UAAU,EAAE,MAAM;QACd,MAAMlD,GAAG,GAAGjC,MAAM,CAACnC,QAAD,CAAlB;QACA,MAAMmO,KAAK,GAAGhM,MAAM,CAACtB,MAAD,CAApB;QACA,MAAM0b,KAAK,GAAG,IAAIZ,aAAJ,EAAd;QACAY,KAAK,CAACX,KAAN,GAAcY,wBAAwB,CAACpY,GAAD,EAAM+J,KAAN,CAAtC;QACA,OAAOoO,KAAP;MACH;IARF,CAAD;EAFqG,CAAD,CAAtH;AAAA;;AAaA,SAASC,wBAAT,CAAkCpY,GAAlC,EAAuC+J,KAAvC,EAA8C;EAC1C;EACA;EACA,MAAMsO,MAAM,GAAGrY,GAAG,CAACsY,cAAJ,CAAmBvO,KAAK,GAAG,QAA3B,CAAf;EACA,IAAIwO,YAAY,GAAG,EAAnB;;EACA,IAAIF,MAAM,IAAIA,MAAM,CAACvQ,WAArB,EAAkC;IAC9B,IAAI;MACA;MACAyQ,YAAY,GAAGN,IAAI,CAACO,KAAL,CAAWpB,YAAY,CAACiB,MAAM,CAACvQ,WAAR,CAAvB,CAAf;IACH,CAHD,CAIA,OAAOkQ,CAAP,EAAU;MACNrN,OAAO,CAACC,IAAR,CAAa,qDAAqDb,KAAlE,EAAyEiO,CAAzE;IACH;EACJ;;EACD,OAAOO,YAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAME,0BAAN,CAAiC;;AAEjCA,0BAA0B,CAACzT,IAA3B;EAAA,iBAAuHyT,0BAAvH;AAAA;;AACAA,0BAA0B,CAACvG,IAA3B,kBAr9C6FlW,EAq9C7F;EAAA,MAAwHyc;AAAxH;AACAA,0BAA0B,CAACtG,IAA3B,kBAt9C6FnW,EAs9C7F;;AACA;EAAA,mDAv9C6FA,EAu9C7F,mBAA2Fyc,0BAA3F,EAAmI,CAAC;IACxHvT,IAAI,EAAExH,QADkH;IAExH+I,IAAI,EAAE,CAAC,EAAD;EAFkH,CAAD,CAAnI;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMiS,EAAN,CAAS;EACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACc,OAAHC,GAAG,GAAG;IACT,OAAO,MAAM,IAAb;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACc,OAAHC,GAAG,CAACvF,QAAD,EAAW;IACjB,OAAQwF,YAAD,IAAkB;MACrB,OAAOA,YAAY,CAACC,aAAb,IAA8B,IAA9B,GACHC,cAAc,CAACF,YAAY,CAACC,aAAd,EAA6BzF,QAA7B,CADX,GAEH,KAFJ;IAGH,CAJD;EAKH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACoB,OAAT2F,SAAS,CAAC9T,IAAD,EAAO;IACnB,OAAQ+T,SAAD,IAAeA,SAAS,CAACC,cAAV,CAAyBvJ,OAAzB,CAAiCzK,IAAjC,MAA2C,CAAC,CAAlE;EACH;;AArCI;;AAuCT,SAAS6T,cAAT,CAAwBI,CAAxB,EAA2B9F,QAA3B,EAAqC;EACjC,IAAI1X,OAAO,GAAG2E,aAAV,CAAwB6Y,CAAxB,CAAJ,EAAgC;IAC5B,OAAOA,CAAC,CAACC,OAAF,IAAaD,CAAC,CAACC,OAAF,CAAU/F,QAAV,CAAb,IACH8F,CAAC,CAACE,iBAAF,IAAuBF,CAAC,CAACE,iBAAF,CAAoBhG,QAApB,CADpB,IAEH8F,CAAC,CAACG,qBAAF,IAA2BH,CAAC,CAACG,qBAAF,CAAwBjG,QAAxB,CAF/B;EAGH;;EACD,OAAO,KAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMkG,WAAW,GAAG;EAChB;EACA,OAAO,IAFS;EAGhB,YAAY,IAHI;EAIhB,WAAW,IAJK;EAKhB,UAAU,IALM;EAMhB,aAAa,IANG;EAOhB,WAAW,IAPK;EAQhB,YAAY,IARI;EAShB,SAAS,IATO;EAUhB,WAAW,IAVK;EAWhB;EACA,SAAS,IAZO;EAahB,cAAc,IAbE;EAchB,aAAa,IAdG;EAehB,YAAY,IAfI;EAgBhB,eAAe,IAhBC;EAiBhB,WAAW,IAjBK;EAkBhB,YAAY,IAlBI;EAmBhB;EACA,SAAS,IApBO;EAqBhB,WAAW,IArBK;EAsBhB;EACA,UAAU,IAvBM;EAwBhB,eAAe,IAxBC;EAyBhB,cAAc,IAzBE;EA0BhB,aAAa,IA1BG;EA2BhB,gBAAgB,IA3BA;EA4BhB;EACA,SAAS,IA7BO;EA8BhB,aAAa,IA9BG;EA+BhB,cAAc,IA/BE;EAgChB,WAAW,IAhCK;EAiChB,aAAa,IAjCG;EAkChB;EACA,OAAO,IAnCS;EAoChB,aAAa;AApCG,CAApB;AAsCA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,qBAAqB,GAAG,IAAIvd,cAAJ,CAAmB,qBAAnB,CAA9B;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMwd,aAAa,GAAG,IAAIxd,cAAJ,CAAmB,cAAnB,CAAtB;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMyd,mBAAN,CAA0B;EACtB5a,WAAW,GAAG;IACV;AACR;AACA;AACA;AACA;IACQ,KAAK6a,MAAL,GAAc,EAAd;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IACQ,KAAKC,SAAL,GAAiB,EAAjB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIC,WAAW,CAAC/T,OAAD,EAAU;IACjB,MAAMgU,EAAE,GAAG,IAAIC,MAAJ,CAAWjU,OAAX,EAAoB,KAAK6K,OAAzB,CAAX;IACAmJ,EAAE,CAACtX,GAAH,CAAO,OAAP,EAAgB8D,GAAhB,CAAoB;MAAE0T,MAAM,EAAE;IAAV,CAApB;IACAF,EAAE,CAACtX,GAAH,CAAO,QAAP,EAAiB8D,GAAjB,CAAqB;MAAE0T,MAAM,EAAE;IAAV,CAArB;;IACA,KAAK,MAAMjU,SAAX,IAAwB,KAAK6T,SAA7B,EAAwC;MACpCE,EAAE,CAACtX,GAAH,CAAOuD,SAAP,EAAkBO,GAAlB,CAAsB,KAAKsT,SAAL,CAAe7T,SAAf,CAAtB;IACH;;IACD,OAAO+T,EAAP;EACH;;AAxCqB;;AA0C1BJ,mBAAmB,CAAC1U,IAApB;EAAA,iBAAgH0U,mBAAhH;AAAA;;AACAA,mBAAmB,CAACzU,KAApB,kBAtoD6FjJ,EAsoD7F;EAAA,OAAoH0d,mBAApH;EAAA,SAAoHA,mBAApH;AAAA;;AACA;EAAA,mDAvoD6F1d,EAuoD7F,mBAA2F0d,mBAA3F,EAA4H,CAAC;IACjHxU,IAAI,EAAE5I;EAD2G,CAAD,CAA5H;AAAA;AAGA;AACA;AACA;AACA;AACA;;;AACA,MAAM2d,oBAAN,SAAmCvT,kBAAnC,CAAsD;EAClD5H,WAAW,CAACkB,GAAD,EAAMka,OAAN,EAAevP,OAAf,EAAwBwP,MAAxB,EAAgC;IACvC,MAAMna,GAAN;IACA,KAAKka,OAAL,GAAeA,OAAf;IACA,KAAKvP,OAAL,GAAeA,OAAf;IACA,KAAKwP,MAAL,GAAcA,MAAd;IACA,KAAKC,cAAL,GAAsB,IAAtB;EACH;;EACD/T,QAAQ,CAACN,SAAD,EAAY;IAChB,IAAI,CAACwT,WAAW,CAAC3B,cAAZ,CAA2B7R,SAAS,CAACmJ,WAAV,EAA3B,CAAD,IAAwD,CAAC,KAAKmL,aAAL,CAAmBtU,SAAnB,CAA7D,EAA4F;MACxF,OAAO,KAAP;IACH;;IACD,IAAI,CAACjF,MAAM,CAACiZ,MAAR,IAAkB,CAAC,KAAKI,MAA5B,EAAoC;MAChC,IAAI,OAAOzR,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;QAC/C,KAAKiC,OAAL,CAAaC,IAAb,CAAmB,QAAO7E,SAAU,mDAAlB,GACb,iDADL;MAEH;;MACD,OAAO,KAAP;IACH;;IACD,OAAO,IAAP;EACH;;EACDxG,gBAAgB,CAACuG,OAAD,EAAUC,SAAV,EAAqBC,OAArB,EAA8B;IAC1C,MAAMqK,IAAI,GAAG,KAAK3K,OAAL,CAAaU,OAAb,EAAb;IACAL,SAAS,GAAGA,SAAS,CAACmJ,WAAV,EAAZ,CAF0C,CAG1C;IACA;;IACA,IAAI,CAACpO,MAAM,CAACiZ,MAAR,IAAkB,KAAKI,MAA3B,EAAmC;MAC/B,KAAKC,cAAL,GAAsB,KAAKA,cAAL,IAAuB/J,IAAI,CAACrB,iBAAL,CAAuB,MAAM,KAAKmL,MAAL,EAA7B,CAA7C,CAD+B,CAE/B;MACA;MACA;;MACA,IAAIG,kBAAkB,GAAG,KAAzB;;MACA,IAAIC,UAAU,GAAG,MAAM;QACnBD,kBAAkB,GAAG,IAArB;MACH,CAFD;;MAGAjK,IAAI,CAACrB,iBAAL,CAAuB,MAAM,KAAKoL,cAAL,CACxB1X,IADwB,CACnB,MAAM;QACZ;QACA,IAAI,CAAC5B,MAAM,CAACiZ,MAAZ,EAAoB;UAChB,IAAI,OAAOrR,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;YAC/C,KAAKiC,OAAL,CAAaC,IAAb,CAAmB,mEAAnB;UACH;;UACD2P,UAAU,GAAG,MAAM,CAAG,CAAtB;;UACA;QACH;;QACD,IAAI,CAACD,kBAAL,EAAyB;UACrB;UACA;UACA;UACAC,UAAU,GAAG,KAAKhb,gBAAL,CAAsBuG,OAAtB,EAA+BC,SAA/B,EAA0CC,OAA1C,CAAb;QACH;MACJ,CAhB4B,EAiBxBwU,KAjBwB,CAiBlB,MAAM;QACb,IAAI,OAAO9R,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;UAC/C,KAAKiC,OAAL,CAAaC,IAAb,CAAmB,QAAO7E,SAAU,6CAAlB,GACb,0BADL;QAEH;;QACDwU,UAAU,GAAG,MAAM,CAAG,CAAtB;MACH,CAvB4B,CAA7B,EAT+B,CAiC/B;MACA;MACA;;MACA,OAAO,MAAM;QACTA,UAAU;MACb,CAFD;IAGH;;IACD,OAAOlK,IAAI,CAACrB,iBAAL,CAAuB,MAAM;MAChC;MACA,MAAM8K,EAAE,GAAG,KAAKI,OAAL,CAAaL,WAAb,CAAyB/T,OAAzB,CAAX;;MACA,MAAM9B,QAAQ,GAAG,UAAUyW,QAAV,EAAoB;QACjCpK,IAAI,CAACC,UAAL,CAAgB,YAAY;UACxBtK,OAAO,CAACyU,QAAD,CAAP;QACH,CAFD;MAGH,CAJD;;MAKAX,EAAE,CAACY,EAAH,CAAM3U,SAAN,EAAiB/B,QAAjB;MACA,OAAO,MAAM;QACT8V,EAAE,CAACa,GAAH,CAAO5U,SAAP,EAAkB/B,QAAlB,EADS,CAET;;QACA,IAAI,OAAO8V,EAAE,CAAC1O,OAAV,KAAsB,UAA1B,EAAsC;UAClC0O,EAAE,CAAC1O,OAAH;QACH;MACJ,CAND;IAOH,CAhBM,CAAP;EAiBH;;EACDiP,aAAa,CAACtU,SAAD,EAAY;IACrB,OAAO,KAAKmU,OAAL,CAAaP,MAAb,CAAoBhK,OAApB,CAA4B5J,SAA5B,IAAyC,CAAC,CAAjD;EACH;;AAtFiD;;AAwFtDkU,oBAAoB,CAACjV,IAArB;EAAA,iBAAiHiV,oBAAjH,EAvuD6Fje,EAuuD7F,UAAuJJ,QAAvJ,GAvuD6FI,EAuuD7F,UAA4Kwd,qBAA5K,GAvuD6Fxd,EAuuD7F,UAA8MA,EAAE,CAACgC,QAAjN,GAvuD6FhC,EAuuD7F,UAAsOyd,aAAtO;AAAA;;AACAQ,oBAAoB,CAAChV,KAArB,kBAxuD6FjJ,EAwuD7F;EAAA,OAAqHie,oBAArH;EAAA,SAAqHA,oBAArH;AAAA;;AACA;EAAA,mDAzuD6Fje,EAyuD7F,mBAA2Fie,oBAA3F,EAA6H,CAAC;IAClH/U,IAAI,EAAE5I;EAD4G,CAAD,CAA7H,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAE4I,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9DtB,IAAI,EAAE3I,MADwD;QAE9DkK,IAAI,EAAE,CAAC7K,QAAD;MAFwD,CAAD;IAA/B,CAAD,EAG3B;MAAEsJ,IAAI,EAAEwU,mBAAR;MAA6BlT,UAAU,EAAE,CAAC;QAC5CtB,IAAI,EAAE3I,MADsC;QAE5CkK,IAAI,EAAE,CAAC+S,qBAAD;MAFsC,CAAD;IAAzC,CAH2B,EAM3B;MAAEtU,IAAI,EAAElJ,EAAE,CAACgC;IAAX,CAN2B,EAMJ;MAAEkH,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACzDtB,IAAI,EAAEvH;MADmD,CAAD,EAEzD;QACCuH,IAAI,EAAE3I,MADP;QAECkK,IAAI,EAAE,CAACgT,aAAD;MAFP,CAFyD;IAA/B,CANI,CAAP;EAWlB,CAbxB;AAAA;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMmB,YAAN,CAAmB;;AAEnBA,YAAY,CAAC5V,IAAb;EAAA,iBAAyG4V,YAAzG;AAAA;;AACAA,YAAY,CAAC1I,IAAb,kBArwD6FlW,EAqwD7F;EAAA,MAA0G4e;AAA1G;AACAA,YAAY,CAACzI,IAAb,kBAtwD6FnW,EAswD7F;EAAA,WAAmI,CAC3H;IACIiH,OAAO,EAAEkC,qBADb;IAEIwM,QAAQ,EAAEsI,oBAFd;IAGI7W,KAAK,EAAE,IAHX;IAIID,IAAI,EAAE,CAACvH,QAAD,EAAW4d,qBAAX,EAAkCxb,QAAlC,EAA4C,CAAC,IAAIL,QAAJ,EAAD,EAAiB8b,aAAjB,CAA5C;EAJV,CAD2H,EAO3H;IAAExW,OAAO,EAAEuW,qBAAX;IAAkC7H,QAAQ,EAAE+H,mBAA5C;IAAiEvW,IAAI,EAAE;EAAvE,CAP2H;AAAnI;;AASA;EAAA,mDA/wD6FnH,EA+wD7F,mBAA2F4e,YAA3F,EAAqH,CAAC;IAC1G1V,IAAI,EAAExH,QADoG;IAE1G+I,IAAI,EAAE,CAAC;MACCuK,SAAS,EAAE,CACP;QACI/N,OAAO,EAAEkC,qBADb;QAEIwM,QAAQ,EAAEsI,oBAFd;QAGI7W,KAAK,EAAE,IAHX;QAIID,IAAI,EAAE,CAACvH,QAAD,EAAW4d,qBAAX,EAAkCxb,QAAlC,EAA4C,CAAC,IAAIL,QAAJ,EAAD,EAAiB8b,aAAjB,CAA5C;MAJV,CADO,EAOP;QAAExW,OAAO,EAAEuW,qBAAX;QAAkC7H,QAAQ,EAAE+H,mBAA5C;QAAiEvW,IAAI,EAAE;MAAvE,CAPO;IADZ,CAAD;EAFoG,CAAD,CAArH;AAAA;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM0X,YAAN,CAAmB;;AAEnBA,YAAY,CAAC7V,IAAb;EAAA,iBAAyG6V,YAAzG;AAAA;;AACAA,YAAY,CAAC5V,KAAb,kBAv0D6FjJ,EAu0D7F;EAAA,OAA6G6e,YAA7G;EAAA;IAAA;;IAAA;MAAA,cAA6GA,YAA7G;IAAA;MAAA,IAv0D6F7e,EAu0D7F,UAA+L8e,gBAA/L;IAAA;;IAAA;EAAA;EAAA,YAAuI;AAAvI;;AACA;EAAA,mDAx0D6F9e,EAw0D7F,mBAA2F6e,YAA3F,EAAqH,CAAC;IAC1G3V,IAAI,EAAE5I,UADoG;IAE1GmK,IAAI,EAAE,CAAC;MAAE2N,UAAU,EAAE,MAAd;MAAsBxC,WAAW,EAAE3T,UAAU,CAAC,MAAM6c,gBAAP;IAA7C,CAAD;EAFoG,CAAD,CAArH;AAAA;;AAIA,SAASC,uBAAT,CAAiCxY,QAAjC,EAA2C;EACvC,OAAO,IAAIuY,gBAAJ,CAAqBvY,QAAQ,CAACC,GAAT,CAAa5G,QAAb,CAArB,CAAP;AACH;;AACD,MAAMkf,gBAAN,SAA+BD,YAA/B,CAA4C;EACxC/b,WAAW,CAAC6H,IAAD,EAAO;IACd;IACA,KAAKA,IAAL,GAAYA,IAAZ;EACH;;EACDqU,QAAQ,CAACC,GAAD,EAAMzP,KAAN,EAAa;IACjB,IAAIA,KAAK,IAAI,IAAb,EACI,OAAO,IAAP;;IACJ,QAAQyP,GAAR;MACI,KAAK/c,eAAe,CAACgd,IAArB;QACI,OAAO1P,KAAP;;MACJ,KAAKtN,eAAe,CAACid,IAArB;QACI,IAAIhd,gCAAgC,CAACqN,KAAD,EAAQ;QAAO;QAAf,CAApC,EAA2E;UACvE,OAAOpN,gBAAgB,CAACoN,KAAD,CAAvB;QACH;;QACD,OAAOlN,cAAc,CAAC,KAAKqI,IAAN,EAAYyU,MAAM,CAAC5P,KAAD,CAAlB,CAAd,CAAyC6P,QAAzC,EAAP;;MACJ,KAAKnd,eAAe,CAACod,KAArB;QACI,IAAInd,gCAAgC,CAACqN,KAAD,EAAQ;QAAQ;QAAhB,CAApC,EAA6E;UACzE,OAAOpN,gBAAgB,CAACoN,KAAD,CAAvB;QACH;;QACD,OAAOA,KAAP;;MACJ,KAAKtN,eAAe,CAACqd,MAArB;QACI,IAAIpd,gCAAgC,CAACqN,KAAD,EAAQ;QAAS;QAAjB,CAApC,EAA+E;UAC3E,OAAOpN,gBAAgB,CAACoN,KAAD,CAAvB;QACH;;QACD,MAAM,IAAI5H,KAAJ,CAAU,uCAAV,CAAN;;MACJ,KAAK1F,eAAe,CAACsd,GAArB;QACI,IAAIrd,gCAAgC,CAACqN,KAAD,EAAQ;QAAM;QAAd,CAApC,EAAyE;UACrE,OAAOpN,gBAAgB,CAACoN,KAAD,CAAvB;QACH;;QACD,OAAOnN,aAAa,CAAC+c,MAAM,CAAC5P,KAAD,CAAP,CAApB;;MACJ,KAAKtN,eAAe,CAACud,YAArB;QACI,IAAItd,gCAAgC,CAACqN,KAAD,EAAQ;QAAc;QAAtB,CAApC,EAAyF;UACrF,OAAOpN,gBAAgB,CAACoN,KAAD,CAAvB;QACH;;QACD,MAAM,IAAI5H,KAAJ,CAAU,gFAAV,CAAN;;MACJ;QACI,MAAM,IAAIA,KAAJ,CAAW,8BAA6BqX,GAAI,qCAA5C,CAAN;IA7BR;EA+BH;;EACDS,uBAAuB,CAAClQ,KAAD,EAAQ;IAC3B,OAAOjN,4BAA4B,CAACiN,KAAD,CAAnC;EACH;;EACDmQ,wBAAwB,CAACnQ,KAAD,EAAQ;IAC5B,OAAOhN,6BAA6B,CAACgN,KAAD,CAApC;EACH;;EACDoQ,yBAAyB,CAACpQ,KAAD,EAAQ;IAC7B,OAAO/M,8BAA8B,CAAC+M,KAAD,CAArC;EACH;;EACDqQ,sBAAsB,CAACrQ,KAAD,EAAQ;IAC1B,OAAO9M,2BAA2B,CAAC8M,KAAD,CAAlC;EACH;;EACDsQ,8BAA8B,CAACtQ,KAAD,EAAQ;IAClC,OAAO7M,mCAAmC,CAAC6M,KAAD,CAA1C;EACH;;AAtDuC;;AAwD5CsP,gBAAgB,CAAC9V,IAAjB;EAAA,iBAA6G8V,gBAA7G,EAv4D6F9e,EAu4D7F,UAA+IJ,QAA/I;AAAA;;AACAkf,gBAAgB,CAAC7V,KAAjB,kBAx4D6FjJ,EAw4D7F;EAAA,OAAiH8e,gBAAjH;EAAA;IAAA;;IAAA;MAAA;IAAA;MAAA,IAAmKC,uBAAnK,CAx4D6F/e,EAw4D7F,UAA4MI,QAA5M;IAAA;;IAAA;EAAA;EAAA,YAA+I;AAA/I;;AACA;EAAA,mDAz4D6FJ,EAy4D7F,mBAA2F8e,gBAA3F,EAAyH,CAAC;IAC9G5V,IAAI,EAAE5I,UADwG;IAE9GmK,IAAI,EAAE,CAAC;MAAE2N,UAAU,EAAE,MAAd;MAAsBlR,UAAU,EAAE6X,uBAAlC;MAA2D5X,IAAI,EAAE,CAAC/G,QAAD;IAAjE,CAAD;EAFwG,CAAD,CAAzH,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAE8I,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9DtB,IAAI,EAAE3I,MADwD;QAE9DkK,IAAI,EAAE,CAAC7K,QAAD;MAFwD,CAAD;IAA/B,CAAD,CAAP;EAGlB,CANxB;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMmgB,OAAO,GAAG,IAAInd,OAAJ,CAAY,QAAZ,CAAhB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASiT,aAAT,EAAwB4G,0BAAxB,EAAoDC,EAApD,EAAwDmC,YAAxD,EAAsE1V,qBAAtE,EAA6FC,YAA7F,EAA2GoU,qBAA3G,EAAkIC,aAAlI,EAAiJC,mBAAjJ,EAAsKkB,YAAtK,EAAoLtI,IAApL,EAA0LiC,KAA1L,EAAiMgD,aAAjM,EAAgNwE,OAAhN,EAAyNtL,oBAAzN,EAA+OI,iBAA/O,EAAkQkG,iBAAlQ,EAAqRD,gBAArR,EAAuSQ,YAAvS,EAAqT7F,eAArT,EAAsUN,+BAAtU,EAAuWlS,iBAAiB,IAAI+c,kBAA5X,EAAgZ3Y,qBAAqB,IAAI4Y,sBAAza,EAAic9N,eAAe,IAAI+N,gBAApd,EAAsetS,mBAAmB,IAAIuS,oBAA7f,EAAmhBrB,gBAAgB,IAAIsB,iBAAviB,EAA0jB5U,mBAAmB,IAAI6U,oBAAjlB,EAAumBpC,oBAAoB,IAAIqC,qBAA/nB,EAAspBpL,mCAAmC,IAAIqL,oCAA7rB,EAAmuB5N,eAAe,IAAI6N,gBAAtvB,EAAwwBjU,cAAc,IAAIkU,eAA1xB,EAA2yB7V,gBAAgB,IAAI8V,iBAA/zB,EAAk1Bta,aAAa,IAAIua,cAAn2B,EAAm3B3F,UAAU,IAAI4F,WAAj4B,EAA84B1T,aAAa,IAAI2T,cAA/5B,EAA+6BxL,cAAc,IAAIyL,eAAj8B,EAAk9BhU,oBAAoB,IAAIiU,qBAA1+B,EAAigC9T,iBAAiB,IAAI+T,kBAAthC"}, "metadata": {}, "sourceType": "module"}