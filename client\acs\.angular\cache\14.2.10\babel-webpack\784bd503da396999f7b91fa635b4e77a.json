{"ast": null, "code": "import * as i1 from '@angular/cdk/a11y';\nimport { FocusKeyManager, isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { UP_ARROW, DOWN_ARROW, RIGHT_ARROW, LEFT_ARROW, ESCAPE, hasModifierKey, ENTER, SPACE } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, Input, QueryList, EventEmitter, TemplateRef, ContentChildren, ViewChild, ContentChild, Output, Self, NgModule } from '@angular/core';\nimport { Subject, Subscription, merge, of, asapScheduler } from 'rxjs';\nimport { startWith, switchMap, take, takeUntil, filter, delay } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { TemplatePortal, DomPortalOutlet } from '@angular/cdk/portal';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i3 from '@angular/material/core';\nimport { mixinDisableRipple, mixinDisabled, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport * as i3$1 from '@angular/cdk/bidi';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Animations used by the mat-menu component.\n * Animation duration and timing values are based on:\n * https://material.io/guidelines/components/menus.html#menus-usage\n * @docs-private\n */\n\nconst _c0 = [\"mat-menu-item\", \"\"];\n\nfunction MatMenuItem__svg_svg_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 2);\n    i0.ɵɵelement(1, \"polygon\", 3);\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c1 = [\"*\"];\n\nfunction MatMenu_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵlistener(\"keydown\", function MatMenu_ng_template_0_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._handleKeydown($event));\n    })(\"click\", function MatMenu_ng_template_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.closed.emit(\"click\"));\n    })(\"@transformMenu.start\", function MatMenu_ng_template_0_Template_div_animation_transformMenu_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4._onAnimationStart($event));\n    })(\"@transformMenu.done\", function MatMenu_ng_template_0_Template_div_animation_transformMenu_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._onAnimationDone($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵprojection(2);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"id\", ctx_r0.panelId)(\"ngClass\", ctx_r0._classList)(\"@transformMenu\", ctx_r0._panelAnimationState);\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.ariaLabel || null)(\"aria-labelledby\", ctx_r0.ariaLabelledby || null)(\"aria-describedby\", ctx_r0.ariaDescribedby || null);\n  }\n}\n\nconst matMenuAnimations = {\n  /**\n   * This animation controls the menu panel's entry and exit from the page.\n   *\n   * When the menu panel is added to the DOM, it scales in and fades in its border.\n   *\n   * When the menu panel is removed from the DOM, it simply fades out after a brief\n   * delay to display the ripple.\n   */\n  transformMenu: trigger('transformMenu', [state('void', style({\n    opacity: 0,\n    transform: 'scale(0.8)'\n  })), transition('void => enter', animate('120ms cubic-bezier(0, 0, 0.2, 1)', style({\n    opacity: 1,\n    transform: 'scale(1)'\n  }))), transition('* => void', animate('100ms 25ms linear', style({\n    opacity: 0\n  })))]),\n\n  /**\n   * This animation fades in the background color and content of the menu panel\n   * after its containing element is scaled in.\n   */\n  fadeInItems: trigger('fadeInItems', [// TODO(crisbeto): this is inside the `transformMenu`\n  // now. Remove next time we do breaking changes.\n  state('showing', style({\n    opacity: 1\n  })), transition('void => *', [style({\n    opacity: 0\n  }), animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)')])])\n};\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\n\nconst fadeInItems = matMenuAnimations.fadeInItems;\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\n\nconst transformMenu = matMenuAnimations.transformMenu;\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token that can be used to reference instances of `MatMenuContent`. It serves\n * as alternative token to the actual `MatMenuContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\nconst MAT_MENU_CONTENT = new InjectionToken('MatMenuContent');\n\nclass _MatMenuContentBase {\n  constructor(_template, _componentFactoryResolver, _appRef, _injector, _viewContainerRef, _document, _changeDetectorRef) {\n    this._template = _template;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._appRef = _appRef;\n    this._injector = _injector;\n    this._viewContainerRef = _viewContainerRef;\n    this._document = _document;\n    this._changeDetectorRef = _changeDetectorRef;\n    /** Emits when the menu content has been attached. */\n\n    this._attached = new Subject();\n  }\n  /**\n   * Attaches the content with a particular context.\n   * @docs-private\n   */\n\n\n  attach(context = {}) {\n    if (!this._portal) {\n      this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n    }\n\n    this.detach();\n\n    if (!this._outlet) {\n      this._outlet = new DomPortalOutlet(this._document.createElement('div'), this._componentFactoryResolver, this._appRef, this._injector);\n    }\n\n    const element = this._template.elementRef.nativeElement; // Because we support opening the same menu from different triggers (which in turn have their\n    // own `OverlayRef` panel), we have to re-insert the host element every time, otherwise we\n    // risk it staying attached to a pane that's no longer in the DOM.\n\n    element.parentNode.insertBefore(this._outlet.outletElement, element); // When `MatMenuContent` is used in an `OnPush` component, the insertion of the menu\n    // content via `createEmbeddedView` does not cause the content to be seen as \"dirty\"\n    // by Angular. This causes the `@ContentChildren` for menu items within the menu to\n    // not be updated by Angular. By explicitly marking for check here, we tell Angular that\n    // it needs to check for new menu items and update the `@ContentChild` in `MatMenu`.\n    // @breaking-change 9.0.0 Make change detector ref required\n\n    this._changeDetectorRef?.markForCheck();\n\n    this._portal.attach(this._outlet, context);\n\n    this._attached.next();\n  }\n  /**\n   * Detaches the content.\n   * @docs-private\n   */\n\n\n  detach() {\n    if (this._portal.isAttached) {\n      this._portal.detach();\n    }\n  }\n\n  ngOnDestroy() {\n    if (this._outlet) {\n      this._outlet.dispose();\n    }\n  }\n\n}\n\n_MatMenuContentBase.ɵfac = function _MatMenuContentBase_Factory(t) {\n  return new (t || _MatMenuContentBase)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ApplicationRef), i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\n_MatMenuContentBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatMenuContentBase\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatMenuContentBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }, {\n      type: i0.ComponentFactoryResolver\n    }, {\n      type: i0.ApplicationRef\n    }, {\n      type: i0.Injector\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, null);\n})();\n/**\n * Menu content that will be rendered lazily once the menu is opened.\n */\n\n\nclass MatMenuContent extends _MatMenuContentBase {}\n\nMatMenuContent.ɵfac = /* @__PURE__ */function () {\n  let ɵMatMenuContent_BaseFactory;\n  return function MatMenuContent_Factory(t) {\n    return (ɵMatMenuContent_BaseFactory || (ɵMatMenuContent_BaseFactory = i0.ɵɵgetInheritedFactory(MatMenuContent)))(t || MatMenuContent);\n  };\n}();\n\nMatMenuContent.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatMenuContent,\n  selectors: [[\"ng-template\", \"matMenuContent\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_MENU_CONTENT,\n    useExisting: MatMenuContent\n  }]), i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuContent, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[matMenuContent]',\n      providers: [{\n        provide: MAT_MENU_CONTENT,\n        useExisting: MatMenuContent\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Throws an exception for the case when menu's x-position value isn't valid.\n * In other words, it doesn't match 'before' or 'after'.\n * @docs-private\n */\n\n\nfunction throwMatMenuInvalidPositionX() {\n  throw Error(`xPosition value must be either 'before' or after'.\n      Example: <mat-menu xPosition=\"before\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when menu's y-position value isn't valid.\n * In other words, it doesn't match 'above' or 'below'.\n * @docs-private\n */\n\n\nfunction throwMatMenuInvalidPositionY() {\n  throw Error(`yPosition value must be either 'above' or below'.\n      Example: <mat-menu yPosition=\"above\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when a menu is assigned\n * to a trigger that is placed inside the same menu.\n * @docs-private\n */\n\n\nfunction throwMatMenuRecursiveError() {\n  throw Error(`matMenuTriggerFor: menu cannot contain its own trigger. Assign a menu that is ` + `not a parent of the trigger or move the trigger outside of the menu.`);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token used to provide the parent menu to menu-specific components.\n * @docs-private\n */\n\n\nconst MAT_MENU_PANEL = new InjectionToken('MAT_MENU_PANEL');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatMenuItem.\n\n/** @docs-private */\n\nconst _MatMenuItemBase = mixinDisableRipple(mixinDisabled(class {}));\n/**\n * Single item inside of a `mat-menu`. Provides the menu item styling and accessibility treatment.\n */\n\n\nclass MatMenuItem extends _MatMenuItemBase {\n  constructor(_elementRef, _document, _focusMonitor, _parentMenu, _changeDetectorRef) {\n    super();\n    this._elementRef = _elementRef;\n    this._document = _document;\n    this._focusMonitor = _focusMonitor;\n    this._parentMenu = _parentMenu;\n    this._changeDetectorRef = _changeDetectorRef;\n    /** ARIA role for the menu item. */\n\n    this.role = 'menuitem';\n    /** Stream that emits when the menu item is hovered. */\n\n    this._hovered = new Subject();\n    /** Stream that emits when the menu item is focused. */\n\n    this._focused = new Subject();\n    /** Whether the menu item is highlighted. */\n\n    this._highlighted = false;\n    /** Whether the menu item acts as a trigger for a sub-menu. */\n\n    this._triggersSubmenu = false;\n    _parentMenu?.addItem?.(this);\n  }\n  /** Focuses the menu item. */\n\n\n  focus(origin, options) {\n    if (this._focusMonitor && origin) {\n      this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n    } else {\n      this._getHostElement().focus(options);\n    }\n\n    this._focused.next(this);\n  }\n\n  ngAfterViewInit() {\n    if (this._focusMonitor) {\n      // Start monitoring the element so it gets the appropriate focused classes. We want\n      // to show the focus style for menu items only when the focus was not caused by a\n      // mouse or touch interaction.\n      this._focusMonitor.monitor(this._elementRef, false);\n    }\n  }\n\n  ngOnDestroy() {\n    if (this._focusMonitor) {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n    }\n\n    if (this._parentMenu && this._parentMenu.removeItem) {\n      this._parentMenu.removeItem(this);\n    }\n\n    this._hovered.complete();\n\n    this._focused.complete();\n  }\n  /** Used to set the `tabindex`. */\n\n\n  _getTabIndex() {\n    return this.disabled ? '-1' : '0';\n  }\n  /** Returns the host DOM element. */\n\n\n  _getHostElement() {\n    return this._elementRef.nativeElement;\n  }\n  /** Prevents the default element actions if it is disabled. */\n\n\n  _checkDisabled(event) {\n    if (this.disabled) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n  }\n  /** Emits to the hover stream. */\n\n\n  _handleMouseEnter() {\n    this._hovered.next(this);\n  }\n  /** Gets the label to be used when determining whether the option should be focused. */\n\n\n  getLabel() {\n    const clone = this._elementRef.nativeElement.cloneNode(true);\n\n    const icons = clone.querySelectorAll('mat-icon, .material-icons'); // Strip away icons so they don't show up in the text.\n\n    for (let i = 0; i < icons.length; i++) {\n      icons[i].remove();\n    }\n\n    return clone.textContent?.trim() || '';\n  }\n\n  _setHighlighted(isHighlighted) {\n    // We need to mark this for check for the case where the content is coming from a\n    // `matMenuContent` whose change detection tree is at the declaration position,\n    // not the insertion position. See #23175.\n    // @breaking-change 12.0.0 Remove null check for `_changeDetectorRef`.\n    this._highlighted = isHighlighted;\n    this._changeDetectorRef?.markForCheck();\n  }\n\n  _hasFocus() {\n    return this._document && this._document.activeElement === this._getHostElement();\n  }\n\n}\n\nMatMenuItem.ɵfac = function MatMenuItem_Factory(t) {\n  return new (t || MatMenuItem)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(MAT_MENU_PANEL, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nMatMenuItem.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatMenuItem,\n  selectors: [[\"\", \"mat-menu-item\", \"\"]],\n  hostAttrs: [1, \"mat-focus-indicator\"],\n  hostVars: 10,\n  hostBindings: function MatMenuItem_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function MatMenuItem_click_HostBindingHandler($event) {\n        return ctx._checkDisabled($event);\n      })(\"mouseenter\", function MatMenuItem_mouseenter_HostBindingHandler() {\n        return ctx._handleMouseEnter();\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵattribute(\"role\", ctx.role)(\"tabindex\", ctx._getTabIndex())(\"aria-disabled\", ctx.disabled.toString())(\"disabled\", ctx.disabled || null);\n      i0.ɵɵclassProp(\"mat-menu-item\", true)(\"mat-menu-item-highlighted\", ctx._highlighted)(\"mat-menu-item-submenu-trigger\", ctx._triggersSubmenu);\n    }\n  },\n  inputs: {\n    disabled: \"disabled\",\n    disableRipple: \"disableRipple\",\n    role: \"role\"\n  },\n  exportAs: [\"matMenuItem\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  attrs: _c0,\n  ngContentSelectors: _c1,\n  decls: 3,\n  vars: 3,\n  consts: [[\"matRipple\", \"\", 1, \"mat-menu-ripple\", 3, \"matRippleDisabled\", \"matRippleTrigger\"], [\"class\", \"mat-menu-submenu-icon\", \"viewBox\", \"0 0 5 10\", \"focusable\", \"false\", 4, \"ngIf\"], [\"viewBox\", \"0 0 5 10\", \"focusable\", \"false\", 1, \"mat-menu-submenu-icon\"], [\"points\", \"0,0 5,5 0,10\"]],\n  template: function MatMenuItem_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n      i0.ɵɵelement(1, \"div\", 0);\n      i0.ɵɵtemplate(2, MatMenuItem__svg_svg_2_Template, 2, 0, \"svg\", 1);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleTrigger\", ctx._getHostElement());\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx._triggersSubmenu);\n    }\n  },\n  dependencies: [i2.NgIf, i3.MatRipple],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuItem, [{\n    type: Component,\n    args: [{\n      selector: '[mat-menu-item]',\n      exportAs: 'matMenuItem',\n      inputs: ['disabled', 'disableRipple'],\n      host: {\n        '[attr.role]': 'role',\n        '[class.mat-menu-item]': 'true',\n        '[class.mat-menu-item-highlighted]': '_highlighted',\n        '[class.mat-menu-item-submenu-trigger]': '_triggersSubmenu',\n        '[attr.tabindex]': '_getTabIndex()',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '[attr.disabled]': 'disabled || null',\n        'class': 'mat-focus-indicator',\n        '(click)': '_checkDisabled($event)',\n        '(mouseenter)': '_handleMouseEnter()'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<ng-content></ng-content>\\n<div class=\\\"mat-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n\\n<svg\\n  *ngIf=\\\"_triggersSubmenu\\\"\\n  class=\\\"mat-menu-submenu-icon\\\"\\n  viewBox=\\\"0 0 5 10\\\"\\n  focusable=\\\"false\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n\"\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1.FocusMonitor\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_MENU_PANEL]\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    role: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injection token to be used to override the default options for `mat-menu`. */\n\n\nconst MAT_MENU_DEFAULT_OPTIONS = new InjectionToken('mat-menu-default-options', {\n  providedIn: 'root',\n  factory: MAT_MENU_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\n\nfunction MAT_MENU_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    overlapTrigger: false,\n    xPosition: 'after',\n    yPosition: 'below',\n    backdropClass: 'cdk-overlay-transparent-backdrop'\n  };\n}\n\nlet menuPanelUid = 0;\n/** Base class with all of the `MatMenu` functionality. */\n\nclass _MatMenuBase {\n  constructor(_elementRef, _ngZone, _defaultOptions, // @breaking-change 15.0.0 `_changeDetectorRef` to become a required parameter.\n  _changeDetectorRef) {\n    this._elementRef = _elementRef;\n    this._ngZone = _ngZone;\n    this._defaultOptions = _defaultOptions;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._xPosition = this._defaultOptions.xPosition;\n    this._yPosition = this._defaultOptions.yPosition;\n    /** Only the direct descendant menu items. */\n\n    this._directDescendantItems = new QueryList();\n    /** Subscription to tab events on the menu panel */\n\n    this._tabSubscription = Subscription.EMPTY;\n    /** Config object to be passed into the menu's ngClass */\n\n    this._classList = {};\n    /** Current state of the panel animation. */\n\n    this._panelAnimationState = 'void';\n    /** Emits whenever an animation on the menu completes. */\n\n    this._animationDone = new Subject();\n    /** Class or list of classes to be added to the overlay panel. */\n\n    this.overlayPanelClass = this._defaultOptions.overlayPanelClass || '';\n    /** Class to be added to the backdrop element. */\n\n    this.backdropClass = this._defaultOptions.backdropClass;\n    this._overlapTrigger = this._defaultOptions.overlapTrigger;\n    this._hasBackdrop = this._defaultOptions.hasBackdrop;\n    /** Event emitted when the menu is closed. */\n\n    this.closed = new EventEmitter();\n    /**\n     * Event emitted when the menu is closed.\n     * @deprecated Switch to `closed` instead\n     * @breaking-change 8.0.0\n     */\n\n    this.close = this.closed;\n    this.panelId = `mat-menu-panel-${menuPanelUid++}`;\n  }\n  /** Position of the menu in the X axis. */\n\n\n  get xPosition() {\n    return this._xPosition;\n  }\n\n  set xPosition(value) {\n    if (value !== 'before' && value !== 'after' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwMatMenuInvalidPositionX();\n    }\n\n    this._xPosition = value;\n    this.setPositionClasses();\n  }\n  /** Position of the menu in the Y axis. */\n\n\n  get yPosition() {\n    return this._yPosition;\n  }\n\n  set yPosition(value) {\n    if (value !== 'above' && value !== 'below' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwMatMenuInvalidPositionY();\n    }\n\n    this._yPosition = value;\n    this.setPositionClasses();\n  }\n  /** Whether the menu should overlap its trigger. */\n\n\n  get overlapTrigger() {\n    return this._overlapTrigger;\n  }\n\n  set overlapTrigger(value) {\n    this._overlapTrigger = coerceBooleanProperty(value);\n  }\n  /** Whether the menu has a backdrop. */\n\n\n  get hasBackdrop() {\n    return this._hasBackdrop;\n  }\n\n  set hasBackdrop(value) {\n    this._hasBackdrop = coerceBooleanProperty(value);\n  }\n  /**\n   * This method takes classes set on the host mat-menu element and applies them on the\n   * menu template that displays in the overlay container.  Otherwise, it's difficult\n   * to style the containing menu from outside the component.\n   * @param classes list of class names\n   */\n\n\n  set panelClass(classes) {\n    const previousPanelClass = this._previousPanelClass;\n\n    if (previousPanelClass && previousPanelClass.length) {\n      previousPanelClass.split(' ').forEach(className => {\n        this._classList[className] = false;\n      });\n    }\n\n    this._previousPanelClass = classes;\n\n    if (classes && classes.length) {\n      classes.split(' ').forEach(className => {\n        this._classList[className] = true;\n      });\n      this._elementRef.nativeElement.className = '';\n    }\n  }\n  /**\n   * This method takes classes set on the host mat-menu element and applies them on the\n   * menu template that displays in the overlay container.  Otherwise, it's difficult\n   * to style the containing menu from outside the component.\n   * @deprecated Use `panelClass` instead.\n   * @breaking-change 8.0.0\n   */\n\n\n  get classList() {\n    return this.panelClass;\n  }\n\n  set classList(classes) {\n    this.panelClass = classes;\n  }\n\n  ngOnInit() {\n    this.setPositionClasses();\n  }\n\n  ngAfterContentInit() {\n    this._updateDirectDescendants();\n\n    this._keyManager = new FocusKeyManager(this._directDescendantItems).withWrap().withTypeAhead().withHomeAndEnd();\n    this._tabSubscription = this._keyManager.tabOut.subscribe(() => this.closed.emit('tab')); // If a user manually (programmatically) focuses a menu item, we need to reflect that focus\n    // change back to the key manager. Note that we don't need to unsubscribe here because _focused\n    // is internal and we know that it gets completed on destroy.\n\n    this._directDescendantItems.changes.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map(item => item._focused)))).subscribe(focusedItem => this._keyManager.updateActiveItem(focusedItem));\n\n    this._directDescendantItems.changes.subscribe(itemsList => {\n      // Move focus to another item, if the active item is removed from the list.\n      // We need to debounce the callback, because multiple items might be removed\n      // in quick succession.\n      const manager = this._keyManager;\n\n      if (this._panelAnimationState === 'enter' && manager.activeItem?._hasFocus()) {\n        const items = itemsList.toArray();\n        const index = Math.max(0, Math.min(items.length - 1, manager.activeItemIndex || 0));\n\n        if (items[index] && !items[index].disabled) {\n          manager.setActiveItem(index);\n        } else {\n          manager.setNextItemActive();\n        }\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    this._directDescendantItems.destroy();\n\n    this._tabSubscription.unsubscribe();\n\n    this.closed.complete();\n  }\n  /** Stream that emits whenever the hovered menu item changes. */\n\n\n  _hovered() {\n    // Coerce the `changes` property because Angular types it as `Observable<any>`\n    const itemChanges = this._directDescendantItems.changes;\n    return itemChanges.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map(item => item._hovered))));\n  }\n  /*\n   * Registers a menu item with the menu.\n   * @docs-private\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 9.0.0\n   */\n\n\n  addItem(_item) {}\n  /**\n   * Removes an item from the menu.\n   * @docs-private\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 9.0.0\n   */\n\n\n  removeItem(_item) {}\n  /** Handle a keyboard event from the menu, delegating to the appropriate action. */\n\n\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    const manager = this._keyManager;\n\n    switch (keyCode) {\n      case ESCAPE:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          this.closed.emit('keydown');\n        }\n\n        break;\n\n      case LEFT_ARROW:\n        if (this.parentMenu && this.direction === 'ltr') {\n          this.closed.emit('keydown');\n        }\n\n        break;\n\n      case RIGHT_ARROW:\n        if (this.parentMenu && this.direction === 'rtl') {\n          this.closed.emit('keydown');\n        }\n\n        break;\n\n      default:\n        if (keyCode === UP_ARROW || keyCode === DOWN_ARROW) {\n          manager.setFocusOrigin('keyboard');\n        }\n\n        manager.onKeydown(event);\n        return;\n    } // Don't allow the event to propagate if we've already handled it, or it may\n    // end up reaching other overlays that were opened earlier (see #22694).\n\n\n    event.stopPropagation();\n  }\n  /**\n   * Focus the first item in the menu.\n   * @param origin Action from which the focus originated. Used to set the correct styling.\n   */\n\n\n  focusFirstItem(origin = 'program') {\n    // Wait for `onStable` to ensure iOS VoiceOver screen reader focuses the first item (#24735).\n    this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n      let menuPanel = null;\n\n      if (this._directDescendantItems.length) {\n        // Because the `mat-menuPanel` is at the DOM insertion point, not inside the overlay, we don't\n        // have a nice way of getting a hold of the menuPanel panel. We can't use a `ViewChild` either\n        // because the panel is inside an `ng-template`. We work around it by starting from one of\n        // the items and walking up the DOM.\n        menuPanel = this._directDescendantItems.first._getHostElement().closest('[role=\"menu\"]');\n      } // If an item in the menuPanel is already focused, avoid overriding the focus.\n\n\n      if (!menuPanel || !menuPanel.contains(document.activeElement)) {\n        const manager = this._keyManager;\n        manager.setFocusOrigin(origin).setFirstItemActive(); // If there's no active item at this point, it means that all the items are disabled.\n        // Move focus to the menuPanel panel so keyboard events like Escape still work. Also this will\n        // give _some_ feedback to screen readers.\n\n        if (!manager.activeItem && menuPanel) {\n          menuPanel.focus();\n        }\n      }\n    });\n  }\n  /**\n   * Resets the active item in the menu. This is used when the menu is opened, allowing\n   * the user to start from the first option when pressing the down arrow.\n   */\n\n\n  resetActiveItem() {\n    this._keyManager.setActiveItem(-1);\n  }\n  /**\n   * Sets the menu panel elevation.\n   * @param depth Number of parent menus that come before the menu.\n   */\n\n\n  setElevation(depth) {\n    // The elevation starts at the base and increases by one for each level.\n    // Capped at 24 because that's the maximum elevation defined in the Material design spec.\n    const elevation = Math.min(this._baseElevation + depth, 24);\n    const newElevation = `${this._elevationPrefix}${elevation}`;\n    const customElevation = Object.keys(this._classList).find(className => {\n      return className.startsWith(this._elevationPrefix);\n    });\n\n    if (!customElevation || customElevation === this._previousElevation) {\n      if (this._previousElevation) {\n        this._classList[this._previousElevation] = false;\n      }\n\n      this._classList[newElevation] = true;\n      this._previousElevation = newElevation;\n    }\n  }\n  /**\n   * Adds classes to the menu panel based on its position. Can be used by\n   * consumers to add specific styling based on the position.\n   * @param posX Position of the menu along the x axis.\n   * @param posY Position of the menu along the y axis.\n   * @docs-private\n   */\n\n\n  setPositionClasses(posX = this.xPosition, posY = this.yPosition) {\n    const classes = this._classList;\n    classes['mat-menu-before'] = posX === 'before';\n    classes['mat-menu-after'] = posX === 'after';\n    classes['mat-menu-above'] = posY === 'above';\n    classes['mat-menu-below'] = posY === 'below'; // @breaking-change 15.0.0 Remove null check for `_changeDetectorRef`.\n\n    this._changeDetectorRef?.markForCheck();\n  }\n  /** Starts the enter animation. */\n\n\n  _startAnimation() {\n    // @breaking-change 8.0.0 Combine with _resetAnimation.\n    this._panelAnimationState = 'enter';\n  }\n  /** Resets the panel animation to its initial state. */\n\n\n  _resetAnimation() {\n    // @breaking-change 8.0.0 Combine with _startAnimation.\n    this._panelAnimationState = 'void';\n  }\n  /** Callback that is invoked when the panel animation completes. */\n\n\n  _onAnimationDone(event) {\n    this._animationDone.next(event);\n\n    this._isAnimating = false;\n  }\n\n  _onAnimationStart(event) {\n    this._isAnimating = true; // Scroll the content element to the top as soon as the animation starts. This is necessary,\n    // because we move focus to the first item while it's still being animated, which can throw\n    // the browser off when it determines the scroll position. Alternatively we can move focus\n    // when the animation is done, however moving focus asynchronously will interrupt screen\n    // readers which are in the process of reading out the menu already. We take the `element`\n    // from the `event` since we can't use a `ViewChild` to access the pane.\n\n    if (event.toState === 'enter' && this._keyManager.activeItemIndex === 0) {\n      event.element.scrollTop = 0;\n    }\n  }\n  /**\n   * Sets up a stream that will keep track of any newly-added menu items and will update the list\n   * of direct descendants. We collect the descendants this way, because `_allItems` can include\n   * items that are part of child menus, and using a custom way of registering items is unreliable\n   * when it comes to maintaining the item order.\n   */\n\n\n  _updateDirectDescendants() {\n    this._allItems.changes.pipe(startWith(this._allItems)).subscribe(items => {\n      this._directDescendantItems.reset(items.filter(item => item._parentMenu === this));\n\n      this._directDescendantItems.notifyOnChanges();\n    });\n  }\n\n}\n\n_MatMenuBase.ɵfac = function _MatMenuBase_Factory(t) {\n  return new (t || _MatMenuBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(MAT_MENU_DEFAULT_OPTIONS), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\n_MatMenuBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatMenuBase,\n  contentQueries: function _MatMenuBase_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MAT_MENU_CONTENT, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatMenuItem, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatMenuItem, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lazyContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allItems = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.items = _t);\n    }\n  },\n  viewQuery: function _MatMenuBase_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(TemplateRef, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateRef = _t.first);\n    }\n  },\n  inputs: {\n    backdropClass: \"backdropClass\",\n    ariaLabel: [\"aria-label\", \"ariaLabel\"],\n    ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n    ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"],\n    xPosition: \"xPosition\",\n    yPosition: \"yPosition\",\n    overlapTrigger: \"overlapTrigger\",\n    hasBackdrop: \"hasBackdrop\",\n    panelClass: [\"class\", \"panelClass\"],\n    classList: \"classList\"\n  },\n  outputs: {\n    closed: \"closed\",\n    close: \"close\"\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatMenuBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_MENU_DEFAULT_OPTIONS]\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    _allItems: [{\n      type: ContentChildren,\n      args: [MatMenuItem, {\n        descendants: true\n      }]\n    }],\n    backdropClass: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    xPosition: [{\n      type: Input\n    }],\n    yPosition: [{\n      type: Input\n    }],\n    templateRef: [{\n      type: ViewChild,\n      args: [TemplateRef]\n    }],\n    items: [{\n      type: ContentChildren,\n      args: [MatMenuItem, {\n        descendants: false\n      }]\n    }],\n    lazyContent: [{\n      type: ContentChild,\n      args: [MAT_MENU_CONTENT]\n    }],\n    overlapTrigger: [{\n      type: Input\n    }],\n    hasBackdrop: [{\n      type: Input\n    }],\n    panelClass: [{\n      type: Input,\n      args: ['class']\n    }],\n    classList: [{\n      type: Input\n    }],\n    closed: [{\n      type: Output\n    }],\n    close: [{\n      type: Output\n    }]\n  });\n})();\n/** @docs-public MatMenu */\n\n\nclass MatMenu extends _MatMenuBase {\n  constructor(elementRef, ngZone, defaultOptions, changeDetectorRef) {\n    super(elementRef, ngZone, defaultOptions, changeDetectorRef);\n    this._elevationPrefix = 'mat-elevation-z';\n    this._baseElevation = 4;\n  }\n\n}\n\nMatMenu.ɵfac = function MatMenu_Factory(t) {\n  return new (t || MatMenu)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(MAT_MENU_DEFAULT_OPTIONS), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nMatMenu.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatMenu,\n  selectors: [[\"mat-menu\"]],\n  hostVars: 3,\n  hostBindings: function MatMenu_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-label\", null)(\"aria-labelledby\", null)(\"aria-describedby\", null);\n    }\n  },\n  exportAs: [\"matMenu\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_MENU_PANEL,\n    useExisting: MatMenu\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c1,\n  decls: 1,\n  vars: 0,\n  consts: [[\"tabindex\", \"-1\", \"role\", \"menu\", 1, \"mat-menu-panel\", 3, \"id\", \"ngClass\", \"keydown\", \"click\"], [1, \"mat-menu-content\"]],\n  template: function MatMenu_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵtemplate(0, MatMenu_ng_template_0_Template, 3, 6, \"ng-template\");\n    }\n  },\n  dependencies: [i2.NgClass],\n  styles: [\"mat-menu{display:none}.mat-menu-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;max-height:calc(100vh - 48px);border-radius:4px;outline:0;min-height:64px;position:relative}.mat-menu-panel.ng-animating{pointer-events:none}.cdk-high-contrast-active .mat-menu-panel{outline:solid 1px}.mat-menu-content:not(:empty){padding-top:8px;padding-bottom:8px}.mat-menu-item{-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;line-height:48px;height:48px;padding:0 16px;text-align:left;text-decoration:none;max-width:100%;position:relative}.mat-menu-item::-moz-focus-inner{border:0}.mat-menu-item[disabled]{cursor:default}[dir=rtl] .mat-menu-item{text-align:right}.mat-menu-item .mat-icon{margin-right:16px;vertical-align:middle}.mat-menu-item .mat-icon svg{vertical-align:top}[dir=rtl] .mat-menu-item .mat-icon{margin-left:16px;margin-right:0}.mat-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.cdk-high-contrast-active .mat-menu-item{margin-top:1px}.mat-menu-item-submenu-trigger{padding-right:32px}[dir=rtl] .mat-menu-item-submenu-trigger{padding-right:16px;padding-left:32px}.mat-menu-submenu-icon{position:absolute;top:50%;right:16px;transform:translateY(-50%);width:5px;height:10px;fill:currentColor}[dir=rtl] .mat-menu-submenu-icon{right:auto;left:16px;transform:translateY(-50%) scaleX(-1)}.cdk-high-contrast-active .mat-menu-submenu-icon{fill:CanvasText}button.mat-menu-item{width:100%}.mat-menu-item .mat-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"],\n  encapsulation: 2,\n  data: {\n    animation: [matMenuAnimations.transformMenu, matMenuAnimations.fadeInItems]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenu, [{\n    type: Component,\n    args: [{\n      selector: 'mat-menu',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matMenu',\n      host: {\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[attr.aria-describedby]': 'null'\n      },\n      animations: [matMenuAnimations.transformMenu, matMenuAnimations.fadeInItems],\n      providers: [{\n        provide: MAT_MENU_PANEL,\n        useExisting: MatMenu\n      }],\n      template: \"<ng-template>\\n  <div\\n    class=\\\"mat-menu-panel\\\"\\n    [id]=\\\"panelId\\\"\\n    [ngClass]=\\\"_classList\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    [@transformMenu]=\\\"_panelAnimationState\\\"\\n    (@transformMenu.start)=\\\"_onAnimationStart($event)\\\"\\n    (@transformMenu.done)=\\\"_onAnimationDone($event)\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-menu-content\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\",\n      styles: [\"mat-menu{display:none}.mat-menu-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;max-height:calc(100vh - 48px);border-radius:4px;outline:0;min-height:64px;position:relative}.mat-menu-panel.ng-animating{pointer-events:none}.cdk-high-contrast-active .mat-menu-panel{outline:solid 1px}.mat-menu-content:not(:empty){padding-top:8px;padding-bottom:8px}.mat-menu-item{-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;line-height:48px;height:48px;padding:0 16px;text-align:left;text-decoration:none;max-width:100%;position:relative}.mat-menu-item::-moz-focus-inner{border:0}.mat-menu-item[disabled]{cursor:default}[dir=rtl] .mat-menu-item{text-align:right}.mat-menu-item .mat-icon{margin-right:16px;vertical-align:middle}.mat-menu-item .mat-icon svg{vertical-align:top}[dir=rtl] .mat-menu-item .mat-icon{margin-left:16px;margin-right:0}.mat-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.cdk-high-contrast-active .mat-menu-item{margin-top:1px}.mat-menu-item-submenu-trigger{padding-right:32px}[dir=rtl] .mat-menu-item-submenu-trigger{padding-right:16px;padding-left:32px}.mat-menu-submenu-icon{position:absolute;top:50%;right:16px;transform:translateY(-50%);width:5px;height:10px;fill:currentColor}[dir=rtl] .mat-menu-submenu-icon{right:auto;left:16px;transform:translateY(-50%) scaleX(-1)}.cdk-high-contrast-active .mat-menu-submenu-icon{fill:CanvasText}button.mat-menu-item{width:100%}.mat-menu-item .mat-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_MENU_DEFAULT_OPTIONS]\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injection token that determines the scroll handling while the menu is open. */\n\n\nconst MAT_MENU_SCROLL_STRATEGY = new InjectionToken('mat-menu-scroll-strategy');\n/** @docs-private */\n\nfunction MAT_MENU_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\n\n\nconst MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_MENU_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_MENU_SCROLL_STRATEGY_FACTORY\n};\n/**\n * Default top padding of the menu panel.\n * @deprecated No longer being used. Will be removed.\n * @breaking-change 15.0.0\n */\n\nconst MENU_PANEL_TOP_PADDING = 8;\n/** Options for binding a passive event listener. */\n\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n}); // TODO(andrewseguin): Remove the kebab versions in favor of camelCased attribute selectors\n\nclass _MatMenuTriggerBase {\n  constructor(_overlay, _element, _viewContainerRef, scrollStrategy, parentMenu, // `MatMenuTrigger` is commonly used in combination with a `MatMenuItem`.\n  // tslint:disable-next-line: lightweight-tokens\n  _menuItemInstance, _dir, _focusMonitor, _ngZone) {\n    this._overlay = _overlay;\n    this._element = _element;\n    this._viewContainerRef = _viewContainerRef;\n    this._menuItemInstance = _menuItemInstance;\n    this._dir = _dir;\n    this._focusMonitor = _focusMonitor;\n    this._ngZone = _ngZone;\n    this._overlayRef = null;\n    this._menuOpen = false;\n    this._closingActionsSubscription = Subscription.EMPTY;\n    this._hoverSubscription = Subscription.EMPTY;\n    this._menuCloseSubscription = Subscription.EMPTY;\n    /**\n     * Handles touch start events on the trigger.\n     * Needs to be an arrow function so we can easily use addEventListener and removeEventListener.\n     */\n\n    this._handleTouchStart = event => {\n      if (!isFakeTouchstartFromScreenReader(event)) {\n        this._openedBy = 'touch';\n      }\n    }; // Tracking input type is necessary so it's possible to only auto-focus\n    // the first item of the list when the menu is opened via the keyboard\n\n\n    this._openedBy = undefined;\n    /**\n     * Whether focus should be restored when the menu is closed.\n     * Note that disabling this option can have accessibility implications\n     * and it's up to you to manage focus, if you decide to turn it off.\n     */\n\n    this.restoreFocus = true;\n    /** Event emitted when the associated menu is opened. */\n\n    this.menuOpened = new EventEmitter();\n    /**\n     * Event emitted when the associated menu is opened.\n     * @deprecated Switch to `menuOpened` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n\n    this.onMenuOpen = this.menuOpened;\n    /** Event emitted when the associated menu is closed. */\n\n    this.menuClosed = new EventEmitter();\n    /**\n     * Event emitted when the associated menu is closed.\n     * @deprecated Switch to `menuClosed` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n\n    this.onMenuClose = this.menuClosed;\n    this._scrollStrategy = scrollStrategy;\n    this._parentMaterialMenu = parentMenu instanceof _MatMenuBase ? parentMenu : undefined;\n\n    _element.nativeElement.addEventListener('touchstart', this._handleTouchStart, passiveEventListenerOptions);\n\n    if (_menuItemInstance) {\n      _menuItemInstance._triggersSubmenu = this.triggersSubmenu();\n    }\n  }\n  /**\n   * @deprecated\n   * @breaking-change 8.0.0\n   */\n\n\n  get _deprecatedMatMenuTriggerFor() {\n    return this.menu;\n  }\n\n  set _deprecatedMatMenuTriggerFor(v) {\n    this.menu = v;\n  }\n  /** References the menu instance that the trigger is associated with. */\n\n\n  get menu() {\n    return this._menu;\n  }\n\n  set menu(menu) {\n    if (menu === this._menu) {\n      return;\n    }\n\n    this._menu = menu;\n\n    this._menuCloseSubscription.unsubscribe();\n\n    if (menu) {\n      if (menu === this._parentMaterialMenu && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwMatMenuRecursiveError();\n      }\n\n      this._menuCloseSubscription = menu.close.subscribe(reason => {\n        this._destroyMenu(reason); // If a click closed the menu, we should close the entire chain of nested menus.\n\n\n        if ((reason === 'click' || reason === 'tab') && this._parentMaterialMenu) {\n          this._parentMaterialMenu.closed.emit(reason);\n        }\n      });\n    }\n  }\n\n  ngAfterContentInit() {\n    this._handleHover();\n  }\n\n  ngOnDestroy() {\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n\n      this._overlayRef = null;\n    }\n\n    this._element.nativeElement.removeEventListener('touchstart', this._handleTouchStart, passiveEventListenerOptions);\n\n    this._menuCloseSubscription.unsubscribe();\n\n    this._closingActionsSubscription.unsubscribe();\n\n    this._hoverSubscription.unsubscribe();\n  }\n  /** Whether the menu is open. */\n\n\n  get menuOpen() {\n    return this._menuOpen;\n  }\n  /** The text direction of the containing app. */\n\n\n  get dir() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Whether the menu triggers a sub-menu or a top-level one. */\n\n\n  triggersSubmenu() {\n    return !!(this._menuItemInstance && this._parentMaterialMenu);\n  }\n  /** Toggles the menu between the open and closed states. */\n\n\n  toggleMenu() {\n    return this._menuOpen ? this.closeMenu() : this.openMenu();\n  }\n  /** Opens the menu. */\n\n\n  openMenu() {\n    const menu = this.menu;\n\n    if (this._menuOpen || !menu) {\n      return;\n    }\n\n    const overlayRef = this._createOverlay(menu);\n\n    const overlayConfig = overlayRef.getConfig();\n    const positionStrategy = overlayConfig.positionStrategy;\n\n    this._setPosition(menu, positionStrategy);\n\n    overlayConfig.hasBackdrop = menu.hasBackdrop == null ? !this.triggersSubmenu() : menu.hasBackdrop;\n    overlayRef.attach(this._getPortal(menu));\n\n    if (menu.lazyContent) {\n      menu.lazyContent.attach(this.menuData);\n    }\n\n    this._closingActionsSubscription = this._menuClosingActions().subscribe(() => this.closeMenu());\n\n    this._initMenu(menu);\n\n    if (menu instanceof _MatMenuBase) {\n      menu._startAnimation();\n\n      menu._directDescendantItems.changes.pipe(takeUntil(menu.close)).subscribe(() => {\n        // Re-adjust the position without locking when the amount of items\n        // changes so that the overlay is allowed to pick a new optimal position.\n        positionStrategy.withLockedPosition(false).reapplyLastPosition();\n        positionStrategy.withLockedPosition(true);\n      });\n    }\n  }\n  /** Closes the menu. */\n\n\n  closeMenu() {\n    this.menu?.close.emit();\n  }\n  /**\n   * Focuses the menu trigger.\n   * @param origin Source of the menu trigger's focus.\n   */\n\n\n  focus(origin, options) {\n    if (this._focusMonitor && origin) {\n      this._focusMonitor.focusVia(this._element, origin, options);\n    } else {\n      this._element.nativeElement.focus(options);\n    }\n  }\n  /**\n   * Updates the position of the menu to ensure that it fits all options within the viewport.\n   */\n\n\n  updatePosition() {\n    this._overlayRef?.updatePosition();\n  }\n  /** Closes the menu and does the necessary cleanup. */\n\n\n  _destroyMenu(reason) {\n    if (!this._overlayRef || !this.menuOpen) {\n      return;\n    }\n\n    const menu = this.menu;\n\n    this._closingActionsSubscription.unsubscribe();\n\n    this._overlayRef.detach(); // Always restore focus if the user is navigating using the keyboard or the menu was opened\n    // programmatically. We don't restore for non-root triggers, because it can prevent focus\n    // from making it back to the root trigger when closing a long chain of menus by clicking\n    // on the backdrop.\n\n\n    if (this.restoreFocus && (reason === 'keydown' || !this._openedBy || !this.triggersSubmenu())) {\n      this.focus(this._openedBy);\n    }\n\n    this._openedBy = undefined;\n\n    if (menu instanceof _MatMenuBase) {\n      menu._resetAnimation();\n\n      if (menu.lazyContent) {\n        // Wait for the exit animation to finish before detaching the content.\n        menu._animationDone.pipe(filter(event => event.toState === 'void'), take(1), // Interrupt if the content got re-attached.\n        takeUntil(menu.lazyContent._attached)).subscribe({\n          next: () => menu.lazyContent.detach(),\n          // No matter whether the content got re-attached, reset the menu.\n          complete: () => this._setIsMenuOpen(false)\n        });\n      } else {\n        this._setIsMenuOpen(false);\n      }\n    } else {\n      this._setIsMenuOpen(false);\n\n      menu?.lazyContent?.detach();\n    }\n  }\n  /**\n   * This method sets the menu state to open and focuses the first item if\n   * the menu was opened via the keyboard.\n   */\n\n\n  _initMenu(menu) {\n    menu.parentMenu = this.triggersSubmenu() ? this._parentMaterialMenu : undefined;\n    menu.direction = this.dir;\n\n    this._setMenuElevation(menu);\n\n    menu.focusFirstItem(this._openedBy || 'program');\n\n    this._setIsMenuOpen(true);\n  }\n  /** Updates the menu elevation based on the amount of parent menus that it has. */\n\n\n  _setMenuElevation(menu) {\n    if (menu.setElevation) {\n      let depth = 0;\n      let parentMenu = menu.parentMenu;\n\n      while (parentMenu) {\n        depth++;\n        parentMenu = parentMenu.parentMenu;\n      }\n\n      menu.setElevation(depth);\n    }\n  } // set state rather than toggle to support triggers sharing a menu\n\n\n  _setIsMenuOpen(isOpen) {\n    this._menuOpen = isOpen;\n    this._menuOpen ? this.menuOpened.emit() : this.menuClosed.emit();\n\n    if (this.triggersSubmenu()) {\n      this._menuItemInstance._setHighlighted(isOpen);\n    }\n  }\n  /**\n   * This method creates the overlay from the provided menu's template and saves its\n   * OverlayRef so that it can be attached to the DOM when openMenu is called.\n   */\n\n\n  _createOverlay(menu) {\n    if (!this._overlayRef) {\n      const config = this._getOverlayConfig(menu);\n\n      this._subscribeToPositions(menu, config.positionStrategy);\n\n      this._overlayRef = this._overlay.create(config); // Consume the `keydownEvents` in order to prevent them from going to another overlay.\n      // Ideally we'd also have our keyboard event logic in here, however doing so will\n      // break anybody that may have implemented the `MatMenuPanel` themselves.\n\n      this._overlayRef.keydownEvents().subscribe();\n    }\n\n    return this._overlayRef;\n  }\n  /**\n   * This method builds the configuration object needed to create the overlay, the OverlayState.\n   * @returns OverlayConfig\n   */\n\n\n  _getOverlayConfig(menu) {\n    return new OverlayConfig({\n      positionStrategy: this._overlay.position().flexibleConnectedTo(this._element).withLockedPosition().withGrowAfterOpen().withTransformOriginOn('.mat-menu-panel, .mat-mdc-menu-panel'),\n      backdropClass: menu.backdropClass || 'cdk-overlay-transparent-backdrop',\n      panelClass: menu.overlayPanelClass,\n      scrollStrategy: this._scrollStrategy(),\n      direction: this._dir\n    });\n  }\n  /**\n   * Listens to changes in the position of the overlay and sets the correct classes\n   * on the menu based on the new position. This ensures the animation origin is always\n   * correct, even if a fallback position is used for the overlay.\n   */\n\n\n  _subscribeToPositions(menu, position) {\n    if (menu.setPositionClasses) {\n      position.positionChanges.subscribe(change => {\n        const posX = change.connectionPair.overlayX === 'start' ? 'after' : 'before';\n        const posY = change.connectionPair.overlayY === 'top' ? 'below' : 'above'; // @breaking-change 15.0.0 Remove null check for `ngZone`.\n        // `positionChanges` fires outside of the `ngZone` and `setPositionClasses` might be\n        // updating something in the view so we need to bring it back in.\n\n        if (this._ngZone) {\n          this._ngZone.run(() => menu.setPositionClasses(posX, posY));\n        } else {\n          menu.setPositionClasses(posX, posY);\n        }\n      });\n    }\n  }\n  /**\n   * Sets the appropriate positions on a position strategy\n   * so the overlay connects with the trigger correctly.\n   * @param positionStrategy Strategy whose position to update.\n   */\n\n\n  _setPosition(menu, positionStrategy) {\n    let [originX, originFallbackX] = menu.xPosition === 'before' ? ['end', 'start'] : ['start', 'end'];\n    let [overlayY, overlayFallbackY] = menu.yPosition === 'above' ? ['bottom', 'top'] : ['top', 'bottom'];\n    let [originY, originFallbackY] = [overlayY, overlayFallbackY];\n    let [overlayX, overlayFallbackX] = [originX, originFallbackX];\n    let offsetY = 0;\n\n    if (this.triggersSubmenu()) {\n      // When the menu is a sub-menu, it should always align itself\n      // to the edges of the trigger, instead of overlapping it.\n      overlayFallbackX = originX = menu.xPosition === 'before' ? 'start' : 'end';\n      originFallbackX = overlayX = originX === 'end' ? 'start' : 'end';\n\n      if (this._parentMaterialMenu) {\n        if (this._parentInnerPadding == null) {\n          const firstItem = this._parentMaterialMenu.items.first;\n          this._parentInnerPadding = firstItem ? firstItem._getHostElement().offsetTop : 0;\n        }\n\n        offsetY = overlayY === 'bottom' ? this._parentInnerPadding : -this._parentInnerPadding;\n      }\n    } else if (!menu.overlapTrigger) {\n      originY = overlayY === 'top' ? 'bottom' : 'top';\n      originFallbackY = overlayFallbackY === 'top' ? 'bottom' : 'top';\n    }\n\n    positionStrategy.withPositions([{\n      originX,\n      originY,\n      overlayX,\n      overlayY,\n      offsetY\n    }, {\n      originX: originFallbackX,\n      originY,\n      overlayX: overlayFallbackX,\n      overlayY,\n      offsetY\n    }, {\n      originX,\n      originY: originFallbackY,\n      overlayX,\n      overlayY: overlayFallbackY,\n      offsetY: -offsetY\n    }, {\n      originX: originFallbackX,\n      originY: originFallbackY,\n      overlayX: overlayFallbackX,\n      overlayY: overlayFallbackY,\n      offsetY: -offsetY\n    }]);\n  }\n  /** Returns a stream that emits whenever an action that should close the menu occurs. */\n\n\n  _menuClosingActions() {\n    const backdrop = this._overlayRef.backdropClick();\n\n    const detachments = this._overlayRef.detachments();\n\n    const parentClose = this._parentMaterialMenu ? this._parentMaterialMenu.closed : of();\n    const hover = this._parentMaterialMenu ? this._parentMaterialMenu._hovered().pipe(filter(active => active !== this._menuItemInstance), filter(() => this._menuOpen)) : of();\n    return merge(backdrop, parentClose, hover, detachments);\n  }\n  /** Handles mouse presses on the trigger. */\n\n\n  _handleMousedown(event) {\n    if (!isFakeMousedownFromScreenReader(event)) {\n      // Since right or middle button clicks won't trigger the `click` event,\n      // we shouldn't consider the menu as opened by mouse in those cases.\n      this._openedBy = event.button === 0 ? 'mouse' : undefined; // Since clicking on the trigger won't close the menu if it opens a sub-menu,\n      // we should prevent focus from moving onto it via click to avoid the\n      // highlight from lingering on the menu item.\n\n      if (this.triggersSubmenu()) {\n        event.preventDefault();\n      }\n    }\n  }\n  /** Handles key presses on the trigger. */\n\n\n  _handleKeydown(event) {\n    const keyCode = event.keyCode; // Pressing enter on the trigger will trigger the click handler later.\n\n    if (keyCode === ENTER || keyCode === SPACE) {\n      this._openedBy = 'keyboard';\n    }\n\n    if (this.triggersSubmenu() && (keyCode === RIGHT_ARROW && this.dir === 'ltr' || keyCode === LEFT_ARROW && this.dir === 'rtl')) {\n      this._openedBy = 'keyboard';\n      this.openMenu();\n    }\n  }\n  /** Handles click events on the trigger. */\n\n\n  _handleClick(event) {\n    if (this.triggersSubmenu()) {\n      // Stop event propagation to avoid closing the parent menu.\n      event.stopPropagation();\n      this.openMenu();\n    } else {\n      this.toggleMenu();\n    }\n  }\n  /** Handles the cases where the user hovers over the trigger. */\n\n\n  _handleHover() {\n    // Subscribe to changes in the hovered item in order to toggle the panel.\n    if (!this.triggersSubmenu() || !this._parentMaterialMenu) {\n      return;\n    }\n\n    this._hoverSubscription = this._parentMaterialMenu._hovered() // Since we might have multiple competing triggers for the same menu (e.g. a sub-menu\n    // with different data and triggers), we have to delay it by a tick to ensure that\n    // it won't be closed immediately after it is opened.\n    .pipe(filter(active => active === this._menuItemInstance && !active.disabled), delay(0, asapScheduler)).subscribe(() => {\n      this._openedBy = 'mouse'; // If the same menu is used between multiple triggers, it might still be animating\n      // while the new trigger tries to re-open it. Wait for the animation to finish\n      // before doing so. Also interrupt if the user moves to another item.\n\n      if (this.menu instanceof _MatMenuBase && this.menu._isAnimating) {\n        // We need the `delay(0)` here in order to avoid\n        // 'changed after checked' errors in some cases. See #12194.\n        this.menu._animationDone.pipe(take(1), delay(0, asapScheduler), takeUntil(this._parentMaterialMenu._hovered())).subscribe(() => this.openMenu());\n      } else {\n        this.openMenu();\n      }\n    });\n  }\n  /** Gets the portal that should be attached to the overlay. */\n\n\n  _getPortal(menu) {\n    // Note that we can avoid this check by keeping the portal on the menu panel.\n    // While it would be cleaner, we'd have to introduce another required method on\n    // `MatMenuPanel`, making it harder to consume.\n    if (!this._portal || this._portal.templateRef !== menu.templateRef) {\n      this._portal = new TemplatePortal(menu.templateRef, this._viewContainerRef);\n    }\n\n    return this._portal;\n  }\n\n}\n\n_MatMenuTriggerBase.ɵfac = function _MatMenuTriggerBase_Factory(t) {\n  return new (t || _MatMenuTriggerBase)(i0.ɵɵdirectiveInject(i1$1.Overlay), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(MAT_MENU_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(MAT_MENU_PANEL, 8), i0.ɵɵdirectiveInject(MatMenuItem, 10), i0.ɵɵdirectiveInject(i3$1.Directionality, 8), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(i0.NgZone));\n};\n\n_MatMenuTriggerBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatMenuTriggerBase,\n  hostVars: 3,\n  hostBindings: function _MatMenuTriggerBase_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function _MatMenuTriggerBase_click_HostBindingHandler($event) {\n        return ctx._handleClick($event);\n      })(\"mousedown\", function _MatMenuTriggerBase_mousedown_HostBindingHandler($event) {\n        return ctx._handleMousedown($event);\n      })(\"keydown\", function _MatMenuTriggerBase_keydown_HostBindingHandler($event) {\n        return ctx._handleKeydown($event);\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-haspopup\", ctx.menu ? \"menu\" : null)(\"aria-expanded\", ctx.menuOpen || null)(\"aria-controls\", ctx.menuOpen ? ctx.menu.panelId : null);\n    }\n  },\n  inputs: {\n    _deprecatedMatMenuTriggerFor: [\"mat-menu-trigger-for\", \"_deprecatedMatMenuTriggerFor\"],\n    menu: [\"matMenuTriggerFor\", \"menu\"],\n    menuData: [\"matMenuTriggerData\", \"menuData\"],\n    restoreFocus: [\"matMenuTriggerRestoreFocus\", \"restoreFocus\"]\n  },\n  outputs: {\n    menuOpened: \"menuOpened\",\n    onMenuOpen: \"onMenuOpen\",\n    menuClosed: \"menuClosed\",\n    onMenuClose: \"onMenuClose\"\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatMenuTriggerBase, [{\n    type: Directive,\n    args: [{\n      host: {\n        '[attr.aria-haspopup]': 'menu ? \"menu\" : null',\n        '[attr.aria-expanded]': 'menuOpen || null',\n        '[attr.aria-controls]': 'menuOpen ? menu.panelId : null',\n        '(click)': '_handleClick($event)',\n        '(mousedown)': '_handleMousedown($event)',\n        '(keydown)': '_handleKeydown($event)'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i1$1.Overlay\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_MENU_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_MENU_PANEL]\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: MatMenuItem,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Self\n      }]\n    }, {\n      type: i3$1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i1.FocusMonitor\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    _deprecatedMatMenuTriggerFor: [{\n      type: Input,\n      args: ['mat-menu-trigger-for']\n    }],\n    menu: [{\n      type: Input,\n      args: ['matMenuTriggerFor']\n    }],\n    menuData: [{\n      type: Input,\n      args: ['matMenuTriggerData']\n    }],\n    restoreFocus: [{\n      type: Input,\n      args: ['matMenuTriggerRestoreFocus']\n    }],\n    menuOpened: [{\n      type: Output\n    }],\n    onMenuOpen: [{\n      type: Output\n    }],\n    menuClosed: [{\n      type: Output\n    }],\n    onMenuClose: [{\n      type: Output\n    }]\n  });\n})();\n/** Directive applied to an element that should trigger a `mat-menu`. */\n\n\nclass MatMenuTrigger extends _MatMenuTriggerBase {}\n\nMatMenuTrigger.ɵfac = /* @__PURE__ */function () {\n  let ɵMatMenuTrigger_BaseFactory;\n  return function MatMenuTrigger_Factory(t) {\n    return (ɵMatMenuTrigger_BaseFactory || (ɵMatMenuTrigger_BaseFactory = i0.ɵɵgetInheritedFactory(MatMenuTrigger)))(t || MatMenuTrigger);\n  };\n}();\n\nMatMenuTrigger.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatMenuTrigger,\n  selectors: [[\"\", \"mat-menu-trigger-for\", \"\"], [\"\", \"matMenuTriggerFor\", \"\"]],\n  hostAttrs: [1, \"mat-menu-trigger\"],\n  exportAs: [\"matMenuTrigger\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuTrigger, [{\n    type: Directive,\n    args: [{\n      selector: `[mat-menu-trigger-for], [matMenuTriggerFor]`,\n      host: {\n        'class': 'mat-menu-trigger'\n      },\n      exportAs: 'matMenuTrigger'\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatMenuModule {}\n\nMatMenuModule.ɵfac = function MatMenuModule_Factory(t) {\n  return new (t || MatMenuModule)();\n};\n\nMatMenuModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatMenuModule,\n  declarations: [MatMenu, MatMenuItem, MatMenuTrigger, MatMenuContent],\n  imports: [CommonModule, MatCommonModule, MatRippleModule, OverlayModule],\n  exports: [CdkScrollableModule, MatCommonModule, MatMenu, MatMenuItem, MatMenuTrigger, MatMenuContent]\n});\nMatMenuModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER],\n  imports: [CommonModule, MatCommonModule, MatRippleModule, OverlayModule, CdkScrollableModule, MatCommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, MatCommonModule, MatRippleModule, OverlayModule],\n      exports: [CdkScrollableModule, MatCommonModule, MatMenu, MatMenuItem, MatMenuTrigger, MatMenuContent],\n      declarations: [MatMenu, MatMenuItem, MatMenuTrigger, MatMenuContent],\n      providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MAT_MENU_CONTENT, MAT_MENU_DEFAULT_OPTIONS, MAT_MENU_PANEL, MAT_MENU_SCROLL_STRATEGY, MatMenu, MatMenuContent, MatMenuItem, MatMenuModule, MatMenuTrigger, _MatMenuBase, _MatMenuContentBase, _MatMenuTriggerBase, fadeInItems, matMenuAnimations, transformMenu };", "map": {"version": 3, "names": ["i1", "FocusKeyManager", "isFakeTouchstartFromScreenReader", "isFakeMousedownFromScreenReader", "coerceBooleanProperty", "UP_ARROW", "DOWN_ARROW", "RIGHT_ARROW", "LEFT_ARROW", "ESCAPE", "hasModifierKey", "ENTER", "SPACE", "i0", "InjectionToken", "Directive", "Inject", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Optional", "Input", "QueryList", "EventEmitter", "TemplateRef", "ContentChildren", "ViewChild", "ContentChild", "Output", "Self", "NgModule", "Subject", "Subscription", "merge", "of", "asapScheduler", "startWith", "switchMap", "take", "takeUntil", "filter", "delay", "trigger", "state", "style", "transition", "animate", "TemplatePortal", "DomPortalOutlet", "i2", "DOCUMENT", "CommonModule", "i3", "mixinDisableRipple", "mixinDisabled", "MatCommonModule", "MatRippleModule", "i3$1", "i1$1", "Overlay", "OverlayConfig", "OverlayModule", "normalizePassiveListenerOptions", "CdkScrollableModule", "matMenuAnimations", "transformMenu", "opacity", "transform", "fadeInItems", "MAT_MENU_CONTENT", "_MatMenuContentBase", "constructor", "_template", "_componentFactoryResolver", "_appRef", "_injector", "_viewContainerRef", "_document", "_changeDetectorRef", "_attached", "attach", "context", "_portal", "detach", "_outlet", "createElement", "element", "elementRef", "nativeElement", "parentNode", "insertBefore", "outletElement", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "next", "isAttached", "ngOnDestroy", "dispose", "ɵfac", "ComponentFactoryResolver", "ApplicationRef", "Injector", "ViewContainerRef", "ChangeDetectorRef", "ɵdir", "type", "undefined", "decorators", "args", "Mat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "provide", "useExisting", "selector", "providers", "throwMatMenuInvalidPositionX", "Error", "throwMatMenuInvalidPositionY", "throwMatMenuRecursiveError", "MAT_MENU_PANEL", "_MatMenuItemBase", "MatMenuItem", "_elementRef", "_focusMonitor", "_parentMenu", "role", "_hovered", "_focused", "_highlighted", "_triggersSubmenu", "addItem", "focus", "origin", "options", "focusVia", "_getHostElement", "ngAfterViewInit", "monitor", "stopMonitoring", "removeItem", "complete", "_getTabIndex", "disabled", "_checkDisabled", "event", "preventDefault", "stopPropagation", "_handleMouseEnter", "get<PERSON><PERSON><PERSON>", "clone", "cloneNode", "icons", "querySelectorAll", "i", "length", "remove", "textContent", "trim", "_setHighlighted", "isHighlighted", "_hasFocus", "activeElement", "ElementRef", "FocusMonitor", "ɵcmp", "NgIf", "<PERSON><PERSON><PERSON><PERSON>", "exportAs", "inputs", "host", "changeDetection", "OnPush", "encapsulation", "None", "template", "MAT_MENU_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_MENU_DEFAULT_OPTIONS_FACTORY", "overlapTrigger", "xPosition", "yPosition", "backdropClass", "menuPanelUid", "_MatMenuBase", "_ngZone", "_defaultOptions", "_xPosition", "_yPosition", "_directDescendantItems", "_tabSubscription", "EMPTY", "_classList", "_panelAnimationState", "_animationDone", "overlayPanelClass", "_overlapTrigger", "_hasBackdrop", "hasBackdrop", "closed", "close", "panelId", "value", "ngDevMode", "setPositionClasses", "panelClass", "classes", "previousPanelClass", "_previousPanelClass", "split", "for<PERSON>ach", "className", "classList", "ngOnInit", "ngAfterContentInit", "_updateDirectDescendants", "_keyManager", "withWrap", "withTypeAhead", "withHomeAndEnd", "tabOut", "subscribe", "emit", "changes", "pipe", "items", "map", "item", "focusedItem", "updateActiveItem", "itemsList", "manager", "activeItem", "toArray", "index", "Math", "max", "min", "activeItemIndex", "setActiveItem", "setNextItemActive", "destroy", "unsubscribe", "itemChanges", "_item", "_handleKeydown", "keyCode", "parentMenu", "direction", "setFocusOrigin", "onKeydown", "focusFirstItem", "onStable", "menuPanel", "first", "closest", "contains", "document", "setFirstItemActive", "resetActiveItem", "setElevation", "depth", "elevation", "_baseElevation", "newElevation", "_elevationPrefix", "customElevation", "Object", "keys", "find", "startsWith", "_previousElevation", "posX", "posY", "_startAnimation", "_resetAnimation", "_onAnimationDone", "_isAnimating", "_onAnimationStart", "toState", "scrollTop", "_allItems", "reset", "notifyOn<PERSON><PERSON>es", "NgZone", "descendants", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON><PERSON>", "templateRef", "lazyContent", "MatMenu", "ngZone", "defaultOptions", "changeDetectorRef", "Ng<PERSON><PERSON>", "animations", "styles", "MAT_MENU_SCROLL_STRATEGY", "MAT_MENU_SCROLL_STRATEGY_FACTORY", "overlay", "scrollStrategies", "reposition", "MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER", "deps", "useFactory", "MENU_PANEL_TOP_PADDING", "passiveEventListenerOptions", "passive", "_MatMenuTriggerBase", "_overlay", "_element", "scrollStrategy", "_menuItemInstance", "_dir", "_overlayRef", "_menuOpen", "_closingActionsSubscription", "_hoverSubscription", "_menuCloseSubscription", "_handleTouchStart", "_openedBy", "restoreFocus", "menuOpened", "onMenuOpen", "menuClosed", "onMenuClose", "_scrollStrategy", "_parentMaterialMenu", "addEventListener", "triggersSubmenu", "_deprecatedMatMenuTriggerFor", "menu", "v", "_menu", "reason", "_destroyMenu", "_handleHover", "removeEventListener", "menuOpen", "dir", "toggleMenu", "closeMenu", "openMenu", "overlayRef", "_createOverlay", "overlayConfig", "getConfig", "positionStrategy", "_setPosition", "_getPortal", "menuData", "_menuClosingActions", "_initMenu", "withLockedPosition", "reapplyLastPosition", "updatePosition", "_setIsMenuOpen", "_setMenuElevation", "isOpen", "config", "_getOverlayConfig", "_subscribeToPositions", "create", "keydownEvents", "position", "flexibleConnectedTo", "withGrowAfterOpen", "withTransformOriginOn", "position<PERSON><PERSON>es", "change", "connectionPair", "overlayX", "overlayY", "run", "originX", "originFallbackX", "overlayFallbackY", "originY", "originFallbackY", "overlayFallbackX", "offsetY", "_parentInnerPadding", "firstItem", "offsetTop", "withPositions", "backdrop", "backdropClick", "detachments", "parentClose", "hover", "active", "_handleMousedown", "button", "_handleClick", "Directionality", "MatMenuTrigger", "MatMenuModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/material/fesm2020/menu.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/a11y';\nimport { FocusKeyManager, isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { UP_ARROW, DOWN_ARROW, RIGHT_ARROW, LEFT_ARROW, ESCAPE, hasModifierKey, ENTER, SPACE } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, Input, QueryList, EventEmitter, TemplateRef, ContentChildren, ViewChild, ContentChild, Output, Self, NgModule } from '@angular/core';\nimport { Subject, Subscription, merge, of, asapScheduler } from 'rxjs';\nimport { startWith, switchMap, take, takeUntil, filter, delay } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { TemplatePortal, DomPortalOutlet } from '@angular/cdk/portal';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i3 from '@angular/material/core';\nimport { mixinDisableRipple, mixinDisabled, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport * as i3$1 from '@angular/cdk/bidi';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Animations used by the mat-menu component.\n * Animation duration and timing values are based on:\n * https://material.io/guidelines/components/menus.html#menus-usage\n * @docs-private\n */\nconst matMenuAnimations = {\n    /**\n     * This animation controls the menu panel's entry and exit from the page.\n     *\n     * When the menu panel is added to the DOM, it scales in and fades in its border.\n     *\n     * When the menu panel is removed from the DOM, it simply fades out after a brief\n     * delay to display the ripple.\n     */\n    transformMenu: trigger('transformMenu', [\n        state('void', style({\n            opacity: 0,\n            transform: 'scale(0.8)',\n        })),\n        transition('void => enter', animate('120ms cubic-bezier(0, 0, 0.2, 1)', style({\n            opacity: 1,\n            transform: 'scale(1)',\n        }))),\n        transition('* => void', animate('100ms 25ms linear', style({ opacity: 0 }))),\n    ]),\n    /**\n     * This animation fades in the background color and content of the menu panel\n     * after its containing element is scaled in.\n     */\n    fadeInItems: trigger('fadeInItems', [\n        // TODO(crisbeto): this is inside the `transformMenu`\n        // now. Remove next time we do breaking changes.\n        state('showing', style({ opacity: 1 })),\n        transition('void => *', [\n            style({ opacity: 0 }),\n            animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n        ]),\n    ]),\n};\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst fadeInItems = matMenuAnimations.fadeInItems;\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst transformMenu = matMenuAnimations.transformMenu;\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `MatMenuContent`. It serves\n * as alternative token to the actual `MatMenuContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_MENU_CONTENT = new InjectionToken('MatMenuContent');\nclass _MatMenuContentBase {\n    constructor(_template, _componentFactoryResolver, _appRef, _injector, _viewContainerRef, _document, _changeDetectorRef) {\n        this._template = _template;\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._appRef = _appRef;\n        this._injector = _injector;\n        this._viewContainerRef = _viewContainerRef;\n        this._document = _document;\n        this._changeDetectorRef = _changeDetectorRef;\n        /** Emits when the menu content has been attached. */\n        this._attached = new Subject();\n    }\n    /**\n     * Attaches the content with a particular context.\n     * @docs-private\n     */\n    attach(context = {}) {\n        if (!this._portal) {\n            this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n        }\n        this.detach();\n        if (!this._outlet) {\n            this._outlet = new DomPortalOutlet(this._document.createElement('div'), this._componentFactoryResolver, this._appRef, this._injector);\n        }\n        const element = this._template.elementRef.nativeElement;\n        // Because we support opening the same menu from different triggers (which in turn have their\n        // own `OverlayRef` panel), we have to re-insert the host element every time, otherwise we\n        // risk it staying attached to a pane that's no longer in the DOM.\n        element.parentNode.insertBefore(this._outlet.outletElement, element);\n        // When `MatMenuContent` is used in an `OnPush` component, the insertion of the menu\n        // content via `createEmbeddedView` does not cause the content to be seen as \"dirty\"\n        // by Angular. This causes the `@ContentChildren` for menu items within the menu to\n        // not be updated by Angular. By explicitly marking for check here, we tell Angular that\n        // it needs to check for new menu items and update the `@ContentChild` in `MatMenu`.\n        // @breaking-change 9.0.0 Make change detector ref required\n        this._changeDetectorRef?.markForCheck();\n        this._portal.attach(this._outlet, context);\n        this._attached.next();\n    }\n    /**\n     * Detaches the content.\n     * @docs-private\n     */\n    detach() {\n        if (this._portal.isAttached) {\n            this._portal.detach();\n        }\n    }\n    ngOnDestroy() {\n        if (this._outlet) {\n            this._outlet.dispose();\n        }\n    }\n}\n_MatMenuContentBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatMenuContentBase, deps: [{ token: i0.TemplateRef }, { token: i0.ComponentFactoryResolver }, { token: i0.ApplicationRef }, { token: i0.Injector }, { token: i0.ViewContainerRef }, { token: DOCUMENT }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\n_MatMenuContentBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: _MatMenuContentBase, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatMenuContentBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }, { type: i0.ComponentFactoryResolver }, { type: i0.ApplicationRef }, { type: i0.Injector }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.ChangeDetectorRef }]; } });\n/**\n * Menu content that will be rendered lazily once the menu is opened.\n */\nclass MatMenuContent extends _MatMenuContentBase {\n}\nMatMenuContent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatMenuContent, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatMenuContent.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatMenuContent, selector: \"ng-template[matMenuContent]\", providers: [{ provide: MAT_MENU_CONTENT, useExisting: MatMenuContent }], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatMenuContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[matMenuContent]',\n                    providers: [{ provide: MAT_MENU_CONTENT, useExisting: MatMenuContent }],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Throws an exception for the case when menu's x-position value isn't valid.\n * In other words, it doesn't match 'before' or 'after'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionX() {\n    throw Error(`xPosition value must be either 'before' or after'.\n      Example: <mat-menu xPosition=\"before\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when menu's y-position value isn't valid.\n * In other words, it doesn't match 'above' or 'below'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionY() {\n    throw Error(`yPosition value must be either 'above' or below'.\n      Example: <mat-menu yPosition=\"above\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when a menu is assigned\n * to a trigger that is placed inside the same menu.\n * @docs-private\n */\nfunction throwMatMenuRecursiveError() {\n    throw Error(`matMenuTriggerFor: menu cannot contain its own trigger. Assign a menu that is ` +\n        `not a parent of the trigger or move the trigger outside of the menu.`);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token used to provide the parent menu to menu-specific components.\n * @docs-private\n */\nconst MAT_MENU_PANEL = new InjectionToken('MAT_MENU_PANEL');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatMenuItem.\n/** @docs-private */\nconst _MatMenuItemBase = mixinDisableRipple(mixinDisabled(class {\n}));\n/**\n * Single item inside of a `mat-menu`. Provides the menu item styling and accessibility treatment.\n */\nclass MatMenuItem extends _MatMenuItemBase {\n    constructor(_elementRef, _document, _focusMonitor, _parentMenu, _changeDetectorRef) {\n        super();\n        this._elementRef = _elementRef;\n        this._document = _document;\n        this._focusMonitor = _focusMonitor;\n        this._parentMenu = _parentMenu;\n        this._changeDetectorRef = _changeDetectorRef;\n        /** ARIA role for the menu item. */\n        this.role = 'menuitem';\n        /** Stream that emits when the menu item is hovered. */\n        this._hovered = new Subject();\n        /** Stream that emits when the menu item is focused. */\n        this._focused = new Subject();\n        /** Whether the menu item is highlighted. */\n        this._highlighted = false;\n        /** Whether the menu item acts as a trigger for a sub-menu. */\n        this._triggersSubmenu = false;\n        _parentMenu?.addItem?.(this);\n    }\n    /** Focuses the menu item. */\n    focus(origin, options) {\n        if (this._focusMonitor && origin) {\n            this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n        }\n        else {\n            this._getHostElement().focus(options);\n        }\n        this._focused.next(this);\n    }\n    ngAfterViewInit() {\n        if (this._focusMonitor) {\n            // Start monitoring the element so it gets the appropriate focused classes. We want\n            // to show the focus style for menu items only when the focus was not caused by a\n            // mouse or touch interaction.\n            this._focusMonitor.monitor(this._elementRef, false);\n        }\n    }\n    ngOnDestroy() {\n        if (this._focusMonitor) {\n            this._focusMonitor.stopMonitoring(this._elementRef);\n        }\n        if (this._parentMenu && this._parentMenu.removeItem) {\n            this._parentMenu.removeItem(this);\n        }\n        this._hovered.complete();\n        this._focused.complete();\n    }\n    /** Used to set the `tabindex`. */\n    _getTabIndex() {\n        return this.disabled ? '-1' : '0';\n    }\n    /** Returns the host DOM element. */\n    _getHostElement() {\n        return this._elementRef.nativeElement;\n    }\n    /** Prevents the default element actions if it is disabled. */\n    _checkDisabled(event) {\n        if (this.disabled) {\n            event.preventDefault();\n            event.stopPropagation();\n        }\n    }\n    /** Emits to the hover stream. */\n    _handleMouseEnter() {\n        this._hovered.next(this);\n    }\n    /** Gets the label to be used when determining whether the option should be focused. */\n    getLabel() {\n        const clone = this._elementRef.nativeElement.cloneNode(true);\n        const icons = clone.querySelectorAll('mat-icon, .material-icons');\n        // Strip away icons so they don't show up in the text.\n        for (let i = 0; i < icons.length; i++) {\n            icons[i].remove();\n        }\n        return clone.textContent?.trim() || '';\n    }\n    _setHighlighted(isHighlighted) {\n        // We need to mark this for check for the case where the content is coming from a\n        // `matMenuContent` whose change detection tree is at the declaration position,\n        // not the insertion position. See #23175.\n        // @breaking-change 12.0.0 Remove null check for `_changeDetectorRef`.\n        this._highlighted = isHighlighted;\n        this._changeDetectorRef?.markForCheck();\n    }\n    _hasFocus() {\n        return this._document && this._document.activeElement === this._getHostElement();\n    }\n}\nMatMenuItem.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatMenuItem, deps: [{ token: i0.ElementRef }, { token: DOCUMENT }, { token: i1.FocusMonitor }, { token: MAT_MENU_PANEL, optional: true }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nMatMenuItem.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatMenuItem, selector: \"[mat-menu-item]\", inputs: { disabled: \"disabled\", disableRipple: \"disableRipple\", role: \"role\" }, host: { listeners: { \"click\": \"_checkDisabled($event)\", \"mouseenter\": \"_handleMouseEnter()\" }, properties: { \"attr.role\": \"role\", \"class.mat-menu-item\": \"true\", \"class.mat-menu-item-highlighted\": \"_highlighted\", \"class.mat-menu-item-submenu-trigger\": \"_triggersSubmenu\", \"attr.tabindex\": \"_getTabIndex()\", \"attr.aria-disabled\": \"disabled.toString()\", \"attr.disabled\": \"disabled || null\" }, classAttribute: \"mat-focus-indicator\" }, exportAs: [\"matMenuItem\"], usesInheritance: true, ngImport: i0, template: \"<ng-content></ng-content>\\n<div class=\\\"mat-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n\\n<svg\\n  *ngIf=\\\"_triggersSubmenu\\\"\\n  class=\\\"mat-menu-submenu-icon\\\"\\n  viewBox=\\\"0 0 5 10\\\"\\n  focusable=\\\"false\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n\", dependencies: [{ kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i3.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatMenuItem, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-menu-item]', exportAs: 'matMenuItem', inputs: ['disabled', 'disableRipple'], host: {\n                        '[attr.role]': 'role',\n                        '[class.mat-menu-item]': 'true',\n                        '[class.mat-menu-item-highlighted]': '_highlighted',\n                        '[class.mat-menu-item-submenu-trigger]': '_triggersSubmenu',\n                        '[attr.tabindex]': '_getTabIndex()',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '[attr.disabled]': 'disabled || null',\n                        'class': 'mat-focus-indicator',\n                        '(click)': '_checkDisabled($event)',\n                        '(mouseenter)': '_handleMouseEnter()',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: \"<ng-content></ng-content>\\n<div class=\\\"mat-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n\\n<svg\\n  *ngIf=\\\"_triggersSubmenu\\\"\\n  class=\\\"mat-menu-submenu-icon\\\"\\n  viewBox=\\\"0 0 5 10\\\"\\n  focusable=\\\"false\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n\" }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1.FocusMonitor }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_MENU_PANEL]\n                }, {\n                    type: Optional\n                }] }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { role: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token to be used to override the default options for `mat-menu`. */\nconst MAT_MENU_DEFAULT_OPTIONS = new InjectionToken('mat-menu-default-options', {\n    providedIn: 'root',\n    factory: MAT_MENU_DEFAULT_OPTIONS_FACTORY,\n});\n/** @docs-private */\nfunction MAT_MENU_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        overlapTrigger: false,\n        xPosition: 'after',\n        yPosition: 'below',\n        backdropClass: 'cdk-overlay-transparent-backdrop',\n    };\n}\nlet menuPanelUid = 0;\n/** Base class with all of the `MatMenu` functionality. */\nclass _MatMenuBase {\n    constructor(_elementRef, _ngZone, _defaultOptions, \n    // @breaking-change 15.0.0 `_changeDetectorRef` to become a required parameter.\n    _changeDetectorRef) {\n        this._elementRef = _elementRef;\n        this._ngZone = _ngZone;\n        this._defaultOptions = _defaultOptions;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._xPosition = this._defaultOptions.xPosition;\n        this._yPosition = this._defaultOptions.yPosition;\n        /** Only the direct descendant menu items. */\n        this._directDescendantItems = new QueryList();\n        /** Subscription to tab events on the menu panel */\n        this._tabSubscription = Subscription.EMPTY;\n        /** Config object to be passed into the menu's ngClass */\n        this._classList = {};\n        /** Current state of the panel animation. */\n        this._panelAnimationState = 'void';\n        /** Emits whenever an animation on the menu completes. */\n        this._animationDone = new Subject();\n        /** Class or list of classes to be added to the overlay panel. */\n        this.overlayPanelClass = this._defaultOptions.overlayPanelClass || '';\n        /** Class to be added to the backdrop element. */\n        this.backdropClass = this._defaultOptions.backdropClass;\n        this._overlapTrigger = this._defaultOptions.overlapTrigger;\n        this._hasBackdrop = this._defaultOptions.hasBackdrop;\n        /** Event emitted when the menu is closed. */\n        this.closed = new EventEmitter();\n        /**\n         * Event emitted when the menu is closed.\n         * @deprecated Switch to `closed` instead\n         * @breaking-change 8.0.0\n         */\n        this.close = this.closed;\n        this.panelId = `mat-menu-panel-${menuPanelUid++}`;\n    }\n    /** Position of the menu in the X axis. */\n    get xPosition() {\n        return this._xPosition;\n    }\n    set xPosition(value) {\n        if (value !== 'before' &&\n            value !== 'after' &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwMatMenuInvalidPositionX();\n        }\n        this._xPosition = value;\n        this.setPositionClasses();\n    }\n    /** Position of the menu in the Y axis. */\n    get yPosition() {\n        return this._yPosition;\n    }\n    set yPosition(value) {\n        if (value !== 'above' && value !== 'below' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwMatMenuInvalidPositionY();\n        }\n        this._yPosition = value;\n        this.setPositionClasses();\n    }\n    /** Whether the menu should overlap its trigger. */\n    get overlapTrigger() {\n        return this._overlapTrigger;\n    }\n    set overlapTrigger(value) {\n        this._overlapTrigger = coerceBooleanProperty(value);\n    }\n    /** Whether the menu has a backdrop. */\n    get hasBackdrop() {\n        return this._hasBackdrop;\n    }\n    set hasBackdrop(value) {\n        this._hasBackdrop = coerceBooleanProperty(value);\n    }\n    /**\n     * This method takes classes set on the host mat-menu element and applies them on the\n     * menu template that displays in the overlay container.  Otherwise, it's difficult\n     * to style the containing menu from outside the component.\n     * @param classes list of class names\n     */\n    set panelClass(classes) {\n        const previousPanelClass = this._previousPanelClass;\n        if (previousPanelClass && previousPanelClass.length) {\n            previousPanelClass.split(' ').forEach((className) => {\n                this._classList[className] = false;\n            });\n        }\n        this._previousPanelClass = classes;\n        if (classes && classes.length) {\n            classes.split(' ').forEach((className) => {\n                this._classList[className] = true;\n            });\n            this._elementRef.nativeElement.className = '';\n        }\n    }\n    /**\n     * This method takes classes set on the host mat-menu element and applies them on the\n     * menu template that displays in the overlay container.  Otherwise, it's difficult\n     * to style the containing menu from outside the component.\n     * @deprecated Use `panelClass` instead.\n     * @breaking-change 8.0.0\n     */\n    get classList() {\n        return this.panelClass;\n    }\n    set classList(classes) {\n        this.panelClass = classes;\n    }\n    ngOnInit() {\n        this.setPositionClasses();\n    }\n    ngAfterContentInit() {\n        this._updateDirectDescendants();\n        this._keyManager = new FocusKeyManager(this._directDescendantItems)\n            .withWrap()\n            .withTypeAhead()\n            .withHomeAndEnd();\n        this._tabSubscription = this._keyManager.tabOut.subscribe(() => this.closed.emit('tab'));\n        // If a user manually (programmatically) focuses a menu item, we need to reflect that focus\n        // change back to the key manager. Note that we don't need to unsubscribe here because _focused\n        // is internal and we know that it gets completed on destroy.\n        this._directDescendantItems.changes\n            .pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map((item) => item._focused))))\n            .subscribe(focusedItem => this._keyManager.updateActiveItem(focusedItem));\n        this._directDescendantItems.changes.subscribe((itemsList) => {\n            // Move focus to another item, if the active item is removed from the list.\n            // We need to debounce the callback, because multiple items might be removed\n            // in quick succession.\n            const manager = this._keyManager;\n            if (this._panelAnimationState === 'enter' && manager.activeItem?._hasFocus()) {\n                const items = itemsList.toArray();\n                const index = Math.max(0, Math.min(items.length - 1, manager.activeItemIndex || 0));\n                if (items[index] && !items[index].disabled) {\n                    manager.setActiveItem(index);\n                }\n                else {\n                    manager.setNextItemActive();\n                }\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._directDescendantItems.destroy();\n        this._tabSubscription.unsubscribe();\n        this.closed.complete();\n    }\n    /** Stream that emits whenever the hovered menu item changes. */\n    _hovered() {\n        // Coerce the `changes` property because Angular types it as `Observable<any>`\n        const itemChanges = this._directDescendantItems.changes;\n        return itemChanges.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map((item) => item._hovered))));\n    }\n    /*\n     * Registers a menu item with the menu.\n     * @docs-private\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 9.0.0\n     */\n    addItem(_item) { }\n    /**\n     * Removes an item from the menu.\n     * @docs-private\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 9.0.0\n     */\n    removeItem(_item) { }\n    /** Handle a keyboard event from the menu, delegating to the appropriate action. */\n    _handleKeydown(event) {\n        const keyCode = event.keyCode;\n        const manager = this._keyManager;\n        switch (keyCode) {\n            case ESCAPE:\n                if (!hasModifierKey(event)) {\n                    event.preventDefault();\n                    this.closed.emit('keydown');\n                }\n                break;\n            case LEFT_ARROW:\n                if (this.parentMenu && this.direction === 'ltr') {\n                    this.closed.emit('keydown');\n                }\n                break;\n            case RIGHT_ARROW:\n                if (this.parentMenu && this.direction === 'rtl') {\n                    this.closed.emit('keydown');\n                }\n                break;\n            default:\n                if (keyCode === UP_ARROW || keyCode === DOWN_ARROW) {\n                    manager.setFocusOrigin('keyboard');\n                }\n                manager.onKeydown(event);\n                return;\n        }\n        // Don't allow the event to propagate if we've already handled it, or it may\n        // end up reaching other overlays that were opened earlier (see #22694).\n        event.stopPropagation();\n    }\n    /**\n     * Focus the first item in the menu.\n     * @param origin Action from which the focus originated. Used to set the correct styling.\n     */\n    focusFirstItem(origin = 'program') {\n        // Wait for `onStable` to ensure iOS VoiceOver screen reader focuses the first item (#24735).\n        this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n            let menuPanel = null;\n            if (this._directDescendantItems.length) {\n                // Because the `mat-menuPanel` is at the DOM insertion point, not inside the overlay, we don't\n                // have a nice way of getting a hold of the menuPanel panel. We can't use a `ViewChild` either\n                // because the panel is inside an `ng-template`. We work around it by starting from one of\n                // the items and walking up the DOM.\n                menuPanel = this._directDescendantItems.first._getHostElement().closest('[role=\"menu\"]');\n            }\n            // If an item in the menuPanel is already focused, avoid overriding the focus.\n            if (!menuPanel || !menuPanel.contains(document.activeElement)) {\n                const manager = this._keyManager;\n                manager.setFocusOrigin(origin).setFirstItemActive();\n                // If there's no active item at this point, it means that all the items are disabled.\n                // Move focus to the menuPanel panel so keyboard events like Escape still work. Also this will\n                // give _some_ feedback to screen readers.\n                if (!manager.activeItem && menuPanel) {\n                    menuPanel.focus();\n                }\n            }\n        });\n    }\n    /**\n     * Resets the active item in the menu. This is used when the menu is opened, allowing\n     * the user to start from the first option when pressing the down arrow.\n     */\n    resetActiveItem() {\n        this._keyManager.setActiveItem(-1);\n    }\n    /**\n     * Sets the menu panel elevation.\n     * @param depth Number of parent menus that come before the menu.\n     */\n    setElevation(depth) {\n        // The elevation starts at the base and increases by one for each level.\n        // Capped at 24 because that's the maximum elevation defined in the Material design spec.\n        const elevation = Math.min(this._baseElevation + depth, 24);\n        const newElevation = `${this._elevationPrefix}${elevation}`;\n        const customElevation = Object.keys(this._classList).find(className => {\n            return className.startsWith(this._elevationPrefix);\n        });\n        if (!customElevation || customElevation === this._previousElevation) {\n            if (this._previousElevation) {\n                this._classList[this._previousElevation] = false;\n            }\n            this._classList[newElevation] = true;\n            this._previousElevation = newElevation;\n        }\n    }\n    /**\n     * Adds classes to the menu panel based on its position. Can be used by\n     * consumers to add specific styling based on the position.\n     * @param posX Position of the menu along the x axis.\n     * @param posY Position of the menu along the y axis.\n     * @docs-private\n     */\n    setPositionClasses(posX = this.xPosition, posY = this.yPosition) {\n        const classes = this._classList;\n        classes['mat-menu-before'] = posX === 'before';\n        classes['mat-menu-after'] = posX === 'after';\n        classes['mat-menu-above'] = posY === 'above';\n        classes['mat-menu-below'] = posY === 'below';\n        // @breaking-change 15.0.0 Remove null check for `_changeDetectorRef`.\n        this._changeDetectorRef?.markForCheck();\n    }\n    /** Starts the enter animation. */\n    _startAnimation() {\n        // @breaking-change 8.0.0 Combine with _resetAnimation.\n        this._panelAnimationState = 'enter';\n    }\n    /** Resets the panel animation to its initial state. */\n    _resetAnimation() {\n        // @breaking-change 8.0.0 Combine with _startAnimation.\n        this._panelAnimationState = 'void';\n    }\n    /** Callback that is invoked when the panel animation completes. */\n    _onAnimationDone(event) {\n        this._animationDone.next(event);\n        this._isAnimating = false;\n    }\n    _onAnimationStart(event) {\n        this._isAnimating = true;\n        // Scroll the content element to the top as soon as the animation starts. This is necessary,\n        // because we move focus to the first item while it's still being animated, which can throw\n        // the browser off when it determines the scroll position. Alternatively we can move focus\n        // when the animation is done, however moving focus asynchronously will interrupt screen\n        // readers which are in the process of reading out the menu already. We take the `element`\n        // from the `event` since we can't use a `ViewChild` to access the pane.\n        if (event.toState === 'enter' && this._keyManager.activeItemIndex === 0) {\n            event.element.scrollTop = 0;\n        }\n    }\n    /**\n     * Sets up a stream that will keep track of any newly-added menu items and will update the list\n     * of direct descendants. We collect the descendants this way, because `_allItems` can include\n     * items that are part of child menus, and using a custom way of registering items is unreliable\n     * when it comes to maintaining the item order.\n     */\n    _updateDirectDescendants() {\n        this._allItems.changes\n            .pipe(startWith(this._allItems))\n            .subscribe((items) => {\n            this._directDescendantItems.reset(items.filter(item => item._parentMenu === this));\n            this._directDescendantItems.notifyOnChanges();\n        });\n    }\n}\n_MatMenuBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatMenuBase, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: MAT_MENU_DEFAULT_OPTIONS }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\n_MatMenuBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: _MatMenuBase, inputs: { backdropClass: \"backdropClass\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"], xPosition: \"xPosition\", yPosition: \"yPosition\", overlapTrigger: \"overlapTrigger\", hasBackdrop: \"hasBackdrop\", panelClass: [\"class\", \"panelClass\"], classList: \"classList\" }, outputs: { closed: \"closed\", close: \"close\" }, queries: [{ propertyName: \"lazyContent\", first: true, predicate: MAT_MENU_CONTENT, descendants: true }, { propertyName: \"_allItems\", predicate: MatMenuItem, descendants: true }, { propertyName: \"items\", predicate: MatMenuItem }], viewQueries: [{ propertyName: \"templateRef\", first: true, predicate: TemplateRef, descendants: true }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatMenuBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_MENU_DEFAULT_OPTIONS]\n                }] }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { _allItems: [{\n                type: ContentChildren,\n                args: [MatMenuItem, { descendants: true }]\n            }], backdropClass: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], ariaDescribedby: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], xPosition: [{\n                type: Input\n            }], yPosition: [{\n                type: Input\n            }], templateRef: [{\n                type: ViewChild,\n                args: [TemplateRef]\n            }], items: [{\n                type: ContentChildren,\n                args: [MatMenuItem, { descendants: false }]\n            }], lazyContent: [{\n                type: ContentChild,\n                args: [MAT_MENU_CONTENT]\n            }], overlapTrigger: [{\n                type: Input\n            }], hasBackdrop: [{\n                type: Input\n            }], panelClass: [{\n                type: Input,\n                args: ['class']\n            }], classList: [{\n                type: Input\n            }], closed: [{\n                type: Output\n            }], close: [{\n                type: Output\n            }] } });\n/** @docs-public MatMenu */\nclass MatMenu extends _MatMenuBase {\n    constructor(elementRef, ngZone, defaultOptions, changeDetectorRef) {\n        super(elementRef, ngZone, defaultOptions, changeDetectorRef);\n        this._elevationPrefix = 'mat-elevation-z';\n        this._baseElevation = 4;\n    }\n}\nMatMenu.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatMenu, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: MAT_MENU_DEFAULT_OPTIONS }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nMatMenu.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatMenu, selector: \"mat-menu\", host: { properties: { \"attr.aria-label\": \"null\", \"attr.aria-labelledby\": \"null\", \"attr.aria-describedby\": \"null\" } }, providers: [{ provide: MAT_MENU_PANEL, useExisting: MatMenu }], exportAs: [\"matMenu\"], usesInheritance: true, ngImport: i0, template: \"<ng-template>\\n  <div\\n    class=\\\"mat-menu-panel\\\"\\n    [id]=\\\"panelId\\\"\\n    [ngClass]=\\\"_classList\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    [@transformMenu]=\\\"_panelAnimationState\\\"\\n    (@transformMenu.start)=\\\"_onAnimationStart($event)\\\"\\n    (@transformMenu.done)=\\\"_onAnimationDone($event)\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-menu-content\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\", styles: [\"mat-menu{display:none}.mat-menu-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;max-height:calc(100vh - 48px);border-radius:4px;outline:0;min-height:64px;position:relative}.mat-menu-panel.ng-animating{pointer-events:none}.cdk-high-contrast-active .mat-menu-panel{outline:solid 1px}.mat-menu-content:not(:empty){padding-top:8px;padding-bottom:8px}.mat-menu-item{-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;line-height:48px;height:48px;padding:0 16px;text-align:left;text-decoration:none;max-width:100%;position:relative}.mat-menu-item::-moz-focus-inner{border:0}.mat-menu-item[disabled]{cursor:default}[dir=rtl] .mat-menu-item{text-align:right}.mat-menu-item .mat-icon{margin-right:16px;vertical-align:middle}.mat-menu-item .mat-icon svg{vertical-align:top}[dir=rtl] .mat-menu-item .mat-icon{margin-left:16px;margin-right:0}.mat-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.cdk-high-contrast-active .mat-menu-item{margin-top:1px}.mat-menu-item-submenu-trigger{padding-right:32px}[dir=rtl] .mat-menu-item-submenu-trigger{padding-right:16px;padding-left:32px}.mat-menu-submenu-icon{position:absolute;top:50%;right:16px;transform:translateY(-50%);width:5px;height:10px;fill:currentColor}[dir=rtl] .mat-menu-submenu-icon{right:auto;left:16px;transform:translateY(-50%) scaleX(-1)}.cdk-high-contrast-active .mat-menu-submenu-icon{fill:CanvasText}button.mat-menu-item{width:100%}.mat-menu-item .mat-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"], dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], animations: [matMenuAnimations.transformMenu, matMenuAnimations.fadeInItems], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatMenu, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-menu', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, exportAs: 'matMenu', host: {\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-labelledby]': 'null',\n                        '[attr.aria-describedby]': 'null',\n                    }, animations: [matMenuAnimations.transformMenu, matMenuAnimations.fadeInItems], providers: [{ provide: MAT_MENU_PANEL, useExisting: MatMenu }], template: \"<ng-template>\\n  <div\\n    class=\\\"mat-menu-panel\\\"\\n    [id]=\\\"panelId\\\"\\n    [ngClass]=\\\"_classList\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    [@transformMenu]=\\\"_panelAnimationState\\\"\\n    (@transformMenu.start)=\\\"_onAnimationStart($event)\\\"\\n    (@transformMenu.done)=\\\"_onAnimationDone($event)\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-menu-content\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\", styles: [\"mat-menu{display:none}.mat-menu-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;max-height:calc(100vh - 48px);border-radius:4px;outline:0;min-height:64px;position:relative}.mat-menu-panel.ng-animating{pointer-events:none}.cdk-high-contrast-active .mat-menu-panel{outline:solid 1px}.mat-menu-content:not(:empty){padding-top:8px;padding-bottom:8px}.mat-menu-item{-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;line-height:48px;height:48px;padding:0 16px;text-align:left;text-decoration:none;max-width:100%;position:relative}.mat-menu-item::-moz-focus-inner{border:0}.mat-menu-item[disabled]{cursor:default}[dir=rtl] .mat-menu-item{text-align:right}.mat-menu-item .mat-icon{margin-right:16px;vertical-align:middle}.mat-menu-item .mat-icon svg{vertical-align:top}[dir=rtl] .mat-menu-item .mat-icon{margin-left:16px;margin-right:0}.mat-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.cdk-high-contrast-active .mat-menu-item{margin-top:1px}.mat-menu-item-submenu-trigger{padding-right:32px}[dir=rtl] .mat-menu-item-submenu-trigger{padding-right:16px;padding-left:32px}.mat-menu-submenu-icon{position:absolute;top:50%;right:16px;transform:translateY(-50%);width:5px;height:10px;fill:currentColor}[dir=rtl] .mat-menu-submenu-icon{right:auto;left:16px;transform:translateY(-50%) scaleX(-1)}.cdk-high-contrast-active .mat-menu-submenu-icon{fill:CanvasText}button.mat-menu-item{width:100%}.mat-menu-item .mat-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_MENU_DEFAULT_OPTIONS]\n                }] }, { type: i0.ChangeDetectorRef }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token that determines the scroll handling while the menu is open. */\nconst MAT_MENU_SCROLL_STRATEGY = new InjectionToken('mat-menu-scroll-strategy');\n/** @docs-private */\nfunction MAT_MENU_SCROLL_STRATEGY_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n    provide: MAT_MENU_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_MENU_SCROLL_STRATEGY_FACTORY,\n};\n/**\n * Default top padding of the menu panel.\n * @deprecated No longer being used. Will be removed.\n * @breaking-change 15.0.0\n */\nconst MENU_PANEL_TOP_PADDING = 8;\n/** Options for binding a passive event listener. */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({ passive: true });\n// TODO(andrewseguin): Remove the kebab versions in favor of camelCased attribute selectors\nclass _MatMenuTriggerBase {\n    constructor(_overlay, _element, _viewContainerRef, scrollStrategy, parentMenu, \n    // `MatMenuTrigger` is commonly used in combination with a `MatMenuItem`.\n    // tslint:disable-next-line: lightweight-tokens\n    _menuItemInstance, _dir, _focusMonitor, _ngZone) {\n        this._overlay = _overlay;\n        this._element = _element;\n        this._viewContainerRef = _viewContainerRef;\n        this._menuItemInstance = _menuItemInstance;\n        this._dir = _dir;\n        this._focusMonitor = _focusMonitor;\n        this._ngZone = _ngZone;\n        this._overlayRef = null;\n        this._menuOpen = false;\n        this._closingActionsSubscription = Subscription.EMPTY;\n        this._hoverSubscription = Subscription.EMPTY;\n        this._menuCloseSubscription = Subscription.EMPTY;\n        /**\n         * Handles touch start events on the trigger.\n         * Needs to be an arrow function so we can easily use addEventListener and removeEventListener.\n         */\n        this._handleTouchStart = (event) => {\n            if (!isFakeTouchstartFromScreenReader(event)) {\n                this._openedBy = 'touch';\n            }\n        };\n        // Tracking input type is necessary so it's possible to only auto-focus\n        // the first item of the list when the menu is opened via the keyboard\n        this._openedBy = undefined;\n        /**\n         * Whether focus should be restored when the menu is closed.\n         * Note that disabling this option can have accessibility implications\n         * and it's up to you to manage focus, if you decide to turn it off.\n         */\n        this.restoreFocus = true;\n        /** Event emitted when the associated menu is opened. */\n        this.menuOpened = new EventEmitter();\n        /**\n         * Event emitted when the associated menu is opened.\n         * @deprecated Switch to `menuOpened` instead\n         * @breaking-change 8.0.0\n         */\n        // tslint:disable-next-line:no-output-on-prefix\n        this.onMenuOpen = this.menuOpened;\n        /** Event emitted when the associated menu is closed. */\n        this.menuClosed = new EventEmitter();\n        /**\n         * Event emitted when the associated menu is closed.\n         * @deprecated Switch to `menuClosed` instead\n         * @breaking-change 8.0.0\n         */\n        // tslint:disable-next-line:no-output-on-prefix\n        this.onMenuClose = this.menuClosed;\n        this._scrollStrategy = scrollStrategy;\n        this._parentMaterialMenu = parentMenu instanceof _MatMenuBase ? parentMenu : undefined;\n        _element.nativeElement.addEventListener('touchstart', this._handleTouchStart, passiveEventListenerOptions);\n        if (_menuItemInstance) {\n            _menuItemInstance._triggersSubmenu = this.triggersSubmenu();\n        }\n    }\n    /**\n     * @deprecated\n     * @breaking-change 8.0.0\n     */\n    get _deprecatedMatMenuTriggerFor() {\n        return this.menu;\n    }\n    set _deprecatedMatMenuTriggerFor(v) {\n        this.menu = v;\n    }\n    /** References the menu instance that the trigger is associated with. */\n    get menu() {\n        return this._menu;\n    }\n    set menu(menu) {\n        if (menu === this._menu) {\n            return;\n        }\n        this._menu = menu;\n        this._menuCloseSubscription.unsubscribe();\n        if (menu) {\n            if (menu === this._parentMaterialMenu && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throwMatMenuRecursiveError();\n            }\n            this._menuCloseSubscription = menu.close.subscribe((reason) => {\n                this._destroyMenu(reason);\n                // If a click closed the menu, we should close the entire chain of nested menus.\n                if ((reason === 'click' || reason === 'tab') && this._parentMaterialMenu) {\n                    this._parentMaterialMenu.closed.emit(reason);\n                }\n            });\n        }\n    }\n    ngAfterContentInit() {\n        this._handleHover();\n    }\n    ngOnDestroy() {\n        if (this._overlayRef) {\n            this._overlayRef.dispose();\n            this._overlayRef = null;\n        }\n        this._element.nativeElement.removeEventListener('touchstart', this._handleTouchStart, passiveEventListenerOptions);\n        this._menuCloseSubscription.unsubscribe();\n        this._closingActionsSubscription.unsubscribe();\n        this._hoverSubscription.unsubscribe();\n    }\n    /** Whether the menu is open. */\n    get menuOpen() {\n        return this._menuOpen;\n    }\n    /** The text direction of the containing app. */\n    get dir() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Whether the menu triggers a sub-menu or a top-level one. */\n    triggersSubmenu() {\n        return !!(this._menuItemInstance && this._parentMaterialMenu);\n    }\n    /** Toggles the menu between the open and closed states. */\n    toggleMenu() {\n        return this._menuOpen ? this.closeMenu() : this.openMenu();\n    }\n    /** Opens the menu. */\n    openMenu() {\n        const menu = this.menu;\n        if (this._menuOpen || !menu) {\n            return;\n        }\n        const overlayRef = this._createOverlay(menu);\n        const overlayConfig = overlayRef.getConfig();\n        const positionStrategy = overlayConfig.positionStrategy;\n        this._setPosition(menu, positionStrategy);\n        overlayConfig.hasBackdrop =\n            menu.hasBackdrop == null ? !this.triggersSubmenu() : menu.hasBackdrop;\n        overlayRef.attach(this._getPortal(menu));\n        if (menu.lazyContent) {\n            menu.lazyContent.attach(this.menuData);\n        }\n        this._closingActionsSubscription = this._menuClosingActions().subscribe(() => this.closeMenu());\n        this._initMenu(menu);\n        if (menu instanceof _MatMenuBase) {\n            menu._startAnimation();\n            menu._directDescendantItems.changes.pipe(takeUntil(menu.close)).subscribe(() => {\n                // Re-adjust the position without locking when the amount of items\n                // changes so that the overlay is allowed to pick a new optimal position.\n                positionStrategy.withLockedPosition(false).reapplyLastPosition();\n                positionStrategy.withLockedPosition(true);\n            });\n        }\n    }\n    /** Closes the menu. */\n    closeMenu() {\n        this.menu?.close.emit();\n    }\n    /**\n     * Focuses the menu trigger.\n     * @param origin Source of the menu trigger's focus.\n     */\n    focus(origin, options) {\n        if (this._focusMonitor && origin) {\n            this._focusMonitor.focusVia(this._element, origin, options);\n        }\n        else {\n            this._element.nativeElement.focus(options);\n        }\n    }\n    /**\n     * Updates the position of the menu to ensure that it fits all options within the viewport.\n     */\n    updatePosition() {\n        this._overlayRef?.updatePosition();\n    }\n    /** Closes the menu and does the necessary cleanup. */\n    _destroyMenu(reason) {\n        if (!this._overlayRef || !this.menuOpen) {\n            return;\n        }\n        const menu = this.menu;\n        this._closingActionsSubscription.unsubscribe();\n        this._overlayRef.detach();\n        // Always restore focus if the user is navigating using the keyboard or the menu was opened\n        // programmatically. We don't restore for non-root triggers, because it can prevent focus\n        // from making it back to the root trigger when closing a long chain of menus by clicking\n        // on the backdrop.\n        if (this.restoreFocus && (reason === 'keydown' || !this._openedBy || !this.triggersSubmenu())) {\n            this.focus(this._openedBy);\n        }\n        this._openedBy = undefined;\n        if (menu instanceof _MatMenuBase) {\n            menu._resetAnimation();\n            if (menu.lazyContent) {\n                // Wait for the exit animation to finish before detaching the content.\n                menu._animationDone\n                    .pipe(filter(event => event.toState === 'void'), take(1), \n                // Interrupt if the content got re-attached.\n                takeUntil(menu.lazyContent._attached))\n                    .subscribe({\n                    next: () => menu.lazyContent.detach(),\n                    // No matter whether the content got re-attached, reset the menu.\n                    complete: () => this._setIsMenuOpen(false),\n                });\n            }\n            else {\n                this._setIsMenuOpen(false);\n            }\n        }\n        else {\n            this._setIsMenuOpen(false);\n            menu?.lazyContent?.detach();\n        }\n    }\n    /**\n     * This method sets the menu state to open and focuses the first item if\n     * the menu was opened via the keyboard.\n     */\n    _initMenu(menu) {\n        menu.parentMenu = this.triggersSubmenu() ? this._parentMaterialMenu : undefined;\n        menu.direction = this.dir;\n        this._setMenuElevation(menu);\n        menu.focusFirstItem(this._openedBy || 'program');\n        this._setIsMenuOpen(true);\n    }\n    /** Updates the menu elevation based on the amount of parent menus that it has. */\n    _setMenuElevation(menu) {\n        if (menu.setElevation) {\n            let depth = 0;\n            let parentMenu = menu.parentMenu;\n            while (parentMenu) {\n                depth++;\n                parentMenu = parentMenu.parentMenu;\n            }\n            menu.setElevation(depth);\n        }\n    }\n    // set state rather than toggle to support triggers sharing a menu\n    _setIsMenuOpen(isOpen) {\n        this._menuOpen = isOpen;\n        this._menuOpen ? this.menuOpened.emit() : this.menuClosed.emit();\n        if (this.triggersSubmenu()) {\n            this._menuItemInstance._setHighlighted(isOpen);\n        }\n    }\n    /**\n     * This method creates the overlay from the provided menu's template and saves its\n     * OverlayRef so that it can be attached to the DOM when openMenu is called.\n     */\n    _createOverlay(menu) {\n        if (!this._overlayRef) {\n            const config = this._getOverlayConfig(menu);\n            this._subscribeToPositions(menu, config.positionStrategy);\n            this._overlayRef = this._overlay.create(config);\n            // Consume the `keydownEvents` in order to prevent them from going to another overlay.\n            // Ideally we'd also have our keyboard event logic in here, however doing so will\n            // break anybody that may have implemented the `MatMenuPanel` themselves.\n            this._overlayRef.keydownEvents().subscribe();\n        }\n        return this._overlayRef;\n    }\n    /**\n     * This method builds the configuration object needed to create the overlay, the OverlayState.\n     * @returns OverlayConfig\n     */\n    _getOverlayConfig(menu) {\n        return new OverlayConfig({\n            positionStrategy: this._overlay\n                .position()\n                .flexibleConnectedTo(this._element)\n                .withLockedPosition()\n                .withGrowAfterOpen()\n                .withTransformOriginOn('.mat-menu-panel, .mat-mdc-menu-panel'),\n            backdropClass: menu.backdropClass || 'cdk-overlay-transparent-backdrop',\n            panelClass: menu.overlayPanelClass,\n            scrollStrategy: this._scrollStrategy(),\n            direction: this._dir,\n        });\n    }\n    /**\n     * Listens to changes in the position of the overlay and sets the correct classes\n     * on the menu based on the new position. This ensures the animation origin is always\n     * correct, even if a fallback position is used for the overlay.\n     */\n    _subscribeToPositions(menu, position) {\n        if (menu.setPositionClasses) {\n            position.positionChanges.subscribe(change => {\n                const posX = change.connectionPair.overlayX === 'start' ? 'after' : 'before';\n                const posY = change.connectionPair.overlayY === 'top' ? 'below' : 'above';\n                // @breaking-change 15.0.0 Remove null check for `ngZone`.\n                // `positionChanges` fires outside of the `ngZone` and `setPositionClasses` might be\n                // updating something in the view so we need to bring it back in.\n                if (this._ngZone) {\n                    this._ngZone.run(() => menu.setPositionClasses(posX, posY));\n                }\n                else {\n                    menu.setPositionClasses(posX, posY);\n                }\n            });\n        }\n    }\n    /**\n     * Sets the appropriate positions on a position strategy\n     * so the overlay connects with the trigger correctly.\n     * @param positionStrategy Strategy whose position to update.\n     */\n    _setPosition(menu, positionStrategy) {\n        let [originX, originFallbackX] = menu.xPosition === 'before' ? ['end', 'start'] : ['start', 'end'];\n        let [overlayY, overlayFallbackY] = menu.yPosition === 'above' ? ['bottom', 'top'] : ['top', 'bottom'];\n        let [originY, originFallbackY] = [overlayY, overlayFallbackY];\n        let [overlayX, overlayFallbackX] = [originX, originFallbackX];\n        let offsetY = 0;\n        if (this.triggersSubmenu()) {\n            // When the menu is a sub-menu, it should always align itself\n            // to the edges of the trigger, instead of overlapping it.\n            overlayFallbackX = originX = menu.xPosition === 'before' ? 'start' : 'end';\n            originFallbackX = overlayX = originX === 'end' ? 'start' : 'end';\n            if (this._parentMaterialMenu) {\n                if (this._parentInnerPadding == null) {\n                    const firstItem = this._parentMaterialMenu.items.first;\n                    this._parentInnerPadding = firstItem ? firstItem._getHostElement().offsetTop : 0;\n                }\n                offsetY = overlayY === 'bottom' ? this._parentInnerPadding : -this._parentInnerPadding;\n            }\n        }\n        else if (!menu.overlapTrigger) {\n            originY = overlayY === 'top' ? 'bottom' : 'top';\n            originFallbackY = overlayFallbackY === 'top' ? 'bottom' : 'top';\n        }\n        positionStrategy.withPositions([\n            { originX, originY, overlayX, overlayY, offsetY },\n            { originX: originFallbackX, originY, overlayX: overlayFallbackX, overlayY, offsetY },\n            {\n                originX,\n                originY: originFallbackY,\n                overlayX,\n                overlayY: overlayFallbackY,\n                offsetY: -offsetY,\n            },\n            {\n                originX: originFallbackX,\n                originY: originFallbackY,\n                overlayX: overlayFallbackX,\n                overlayY: overlayFallbackY,\n                offsetY: -offsetY,\n            },\n        ]);\n    }\n    /** Returns a stream that emits whenever an action that should close the menu occurs. */\n    _menuClosingActions() {\n        const backdrop = this._overlayRef.backdropClick();\n        const detachments = this._overlayRef.detachments();\n        const parentClose = this._parentMaterialMenu ? this._parentMaterialMenu.closed : of();\n        const hover = this._parentMaterialMenu\n            ? this._parentMaterialMenu._hovered().pipe(filter(active => active !== this._menuItemInstance), filter(() => this._menuOpen))\n            : of();\n        return merge(backdrop, parentClose, hover, detachments);\n    }\n    /** Handles mouse presses on the trigger. */\n    _handleMousedown(event) {\n        if (!isFakeMousedownFromScreenReader(event)) {\n            // Since right or middle button clicks won't trigger the `click` event,\n            // we shouldn't consider the menu as opened by mouse in those cases.\n            this._openedBy = event.button === 0 ? 'mouse' : undefined;\n            // Since clicking on the trigger won't close the menu if it opens a sub-menu,\n            // we should prevent focus from moving onto it via click to avoid the\n            // highlight from lingering on the menu item.\n            if (this.triggersSubmenu()) {\n                event.preventDefault();\n            }\n        }\n    }\n    /** Handles key presses on the trigger. */\n    _handleKeydown(event) {\n        const keyCode = event.keyCode;\n        // Pressing enter on the trigger will trigger the click handler later.\n        if (keyCode === ENTER || keyCode === SPACE) {\n            this._openedBy = 'keyboard';\n        }\n        if (this.triggersSubmenu() &&\n            ((keyCode === RIGHT_ARROW && this.dir === 'ltr') ||\n                (keyCode === LEFT_ARROW && this.dir === 'rtl'))) {\n            this._openedBy = 'keyboard';\n            this.openMenu();\n        }\n    }\n    /** Handles click events on the trigger. */\n    _handleClick(event) {\n        if (this.triggersSubmenu()) {\n            // Stop event propagation to avoid closing the parent menu.\n            event.stopPropagation();\n            this.openMenu();\n        }\n        else {\n            this.toggleMenu();\n        }\n    }\n    /** Handles the cases where the user hovers over the trigger. */\n    _handleHover() {\n        // Subscribe to changes in the hovered item in order to toggle the panel.\n        if (!this.triggersSubmenu() || !this._parentMaterialMenu) {\n            return;\n        }\n        this._hoverSubscription = this._parentMaterialMenu\n            ._hovered()\n            // Since we might have multiple competing triggers for the same menu (e.g. a sub-menu\n            // with different data and triggers), we have to delay it by a tick to ensure that\n            // it won't be closed immediately after it is opened.\n            .pipe(filter(active => active === this._menuItemInstance && !active.disabled), delay(0, asapScheduler))\n            .subscribe(() => {\n            this._openedBy = 'mouse';\n            // If the same menu is used between multiple triggers, it might still be animating\n            // while the new trigger tries to re-open it. Wait for the animation to finish\n            // before doing so. Also interrupt if the user moves to another item.\n            if (this.menu instanceof _MatMenuBase && this.menu._isAnimating) {\n                // We need the `delay(0)` here in order to avoid\n                // 'changed after checked' errors in some cases. See #12194.\n                this.menu._animationDone\n                    .pipe(take(1), delay(0, asapScheduler), takeUntil(this._parentMaterialMenu._hovered()))\n                    .subscribe(() => this.openMenu());\n            }\n            else {\n                this.openMenu();\n            }\n        });\n    }\n    /** Gets the portal that should be attached to the overlay. */\n    _getPortal(menu) {\n        // Note that we can avoid this check by keeping the portal on the menu panel.\n        // While it would be cleaner, we'd have to introduce another required method on\n        // `MatMenuPanel`, making it harder to consume.\n        if (!this._portal || this._portal.templateRef !== menu.templateRef) {\n            this._portal = new TemplatePortal(menu.templateRef, this._viewContainerRef);\n        }\n        return this._portal;\n    }\n}\n_MatMenuTriggerBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatMenuTriggerBase, deps: [{ token: i1$1.Overlay }, { token: i0.ElementRef }, { token: i0.ViewContainerRef }, { token: MAT_MENU_SCROLL_STRATEGY }, { token: MAT_MENU_PANEL, optional: true }, { token: MatMenuItem, optional: true, self: true }, { token: i3$1.Directionality, optional: true }, { token: i1.FocusMonitor }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive });\n_MatMenuTriggerBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: _MatMenuTriggerBase, inputs: { _deprecatedMatMenuTriggerFor: [\"mat-menu-trigger-for\", \"_deprecatedMatMenuTriggerFor\"], menu: [\"matMenuTriggerFor\", \"menu\"], menuData: [\"matMenuTriggerData\", \"menuData\"], restoreFocus: [\"matMenuTriggerRestoreFocus\", \"restoreFocus\"] }, outputs: { menuOpened: \"menuOpened\", onMenuOpen: \"onMenuOpen\", menuClosed: \"menuClosed\", onMenuClose: \"onMenuClose\" }, host: { listeners: { \"click\": \"_handleClick($event)\", \"mousedown\": \"_handleMousedown($event)\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"attr.aria-haspopup\": \"menu ? \\\"menu\\\" : null\", \"attr.aria-expanded\": \"menuOpen || null\", \"attr.aria-controls\": \"menuOpen ? menu.panelId : null\" } }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatMenuTriggerBase, decorators: [{\n            type: Directive,\n            args: [{\n                    host: {\n                        '[attr.aria-haspopup]': 'menu ? \"menu\" : null',\n                        '[attr.aria-expanded]': 'menuOpen || null',\n                        '[attr.aria-controls]': 'menuOpen ? menu.panelId : null',\n                        '(click)': '_handleClick($event)',\n                        '(mousedown)': '_handleMousedown($event)',\n                        '(keydown)': '_handleKeydown($event)',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i1$1.Overlay }, { type: i0.ElementRef }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_MENU_SCROLL_STRATEGY]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_MENU_PANEL]\n                }, {\n                    type: Optional\n                }] }, { type: MatMenuItem, decorators: [{\n                    type: Optional\n                }, {\n                    type: Self\n                }] }, { type: i3$1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i1.FocusMonitor }, { type: i0.NgZone }]; }, propDecorators: { _deprecatedMatMenuTriggerFor: [{\n                type: Input,\n                args: ['mat-menu-trigger-for']\n            }], menu: [{\n                type: Input,\n                args: ['matMenuTriggerFor']\n            }], menuData: [{\n                type: Input,\n                args: ['matMenuTriggerData']\n            }], restoreFocus: [{\n                type: Input,\n                args: ['matMenuTriggerRestoreFocus']\n            }], menuOpened: [{\n                type: Output\n            }], onMenuOpen: [{\n                type: Output\n            }], menuClosed: [{\n                type: Output\n            }], onMenuClose: [{\n                type: Output\n            }] } });\n/** Directive applied to an element that should trigger a `mat-menu`. */\nclass MatMenuTrigger extends _MatMenuTriggerBase {\n}\nMatMenuTrigger.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatMenuTrigger, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatMenuTrigger.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatMenuTrigger, selector: \"[mat-menu-trigger-for], [matMenuTriggerFor]\", host: { classAttribute: \"mat-menu-trigger\" }, exportAs: [\"matMenuTrigger\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatMenuTrigger, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[mat-menu-trigger-for], [matMenuTriggerFor]`,\n                    host: {\n                        'class': 'mat-menu-trigger',\n                    },\n                    exportAs: 'matMenuTrigger',\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatMenuModule {\n}\nMatMenuModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatMenuModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatMenuModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: MatMenuModule, declarations: [MatMenu, MatMenuItem, MatMenuTrigger, MatMenuContent], imports: [CommonModule, MatCommonModule, MatRippleModule, OverlayModule], exports: [CdkScrollableModule,\n        MatCommonModule,\n        MatMenu,\n        MatMenuItem,\n        MatMenuTrigger,\n        MatMenuContent] });\nMatMenuModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatMenuModule, providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER], imports: [CommonModule, MatCommonModule, MatRippleModule, OverlayModule, CdkScrollableModule,\n        MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatMenuModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, MatCommonModule, MatRippleModule, OverlayModule],\n                    exports: [\n                        CdkScrollableModule,\n                        MatCommonModule,\n                        MatMenu,\n                        MatMenuItem,\n                        MatMenuTrigger,\n                        MatMenuContent,\n                    ],\n                    declarations: [MatMenu, MatMenuItem, MatMenuTrigger, MatMenuContent],\n                    providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_MENU_CONTENT, MAT_MENU_DEFAULT_OPTIONS, MAT_MENU_PANEL, MAT_MENU_SCROLL_STRATEGY, MatMenu, MatMenuContent, MatMenuItem, MatMenuModule, MatMenuTrigger, _MatMenuBase, _MatMenuContentBase, _MatMenuTriggerBase, fadeInItems, matMenuAnimations, transformMenu };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,mBAApB;AACA,SAASC,eAAT,EAA0BC,gCAA1B,EAA4DC,+BAA5D,QAAmG,mBAAnG;AACA,SAASC,qBAAT,QAAsC,uBAAtC;AACA,SAASC,QAAT,EAAmBC,UAAnB,EAA+BC,WAA/B,EAA4CC,UAA5C,EAAwDC,MAAxD,EAAgEC,cAAhE,EAAgFC,KAAhF,EAAuFC,KAAvF,QAAoG,uBAApG;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,SAAzB,EAAoCC,MAApC,EAA4CC,SAA5C,EAAuDC,uBAAvD,EAAgFC,iBAAhF,EAAmGC,QAAnG,EAA6GC,KAA7G,EAAoHC,SAApH,EAA+HC,YAA/H,EAA6IC,WAA7I,EAA0JC,eAA1J,EAA2KC,SAA3K,EAAsLC,YAAtL,EAAoMC,MAApM,EAA4MC,IAA5M,EAAkNC,QAAlN,QAAkO,eAAlO;AACA,SAASC,OAAT,EAAkBC,YAAlB,EAAgCC,KAAhC,EAAuCC,EAAvC,EAA2CC,aAA3C,QAAgE,MAAhE;AACA,SAASC,SAAT,EAAoBC,SAApB,EAA+BC,IAA/B,EAAqCC,SAArC,EAAgDC,MAAhD,EAAwDC,KAAxD,QAAqE,gBAArE;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,KAAzB,EAAgCC,UAAhC,EAA4CC,OAA5C,QAA2D,qBAA3D;AACA,SAASC,cAAT,EAAyBC,eAAzB,QAAgD,qBAAhD;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,QAAT,EAAmBC,YAAnB,QAAuC,iBAAvC;AACA,OAAO,KAAKC,EAAZ,MAAoB,wBAApB;AACA,SAASC,kBAAT,EAA6BC,aAA7B,EAA4CC,eAA5C,EAA6DC,eAA7D,QAAoF,wBAApF;AACA,OAAO,KAAKC,IAAZ,MAAsB,mBAAtB;AACA,OAAO,KAAKC,IAAZ,MAAsB,sBAAtB;AACA,SAASC,OAAT,EAAkBC,aAAlB,EAAiCC,aAAjC,QAAsD,sBAAtD;AACA,SAASC,+BAAT,QAAgD,uBAAhD;AACA,SAASC,mBAAT,QAAoC,wBAApC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;IAmHsGlD,EA+KiyB,iB;IA/KjyBA,EA+KiyB,4B;IA/KjyBA,EA+Ks5B,2B;IA/Kt5BA,EA+Kw7B,e;;;;;;;;gBA/Kx7BA,E;;IAAAA,EAilB+R,4B;IAjlB/RA,EAilB2X;MAjlB3XA,EAilB2X;MAAA,eAjlB3XA,EAilB2X;MAAA,OAjlB3XA,EAilBuY,2CAAZ;IAAA;MAjlB3XA,EAilB2X;MAAA,eAjlB3XA,EAilB2X;MAAA,OAjlB3XA,EAilB+a,gCAAY,OAAZ,EAApD;IAAA;MAjlB3XA,EAilB2X;MAAA,eAjlB3XA,EAilB2X;MAAA,OAjlB3XA,EAilBmhB,8CAAxJ;IAAA;MAjlB3XA,EAilB2X;MAAA,eAjlB3XA,EAilB2X;MAAA,OAjlB3XA,EAilB4kB,6CAAjN;IAAA,E;IAjlB3XA,EAilBkzB,4B;IAjlBlzBA,EAilB01B,gB;IAjlB11BA,EAilBy3B,iB;;;;mBAjlBz3BA,E;IAAAA,EAilBuU,8G;IAjlBvUA,EAilBopB,0J;;;;AAnsB1vB,MAAMmD,iBAAiB,GAAG;EACtB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,aAAa,EAAEvB,OAAO,CAAC,eAAD,EAAkB,CACpCC,KAAK,CAAC,MAAD,EAASC,KAAK,CAAC;IAChBsB,OAAO,EAAE,CADO;IAEhBC,SAAS,EAAE;EAFK,CAAD,CAAd,CAD+B,EAKpCtB,UAAU,CAAC,eAAD,EAAkBC,OAAO,CAAC,kCAAD,EAAqCF,KAAK,CAAC;IAC1EsB,OAAO,EAAE,CADiE;IAE1EC,SAAS,EAAE;EAF+D,CAAD,CAA1C,CAAzB,CAL0B,EASpCtB,UAAU,CAAC,WAAD,EAAcC,OAAO,CAAC,mBAAD,EAAsBF,KAAK,CAAC;IAAEsB,OAAO,EAAE;EAAX,CAAD,CAA3B,CAArB,CAT0B,CAAlB,CATA;;EAoBtB;AACJ;AACA;AACA;EACIE,WAAW,EAAE1B,OAAO,CAAC,aAAD,EAAgB,CAChC;EACA;EACAC,KAAK,CAAC,SAAD,EAAYC,KAAK,CAAC;IAAEsB,OAAO,EAAE;EAAX,CAAD,CAAjB,CAH2B,EAIhCrB,UAAU,CAAC,WAAD,EAAc,CACpBD,KAAK,CAAC;IAAEsB,OAAO,EAAE;EAAX,CAAD,CADe,EAEpBpB,OAAO,CAAC,8CAAD,CAFa,CAAd,CAJsB,CAAhB;AAxBE,CAA1B;AAkCA;AACA;AACA;AACA;AACA;;AACA,MAAMsB,WAAW,GAAGJ,iBAAiB,CAACI,WAAtC;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMH,aAAa,GAAGD,iBAAiB,CAACC,aAAxC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMI,gBAAgB,GAAG,IAAIvD,cAAJ,CAAmB,gBAAnB,CAAzB;;AACA,MAAMwD,mBAAN,CAA0B;EACtBC,WAAW,CAACC,SAAD,EAAYC,yBAAZ,EAAuCC,OAAvC,EAAgDC,SAAhD,EAA2DC,iBAA3D,EAA8EC,SAA9E,EAAyFC,kBAAzF,EAA6G;IACpH,KAAKN,SAAL,GAAiBA,SAAjB;IACA,KAAKC,yBAAL,GAAiCA,yBAAjC;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKC,SAAL,GAAiBA,SAAjB;IACA,KAAKC,iBAAL,GAAyBA,iBAAzB;IACA,KAAKC,SAAL,GAAiBA,SAAjB;IACA,KAAKC,kBAAL,GAA0BA,kBAA1B;IACA;;IACA,KAAKC,SAAL,GAAiB,IAAIhD,OAAJ,EAAjB;EACH;EACD;AACJ;AACA;AACA;;;EACIiD,MAAM,CAACC,OAAO,GAAG,EAAX,EAAe;IACjB,IAAI,CAAC,KAAKC,OAAV,EAAmB;MACf,KAAKA,OAAL,GAAe,IAAInC,cAAJ,CAAmB,KAAKyB,SAAxB,EAAmC,KAAKI,iBAAxC,CAAf;IACH;;IACD,KAAKO,MAAL;;IACA,IAAI,CAAC,KAAKC,OAAV,EAAmB;MACf,KAAKA,OAAL,GAAe,IAAIpC,eAAJ,CAAoB,KAAK6B,SAAL,CAAeQ,aAAf,CAA6B,KAA7B,CAApB,EAAyD,KAAKZ,yBAA9D,EAAyF,KAAKC,OAA9F,EAAuG,KAAKC,SAA5G,CAAf;IACH;;IACD,MAAMW,OAAO,GAAG,KAAKd,SAAL,CAAee,UAAf,CAA0BC,aAA1C,CARiB,CASjB;IACA;IACA;;IACAF,OAAO,CAACG,UAAR,CAAmBC,YAAnB,CAAgC,KAAKN,OAAL,CAAaO,aAA7C,EAA4DL,OAA5D,EAZiB,CAajB;IACA;IACA;IACA;IACA;IACA;;IACA,KAAKR,kBAAL,EAAyBc,YAAzB;;IACA,KAAKV,OAAL,CAAaF,MAAb,CAAoB,KAAKI,OAAzB,EAAkCH,OAAlC;;IACA,KAAKF,SAAL,CAAec,IAAf;EACH;EACD;AACJ;AACA;AACA;;;EACIV,MAAM,GAAG;IACL,IAAI,KAAKD,OAAL,CAAaY,UAAjB,EAA6B;MACzB,KAAKZ,OAAL,CAAaC,MAAb;IACH;EACJ;;EACDY,WAAW,GAAG;IACV,IAAI,KAAKX,OAAT,EAAkB;MACd,KAAKA,OAAL,CAAaY,OAAb;IACH;EACJ;;AApDqB;;AAsD1B1B,mBAAmB,CAAC2B,IAApB;EAAA,iBAAgH3B,mBAAhH,EAAsGzD,EAAtG,mBAAqJA,EAAE,CAACW,WAAxJ,GAAsGX,EAAtG,mBAAgLA,EAAE,CAACqF,wBAAnL,GAAsGrF,EAAtG,mBAAwNA,EAAE,CAACsF,cAA3N,GAAsGtF,EAAtG,mBAAsPA,EAAE,CAACuF,QAAzP,GAAsGvF,EAAtG,mBAA8QA,EAAE,CAACwF,gBAAjR,GAAsGxF,EAAtG,mBAA8SqC,QAA9S,GAAsGrC,EAAtG,mBAAmUA,EAAE,CAACyF,iBAAtU;AAAA;;AACAhC,mBAAmB,CAACiC,IAApB,kBADsG1F,EACtG;EAAA,MAAoGyD;AAApG;;AACA;EAAA,mDAFsGzD,EAEtG,mBAA2FyD,mBAA3F,EAA4H,CAAC;IACjHkC,IAAI,EAAEzF;EAD2G,CAAD,CAA5H,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAEyF,IAAI,EAAE3F,EAAE,CAACW;IAAX,CAAD,EAA2B;MAAEgF,IAAI,EAAE3F,EAAE,CAACqF;IAAX,CAA3B,EAAkE;MAAEM,IAAI,EAAE3F,EAAE,CAACsF;IAAX,CAAlE,EAA+F;MAAEK,IAAI,EAAE3F,EAAE,CAACuF;IAAX,CAA/F,EAAsH;MAAEI,IAAI,EAAE3F,EAAE,CAACwF;IAAX,CAAtH,EAAqJ;MAAEG,IAAI,EAAEC,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClNF,IAAI,EAAExF,MAD4M;QAElN2F,IAAI,EAAE,CAACzD,QAAD;MAF4M,CAAD;IAA/B,CAArJ,EAG3B;MAAEsD,IAAI,EAAE3F,EAAE,CAACyF;IAAX,CAH2B,CAAP;EAGc,CALxD;AAAA;AAMA;AACA;AACA;;;AACA,MAAMM,cAAN,SAA6BtC,mBAA7B,CAAiD;;AAEjDsC,cAAc,CAACX,IAAf;EAAA;EAAA;IAAA,sEAbsGpF,EAatG,uBAA2G+F,cAA3G,SAA2GA,cAA3G;EAAA;AAAA;;AACAA,cAAc,CAACL,IAAf,kBAdsG1F,EActG;EAAA,MAA+F+F,cAA/F;EAAA;EAAA,WAdsG/F,EActG,oBAAmK,CAAC;IAAEgG,OAAO,EAAExC,gBAAX;IAA6ByC,WAAW,EAAEF;EAA1C,CAAD,CAAnK,GAdsG/F,EActG;AAAA;;AACA;EAAA,mDAfsGA,EAetG,mBAA2F+F,cAA3F,EAAuH,CAAC;IAC5GJ,IAAI,EAAEzF,SADsG;IAE5G4F,IAAI,EAAE,CAAC;MACCI,QAAQ,EAAE,6BADX;MAECC,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAExC,gBAAX;QAA6ByC,WAAW,EAAEF;MAA1C,CAAD;IAFZ,CAAD;EAFsG,CAAD,CAAvH;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASK,4BAAT,GAAwC;EACpC,MAAMC,KAAK,CAAE;AACjB,wEADe,CAAX;AAEH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASC,4BAAT,GAAwC;EACpC,MAAMD,KAAK,CAAE;AACjB,uEADe,CAAX;AAEH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASE,0BAAT,GAAsC;EAClC,MAAMF,KAAK,CAAE,gFAAD,GACP,sEADM,CAAX;AAEH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMG,cAAc,GAAG,IAAIvG,cAAJ,CAAmB,gBAAnB,CAAvB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;AACA,MAAMwG,gBAAgB,GAAGjE,kBAAkB,CAACC,aAAa,CAAC,MAAM,EAAP,CAAd,CAA3C;AAEA;AACA;AACA;;;AACA,MAAMiE,WAAN,SAA0BD,gBAA1B,CAA2C;EACvC/C,WAAW,CAACiD,WAAD,EAAc3C,SAAd,EAAyB4C,aAAzB,EAAwCC,WAAxC,EAAqD5C,kBAArD,EAAyE;IAChF;IACA,KAAK0C,WAAL,GAAmBA,WAAnB;IACA,KAAK3C,SAAL,GAAiBA,SAAjB;IACA,KAAK4C,aAAL,GAAqBA,aAArB;IACA,KAAKC,WAAL,GAAmBA,WAAnB;IACA,KAAK5C,kBAAL,GAA0BA,kBAA1B;IACA;;IACA,KAAK6C,IAAL,GAAY,UAAZ;IACA;;IACA,KAAKC,QAAL,GAAgB,IAAI7F,OAAJ,EAAhB;IACA;;IACA,KAAK8F,QAAL,GAAgB,IAAI9F,OAAJ,EAAhB;IACA;;IACA,KAAK+F,YAAL,GAAoB,KAApB;IACA;;IACA,KAAKC,gBAAL,GAAwB,KAAxB;IACAL,WAAW,EAAEM,OAAb,GAAuB,IAAvB;EACH;EACD;;;EACAC,KAAK,CAACC,MAAD,EAASC,OAAT,EAAkB;IACnB,IAAI,KAAKV,aAAL,IAAsBS,MAA1B,EAAkC;MAC9B,KAAKT,aAAL,CAAmBW,QAAnB,CAA4B,KAAKC,eAAL,EAA5B,EAAoDH,MAApD,EAA4DC,OAA5D;IACH,CAFD,MAGK;MACD,KAAKE,eAAL,GAAuBJ,KAAvB,CAA6BE,OAA7B;IACH;;IACD,KAAKN,QAAL,CAAchC,IAAd,CAAmB,IAAnB;EACH;;EACDyC,eAAe,GAAG;IACd,IAAI,KAAKb,aAAT,EAAwB;MACpB;MACA;MACA;MACA,KAAKA,aAAL,CAAmBc,OAAnB,CAA2B,KAAKf,WAAhC,EAA6C,KAA7C;IACH;EACJ;;EACDzB,WAAW,GAAG;IACV,IAAI,KAAK0B,aAAT,EAAwB;MACpB,KAAKA,aAAL,CAAmBe,cAAnB,CAAkC,KAAKhB,WAAvC;IACH;;IACD,IAAI,KAAKE,WAAL,IAAoB,KAAKA,WAAL,CAAiBe,UAAzC,EAAqD;MACjD,KAAKf,WAAL,CAAiBe,UAAjB,CAA4B,IAA5B;IACH;;IACD,KAAKb,QAAL,CAAcc,QAAd;;IACA,KAAKb,QAAL,CAAca,QAAd;EACH;EACD;;;EACAC,YAAY,GAAG;IACX,OAAO,KAAKC,QAAL,GAAgB,IAAhB,GAAuB,GAA9B;EACH;EACD;;;EACAP,eAAe,GAAG;IACd,OAAO,KAAKb,WAAL,CAAiBhC,aAAxB;EACH;EACD;;;EACAqD,cAAc,CAACC,KAAD,EAAQ;IAClB,IAAI,KAAKF,QAAT,EAAmB;MACfE,KAAK,CAACC,cAAN;MACAD,KAAK,CAACE,eAAN;IACH;EACJ;EACD;;;EACAC,iBAAiB,GAAG;IAChB,KAAKrB,QAAL,CAAc/B,IAAd,CAAmB,IAAnB;EACH;EACD;;;EACAqD,QAAQ,GAAG;IACP,MAAMC,KAAK,GAAG,KAAK3B,WAAL,CAAiBhC,aAAjB,CAA+B4D,SAA/B,CAAyC,IAAzC,CAAd;;IACA,MAAMC,KAAK,GAAGF,KAAK,CAACG,gBAAN,CAAuB,2BAAvB,CAAd,CAFO,CAGP;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,KAAK,CAACG,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;MACnCF,KAAK,CAACE,CAAD,CAAL,CAASE,MAAT;IACH;;IACD,OAAON,KAAK,CAACO,WAAN,EAAmBC,IAAnB,MAA6B,EAApC;EACH;;EACDC,eAAe,CAACC,aAAD,EAAgB;IAC3B;IACA;IACA;IACA;IACA,KAAK/B,YAAL,GAAoB+B,aAApB;IACA,KAAK/E,kBAAL,EAAyBc,YAAzB;EACH;;EACDkE,SAAS,GAAG;IACR,OAAO,KAAKjF,SAAL,IAAkB,KAAKA,SAAL,CAAekF,aAAf,KAAiC,KAAK1B,eAAL,EAA1D;EACH;;AAvFsC;;AAyF3Cd,WAAW,CAACtB,IAAZ;EAAA,iBAAwGsB,WAAxG,EA9KsG1G,EA8KtG,mBAAqIA,EAAE,CAACmJ,UAAxI,GA9KsGnJ,EA8KtG,mBAA+JqC,QAA/J,GA9KsGrC,EA8KtG,mBAAoLb,EAAE,CAACiK,YAAvL,GA9KsGpJ,EA8KtG,mBAAgNwG,cAAhN,MA9KsGxG,EA8KtG,mBAA2PA,EAAE,CAACyF,iBAA9P;AAAA;;AACAiB,WAAW,CAAC2C,IAAZ,kBA/KsGrJ,EA+KtG;EAAA,MAA4F0G,WAA5F;EAAA;EAAA;EAAA;EAAA;IAAA;MA/KsG1G,EA+KtG;QAAA,OAA4F,0BAA5F;MAAA;QAAA,OAA4F,uBAA5F;MAAA;IAAA;;IAAA;MA/KsGA,EA+KtG;MA/KsGA,EA+KtG;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WA/KsGA,EA+KtG;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA/KsGA,EA+KtG;MA/KsGA,EA+K0mB,gBAAhtB;MA/KsGA,EA+KqoB,uBAA3uB;MA/KsGA,EA+KiyB,+DAAv4B;IAAA;;IAAA;MA/KsGA,EA+KorB,aAA1xB;MA/KsGA,EA+KorB,8GAA1xB;MA/KsGA,EA+K0yB,aAAh5B;MA/KsGA,EA+K0yB,yCAAh5B;IAAA;EAAA;EAAA,eAAmlCoC,EAAE,CAACkH,IAAtlC,EAAurC/G,EAAE,CAACgH,SAA1rC;EAAA;EAAA;AAAA;;AACA;EAAA,mDAhLsGvJ,EAgLtG,mBAA2F0G,WAA3F,EAAoH,CAAC;IACzGf,IAAI,EAAEvF,SADmG;IAEzG0F,IAAI,EAAE,CAAC;MAAEI,QAAQ,EAAE,iBAAZ;MAA+BsD,QAAQ,EAAE,aAAzC;MAAwDC,MAAM,EAAE,CAAC,UAAD,EAAa,eAAb,CAAhE;MAA+FC,IAAI,EAAE;QAChG,eAAe,MADiF;QAEhG,yBAAyB,MAFuE;QAGhG,qCAAqC,cAH2D;QAIhG,yCAAyC,kBAJuD;QAKhG,mBAAmB,gBAL6E;QAMhG,wBAAwB,qBANwE;QAOhG,mBAAmB,kBAP6E;QAQhG,SAAS,qBARuF;QAShG,WAAW,wBATqF;QAUhG,gBAAgB;MAVgF,CAArG;MAWIC,eAAe,EAAEtJ,uBAAuB,CAACuJ,MAX7C;MAWqDC,aAAa,EAAEvJ,iBAAiB,CAACwJ,IAXtF;MAW4FC,QAAQ,EAAE;IAXtG,CAAD;EAFmG,CAAD,CAApH,EAc4B,YAAY;IAAE,OAAO,CAAC;MAAEpE,IAAI,EAAE3F,EAAE,CAACmJ;IAAX,CAAD,EAA0B;MAAExD,IAAI,EAAEC,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACvFF,IAAI,EAAExF,MADiF;QAEvF2F,IAAI,EAAE,CAACzD,QAAD;MAFiF,CAAD;IAA/B,CAA1B,EAG3B;MAAEsD,IAAI,EAAExG,EAAE,CAACiK;IAAX,CAH2B,EAGA;MAAEzD,IAAI,EAAEC,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC7DF,IAAI,EAAExF,MADuD;QAE7D2F,IAAI,EAAE,CAACU,cAAD;MAFuD,CAAD,EAG7D;QACCb,IAAI,EAAEpF;MADP,CAH6D;IAA/B,CAHA,EAQ3B;MAAEoF,IAAI,EAAE3F,EAAE,CAACyF;IAAX,CAR2B,CAAP;EAQc,CAtBxD,EAsB0E;IAAEqB,IAAI,EAAE,CAAC;MACnEnB,IAAI,EAAEnF;IAD6D,CAAD;EAAR,CAtB1E;AAAA;AA0BA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMwJ,wBAAwB,GAAG,IAAI/J,cAAJ,CAAmB,0BAAnB,EAA+C;EAC5EgK,UAAU,EAAE,MADgE;EAE5EC,OAAO,EAAEC;AAFmE,CAA/C,CAAjC;AAIA;;AACA,SAASA,gCAAT,GAA4C;EACxC,OAAO;IACHC,cAAc,EAAE,KADb;IAEHC,SAAS,EAAE,OAFR;IAGHC,SAAS,EAAE,OAHR;IAIHC,aAAa,EAAE;EAJZ,CAAP;AAMH;;AACD,IAAIC,YAAY,GAAG,CAAnB;AACA;;AACA,MAAMC,YAAN,CAAmB;EACf/G,WAAW,CAACiD,WAAD,EAAc+D,OAAd,EAAuBC,eAAvB,EACX;EACA1G,kBAFW,EAES;IAChB,KAAK0C,WAAL,GAAmBA,WAAnB;IACA,KAAK+D,OAAL,GAAeA,OAAf;IACA,KAAKC,eAAL,GAAuBA,eAAvB;IACA,KAAK1G,kBAAL,GAA0BA,kBAA1B;IACA,KAAK2G,UAAL,GAAkB,KAAKD,eAAL,CAAqBN,SAAvC;IACA,KAAKQ,UAAL,GAAkB,KAAKF,eAAL,CAAqBL,SAAvC;IACA;;IACA,KAAKQ,sBAAL,GAA8B,IAAIrK,SAAJ,EAA9B;IACA;;IACA,KAAKsK,gBAAL,GAAwB5J,YAAY,CAAC6J,KAArC;IACA;;IACA,KAAKC,UAAL,GAAkB,EAAlB;IACA;;IACA,KAAKC,oBAAL,GAA4B,MAA5B;IACA;;IACA,KAAKC,cAAL,GAAsB,IAAIjK,OAAJ,EAAtB;IACA;;IACA,KAAKkK,iBAAL,GAAyB,KAAKT,eAAL,CAAqBS,iBAArB,IAA0C,EAAnE;IACA;;IACA,KAAKb,aAAL,GAAqB,KAAKI,eAAL,CAAqBJ,aAA1C;IACA,KAAKc,eAAL,GAAuB,KAAKV,eAAL,CAAqBP,cAA5C;IACA,KAAKkB,YAAL,GAAoB,KAAKX,eAAL,CAAqBY,WAAzC;IACA;;IACA,KAAKC,MAAL,GAAc,IAAI9K,YAAJ,EAAd;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAK+K,KAAL,GAAa,KAAKD,MAAlB;IACA,KAAKE,OAAL,GAAgB,kBAAiBlB,YAAY,EAAG,EAAhD;EACH;EACD;;;EACa,IAATH,SAAS,GAAG;IACZ,OAAO,KAAKO,UAAZ;EACH;;EACY,IAATP,SAAS,CAACsB,KAAD,EAAQ;IACjB,IAAIA,KAAK,KAAK,QAAV,IACAA,KAAK,KAAK,OADV,KAEC,OAAOC,SAAP,KAAqB,WAArB,IAAoCA,SAFrC,CAAJ,EAEqD;MACjDxF,4BAA4B;IAC/B;;IACD,KAAKwE,UAAL,GAAkBe,KAAlB;IACA,KAAKE,kBAAL;EACH;EACD;;;EACa,IAATvB,SAAS,GAAG;IACZ,OAAO,KAAKO,UAAZ;EACH;;EACY,IAATP,SAAS,CAACqB,KAAD,EAAQ;IACjB,IAAIA,KAAK,KAAK,OAAV,IAAqBA,KAAK,KAAK,OAA/B,KAA2C,OAAOC,SAAP,KAAqB,WAArB,IAAoCA,SAA/E,CAAJ,EAA+F;MAC3FtF,4BAA4B;IAC/B;;IACD,KAAKuE,UAAL,GAAkBc,KAAlB;IACA,KAAKE,kBAAL;EACH;EACD;;;EACkB,IAAdzB,cAAc,GAAG;IACjB,OAAO,KAAKiB,eAAZ;EACH;;EACiB,IAAdjB,cAAc,CAACuB,KAAD,EAAQ;IACtB,KAAKN,eAAL,GAAuB9L,qBAAqB,CAACoM,KAAD,CAA5C;EACH;EACD;;;EACe,IAAXJ,WAAW,GAAG;IACd,OAAO,KAAKD,YAAZ;EACH;;EACc,IAAXC,WAAW,CAACI,KAAD,EAAQ;IACnB,KAAKL,YAAL,GAAoB/L,qBAAqB,CAACoM,KAAD,CAAzC;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACkB,IAAVG,UAAU,CAACC,OAAD,EAAU;IACpB,MAAMC,kBAAkB,GAAG,KAAKC,mBAAhC;;IACA,IAAID,kBAAkB,IAAIA,kBAAkB,CAACrD,MAA7C,EAAqD;MACjDqD,kBAAkB,CAACE,KAAnB,CAAyB,GAAzB,EAA8BC,OAA9B,CAAuCC,SAAD,IAAe;QACjD,KAAKnB,UAAL,CAAgBmB,SAAhB,IAA6B,KAA7B;MACH,CAFD;IAGH;;IACD,KAAKH,mBAAL,GAA2BF,OAA3B;;IACA,IAAIA,OAAO,IAAIA,OAAO,CAACpD,MAAvB,EAA+B;MAC3BoD,OAAO,CAACG,KAAR,CAAc,GAAd,EAAmBC,OAAnB,CAA4BC,SAAD,IAAe;QACtC,KAAKnB,UAAL,CAAgBmB,SAAhB,IAA6B,IAA7B;MACH,CAFD;MAGA,KAAKzF,WAAL,CAAiBhC,aAAjB,CAA+ByH,SAA/B,GAA2C,EAA3C;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACiB,IAATC,SAAS,GAAG;IACZ,OAAO,KAAKP,UAAZ;EACH;;EACY,IAATO,SAAS,CAACN,OAAD,EAAU;IACnB,KAAKD,UAAL,GAAkBC,OAAlB;EACH;;EACDO,QAAQ,GAAG;IACP,KAAKT,kBAAL;EACH;;EACDU,kBAAkB,GAAG;IACjB,KAAKC,wBAAL;;IACA,KAAKC,WAAL,GAAmB,IAAIrN,eAAJ,CAAoB,KAAK0L,sBAAzB,EACd4B,QADc,GAEdC,aAFc,GAGdC,cAHc,EAAnB;IAIA,KAAK7B,gBAAL,GAAwB,KAAK0B,WAAL,CAAiBI,MAAjB,CAAwBC,SAAxB,CAAkC,MAAM,KAAKtB,MAAL,CAAYuB,IAAZ,CAAiB,KAAjB,CAAxC,CAAxB,CANiB,CAOjB;IACA;IACA;;IACA,KAAKjC,sBAAL,CAA4BkC,OAA5B,CACKC,IADL,CACU1L,SAAS,CAAC,KAAKuJ,sBAAN,CADnB,EACkDtJ,SAAS,CAAC0L,KAAK,IAAI9L,KAAK,CAAC,GAAG8L,KAAK,CAACC,GAAN,CAAWC,IAAD,IAAUA,IAAI,CAACpG,QAAzB,CAAJ,CAAf,CAD3D,EAEK8F,SAFL,CAEeO,WAAW,IAAI,KAAKZ,WAAL,CAAiBa,gBAAjB,CAAkCD,WAAlC,CAF9B;;IAGA,KAAKvC,sBAAL,CAA4BkC,OAA5B,CAAoCF,SAApC,CAA+CS,SAAD,IAAe;MACzD;MACA;MACA;MACA,MAAMC,OAAO,GAAG,KAAKf,WAArB;;MACA,IAAI,KAAKvB,oBAAL,KAA8B,OAA9B,IAAyCsC,OAAO,CAACC,UAAR,EAAoBxE,SAApB,EAA7C,EAA8E;QAC1E,MAAMiE,KAAK,GAAGK,SAAS,CAACG,OAAV,EAAd;QACA,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAASZ,KAAK,CAACvE,MAAN,GAAe,CAAxB,EAA2B6E,OAAO,CAACO,eAAR,IAA2B,CAAtD,CAAZ,CAAd;;QACA,IAAIb,KAAK,CAACS,KAAD,CAAL,IAAgB,CAACT,KAAK,CAACS,KAAD,CAAL,CAAa5F,QAAlC,EAA4C;UACxCyF,OAAO,CAACQ,aAAR,CAAsBL,KAAtB;QACH,CAFD,MAGK;UACDH,OAAO,CAACS,iBAAR;QACH;MACJ;IACJ,CAfD;EAgBH;;EACD/I,WAAW,GAAG;IACV,KAAK4F,sBAAL,CAA4BoD,OAA5B;;IACA,KAAKnD,gBAAL,CAAsBoD,WAAtB;;IACA,KAAK3C,MAAL,CAAY3D,QAAZ;EACH;EACD;;;EACAd,QAAQ,GAAG;IACP;IACA,MAAMqH,WAAW,GAAG,KAAKtD,sBAAL,CAA4BkC,OAAhD;IACA,OAAOoB,WAAW,CAACnB,IAAZ,CAAiB1L,SAAS,CAAC,KAAKuJ,sBAAN,CAA1B,EAAyDtJ,SAAS,CAAC0L,KAAK,IAAI9L,KAAK,CAAC,GAAG8L,KAAK,CAACC,GAAN,CAAWC,IAAD,IAAUA,IAAI,CAACrG,QAAzB,CAAJ,CAAf,CAAlE,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACII,OAAO,CAACkH,KAAD,EAAQ,CAAG;EAClB;AACJ;AACA;AACA;AACA;AACA;;;EACIzG,UAAU,CAACyG,KAAD,EAAQ,CAAG;EACrB;;;EACAC,cAAc,CAACrG,KAAD,EAAQ;IAClB,MAAMsG,OAAO,GAAGtG,KAAK,CAACsG,OAAtB;IACA,MAAMf,OAAO,GAAG,KAAKf,WAArB;;IACA,QAAQ8B,OAAR;MACI,KAAK3O,MAAL;QACI,IAAI,CAACC,cAAc,CAACoI,KAAD,CAAnB,EAA4B;UACxBA,KAAK,CAACC,cAAN;UACA,KAAKsD,MAAL,CAAYuB,IAAZ,CAAiB,SAAjB;QACH;;QACD;;MACJ,KAAKpN,UAAL;QACI,IAAI,KAAK6O,UAAL,IAAmB,KAAKC,SAAL,KAAmB,KAA1C,EAAiD;UAC7C,KAAKjD,MAAL,CAAYuB,IAAZ,CAAiB,SAAjB;QACH;;QACD;;MACJ,KAAKrN,WAAL;QACI,IAAI,KAAK8O,UAAL,IAAmB,KAAKC,SAAL,KAAmB,KAA1C,EAAiD;UAC7C,KAAKjD,MAAL,CAAYuB,IAAZ,CAAiB,SAAjB;QACH;;QACD;;MACJ;QACI,IAAIwB,OAAO,KAAK/O,QAAZ,IAAwB+O,OAAO,KAAK9O,UAAxC,EAAoD;UAChD+N,OAAO,CAACkB,cAAR,CAAuB,UAAvB;QACH;;QACDlB,OAAO,CAACmB,SAAR,CAAkB1G,KAAlB;QACA;IAtBR,CAHkB,CA2BlB;IACA;;;IACAA,KAAK,CAACE,eAAN;EACH;EACD;AACJ;AACA;AACA;;;EACIyG,cAAc,CAACvH,MAAM,GAAG,SAAV,EAAqB;IAC/B;IACA,KAAKqD,OAAL,CAAamE,QAAb,CAAsB5B,IAAtB,CAA2BxL,IAAI,CAAC,CAAD,CAA/B,EAAoCqL,SAApC,CAA8C,MAAM;MAChD,IAAIgC,SAAS,GAAG,IAAhB;;MACA,IAAI,KAAKhE,sBAAL,CAA4BnC,MAAhC,EAAwC;QACpC;QACA;QACA;QACA;QACAmG,SAAS,GAAG,KAAKhE,sBAAL,CAA4BiE,KAA5B,CAAkCvH,eAAlC,GAAoDwH,OAApD,CAA4D,eAA5D,CAAZ;MACH,CAR+C,CAShD;;;MACA,IAAI,CAACF,SAAD,IAAc,CAACA,SAAS,CAACG,QAAV,CAAmBC,QAAQ,CAAChG,aAA5B,CAAnB,EAA+D;QAC3D,MAAMsE,OAAO,GAAG,KAAKf,WAArB;QACAe,OAAO,CAACkB,cAAR,CAAuBrH,MAAvB,EAA+B8H,kBAA/B,GAF2D,CAG3D;QACA;QACA;;QACA,IAAI,CAAC3B,OAAO,CAACC,UAAT,IAAuBqB,SAA3B,EAAsC;UAClCA,SAAS,CAAC1H,KAAV;QACH;MACJ;IACJ,CApBD;EAqBH;EACD;AACJ;AACA;AACA;;;EACIgI,eAAe,GAAG;IACd,KAAK3C,WAAL,CAAiBuB,aAAjB,CAA+B,CAAC,CAAhC;EACH;EACD;AACJ;AACA;AACA;;;EACIqB,YAAY,CAACC,KAAD,EAAQ;IAChB;IACA;IACA,MAAMC,SAAS,GAAG3B,IAAI,CAACE,GAAL,CAAS,KAAK0B,cAAL,GAAsBF,KAA/B,EAAsC,EAAtC,CAAlB;IACA,MAAMG,YAAY,GAAI,GAAE,KAAKC,gBAAiB,GAAEH,SAAU,EAA1D;IACA,MAAMI,eAAe,GAAGC,MAAM,CAACC,IAAP,CAAY,KAAK5E,UAAjB,EAA6B6E,IAA7B,CAAkC1D,SAAS,IAAI;MACnE,OAAOA,SAAS,CAAC2D,UAAV,CAAqB,KAAKL,gBAA1B,CAAP;IACH,CAFuB,CAAxB;;IAGA,IAAI,CAACC,eAAD,IAAoBA,eAAe,KAAK,KAAKK,kBAAjD,EAAqE;MACjE,IAAI,KAAKA,kBAAT,EAA6B;QACzB,KAAK/E,UAAL,CAAgB,KAAK+E,kBAArB,IAA2C,KAA3C;MACH;;MACD,KAAK/E,UAAL,CAAgBwE,YAAhB,IAAgC,IAAhC;MACA,KAAKO,kBAAL,GAA0BP,YAA1B;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACI5D,kBAAkB,CAACoE,IAAI,GAAG,KAAK5F,SAAb,EAAwB6F,IAAI,GAAG,KAAK5F,SAApC,EAA+C;IAC7D,MAAMyB,OAAO,GAAG,KAAKd,UAArB;IACAc,OAAO,CAAC,iBAAD,CAAP,GAA6BkE,IAAI,KAAK,QAAtC;IACAlE,OAAO,CAAC,gBAAD,CAAP,GAA4BkE,IAAI,KAAK,OAArC;IACAlE,OAAO,CAAC,gBAAD,CAAP,GAA4BmE,IAAI,KAAK,OAArC;IACAnE,OAAO,CAAC,gBAAD,CAAP,GAA4BmE,IAAI,KAAK,OAArC,CAL6D,CAM7D;;IACA,KAAKjM,kBAAL,EAAyBc,YAAzB;EACH;EACD;;;EACAoL,eAAe,GAAG;IACd;IACA,KAAKjF,oBAAL,GAA4B,OAA5B;EACH;EACD;;;EACAkF,eAAe,GAAG;IACd;IACA,KAAKlF,oBAAL,GAA4B,MAA5B;EACH;EACD;;;EACAmF,gBAAgB,CAACpI,KAAD,EAAQ;IACpB,KAAKkD,cAAL,CAAoBnG,IAApB,CAAyBiD,KAAzB;;IACA,KAAKqI,YAAL,GAAoB,KAApB;EACH;;EACDC,iBAAiB,CAACtI,KAAD,EAAQ;IACrB,KAAKqI,YAAL,GAAoB,IAApB,CADqB,CAErB;IACA;IACA;IACA;IACA;IACA;;IACA,IAAIrI,KAAK,CAACuI,OAAN,KAAkB,OAAlB,IAA6B,KAAK/D,WAAL,CAAiBsB,eAAjB,KAAqC,CAAtE,EAAyE;MACrE9F,KAAK,CAACxD,OAAN,CAAcgM,SAAd,GAA0B,CAA1B;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIjE,wBAAwB,GAAG;IACvB,KAAKkE,SAAL,CAAe1D,OAAf,CACKC,IADL,CACU1L,SAAS,CAAC,KAAKmP,SAAN,CADnB,EAEK5D,SAFL,CAEgBI,KAAD,IAAW;MACtB,KAAKpC,sBAAL,CAA4B6F,KAA5B,CAAkCzD,KAAK,CAACvL,MAAN,CAAayL,IAAI,IAAIA,IAAI,CAACvG,WAAL,KAAqB,IAA1C,CAAlC;;MACA,KAAKiE,sBAAL,CAA4B8F,eAA5B;IACH,CALD;EAMH;;AArTc;;AAuTnBnG,YAAY,CAACrF,IAAb;EAAA,iBAAyGqF,YAAzG,EAxhBsGzK,EAwhBtG,mBAAuIA,EAAE,CAACmJ,UAA1I,GAxhBsGnJ,EAwhBtG,mBAAiKA,EAAE,CAAC6Q,MAApK,GAxhBsG7Q,EAwhBtG,mBAAuLgK,wBAAvL,GAxhBsGhK,EAwhBtG,mBAA4NA,EAAE,CAACyF,iBAA/N;AAAA;;AACAgF,YAAY,CAAC/E,IAAb,kBAzhBsG1F,EAyhBtG;EAAA,MAA6FyK,YAA7F;EAAA;IAAA;MAzhBsGzK,EAyhBtG,0BAA2kBwD,gBAA3kB;MAzhBsGxD,EAyhBtG,0BAA0pB0G,WAA1pB;MAzhBsG1G,EAyhBtG,0BAAguB0G,WAAhuB;IAAA;;IAAA;MAAA;;MAzhBsG1G,EAyhBtG,qBAzhBsGA,EAyhBtG;MAzhBsGA,EAyhBtG,qBAzhBsGA,EAyhBtG;MAzhBsGA,EAyhBtG,qBAzhBsGA,EAyhBtG;IAAA;EAAA;EAAA;IAAA;MAzhBsGA,EAyhBtG,aAAqzBW,WAArzB;IAAA;;IAAA;MAAA;;MAzhBsGX,EAyhBtG,qBAzhBsGA,EAyhBtG;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;AAAA;;AACA;EAAA,mDA1hBsGA,EA0hBtG,mBAA2FyK,YAA3F,EAAqH,CAAC;IAC1G9E,IAAI,EAAEzF;EADoG,CAAD,CAArH,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAEyF,IAAI,EAAE3F,EAAE,CAACmJ;IAAX,CAAD,EAA0B;MAAExD,IAAI,EAAE3F,EAAE,CAAC6Q;IAAX,CAA1B,EAA+C;MAAElL,IAAI,EAAEC,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC5GF,IAAI,EAAExF,MADsG;QAE5G2F,IAAI,EAAE,CAACkE,wBAAD;MAFsG,CAAD;IAA/B,CAA/C,EAG3B;MAAErE,IAAI,EAAE3F,EAAE,CAACyF;IAAX,CAH2B,CAAP;EAGc,CALxD,EAK0E;IAAEiL,SAAS,EAAE,CAAC;MACxE/K,IAAI,EAAE/E,eADkE;MAExEkF,IAAI,EAAE,CAACY,WAAD,EAAc;QAAEoK,WAAW,EAAE;MAAf,CAAd;IAFkE,CAAD,CAAb;IAG1DvG,aAAa,EAAE,CAAC;MAChB5E,IAAI,EAAEnF;IADU,CAAD,CAH2C;IAK1DuQ,SAAS,EAAE,CAAC;MACZpL,IAAI,EAAEnF,KADM;MAEZsF,IAAI,EAAE,CAAC,YAAD;IAFM,CAAD,CAL+C;IAQ1DkL,cAAc,EAAE,CAAC;MACjBrL,IAAI,EAAEnF,KADW;MAEjBsF,IAAI,EAAE,CAAC,iBAAD;IAFW,CAAD,CAR0C;IAW1DmL,eAAe,EAAE,CAAC;MAClBtL,IAAI,EAAEnF,KADY;MAElBsF,IAAI,EAAE,CAAC,kBAAD;IAFY,CAAD,CAXyC;IAc1DuE,SAAS,EAAE,CAAC;MACZ1E,IAAI,EAAEnF;IADM,CAAD,CAd+C;IAgB1D8J,SAAS,EAAE,CAAC;MACZ3E,IAAI,EAAEnF;IADM,CAAD,CAhB+C;IAkB1D0Q,WAAW,EAAE,CAAC;MACdvL,IAAI,EAAE9E,SADQ;MAEdiF,IAAI,EAAE,CAACnF,WAAD;IAFQ,CAAD,CAlB6C;IAqB1DuM,KAAK,EAAE,CAAC;MACRvH,IAAI,EAAE/E,eADE;MAERkF,IAAI,EAAE,CAACY,WAAD,EAAc;QAAEoK,WAAW,EAAE;MAAf,CAAd;IAFE,CAAD,CArBmD;IAwB1DK,WAAW,EAAE,CAAC;MACdxL,IAAI,EAAE7E,YADQ;MAEdgF,IAAI,EAAE,CAACtC,gBAAD;IAFQ,CAAD,CAxB6C;IA2B1D4G,cAAc,EAAE,CAAC;MACjBzE,IAAI,EAAEnF;IADW,CAAD,CA3B0C;IA6B1D+K,WAAW,EAAE,CAAC;MACd5F,IAAI,EAAEnF;IADQ,CAAD,CA7B6C;IA+B1DsL,UAAU,EAAE,CAAC;MACbnG,IAAI,EAAEnF,KADO;MAEbsF,IAAI,EAAE,CAAC,OAAD;IAFO,CAAD,CA/B8C;IAkC1DuG,SAAS,EAAE,CAAC;MACZ1G,IAAI,EAAEnF;IADM,CAAD,CAlC+C;IAoC1DgL,MAAM,EAAE,CAAC;MACT7F,IAAI,EAAE5E;IADG,CAAD,CApCkD;IAsC1D0K,KAAK,EAAE,CAAC;MACR9F,IAAI,EAAE5E;IADE,CAAD;EAtCmD,CAL1E;AAAA;AA8CA;;;AACA,MAAMqQ,OAAN,SAAsB3G,YAAtB,CAAmC;EAC/B/G,WAAW,CAACgB,UAAD,EAAa2M,MAAb,EAAqBC,cAArB,EAAqCC,iBAArC,EAAwD;IAC/D,MAAM7M,UAAN,EAAkB2M,MAAlB,EAA0BC,cAA1B,EAA0CC,iBAA1C;IACA,KAAK7B,gBAAL,GAAwB,iBAAxB;IACA,KAAKF,cAAL,GAAsB,CAAtB;EACH;;AAL8B;;AAOnC4B,OAAO,CAAChM,IAAR;EAAA,iBAAoGgM,OAApG,EAhlBsGpR,EAglBtG,mBAA6HA,EAAE,CAACmJ,UAAhI,GAhlBsGnJ,EAglBtG,mBAAuJA,EAAE,CAAC6Q,MAA1J,GAhlBsG7Q,EAglBtG,mBAA6KgK,wBAA7K,GAhlBsGhK,EAglBtG,mBAAkNA,EAAE,CAACyF,iBAArN;AAAA;;AACA2L,OAAO,CAAC/H,IAAR,kBAjlBsGrJ,EAilBtG;EAAA,MAAwFoR,OAAxF;EAAA;EAAA;EAAA;IAAA;MAjlBsGpR,EAilBtG;IAAA;EAAA;EAAA;EAAA,WAjlBsGA,EAilBtG,oBAAwP,CAAC;IAAEgG,OAAO,EAAEQ,cAAX;IAA2BP,WAAW,EAAEmL;EAAxC,CAAD,CAAxP,GAjlBsGpR,EAilBtG;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAjlBsGA,EAilBtG;MAjlBsGA,EAilB8Q,mEAApX;IAAA;EAAA;EAAA,eAAmvFoC,EAAE,CAACoP,OAAtvF;EAAA;EAAA;EAAA;IAAA,WAAm0F,CAACrO,iBAAiB,CAACC,aAAnB,EAAkCD,iBAAiB,CAACI,WAApD;EAAn0F;EAAA;AAAA;;AACA;EAAA,mDAllBsGvD,EAklBtG,mBAA2FoR,OAA3F,EAAgH,CAAC;IACrGzL,IAAI,EAAEvF,SAD+F;IAErG0F,IAAI,EAAE,CAAC;MAAEI,QAAQ,EAAE,UAAZ;MAAwByD,eAAe,EAAEtJ,uBAAuB,CAACuJ,MAAjE;MAAyEC,aAAa,EAAEvJ,iBAAiB,CAACwJ,IAA1G;MAAgHN,QAAQ,EAAE,SAA1H;MAAqIE,IAAI,EAAE;QACtI,qBAAqB,MADiH;QAEtI,0BAA0B,MAF4G;QAGtI,2BAA2B;MAH2G,CAA3I;MAII+H,UAAU,EAAE,CAACtO,iBAAiB,CAACC,aAAnB,EAAkCD,iBAAiB,CAACI,WAApD,CAJhB;MAIkF4C,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAEQ,cAAX;QAA2BP,WAAW,EAAEmL;MAAxC,CAAD,CAJ7F;MAIkJrH,QAAQ,EAAE,+oBAJ5J;MAI6yB2H,MAAM,EAAE,CAAC,yrDAAD;IAJrzB,CAAD;EAF+F,CAAD,CAAhH,EAO4B,YAAY;IAAE,OAAO,CAAC;MAAE/L,IAAI,EAAE3F,EAAE,CAACmJ;IAAX,CAAD,EAA0B;MAAExD,IAAI,EAAE3F,EAAE,CAAC6Q;IAAX,CAA1B,EAA+C;MAAElL,IAAI,EAAEC,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC5GF,IAAI,EAAExF,MADsG;QAE5G2F,IAAI,EAAE,CAACkE,wBAAD;MAFsG,CAAD;IAA/B,CAA/C,EAG3B;MAAErE,IAAI,EAAE3F,EAAE,CAACyF;IAAX,CAH2B,CAAP;EAGc,CAVxD;AAAA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMkM,wBAAwB,GAAG,IAAI1R,cAAJ,CAAmB,0BAAnB,CAAjC;AACA;;AACA,SAAS2R,gCAAT,CAA0CC,OAA1C,EAAmD;EAC/C,OAAO,MAAMA,OAAO,CAACC,gBAAR,CAAyBC,UAAzB,EAAb;AACH;AACD;;;AACA,MAAMC,yCAAyC,GAAG;EAC9ChM,OAAO,EAAE2L,wBADqC;EAE9CM,IAAI,EAAE,CAACnP,OAAD,CAFwC;EAG9CoP,UAAU,EAAEN;AAHkC,CAAlD;AAKA;AACA;AACA;AACA;AACA;;AACA,MAAMO,sBAAsB,GAAG,CAA/B;AACA;;AACA,MAAMC,2BAA2B,GAAGnP,+BAA+B,CAAC;EAAEoP,OAAO,EAAE;AAAX,CAAD,CAAnE,C,CACA;;AACA,MAAMC,mBAAN,CAA0B;EACtB5O,WAAW,CAAC6O,QAAD,EAAWC,QAAX,EAAqBzO,iBAArB,EAAwC0O,cAAxC,EAAwDjE,UAAxD,EACX;EACA;EACAkE,iBAHW,EAGQC,IAHR,EAGc/L,aAHd,EAG6B8D,OAH7B,EAGsC;IAC7C,KAAK6H,QAAL,GAAgBA,QAAhB;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKzO,iBAAL,GAAyBA,iBAAzB;IACA,KAAK2O,iBAAL,GAAyBA,iBAAzB;IACA,KAAKC,IAAL,GAAYA,IAAZ;IACA,KAAK/L,aAAL,GAAqBA,aAArB;IACA,KAAK8D,OAAL,GAAeA,OAAf;IACA,KAAKkI,WAAL,GAAmB,IAAnB;IACA,KAAKC,SAAL,GAAiB,KAAjB;IACA,KAAKC,2BAAL,GAAmC3R,YAAY,CAAC6J,KAAhD;IACA,KAAK+H,kBAAL,GAA0B5R,YAAY,CAAC6J,KAAvC;IACA,KAAKgI,sBAAL,GAA8B7R,YAAY,CAAC6J,KAA3C;IACA;AACR;AACA;AACA;;IACQ,KAAKiI,iBAAL,GAA0BhL,KAAD,IAAW;MAChC,IAAI,CAAC5I,gCAAgC,CAAC4I,KAAD,CAArC,EAA8C;QAC1C,KAAKiL,SAAL,GAAiB,OAAjB;MACH;IACJ,CAJD,CAjB6C,CAsB7C;IACA;;;IACA,KAAKA,SAAL,GAAiBtN,SAAjB;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKuN,YAAL,GAAoB,IAApB;IACA;;IACA,KAAKC,UAAL,GAAkB,IAAI1S,YAAJ,EAAlB;IACA;AACR;AACA;AACA;AACA;IACQ;;IACA,KAAK2S,UAAL,GAAkB,KAAKD,UAAvB;IACA;;IACA,KAAKE,UAAL,GAAkB,IAAI5S,YAAJ,EAAlB;IACA;AACR;AACA;AACA;AACA;IACQ;;IACA,KAAK6S,WAAL,GAAmB,KAAKD,UAAxB;IACA,KAAKE,eAAL,GAAuBf,cAAvB;IACA,KAAKgB,mBAAL,GAA2BjF,UAAU,YAAY/D,YAAtB,GAAqC+D,UAArC,GAAkD5I,SAA7E;;IACA4M,QAAQ,CAAC7N,aAAT,CAAuB+O,gBAAvB,CAAwC,YAAxC,EAAsD,KAAKT,iBAA3D,EAA8Eb,2BAA9E;;IACA,IAAIM,iBAAJ,EAAuB;MACnBA,iBAAiB,CAACxL,gBAAlB,GAAqC,KAAKyM,eAAL,EAArC;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACoC,IAA5BC,4BAA4B,GAAG;IAC/B,OAAO,KAAKC,IAAZ;EACH;;EAC+B,IAA5BD,4BAA4B,CAACE,CAAD,EAAI;IAChC,KAAKD,IAAL,GAAYC,CAAZ;EACH;EACD;;;EACQ,IAAJD,IAAI,GAAG;IACP,OAAO,KAAKE,KAAZ;EACH;;EACO,IAAJF,IAAI,CAACA,IAAD,EAAO;IACX,IAAIA,IAAI,KAAK,KAAKE,KAAlB,EAAyB;MACrB;IACH;;IACD,KAAKA,KAAL,GAAaF,IAAb;;IACA,KAAKb,sBAAL,CAA4B7E,WAA5B;;IACA,IAAI0F,IAAJ,EAAU;MACN,IAAIA,IAAI,KAAK,KAAKJ,mBAAd,KAAsC,OAAO7H,SAAP,KAAqB,WAArB,IAAoCA,SAA1E,CAAJ,EAA0F;QACtFrF,0BAA0B;MAC7B;;MACD,KAAKyM,sBAAL,GAA8Ba,IAAI,CAACpI,KAAL,CAAWqB,SAAX,CAAsBkH,MAAD,IAAY;QAC3D,KAAKC,YAAL,CAAkBD,MAAlB,EAD2D,CAE3D;;;QACA,IAAI,CAACA,MAAM,KAAK,OAAX,IAAsBA,MAAM,KAAK,KAAlC,KAA4C,KAAKP,mBAArD,EAA0E;UACtE,KAAKA,mBAAL,CAAyBjI,MAAzB,CAAgCuB,IAAhC,CAAqCiH,MAArC;QACH;MACJ,CAN6B,CAA9B;IAOH;EACJ;;EACDzH,kBAAkB,GAAG;IACjB,KAAK2H,YAAL;EACH;;EACDhP,WAAW,GAAG;IACV,IAAI,KAAK0N,WAAT,EAAsB;MAClB,KAAKA,WAAL,CAAiBzN,OAAjB;;MACA,KAAKyN,WAAL,GAAmB,IAAnB;IACH;;IACD,KAAKJ,QAAL,CAAc7N,aAAd,CAA4BwP,mBAA5B,CAAgD,YAAhD,EAA8D,KAAKlB,iBAAnE,EAAsFb,2BAAtF;;IACA,KAAKY,sBAAL,CAA4B7E,WAA5B;;IACA,KAAK2E,2BAAL,CAAiC3E,WAAjC;;IACA,KAAK4E,kBAAL,CAAwB5E,WAAxB;EACH;EACD;;;EACY,IAARiG,QAAQ,GAAG;IACX,OAAO,KAAKvB,SAAZ;EACH;EACD;;;EACO,IAAHwB,GAAG,GAAG;IACN,OAAO,KAAK1B,IAAL,IAAa,KAAKA,IAAL,CAAUhH,KAAV,KAAoB,KAAjC,GAAyC,KAAzC,GAAiD,KAAxD;EACH;EACD;;;EACAgI,eAAe,GAAG;IACd,OAAO,CAAC,EAAE,KAAKjB,iBAAL,IAA0B,KAAKe,mBAAjC,CAAR;EACH;EACD;;;EACAa,UAAU,GAAG;IACT,OAAO,KAAKzB,SAAL,GAAiB,KAAK0B,SAAL,EAAjB,GAAoC,KAAKC,QAAL,EAA3C;EACH;EACD;;;EACAA,QAAQ,GAAG;IACP,MAAMX,IAAI,GAAG,KAAKA,IAAlB;;IACA,IAAI,KAAKhB,SAAL,IAAkB,CAACgB,IAAvB,EAA6B;MACzB;IACH;;IACD,MAAMY,UAAU,GAAG,KAAKC,cAAL,CAAoBb,IAApB,CAAnB;;IACA,MAAMc,aAAa,GAAGF,UAAU,CAACG,SAAX,EAAtB;IACA,MAAMC,gBAAgB,GAAGF,aAAa,CAACE,gBAAvC;;IACA,KAAKC,YAAL,CAAkBjB,IAAlB,EAAwBgB,gBAAxB;;IACAF,aAAa,CAACpJ,WAAd,GACIsI,IAAI,CAACtI,WAAL,IAAoB,IAApB,GAA2B,CAAC,KAAKoI,eAAL,EAA5B,GAAqDE,IAAI,CAACtI,WAD9D;IAEAkJ,UAAU,CAACtQ,MAAX,CAAkB,KAAK4Q,UAAL,CAAgBlB,IAAhB,CAAlB;;IACA,IAAIA,IAAI,CAAC1C,WAAT,EAAsB;MAClB0C,IAAI,CAAC1C,WAAL,CAAiBhN,MAAjB,CAAwB,KAAK6Q,QAA7B;IACH;;IACD,KAAKlC,2BAAL,GAAmC,KAAKmC,mBAAL,GAA2BnI,SAA3B,CAAqC,MAAM,KAAKyH,SAAL,EAA3C,CAAnC;;IACA,KAAKW,SAAL,CAAerB,IAAf;;IACA,IAAIA,IAAI,YAAYpJ,YAApB,EAAkC;MAC9BoJ,IAAI,CAAC1D,eAAL;;MACA0D,IAAI,CAAC/I,sBAAL,CAA4BkC,OAA5B,CAAoCC,IAApC,CAAyCvL,SAAS,CAACmS,IAAI,CAACpI,KAAN,CAAlD,EAAgEqB,SAAhE,CAA0E,MAAM;QAC5E;QACA;QACA+H,gBAAgB,CAACM,kBAAjB,CAAoC,KAApC,EAA2CC,mBAA3C;QACAP,gBAAgB,CAACM,kBAAjB,CAAoC,IAApC;MACH,CALD;IAMH;EACJ;EACD;;;EACAZ,SAAS,GAAG;IACR,KAAKV,IAAL,EAAWpI,KAAX,CAAiBsB,IAAjB;EACH;EACD;AACJ;AACA;AACA;;;EACI3F,KAAK,CAACC,MAAD,EAASC,OAAT,EAAkB;IACnB,IAAI,KAAKV,aAAL,IAAsBS,MAA1B,EAAkC;MAC9B,KAAKT,aAAL,CAAmBW,QAAnB,CAA4B,KAAKiL,QAAjC,EAA2CnL,MAA3C,EAAmDC,OAAnD;IACH,CAFD,MAGK;MACD,KAAKkL,QAAL,CAAc7N,aAAd,CAA4ByC,KAA5B,CAAkCE,OAAlC;IACH;EACJ;EACD;AACJ;AACA;;;EACI+N,cAAc,GAAG;IACb,KAAKzC,WAAL,EAAkByC,cAAlB;EACH;EACD;;;EACApB,YAAY,CAACD,MAAD,EAAS;IACjB,IAAI,CAAC,KAAKpB,WAAN,IAAqB,CAAC,KAAKwB,QAA/B,EAAyC;MACrC;IACH;;IACD,MAAMP,IAAI,GAAG,KAAKA,IAAlB;;IACA,KAAKf,2BAAL,CAAiC3E,WAAjC;;IACA,KAAKyE,WAAL,CAAiBtO,MAAjB,GANiB,CAOjB;IACA;IACA;IACA;;;IACA,IAAI,KAAK6O,YAAL,KAAsBa,MAAM,KAAK,SAAX,IAAwB,CAAC,KAAKd,SAA9B,IAA2C,CAAC,KAAKS,eAAL,EAAlE,CAAJ,EAA+F;MAC3F,KAAKvM,KAAL,CAAW,KAAK8L,SAAhB;IACH;;IACD,KAAKA,SAAL,GAAiBtN,SAAjB;;IACA,IAAIiO,IAAI,YAAYpJ,YAApB,EAAkC;MAC9BoJ,IAAI,CAACzD,eAAL;;MACA,IAAIyD,IAAI,CAAC1C,WAAT,EAAsB;QAClB;QACA0C,IAAI,CAAC1I,cAAL,CACK8B,IADL,CACUtL,MAAM,CAACsG,KAAK,IAAIA,KAAK,CAACuI,OAAN,KAAkB,MAA5B,CADhB,EACqD/O,IAAI,CAAC,CAAD,CADzD,EAEA;QACAC,SAAS,CAACmS,IAAI,CAAC1C,WAAL,CAAiBjN,SAAlB,CAHT,EAIK4I,SAJL,CAIe;UACX9H,IAAI,EAAE,MAAM6O,IAAI,CAAC1C,WAAL,CAAiB7M,MAAjB,EADD;UAEX;UACAuD,QAAQ,EAAE,MAAM,KAAKyN,cAAL,CAAoB,KAApB;QAHL,CAJf;MASH,CAXD,MAYK;QACD,KAAKA,cAAL,CAAoB,KAApB;MACH;IACJ,CAjBD,MAkBK;MACD,KAAKA,cAAL,CAAoB,KAApB;;MACAzB,IAAI,EAAE1C,WAAN,EAAmB7M,MAAnB;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACI4Q,SAAS,CAACrB,IAAD,EAAO;IACZA,IAAI,CAACrF,UAAL,GAAkB,KAAKmF,eAAL,KAAyB,KAAKF,mBAA9B,GAAoD7N,SAAtE;IACAiO,IAAI,CAACpF,SAAL,GAAiB,KAAK4F,GAAtB;;IACA,KAAKkB,iBAAL,CAAuB1B,IAAvB;;IACAA,IAAI,CAACjF,cAAL,CAAoB,KAAKsE,SAAL,IAAkB,SAAtC;;IACA,KAAKoC,cAAL,CAAoB,IAApB;EACH;EACD;;;EACAC,iBAAiB,CAAC1B,IAAD,EAAO;IACpB,IAAIA,IAAI,CAACxE,YAAT,EAAuB;MACnB,IAAIC,KAAK,GAAG,CAAZ;MACA,IAAId,UAAU,GAAGqF,IAAI,CAACrF,UAAtB;;MACA,OAAOA,UAAP,EAAmB;QACfc,KAAK;QACLd,UAAU,GAAGA,UAAU,CAACA,UAAxB;MACH;;MACDqF,IAAI,CAACxE,YAAL,CAAkBC,KAAlB;IACH;EACJ,CAzOqB,CA0OtB;;;EACAgG,cAAc,CAACE,MAAD,EAAS;IACnB,KAAK3C,SAAL,GAAiB2C,MAAjB;IACA,KAAK3C,SAAL,GAAiB,KAAKO,UAAL,CAAgBrG,IAAhB,EAAjB,GAA0C,KAAKuG,UAAL,CAAgBvG,IAAhB,EAA1C;;IACA,IAAI,KAAK4G,eAAL,EAAJ,EAA4B;MACxB,KAAKjB,iBAAL,CAAuB3J,eAAvB,CAAuCyM,MAAvC;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACId,cAAc,CAACb,IAAD,EAAO;IACjB,IAAI,CAAC,KAAKjB,WAAV,EAAuB;MACnB,MAAM6C,MAAM,GAAG,KAAKC,iBAAL,CAAuB7B,IAAvB,CAAf;;MACA,KAAK8B,qBAAL,CAA2B9B,IAA3B,EAAiC4B,MAAM,CAACZ,gBAAxC;;MACA,KAAKjC,WAAL,GAAmB,KAAKL,QAAL,CAAcqD,MAAd,CAAqBH,MAArB,CAAnB,CAHmB,CAInB;MACA;MACA;;MACA,KAAK7C,WAAL,CAAiBiD,aAAjB,GAAiC/I,SAAjC;IACH;;IACD,OAAO,KAAK8F,WAAZ;EACH;EACD;AACJ;AACA;AACA;;;EACI8C,iBAAiB,CAAC7B,IAAD,EAAO;IACpB,OAAO,IAAI9Q,aAAJ,CAAkB;MACrB8R,gBAAgB,EAAE,KAAKtC,QAAL,CACbuD,QADa,GAEbC,mBAFa,CAEO,KAAKvD,QAFZ,EAGb2C,kBAHa,GAIba,iBAJa,GAKbC,qBALa,CAKS,sCALT,CADG;MAOrB1L,aAAa,EAAEsJ,IAAI,CAACtJ,aAAL,IAAsB,kCAPhB;MAQrBuB,UAAU,EAAE+H,IAAI,CAACzI,iBARI;MASrBqH,cAAc,EAAE,KAAKe,eAAL,EATK;MAUrB/E,SAAS,EAAE,KAAKkE;IAVK,CAAlB,CAAP;EAYH;EACD;AACJ;AACA;AACA;AACA;;;EACIgD,qBAAqB,CAAC9B,IAAD,EAAOiC,QAAP,EAAiB;IAClC,IAAIjC,IAAI,CAAChI,kBAAT,EAA6B;MACzBiK,QAAQ,CAACI,eAAT,CAAyBpJ,SAAzB,CAAmCqJ,MAAM,IAAI;QACzC,MAAMlG,IAAI,GAAGkG,MAAM,CAACC,cAAP,CAAsBC,QAAtB,KAAmC,OAAnC,GAA6C,OAA7C,GAAuD,QAApE;QACA,MAAMnG,IAAI,GAAGiG,MAAM,CAACC,cAAP,CAAsBE,QAAtB,KAAmC,KAAnC,GAA2C,OAA3C,GAAqD,OAAlE,CAFyC,CAGzC;QACA;QACA;;QACA,IAAI,KAAK5L,OAAT,EAAkB;UACd,KAAKA,OAAL,CAAa6L,GAAb,CAAiB,MAAM1C,IAAI,CAAChI,kBAAL,CAAwBoE,IAAxB,EAA8BC,IAA9B,CAAvB;QACH,CAFD,MAGK;UACD2D,IAAI,CAAChI,kBAAL,CAAwBoE,IAAxB,EAA8BC,IAA9B;QACH;MACJ,CAZD;IAaH;EACJ;EACD;AACJ;AACA;AACA;AACA;;;EACI4E,YAAY,CAACjB,IAAD,EAAOgB,gBAAP,EAAyB;IACjC,IAAI,CAAC2B,OAAD,EAAUC,eAAV,IAA6B5C,IAAI,CAACxJ,SAAL,KAAmB,QAAnB,GAA8B,CAAC,KAAD,EAAQ,OAAR,CAA9B,GAAiD,CAAC,OAAD,EAAU,KAAV,CAAlF;IACA,IAAI,CAACiM,QAAD,EAAWI,gBAAX,IAA+B7C,IAAI,CAACvJ,SAAL,KAAmB,OAAnB,GAA6B,CAAC,QAAD,EAAW,KAAX,CAA7B,GAAiD,CAAC,KAAD,EAAQ,QAAR,CAApF;IACA,IAAI,CAACqM,OAAD,EAAUC,eAAV,IAA6B,CAACN,QAAD,EAAWI,gBAAX,CAAjC;IACA,IAAI,CAACL,QAAD,EAAWQ,gBAAX,IAA+B,CAACL,OAAD,EAAUC,eAAV,CAAnC;IACA,IAAIK,OAAO,GAAG,CAAd;;IACA,IAAI,KAAKnD,eAAL,EAAJ,EAA4B;MACxB;MACA;MACAkD,gBAAgB,GAAGL,OAAO,GAAG3C,IAAI,CAACxJ,SAAL,KAAmB,QAAnB,GAA8B,OAA9B,GAAwC,KAArE;MACAoM,eAAe,GAAGJ,QAAQ,GAAGG,OAAO,KAAK,KAAZ,GAAoB,OAApB,GAA8B,KAA3D;;MACA,IAAI,KAAK/C,mBAAT,EAA8B;QAC1B,IAAI,KAAKsD,mBAAL,IAA4B,IAAhC,EAAsC;UAClC,MAAMC,SAAS,GAAG,KAAKvD,mBAAL,CAAyBvG,KAAzB,CAA+B6B,KAAjD;UACA,KAAKgI,mBAAL,GAA2BC,SAAS,GAAGA,SAAS,CAACxP,eAAV,GAA4ByP,SAA/B,GAA2C,CAA/E;QACH;;QACDH,OAAO,GAAGR,QAAQ,KAAK,QAAb,GAAwB,KAAKS,mBAA7B,GAAmD,CAAC,KAAKA,mBAAnE;MACH;IACJ,CAZD,MAaK,IAAI,CAAClD,IAAI,CAACzJ,cAAV,EAA0B;MAC3BuM,OAAO,GAAGL,QAAQ,KAAK,KAAb,GAAqB,QAArB,GAAgC,KAA1C;MACAM,eAAe,GAAGF,gBAAgB,KAAK,KAArB,GAA6B,QAA7B,GAAwC,KAA1D;IACH;;IACD7B,gBAAgB,CAACqC,aAAjB,CAA+B,CAC3B;MAAEV,OAAF;MAAWG,OAAX;MAAoBN,QAApB;MAA8BC,QAA9B;MAAwCQ;IAAxC,CAD2B,EAE3B;MAAEN,OAAO,EAAEC,eAAX;MAA4BE,OAA5B;MAAqCN,QAAQ,EAAEQ,gBAA/C;MAAiEP,QAAjE;MAA2EQ;IAA3E,CAF2B,EAG3B;MACIN,OADJ;MAEIG,OAAO,EAAEC,eAFb;MAGIP,QAHJ;MAIIC,QAAQ,EAAEI,gBAJd;MAKII,OAAO,EAAE,CAACA;IALd,CAH2B,EAU3B;MACIN,OAAO,EAAEC,eADb;MAEIE,OAAO,EAAEC,eAFb;MAGIP,QAAQ,EAAEQ,gBAHd;MAIIP,QAAQ,EAAEI,gBAJd;MAKII,OAAO,EAAE,CAACA;IALd,CAV2B,CAA/B;EAkBH;EACD;;;EACA7B,mBAAmB,GAAG;IAClB,MAAMkC,QAAQ,GAAG,KAAKvE,WAAL,CAAiBwE,aAAjB,EAAjB;;IACA,MAAMC,WAAW,GAAG,KAAKzE,WAAL,CAAiByE,WAAjB,EAApB;;IACA,MAAMC,WAAW,GAAG,KAAK7D,mBAAL,GAA2B,KAAKA,mBAAL,CAAyBjI,MAApD,GAA6DnK,EAAE,EAAnF;IACA,MAAMkW,KAAK,GAAG,KAAK9D,mBAAL,GACR,KAAKA,mBAAL,CAAyB1M,QAAzB,GAAoCkG,IAApC,CAAyCtL,MAAM,CAAC6V,MAAM,IAAIA,MAAM,KAAK,KAAK9E,iBAA3B,CAA/C,EAA8F/Q,MAAM,CAAC,MAAM,KAAKkR,SAAZ,CAApG,CADQ,GAERxR,EAAE,EAFR;IAGA,OAAOD,KAAK,CAAC+V,QAAD,EAAWG,WAAX,EAAwBC,KAAxB,EAA+BF,WAA/B,CAAZ;EACH;EACD;;;EACAI,gBAAgB,CAACxP,KAAD,EAAQ;IACpB,IAAI,CAAC3I,+BAA+B,CAAC2I,KAAD,CAApC,EAA6C;MACzC;MACA;MACA,KAAKiL,SAAL,GAAiBjL,KAAK,CAACyP,MAAN,KAAiB,CAAjB,GAAqB,OAArB,GAA+B9R,SAAhD,CAHyC,CAIzC;MACA;MACA;;MACA,IAAI,KAAK+N,eAAL,EAAJ,EAA4B;QACxB1L,KAAK,CAACC,cAAN;MACH;IACJ;EACJ;EACD;;;EACAoG,cAAc,CAACrG,KAAD,EAAQ;IAClB,MAAMsG,OAAO,GAAGtG,KAAK,CAACsG,OAAtB,CADkB,CAElB;;IACA,IAAIA,OAAO,KAAKzO,KAAZ,IAAqByO,OAAO,KAAKxO,KAArC,EAA4C;MACxC,KAAKmT,SAAL,GAAiB,UAAjB;IACH;;IACD,IAAI,KAAKS,eAAL,OACEpF,OAAO,KAAK7O,WAAZ,IAA2B,KAAK2U,GAAL,KAAa,KAAzC,IACI9F,OAAO,KAAK5O,UAAZ,IAA0B,KAAK0U,GAAL,KAAa,KAF5C,CAAJ,EAEyD;MACrD,KAAKnB,SAAL,GAAiB,UAAjB;MACA,KAAKsB,QAAL;IACH;EACJ;EACD;;;EACAmD,YAAY,CAAC1P,KAAD,EAAQ;IAChB,IAAI,KAAK0L,eAAL,EAAJ,EAA4B;MACxB;MACA1L,KAAK,CAACE,eAAN;MACA,KAAKqM,QAAL;IACH,CAJD,MAKK;MACD,KAAKF,UAAL;IACH;EACJ;EACD;;;EACAJ,YAAY,GAAG;IACX;IACA,IAAI,CAAC,KAAKP,eAAL,EAAD,IAA2B,CAAC,KAAKF,mBAArC,EAA0D;MACtD;IACH;;IACD,KAAKV,kBAAL,GAA0B,KAAKU,mBAAL,CACrB1M,QADqB,GAEtB;IACA;IACA;IAJsB,CAKrBkG,IALqB,CAKhBtL,MAAM,CAAC6V,MAAM,IAAIA,MAAM,KAAK,KAAK9E,iBAAhB,IAAqC,CAAC8E,MAAM,CAACzP,QAAxD,CALU,EAKyDnG,KAAK,CAAC,CAAD,EAAIN,aAAJ,CAL9D,EAMrBwL,SANqB,CAMX,MAAM;MACjB,KAAKoG,SAAL,GAAiB,OAAjB,CADiB,CAEjB;MACA;MACA;;MACA,IAAI,KAAKW,IAAL,YAAqBpJ,YAArB,IAAqC,KAAKoJ,IAAL,CAAUvD,YAAnD,EAAiE;QAC7D;QACA;QACA,KAAKuD,IAAL,CAAU1I,cAAV,CACK8B,IADL,CACUxL,IAAI,CAAC,CAAD,CADd,EACmBG,KAAK,CAAC,CAAD,EAAIN,aAAJ,CADxB,EAC4CI,SAAS,CAAC,KAAK+R,mBAAL,CAAyB1M,QAAzB,EAAD,CADrD,EAEK+F,SAFL,CAEe,MAAM,KAAK0H,QAAL,EAFrB;MAGH,CAND,MAOK;QACD,KAAKA,QAAL;MACH;IACJ,CArByB,CAA1B;EAsBH;EACD;;;EACAO,UAAU,CAAClB,IAAD,EAAO;IACb;IACA;IACA;IACA,IAAI,CAAC,KAAKxP,OAAN,IAAiB,KAAKA,OAAL,CAAa6M,WAAb,KAA6B2C,IAAI,CAAC3C,WAAvD,EAAoE;MAChE,KAAK7M,OAAL,GAAe,IAAInC,cAAJ,CAAmB2R,IAAI,CAAC3C,WAAxB,EAAqC,KAAKnN,iBAA1C,CAAf;IACH;;IACD,OAAO,KAAKM,OAAZ;EACH;;AAhbqB;;AAkb1BiO,mBAAmB,CAAClN,IAApB;EAAA,iBAAgHkN,mBAAhH,EA5iCsGtS,EA4iCtG,mBAAqJ6C,IAAI,CAACC,OAA1J,GA5iCsG9C,EA4iCtG,mBAA8KA,EAAE,CAACmJ,UAAjL,GA5iCsGnJ,EA4iCtG,mBAAwMA,EAAE,CAACwF,gBAA3M,GA5iCsGxF,EA4iCtG,mBAAwO2R,wBAAxO,GA5iCsG3R,EA4iCtG,mBAA6QwG,cAA7Q,MA5iCsGxG,EA4iCtG,mBAAwT0G,WAAxT,OA5iCsG1G,EA4iCtG,mBAA4W4C,IAAI,CAACgV,cAAjX,MA5iCsG5X,EA4iCtG,mBAA4Zb,EAAE,CAACiK,YAA/Z,GA5iCsGpJ,EA4iCtG,mBAAwbA,EAAE,CAAC6Q,MAA3b;AAAA;;AACAyB,mBAAmB,CAAC5M,IAApB,kBA7iCsG1F,EA6iCtG;EAAA,MAAoGsS,mBAApG;EAAA;EAAA;IAAA;MA7iCsGtS,EA6iCtG;QAAA,OAAoG,wBAApG;MAAA;QAAA,OAAoG,4BAApG;MAAA;QAAA,OAAoG,0BAApG;MAAA;IAAA;;IAAA;MA7iCsGA,EA6iCtG;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;AAAA;;AACA;EAAA,mDA9iCsGA,EA8iCtG,mBAA2FsS,mBAA3F,EAA4H,CAAC;IACjH3M,IAAI,EAAEzF,SAD2G;IAEjH4F,IAAI,EAAE,CAAC;MACC4D,IAAI,EAAE;QACF,wBAAwB,sBADtB;QAEF,wBAAwB,kBAFtB;QAGF,wBAAwB,gCAHtB;QAIF,WAAW,sBAJT;QAKF,eAAe,0BALb;QAMF,aAAa;MANX;IADP,CAAD;EAF2G,CAAD,CAA5H,EAY4B,YAAY;IAAE,OAAO,CAAC;MAAE/D,IAAI,EAAE9C,IAAI,CAACC;IAAb,CAAD,EAAyB;MAAE6C,IAAI,EAAE3F,EAAE,CAACmJ;IAAX,CAAzB,EAAkD;MAAExD,IAAI,EAAE3F,EAAE,CAACwF;IAAX,CAAlD,EAAiF;MAAEG,IAAI,EAAEC,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9IF,IAAI,EAAExF,MADwI;QAE9I2F,IAAI,EAAE,CAAC6L,wBAAD;MAFwI,CAAD;IAA/B,CAAjF,EAG3B;MAAEhM,IAAI,EAAEC,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClCF,IAAI,EAAExF,MAD4B;QAElC2F,IAAI,EAAE,CAACU,cAAD;MAF4B,CAAD,EAGlC;QACCb,IAAI,EAAEpF;MADP,CAHkC;IAA/B,CAH2B,EAQ3B;MAAEoF,IAAI,EAAEe,WAAR;MAAqBb,UAAU,EAAE,CAAC;QACpCF,IAAI,EAAEpF;MAD8B,CAAD,EAEpC;QACCoF,IAAI,EAAE3E;MADP,CAFoC;IAAjC,CAR2B,EAY3B;MAAE2E,IAAI,EAAE/C,IAAI,CAACgV,cAAb;MAA6B/R,UAAU,EAAE,CAAC;QAC5CF,IAAI,EAAEpF;MADsC,CAAD;IAAzC,CAZ2B,EAc3B;MAAEoF,IAAI,EAAExG,EAAE,CAACiK;IAAX,CAd2B,EAcA;MAAEzD,IAAI,EAAE3F,EAAE,CAAC6Q;IAAX,CAdA,CAAP;EAc8B,CA1BxE,EA0B0F;IAAE+C,4BAA4B,EAAE,CAAC;MAC3GjO,IAAI,EAAEnF,KADqG;MAE3GsF,IAAI,EAAE,CAAC,sBAAD;IAFqG,CAAD,CAAhC;IAG1E+N,IAAI,EAAE,CAAC;MACPlO,IAAI,EAAEnF,KADC;MAEPsF,IAAI,EAAE,CAAC,mBAAD;IAFC,CAAD,CAHoE;IAM1EkP,QAAQ,EAAE,CAAC;MACXrP,IAAI,EAAEnF,KADK;MAEXsF,IAAI,EAAE,CAAC,oBAAD;IAFK,CAAD,CANgE;IAS1EqN,YAAY,EAAE,CAAC;MACfxN,IAAI,EAAEnF,KADS;MAEfsF,IAAI,EAAE,CAAC,4BAAD;IAFS,CAAD,CAT4D;IAY1EsN,UAAU,EAAE,CAAC;MACbzN,IAAI,EAAE5E;IADO,CAAD,CAZ8D;IAc1EsS,UAAU,EAAE,CAAC;MACb1N,IAAI,EAAE5E;IADO,CAAD,CAd8D;IAgB1EuS,UAAU,EAAE,CAAC;MACb3N,IAAI,EAAE5E;IADO,CAAD,CAhB8D;IAkB1EwS,WAAW,EAAE,CAAC;MACd5N,IAAI,EAAE5E;IADQ,CAAD;EAlB6D,CA1B1F;AAAA;AA+CA;;;AACA,MAAM8W,cAAN,SAA6BvF,mBAA7B,CAAiD;;AAEjDuF,cAAc,CAACzS,IAAf;EAAA;EAAA;IAAA,sEAhmCsGpF,EAgmCtG,uBAA2G6X,cAA3G,SAA2GA,cAA3G;EAAA;AAAA;;AACAA,cAAc,CAACnS,IAAf,kBAjmCsG1F,EAimCtG;EAAA,MAA+F6X,cAA/F;EAAA;EAAA;EAAA;EAAA,WAjmCsG7X,EAimCtG;AAAA;;AACA;EAAA,mDAlmCsGA,EAkmCtG,mBAA2F6X,cAA3F,EAAuH,CAAC;IAC5GlS,IAAI,EAAEzF,SADsG;IAE5G4F,IAAI,EAAE,CAAC;MACCI,QAAQ,EAAG,6CADZ;MAECwD,IAAI,EAAE;QACF,SAAS;MADP,CAFP;MAKCF,QAAQ,EAAE;IALX,CAAD;EAFsG,CAAD,CAAvH;AAAA;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMsO,aAAN,CAAoB;;AAEpBA,aAAa,CAAC1S,IAAd;EAAA,iBAA0G0S,aAA1G;AAAA;;AACAA,aAAa,CAACC,IAAd,kBAvnCsG/X,EAunCtG;EAAA,MAA2G8X,aAA3G;EAAA,eAAyI1G,OAAzI,EAAkJ1K,WAAlJ,EAA+JmR,cAA/J,EAA+K9R,cAA/K;EAAA,UAA0MzD,YAA1M,EAAwNI,eAAxN,EAAyOC,eAAzO,EAA0PK,aAA1P;EAAA,UAAoRE,mBAApR,EACQR,eADR,EAEQ0O,OAFR,EAGQ1K,WAHR,EAIQmR,cAJR,EAKQ9R,cALR;AAAA;AAMA+R,aAAa,CAACE,IAAd,kBA7nCsGhY,EA6nCtG;EAAA,WAAqI,CAACgS,yCAAD,CAArI;EAAA,UAA4L1P,YAA5L,EAA0MI,eAA1M,EAA2NC,eAA3N,EAA4OK,aAA5O,EAA2PE,mBAA3P,EACQR,eADR;AAAA;;AAEA;EAAA,mDA/nCsG1C,EA+nCtG,mBAA2F8X,aAA3F,EAAsH,CAAC;IAC3GnS,IAAI,EAAE1E,QADqG;IAE3G6E,IAAI,EAAE,CAAC;MACCmS,OAAO,EAAE,CAAC3V,YAAD,EAAeI,eAAf,EAAgCC,eAAhC,EAAiDK,aAAjD,CADV;MAECkV,OAAO,EAAE,CACLhV,mBADK,EAELR,eAFK,EAGL0O,OAHK,EAIL1K,WAJK,EAKLmR,cALK,EAML9R,cANK,CAFV;MAUCoS,YAAY,EAAE,CAAC/G,OAAD,EAAU1K,WAAV,EAAuBmR,cAAvB,EAAuC9R,cAAvC,CAVf;MAWCI,SAAS,EAAE,CAAC6L,yCAAD;IAXZ,CAAD;EAFqG,CAAD,CAAtH;AAAA;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASxO,gBAAT,EAA2BwG,wBAA3B,EAAqDxD,cAArD,EAAqEmL,wBAArE,EAA+FP,OAA/F,EAAwGrL,cAAxG,EAAwHW,WAAxH,EAAqIoR,aAArI,EAAoJD,cAApJ,EAAoKpN,YAApK,EAAkLhH,mBAAlL,EAAuM6O,mBAAvM,EAA4N/O,WAA5N,EAAyOJ,iBAAzO,EAA4PC,aAA5P"}, "metadata": {}, "sourceType": "module"}