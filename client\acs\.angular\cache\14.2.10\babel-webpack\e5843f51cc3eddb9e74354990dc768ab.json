{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { isDate } from '../util/isDate';\nimport { Subscriber } from '../Subscriber';\nimport { Notification } from '../Notification';\nexport function delay(delay, scheduler = async) {\n  const absoluteDelay = isDate(delay);\n  const delayFor = absoluteDelay ? +delay - scheduler.now() : Math.abs(delay);\n  return source => source.lift(new DelayOperator(delayFor, scheduler));\n}\n\nclass DelayOperator {\n  constructor(delay, scheduler) {\n    this.delay = delay;\n    this.scheduler = scheduler;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new DelaySubscriber(subscriber, this.delay, this.scheduler));\n  }\n\n}\n\nclass DelaySubscriber extends Subscriber {\n  constructor(destination, delay, scheduler) {\n    super(destination);\n    this.delay = delay;\n    this.scheduler = scheduler;\n    this.queue = [];\n    this.active = false;\n    this.errored = false;\n  }\n\n  static dispatch(state) {\n    const source = state.source;\n    const queue = source.queue;\n    const scheduler = state.scheduler;\n    const destination = state.destination;\n\n    while (queue.length > 0 && queue[0].time - scheduler.now() <= 0) {\n      queue.shift().notification.observe(destination);\n    }\n\n    if (queue.length > 0) {\n      const delay = Math.max(0, queue[0].time - scheduler.now());\n      this.schedule(state, delay);\n    } else {\n      this.unsubscribe();\n      source.active = false;\n    }\n  }\n\n  _schedule(scheduler) {\n    this.active = true;\n    const destination = this.destination;\n    destination.add(scheduler.schedule(DelaySubscriber.dispatch, this.delay, {\n      source: this,\n      destination: this.destination,\n      scheduler: scheduler\n    }));\n  }\n\n  scheduleNotification(notification) {\n    if (this.errored === true) {\n      return;\n    }\n\n    const scheduler = this.scheduler;\n    const message = new DelayMessage(scheduler.now() + this.delay, notification);\n    this.queue.push(message);\n\n    if (this.active === false) {\n      this._schedule(scheduler);\n    }\n  }\n\n  _next(value) {\n    this.scheduleNotification(Notification.createNext(value));\n  }\n\n  _error(err) {\n    this.errored = true;\n    this.queue = [];\n    this.destination.error(err);\n    this.unsubscribe();\n  }\n\n  _complete() {\n    this.scheduleNotification(Notification.createComplete());\n    this.unsubscribe();\n  }\n\n}\n\nclass DelayMessage {\n  constructor(time, notification) {\n    this.time = time;\n    this.notification = notification;\n  }\n\n}", "map": {"version": 3, "names": ["async", "isDate", "Subscriber", "Notification", "delay", "scheduler", "absoluteDelay", "delayFor", "now", "Math", "abs", "source", "lift", "DelayOperator", "constructor", "call", "subscriber", "subscribe", "DelaySubscriber", "destination", "queue", "active", "errored", "dispatch", "state", "length", "time", "shift", "notification", "observe", "max", "schedule", "unsubscribe", "_schedule", "add", "scheduleNotification", "message", "DelayMessage", "push", "_next", "value", "createNext", "_error", "err", "error", "_complete", "createComplete"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/delay.js"], "sourcesContent": ["import { async } from '../scheduler/async';\nimport { isDate } from '../util/isDate';\nimport { Subscriber } from '../Subscriber';\nimport { Notification } from '../Notification';\nexport function delay(delay, scheduler = async) {\n    const absoluteDelay = isDate(delay);\n    const delayFor = absoluteDelay ? (+delay - scheduler.now()) : Math.abs(delay);\n    return (source) => source.lift(new DelayOperator(delayFor, scheduler));\n}\nclass DelayOperator {\n    constructor(delay, scheduler) {\n        this.delay = delay;\n        this.scheduler = scheduler;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new DelaySubscriber(subscriber, this.delay, this.scheduler));\n    }\n}\nclass DelaySubscriber extends Subscriber {\n    constructor(destination, delay, scheduler) {\n        super(destination);\n        this.delay = delay;\n        this.scheduler = scheduler;\n        this.queue = [];\n        this.active = false;\n        this.errored = false;\n    }\n    static dispatch(state) {\n        const source = state.source;\n        const queue = source.queue;\n        const scheduler = state.scheduler;\n        const destination = state.destination;\n        while (queue.length > 0 && (queue[0].time - scheduler.now()) <= 0) {\n            queue.shift().notification.observe(destination);\n        }\n        if (queue.length > 0) {\n            const delay = Math.max(0, queue[0].time - scheduler.now());\n            this.schedule(state, delay);\n        }\n        else {\n            this.unsubscribe();\n            source.active = false;\n        }\n    }\n    _schedule(scheduler) {\n        this.active = true;\n        const destination = this.destination;\n        destination.add(scheduler.schedule(DelaySubscriber.dispatch, this.delay, {\n            source: this, destination: this.destination, scheduler: scheduler\n        }));\n    }\n    scheduleNotification(notification) {\n        if (this.errored === true) {\n            return;\n        }\n        const scheduler = this.scheduler;\n        const message = new DelayMessage(scheduler.now() + this.delay, notification);\n        this.queue.push(message);\n        if (this.active === false) {\n            this._schedule(scheduler);\n        }\n    }\n    _next(value) {\n        this.scheduleNotification(Notification.createNext(value));\n    }\n    _error(err) {\n        this.errored = true;\n        this.queue = [];\n        this.destination.error(err);\n        this.unsubscribe();\n    }\n    _complete() {\n        this.scheduleNotification(Notification.createComplete());\n        this.unsubscribe();\n    }\n}\nclass DelayMessage {\n    constructor(time, notification) {\n        this.time = time;\n        this.notification = notification;\n    }\n}\n"], "mappings": "AAAA,SAASA,KAAT,QAAsB,oBAAtB;AACA,SAASC,MAAT,QAAuB,gBAAvB;AACA,SAASC,UAAT,QAA2B,eAA3B;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,SAASC,KAAT,CAAeA,KAAf,EAAsBC,SAAS,GAAGL,KAAlC,EAAyC;EAC5C,MAAMM,aAAa,GAAGL,MAAM,CAACG,KAAD,CAA5B;EACA,MAAMG,QAAQ,GAAGD,aAAa,GAAI,CAACF,KAAD,GAASC,SAAS,CAACG,GAAV,EAAb,GAAgCC,IAAI,CAACC,GAAL,CAASN,KAAT,CAA9D;EACA,OAAQO,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,aAAJ,CAAkBN,QAAlB,EAA4BF,SAA5B,CAAZ,CAAnB;AACH;;AACD,MAAMQ,aAAN,CAAoB;EAChBC,WAAW,CAACV,KAAD,EAAQC,SAAR,EAAmB;IAC1B,KAAKD,KAAL,GAAaA,KAAb;IACA,KAAKC,SAAL,GAAiBA,SAAjB;EACH;;EACDU,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,eAAJ,CAAoBF,UAApB,EAAgC,KAAKZ,KAArC,EAA4C,KAAKC,SAAjD,CAAjB,CAAP;EACH;;AAPe;;AASpB,MAAMa,eAAN,SAA8BhB,UAA9B,CAAyC;EACrCY,WAAW,CAACK,WAAD,EAAcf,KAAd,EAAqBC,SAArB,EAAgC;IACvC,MAAMc,WAAN;IACA,KAAKf,KAAL,GAAaA,KAAb;IACA,KAAKC,SAAL,GAAiBA,SAAjB;IACA,KAAKe,KAAL,GAAa,EAAb;IACA,KAAKC,MAAL,GAAc,KAAd;IACA,KAAKC,OAAL,GAAe,KAAf;EACH;;EACc,OAARC,QAAQ,CAACC,KAAD,EAAQ;IACnB,MAAMb,MAAM,GAAGa,KAAK,CAACb,MAArB;IACA,MAAMS,KAAK,GAAGT,MAAM,CAACS,KAArB;IACA,MAAMf,SAAS,GAAGmB,KAAK,CAACnB,SAAxB;IACA,MAAMc,WAAW,GAAGK,KAAK,CAACL,WAA1B;;IACA,OAAOC,KAAK,CAACK,MAAN,GAAe,CAAf,IAAqBL,KAAK,CAAC,CAAD,CAAL,CAASM,IAAT,GAAgBrB,SAAS,CAACG,GAAV,EAAjB,IAAqC,CAAhE,EAAmE;MAC/DY,KAAK,CAACO,KAAN,GAAcC,YAAd,CAA2BC,OAA3B,CAAmCV,WAAnC;IACH;;IACD,IAAIC,KAAK,CAACK,MAAN,GAAe,CAAnB,EAAsB;MAClB,MAAMrB,KAAK,GAAGK,IAAI,CAACqB,GAAL,CAAS,CAAT,EAAYV,KAAK,CAAC,CAAD,CAAL,CAASM,IAAT,GAAgBrB,SAAS,CAACG,GAAV,EAA5B,CAAd;MACA,KAAKuB,QAAL,CAAcP,KAAd,EAAqBpB,KAArB;IACH,CAHD,MAIK;MACD,KAAK4B,WAAL;MACArB,MAAM,CAACU,MAAP,GAAgB,KAAhB;IACH;EACJ;;EACDY,SAAS,CAAC5B,SAAD,EAAY;IACjB,KAAKgB,MAAL,GAAc,IAAd;IACA,MAAMF,WAAW,GAAG,KAAKA,WAAzB;IACAA,WAAW,CAACe,GAAZ,CAAgB7B,SAAS,CAAC0B,QAAV,CAAmBb,eAAe,CAACK,QAAnC,EAA6C,KAAKnB,KAAlD,EAAyD;MACrEO,MAAM,EAAE,IAD6D;MACvDQ,WAAW,EAAE,KAAKA,WADqC;MACxBd,SAAS,EAAEA;IADa,CAAzD,CAAhB;EAGH;;EACD8B,oBAAoB,CAACP,YAAD,EAAe;IAC/B,IAAI,KAAKN,OAAL,KAAiB,IAArB,EAA2B;MACvB;IACH;;IACD,MAAMjB,SAAS,GAAG,KAAKA,SAAvB;IACA,MAAM+B,OAAO,GAAG,IAAIC,YAAJ,CAAiBhC,SAAS,CAACG,GAAV,KAAkB,KAAKJ,KAAxC,EAA+CwB,YAA/C,CAAhB;IACA,KAAKR,KAAL,CAAWkB,IAAX,CAAgBF,OAAhB;;IACA,IAAI,KAAKf,MAAL,KAAgB,KAApB,EAA2B;MACvB,KAAKY,SAAL,CAAe5B,SAAf;IACH;EACJ;;EACDkC,KAAK,CAACC,KAAD,EAAQ;IACT,KAAKL,oBAAL,CAA0BhC,YAAY,CAACsC,UAAb,CAAwBD,KAAxB,CAA1B;EACH;;EACDE,MAAM,CAACC,GAAD,EAAM;IACR,KAAKrB,OAAL,GAAe,IAAf;IACA,KAAKF,KAAL,GAAa,EAAb;IACA,KAAKD,WAAL,CAAiByB,KAAjB,CAAuBD,GAAvB;IACA,KAAKX,WAAL;EACH;;EACDa,SAAS,GAAG;IACR,KAAKV,oBAAL,CAA0BhC,YAAY,CAAC2C,cAAb,EAA1B;IACA,KAAKd,WAAL;EACH;;AAxDoC;;AA0DzC,MAAMK,YAAN,CAAmB;EACfvB,WAAW,CAACY,IAAD,EAAOE,YAAP,EAAqB;IAC5B,KAAKF,IAAL,GAAYA,IAAZ;IACA,KAAKE,YAAL,GAAoBA,YAApB;EACH;;AAJc"}, "metadata": {}, "sourceType": "module"}