{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./product-detail.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./product-detail.component.css?ngResource\";\nimport { Component, ViewChild, Input, ChangeDetectorRef, Renderer2 } from \"@angular/core\";\nimport { KitsService } from \"../../Services/kits.service\";\nimport { ActivatedRoute } from \"@angular/router\";\nimport { LoaderService } from '../../../../Modules/Core/Services/Common/loader.service';\nimport { FormArray, FormControl, FormGroup, Validators } from \"@angular/forms\";\nimport { KitCodes } from \"src/app/Modules/Shared/Enums/kits-minutebook.enum\";\nimport { ProductDetailControls } from \"src/app/Modules/Shared/Enums/product-detail-controls.enum\";\nimport { ProductDetailMasterDataModel } from \"src/app/Modules/Shared/Models/KitsModel/product-detail-master-data.model\";\nimport { EditRequest } from \"../../../Shared/Models/KitsModel/EditRequest\";\nimport { Util } from \"src/app/Modules/Shared/helper/Util\";\nimport { CustomSharedValidations } from \"src/app/Modules/Shared/functions/custom-validations\";\nimport { ToastrService } from \"ngx-toastr\";\nimport { ProductDetail } from \"src/app/Modules/Shared/Models/KitsModel/ProductDetail\";\nimport { StringValue } from \"src/app/Modules/Shared/Enums/string-values.enum\";\nimport { ComponentMessageService } from \"src/app/Modules/Core/Services/Common/component-message.service\";\nimport { SimpleProductService } from \"src/app/Modules/Core/Services/Common/simple-product.service\";\nimport { PageTitleService } from \"src/app/Modules/Shared/Services/Common/page-title.service\";\nlet ProductDetailComponent = class ProductDetailComponent {\n  constructor(render, kitsServiceApi, cdr, route, isLoading, toaster, productService, ComponentMessags, pageTitleService) {\n    this.render = render;\n    this.kitsServiceApi = kitsServiceApi;\n    this.cdr = cdr;\n    this.route = route;\n    this.isLoading = isLoading;\n    this.toaster = toaster;\n    this.productService = productService;\n    this.ComponentMessags = ComponentMessags;\n    this.pageTitleService = pageTitleService;\n    this.selectKitFlag = 0;\n    this.enableZoom = true;\n    this.isMoveEventCalling = false;\n    this.corporationTypeCode = [];\n    this.bulkPricing = [];\n    this.multiCertificates = [];\n    this.CustomizeLegendsFormArray = new FormArray([]);\n    this.AdditionalLegendsFormArray = new FormArray([]);\n    this.CloseCorpLegendsFormArray = new FormArray([]);\n    this.OtherLegendsFormArray = new FormArray([]);\n    this.AllKitTypes = [{\n      name: \"Corporate Kit\",\n      value: \"CK\"\n    }, {\n      name: \"LLC Kit\",\n      value: \"LK\"\n    }, {\n      name: \"Partnership Kit\",\n      value: \"PS\"\n    }, {\n      name: \"Minute Books\",\n      value: \"M\"\n    }];\n    this.showLegend = false;\n    this.buttonText = StringValue.addToOrder;\n    this.savedOrderCopy = {};\n    this.savedCustomLegends = [];\n    this.price = 0;\n    this.isAdd = true;\n    this.orderNumber = 0;\n    this.kitLineNumber = 0;\n    this.productNumber = 0;\n    this.showCertsAndBylaws = false;\n    this.kitsByCategoryCode = [];\n    this.product = {};\n    this.availableKitTypes = [];\n    this.certificateColors = [];\n    this.masterData = new ProductDetailMasterDataModel();\n    this.colors = [];\n    this.UpSellingKitsData = [];\n    this.UpSellingKitsDetails = [];\n    this.IsUpSelling = false;\n    this.OtherLegendPrice = 0;\n    this.AdditionalLegendPrice = 0;\n    this.CustomizedLegendPrice = 0;\n    this.ProductsNotHavingCertColors = [\"DR\", \"DP\"];\n    this.multiClassResult = [];\n    this.showMultiCerts = false;\n    this.UpsellingProductCode = \"CK\"; //Defauled to Corporate kit\n\n    this.Form1 = new FormGroup({\n      selectedKit: new FormControl(null),\n      KitType: new FormControl(null),\n      FilingState: new FormControl(null),\n      EntityName: new FormControl(null),\n      TotalShares: new FormControl({\n        value: null,\n        disabled: true\n      }),\n      ExpirationDateData: new FormControl({\n        value: null,\n        disabled: true\n      }),\n      filingStatusData: new FormControl({\n        value: true,\n        disabled: true\n      }),\n      FormationType: new FormControl(null),\n      Color: new FormControl(null),\n      BylawsAndMinutes: new FormControl(null),\n      OperatingAgreements: new FormControl(null),\n      GoldStamping: new FormControl(null),\n      ProfessionalCorporation: new FormControl(null),\n      OtherProfessionalCorpType: new FormControl(null)\n    });\n    this.Form2 = new FormGroup({\n      FormationDate: new FormControl(null, CustomSharedValidations.validateFormationDate(\"FormationDate\", \"FormationMonth\", \"FormationYear\")),\n      FormationMonth: new FormControl(null, CustomSharedValidations.validateFormationDate(\"FormationDate\", \"FormationMonth\", \"FormationYear\")),\n      FormationYear: new FormControl(null, CustomSharedValidations.validateFormationDate(\"FormationDate\", \"FormationMonth\", \"FormationYear\")),\n      FilingStatus: new FormControl(null),\n      TotalAuthorizedShares: new FormControl(null),\n      MultiCerts: new FormControl(null),\n      DesignationCK: new FormControl(null),\n      // ReflectiveCerts: new FormControl(null),\n      AddLegends: new FormControl(null, [Validators.required]),\n      //NOTE: These FormArray names are used in the HTML template, to be passed on 'AddLegendsInFormArray' method... change there also if renamed here\n      AdditionalLegends: this.AdditionalLegendsFormArray,\n      CustomizedLegends: this.CustomizeLegendsFormArray,\n      //--\n      OtherLegends: this.OtherLegendsFormArray,\n      LegendValues: new FormArray([]),\n      CloseCorporationLegends: this.CloseCorpLegendsFormArray,\n      //--\n      SpecialInstructions: new FormControl(null),\n      Captcha: new FormControl(null),\n      IsCloseCorporation: new FormControl(null),\n      CloseCorpType: new FormControl(0) //ProfessionalCorporation: new FormControl(null),\n\n    });\n  }\n\n  set certificateColorComponent(certificateColorComponent) {\n    if (certificateColorComponent) {\n      this.CertificateColorComponent = certificateColorComponent;\n      this.RenderCertificateColors();\n    }\n  }\n\n  set corpTypeComponent(corpTypeComponent) {\n    if (corpTypeComponent) {\n      this.CorpTypeComponent = corpTypeComponent; //if (this.selectedKitType === KitCodes.CK || this.selectedKitType === KitCodes.PS) {\n\n      this.CorpTypeComponent.IsVisible = true;\n      this.CorpTypeComponent.CorporationTypes = this.masterData.corporationTypes; // } else {\n      //   this.CorpTypeComponent.IsVisible = false;\n      // }\n\n      this.cdr.detectChanges();\n    }\n  }\n\n  set colorComponent(colorComponent) {\n    if (colorComponent) {\n      this.ColorComponent = colorComponent; // Updating product price based on the color selection\n\n      this.ColorComponent.$OnColorSelection.subscribe(color => {\n        this.price = this.colors.find(x => x.id === color).price;\n      });\n      this.cdr.detectChanges();\n    }\n  }\n\n  set _FormRef1(FormRef) {\n    this.FormRef1 = FormRef;\n  }\n\n  set _FormRef2(FormRef) {\n    this.FormRef2 = FormRef;\n  }\n\n  set certControlsComponent(certControlsComponent) {\n    if (certControlsComponent) {\n      certControlsComponent.FormationType = this.Form1.controls.FormationType.value;\n      this.CertControlsComponent = certControlsComponent;\n\n      if (this.corporationTypeCode && this.corporationTypeCode.length > 0) {\n        certControlsComponent.getCertTypes(this.corporationTypeCode);\n      }\n    }\n\n    this.cdr.detectChanges();\n  }\n\n  getUpSellingData() {\n    setTimeout(() => {\n      this.FormRef1.nativeElement.click();\n      this.FormRef2.nativeElement.click();\n    }, 10);\n    return new Promise(res => this.UpSellingResolver = res);\n  }\n\n  set _IsUpselling(IsUpselling) {\n    this.IsUpSelling = IsUpselling;\n\n    if (IsUpselling) {\n      // Call Listing API Data\n      this.getKitListData();\n    }\n  }\n\n  ngOnInit() {\n    this.route.queryParamMap.subscribe(queryParams => {\n      this.subCategoryCode = queryParams.get(\"scc\");\n      this.categoryCode = queryParams.get(\"cc\");\n      this.kitType = queryParams.get(\"type\");\n      let orderNumber = Util.decryptData(queryParams.get(\"on\"));\n      let kitLineNumber = Util.decryptData(queryParams.get(\"kln\"));\n      let productNumber = Util.decryptData(queryParams.get(\"pn\"));\n      this.orderNumber = orderNumber ? parseInt(orderNumber, 10) : 0;\n      this.kitLineNumber = kitLineNumber ? parseInt(kitLineNumber, 10) : 0;\n      this.productNumber = productNumber ? parseInt(productNumber, 10) : 0;\n      this.kitsServiceApi.GetProductDetailData().subscribe(data => {\n        this.MapAllMasterData(data);\n\n        if (this.orderNumber && this.kitLineNumber && this.productNumber) {\n          var request = new EditRequest();\n          request.orderNumber = this.orderNumber;\n          request.kitLineNumber = this.kitLineNumber;\n          request.productNumber = this.productNumber;\n          this.kitsServiceApi.GetKitDetail(request).subscribe(data => {\n            this.isAdd = false;\n            this.subCategoryCode = data.subCategoryCode;\n            this.categoryCode = data.categoryCode;\n            this.kitType = data.productCode;\n            this.selectedKitType = data.productCode;\n            this.GetKitData();\n            this.savedOrderCopy = data;\n            this.LoadData(data);\n          });\n        } else {\n          this.GetKitData();\n        }\n      });\n    }); // Get all States from store if store is empty call states API\n\n    this.kitsServiceApi.GetStates().subscribe(States => {\n      this.masterData.states = States;\n      this.isLoading.hide();\n    });\n    this.kitsServiceApi.GetYesNo().subscribe(data => {\n      this.masterData.certificateNumbering = data;\n    });\n    this.Form2.controls.FilingStatus.valueChanges.subscribe(value => {\n      if (value) {\n        this.Form2.controls.FormationYear.reset();\n        this.Form2.controls.FormationMonth.reset();\n        this.Form2.controls.FormationDate.reset();\n        this.Form2.controls.FormationYear.disable();\n        this.Form2.controls.FormationMonth.disable();\n        this.Form2.controls.FormationDate.disable();\n      } else {\n        this.Form2.controls.FormationYear.setValidators([CustomSharedValidations.validateFormationDate(\"FormationDate\", \"FormationMonth\", \"FormationYear\")]);\n        this.Form2.controls.FormationYear.enable();\n        this.Form2.controls.FormationMonth.enable();\n        this.Form2.controls.FormationDate.enable();\n      }\n    });\n    this.Form1.controls.FormationType.valueChanges.subscribe(value => {\n      if (this.IsUpSelling) {\n        this.onCorporationTypeChange();\n      }\n    });\n\n    if (this.IsUpSelling) {\n      this.routesSubscription = this.ComponentMessags.replaySubject.subscribe(data => {\n        if (data.payload && data.payload.value && data.payload.name == 'EntityName') {\n          this.Form1.controls.EntityName.setValue(data.payload.value);\n        }\n\n        if (data.payload && data.payload.value && data.payload.name == 'Expiration_Date') {\n          if (data.payload.value == 'Invalid Date') {\n            this.Form1.controls.ExpirationDateData.setValue('');\n          } else {\n            this.Form1.controls.ExpirationDateData.setValue(data.payload.value);\n          }\n        }\n\n        if (data.payload && data.payload.value && data.payload.name == 'filingStatusData') {\n          if (data.payload.value == 'NO') {\n            this.Form1.controls.ExpirationDateData.setValue('');\n          }\n        }\n\n        if (data.payload && data.payload.value && data.payload.name == 'TotalAuthorizedShares') {\n          this.Form1.controls.TotalShares.setValue(data.payload.value);\n        }\n\n        if (data.payload && data.payload.filingModeCheck) {\n          this.ngOnDestroy();\n          this.Form1.controls.ExpirationDateData.setValue('');\n          this.Form1.controls.EntityName.setValue('');\n          this.Form1.controls.TotalShares.setValue('');\n          this.filingModeCheck = data.payload.filingModeCheck;\n        }\n      });\n    }\n  }\n\n  getBulkPricing(selectedKitType) {\n    this.bulkPricing = [];\n    this.productService.getBulkPricing(selectedKitType, this.categoryCode, 'CT', 'NA').subscribe(response => {\n      this.bulkPricing = response;\n    });\n  }\n\n  GetLegends() {\n    this.kitsServiceApi.GetLegends(this.selectedKitType, this.categoryCode).subscribe(data => {\n      this.allLegendData = data;\n      this.mapLegends(data);\n\n      if (this.onLegendLoadedResolver) {\n        this.onLegendLoadedResolver();\n      }\n    });\n  }\n\n  onCorporateSubTypeChange(value) {\n    var data = this.allLegendData;\n    this.mapLegends(data, value);\n\n    if (this.CertControlsComponent) {\n      this.CertControlsComponent.mapLegendsOnCorpSubTypeChange(value);\n    }\n\n    if (this.onLegendLoadedResolver) {\n      this.onLegendLoadedResolver();\n    }\n  }\n\n  mapLegends(data, corpSubType) {\n    var formationType;\n\n    if (this.masterData.corporationTypes) {\n      formationType = this.masterData.corporationTypes.find(x => x.value == this.Form1.controls.FormationType.value);\n    }\n\n    this.AdditionalLegendsFormArray.clear();\n    this.CustomizeLegendsFormArray.clear();\n    this.CloseCorpLegendsFormArray.clear();\n    this.OtherLegendsFormArray.clear();\n    this.masterData.closeCorporationTypes = [];\n\n    if (data.length > 0 && data.find(x => x.legendType === \"A\")) {\n      var filteredLegends = this.getFilteredLegends(data, 'A', formationType, corpSubType);\n      filteredLegends.forEach(legend => {\n        let formGroup = new FormGroup({\n          id: new FormControl(legend.productionNumber),\n          value1: new FormControl(legend.webDescriptionline),\n          value2: new FormControl(legend.webDescriptionline2),\n          price: new FormControl(legend.retailPrice),\n          isChecked: new FormControl(false)\n        });\n        this.AdditionalLegendPrice = legend.retailPrice;\n        this.AdditionalLegendsFormArray.push(formGroup);\n      });\n      if (data.length > 0 && data.find(x => x.legendType === \"C\")) var filteredLegends = this.getFilteredLegends(data, 'C', formationType, corpSubType);\n      filteredLegends.forEach(legend => {\n        let formGroup = new FormGroup({\n          id: new FormControl(legend.productionNumber),\n          value1: new FormControl(legend.webDescriptionline),\n          value2: new FormControl(legend.webDescriptionline2),\n          price: new FormControl(legend.retailPrice),\n          isChecked: new FormControl(false),\n          legendValue: new FormControl(null, legend.productionNumber == 71235 ? CustomSharedValidations.maxValue(35) : null)\n        });\n        this.CustomizedLegendPrice = legend.retailPrice;\n        this.CustomizeLegendsFormArray.push(formGroup);\n      });\n      if (data.length > 0 && data.find(x => x.legendType === \"B\")) data.find(x => x.legendType === \"B\").legends.forEach(legend => {\n        if (legend.subCategoryCode === \"CLS\") {\n          let closeCorpTypes = {\n            id: legend.productionNumber,\n            value1: legend.webDescriptionline,\n            value2: legend.webDescriptionline2,\n            price: legend.retailPrice\n          };\n          this.masterData.closeCorporationTypes.push(closeCorpTypes);\n        } else if (legend.subCategoryCode === \"MLT\") {\n          let formGroup = new FormGroup({\n            id: new FormControl(legend.productionNumber),\n            value1: new FormControl(legend.webDescriptionline),\n            value2: new FormControl(legend.webDescriptionline2),\n            price: new FormControl(legend.retailPrice),\n            isChecked: new FormControl(false),\n            legendValue: new FormControl(null, CustomSharedValidations.alphaNumeric)\n          });\n          this.CloseCorpLegendsFormArray.push(formGroup);\n        }\n      });\n\n      if (data.length > 0 && data.find(x => x.legendType === \"O\")) {\n        var filteredLegends = this.getFilteredLegends(data, 'O', formationType, corpSubType);\n        filteredLegends.forEach(legend => {\n          let formGroup = new FormGroup({\n            id: new FormControl(legend.productionNumber),\n            value1: new FormControl(legend.webDescriptionline),\n            value2: new FormControl(legend.webDescriptionline2),\n            price: new FormControl(legend.retailPrice),\n            isChecked: new FormControl(false)\n          });\n          this.OtherLegendPrice = legend.retailPrice;\n          this.OtherLegendsFormArray.push(formGroup);\n        });\n      }\n    }\n  }\n\n  getFilteredLegends(data, legendType, formationType, corpSubType) {\n    if (data.length > 0) {\n      var filteredData = data.find(x => x.legendType === legendType);\n      if (this.selectedKitType != 'CK') return filteredData.legends;\n\n      if (formationType && formationType.code != '010' && corpSubType) {\n        return filteredData.legends.filter(x => x.subCategoryCode == formationType.code && x.optionCode == corpSubType);\n      } else if (formationType && formationType.code == '010') {\n        return filteredData.legends.filter(x => x.subCategoryCode == '' && x.optionCode == '');\n      } else {\n        return [];\n      }\n    }\n  }\n\n  setParValue(data) {\n    if (data.parValueLeaveBlank === \"Y\") return \"Leave Blank\";else if (data.noParValue === \"Y\") return \"No Par\";else if (data.withoutParValue === \"W\") return \"Without Par\";else if (data.parValue != \"N\") return \"Par Value Each\";\n  }\n\n  onCorporationTypeChange() {\n    if (this.Form1.controls.FormationType.value) {\n      this.masterData.typeOfCorporations = this.masterData.allTypeOfCorporations.filter(x => x.optionCode == this.Form1.controls.FormationType.value) || [];\n\n      if (this.allLegendData) {\n        this.mapLegends(this.allLegendData);\n      }\n\n      this.setCorporationTypeCodeValue();\n\n      if (this.corporationTypeCode && this.corporationTypeCode.length > 0 && this.CertControlsComponent) {\n        this.CertControlsComponent.getCertTypes(this.corporationTypeCode);\n      }\n    } //if(this.selectedKitType != KitCodes.PS){\n\n\n    this.GetColors(true, this.selectedKitType, this.savedColor); //}\n  }\n\n  getCorporateSubTypes(selectedKitType, categoryCode, subCategoryCode) {\n    this.kitsServiceApi.GetSubCorporateType(selectedKitType, categoryCode, subCategoryCode).subscribe(data => {\n      this.masterData.allTypeOfCorporations = data.map(item => {\n        var data = {\n          id: item.corporationSubTypeCode,\n          value: item.corporationSubTypeDescription,\n          optionCode: item.corporationTypeCode\n        };\n        return data;\n      });\n\n      if (this.onTypesOfCorpLoadedResolver) {\n        this.onTypesOfCorpLoadedResolver();\n      }\n    });\n  }\n\n  LoadData(data) {\n    let onLegendLoaded = new Promise((resolve, reject) => this.onLegendLoadedResolver = resolve);\n    let onTypesOfCorpLoaded = new Promise((resolve, reject) => this.onTypesOfCorpLoadedResolver = resolve);\n    this.isAdd = false;\n    this.buttonText = StringValue.updateOrder;\n    onTypesOfCorpLoaded.then(() => {\n      this.masterData.typeOfCorporations = this.masterData.allTypeOfCorporations.filter(x => x.optionCode == data.entityType);\n    }); // Form 1\n\n    this.selectedKitType = data.productCode;\n    this.Form1.controls.FormationType.setValue(data.entityType);\n    this.Form1.controls.KitType.setValue(data.productCode);\n    this.Form1.controls.FilingState.setValue(data.filingState);\n    this.Form1.controls.EntityName.setValue(data.entityName);\n    this.Form1.controls.BylawsAndMinutes.setValue(data.bylawsAndMinutes);\n    this.Form1.controls.OperatingAgreements.setValue(data.operatingAgreements);\n    this.Form1.controls.GoldStamping.setValue(Number(data.goldStamping).toString());\n    this.Form1.controls.ProfessionalCorporation.setValue((data.professionalCorporation || \"\").trim());\n    this.Form1.controls.OtherProfessionalCorpType.setValue(data.otherProfessionalCorpType);\n    this.GetColors(data.productCode === \"CK\", data.productCode, data.color); // Form 2\n\n    if (data.formationDate) {\n      var formationDateSplit = data.formationDate.split(\"/\");\n      this.Form2.controls.FormationDate.setValue(formationDateSplit[1]);\n      this.Form2.controls.FormationMonth.setValue(formationDateSplit[0]);\n      this.Form2.controls.FormationYear.setValue(formationDateSplit[2]);\n    }\n\n    this.Form2.controls.FilingStatus.setValue(data.filingStatus);\n    this.Form2.controls.TotalAuthorizedShares.setValue(data.totalAuthorizedShares);\n    this.Form2.controls.SpecialInstructions.setValue(data.specialInstructions);\n    this.Form2.controls.IsCloseCorporation.setValue(data.isCloseCorporation);\n    this.Form2.controls.CloseCorpType.setValue(data.closeCorpType); // this.Form2.controls.ProfessionalCorporation.setValue(\n    //   (data.professionalCorporation || \"\").trim()\n    // );\n\n    let addLegendsValue = (data.additionalLegends || data.customizedLegends || data.otherLegends || []).length > 0 ? \"YES\" : \"NO\";\n    this.Form2.controls.AddLegends.setValue(addLegendsValue);\n    this.savedColor = data.color;\n    onLegendLoaded.then(() => {\n      this.mapLegends(this.allLegendData, data.professionalCorporation); // this.setSelectedLegends(data.additionalLegends);\n      // this.setCustomizeLegends(data.customizedLegends, \"CustomizedLegends\");\n\n      this.setCustomizeLegends(data.closeCorporationLegends, \"CloseCorporationLegends\"); // this.setOtherLegends(data.otherLegends);\n\n      if (data.closeCorpType) {\n        this.Form2.controls.IsCloseCorporation.setValue(true);\n      }\n    });\n    this.savedOrderCopy.certificates.length > 1 || this.savedOrderCopy.certificates[0].reflective != '' ? this.Form2.controls.MultiCerts.setValue(true) : null; // this.savedOrderCopy.reflective ? this.Form2.controls.ReflectiveCerts.setValue(this.savedOrderCopy.reflective) : this.Form2.controls.ReflectiveCerts.setValue(null)\n\n    this.getProductMultiClass(this.selectedKitType, this.categoryCode, this.subCategoryCode);\n  }\n\n  clearMultiCertificates() {\n    const valueToKeep = this.CertControlsComponent.Certificates.at(0);\n    this.CertControlsComponent.Certificates.clear();\n    this.CertControlsComponent.Certificates.push(valueToKeep);\n  }\n\n  getProductMultiClass(value, categoryCode, subCategoryCode) {\n    this.kitsServiceApi.GetProductMultiClass(value, categoryCode, subCategoryCode).subscribe(response => {\n      this.multiClassResult = response;\n    });\n  }\n\n  OnKitTypeSelection(value) {\n    this.isLoading.show();\n\n    if (this.CertControlsComponent) {\n      if (!this.isAdd) {\n        this.CertControlsComponent.savedOrderCopy = this.savedOrderCopy;\n        this.firstKitType = this.kitType;\n        let onLegendLoaded = new Promise((resolve, reject) => this.CertControlsComponent.onLegendLoadedResolver = resolve);\n        onLegendLoaded.then(() => {\n          this.CertControlsComponent.loadCertificates();\n        });\n      }\n\n      this.isLoading.hide();\n    }\n\n    if (value && this.selectedKitType != value) {\n      this.getBulkPricing(value);\n    }\n\n    if (value && this.selectKitFlag == 0 || this.selectedKitType != value) {\n      this.getProductMultiClass(value, this.categoryCode, this.subCategoryCode);\n      this.GetCorporationTypes(value, this.subCategoryCode, this.categoryCode);\n      this.getCorporateSubTypes(value, this.categoryCode, this.subCategoryCode);\n      this.GetLegends();\n      this.selectKitFlag++;\n    } else {\n      return;\n    }\n\n    if (this.selectedKitType !== value && !this.IsUpSelling) {\n      this.Form1.reset();\n      this.Form2.reset();\n\n      if (!this.isAdd && value === this.savedOrderCopy.productCode) {\n        this.LoadData(this.savedOrderCopy);\n      }\n\n      if (this.ColorComponent) {\n        this.ColorComponent.IsVisible = false;\n      }\n    }\n\n    this.selectedKitType = value;\n\n    if (value == KitCodes.PS || value == KitCodes.LK) {// if(!this.isAdd)\n      // {\n      // }      \n      //this.GetColors(false, value);\n    }\n\n    this.product = this.kitsByCategoryCode.find(obj => obj.productCode.includes(value) && obj.subCategoryCode.includes(this.subCategoryCode));\n    this.productImageUrl = this.product.productImage;\n    this.price = this.product.price;\n  }\n\n  setSelectedLegends(data) {\n    this.Form2.get(\"AdditionalLegends\")[\"controls\"].forEach(formgrp => {\n      if (data && data.includes(formgrp.controls.id.value.toString())) {\n        formgrp.controls.isChecked.setValue(true);\n      }\n    });\n  }\n\n  setCustomizeLegends(data, controlName) {\n    if (data) this.Form2.get(controlName)[\"controls\"].forEach(formgrp => {\n      let val = data.filter(x => x.productNumber == formgrp.controls.id.value.toString());\n\n      if (val.length) {\n        formgrp.controls.isChecked.setValue(true);\n\n        if (formgrp.controls.hasOwnProperty(\"legendValue\")) {\n          formgrp.controls.legendValue.setValue(val[0].legendValue);\n        }\n      }\n    });\n  }\n\n  setOtherLegends(data) {\n    this.Form2.get(\"OtherLegends\")[\"controls\"].forEach(formgrp => {\n      if (data && data.includes(formgrp.controls.id.value.toString())) {\n        formgrp.controls.isChecked.setValue(true);\n      }\n    });\n  }\n\n  GetKitData() {\n    this.isLoading.show();\n    this.kitsServiceApi.getkitsListing().subscribe(data => {\n      this.isLoading.hide(); // Get Individual Kits by CategoryCode\n\n      let kitData = data.find(obj => obj.categoryCode.includes(this.categoryCode));\n\n      if (!kitData) {\n        return;\n      }\n\n      this.kitsByCategoryCode = kitData.individualKits; // Get available Product Codes from the Available Individual Kits\n\n      let kitCodes = this.kitsByCategoryCode.map(function (obj) {\n        return obj.productCode;\n      });\n      kitCodes = kitCodes.filter((n, i) => kitCodes.indexOf(n) === i);\n      this.availableKitTypes = this.AllKitTypes.filter(obj => kitCodes.includes(obj.value)); // Need to check: individual kit of which Product code to be passed\n\n      const filteredKitCode = this.kitType ? this.kitType : kitCodes[0];\n      this.product = this.kitsByCategoryCode.find(obj => obj.productCode.includes(filteredKitCode) && obj.subCategoryCode.includes(this.subCategoryCode));\n      this.productImageUrl = this.selectedKitType ? this.product.productImage : this.product.image;\n      this.price = this.product.price;\n\n      if (this.kitType) {\n        //this.availableKitTypes.length > 0th\n        this.Form1.controls.KitType.setValue(this.kitType);\n        this.selectedKitType = this.kitType;\n        this.OnKitTypeSelection(filteredKitCode);\n      }\n\n      if (this.IsUpSelling) {// this.setDefaultValues(true);\n      }\n    });\n  }\n\n  GetCorporationTypes(productCode, categoryCode, subCategoryCode) {\n    this.kitsServiceApi.GetCorporationTypes(productCode, categoryCode, subCategoryCode).subscribe(data => {\n      this.masterData.corporationTypes = data.map(item => {\n        let icon = \"\";\n\n        switch (item.corpTypeCodeString) {\n          case \"FP\":\n            {\n              icon = \"icon-profits\";\n              break;\n            }\n\n          case \"NP\":\n            {\n              icon = \"icon-non-profit\";\n              break;\n            }\n\n          case \"PC\":\n            {\n              icon = \"icon-professional-corporation\";\n              break;\n            }\n        }\n\n        var data = {\n          value: item.corporationTypeCode,\n          name: item.corporationTypeDescription,\n          icon: icon,\n          code: item.corporationTypeCode,\n          corporationCode: item.corporationCode,\n          corpCode: item.corpCode,\n          corpTypeCode: item.corpTypeCode\n        };\n        return data;\n      });\n\n      if (!this.isAdd) {\n        this.setCorporationTypeCodeValue(); //setting designationData for certificate\n\n        if (this.corporationTypeCode && this.corporationTypeCode.length > 0 && this.CertControlsComponent) {\n          this.CertControlsComponent.getCertTypes(this.corporationTypeCode);\n        }\n      }\n\n      if (this.CorpTypeComponent) {\n        this.CorpTypeComponent.IsVisible = true;\n        this.CorpTypeComponent.CorporationTypes = this.masterData.corporationTypes;\n      }\n    });\n  } //Setting value for CorporationCode and CorporationTypeCode to get bifurcated Designation values\n\n\n  setCorporationTypeCodeValue() {\n    if (this.Form1.controls.FormationType.value) {\n      if (this.masterData.corporationTypes) {\n        this.corporationTypeCode = this.masterData.corporationTypes.filter(data => {\n          return data.code.match(this.Form1.controls.FormationType.value) && data.code.length == this.Form1.controls.FormationType.value.length;\n        }).map(obj => {\n          return obj.corpTypeCode;\n        });\n      }\n    } else {\n      this.corporationTypeCode = null;\n    }\n  }\n\n  setCertificateSignatureValues() {\n    // binding certificate signatures dropdown values\n    if (this.selectedKitType == 'LK') {\n      // this.CertControlsComponent.certificateSignature1Data =\n      //   this.masterData.certificateSignatures1LK;\n      // this.CertControlsComponent.certificateSignature2Data =\n      //   this.masterData.certificateSignatures2LK;\n      this.CertControlsComponent.certificateSignatureData = this.masterData.certificateSignatures2LK;\n    } else if (this.selectedKitType == 'CK') {\n      // this.CertControlsComponent.certificateSignature1Data =\n      //   this.masterData.certificateSignatures1CK;\n      // this.CertControlsComponent.certificateSignature2Data =\n      //   this.masterData.certificateSignatures2CK;\n      this.CertControlsComponent.certificateSignatureData = this.masterData.certificateSignatures2CK;\n    }\n\n    if (!this.isAdd && this.selectedKitType != 'PS') {\n      this.CertControlsComponent.setOtherSignatureTitleDefaultValue();\n    }\n  }\n\n  ShowCertsAndByLaws(value) {\n    try {\n      // Validate Page 1 validations\n      if (!this.Form1.valid) {\n        return;\n      }\n\n      var mutliCert = this.multiClassResult.find(x => x.entityCode === this.Form1.controls.FormationType.value);\n      this.showMultiCerts = mutliCert ? mutliCert.masterClassCode == 'M' : false;\n\n      if (this.Form1.controls.FormationType.value != \"FP\") {\n        this.clearMultiCertificates();\n        this.Form2.controls.MultiCerts.setValue(null);\n      }\n\n      if (this.CertControlsComponent) {\n        this.setCertificateSignatureValues(); // this.CertControlsComponent.GetLegends()\n      } // set flag to show next page\n\n\n      this.showCertsAndBylaws = value; //add dynamic validations on the Page 2 fields based on conditions\n\n      if (value) {\n        this.selectedKitName = this.AllKitTypes.find(x => x.value === this.selectedKitType).name;\n        this.selectedStateName = this.masterData.states.find(x => x.stateCode === this.Form1.controls.FilingState.value).stateName;\n        this.selectedGoldStampingOption = this.Form1.controls.GoldStamping.value ? this.masterData.goldStamping.find(x => String(x.id) == this.Form1.controls.GoldStamping.value).value : \"No\";\n        if (this.Form1.controls.FormationType.value) // this.selectedKitType != \"LK\" &&\n          this.selectedEntityTypeName = this.masterData.corporationTypes.find(x => x.value === this.Form1.controls.FormationType.value).name;\n\n        if (this.Form1.controls.ProfessionalCorporation.value) {\n          let somevalue = this.masterData.typeOfCorporations.find(x => x.id == this.Form1.controls.ProfessionalCorporation.value);\n          this.selectedCorporationType = somevalue.value;\n        }\n\n        if (this.Form1.controls.BylawsAndMinutes.value) this.selectedBylawsOption = this.masterData.bylawsAndMinutesData.find(x => String(x.id) === this.Form1.controls.BylawsAndMinutes.value).value;\n        if (this.Form1.controls.OperatingAgreements.value) this.selectedOperatingAgreementOption = this.masterData.operatingAgreementData.find(x => String(x.id) === this.Form1.controls.OperatingAgreements.value).value;\n\n        if (!this.isAdd) {\n          let additionalValues = this.Form2.get(\"AdditionalLegends\").controls.map(x => x.value);\n          let customValues = this.Form2.get(\"CustomizedLegends\").controls.map(x => x.value);\n          let controlValues = additionalValues.concat(customValues);\n          controlValues.forEach(value => {\n            let html = document.getElementById(value);\n            if (html) html.checked = true;\n            let textboxValue = `customizedLegend-${value}`;\n            let textboxHtml = document.getElementById(textboxValue);\n\n            if (textboxHtml) {\n              let legendValue = this.savedCustomLegends.find(x => x.productNumber === value).legendValue;\n              textboxHtml.value = legendValue;\n            }\n          });\n\n          if (this.selectedKitType !== this.savedOrderCopy.productCode) {// this.setDefaultValues();\n          }\n\n          if (this.Form1.controls.FormationType.value !== \"NP\" && this.Form1.controls.FormationType.value !== this.savedOrderCopy.entityType) {\n            this.Form2.controls.DesignationCK.setValue(\"COM\");\n          }\n        } else {// this.setDefaultValues();\n        }\n\n        if (!this.IsUpSelling) {\n          window.scrollTo(0, 0);\n        }\n      }\n    } catch (ex) {}\n  }\n\n  setSelectedColor() {\n    return {\n      \"background-color\": this.Form1.controls.Color.value\n    };\n  }\n\n  ToggleMulticerts(checked) {\n    this.CertControlsComponent.showMulticerts = checked;\n  }\n\n  OnSave() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        _this.CertControlsComponent.removeValidationFromDesignation();\n\n        _this.CertControlsComponent.getAuthorizedSharesCount();\n\n        _this.validateAuthorisedShares();\n\n        _this.multiCertificates = yield _this.CertControlsComponent.getCertificateData();\n\n        if (_this.Form2.valid && _this.Form1.valid && _this.CertControlsComponent.Form.controls.CertificatesForm.valid) {\n          if (!_this.isAdd && _this.selectedKitType !== _this.savedOrderCopy.productCode) {\n            _this.kitsServiceApi.DeleteItem(_this.orderNumber, _this.kitLineNumber, _this.productNumber).subscribe(response => {\n              _this.isAdd = true;\n\n              _this.Save();\n            });\n          } else {\n            _this.Save();\n          }\n        }\n      } catch (ex) {\n        console.error(ex);\n      }\n    })();\n  }\n\n  Save() {\n    var kitOrderVM = new ProductDetail(); //Form1\n\n    kitOrderVM.ProductCode = this.Form1.controls.KitType.value;\n    kitOrderVM.CategoryCode = this.categoryCode;\n    kitOrderVM.SubCategoryCode = this.subCategoryCode;\n    kitOrderVM.FilingState = this.Form1.controls.FilingState.value;\n    kitOrderVM.EntityName = this.Form1.controls.EntityName.value;\n    kitOrderVM.GoldStamping = this.Form1.controls.GoldStamping.value || 0;\n    kitOrderVM.EntityType = this.Form1.controls.FormationType.value;\n    kitOrderVM.Color = this.Form1.controls.Color.value;\n    kitOrderVM.BylawsAndMinutes = this.Form1.controls.BylawsAndMinutes.value;\n    kitOrderVM.OperatingAgreements = this.Form1.controls.OperatingAgreements.value;\n    kitOrderVM.ProfessionalCorporation = this.Form1.controls.ProfessionalCorporation.value;\n    kitOrderVM.OtherProfessionalCorpType = this.Form1.controls.OtherProfessionalCorpType.value; //Form2\n\n    let validFullDate = this.Form2.controls.FormationMonth.value && this.Form2.controls.FormationDate.value && this.Form2.controls.FormationYear.value;\n    const formationYear = this.Form2.controls.FormationYear.value;\n    kitOrderVM.FormationDate = validFullDate ? `${this.Form2.controls.FormationMonth.value}/${this.Form2.controls.FormationDate.value}/${this.Form2.controls.FormationYear.value}` : formationYear ? `//${formationYear}` : \"\";\n    kitOrderVM.FilingStatus = this.Form2.controls.FilingStatus.value ? this.Form2.controls.FilingStatus.value : false;\n    kitOrderVM.TotalAuthorizedShares = this.Form2.controls.TotalAuthorizedShares.value || 0;\n    kitOrderVM.AddLegends = this.Form2.controls.AddLegends.value;\n    kitOrderVM.OtherLegends = this.showLegend ? this.Form2.controls.OtherLegends.value.filter(x => x.isChecked).map(x => x.id.toString()) : [];\n    kitOrderVM.AdditionalLegends = this.showLegend ? this.Form2.controls.AdditionalLegends.value.filter(x => x.isChecked).map(x => x.id.toString()) : [];\n    kitOrderVM.CustomizedLegends = this.showLegend ? this.Form2.controls.CustomizedLegends.value.filter(x => x.isChecked).map(x => this.CustomLegendMapping(x)) : [];\n    kitOrderVM.CloseCorporationLegends = this.Form2.controls.CloseCorporationLegends.value.filter(x => x.isChecked).map(x => this.CustomLegendMapping(x));\n    kitOrderVM.SpecialInstructions = this.Form2.controls.SpecialInstructions.value;\n    kitOrderVM.IsCloseCorporation = this.Form2.controls.IsCloseCorporation.value ? \"YES\" : null;\n    kitOrderVM.CloseCorpType = this.Form2.controls.CloseCorpType.value || 0; // kitOrderVM.ProfessionalCorporation =\n    //   this.Form2.controls.ProfessionalCorporation.value;\n\n    if (this.Form2.controls.MultiCerts.value) {\n      kitOrderVM.certificates = this.multiCertificates;\n    } else {\n      this.multiCertificates.length = 1;\n      kitOrderVM.certificates = this.multiCertificates;\n    } // kitOrderVM.Reflective = this.Form2.controls.ReflectiveCerts.value\n\n\n    kitOrderVM.orderNumber = this.orderNumber;\n    kitOrderVM.kitLineNumber = this.kitLineNumber; //#endregion Request Mapping\n\n    if (this.IsUpSelling) {\n      this.UpSellingResolver(Object.assign({}, kitOrderVM));\n      return;\n    }\n\n    this.kitsServiceApi.saveKits(kitOrderVM, this.isAdd).subscribe(response => {\n      this.Form1.reset();\n      this.Form2.reset();\n    });\n  }\n\n  CustomLegendMapping(x) {\n    let obj = {};\n    obj.ProductNumber = x.id.toString();\n    obj.LegendValue = x.legendValue.toString();\n    return obj;\n  }\n\n  GetColors(forCorporateKit, productCode, selectedColor) {\n    if (this.Form1.controls.BylawsAndMinutes.value || this.Form1.controls.OperatingAgreements.value || productCode === KitCodes.PS) {\n      if (forCorporateKit && !this.Form1.controls.FormationType.value) return;\n      let optionCode = \"NA\";\n      let entityCode = \"NA\";\n\n      if (forCorporateKit && (productCode == KitCodes.CK || productCode == KitCodes.SC)) {\n        optionCode = this.Form1.controls.BylawsAndMinutes.value;\n        entityCode = this.Form1.controls.FormationType.value;\n      } else if (productCode == KitCodes.PS) {\n        entityCode = this.Form1.controls.FormationType.value; // productCode;\n\n        optionCode = \"COL\";\n      } else if (productCode == KitCodes.LK) {\n        entityCode = this.Form1.controls.FormationType.value; //\"LC\";\n\n        optionCode = this.Form1.controls.OperatingAgreements.value;\n      }\n\n      const colorsRequest = {\n        productCode: this.selectedKitType,\n        categoryCode: this.categoryCode,\n        subCategoryCode: this.subCategoryCode,\n        optionCode: optionCode,\n        entityCode: entityCode\n      };\n      this.kitsServiceApi.GetColors(colorsRequest).subscribe(data => {\n        this.ColorComponent.IsVisible = true;\n\n        if (this.isAdd || !this.isAdd && this.selectedKitType != this.savedOrderCopy.productCode) {\n          this.ColorComponent.SelectedColor = null;\n        }\n\n        if (data.length > 0) {\n          //setting product price\n          this.price = data[0].price; // If only one color present, then setting it to form control and not showing color panel\n\n          if (data.length === 1) {\n            this.ColorComponent.IsVisible = false;\n            this.Form1.controls.Color.setValue(data[0].code);\n          } else {\n            let colors = data.map(function (obj) {\n              return obj.code;\n            });\n            let colorName = data.map(function (obj) {\n              return obj.description;\n            });\n            colors = colors.filter((n, i) => colors.indexOf(n) === i);\n            this.colors = data.map(item => {\n              var data = {\n                id: item.code,\n                value: item.description,\n                price: item.price\n              };\n              return data;\n            });\n            this.ColorComponent.Colors = colors;\n            this.ColorComponent.colorNames = colorName;\n\n            if (!this.isAdd && selectedColor && this.selectedKitType === this.savedOrderCopy.productCode) {\n              this.Form1.controls.Color.setValue(selectedColor);\n              this.ColorComponent.SelectedColor = selectedColor;\n            }\n          }\n        } else {\n          this.ColorComponent.Colors = [];\n          this.Form1.controls.Color.setValue(null);\n        }\n      });\n    }\n\n    if (this.CertControlsComponent) {\n      this.setCertificateSignatureValues();\n    }\n  }\n\n  ColorCondition() {\n    return this.selectedKitType === \"CK\" && this.Form1.controls.FormationType.value && this.Form1.controls.BylawsAndMinutes.value || this.selectedKitType === \"LK\" && this.Form1.controls.OperatingAgreements.value || this.selectedKitType === \"PS\";\n  }\n\n  MapMasterDataByControlId(data, controlId) {\n    let mappedData = data.filter(obj => obj.controlID === controlId).map(item => {\n      var data = {\n        id: item.controlValue,\n        value: item.controlText\n      };\n      return data;\n    });\n    return mappedData || [];\n  }\n\n  MapAllMasterData(data) {\n    this.masterData.designationCk = this.MapMasterDataByControlId(data, ProductDetailControls.DesignationForCK);\n    this.masterData.designationLk = this.MapMasterDataByControlId(data, ProductDetailControls.DesignationForLK);\n    this.masterData.parValue = this.MapMasterDataByControlId(data, ProductDetailControls.ParValue);\n    this.masterData.certificateSignatures1CK = this.MapMasterDataByControlId(data, ProductDetailControls.CertificateSignatureForCK);\n    this.masterData.certificateSignatures1LK = this.MapMasterDataByControlId(data, ProductDetailControls.CertificateSignatureForLK);\n    this.masterData.certificateSignatures2CK = this.MapMasterDataByControlId(data, ProductDetailControls.CertificateSignatureForCK);\n    this.masterData.certificateSignatures2LK = this.MapMasterDataByControlId(data, ProductDetailControls.CertificateSignatureForLK);\n    this.masterData.bylawsAndMinutesData = this.MapMasterDataByControlId(data, ProductDetailControls.ByLawsAndMinutes);\n    this.masterData.operatingAgreementData = this.MapMasterDataByControlId(data, ProductDetailControls.OperatingAgreements);\n    this.masterData.goldStamping = this.MapMasterDataByControlId(data, ProductDetailControls.GoldStamping);\n    this.masterData.legendOptionsData = this.MapMasterDataByControlId(data, ProductDetailControls.LegendOptions);\n\n    if (this.IsDeluxeKit()) {\n      this.masterData.certificateColors = this.MapMasterDataByControlId(data, ProductDetailControls.DeluxeCertificateColors);\n    } else {\n      this.masterData.certificateColors = this.MapMasterDataByControlId(data, ProductDetailControls.CertificateColors);\n    }\n\n    this.certificateColors = this.masterData.certificateColors.map(obj => obj.id);\n  }\n\n  IsDeluxeKit() {\n    if (this.categoryCode == 'AI1' && this.subCategoryCode == 'DC' || this.categoryCode == 'BXK' && this.subCategoryCode == 'DP' || this.categoryCode == 'BXK' && this.subCategoryCode == 'DR') {\n      return true;\n    }\n  }\n\n  RenderCertificateColors() {\n    this.CertificateColorComponent.IsVisible = true;\n    this.CertificateColorComponent.Label = \"Certificate Color\";\n    this.CertificateColorComponent.Colors = this.certificateColors;\n    this.cdr.detectChanges();\n  }\n\n  getKitListData() {\n    this.isLoading.show();\n    this.kitsServiceApi.getkitsListing().subscribe(res => {\n      this.isLoading.hide();\n      this.UpSellingKitsData = res.filter(obj => obj.categoryCode == \"AI1\" || obj.categoryCode == \"BXK\");\n    });\n  }\n\n  upSellingKitSelection(kitDetails) {\n    this.subCategoryCode = kitDetails.subCategoryCode;\n    this.categoryCode = kitDetails.categoryCode;\n    this.kitType = kitDetails.productCode;\n    this.GetKitData();\n  }\n\n  DisableCertColor() {\n    let category = \"BXK\";\n    let subCategories = [\"DR\", \"DP\"];\n\n    if (subCategories.includes(this.subCategoryCode) && category == this.categoryCode) {\n      return true;\n    }\n\n    return false;\n  }\n\n  onLoad() {\n    this.imgZoom = document.getElementById(\"myimage\");\n    this.imgLens = document.getElementById(\"divLens\");\n    this.divZoom = document.getElementById(\"divZoomed\");\n    const dim = this.divZoom.getBoundingClientRect();\n    this.imgClientRect = this.imgZoom.getBoundingClientRect();\n    var src = this.imgZoom.getAttribute(\"src\");\n    this.render.setStyle(this.divZoom, \"background-image\", \"url('\" + src + \"')\");\n    this.render.setStyle(this.divZoom, \"background-size\", this.imgZoom.width * 3 + \"px \" + this.imgZoom.height * 3 + \"px\");\n    this.render.setStyle(this.divZoom, \"background-repeat\", \"no-repeat\");\n    this.render.setStyle(this.divZoom, \"background-position\", \"center top\");\n    this.render.setStyle(this.divZoom, \"transition\", \"background-position .2s ease-out\");\n\n    if (!this.cx && !this.cy) {\n      this.cx = (dim.width - this.imgZoom.width * 3) / (this.imgZoom.width - this.imgLens.offsetWidth);\n      this.cy = (dim.height - this.imgZoom.height * 3) / (this.imgZoom.height - this.imgLens.offsetHeight);\n    }\n\n    document.getElementById(\"divLens\").style.display = \"none\";\n    document.getElementById(\"divZoomed\").style.display = \"none\";\n    this.render.addClass(this.imgLens, \"img-zoom-lens\");\n    this.render.addClass(this.divZoom, \"zoom-preview\");\n  }\n\n  ImgMouseEnter() {\n    document.getElementById(\"divLens\").style.display = \"block\";\n    document.getElementById(\"divZoomed\").style.display = \"block\";\n  }\n\n  ImgMouseLeave() {\n    document.getElementById(\"divLens\").style.display = \"none\";\n    document.getElementById(\"divZoomed\").style.display = \"none\";\n  }\n\n  ImgMouseMove(event) {\n    if (!this.isMoveEventCalling) {\n      this.isMoveEventCalling = true;\n      var pos = this.getCursorPos(event);\n      this.render.setStyle(this.divZoom, \"background-position\", pos.x * this.cx + \"px \" + pos.y * this.cy + \"px\");\n      this.isMoveEventCalling = false;\n    }\n  } // ToDo : Need to remove later after testing above method\n\n\n  mouseMove(event) {\n    if (!this.isMoveEventCalling) {\n      event.preventDefault();\n      this.isMoveEventCalling = true;\n      const result = this.moveLens(event);\n      this.render.setStyle(this.divZoom, \"background-position\", result);\n    }\n  }\n\n  moveLens(e) {\n    let pos;\n    let x;\n    let y;\n    /*prevent any other actions that may occur when moving over the image:*/\n\n    e.preventDefault();\n    /*get the cursor's x and y positions:*/\n\n    pos = this.getCursorPos(e);\n    /*calculate the position of the lens:*/\n\n    x = pos.x - this.imgLens.offsetWidth / 2;\n    y = pos.y - this.imgLens.offsetHeight / 2;\n    /*prevent the lens from being positioned outside the image:*/\n\n    if (x > this.imgZoom.width - this.imgLens.clientWidth) {\n      x = this.imgZoom.width - this.imgLens.offsetWidth;\n    }\n\n    if (x < 0) {\n      x = 0;\n    }\n\n    if (y > this.imgZoom.height - this.imgLens.clientHeight) {\n      y = this.imgZoom.height - this.imgLens.offsetHeight;\n    }\n\n    if (y < 0) {\n      y = 0;\n    }\n    /*set the position of the lens:*/\n    // this.render.setStyle(this.imgLens, 'left', x + 'px');\n    //this.render.setStyle(this.imgLens, 'top', y + 'px');\n\n    /*display what the lens \"sees\":*/\n\n\n    let result = x * this.cx + \"px \" + y * this.cy + \"px\";\n    this.isMoveEventCalling = false;\n    return result;\n  }\n\n  getCursorPos(e) {\n    let x = 0,\n        y = 0;\n    e = e || window.event;\n    /*get the x and y positions of the image:*/\n\n    /*calculate the cursor's x and y coordinates, relative to the image:*/\n\n    x = e.pageX - this.imgClientRect.left;\n    y = e.pageY - this.imgClientRect.top;\n    /*consider any page scrolling:*/\n\n    x = x - window.pageXOffset;\n    y = y - window.pageYOffset;\n    return {\n      x: x,\n      y: y\n    };\n  }\n\n  validateAuthorisedShares() {\n    if (this.Form1.controls.FormationType.value != 'FP') return;\n\n    if (this.Form2.controls.TotalAuthorizedShares.value && this.Form2.controls.TotalAuthorizedShares.value < this.CertControlsComponent.authorizedSharesCount) {\n      this.toaster.warning(\"Total Authorized Shares should not be less than total Certificates.\", \"Warning\");\n      return true;\n    }\n  }\n\n  showLegends() {\n    //var isValidProfessionalCorp = this.Form2.controls.ProfessionalCorporation.value && this.masterData.typeOfCorporations.map(x => x.id).includes(this.Form2.controls.ProfessionalCorporation.value);\n    var isValidProfessionalCorp = this.Form1.controls.ProfessionalCorporation.value && this.masterData.typeOfCorporations.map(x => x.id).includes(this.Form1.controls.ProfessionalCorporation.value);\n    this.showLegend = this.selectedKitType == 'CK' ? this.Form1.controls.FormationType.value != 'NP' || this.Form1.controls.FormationType.value == 'NP' && isValidProfessionalCorp && !['060', '065'].includes(this.Form1.controls.ProfessionalCorporation.value) : this.selectedKitType !== 'PS';\n    return this.showLegend;\n  }\n\n  ngOnDestroy() {\n    // when the component get's destroyed, unsubscribe all the subscriptions\n    if (this.routesSubscription) {\n      this.routesSubscription.unsubscribe();\n    }\n  }\n\n  addRBallToWord(inputString) {\n    const pageTitle = this.productService.addRBallToWord(inputString);\n    this.pageTitleService.setPageTitle(pageTitle?.replace(/<[^>]+>/g, ''));\n    return pageTitle;\n  }\n\n};\n\nProductDetailComponent.ctorParameters = () => [{\n  type: Renderer2\n}, {\n  type: KitsService\n}, {\n  type: ChangeDetectorRef\n}, {\n  type: ActivatedRoute\n}, {\n  type: LoaderService\n}, {\n  type: ToastrService\n}, {\n  type: SimpleProductService\n}, {\n  type: ComponentMessageService\n}, {\n  type: PageTitleService\n}];\n\nProductDetailComponent.propDecorators = {\n  certificateColorComponent: [{\n    type: ViewChild,\n    args: [\"CertificateColorComponent\", {\n      static: false\n    }]\n  }],\n  corpTypeComponent: [{\n    type: ViewChild,\n    args: [\"CorpTypeComponent\", {\n      static: false\n    }]\n  }],\n  colorComponent: [{\n    type: ViewChild,\n    args: [\"ColorComponent\", {\n      static: false\n    }]\n  }],\n  formSubmit1: [{\n    type: ViewChild,\n    args: [\"formSubmit1\", {\n      static: true\n    }]\n  }],\n  _FormRef1: [{\n    type: ViewChild,\n    args: [\"submitButton1\", {\n      static: true\n    }]\n  }],\n  _FormRef2: [{\n    type: ViewChild,\n    args: [\"submitButton2\", {\n      static: true\n    }]\n  }],\n  certControlsComponent: [{\n    type: ViewChild,\n    args: [\"CertControlsComponent\", {\n      static: false\n    }]\n  }],\n  _IsUpselling: [{\n    type: Input,\n    args: [\"IsUpselling\"]\n  }]\n};\nProductDetailComponent = __decorate([Component({\n  selector: \"app-product-detail\",\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], ProductDetailComponent);\nexport { ProductDetailComponent };", "map": {"version": 3, "mappings": ";;;;AAAA,SACEA,SADF,EAGEC,SAHF,EAIEC,KAJF,EAMEC,iBANF,EAOEC,SAPF,QAQO,eARP;AASA,SAASC,WAAT,QAA4B,6BAA5B;AACA,SAASC,cAAT,QAA+B,iBAA/B;AACA,SAASC,aAAT,QAA8B,yDAA9B;AACA,SAASC,SAAT,EAAoBC,WAApB,EAAiCC,SAAjC,EAA4CC,UAA5C,QAA8D,gBAA9D;AAMA,SAASC,QAAT,QAAyB,mDAAzB;AACA,SAASC,qBAAT,QAAsC,2DAAtC;AACA,SAASC,4BAAT,QAA6C,0EAA7C;AACA,SAASC,WAAT,QAA4B,8CAA5B;AACA,SAASC,IAAT,QAAqB,oCAArB;AACA,SAASC,uBAAT,QAAwC,qDAAxC;AACA,SAASC,aAAT,QAA8B,YAA9B;AACA,SACEC,aADF,QAEO,uDAFP;AAGA,SAASC,WAAT,QAA4B,iDAA5B;AAEA,SAASC,uBAAT,QAAwC,gEAAxC;AACA,SAASC,oBAAT,QAAqC,6DAArC;AACA,SAASC,gBAAT,QAAiC,2DAAjC;IASaC,sBAAsB,SAAtBA,sBAAsB;EAuBjCC,YACUC,MADV,EAEUC,cAFV,EAGUC,GAHV,EAIUC,KAJV,EAKUC,SALV,EAMUC,OANV,EAOUC,cAPV,EAQUC,gBARV,EASUC,gBATV,EAS4C;IARlC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IArBV,qBAAwB,CAAxB;IAEA,kBAAsB,IAAtB;IAIA,0BAA8B,KAA9B;IACA,2BAA2B,EAA3B;IAGA,mBAAc,EAAd;IAkBA,yBAAyB,EAAzB;IAEA,iCAA4B,IAAI1B,SAAJ,CAAc,EAAd,CAA5B;IACA,kCAA6B,IAAIA,SAAJ,CAAc,EAAd,CAA7B;IACA,iCAA4B,IAAIA,SAAJ,CAAc,EAAd,CAA5B;IACA,6BAAwB,IAAIA,SAAJ,CAAc,EAAd,CAAxB;IA+EA,mBAAc,CACZ;MAAE2B,IAAI,EAAE,eAAR;MAAyBC,KAAK,EAAE;IAAhC,CADY,EAEZ;MAAED,IAAI,EAAE,SAAR;MAAmBC,KAAK,EAAE;IAA1B,CAFY,EAGZ;MAAED,IAAI,EAAE,iBAAR;MAA2BC,KAAK,EAAE;IAAlC,CAHY,EAIZ;MAAED,IAAI,EAAE,cAAR;MAAwBC,KAAK,EAAE;IAA/B,CAJY,CAAd;IAOA,kBAAsB,KAAtB;IACA,kBAAahB,WAAW,CAACiB,UAAzB;IACA,sBAAsB,EAAtB;IACA,0BAA4B,EAA5B;IAEA,aAAgB,CAAhB;IACA,aAAiB,IAAjB;IACA,mBAAsB,CAAtB;IACA,qBAAwB,CAAxB;IACA,qBAAwB,CAAxB;IAcA,0BAA0B,KAA1B;IAGA,0BAAsC,EAAtC;IACA,eAAyB,EAAzB;IAEA,yBAA2B,EAA3B;IACA,yBAA2B,EAA3B;IACA,kBAA2C,IAAIvB,4BAAJ,EAA3C;IACA,cAAgB,EAAhB;IACA,yBAAwC,EAAxC;IACA,4BAAwC,EAAxC;IAEA,mBAAuB,KAAvB;IAIA,wBAA2B,CAA3B;IACA,6BAAgC,CAAhC;IACA,6BAAgC,CAAhC;IACA,mCAA8B,CAAC,IAAD,EAAO,IAAP,CAA9B;IAEA,wBAA0B,EAA1B;IACA,sBAA0B,KAA1B;IAEA,4BAA+B,IAA/B,CAlJ4C,CAkJP;;IAgfrC,aAAQ,IAAIJ,SAAJ,CAAc;MACpB4B,WAAW,EAAE,IAAI7B,WAAJ,CAA4B,IAA5B,CADO;MAEpB8B,OAAO,EAAE,IAAI9B,WAAJ,CAA4B,IAA5B,CAFW;MAGpB+B,WAAW,EAAE,IAAI/B,WAAJ,CAA4B,IAA5B,CAHO;MAKpBgC,UAAU,EAAE,IAAIhC,WAAJ,CAA4B,IAA5B,CALQ;MAMpBiC,WAAW,EAAE,IAAIjC,WAAJ,CAA4B;QAAE2B,KAAK,EAAE,IAAT;QAAeO,QAAQ,EAAE;MAAzB,CAA5B,CANO;MAOpBC,kBAAkB,EAAE,IAAInC,WAAJ,CAA4B;QAAE2B,KAAK,EAAE,IAAT;QAAeO,QAAQ,EAAE;MAAzB,CAA5B,CAPA;MAQpBE,gBAAgB,EAAE,IAAIpC,WAAJ,CAA4B;QAAE2B,KAAK,EAAE,IAAT;QAAeO,QAAQ,EAAE;MAAzB,CAA5B,CARE;MAUpBG,aAAa,EAAE,IAAIrC,WAAJ,CAA4B,IAA5B,CAVK;MAWpBsC,KAAK,EAAE,IAAItC,WAAJ,CAA4B,IAA5B,CAXa;MAYpBuC,gBAAgB,EAAE,IAAIvC,WAAJ,CAA4B,IAA5B,CAZE;MAapBwC,mBAAmB,EAAE,IAAIxC,WAAJ,CAA4B,IAA5B,CAbD;MAcpByC,YAAY,EAAE,IAAIzC,WAAJ,CAA4B,IAA5B,CAdM;MAgBpB0C,uBAAuB,EAAE,IAAI1C,WAAJ,CAA4B,IAA5B,CAhBL;MAiBpB2C,yBAAyB,EAAE,IAAI3C,WAAJ,CAA4B,IAA5B;IAjBP,CAAd,CAAR;IAmBA,aAAQ,IAAIC,SAAJ,CAAc;MACpB2C,aAAa,EAAE,IAAI5C,WAAJ,CACb,IADa,EAEbQ,uBAAuB,CAACqC,qBAAxB,CACE,eADF,EAEE,gBAFF,EAGE,eAHF,CAFa,CADK;MASpBC,cAAc,EAAE,IAAI9C,WAAJ,CACd,IADc,EAEdQ,uBAAuB,CAACqC,qBAAxB,CACE,eADF,EAEE,gBAFF,EAGE,eAHF,CAFc,CATI;MAiBpBE,aAAa,EAAE,IAAI/C,WAAJ,CAA4B,IAA5B,EACbQ,uBAAuB,CAACqC,qBAAxB,CACE,eADF,EAEE,gBAFF,EAGE,eAHF,CADa,CAjBK;MAwBpBG,YAAY,EAAE,IAAIhD,WAAJ,CAA4B,IAA5B,CAxBM;MAyBpBiD,qBAAqB,EAAE,IAAIjD,WAAJ,CAA4B,IAA5B,CAzBH;MA0BpBkD,UAAU,EAAE,IAAIlD,WAAJ,CAA4B,IAA5B,CA1BQ;MA2BpBmD,aAAa,EAAE,IAAInD,WAAJ,CAA4B,IAA5B,CA3BK;MA4BpB;MAEAoD,UAAU,EAAE,IAAIpD,WAAJ,CAA4B,IAA5B,EAAkC,CAACE,UAAU,CAACmD,QAAZ,CAAlC,CA9BQ;MA+BpB;MACAC,iBAAiB,EAAE,KAAKC,0BAhCJ;MAiCpBC,iBAAiB,EAAE,KAAKC,yBAjCJ;MAkCpB;MACAC,YAAY,EAAE,KAAKC,qBAnCC;MAoCpBC,YAAY,EAAE,IAAI7D,SAAJ,CAAc,EAAd,CApCM;MAqCpB8D,uBAAuB,EAAE,KAAKC,yBArCV;MAsCpB;MACAC,mBAAmB,EAAE,IAAI/D,WAAJ,CAA4B,IAA5B,CAvCD;MAwCpBgE,OAAO,EAAE,IAAIhE,WAAJ,CAA4B,IAA5B,CAxCW;MAyCpBiE,kBAAkB,EAAE,IAAIjE,WAAJ,CAA4B,IAA5B,CAzCA;MA0CpBkE,aAAa,EAAE,IAAIlE,WAAJ,CAA4B,CAA5B,CA1CK,CA2CpB;;IA3CoB,CAAd,CAAR;EAppBK;;EAcwB,IAAzBmE,yBAAyB,CAC3BA,yBAD2B,EACsB;IAEjD,IAAIA,yBAAJ,EAA+B;MAC7B,KAAKC,yBAAL,GAAiCD,yBAAjC;MACA,KAAKE,uBAAL;IACD;EACF;;EACuE,IAAjBC,iBAAiB,CACtEA,iBADsE,EACnB;IAEnD,IAAIA,iBAAJ,EAAuB;MACrB,KAAKC,iBAAL,GAAyBD,iBAAzB,CADqB,CAErB;;MACA,KAAKC,iBAAL,CAAuBC,SAAvB,GAAmC,IAAnC;MACA,KAAKD,iBAAL,CAAuBE,gBAAvB,GACE,KAAKC,UAAL,CAAgBC,gBADlB,CAJqB,CAQrB;MACA;MACA;;MACA,KAAKxD,GAAL,CAASyD,aAAT;IAED;EACF;;EACiE,IAAdC,cAAc,CAChEA,cADgE,EAC1B;IAEtC,IAAIA,cAAJ,EAAoB;MAClB,KAAKC,cAAL,GAAsBD,cAAtB,CADkB,CAElB;;MACA,KAAKC,cAAL,CAAoBC,iBAApB,CAAsCC,SAAtC,CAAiDC,KAAD,IAAU;QACxD,KAAKC,KAAL,GAAa,KAAKC,MAAL,CAAYC,IAAZ,CAAkBC,CAAD,IAAOA,CAAC,CAACC,EAAF,KAASL,KAAjC,EAAwCC,KAArD;MACD,CAFD;MAGA,KAAK/D,GAAL,CAASyD,aAAT;IACD;EACF;;EAG0D,IAATW,SAAS,CACzDC,OADyD,EACtC;IAEnB,KAAKC,QAAL,GAAgBD,OAAhB;EACD;;EAE0D,IAATE,SAAS,CACzDF,OADyD,EACtC;IAEnB,KAAKG,QAAL,GAAgBH,OAAhB;EACD;;EAGwB,IAArBI,qBAAqB,CACvBA,qBADuB,EAC4B;IAEnD,IAAIA,qBAAJ,EAA2B;MACzBA,qBAAqB,CAACvD,aAAtB,GACE,KAAKwD,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KADpC;MAEA,KAAKoE,qBAAL,GAA6BH,qBAA7B;;MAEA,IAAI,KAAKI,mBAAL,IAA4B,KAAKA,mBAAL,CAAyBC,MAAzB,GAAkC,CAAlE,EAAqE;QACnEL,qBAAqB,CAACM,YAAtB,CAAmC,KAAKF,mBAAxC;MACD;IACF;;IACD,KAAK7E,GAAL,CAASyD,aAAT;EACD;;EAEDuB,gBAAgB;IACdC,UAAU,CAAC,MAAK;MACd,KAAKX,QAAL,CAAcY,aAAd,CAA4BC,KAA5B;MACA,KAAKX,QAAL,CAAcU,aAAd,CAA4BC,KAA5B;IACD,CAHS,EAGP,EAHO,CAAV;IAIA,OAAO,IAAIC,OAAJ,CAAaC,GAAD,IAAU,KAAKC,iBAAL,GAAyBD,GAA/C,CAAP;EACD;;EA6DqC,IAAZE,YAAY,CAACC,WAAD,EAAqB;IACzD,KAAKC,WAAL,GAAmBD,WAAnB;;IACA,IAAIA,WAAJ,EAAiB;MACf;MACA,KAAKE,cAAL;IACD;EACF;;EAEDC,QAAQ;IACN,KAAK1F,KAAL,CAAW2F,aAAX,CAAyB/B,SAAzB,CAAoCgC,WAAD,IAAgB;MACjD,KAAKC,eAAL,GAAuBD,WAAW,CAACE,GAAZ,CAAgB,KAAhB,CAAvB;MACA,KAAKC,YAAL,GAAoBH,WAAW,CAACE,GAAZ,CAAgB,IAAhB,CAApB;MACA,KAAKE,OAAL,GAAeJ,WAAW,CAACE,GAAZ,CAAgB,MAAhB,CAAf;MACA,IAAIG,WAAW,GAAG9G,IAAI,CAAC+G,WAAL,CAAiBN,WAAW,CAACE,GAAZ,CAAgB,IAAhB,CAAjB,CAAlB;MACA,IAAIK,aAAa,GAAGhH,IAAI,CAAC+G,WAAL,CAAiBN,WAAW,CAACE,GAAZ,CAAgB,KAAhB,CAAjB,CAApB;MACA,IAAIM,aAAa,GAAGjH,IAAI,CAAC+G,WAAL,CAAiBN,WAAW,CAACE,GAAZ,CAAgB,IAAhB,CAAjB,CAApB;MACA,KAAKG,WAAL,GAAmBA,WAAW,GAAGI,QAAQ,CAACJ,WAAD,EAAc,EAAd,CAAX,GAA+B,CAA7D;MACA,KAAKE,aAAL,GAAqBA,aAAa,GAAGE,QAAQ,CAACF,aAAD,EAAgB,EAAhB,CAAX,GAAiC,CAAnE;MACA,KAAKC,aAAL,GAAqBA,aAAa,GAAGC,QAAQ,CAACD,aAAD,EAAgB,EAAhB,CAAX,GAAiC,CAAnE;MAEA,KAAKtG,cAAL,CAAoBwG,oBAApB,GAA2C1C,SAA3C,CAAsD2C,IAAD,IAAS;QAC5D,KAAKC,gBAAL,CAAsBD,IAAtB;;QACA,IAAI,KAAKN,WAAL,IAAoB,KAAKE,aAAzB,IAA0C,KAAKC,aAAnD,EAAkE;UAChE,IAAIK,OAAO,GAAG,IAAIvH,WAAJ,EAAd;UACAuH,OAAO,CAACR,WAAR,GAAsB,KAAKA,WAA3B;UACAQ,OAAO,CAACN,aAAR,GAAwB,KAAKA,aAA7B;UACAM,OAAO,CAACL,aAAR,GAAwB,KAAKA,aAA7B;UACA,KAAKtG,cAAL,CAAoB4G,YAApB,CAAiCD,OAAjC,EAA0C7C,SAA1C,CAAqD2C,IAAD,IAAS;YAC3D,KAAKI,KAAL,GAAa,KAAb;YACA,KAAKd,eAAL,GAAuBU,IAAI,CAACV,eAA5B;YAEA,KAAKE,YAAL,GAAoBQ,IAAI,CAACR,YAAzB;YACA,KAAKC,OAAL,GAAeO,IAAI,CAACK,WAApB;YACA,KAAKC,eAAL,GAAuBN,IAAI,CAACK,WAA5B;YACA,KAAKE,UAAL;YACA,KAAKC,cAAL,GAAsBR,IAAtB;YACA,KAAKS,QAAL,CAAcT,IAAd;UACD,CAVD;QAWD,CAhBD,MAgBO;UACL,KAAKO,UAAL;QACD;MACF,CArBD;IAsBD,CAjCD,EADM,CAoCN;;IAEA,KAAKhH,cAAL,CAAoBmH,SAApB,GAAgCrD,SAAhC,CAA2CsD,MAAD,IAAW;MACnD,KAAK5D,UAAL,CAAgB6D,MAAhB,GAAyBD,MAAzB;MACA,KAAKjH,SAAL,CAAemH,IAAf;IACD,CAHD;IAMA,KAAKtH,cAAL,CAAoBuH,QAApB,GAA+BzD,SAA/B,CAA0C2C,IAAD,IAAS;MAChD,KAAKjD,UAAL,CAAgBgE,oBAAhB,GAAuCf,IAAvC;IACD,CAFD;IAKA,KAAKgB,KAAL,CAAW7C,QAAX,CAAoB9C,YAApB,CAAiC4F,YAAjC,CAA8C5D,SAA9C,CAAyDrD,KAAD,IAAU;MAChE,IAAIA,KAAJ,EAAW;QACT,KAAKgH,KAAL,CAAW7C,QAAX,CAAoB/C,aAApB,CAAkC8F,KAAlC;QACA,KAAKF,KAAL,CAAW7C,QAAX,CAAoBhD,cAApB,CAAmC+F,KAAnC;QACA,KAAKF,KAAL,CAAW7C,QAAX,CAAoBlD,aAApB,CAAkCiG,KAAlC;QACA,KAAKF,KAAL,CAAW7C,QAAX,CAAoB/C,aAApB,CAAkC+F,OAAlC;QACA,KAAKH,KAAL,CAAW7C,QAAX,CAAoBhD,cAApB,CAAmCgG,OAAnC;QACA,KAAKH,KAAL,CAAW7C,QAAX,CAAoBlD,aAApB,CAAkCkG,OAAlC;MACD,CAPD,MAOO;QACL,KAAKH,KAAL,CAAW7C,QAAX,CAAoB/C,aAApB,CAAkCgG,aAAlC,CAAgD,CAC9CvI,uBAAuB,CAACqC,qBAAxB,CACE,eADF,EAEE,gBAFF,EAGE,eAHF,CAD8C,CAAhD;QAOA,KAAK8F,KAAL,CAAW7C,QAAX,CAAoB/C,aAApB,CAAkCiG,MAAlC;QACA,KAAKL,KAAL,CAAW7C,QAAX,CAAoBhD,cAApB,CAAmCkG,MAAnC;QACA,KAAKL,KAAL,CAAW7C,QAAX,CAAoBlD,aAApB,CAAkCoG,MAAlC;MACD;IACF,CApBD;IAsBA,KAAKnD,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCuG,YAAlC,CAA+C5D,SAA/C,CAA0DrD,KAAD,IAAU;MACjE,IAAI,KAAKiF,WAAT,EAAsB;QACpB,KAAKqC,uBAAL;MACD;IACF,CAJD;;IAOA,IAAI,KAAKrC,WAAT,EAAsB;MACpB,KAAKsC,kBAAL,GAA0B,KAAK1H,gBAAL,CAAsB2H,aAAtB,CAAoCnE,SAApC,CAA8C2C,IAAI,IAAG;QAC7E,IAAIA,IAAI,CAACyB,OAAL,IAAgBzB,IAAI,CAACyB,OAAL,CAAazH,KAA7B,IAAsCgG,IAAI,CAACyB,OAAL,CAAa1H,IAAb,IAAqB,YAA/D,EAA6E;UAC3E,KAAKmE,KAAL,CAAWC,QAAX,CAAoB9D,UAApB,CAA+BqH,QAA/B,CAAwC1B,IAAI,CAACyB,OAAL,CAAazH,KAArD;QACD;;QAED,IAAIgG,IAAI,CAACyB,OAAL,IAAgBzB,IAAI,CAACyB,OAAL,CAAazH,KAA7B,IAAsCgG,IAAI,CAACyB,OAAL,CAAa1H,IAAb,IAAqB,iBAA/D,EAAkF;UAChF,IAAIiG,IAAI,CAACyB,OAAL,CAAazH,KAAb,IAAsB,cAA1B,EAA0C;YACxC,KAAKkE,KAAL,CAAWC,QAAX,CAAoB3D,kBAApB,CAAuCkH,QAAvC,CAAgD,EAAhD;UACD,CAFD,MAEO;YACL,KAAKxD,KAAL,CAAWC,QAAX,CAAoB3D,kBAApB,CAAuCkH,QAAvC,CAAgD1B,IAAI,CAACyB,OAAL,CAAazH,KAA7D;UACD;QACF;;QAED,IAAIgG,IAAI,CAACyB,OAAL,IAAgBzB,IAAI,CAACyB,OAAL,CAAazH,KAA7B,IAAsCgG,IAAI,CAACyB,OAAL,CAAa1H,IAAb,IAAqB,kBAA/D,EAAmF;UACjF,IAAIiG,IAAI,CAACyB,OAAL,CAAazH,KAAb,IAAsB,IAA1B,EAAgC;YAC9B,KAAKkE,KAAL,CAAWC,QAAX,CAAoB3D,kBAApB,CAAuCkH,QAAvC,CAAgD,EAAhD;UACD;QACF;;QAED,IAAI1B,IAAI,CAACyB,OAAL,IAAgBzB,IAAI,CAACyB,OAAL,CAAazH,KAA7B,IAAsCgG,IAAI,CAACyB,OAAL,CAAa1H,IAAb,IAAqB,uBAA/D,EAAwF;UACtF,KAAKmE,KAAL,CAAWC,QAAX,CAAoB7D,WAApB,CAAgCoH,QAAhC,CAAyC1B,IAAI,CAACyB,OAAL,CAAazH,KAAtD;QACD;;QAED,IAAIgG,IAAI,CAACyB,OAAL,IAAgBzB,IAAI,CAACyB,OAAL,CAAaE,eAAjC,EAAkD;UAChD,KAAKC,WAAL;UACA,KAAK1D,KAAL,CAAWC,QAAX,CAAoB3D,kBAApB,CAAuCkH,QAAvC,CAAgD,EAAhD;UACA,KAAKxD,KAAL,CAAWC,QAAX,CAAoB9D,UAApB,CAA+BqH,QAA/B,CAAwC,EAAxC;UACA,KAAKxD,KAAL,CAAWC,QAAX,CAAoB7D,WAApB,CAAgCoH,QAAhC,CAAyC,EAAzC;UACA,KAAKC,eAAL,GAAuB3B,IAAI,CAACyB,OAAL,CAAaE,eAApC;QACD;MAEF,CA/ByB,CAA1B;IAiCD;EAEF;;EACDE,cAAc,CAACvB,eAAD,EAAgB;IAC5B,KAAKwB,WAAL,GAAmB,EAAnB;IACA,KAAKlI,cAAL,CAAoBiI,cAApB,CAAmCvB,eAAnC,EAAoD,KAAKd,YAAzD,EAAuE,IAAvE,EAA6E,IAA7E,EAAmFnC,SAAnF,CAA8F0E,QAAD,IAAa;MACxG,KAAKD,WAAL,GAAmBC,QAAnB;IACD,CAFD;EAGD;;EAEOC,UAAU;IAChB,KAAKzI,cAAL,CACGyI,UADH,CACc,KAAK1B,eADnB,EACoC,KAAKd,YADzC,EAEGnC,SAFH,CAEc2C,IAAD,IAAS;MAClB,KAAKiC,aAAL,GAAqBjC,IAArB;MACA,KAAKkC,UAAL,CAAgBlC,IAAhB;;MAEA,IAAI,KAAKmC,sBAAT,EAAiC;QAC/B,KAAKA,sBAAL;MACD;IACF,CATH;EAUD;;EAEDC,wBAAwB,CAACpI,KAAD,EAAM;IAE5B,IAAIgG,IAAI,GAAG,KAAKiC,aAAhB;IACA,KAAKC,UAAL,CAAgBlC,IAAhB,EAAsBhG,KAAtB;;IACA,IAAI,KAAKoE,qBAAT,EAAgC;MAC9B,KAAKA,qBAAL,CAA2BiE,6BAA3B,CAAyDrI,KAAzD;IACD;;IAED,IAAI,KAAKmI,sBAAT,EAAiC;MAC/B,KAAKA,sBAAL;IACD;EACF;;EAEDD,UAAU,CAAClC,IAAD,EAAcsC,WAAd,EAA0B;IAClC,IAAIC,aAAJ;;IACA,IAAI,KAAKxF,UAAL,CAAgBC,gBAApB,EAAsC;MACpCuF,aAAa,GAAG,KAAKxF,UAAL,CAAgBC,gBAAhB,CAAiCS,IAAjC,CAAsCC,CAAC,IAAIA,CAAC,CAAC1D,KAAF,IAAW,KAAKkE,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KAAxF,CAAhB;IACD;;IACD,KAAK4B,0BAAL,CAAgC4G,KAAhC;IACA,KAAK1G,yBAAL,CAA+B0G,KAA/B;IACA,KAAKrG,yBAAL,CAA+BqG,KAA/B;IACA,KAAKxG,qBAAL,CAA2BwG,KAA3B;IACA,KAAKzF,UAAL,CAAgB0F,qBAAhB,GAAwC,EAAxC;;IAEA,IAAIzC,IAAI,CAAC1B,MAAL,GAAc,CAAd,IAAmB0B,IAAI,CAACvC,IAAL,CAAWC,CAAD,IAAOA,CAAC,CAACgF,UAAF,KAAiB,GAAlC,CAAvB,EAA+D;MAE7D,IAAIC,eAAe,GAAG,KAAKC,kBAAL,CAAwB5C,IAAxB,EAA8B,GAA9B,EAAmCuC,aAAnC,EAAkDD,WAAlD,CAAtB;MAEAK,eAAe,CAACE,OAAhB,CAAyBC,MAAD,IAAW;QACjC,IAAIC,SAAS,GAAG,IAAIzK,SAAJ,CAAc;UAC5BqF,EAAE,EAAE,IAAItF,WAAJ,CAA4ByK,MAAM,CAACE,gBAAnC,CADwB;UAE5BC,MAAM,EAAE,IAAI5K,WAAJ,CAA4ByK,MAAM,CAACI,kBAAnC,CAFoB;UAG5BC,MAAM,EAAE,IAAI9K,WAAJ,CAA4ByK,MAAM,CAACM,mBAAnC,CAHoB;UAI5B7F,KAAK,EAAE,IAAIlF,WAAJ,CAA4ByK,MAAM,CAACO,WAAnC,CAJqB;UAK5BC,SAAS,EAAE,IAAIjL,WAAJ,CAA4B,KAA5B;QALiB,CAAd,CAAhB;QAQA,KAAKkL,qBAAL,GAA6BT,MAAM,CAACO,WAApC;QACA,KAAKzH,0BAAL,CAAgC4H,IAAhC,CAAqCT,SAArC;MACD,CAXD;MAcA,IAAI/C,IAAI,CAAC1B,MAAL,GAAc,CAAd,IAAmB0B,IAAI,CAACvC,IAAL,CAAWC,CAAD,IAAOA,CAAC,CAACgF,UAAF,KAAiB,GAAlC,CAAvB,EAEE,IAAIC,eAAe,GAAG,KAAKC,kBAAL,CAAwB5C,IAAxB,EAA8B,GAA9B,EAAmCuC,aAAnC,EAAkDD,WAAlD,CAAtB;MAEFK,eAAe,CAACE,OAAhB,CAAyBC,MAAD,IAAW;QACjC,IAAIC,SAAS,GAAG,IAAIzK,SAAJ,CAAc;UAC5BqF,EAAE,EAAE,IAAItF,WAAJ,CAA4ByK,MAAM,CAACE,gBAAnC,CADwB;UAE5BC,MAAM,EAAE,IAAI5K,WAAJ,CAA4ByK,MAAM,CAACI,kBAAnC,CAFoB;UAG5BC,MAAM,EAAE,IAAI9K,WAAJ,CAA4ByK,MAAM,CAACM,mBAAnC,CAHoB;UAI5B7F,KAAK,EAAE,IAAIlF,WAAJ,CAA4ByK,MAAM,CAACO,WAAnC,CAJqB;UAK5BC,SAAS,EAAE,IAAIjL,WAAJ,CAA4B,KAA5B,CALiB;UAM5BoL,WAAW,EAAE,IAAIpL,WAAJ,CACX,IADW,EAEXyK,MAAM,CAACE,gBAAP,IAA2B,KAA3B,GACInK,uBAAuB,CAAC6K,QAAxB,CAAiC,EAAjC,CADJ,GAEI,IAJO;QANe,CAAd,CAAhB;QAcA,KAAKC,qBAAL,GAA6Bb,MAAM,CAACO,WAApC;QACA,KAAKvH,yBAAL,CAA+B0H,IAA/B,CAAoCT,SAApC;MACD,CAjBD;MAmBA,IAAI/C,IAAI,CAAC1B,MAAL,GAAc,CAAd,IAAmB0B,IAAI,CAACvC,IAAL,CAAWC,CAAD,IAAOA,CAAC,CAACgF,UAAF,KAAiB,GAAlC,CAAvB,EACE1C,IAAI,CAACvC,IAAL,CAAWC,CAAD,IAAOA,CAAC,CAACgF,UAAF,KAAiB,GAAlC,EACGkB,OADH,CACWf,OADX,CACoBC,MAAD,IAAW;QAC1B,IAAIA,MAAM,CAACxD,eAAP,KAA2B,KAA/B,EAAsC;UACpC,IAAIuE,cAAc,GAAG;YACnBlG,EAAE,EAAEmF,MAAM,CAACE,gBADQ;YAEnBC,MAAM,EAAEH,MAAM,CAACI,kBAFI;YAGnBC,MAAM,EAAEL,MAAM,CAACM,mBAHI;YAInB7F,KAAK,EAAEuF,MAAM,CAACO;UAJK,CAArB;UAOA,KAAKtG,UAAL,CAAgB0F,qBAAhB,CAAsCe,IAAtC,CAA2CK,cAA3C;QACD,CATD,MASO,IAAIf,MAAM,CAACxD,eAAP,KAA2B,KAA/B,EAAsC;UAC3C,IAAIyD,SAAS,GAAG,IAAIzK,SAAJ,CAAc;YAC5BqF,EAAE,EAAE,IAAItF,WAAJ,CAA4ByK,MAAM,CAACE,gBAAnC,CADwB;YAE5BC,MAAM,EAAE,IAAI5K,WAAJ,CAA4ByK,MAAM,CAACI,kBAAnC,CAFoB;YAG5BC,MAAM,EAAE,IAAI9K,WAAJ,CAA4ByK,MAAM,CAACM,mBAAnC,CAHoB;YAI5B7F,KAAK,EAAE,IAAIlF,WAAJ,CAA4ByK,MAAM,CAACO,WAAnC,CAJqB;YAK5BC,SAAS,EAAE,IAAIjL,WAAJ,CAA4B,KAA5B,CALiB;YAM5BoL,WAAW,EAAE,IAAIpL,WAAJ,CACX,IADW,EAEXQ,uBAAuB,CAACiL,YAFb;UANe,CAAd,CAAhB;UAYA,KAAK3H,yBAAL,CAA+BqH,IAA/B,CAAoCT,SAApC;QACD;MACF,CA1BH;;MA4BF,IAAI/C,IAAI,CAAC1B,MAAL,GAAc,CAAd,IAAmB0B,IAAI,CAACvC,IAAL,CAAWC,CAAD,IAAOA,CAAC,CAACgF,UAAF,KAAiB,GAAlC,CAAvB,EAA+D;QAC7D,IAAIC,eAAe,GAAG,KAAKC,kBAAL,CAAwB5C,IAAxB,EAA8B,GAA9B,EAAmCuC,aAAnC,EAAkDD,WAAlD,CAAtB;QAEAK,eAAe,CAACE,OAAhB,CAAyBC,MAAD,IAAW;UACjC,IAAIC,SAAS,GAAG,IAAIzK,SAAJ,CAAc;YAC5BqF,EAAE,EAAE,IAAItF,WAAJ,CAA4ByK,MAAM,CAACE,gBAAnC,CADwB;YAE5BC,MAAM,EAAE,IAAI5K,WAAJ,CAA4ByK,MAAM,CAACI,kBAAnC,CAFoB;YAG5BC,MAAM,EAAE,IAAI9K,WAAJ,CAA4ByK,MAAM,CAACM,mBAAnC,CAHoB;YAI5B7F,KAAK,EAAE,IAAIlF,WAAJ,CAA4ByK,MAAM,CAACO,WAAnC,CAJqB;YAK5BC,SAAS,EAAE,IAAIjL,WAAJ,CAA4B,KAA5B;UALiB,CAAd,CAAhB;UAQA,KAAK0L,gBAAL,GAAwBjB,MAAM,CAACO,WAA/B;UACA,KAAKrH,qBAAL,CAA2BwH,IAA3B,CAAgCT,SAAhC;QACD,CAXD;MAYD;IAEF;EACF;;EAGDH,kBAAkB,CAAC5C,IAAD,EAAO0C,UAAP,EAAmBH,aAAnB,EAAkCD,WAAlC,EAA6C;IAC7D,IAAItC,IAAI,CAAC1B,MAAL,GAAc,CAAlB,EAAqB;MACnB,IAAI0F,YAAY,GAAGhE,IAAI,CAACvC,IAAL,CAAWC,CAAD,IAAOA,CAAC,CAACgF,UAAF,KAAiBA,UAAlC,CAAnB;MAEA,IAAI,KAAKpC,eAAL,IAAwB,IAA5B,EAAkC,OAAO0D,YAAY,CAACJ,OAApB;;MAElC,IAAKrB,aAAa,IAAIA,aAAa,CAAC0B,IAAd,IAAsB,KAAxC,IAAkD3B,WAAtD,EAAmE;QACjE,OAAO0B,YAAY,CAACJ,OAAb,CAAqBM,MAArB,CAA4BxG,CAAC,IAAIA,CAAC,CAAC4B,eAAF,IAAqBiD,aAAa,CAAC0B,IAAnC,IAA2CvG,CAAC,CAACyG,UAAF,IAAgB7B,WAA5F,CAAP;MACD,CAFD,MAGK,IAAIC,aAAa,IAAIA,aAAa,CAAC0B,IAAd,IAAsB,KAA3C,EAAkD;QACrD,OAAOD,YAAY,CAACJ,OAAb,CAAqBM,MAArB,CAA4BxG,CAAC,IAAIA,CAAC,CAAC4B,eAAF,IAAqB,EAArB,IAA2B5B,CAAC,CAACyG,UAAF,IAAgB,EAA5E,CAAP;MACD,CAFI,MAGA;QACH,OAAO,EAAP;MACD;IACF;EACF;;EAEDC,WAAW,CAACpE,IAAD,EAAK;IACd,IAAIA,IAAI,CAACqE,kBAAL,KAA4B,GAAhC,EAAqC,OAAO,aAAP,CAArC,KACK,IAAIrE,IAAI,CAACsE,UAAL,KAAoB,GAAxB,EAA6B,OAAO,QAAP,CAA7B,KACA,IAAItE,IAAI,CAACuE,eAAL,KAAyB,GAA7B,EAAkC,OAAO,aAAP,CAAlC,KACA,IAAIvE,IAAI,CAACwE,QAAL,IAAiB,GAArB,EAA0B,OAAO,gBAAP;EAChC;;EAEDlD,uBAAuB;IACrB,IAAI,KAAKpD,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KAAtC,EAA6C;MAC3C,KAAK+C,UAAL,CAAgB0H,kBAAhB,GACE,KAAK1H,UAAL,CAAgB2H,qBAAhB,CAAsCR,MAAtC,CACGxG,CAAD,IAAOA,CAAC,CAACyG,UAAF,IAAgB,KAAKjG,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KAD3D,KAEK,EAHP;;MAKA,IAAI,KAAKiI,aAAT,EAAwB;QACtB,KAAKC,UAAL,CAAgB,KAAKD,aAArB;MACD;;MAED,KAAK0C,2BAAL;;MACA,IAAI,KAAKtG,mBAAL,IAA4B,KAAKA,mBAAL,CAAyBC,MAAzB,GAAkC,CAA9D,IAAmE,KAAKF,qBAA5E,EAAmG;QACjG,KAAKA,qBAAL,CAA2BG,YAA3B,CAAwC,KAAKF,mBAA7C;MACD;IAEF,CAhBoB,CAiBrB;;;IACA,KAAKuG,SAAL,CAAe,IAAf,EAAqB,KAAKtE,eAA1B,EAA2C,KAAKuE,UAAhD,EAlBqB,CAmBrB;EACD;;EAGOC,oBAAoB,CAACxE,eAAD,EAAkBd,YAAlB,EAAgCF,eAAhC,EAA+C;IACzE,KAAK/F,cAAL,CACGwL,mBADH,CACuBzE,eADvB,EACwCd,YADxC,EACsDF,eADtD,EAEGjC,SAFH,CAEc2C,IAAD,IAAS;MAClB,KAAKjD,UAAL,CAAgB2H,qBAAhB,GAAwC1E,IAAI,CAACgF,GAAL,CAAUC,IAAD,IAAS;QACxD,IAAIjF,IAAI,GAAG;UACTrC,EAAE,EAAEsH,IAAI,CAACC,sBADA;UAETlL,KAAK,EAAEiL,IAAI,CAACE,6BAFH;UAGThB,UAAU,EAAEc,IAAI,CAAC5G;QAHR,CAAX;QAKA,OAAO2B,IAAP;MACD,CAPuC,CAAxC;;MAQA,IAAI,KAAKoF,2BAAT,EAAsC;QACpC,KAAKA,2BAAL;MACD;IACF,CAdH;EAeD;;EAED3E,QAAQ,CAACT,IAAD,EAAK;IACX,IAAIqF,cAAc,GAAG,IAAIzG,OAAJ,CACnB,CAAC0G,OAAD,EAAUC,MAAV,KAAsB,KAAKpD,sBAAL,GAA8BmD,OADjC,CAArB;IAGA,IAAIE,mBAAmB,GAAG,IAAI5G,OAAJ,CACxB,CAAC0G,OAAD,EAAUC,MAAV,KAAsB,KAAKH,2BAAL,GAAmCE,OADjC,CAA1B;IAIA,KAAKlF,KAAL,GAAa,KAAb;IAEA,KAAKqF,UAAL,GAAkBzM,WAAW,CAAC0M,WAA9B;IACAF,mBAAmB,CAACG,IAApB,CAAyB,MAAK;MAC5B,KAAK5I,UAAL,CAAgB0H,kBAAhB,GACE,KAAK1H,UAAL,CAAgB2H,qBAAhB,CAAsCR,MAAtC,CACGxG,CAAD,IAAOA,CAAC,CAACyG,UAAF,IAAgBnE,IAAI,CAAC4F,UAD9B,CADF;IAID,CALD,EAXW,CAiBX;;IACA,KAAKtF,eAAL,GAAuBN,IAAI,CAACK,WAA5B;IACA,KAAKnC,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCgH,QAAlC,CAA2C1B,IAAI,CAAC4F,UAAhD;IACA,KAAK1H,KAAL,CAAWC,QAAX,CAAoBhE,OAApB,CAA4BuH,QAA5B,CAAqC1B,IAAI,CAACK,WAA1C;IACA,KAAKnC,KAAL,CAAWC,QAAX,CAAoB/D,WAApB,CAAgCsH,QAAhC,CAAyC1B,IAAI,CAAC6F,WAA9C;IACA,KAAK3H,KAAL,CAAWC,QAAX,CAAoB9D,UAApB,CAA+BqH,QAA/B,CAAwC1B,IAAI,CAAC8F,UAA7C;IACA,KAAK5H,KAAL,CAAWC,QAAX,CAAoBvD,gBAApB,CAAqC8G,QAArC,CAA8C1B,IAAI,CAAC+F,gBAAnD;IACA,KAAK7H,KAAL,CAAWC,QAAX,CAAoBtD,mBAApB,CAAwC6G,QAAxC,CAAiD1B,IAAI,CAACgG,mBAAtD;IACA,KAAK9H,KAAL,CAAWC,QAAX,CAAoBrD,YAApB,CAAiC4G,QAAjC,CACEuE,MAAM,CAACjG,IAAI,CAACkG,YAAN,CAAN,CAA0BC,QAA1B,EADF;IAGA,KAAKjI,KAAL,CAAWC,QAAX,CAAoBpD,uBAApB,CAA4C2G,QAA5C,CACE,CAAC1B,IAAI,CAACoG,uBAAL,IAAgC,EAAjC,EAAqCC,IAArC,EADF;IAIA,KAAKnI,KAAL,CAAWC,QAAX,CAAoBnD,yBAApB,CAA8C0G,QAA9C,CACE1B,IAAI,CAACsG,yBADP;IAKA,KAAK1B,SAAL,CAAe5E,IAAI,CAACK,WAAL,KAAqB,IAApC,EAA0CL,IAAI,CAACK,WAA/C,EAA4DL,IAAI,CAAC1C,KAAjE,EArCW,CAsCX;;IACA,IAAI0C,IAAI,CAACuG,aAAT,EAAwB;MACtB,IAAIC,kBAAkB,GAAGxG,IAAI,CAACuG,aAAL,CAAmBE,KAAnB,CAAyB,GAAzB,CAAzB;MACA,KAAKzF,KAAL,CAAW7C,QAAX,CAAoBlD,aAApB,CAAkCyG,QAAlC,CAA2C8E,kBAAkB,CAAC,CAAD,CAA7D;MACA,KAAKxF,KAAL,CAAW7C,QAAX,CAAoBhD,cAApB,CAAmCuG,QAAnC,CAA4C8E,kBAAkB,CAAC,CAAD,CAA9D;MACA,KAAKxF,KAAL,CAAW7C,QAAX,CAAoB/C,aAApB,CAAkCsG,QAAlC,CAA2C8E,kBAAkB,CAAC,CAAD,CAA7D;IACD;;IACD,KAAKxF,KAAL,CAAW7C,QAAX,CAAoB9C,YAApB,CAAiCqG,QAAjC,CAA0C1B,IAAI,CAAC0G,YAA/C;IAIA,KAAK1F,KAAL,CAAW7C,QAAX,CAAoB7C,qBAApB,CAA0CoG,QAA1C,CACE1B,IAAI,CAAC2G,qBADP;IAIA,KAAK3F,KAAL,CAAW7C,QAAX,CAAoB/B,mBAApB,CAAwCsF,QAAxC,CAAiD1B,IAAI,CAAC4G,mBAAtD;IAEA,KAAK5F,KAAL,CAAW7C,QAAX,CAAoB7B,kBAApB,CAAuCoF,QAAvC,CAAgD1B,IAAI,CAAC6G,kBAArD;IACA,KAAK7F,KAAL,CAAW7C,QAAX,CAAoB5B,aAApB,CAAkCmF,QAAlC,CAA2C1B,IAAI,CAAC8G,aAAhD,EAxDW,CAyDX;IACA;IACA;;IAEA,IAAIC,eAAe,GACjB,CACE/G,IAAI,CAACgH,iBAAL,IACAhH,IAAI,CAACiH,iBADL,IAEAjH,IAAI,CAACkH,YAFL,IAGA,EAJF,EAKE5I,MALF,GAKW,CALX,GAMI,KANJ,GAOI,IARN;IASA,KAAK0C,KAAL,CAAW7C,QAAX,CAAoB1C,UAApB,CAA+BiG,QAA/B,CAAwCqF,eAAxC;IACA,KAAKlC,UAAL,GAAkB7E,IAAI,CAAC1C,KAAvB;IAEA+H,cAAc,CAACM,IAAf,CAAoB,MAAK;MACvB,KAAKzD,UAAL,CAAgB,KAAKD,aAArB,EAAoCjC,IAAI,CAACoG,uBAAzC,EADuB,CAGvB;MACA;;MACA,KAAKe,mBAAL,CACEnH,IAAI,CAACoH,uBADP,EAEE,yBAFF,EALuB,CASvB;;MAEA,IAAIpH,IAAI,CAAC8G,aAAT,EAAwB;QACtB,KAAK9F,KAAL,CAAW7C,QAAX,CAAoB7B,kBAApB,CAAuCoF,QAAvC,CAAgD,IAAhD;MACD;IACF,CAdD;IAeA,KAAKlB,cAAL,CAAoB6G,YAApB,CAAiC/I,MAAjC,GAA0C,CAA1C,IAA+C,KAAKkC,cAAL,CAAoB6G,YAApB,CAAiC,CAAjC,EAAoCC,UAApC,IAAkD,EAAjG,GAAsG,KAAKtG,KAAL,CAAW7C,QAAX,CAAoB5C,UAApB,CAA+BmG,QAA/B,CAAwC,IAAxC,CAAtG,GAAsJ,IAAtJ,CAxFW,CAyFX;;IAGA,KAAK6F,oBAAL,CAA0B,KAAKjH,eAA/B,EAAgD,KAAKd,YAArD,EAAmE,KAAKF,eAAxE;EACD;;EAEDkI,sBAAsB;IACpB,MAAMC,WAAW,GAAG,KAAKrJ,qBAAL,CAA2BsJ,YAA3B,CAAwCC,EAAxC,CAA2C,CAA3C,CAApB;IACA,KAAKvJ,qBAAL,CAA2BsJ,YAA3B,CAAwClF,KAAxC;IACA,KAAKpE,qBAAL,CAA2BsJ,YAA3B,CAAwClE,IAAxC,CAA6CiE,WAA7C;EACD;;EAEOF,oBAAoB,CAACvN,KAAD,EAAQwF,YAAR,EAAsBF,eAAtB,EAAqC;IAC/D,KAAK/F,cAAL,CAAoBqO,oBAApB,CAAyC5N,KAAzC,EAAgDwF,YAAhD,EAA8DF,eAA9D,EAA+EjC,SAA/E,CAAyF0E,QAAQ,IAAG;MAClG,KAAK8F,gBAAL,GAAwB9F,QAAxB;IACD,CAFD;EAGD;;EAED+F,kBAAkB,CAAC9N,KAAD,EAAM;IACtB,KAAKN,SAAL,CAAeqO,IAAf;;IACA,IAAI,KAAK3J,qBAAT,EAAgC;MAC9B,IAAI,CAAC,KAAKgC,KAAV,EAAiB;QACf,KAAKhC,qBAAL,CAA2BoC,cAA3B,GAA4C,KAAKA,cAAjD;QACA,KAAKwH,YAAL,GAAoB,KAAKvI,OAAzB;QACA,IAAI4F,cAAc,GAAG,IAAIzG,OAAJ,CACnB,CAAC0G,OAAD,EAAUC,MAAV,KAAsB,KAAKnH,qBAAL,CAA2B+D,sBAA3B,GAAoDmD,OADvD,CAArB;QAGAD,cAAc,CAACM,IAAf,CAAoB,MAAK;UACvB,KAAKvH,qBAAL,CAA2B6J,gBAA3B;QACD,CAFD;MAGD;;MACD,KAAKvO,SAAL,CAAemH,IAAf;IACD;;IACD,IAAI7G,KAAK,IAAI,KAAKsG,eAAL,IAAwBtG,KAArC,EAA4C;MAC1C,KAAK6H,cAAL,CAAoB7H,KAApB;IACD;;IACD,IAAKA,KAAK,IAAI,KAAKkO,aAAL,IAAsB,CAAhC,IAAuC,KAAK5H,eAAL,IAAwBtG,KAAnE,EAA2E;MACzE,KAAKuN,oBAAL,CAA0BvN,KAA1B,EAAiC,KAAKwF,YAAtC,EAAoD,KAAKF,eAAzD;MACA,KAAK6I,mBAAL,CAAyBnO,KAAzB,EAAgC,KAAKsF,eAArC,EAAsD,KAAKE,YAA3D;MACA,KAAKsF,oBAAL,CAA0B9K,KAA1B,EAAiC,KAAKwF,YAAtC,EAAoD,KAAKF,eAAzD;MACA,KAAK0C,UAAL;MACA,KAAKkG,aAAL;IACD,CAND,MAMO;MACL;IACD;;IAED,IAAI,KAAK5H,eAAL,KAAyBtG,KAAzB,IAAkC,CAAC,KAAKiF,WAA5C,EAAyD;MACvD,KAAKf,KAAL,CAAWgD,KAAX;MACA,KAAKF,KAAL,CAAWE,KAAX;;MACA,IAAI,CAAC,KAAKd,KAAN,IAAepG,KAAK,KAAK,KAAKwG,cAAL,CAAoBH,WAAjD,EAA8D;QAC5D,KAAKI,QAAL,CAAc,KAAKD,cAAnB;MACD;;MACD,IAAI,KAAKrD,cAAT,EAAyB;QACvB,KAAKA,cAAL,CAAoBN,SAApB,GAAgC,KAAhC;MACD;IAEF;;IAGD,KAAKyD,eAAL,GAAuBtG,KAAvB;;IAEA,IAAIA,KAAK,IAAIxB,QAAQ,CAAC4P,EAAlB,IAAwBpO,KAAK,IAAIxB,QAAQ,CAAC6P,EAA9C,EAAkD,CAChD;MACA;MAEA;MACA;IACD;;IAED,KAAKC,OAAL,GAAe,KAAKC,kBAAL,CAAwB9K,IAAxB,CACZ+K,GAAD,IACEA,GAAG,CAACnI,WAAJ,CAAgBoI,QAAhB,CAAyBzO,KAAzB,KACAwO,GAAG,CAAClJ,eAAJ,CAAoBmJ,QAApB,CAA6B,KAAKnJ,eAAlC,CAHW,CAAf;IAKA,KAAKoJ,eAAL,GAAuB,KAAKJ,OAAL,CAAaK,YAApC;IACA,KAAKpL,KAAL,GAAa,KAAK+K,OAAL,CAAa/K,KAA1B;EAED;;EAmEDqL,kBAAkB,CAAC5I,IAAD,EAAY;IAC5B,KAAKgB,KAAL,CAAWzB,GAAX,CAAe,mBAAf,EAAoC,UAApC,EAAgDsD,OAAhD,CAAyDgG,OAAD,IAAY;MAClE,IAAI7I,IAAI,IAAIA,IAAI,CAACyI,QAAL,CAAcI,OAAO,CAAC1K,QAAR,CAAiBR,EAAjB,CAAoB3D,KAApB,CAA0BmM,QAA1B,EAAd,CAAZ,EAAiE;QAC/D0C,OAAO,CAAC1K,QAAR,CAAiBmF,SAAjB,CAA2B5B,QAA3B,CAAoC,IAApC;MACD;IACF,CAJD;EAKD;;EAEDyF,mBAAmB,CAACnH,IAAD,EAAc8I,WAAd,EAAiC;IAClD,IAAI9I,IAAJ,EACE,KAAKgB,KAAL,CAAWzB,GAAX,CAAeuJ,WAAf,EAA4B,UAA5B,EAAwCjG,OAAxC,CAAiDgG,OAAD,IAAY;MAC1D,IAAIE,GAAG,GAAG/I,IAAI,CAACkE,MAAL,CACPxG,CAAD,IAAOA,CAAC,CAACmC,aAAF,IAAmBgJ,OAAO,CAAC1K,QAAR,CAAiBR,EAAjB,CAAoB3D,KAApB,CAA0BmM,QAA1B,EADlB,CAAV;;MAGA,IAAI4C,GAAG,CAACzK,MAAR,EAAgB;QACduK,OAAO,CAAC1K,QAAR,CAAiBmF,SAAjB,CAA2B5B,QAA3B,CAAoC,IAApC;;QACA,IAAImH,OAAO,CAAC1K,QAAR,CAAiB6K,cAAjB,CAAgC,aAAhC,CAAJ,EAAoD;UAClDH,OAAO,CAAC1K,QAAR,CAAiBsF,WAAjB,CAA6B/B,QAA7B,CAAsCqH,GAAG,CAAC,CAAD,CAAH,CAAOtF,WAA7C;QACD;MACF;IACF,CAVD;EAWH;;EAEDwF,eAAe,CAACjJ,IAAD,EAAY;IACzB,KAAKgB,KAAL,CAAWzB,GAAX,CAAe,cAAf,EAA+B,UAA/B,EAA2CsD,OAA3C,CAAoDgG,OAAD,IAAY;MAC7D,IAAI7I,IAAI,IAAIA,IAAI,CAACyI,QAAL,CAAcI,OAAO,CAAC1K,QAAR,CAAiBR,EAAjB,CAAoB3D,KAApB,CAA0BmM,QAA1B,EAAd,CAAZ,EAAiE;QAC/D0C,OAAO,CAAC1K,QAAR,CAAiBmF,SAAjB,CAA2B5B,QAA3B,CAAoC,IAApC;MACD;IACF,CAJD;EAKD;;EAEDnB,UAAU;IACR,KAAK7G,SAAL,CAAeqO,IAAf;IACA,KAAKxO,cAAL,CACG2P,cADH,GAEG7L,SAFH,CAEc2C,IAAD,IAA6B;MACtC,KAAKtG,SAAL,CAAemH,IAAf,GADsC,CAEtC;;MACA,IAAIsI,OAAO,GAAGnJ,IAAI,CAACvC,IAAL,CAAW+K,GAAD,IACtBA,GAAG,CAAChJ,YAAJ,CAAiBiJ,QAAjB,CAA0B,KAAKjJ,YAA/B,CADY,CAAd;;MAIA,IAAI,CAAC2J,OAAL,EAAc;QACZ;MACD;;MACD,KAAKZ,kBAAL,GAA0BY,OAAO,CAACC,cAAlC,CAVsC,CAYtC;;MACA,IAAIC,QAAQ,GAAG,KAAKd,kBAAL,CAAwBvD,GAAxB,CAA4B,UAAUwD,GAAV,EAAa;QACtD,OAAOA,GAAG,CAACnI,WAAX;MACD,CAFc,CAAf;MAGAgJ,QAAQ,GAAGA,QAAQ,CAACnF,MAAT,CAAgB,CAACoF,CAAD,EAAIC,CAAJ,KAAUF,QAAQ,CAACG,OAAT,CAAiBF,CAAjB,MAAwBC,CAAlD,CAAX;MACA,KAAKE,iBAAL,GAAyB,KAAKC,WAAL,CAAiBxF,MAAjB,CAAyBsE,GAAD,IAC/Ca,QAAQ,CAACZ,QAAT,CAAkBD,GAAG,CAACxO,KAAtB,CADuB,CAAzB,CAjBsC,CAqBtC;;MACA,MAAM2P,eAAe,GAAG,KAAKlK,OAAL,GAAe,KAAKA,OAApB,GAA8B4J,QAAQ,CAAC,CAAD,CAA9D;MACA,KAAKf,OAAL,GAAe,KAAKC,kBAAL,CAAwB9K,IAAxB,CACZ+K,GAAD,IACEA,GAAG,CAACnI,WAAJ,CAAgBoI,QAAhB,CAAyBkB,eAAzB,KACAnB,GAAG,CAAClJ,eAAJ,CAAoBmJ,QAApB,CAA6B,KAAKnJ,eAAlC,CAHW,CAAf;MAKA,KAAKoJ,eAAL,GAAuB,KAAKpI,eAAL,GACnB,KAAKgI,OAAL,CAAaK,YADM,GAEnB,KAAKL,OAAL,CAAasB,KAFjB;MAGA,KAAKrM,KAAL,GAAa,KAAK+K,OAAL,CAAa/K,KAA1B;;MACA,IAAI,KAAKkC,OAAT,EAAkB;QAChB;QACA,KAAKvB,KAAL,CAAWC,QAAX,CAAoBhE,OAApB,CAA4BuH,QAA5B,CAAqC,KAAKjC,OAA1C;QACA,KAAKa,eAAL,GAAuB,KAAKb,OAA5B;QACA,KAAKqI,kBAAL,CAAwB6B,eAAxB;MACD;;MAED,IAAI,KAAK1K,WAAT,EAAsB,CACpB;MACD;IACF,CA5CH;EA6CD;;EAEDkJ,mBAAmB,CAAC9H,WAAD,EAAcb,YAAd,EAA4BF,eAA5B,EAA2C;IAC5D,KAAK/F,cAAL,CACG4O,mBADH,CACuB9H,WADvB,EACoCb,YADpC,EACkDF,eADlD,EAEGjC,SAFH,CAEc2C,IAAD,IAAS;MAClB,KAAKjD,UAAL,CAAgBC,gBAAhB,GAAmCgD,IAAI,CAACgF,GAAL,CAAUC,IAAD,IAAS;QACnD,IAAI4E,IAAI,GAAG,EAAX;;QACA,QAAQ5E,IAAI,CAAC6E,kBAAb;UACE,KAAK,IAAL;YAAW;cACTD,IAAI,GAAG,cAAP;cACA;YACD;;UACD,KAAK,IAAL;YAAW;cACTA,IAAI,GAAG,iBAAP;cACA;YACD;;UACD,KAAK,IAAL;YAAW;cACTA,IAAI,GAAG,+BAAP;cACA;YACD;QAZH;;QAcA,IAAI7J,IAAI,GAAG;UACThG,KAAK,EAAEiL,IAAI,CAAC5G,mBADH;UAETtE,IAAI,EAAEkL,IAAI,CAAC8E,0BAFF;UAGTF,IAAI,EAAEA,IAHG;UAIT5F,IAAI,EAAEgB,IAAI,CAAC5G,mBAJF;UAKT2L,eAAe,EAAE/E,IAAI,CAAC+E,eALb;UAMTC,QAAQ,EAAEhF,IAAI,CAACgF,QANN;UAOTC,YAAY,EAAEjF,IAAI,CAACiF;QAPV,CAAX;QASA,OAAOlK,IAAP;MACD,CA1BkC,CAAnC;;MA4BA,IAAI,CAAC,KAAKI,KAAV,EAAiB;QACf,KAAKuE,2BAAL,GADe,CAEf;;QACA,IAAI,KAAKtG,mBAAL,IAA4B,KAAKA,mBAAL,CAAyBC,MAAzB,GAAkC,CAA9D,IAAmE,KAAKF,qBAA5E,EAAmG;UACjG,KAAKA,qBAAL,CAA2BG,YAA3B,CAAwC,KAAKF,mBAA7C;QACD;MACF;;MAED,IAAI,KAAKzB,iBAAT,EAA4B;QAC1B,KAAKA,iBAAL,CAAuBC,SAAvB,GAAmC,IAAnC;QACA,KAAKD,iBAAL,CAAuBE,gBAAvB,GACE,KAAKC,UAAL,CAAgBC,gBADlB;MAED;IAEF,CA7CH;EA8CD,CAl2BgC,CAo2BjC;;;EACA2H,2BAA2B;IACzB,IAAI,KAAKzG,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KAAtC,EAA6C;MAC3C,IAAI,KAAK+C,UAAL,CAAgBC,gBAApB,EAAsC;QACpC,KAAKqB,mBAAL,GAA2B,KAAKtB,UAAL,CAAgBC,gBAAhB,CACxBkH,MADwB,CACjBlE,IAAI,IAAG;UACb,OAAOA,IAAI,CAACiE,IAAL,CAAUkG,KAAV,CAAgB,KAAKjM,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KAAlD,KAA6DgG,IAAI,CAACiE,IAAL,CAAU3F,MAAV,IAAoB,KAAKJ,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KAAlC,CAAwCsE,MAAhI;QACD,CAHwB,EAIxB0G,GAJwB,CAInBwD,GAAD,IAAa;UAAG,OAAOA,GAAG,CAAC0B,YAAX;QAAyB,CAJrB,CAA3B;MAKD;IACF,CARD,MAQO;MACL,KAAK7L,mBAAL,GAA2B,IAA3B;IACD;EACF;;EAGD+L,6BAA6B;IAC3B;IACA,IAAI,KAAK9J,eAAL,IAAwB,IAA5B,EAAkC;MAChC;MACA;MACA;MACA;MACA,KAAKlC,qBAAL,CAA2BiM,wBAA3B,GACE,KAAKtN,UAAL,CAAgBuN,wBADlB;IAED,CAPD,MAQK,IAAI,KAAKhK,eAAL,IAAwB,IAA5B,EAAkC;MACrC;MACA;MACA;MACA;MACA,KAAKlC,qBAAL,CAA2BiM,wBAA3B,GACE,KAAKtN,UAAL,CAAgBwN,wBADlB;IAED;;IACD,IAAI,CAAC,KAAKnK,KAAN,IAAe,KAAKE,eAAL,IAAwB,IAA3C,EAAiD;MAC/C,KAAKlC,qBAAL,CAA2BoM,kCAA3B;IACD;EACF;;EAEDC,kBAAkB,CAACzQ,KAAD,EAAe;IAC/B,IAAI;MACF;MAEA,IAAI,CAAC,KAAKkE,KAAL,CAAWwM,KAAhB,EAAuB;QACrB;MACD;;MAED,IAAIC,SAAS,GAAG,KAAK9C,gBAAL,CAAsBpK,IAAtB,CAA2BC,CAAC,IAAIA,CAAC,CAACkN,UAAF,KAAiB,KAAK1M,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KAAnF,CAAhB;MACA,KAAK6Q,cAAL,GAAsBF,SAAS,GAAGA,SAAS,CAACG,eAAV,IAA6B,GAAhC,GAAsC,KAArE;;MAEA,IAAI,KAAK5M,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KAAlC,IAA2C,IAA/C,EAAqD;QACnD,KAAKwN,sBAAL;QACA,KAAKxG,KAAL,CAAW7C,QAAX,CAAoB5C,UAApB,CAA+BmG,QAA/B,CAAwC,IAAxC;MACD;;MACD,IAAI,KAAKtD,qBAAT,EAAgC;QAC9B,KAAKgM,6BAAL,GAD8B,CAE9B;MACD,CAjBC,CAkBF;;;MACA,KAAKW,kBAAL,GAA0B/Q,KAA1B,CAnBE,CAoBF;;MACA,IAAIA,KAAJ,EAAW;QACT,KAAKgR,eAAL,GAAuB,KAAKtB,WAAL,CAAiBjM,IAAjB,CACpBC,CAAD,IAAOA,CAAC,CAAC1D,KAAF,KAAY,KAAKsG,eADH,EAErBvG,IAFF;QAGA,KAAKkR,iBAAL,GAAyB,KAAKlO,UAAL,CAAgB6D,MAAhB,CAAuBnD,IAAvB,CACtBC,CAAD,IAAOA,CAAC,CAACwN,SAAF,KAAgB,KAAKhN,KAAL,CAAWC,QAAX,CAAoB/D,WAApB,CAAgCJ,KADhC,EAEvBmR,SAFF;QAIA,KAAKC,0BAAL,GAAkC,KAAKlN,KAAL,CAAWC,QAAX,CAAoBrD,YAApB,CAAiCd,KAAjC,GAC9B,KAAK+C,UAAL,CAAgBmJ,YAAhB,CAA6BzI,IAA7B,CACCC,CAAD,IAAO2N,MAAM,CAAC3N,CAAC,CAACC,EAAH,CAAN,IAAgB,KAAKO,KAAL,CAAWC,QAAX,CAAoBrD,YAApB,CAAiCd,KADxD,EAEAA,KAH8B,GAI9B,IAJJ;QAMA,IAEE,KAAKkE,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KAFpC,EAGC;UACC,KAAKsR,sBAAL,GAA8B,KAAKvO,UAAL,CAAgBC,gBAAhB,CAAiCS,IAAjC,CAC3BC,CAAD,IAAOA,CAAC,CAAC1D,KAAF,KAAY,KAAKkE,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KADzB,EAE5BD,IAFF;;QAIF,IAAI,KAAKmE,KAAL,CAAWC,QAAX,CAAoBpD,uBAApB,CAA4Cf,KAAhD,EAAuD;UACrD,IAAIuR,SAAS,GAAG,KAAKxO,UAAL,CAAgB0H,kBAAhB,CAAmChH,IAAnC,CACbC,CAAD,IAAOA,CAAC,CAACC,EAAF,IAAQ,KAAKO,KAAL,CAAWC,QAAX,CAAoBpD,uBAApB,CAA4Cf,KAD7C,CAAhB;UAGA,KAAKwR,uBAAL,GAA+BD,SAAS,CAACvR,KAAzC;QACD;;QAED,IAAI,KAAKkE,KAAL,CAAWC,QAAX,CAAoBvD,gBAApB,CAAqCZ,KAAzC,EACE,KAAKyR,oBAAL,GAA4B,KAAK1O,UAAL,CAAgB2O,oBAAhB,CAAqCjO,IAArC,CACzBC,CAAD,IAAO2N,MAAM,CAAC3N,CAAC,CAACC,EAAH,CAAN,KAAiB,KAAKO,KAAL,CAAWC,QAAX,CAAoBvD,gBAApB,CAAqCZ,KADnC,EAE1BA,KAFF;QAIF,IAAI,KAAKkE,KAAL,CAAWC,QAAX,CAAoBtD,mBAApB,CAAwCb,KAA5C,EACE,KAAK2R,gCAAL,GACE,KAAK5O,UAAL,CAAgB6O,sBAAhB,CAAuCnO,IAAvC,CACGC,CAAD,IACE2N,MAAM,CAAC3N,CAAC,CAACC,EAAH,CAAN,KAAiB,KAAKO,KAAL,CAAWC,QAAX,CAAoBtD,mBAApB,CAAwCb,KAF7D,EAGEA,KAJJ;;QAMF,IAAI,CAAC,KAAKoG,KAAV,EAAiB;UACf,IAAIyL,gBAAgB,GAClB,KAAK7K,KAAL,CAAWzB,GAAX,CAAe,mBAAf,EACApB,QADA,CACS6G,GADT,CACctH,CAAD,IAAOA,CAAC,CAAC1D,KADtB,CADF;UAGA,IAAI8R,YAAY,GACd,KAAK9K,KAAL,CAAWzB,GAAX,CAAe,mBAAf,EACApB,QADA,CACS6G,GADT,CACctH,CAAD,IAAOA,CAAC,CAAC1D,KADtB,CADF;UAGA,IAAI+R,aAAa,GAAGF,gBAAgB,CAACG,MAAjB,CAAwBF,YAAxB,CAApB;UACAC,aAAa,CAAClJ,OAAd,CAAuB7I,KAAD,IAAU;YAC9B,IAAIiS,IAAI,GAAGC,QAAQ,CAACC,cAAT,CAAwBnS,KAAxB,CAAX;YACA,IAAIiS,IAAJ,EAAUA,IAAI,CAACG,OAAL,GAAe,IAAf;YACV,IAAIC,YAAY,GAAG,oBAAoBrS,KAAK,EAA5C;YACA,IAAIsS,WAAW,GAAGJ,QAAQ,CAACC,cAAT,CAChBE,YADgB,CAAlB;;YAGA,IAAIC,WAAJ,EAAiB;cACf,IAAI7I,WAAW,GAAG,KAAK8I,kBAAL,CAAwB9O,IAAxB,CACfC,CAAD,IAAOA,CAAC,CAACmC,aAAF,KAAoB7F,KADX,EAEhByJ,WAFF;cAGA6I,WAAW,CAACtS,KAAZ,GAAoByJ,WAApB;YACD;UACF,CAbD;;UAcA,IAAI,KAAKnD,eAAL,KAAyB,KAAKE,cAAL,CAAoBH,WAAjD,EAA8D,CAC5D;UACD;;UACD,IACE,KAAKnC,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KAAlC,KAA4C,IAA5C,IACA,KAAKkE,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KAAlC,KACA,KAAKwG,cAAL,CAAoBoF,UAHtB,EAIE;YACA,KAAK5E,KAAL,CAAW7C,QAAX,CAAoB3C,aAApB,CAAkCkG,QAAlC,CAA2C,KAA3C;UACD;QACF,CAhCD,MAgCO,CACL;QACD;;QAED,IAAI,CAAC,KAAKzC,WAAV,EAAuB;UACrBuN,MAAM,CAACC,QAAP,CAAgB,CAAhB,EAAmB,CAAnB;QACD;MACF;IACF,CAtGD,CAsGE,OAAOC,EAAP,EAAW,CAAG;EACjB;;EAEDC,gBAAgB;IACd,OAAO;MAAE,oBAAoB,KAAKzO,KAAL,CAAWC,QAAX,CAAoBxD,KAApB,CAA0BX;IAAhD,CAAP;EACD;;EACD4S,gBAAgB,CAACR,OAAD,EAAQ;IACtB,KAAKhO,qBAAL,CAA2ByO,cAA3B,GAA4CT,OAA5C;EACD;;EAIKU,MAAM;IAAA;;IAAA;MAGV,IAAI;QACF,KAAI,CAAC1O,qBAAL,CAA2B2O,+BAA3B;;QACA,KAAI,CAAC3O,qBAAL,CAA2B4O,wBAA3B;;QACA,KAAI,CAACC,wBAAL;;QAEA,KAAI,CAACC,iBAAL,SACQ,KAAI,CAAC9O,qBAAL,CAA2B+O,kBAA3B,EADR;;QAEA,IACE,KAAI,CAACnM,KAAL,CAAW0J,KAAX,IACA,KAAI,CAACxM,KAAL,CAAWwM,KADX,IAEA,KAAI,CAACtM,qBAAL,CAA2BgP,IAA3B,CAAgCjP,QAAhC,CAAyCkP,gBAAzC,CAA0D3C,KAH5D,EAIE;UAEA,IACE,CAAC,KAAI,CAACtK,KAAN,IACA,KAAI,CAACE,eAAL,KAAyB,KAAI,CAACE,cAAL,CAAoBH,WAF/C,EAGE;YACA,KAAI,CAAC9G,cAAL,CACG+T,UADH,CAEI,KAAI,CAAC5N,WAFT,EAGI,KAAI,CAACE,aAHT,EAII,KAAI,CAACC,aAJT,EAMGxC,SANH,CAMc0E,QAAD,IAAa;cACtB,KAAI,CAAC3B,KAAL,GAAa,IAAb;;cACA,KAAI,CAACmN,IAAL;YACD,CATH;UAUD,CAdD,MAcO;YACL,KAAI,CAACA,IAAL;UACD;QACF;MACF,CA/BD,CA+BE,OAAOb,EAAP,EAAW;QACXc,OAAO,CAACC,KAAR,CAAcf,EAAd;MACD;IApCS;EAqCX;;EAEDa,IAAI;IAEF,IAAIG,UAAU,GAAG,IAAI3U,aAAJ,EAAjB,CAFE,CAIF;;IAEA2U,UAAU,CAACC,WAAX,GAAyB,KAAKzP,KAAL,CAAWC,QAAX,CAAoBhE,OAApB,CAA4BH,KAArD;IACA0T,UAAU,CAACE,YAAX,GAA0B,KAAKpO,YAA/B;IACAkO,UAAU,CAACG,eAAX,GAA6B,KAAKvO,eAAlC;IACAoO,UAAU,CAACtT,WAAX,GAAyB,KAAK8D,KAAL,CAAWC,QAAX,CAAoB/D,WAApB,CAAgCJ,KAAzD;IACA0T,UAAU,CAACrT,UAAX,GAAwB,KAAK6D,KAAL,CAAWC,QAAX,CAAoB9D,UAApB,CAA+BL,KAAvD;IACA0T,UAAU,CAAC5S,YAAX,GAA0B,KAAKoD,KAAL,CAAWC,QAAX,CAAoBrD,YAApB,CAAiCd,KAAjC,IAA0C,CAApE;IACA0T,UAAU,CAACI,UAAX,GAAwB,KAAK5P,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KAA1D;IACA0T,UAAU,CAAC/S,KAAX,GAAmB,KAAKuD,KAAL,CAAWC,QAAX,CAAoBxD,KAApB,CAA0BX,KAA7C;IACA0T,UAAU,CAAC9S,gBAAX,GAA8B,KAAKsD,KAAL,CAAWC,QAAX,CAAoBvD,gBAApB,CAAqCZ,KAAnE;IACA0T,UAAU,CAAC7S,mBAAX,GACE,KAAKqD,KAAL,CAAWC,QAAX,CAAoBtD,mBAApB,CAAwCb,KAD1C;IAGA0T,UAAU,CAAC3S,uBAAX,GACE,KAAKmD,KAAL,CAAWC,QAAX,CAAoBpD,uBAApB,CAA4Cf,KAD9C;IAEA0T,UAAU,CAAC1S,yBAAX,GACE,KAAKkD,KAAL,CAAWC,QAAX,CAAoBnD,yBAApB,CAA8ChB,KADhD,CApBE,CAuBF;;IACA,IAAI+T,aAAa,GACf,KAAK/M,KAAL,CAAW7C,QAAX,CAAoBhD,cAApB,CAAmCnB,KAAnC,IACA,KAAKgH,KAAL,CAAW7C,QAAX,CAAoBlD,aAApB,CAAkCjB,KADlC,IAEA,KAAKgH,KAAL,CAAW7C,QAAX,CAAoB/C,aAApB,CAAkCpB,KAHpC;IAIA,MAAMgU,aAAa,GAAG,KAAKhN,KAAL,CAAW7C,QAAX,CAAoB/C,aAApB,CAAkCpB,KAAxD;IACA0T,UAAU,CAACzS,aAAX,GAA2B8S,aAAa,GACpC,GAAG,KAAK/M,KAAL,CAAW7C,QAAX,CAAoBhD,cAApB,CAAmCnB,KAAK,IAAI,KAAKgH,KAAL,CAAW7C,QAAX,CAAoBlD,aAApB,CAAkCjB,KAAK,IAAI,KAAKgH,KAAL,CAAW7C,QAAX,CAAoB/C,aAApB,CAAkCpB,KAAK,EAD7F,GAEpCgU,aAAa,GACX,KAAKA,aAAa,EADP,GAEX,EAJN;IAKAN,UAAU,CAACrS,YAAX,GAA0B,KAAK2F,KAAL,CAAW7C,QAAX,CAAoB9C,YAApB,CAAiCrB,KAAjC,GACtB,KAAKgH,KAAL,CAAW7C,QAAX,CAAoB9C,YAApB,CAAiCrB,KADX,GAEtB,KAFJ;IAIA0T,UAAU,CAACpS,qBAAX,GACE,KAAK0F,KAAL,CAAW7C,QAAX,CAAoB7C,qBAApB,CAA0CtB,KAA1C,IAAmD,CADrD;IAGA0T,UAAU,CAACjS,UAAX,GAAwB,KAAKuF,KAAL,CAAW7C,QAAX,CAAoB1C,UAApB,CAA+BzB,KAAvD;IAEA0T,UAAU,CAAC3R,YAAX,GAA0B,KAAKkS,UAAL,GAAkB,KAAKjN,KAAL,CAAW7C,QAAX,CAAoBpC,YAApB,CAAiC/B,KAAjC,CACzCkK,MADyC,CACjCxG,CAAD,IAAOA,CAAC,CAAC4F,SADyB,EACd0B,GADc,CACTtH,CAAD,IAAOA,CAAC,CAACC,EAAF,CAAKwI,QAAL,EADG,CAAlB,GACkC,EAD5D;IAGAuH,UAAU,CAAC/R,iBAAX,GAA+B,KAAKsS,UAAL,GAAkB,KAAKjN,KAAL,CAAW7C,QAAX,CAAoBxC,iBAApB,CAAsC3B,KAAtC,CAC9CkK,MAD8C,CACtCxG,CAAD,IAAOA,CAAC,CAAC4F,SAD8B,EACnB0B,GADmB,CACdtH,CAAD,IAAOA,CAAC,CAACC,EAAF,CAAKwI,QAAL,EADQ,CAAlB,GAC6B,EAD5D;IAEAuH,UAAU,CAAC7R,iBAAX,GAA+B,KAAKoS,UAAL,GAAkB,KAAKjN,KAAL,CAAW7C,QAAX,CAAoBtC,iBAApB,CAAsC7B,KAAtC,CAC9CkK,MAD8C,CACtCxG,CAAD,IAAOA,CAAC,CAAC4F,SAD8B,EACnB0B,GADmB,CACdtH,CAAD,IAAO,KAAKwQ,mBAAL,CAAyBxQ,CAAzB,CADQ,CAAlB,GACyC,EADxE;IAEAgQ,UAAU,CAACxR,uBAAX,GAAqC,KAAK8E,KAAL,CAAW7C,QAAX,CAAoBjC,uBAApB,CAA4ClC,KAA5C,CAClCkK,MADkC,CAC1BxG,CAAD,IAAOA,CAAC,CAAC4F,SADkB,EACP0B,GADO,CACFtH,CAAD,IAAO,KAAKwQ,mBAAL,CAAyBxQ,CAAzB,CADJ,CAArC;IAEAgQ,UAAU,CAACtR,mBAAX,GACE,KAAK4E,KAAL,CAAW7C,QAAX,CAAoB/B,mBAApB,CAAwCpC,KAD1C;IAEA0T,UAAU,CAACpR,kBAAX,GAAgC,KAAK0E,KAAL,CAAW7C,QAAX,CAAoB7B,kBAApB,CAAuCtC,KAAvC,GAC5B,KAD4B,GAE5B,IAFJ;IAGA0T,UAAU,CAACnR,aAAX,GAA2B,KAAKyE,KAAL,CAAW7C,QAAX,CAAoB5B,aAApB,CAAkCvC,KAAlC,IAA2C,CAAtE,CAzDE,CA0DF;IACA;;IACA,IAAI,KAAKgH,KAAL,CAAW7C,QAAX,CAAoB5C,UAApB,CAA+BvB,KAAnC,EAA0C;MACxC0T,UAAU,CAACrG,YAAX,GAA0B,KAAK6F,iBAA/B;IACD,CAFD,MAGK;MACH,KAAKA,iBAAL,CAAuB5O,MAAvB,GAAgC,CAAhC;MACAoP,UAAU,CAACrG,YAAX,GAA0B,KAAK6F,iBAA/B;IACD,CAlEC,CAqEF;;;IACAQ,UAAU,CAAChO,WAAX,GAAyB,KAAKA,WAA9B;IACAgO,UAAU,CAAC9N,aAAX,GAA2B,KAAKA,aAAhC,CAvEE,CAyEF;;IACA,IAAI,KAAKX,WAAT,EAAsB;MACpB,KAAKH,iBAAL,CAAuBqP,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBV,UAAlB,CAAvB;MACA;IACD;;IAED,KAAKnU,cAAL,CACG8U,QADH,CACYX,UADZ,EACwB,KAAKtN,KAD7B,EAEG/C,SAFH,CAEc0E,QAAD,IAAa;MACtB,KAAK7D,KAAL,CAAWgD,KAAX;MACA,KAAKF,KAAL,CAAWE,KAAX;IACD,CALH;EAMD;;EAEDgN,mBAAmB,CAACxQ,CAAD,EAAE;IACnB,IAAI8K,GAAG,GAAQ,EAAf;IACAA,GAAG,CAAC8F,aAAJ,GAAoB5Q,CAAC,CAACC,EAAF,CAAKwI,QAAL,EAApB;IACAqC,GAAG,CAAC+F,WAAJ,GAAkB7Q,CAAC,CAAC+F,WAAF,CAAc0C,QAAd,EAAlB;IAEA,OAAOqC,GAAP;EACD;;EAED5D,SAAS,CAAC4J,eAAD,EAAkBnO,WAAlB,EAAuCoO,aAAvC,EAA6D;IACpE,IACE,KAAKvQ,KAAL,CAAWC,QAAX,CAAoBvD,gBAApB,CAAqCZ,KAArC,IACA,KAAKkE,KAAL,CAAWC,QAAX,CAAoBtD,mBAApB,CAAwCb,KADxC,IAEAqG,WAAW,KAAK7H,QAAQ,CAAC4P,EAH3B,EAIE;MACA,IAAIoG,eAAe,IAAI,CAAC,KAAKtQ,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KAA1D,EAAiE;MAEjE,IAAImK,UAAU,GAAG,IAAjB;MACA,IAAIyG,UAAU,GAAG,IAAjB;;MACA,IAAI4D,eAAe,KAAKnO,WAAW,IAAI7H,QAAQ,CAACkW,EAAxB,IAA8BrO,WAAW,IAAI7H,QAAQ,CAACmW,EAA3D,CAAnB,EAAmF;QACjFxK,UAAU,GAAG,KAAKjG,KAAL,CAAWC,QAAX,CAAoBvD,gBAApB,CAAqCZ,KAAlD;QACA4Q,UAAU,GAAG,KAAK1M,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KAA/C;MACD,CAHD,MAGO,IAAIqG,WAAW,IAAI7H,QAAQ,CAAC4P,EAA5B,EAAgC;QACrCwC,UAAU,GAAG,KAAK1M,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KAA/C,CADqC,CACiB;;QACtDmK,UAAU,GAAG,KAAb;MACD,CAHM,MAGA,IAAI9D,WAAW,IAAI7H,QAAQ,CAAC6P,EAA5B,EAAgC;QACrCuC,UAAU,GAAG,KAAK1M,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KAA/C,CADqC,CACiB;;QACtDmK,UAAU,GAAG,KAAKjG,KAAL,CAAWC,QAAX,CAAoBtD,mBAApB,CAAwCb,KAArD;MACD;;MAED,MAAM4U,aAAa,GAAuB;QACxCvO,WAAW,EAAE,KAAKC,eADsB;QAExCd,YAAY,EAAE,KAAKA,YAFqB;QAIxCF,eAAe,EAAE,KAAKA,eAJkB;QAKxC6E,UAAU,EAAEA,UAL4B;QAMxCyG,UAAU,EAAEA;MAN4B,CAA1C;MAQA,KAAKrR,cAAL,CAAoBqL,SAApB,CAA8BgK,aAA9B,EAA6CvR,SAA7C,CAAwD2C,IAAD,IAAS;QAE9D,KAAK7C,cAAL,CAAoBN,SAApB,GAAgC,IAAhC;;QACA,IAAI,KAAKuD,KAAL,IAAc,CAAC,KAAKA,KAAN,IAAe,KAAKE,eAAL,IAAwB,KAAKE,cAAL,CAAoBH,WAA7E,EAA0F;UACxF,KAAKlD,cAAL,CAAoB0R,aAApB,GAAoC,IAApC;QACD;;QAED,IAAI7O,IAAI,CAAC1B,MAAL,GAAc,CAAlB,EAAqB;UACnB;UACA,KAAKf,KAAL,GAAayC,IAAI,CAAC,CAAD,CAAJ,CAAQzC,KAArB,CAFmB,CAInB;;UACA,IAAIyC,IAAI,CAAC1B,MAAL,KAAgB,CAApB,EAAuB;YACrB,KAAKnB,cAAL,CAAoBN,SAApB,GAAgC,KAAhC;YAEA,KAAKqB,KAAL,CAAWC,QAAX,CAAoBxD,KAApB,CAA0B+G,QAA1B,CAAmC1B,IAAI,CAAC,CAAD,CAAJ,CAAQiE,IAA3C;UACD,CAJD,MAIO;YACL,IAAIzG,MAAM,GAAGwC,IAAI,CAACgF,GAAL,CAAS,UAAUwD,GAAV,EAAa;cACjC,OAAOA,GAAG,CAACvE,IAAX;YACD,CAFY,CAAb;YAGA,IAAI6K,SAAS,GAAG9O,IAAI,CAACgF,GAAL,CAAS,UAAUwD,GAAV,EAAa;cACpC,OAAOA,GAAG,CAACuG,WAAX;YACD,CAFe,CAAhB;YAGAvR,MAAM,GAAGA,MAAM,CAAC0G,MAAP,CAAc,CAACoF,CAAD,EAAIC,CAAJ,KAAU/L,MAAM,CAACgM,OAAP,CAAeF,CAAf,MAAsBC,CAA9C,CAAT;YACA,KAAK/L,MAAL,GAAcwC,IAAI,CAACgF,GAAL,CAAUC,IAAD,IAAS;cAC9B,IAAIjF,IAAI,GAAG;gBACTrC,EAAE,EAAEsH,IAAI,CAAChB,IADA;gBAETjK,KAAK,EAAEiL,IAAI,CAAC8J,WAFH;gBAGTxR,KAAK,EAAE0H,IAAI,CAAC1H;cAHH,CAAX;cAKA,OAAOyC,IAAP;YACD,CAPa,CAAd;YAQA,KAAK7C,cAAL,CAAoB6R,MAApB,GAA6BxR,MAA7B;YACA,KAAKL,cAAL,CAAoB8R,UAApB,GAAiCH,SAAjC;;YACA,IACE,CAAC,KAAK1O,KAAN,IACAqO,aADA,IAEA,KAAKnO,eAAL,KAAyB,KAAKE,cAAL,CAAoBH,WAH/C,EAIE;cACA,KAAKnC,KAAL,CAAWC,QAAX,CAAoBxD,KAApB,CAA0B+G,QAA1B,CAAmC+M,aAAnC;cACA,KAAKtR,cAAL,CAAoB0R,aAApB,GAAoCJ,aAApC;YACD;UACF;QACF,CApCD,MAoCO;UACL,KAAKtR,cAAL,CAAoB6R,MAApB,GAA6B,EAA7B;UACA,KAAK9Q,KAAL,CAAWC,QAAX,CAAoBxD,KAApB,CAA0B+G,QAA1B,CAAmC,IAAnC;QACD;MACF,CA/CD;IAgDD;;IACD,IAAI,KAAKtD,qBAAT,EAAgC;MAC9B,KAAKgM,6BAAL;IACD;EACF;;EAED8E,cAAc;IACZ,OACG,KAAK5O,eAAL,KAAyB,IAAzB,IACC,KAAKpC,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KADnC,IAEC,KAAKkE,KAAL,CAAWC,QAAX,CAAoBvD,gBAApB,CAAqCZ,KAFvC,IAGC,KAAKsG,eAAL,KAAyB,IAAzB,IACC,KAAKpC,KAAL,CAAWC,QAAX,CAAoBtD,mBAApB,CAAwCb,KAJ1C,IAKA,KAAKsG,eAAL,KAAyB,IAN3B;EAQD;;EAEO6O,wBAAwB,CAACnP,IAAD,EAAcoP,SAAd,EAA4B;IAC1D,IAAIC,UAAU,GAAGrP,IAAI,CAClBkE,MADc,CACNsE,GAAD,IAASA,GAAG,CAAC8G,SAAJ,KAAkBF,SADpB,EAEdpK,GAFc,CAETC,IAAD,IAAS;MACZ,IAAIjF,IAAI,GAAG;QAAErC,EAAE,EAAEsH,IAAI,CAACsK,YAAX;QAAyBvV,KAAK,EAAEiL,IAAI,CAACuK;MAArC,CAAX;MACA,OAAOxP,IAAP;IACD,CALc,CAAjB;IAMA,OAAOqP,UAAU,IAAI,EAArB;EACD;;EAEOpP,gBAAgB,CAACD,IAAD,EAAY;IAClC,KAAKjD,UAAL,CAAgB0S,aAAhB,GAAgC,KAAKN,wBAAL,CAC9BnP,IAD8B,EAE9BvH,qBAAqB,CAACiX,gBAFQ,CAAhC;IAIA,KAAK3S,UAAL,CAAgB4S,aAAhB,GAAgC,KAAKR,wBAAL,CAC9BnP,IAD8B,EAE9BvH,qBAAqB,CAACmX,gBAFQ,CAAhC;IAIA,KAAK7S,UAAL,CAAgByH,QAAhB,GAA2B,KAAK2K,wBAAL,CACzBnP,IADyB,EAEzBvH,qBAAqB,CAACoX,QAFG,CAA3B;IAIA,KAAK9S,UAAL,CAAgB+S,wBAAhB,GAA2C,KAAKX,wBAAL,CACzCnP,IADyC,EAEzCvH,qBAAqB,CAACsX,yBAFmB,CAA3C;IAIA,KAAKhT,UAAL,CAAgBiT,wBAAhB,GAA2C,KAAKb,wBAAL,CACzCnP,IADyC,EAEzCvH,qBAAqB,CAACwX,yBAFmB,CAA3C;IAIA,KAAKlT,UAAL,CAAgBwN,wBAAhB,GAA2C,KAAK4E,wBAAL,CACzCnP,IADyC,EAEzCvH,qBAAqB,CAACsX,yBAFmB,CAA3C;IAIA,KAAKhT,UAAL,CAAgBuN,wBAAhB,GAA2C,KAAK6E,wBAAL,CACzCnP,IADyC,EAEzCvH,qBAAqB,CAACwX,yBAFmB,CAA3C;IAIA,KAAKlT,UAAL,CAAgB2O,oBAAhB,GAAuC,KAAKyD,wBAAL,CACrCnP,IADqC,EAErCvH,qBAAqB,CAACyX,gBAFe,CAAvC;IAIA,KAAKnT,UAAL,CAAgB6O,sBAAhB,GAAyC,KAAKuD,wBAAL,CACvCnP,IADuC,EAEvCvH,qBAAqB,CAACoC,mBAFiB,CAAzC;IAIA,KAAKkC,UAAL,CAAgBmJ,YAAhB,GAA+B,KAAKiJ,wBAAL,CAC7BnP,IAD6B,EAE7BvH,qBAAqB,CAACqC,YAFO,CAA/B;IAIA,KAAKiC,UAAL,CAAgBoT,iBAAhB,GAAoC,KAAKhB,wBAAL,CAClCnP,IADkC,EAElCvH,qBAAqB,CAAC2X,aAFY,CAApC;;IAKA,IAAI,KAAKC,WAAL,EAAJ,EAAwB;MACtB,KAAKtT,UAAL,CAAgBuT,iBAAhB,GAAoC,KAAKnB,wBAAL,CAClCnP,IADkC,EAElCvH,qBAAqB,CAAC8X,uBAFY,CAApC;IAID,CALD,MAKO;MACL,KAAKxT,UAAL,CAAgBuT,iBAAhB,GAAoC,KAAKnB,wBAAL,CAClCnP,IADkC,EAElCvH,qBAAqB,CAAC+X,iBAFY,CAApC;IAID;;IAED,KAAKF,iBAAL,GAAyB,KAAKvT,UAAL,CAAgBuT,iBAAhB,CAAkCtL,GAAlC,CACtBwD,GAAD,IAASA,GAAG,CAAC7K,EADU,CAAzB;EAGD;;EAEO0S,WAAW;IACjB,IAAK,KAAK7Q,YAAL,IAAqB,KAArB,IAA8B,KAAKF,eAAL,IAAwB,IAAtD,IACA,KAAKE,YAAL,IAAqB,KAArB,IAA8B,KAAKF,eAAL,IAAwB,IADtD,IAEA,KAAKE,YAAL,IAAqB,KAArB,IAA8B,KAAKF,eAAL,IAAwB,IAF3D,EAGG;MAAE,OAAO,IAAP;IAAa;EACnB;;EAEO5C,uBAAuB;IAC7B,KAAKD,yBAAL,CAA+BI,SAA/B,GAA2C,IAA3C;IACA,KAAKJ,yBAAL,CAA+BgU,KAA/B,GAAuC,mBAAvC;IACA,KAAKhU,yBAAL,CAA+BuS,MAA/B,GAAwC,KAAKsB,iBAA7C;IACA,KAAK9W,GAAL,CAASyD,aAAT;EACD;;EAEDiC,cAAc;IACZ,KAAKxF,SAAL,CAAeqO,IAAf;IACA,KAAKxO,cAAL,CAAoB2P,cAApB,GAAqC7L,SAArC,CAAgDwB,GAAD,IAAQ;MACrD,KAAKnF,SAAL,CAAemH,IAAf;MACA,KAAK6P,iBAAL,GAAyB7R,GAAG,CAACqF,MAAJ,CACtBsE,GAAD,IAASA,GAAG,CAAChJ,YAAJ,IAAoB,KAApB,IAA6BgJ,GAAG,CAAChJ,YAAJ,IAAoB,KADnC,CAAzB;IAGD,CALD;EAMD;;EACDmR,qBAAqB,CAACC,UAAD,EAA0B;IAC7C,KAAKtR,eAAL,GAAuBsR,UAAU,CAACtR,eAAlC;IACA,KAAKE,YAAL,GAAoBoR,UAAU,CAACpR,YAA/B;IACA,KAAKC,OAAL,GAAemR,UAAU,CAACvQ,WAA1B;IACA,KAAKE,UAAL;EACD;;EAGDsQ,gBAAgB;IACd,IAAIC,QAAQ,GAAG,KAAf;IACA,IAAIC,aAAa,GAAG,CAAC,IAAD,EAAO,IAAP,CAApB;;IAEA,IACEA,aAAa,CAACtI,QAAd,CAAuB,KAAKnJ,eAA5B,KACAwR,QAAQ,IAAI,KAAKtR,YAFnB,EAGE;MACA,OAAO,IAAP;IACD;;IAED,OAAO,KAAP;EACD;;EAEDwR,MAAM;IACJ,KAAKC,OAAL,GAAe/E,QAAQ,CAACC,cAAT,CAAwB,SAAxB,CAAf;IACA,KAAK+E,OAAL,GAAehF,QAAQ,CAACC,cAAT,CAAwB,SAAxB,CAAf;IACA,KAAKgF,OAAL,GAAejF,QAAQ,CAACC,cAAT,CAAwB,WAAxB,CAAf;IAEA,MAAMiF,GAAG,GAAI,KAAKD,OAAL,CAAqBE,qBAArB,EAAb;IAEA,KAAKC,aAAL,GAAqB,KAAKL,OAAL,CAAaI,qBAAb,EAArB;IAEA,IAAIE,GAAG,GAAG,KAAKN,OAAL,CAAaO,YAAb,CAA0B,KAA1B,CAAV;IAEA,KAAKlY,MAAL,CAAYmY,QAAZ,CACE,KAAKN,OADP,EAEE,kBAFF,EAGE,UAAUI,GAAV,GAAgB,IAHlB;IAKA,KAAKjY,MAAL,CAAYmY,QAAZ,CACE,KAAKN,OADP,EAEE,iBAFF,EAGE,KAAKF,OAAL,CAAaS,KAAb,GAAqB,CAArB,GAAyB,KAAzB,GAAiC,KAAKT,OAAL,CAAaU,MAAb,GAAsB,CAAvD,GAA2D,IAH7D;IAKA,KAAKrY,MAAL,CAAYmY,QAAZ,CAAqB,KAAKN,OAA1B,EAAmC,mBAAnC,EAAwD,WAAxD;IACA,KAAK7X,MAAL,CAAYmY,QAAZ,CAAqB,KAAKN,OAA1B,EAAmC,qBAAnC,EAA0D,YAA1D;IACA,KAAK7X,MAAL,CAAYmY,QAAZ,CACE,KAAKN,OADP,EAEE,YAFF,EAGE,kCAHF;;IAMA,IAAI,CAAC,KAAKS,EAAN,IAAY,CAAC,KAAKC,EAAtB,EAA0B;MACxB,KAAKD,EAAL,GACE,CAACR,GAAG,CAACM,KAAJ,GAAY,KAAKT,OAAL,CAAaS,KAAb,GAAqB,CAAlC,KACC,KAAKT,OAAL,CAAaS,KAAb,GAAqB,KAAKR,OAAL,CAAaY,WADnC,CADF;MAGA,KAAKD,EAAL,GACE,CAACT,GAAG,CAACO,MAAJ,GAAa,KAAKV,OAAL,CAAaU,MAAb,GAAsB,CAApC,KACC,KAAKV,OAAL,CAAaU,MAAb,GAAsB,KAAKT,OAAL,CAAaa,YADpC,CADF;IAGD;;IAED7F,QAAQ,CAACC,cAAT,CAAwB,SAAxB,EAAmC6F,KAAnC,CAAyCC,OAAzC,GAAmD,MAAnD;IACA/F,QAAQ,CAACC,cAAT,CAAwB,WAAxB,EAAqC6F,KAArC,CAA2CC,OAA3C,GAAqD,MAArD;IACA,KAAK3Y,MAAL,CAAY4Y,QAAZ,CAAqB,KAAKhB,OAA1B,EAAmC,eAAnC;IACA,KAAK5X,MAAL,CAAY4Y,QAAZ,CAAqB,KAAKf,OAA1B,EAAmC,cAAnC;EACD;;EAEDgB,aAAa;IACXjG,QAAQ,CAACC,cAAT,CAAwB,SAAxB,EAAmC6F,KAAnC,CAAyCC,OAAzC,GAAmD,OAAnD;IACA/F,QAAQ,CAACC,cAAT,CAAwB,WAAxB,EAAqC6F,KAArC,CAA2CC,OAA3C,GAAqD,OAArD;EACD;;EAEDG,aAAa;IACXlG,QAAQ,CAACC,cAAT,CAAwB,SAAxB,EAAmC6F,KAAnC,CAAyCC,OAAzC,GAAmD,MAAnD;IACA/F,QAAQ,CAACC,cAAT,CAAwB,WAAxB,EAAqC6F,KAArC,CAA2CC,OAA3C,GAAqD,MAArD;EACD;;EAEDI,YAAY,CAACC,KAAD,EAAW;IACrB,IAAI,CAAC,KAAKC,kBAAV,EAA8B;MAC5B,KAAKA,kBAAL,GAA0B,IAA1B;MAEA,IAAIC,GAAG,GAAG,KAAKC,YAAL,CAAkBH,KAAlB,CAAV;MAEA,KAAKhZ,MAAL,CAAYmY,QAAZ,CACE,KAAKN,OADP,EAEE,qBAFF,EAGEqB,GAAG,CAAC9U,CAAJ,GAAQ,KAAKkU,EAAb,GAAkB,KAAlB,GAA0BY,GAAG,CAACE,CAAJ,GAAQ,KAAKb,EAAvC,GAA4C,IAH9C;MAMA,KAAKU,kBAAL,GAA0B,KAA1B;IACD;EACF,CA55CgC,CA85CjC;;;EACAI,SAAS,CAACL,KAAD,EAAW;IAClB,IAAI,CAAC,KAAKC,kBAAV,EAA8B;MAC5BD,KAAK,CAACM,cAAN;MAEA,KAAKL,kBAAL,GAA0B,IAA1B;MAEA,MAAMM,MAAM,GAAG,KAAKC,QAAL,CAAcR,KAAd,CAAf;MACA,KAAKhZ,MAAL,CAAYmY,QAAZ,CAAqB,KAAKN,OAA1B,EAAmC,qBAAnC,EAA0D0B,MAA1D;IACD;EACF;;EAEDC,QAAQ,CAACC,CAAD,EAAO;IACb,IAAIP,GAAJ;IACA,IAAI9U,CAAJ;IACA,IAAIgV,CAAJ;IACA;;IACAK,CAAC,CAACH,cAAF;IACA;;IACAJ,GAAG,GAAG,KAAKC,YAAL,CAAkBM,CAAlB,CAAN;IAEA;;IACArV,CAAC,GAAG8U,GAAG,CAAC9U,CAAJ,GAAQ,KAAKwT,OAAL,CAAaY,WAAb,GAA2B,CAAvC;IACAY,CAAC,GAAGF,GAAG,CAACE,CAAJ,GAAQ,KAAKxB,OAAL,CAAaa,YAAb,GAA4B,CAAxC;IACA;;IACA,IAAIrU,CAAC,GAAG,KAAKuT,OAAL,CAAaS,KAAb,GAAqB,KAAKR,OAAL,CAAa8B,WAA1C,EAAuD;MACrDtV,CAAC,GAAG,KAAKuT,OAAL,CAAaS,KAAb,GAAqB,KAAKR,OAAL,CAAaY,WAAtC;IACD;;IACD,IAAIpU,CAAC,GAAG,CAAR,EAAW;MACTA,CAAC,GAAG,CAAJ;IACD;;IACD,IAAIgV,CAAC,GAAG,KAAKzB,OAAL,CAAaU,MAAb,GAAsB,KAAKT,OAAL,CAAa+B,YAA3C,EAAyD;MACvDP,CAAC,GAAG,KAAKzB,OAAL,CAAaU,MAAb,GAAsB,KAAKT,OAAL,CAAaa,YAAvC;IACD;;IACD,IAAIW,CAAC,GAAG,CAAR,EAAW;MACTA,CAAC,GAAG,CAAJ;IACD;IACD;IAEA;IACA;;IACA;;;IAEA,IAAIG,MAAM,GAAGnV,CAAC,GAAG,KAAKkU,EAAT,GAAc,KAAd,GAAsBc,CAAC,GAAG,KAAKb,EAA/B,GAAoC,IAAjD;IACA,KAAKU,kBAAL,GAA0B,KAA1B;IACA,OAAOM,MAAP;EACD;;EAEDJ,YAAY,CAACM,CAAD,EAAE;IACZ,IAAIrV,CAAC,GAAG,CAAR;IAAA,IACEgV,CAAC,GAAG,CADN;IAEAK,CAAC,GAAGA,CAAC,IAAIvG,MAAM,CAAC8F,KAAhB;IACA;;IAEA;;IACA5U,CAAC,GAAGqV,CAAC,CAACG,KAAF,GAAU,KAAK5B,aAAL,CAAmB6B,IAAjC;IACAT,CAAC,GAAGK,CAAC,CAACK,KAAF,GAAU,KAAK9B,aAAL,CAAmB+B,GAAjC;IACA;;IACA3V,CAAC,GAAGA,CAAC,GAAG8O,MAAM,CAAC8G,WAAf;IACAZ,CAAC,GAAGA,CAAC,GAAGlG,MAAM,CAAC+G,WAAf;IAEA,OAAO;MAAE7V,CAAC,EAAEA,CAAL;MAAQgV,CAAC,EAAEA;IAAX,CAAP;EACD;;EAEDzF,wBAAwB;IACtB,IAAI,KAAK/O,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KAAlC,IAA2C,IAA/C,EAAqD;;IAErD,IACE,KAAKgH,KAAL,CAAW7C,QAAX,CAAoB7C,qBAApB,CAA0CtB,KAA1C,IACA,KAAKgH,KAAL,CAAW7C,QAAX,CAAoB7C,qBAApB,CAA0CtB,KAA1C,GACA,KAAKoE,qBAAL,CAA2BoV,qBAH7B,EAIE;MACA,KAAK7Z,OAAL,CAAa8Z,OAAb,CACE,qEADF,EAEE,SAFF;MAIA,OAAO,IAAP;IACD;EACF;;EAGDC,WAAW;IAET;IAEA,IAAIC,uBAAuB,GAAG,KAAKzV,KAAL,CAAWC,QAAX,CAAoBpD,uBAApB,CAA4Cf,KAA5C,IAAqD,KAAK+C,UAAL,CAAgB0H,kBAAhB,CAAmCO,GAAnC,CAAuCtH,CAAC,IAAIA,CAAC,CAACC,EAA9C,EAAkD8K,QAAlD,CAA2D,KAAKvK,KAAL,CAAWC,QAAX,CAAoBpD,uBAApB,CAA4Cf,KAAvG,CAAnF;IAEA,KAAKiU,UAAL,GAAkB,KAAK3N,eAAL,IAAwB,IAAxB,GAA+B,KAAKpC,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KAAlC,IAA2C,IAA3C,IAAoD,KAAKkE,KAAL,CAAWC,QAAX,CAAoBzD,aAApB,CAAkCV,KAAlC,IAA2C,IAA3C,IAAmD2Z,uBAAnD,IAA8E,CAAC,CAAC,KAAD,EAAQ,KAAR,EAAelL,QAAf,CAAwB,KAAKvK,KAAL,CAAWC,QAAX,CAAoBpD,uBAApB,CAA4Cf,KAApE,CAAlK,GAAgP,KAAKsG,eAAL,KAAyB,IAA3R;IAEA,OAAO,KAAK2N,UAAZ;EAED;;EAEDrM,WAAW;IACT;IACA,IAAI,KAAKL,kBAAT,EAA6B;MAC3B,KAAKA,kBAAL,CAAwBqS,WAAxB;IACD;EACF;;EACDC,cAAc,CAACC,WAAD,EAAoB;IAChC,MAAMC,SAAS,GAAG,KAAKna,cAAL,CAAoBia,cAApB,CAAmCC,WAAnC,CAAlB;IACA,KAAKha,gBAAL,CAAsBka,YAAtB,CAAmCD,SAAS,EAAEE,OAAX,CAAmB,UAAnB,EAA+B,EAA/B,CAAnC;IACA,OAAOF,SAAP;EACD;;AArgDgC;;;;;;;;;;;;;;;;;;;;;;;;UA8ChClc;IAASqc,OAAC,2BAAD,EAA8B;MAAEC,MAAM,EAAE;IAAV,CAA9B;;;UASTtc;IAASqc,OAAC,mBAAD,EAAsB;MAAEC,MAAM,EAAE;IAAV,CAAtB;;;UAkBTtc;IAASqc,OAAC,gBAAD,EAAmB;MAAEC,MAAM,EAAE;IAAV,CAAnB;;;UAYTtc;IAASqc,OAAC,aAAD,EAAgB;MAAEC,MAAM,EAAE;IAAV,CAAhB;;;UAETtc;IAASqc,OAAC,eAAD,EAAkB;MAAEC,MAAM,EAAE;IAAV,CAAlB;;;UAMTtc;IAASqc,OAAC,eAAD,EAAkB;MAAEC,MAAM,EAAE;IAAV,CAAlB;;;UAMTtc;IAASqc,OAAC,uBAAD,EAA0B;MAAEC,MAAM,EAAE;IAAV,CAA1B;;;UAmFTrc;IAAKoc,OAAC,aAAD;;;AAtLK9a,sBAAsB,eALlCxB,SAAS,CAAC;EACTwc,QAAQ,EAAE,oBADD;EAETC,8BAFS;;AAAA,CAAD,CAKyB,GAAtBjb,sBAAsB,CAAtB;SAAAA", "names": ["Component", "ViewChild", "Input", "ChangeDetectorRef", "Renderer2", "KitsService", "ActivatedRoute", "LoaderService", "FormArray", "FormControl", "FormGroup", "Validators", "KitCodes", "ProductDetailControls", "ProductDetailMasterDataModel", "EditRequest", "<PERSON><PERSON>", "CustomSharedValidations", "ToastrService", "ProductDetail", "StringValue", "ComponentMessageService", "SimpleProductService", "PageTitleService", "ProductDetailComponent", "constructor", "render", "kitsServiceApi", "cdr", "route", "isLoading", "toaster", "productService", "ComponentMessags", "pageTitleService", "name", "value", "addToOrder", "selectedKit", "KitType", "FilingState", "EntityName", "TotalShares", "disabled", "ExpirationDateData", "filingStatusData", "FormationType", "Color", "BylawsAndMinutes", "OperatingAgreements", "GoldStamping", "ProfessionalCorporation", "OtherProfessionalCorpType", "FormationDate", "validateFormationDate", "FormationMonth", "FormationYear", "FilingStatus", "TotalAuthorizedShares", "MultiCerts", "DesignationCK", "AddLegends", "required", "AdditionalLegends", "AdditionalLegendsFormArray", "CustomizedLegends", "CustomizeLegendsFormArray", "OtherLegends", "OtherLegendsFormArray", "LegendValues", "CloseCorporationLegends", "CloseCorpLegendsFormArray", "SpecialInstructions", "<PERSON><PERSON>", "IsCloseCorporation", "CloseCorpType", "certificateColorComponent", "CertificateColorComponent", "RenderCertificateColors", "corpTypeComponent", "CorpTypeComponent", "IsVisible", "CorporationTypes", "masterData", "corporationTypes", "detectChanges", "colorComponent", "ColorComponent", "$OnColorSelection", "subscribe", "color", "price", "colors", "find", "x", "id", "_FormRef1", "FormRef", "FormRef1", "_FormRef2", "FormRef2", "certControlsComponent", "Form1", "controls", "CertControlsComponent", "corporationTypeCode", "length", "getCertTypes", "getUpSellingData", "setTimeout", "nativeElement", "click", "Promise", "res", "UpSellingResolver", "_IsUpselling", "IsUpselling", "IsUpSelling", "getKitListData", "ngOnInit", "queryParamMap", "queryParams", "subCategoryCode", "get", "categoryCode", "kitType", "orderNumber", "decryptData", "kitLineNumber", "productNumber", "parseInt", "GetProductDetailData", "data", "MapAllMasterData", "request", "GetKitDetail", "isAdd", "productCode", "selectedKitType", "GetKitData", "savedOrderCopy", "LoadData", "GetStates", "States", "states", "hide", "GetYesNo", "certificateNumbering", "Form2", "valueChanges", "reset", "disable", "setValidators", "enable", "onCorporationTypeChange", "routesSubscription", "replaySubject", "payload", "setValue", "filingModeCheck", "ngOnDestroy", "getBulkPricing", "bulkPricing", "response", "GetLegends", "allLegendData", "mapLegends", "onLegendLoadedResolver", "onCorporateSubTypeChange", "mapLegendsOnCorpSubTypeChange", "corpSubType", "formationType", "clear", "closeCorporationTypes", "legendType", "filteredLegends", "getFilteredLegends", "for<PERSON>ach", "legend", "formGroup", "productionNumber", "value1", "webDescriptionline", "value2", "webDescriptionline2", "retailPrice", "isChecked", "AdditionalLegendPrice", "push", "legendValue", "maxValue", "CustomizedLegendPrice", "legends", "closeCorpTypes", "alphaNumeric", "OtherLegendPrice", "filteredData", "code", "filter", "optionCode", "setParValue", "parValueLeaveBlank", "noParValue", "without<PERSON>ar<PERSON><PERSON><PERSON>", "parValue", "typeOfCorporations", "allTypeOfCorporations", "setCorporationTypeCodeValue", "GetColors", "savedColor", "getCorporateSubTypes", "GetSubCorporateType", "map", "item", "corporationSubTypeCode", "corporationSubTypeDescription", "onTypesOfCorpLoadedResolver", "onLegendLoaded", "resolve", "reject", "onTypesOfCorpLoaded", "buttonText", "updateOrder", "then", "entityType", "filingState", "entityName", "bylawsAndMinutes", "operatingAgreements", "Number", "goldStamping", "toString", "professionalCorporation", "trim", "otherProfessionalCorpType", "formationDate", "formationDateSplit", "split", "filingStatus", "totalAuthorizedShares", "specialInstructions", "isCloseCorporation", "closeCorpType", "addLegendsValue", "additionalLegends", "customizedLegends", "otherLegends", "setCustomizeLegends", "closeCorporationLegends", "certificates", "reflective", "getProductMultiClass", "clearMultiCertificates", "valueToKeep", "Certificates", "at", "GetProductMultiClass", "multiClassResult", "OnKitTypeSelection", "show", "firstKitType", "loadCertificates", "selectKitFlag", "GetCorporationTypes", "PS", "LK", "product", "kitsByCategoryCode", "obj", "includes", "productImageUrl", "productImage", "setSelectedLegends", "formgrp", "controlName", "val", "hasOwnProperty", "setOtherLegends", "getkitsListing", "kitData", "individualKits", "kitCodes", "n", "i", "indexOf", "availableKitTypes", "AllKitTypes", "filteredKitCode", "image", "icon", "corpTypeCodeString", "corporationTypeDescription", "corporationCode", "corpCode", "corpTypeCode", "match", "setCertificateSignatureValues", "certificateSignatureData", "certificateSignatures2LK", "certificateSignatures2CK", "setOtherSignatureTitleDefaultValue", "ShowCertsAndByLaws", "valid", "mutli<PERSON>ert", "entityCode", "showMultiCerts", "masterClassCode", "showCertsAndBylaws", "selectedKitName", "selectedStateName", "stateCode", "stateName", "selectedGoldStampingOption", "String", "selectedEntityTypeName", "somevalue", "selectedCorporationType", "selectedBylawsOption", "bylawsAndMinutesData", "selectedOperatingAgreementOption", "operatingAgreementData", "additionalValues", "customValues", "controlValues", "concat", "html", "document", "getElementById", "checked", "textboxValue", "textboxHtml", "savedCustomLegends", "window", "scrollTo", "ex", "setSelectedColor", "ToggleMulticerts", "showMulticerts", "OnSave", "removeValidationFromDesignation", "getAuthorizedSharesCount", "validateAuthorisedShares", "multiCertificates", "getCertificateData", "Form", "CertificatesForm", "DeleteItem", "Save", "console", "error", "kitOrderVM", "ProductCode", "CategoryCode", "SubCategoryCode", "EntityType", "validFullDate", "formationYear", "showLegend", "CustomLegendMapping", "Object", "assign", "saveKits", "ProductNumber", "LegendValue", "forCorporateKit", "selectedColor", "CK", "SC", "colorsRequest", "SelectedColor", "colorName", "description", "Colors", "colorNames", "ColorCondition", "MapMasterDataByControlId", "controlId", "mappedData", "controlID", "controlValue", "controlText", "designationCk", "DesignationForCK", "designationLk", "DesignationForLK", "ParValue", "certificateSignatures1CK", "CertificateSignatureForCK", "certificateSignatures1LK", "CertificateSignatureForLK", "ByLawsAndMinutes", "legendOptionsData", "LegendOptions", "IsDeluxeKit", "certificateColors", "DeluxeCertificateColors", "CertificateColors", "Label", "UpSellingKitsData", "upSellingKitSelection", "kitDetails", "DisableCertColor", "category", "subCategories", "onLoad", "imgZoom", "imgLens", "divZoom", "dim", "getBoundingClientRect", "imgClientRect", "src", "getAttribute", "setStyle", "width", "height", "cx", "cy", "offsetWidth", "offsetHeight", "style", "display", "addClass", "ImgMouseEnter", "ImgMouseLeave", "ImgMouseMove", "event", "isMoveEventCalling", "pos", "getCursorPos", "y", "mouseMove", "preventDefault", "result", "moveLens", "e", "clientWidth", "clientHeight", "pageX", "left", "pageY", "top", "pageXOffset", "pageYOffset", "authorizedSharesCount", "warning", "showLegends", "isValidProfessionalCorp", "unsubscribe", "addRBallToWord", "inputString", "pageTitle", "setPageTitle", "replace", "args", "static", "selector", "template"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\viatech\\Ecom\\client\\acs\\src\\app\\Modules\\KitsMinutesBook\\Components\\product-detail\\product-detail.component.ts"], "sourcesContent": ["import {\r\n  Component,\r\n  OnInit,\r\n  ViewChild,\r\n  Input,\r\n  ElementRef,\r\n  ChangeDetectorRef,\r\n  Renderer2,\r\n} from \"@angular/core\";\r\nimport { KitsService } from \"../../Services/kits.service\";\r\nimport { ActivatedRoute } from \"@angular/router\";\r\nimport { LoaderService } from '../../../../Modules/Core/Services/Common/loader.service';\r\nimport { FormArray, FormControl, FormGroup, Validators } from \"@angular/forms\";\r\nimport { KitsListingModel } from \"src/app/Modules/Shared/Models/KitsModel/kits-listing.model\";\r\nimport { KitsTypeModel } from \"src/app/Modules/Shared/Models/KitsModel/kits-type.model\";\r\nimport { CorporationTypeSelectorComponent } from \"src/app/Modules/Shared/Components/formComponents/corporation-type-selector/corporation-type-selector.component\";\r\nimport { ColorSelectorComponent } from \"src/app/Modules/Shared/Components/formComponents/color-selector/color-selector.component\";\r\nimport { ColorsRequestModel } from \"src/app/Modules/Shared/Models/Common/colors-request.model\";\r\nimport { KitCodes } from \"src/app/Modules/Shared/Enums/kits-minutebook.enum\";\r\nimport { ProductDetailControls } from \"src/app/Modules/Shared/Enums/product-detail-controls.enum\";\r\nimport { ProductDetailMasterDataModel } from \"src/app/Modules/Shared/Models/KitsModel/product-detail-master-data.model\";\r\nimport { EditRequest } from \"../../../Shared/Models/KitsModel/EditRequest\";\r\nimport { Util } from \"src/app/Modules/Shared/helper/Util\";\r\nimport { CustomSharedValidations } from \"src/app/Modules/Shared/functions/custom-validations\";\r\nimport { ToastrService } from \"ngx-toastr\";\r\nimport {\r\n  ProductDetail,\r\n} from \"src/app/Modules/Shared/Models/KitsModel/ProductDetail\";\r\nimport { StringValue } from \"src/app/Modules/Shared/Enums/string-values.enum\";\r\nimport { CertificateControlsComponent } from \"../certificate-controls/certificate-controls.component\";\r\nimport { ComponentMessageService } from \"src/app/Modules/Core/Services/Common/component-message.service\";\r\nimport { SimpleProductService } from \"src/app/Modules/Core/Services/Common/simple-product.service\";\r\nimport { PageTitleService } from \"src/app/Modules/Shared/Services/Common/page-title.service\";\r\n\r\n\r\n\r\n@Component({\r\n  selector: \"app-product-detail\",\r\n  templateUrl: \"product-detail.component.html\",\r\n  styleUrls: [\"./product-detail.component.css\"],\r\n})\r\nexport class ProductDetailComponent implements OnInit {\r\n\r\n  posX: any;\r\n  posY: any;\r\n  cx: any;\r\n  cy: any;\r\n\r\n  imgZoom: any;\r\n  divZoom: any;\r\n  imgLens: any;\r\n\r\n  selectKitFlag: number = 0;\r\n\r\n  enableZoom: Boolean = true;\r\n  previewImageSrc: any;\r\n  zoomImageSrc: any;\r\n  imgClientRect: any;\r\n  isMoveEventCalling: boolean = false;\r\n  corporationTypeCode: any = [];\r\n  corporationCode: any;\r\n  firstKitType: string;\r\n  bulkPricing = [];\r\n\r\n  constructor(\r\n    private render: Renderer2,\r\n    private kitsServiceApi: KitsService,\r\n    private cdr: ChangeDetectorRef,\r\n    private route: ActivatedRoute,\r\n    private isLoading: LoaderService,\r\n    private toaster: ToastrService,\r\n    private productService: SimpleProductService,\r\n    private ComponentMessags: ComponentMessageService,\r\n    private pageTitleService: PageTitleService\r\n  ) { }\r\n\r\n  ColorComponent: ColorSelectorComponent;\r\n  CorpTypeComponent: CorporationTypeSelectorComponent;\r\n  CertificateColorComponent: ColorSelectorComponent;\r\n  CertControlsComponent: CertificateControlsComponent;\r\n  multiCertificates: any = [];\r\n\r\n  CustomizeLegendsFormArray = new FormArray([]);\r\n  AdditionalLegendsFormArray = new FormArray([]);\r\n  CloseCorpLegendsFormArray = new FormArray([]);\r\n  OtherLegendsFormArray = new FormArray([]);\r\n\r\n  @ViewChild(\"CertificateColorComponent\", { static: false })\r\n  set certificateColorComponent(\r\n    certificateColorComponent: ColorSelectorComponent\r\n  ) {\r\n    if (certificateColorComponent) {\r\n      this.CertificateColorComponent = certificateColorComponent;\r\n      this.RenderCertificateColors();\r\n    }\r\n  }\r\n  @ViewChild(\"CorpTypeComponent\", { static: false }) set corpTypeComponent(\r\n    corpTypeComponent: CorporationTypeSelectorComponent\r\n  ) {\r\n    if (corpTypeComponent) {\r\n      this.CorpTypeComponent = corpTypeComponent;\r\n      //if (this.selectedKitType === KitCodes.CK || this.selectedKitType === KitCodes.PS) {\r\n      this.CorpTypeComponent.IsVisible = true;\r\n      this.CorpTypeComponent.CorporationTypes =\r\n        this.masterData.corporationTypes;\r\n\r\n\r\n      // } else {\r\n      //   this.CorpTypeComponent.IsVisible = false;\r\n      // }\r\n      this.cdr.detectChanges();\r\n\r\n    }\r\n  }\r\n  @ViewChild(\"ColorComponent\", { static: false }) set colorComponent(\r\n    colorComponent: ColorSelectorComponent\r\n  ) {\r\n    if (colorComponent) {\r\n      this.ColorComponent = colorComponent;\r\n      // Updating product price based on the color selection\r\n      this.ColorComponent.$OnColorSelection.subscribe((color) => {\r\n        this.price = this.colors.find((x) => x.id === color).price;\r\n      });\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n  @ViewChild(\"formSubmit1\", { static: true }) formSubmit1: ElementRef;\r\n  FormRef1: ElementRef;\r\n  @ViewChild(\"submitButton1\", { static: true }) set _FormRef1(\r\n    FormRef: ElementRef\r\n  ) {\r\n    this.FormRef1 = FormRef;\r\n  }\r\n  FormRef2: ElementRef;\r\n  @ViewChild(\"submitButton2\", { static: true }) set _FormRef2(\r\n    FormRef: ElementRef\r\n  ) {\r\n    this.FormRef2 = FormRef;\r\n  }\r\n\r\n  @ViewChild(\"CertControlsComponent\", { static: false })\r\n  set certControlsComponent(\r\n    certControlsComponent: CertificateControlsComponent\r\n  ) {\r\n    if (certControlsComponent) {\r\n      certControlsComponent.FormationType =\r\n        this.Form1.controls.FormationType.value;\r\n      this.CertControlsComponent = certControlsComponent;\r\n\r\n      if (this.corporationTypeCode && this.corporationTypeCode.length > 0) {\r\n        certControlsComponent.getCertTypes(this.corporationTypeCode);\r\n      }\r\n    }\r\n    this.cdr.detectChanges();\r\n  }\r\n  UpSellingResolver;\r\n  getUpSellingData(): Promise<any> {\r\n    setTimeout(() => {\r\n      this.FormRef1.nativeElement.click();\r\n      this.FormRef2.nativeElement.click();\r\n    }, 10);\r\n    return new Promise((res) => (this.UpSellingResolver = res));\r\n  }\r\n\r\n  AllKitTypes = [\r\n    { name: \"Corporate Kit\", value: \"CK\" },\r\n    { name: \"LLC Kit\", value: \"LK\" },\r\n    { name: \"Partnership Kit\", value: \"PS\" },\r\n    { name: \"Minute Books\", value: \"M\" },\r\n  ];\r\n\r\n  showLegend: boolean = false;\r\n  buttonText = StringValue.addToOrder;\r\n  savedOrderCopy: any = {};\r\n  savedCustomLegends: any[] = [];\r\n  productImageUrl: string;\r\n  price: number = 0;\r\n  isAdd: boolean = true;\r\n  orderNumber: number = 0;\r\n  kitLineNumber: number = 0;\r\n  productNumber: number = 0;\r\n  savedColor: string;\r\n  //#region Properties for Right Pane Saved Data\r\n  selectedKitName: any;\r\n  selectedStateName: any;\r\n  selectedBylawsOption: any;\r\n  selectedGoldStampingOption: any;\r\n  selectedOperatingAgreementOption: any;\r\n  selectedEntityTypeName: any;\r\n  selectedColor: any;\r\n  selectedCorporationType: any;\r\n  //#endregion\r\n\r\n  kitType: any;\r\n  showCertsAndBylaws: any = false;\r\n  subCategoryCode: any;\r\n  categoryCode: any;\r\n  kitsByCategoryCode: KitsTypeModel[] = [];\r\n  product: KitsTypeModel = {};\r\n  selectedKitType: any;\r\n  availableKitTypes: any[] = [];\r\n  certificateColors: any[] = [];\r\n  masterData: ProductDetailMasterDataModel = new ProductDetailMasterDataModel();\r\n  colors: any[] = [];\r\n  UpSellingKitsData: KitsListingModel[] = [];\r\n  UpSellingKitsDetails: KitsTypeModel[] = [];\r\n  UpSellingKitsTypeModel: KitsTypeModel;\r\n  IsUpSelling: boolean = false;\r\n  selectedKit: any;\r\n  allLegendData: any;\r\n  onLegendLoadedResolver: any;\r\n  OtherLegendPrice: number = 0;\r\n  AdditionalLegendPrice: number = 0;\r\n  CustomizedLegendPrice: number = 0;\r\n  ProductsNotHavingCertColors = [\"DR\", \"DP\"];\r\n  onTypesOfCorpLoadedResolver: any;\r\n  multiClassResult: any[] = [];\r\n  showMultiCerts: boolean = false;\r\n  filingModeCheck: any;\r\n  UpsellingProductCode: string = \"CK\"; //Defauled to Corporate kit\r\n\r\n  routesSubscription: any;\r\n\r\n  @Input(\"IsUpselling\") set _IsUpselling(IsUpselling: boolean) {\r\n    this.IsUpSelling = IsUpselling;\r\n    if (IsUpselling) {\r\n      // Call Listing API Data\r\n      this.getKitListData();\r\n    }\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.route.queryParamMap.subscribe((queryParams) => {\r\n      this.subCategoryCode = queryParams.get(\"scc\");\r\n      this.categoryCode = queryParams.get(\"cc\");\r\n      this.kitType = queryParams.get(\"type\");\r\n      let orderNumber = Util.decryptData(queryParams.get(\"on\"));\r\n      let kitLineNumber = Util.decryptData(queryParams.get(\"kln\"));\r\n      let productNumber = Util.decryptData(queryParams.get(\"pn\"));\r\n      this.orderNumber = orderNumber ? parseInt(orderNumber, 10) : 0;\r\n      this.kitLineNumber = kitLineNumber ? parseInt(kitLineNumber, 10) : 0;\r\n      this.productNumber = productNumber ? parseInt(productNumber, 10) : 0;\r\n\r\n      this.kitsServiceApi.GetProductDetailData().subscribe((data) => {\r\n        this.MapAllMasterData(data);\r\n        if (this.orderNumber && this.kitLineNumber && this.productNumber) {\r\n          var request = new EditRequest();\r\n          request.orderNumber = this.orderNumber;\r\n          request.kitLineNumber = this.kitLineNumber;\r\n          request.productNumber = this.productNumber;\r\n          this.kitsServiceApi.GetKitDetail(request).subscribe((data) => {\r\n            this.isAdd = false;\r\n            this.subCategoryCode = data.subCategoryCode;\r\n\r\n            this.categoryCode = data.categoryCode;\r\n            this.kitType = data.productCode;\r\n            this.selectedKitType = data.productCode;\r\n            this.GetKitData();\r\n            this.savedOrderCopy = data;\r\n            this.LoadData(data);\r\n          });\r\n        } else {\r\n          this.GetKitData();\r\n        }\r\n      });\r\n    });\r\n\r\n    // Get all States from store if store is empty call states API\r\n\r\n    this.kitsServiceApi.GetStates().subscribe((States) => {\r\n      this.masterData.states = States;\r\n      this.isLoading.hide();\r\n    });\r\n\r\n\r\n    this.kitsServiceApi.GetYesNo().subscribe((data) => {\r\n      this.masterData.certificateNumbering = data;\r\n    });\r\n\r\n\r\n    this.Form2.controls.FilingStatus.valueChanges.subscribe((value) => {\r\n      if (value) {\r\n        this.Form2.controls.FormationYear.reset();\r\n        this.Form2.controls.FormationMonth.reset();\r\n        this.Form2.controls.FormationDate.reset();\r\n        this.Form2.controls.FormationYear.disable();\r\n        this.Form2.controls.FormationMonth.disable();\r\n        this.Form2.controls.FormationDate.disable();\r\n      } else {\r\n        this.Form2.controls.FormationYear.setValidators([\r\n          CustomSharedValidations.validateFormationDate(\r\n            \"FormationDate\",\r\n            \"FormationMonth\",\r\n            \"FormationYear\"\r\n          ),\r\n        ]);\r\n        this.Form2.controls.FormationYear.enable();\r\n        this.Form2.controls.FormationMonth.enable();\r\n        this.Form2.controls.FormationDate.enable();\r\n      }\r\n    });\r\n\r\n    this.Form1.controls.FormationType.valueChanges.subscribe((value) => {\r\n      if (this.IsUpSelling) {\r\n        this.onCorporationTypeChange();\r\n      }\r\n    });\r\n\r\n\r\n    if (this.IsUpSelling) {\r\n      this.routesSubscription = this.ComponentMessags.replaySubject.subscribe(data => {\r\n        if (data.payload && data.payload.value && data.payload.name == 'EntityName') {\r\n          this.Form1.controls.EntityName.setValue(data.payload.value);\r\n        }\r\n\r\n        if (data.payload && data.payload.value && data.payload.name == 'Expiration_Date') {\r\n          if (data.payload.value == 'Invalid Date') {\r\n            this.Form1.controls.ExpirationDateData.setValue('')\r\n          } else {\r\n            this.Form1.controls.ExpirationDateData.setValue(data.payload.value)\r\n          }\r\n        }\r\n\r\n        if (data.payload && data.payload.value && data.payload.name == 'filingStatusData') {\r\n          if (data.payload.value == 'NO') {\r\n            this.Form1.controls.ExpirationDateData.setValue('');\r\n          }\r\n        }\r\n\r\n        if (data.payload && data.payload.value && data.payload.name == 'TotalAuthorizedShares') {\r\n          this.Form1.controls.TotalShares.setValue(data.payload.value)\r\n        }\r\n\r\n        if (data.payload && data.payload.filingModeCheck) {\r\n          this.ngOnDestroy();\r\n          this.Form1.controls.ExpirationDateData.setValue('');\r\n          this.Form1.controls.EntityName.setValue('');\r\n          this.Form1.controls.TotalShares.setValue('');\r\n          this.filingModeCheck = data.payload.filingModeCheck;\r\n        }\r\n\r\n      });\r\n\r\n    }\r\n\r\n  }\r\n  getBulkPricing(selectedKitType) {\r\n    this.bulkPricing = [];\r\n    this.productService.getBulkPricing(selectedKitType, this.categoryCode, 'CT', 'NA').subscribe((response) => {\r\n      this.bulkPricing = response;\r\n    });\r\n  }\r\n\r\n  private GetLegends() {\r\n    this.kitsServiceApi\r\n      .GetLegends(this.selectedKitType, this.categoryCode)\r\n      .subscribe((data) => {\r\n        this.allLegendData = data;\r\n        this.mapLegends(data)\r\n\r\n        if (this.onLegendLoadedResolver) {\r\n          this.onLegendLoadedResolver();\r\n        }\r\n      });\r\n  }\r\n\r\n  onCorporateSubTypeChange(value) {\r\n\r\n    var data = this.allLegendData;\r\n    this.mapLegends(data, value)\r\n    if (this.CertControlsComponent) {\r\n      this.CertControlsComponent.mapLegendsOnCorpSubTypeChange(value);\r\n    }\r\n\r\n    if (this.onLegendLoadedResolver) {\r\n      this.onLegendLoadedResolver();\r\n    }\r\n  }\r\n\r\n  mapLegends(data: any[], corpSubType?) {\r\n    var formationType;\r\n    if (this.masterData.corporationTypes) {\r\n      formationType = this.masterData.corporationTypes.find(x => x.value == this.Form1.controls.FormationType.value);\r\n    }\r\n    this.AdditionalLegendsFormArray.clear();\r\n    this.CustomizeLegendsFormArray.clear();\r\n    this.CloseCorpLegendsFormArray.clear();\r\n    this.OtherLegendsFormArray.clear();\r\n    this.masterData.closeCorporationTypes = [];\r\n\r\n    if (data.length > 0 && data.find((x) => x.legendType === \"A\")) {\r\n\r\n      var filteredLegends = this.getFilteredLegends(data, 'A', formationType, corpSubType);\r\n\r\n      filteredLegends.forEach((legend) => {\r\n        let formGroup = new FormGroup({\r\n          id: new FormControl<any | null>(legend.productionNumber),\r\n          value1: new FormControl<any | null>(legend.webDescriptionline),\r\n          value2: new FormControl<any | null>(legend.webDescriptionline2),\r\n          price: new FormControl<any | null>(legend.retailPrice),\r\n          isChecked: new FormControl<any | null>(false),\r\n        });\r\n\r\n        this.AdditionalLegendPrice = legend.retailPrice;\r\n        this.AdditionalLegendsFormArray.push(formGroup);\r\n      });\r\n\r\n\r\n      if (data.length > 0 && data.find((x) => x.legendType === \"C\"))\r\n\r\n        var filteredLegends = this.getFilteredLegends(data, 'C', formationType, corpSubType);\r\n\r\n      filteredLegends.forEach((legend) => {\r\n        let formGroup = new FormGroup({\r\n          id: new FormControl<any | null>(legend.productionNumber),\r\n          value1: new FormControl<any | null>(legend.webDescriptionline),\r\n          value2: new FormControl<any | null>(legend.webDescriptionline2),\r\n          price: new FormControl<any | null>(legend.retailPrice),\r\n          isChecked: new FormControl<any | null>(false),\r\n          legendValue: new FormControl<any | null>(\r\n            null,\r\n            legend.productionNumber == 71235\r\n              ? CustomSharedValidations.maxValue(35)\r\n              : null\r\n          ),\r\n        });\r\n\r\n        this.CustomizedLegendPrice = legend.retailPrice;\r\n        this.CustomizeLegendsFormArray.push(formGroup);\r\n      });\r\n\r\n      if (data.length > 0 && data.find((x) => x.legendType === \"B\"))\r\n        data.find((x) => x.legendType === \"B\")\r\n          .legends.forEach((legend) => {\r\n            if (legend.subCategoryCode === \"CLS\") {\r\n              let closeCorpTypes = {\r\n                id: legend.productionNumber,\r\n                value1: legend.webDescriptionline,\r\n                value2: legend.webDescriptionline2,\r\n                price: legend.retailPrice,\r\n              };\r\n\r\n              this.masterData.closeCorporationTypes.push(closeCorpTypes);\r\n            } else if (legend.subCategoryCode === \"MLT\") {\r\n              let formGroup = new FormGroup({\r\n                id: new FormControl<any | null>(legend.productionNumber),\r\n                value1: new FormControl<any | null>(legend.webDescriptionline),\r\n                value2: new FormControl<any | null>(legend.webDescriptionline2),\r\n                price: new FormControl<any | null>(legend.retailPrice),\r\n                isChecked: new FormControl<any | null>(false),\r\n                legendValue: new FormControl<any | null>(\r\n                  null,\r\n                  CustomSharedValidations.alphaNumeric\r\n                ),\r\n              });\r\n\r\n              this.CloseCorpLegendsFormArray.push(formGroup);\r\n            }\r\n          });\r\n\r\n      if (data.length > 0 && data.find((x) => x.legendType === \"O\")) {\r\n        var filteredLegends = this.getFilteredLegends(data, 'O', formationType, corpSubType);\r\n\r\n        filteredLegends.forEach((legend) => {\r\n          let formGroup = new FormGroup({\r\n            id: new FormControl<any | null>(legend.productionNumber),\r\n            value1: new FormControl<any | null>(legend.webDescriptionline),\r\n            value2: new FormControl<any | null>(legend.webDescriptionline2),\r\n            price: new FormControl<any | null>(legend.retailPrice),\r\n            isChecked: new FormControl<any | null>(false),\r\n          });\r\n\r\n          this.OtherLegendPrice = legend.retailPrice;\r\n          this.OtherLegendsFormArray.push(formGroup);\r\n        });\r\n      }\r\n\r\n    }\r\n  }\r\n\r\n\r\n  getFilteredLegends(data, legendType, formationType, corpSubType) {\r\n    if (data.length > 0) {\r\n      var filteredData = data.find((x) => x.legendType === legendType);\r\n\r\n      if (this.selectedKitType != 'CK') return filteredData.legends;\r\n\r\n      if ((formationType && formationType.code != '010') && corpSubType) {\r\n        return filteredData.legends.filter(x => x.subCategoryCode == formationType.code && x.optionCode == corpSubType);\r\n      }\r\n      else if (formationType && formationType.code == '010') {\r\n        return filteredData.legends.filter(x => x.subCategoryCode == '' && x.optionCode == '');\r\n      }\r\n      else {\r\n        return [];\r\n      }\r\n    }\r\n  }\r\n\r\n  setParValue(data) {\r\n    if (data.parValueLeaveBlank === \"Y\") return \"Leave Blank\";\r\n    else if (data.noParValue === \"Y\") return \"No Par\";\r\n    else if (data.withoutParValue === \"W\") return \"Without Par\";\r\n    else if (data.parValue != \"N\") return \"Par Value Each\";\r\n  }\r\n\r\n  onCorporationTypeChange() {\r\n    if (this.Form1.controls.FormationType.value) {\r\n      this.masterData.typeOfCorporations =\r\n        this.masterData.allTypeOfCorporations.filter(\r\n          (x) => x.optionCode == this.Form1.controls.FormationType.value\r\n        ) || [];\r\n\r\n      if (this.allLegendData) {\r\n        this.mapLegends(this.allLegendData);\r\n      }\r\n\r\n      this.setCorporationTypeCodeValue();\r\n      if (this.corporationTypeCode && this.corporationTypeCode.length > 0 && this.CertControlsComponent) {\r\n        this.CertControlsComponent.getCertTypes(this.corporationTypeCode);\r\n      }\r\n\r\n    }\r\n    //if(this.selectedKitType != KitCodes.PS){\r\n    this.GetColors(true, this.selectedKitType, this.savedColor);\r\n    //}\r\n  }\r\n\r\n\r\n  private getCorporateSubTypes(selectedKitType, categoryCode, subCategoryCode) {\r\n    this.kitsServiceApi\r\n      .GetSubCorporateType(selectedKitType, categoryCode, subCategoryCode)\r\n      .subscribe((data) => {\r\n        this.masterData.allTypeOfCorporations = data.map((item) => {\r\n          var data = {\r\n            id: item.corporationSubTypeCode,\r\n            value: item.corporationSubTypeDescription,\r\n            optionCode: item.corporationTypeCode,\r\n          };\r\n          return data;\r\n        });\r\n        if (this.onTypesOfCorpLoadedResolver) {\r\n          this.onTypesOfCorpLoadedResolver();\r\n        }\r\n      });\r\n  }\r\n\r\n  LoadData(data) {\r\n    let onLegendLoaded = new Promise<any>(\r\n      (resolve, reject) => (this.onLegendLoadedResolver = resolve)\r\n    );\r\n    let onTypesOfCorpLoaded = new Promise<any>(\r\n      (resolve, reject) => (this.onTypesOfCorpLoadedResolver = resolve)\r\n    );\r\n\r\n    this.isAdd = false;\r\n\r\n    this.buttonText = StringValue.updateOrder;\r\n    onTypesOfCorpLoaded.then(() => {\r\n      this.masterData.typeOfCorporations =\r\n        this.masterData.allTypeOfCorporations.filter(\r\n          (x) => x.optionCode == data.entityType\r\n        );\r\n    });\r\n    // Form 1\r\n    this.selectedKitType = data.productCode;\r\n    this.Form1.controls.FormationType.setValue(data.entityType);\r\n    this.Form1.controls.KitType.setValue(data.productCode);\r\n    this.Form1.controls.FilingState.setValue(data.filingState);\r\n    this.Form1.controls.EntityName.setValue(data.entityName);\r\n    this.Form1.controls.BylawsAndMinutes.setValue(data.bylawsAndMinutes);\r\n    this.Form1.controls.OperatingAgreements.setValue(data.operatingAgreements);\r\n    this.Form1.controls.GoldStamping.setValue(\r\n      Number(data.goldStamping).toString()\r\n    );\r\n    this.Form1.controls.ProfessionalCorporation.setValue(\r\n      (data.professionalCorporation || \"\").trim()\r\n    );\r\n\r\n    this.Form1.controls.OtherProfessionalCorpType.setValue(\r\n      data.otherProfessionalCorpType\r\n    );\r\n\r\n\r\n    this.GetColors(data.productCode === \"CK\", data.productCode, data.color);\r\n    // Form 2\r\n    if (data.formationDate) {\r\n      var formationDateSplit = data.formationDate.split(\"/\");\r\n      this.Form2.controls.FormationDate.setValue(formationDateSplit[1]);\r\n      this.Form2.controls.FormationMonth.setValue(formationDateSplit[0]);\r\n      this.Form2.controls.FormationYear.setValue(formationDateSplit[2]);\r\n    }\r\n    this.Form2.controls.FilingStatus.setValue(data.filingStatus);\r\n\r\n\r\n\r\n    this.Form2.controls.TotalAuthorizedShares.setValue(\r\n      data.totalAuthorizedShares\r\n    );\r\n\r\n    this.Form2.controls.SpecialInstructions.setValue(data.specialInstructions);\r\n\r\n    this.Form2.controls.IsCloseCorporation.setValue(data.isCloseCorporation);\r\n    this.Form2.controls.CloseCorpType.setValue(data.closeCorpType);\r\n    // this.Form2.controls.ProfessionalCorporation.setValue(\r\n    //   (data.professionalCorporation || \"\").trim()\r\n    // );\r\n\r\n    let addLegendsValue =\r\n      (\r\n        data.additionalLegends ||\r\n        data.customizedLegends ||\r\n        data.otherLegends ||\r\n        []\r\n      ).length > 0\r\n        ? \"YES\"\r\n        : \"NO\";\r\n    this.Form2.controls.AddLegends.setValue(addLegendsValue);\r\n    this.savedColor = data.color;\r\n\r\n    onLegendLoaded.then(() => {\r\n      this.mapLegends(this.allLegendData, data.professionalCorporation);\r\n\r\n      // this.setSelectedLegends(data.additionalLegends);\r\n      // this.setCustomizeLegends(data.customizedLegends, \"CustomizedLegends\");\r\n      this.setCustomizeLegends(\r\n        data.closeCorporationLegends,\r\n        \"CloseCorporationLegends\"\r\n      );\r\n      // this.setOtherLegends(data.otherLegends);\r\n\r\n      if (data.closeCorpType) {\r\n        this.Form2.controls.IsCloseCorporation.setValue(true);\r\n      }\r\n    });\r\n    this.savedOrderCopy.certificates.length > 1 || this.savedOrderCopy.certificates[0].reflective != '' ? this.Form2.controls.MultiCerts.setValue(true) : null\r\n    // this.savedOrderCopy.reflective ? this.Form2.controls.ReflectiveCerts.setValue(this.savedOrderCopy.reflective) : this.Form2.controls.ReflectiveCerts.setValue(null)\r\n\r\n\r\n    this.getProductMultiClass(this.selectedKitType, this.categoryCode, this.subCategoryCode);\r\n  }\r\n\r\n  clearMultiCertificates() {\r\n    const valueToKeep = this.CertControlsComponent.Certificates.at(0);\r\n    this.CertControlsComponent.Certificates.clear();\r\n    this.CertControlsComponent.Certificates.push(valueToKeep);\r\n  }\r\n\r\n  private getProductMultiClass(value, categoryCode, subCategoryCode) {\r\n    this.kitsServiceApi.GetProductMultiClass(value, categoryCode, subCategoryCode).subscribe(response => {\r\n      this.multiClassResult = response;\r\n    });\r\n  }\r\n\r\n  OnKitTypeSelection(value) {\r\n    this.isLoading.show();\r\n    if (this.CertControlsComponent) {\r\n      if (!this.isAdd) {\r\n        this.CertControlsComponent.savedOrderCopy = this.savedOrderCopy;\r\n        this.firstKitType = this.kitType;\r\n        let onLegendLoaded = new Promise<any>(\r\n          (resolve, reject) => (this.CertControlsComponent.onLegendLoadedResolver = resolve)\r\n        );\r\n        onLegendLoaded.then(() => {\r\n          this.CertControlsComponent.loadCertificates();\r\n        });\r\n      }\r\n      this.isLoading.hide();\r\n    }\r\n    if (value && this.selectedKitType != value) {\r\n      this.getBulkPricing(value);\r\n    }\r\n    if ((value && this.selectKitFlag == 0) || (this.selectedKitType != value)) {\r\n      this.getProductMultiClass(value, this.categoryCode, this.subCategoryCode);\r\n      this.GetCorporationTypes(value, this.subCategoryCode, this.categoryCode);\r\n      this.getCorporateSubTypes(value, this.categoryCode, this.subCategoryCode);\r\n      this.GetLegends();\r\n      this.selectKitFlag++;\r\n    } else {\r\n      return;\r\n    }\r\n\r\n    if (this.selectedKitType !== value && !this.IsUpSelling) {\r\n      this.Form1.reset();\r\n      this.Form2.reset();\r\n      if (!this.isAdd && value === this.savedOrderCopy.productCode) {\r\n        this.LoadData(this.savedOrderCopy);\r\n      }\r\n      if (this.ColorComponent) {\r\n        this.ColorComponent.IsVisible = false;\r\n      }\r\n\r\n    }\r\n\r\n\r\n    this.selectedKitType = value;\r\n\r\n    if (value == KitCodes.PS || value == KitCodes.LK) {\r\n      // if(!this.isAdd)\r\n      // {\r\n\r\n      // }      \r\n      //this.GetColors(false, value);\r\n    }\r\n\r\n    this.product = this.kitsByCategoryCode.find(\r\n      (obj) =>\r\n        obj.productCode.includes(value) &&\r\n        obj.subCategoryCode.includes(this.subCategoryCode)\r\n    );\r\n    this.productImageUrl = this.product.productImage;\r\n    this.price = this.product.price;\r\n\r\n  }\r\n\r\n  Form1 = new FormGroup({\r\n    selectedKit: new FormControl<any | null>(null),\r\n    KitType: new FormControl<any | null>(null),\r\n    FilingState: new FormControl<any | null>(null),\r\n\r\n    EntityName: new FormControl<any | null>(null),\r\n    TotalShares: new FormControl<any | null>({ value: null, disabled: true }),\r\n    ExpirationDateData: new FormControl<any | null>({ value: null, disabled: true }),\r\n    filingStatusData: new FormControl<any | null>({ value: true, disabled: true }),\r\n\r\n    FormationType: new FormControl<any | null>(null),\r\n    Color: new FormControl<any | null>(null),\r\n    BylawsAndMinutes: new FormControl<any | null>(null),\r\n    OperatingAgreements: new FormControl<any | null>(null),\r\n    GoldStamping: new FormControl<any | null>(null),\r\n\r\n    ProfessionalCorporation: new FormControl<any | null>(null),\r\n    OtherProfessionalCorpType: new FormControl<any | null>(null)\r\n  });\r\n  Form2 = new FormGroup({\r\n    FormationDate: new FormControl<any | null>(\r\n      null,\r\n      CustomSharedValidations.validateFormationDate(\r\n        \"FormationDate\",\r\n        \"FormationMonth\",\r\n        \"FormationYear\"\r\n      )\r\n    ),\r\n    FormationMonth: new FormControl<any | null>(\r\n      null,\r\n      CustomSharedValidations.validateFormationDate(\r\n        \"FormationDate\",\r\n        \"FormationMonth\",\r\n        \"FormationYear\"\r\n      )\r\n    ),\r\n    FormationYear: new FormControl<any | null>(null,\r\n      CustomSharedValidations.validateFormationDate(\r\n        \"FormationDate\",\r\n        \"FormationMonth\",\r\n        \"FormationYear\"\r\n      ),\r\n    ),\r\n    FilingStatus: new FormControl<any | null>(null),\r\n    TotalAuthorizedShares: new FormControl<any | null>(null),\r\n    MultiCerts: new FormControl<any | null>(null),\r\n    DesignationCK: new FormControl<any | null>(null),\r\n    // ReflectiveCerts: new FormControl(null),\r\n\r\n    AddLegends: new FormControl<any | null>(null, [Validators.required]),\r\n    //NOTE: These FormArray names are used in the HTML template, to be passed on 'AddLegendsInFormArray' method... change there also if renamed here\r\n    AdditionalLegends: this.AdditionalLegendsFormArray,\r\n    CustomizedLegends: this.CustomizeLegendsFormArray,\r\n    //--\r\n    OtherLegends: this.OtherLegendsFormArray,\r\n    LegendValues: new FormArray([]),\r\n    CloseCorporationLegends: this.CloseCorpLegendsFormArray,\r\n    //--\r\n    SpecialInstructions: new FormControl<any | null>(null),\r\n    Captcha: new FormControl<any | null>(null),\r\n    IsCloseCorporation: new FormControl<any | null>(null),\r\n    CloseCorpType: new FormControl<any | null>(0),\r\n    //ProfessionalCorporation: new FormControl(null),\r\n  });\r\n\r\n  setSelectedLegends(data: any[]) {\r\n    this.Form2.get(\"AdditionalLegends\")[\"controls\"].forEach((formgrp) => {\r\n      if (data && data.includes(formgrp.controls.id.value.toString())) {\r\n        formgrp.controls.isChecked.setValue(true);\r\n      }\r\n    });\r\n  }\r\n\r\n  setCustomizeLegends(data: any[], controlName: string) {\r\n    if (data)\r\n      this.Form2.get(controlName)[\"controls\"].forEach((formgrp) => {\r\n        let val = data.filter(\r\n          (x) => x.productNumber == formgrp.controls.id.value.toString()\r\n        );\r\n        if (val.length) {\r\n          formgrp.controls.isChecked.setValue(true);\r\n          if (formgrp.controls.hasOwnProperty(\"legendValue\")) {\r\n            formgrp.controls.legendValue.setValue(val[0].legendValue);\r\n          }\r\n        }\r\n      });\r\n  }\r\n\r\n  setOtherLegends(data: any[]) {\r\n    this.Form2.get(\"OtherLegends\")[\"controls\"].forEach((formgrp) => {\r\n      if (data && data.includes(formgrp.controls.id.value.toString())) {\r\n        formgrp.controls.isChecked.setValue(true);\r\n      }\r\n    });\r\n  }\r\n\r\n  GetKitData() {\r\n    this.isLoading.show();\r\n    this.kitsServiceApi\r\n      .getkitsListing()\r\n      .subscribe((data: KitsListingModel[]) => {\r\n        this.isLoading.hide();\r\n        // Get Individual Kits by CategoryCode\r\n        let kitData = data.find((obj) =>\r\n          obj.categoryCode.includes(this.categoryCode)\r\n        );\r\n\r\n        if (!kitData) {\r\n          return;\r\n        }\r\n        this.kitsByCategoryCode = kitData.individualKits;\r\n\r\n        // Get available Product Codes from the Available Individual Kits\r\n        let kitCodes = this.kitsByCategoryCode.map(function (obj) {\r\n          return obj.productCode;\r\n        });\r\n        kitCodes = kitCodes.filter((n, i) => kitCodes.indexOf(n) === i);\r\n        this.availableKitTypes = this.AllKitTypes.filter((obj) =>\r\n          kitCodes.includes(obj.value)\r\n        );\r\n\r\n        // Need to check: individual kit of which Product code to be passed\r\n        const filteredKitCode = this.kitType ? this.kitType : kitCodes[0];\r\n        this.product = this.kitsByCategoryCode.find(\r\n          (obj) =>\r\n            obj.productCode.includes(filteredKitCode) &&\r\n            obj.subCategoryCode.includes(this.subCategoryCode)\r\n        );\r\n        this.productImageUrl = this.selectedKitType\r\n          ? this.product.productImage\r\n          : this.product.image;\r\n        this.price = this.product.price;\r\n        if (this.kitType) {\r\n          //this.availableKitTypes.length > 0th\r\n          this.Form1.controls.KitType.setValue(this.kitType);\r\n          this.selectedKitType = this.kitType;\r\n          this.OnKitTypeSelection(filteredKitCode);\r\n        }\r\n\r\n        if (this.IsUpSelling) {\r\n          // this.setDefaultValues(true);\r\n        }\r\n      });\r\n  }\r\n\r\n  GetCorporationTypes(productCode, categoryCode, subCategoryCode) {\r\n    this.kitsServiceApi\r\n      .GetCorporationTypes(productCode, categoryCode, subCategoryCode)\r\n      .subscribe((data) => {\r\n        this.masterData.corporationTypes = data.map((item) => {\r\n          let icon = \"\";\r\n          switch (item.corpTypeCodeString) {\r\n            case \"FP\": {\r\n              icon = \"icon-profits\";\r\n              break;\r\n            }\r\n            case \"NP\": {\r\n              icon = \"icon-non-profit\";\r\n              break;\r\n            }\r\n            case \"PC\": {\r\n              icon = \"icon-professional-corporation\";\r\n              break;\r\n            }\r\n          }\r\n          var data = {\r\n            value: item.corporationTypeCode,\r\n            name: item.corporationTypeDescription,\r\n            icon: icon,\r\n            code: item.corporationTypeCode,\r\n            corporationCode: item.corporationCode,\r\n            corpCode: item.corpCode,\r\n            corpTypeCode: item.corpTypeCode,\r\n          };\r\n          return data;\r\n        });\r\n\r\n        if (!this.isAdd) {\r\n          this.setCorporationTypeCodeValue();\r\n          //setting designationData for certificate\r\n          if (this.corporationTypeCode && this.corporationTypeCode.length > 0 && this.CertControlsComponent) {\r\n            this.CertControlsComponent.getCertTypes(this.corporationTypeCode);\r\n          }\r\n        }\r\n\r\n        if (this.CorpTypeComponent) {\r\n          this.CorpTypeComponent.IsVisible = true;\r\n          this.CorpTypeComponent.CorporationTypes =\r\n            this.masterData.corporationTypes;\r\n        }\r\n\r\n      });\r\n  }\r\n\r\n  //Setting value for CorporationCode and CorporationTypeCode to get bifurcated Designation values\r\n  setCorporationTypeCodeValue() {\r\n    if (this.Form1.controls.FormationType.value) {\r\n      if (this.masterData.corporationTypes) {\r\n        this.corporationTypeCode = this.masterData.corporationTypes\r\n          .filter(data => {\r\n            return data.code.match(this.Form1.controls.FormationType.value) && (data.code.length == this.Form1.controls.FormationType.value.length)\r\n          })\r\n          .map((obj: any) => { return obj.corpTypeCode });\r\n      }\r\n    } else {\r\n      this.corporationTypeCode = null;\r\n    }\r\n  }\r\n\r\n\r\n  setCertificateSignatureValues() {\r\n    // binding certificate signatures dropdown values\r\n    if (this.selectedKitType == 'LK') {\r\n      // this.CertControlsComponent.certificateSignature1Data =\r\n      //   this.masterData.certificateSignatures1LK;\r\n      // this.CertControlsComponent.certificateSignature2Data =\r\n      //   this.masterData.certificateSignatures2LK;\r\n      this.CertControlsComponent.certificateSignatureData =\r\n        this.masterData.certificateSignatures2LK;\r\n    }\r\n    else if (this.selectedKitType == 'CK') {\r\n      // this.CertControlsComponent.certificateSignature1Data =\r\n      //   this.masterData.certificateSignatures1CK;\r\n      // this.CertControlsComponent.certificateSignature2Data =\r\n      //   this.masterData.certificateSignatures2CK;\r\n      this.CertControlsComponent.certificateSignatureData =\r\n        this.masterData.certificateSignatures2CK;\r\n    }\r\n    if (!this.isAdd && this.selectedKitType != 'PS') {\r\n      this.CertControlsComponent.setOtherSignatureTitleDefaultValue();\r\n    }\r\n  }\r\n\r\n  ShowCertsAndByLaws(value: boolean) {\r\n    try {\r\n      // Validate Page 1 validations\r\n\r\n      if (!this.Form1.valid) {\r\n        return;\r\n      }\r\n\r\n      var mutliCert = this.multiClassResult.find(x => x.entityCode === this.Form1.controls.FormationType.value);\r\n      this.showMultiCerts = mutliCert ? mutliCert.masterClassCode == 'M' : false;\r\n\r\n      if (this.Form1.controls.FormationType.value != \"FP\") {\r\n        this.clearMultiCertificates()\r\n        this.Form2.controls.MultiCerts.setValue(null)\r\n      }\r\n      if (this.CertControlsComponent) {\r\n        this.setCertificateSignatureValues()\r\n        // this.CertControlsComponent.GetLegends()\r\n      }\r\n      // set flag to show next page\r\n      this.showCertsAndBylaws = value;\r\n      //add dynamic validations on the Page 2 fields based on conditions\r\n      if (value) {\r\n        this.selectedKitName = this.AllKitTypes.find(\r\n          (x) => x.value === this.selectedKitType\r\n        ).name;\r\n        this.selectedStateName = this.masterData.states.find(\r\n          (x) => x.stateCode === this.Form1.controls.FilingState.value\r\n        ).stateName;\r\n\r\n        this.selectedGoldStampingOption = this.Form1.controls.GoldStamping.value\r\n          ? this.masterData.goldStamping.find(\r\n            (x) => String(x.id) == this.Form1.controls.GoldStamping.value\r\n          ).value\r\n          : \"No\";\r\n\r\n        if (\r\n\r\n          this.Form1.controls.FormationType.value\r\n        )// this.selectedKitType != \"LK\" &&\r\n          this.selectedEntityTypeName = this.masterData.corporationTypes.find(\r\n            (x) => x.value === this.Form1.controls.FormationType.value\r\n          ).name;\r\n\r\n        if (this.Form1.controls.ProfessionalCorporation.value) {\r\n          let somevalue = this.masterData.typeOfCorporations.find(\r\n            (x) => x.id == this.Form1.controls.ProfessionalCorporation.value\r\n          );\r\n          this.selectedCorporationType = somevalue.value;\r\n        }\r\n\r\n        if (this.Form1.controls.BylawsAndMinutes.value)\r\n          this.selectedBylawsOption = this.masterData.bylawsAndMinutesData.find(\r\n            (x) => String(x.id) === this.Form1.controls.BylawsAndMinutes.value\r\n          ).value;\r\n\r\n        if (this.Form1.controls.OperatingAgreements.value)\r\n          this.selectedOperatingAgreementOption =\r\n            this.masterData.operatingAgreementData.find(\r\n              (x) =>\r\n                String(x.id) === this.Form1.controls.OperatingAgreements.value\r\n            ).value;\r\n\r\n        if (!this.isAdd) {\r\n          let additionalValues = (\r\n            this.Form2.get(\"AdditionalLegends\") as FormArray\r\n          ).controls.map((x) => x.value);\r\n          let customValues = (\r\n            this.Form2.get(\"CustomizedLegends\") as FormArray\r\n          ).controls.map((x) => x.value);\r\n          let controlValues = additionalValues.concat(customValues);\r\n          controlValues.forEach((value) => {\r\n            let html = document.getElementById(value) as HTMLInputElement;\r\n            if (html) html.checked = true;\r\n            let textboxValue = `customizedLegend-${value}`;\r\n            let textboxHtml = document.getElementById(\r\n              textboxValue\r\n            ) as HTMLInputElement;\r\n            if (textboxHtml) {\r\n              let legendValue = this.savedCustomLegends.find(\r\n                (x) => x.productNumber === value\r\n              ).legendValue;\r\n              textboxHtml.value = legendValue;\r\n            }\r\n          });\r\n          if (this.selectedKitType !== this.savedOrderCopy.productCode) {\r\n            // this.setDefaultValues();\r\n          }\r\n          if (\r\n            this.Form1.controls.FormationType.value !== \"NP\" &&\r\n            this.Form1.controls.FormationType.value !==\r\n            this.savedOrderCopy.entityType\r\n          ) {\r\n            this.Form2.controls.DesignationCK.setValue(\"COM\");\r\n          }\r\n        } else {\r\n          // this.setDefaultValues();\r\n        }\r\n\r\n        if (!this.IsUpSelling) {\r\n          window.scrollTo(0, 0);\r\n        }\r\n      }\r\n    } catch (ex) { }\r\n  }\r\n\r\n  setSelectedColor() {\r\n    return { \"background-color\": this.Form1.controls.Color.value };\r\n  }\r\n  ToggleMulticerts(checked) {\r\n    this.CertControlsComponent.showMulticerts = checked\r\n  }\r\n\r\n\r\n\r\n  async OnSave() {\r\n\r\n\r\n    try {\r\n      this.CertControlsComponent.removeValidationFromDesignation();\r\n      this.CertControlsComponent.getAuthorizedSharesCount();\r\n      this.validateAuthorisedShares();\r\n\r\n      this.multiCertificates =\r\n        await this.CertControlsComponent.getCertificateData();\r\n      if (\r\n        this.Form2.valid &&\r\n        this.Form1.valid &&\r\n        this.CertControlsComponent.Form.controls.CertificatesForm.valid\r\n      ) {\r\n\r\n        if (\r\n          !this.isAdd &&\r\n          this.selectedKitType !== this.savedOrderCopy.productCode\r\n        ) {\r\n          this.kitsServiceApi\r\n            .DeleteItem(\r\n              this.orderNumber,\r\n              this.kitLineNumber,\r\n              this.productNumber\r\n            )\r\n            .subscribe((response) => {\r\n              this.isAdd = true;\r\n              this.Save();\r\n            });\r\n        } else {\r\n          this.Save();\r\n        }\r\n      }\r\n    } catch (ex) {\r\n      console.error(ex);\r\n    }\r\n  }\r\n\r\n  Save() {\r\n\r\n    var kitOrderVM = new ProductDetail();\r\n\r\n    //Form1\r\n\r\n    kitOrderVM.ProductCode = this.Form1.controls.KitType.value;\r\n    kitOrderVM.CategoryCode = this.categoryCode;\r\n    kitOrderVM.SubCategoryCode = this.subCategoryCode;\r\n    kitOrderVM.FilingState = this.Form1.controls.FilingState.value;\r\n    kitOrderVM.EntityName = this.Form1.controls.EntityName.value;\r\n    kitOrderVM.GoldStamping = this.Form1.controls.GoldStamping.value || 0;\r\n    kitOrderVM.EntityType = this.Form1.controls.FormationType.value;\r\n    kitOrderVM.Color = this.Form1.controls.Color.value;\r\n    kitOrderVM.BylawsAndMinutes = this.Form1.controls.BylawsAndMinutes.value;\r\n    kitOrderVM.OperatingAgreements =\r\n      this.Form1.controls.OperatingAgreements.value;\r\n\r\n    kitOrderVM.ProfessionalCorporation =\r\n      this.Form1.controls.ProfessionalCorporation.value;\r\n    kitOrderVM.OtherProfessionalCorpType =\r\n      this.Form1.controls.OtherProfessionalCorpType.value;\r\n\r\n    //Form2\r\n    let validFullDate =\r\n      this.Form2.controls.FormationMonth.value &&\r\n      this.Form2.controls.FormationDate.value &&\r\n      this.Form2.controls.FormationYear.value;\r\n    const formationYear = this.Form2.controls.FormationYear.value;\r\n    kitOrderVM.FormationDate = validFullDate\r\n      ? `${this.Form2.controls.FormationMonth.value}/${this.Form2.controls.FormationDate.value}/${this.Form2.controls.FormationYear.value}`\r\n      : formationYear\r\n        ? `//${formationYear}`\r\n        : \"\";\r\n    kitOrderVM.FilingStatus = this.Form2.controls.FilingStatus.value\r\n      ? this.Form2.controls.FilingStatus.value\r\n      : false;\r\n\r\n    kitOrderVM.TotalAuthorizedShares =\r\n      this.Form2.controls.TotalAuthorizedShares.value || 0;\r\n\r\n    kitOrderVM.AddLegends = this.Form2.controls.AddLegends.value;\r\n\r\n    kitOrderVM.OtherLegends = this.showLegend ? this.Form2.controls.OtherLegends.value\r\n      .filter((x) => x.isChecked).map((x) => x.id.toString()) : [];\r\n\r\n    kitOrderVM.AdditionalLegends = this.showLegend ? this.Form2.controls.AdditionalLegends.value\r\n      .filter((x) => x.isChecked).map((x) => x.id.toString()) : [];\r\n    kitOrderVM.CustomizedLegends = this.showLegend ? this.Form2.controls.CustomizedLegends.value\r\n      .filter((x) => x.isChecked).map((x) => this.CustomLegendMapping(x)) : [];\r\n    kitOrderVM.CloseCorporationLegends = this.Form2.controls.CloseCorporationLegends.value\r\n      .filter((x) => x.isChecked).map((x) => this.CustomLegendMapping(x));\r\n    kitOrderVM.SpecialInstructions =\r\n      this.Form2.controls.SpecialInstructions.value;\r\n    kitOrderVM.IsCloseCorporation = this.Form2.controls.IsCloseCorporation.value\r\n      ? \"YES\"\r\n      : null;\r\n    kitOrderVM.CloseCorpType = this.Form2.controls.CloseCorpType.value || 0;\r\n    // kitOrderVM.ProfessionalCorporation =\r\n    //   this.Form2.controls.ProfessionalCorporation.value;\r\n    if (this.Form2.controls.MultiCerts.value) {\r\n      kitOrderVM.certificates = this.multiCertificates\r\n    }\r\n    else {\r\n      this.multiCertificates.length = 1\r\n      kitOrderVM.certificates = this.multiCertificates\r\n    }\r\n\r\n\r\n    // kitOrderVM.Reflective = this.Form2.controls.ReflectiveCerts.value\r\n    kitOrderVM.orderNumber = this.orderNumber;\r\n    kitOrderVM.kitLineNumber = this.kitLineNumber;\r\n\r\n    //#endregion Request Mapping\r\n    if (this.IsUpSelling) {\r\n      this.UpSellingResolver(Object.assign({}, kitOrderVM));\r\n      return;\r\n    }\r\n\r\n    this.kitsServiceApi\r\n      .saveKits(kitOrderVM, this.isAdd)\r\n      .subscribe((response) => {\r\n        this.Form1.reset();\r\n        this.Form2.reset();\r\n      });\r\n  }\r\n\r\n  CustomLegendMapping(x) {\r\n    let obj: any = {};\r\n    obj.ProductNumber = x.id.toString();\r\n    obj.LegendValue = x.legendValue.toString();\r\n\r\n    return obj;\r\n  }\r\n\r\n  GetColors(forCorporateKit, productCode: string, selectedColor?: string) {\r\n    if (\r\n      this.Form1.controls.BylawsAndMinutes.value ||\r\n      this.Form1.controls.OperatingAgreements.value ||\r\n      productCode === KitCodes.PS\r\n    ) {\r\n      if (forCorporateKit && !this.Form1.controls.FormationType.value) return;\r\n\r\n      let optionCode = \"NA\";\r\n      let entityCode = \"NA\";\r\n      if (forCorporateKit && (productCode == KitCodes.CK || productCode == KitCodes.SC)) {\r\n        optionCode = this.Form1.controls.BylawsAndMinutes.value;\r\n        entityCode = this.Form1.controls.FormationType.value;\r\n      } else if (productCode == KitCodes.PS) {\r\n        entityCode = this.Form1.controls.FormationType.value; // productCode;\r\n        optionCode = \"COL\";\r\n      } else if (productCode == KitCodes.LK) {\r\n        entityCode = this.Form1.controls.FormationType.value; //\"LC\";\r\n        optionCode = this.Form1.controls.OperatingAgreements.value;\r\n      }\r\n\r\n      const colorsRequest: ColorsRequestModel = {\r\n        productCode: this.selectedKitType,\r\n        categoryCode: this.categoryCode,\r\n\r\n        subCategoryCode: this.subCategoryCode,\r\n        optionCode: optionCode,\r\n        entityCode: entityCode,\r\n      };\r\n      this.kitsServiceApi.GetColors(colorsRequest).subscribe((data) => {\r\n\r\n        this.ColorComponent.IsVisible = true;\r\n        if (this.isAdd || !this.isAdd && this.selectedKitType != this.savedOrderCopy.productCode) {\r\n          this.ColorComponent.SelectedColor = null;\r\n        }\r\n\r\n        if (data.length > 0) {\r\n          //setting product price\r\n          this.price = data[0].price;\r\n\r\n          // If only one color present, then setting it to form control and not showing color panel\r\n          if (data.length === 1) {\r\n            this.ColorComponent.IsVisible = false;\r\n\r\n            this.Form1.controls.Color.setValue(data[0].code);\r\n          } else {\r\n            let colors = data.map(function (obj) {\r\n              return obj.code;\r\n            });\r\n            let colorName = data.map(function (obj) {\r\n              return obj.description;\r\n            });\r\n            colors = colors.filter((n, i) => colors.indexOf(n) === i);\r\n            this.colors = data.map((item) => {\r\n              var data = {\r\n                id: item.code,\r\n                value: item.description,\r\n                price: item.price,\r\n              };\r\n              return data;\r\n            });\r\n            this.ColorComponent.Colors = colors;\r\n            this.ColorComponent.colorNames = colorName;\r\n            if (\r\n              !this.isAdd &&\r\n              selectedColor &&\r\n              this.selectedKitType === this.savedOrderCopy.productCode\r\n            ) {\r\n              this.Form1.controls.Color.setValue(selectedColor);\r\n              this.ColorComponent.SelectedColor = selectedColor;\r\n            }\r\n          }\r\n        } else {\r\n          this.ColorComponent.Colors = [];\r\n          this.Form1.controls.Color.setValue(null);\r\n        }\r\n      });\r\n    }\r\n    if (this.CertControlsComponent) {\r\n      this.setCertificateSignatureValues()\r\n    }\r\n  }\r\n\r\n  ColorCondition() {\r\n    return (\r\n      (this.selectedKitType === \"CK\" &&\r\n        this.Form1.controls.FormationType.value &&\r\n        this.Form1.controls.BylawsAndMinutes.value) ||\r\n      (this.selectedKitType === \"LK\" &&\r\n        this.Form1.controls.OperatingAgreements.value) ||\r\n      this.selectedKitType === \"PS\"\r\n    );\r\n  }\r\n\r\n  private MapMasterDataByControlId(data: any[], controlId: any) {\r\n    let mappedData = data\r\n      .filter((obj) => obj.controlID === controlId)\r\n      .map((item) => {\r\n        var data = { id: item.controlValue, value: item.controlText };\r\n        return data;\r\n      });\r\n    return mappedData || [];\r\n  }\r\n\r\n  private MapAllMasterData(data: any[]) {\r\n    this.masterData.designationCk = this.MapMasterDataByControlId(\r\n      data,\r\n      ProductDetailControls.DesignationForCK\r\n    );\r\n    this.masterData.designationLk = this.MapMasterDataByControlId(\r\n      data,\r\n      ProductDetailControls.DesignationForLK\r\n    );\r\n    this.masterData.parValue = this.MapMasterDataByControlId(\r\n      data,\r\n      ProductDetailControls.ParValue\r\n    );\r\n    this.masterData.certificateSignatures1CK = this.MapMasterDataByControlId(\r\n      data,\r\n      ProductDetailControls.CertificateSignatureForCK\r\n    );\r\n    this.masterData.certificateSignatures1LK = this.MapMasterDataByControlId(\r\n      data,\r\n      ProductDetailControls.CertificateSignatureForLK\r\n    );\r\n    this.masterData.certificateSignatures2CK = this.MapMasterDataByControlId(\r\n      data,\r\n      ProductDetailControls.CertificateSignatureForCK\r\n    );\r\n    this.masterData.certificateSignatures2LK = this.MapMasterDataByControlId(\r\n      data,\r\n      ProductDetailControls.CertificateSignatureForLK\r\n    );\r\n    this.masterData.bylawsAndMinutesData = this.MapMasterDataByControlId(\r\n      data,\r\n      ProductDetailControls.ByLawsAndMinutes\r\n    );\r\n    this.masterData.operatingAgreementData = this.MapMasterDataByControlId(\r\n      data,\r\n      ProductDetailControls.OperatingAgreements\r\n    );\r\n    this.masterData.goldStamping = this.MapMasterDataByControlId(\r\n      data,\r\n      ProductDetailControls.GoldStamping\r\n    );\r\n    this.masterData.legendOptionsData = this.MapMasterDataByControlId(\r\n      data,\r\n      ProductDetailControls.LegendOptions\r\n    );\r\n\r\n    if (this.IsDeluxeKit()) {\r\n      this.masterData.certificateColors = this.MapMasterDataByControlId(\r\n        data,\r\n        ProductDetailControls.DeluxeCertificateColors\r\n      );\r\n    } else {\r\n      this.masterData.certificateColors = this.MapMasterDataByControlId(\r\n        data,\r\n        ProductDetailControls.CertificateColors\r\n      );\r\n    }\r\n\r\n    this.certificateColors = this.masterData.certificateColors.map(\r\n      (obj) => obj.id\r\n    );\r\n  }\r\n\r\n  private IsDeluxeKit(): boolean {\r\n    if ((this.categoryCode == 'AI1' && this.subCategoryCode == 'DC'\r\n      || this.categoryCode == 'BXK' && this.subCategoryCode == 'DP'\r\n      || this.categoryCode == 'BXK' && this.subCategoryCode == 'DR'\r\n    )) { return true }\r\n  }\r\n\r\n  private RenderCertificateColors() {\r\n    this.CertificateColorComponent.IsVisible = true;\r\n    this.CertificateColorComponent.Label = \"Certificate Color\";\r\n    this.CertificateColorComponent.Colors = this.certificateColors;\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  getKitListData() {\r\n    this.isLoading.show();\r\n    this.kitsServiceApi.getkitsListing().subscribe((res) => {\r\n      this.isLoading.hide();\r\n      this.UpSellingKitsData = res.filter(\r\n        (obj) => obj.categoryCode == \"AI1\" || obj.categoryCode == \"BXK\"\r\n      );\r\n    });\r\n  }\r\n  upSellingKitSelection(kitDetails: KitsTypeModel) {\r\n    this.subCategoryCode = kitDetails.subCategoryCode;\r\n    this.categoryCode = kitDetails.categoryCode;\r\n    this.kitType = kitDetails.productCode;\r\n    this.GetKitData();\r\n  }\r\n\r\n\r\n  DisableCertColor() {\r\n    let category = \"BXK\";\r\n    let subCategories = [\"DR\", \"DP\"];\r\n\r\n    if (\r\n      subCategories.includes(this.subCategoryCode) &&\r\n      category == this.categoryCode\r\n    ) {\r\n      return true;\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  onLoad() {\r\n    this.imgZoom = document.getElementById(\"myimage\");\r\n    this.imgLens = document.getElementById(\"divLens\");\r\n    this.divZoom = document.getElementById(\"divZoomed\");\r\n\r\n    const dim = (this.divZoom as any).getBoundingClientRect();\r\n\r\n    this.imgClientRect = this.imgZoom.getBoundingClientRect();\r\n\r\n    var src = this.imgZoom.getAttribute(\"src\");\r\n\r\n    this.render.setStyle(\r\n      this.divZoom,\r\n      \"background-image\",\r\n      \"url('\" + src + \"')\"\r\n    );\r\n    this.render.setStyle(\r\n      this.divZoom,\r\n      \"background-size\",\r\n      this.imgZoom.width * 3 + \"px \" + this.imgZoom.height * 3 + \"px\"\r\n    );\r\n    this.render.setStyle(this.divZoom, \"background-repeat\", \"no-repeat\");\r\n    this.render.setStyle(this.divZoom, \"background-position\", \"center top\");\r\n    this.render.setStyle(\r\n      this.divZoom,\r\n      \"transition\",\r\n      \"background-position .2s ease-out\"\r\n    );\r\n\r\n    if (!this.cx && !this.cy) {\r\n      this.cx =\r\n        (dim.width - this.imgZoom.width * 3) /\r\n        (this.imgZoom.width - this.imgLens.offsetWidth);\r\n      this.cy =\r\n        (dim.height - this.imgZoom.height * 3) /\r\n        (this.imgZoom.height - this.imgLens.offsetHeight);\r\n    }\r\n\r\n    document.getElementById(\"divLens\").style.display = \"none\";\r\n    document.getElementById(\"divZoomed\").style.display = \"none\";\r\n    this.render.addClass(this.imgLens, \"img-zoom-lens\");\r\n    this.render.addClass(this.divZoom, \"zoom-preview\");\r\n  }\r\n\r\n  ImgMouseEnter() {\r\n    document.getElementById(\"divLens\").style.display = \"block\";\r\n    document.getElementById(\"divZoomed\").style.display = \"block\";\r\n  }\r\n\r\n  ImgMouseLeave() {\r\n    document.getElementById(\"divLens\").style.display = \"none\";\r\n    document.getElementById(\"divZoomed\").style.display = \"none\";\r\n  }\r\n\r\n  ImgMouseMove(event: any) {\r\n    if (!this.isMoveEventCalling) {\r\n      this.isMoveEventCalling = true;\r\n\r\n      var pos = this.getCursorPos(event);\r\n\r\n      this.render.setStyle(\r\n        this.divZoom,\r\n        \"background-position\",\r\n        pos.x * this.cx + \"px \" + pos.y * this.cy + \"px\"\r\n      );\r\n\r\n      this.isMoveEventCalling = false;\r\n    }\r\n  }\r\n\r\n  // ToDo : Need to remove later after testing above method\r\n  mouseMove(event: any) {\r\n    if (!this.isMoveEventCalling) {\r\n      event.preventDefault();\r\n\r\n      this.isMoveEventCalling = true;\r\n\r\n      const result = this.moveLens(event);\r\n      this.render.setStyle(this.divZoom, \"background-position\", result);\r\n    }\r\n  }\r\n\r\n  moveLens(e: any) {\r\n    let pos;\r\n    let x;\r\n    let y;\r\n    /*prevent any other actions that may occur when moving over the image:*/\r\n    e.preventDefault();\r\n    /*get the cursor's x and y positions:*/\r\n    pos = this.getCursorPos(e);\r\n\r\n    /*calculate the position of the lens:*/\r\n    x = pos.x - this.imgLens.offsetWidth / 2;\r\n    y = pos.y - this.imgLens.offsetHeight / 2;\r\n    /*prevent the lens from being positioned outside the image:*/\r\n    if (x > this.imgZoom.width - this.imgLens.clientWidth) {\r\n      x = this.imgZoom.width - this.imgLens.offsetWidth;\r\n    }\r\n    if (x < 0) {\r\n      x = 0;\r\n    }\r\n    if (y > this.imgZoom.height - this.imgLens.clientHeight) {\r\n      y = this.imgZoom.height - this.imgLens.offsetHeight;\r\n    }\r\n    if (y < 0) {\r\n      y = 0;\r\n    }\r\n    /*set the position of the lens:*/\r\n\r\n    // this.render.setStyle(this.imgLens, 'left', x + 'px');\r\n    //this.render.setStyle(this.imgLens, 'top', y + 'px');\r\n    /*display what the lens \"sees\":*/\r\n\r\n    let result = x * this.cx + \"px \" + y * this.cy + \"px\";\r\n    this.isMoveEventCalling = false;\r\n    return result;\r\n  }\r\n\r\n  getCursorPos(e) {\r\n    let x = 0,\r\n      y = 0;\r\n    e = e || window.event;\r\n    /*get the x and y positions of the image:*/\r\n\r\n    /*calculate the cursor's x and y coordinates, relative to the image:*/\r\n    x = e.pageX - this.imgClientRect.left;\r\n    y = e.pageY - this.imgClientRect.top;\r\n    /*consider any page scrolling:*/\r\n    x = x - window.pageXOffset;\r\n    y = y - window.pageYOffset;\r\n\r\n    return { x: x, y: y };\r\n  }\r\n\r\n  validateAuthorisedShares() {\r\n    if (this.Form1.controls.FormationType.value != 'FP') return;\r\n\r\n    if (\r\n      this.Form2.controls.TotalAuthorizedShares.value &&\r\n      this.Form2.controls.TotalAuthorizedShares.value <\r\n      this.CertControlsComponent.authorizedSharesCount\r\n    ) {\r\n      this.toaster.warning(\r\n        \"Total Authorized Shares should not be less than total Certificates.\",\r\n        \"Warning\"\r\n      );\r\n      return true;\r\n    }\r\n  }\r\n\r\n\r\n  showLegends() {\r\n\r\n    //var isValidProfessionalCorp = this.Form2.controls.ProfessionalCorporation.value && this.masterData.typeOfCorporations.map(x => x.id).includes(this.Form2.controls.ProfessionalCorporation.value);\r\n\r\n    var isValidProfessionalCorp = this.Form1.controls.ProfessionalCorporation.value && this.masterData.typeOfCorporations.map(x => x.id).includes(this.Form1.controls.ProfessionalCorporation.value);\r\n\r\n    this.showLegend = this.selectedKitType == 'CK' ? this.Form1.controls.FormationType.value != 'NP' || (this.Form1.controls.FormationType.value == 'NP' && isValidProfessionalCorp && !['060', '065'].includes(this.Form1.controls.ProfessionalCorporation.value)) : this.selectedKitType !== 'PS';\r\n\r\n    return this.showLegend;\r\n\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    // when the component get's destroyed, unsubscribe all the subscriptions\r\n    if (this.routesSubscription) {\r\n      this.routesSubscription.unsubscribe();\r\n    }\r\n  }\r\n  addRBallToWord(inputString: string): string {\r\n    const pageTitle = this.productService.addRBallToWord(inputString);\r\n    this.pageTitleService.setPageTitle(pageTitle?.replace(/<[^>]+>/g, ''));\r\n    return pageTitle;\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}