{"ast": null, "code": "import { hostReportError } from './hostReportError';\nexport const subscribeToPromise = promise => subscriber => {\n  promise.then(value => {\n    if (!subscriber.closed) {\n      subscriber.next(value);\n      subscriber.complete();\n    }\n  }, err => subscriber.error(err)).then(null, hostReportError);\n  return subscriber;\n};", "map": {"version": 3, "names": ["hostReportError", "subscribeToPromise", "promise", "subscriber", "then", "value", "closed", "next", "complete", "err", "error"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/subscribeToPromise.js"], "sourcesContent": ["import { hostReportError } from './hostReportError';\nexport const subscribeToPromise = (promise) => (subscriber) => {\n    promise.then((value) => {\n        if (!subscriber.closed) {\n            subscriber.next(value);\n            subscriber.complete();\n        }\n    }, (err) => subscriber.error(err))\n        .then(null, hostReportError);\n    return subscriber;\n};\n"], "mappings": "AAAA,SAASA,eAAT,QAAgC,mBAAhC;AACA,OAAO,MAAMC,kBAAkB,GAAIC,OAAD,IAAcC,UAAD,IAAgB;EAC3DD,OAAO,CAACE,IAAR,CAAcC,KAAD,IAAW;IACpB,IAAI,CAACF,UAAU,CAACG,MAAhB,EAAwB;MACpBH,UAAU,CAACI,IAAX,CAAgBF,KAAhB;MACAF,UAAU,CAACK,QAAX;IACH;EACJ,CALD,EAKIC,GAAD,IAASN,UAAU,CAACO,KAAX,CAAiBD,GAAjB,CALZ,EAMKL,IANL,CAMU,IANV,EAMgBJ,eANhB;EAOA,OAAOG,UAAP;AACH,CATM"}, "metadata": {}, "sourceType": "module"}