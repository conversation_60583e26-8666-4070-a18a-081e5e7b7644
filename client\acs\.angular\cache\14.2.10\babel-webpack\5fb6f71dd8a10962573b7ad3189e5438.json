{"ast": null, "code": "export function hostReportError(err) {\n  setTimeout(() => {\n    throw err;\n  }, 0);\n}", "map": {"version": 3, "names": ["hostReportError", "err", "setTimeout"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/hostReportError.js"], "sourcesContent": ["export function hostReportError(err) {\n    setTimeout(() => { throw err; }, 0);\n}\n"], "mappings": "AAAA,OAAO,SAASA,eAAT,CAAyBC,GAAzB,EAA8B;EACjCC,UAAU,CAAC,MAAM;IAAE,MAAMD,GAAN;EAAY,CAArB,EAAuB,CAAvB,CAAV;AACH"}, "metadata": {}, "sourceType": "module"}