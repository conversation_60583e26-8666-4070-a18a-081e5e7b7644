{"ast": null, "code": "import { AsyncAction } from './AsyncAction';\nexport class Animation<PERSON>rameAction extends AsyncAction {\n  constructor(scheduler, work) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n  }\n\n  requestAsyncId(scheduler, id, delay = 0) {\n    if (delay !== null && delay > 0) {\n      return super.requestAsyncId(scheduler, id, delay);\n    }\n\n    scheduler.actions.push(this);\n    return scheduler.scheduled || (scheduler.scheduled = requestAnimationFrame(() => scheduler.flush(null)));\n  }\n\n  recycleAsyncId(scheduler, id, delay = 0) {\n    if (delay !== null && delay > 0 || delay === null && this.delay > 0) {\n      return super.recycleAsyncId(scheduler, id, delay);\n    }\n\n    if (scheduler.actions.length === 0) {\n      cancelAnimationFrame(id);\n      scheduler.scheduled = undefined;\n    }\n\n    return undefined;\n  }\n\n}", "map": {"version": 3, "names": ["AsyncAction", "AnimationFrameAction", "constructor", "scheduler", "work", "requestAsyncId", "id", "delay", "actions", "push", "scheduled", "requestAnimationFrame", "flush", "recycleAsyncId", "length", "cancelAnimationFrame", "undefined"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/scheduler/AnimationFrameAction.js"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nexport class Animation<PERSON>rameAction extends AsyncAction {\n    constructor(scheduler, work) {\n        super(scheduler, work);\n        this.scheduler = scheduler;\n        this.work = work;\n    }\n    requestAsyncId(scheduler, id, delay = 0) {\n        if (delay !== null && delay > 0) {\n            return super.requestAsyncId(scheduler, id, delay);\n        }\n        scheduler.actions.push(this);\n        return scheduler.scheduled || (scheduler.scheduled = requestAnimationFrame(() => scheduler.flush(null)));\n    }\n    recycleAsyncId(scheduler, id, delay = 0) {\n        if ((delay !== null && delay > 0) || (delay === null && this.delay > 0)) {\n            return super.recycleAsyncId(scheduler, id, delay);\n        }\n        if (scheduler.actions.length === 0) {\n            cancelAnimationFrame(id);\n            scheduler.scheduled = undefined;\n        }\n        return undefined;\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAT,QAA4B,eAA5B;AACA,OAAO,MAAMC,oBAAN,SAAmCD,WAAnC,CAA+C;EAClDE,WAAW,CAACC,SAAD,EAAYC,IAAZ,EAAkB;IACzB,MAAMD,SAAN,EAAiBC,IAAjB;IACA,KAAKD,SAAL,GAAiBA,SAAjB;IACA,KAAKC,IAAL,GAAYA,IAAZ;EACH;;EACDC,cAAc,CAACF,SAAD,EAAYG,EAAZ,EAAgBC,KAAK,GAAG,CAAxB,EAA2B;IACrC,IAAIA,KAAK,KAAK,IAAV,IAAkBA,KAAK,GAAG,CAA9B,EAAiC;MAC7B,OAAO,MAAMF,cAAN,CAAqBF,SAArB,EAAgCG,EAAhC,EAAoCC,KAApC,CAAP;IACH;;IACDJ,SAAS,CAACK,OAAV,CAAkBC,IAAlB,CAAuB,IAAvB;IACA,OAAON,SAAS,CAACO,SAAV,KAAwBP,SAAS,CAACO,SAAV,GAAsBC,qBAAqB,CAAC,MAAMR,SAAS,CAACS,KAAV,CAAgB,IAAhB,CAAP,CAAnE,CAAP;EACH;;EACDC,cAAc,CAACV,SAAD,EAAYG,EAAZ,EAAgBC,KAAK,GAAG,CAAxB,EAA2B;IACrC,IAAKA,KAAK,KAAK,IAAV,IAAkBA,KAAK,GAAG,CAA3B,IAAkCA,KAAK,KAAK,IAAV,IAAkB,KAAKA,KAAL,GAAa,CAArE,EAAyE;MACrE,OAAO,MAAMM,cAAN,CAAqBV,SAArB,EAAgCG,EAAhC,EAAoCC,KAApC,CAAP;IACH;;IACD,IAAIJ,SAAS,CAACK,OAAV,CAAkBM,MAAlB,KAA6B,CAAjC,EAAoC;MAChCC,oBAAoB,CAACT,EAAD,CAApB;MACAH,SAAS,CAACO,SAAV,GAAsBM,SAAtB;IACH;;IACD,OAAOA,SAAP;EACH;;AAtBiD"}, "metadata": {}, "sourceType": "module"}