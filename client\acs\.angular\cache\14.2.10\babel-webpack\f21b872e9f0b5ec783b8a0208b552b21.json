{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function map(project, thisArg) {\n  return function mapOperation(source) {\n    if (typeof project !== 'function') {\n      throw new TypeError('argument is not a function. Are you looking for `mapTo()`?');\n    }\n\n    return source.lift(new MapOperator(project, thisArg));\n  };\n}\nexport class MapOperator {\n  constructor(project, thisArg) {\n    this.project = project;\n    this.thisArg = thisArg;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new MapSubscriber(subscriber, this.project, this.thisArg));\n  }\n\n}\n\nclass MapSubscriber extends Subscriber {\n  constructor(destination, project, thisArg) {\n    super(destination);\n    this.project = project;\n    this.count = 0;\n    this.thisArg = thisArg || this;\n  }\n\n  _next(value) {\n    let result;\n\n    try {\n      result = this.project.call(this.thisArg, value, this.count++);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n\n    this.destination.next(result);\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "map", "project", "thisArg", "mapOperation", "source", "TypeError", "lift", "MapOperator", "constructor", "call", "subscriber", "subscribe", "MapSubscriber", "destination", "count", "_next", "value", "result", "err", "error", "next"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/map.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function map(project, thisArg) {\n    return function mapOperation(source) {\n        if (typeof project !== 'function') {\n            throw new TypeError('argument is not a function. Are you looking for `mapTo()`?');\n        }\n        return source.lift(new MapOperator(project, thisArg));\n    };\n}\nexport class MapOperator {\n    constructor(project, thisArg) {\n        this.project = project;\n        this.thisArg = thisArg;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new MapSubscriber(subscriber, this.project, this.thisArg));\n    }\n}\nclass MapSubscriber extends Subscriber {\n    constructor(destination, project, thisArg) {\n        super(destination);\n        this.project = project;\n        this.count = 0;\n        this.thisArg = thisArg || this;\n    }\n    _next(value) {\n        let result;\n        try {\n            result = this.project.call(this.thisArg, value, this.count++);\n        }\n        catch (err) {\n            this.destination.error(err);\n            return;\n        }\n        this.destination.next(result);\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,GAAT,CAAaC,OAAb,EAAsBC,OAAtB,EAA+B;EAClC,OAAO,SAASC,YAAT,CAAsBC,MAAtB,EAA8B;IACjC,IAAI,OAAOH,OAAP,KAAmB,UAAvB,EAAmC;MAC/B,MAAM,IAAII,SAAJ,CAAc,4DAAd,CAAN;IACH;;IACD,OAAOD,MAAM,CAACE,IAAP,CAAY,IAAIC,WAAJ,CAAgBN,OAAhB,EAAyBC,OAAzB,CAAZ,CAAP;EACH,CALD;AAMH;AACD,OAAO,MAAMK,WAAN,CAAkB;EACrBC,WAAW,CAACP,OAAD,EAAUC,OAAV,EAAmB;IAC1B,KAAKD,OAAL,GAAeA,OAAf;IACA,KAAKC,OAAL,GAAeA,OAAf;EACH;;EACDO,IAAI,CAACC,UAAD,EAAaN,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACO,SAAP,CAAiB,IAAIC,aAAJ,CAAkBF,UAAlB,EAA8B,KAAKT,OAAnC,EAA4C,KAAKC,OAAjD,CAAjB,CAAP;EACH;;AAPoB;;AASzB,MAAMU,aAAN,SAA4Bb,UAA5B,CAAuC;EACnCS,WAAW,CAACK,WAAD,EAAcZ,OAAd,EAAuBC,OAAvB,EAAgC;IACvC,MAAMW,WAAN;IACA,KAAKZ,OAAL,GAAeA,OAAf;IACA,KAAKa,KAAL,GAAa,CAAb;IACA,KAAKZ,OAAL,GAAeA,OAAO,IAAI,IAA1B;EACH;;EACDa,KAAK,CAACC,KAAD,EAAQ;IACT,IAAIC,MAAJ;;IACA,IAAI;MACAA,MAAM,GAAG,KAAKhB,OAAL,CAAaQ,IAAb,CAAkB,KAAKP,OAAvB,EAAgCc,KAAhC,EAAuC,KAAKF,KAAL,EAAvC,CAAT;IACH,CAFD,CAGA,OAAOI,GAAP,EAAY;MACR,KAAKL,WAAL,CAAiBM,KAAjB,CAAuBD,GAAvB;MACA;IACH;;IACD,KAAKL,WAAL,CAAiBO,IAAjB,CAAsBH,MAAtB;EACH;;AAjBkC"}, "metadata": {}, "sourceType": "module"}