{"ast": null, "code": "import { concat } from '../observable/concat';\nimport { isScheduler } from '../util/isScheduler';\nexport function startWith(...array) {\n  const scheduler = array[array.length - 1];\n\n  if (isScheduler(scheduler)) {\n    array.pop();\n    return source => concat(array, source, scheduler);\n  } else {\n    return source => concat(array, source);\n  }\n}", "map": {"version": 3, "names": ["concat", "isScheduler", "startWith", "array", "scheduler", "length", "pop", "source"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/startWith.js"], "sourcesContent": ["import { concat } from '../observable/concat';\nimport { isScheduler } from '../util/isScheduler';\nexport function startWith(...array) {\n    const scheduler = array[array.length - 1];\n    if (isScheduler(scheduler)) {\n        array.pop();\n        return (source) => concat(array, source, scheduler);\n    }\n    else {\n        return (source) => concat(array, source);\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAT,QAAuB,sBAAvB;AACA,SAASC,WAAT,QAA4B,qBAA5B;AACA,OAAO,SAASC,SAAT,CAAmB,GAAGC,KAAtB,EAA6B;EAChC,MAAMC,SAAS,GAAGD,KAAK,CAACA,KAAK,CAACE,MAAN,GAAe,CAAhB,CAAvB;;EACA,IAAIJ,WAAW,CAACG,SAAD,CAAf,EAA4B;IACxBD,KAAK,CAACG,GAAN;IACA,OAAQC,MAAD,IAAYP,MAAM,CAACG,KAAD,EAAQI,MAAR,EAAgBH,SAAhB,CAAzB;EACH,CAHD,MAIK;IACD,OAAQG,MAAD,IAAYP,MAAM,CAACG,KAAD,EAAQI,MAAR,CAAzB;EACH;AACJ"}, "metadata": {}, "sourceType": "module"}