{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { rxSubscriber as rxSubscriberSymbol } from '../symbol/rxSubscriber';\nimport { empty as emptyObserver } from '../Observer';\nexport function toSubscriber(nextOrObserver, error, complete) {\n  if (nextOrObserver) {\n    if (nextOrObserver instanceof Subscriber) {\n      return nextOrObserver;\n    }\n\n    if (nextOrObserver[rxSubscriberSymbol]) {\n      return nextOrObserver[rxSubscriberSymbol]();\n    }\n  }\n\n  if (!nextOrObserver && !error && !complete) {\n    return new Subscriber(emptyObserver);\n  }\n\n  return new Subscriber(nextOrObserver, error, complete);\n}", "map": {"version": 3, "names": ["Subscriber", "rxSubscriber", "rxSubscriberSymbol", "empty", "emptyObserver", "toSubscriber", "nextOrObserver", "error", "complete"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/toSubscriber.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { rxSubscriber as rxSubscriberSymbol } from '../symbol/rxSubscriber';\nimport { empty as emptyObserver } from '../Observer';\nexport function toSubscriber(nextOrObserver, error, complete) {\n    if (nextOrObserver) {\n        if (nextOrObserver instanceof Subscriber) {\n            return nextOrObserver;\n        }\n        if (nextOrObserver[rxSubscriberSymbol]) {\n            return nextOrObserver[rxSubscriberSymbol]();\n        }\n    }\n    if (!nextOrObserver && !error && !complete) {\n        return new Subscriber(emptyObserver);\n    }\n    return new Subscriber(nextOrObserver, error, complete);\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,YAAY,IAAIC,kBAAzB,QAAmD,wBAAnD;AACA,SAASC,KAAK,IAAIC,aAAlB,QAAuC,aAAvC;AACA,OAAO,SAASC,YAAT,CAAsBC,cAAtB,EAAsCC,KAAtC,EAA6CC,QAA7C,EAAuD;EAC1D,IAAIF,cAAJ,EAAoB;IAChB,IAAIA,cAAc,YAAYN,UAA9B,EAA0C;MACtC,OAAOM,cAAP;IACH;;IACD,IAAIA,cAAc,CAACJ,kBAAD,CAAlB,EAAwC;MACpC,OAAOI,cAAc,CAACJ,kBAAD,CAAd,EAAP;IACH;EACJ;;EACD,IAAI,CAACI,cAAD,IAAmB,CAACC,KAApB,IAA6B,CAACC,QAAlC,EAA4C;IACxC,OAAO,IAAIR,UAAJ,CAAeI,aAAf,CAAP;EACH;;EACD,OAAO,IAAIJ,UAAJ,CAAeM,cAAf,EAA+BC,KAA/B,EAAsCC,QAAtC,CAAP;AACH"}, "metadata": {}, "sourceType": "module"}