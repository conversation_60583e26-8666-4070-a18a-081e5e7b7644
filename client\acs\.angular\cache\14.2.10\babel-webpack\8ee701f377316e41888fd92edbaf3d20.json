{"ast": null, "code": "import getUAString from \"../utils/userAgent.js\";\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}", "map": {"version": 3, "names": ["getUAString", "isLayoutViewport", "test"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js"], "sourcesContent": ["import getUAString from \"../utils/userAgent.js\";\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}"], "mappings": "AAAA,OAAOA,WAAP,MAAwB,uBAAxB;AACA,eAAe,SAASC,gBAAT,GAA4B;EACzC,OAAO,CAAC,iCAAiCC,IAAjC,CAAsCF,WAAW,EAAjD,CAAR;AACD"}, "metadata": {}, "sourceType": "module"}