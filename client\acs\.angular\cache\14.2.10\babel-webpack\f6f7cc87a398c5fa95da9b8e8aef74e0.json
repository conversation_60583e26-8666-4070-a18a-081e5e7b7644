{"ast": null, "code": "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "map": {"version": 3, "names": ["mergeByName", "modifiers", "merged", "reduce", "current", "existing", "name", "Object", "assign", "options", "data", "keys", "map", "key"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/utils/mergeByName.js"], "sourcesContent": ["export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}"], "mappings": "AAAA,eAAe,SAASA,WAAT,CAAqBC,SAArB,EAAgC;EAC7C,IAAIC,MAAM,GAAGD,SAAS,CAACE,MAAV,CAAiB,UAAUD,MAAV,EAAkBE,OAAlB,EAA2B;IACvD,IAAIC,QAAQ,GAAGH,MAAM,CAACE,OAAO,CAACE,IAAT,CAArB;IACAJ,MAAM,CAACE,OAAO,CAACE,IAAT,CAAN,GAAuBD,QAAQ,GAAGE,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBH,QAAlB,EAA4BD,OAA5B,EAAqC;MACrEK,OAAO,EAAEF,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBH,QAAQ,CAACI,OAA3B,EAAoCL,OAAO,CAACK,OAA5C,CAD4D;MAErEC,IAAI,EAAEH,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBH,QAAQ,CAACK,IAA3B,EAAiCN,OAAO,CAACM,IAAzC;IAF+D,CAArC,CAAH,GAG1BN,OAHL;IAIA,OAAOF,MAAP;EACD,CAPY,EAOV,EAPU,CAAb,CAD6C,CAQrC;;EAER,OAAOK,MAAM,CAACI,IAAP,CAAYT,MAAZ,EAAoBU,GAApB,CAAwB,UAAUC,GAAV,EAAe;IAC5C,OAAOX,MAAM,CAACW,GAAD,CAAb;EACD,CAFM,CAAP;AAGD"}, "metadata": {}, "sourceType": "module"}