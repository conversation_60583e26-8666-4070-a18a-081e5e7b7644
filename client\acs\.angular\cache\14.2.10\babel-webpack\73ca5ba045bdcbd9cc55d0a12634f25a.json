{"ast": null, "code": "const ArgumentOutOfRangeErrorImpl = (() => {\n  function ArgumentOutOfRangeErrorImpl() {\n    Error.call(this);\n    this.message = 'argument out of range';\n    this.name = 'ArgumentOutOfRangeError';\n    return this;\n  }\n\n  ArgumentOutOfRangeErrorImpl.prototype = Object.create(Error.prototype);\n  return ArgumentOutOfRangeErrorImpl;\n})();\n\nexport const ArgumentOutOfRangeError = ArgumentOutOfRangeErrorImpl;", "map": {"version": 3, "names": ["ArgumentOutOfRangeErrorImpl", "Error", "call", "message", "name", "prototype", "Object", "create", "ArgumentOutOfRangeError"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/ArgumentOutOfRangeError.js"], "sourcesContent": ["const ArgumentOutOfRangeErrorImpl = (() => {\n    function ArgumentOutOfRangeErrorImpl() {\n        Error.call(this);\n        this.message = 'argument out of range';\n        this.name = 'ArgumentOutOfRangeError';\n        return this;\n    }\n    ArgumentOutOfRangeErrorImpl.prototype = Object.create(Error.prototype);\n    return ArgumentOutOfRangeErrorImpl;\n})();\nexport const ArgumentOutOfRangeError = ArgumentOutOfRangeErrorImpl;\n"], "mappings": "AAAA,MAAMA,2BAA2B,GAAG,CAAC,MAAM;EACvC,SAASA,2BAAT,GAAuC;IACnCC,KAAK,CAACC,IAAN,CAAW,IAAX;IACA,KAAKC,OAAL,GAAe,uBAAf;IACA,KAAKC,IAAL,GAAY,yBAAZ;IACA,OAAO,IAAP;EACH;;EACDJ,2BAA2B,CAACK,SAA5B,GAAwCC,MAAM,CAACC,MAAP,CAAcN,KAAK,CAACI,SAApB,CAAxC;EACA,OAAOL,2BAAP;AACH,CATmC,GAApC;;AAUA,OAAO,MAAMQ,uBAAuB,GAAGR,2BAAhC"}, "metadata": {}, "sourceType": "module"}