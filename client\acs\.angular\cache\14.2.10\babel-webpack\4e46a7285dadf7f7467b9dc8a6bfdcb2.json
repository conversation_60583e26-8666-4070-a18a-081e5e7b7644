{"ast": null, "code": "import { Observable } from '../Observable';\nexport const EMPTY = new Observable(subscriber => subscriber.complete());\nexport function empty(scheduler) {\n  return scheduler ? emptyScheduled(scheduler) : EMPTY;\n}\n\nfunction emptyScheduled(scheduler) {\n  return new Observable(subscriber => scheduler.schedule(() => subscriber.complete()));\n}", "map": {"version": 3, "names": ["Observable", "EMPTY", "subscriber", "complete", "empty", "scheduler", "emptyScheduled", "schedule"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/empty.js"], "sourcesContent": ["import { Observable } from '../Observable';\nexport const EMPTY = new Observable(subscriber => subscriber.complete());\nexport function empty(scheduler) {\n    return scheduler ? emptyScheduled(scheduler) : EMPTY;\n}\nfunction emptyScheduled(scheduler) {\n    return new Observable(subscriber => scheduler.schedule(() => subscriber.complete()));\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,MAAMC,KAAK,GAAG,IAAID,UAAJ,CAAeE,UAAU,IAAIA,UAAU,CAACC,QAAX,EAA7B,CAAd;AACP,OAAO,SAASC,KAAT,CAAeC,SAAf,EAA0B;EAC7B,OAAOA,SAAS,GAAGC,cAAc,CAACD,SAAD,CAAjB,GAA+BJ,KAA/C;AACH;;AACD,SAASK,cAAT,CAAwBD,SAAxB,EAAmC;EAC/B,OAAO,IAAIL,UAAJ,CAAeE,UAAU,IAAIG,SAAS,CAACE,QAAV,CAAmB,MAAML,UAAU,CAACC,QAAX,EAAzB,CAA7B,CAAP;AACH"}, "metadata": {}, "sourceType": "module"}