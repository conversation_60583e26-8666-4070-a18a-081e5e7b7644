{"ast": null, "code": "'use strict';\n\nvar toStr = Object.prototype.toString;\nvar fnToStr = Function.prototype.toString;\nvar isFnRegex = /^\\s*(?:function)?\\*/;\n\nvar hasToStringTag = require('has-tostringtag/shams')();\n\nvar getProto = Object.getPrototypeOf;\n\nvar getGeneratorFunc = function () {\n  // eslint-disable-line consistent-return\n  if (!hasToStringTag) {\n    return false;\n  }\n\n  try {\n    return Function('return function*() {}')();\n  } catch (e) {}\n};\n\nvar GeneratorFunction;\n\nmodule.exports = function isGeneratorFunction(fn) {\n  if (typeof fn !== 'function') {\n    return false;\n  }\n\n  if (isFnRegex.test(fnToStr.call(fn))) {\n    return true;\n  }\n\n  if (!hasToStringTag) {\n    var str = toStr.call(fn);\n    return str === '[object GeneratorFunction]';\n  }\n\n  if (!getProto) {\n    return false;\n  }\n\n  if (typeof GeneratorFunction === 'undefined') {\n    var generatorFunc = getGeneratorFunc();\n    GeneratorFunction = generatorFunc ? getProto(generatorFunc) : false;\n  }\n\n  return getProto(fn) === GeneratorFunction;\n};", "map": {"version": 3, "names": ["toStr", "Object", "prototype", "toString", "fnToStr", "Function", "isFnRegex", "hasToStringTag", "require", "getProto", "getPrototypeOf", "getGeneratorFunc", "e", "GeneratorFunction", "module", "exports", "isGeneratorFunction", "fn", "test", "call", "str", "generatorFunc"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/is-generator-function/index.js"], "sourcesContent": ["'use strict';\n\nvar toStr = Object.prototype.toString;\nvar fnToStr = Function.prototype.toString;\nvar isFnRegex = /^\\s*(?:function)?\\*/;\nvar hasToStringTag = require('has-tostringtag/shams')();\nvar getProto = Object.getPrototypeOf;\nvar getGeneratorFunc = function () { // eslint-disable-line consistent-return\n\tif (!hasToStringTag) {\n\t\treturn false;\n\t}\n\ttry {\n\t\treturn Function('return function*() {}')();\n\t} catch (e) {\n\t}\n};\nvar GeneratorFunction;\n\nmodule.exports = function isGeneratorFunction(fn) {\n\tif (typeof fn !== 'function') {\n\t\treturn false;\n\t}\n\tif (isFnRegex.test(fnToStr.call(fn))) {\n\t\treturn true;\n\t}\n\tif (!hasToStringTag) {\n\t\tvar str = toStr.call(fn);\n\t\treturn str === '[object GeneratorFunction]';\n\t}\n\tif (!getProto) {\n\t\treturn false;\n\t}\n\tif (typeof GeneratorFunction === 'undefined') {\n\t\tvar generatorFunc = getGeneratorFunc();\n\t\tGeneratorFunction = generatorFunc ? getProto(generatorFunc) : false;\n\t}\n\treturn getProto(fn) === GeneratorFunction;\n};\n"], "mappings": "AAAA;;AAEA,IAAIA,KAAK,GAAGC,MAAM,CAACC,SAAP,CAAiBC,QAA7B;AACA,IAAIC,OAAO,GAAGC,QAAQ,CAACH,SAAT,CAAmBC,QAAjC;AACA,IAAIG,SAAS,GAAG,qBAAhB;;AACA,IAAIC,cAAc,GAAGC,OAAO,CAAC,uBAAD,CAAP,EAArB;;AACA,IAAIC,QAAQ,GAAGR,MAAM,CAACS,cAAtB;;AACA,IAAIC,gBAAgB,GAAG,YAAY;EAAE;EACpC,IAAI,CAACJ,cAAL,EAAqB;IACpB,OAAO,KAAP;EACA;;EACD,IAAI;IACH,OAAOF,QAAQ,CAAC,uBAAD,CAAR,EAAP;EACA,CAFD,CAEE,OAAOO,CAAP,EAAU,CACX;AACD,CARD;;AASA,IAAIC,iBAAJ;;AAEAC,MAAM,CAACC,OAAP,GAAiB,SAASC,mBAAT,CAA6BC,EAA7B,EAAiC;EACjD,IAAI,OAAOA,EAAP,KAAc,UAAlB,EAA8B;IAC7B,OAAO,KAAP;EACA;;EACD,IAAIX,SAAS,CAACY,IAAV,CAAed,OAAO,CAACe,IAAR,CAAaF,EAAb,CAAf,CAAJ,EAAsC;IACrC,OAAO,IAAP;EACA;;EACD,IAAI,CAACV,cAAL,EAAqB;IACpB,IAAIa,GAAG,GAAGpB,KAAK,CAACmB,IAAN,CAAWF,EAAX,CAAV;IACA,OAAOG,GAAG,KAAK,4BAAf;EACA;;EACD,IAAI,CAACX,QAAL,EAAe;IACd,OAAO,KAAP;EACA;;EACD,IAAI,OAAOI,iBAAP,KAA6B,WAAjC,EAA8C;IAC7C,IAAIQ,aAAa,GAAGV,gBAAgB,EAApC;IACAE,iBAAiB,GAAGQ,aAAa,GAAGZ,QAAQ,CAACY,aAAD,CAAX,GAA6B,KAA9D;EACA;;EACD,OAAOZ,QAAQ,CAACQ,EAAD,CAAR,KAAiBJ,iBAAxB;AACA,CAnBD"}, "metadata": {}, "sourceType": "script"}