{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { Observable } from '../Observable';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function delayWhen(delayDurationSelector, subscriptionDelay) {\n  if (subscriptionDelay) {\n    return source => new SubscriptionDelayObservable(source, subscriptionDelay).lift(new DelayWhenOperator(delayDurationSelector));\n  }\n\n  return source => source.lift(new DelayWhenOperator(delayDurationSelector));\n}\n\nclass DelayWhenOperator {\n  constructor(delayDurationSelector) {\n    this.delayDurationSelector = delayDurationSelector;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new DelayWhenSubscriber(subscriber, this.delayDurationSelector));\n  }\n\n}\n\nclass DelayWhenSubscriber extends OuterSubscriber {\n  constructor(destination, delayDurationSelector) {\n    super(destination);\n    this.delayDurationSelector = delayDurationSelector;\n    this.completed = false;\n    this.delayNotifierSubscriptions = [];\n    this.index = 0;\n  }\n\n  notifyNext(outerValue, _innerValue, _outerIndex, _innerIndex, innerSub) {\n    this.destination.next(outerValue);\n    this.removeSubscription(innerSub);\n    this.tryComplete();\n  }\n\n  notifyError(error, innerSub) {\n    this._error(error);\n  }\n\n  notifyComplete(innerSub) {\n    const value = this.removeSubscription(innerSub);\n\n    if (value) {\n      this.destination.next(value);\n    }\n\n    this.tryComplete();\n  }\n\n  _next(value) {\n    const index = this.index++;\n\n    try {\n      const delayNotifier = this.delayDurationSelector(value, index);\n\n      if (delayNotifier) {\n        this.tryDelay(delayNotifier, value);\n      }\n    } catch (err) {\n      this.destination.error(err);\n    }\n  }\n\n  _complete() {\n    this.completed = true;\n    this.tryComplete();\n    this.unsubscribe();\n  }\n\n  removeSubscription(subscription) {\n    subscription.unsubscribe();\n    const subscriptionIdx = this.delayNotifierSubscriptions.indexOf(subscription);\n\n    if (subscriptionIdx !== -1) {\n      this.delayNotifierSubscriptions.splice(subscriptionIdx, 1);\n    }\n\n    return subscription.outerValue;\n  }\n\n  tryDelay(delayNotifier, value) {\n    const notifierSubscription = subscribeToResult(this, delayNotifier, value);\n\n    if (notifierSubscription && !notifierSubscription.closed) {\n      const destination = this.destination;\n      destination.add(notifierSubscription);\n      this.delayNotifierSubscriptions.push(notifierSubscription);\n    }\n  }\n\n  tryComplete() {\n    if (this.completed && this.delayNotifierSubscriptions.length === 0) {\n      this.destination.complete();\n    }\n  }\n\n}\n\nclass SubscriptionDelayObservable extends Observable {\n  constructor(source, subscriptionDelay) {\n    super();\n    this.source = source;\n    this.subscriptionDelay = subscriptionDelay;\n  }\n\n  _subscribe(subscriber) {\n    this.subscriptionDelay.subscribe(new SubscriptionDelaySubscriber(subscriber, this.source));\n  }\n\n}\n\nclass SubscriptionDelaySubscriber extends Subscriber {\n  constructor(parent, source) {\n    super();\n    this.parent = parent;\n    this.source = source;\n    this.sourceSubscribed = false;\n  }\n\n  _next(unused) {\n    this.subscribeToSource();\n  }\n\n  _error(err) {\n    this.unsubscribe();\n    this.parent.error(err);\n  }\n\n  _complete() {\n    this.unsubscribe();\n    this.subscribeToSource();\n  }\n\n  subscribeToSource() {\n    if (!this.sourceSubscribed) {\n      this.sourceSubscribed = true;\n      this.unsubscribe();\n      this.source.subscribe(this.parent);\n    }\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "Observable", "OuterSubscriber", "subscribeToResult", "<PERSON><PERSON>hen", "delayDurationSelector", "subscriptionDelay", "source", "SubscriptionDelayObservable", "lift", "DelayWhenOperator", "constructor", "call", "subscriber", "subscribe", "DelayWhenSubscriber", "destination", "completed", "delayNotifierSubscriptions", "index", "notifyNext", "outerValue", "_innerValue", "_outerIndex", "_innerIndex", "innerSub", "next", "removeSubscription", "tryComplete", "notifyError", "error", "_error", "notifyComplete", "value", "_next", "delayNotifier", "<PERSON><PERSON><PERSON><PERSON>", "err", "_complete", "unsubscribe", "subscription", "subscriptionIdx", "indexOf", "splice", "notifierSubscription", "closed", "add", "push", "length", "complete", "_subscribe", "SubscriptionDelaySubscriber", "parent", "sourceSubscribed", "unused", "subscribeToSource"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/delayWhen.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { Observable } from '../Observable';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function delayWhen(delayDurationSelector, subscriptionDelay) {\n    if (subscriptionDelay) {\n        return (source) => new SubscriptionDelayObservable(source, subscriptionDelay)\n            .lift(new DelayWhenOperator(delayDurationSelector));\n    }\n    return (source) => source.lift(new DelayWhenOperator(delayDurationSelector));\n}\nclass DelayWhenOperator {\n    constructor(delayDurationSelector) {\n        this.delayDurationSelector = delayDurationSelector;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new DelayWhenSubscriber(subscriber, this.delayDurationSelector));\n    }\n}\nclass DelayWhenSubscriber extends OuterSubscriber {\n    constructor(destination, delayDurationSelector) {\n        super(destination);\n        this.delayDurationSelector = delayDurationSelector;\n        this.completed = false;\n        this.delayNotifierSubscriptions = [];\n        this.index = 0;\n    }\n    notifyNext(outerValue, _innerValue, _outerIndex, _innerIndex, innerSub) {\n        this.destination.next(outerValue);\n        this.removeSubscription(innerSub);\n        this.tryComplete();\n    }\n    notifyError(error, innerSub) {\n        this._error(error);\n    }\n    notifyComplete(innerSub) {\n        const value = this.removeSubscription(innerSub);\n        if (value) {\n            this.destination.next(value);\n        }\n        this.tryComplete();\n    }\n    _next(value) {\n        const index = this.index++;\n        try {\n            const delayNotifier = this.delayDurationSelector(value, index);\n            if (delayNotifier) {\n                this.tryDelay(delayNotifier, value);\n            }\n        }\n        catch (err) {\n            this.destination.error(err);\n        }\n    }\n    _complete() {\n        this.completed = true;\n        this.tryComplete();\n        this.unsubscribe();\n    }\n    removeSubscription(subscription) {\n        subscription.unsubscribe();\n        const subscriptionIdx = this.delayNotifierSubscriptions.indexOf(subscription);\n        if (subscriptionIdx !== -1) {\n            this.delayNotifierSubscriptions.splice(subscriptionIdx, 1);\n        }\n        return subscription.outerValue;\n    }\n    tryDelay(delayNotifier, value) {\n        const notifierSubscription = subscribeToResult(this, delayNotifier, value);\n        if (notifierSubscription && !notifierSubscription.closed) {\n            const destination = this.destination;\n            destination.add(notifierSubscription);\n            this.delayNotifierSubscriptions.push(notifierSubscription);\n        }\n    }\n    tryComplete() {\n        if (this.completed && this.delayNotifierSubscriptions.length === 0) {\n            this.destination.complete();\n        }\n    }\n}\nclass SubscriptionDelayObservable extends Observable {\n    constructor(source, subscriptionDelay) {\n        super();\n        this.source = source;\n        this.subscriptionDelay = subscriptionDelay;\n    }\n    _subscribe(subscriber) {\n        this.subscriptionDelay.subscribe(new SubscriptionDelaySubscriber(subscriber, this.source));\n    }\n}\nclass SubscriptionDelaySubscriber extends Subscriber {\n    constructor(parent, source) {\n        super();\n        this.parent = parent;\n        this.source = source;\n        this.sourceSubscribed = false;\n    }\n    _next(unused) {\n        this.subscribeToSource();\n    }\n    _error(err) {\n        this.unsubscribe();\n        this.parent.error(err);\n    }\n    _complete() {\n        this.unsubscribe();\n        this.subscribeToSource();\n    }\n    subscribeToSource() {\n        if (!this.sourceSubscribed) {\n            this.sourceSubscribed = true;\n            this.unsubscribe();\n            this.source.subscribe(this.parent);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,UAAT,QAA2B,eAA3B;AACA,SAASC,eAAT,QAAgC,oBAAhC;AACA,SAASC,iBAAT,QAAkC,2BAAlC;AACA,OAAO,SAASC,SAAT,CAAmBC,qBAAnB,EAA0CC,iBAA1C,EAA6D;EAChE,IAAIA,iBAAJ,EAAuB;IACnB,OAAQC,MAAD,IAAY,IAAIC,2BAAJ,CAAgCD,MAAhC,EAAwCD,iBAAxC,EACdG,IADc,CACT,IAAIC,iBAAJ,CAAsBL,qBAAtB,CADS,CAAnB;EAEH;;EACD,OAAQE,MAAD,IAAYA,MAAM,CAACE,IAAP,CAAY,IAAIC,iBAAJ,CAAsBL,qBAAtB,CAAZ,CAAnB;AACH;;AACD,MAAMK,iBAAN,CAAwB;EACpBC,WAAW,CAACN,qBAAD,EAAwB;IAC/B,KAAKA,qBAAL,GAA6BA,qBAA7B;EACH;;EACDO,IAAI,CAACC,UAAD,EAAaN,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACO,SAAP,CAAiB,IAAIC,mBAAJ,CAAwBF,UAAxB,EAAoC,KAAKR,qBAAzC,CAAjB,CAAP;EACH;;AANmB;;AAQxB,MAAMU,mBAAN,SAAkCb,eAAlC,CAAkD;EAC9CS,WAAW,CAACK,WAAD,EAAcX,qBAAd,EAAqC;IAC5C,MAAMW,WAAN;IACA,KAAKX,qBAAL,GAA6BA,qBAA7B;IACA,KAAKY,SAAL,GAAiB,KAAjB;IACA,KAAKC,0BAAL,GAAkC,EAAlC;IACA,KAAKC,KAAL,GAAa,CAAb;EACH;;EACDC,UAAU,CAACC,UAAD,EAAaC,WAAb,EAA0BC,WAA1B,EAAuCC,WAAvC,EAAoDC,QAApD,EAA8D;IACpE,KAAKT,WAAL,CAAiBU,IAAjB,CAAsBL,UAAtB;IACA,KAAKM,kBAAL,CAAwBF,QAAxB;IACA,KAAKG,WAAL;EACH;;EACDC,WAAW,CAACC,KAAD,EAAQL,QAAR,EAAkB;IACzB,KAAKM,MAAL,CAAYD,KAAZ;EACH;;EACDE,cAAc,CAACP,QAAD,EAAW;IACrB,MAAMQ,KAAK,GAAG,KAAKN,kBAAL,CAAwBF,QAAxB,CAAd;;IACA,IAAIQ,KAAJ,EAAW;MACP,KAAKjB,WAAL,CAAiBU,IAAjB,CAAsBO,KAAtB;IACH;;IACD,KAAKL,WAAL;EACH;;EACDM,KAAK,CAACD,KAAD,EAAQ;IACT,MAAMd,KAAK,GAAG,KAAKA,KAAL,EAAd;;IACA,IAAI;MACA,MAAMgB,aAAa,GAAG,KAAK9B,qBAAL,CAA2B4B,KAA3B,EAAkCd,KAAlC,CAAtB;;MACA,IAAIgB,aAAJ,EAAmB;QACf,KAAKC,QAAL,CAAcD,aAAd,EAA6BF,KAA7B;MACH;IACJ,CALD,CAMA,OAAOI,GAAP,EAAY;MACR,KAAKrB,WAAL,CAAiBc,KAAjB,CAAuBO,GAAvB;IACH;EACJ;;EACDC,SAAS,GAAG;IACR,KAAKrB,SAAL,GAAiB,IAAjB;IACA,KAAKW,WAAL;IACA,KAAKW,WAAL;EACH;;EACDZ,kBAAkB,CAACa,YAAD,EAAe;IAC7BA,YAAY,CAACD,WAAb;IACA,MAAME,eAAe,GAAG,KAAKvB,0BAAL,CAAgCwB,OAAhC,CAAwCF,YAAxC,CAAxB;;IACA,IAAIC,eAAe,KAAK,CAAC,CAAzB,EAA4B;MACxB,KAAKvB,0BAAL,CAAgCyB,MAAhC,CAAuCF,eAAvC,EAAwD,CAAxD;IACH;;IACD,OAAOD,YAAY,CAACnB,UAApB;EACH;;EACDe,QAAQ,CAACD,aAAD,EAAgBF,KAAhB,EAAuB;IAC3B,MAAMW,oBAAoB,GAAGzC,iBAAiB,CAAC,IAAD,EAAOgC,aAAP,EAAsBF,KAAtB,CAA9C;;IACA,IAAIW,oBAAoB,IAAI,CAACA,oBAAoB,CAACC,MAAlD,EAA0D;MACtD,MAAM7B,WAAW,GAAG,KAAKA,WAAzB;MACAA,WAAW,CAAC8B,GAAZ,CAAgBF,oBAAhB;MACA,KAAK1B,0BAAL,CAAgC6B,IAAhC,CAAqCH,oBAArC;IACH;EACJ;;EACDhB,WAAW,GAAG;IACV,IAAI,KAAKX,SAAL,IAAkB,KAAKC,0BAAL,CAAgC8B,MAAhC,KAA2C,CAAjE,EAAoE;MAChE,KAAKhC,WAAL,CAAiBiC,QAAjB;IACH;EACJ;;AA5D6C;;AA8DlD,MAAMzC,2BAAN,SAA0CP,UAA1C,CAAqD;EACjDU,WAAW,CAACJ,MAAD,EAASD,iBAAT,EAA4B;IACnC;IACA,KAAKC,MAAL,GAAcA,MAAd;IACA,KAAKD,iBAAL,GAAyBA,iBAAzB;EACH;;EACD4C,UAAU,CAACrC,UAAD,EAAa;IACnB,KAAKP,iBAAL,CAAuBQ,SAAvB,CAAiC,IAAIqC,2BAAJ,CAAgCtC,UAAhC,EAA4C,KAAKN,MAAjD,CAAjC;EACH;;AARgD;;AAUrD,MAAM4C,2BAAN,SAA0CnD,UAA1C,CAAqD;EACjDW,WAAW,CAACyC,MAAD,EAAS7C,MAAT,EAAiB;IACxB;IACA,KAAK6C,MAAL,GAAcA,MAAd;IACA,KAAK7C,MAAL,GAAcA,MAAd;IACA,KAAK8C,gBAAL,GAAwB,KAAxB;EACH;;EACDnB,KAAK,CAACoB,MAAD,EAAS;IACV,KAAKC,iBAAL;EACH;;EACDxB,MAAM,CAACM,GAAD,EAAM;IACR,KAAKE,WAAL;IACA,KAAKa,MAAL,CAAYtB,KAAZ,CAAkBO,GAAlB;EACH;;EACDC,SAAS,GAAG;IACR,KAAKC,WAAL;IACA,KAAKgB,iBAAL;EACH;;EACDA,iBAAiB,GAAG;IAChB,IAAI,CAAC,KAAKF,gBAAV,EAA4B;MACxB,KAAKA,gBAAL,GAAwB,IAAxB;MACA,KAAKd,WAAL;MACA,KAAKhC,MAAL,CAAYO,SAAZ,CAAsB,KAAKsC,MAA3B;IACH;EACJ;;AAxBgD"}, "metadata": {}, "sourceType": "module"}