{"ast": null, "code": "import { Subscription } from '../Subscription';\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { OuterSubscriber } from '../OuterSubscriber';\nexport function bufferToggle(openings, closingSelector) {\n  return function bufferToggleOperatorFunction(source) {\n    return source.lift(new BufferToggleOperator(openings, closingSelector));\n  };\n}\n\nclass BufferToggleOperator {\n  constructor(openings, closingSelector) {\n    this.openings = openings;\n    this.closingSelector = closingSelector;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new BufferToggleSubscriber(subscriber, this.openings, this.closingSelector));\n  }\n\n}\n\nclass BufferToggleSubscriber extends OuterSubscriber {\n  constructor(destination, openings, closingSelector) {\n    super(destination);\n    this.closingSelector = closingSelector;\n    this.contexts = [];\n    this.add(subscribeToResult(this, openings));\n  }\n\n  _next(value) {\n    const contexts = this.contexts;\n    const len = contexts.length;\n\n    for (let i = 0; i < len; i++) {\n      contexts[i].buffer.push(value);\n    }\n  }\n\n  _error(err) {\n    const contexts = this.contexts;\n\n    while (contexts.length > 0) {\n      const context = contexts.shift();\n      context.subscription.unsubscribe();\n      context.buffer = null;\n      context.subscription = null;\n    }\n\n    this.contexts = null;\n\n    super._error(err);\n  }\n\n  _complete() {\n    const contexts = this.contexts;\n\n    while (contexts.length > 0) {\n      const context = contexts.shift();\n      this.destination.next(context.buffer);\n      context.subscription.unsubscribe();\n      context.buffer = null;\n      context.subscription = null;\n    }\n\n    this.contexts = null;\n\n    super._complete();\n  }\n\n  notifyNext(outerValue, innerValue) {\n    outerValue ? this.closeBuffer(outerValue) : this.openBuffer(innerValue);\n  }\n\n  notifyComplete(innerSub) {\n    this.closeBuffer(innerSub.context);\n  }\n\n  openBuffer(value) {\n    try {\n      const closingSelector = this.closingSelector;\n      const closingNotifier = closingSelector.call(this, value);\n\n      if (closingNotifier) {\n        this.trySubscribe(closingNotifier);\n      }\n    } catch (err) {\n      this._error(err);\n    }\n  }\n\n  closeBuffer(context) {\n    const contexts = this.contexts;\n\n    if (contexts && context) {\n      const {\n        buffer,\n        subscription\n      } = context;\n      this.destination.next(buffer);\n      contexts.splice(contexts.indexOf(context), 1);\n      this.remove(subscription);\n      subscription.unsubscribe();\n    }\n  }\n\n  trySubscribe(closingNotifier) {\n    const contexts = this.contexts;\n    const buffer = [];\n    const subscription = new Subscription();\n    const context = {\n      buffer,\n      subscription\n    };\n    contexts.push(context);\n    const innerSubscription = subscribeToResult(this, closingNotifier, context);\n\n    if (!innerSubscription || innerSubscription.closed) {\n      this.closeBuffer(context);\n    } else {\n      innerSubscription.context = context;\n      this.add(innerSubscription);\n      subscription.add(innerSubscription);\n    }\n  }\n\n}", "map": {"version": 3, "names": ["Subscription", "subscribeToResult", "OuterSubscriber", "bufferToggle", "openings", "closingSelector", "bufferToggleOperatorFunction", "source", "lift", "BufferToggleOperator", "constructor", "call", "subscriber", "subscribe", "BufferToggleSubscriber", "destination", "contexts", "add", "_next", "value", "len", "length", "i", "buffer", "push", "_error", "err", "context", "shift", "subscription", "unsubscribe", "_complete", "next", "notifyNext", "outerValue", "innerValue", "<PERSON><PERSON><PERSON><PERSON>", "openBuffer", "notifyComplete", "innerSub", "closingNotifier", "trySubscribe", "splice", "indexOf", "remove", "innerSubscription", "closed"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/bufferToggle.js"], "sourcesContent": ["import { Subscription } from '../Subscription';\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { OuterSubscriber } from '../OuterSubscriber';\nexport function bufferToggle(openings, closingSelector) {\n    return function bufferToggleOperatorFunction(source) {\n        return source.lift(new BufferToggleOperator(openings, closingSelector));\n    };\n}\nclass BufferToggleOperator {\n    constructor(openings, closingSelector) {\n        this.openings = openings;\n        this.closingSelector = closingSelector;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new BufferToggleSubscriber(subscriber, this.openings, this.closingSelector));\n    }\n}\nclass BufferToggleSubscriber extends OuterSubscriber {\n    constructor(destination, openings, closingSelector) {\n        super(destination);\n        this.closingSelector = closingSelector;\n        this.contexts = [];\n        this.add(subscribeToResult(this, openings));\n    }\n    _next(value) {\n        const contexts = this.contexts;\n        const len = contexts.length;\n        for (let i = 0; i < len; i++) {\n            contexts[i].buffer.push(value);\n        }\n    }\n    _error(err) {\n        const contexts = this.contexts;\n        while (contexts.length > 0) {\n            const context = contexts.shift();\n            context.subscription.unsubscribe();\n            context.buffer = null;\n            context.subscription = null;\n        }\n        this.contexts = null;\n        super._error(err);\n    }\n    _complete() {\n        const contexts = this.contexts;\n        while (contexts.length > 0) {\n            const context = contexts.shift();\n            this.destination.next(context.buffer);\n            context.subscription.unsubscribe();\n            context.buffer = null;\n            context.subscription = null;\n        }\n        this.contexts = null;\n        super._complete();\n    }\n    notifyNext(outerValue, innerValue) {\n        outerValue ? this.closeBuffer(outerValue) : this.openBuffer(innerValue);\n    }\n    notifyComplete(innerSub) {\n        this.closeBuffer(innerSub.context);\n    }\n    openBuffer(value) {\n        try {\n            const closingSelector = this.closingSelector;\n            const closingNotifier = closingSelector.call(this, value);\n            if (closingNotifier) {\n                this.trySubscribe(closingNotifier);\n            }\n        }\n        catch (err) {\n            this._error(err);\n        }\n    }\n    closeBuffer(context) {\n        const contexts = this.contexts;\n        if (contexts && context) {\n            const { buffer, subscription } = context;\n            this.destination.next(buffer);\n            contexts.splice(contexts.indexOf(context), 1);\n            this.remove(subscription);\n            subscription.unsubscribe();\n        }\n    }\n    trySubscribe(closingNotifier) {\n        const contexts = this.contexts;\n        const buffer = [];\n        const subscription = new Subscription();\n        const context = { buffer, subscription };\n        contexts.push(context);\n        const innerSubscription = subscribeToResult(this, closingNotifier, context);\n        if (!innerSubscription || innerSubscription.closed) {\n            this.closeBuffer(context);\n        }\n        else {\n            innerSubscription.context = context;\n            this.add(innerSubscription);\n            subscription.add(innerSubscription);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAT,QAA6B,iBAA7B;AACA,SAASC,iBAAT,QAAkC,2BAAlC;AACA,SAASC,eAAT,QAAgC,oBAAhC;AACA,OAAO,SAASC,YAAT,CAAsBC,QAAtB,EAAgCC,eAAhC,EAAiD;EACpD,OAAO,SAASC,4BAAT,CAAsCC,MAAtC,EAA8C;IACjD,OAAOA,MAAM,CAACC,IAAP,CAAY,IAAIC,oBAAJ,CAAyBL,QAAzB,EAAmCC,eAAnC,CAAZ,CAAP;EACH,CAFD;AAGH;;AACD,MAAMI,oBAAN,CAA2B;EACvBC,WAAW,CAACN,QAAD,EAAWC,eAAX,EAA4B;IACnC,KAAKD,QAAL,GAAgBA,QAAhB;IACA,KAAKC,eAAL,GAAuBA,eAAvB;EACH;;EACDM,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,sBAAJ,CAA2BF,UAA3B,EAAuC,KAAKR,QAA5C,EAAsD,KAAKC,eAA3D,CAAjB,CAAP;EACH;;AAPsB;;AAS3B,MAAMS,sBAAN,SAAqCZ,eAArC,CAAqD;EACjDQ,WAAW,CAACK,WAAD,EAAcX,QAAd,EAAwBC,eAAxB,EAAyC;IAChD,MAAMU,WAAN;IACA,KAAKV,eAAL,GAAuBA,eAAvB;IACA,KAAKW,QAAL,GAAgB,EAAhB;IACA,KAAKC,GAAL,CAAShB,iBAAiB,CAAC,IAAD,EAAOG,QAAP,CAA1B;EACH;;EACDc,KAAK,CAACC,KAAD,EAAQ;IACT,MAAMH,QAAQ,GAAG,KAAKA,QAAtB;IACA,MAAMI,GAAG,GAAGJ,QAAQ,CAACK,MAArB;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,GAApB,EAAyBE,CAAC,EAA1B,EAA8B;MAC1BN,QAAQ,CAACM,CAAD,CAAR,CAAYC,MAAZ,CAAmBC,IAAnB,CAAwBL,KAAxB;IACH;EACJ;;EACDM,MAAM,CAACC,GAAD,EAAM;IACR,MAAMV,QAAQ,GAAG,KAAKA,QAAtB;;IACA,OAAOA,QAAQ,CAACK,MAAT,GAAkB,CAAzB,EAA4B;MACxB,MAAMM,OAAO,GAAGX,QAAQ,CAACY,KAAT,EAAhB;MACAD,OAAO,CAACE,YAAR,CAAqBC,WAArB;MACAH,OAAO,CAACJ,MAAR,GAAiB,IAAjB;MACAI,OAAO,CAACE,YAAR,GAAuB,IAAvB;IACH;;IACD,KAAKb,QAAL,GAAgB,IAAhB;;IACA,MAAMS,MAAN,CAAaC,GAAb;EACH;;EACDK,SAAS,GAAG;IACR,MAAMf,QAAQ,GAAG,KAAKA,QAAtB;;IACA,OAAOA,QAAQ,CAACK,MAAT,GAAkB,CAAzB,EAA4B;MACxB,MAAMM,OAAO,GAAGX,QAAQ,CAACY,KAAT,EAAhB;MACA,KAAKb,WAAL,CAAiBiB,IAAjB,CAAsBL,OAAO,CAACJ,MAA9B;MACAI,OAAO,CAACE,YAAR,CAAqBC,WAArB;MACAH,OAAO,CAACJ,MAAR,GAAiB,IAAjB;MACAI,OAAO,CAACE,YAAR,GAAuB,IAAvB;IACH;;IACD,KAAKb,QAAL,GAAgB,IAAhB;;IACA,MAAMe,SAAN;EACH;;EACDE,UAAU,CAACC,UAAD,EAAaC,UAAb,EAAyB;IAC/BD,UAAU,GAAG,KAAKE,WAAL,CAAiBF,UAAjB,CAAH,GAAkC,KAAKG,UAAL,CAAgBF,UAAhB,CAA5C;EACH;;EACDG,cAAc,CAACC,QAAD,EAAW;IACrB,KAAKH,WAAL,CAAiBG,QAAQ,CAACZ,OAA1B;EACH;;EACDU,UAAU,CAAClB,KAAD,EAAQ;IACd,IAAI;MACA,MAAMd,eAAe,GAAG,KAAKA,eAA7B;MACA,MAAMmC,eAAe,GAAGnC,eAAe,CAACM,IAAhB,CAAqB,IAArB,EAA2BQ,KAA3B,CAAxB;;MACA,IAAIqB,eAAJ,EAAqB;QACjB,KAAKC,YAAL,CAAkBD,eAAlB;MACH;IACJ,CAND,CAOA,OAAOd,GAAP,EAAY;MACR,KAAKD,MAAL,CAAYC,GAAZ;IACH;EACJ;;EACDU,WAAW,CAACT,OAAD,EAAU;IACjB,MAAMX,QAAQ,GAAG,KAAKA,QAAtB;;IACA,IAAIA,QAAQ,IAAIW,OAAhB,EAAyB;MACrB,MAAM;QAAEJ,MAAF;QAAUM;MAAV,IAA2BF,OAAjC;MACA,KAAKZ,WAAL,CAAiBiB,IAAjB,CAAsBT,MAAtB;MACAP,QAAQ,CAAC0B,MAAT,CAAgB1B,QAAQ,CAAC2B,OAAT,CAAiBhB,OAAjB,CAAhB,EAA2C,CAA3C;MACA,KAAKiB,MAAL,CAAYf,YAAZ;MACAA,YAAY,CAACC,WAAb;IACH;EACJ;;EACDW,YAAY,CAACD,eAAD,EAAkB;IAC1B,MAAMxB,QAAQ,GAAG,KAAKA,QAAtB;IACA,MAAMO,MAAM,GAAG,EAAf;IACA,MAAMM,YAAY,GAAG,IAAI7B,YAAJ,EAArB;IACA,MAAM2B,OAAO,GAAG;MAAEJ,MAAF;MAAUM;IAAV,CAAhB;IACAb,QAAQ,CAACQ,IAAT,CAAcG,OAAd;IACA,MAAMkB,iBAAiB,GAAG5C,iBAAiB,CAAC,IAAD,EAAOuC,eAAP,EAAwBb,OAAxB,CAA3C;;IACA,IAAI,CAACkB,iBAAD,IAAsBA,iBAAiB,CAACC,MAA5C,EAAoD;MAChD,KAAKV,WAAL,CAAiBT,OAAjB;IACH,CAFD,MAGK;MACDkB,iBAAiB,CAAClB,OAAlB,GAA4BA,OAA5B;MACA,KAAKV,GAAL,CAAS4B,iBAAT;MACAhB,YAAY,CAACZ,GAAb,CAAiB4B,iBAAjB;IACH;EACJ;;AAhFgD"}, "metadata": {}, "sourceType": "module"}