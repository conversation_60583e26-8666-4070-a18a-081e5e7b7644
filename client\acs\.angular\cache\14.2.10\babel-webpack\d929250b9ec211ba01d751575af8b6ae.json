{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./form-llc.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./form-llc.component.css?ngResource\";\nimport { ChangeDetectorRef, Component, ViewChild } from '@angular/core';\nimport { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';\nimport { StateSelectorComponent } from 'src/app/Modules/Shared/Components/formComponents/stateSelector/state-selector.component';\nimport { FilingApiService } from '../../Services/FilingApiService';\nimport { FormLLCControls } from 'src/app/Modules/Shared/Enums/form-llc-controls.enum';\nimport { FormALLC } from '../../Models/FormALLC';\nimport { CategoryCode } from 'src/app/Modules/Shared/Enums/category-code.enum';\nimport { ActivatedRoute } from '@angular/router';\nimport { LoadFilingService } from '../../Functions/filing-service-load.func';\nimport { FilingServiceResponseObject } from '../../Enums/filing-service-enum';\nimport { FormModeService } from 'src/app/Modules/Shared/Services/Common/FormModeService';\nimport { FilingModeComponent } from 'src/app/Modules/Shared/Components/formComponents/filing-mode/filing-mode.component';\nimport { ResetExcept } from 'src/app/Modules/Shared/functions/form-reset';\nimport { SubCategoryCode } from 'src/app/Modules/Shared/Enums/sub-category-code.enum';\nimport { CustomSharedValidations } from 'src/app/Modules/Shared/functions/custom-validations';\nimport { EmployerIdentificationNumberComponent } from '../employer-identification-number/employer-identification-number.component';\nimport { StatementOfInfromationComponent } from '../statement-of-information/statement-of-information.component';\nimport { ProductDetailComponent } from 'src/app/Modules/KitsMinutesBook/Components/product-detail/product-detail.component';\nimport { FilingInfoService } from '../../Services/filing-price.service';\nimport { StatePriceRequest } from 'src/app/Modules/Shared/Models/Common/state-price-request.model';\nlet FormLLCComponent = class FormLLCComponent {\n  constructor(cdr, filingInfoService, Api, formMode, activatedRoute) {\n    this.cdr = cdr;\n    this.filingInfoService = filingInfoService;\n    this.Api = Api;\n    this.formMode = formMode;\n    this.activatedRoute = activatedRoute;\n    this.isAdd = true;\n    this.ManagerNames = new FormArray([]);\n    this.FormALLC = new FormALLC();\n    this._IsEINSelected = false;\n    this._IsSOISelected = false;\n    this._IsCKSelected = false;\n    this.EmployerIdentificationNo = null;\n    this.AnnualReport = null;\n    this.KitOrderVM = null;\n    this.ClassUpSelling = \"col-md-12\";\n    this.Form = new FormGroup({\n      FormationState: new FormControl(null),\n      SelectedFilingOptionType: new FormControl(null),\n      FilingServiceType: new FormControl(null),\n      ProposedEntityName: new FormControl(null),\n      AlternateName: new FormControl(null),\n      ReservedEntityName: new FormControl(null, CustomSharedValidations.alphaNumeric),\n      ReservationNo: new FormControl(null),\n      PersonName: new FormControl(null, CustomSharedValidations.alphaNumeric),\n      ExpirationDate: new FormControl(null),\n      AcsPerformNameCheck: new FormControl(null),\n      Management: new FormControl(null, CustomSharedValidations.addressField),\n      ManagerNames: this.ManagerNames,\n      AddAnotherMember: new FormControl(null),\n      BusinessAddress: new FormControl(null, CustomSharedValidations.addressField),\n      City: new FormControl(null, CustomSharedValidations.alphaNumeric),\n      State: new FormControl(null),\n      Zip: new FormControl(null, [CustomSharedValidations.zipLength]),\n      MailingAddressSameAsBusinessAddress: new FormControl(null),\n      MailingAddress: new FormControl(null, CustomSharedValidations.addressField),\n      MailingCity: new FormControl(null, CustomSharedValidations.alphaNumeric),\n      MailingState: new FormControl(null),\n      MailingZip: new FormControl(null, [CustomSharedValidations.zipLength]),\n      AcsProvideRegisteredAgentService: new FormControl(null),\n      RegisteredAgentName: new FormControl(null, CustomSharedValidations.alphaNumeric),\n      RegisteredAgentAddress: new FormControl(null, CustomSharedValidations.addressField),\n      RegisteredAgentCity: new FormControl(null, CustomSharedValidations.alphaNumeric),\n      RegisteredAgentState: new FormControl(null),\n      RegisteredAgentZip: new FormControl(null, [CustomSharedValidations.zipLength]),\n      MailForwardingName: new FormControl(null, CustomSharedValidations.alphaNumeric),\n      MailForwardingStreet: new FormControl(null, CustomSharedValidations.addressField),\n      MailForwardingPhone: new FormControl(null, CustomSharedValidations.phoneLength),\n      MailForwardingEmail: new FormControl(null, Validators.email),\n      MailForwardingCity: new FormControl(null, CustomSharedValidations.alphaNumeric),\n      MailForwardingState: new FormControl(null),\n      MailForwardingZip: new FormControl(null, [CustomSharedValidations.zipLength]),\n      SpecialInstructions: new FormControl(null, [CustomSharedValidations.specialInstructions]),\n      RAThirdParty: new FormControl(null),\n      CARAName: new FormControl(null)\n    });\n  }\n\n  set _UpSelling_EIN(UpSelling_EIN) {\n    this.UpSelling_EIN = UpSelling_EIN;\n\n    if (!this.UpSelling_EIN || this.UpSelling_EIN.Form.controls.FormationState.value) {\n      return;\n    }\n\n    this.UpSelling_EIN.IsUpSelling = true;\n    this.UpSelling_EIN.Form.controls.FormationState.setValue(this.Form.controls.FormationState.value);\n  }\n\n  set _UpSelling_SOI(UpSelling_SOI) {\n    this.UpSelling_SOI = UpSelling_SOI;\n\n    if (!this.UpSelling_SOI || this.UpSelling_SOI.Form.controls.FormationState.value || this.UpSelling_SOI.Form.controls.FormationType.value) {\n      return;\n    }\n\n    this.UpSelling_SOI.IsUpSelling = true;\n    this.UpSelling_SOI.Form.controls.FormationState.setValue(this.Form.controls.FormationState.value);\n    this.UpSelling_SOI.Form.controls.FormationType.setValue('LC');\n    this.UpSelling_SOI.Form.controls.FiledBy.setValue(2);\n    this.UpSelling_SOI.Form.controls.EntityName.setValue(this.Form.controls.ProposedEntityName.value);\n\n    if (this.Form.controls.AcsProvideRegisteredAgentService.value == 'YES') {\n      this.UpSelling_SOI.AddressToggle = true;\n    } else if (this.Form.controls.AcsProvideRegisteredAgentService.value == 'NO') {\n      if (this.Form.controls.RAThirdParty.value == 'YES') {\n        this.UpSelling_SOI.AddressToggle = false;\n      } else if (this.Form.controls.RAThirdParty.value == 'NO') {\n        this.UpSelling_SOI.AddressToggle = true;\n      }\n    }\n\n    this.cdr.detectChanges();\n  }\n\n  set _UpSelling_CK(UpSelling_CK) {\n    this.UpSelling_CK = UpSelling_CK;\n\n    if (!this.UpSelling_CK || this.UpSelling_CK.Form1.controls.FilingState.value) {\n      return;\n    }\n\n    this.UpSelling_CK._IsUpselling = true;\n    this.UpSelling_CK.UpsellingProductCode = 'LK';\n    this.UpSelling_CK.Form1.controls.FilingState.setValue(this.Form.controls.FormationState.value);\n    this.UpSelling_CK.Form1.controls.KitType.setValue('LK');\n    this.cdr.detectChanges();\n  } //=====================\n\n\n  ngOnInit() {\n    this.filingInfoService.getPrice(this.filingInfoService.SubCatCode.formllc).subscribe(res => {\n      this.price = res;\n      this.basePrice = res;\n    });\n    this.upSellingValuesUpdate();\n    this.Api.FCGetStates().subscribe(States => {\n      this.StateComponent.States = States;\n    });\n    this.Api.GetFormLLCData().subscribe(data => {\n      this.filingServiceType = this.MapDynamicDataByControlId(data, FormLLCControls.FilingService);\n      this.addMemberOptions = this.MapDynamicDataByControlId(data, FormLLCControls.AddMemberManager);\n      this.reservationWithStateOptions = this.MapDynamicDataByControlId(data, FormLLCControls.ReservationWithState);\n      this.entityNameReservedOptions = this.MapDynamicDataByControlId(data, FormLLCControls.ReservedEntityName);\n      this.mailingAddressSameOptions = this.MapDynamicDataByControlId(data, FormLLCControls.MailingAddress);\n      this.managementData = this.MapDynamicDataByControlId(data, FormLLCControls.Management);\n      this.acsProvideRegisteredAgentServiceOptions = this.MapDynamicDataByControlId(data, FormLLCControls.AgentService);\n      ;\n      this.StartLoad();\n      if (this.isAdd) this.AddManagers('YES');\n    });\n    this.StateComponent.$OnStateSelection.subscribe(selectedState => {\n      this.FilingModeComponent.reset();\n\n      if (selectedState) {\n        this.getStatePrice(selectedState);\n        this.selectedState = selectedState;\n        this.FilingModeComponent.downloadPDFForm(this.selectedState, CategoryCode.formllc, SubCategoryCode.formllc);\n        this.FilingModeComponent.SelectedFilingType = null;\n        this.FilingModeComponent.Price = this.price;\n\n        if (selectedState === 'CA') {\n          this.FilingModeComponent.SelectedFilingType = this.FilingModeComponent.FilingTypes.OnlineMode;\n          this.FilingModeComponent.LineNo = 3;\n          this.FilingModeComponent.TabLineNo = 2;\n        } else {\n          this.FilingModeComponent.SelectedFilingType = this.FilingModeComponent.FilingTypes.OfflineMode;\n          this.FilingModeComponent.LineNo = 2;\n        }\n      }\n\n      ResetExcept(this.Form, [\"FormationState\"]);\n    });\n    this.Form.controls.SelectedFilingOptionType.valueChanges.subscribe(value => {\n      if (this.selectedState == 'CA') {\n        this.Form.controls.State.setValue('CA');\n      }\n    });\n    this.Form.controls.RAThirdParty.valueChanges.subscribe(value => {\n      if (this.Form.controls.FormationState.value == 'CA') {\n        this.Form.controls.RegisteredAgentState.setValue('CA');\n      }\n    });\n    this.FilingModeComponent.DownloadStatement = `Please download the service form, complete and email (<a class='email' href=\"mailto:<EMAIL>\"><EMAIL></a>) to us along with Articles of Organization.`;\n  }\n\n  AddManagers(value, controlValue) {\n    if (value !== 'YES') return;\n    let managerNameForm = new FormGroup({\n      ManagerName: new FormControl(controlValue || '', [Validators.required, CustomSharedValidations.alphaNumeric])\n    });\n    this.ManagerNames.push(managerNameForm);\n    this.Form.controls.AddAnotherMember.reset();\n  }\n\n  OnSave() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this.UpSellingData();\n\n        if (_this.Form.valid) {\n          _this.Save();\n        }\n      } catch (ex) {\n        console.error(ex);\n      }\n    })();\n  }\n\n  RemoveValidationFromDynamicControls(controls) {\n    controls.forEach(control => {\n      control.clearValidators();\n      control.updateValueAndValidity();\n    });\n  }\n\n  MapDynamicDataByControlId(data, controlId) {\n    let mappedData = data.filter(obj => obj.controlID === controlId).map(item => {\n      var data = {\n        id: item.controlValue,\n        value: item.controlText\n      };\n      return data;\n    });\n    return mappedData;\n  }\n\n  Save() {\n    this.FormALLC.FormationState = this.Form.controls.FormationState.value;\n    this.FormALLC.FilingServiceType = this.Form.controls.FilingServiceType.value;\n    this.FormALLC.ProposedEntityName = this.Form.controls.ProposedEntityName.value;\n    this.FormALLC.AlternateName = this.Form.controls.AlternateName.value;\n    this.FormALLC.ReservedEntityName = this.Form.controls.ReservedEntityName.value;\n    this.FormALLC.PersonName = this.Form.controls.PersonName.value;\n    this.FormALLC.ReservationNo = this.Form.controls.ReservationNo.value;\n    this.FormALLC.ExpirationDate = this.Form.controls.ExpirationDate.value;\n    this.FormALLC.AcsPerformNameCheck = this.Form.controls.AcsPerformNameCheck.value;\n    this.FormALLC.Management = this.Form.controls.Management.value;\n    this.FormALLC.ManagerName = this.Form.controls.ManagerNames.value.map(x => x.ManagerName);\n    this.FormALLC.AddAnotherMember = this.Form.controls.AddAnotherMember.value;\n    this.FormALLC.BusinessAddress = this.Form.controls.BusinessAddress.value;\n    this.FormALLC.City = this.Form.controls.City.value;\n    this.FormALLC.State = this.Form.controls.State.value;\n    this.FormALLC.Zip = this.Form.controls.Zip.value;\n    this.FormALLC.MailingAddressSameAsBusinessAddress = this.Form.controls.MailingAddressSameAsBusinessAddress.value;\n    this.FormALLC.MailingAddress = this.Form.controls.MailingAddress.value;\n    this.FormALLC.MailingCity = this.Form.controls.MailingCity.value;\n    this.FormALLC.MailingState = this.Form.controls.MailingState.value;\n    this.FormALLC.MailingZip = this.Form.controls.MailingZip.value;\n    this.FormALLC.AcsProvideRegisteredAgentService = this.Form.controls.AcsProvideRegisteredAgentService.value;\n    this.FormALLC.RegisteredAgentName = this.Form.controls.RegisteredAgentName.value;\n    this.FormALLC.RegisteredAgentAddress = this.Form.controls.RegisteredAgentAddress.value;\n    this.FormALLC.RegisteredAgentCity = this.Form.controls.RegisteredAgentCity.value;\n    this.FormALLC.RegisteredAgentState = this.Form.controls.RegisteredAgentState.value;\n    this.FormALLC.RegisteredAgentZip = this.Form.controls.RegisteredAgentZip.value;\n    this.FormALLC.MailForwardingName = this.Form.controls.MailForwardingName.value;\n    this.FormALLC.MailForwardingStreet = this.Form.controls.MailForwardingStreet.value;\n    this.FormALLC.MailForwardingPhone = this.Form.controls.MailForwardingPhone.value;\n    this.FormALLC.MailForwardingEmail = this.Form.controls.MailForwardingEmail.value;\n    this.FormALLC.MailForwardingCity = this.Form.controls.MailForwardingCity.value;\n    this.FormALLC.MailForwardingState = this.Form.controls.MailForwardingState.value;\n    this.FormALLC.MailForwardingZip = this.Form.controls.MailForwardingZip.value;\n    this.FormALLC.Remarks = this.Form.controls.SpecialInstructions.value;\n    this.FormALLC.RaThirdParty = this.Form.controls.RAThirdParty.value;\n    this.FormALLC.CaraName = this.Form.controls.CARAName.value;\n    let filingService = {\n      formLLC: this.FormALLC,\n      upSelling: {\n        annualReport: this.AnnualReport,\n        employerIdentificationNumber: this.EmployerIdentificationNo,\n        corporateKit: this.KitOrderVM\n      }\n    };\n    this.Api.SaveFilingService(filingService).subscribe(x => {\n      this.Form.reset();\n    });\n  }\n\n  StartLoad() {\n    var _this2 = this;\n\n    this.activatedRoute.queryParams.subscribe( /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (queryString) {\n        if (queryString.on) {\n          _this2.isAdd = false;\n        }\n\n        LoadFilingService(queryString, _this2.Api, FilingServiceResponseObject.FormLLC, _this2.formMode).then(serviceData => {\n          _this2.FormALLC = serviceData;\n\n          _this2.Load();\n        }).catch(e => console.log(e));\n      });\n\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  }\n\n  RemoveMembers(index) {\n    if (this.ManagerNames.length > 1) this.ManagerNames.removeAt(index);\n  }\n\n  Load() {\n    this.getStatePrice(this.FormALLC.FormationState);\n    this.Form.controls.FormationState.setValue(this.FormALLC.FormationState);\n    this.FilingModeComponent.SelectedOnlineMethodChange(2);\n    this.Form.controls.SelectedFilingOptionType.setValue(2);\n    this.Form.controls.FilingServiceType.setValue(this.FormALLC.FilingServiceType);\n    this.Form.controls.ProposedEntityName.setValue(this.FormALLC.ProposedEntityName);\n    this.Form.controls.AlternateName.setValue(this.FormALLC.AlternateName);\n    this.Form.controls.ReservedEntityName.setValue(this.FormALLC.ReservedEntityName);\n    this.Form.controls.PersonName.setValue(this.FormALLC.PersonName);\n    this.Form.controls.ReservationNo.setValue(this.FormALLC.ReservationNo);\n    this.Form.controls.ExpirationDate.setValue(this.FormALLC.ExpirationDate);\n    this.Form.controls.AcsPerformNameCheck.setValue(this.FormALLC.AcsPerformNameCheck);\n    this.Form.controls.Management.setValue(this.FormALLC.Management); //this.Form.controls.ManagerName.setValue(this.FormALLC.ManagerName)\n\n    this.FormALLC.ManagerName.forEach(e => this.AddManagers(\"YES\", e));\n    this.Form.controls.AddAnotherMember.setValue('NO');\n    this.Form.controls.BusinessAddress.setValue(this.FormALLC.BusinessAddress);\n    this.Form.controls.City.setValue(this.FormALLC.City);\n    this.Form.controls.State.setValue(this.FormALLC.State);\n    this.Form.controls.Zip.setValue(this.FormALLC.Zip);\n    this.Form.controls.MailingAddressSameAsBusinessAddress.setValue(this.FormALLC.MailingAddressSameAsBusinessAddress);\n    this.Form.controls.MailingAddress.setValue(this.FormALLC.MailingAddress);\n    this.Form.controls.MailingCity.setValue(this.FormALLC.MailingCity);\n    this.Form.controls.MailingState.setValue(this.FormALLC.MailingState);\n    this.Form.controls.MailingZip.setValue(this.FormALLC.MailingZip);\n    this.Form.controls.AcsProvideRegisteredAgentService.setValue(this.FormALLC.AcsProvideRegisteredAgentService);\n    this.Form.controls.RegisteredAgentName.setValue(this.FormALLC.RegisteredAgentName);\n    this.Form.controls.RegisteredAgentAddress.setValue(this.FormALLC.RegisteredAgentAddress);\n    this.Form.controls.RegisteredAgentCity.setValue(this.FormALLC.RegisteredAgentCity);\n    this.Form.controls.RegisteredAgentState.setValue(this.FormALLC.RegisteredAgentState);\n    this.Form.controls.RegisteredAgentZip.setValue(this.FormALLC.RegisteredAgentZip);\n    this.Form.controls.MailForwardingName.setValue(this.FormALLC.MailForwardingName);\n    this.Form.controls.MailForwardingStreet.setValue(this.FormALLC.MailForwardingStreet);\n    this.Form.controls.MailForwardingPhone.setValue(this.FormALLC.MailForwardingPhone);\n    this.Form.controls.MailForwardingEmail.setValue(this.FormALLC.MailForwardingEmail);\n    this.Form.controls.MailForwardingCity.setValue(this.FormALLC.MailForwardingCity);\n    this.Form.controls.MailForwardingState.setValue(this.FormALLC.MailForwardingState);\n    this.Form.controls.MailForwardingZip.setValue(this.FormALLC.MailForwardingZip);\n    this.Form.controls.SpecialInstructions.setValue(this.FormALLC.Remarks);\n    this.Form.controls.RAThirdParty.setValue(this.FormALLC.RaThirdParty);\n    this.Form.controls.CARAName.setValue(this.FormALLC.CaraName);\n  }\n\n  onUpSellingSelection() {\n    let selectedServices = 0;\n    if (this._IsSOISelected) selectedServices++;\n    if (this._IsEINSelected) selectedServices++;\n    if (this._IsCKSelected) selectedServices++;\n    selectedServices = selectedServices == 0 ? 1 : selectedServices;\n    this.ClassUpSelling = \"col-md-\" + 12 / selectedServices;\n  }\n\n  UpSellingData() {\n    let upSellingData = [];\n    let resolver;\n    let respo = new Promise(res => resolver = res);\n    let services = [];\n\n    if (this.UpSelling_EIN) {\n      this.UpSelling_EIN.Form.controls.LegalName.setValue(this.Form.controls.ProposedEntityName.value);\n      upSellingData.push(this.UpSelling_EIN.getUpSellingData());\n      services.push(\"EIN\");\n    }\n\n    if (this.UpSelling_SOI) {\n      upSellingData.push(this.UpSelling_SOI.getUpSellingData());\n      this.UpSelling_SOI.Form.controls.StateFileNo.setValue('');\n      this.UpSelling_SOI.Form.controls.CaliforniaMaintanenceOffice.setValue('');\n      this.UpSelling_SOI.Form.controls.PrincipalExecutiveOffice.setValue(this.Form.controls.BusinessAddress.value);\n      this.UpSelling_SOI.Form.controls.PrincipalExecutiveOfficeCity.setValue(this.Form.controls.City.value);\n      this.UpSelling_SOI.Form.controls.PrincipalExecutiveOfficeState.setValue(this.Form.controls.State.value);\n      this.UpSelling_SOI.Form.controls.PrincipalExecutiveOfficeZip.setValue(this.Form.controls.Zip.value);\n      this.UpSelling_SOI.Form.controls.PrincipalCaliforniaOfficeDifferent.setValue('');\n      this.UpSelling_SOI.Form.controls.MailingAddressDifferent.setValue('');\n\n      if (this.Form.controls.AcsProvideRegisteredAgentService.value == 'YES') {\n        this.UpSelling_SOI.Form.controls.ServiceAgentName.setValue(\"Attorneys Corporation Services\");\n        this.UpSelling_SOI.Form.controls.AgentAddress.setValue(this.Form.controls.MailForwardingStreet.value);\n      } else if (this.Form.controls.AcsProvideRegisteredAgentService.value == 'NO') {\n        if (this.Form.controls.RAThirdParty.value == 'YES') {\n          this.UpSelling_SOI.Form.controls.ServiceAgentName.setValue(this.Form.controls.CARAName.value);\n        } else if (this.Form.controls.RAThirdParty.value == 'NO') {\n          this.UpSelling_SOI.Form.controls.ServiceAgentName.setValue(this.Form.controls.RegisteredAgentName.value);\n          this.UpSelling_SOI.Form.controls.AgentAddress.setValue(this.Form.controls.RegisteredAgentAddress.value);\n        }\n      }\n\n      services.push(\"SOI\");\n    }\n\n    if (this.UpSelling_CK) {\n      upSellingData.push(this.UpSelling_CK.getUpSellingData());\n      services.push(\"CK\");\n    }\n\n    Promise.all(upSellingData).then(response => {\n      if (upSellingData.length <= 0) {\n        resolver();\n        return;\n      }\n\n      let index_EIN = this.getIndexNo(\"EIN\", services);\n      let index_SOI = this.getIndexNo(\"SOI\", services);\n      let index_CK = this.getIndexNo(\"CK\", services);\n      if (index_EIN != null) this.EmployerIdentificationNo = response[index_EIN];\n\n      if (index_SOI != null) {\n        this.AnnualReport = response[index_SOI];\n      }\n\n      if (index_CK != null) {\n        this.KitOrderVM = response[index_CK];\n      }\n\n      resolver();\n      return;\n    });\n    return respo;\n  }\n\n  getIndexNo(code, array) {\n    let val = array.findIndex(x => x == code);\n\n    if (val == -1) {\n      return null;\n    }\n\n    return val;\n  }\n\n  upSellingValuesUpdate() {\n    this.Form.valueChanges.subscribe(val => {\n      if (this._IsEINSelected) {\n        this.UpSelling_EIN.Form.controls.LegalName.setValue(this.Form.controls.ProposedEntityName.value);\n\n        if (this.Form.controls.ExpirationDate.value) {\n          let date = new Date(this.Form.controls.ExpirationDate.value);\n          this.UpSelling_EIN.Form.controls.file_date_y.setValue(date.getFullYear());\n          this.UpSelling_EIN.Form.controls.file_date_m.setValue(date.getMonth() + 1);\n          this.UpSelling_EIN.Form.controls.file_date_d.setValue(date.getDate());\n        } else {\n          this.UpSelling_EIN.Form.controls.file_date_y.setValue(null);\n          this.UpSelling_EIN.Form.controls.file_date_d.setValue(null);\n          this.UpSelling_EIN.Form.controls.file_date_m.setValue(null);\n        }\n      }\n    });\n  }\n\n  getStatePrice(state) {\n    var request = new StatePriceRequest();\n    request.ProductCode = 'FS';\n    request.CategoryCode = '003';\n    request.SubCategoryCode = '070';\n    request.State = state;\n    this.Api.GetStateWisePrice(request).subscribe(res => {\n      this.price = res.price > 0 ? res.price : this.basePrice;\n      this.FilingModeComponent.Price = this.price;\n    });\n  }\n\n  callSubmit() {\n    document.getElementById('__main1').click();\n  }\n\n};\n\nFormLLCComponent.ctorParameters = () => [{\n  type: ChangeDetectorRef\n}, {\n  type: FilingInfoService\n}, {\n  type: FilingApiService\n}, {\n  type: FormModeService\n}, {\n  type: ActivatedRoute\n}];\n\nFormLLCComponent.propDecorators = {\n  StateComponent: [{\n    type: ViewChild,\n    args: [StateSelectorComponent, {\n      static: true\n    }]\n  }],\n  FilingModeComponent: [{\n    type: ViewChild,\n    args: [FilingModeComponent, {\n      static: true\n    }]\n  }],\n  _UpSelling_EIN: [{\n    type: ViewChild,\n    args: [EmployerIdentificationNumberComponent, {\n      static: false\n    }]\n  }],\n  _UpSelling_SOI: [{\n    type: ViewChild,\n    args: [StatementOfInfromationComponent, {\n      static: false\n    }]\n  }],\n  _UpSelling_CK: [{\n    type: ViewChild,\n    args: [ProductDetailComponent, {\n      static: false\n    }]\n  }]\n};\nFormLLCComponent = __decorate([Component({\n  selector: 'app-form-llc',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], FormLLCComponent);\nexport { FormLLCComponent };", "map": {"version": 3, "mappings": ";;;;AAAA,SAASA,iBAAT,EAA4BC,SAA5B,EAA+CC,SAA/C,QAAgE,eAAhE;AACA,SAASC,SAAT,EAAoBC,WAApB,EAAiCC,SAAjC,EAA4CC,UAA5C,QAA8D,gBAA9D;AACA,SAASC,sBAAT,QAAuC,yFAAvC;AACA,SAASC,gBAAT,QAAiC,iCAAjC;AACA,SAASC,eAAT,QAAgC,qDAAhC;AACA,SAASC,QAAT,QAAyB,uBAAzB;AAEA,SAASC,YAAT,QAA6B,iDAA7B;AAEA,SAASC,cAAT,QAA+B,iBAA/B;AACA,SAASC,iBAAT,QAAkC,0CAAlC;AACA,SAASC,2BAAT,QAA4C,iCAA5C;AACA,SAASC,eAAT,QAAgC,wDAAhC;AAEA,SAASC,mBAAT,QAAoC,oFAApC;AACA,SAASC,WAAT,QAA4B,6CAA5B;AACA,SAASC,eAAT,QAAgC,qDAAhC;AACA,SAASC,uBAAT,QAAwC,qDAAxC;AACA,SAASC,qCAAT,QAAsD,4EAAtD;AACA,SAASC,+BAAT,QAAgD,gEAAhD;AAGA,SAASC,sBAAT,QAAuC,oFAAvC;AACA,SAASC,iBAAT,QAAkC,qCAAlC;AAEA,SAASC,iBAAT,QAAkC,gEAAlC;IAOaC,gBAAgB,SAAhBA,gBAAgB;EAEzBC,YAAoBC,GAApB,EAAiDC,iBAAjD,EAA6FC,GAA7F,EAA0HC,QAA1H,EAA4JC,cAA5J,EAA0L;IAAtK;IAA6B;IAA4C;IAA6B;IAAkC;IAC5J,aAAQ,IAAR;IACA,oBAAe,IAAI5B,SAAJ,CAAc,EAAd,CAAf;IAcA,gBAAqB,IAAIO,QAAJ,EAArB;IAUF,sBAAiB,KAAjB;IACA,sBAAiB,KAAjB;IACA,qBAAgB,KAAhB;IAEA,gCAAyD,IAAzD;IACA,oBAAuC,IAAvC;IACA,kBAA4B,IAA5B;IAoDA,sBAAsB,WAAtB;IAoFE,YAAO,IAAIL,SAAJ,CAAc;MACjB2B,cAAc,EAAE,IAAI5B,WAAJ,CAA+B,IAA/B,CADC;MAEjB6B,wBAAwB,EAAE,IAAI7B,WAAJ,CAA4B,IAA5B,CAFT;MAGjB8B,iBAAiB,EAAE,IAAI9B,WAAJ,CAA+B,IAA/B,CAHF;MAIjB+B,kBAAkB,EAAE,IAAI/B,WAAJ,CAA+B,IAA/B,CAJH;MAKjBgC,aAAa,EAAE,IAAIhC,WAAJ,CAA+B,IAA/B,CALE;MAMjBiC,kBAAkB,EAAE,IAAIjC,WAAJ,CAA+B,IAA/B,EAAoCe,uBAAuB,CAACmB,YAA5D,CANH;MAOjBC,aAAa,EAAE,IAAInC,WAAJ,CAA+B,IAA/B,CAPE;MAQjBoC,UAAU,EAAE,IAAIpC,WAAJ,CAA+B,IAA/B,EAAoCe,uBAAuB,CAACmB,YAA5D,CARK;MASjBG,cAAc,EAAE,IAAIrC,WAAJ,CAA+B,IAA/B,CATC;MAUjBsC,mBAAmB,EAAE,IAAItC,WAAJ,CAA+B,IAA/B,CAVJ;MAWjBuC,UAAU,EAAE,IAAIvC,WAAJ,CAA+B,IAA/B,EAAoCe,uBAAuB,CAACyB,YAA5D,CAXK;MAYjBC,YAAY,EAAE,KAAKA,YAZF;MAajBC,gBAAgB,EAAE,IAAI1C,WAAJ,CAA+B,IAA/B,CAbD;MAcjB2C,eAAe,EAAE,IAAI3C,WAAJ,CAA+B,IAA/B,EAAoCe,uBAAuB,CAACyB,YAA5D,CAdA;MAejBI,IAAI,EAAE,IAAI5C,WAAJ,CAA+B,IAA/B,EAAoCe,uBAAuB,CAACmB,YAA5D,CAfW;MAgBjBW,KAAK,EAAE,IAAI7C,WAAJ,CAA+B,IAA/B,CAhBU;MAiBjB8C,GAAG,EAAE,IAAI9C,WAAJ,CAA+B,IAA/B,EAAoC,CAACe,uBAAuB,CAACgC,SAAzB,CAApC,CAjBY;MAkBjBC,mCAAmC,EAAE,IAAIhD,WAAJ,CAA+B,IAA/B,CAlBpB;MAmBjBiD,cAAc,EAAE,IAAIjD,WAAJ,CAA+B,IAA/B,EAAoCe,uBAAuB,CAACyB,YAA5D,CAnBC;MAoBjBU,WAAW,EAAE,IAAIlD,WAAJ,CAA+B,IAA/B,EAAoCe,uBAAuB,CAACmB,YAA5D,CApBI;MAqBjBiB,YAAY,EAAE,IAAInD,WAAJ,CAA+B,IAA/B,CArBG;MAsBjBoD,UAAU,EAAE,IAAIpD,WAAJ,CAA+B,IAA/B,EAAoC,CAACe,uBAAuB,CAACgC,SAAzB,CAApC,CAtBK;MAuBjBM,gCAAgC,EAAE,IAAIrD,WAAJ,CAA+B,IAA/B,CAvBjB;MAwBjBsD,mBAAmB,EAAE,IAAItD,WAAJ,CAA+B,IAA/B,EAAoCe,uBAAuB,CAACmB,YAA5D,CAxBJ;MAyBjBqB,sBAAsB,EAAE,IAAIvD,WAAJ,CAA+B,IAA/B,EAAoCe,uBAAuB,CAACyB,YAA5D,CAzBP;MA0BjBgB,mBAAmB,EAAE,IAAIxD,WAAJ,CAA+B,IAA/B,EAAoCe,uBAAuB,CAACmB,YAA5D,CA1BJ;MA2BjBuB,oBAAoB,EAAE,IAAIzD,WAAJ,CAA+B,IAA/B,CA3BL;MA4BjB0D,kBAAkB,EAAE,IAAI1D,WAAJ,CAA+B,IAA/B,EAAoC,CAACe,uBAAuB,CAACgC,SAAzB,CAApC,CA5BH;MA6BjBY,kBAAkB,EAAE,IAAI3D,WAAJ,CAA+B,IAA/B,EAAoCe,uBAAuB,CAACmB,YAA5D,CA7BH;MA8BjB0B,oBAAoB,EAAE,IAAI5D,WAAJ,CAA+B,IAA/B,EAAoCe,uBAAuB,CAACyB,YAA5D,CA9BL;MA+BjBqB,mBAAmB,EAAE,IAAI7D,WAAJ,CAA+B,IAA/B,EAAoCe,uBAAuB,CAAC+C,WAA5D,CA/BJ;MAgCjBC,mBAAmB,EAAE,IAAI/D,WAAJ,CAA+B,IAA/B,EAAoCE,UAAU,CAAC8D,KAA/C,CAhCJ;MAiCjBC,kBAAkB,EAAE,IAAIjE,WAAJ,CAA+B,IAA/B,EAAoCe,uBAAuB,CAACmB,YAA5D,CAjCH;MAkCjBgC,mBAAmB,EAAE,IAAIlE,WAAJ,CAA+B,IAA/B,CAlCJ;MAmCjBmE,iBAAiB,EAAE,IAAInE,WAAJ,CAA+B,IAA/B,EAAoC,CAACe,uBAAuB,CAACgC,SAAzB,CAApC,CAnCF;MAoCjBqB,mBAAmB,EAAE,IAAIpE,WAAJ,CAA+B,IAA/B,EAAoC,CAACe,uBAAuB,CAACsD,mBAAzB,CAApC,CApCJ;MAqCjBC,YAAY,EAAC,IAAItE,WAAJ,CAA+B,IAA/B,CArCI;MAsCjBuE,QAAQ,EAAC,IAAIvE,WAAJ,CAA+B,IAA/B;IAtCQ,CAAd,CAAP;EAxK+L;;EAkC1G,IAAdwE,cAAc,CAACC,aAAD,EAAqD;IAE1I,KAAKA,aAAL,GAAsBA,aAAtB;;IAEA,IAAG,CAAC,KAAKA,aAAN,IAAuB,KAAKA,aAAL,CAAmBC,IAAnB,CAAwBC,QAAxB,CAAiC/C,cAAjC,CAAgDgD,KAA1E,EAAgF;MAAC;IAAQ;;IAEzF,KAAKH,aAAL,CAAmBI,WAAnB,GAAiC,IAAjC;IACA,KAAKJ,aAAL,CAAmBC,IAAnB,CAAwBC,QAAxB,CAAiC/C,cAAjC,CAAgDkD,QAAhD,CAAyD,KAAKJ,IAAL,CAAUC,QAAV,CAAmB/C,cAAnB,CAAkCgD,KAA3F;EACD;;EAEgF,IAAdG,cAAc,CAACC,aAAD,EAA+C;IAC9H,KAAKA,aAAL,GAAsBA,aAAtB;;IACA,IAAG,CAAC,KAAKA,aAAN,IAAuB,KAAKA,aAAL,CAAmBN,IAAnB,CAAwBC,QAAxB,CAAiC/C,cAAjC,CAAgDgD,KAAvE,IAAiF,KAAKI,aAAL,CAAmBN,IAAnB,CAAwBC,QAAxB,CAAiCM,aAAjC,CAA+CL,KAAnI,EAAyI;MAAC;IAAQ;;IAClJ,KAAKI,aAAL,CAAmBH,WAAnB,GAAiC,IAAjC;IACA,KAAKG,aAAL,CAAmBN,IAAnB,CAAwBC,QAAxB,CAAiC/C,cAAjC,CAAgDkD,QAAhD,CAAyD,KAAKJ,IAAL,CAAUC,QAAV,CAAmB/C,cAAnB,CAAkCgD,KAA3F;IACA,KAAKI,aAAL,CAAmBN,IAAnB,CAAwBC,QAAxB,CAAiCM,aAAjC,CAA+CH,QAA/C,CAAwD,IAAxD;IACA,KAAKE,aAAL,CAAmBN,IAAnB,CAAwBC,QAAxB,CAAiCO,OAAjC,CAAyCJ,QAAzC,CAAkD,CAAlD;IACA,KAAKE,aAAL,CAAmBN,IAAnB,CAAwBC,QAAxB,CAAiCQ,UAAjC,CAA4CL,QAA5C,CAAqD,KAAKJ,IAAL,CAAUC,QAAV,CAAmB5C,kBAAnB,CAAsC6C,KAA3F;;IACA,IAAG,KAAKF,IAAL,CAAUC,QAAV,CAAmBtB,gCAAnB,CAAoDuB,KAApD,IAA6D,KAAhE,EAAsE;MACpE,KAAKI,aAAL,CAAmBI,aAAnB,GAAmC,IAAnC;IACD,CAFD,MAGK,IAAG,KAAKV,IAAL,CAAUC,QAAV,CAAmBtB,gCAAnB,CAAoDuB,KAApD,IAA6D,IAAhE,EAAqE;MACzE,IAAG,KAAKF,IAAL,CAAUC,QAAV,CAAmBL,YAAnB,CAAgCM,KAAhC,IAAyC,KAA5C,EACA;QACC,KAAKI,aAAL,CAAmBI,aAAnB,GAAmC,KAAnC;MACA,CAHD,MAIK,IAAI,KAAKV,IAAL,CAAUC,QAAV,CAAmBL,YAAnB,CAAgCM,KAAhC,IAAyC,IAA7C,EACL;QACC,KAAKI,aAAL,CAAmBI,aAAnB,GAAmC,IAAnC;MACA;IACD;;IACD,KAAK7D,GAAL,CAAS8D,aAAT;EACD;;EAGsE,IAAbC,aAAa,CAACC,YAAD,EAAqC;IAE1G,KAAKA,YAAL,GAAoBA,YAApB;;IAEA,IAAI,CAAC,KAAKA,YAAN,IAAsB,KAAKA,YAAL,CAAkBC,KAAlB,CAAwBb,QAAxB,CAAiCc,WAAjC,CAA6Cb,KAAvE,EAA8E;MAAE;IAAS;;IACzF,KAAKW,YAAL,CAAkBG,YAAlB,GAAiC,IAAjC;IACA,KAAKH,YAAL,CAAkBI,oBAAlB,GAAyC,IAAzC;IAEA,KAAKJ,YAAL,CAAkBC,KAAlB,CAAwBb,QAAxB,CAAiCc,WAAjC,CAA6CX,QAA7C,CAAsD,KAAKJ,IAAL,CAAUC,QAAV,CAAmB/C,cAAnB,CAAkCgD,KAAxF;IACA,KAAKW,YAAL,CAAkBC,KAAlB,CAAwBb,QAAxB,CAAiCiB,OAAjC,CAAyCd,QAAzC,CAAkD,IAAlD;IAEA,KAAKvD,GAAL,CAAS8D,aAAT;EAED,CApF0B,CA0F3B;;;EAEEQ,QAAQ;IACJ,KAAKrE,iBAAL,CAAuBsE,QAAvB,CAAgC,KAAKtE,iBAAL,CAAuBuE,UAAvB,CAAkCC,OAAlE,EAA2EC,SAA3E,CAAqFC,GAAG,IAAG;MACzF,KAAKC,KAAL,GAAaD,GAAb;MACA,KAAKE,SAAL,GAAiBF,GAAjB;IACD,CAHD;IAKA,KAAKG,qBAAL;IAEA,KAAK5E,GAAL,CAAS6E,WAAT,GAAuBL,SAAvB,CAAiCM,MAAM,IAAG;MACtC,KAAKC,cAAL,CAAoBD,MAApB,GAA6BA,MAA7B;IACH,CAFD;IAIA,KAAK9E,GAAL,CAASgF,cAAT,GAA0BR,SAA1B,CAAoCS,IAAI,IAAG;MACvC,KAAKC,iBAAL,GAAyB,KAAKC,yBAAL,CAA+BF,IAA/B,EAAqCrG,eAAe,CAACwG,aAArD,CAAzB;MACA,KAAKC,gBAAL,GAAwB,KAAKF,yBAAL,CAA+BF,IAA/B,EAAqCrG,eAAe,CAAC0G,gBAArD,CAAxB;MACA,KAAKC,2BAAL,GAAmC,KAAKJ,yBAAL,CAA+BF,IAA/B,EAAqCrG,eAAe,CAAC4G,oBAArD,CAAnC;MACA,KAAKC,yBAAL,GAAiC,KAAKN,yBAAL,CAA+BF,IAA/B,EAAqCrG,eAAe,CAAC4B,kBAArD,CAAjC;MACA,KAAKkF,yBAAL,GAAiC,KAAKP,yBAAL,CAA+BF,IAA/B,EAAqCrG,eAAe,CAAC4C,cAArD,CAAjC;MACA,KAAKmE,cAAL,GAAsB,KAAKR,yBAAL,CAA+BF,IAA/B,EAAqCrG,eAAe,CAACkC,UAArD,CAAtB;MACA,KAAK8E,uCAAL,GAA+C,KAAKT,yBAAL,CAA+BF,IAA/B,EAAqCrG,eAAe,CAACiH,YAArD,CAA/C;MAAkH;MAIlH,KAAKC,SAAL;MACA,IAAG,KAAKC,KAAR,EACI,KAAKC,WAAL,CAAiB,KAAjB;IAEP,CAfD;IAiBA,KAAKjB,cAAL,CAAoBkB,iBAApB,CAAsCzB,SAAtC,CAAgD0B,aAAa,IAAG;MAC5D,KAAK/G,mBAAL,CAAyBgH,KAAzB;;MACA,IAAID,aAAJ,EAAmB;QACf,KAAKE,aAAL,CAAmBF,aAAnB;QACA,KAAKA,aAAL,GAAqBA,aAArB;QAEA,KAAK/G,mBAAL,CAAyBkH,eAAzB,CAAyC,KAAKH,aAA9C,EAA4DpH,YAAY,CAACyF,OAAzE,EAAiFlF,eAAe,CAACkF,OAAjG;QACA,KAAKpF,mBAAL,CAAyBmH,kBAAzB,GAA8C,IAA9C;QACA,KAAKnH,mBAAL,CAAyBoH,KAAzB,GAAiC,KAAK7B,KAAtC;;QACA,IAAIwB,aAAa,KAAK,IAAtB,EAA6B;UACzB,KAAK/G,mBAAL,CAAyBmH,kBAAzB,GAA8C,KAAKnH,mBAAL,CAAyBqH,WAAzB,CAAqCC,UAAnF;UACA,KAAKtH,mBAAL,CAAyBuH,MAAzB,GAAkC,CAAlC;UACA,KAAKvH,mBAAL,CAAyBwH,SAAzB,GAAqC,CAArC;QACH,CAJD,MAKK;UACD,KAAKxH,mBAAL,CAAyBmH,kBAAzB,GAA8C,KAAKnH,mBAAL,CAAyBqH,WAAzB,CAAqCI,WAAnF;UACA,KAAKzH,mBAAL,CAAyBuH,MAAzB,GAAkC,CAAlC;QACH;MACJ;;MAEDtH,WAAW,CAAC,KAAK6D,IAAN,EAAY,CAAC,gBAAD,CAAZ,CAAX;IAIH,CAxBD;IA0BA,KAAKA,IAAL,CAAUC,QAAV,CAAmB9C,wBAAnB,CAA4CyG,YAA5C,CAAyDrC,SAAzD,CAAmErB,KAAK,IAAE;MAItE,IAAG,KAAK+C,aAAL,IAAsB,IAAzB,EAA8B;QAE1B,KAAKjD,IAAL,CAAUC,QAAV,CAAmB9B,KAAnB,CAAyBiC,QAAzB,CAAkC,IAAlC;MACH;IACJ,CARD;IAUA,KAAKJ,IAAL,CAAUC,QAAV,CAAmBL,YAAnB,CAAgCgE,YAAhC,CAA6CrC,SAA7C,CAAuDrB,KAAK,IAAE;MAE5D,IAAG,KAAKF,IAAL,CAAUC,QAAV,CAAmB/C,cAAnB,CAAkCgD,KAAlC,IAA0C,IAA7C,EAAkD;QAChD,KAAKF,IAAL,CAAUC,QAAV,CAAmBlB,oBAAnB,CAAwCqB,QAAxC,CAAiD,IAAjD;MACD;IACF,CALD;IAQA,KAAKlE,mBAAL,CAAyB2H,iBAAzB,GAA4C,wMAA5C;EAEH;;EA4CDd,WAAW,CAAC7C,KAAD,EAAQ4D,YAAR,EAA4B;IACnC,IAAI5D,KAAK,KAAK,KAAd,EAAqB;IAErB,IAAI6D,eAAe,GAAG,IAAIxI,SAAJ,CAAc;MAChCyI,WAAW,EAAE,IAAI1I,WAAJ,CAA+BwI,YAAY,IAAI,EAA/C,EAAmD,CAACtI,UAAU,CAACyI,QAAZ,EAAqB5H,uBAAuB,CAACmB,YAA7C,CAAnD;IADmB,CAAd,CAAtB;IAGA,KAAKO,YAAL,CAAkBmG,IAAlB,CAAuBH,eAAvB;IACA,KAAK/D,IAAL,CAAUC,QAAV,CAAmBjC,gBAAnB,CAAoCkF,KAApC;EACH;;EAGIiB,MAAM;IAAA;;IAAA;MACP,IAAG;QAEC,MAAM,KAAI,CAACC,aAAL,EAAN;;QAEA,IAAG,KAAI,CAACpE,IAAL,CAAUqE,KAAb,EAAmB;UAEf,KAAI,CAACC,IAAL;QACH;MACJ,CARD,CASA,OAAMC,EAAN,EAAS;QACLC,OAAO,CAACC,KAAR,CAAcF,EAAd;MACH;IAZM;EAaV;;EAEOG,mCAAmC,CAACzE,QAAD,EAAgB;IACvDA,QAAQ,CAAC0E,OAAT,CAAiBC,OAAO,IAAG;MACvBA,OAAO,CAACC,eAAR;MACAD,OAAO,CAACE,sBAAR;IACH,CAHD;EAIH;;EAEO5C,yBAAyB,CAACF,IAAD,EAAc+C,SAAd,EAA4B;IACzD,IAAIC,UAAU,GAAGhD,IAAI,CAACiD,MAAL,CAAYC,GAAG,IAAIA,GAAG,CAACC,SAAJ,KAAkBJ,SAArC,EAAgDK,GAAhD,CAAoDC,IAAI,IAAG;MACxE,IAAIrD,IAAI,GAAG;QAAEsD,EAAE,EAAED,IAAI,CAACvB,YAAX;QAAyB5D,KAAK,EAAEmF,IAAI,CAACE;MAArC,CAAX;MACA,OAAOvD,IAAP;IACH,CAHgB,CAAjB;IAIA,OAAOgD,UAAP;EACH;;EAEDV,IAAI;IACA,KAAK1I,QAAL,CAAcsB,cAAd,GAA+B,KAAK8C,IAAL,CAAUC,QAAV,CAAmB/C,cAAnB,CAAkCgD,KAAjE;IACA,KAAKtE,QAAL,CAAcwB,iBAAd,GAAkC,KAAK4C,IAAL,CAAUC,QAAV,CAAmB7C,iBAAnB,CAAqC8C,KAAvE;IACA,KAAKtE,QAAL,CAAcyB,kBAAd,GAAmC,KAAK2C,IAAL,CAAUC,QAAV,CAAmB5C,kBAAnB,CAAsC6C,KAAzE;IACA,KAAKtE,QAAL,CAAc0B,aAAd,GAA8B,KAAK0C,IAAL,CAAUC,QAAV,CAAmB3C,aAAnB,CAAiC4C,KAA/D;IACA,KAAKtE,QAAL,CAAc2B,kBAAd,GAAmC,KAAKyC,IAAL,CAAUC,QAAV,CAAmB1C,kBAAnB,CAAsC2C,KAAzE;IACA,KAAKtE,QAAL,CAAc8B,UAAd,GAA2B,KAAKsC,IAAL,CAAUC,QAAV,CAAmBvC,UAAnB,CAA8BwC,KAAzD;IACA,KAAKtE,QAAL,CAAc6B,aAAd,GAA8B,KAAKuC,IAAL,CAAUC,QAAV,CAAmBxC,aAAnB,CAAiCyC,KAA/D;IACA,KAAKtE,QAAL,CAAc+B,cAAd,GAA+B,KAAKqC,IAAL,CAAUC,QAAV,CAAmBtC,cAAnB,CAAkCuC,KAAjE;IACA,KAAKtE,QAAL,CAAcgC,mBAAd,GAAoC,KAAKoC,IAAL,CAAUC,QAAV,CAAmBrC,mBAAnB,CAAuCsC,KAA3E;IACA,KAAKtE,QAAL,CAAciC,UAAd,GAA2B,KAAKmC,IAAL,CAAUC,QAAV,CAAmBpC,UAAnB,CAA8BqC,KAAzD;IACA,KAAKtE,QAAL,CAAcoI,WAAd,GAA4B,KAAKhE,IAAL,CAAUC,QAAV,CAAmBlC,YAAnB,CAAgCmC,KAAhC,CAAsCkF,GAAtC,CAA0CI,CAAC,IAAEA,CAAC,CAACxB,WAA/C,CAA5B;IACA,KAAKpI,QAAL,CAAcoC,gBAAd,GAAiC,KAAKgC,IAAL,CAAUC,QAAV,CAAmBjC,gBAAnB,CAAoCkC,KAArE;IACA,KAAKtE,QAAL,CAAcqC,eAAd,GAAgC,KAAK+B,IAAL,CAAUC,QAAV,CAAmBhC,eAAnB,CAAmCiC,KAAnE;IACA,KAAKtE,QAAL,CAAcsC,IAAd,GAAqB,KAAK8B,IAAL,CAAUC,QAAV,CAAmB/B,IAAnB,CAAwBgC,KAA7C;IACA,KAAKtE,QAAL,CAAcuC,KAAd,GAAsB,KAAK6B,IAAL,CAAUC,QAAV,CAAmB9B,KAAnB,CAAyB+B,KAA/C;IACA,KAAKtE,QAAL,CAAcwC,GAAd,GAAoB,KAAK4B,IAAL,CAAUC,QAAV,CAAmB7B,GAAnB,CAAuB8B,KAA3C;IACA,KAAKtE,QAAL,CAAc0C,mCAAd,GAAoD,KAAK0B,IAAL,CAAUC,QAAV,CAAmB3B,mCAAnB,CAAuD4B,KAA3G;IACA,KAAKtE,QAAL,CAAc2C,cAAd,GAA+B,KAAKyB,IAAL,CAAUC,QAAV,CAAmB1B,cAAnB,CAAkC2B,KAAjE;IACA,KAAKtE,QAAL,CAAc4C,WAAd,GAA4B,KAAKwB,IAAL,CAAUC,QAAV,CAAmBzB,WAAnB,CAA+B0B,KAA3D;IACA,KAAKtE,QAAL,CAAc6C,YAAd,GAA6B,KAAKuB,IAAL,CAAUC,QAAV,CAAmBxB,YAAnB,CAAgCyB,KAA7D;IACA,KAAKtE,QAAL,CAAc8C,UAAd,GAA2B,KAAKsB,IAAL,CAAUC,QAAV,CAAmBvB,UAAnB,CAA8BwB,KAAzD;IACA,KAAKtE,QAAL,CAAc+C,gCAAd,GAAiD,KAAKqB,IAAL,CAAUC,QAAV,CAAmBtB,gCAAnB,CAAoDuB,KAArG;IACA,KAAKtE,QAAL,CAAcgD,mBAAd,GAAoC,KAAKoB,IAAL,CAAUC,QAAV,CAAmBrB,mBAAnB,CAAuCsB,KAA3E;IACA,KAAKtE,QAAL,CAAciD,sBAAd,GAAuC,KAAKmB,IAAL,CAAUC,QAAV,CAAmBpB,sBAAnB,CAA0CqB,KAAjF;IACA,KAAKtE,QAAL,CAAckD,mBAAd,GAAoC,KAAKkB,IAAL,CAAUC,QAAV,CAAmBnB,mBAAnB,CAAuCoB,KAA3E;IACA,KAAKtE,QAAL,CAAcmD,oBAAd,GAAqC,KAAKiB,IAAL,CAAUC,QAAV,CAAmBlB,oBAAnB,CAAwCmB,KAA7E;IACA,KAAKtE,QAAL,CAAcoD,kBAAd,GAAmC,KAAKgB,IAAL,CAAUC,QAAV,CAAmBjB,kBAAnB,CAAsCkB,KAAzE;IACA,KAAKtE,QAAL,CAAcqD,kBAAd,GAAmC,KAAKe,IAAL,CAAUC,QAAV,CAAmBhB,kBAAnB,CAAsCiB,KAAzE;IACA,KAAKtE,QAAL,CAAcsD,oBAAd,GAAqC,KAAKc,IAAL,CAAUC,QAAV,CAAmBf,oBAAnB,CAAwCgB,KAA7E;IACA,KAAKtE,QAAL,CAAcuD,mBAAd,GAAoC,KAAKa,IAAL,CAAUC,QAAV,CAAmBd,mBAAnB,CAAuCe,KAA3E;IACA,KAAKtE,QAAL,CAAcyD,mBAAd,GAAoC,KAAKW,IAAL,CAAUC,QAAV,CAAmBZ,mBAAnB,CAAuCa,KAA3E;IACA,KAAKtE,QAAL,CAAc2D,kBAAd,GAAmC,KAAKS,IAAL,CAAUC,QAAV,CAAmBV,kBAAnB,CAAsCW,KAAzE;IACA,KAAKtE,QAAL,CAAc4D,mBAAd,GAAoC,KAAKQ,IAAL,CAAUC,QAAV,CAAmBT,mBAAnB,CAAuCU,KAA3E;IACA,KAAKtE,QAAL,CAAc6D,iBAAd,GAAkC,KAAKO,IAAL,CAAUC,QAAV,CAAmBR,iBAAnB,CAAqCS,KAAvE;IACA,KAAKtE,QAAL,CAAc6J,OAAd,GAAwB,KAAKzF,IAAL,CAAUC,QAAV,CAAmBP,mBAAnB,CAAuCQ,KAA/D;IACA,KAAKtE,QAAL,CAAc8J,YAAd,GAA6B,KAAK1F,IAAL,CAAUC,QAAV,CAAmBL,YAAnB,CAAgCM,KAA7D;IACA,KAAKtE,QAAL,CAAc+J,QAAd,GAAyB,KAAK3F,IAAL,CAAUC,QAAV,CAAmBJ,QAAnB,CAA4BK,KAArD;IAEA,IAAI0F,aAAa,GAAG;MAChBC,OAAO,EAAC,KAAKjK,QADG;MAEhBkK,SAAS,EAAE;QACTC,YAAY,EAAE,KAAKC,YADV;QAETC,4BAA4B,EAAE,KAAKC,wBAF1B;QAGTC,YAAY,EAAE,KAAKC;MAHV;IAFK,CAApB;IASA,KAAKrJ,GAAL,CAASsJ,iBAAT,CAA2BT,aAA3B,EAA0CrE,SAA1C,CAAoDiE,CAAC,IAAE;MAEnD,KAAKxF,IAAL,CAAUkD,KAAV;IACH,CAHD;EAIH;;EAEDL,SAAS;IAAA;;IACL,KAAK5F,cAAL,CAAoBqJ,WAApB,CAAgC/E,SAAhC;MAAA,6BAA0C,WAAMgF,WAAN,EAAmB;QACzD,IAAGA,WAAW,CAACC,EAAf,EAAkB;UACd,MAAI,CAAC1D,KAAL,GAAa,KAAb;QACH;;QACD/G,iBAAiB,CAAWwK,WAAX,EAAuB,MAAI,CAACxJ,GAA5B,EAAgCf,2BAA2B,CAACyK,OAA5D,EAAoE,MAAI,CAACzJ,QAAzE,CAAjB,CAAoG0J,IAApG,CAAyGC,WAAW,IAAE;UAGlH,MAAI,CAAC/K,QAAL,GAAgB+K,WAAhB;;UACA,MAAI,CAACC,IAAL;QACH,CALD,EAKGC,KALH,CAKSC,CAAC,IAAEtC,OAAO,CAACuC,GAAR,CAAYD,CAAZ,CALZ;MAQH,CAZD;;MAAA;QAAA;MAAA;IAAA;EAaD;;EAEDE,aAAa,CAACC,KAAD,EAAM;IACf,IAAG,KAAKlJ,YAAL,CAAkBmJ,MAAlB,GAAyB,CAA5B,EACA,KAAKnJ,YAAL,CAAkBoJ,QAAlB,CAA2BF,KAA3B;EACH;;EAGHL,IAAI;IACA,KAAKzD,aAAL,CAAmB,KAAKvH,QAAL,CAAcsB,cAAjC;IACA,KAAK8C,IAAL,CAAUC,QAAV,CAAmB/C,cAAnB,CAAkCkD,QAAlC,CAA2C,KAAKxE,QAAL,CAAcsB,cAAzD;IACA,KAAKhB,mBAAL,CAAyBkL,0BAAzB,CAAoD,CAApD;IACA,KAAKpH,IAAL,CAAUC,QAAV,CAAmB9C,wBAAnB,CAA4CiD,QAA5C,CAAqD,CAArD;IACA,KAAKJ,IAAL,CAAUC,QAAV,CAAmB7C,iBAAnB,CAAqCgD,QAArC,CAA8C,KAAKxE,QAAL,CAAcwB,iBAA5D;IACA,KAAK4C,IAAL,CAAUC,QAAV,CAAmB5C,kBAAnB,CAAsC+C,QAAtC,CAA+C,KAAKxE,QAAL,CAAcyB,kBAA7D;IACA,KAAK2C,IAAL,CAAUC,QAAV,CAAmB3C,aAAnB,CAAiC8C,QAAjC,CAA0C,KAAKxE,QAAL,CAAc0B,aAAxD;IACA,KAAK0C,IAAL,CAAUC,QAAV,CAAmB1C,kBAAnB,CAAsC6C,QAAtC,CAA+C,KAAKxE,QAAL,CAAc2B,kBAA7D;IACA,KAAKyC,IAAL,CAAUC,QAAV,CAAmBvC,UAAnB,CAA8B0C,QAA9B,CAAuC,KAAKxE,QAAL,CAAc8B,UAArD;IACA,KAAKsC,IAAL,CAAUC,QAAV,CAAmBxC,aAAnB,CAAiC2C,QAAjC,CAA0C,KAAKxE,QAAL,CAAc6B,aAAxD;IACA,KAAKuC,IAAL,CAAUC,QAAV,CAAmBtC,cAAnB,CAAkCyC,QAAlC,CAA2C,KAAKxE,QAAL,CAAc+B,cAAzD;IACA,KAAKqC,IAAL,CAAUC,QAAV,CAAmBrC,mBAAnB,CAAuCwC,QAAvC,CAAgD,KAAKxE,QAAL,CAAcgC,mBAA9D;IACA,KAAKoC,IAAL,CAAUC,QAAV,CAAmBpC,UAAnB,CAA8BuC,QAA9B,CAAuC,KAAKxE,QAAL,CAAciC,UAArD,EAbA,CAcA;;IACA,KAAKjC,QAAL,CAAcoI,WAAd,CAA0BW,OAA1B,CAAkCmC,CAAC,IAAE,KAAK/D,WAAL,CAAiB,KAAjB,EAAwB+D,CAAxB,CAArC;IACA,KAAK9G,IAAL,CAAUC,QAAV,CAAmBjC,gBAAnB,CAAoCoC,QAApC,CAA6C,IAA7C;IACA,KAAKJ,IAAL,CAAUC,QAAV,CAAmBhC,eAAnB,CAAmCmC,QAAnC,CAA4C,KAAKxE,QAAL,CAAcqC,eAA1D;IACA,KAAK+B,IAAL,CAAUC,QAAV,CAAmB/B,IAAnB,CAAwBkC,QAAxB,CAAiC,KAAKxE,QAAL,CAAcsC,IAA/C;IACA,KAAK8B,IAAL,CAAUC,QAAV,CAAmB9B,KAAnB,CAAyBiC,QAAzB,CAAkC,KAAKxE,QAAL,CAAcuC,KAAhD;IACA,KAAK6B,IAAL,CAAUC,QAAV,CAAmB7B,GAAnB,CAAuBgC,QAAvB,CAAgC,KAAKxE,QAAL,CAAcwC,GAA9C;IACA,KAAK4B,IAAL,CAAUC,QAAV,CAAmB3B,mCAAnB,CAAuD8B,QAAvD,CAAgE,KAAKxE,QAAL,CAAc0C,mCAA9E;IACA,KAAK0B,IAAL,CAAUC,QAAV,CAAmB1B,cAAnB,CAAkC6B,QAAlC,CAA2C,KAAKxE,QAAL,CAAc2C,cAAzD;IACA,KAAKyB,IAAL,CAAUC,QAAV,CAAmBzB,WAAnB,CAA+B4B,QAA/B,CAAwC,KAAKxE,QAAL,CAAc4C,WAAtD;IACA,KAAKwB,IAAL,CAAUC,QAAV,CAAmBxB,YAAnB,CAAgC2B,QAAhC,CAAyC,KAAKxE,QAAL,CAAc6C,YAAvD;IACA,KAAKuB,IAAL,CAAUC,QAAV,CAAmBvB,UAAnB,CAA8B0B,QAA9B,CAAuC,KAAKxE,QAAL,CAAc8C,UAArD;IACA,KAAKsB,IAAL,CAAUC,QAAV,CAAmBtB,gCAAnB,CAAoDyB,QAApD,CAA6D,KAAKxE,QAAL,CAAc+C,gCAA3E;IACA,KAAKqB,IAAL,CAAUC,QAAV,CAAmBrB,mBAAnB,CAAuCwB,QAAvC,CAAgD,KAAKxE,QAAL,CAAcgD,mBAA9D;IACA,KAAKoB,IAAL,CAAUC,QAAV,CAAmBpB,sBAAnB,CAA0CuB,QAA1C,CAAmD,KAAKxE,QAAL,CAAciD,sBAAjE;IACA,KAAKmB,IAAL,CAAUC,QAAV,CAAmBnB,mBAAnB,CAAuCsB,QAAvC,CAAgD,KAAKxE,QAAL,CAAckD,mBAA9D;IACA,KAAKkB,IAAL,CAAUC,QAAV,CAAmBlB,oBAAnB,CAAwCqB,QAAxC,CAAiD,KAAKxE,QAAL,CAAcmD,oBAA/D;IACA,KAAKiB,IAAL,CAAUC,QAAV,CAAmBjB,kBAAnB,CAAsCoB,QAAtC,CAA+C,KAAKxE,QAAL,CAAcoD,kBAA7D;IACA,KAAKgB,IAAL,CAAUC,QAAV,CAAmBhB,kBAAnB,CAAsCmB,QAAtC,CAA+C,KAAKxE,QAAL,CAAcqD,kBAA7D;IACA,KAAKe,IAAL,CAAUC,QAAV,CAAmBf,oBAAnB,CAAwCkB,QAAxC,CAAiD,KAAKxE,QAAL,CAAcsD,oBAA/D;IACA,KAAKc,IAAL,CAAUC,QAAV,CAAmBd,mBAAnB,CAAuCiB,QAAvC,CAAgD,KAAKxE,QAAL,CAAcuD,mBAA9D;IACA,KAAKa,IAAL,CAAUC,QAAV,CAAmBZ,mBAAnB,CAAuCe,QAAvC,CAAgD,KAAKxE,QAAL,CAAcyD,mBAA9D;IACA,KAAKW,IAAL,CAAUC,QAAV,CAAmBV,kBAAnB,CAAsCa,QAAtC,CAA+C,KAAKxE,QAAL,CAAc2D,kBAA7D;IACA,KAAKS,IAAL,CAAUC,QAAV,CAAmBT,mBAAnB,CAAuCY,QAAvC,CAAgD,KAAKxE,QAAL,CAAc4D,mBAA9D;IACA,KAAKQ,IAAL,CAAUC,QAAV,CAAmBR,iBAAnB,CAAqCW,QAArC,CAA8C,KAAKxE,QAAL,CAAc6D,iBAA5D;IACA,KAAKO,IAAL,CAAUC,QAAV,CAAmBP,mBAAnB,CAAuCU,QAAvC,CAAgD,KAAKxE,QAAL,CAAc6J,OAA9D;IACA,KAAKzF,IAAL,CAAUC,QAAV,CAAmBL,YAAnB,CAAgCQ,QAAhC,CAAyC,KAAKxE,QAAL,CAAc8J,YAAvD;IACA,KAAK1F,IAAL,CAAUC,QAAV,CAAmBJ,QAAnB,CAA4BO,QAA5B,CAAqC,KAAKxE,QAAL,CAAc+J,QAAnD;EACH;;EAED0B,oBAAoB;IAEhB,IAAIC,gBAAgB,GAAE,CAAtB;IACA,IAAG,KAAKC,cAAR,EACAD,gBAAgB;IAEhB,IAAG,KAAKE,cAAR,EACAF,gBAAgB;IAEhB,IAAG,KAAKG,aAAR,EACAH,gBAAgB;IAEhBA,gBAAgB,GAAEA,gBAAgB,IAAE,CAAlB,GAAoB,CAApB,GAAsBA,gBAAxC;IAEA,KAAKI,cAAL,GAAqB,YAAW,KAAGJ,gBAAnC;EACD;;EAEDlD,aAAa;IAEX,IAAIuD,aAAa,GAAG,EAApB;IACA,IAAIC,QAAJ;IACA,IAAIC,KAAK,GAAiB,IAAIC,OAAJ,CAAatG,GAAG,IAAIoG,QAAQ,GAAGpG,GAA/B,CAA1B;IACA,IAAIuG,QAAQ,GAAO,EAAnB;;IAEA,IAAI,KAAKhI,aAAT,EAAwB;MACtB,KAAKA,aAAL,CAAmBC,IAAnB,CAAwBC,QAAxB,CAAiC+H,SAAjC,CAA2C5H,QAA3C,CAAoD,KAAKJ,IAAL,CAAUC,QAAV,CAAmB5C,kBAAnB,CAAsC6C,KAA1F;MACAyH,aAAa,CAACzD,IAAd,CAAmB,KAAKnE,aAAL,CAAmBkI,gBAAnB,EAAnB;MACAF,QAAQ,CAAC7D,IAAT,CAAc,KAAd;IAED;;IACD,IAAI,KAAK5D,aAAT,EAAwB;MACtBqH,aAAa,CAACzD,IAAd,CAAmB,KAAK5D,aAAL,CAAmB2H,gBAAnB,EAAnB;MACA,KAAK3H,aAAL,CAAmBN,IAAnB,CAAwBC,QAAxB,CAAiCiI,WAAjC,CAA6C9H,QAA7C,CAAsD,EAAtD;MACA,KAAKE,aAAL,CAAmBN,IAAnB,CAAwBC,QAAxB,CAAiCkI,2BAAjC,CAA6D/H,QAA7D,CAAsE,EAAtE;MACA,KAAKE,aAAL,CAAmBN,IAAnB,CAAwBC,QAAxB,CAAiCmI,wBAAjC,CAA0DhI,QAA1D,CAAmE,KAAKJ,IAAL,CAAUC,QAAV,CAAmBhC,eAAnB,CAAmCiC,KAAtG;MACA,KAAKI,aAAL,CAAmBN,IAAnB,CAAwBC,QAAxB,CAAiCoI,4BAAjC,CAA8DjI,QAA9D,CAAuE,KAAKJ,IAAL,CAAUC,QAAV,CAAmB/B,IAAnB,CAAwBgC,KAA/F;MACA,KAAKI,aAAL,CAAmBN,IAAnB,CAAwBC,QAAxB,CAAiCqI,6BAAjC,CAA+DlI,QAA/D,CAAwE,KAAKJ,IAAL,CAAUC,QAAV,CAAmB9B,KAAnB,CAAyB+B,KAAjG;MACA,KAAKI,aAAL,CAAmBN,IAAnB,CAAwBC,QAAxB,CAAiCsI,2BAAjC,CAA6DnI,QAA7D,CAAsE,KAAKJ,IAAL,CAAUC,QAAV,CAAmB7B,GAAnB,CAAuB8B,KAA7F;MACA,KAAKI,aAAL,CAAmBN,IAAnB,CAAwBC,QAAxB,CAAiCuI,kCAAjC,CAAoEpI,QAApE,CAA6E,EAA7E;MACA,KAAKE,aAAL,CAAmBN,IAAnB,CAAwBC,QAAxB,CAAiCwI,uBAAjC,CAAyDrI,QAAzD,CAAkE,EAAlE;;MACA,IAAG,KAAKJ,IAAL,CAAUC,QAAV,CAAmBtB,gCAAnB,CAAoDuB,KAApD,IAA6D,KAAhE,EAAsE;QACpE,KAAKI,aAAL,CAAmBN,IAAnB,CAAwBC,QAAxB,CAAiCyI,gBAAjC,CAAkDtI,QAAlD,CAA2D,gCAA3D;QACA,KAAKE,aAAL,CAAmBN,IAAnB,CAAwBC,QAAxB,CAAiC0I,YAAjC,CAA8CvI,QAA9C,CAAuD,KAAKJ,IAAL,CAAUC,QAAV,CAAmBf,oBAAnB,CAAwCgB,KAA/F;MACD,CAHD,MAIK,IAAG,KAAKF,IAAL,CAAUC,QAAV,CAAmBtB,gCAAnB,CAAoDuB,KAApD,IAA6D,IAAhE,EAAqE;QACzE,IAAG,KAAKF,IAAL,CAAUC,QAAV,CAAmBL,YAAnB,CAAgCM,KAAhC,IAAyC,KAA5C,EACA;UACC,KAAKI,aAAL,CAAmBN,IAAnB,CAAwBC,QAAxB,CAAiCyI,gBAAjC,CAAkDtI,QAAlD,CAA2D,KAAKJ,IAAL,CAAUC,QAAV,CAAmBJ,QAAnB,CAA4BK,KAAvF;QACA,CAHD,MAIK,IAAI,KAAKF,IAAL,CAAUC,QAAV,CAAmBL,YAAnB,CAAgCM,KAAhC,IAAyC,IAA7C,EACL;UACC,KAAKI,aAAL,CAAmBN,IAAnB,CAAwBC,QAAxB,CAAiCyI,gBAAjC,CAAkDtI,QAAlD,CAA2D,KAAKJ,IAAL,CAAUC,QAAV,CAAmBrB,mBAAnB,CAAuCsB,KAAlG;UACA,KAAKI,aAAL,CAAmBN,IAAnB,CAAwBC,QAAxB,CAAiC0I,YAAjC,CAA8CvI,QAA9C,CAAuD,KAAKJ,IAAL,CAAUC,QAAV,CAAmBpB,sBAAnB,CAA0CqB,KAAjG;QACA;MACD;;MACD6H,QAAQ,CAAC7D,IAAT,CAAc,KAAd;IACD;;IACD,IAAI,KAAKrD,YAAT,EAAuB;MACnB8G,aAAa,CAACzD,IAAd,CAAmB,KAAKrD,YAAL,CAAkBoH,gBAAlB,EAAnB;MACAF,QAAQ,CAAC7D,IAAT,CAAc,IAAd;IACD;;IAEH4D,OAAO,CAACc,GAAR,CAAYjB,aAAZ,EAA2BjB,IAA3B,CAAgCmC,QAAQ,IAAG;MACzC,IAAIlB,aAAa,CAACT,MAAd,IAAwB,CAA5B,EAA+B;QAC7BU,QAAQ;QACR;MACD;;MAED,IAAIkB,SAAS,GAAG,KAAKC,UAAL,CAAgB,KAAhB,EAAuBhB,QAAvB,CAAhB;MACA,IAAIiB,SAAS,GAAG,KAAKD,UAAL,CAAgB,KAAhB,EAAuBhB,QAAvB,CAAhB;MACA,IAAIkB,QAAQ,GAAG,KAAKF,UAAL,CAAgB,IAAhB,EAAsBhB,QAAtB,CAAf;MAEA,IAAIe,SAAS,IAAI,IAAjB,EACE,KAAK5C,wBAAL,GAAgC2C,QAAQ,CAACC,SAAD,CAAxC;;MAEF,IAAIE,SAAS,IAAI,IAAjB,EAAuB;QACrB,KAAKhD,YAAL,GAAoB6C,QAAQ,CAACG,SAAD,CAA5B;MACD;;MAED,IAAIC,QAAQ,IAAI,IAAhB,EAAsB;QACpB,KAAK7C,UAAL,GAAkByC,QAAQ,CAACI,QAAD,CAA1B;MACD;;MAEDrB,QAAQ;MACR;IAED,CAxBD;IA0BA,OAAOC,KAAP;EAED;;EAEOkB,UAAU,CAACG,IAAD,EAAMC,KAAN,EAAiB;IAEjC,IAAIC,GAAG,GAAID,KAAK,CAACE,SAAN,CAAgB7D,CAAC,IAAEA,CAAC,IAAE0D,IAAtB,CAAX;;IACE,IAAGE,GAAG,IAAE,CAAC,CAAT,EAAW;MACT,OAAO,IAAP;IACD;;IAED,OAAOA,GAAP;EACH;;EAEDzH,qBAAqB;IAEnB,KAAK3B,IAAL,CAAU4D,YAAV,CAAuBrC,SAAvB,CAAkC6H,GAAD,IAAO;MAEtC,IAAI,KAAK5B,cAAT,EAAwB;QAEtB,KAAKzH,aAAL,CAAmBC,IAAnB,CAAwBC,QAAxB,CAAiC+H,SAAjC,CAA2C5H,QAA3C,CAAoD,KAAKJ,IAAL,CAAUC,QAAV,CAAmB5C,kBAAnB,CAAsC6C,KAA1F;;QAEA,IAAG,KAAKF,IAAL,CAAUC,QAAV,CAAmBtC,cAAnB,CAAkCuC,KAArC,EAA2C;UAEzC,IAAIoJ,IAAI,GAAG,IAAIC,IAAJ,CAAS,KAAKvJ,IAAL,CAAUC,QAAV,CAAmBtC,cAAnB,CAAkCuC,KAA3C,CAAX;UAEF,KAAKH,aAAL,CAAmBC,IAAnB,CAAwBC,QAAxB,CAAiCuJ,WAAjC,CAA6CpJ,QAA7C,CAAsDkJ,IAAI,CAACG,WAAL,EAAtD;UACA,KAAK1J,aAAL,CAAmBC,IAAnB,CAAwBC,QAAxB,CAAiCyJ,WAAjC,CAA6CtJ,QAA7C,CAAuDkJ,IAAI,CAACK,QAAL,KAAgB,CAAvE;UACA,KAAK5J,aAAL,CAAmBC,IAAnB,CAAwBC,QAAxB,CAAiC2J,WAAjC,CAA6CxJ,QAA7C,CAAsDkJ,IAAI,CAACO,OAAL,EAAtD;QAEC,CARD,MASI;UAEF,KAAK9J,aAAL,CAAmBC,IAAnB,CAAwBC,QAAxB,CAAiCuJ,WAAjC,CAA6CpJ,QAA7C,CAAsD,IAAtD;UACA,KAAKL,aAAL,CAAmBC,IAAnB,CAAwBC,QAAxB,CAAiC2J,WAAjC,CAA6CxJ,QAA7C,CAAsD,IAAtD;UACA,KAAKL,aAAL,CAAmBC,IAAnB,CAAwBC,QAAxB,CAAiCyJ,WAAjC,CAA6CtJ,QAA7C,CAAsD,IAAtD;QACD;MAEF;IACF,CAvBD;EAwBD;;EAED+C,aAAa,CAAC2G,KAAD,EAAM;IACjB,IAAIC,OAAO,GAAG,IAAIrN,iBAAJ,EAAd;IACAqN,OAAO,CAACC,WAAR,GAAsB,IAAtB;IACAD,OAAO,CAAClO,YAAR,GAAuB,KAAvB;IACAkO,OAAO,CAAC3N,eAAR,GAA0B,KAA1B;IACA2N,OAAO,CAAC5L,KAAR,GAAgB2L,KAAhB;IACA,KAAK/M,GAAL,CAASkN,iBAAT,CAA2BF,OAA3B,EAAoCxI,SAApC,CAA8CC,GAAG,IAAG;MAElD,KAAKC,KAAL,GAAaD,GAAG,CAACC,KAAJ,GAAY,CAAZ,GAAgBD,GAAG,CAACC,KAApB,GAA4B,KAAKC,SAA9C;MACA,KAAKxF,mBAAL,CAAyBoH,KAAzB,GAAiC,KAAK7B,KAAtC;IACD,CAJD;EAKD;;EAEDyI,UAAU;IACRC,QAAQ,CAACC,cAAT,CAAwB,SAAxB,EAAmCC,KAAnC;EACD;;AArgBsB;;;;;;;;;;;;;;;;UAoBxBjP;IAASkP,OAAC7O,sBAAD,EAAyB;MAAE8O,MAAM,EAAE;IAAV,CAAzB;;;UACTnP;IAASkP,OAACpO,mBAAD,EAAsB;MAAEqO,MAAM,EAAE;IAAV,CAAtB;;;UAeXnP;IAASkP,OAAChO,qCAAD,EAAwC;MAAEiO,MAAM,EAAE;IAAV,CAAxC;;;UAUTnP;IAASkP,OAAC/N,+BAAD,EAAkC;MAAEgO,MAAM,EAAE;IAAV,CAAlC;;;UAyBTnP;IAASkP,OAAC9N,sBAAD,EAAyB;MAAE+N,MAAM,EAAE;IAAV,CAAzB;;;AAvEC5N,gBAAgB,eAL5BxB,SAAS,CAAC;EACPqP,QAAQ,EAAE,cADH;EAEPC,8BAFO;;AAAA,CAAD,CAKmB,GAAhB9N,gBAAgB,CAAhB;SAAAA", "names": ["ChangeDetectorRef", "Component", "ViewChild", "FormArray", "FormControl", "FormGroup", "Validators", "StateSelectorComponent", "FilingApiService", "FormLLCControls", "FormALLC", "CategoryCode", "ActivatedRoute", "LoadFilingService", "FilingServiceResponseObject", "FormModeService", "FilingModeComponent", "ResetExcept", "SubCategoryCode", "CustomSharedValidations", "EmployerIdentificationNumberComponent", "StatementOfInfromationComponent", "ProductDetailComponent", "FilingInfoService", "StatePriceRequest", "FormLLCComponent", "constructor", "cdr", "filingInfoService", "Api", "formMode", "activatedRoute", "FormationState", "SelectedFilingOptionType", "FilingServiceType", "ProposedEntityName", "Alternate<PERSON><PERSON>", "ReservedEntityName", "alphaNumeric", "ReservationNo", "PersonName", "ExpirationDate", "AcsPerformNameCheck", "Management", "addressField", "ManagerNames", "AddAnotherMember", "BusinessAddress", "City", "State", "Zip", "zipLength", "MailingAddressSameAsBusinessAddress", "MailingAddress", "MailingCity", "MailingState", "MailingZip", "AcsProvideRegisteredAgentService", "RegisteredAgentName", "RegisteredAgentAddress", "RegisteredAgentCity", "RegisteredAgentState", "RegisteredAgentZip", "MailForwardingName", "MailForwardingStreet", "MailForwardingPhone", "phoneLength", "MailForwardingEmail", "email", "MailForwardingCity", "MailForwardingState", "MailForwardingZip", "SpecialInstructions", "specialInstructions", "RAThirdParty", "CARAName", "_UpSelling_EIN", "UpSelling_EIN", "Form", "controls", "value", "IsUpSelling", "setValue", "_UpSelling_SOI", "UpSelling_SOI", "FormationType", "FiledBy", "EntityName", "AddressToggle", "detectChanges", "_UpSelling_CK", "UpSelling_CK", "Form1", "FilingState", "_IsUpselling", "UpsellingProductCode", "KitType", "ngOnInit", "getPrice", "SubCatCode", "formllc", "subscribe", "res", "price", "basePrice", "upSellingValuesUpdate", "FCGetStates", "States", "StateComponent", "GetFormLLCData", "data", "filingServiceType", "MapDynamicDataByControlId", "FilingService", "addMemberOptions", "AddMemberManager", "reservationWithStateOptions", "ReservationWithState", "entityNameReservedOptions", "mailingAddressSameOptions", "managementData", "acsProvideRegisteredAgentServiceOptions", "AgentService", "StartLoad", "isAdd", "AddManagers", "$OnStateSelection", "selectedState", "reset", "getStatePrice", "downloadPDFForm", "SelectedFilingType", "Price", "FilingTypes", "OnlineMode", "LineNo", "TabLineNo", "OfflineMode", "valueChanges", "DownloadStatement", "controlValue", "managerNameForm", "Manager<PERSON>ame", "required", "push", "OnSave", "UpSellingData", "valid", "Save", "ex", "console", "error", "RemoveValidationFromDynamicControls", "for<PERSON>ach", "control", "clearValidators", "updateValueAndValidity", "controlId", "mappedData", "filter", "obj", "controlID", "map", "item", "id", "controlText", "x", "Remarks", "Ra<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CaraName", "filingService", "formLLC", "upSelling", "annualReport", "AnnualReport", "employerIdentificationNumber", "EmployerIdentificationNo", "corporateKit", "KitOrderVM", "SaveFilingService", "queryParams", "queryString", "on", "FormLLC", "then", "serviceData", "Load", "catch", "e", "log", "RemoveMembers", "index", "length", "removeAt", "SelectedOnlineMethodChange", "onUpSellingSelection", "selectedServices", "_IsSOISelected", "_IsEINSelected", "_IsCKSelected", "ClassUpSelling", "upSellingData", "resolver", "respo", "Promise", "services", "LegalName", "getUpSellingData", "StateFileNo", "CaliforniaMaintanenceOffice", "PrincipalExecutiveOffice", "PrincipalExecutiveOfficeCity", "PrincipalExecutiveOfficeState", "PrincipalExecutiveOfficeZip", "PrincipalCaliforniaOfficeDifferent", "MailingAddressDifferent", "ServiceAgentName", "Agent<PERSON><PERSON><PERSON>", "all", "response", "index_EIN", "getIndexNo", "index_SOI", "index_CK", "code", "array", "val", "findIndex", "date", "Date", "file_date_y", "getFullYear", "file_date_m", "getMonth", "file_date_d", "getDate", "state", "request", "ProductCode", "GetStateWisePrice", "callSubmit", "document", "getElementById", "click", "args", "static", "selector", "template"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\viatech\\Ecom\\client\\acs\\src\\app\\Modules\\FilingService\\Pages\\form-llc\\form-llc.component.ts"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';\r\nimport { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport { StateSelectorComponent } from 'src/app/Modules/Shared/Components/formComponents/stateSelector/state-selector.component';\r\nimport { FilingApiService } from '../../Services/FilingApiService';\r\nimport { FormLLCControls } from 'src/app/Modules/Shared/Enums/form-llc-controls.enum';\r\nimport { FormALLC } from '../../Models/FormALLC';\r\nimport { PDFDownloadURLModel } from 'src/app/Modules/Shared/Models/FilingService/PDFDownloadURLModel';\r\nimport { CategoryCode } from 'src/app/Modules/Shared/Enums/category-code.enum';\r\nimport { ProductCode } from 'src/app/Modules/Shared/Enums/products-code.enum';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { LoadFilingService } from '../../Functions/filing-service-load.func';\r\nimport { FilingServiceResponseObject } from '../../Enums/filing-service-enum';\r\nimport { FormModeService } from 'src/app/Modules/Shared/Services/Common/FormModeService';\r\n\r\nimport { FilingModeComponent } from 'src/app/Modules/Shared/Components/formComponents/filing-mode/filing-mode.component';\r\nimport { ResetExcept } from 'src/app/Modules/Shared/functions/form-reset';\r\nimport { SubCategoryCode } from 'src/app/Modules/Shared/Enums/sub-category-code.enum';\r\nimport { CustomSharedValidations } from 'src/app/Modules/Shared/functions/custom-validations';\r\nimport { EmployerIdentificationNumberComponent } from '../employer-identification-number/employer-identification-number.component';\r\nimport { StatementOfInfromationComponent } from '../statement-of-information/statement-of-information.component';\r\nimport { EmployerIdnetificationNumber } from '../../Models/EmployerIdentificationNumber';\r\nimport { StatementOfInfromation } from '../../Models/StatementOfInformation';\r\nimport { ProductDetailComponent } from 'src/app/Modules/KitsMinutesBook/Components/product-detail/product-detail.component';\r\nimport { FilingInfoService } from '../../Services/filing-price.service';\r\nimport { ProductDetail } from 'src/app/Modules/Shared/Models/KitsModel/ProductDetail';\r\nimport { StatePriceRequest } from 'src/app/Modules/Shared/Models/Common/state-price-request.model';\r\n\r\n@Component({\r\n    selector: 'app-form-llc',\r\n    templateUrl: 'form-llc.component.html',\r\n    styleUrls: ['./form-llc.component.css']\r\n})\r\nexport class FormLLCComponent implements OnInit {\r\n\r\n    constructor(private cdr:ChangeDetectorRef,public filingInfoService:FilingInfoService,private Api: FilingApiService,public formMode :FormModeService,private activatedRoute :ActivatedRoute) { }\r\n    isAdd = true;\r\n    ManagerNames = new FormArray([]);\r\n    selectedState: any;\r\n    filingServiceType: any[];\r\n    managementData: any[];\r\n    acsProvideRegisteredAgentServiceOptions: any[];\r\n    addMemberOptions: any[];\r\n    reservationWithStateOptions: any[];\r\n    entityNameReservedOptions: any[];\r\n    mailingAddressSameOptions: any[];\r\n\r\n    obtainACopyOptions: any[];\r\n    officerTitleOptions: any[];\r\n    anotherOfficerOptions: any[];\r\n\r\n    FormALLC: FormALLC = new FormALLC();\r\n\r\n    @ViewChild(StateSelectorComponent, { static: true }) StateComponent: StateSelectorComponent;\r\n    @ViewChild(FilingModeComponent, { static: true }) FilingModeComponent: FilingModeComponent;\r\n\r\n     //======= Up Selling varibales\r\n  UpSelling_EIN: EmployerIdentificationNumberComponent;\r\n  UpSelling_SOI: StatementOfInfromationComponent;\r\n  UpSelling_CK: ProductDetailComponent;\r\n\r\n  _IsEINSelected = false;\r\n  _IsSOISelected = false;\r\n  _IsCKSelected = false;\r\n\r\n  EmployerIdentificationNo: EmployerIdnetificationNumber = null;\r\n  AnnualReport: StatementOfInfromation = null;\r\n  KitOrderVM: ProductDetail = null;\r\n\r\n  @ViewChild(EmployerIdentificationNumberComponent, { static: false }) set _UpSelling_EIN(UpSelling_EIN: EmployerIdentificationNumberComponent) {\r\n    \r\n    this.UpSelling_EIN =  UpSelling_EIN;\r\n\r\n    if(!this.UpSelling_EIN || this.UpSelling_EIN.Form.controls.FormationState.value){return;}\r\n\r\n    this.UpSelling_EIN.IsUpSelling = true;\r\n    this.UpSelling_EIN.Form.controls.FormationState.setValue(this.Form.controls.FormationState.value)\r\n  }\r\n\r\n  @ViewChild(StatementOfInfromationComponent, { static: false }) set _UpSelling_SOI(UpSelling_SOI: StatementOfInfromationComponent) {\r\n    this.UpSelling_SOI =  UpSelling_SOI;   \r\n    if(!this.UpSelling_SOI || this.UpSelling_SOI.Form.controls.FormationState.value ||  this.UpSelling_SOI.Form.controls.FormationType.value){return;}\r\n    this.UpSelling_SOI.IsUpSelling = true;\r\n    this.UpSelling_SOI.Form.controls.FormationState.setValue(this.Form.controls.FormationState.value);\r\n    this.UpSelling_SOI.Form.controls.FormationType.setValue('LC');\r\n    this.UpSelling_SOI.Form.controls.FiledBy.setValue(2);\r\n    this.UpSelling_SOI.Form.controls.EntityName.setValue(this.Form.controls.ProposedEntityName.value);\r\n    if(this.Form.controls.AcsProvideRegisteredAgentService.value == 'YES'){\r\n      this.UpSelling_SOI.AddressToggle = true\r\n    }\r\n    else if(this.Form.controls.AcsProvideRegisteredAgentService.value == 'NO'){\r\n     if(this.Form.controls.RAThirdParty.value == 'YES')\r\n     {   \r\n      this.UpSelling_SOI.AddressToggle = false\r\n     }\r\n     else if (this.Form.controls.RAThirdParty.value == 'NO')\r\n     {    \r\n      this.UpSelling_SOI.AddressToggle = true\r\n     }\r\n    } \r\n    this.cdr.detectChanges()\r\n  }\r\n\r\n  \r\n  @ViewChild(ProductDetailComponent, { static: false }) set _UpSelling_CK(UpSelling_CK: ProductDetailComponent) {\r\n\r\n    this.UpSelling_CK = UpSelling_CK;\r\n\r\n    if (!this.UpSelling_CK || this.UpSelling_CK.Form1.controls.FilingState.value) { return; }\r\n    this.UpSelling_CK._IsUpselling = true;\r\n    this.UpSelling_CK.UpsellingProductCode = 'LK'\r\n\r\n    this.UpSelling_CK.Form1.controls.FilingState.setValue(this.Form.controls.FormationState.value);\r\n    this.UpSelling_CK.Form1.controls.KitType.setValue('LK');\r\n\r\n    this.cdr.detectChanges();\r\n\r\n  }\r\n\r\n  ClassUpSelling:string=\"col-md-12\"\r\n  price: number;\r\n  basePrice: number;\r\n\r\n  //=====================\r\n\r\n    ngOnInit() {\r\n        this.filingInfoService.getPrice(this.filingInfoService.SubCatCode.formllc).subscribe(res => {\r\n          this.price = res;\r\n          this.basePrice = res;\r\n        });\r\n\r\n        this.upSellingValuesUpdate();\r\n        \r\n        this.Api.FCGetStates().subscribe(States => {\r\n            this.StateComponent.States = States;\r\n        });\r\n\r\n        this.Api.GetFormLLCData().subscribe(data => {\r\n            this.filingServiceType = this.MapDynamicDataByControlId(data, FormLLCControls.FilingService);\r\n            this.addMemberOptions = this.MapDynamicDataByControlId(data, FormLLCControls.AddMemberManager);\r\n            this.reservationWithStateOptions = this.MapDynamicDataByControlId(data, FormLLCControls.ReservationWithState);\r\n            this.entityNameReservedOptions = this.MapDynamicDataByControlId(data, FormLLCControls.ReservedEntityName);\r\n            this.mailingAddressSameOptions = this.MapDynamicDataByControlId(data, FormLLCControls.MailingAddress);\r\n            this.managementData = this.MapDynamicDataByControlId(data, FormLLCControls.Management);\r\n            this.acsProvideRegisteredAgentServiceOptions = this.MapDynamicDataByControlId(data, FormLLCControls.AgentService);;\r\n\r\n          \r\n\r\n            this.StartLoad();\r\n            if(this.isAdd)\r\n                this.AddManagers('YES');\r\n\r\n        });\r\n\r\n        this.StateComponent.$OnStateSelection.subscribe(selectedState => {\r\n            this.FilingModeComponent.reset();\r\n            if (selectedState) {\r\n                this.getStatePrice(selectedState);\r\n                this.selectedState = selectedState;\r\n\r\n                this.FilingModeComponent.downloadPDFForm(this.selectedState,CategoryCode.formllc,SubCategoryCode.formllc);\r\n                this.FilingModeComponent.SelectedFilingType = null;\r\n                this.FilingModeComponent.Price = this.price;\r\n                if (selectedState === 'CA' ) {\r\n                    this.FilingModeComponent.SelectedFilingType = this.FilingModeComponent.FilingTypes.OnlineMode;\r\n                    this.FilingModeComponent.LineNo = 3;\r\n                    this.FilingModeComponent.TabLineNo = 2;\r\n                }\r\n                else {\r\n                    this.FilingModeComponent.SelectedFilingType = this.FilingModeComponent.FilingTypes.OfflineMode;\r\n                    this.FilingModeComponent.LineNo = 2;\r\n                }\r\n            }\r\n            \r\n            ResetExcept(this.Form, [\"FormationState\"])\r\n\r\n            \r\n            \r\n        });\r\n\r\n        this.Form.controls.SelectedFilingOptionType.valueChanges.subscribe(value=>{\r\n\r\n\r\n\r\n            if(this.selectedState == 'CA'){\r\n             \r\n                this.Form.controls.State.setValue('CA')\r\n            }\r\n        })\r\n\r\n        this.Form.controls.RAThirdParty.valueChanges.subscribe(value=>{\r\n\r\n          if(this.Form.controls.FormationState.value =='CA'){\r\n            this.Form.controls.RegisteredAgentState.setValue('CA')\r\n          }\r\n        })\r\n\r\n\r\n        this.FilingModeComponent.DownloadStatement =`Please download the service form, complete and email (<a class='email' href=\"mailto:<EMAIL>\"><EMAIL></a>) to us along with Articles of Organization.`\r\n       \r\n    }\r\n   \r\n\r\n    Form = new FormGroup({\r\n        FormationState: new FormControl<string | null>(null),\r\n        SelectedFilingOptionType: new FormControl<any | null>(null),\r\n        FilingServiceType: new FormControl<string | null>(null),\r\n        ProposedEntityName: new FormControl<string | null>(null),\r\n        AlternateName: new FormControl<string | null>(null),\r\n        ReservedEntityName: new FormControl<string | null>(null,CustomSharedValidations.alphaNumeric),\r\n        ReservationNo: new FormControl<string | null>(null),\r\n        PersonName: new FormControl<string | null>(null,CustomSharedValidations.alphaNumeric),\r\n        ExpirationDate: new FormControl<string | null>(null),\r\n        AcsPerformNameCheck: new FormControl<string | null>(null),\r\n        Management: new FormControl<string | null>(null,CustomSharedValidations.addressField),\r\n        ManagerNames: this.ManagerNames,\r\n        AddAnotherMember: new FormControl<string | null>(null),\r\n        BusinessAddress: new FormControl<string | null>(null,CustomSharedValidations.addressField),\r\n        City: new FormControl<string | null>(null,CustomSharedValidations.alphaNumeric),\r\n        State: new FormControl<string | null>(null),\r\n        Zip: new FormControl<number | null>(null,[CustomSharedValidations.zipLength]),\r\n        MailingAddressSameAsBusinessAddress: new FormControl<string | null>(null),\r\n        MailingAddress: new FormControl<string | null>(null,CustomSharedValidations.addressField),\r\n        MailingCity: new FormControl<string | null>(null,CustomSharedValidations.alphaNumeric),\r\n        MailingState: new FormControl<string | null>(null),\r\n        MailingZip: new FormControl<number | null>(null,[CustomSharedValidations.zipLength]),\r\n        AcsProvideRegisteredAgentService: new FormControl<string | null>(null),\r\n        RegisteredAgentName: new FormControl<string | null>(null,CustomSharedValidations.alphaNumeric),\r\n        RegisteredAgentAddress: new FormControl<string | null>(null,CustomSharedValidations.addressField),\r\n        RegisteredAgentCity: new FormControl<string | null>(null,CustomSharedValidations.alphaNumeric),\r\n        RegisteredAgentState: new FormControl<string | null>(null),\r\n        RegisteredAgentZip: new FormControl<number | null>(null,[CustomSharedValidations.zipLength]),\r\n        MailForwardingName: new FormControl<string | null>(null,CustomSharedValidations.alphaNumeric),\r\n        MailForwardingStreet: new FormControl<string | null>(null,CustomSharedValidations.addressField),\r\n        MailForwardingPhone: new FormControl<number | null>(null,CustomSharedValidations.phoneLength),\r\n        MailForwardingEmail: new FormControl<string | null>(null,Validators.email),\r\n        MailForwardingCity: new FormControl<string | null>(null,CustomSharedValidations.alphaNumeric),\r\n        MailForwardingState: new FormControl<string | null>(null),\r\n        MailForwardingZip: new FormControl<number | null>(null,[CustomSharedValidations.zipLength]),\r\n        SpecialInstructions: new FormControl<string | null>(null,[CustomSharedValidations.specialInstructions]),\r\n        RAThirdParty:new FormControl<string | null>(null),\r\n        CARAName:new FormControl<string | null>(null),\r\n        });\r\n\r\n    AddManagers(value, controlValue?:string) {\r\n        if (value !== 'YES') return;\r\n        \r\n        let managerNameForm = new FormGroup({\r\n            ManagerName: new FormControl<string | null>(controlValue || '', [Validators.required,CustomSharedValidations.alphaNumeric])\r\n        })\r\n        this.ManagerNames.push(managerNameForm);\r\n        this.Form.controls.AddAnotherMember.reset();\r\n    }\r\n\r\n   \r\n   async OnSave(){\r\n        try{\r\n          \r\n            await this.UpSellingData()\r\n\r\n            if(this.Form.valid){\r\n            \r\n                this.Save();\r\n            }\r\n        }\r\n        catch(ex){\r\n            console.error(ex)\r\n        }\r\n    }\r\n\r\n    private RemoveValidationFromDynamicControls(controls: any[]) {\r\n        controls.forEach(control => {\r\n            control.clearValidators();\r\n            control.updateValueAndValidity();\r\n        });\r\n    }\r\n\r\n    private MapDynamicDataByControlId(data: any[], controlId: any) {\r\n        let mappedData = data.filter(obj => obj.controlID === controlId).map(item => {\r\n            var data = { id: item.controlValue, value: item.controlText };\r\n            return data;\r\n        });\r\n        return mappedData;\r\n    }\r\n\r\n    Save() {\r\n        this.FormALLC.FormationState = this.Form.controls.FormationState.value;\r\n        this.FormALLC.FilingServiceType = this.Form.controls.FilingServiceType.value;\r\n        this.FormALLC.ProposedEntityName = this.Form.controls.ProposedEntityName.value;\r\n        this.FormALLC.AlternateName = this.Form.controls.AlternateName.value;\r\n        this.FormALLC.ReservedEntityName = this.Form.controls.ReservedEntityName.value;\r\n        this.FormALLC.PersonName = this.Form.controls.PersonName.value;\r\n        this.FormALLC.ReservationNo = this.Form.controls.ReservationNo.value;\r\n        this.FormALLC.ExpirationDate = this.Form.controls.ExpirationDate.value;\r\n        this.FormALLC.AcsPerformNameCheck = this.Form.controls.AcsPerformNameCheck.value;\r\n        this.FormALLC.Management = this.Form.controls.Management.value;\r\n        this.FormALLC.ManagerName = this.Form.controls.ManagerNames.value.map(x=>x.ManagerName);\r\n        this.FormALLC.AddAnotherMember = this.Form.controls.AddAnotherMember.value;\r\n        this.FormALLC.BusinessAddress = this.Form.controls.BusinessAddress.value;\r\n        this.FormALLC.City = this.Form.controls.City.value;\r\n        this.FormALLC.State = this.Form.controls.State.value;\r\n        this.FormALLC.Zip = this.Form.controls.Zip.value;\r\n        this.FormALLC.MailingAddressSameAsBusinessAddress = this.Form.controls.MailingAddressSameAsBusinessAddress.value;\r\n        this.FormALLC.MailingAddress = this.Form.controls.MailingAddress.value;\r\n        this.FormALLC.MailingCity = this.Form.controls.MailingCity.value;\r\n        this.FormALLC.MailingState = this.Form.controls.MailingState.value;\r\n        this.FormALLC.MailingZip = this.Form.controls.MailingZip.value;\r\n        this.FormALLC.AcsProvideRegisteredAgentService = this.Form.controls.AcsProvideRegisteredAgentService.value;\r\n        this.FormALLC.RegisteredAgentName = this.Form.controls.RegisteredAgentName.value;\r\n        this.FormALLC.RegisteredAgentAddress = this.Form.controls.RegisteredAgentAddress.value;\r\n        this.FormALLC.RegisteredAgentCity = this.Form.controls.RegisteredAgentCity.value;\r\n        this.FormALLC.RegisteredAgentState = this.Form.controls.RegisteredAgentState.value;\r\n        this.FormALLC.RegisteredAgentZip = this.Form.controls.RegisteredAgentZip.value;\r\n        this.FormALLC.MailForwardingName = this.Form.controls.MailForwardingName.value;\r\n        this.FormALLC.MailForwardingStreet = this.Form.controls.MailForwardingStreet.value;\r\n        this.FormALLC.MailForwardingPhone = this.Form.controls.MailForwardingPhone.value;\r\n        this.FormALLC.MailForwardingEmail = this.Form.controls.MailForwardingEmail.value;\r\n        this.FormALLC.MailForwardingCity = this.Form.controls.MailForwardingCity.value;\r\n        this.FormALLC.MailForwardingState = this.Form.controls.MailForwardingState.value;\r\n        this.FormALLC.MailForwardingZip = this.Form.controls.MailForwardingZip.value;\r\n        this.FormALLC.Remarks = this.Form.controls.SpecialInstructions.value;\r\n        this.FormALLC.RaThirdParty = this.Form.controls.RAThirdParty.value;\r\n        this.FormALLC.CaraName = this.Form.controls.CARAName.value;\r\n        \r\n        let filingService = {\r\n            formLLC:this.FormALLC,\r\n            upSelling: {\r\n              annualReport: this.AnnualReport,\r\n              employerIdentificationNumber: this.EmployerIdentificationNo,\r\n              corporateKit: this.KitOrderVM\r\n            }\r\n          }\r\n\r\n        this.Api.SaveFilingService(filingService).subscribe(x=>{\r\n\r\n            this.Form.reset();\r\n        })\r\n    }\r\n\r\n    StartLoad(){\r\n        this.activatedRoute.queryParams.subscribe(async queryString=>{\r\n            if(queryString.on){\r\n                this.isAdd = false;\r\n            }\r\n            LoadFilingService<FormALLC>(queryString,this.Api,FilingServiceResponseObject.FormLLC,this.formMode).then(serviceData=>{\r\n      \r\n              \r\n                this.FormALLC = serviceData ;\r\n                this.Load();\r\n            }).catch(e=>console.log(e))\r\n           \r\n      \r\n        })\r\n      }\r\n\r\n      RemoveMembers(index){\r\n          if(this.ManagerNames.length>1)\r\n          this.ManagerNames.removeAt(index);\r\n      }\r\n\r\n\r\n    Load() {\r\n        this.getStatePrice(this.FormALLC.FormationState);\r\n        this.Form.controls.FormationState.setValue(this.FormALLC.FormationState)\r\n        this.FilingModeComponent.SelectedOnlineMethodChange(2);\r\n        this.Form.controls.SelectedFilingOptionType.setValue(2);\r\n        this.Form.controls.FilingServiceType.setValue(this.FormALLC.FilingServiceType)\r\n        this.Form.controls.ProposedEntityName.setValue(this.FormALLC.ProposedEntityName)\r\n        this.Form.controls.AlternateName.setValue(this.FormALLC.AlternateName)\r\n        this.Form.controls.ReservedEntityName.setValue(this.FormALLC.ReservedEntityName)\r\n        this.Form.controls.PersonName.setValue(this.FormALLC.PersonName)\r\n        this.Form.controls.ReservationNo.setValue(this.FormALLC.ReservationNo)\r\n        this.Form.controls.ExpirationDate.setValue(this.FormALLC.ExpirationDate)\r\n        this.Form.controls.AcsPerformNameCheck.setValue(this.FormALLC.AcsPerformNameCheck)\r\n        this.Form.controls.Management.setValue(this.FormALLC.Management)\r\n        //this.Form.controls.ManagerName.setValue(this.FormALLC.ManagerName)\r\n        this.FormALLC.ManagerName.forEach(e=>this.AddManagers(\"YES\", e))\r\n        this.Form.controls.AddAnotherMember.setValue('NO')\r\n        this.Form.controls.BusinessAddress.setValue(this.FormALLC.BusinessAddress)\r\n        this.Form.controls.City.setValue(this.FormALLC.City)\r\n        this.Form.controls.State.setValue(this.FormALLC.State)\r\n        this.Form.controls.Zip.setValue(this.FormALLC.Zip)\r\n        this.Form.controls.MailingAddressSameAsBusinessAddress.setValue(this.FormALLC.MailingAddressSameAsBusinessAddress)\r\n        this.Form.controls.MailingAddress.setValue(this.FormALLC.MailingAddress)\r\n        this.Form.controls.MailingCity.setValue(this.FormALLC.MailingCity)\r\n        this.Form.controls.MailingState.setValue(this.FormALLC.MailingState)\r\n        this.Form.controls.MailingZip.setValue(this.FormALLC.MailingZip)\r\n        this.Form.controls.AcsProvideRegisteredAgentService.setValue(this.FormALLC.AcsProvideRegisteredAgentService)\r\n        this.Form.controls.RegisteredAgentName.setValue(this.FormALLC.RegisteredAgentName)\r\n        this.Form.controls.RegisteredAgentAddress.setValue(this.FormALLC.RegisteredAgentAddress)\r\n        this.Form.controls.RegisteredAgentCity.setValue(this.FormALLC.RegisteredAgentCity)\r\n        this.Form.controls.RegisteredAgentState.setValue(this.FormALLC.RegisteredAgentState)\r\n        this.Form.controls.RegisteredAgentZip.setValue(this.FormALLC.RegisteredAgentZip)\r\n        this.Form.controls.MailForwardingName.setValue(this.FormALLC.MailForwardingName)\r\n        this.Form.controls.MailForwardingStreet.setValue(this.FormALLC.MailForwardingStreet)\r\n        this.Form.controls.MailForwardingPhone.setValue(this.FormALLC.MailForwardingPhone)\r\n        this.Form.controls.MailForwardingEmail.setValue(this.FormALLC.MailForwardingEmail)\r\n        this.Form.controls.MailForwardingCity.setValue(this.FormALLC.MailForwardingCity)\r\n        this.Form.controls.MailForwardingState.setValue(this.FormALLC.MailForwardingState)\r\n        this.Form.controls.MailForwardingZip.setValue(this.FormALLC.MailForwardingZip)\r\n        this.Form.controls.SpecialInstructions.setValue(this.FormALLC.Remarks)\r\n        this.Form.controls.RAThirdParty.setValue(this.FormALLC.RaThirdParty)\r\n        this.Form.controls.CARAName.setValue(this.FormALLC.CaraName)\r\n    }\r\n\r\n    onUpSellingSelection(){\r\n\r\n        let selectedServices =0;\r\n        if(this._IsSOISelected)\r\n        selectedServices++;\r\n    \r\n        if(this._IsEINSelected)\r\n        selectedServices++;\r\n    \r\n        if(this._IsCKSelected)\r\n        selectedServices++;\r\n    \r\n        selectedServices= selectedServices==0?1:selectedServices;\r\n    \r\n        this.ClassUpSelling =\"col-md-\"+(12/selectedServices);\r\n      }\r\n\r\n      UpSellingData(): Promise<any> {\r\n\r\n        let upSellingData = [];\r\n        let resolver;\r\n        let respo: Promise<any> = new Promise((res => resolver = res));\r\n        let services:any[]=[]\r\n    \r\n        if (this.UpSelling_EIN) {\r\n          this.UpSelling_EIN.Form.controls.LegalName.setValue(this.Form.controls.ProposedEntityName.value);\r\n          upSellingData.push(this.UpSelling_EIN.getUpSellingData());\r\n          services.push(\"EIN\")\r\n    \r\n        }\r\n        if (this.UpSelling_SOI) {\r\n          upSellingData.push(this.UpSelling_SOI.getUpSellingData());\r\n          this.UpSelling_SOI.Form.controls.StateFileNo.setValue(''); \r\n          this.UpSelling_SOI.Form.controls.CaliforniaMaintanenceOffice.setValue('');         \r\n          this.UpSelling_SOI.Form.controls.PrincipalExecutiveOffice.setValue(this.Form.controls.BusinessAddress.value);\r\n          this.UpSelling_SOI.Form.controls.PrincipalExecutiveOfficeCity.setValue(this.Form.controls.City.value);\r\n          this.UpSelling_SOI.Form.controls.PrincipalExecutiveOfficeState.setValue(this.Form.controls.State.value);\r\n          this.UpSelling_SOI.Form.controls.PrincipalExecutiveOfficeZip.setValue(this.Form.controls.Zip.value);\r\n          this.UpSelling_SOI.Form.controls.PrincipalCaliforniaOfficeDifferent.setValue('');\r\n          this.UpSelling_SOI.Form.controls.MailingAddressDifferent.setValue('');\r\n          if(this.Form.controls.AcsProvideRegisteredAgentService.value == 'YES'){\r\n            this.UpSelling_SOI.Form.controls.ServiceAgentName.setValue(\"Attorneys Corporation Services\");\r\n            this.UpSelling_SOI.Form.controls.AgentAddress.setValue(this.Form.controls.MailForwardingStreet.value);\r\n          }\r\n          else if(this.Form.controls.AcsProvideRegisteredAgentService.value == 'NO'){\r\n           if(this.Form.controls.RAThirdParty.value == 'YES')\r\n           {   \r\n            this.UpSelling_SOI.Form.controls.ServiceAgentName.setValue(this.Form.controls.CARAName.value);\r\n           }\r\n           else if (this.Form.controls.RAThirdParty.value == 'NO')\r\n           {\r\n            this.UpSelling_SOI.Form.controls.ServiceAgentName.setValue(this.Form.controls.RegisteredAgentName.value);  \r\n            this.UpSelling_SOI.Form.controls.AgentAddress.setValue(this.Form.controls.RegisteredAgentAddress.value);\r\n           }\r\n          }\r\n          services.push(\"SOI\")\r\n        }\r\n        if (this.UpSelling_CK) {\r\n            upSellingData.push(this.UpSelling_CK.getUpSellingData());\r\n            services.push(\"CK\")\r\n          }\r\n    \r\n        Promise.all(upSellingData).then(response => {\r\n          if (upSellingData.length <= 0) {\r\n            resolver();\r\n            return;\r\n          }\r\n    \r\n          let index_EIN = this.getIndexNo(\"EIN\", services);\r\n          let index_SOI = this.getIndexNo(\"SOI\", services);\r\n          let index_CK = this.getIndexNo(\"CK\", services);\r\n    \r\n          if (index_EIN != null)\r\n            this.EmployerIdentificationNo = response[index_EIN];\r\n    \r\n          if (index_SOI != null) {\r\n            this.AnnualReport = response[index_SOI];\r\n          }\r\n    \r\n          if (index_CK != null) {\r\n            this.KitOrderVM = response[index_CK];\r\n          }\r\n    \r\n          resolver();\r\n          return;\r\n    \r\n        })\r\n    \r\n        return respo\r\n    \r\n      }\r\n\r\n      private getIndexNo(code,array:any[]){\r\n\r\n        let val =  array.findIndex(x=>x==code);\r\n          if(val==-1){\r\n            return null;\r\n          }\r\n    \r\n          return val;\r\n      }\r\n\r\n      upSellingValuesUpdate(){\r\n\r\n        this.Form.valueChanges.subscribe((val)=>{\r\n    \r\n          if (this._IsEINSelected){\r\n    \r\n            this.UpSelling_EIN.Form.controls.LegalName.setValue(this.Form.controls.ProposedEntityName.value)\r\n    \r\n            if(this.Form.controls.ExpirationDate.value){\r\n    \r\n              let date = new Date(this.Form.controls.ExpirationDate.value)\r\n    \r\n            this.UpSelling_EIN.Form.controls.file_date_y.setValue(date.getFullYear())\r\n            this.UpSelling_EIN.Form.controls.file_date_m.setValue((date.getMonth()+1))\r\n            this.UpSelling_EIN.Form.controls.file_date_d.setValue(date.getDate())\r\n    \r\n            }\r\n            else{\r\n    \r\n              this.UpSelling_EIN.Form.controls.file_date_y.setValue(null)\r\n              this.UpSelling_EIN.Form.controls.file_date_d.setValue(null)\r\n              this.UpSelling_EIN.Form.controls.file_date_m.setValue(null)\r\n            }\r\n            \r\n          }\r\n        })\r\n      }\r\n\r\n      getStatePrice(state){\r\n        var request = new StatePriceRequest();\r\n        request.ProductCode = 'FS';\r\n        request.CategoryCode = '003';\r\n        request.SubCategoryCode = '070';\r\n        request.State = state;\r\n        this.Api.GetStateWisePrice(request).subscribe(res => \r\n        {\r\n          this.price = res.price > 0 ? res.price : this.basePrice;\r\n          this.FilingModeComponent.Price = this.price;\r\n        });\r\n      }\r\n\r\n      callSubmit(){\r\n        document.getElementById('__main1').click()\r\n      }\r\n}"]}, "metadata": {}, "sourceType": "module"}