{"ast": null, "code": "import { CdkAccordionItem, CdkAccordion, CdkAccordionModule } from '@angular/cdk/accordion';\nimport * as i2 from '@angular/cdk/portal';\nimport { TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, SkipSelf, Input, Output, ContentChild, ViewChild, Host, Attribute, QueryList, ContentChildren, NgModule } from '@angular/core';\nimport { mixinTabIndex, MatCommonModule } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i2$1 from '@angular/cdk/a11y';\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport { distinctUntilChanged, startWith, filter, take } from 'rxjs/operators';\nimport { ENTER, hasModifierKey, SPACE } from '@angular/cdk/keycodes';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { Subject, Subscription, EMPTY, merge } from 'rxjs';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/cdk/collections';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Token used to provide a `MatAccordion` to `MatExpansionPanel`.\n * Used primarily to avoid circular imports between `MatAccordion` and `MatExpansionPanel`.\n */\n\nconst _c0 = [\"body\"];\n\nfunction MatExpansionPanel_ng_template_5_Template(rf, ctx) {}\n\nconst _c1 = [[[\"mat-expansion-panel-header\"]], \"*\", [[\"mat-action-row\"]]];\nconst _c2 = [\"mat-expansion-panel-header\", \"*\", \"mat-action-row\"];\n\nfunction MatExpansionPanelHeader_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 2);\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@indicatorRotate\", ctx_r0._getExpandedState());\n  }\n}\n\nconst _c3 = [[[\"mat-panel-title\"]], [[\"mat-panel-description\"]], \"*\"];\nconst _c4 = [\"mat-panel-title\", \"mat-panel-description\", \"*\"];\nconst MAT_ACCORDION = new InjectionToken('MAT_ACCORDION');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Time and timing curve for expansion panel animations. */\n// Note: Keep this in sync with the Sass variable for the panel header animation.\n\nconst EXPANSION_PANEL_ANIMATION_TIMING = '225ms cubic-bezier(0.4,0.0,0.2,1)';\n/**\n * Animations used by the Material expansion panel.\n *\n * A bug in angular animation's `state` when ViewContainers are moved using ViewContainerRef.move()\n * causes the animation state of moved components to become `void` upon exit, and not update again\n * upon reentry into the DOM.  This can lead a to situation for the expansion panel where the state\n * of the panel is `expanded` or `collapsed` but the animation state is `void`.\n *\n * To correctly handle animating to the next state, we animate between `void` and `collapsed` which\n * are defined to have the same styles. Since angular animates from the current styles to the\n * destination state's style definition, in situations where we are moving from `void`'s styles to\n * `collapsed` this acts a noop since no style values change.\n *\n * In the case where angular's animation state is out of sync with the expansion panel's state, the\n * expansion panel being `expanded` and angular animations being `void`, the animation from the\n * `expanded`'s effective styles (though in a `void` animation state) to the collapsed state will\n * occur as expected.\n *\n * Angular Bug: https://github.com/angular/angular/issues/18847\n *\n * @docs-private\n */\n\nconst matExpansionAnimations = {\n  /** Animation that rotates the indicator arrow. */\n  indicatorRotate: trigger('indicatorRotate', [state('collapsed, void', style({\n    transform: 'rotate(0deg)'\n  })), state('expanded', style({\n    transform: 'rotate(180deg)'\n  })), transition('expanded <=> collapsed, void => collapsed', animate(EXPANSION_PANEL_ANIMATION_TIMING))]),\n\n  /** Animation that expands and collapses the panel content. */\n  bodyExpansion: trigger('bodyExpansion', [state('collapsed, void', style({\n    height: '0px',\n    visibility: 'hidden'\n  })), state('expanded', style({\n    height: '*',\n    visibility: 'visible'\n  })), transition('expanded <=> collapsed, void => collapsed', animate(EXPANSION_PANEL_ANIMATION_TIMING))])\n};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Token used to provide a `MatExpansionPanel` to `MatExpansionPanelContent`.\n * Used to avoid circular imports between `MatExpansionPanel` and `MatExpansionPanelContent`.\n */\n\nconst MAT_EXPANSION_PANEL = new InjectionToken('MAT_EXPANSION_PANEL');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Expansion panel content that will be rendered lazily\n * after the panel is opened for the first time.\n */\n\nclass MatExpansionPanelContent {\n  constructor(_template, _expansionPanel) {\n    this._template = _template;\n    this._expansionPanel = _expansionPanel;\n  }\n\n}\n\nMatExpansionPanelContent.ɵfac = function MatExpansionPanelContent_Factory(t) {\n  return new (t || MatExpansionPanelContent)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(MAT_EXPANSION_PANEL, 8));\n};\n\nMatExpansionPanelContent.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatExpansionPanelContent,\n  selectors: [[\"ng-template\", \"matExpansionPanelContent\", \"\"]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelContent, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[matExpansionPanelContent]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_EXPANSION_PANEL]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n/** Counter for generating unique element ids. */\n\n\nlet uniqueId = 0;\n/**\n * Injection token that can be used to configure the default\n * options for the expansion panel component.\n */\n\nconst MAT_EXPANSION_PANEL_DEFAULT_OPTIONS = new InjectionToken('MAT_EXPANSION_PANEL_DEFAULT_OPTIONS');\n/**\n * This component can be used as a single element to show expandable content, or as one of\n * multiple children of an element with the MatAccordion directive attached.\n */\n\nclass MatExpansionPanel extends CdkAccordionItem {\n  constructor(accordion, _changeDetectorRef, _uniqueSelectionDispatcher, _viewContainerRef, _document, _animationMode, defaultOptions) {\n    super(accordion, _changeDetectorRef, _uniqueSelectionDispatcher);\n    this._viewContainerRef = _viewContainerRef;\n    this._animationMode = _animationMode;\n    this._hideToggle = false;\n    /** An event emitted after the body's expansion animation happens. */\n\n    this.afterExpand = new EventEmitter();\n    /** An event emitted after the body's collapse animation happens. */\n\n    this.afterCollapse = new EventEmitter();\n    /** Stream that emits for changes in `@Input` properties. */\n\n    this._inputChanges = new Subject();\n    /** ID for the associated header element. Used for a11y labelling. */\n\n    this._headerId = `mat-expansion-panel-header-${uniqueId++}`;\n    /** Stream of body animation done events. */\n\n    this._bodyAnimationDone = new Subject();\n    this.accordion = accordion;\n    this._document = _document; // We need a Subject with distinctUntilChanged, because the `done` event\n    // fires twice on some browsers. See https://github.com/angular/angular/issues/24084\n\n    this._bodyAnimationDone.pipe(distinctUntilChanged((x, y) => {\n      return x.fromState === y.fromState && x.toState === y.toState;\n    })).subscribe(event => {\n      if (event.fromState !== 'void') {\n        if (event.toState === 'expanded') {\n          this.afterExpand.emit();\n        } else if (event.toState === 'collapsed') {\n          this.afterCollapse.emit();\n        }\n      }\n    });\n\n    if (defaultOptions) {\n      this.hideToggle = defaultOptions.hideToggle;\n    }\n  }\n  /** Whether the toggle indicator should be hidden. */\n\n\n  get hideToggle() {\n    return this._hideToggle || this.accordion && this.accordion.hideToggle;\n  }\n\n  set hideToggle(value) {\n    this._hideToggle = coerceBooleanProperty(value);\n  }\n  /** The position of the expansion indicator. */\n\n\n  get togglePosition() {\n    return this._togglePosition || this.accordion && this.accordion.togglePosition;\n  }\n\n  set togglePosition(value) {\n    this._togglePosition = value;\n  }\n  /** Determines whether the expansion panel should have spacing between it and its siblings. */\n\n\n  _hasSpacing() {\n    if (this.accordion) {\n      return this.expanded && this.accordion.displayMode === 'default';\n    }\n\n    return false;\n  }\n  /** Gets the expanded state string. */\n\n\n  _getExpandedState() {\n    return this.expanded ? 'expanded' : 'collapsed';\n  }\n  /** Toggles the expanded state of the expansion panel. */\n\n\n  toggle() {\n    this.expanded = !this.expanded;\n  }\n  /** Sets the expanded state of the expansion panel to false. */\n\n\n  close() {\n    this.expanded = false;\n  }\n  /** Sets the expanded state of the expansion panel to true. */\n\n\n  open() {\n    this.expanded = true;\n  }\n\n  ngAfterContentInit() {\n    if (this._lazyContent && this._lazyContent._expansionPanel === this) {\n      // Render the content as soon as the panel becomes open.\n      this.opened.pipe(startWith(null), filter(() => this.expanded && !this._portal), take(1)).subscribe(() => {\n        this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef);\n      });\n    }\n  }\n\n  ngOnChanges(changes) {\n    this._inputChanges.next(changes);\n  }\n\n  ngOnDestroy() {\n    super.ngOnDestroy();\n\n    this._bodyAnimationDone.complete();\n\n    this._inputChanges.complete();\n  }\n  /** Checks whether the expansion panel's content contains the currently-focused element. */\n\n\n  _containsFocus() {\n    if (this._body) {\n      const focusedElement = this._document.activeElement;\n      const bodyElement = this._body.nativeElement;\n      return focusedElement === bodyElement || bodyElement.contains(focusedElement);\n    }\n\n    return false;\n  }\n\n}\n\nMatExpansionPanel.ɵfac = function MatExpansionPanel_Factory(t) {\n  return new (t || MatExpansionPanel)(i0.ɵɵdirectiveInject(MAT_ACCORDION, 12), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.UniqueSelectionDispatcher), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, 8));\n};\n\nMatExpansionPanel.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatExpansionPanel,\n  selectors: [[\"mat-expansion-panel\"]],\n  contentQueries: function MatExpansionPanel_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatExpansionPanelContent, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lazyContent = _t.first);\n    }\n  },\n  viewQuery: function MatExpansionPanel_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._body = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-expansion-panel\"],\n  hostVars: 6,\n  hostBindings: function MatExpansionPanel_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-expanded\", ctx.expanded)(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-expansion-panel-spacing\", ctx._hasSpacing());\n    }\n  },\n  inputs: {\n    disabled: \"disabled\",\n    expanded: \"expanded\",\n    hideToggle: \"hideToggle\",\n    togglePosition: \"togglePosition\"\n  },\n  outputs: {\n    opened: \"opened\",\n    closed: \"closed\",\n    expandedChange: \"expandedChange\",\n    afterExpand: \"afterExpand\",\n    afterCollapse: \"afterCollapse\"\n  },\n  exportAs: [\"matExpansionPanel\"],\n  features: [i0.ɵɵProvidersFeature([// Provide MatAccordion as undefined to prevent nested expansion panels from registering\n  // to the same accordion.\n  {\n    provide: MAT_ACCORDION,\n    useValue: undefined\n  }, {\n    provide: MAT_EXPANSION_PANEL,\n    useExisting: MatExpansionPanel\n  }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n  ngContentSelectors: _c2,\n  decls: 7,\n  vars: 4,\n  consts: [[\"role\", \"region\", 1, \"mat-expansion-panel-content\", 3, \"id\"], [\"body\", \"\"], [1, \"mat-expansion-panel-body\"], [3, \"cdkPortalOutlet\"]],\n  template: function MatExpansionPanel_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c1);\n      i0.ɵɵprojection(0);\n      i0.ɵɵelementStart(1, \"div\", 0, 1);\n      i0.ɵɵlistener(\"@bodyExpansion.done\", function MatExpansionPanel_Template_div_animation_bodyExpansion_done_1_listener($event) {\n        return ctx._bodyAnimationDone.next($event);\n      });\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵprojection(4, 1);\n      i0.ɵɵtemplate(5, MatExpansionPanel_ng_template_5_Template, 0, 0, \"ng-template\", 3);\n      i0.ɵɵelementEnd();\n      i0.ɵɵprojection(6, 2);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"@bodyExpansion\", ctx._getExpandedState())(\"id\", ctx.id);\n      i0.ɵɵattribute(\"aria-labelledby\", ctx._headerId);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"cdkPortalOutlet\", ctx._portal);\n    }\n  },\n  dependencies: [i2.CdkPortalOutlet],\n  styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;border-radius:4px;overflow:hidden;transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);position:relative}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:4px;border-top-left-radius:4px}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:4px;border-bottom-left-radius:4px}.cdk-high-contrast-active .mat-expansion-panel{outline:solid 1px}.mat-expansion-panel.ng-animate-disabled,.ng-animate-disabled .mat-expansion-panel,.mat-expansion-panel._mat-animation-noopable{transition:none}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible}.mat-expansion-panel-content[style*=\\\"visibility: hidden\\\"] *{visibility:hidden !important}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\"],\n  encapsulation: 2,\n  data: {\n    animation: [matExpansionAnimations.bodyExpansion]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanel, [{\n    type: Component,\n    args: [{\n      selector: 'mat-expansion-panel',\n      exportAs: 'matExpansionPanel',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      inputs: ['disabled', 'expanded'],\n      outputs: ['opened', 'closed', 'expandedChange'],\n      animations: [matExpansionAnimations.bodyExpansion],\n      providers: [// Provide MatAccordion as undefined to prevent nested expansion panels from registering\n      // to the same accordion.\n      {\n        provide: MAT_ACCORDION,\n        useValue: undefined\n      }, {\n        provide: MAT_EXPANSION_PANEL,\n        useExisting: MatExpansionPanel\n      }],\n      host: {\n        'class': 'mat-expansion-panel',\n        '[class.mat-expanded]': 'expanded',\n        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n        '[class.mat-expansion-panel-spacing]': '_hasSpacing()'\n      },\n      template: \"<ng-content select=\\\"mat-expansion-panel-header\\\"></ng-content>\\n<div class=\\\"mat-expansion-panel-content\\\"\\n     role=\\\"region\\\"\\n     [@bodyExpansion]=\\\"_getExpandedState()\\\"\\n     (@bodyExpansion.done)=\\\"_bodyAnimationDone.next($event)\\\"\\n     [attr.aria-labelledby]=\\\"_headerId\\\"\\n     [id]=\\\"id\\\"\\n     #body>\\n  <div class=\\\"mat-expansion-panel-body\\\">\\n    <ng-content></ng-content>\\n    <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n  </div>\\n  <ng-content select=\\\"mat-action-row\\\"></ng-content>\\n</div>\\n\",\n      styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;border-radius:4px;overflow:hidden;transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);position:relative}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:4px;border-top-left-radius:4px}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:4px;border-bottom-left-radius:4px}.cdk-high-contrast-active .mat-expansion-panel{outline:solid 1px}.mat-expansion-panel.ng-animate-disabled,.ng-animate-disabled .mat-expansion-panel,.mat-expansion-panel._mat-animation-noopable{transition:none}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible}.mat-expansion-panel-content[style*=\\\"visibility: hidden\\\"] *{visibility:hidden !important}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\"]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }, {\n        type: Inject,\n        args: [MAT_ACCORDION]\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1.UniqueSelectionDispatcher\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_EXPANSION_PANEL_DEFAULT_OPTIONS]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, {\n    hideToggle: [{\n      type: Input\n    }],\n    togglePosition: [{\n      type: Input\n    }],\n    afterExpand: [{\n      type: Output\n    }],\n    afterCollapse: [{\n      type: Output\n    }],\n    _lazyContent: [{\n      type: ContentChild,\n      args: [MatExpansionPanelContent]\n    }],\n    _body: [{\n      type: ViewChild,\n      args: ['body']\n    }]\n  });\n})();\n/**\n * Actions of a `<mat-expansion-panel>`.\n */\n\n\nclass MatExpansionPanelActionRow {}\n\nMatExpansionPanelActionRow.ɵfac = function MatExpansionPanelActionRow_Factory(t) {\n  return new (t || MatExpansionPanelActionRow)();\n};\n\nMatExpansionPanelActionRow.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatExpansionPanelActionRow,\n  selectors: [[\"mat-action-row\"]],\n  hostAttrs: [1, \"mat-action-row\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelActionRow, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-action-row',\n      host: {\n        class: 'mat-action-row'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatExpansionPanelHeader.\n\n/** @docs-private */\n\n\nclass MatExpansionPanelHeaderBase {}\n\nconst _MatExpansionPanelHeaderMixinBase = mixinTabIndex(MatExpansionPanelHeaderBase);\n/**\n * Header element of a `<mat-expansion-panel>`.\n */\n\n\nclass MatExpansionPanelHeader extends _MatExpansionPanelHeaderMixinBase {\n  constructor(panel, _element, _focusMonitor, _changeDetectorRef, defaultOptions, _animationMode, tabIndex) {\n    super();\n    this.panel = panel;\n    this._element = _element;\n    this._focusMonitor = _focusMonitor;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._animationMode = _animationMode;\n    this._parentChangeSubscription = Subscription.EMPTY;\n    const accordionHideToggleChange = panel.accordion ? panel.accordion._stateChanges.pipe(filter(changes => !!(changes['hideToggle'] || changes['togglePosition']))) : EMPTY;\n    this.tabIndex = parseInt(tabIndex || '') || 0; // Since the toggle state depends on an @Input on the panel, we\n    // need to subscribe and trigger change detection manually.\n\n    this._parentChangeSubscription = merge(panel.opened, panel.closed, accordionHideToggleChange, panel._inputChanges.pipe(filter(changes => {\n      return !!(changes['hideToggle'] || changes['disabled'] || changes['togglePosition']);\n    }))).subscribe(() => this._changeDetectorRef.markForCheck()); // Avoids focus being lost if the panel contained the focused element and was closed.\n\n    panel.closed.pipe(filter(() => panel._containsFocus())).subscribe(() => _focusMonitor.focusVia(_element, 'program'));\n\n    if (defaultOptions) {\n      this.expandedHeight = defaultOptions.expandedHeight;\n      this.collapsedHeight = defaultOptions.collapsedHeight;\n    }\n  }\n  /**\n   * Whether the associated panel is disabled. Implemented as a part of `FocusableOption`.\n   * @docs-private\n   */\n\n\n  get disabled() {\n    return this.panel.disabled;\n  }\n  /** Toggles the expanded state of the panel. */\n\n\n  _toggle() {\n    if (!this.disabled) {\n      this.panel.toggle();\n    }\n  }\n  /** Gets whether the panel is expanded. */\n\n\n  _isExpanded() {\n    return this.panel.expanded;\n  }\n  /** Gets the expanded state string of the panel. */\n\n\n  _getExpandedState() {\n    return this.panel._getExpandedState();\n  }\n  /** Gets the panel id. */\n\n\n  _getPanelId() {\n    return this.panel.id;\n  }\n  /** Gets the toggle position for the header. */\n\n\n  _getTogglePosition() {\n    return this.panel.togglePosition;\n  }\n  /** Gets whether the expand indicator should be shown. */\n\n\n  _showToggle() {\n    return !this.panel.hideToggle && !this.panel.disabled;\n  }\n  /**\n   * Gets the current height of the header. Null if no custom height has been\n   * specified, and if the default height from the stylesheet should be used.\n   */\n\n\n  _getHeaderHeight() {\n    const isExpanded = this._isExpanded();\n\n    if (isExpanded && this.expandedHeight) {\n      return this.expandedHeight;\n    } else if (!isExpanded && this.collapsedHeight) {\n      return this.collapsedHeight;\n    }\n\n    return null;\n  }\n  /** Handle keydown event calling to toggle() if appropriate. */\n\n\n  _keydown(event) {\n    switch (event.keyCode) {\n      // Toggle for space and enter keys.\n      case SPACE:\n      case ENTER:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n\n          this._toggle();\n        }\n\n        break;\n\n      default:\n        if (this.panel.accordion) {\n          this.panel.accordion._handleHeaderKeydown(event);\n        }\n\n        return;\n    }\n  }\n  /**\n   * Focuses the panel header. Implemented as a part of `FocusableOption`.\n   * @param origin Origin of the action that triggered the focus.\n   * @docs-private\n   */\n\n\n  focus(origin, options) {\n    if (origin) {\n      this._focusMonitor.focusVia(this._element, origin, options);\n    } else {\n      this._element.nativeElement.focus(options);\n    }\n  }\n\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._element).subscribe(origin => {\n      if (origin && this.panel.accordion) {\n        this.panel.accordion._handleHeaderFocus(this);\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    this._parentChangeSubscription.unsubscribe();\n\n    this._focusMonitor.stopMonitoring(this._element);\n  }\n\n}\n\nMatExpansionPanelHeader.ɵfac = function MatExpansionPanelHeader_Factory(t) {\n  return new (t || MatExpansionPanelHeader)(i0.ɵɵdirectiveInject(MatExpansionPanel, 1), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2$1.FocusMonitor), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵinjectAttribute('tabindex'));\n};\n\nMatExpansionPanelHeader.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatExpansionPanelHeader,\n  selectors: [[\"mat-expansion-panel-header\"]],\n  hostAttrs: [\"role\", \"button\", 1, \"mat-expansion-panel-header\", \"mat-focus-indicator\"],\n  hostVars: 15,\n  hostBindings: function MatExpansionPanelHeader_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function MatExpansionPanelHeader_click_HostBindingHandler() {\n        return ctx._toggle();\n      })(\"keydown\", function MatExpansionPanelHeader_keydown_HostBindingHandler($event) {\n        return ctx._keydown($event);\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵattribute(\"id\", ctx.panel._headerId)(\"tabindex\", ctx.tabIndex)(\"aria-controls\", ctx._getPanelId())(\"aria-expanded\", ctx._isExpanded())(\"aria-disabled\", ctx.panel.disabled);\n      i0.ɵɵstyleProp(\"height\", ctx._getHeaderHeight());\n      i0.ɵɵclassProp(\"mat-expanded\", ctx._isExpanded())(\"mat-expansion-toggle-indicator-after\", ctx._getTogglePosition() === \"after\")(\"mat-expansion-toggle-indicator-before\", ctx._getTogglePosition() === \"before\")(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n    }\n  },\n  inputs: {\n    tabIndex: \"tabIndex\",\n    expandedHeight: \"expandedHeight\",\n    collapsedHeight: \"collapsedHeight\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c4,\n  decls: 5,\n  vars: 3,\n  consts: [[1, \"mat-content\"], [\"class\", \"mat-expansion-indicator\", 4, \"ngIf\"], [1, \"mat-expansion-indicator\"]],\n  template: function MatExpansionPanelHeader_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c3);\n      i0.ɵɵelementStart(0, \"span\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵprojection(2, 1);\n      i0.ɵɵprojection(3, 2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(4, MatExpansionPanelHeader_span_4_Template, 1, 1, \"span\", 1);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-content-hide-toggle\", !ctx._showToggle());\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngIf\", ctx._showToggle());\n    }\n  },\n  dependencies: [i3.NgIf],\n  styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header-description{flex-grow:2}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle}.cdk-high-contrast-active .mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}\"],\n  encapsulation: 2,\n  data: {\n    animation: [matExpansionAnimations.indicatorRotate]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-expansion-panel-header',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      inputs: ['tabIndex'],\n      animations: [matExpansionAnimations.indicatorRotate],\n      host: {\n        'class': 'mat-expansion-panel-header mat-focus-indicator',\n        'role': 'button',\n        '[attr.id]': 'panel._headerId',\n        '[attr.tabindex]': 'tabIndex',\n        '[attr.aria-controls]': '_getPanelId()',\n        '[attr.aria-expanded]': '_isExpanded()',\n        '[attr.aria-disabled]': 'panel.disabled',\n        '[class.mat-expanded]': '_isExpanded()',\n        '[class.mat-expansion-toggle-indicator-after]': `_getTogglePosition() === 'after'`,\n        '[class.mat-expansion-toggle-indicator-before]': `_getTogglePosition() === 'before'`,\n        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n        '[style.height]': '_getHeaderHeight()',\n        '(click)': '_toggle()',\n        '(keydown)': '_keydown($event)'\n      },\n      template: \"<span class=\\\"mat-content\\\" [class.mat-content-hide-toggle]=\\\"!_showToggle()\\\">\\n  <ng-content select=\\\"mat-panel-title\\\"></ng-content>\\n  <ng-content select=\\\"mat-panel-description\\\"></ng-content>\\n  <ng-content></ng-content>\\n</span>\\n<span [@indicatorRotate]=\\\"_getExpandedState()\\\" *ngIf=\\\"_showToggle()\\\"\\n      class=\\\"mat-expansion-indicator\\\"></span>\\n\",\n      styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header-description{flex-grow:2}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle}.cdk-high-contrast-active .mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}\"]\n    }]\n  }], function () {\n    return [{\n      type: MatExpansionPanel,\n      decorators: [{\n        type: Host\n      }]\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i2$1.FocusMonitor\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_EXPANSION_PANEL_DEFAULT_OPTIONS]\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['tabindex']\n      }]\n    }];\n  }, {\n    expandedHeight: [{\n      type: Input\n    }],\n    collapsedHeight: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Description element of a `<mat-expansion-panel-header>`.\n */\n\n\nclass MatExpansionPanelDescription {}\n\nMatExpansionPanelDescription.ɵfac = function MatExpansionPanelDescription_Factory(t) {\n  return new (t || MatExpansionPanelDescription)();\n};\n\nMatExpansionPanelDescription.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatExpansionPanelDescription,\n  selectors: [[\"mat-panel-description\"]],\n  hostAttrs: [1, \"mat-expansion-panel-header-description\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelDescription, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-panel-description',\n      host: {\n        class: 'mat-expansion-panel-header-description'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Title element of a `<mat-expansion-panel-header>`.\n */\n\n\nclass MatExpansionPanelTitle {}\n\nMatExpansionPanelTitle.ɵfac = function MatExpansionPanelTitle_Factory(t) {\n  return new (t || MatExpansionPanelTitle)();\n};\n\nMatExpansionPanelTitle.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatExpansionPanelTitle,\n  selectors: [[\"mat-panel-title\"]],\n  hostAttrs: [1, \"mat-expansion-panel-header-title\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelTitle, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-panel-title',\n      host: {\n        class: 'mat-expansion-panel-header-title'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Directive for a Material Design Accordion.\n */\n\n\nclass MatAccordion extends CdkAccordion {\n  constructor() {\n    super(...arguments);\n    /** Headers belonging to this accordion. */\n\n    this._ownHeaders = new QueryList();\n    this._hideToggle = false;\n    /**\n     * Display mode used for all expansion panels in the accordion. Currently two display\n     * modes exist:\n     *  default - a gutter-like spacing is placed around any expanded panel, placing the expanded\n     *     panel at a different elevation from the rest of the accordion.\n     *  flat - no spacing is placed around expanded panels, showing all panels at the same\n     *     elevation.\n     */\n\n    this.displayMode = 'default';\n    /** The position of the expansion indicator. */\n\n    this.togglePosition = 'after';\n  }\n  /** Whether the expansion indicator should be hidden. */\n\n\n  get hideToggle() {\n    return this._hideToggle;\n  }\n\n  set hideToggle(show) {\n    this._hideToggle = coerceBooleanProperty(show);\n  }\n\n  ngAfterContentInit() {\n    this._headers.changes.pipe(startWith(this._headers)).subscribe(headers => {\n      this._ownHeaders.reset(headers.filter(header => header.panel.accordion === this));\n\n      this._ownHeaders.notifyOnChanges();\n    });\n\n    this._keyManager = new FocusKeyManager(this._ownHeaders).withWrap().withHomeAndEnd();\n  }\n  /** Handles keyboard events coming in from the panel headers. */\n\n\n  _handleHeaderKeydown(event) {\n    this._keyManager.onKeydown(event);\n  }\n\n  _handleHeaderFocus(header) {\n    this._keyManager.updateActiveItem(header);\n  }\n\n  ngOnDestroy() {\n    super.ngOnDestroy();\n\n    this._ownHeaders.destroy();\n  }\n\n}\n\nMatAccordion.ɵfac = /* @__PURE__ */function () {\n  let ɵMatAccordion_BaseFactory;\n  return function MatAccordion_Factory(t) {\n    return (ɵMatAccordion_BaseFactory || (ɵMatAccordion_BaseFactory = i0.ɵɵgetInheritedFactory(MatAccordion)))(t || MatAccordion);\n  };\n}();\n\nMatAccordion.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatAccordion,\n  selectors: [[\"mat-accordion\"]],\n  contentQueries: function MatAccordion_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatExpansionPanelHeader, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._headers = _t);\n    }\n  },\n  hostAttrs: [1, \"mat-accordion\"],\n  hostVars: 2,\n  hostBindings: function MatAccordion_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-accordion-multi\", ctx.multi);\n    }\n  },\n  inputs: {\n    multi: \"multi\",\n    hideToggle: \"hideToggle\",\n    displayMode: \"displayMode\",\n    togglePosition: \"togglePosition\"\n  },\n  exportAs: [\"matAccordion\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_ACCORDION,\n    useExisting: MatAccordion\n  }]), i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAccordion, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-accordion',\n      exportAs: 'matAccordion',\n      inputs: ['multi'],\n      providers: [{\n        provide: MAT_ACCORDION,\n        useExisting: MatAccordion\n      }],\n      host: {\n        class: 'mat-accordion',\n        // Class binding which is only used by the test harness as there is no other\n        // way for the harness to detect if multiple panel support is enabled.\n        '[class.mat-accordion-multi]': 'this.multi'\n      }\n    }]\n  }], null, {\n    _headers: [{\n      type: ContentChildren,\n      args: [MatExpansionPanelHeader, {\n        descendants: true\n      }]\n    }],\n    hideToggle: [{\n      type: Input\n    }],\n    displayMode: [{\n      type: Input\n    }],\n    togglePosition: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatExpansionModule {}\n\nMatExpansionModule.ɵfac = function MatExpansionModule_Factory(t) {\n  return new (t || MatExpansionModule)();\n};\n\nMatExpansionModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatExpansionModule,\n  declarations: [MatAccordion, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, MatExpansionPanelContent],\n  imports: [CommonModule, MatCommonModule, CdkAccordionModule, PortalModule],\n  exports: [MatAccordion, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, MatExpansionPanelContent]\n});\nMatExpansionModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, MatCommonModule, CdkAccordionModule, PortalModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, MatCommonModule, CdkAccordionModule, PortalModule],\n      exports: [MatAccordion, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, MatExpansionPanelContent],\n      declarations: [MatAccordion, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, MatExpansionPanelContent]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { EXPANSION_PANEL_ANIMATION_TIMING, MAT_ACCORDION, MAT_EXPANSION_PANEL, MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, MatAccordion, MatExpansionModule, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelContent, MatExpansionPanelDescription, MatExpansionPanelHeader, MatExpansionPanelTitle, matExpansionAnimations };", "map": {"version": 3, "names": ["CdkAccordionItem", "CdkAccordion", "CdkAccordionModule", "i2", "TemplatePortal", "PortalModule", "i3", "DOCUMENT", "CommonModule", "i0", "InjectionToken", "Directive", "Inject", "Optional", "EventEmitter", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "SkipSelf", "Input", "Output", "ContentChild", "ViewChild", "Host", "Attribute", "QueryList", "ContentChildren", "NgModule", "mixinTabIndex", "MatCommonModule", "coerceBooleanProperty", "i2$1", "FocusKeyManager", "distinctUntilChanged", "startWith", "filter", "take", "ENTER", "hasModifierKey", "SPACE", "ANIMATION_MODULE_TYPE", "Subject", "Subscription", "EMPTY", "merge", "trigger", "state", "style", "transition", "animate", "i1", "MAT_ACCORDION", "EXPANSION_PANEL_ANIMATION_TIMING", "matExpansionAnimations", "indicatorRotate", "transform", "bodyExpansion", "height", "visibility", "MAT_EXPANSION_PANEL", "MatExpansionPanelContent", "constructor", "_template", "_expansionPanel", "ɵfac", "TemplateRef", "ɵdir", "type", "args", "selector", "undefined", "decorators", "uniqueId", "MAT_EXPANSION_PANEL_DEFAULT_OPTIONS", "MatExpansionPanel", "accordion", "_changeDetectorRef", "_uniqueSelectionDispatcher", "_viewContainerRef", "_document", "_animationMode", "defaultOptions", "_hideToggle", "afterExpand", "afterCollapse", "_inputChanges", "_headerId", "_bodyAnimationDone", "pipe", "x", "y", "fromState", "toState", "subscribe", "event", "emit", "hideToggle", "value", "togglePosition", "_togglePosition", "_hasSpacing", "expanded", "displayMode", "_getExpandedState", "toggle", "close", "open", "ngAfterContentInit", "_lazyContent", "opened", "_portal", "ngOnChanges", "changes", "next", "ngOnDestroy", "complete", "_containsFocus", "_body", "focusedElement", "activeElement", "bodyElement", "nativeElement", "contains", "ChangeDetectorRef", "UniqueSelectionDispatcher", "ViewContainerRef", "ɵcmp", "provide", "useValue", "useExisting", "CdkPortalOutlet", "exportAs", "encapsulation", "None", "changeDetection", "OnPush", "inputs", "outputs", "animations", "providers", "host", "template", "styles", "MatExpansionPanelActionRow", "class", "MatExpansionPanelHeaderBase", "_MatExpansionPanelHeaderMixinBase", "MatExpansionPanelHeader", "panel", "_element", "_focusMonitor", "tabIndex", "_parentChangeSubscription", "accordionHideToggleChange", "_stateChanges", "parseInt", "closed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "focusVia", "expandedHeight", "collapsedHeight", "disabled", "_toggle", "_isExpanded", "_getPanelId", "id", "_getTogglePosition", "_showToggle", "_getHeaderHeight", "isExpanded", "_keydown", "keyCode", "preventDefault", "_handleHeaderKeydown", "focus", "origin", "options", "ngAfterViewInit", "monitor", "_handleHeaderFocus", "unsubscribe", "stopMonitoring", "ElementRef", "FocusMonitor", "NgIf", "MatExpansionPanelDescription", "MatExpansionPanelTitle", "Mat<PERSON><PERSON>rdi<PERSON>", "arguments", "_ownHeaders", "show", "_headers", "headers", "reset", "header", "notifyOn<PERSON><PERSON>es", "_keyManager", "withWrap", "withHomeAndEnd", "onKeydown", "updateActiveItem", "destroy", "descendants", "MatExpansionModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/material/fesm2020/expansion.mjs"], "sourcesContent": ["import { CdkAccordionItem, CdkAccordion, CdkAccordionModule } from '@angular/cdk/accordion';\nimport * as i2 from '@angular/cdk/portal';\nimport { TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, SkipSelf, Input, Output, ContentChild, ViewChild, Host, Attribute, QueryList, ContentChildren, NgModule } from '@angular/core';\nimport { mixinTabIndex, MatCommonModule } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i2$1 from '@angular/cdk/a11y';\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport { distinctUntilChanged, startWith, filter, take } from 'rxjs/operators';\nimport { ENTER, hasModifierKey, SPACE } from '@angular/cdk/keycodes';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { Subject, Subscription, EMPTY, merge } from 'rxjs';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/cdk/collections';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Token used to provide a `MatAccordion` to `MatExpansionPanel`.\n * Used primarily to avoid circular imports between `MatAccordion` and `MatExpansionPanel`.\n */\nconst MAT_ACCORDION = new InjectionToken('MAT_ACCORDION');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Time and timing curve for expansion panel animations. */\n// Note: Keep this in sync with the Sass variable for the panel header animation.\nconst EXPANSION_PANEL_ANIMATION_TIMING = '225ms cubic-bezier(0.4,0.0,0.2,1)';\n/**\n * Animations used by the Material expansion panel.\n *\n * A bug in angular animation's `state` when ViewContainers are moved using ViewContainerRef.move()\n * causes the animation state of moved components to become `void` upon exit, and not update again\n * upon reentry into the DOM.  This can lead a to situation for the expansion panel where the state\n * of the panel is `expanded` or `collapsed` but the animation state is `void`.\n *\n * To correctly handle animating to the next state, we animate between `void` and `collapsed` which\n * are defined to have the same styles. Since angular animates from the current styles to the\n * destination state's style definition, in situations where we are moving from `void`'s styles to\n * `collapsed` this acts a noop since no style values change.\n *\n * In the case where angular's animation state is out of sync with the expansion panel's state, the\n * expansion panel being `expanded` and angular animations being `void`, the animation from the\n * `expanded`'s effective styles (though in a `void` animation state) to the collapsed state will\n * occur as expected.\n *\n * Angular Bug: https://github.com/angular/angular/issues/18847\n *\n * @docs-private\n */\nconst matExpansionAnimations = {\n    /** Animation that rotates the indicator arrow. */\n    indicatorRotate: trigger('indicatorRotate', [\n        state('collapsed, void', style({ transform: 'rotate(0deg)' })),\n        state('expanded', style({ transform: 'rotate(180deg)' })),\n        transition('expanded <=> collapsed, void => collapsed', animate(EXPANSION_PANEL_ANIMATION_TIMING)),\n    ]),\n    /** Animation that expands and collapses the panel content. */\n    bodyExpansion: trigger('bodyExpansion', [\n        state('collapsed, void', style({ height: '0px', visibility: 'hidden' })),\n        state('expanded', style({ height: '*', visibility: 'visible' })),\n        transition('expanded <=> collapsed, void => collapsed', animate(EXPANSION_PANEL_ANIMATION_TIMING)),\n    ]),\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Token used to provide a `MatExpansionPanel` to `MatExpansionPanelContent`.\n * Used to avoid circular imports between `MatExpansionPanel` and `MatExpansionPanelContent`.\n */\nconst MAT_EXPANSION_PANEL = new InjectionToken('MAT_EXPANSION_PANEL');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Expansion panel content that will be rendered lazily\n * after the panel is opened for the first time.\n */\nclass MatExpansionPanelContent {\n    constructor(_template, _expansionPanel) {\n        this._template = _template;\n        this._expansionPanel = _expansionPanel;\n    }\n}\nMatExpansionPanelContent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatExpansionPanelContent, deps: [{ token: i0.TemplateRef }, { token: MAT_EXPANSION_PANEL, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nMatExpansionPanelContent.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatExpansionPanelContent, selector: \"ng-template[matExpansionPanelContent]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatExpansionPanelContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[matExpansionPanelContent]',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_EXPANSION_PANEL]\n                }, {\n                    type: Optional\n                }] }]; } });\n\n/** Counter for generating unique element ids. */\nlet uniqueId = 0;\n/**\n * Injection token that can be used to configure the default\n * options for the expansion panel component.\n */\nconst MAT_EXPANSION_PANEL_DEFAULT_OPTIONS = new InjectionToken('MAT_EXPANSION_PANEL_DEFAULT_OPTIONS');\n/**\n * This component can be used as a single element to show expandable content, or as one of\n * multiple children of an element with the MatAccordion directive attached.\n */\nclass MatExpansionPanel extends CdkAccordionItem {\n    constructor(accordion, _changeDetectorRef, _uniqueSelectionDispatcher, _viewContainerRef, _document, _animationMode, defaultOptions) {\n        super(accordion, _changeDetectorRef, _uniqueSelectionDispatcher);\n        this._viewContainerRef = _viewContainerRef;\n        this._animationMode = _animationMode;\n        this._hideToggle = false;\n        /** An event emitted after the body's expansion animation happens. */\n        this.afterExpand = new EventEmitter();\n        /** An event emitted after the body's collapse animation happens. */\n        this.afterCollapse = new EventEmitter();\n        /** Stream that emits for changes in `@Input` properties. */\n        this._inputChanges = new Subject();\n        /** ID for the associated header element. Used for a11y labelling. */\n        this._headerId = `mat-expansion-panel-header-${uniqueId++}`;\n        /** Stream of body animation done events. */\n        this._bodyAnimationDone = new Subject();\n        this.accordion = accordion;\n        this._document = _document;\n        // We need a Subject with distinctUntilChanged, because the `done` event\n        // fires twice on some browsers. See https://github.com/angular/angular/issues/24084\n        this._bodyAnimationDone\n            .pipe(distinctUntilChanged((x, y) => {\n            return x.fromState === y.fromState && x.toState === y.toState;\n        }))\n            .subscribe(event => {\n            if (event.fromState !== 'void') {\n                if (event.toState === 'expanded') {\n                    this.afterExpand.emit();\n                }\n                else if (event.toState === 'collapsed') {\n                    this.afterCollapse.emit();\n                }\n            }\n        });\n        if (defaultOptions) {\n            this.hideToggle = defaultOptions.hideToggle;\n        }\n    }\n    /** Whether the toggle indicator should be hidden. */\n    get hideToggle() {\n        return this._hideToggle || (this.accordion && this.accordion.hideToggle);\n    }\n    set hideToggle(value) {\n        this._hideToggle = coerceBooleanProperty(value);\n    }\n    /** The position of the expansion indicator. */\n    get togglePosition() {\n        return this._togglePosition || (this.accordion && this.accordion.togglePosition);\n    }\n    set togglePosition(value) {\n        this._togglePosition = value;\n    }\n    /** Determines whether the expansion panel should have spacing between it and its siblings. */\n    _hasSpacing() {\n        if (this.accordion) {\n            return this.expanded && this.accordion.displayMode === 'default';\n        }\n        return false;\n    }\n    /** Gets the expanded state string. */\n    _getExpandedState() {\n        return this.expanded ? 'expanded' : 'collapsed';\n    }\n    /** Toggles the expanded state of the expansion panel. */\n    toggle() {\n        this.expanded = !this.expanded;\n    }\n    /** Sets the expanded state of the expansion panel to false. */\n    close() {\n        this.expanded = false;\n    }\n    /** Sets the expanded state of the expansion panel to true. */\n    open() {\n        this.expanded = true;\n    }\n    ngAfterContentInit() {\n        if (this._lazyContent && this._lazyContent._expansionPanel === this) {\n            // Render the content as soon as the panel becomes open.\n            this.opened\n                .pipe(startWith(null), filter(() => this.expanded && !this._portal), take(1))\n                .subscribe(() => {\n                this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef);\n            });\n        }\n    }\n    ngOnChanges(changes) {\n        this._inputChanges.next(changes);\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._bodyAnimationDone.complete();\n        this._inputChanges.complete();\n    }\n    /** Checks whether the expansion panel's content contains the currently-focused element. */\n    _containsFocus() {\n        if (this._body) {\n            const focusedElement = this._document.activeElement;\n            const bodyElement = this._body.nativeElement;\n            return focusedElement === bodyElement || bodyElement.contains(focusedElement);\n        }\n        return false;\n    }\n}\nMatExpansionPanel.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatExpansionPanel, deps: [{ token: MAT_ACCORDION, optional: true, skipSelf: true }, { token: i0.ChangeDetectorRef }, { token: i1.UniqueSelectionDispatcher }, { token: i0.ViewContainerRef }, { token: DOCUMENT }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatExpansionPanel.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatExpansionPanel, selector: \"mat-expansion-panel\", inputs: { disabled: \"disabled\", expanded: \"expanded\", hideToggle: \"hideToggle\", togglePosition: \"togglePosition\" }, outputs: { opened: \"opened\", closed: \"closed\", expandedChange: \"expandedChange\", afterExpand: \"afterExpand\", afterCollapse: \"afterCollapse\" }, host: { properties: { \"class.mat-expanded\": \"expanded\", \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\", \"class.mat-expansion-panel-spacing\": \"_hasSpacing()\" }, classAttribute: \"mat-expansion-panel\" }, providers: [\n        // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n        // to the same accordion.\n        { provide: MAT_ACCORDION, useValue: undefined },\n        { provide: MAT_EXPANSION_PANEL, useExisting: MatExpansionPanel },\n    ], queries: [{ propertyName: \"_lazyContent\", first: true, predicate: MatExpansionPanelContent, descendants: true }], viewQueries: [{ propertyName: \"_body\", first: true, predicate: [\"body\"], descendants: true }], exportAs: [\"matExpansionPanel\"], usesInheritance: true, usesOnChanges: true, ngImport: i0, template: \"<ng-content select=\\\"mat-expansion-panel-header\\\"></ng-content>\\n<div class=\\\"mat-expansion-panel-content\\\"\\n     role=\\\"region\\\"\\n     [@bodyExpansion]=\\\"_getExpandedState()\\\"\\n     (@bodyExpansion.done)=\\\"_bodyAnimationDone.next($event)\\\"\\n     [attr.aria-labelledby]=\\\"_headerId\\\"\\n     [id]=\\\"id\\\"\\n     #body>\\n  <div class=\\\"mat-expansion-panel-body\\\">\\n    <ng-content></ng-content>\\n    <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n  </div>\\n  <ng-content select=\\\"mat-action-row\\\"></ng-content>\\n</div>\\n\", styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;border-radius:4px;overflow:hidden;transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);position:relative}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:4px;border-top-left-radius:4px}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:4px;border-bottom-left-radius:4px}.cdk-high-contrast-active .mat-expansion-panel{outline:solid 1px}.mat-expansion-panel.ng-animate-disabled,.ng-animate-disabled .mat-expansion-panel,.mat-expansion-panel._mat-animation-noopable{transition:none}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible}.mat-expansion-panel-content[style*=\\\"visibility: hidden\\\"] *{visibility:hidden !important}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\"], dependencies: [{ kind: \"directive\", type: i2.CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], animations: [matExpansionAnimations.bodyExpansion], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatExpansionPanel, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-expansion-panel', exportAs: 'matExpansionPanel', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, inputs: ['disabled', 'expanded'], outputs: ['opened', 'closed', 'expandedChange'], animations: [matExpansionAnimations.bodyExpansion], providers: [\n                        // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n                        // to the same accordion.\n                        { provide: MAT_ACCORDION, useValue: undefined },\n                        { provide: MAT_EXPANSION_PANEL, useExisting: MatExpansionPanel },\n                    ], host: {\n                        'class': 'mat-expansion-panel',\n                        '[class.mat-expanded]': 'expanded',\n                        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                        '[class.mat-expansion-panel-spacing]': '_hasSpacing()',\n                    }, template: \"<ng-content select=\\\"mat-expansion-panel-header\\\"></ng-content>\\n<div class=\\\"mat-expansion-panel-content\\\"\\n     role=\\\"region\\\"\\n     [@bodyExpansion]=\\\"_getExpandedState()\\\"\\n     (@bodyExpansion.done)=\\\"_bodyAnimationDone.next($event)\\\"\\n     [attr.aria-labelledby]=\\\"_headerId\\\"\\n     [id]=\\\"id\\\"\\n     #body>\\n  <div class=\\\"mat-expansion-panel-body\\\">\\n    <ng-content></ng-content>\\n    <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n  </div>\\n  <ng-content select=\\\"mat-action-row\\\"></ng-content>\\n</div>\\n\", styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;border-radius:4px;overflow:hidden;transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);position:relative}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:4px;border-top-left-radius:4px}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:4px;border-bottom-left-radius:4px}.cdk-high-contrast-active .mat-expansion-panel{outline:solid 1px}.mat-expansion-panel.ng-animate-disabled,.ng-animate-disabled .mat-expansion-panel,.mat-expansion-panel._mat-animation-noopable{transition:none}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible}.mat-expansion-panel-content[style*=\\\"visibility: hidden\\\"] *{visibility:hidden !important}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\"] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }, {\n                    type: Inject,\n                    args: [MAT_ACCORDION]\n                }] }, { type: i0.ChangeDetectorRef }, { type: i1.UniqueSelectionDispatcher }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_EXPANSION_PANEL_DEFAULT_OPTIONS]\n                }, {\n                    type: Optional\n                }] }]; }, propDecorators: { hideToggle: [{\n                type: Input\n            }], togglePosition: [{\n                type: Input\n            }], afterExpand: [{\n                type: Output\n            }], afterCollapse: [{\n                type: Output\n            }], _lazyContent: [{\n                type: ContentChild,\n                args: [MatExpansionPanelContent]\n            }], _body: [{\n                type: ViewChild,\n                args: ['body']\n            }] } });\n/**\n * Actions of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelActionRow {\n}\nMatExpansionPanelActionRow.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatExpansionPanelActionRow, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatExpansionPanelActionRow.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatExpansionPanelActionRow, selector: \"mat-action-row\", host: { classAttribute: \"mat-action-row\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatExpansionPanelActionRow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-action-row',\n                    host: {\n                        class: 'mat-action-row',\n                    },\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatExpansionPanelHeader.\n/** @docs-private */\nclass MatExpansionPanelHeaderBase {\n}\nconst _MatExpansionPanelHeaderMixinBase = mixinTabIndex(MatExpansionPanelHeaderBase);\n/**\n * Header element of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelHeader extends _MatExpansionPanelHeaderMixinBase {\n    constructor(panel, _element, _focusMonitor, _changeDetectorRef, defaultOptions, _animationMode, tabIndex) {\n        super();\n        this.panel = panel;\n        this._element = _element;\n        this._focusMonitor = _focusMonitor;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._animationMode = _animationMode;\n        this._parentChangeSubscription = Subscription.EMPTY;\n        const accordionHideToggleChange = panel.accordion\n            ? panel.accordion._stateChanges.pipe(filter(changes => !!(changes['hideToggle'] || changes['togglePosition'])))\n            : EMPTY;\n        this.tabIndex = parseInt(tabIndex || '') || 0;\n        // Since the toggle state depends on an @Input on the panel, we\n        // need to subscribe and trigger change detection manually.\n        this._parentChangeSubscription = merge(panel.opened, panel.closed, accordionHideToggleChange, panel._inputChanges.pipe(filter(changes => {\n            return !!(changes['hideToggle'] || changes['disabled'] || changes['togglePosition']);\n        }))).subscribe(() => this._changeDetectorRef.markForCheck());\n        // Avoids focus being lost if the panel contained the focused element and was closed.\n        panel.closed\n            .pipe(filter(() => panel._containsFocus()))\n            .subscribe(() => _focusMonitor.focusVia(_element, 'program'));\n        if (defaultOptions) {\n            this.expandedHeight = defaultOptions.expandedHeight;\n            this.collapsedHeight = defaultOptions.collapsedHeight;\n        }\n    }\n    /**\n     * Whether the associated panel is disabled. Implemented as a part of `FocusableOption`.\n     * @docs-private\n     */\n    get disabled() {\n        return this.panel.disabled;\n    }\n    /** Toggles the expanded state of the panel. */\n    _toggle() {\n        if (!this.disabled) {\n            this.panel.toggle();\n        }\n    }\n    /** Gets whether the panel is expanded. */\n    _isExpanded() {\n        return this.panel.expanded;\n    }\n    /** Gets the expanded state string of the panel. */\n    _getExpandedState() {\n        return this.panel._getExpandedState();\n    }\n    /** Gets the panel id. */\n    _getPanelId() {\n        return this.panel.id;\n    }\n    /** Gets the toggle position for the header. */\n    _getTogglePosition() {\n        return this.panel.togglePosition;\n    }\n    /** Gets whether the expand indicator should be shown. */\n    _showToggle() {\n        return !this.panel.hideToggle && !this.panel.disabled;\n    }\n    /**\n     * Gets the current height of the header. Null if no custom height has been\n     * specified, and if the default height from the stylesheet should be used.\n     */\n    _getHeaderHeight() {\n        const isExpanded = this._isExpanded();\n        if (isExpanded && this.expandedHeight) {\n            return this.expandedHeight;\n        }\n        else if (!isExpanded && this.collapsedHeight) {\n            return this.collapsedHeight;\n        }\n        return null;\n    }\n    /** Handle keydown event calling to toggle() if appropriate. */\n    _keydown(event) {\n        switch (event.keyCode) {\n            // Toggle for space and enter keys.\n            case SPACE:\n            case ENTER:\n                if (!hasModifierKey(event)) {\n                    event.preventDefault();\n                    this._toggle();\n                }\n                break;\n            default:\n                if (this.panel.accordion) {\n                    this.panel.accordion._handleHeaderKeydown(event);\n                }\n                return;\n        }\n    }\n    /**\n     * Focuses the panel header. Implemented as a part of `FocusableOption`.\n     * @param origin Origin of the action that triggered the focus.\n     * @docs-private\n     */\n    focus(origin, options) {\n        if (origin) {\n            this._focusMonitor.focusVia(this._element, origin, options);\n        }\n        else {\n            this._element.nativeElement.focus(options);\n        }\n    }\n    ngAfterViewInit() {\n        this._focusMonitor.monitor(this._element).subscribe(origin => {\n            if (origin && this.panel.accordion) {\n                this.panel.accordion._handleHeaderFocus(this);\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._parentChangeSubscription.unsubscribe();\n        this._focusMonitor.stopMonitoring(this._element);\n    }\n}\nMatExpansionPanelHeader.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatExpansionPanelHeader, deps: [{ token: MatExpansionPanel, host: true }, { token: i0.ElementRef }, { token: i2$1.FocusMonitor }, { token: i0.ChangeDetectorRef }, { token: MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: 'tabindex', attribute: true }], target: i0.ɵɵFactoryTarget.Component });\nMatExpansionPanelHeader.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatExpansionPanelHeader, selector: \"mat-expansion-panel-header\", inputs: { tabIndex: \"tabIndex\", expandedHeight: \"expandedHeight\", collapsedHeight: \"collapsedHeight\" }, host: { attributes: { \"role\": \"button\" }, listeners: { \"click\": \"_toggle()\", \"keydown\": \"_keydown($event)\" }, properties: { \"attr.id\": \"panel._headerId\", \"attr.tabindex\": \"tabIndex\", \"attr.aria-controls\": \"_getPanelId()\", \"attr.aria-expanded\": \"_isExpanded()\", \"attr.aria-disabled\": \"panel.disabled\", \"class.mat-expanded\": \"_isExpanded()\", \"class.mat-expansion-toggle-indicator-after\": \"_getTogglePosition() === 'after'\", \"class.mat-expansion-toggle-indicator-before\": \"_getTogglePosition() === 'before'\", \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\", \"style.height\": \"_getHeaderHeight()\" }, classAttribute: \"mat-expansion-panel-header mat-focus-indicator\" }, usesInheritance: true, ngImport: i0, template: \"<span class=\\\"mat-content\\\" [class.mat-content-hide-toggle]=\\\"!_showToggle()\\\">\\n  <ng-content select=\\\"mat-panel-title\\\"></ng-content>\\n  <ng-content select=\\\"mat-panel-description\\\"></ng-content>\\n  <ng-content></ng-content>\\n</span>\\n<span [@indicatorRotate]=\\\"_getExpandedState()\\\" *ngIf=\\\"_showToggle()\\\"\\n      class=\\\"mat-expansion-indicator\\\"></span>\\n\", styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header-description{flex-grow:2}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle}.cdk-high-contrast-active .mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}\"], dependencies: [{ kind: \"directive\", type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], animations: [matExpansionAnimations.indicatorRotate], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatExpansionPanelHeader, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-expansion-panel-header', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, inputs: ['tabIndex'], animations: [matExpansionAnimations.indicatorRotate], host: {\n                        'class': 'mat-expansion-panel-header mat-focus-indicator',\n                        'role': 'button',\n                        '[attr.id]': 'panel._headerId',\n                        '[attr.tabindex]': 'tabIndex',\n                        '[attr.aria-controls]': '_getPanelId()',\n                        '[attr.aria-expanded]': '_isExpanded()',\n                        '[attr.aria-disabled]': 'panel.disabled',\n                        '[class.mat-expanded]': '_isExpanded()',\n                        '[class.mat-expansion-toggle-indicator-after]': `_getTogglePosition() === 'after'`,\n                        '[class.mat-expansion-toggle-indicator-before]': `_getTogglePosition() === 'before'`,\n                        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                        '[style.height]': '_getHeaderHeight()',\n                        '(click)': '_toggle()',\n                        '(keydown)': '_keydown($event)',\n                    }, template: \"<span class=\\\"mat-content\\\" [class.mat-content-hide-toggle]=\\\"!_showToggle()\\\">\\n  <ng-content select=\\\"mat-panel-title\\\"></ng-content>\\n  <ng-content select=\\\"mat-panel-description\\\"></ng-content>\\n  <ng-content></ng-content>\\n</span>\\n<span [@indicatorRotate]=\\\"_getExpandedState()\\\" *ngIf=\\\"_showToggle()\\\"\\n      class=\\\"mat-expansion-indicator\\\"></span>\\n\", styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header-description{flex-grow:2}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle}.cdk-high-contrast-active .mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}\"] }]\n        }], ctorParameters: function () { return [{ type: MatExpansionPanel, decorators: [{\n                    type: Host\n                }] }, { type: i0.ElementRef }, { type: i2$1.FocusMonitor }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_EXPANSION_PANEL_DEFAULT_OPTIONS]\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }]; }, propDecorators: { expandedHeight: [{\n                type: Input\n            }], collapsedHeight: [{\n                type: Input\n            }] } });\n/**\n * Description element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelDescription {\n}\nMatExpansionPanelDescription.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatExpansionPanelDescription, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatExpansionPanelDescription.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatExpansionPanelDescription, selector: \"mat-panel-description\", host: { classAttribute: \"mat-expansion-panel-header-description\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatExpansionPanelDescription, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-panel-description',\n                    host: {\n                        class: 'mat-expansion-panel-header-description',\n                    },\n                }]\n        }] });\n/**\n * Title element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelTitle {\n}\nMatExpansionPanelTitle.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatExpansionPanelTitle, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatExpansionPanelTitle.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatExpansionPanelTitle, selector: \"mat-panel-title\", host: { classAttribute: \"mat-expansion-panel-header-title\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatExpansionPanelTitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-panel-title',\n                    host: {\n                        class: 'mat-expansion-panel-header-title',\n                    },\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Directive for a Material Design Accordion.\n */\nclass MatAccordion extends CdkAccordion {\n    constructor() {\n        super(...arguments);\n        /** Headers belonging to this accordion. */\n        this._ownHeaders = new QueryList();\n        this._hideToggle = false;\n        /**\n         * Display mode used for all expansion panels in the accordion. Currently two display\n         * modes exist:\n         *  default - a gutter-like spacing is placed around any expanded panel, placing the expanded\n         *     panel at a different elevation from the rest of the accordion.\n         *  flat - no spacing is placed around expanded panels, showing all panels at the same\n         *     elevation.\n         */\n        this.displayMode = 'default';\n        /** The position of the expansion indicator. */\n        this.togglePosition = 'after';\n    }\n    /** Whether the expansion indicator should be hidden. */\n    get hideToggle() {\n        return this._hideToggle;\n    }\n    set hideToggle(show) {\n        this._hideToggle = coerceBooleanProperty(show);\n    }\n    ngAfterContentInit() {\n        this._headers.changes\n            .pipe(startWith(this._headers))\n            .subscribe((headers) => {\n            this._ownHeaders.reset(headers.filter(header => header.panel.accordion === this));\n            this._ownHeaders.notifyOnChanges();\n        });\n        this._keyManager = new FocusKeyManager(this._ownHeaders).withWrap().withHomeAndEnd();\n    }\n    /** Handles keyboard events coming in from the panel headers. */\n    _handleHeaderKeydown(event) {\n        this._keyManager.onKeydown(event);\n    }\n    _handleHeaderFocus(header) {\n        this._keyManager.updateActiveItem(header);\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._ownHeaders.destroy();\n    }\n}\nMatAccordion.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatAccordion, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatAccordion.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatAccordion, selector: \"mat-accordion\", inputs: { multi: \"multi\", hideToggle: \"hideToggle\", displayMode: \"displayMode\", togglePosition: \"togglePosition\" }, host: { properties: { \"class.mat-accordion-multi\": \"this.multi\" }, classAttribute: \"mat-accordion\" }, providers: [\n        {\n            provide: MAT_ACCORDION,\n            useExisting: MatAccordion,\n        },\n    ], queries: [{ propertyName: \"_headers\", predicate: MatExpansionPanelHeader, descendants: true }], exportAs: [\"matAccordion\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatAccordion, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-accordion',\n                    exportAs: 'matAccordion',\n                    inputs: ['multi'],\n                    providers: [\n                        {\n                            provide: MAT_ACCORDION,\n                            useExisting: MatAccordion,\n                        },\n                    ],\n                    host: {\n                        class: 'mat-accordion',\n                        // Class binding which is only used by the test harness as there is no other\n                        // way for the harness to detect if multiple panel support is enabled.\n                        '[class.mat-accordion-multi]': 'this.multi',\n                    },\n                }]\n        }], propDecorators: { _headers: [{\n                type: ContentChildren,\n                args: [MatExpansionPanelHeader, { descendants: true }]\n            }], hideToggle: [{\n                type: Input\n            }], displayMode: [{\n                type: Input\n            }], togglePosition: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatExpansionModule {\n}\nMatExpansionModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatExpansionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatExpansionModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: MatExpansionModule, declarations: [MatAccordion,\n        MatExpansionPanel,\n        MatExpansionPanelActionRow,\n        MatExpansionPanelHeader,\n        MatExpansionPanelTitle,\n        MatExpansionPanelDescription,\n        MatExpansionPanelContent], imports: [CommonModule, MatCommonModule, CdkAccordionModule, PortalModule], exports: [MatAccordion,\n        MatExpansionPanel,\n        MatExpansionPanelActionRow,\n        MatExpansionPanelHeader,\n        MatExpansionPanelTitle,\n        MatExpansionPanelDescription,\n        MatExpansionPanelContent] });\nMatExpansionModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatExpansionModule, imports: [CommonModule, MatCommonModule, CdkAccordionModule, PortalModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatExpansionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, MatCommonModule, CdkAccordionModule, PortalModule],\n                    exports: [\n                        MatAccordion,\n                        MatExpansionPanel,\n                        MatExpansionPanelActionRow,\n                        MatExpansionPanelHeader,\n                        MatExpansionPanelTitle,\n                        MatExpansionPanelDescription,\n                        MatExpansionPanelContent,\n                    ],\n                    declarations: [\n                        MatAccordion,\n                        MatExpansionPanel,\n                        MatExpansionPanelActionRow,\n                        MatExpansionPanelHeader,\n                        MatExpansionPanelTitle,\n                        MatExpansionPanelDescription,\n                        MatExpansionPanelContent,\n                    ],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { EXPANSION_PANEL_ANIMATION_TIMING, MAT_ACCORDION, MAT_EXPANSION_PANEL, MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, MatAccordion, MatExpansionModule, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelContent, MatExpansionPanelDescription, MatExpansionPanelHeader, MatExpansionPanelTitle, matExpansionAnimations };\n"], "mappings": "AAAA,SAASA,gBAAT,EAA2BC,YAA3B,EAAyCC,kBAAzC,QAAmE,wBAAnE;AACA,OAAO,KAAKC,EAAZ,MAAoB,qBAApB;AACA,SAASC,cAAT,EAAyBC,YAAzB,QAA6C,qBAA7C;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,QAAT,EAAmBC,YAAnB,QAAuC,iBAAvC;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,SAAzB,EAAoCC,MAApC,EAA4CC,QAA5C,EAAsDC,YAAtD,EAAoEC,SAApE,EAA+EC,iBAA/E,EAAkGC,uBAAlG,EAA2HC,QAA3H,EAAqIC,KAArI,EAA4IC,MAA5I,EAAoJC,YAApJ,EAAkKC,SAAlK,EAA6KC,IAA7K,EAAmLC,SAAnL,EAA8LC,SAA9L,EAAyMC,eAAzM,EAA0NC,QAA1N,QAA0O,eAA1O;AACA,SAASC,aAAT,EAAwBC,eAAxB,QAA+C,wBAA/C;AACA,SAASC,qBAAT,QAAsC,uBAAtC;AACA,OAAO,KAAKC,IAAZ,MAAsB,mBAAtB;AACA,SAASC,eAAT,QAAgC,mBAAhC;AACA,SAASC,oBAAT,EAA+BC,SAA/B,EAA0CC,MAA1C,EAAkDC,IAAlD,QAA8D,gBAA9D;AACA,SAASC,KAAT,EAAgBC,cAAhB,EAAgCC,KAAhC,QAA6C,uBAA7C;AACA,SAASC,qBAAT,QAAsC,sCAAtC;AACA,SAASC,OAAT,EAAkBC,YAAlB,EAAgCC,KAAhC,EAAuCC,KAAvC,QAAoD,MAApD;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,KAAzB,EAAgCC,UAAhC,EAA4CC,OAA5C,QAA2D,qBAA3D;AACA,OAAO,KAAKC,EAAZ,MAAoB,0BAApB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;;;;;;;;;IAgF2GzC,EA6UmnC,wB;;;;mBA7UnnCA,E;IAAAA,EA6UynC,2D;;;;;;AA5ZpuC,MAAM0C,aAAa,GAAG,IAAIzC,cAAJ,CAAmB,eAAnB,CAAtB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;;AACA,MAAM0C,gCAAgC,GAAG,mCAAzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,sBAAsB,GAAG;EAC3B;EACAC,eAAe,EAAET,OAAO,CAAC,iBAAD,EAAoB,CACxCC,KAAK,CAAC,iBAAD,EAAoBC,KAAK,CAAC;IAAEQ,SAAS,EAAE;EAAb,CAAD,CAAzB,CADmC,EAExCT,KAAK,CAAC,UAAD,EAAaC,KAAK,CAAC;IAAEQ,SAAS,EAAE;EAAb,CAAD,CAAlB,CAFmC,EAGxCP,UAAU,CAAC,2CAAD,EAA8CC,OAAO,CAACG,gCAAD,CAArD,CAH8B,CAApB,CAFG;;EAO3B;EACAI,aAAa,EAAEX,OAAO,CAAC,eAAD,EAAkB,CACpCC,KAAK,CAAC,iBAAD,EAAoBC,KAAK,CAAC;IAAEU,MAAM,EAAE,KAAV;IAAiBC,UAAU,EAAE;EAA7B,CAAD,CAAzB,CAD+B,EAEpCZ,KAAK,CAAC,UAAD,EAAaC,KAAK,CAAC;IAAEU,MAAM,EAAE,GAAV;IAAeC,UAAU,EAAE;EAA3B,CAAD,CAAlB,CAF+B,EAGpCV,UAAU,CAAC,2CAAD,EAA8CC,OAAO,CAACG,gCAAD,CAArD,CAH0B,CAAlB;AARK,CAA/B;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;AACA,MAAMO,mBAAmB,GAAG,IAAIjD,cAAJ,CAAmB,qBAAnB,CAA5B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;AACA,MAAMkD,wBAAN,CAA+B;EAC3BC,WAAW,CAACC,SAAD,EAAYC,eAAZ,EAA6B;IACpC,KAAKD,SAAL,GAAiBA,SAAjB;IACA,KAAKC,eAAL,GAAuBA,eAAvB;EACH;;AAJ0B;;AAM/BH,wBAAwB,CAACI,IAAzB;EAAA,iBAAqHJ,wBAArH,EAA2GnD,EAA3G,mBAA+JA,EAAE,CAACwD,WAAlK,GAA2GxD,EAA3G,mBAA0LkD,mBAA1L;AAAA;;AACAC,wBAAwB,CAACM,IAAzB,kBAD2GzD,EAC3G;EAAA,MAAyGmD,wBAAzG;EAAA;AAAA;;AACA;EAAA,mDAF2GnD,EAE3G,mBAA2FmD,wBAA3F,EAAiI,CAAC;IACtHO,IAAI,EAAExD,SADgH;IAEtHyD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IADX,CAAD;EAFgH,CAAD,CAAjI,EAK4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAE1D,EAAE,CAACwD;IAAX,CAAD,EAA2B;MAAEE,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACxFJ,IAAI,EAAEvD,MADkF;QAExFwD,IAAI,EAAE,CAACT,mBAAD;MAFkF,CAAD,EAGxF;QACCQ,IAAI,EAAEtD;MADP,CAHwF;IAA/B,CAA3B,CAAP;EAKlB,CAVxB;AAAA;AAYA;;;AACA,IAAI2D,QAAQ,GAAG,CAAf;AACA;AACA;AACA;AACA;;AACA,MAAMC,mCAAmC,GAAG,IAAI/D,cAAJ,CAAmB,qCAAnB,CAA5C;AACA;AACA;AACA;AACA;;AACA,MAAMgE,iBAAN,SAAgC1E,gBAAhC,CAAiD;EAC7C6D,WAAW,CAACc,SAAD,EAAYC,kBAAZ,EAAgCC,0BAAhC,EAA4DC,iBAA5D,EAA+EC,SAA/E,EAA0FC,cAA1F,EAA0GC,cAA1G,EAA0H;IACjI,MAAMN,SAAN,EAAiBC,kBAAjB,EAAqCC,0BAArC;IACA,KAAKC,iBAAL,GAAyBA,iBAAzB;IACA,KAAKE,cAAL,GAAsBA,cAAtB;IACA,KAAKE,WAAL,GAAmB,KAAnB;IACA;;IACA,KAAKC,WAAL,GAAmB,IAAIrE,YAAJ,EAAnB;IACA;;IACA,KAAKsE,aAAL,GAAqB,IAAItE,YAAJ,EAArB;IACA;;IACA,KAAKuE,aAAL,GAAqB,IAAI5C,OAAJ,EAArB;IACA;;IACA,KAAK6C,SAAL,GAAkB,8BAA6Bd,QAAQ,EAAG,EAA1D;IACA;;IACA,KAAKe,kBAAL,GAA0B,IAAI9C,OAAJ,EAA1B;IACA,KAAKkC,SAAL,GAAiBA,SAAjB;IACA,KAAKI,SAAL,GAAiBA,SAAjB,CAhBiI,CAiBjI;IACA;;IACA,KAAKQ,kBAAL,CACKC,IADL,CACUvD,oBAAoB,CAAC,CAACwD,CAAD,EAAIC,CAAJ,KAAU;MACrC,OAAOD,CAAC,CAACE,SAAF,KAAgBD,CAAC,CAACC,SAAlB,IAA+BF,CAAC,CAACG,OAAF,KAAcF,CAAC,CAACE,OAAtD;IACH,CAF6B,CAD9B,EAIKC,SAJL,CAIeC,KAAK,IAAI;MACpB,IAAIA,KAAK,CAACH,SAAN,KAAoB,MAAxB,EAAgC;QAC5B,IAAIG,KAAK,CAACF,OAAN,KAAkB,UAAtB,EAAkC;UAC9B,KAAKT,WAAL,CAAiBY,IAAjB;QACH,CAFD,MAGK,IAAID,KAAK,CAACF,OAAN,KAAkB,WAAtB,EAAmC;UACpC,KAAKR,aAAL,CAAmBW,IAAnB;QACH;MACJ;IACJ,CAbD;;IAcA,IAAId,cAAJ,EAAoB;MAChB,KAAKe,UAAL,GAAkBf,cAAc,CAACe,UAAjC;IACH;EACJ;EACD;;;EACc,IAAVA,UAAU,GAAG;IACb,OAAO,KAAKd,WAAL,IAAqB,KAAKP,SAAL,IAAkB,KAAKA,SAAL,CAAeqB,UAA7D;EACH;;EACa,IAAVA,UAAU,CAACC,KAAD,EAAQ;IAClB,KAAKf,WAAL,GAAmBpD,qBAAqB,CAACmE,KAAD,CAAxC;EACH;EACD;;;EACkB,IAAdC,cAAc,GAAG;IACjB,OAAO,KAAKC,eAAL,IAAyB,KAAKxB,SAAL,IAAkB,KAAKA,SAAL,CAAeuB,cAAjE;EACH;;EACiB,IAAdA,cAAc,CAACD,KAAD,EAAQ;IACtB,KAAKE,eAAL,GAAuBF,KAAvB;EACH;EACD;;;EACAG,WAAW,GAAG;IACV,IAAI,KAAKzB,SAAT,EAAoB;MAChB,OAAO,KAAK0B,QAAL,IAAiB,KAAK1B,SAAL,CAAe2B,WAAf,KAA+B,SAAvD;IACH;;IACD,OAAO,KAAP;EACH;EACD;;;EACAC,iBAAiB,GAAG;IAChB,OAAO,KAAKF,QAAL,GAAgB,UAAhB,GAA6B,WAApC;EACH;EACD;;;EACAG,MAAM,GAAG;IACL,KAAKH,QAAL,GAAgB,CAAC,KAAKA,QAAtB;EACH;EACD;;;EACAI,KAAK,GAAG;IACJ,KAAKJ,QAAL,GAAgB,KAAhB;EACH;EACD;;;EACAK,IAAI,GAAG;IACH,KAAKL,QAAL,GAAgB,IAAhB;EACH;;EACDM,kBAAkB,GAAG;IACjB,IAAI,KAAKC,YAAL,IAAqB,KAAKA,YAAL,CAAkB7C,eAAlB,KAAsC,IAA/D,EAAqE;MACjE;MACA,KAAK8C,MAAL,CACKrB,IADL,CACUtD,SAAS,CAAC,IAAD,CADnB,EAC2BC,MAAM,CAAC,MAAM,KAAKkE,QAAL,IAAiB,CAAC,KAAKS,OAA9B,CADjC,EACyE1E,IAAI,CAAC,CAAD,CAD7E,EAEKyD,SAFL,CAEe,MAAM;QACjB,KAAKiB,OAAL,GAAe,IAAI1G,cAAJ,CAAmB,KAAKwG,YAAL,CAAkB9C,SAArC,EAAgD,KAAKgB,iBAArD,CAAf;MACH,CAJD;IAKH;EACJ;;EACDiC,WAAW,CAACC,OAAD,EAAU;IACjB,KAAK3B,aAAL,CAAmB4B,IAAnB,CAAwBD,OAAxB;EACH;;EACDE,WAAW,GAAG;IACV,MAAMA,WAAN;;IACA,KAAK3B,kBAAL,CAAwB4B,QAAxB;;IACA,KAAK9B,aAAL,CAAmB8B,QAAnB;EACH;EACD;;;EACAC,cAAc,GAAG;IACb,IAAI,KAAKC,KAAT,EAAgB;MACZ,MAAMC,cAAc,GAAG,KAAKvC,SAAL,CAAewC,aAAtC;MACA,MAAMC,WAAW,GAAG,KAAKH,KAAL,CAAWI,aAA/B;MACA,OAAOH,cAAc,KAAKE,WAAnB,IAAkCA,WAAW,CAACE,QAAZ,CAAqBJ,cAArB,CAAzC;IACH;;IACD,OAAO,KAAP;EACH;;AArG4C;;AAuGjD5C,iBAAiB,CAACV,IAAlB;EAAA,iBAA8GU,iBAA9G,EAhI2GjE,EAgI3G,mBAAiJ0C,aAAjJ,OAhI2G1C,EAgI3G,mBAA2MA,EAAE,CAACkH,iBAA9M,GAhI2GlH,EAgI3G,mBAA4OyC,EAAE,CAAC0E,yBAA/O,GAhI2GnH,EAgI3G,mBAAqRA,EAAE,CAACoH,gBAAxR,GAhI2GpH,EAgI3G,mBAAqTF,QAArT,GAhI2GE,EAgI3G,mBAA0U+B,qBAA1U,MAhI2G/B,EAgI3G,mBAA4XgE,mCAA5X;AAAA;;AACAC,iBAAiB,CAACoD,IAAlB,kBAjI2GrH,EAiI3G;EAAA,MAAkGiE,iBAAlG;EAAA;EAAA;IAAA;MAjI2GjE,EAiI3G,0BAKyEmD,wBALzE;IAAA;;IAAA;MAAA;;MAjI2GnD,EAiI3G,qBAjI2GA,EAiI3G;IAAA;EAAA;EAAA;IAAA;MAjI2GA,EAiI3G;IAAA;;IAAA;MAAA;;MAjI2GA,EAiI3G,qBAjI2GA,EAiI3G;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAjI2GA,EAiI3G;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAjI2GA,EAiI3G,oBAAuoB,CAC/nB;EACA;EACA;IAAEsH,OAAO,EAAE5E,aAAX;IAA0B6E,QAAQ,EAAE1D;EAApC,CAH+nB,EAI/nB;IAAEyD,OAAO,EAAEpE,mBAAX;IAAgCsE,WAAW,EAAEvD;EAA7C,CAJ+nB,CAAvoB,GAjI2GjE,EAiI3G,6BAjI2GA,EAiI3G;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAjI2GA,EAiI3G;MAjI2GA,EAsImN,gBAL9T;MAjI2GA,EAsIoR,+BAL/X;MAjI2GA,EAsI0Y;QAAA,OAAwB,mCAAxB;MAAA,EALrf;MAjI2GA,EAsIihB,4BAL5nB;MAjI2GA,EAsI+jB,mBAL1qB;MAjI2GA,EAsI8lB,gFALzsB;MAjI2GA,EAsI2pB,eALtwB;MAjI2GA,EAsIqqB,mBALhxB;MAjI2GA,EAsI0tB,eALr0B;IAAA;;IAAA;MAjI2GA,EAsI2V,aALtc;MAjI2GA,EAsI2V,oEALtc;MAjI2GA,EAsI0c,8CALrjB;MAjI2GA,EAsI2mB,aALttB;MAjI2GA,EAsI2mB,2CALttB;IAAA;EAAA;EAAA,eAK8iFN,EAAE,CAAC+H,eALjjF;EAAA;EAAA;EAAA;IAAA,WAKmsF,CAAC7E,sBAAsB,CAACG,aAAxB;EALnsF;EAAA;AAAA;;AAMA;EAAA,mDAvI2G/C,EAuI3G,mBAA2FiE,iBAA3F,EAA0H,CAAC;IAC/GP,IAAI,EAAEpD,SADyG;IAE/GqD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,qBAAZ;MAAmC8D,QAAQ,EAAE,mBAA7C;MAAkEC,aAAa,EAAEpH,iBAAiB,CAACqH,IAAnG;MAAyGC,eAAe,EAAErH,uBAAuB,CAACsH,MAAlJ;MAA0JC,MAAM,EAAE,CAAC,UAAD,EAAa,UAAb,CAAlK;MAA4LC,OAAO,EAAE,CAAC,QAAD,EAAW,QAAX,EAAqB,gBAArB,CAArM;MAA6OC,UAAU,EAAE,CAACrF,sBAAsB,CAACG,aAAxB,CAAzP;MAAiSmF,SAAS,EAAE,CACvS;MACA;MACA;QAAEZ,OAAO,EAAE5E,aAAX;QAA0B6E,QAAQ,EAAE1D;MAApC,CAHuS,EAIvS;QAAEyD,OAAO,EAAEpE,mBAAX;QAAgCsE,WAAW,EAAEvD;MAA7C,CAJuS,CAA5S;MAKIkE,IAAI,EAAE;QACL,SAAS,qBADJ;QAEL,wBAAwB,UAFnB;QAGL,mCAAmC,qCAH9B;QAIL,uCAAuC;MAJlC,CALV;MAUIC,QAAQ,EAAE,ihBAVd;MAUiiBC,MAAM,EAAE,CAAC,wqDAAD;IAVziB,CAAD;EAFyG,CAAD,CAA1H,EAa4B,YAAY;IAAE,OAAO,CAAC;MAAE3E,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9DJ,IAAI,EAAEtD;MADwD,CAAD,EAE9D;QACCsD,IAAI,EAAEjD;MADP,CAF8D,EAI9D;QACCiD,IAAI,EAAEvD,MADP;QAECwD,IAAI,EAAE,CAACjB,aAAD;MAFP,CAJ8D;IAA/B,CAAD,EAO3B;MAAEgB,IAAI,EAAE1D,EAAE,CAACkH;IAAX,CAP2B,EAOK;MAAExD,IAAI,EAAEjB,EAAE,CAAC0E;IAAX,CAPL,EAO6C;MAAEzD,IAAI,EAAE1D,EAAE,CAACoH;IAAX,CAP7C,EAO4E;MAAE1D,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACzIJ,IAAI,EAAEvD,MADmI;QAEzIwD,IAAI,EAAE,CAAC7D,QAAD;MAFmI,CAAD;IAA/B,CAP5E,EAU3B;MAAE4D,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClCJ,IAAI,EAAEtD;MAD4B,CAAD,EAElC;QACCsD,IAAI,EAAEvD,MADP;QAECwD,IAAI,EAAE,CAAC5B,qBAAD;MAFP,CAFkC;IAA/B,CAV2B,EAe3B;MAAE2B,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClCJ,IAAI,EAAEvD,MAD4B;QAElCwD,IAAI,EAAE,CAACK,mCAAD;MAF4B,CAAD,EAGlC;QACCN,IAAI,EAAEtD;MADP,CAHkC;IAA/B,CAf2B,CAAP;EAoBlB,CAjCxB,EAiC0C;IAAEmF,UAAU,EAAE,CAAC;MACzC7B,IAAI,EAAEhD;IADmC,CAAD,CAAd;IAE1B+E,cAAc,EAAE,CAAC;MACjB/B,IAAI,EAAEhD;IADW,CAAD,CAFU;IAI1BgE,WAAW,EAAE,CAAC;MACdhB,IAAI,EAAE/C;IADQ,CAAD,CAJa;IAM1BgE,aAAa,EAAE,CAAC;MAChBjB,IAAI,EAAE/C;IADU,CAAD,CANW;IAQ1BwF,YAAY,EAAE,CAAC;MACfzC,IAAI,EAAE9C,YADS;MAEf+C,IAAI,EAAE,CAACR,wBAAD;IAFS,CAAD,CARY;IAW1ByD,KAAK,EAAE,CAAC;MACRlD,IAAI,EAAE7C,SADE;MAER8C,IAAI,EAAE,CAAC,MAAD;IAFE,CAAD;EAXmB,CAjC1C;AAAA;AAgDA;AACA;AACA;;;AACA,MAAM2E,0BAAN,CAAiC;;AAEjCA,0BAA0B,CAAC/E,IAA3B;EAAA,iBAAuH+E,0BAAvH;AAAA;;AACAA,0BAA0B,CAAC7E,IAA3B,kBA7L2GzD,EA6L3G;EAAA,MAA2GsI,0BAA3G;EAAA;EAAA;AAAA;;AACA;EAAA,mDA9L2GtI,EA8L3G,mBAA2FsI,0BAA3F,EAAmI,CAAC;IACxH5E,IAAI,EAAExD,SADkH;IAExHyD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBADX;MAECuE,IAAI,EAAE;QACFI,KAAK,EAAE;MADL;IAFP,CAAD;EAFkH,CAAD,CAAnI;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMC,2BAAN,CAAkC;;AAElC,MAAMC,iCAAiC,GAAGtH,aAAa,CAACqH,2BAAD,CAAvD;AACA;AACA;AACA;;;AACA,MAAME,uBAAN,SAAsCD,iCAAtC,CAAwE;EACpErF,WAAW,CAACuF,KAAD,EAAQC,QAAR,EAAkBC,aAAlB,EAAiC1E,kBAAjC,EAAqDK,cAArD,EAAqED,cAArE,EAAqFuE,QAArF,EAA+F;IACtG;IACA,KAAKH,KAAL,GAAaA,KAAb;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,aAAL,GAAqBA,aAArB;IACA,KAAK1E,kBAAL,GAA0BA,kBAA1B;IACA,KAAKI,cAAL,GAAsBA,cAAtB;IACA,KAAKwE,yBAAL,GAAiC9G,YAAY,CAACC,KAA9C;IACA,MAAM8G,yBAAyB,GAAGL,KAAK,CAACzE,SAAN,GAC5ByE,KAAK,CAACzE,SAAN,CAAgB+E,aAAhB,CAA8BlE,IAA9B,CAAmCrD,MAAM,CAAC6E,OAAO,IAAI,CAAC,EAAEA,OAAO,CAAC,YAAD,CAAP,IAAyBA,OAAO,CAAC,gBAAD,CAAlC,CAAb,CAAzC,CAD4B,GAE5BrE,KAFN;IAGA,KAAK4G,QAAL,GAAgBI,QAAQ,CAACJ,QAAQ,IAAI,EAAb,CAAR,IAA4B,CAA5C,CAXsG,CAYtG;IACA;;IACA,KAAKC,yBAAL,GAAiC5G,KAAK,CAACwG,KAAK,CAACvC,MAAP,EAAeuC,KAAK,CAACQ,MAArB,EAA6BH,yBAA7B,EAAwDL,KAAK,CAAC/D,aAAN,CAAoBG,IAApB,CAAyBrD,MAAM,CAAC6E,OAAO,IAAI;MACrI,OAAO,CAAC,EAAEA,OAAO,CAAC,YAAD,CAAP,IAAyBA,OAAO,CAAC,UAAD,CAAhC,IAAgDA,OAAO,CAAC,gBAAD,CAAzD,CAAR;IACH,CAF4H,CAA/B,CAAxD,CAAL,CAE5BnB,SAF4B,CAElB,MAAM,KAAKjB,kBAAL,CAAwBiF,YAAxB,EAFY,CAAjC,CAdsG,CAiBtG;;IACAT,KAAK,CAACQ,MAAN,CACKpE,IADL,CACUrD,MAAM,CAAC,MAAMiH,KAAK,CAAChC,cAAN,EAAP,CADhB,EAEKvB,SAFL,CAEe,MAAMyD,aAAa,CAACQ,QAAd,CAAuBT,QAAvB,EAAiC,SAAjC,CAFrB;;IAGA,IAAIpE,cAAJ,EAAoB;MAChB,KAAK8E,cAAL,GAAsB9E,cAAc,CAAC8E,cAArC;MACA,KAAKC,eAAL,GAAuB/E,cAAc,CAAC+E,eAAtC;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACgB,IAARC,QAAQ,GAAG;IACX,OAAO,KAAKb,KAAL,CAAWa,QAAlB;EACH;EACD;;;EACAC,OAAO,GAAG;IACN,IAAI,CAAC,KAAKD,QAAV,EAAoB;MAChB,KAAKb,KAAL,CAAW5C,MAAX;IACH;EACJ;EACD;;;EACA2D,WAAW,GAAG;IACV,OAAO,KAAKf,KAAL,CAAW/C,QAAlB;EACH;EACD;;;EACAE,iBAAiB,GAAG;IAChB,OAAO,KAAK6C,KAAL,CAAW7C,iBAAX,EAAP;EACH;EACD;;;EACA6D,WAAW,GAAG;IACV,OAAO,KAAKhB,KAAL,CAAWiB,EAAlB;EACH;EACD;;;EACAC,kBAAkB,GAAG;IACjB,OAAO,KAAKlB,KAAL,CAAWlD,cAAlB;EACH;EACD;;;EACAqE,WAAW,GAAG;IACV,OAAO,CAAC,KAAKnB,KAAL,CAAWpD,UAAZ,IAA0B,CAAC,KAAKoD,KAAL,CAAWa,QAA7C;EACH;EACD;AACJ;AACA;AACA;;;EACIO,gBAAgB,GAAG;IACf,MAAMC,UAAU,GAAG,KAAKN,WAAL,EAAnB;;IACA,IAAIM,UAAU,IAAI,KAAKV,cAAvB,EAAuC;MACnC,OAAO,KAAKA,cAAZ;IACH,CAFD,MAGK,IAAI,CAACU,UAAD,IAAe,KAAKT,eAAxB,EAAyC;MAC1C,OAAO,KAAKA,eAAZ;IACH;;IACD,OAAO,IAAP;EACH;EACD;;;EACAU,QAAQ,CAAC5E,KAAD,EAAQ;IACZ,QAAQA,KAAK,CAAC6E,OAAd;MACI;MACA,KAAKpI,KAAL;MACA,KAAKF,KAAL;QACI,IAAI,CAACC,cAAc,CAACwD,KAAD,CAAnB,EAA4B;UACxBA,KAAK,CAAC8E,cAAN;;UACA,KAAKV,OAAL;QACH;;QACD;;MACJ;QACI,IAAI,KAAKd,KAAL,CAAWzE,SAAf,EAA0B;UACtB,KAAKyE,KAAL,CAAWzE,SAAX,CAAqBkG,oBAArB,CAA0C/E,KAA1C;QACH;;QACD;IAbR;EAeH;EACD;AACJ;AACA;AACA;AACA;;;EACIgF,KAAK,CAACC,MAAD,EAASC,OAAT,EAAkB;IACnB,IAAID,MAAJ,EAAY;MACR,KAAKzB,aAAL,CAAmBQ,QAAnB,CAA4B,KAAKT,QAAjC,EAA2C0B,MAA3C,EAAmDC,OAAnD;IACH,CAFD,MAGK;MACD,KAAK3B,QAAL,CAAc5B,aAAd,CAA4BqD,KAA5B,CAAkCE,OAAlC;IACH;EACJ;;EACDC,eAAe,GAAG;IACd,KAAK3B,aAAL,CAAmB4B,OAAnB,CAA2B,KAAK7B,QAAhC,EAA0CxD,SAA1C,CAAoDkF,MAAM,IAAI;MAC1D,IAAIA,MAAM,IAAI,KAAK3B,KAAL,CAAWzE,SAAzB,EAAoC;QAChC,KAAKyE,KAAL,CAAWzE,SAAX,CAAqBwG,kBAArB,CAAwC,IAAxC;MACH;IACJ,CAJD;EAKH;;EACDjE,WAAW,GAAG;IACV,KAAKsC,yBAAL,CAA+B4B,WAA/B;;IACA,KAAK9B,aAAL,CAAmB+B,cAAnB,CAAkC,KAAKhC,QAAvC;EACH;;AAnHmE;;AAqHxEF,uBAAuB,CAACnF,IAAxB;EAAA,iBAAoHmF,uBAApH,EA5U2G1I,EA4U3G,mBAA6JiE,iBAA7J,MA5U2GjE,EA4U3G,mBAAuMA,EAAE,CAAC6K,UAA1M,GA5U2G7K,EA4U3G,mBAAiOsB,IAAI,CAACwJ,YAAtO,GA5U2G9K,EA4U3G,mBAA+PA,EAAE,CAACkH,iBAAlQ,GA5U2GlH,EA4U3G,mBAAgSgE,mCAAhS,MA5U2GhE,EA4U3G,mBAAgW+B,qBAAhW,MA5U2G/B,EA4U3G,mBAAkZ,UAAlZ;AAAA;;AACA0I,uBAAuB,CAACrB,IAAxB,kBA7U2GrH,EA6U3G;EAAA,MAAwG0I,uBAAxG;EAAA;EAAA,oBAA+S,QAA/S;EAAA;EAAA;IAAA;MA7U2G1I,EA6U3G;QAAA,OAAwG,aAAxG;MAAA;QAAA,OAAwG,oBAAxG;MAAA;IAAA;;IAAA;MA7U2GA,EA6U3G;MA7U2GA,EA6U3G;MA7U2GA,EA6U3G;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WA7U2GA,EA6U3G;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA7U2GA,EA6U3G;MA7U2GA,EA6Us4B,6BAAj/B;MA7U2GA,EA6Uy9B,gBAApkC;MA7U2GA,EA6UihC,mBAA5nC;MA7U2GA,EA6U+kC,mBAA1rC;MA7U2GA,EA6U0mC,eAArtC;MA7U2GA,EA6UmnC,wEAA9tC;IAAA;;IAAA;MA7U2GA,EA6Uk6B,2DAA7gC;MA7U2GA,EA6UqqC,aAAhxC;MA7U2GA,EA6UqqC,sCAAhxC;IAAA;EAAA;EAAA,eAAiuGH,EAAE,CAACkL,IAApuG;EAAA;EAAA;EAAA;IAAA,WAAuzG,CAACnI,sBAAsB,CAACC,eAAxB;EAAvzG;EAAA;AAAA;;AACA;EAAA,mDA9U2G7C,EA8U3G,mBAA2F0I,uBAA3F,EAAgI,CAAC;IACrHhF,IAAI,EAAEpD,SAD+G;IAErHqD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,4BAAZ;MAA0C+D,aAAa,EAAEpH,iBAAiB,CAACqH,IAA3E;MAAiFC,eAAe,EAAErH,uBAAuB,CAACsH,MAA1H;MAAkIC,MAAM,EAAE,CAAC,UAAD,CAA1I;MAAwJE,UAAU,EAAE,CAACrF,sBAAsB,CAACC,eAAxB,CAApK;MAA8MsF,IAAI,EAAE;QAC/M,SAAS,gDADsM;QAE/M,QAAQ,QAFuM;QAG/M,aAAa,iBAHkM;QAI/M,mBAAmB,UAJ4L;QAK/M,wBAAwB,eALuL;QAM/M,wBAAwB,eANuL;QAO/M,wBAAwB,gBAPuL;QAQ/M,wBAAwB,eARuL;QAS/M,gDAAiD,kCAT8J;QAU/M,iDAAkD,mCAV6J;QAW/M,mCAAmC,qCAX4K;QAY/M,kBAAkB,oBAZ6L;QAa/M,WAAW,WAboM;QAc/M,aAAa;MAdkM,CAApN;MAeIC,QAAQ,EAAE,0WAfd;MAe0XC,MAAM,EAAE,CAAC,+0DAAD;IAflY,CAAD;EAF+G,CAAD,CAAhI,EAkB4B,YAAY;IAAE,OAAO,CAAC;MAAE3E,IAAI,EAAEO,iBAAR;MAA2BH,UAAU,EAAE,CAAC;QACtEJ,IAAI,EAAE5C;MADgE,CAAD;IAAvC,CAAD,EAE3B;MAAE4C,IAAI,EAAE1D,EAAE,CAAC6K;IAAX,CAF2B,EAEF;MAAEnH,IAAI,EAAEpC,IAAI,CAACwJ;IAAb,CAFE,EAE2B;MAAEpH,IAAI,EAAE1D,EAAE,CAACkH;IAAX,CAF3B,EAE2D;MAAExD,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACxHJ,IAAI,EAAEvD,MADkH;QAExHwD,IAAI,EAAE,CAACK,mCAAD;MAFkH,CAAD,EAGxH;QACCN,IAAI,EAAEtD;MADP,CAHwH;IAA/B,CAF3D,EAO3B;MAAEsD,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClCJ,IAAI,EAAEtD;MAD4B,CAAD,EAElC;QACCsD,IAAI,EAAEvD,MADP;QAECwD,IAAI,EAAE,CAAC5B,qBAAD;MAFP,CAFkC;IAA/B,CAP2B,EAY3B;MAAE2B,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClCJ,IAAI,EAAE3C,SAD4B;QAElC4C,IAAI,EAAE,CAAC,UAAD;MAF4B,CAAD;IAA/B,CAZ2B,CAAP;EAelB,CAjCxB,EAiC0C;IAAE2F,cAAc,EAAE,CAAC;MAC7C5F,IAAI,EAAEhD;IADuC,CAAD,CAAlB;IAE1B6I,eAAe,EAAE,CAAC;MAClB7F,IAAI,EAAEhD;IADY,CAAD;EAFS,CAjC1C;AAAA;AAsCA;AACA;AACA;;;AACA,MAAMsK,4BAAN,CAAmC;;AAEnCA,4BAA4B,CAACzH,IAA7B;EAAA,iBAAyHyH,4BAAzH;AAAA;;AACAA,4BAA4B,CAACvH,IAA7B,kBA1X2GzD,EA0X3G;EAAA,MAA6GgL,4BAA7G;EAAA;EAAA;AAAA;;AACA;EAAA,mDA3X2GhL,EA2X3G,mBAA2FgL,4BAA3F,EAAqI,CAAC;IAC1HtH,IAAI,EAAExD,SADoH;IAE1HyD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uBADX;MAECuE,IAAI,EAAE;QACFI,KAAK,EAAE;MADL;IAFP,CAAD;EAFoH,CAAD,CAArI;AAAA;AASA;AACA;AACA;;;AACA,MAAM0C,sBAAN,CAA6B;;AAE7BA,sBAAsB,CAAC1H,IAAvB;EAAA,iBAAmH0H,sBAAnH;AAAA;;AACAA,sBAAsB,CAACxH,IAAvB,kBA1Y2GzD,EA0Y3G;EAAA,MAAuGiL,sBAAvG;EAAA;EAAA;AAAA;;AACA;EAAA,mDA3Y2GjL,EA2Y3G,mBAA2FiL,sBAA3F,EAA+H,CAAC;IACpHvH,IAAI,EAAExD,SAD8G;IAEpHyD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBADX;MAECuE,IAAI,EAAE;QACFI,KAAK,EAAE;MADL;IAFP,CAAD;EAF8G,CAAD,CAA/H;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAM2C,YAAN,SAA2B1L,YAA3B,CAAwC;EACpC4D,WAAW,GAAG;IACV,MAAM,GAAG+H,SAAT;IACA;;IACA,KAAKC,WAAL,GAAmB,IAAIpK,SAAJ,EAAnB;IACA,KAAKyD,WAAL,GAAmB,KAAnB;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;;IACQ,KAAKoB,WAAL,GAAmB,SAAnB;IACA;;IACA,KAAKJ,cAAL,GAAsB,OAAtB;EACH;EACD;;;EACc,IAAVF,UAAU,GAAG;IACb,OAAO,KAAKd,WAAZ;EACH;;EACa,IAAVc,UAAU,CAAC8F,IAAD,EAAO;IACjB,KAAK5G,WAAL,GAAmBpD,qBAAqB,CAACgK,IAAD,CAAxC;EACH;;EACDnF,kBAAkB,GAAG;IACjB,KAAKoF,QAAL,CAAc/E,OAAd,CACKxB,IADL,CACUtD,SAAS,CAAC,KAAK6J,QAAN,CADnB,EAEKlG,SAFL,CAEgBmG,OAAD,IAAa;MACxB,KAAKH,WAAL,CAAiBI,KAAjB,CAAuBD,OAAO,CAAC7J,MAAR,CAAe+J,MAAM,IAAIA,MAAM,CAAC9C,KAAP,CAAazE,SAAb,KAA2B,IAApD,CAAvB;;MACA,KAAKkH,WAAL,CAAiBM,eAAjB;IACH,CALD;;IAMA,KAAKC,WAAL,GAAmB,IAAIpK,eAAJ,CAAoB,KAAK6J,WAAzB,EAAsCQ,QAAtC,GAAiDC,cAAjD,EAAnB;EACH;EACD;;;EACAzB,oBAAoB,CAAC/E,KAAD,EAAQ;IACxB,KAAKsG,WAAL,CAAiBG,SAAjB,CAA2BzG,KAA3B;EACH;;EACDqF,kBAAkB,CAACe,MAAD,EAAS;IACvB,KAAKE,WAAL,CAAiBI,gBAAjB,CAAkCN,MAAlC;EACH;;EACDhF,WAAW,GAAG;IACV,MAAMA,WAAN;;IACA,KAAK2E,WAAL,CAAiBY,OAAjB;EACH;;AA5CmC;;AA8CxCd,YAAY,CAAC3H,IAAb;EAAA;EAAA;IAAA,kEA7c2GvD,EA6c3G,uBAAyGkL,YAAzG,SAAyGA,YAAzG;EAAA;AAAA;;AACAA,YAAY,CAACzH,IAAb,kBA9c2GzD,EA8c3G;EAAA,MAA6FkL,YAA7F;EAAA;EAAA;IAAA;MA9c2GlL,EA8c3G,0BAKwD0I,uBALxD;IAAA;;IAAA;MAAA;;MA9c2G1I,EA8c3G,qBA9c2GA,EA8c3G;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA9c2GA,EA8c3G;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WA9c2GA,EA8c3G,oBAA2W,CACnW;IACIsH,OAAO,EAAE5E,aADb;IAEI8E,WAAW,EAAE0D;EAFjB,CADmW,CAA3W,GA9c2GlL,EA8c3G;AAAA;;AAMA;EAAA,mDApd2GA,EAod3G,mBAA2FkL,YAA3F,EAAqH,CAAC;IAC1GxH,IAAI,EAAExD,SADoG;IAE1GyD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,eADX;MAEC8D,QAAQ,EAAE,cAFX;MAGCK,MAAM,EAAE,CAAC,OAAD,CAHT;MAICG,SAAS,EAAE,CACP;QACIZ,OAAO,EAAE5E,aADb;QAEI8E,WAAW,EAAE0D;MAFjB,CADO,CAJZ;MAUC/C,IAAI,EAAE;QACFI,KAAK,EAAE,eADL;QAEF;QACA;QACA,+BAA+B;MAJ7B;IAVP,CAAD;EAFoG,CAAD,CAArH,QAmB4B;IAAE+C,QAAQ,EAAE,CAAC;MACzB5H,IAAI,EAAEzC,eADmB;MAEzB0C,IAAI,EAAE,CAAC+E,uBAAD,EAA0B;QAAEuD,WAAW,EAAE;MAAf,CAA1B;IAFmB,CAAD,CAAZ;IAGZ1G,UAAU,EAAE,CAAC;MACb7B,IAAI,EAAEhD;IADO,CAAD,CAHA;IAKZmF,WAAW,EAAE,CAAC;MACdnC,IAAI,EAAEhD;IADQ,CAAD,CALD;IAOZ+E,cAAc,EAAE,CAAC;MACjB/B,IAAI,EAAEhD;IADW,CAAD;EAPJ,CAnB5B;AAAA;AA8BA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMwL,kBAAN,CAAyB;;AAEzBA,kBAAkB,CAAC3I,IAAnB;EAAA,iBAA+G2I,kBAA/G;AAAA;;AACAA,kBAAkB,CAACC,IAAnB,kBA5f2GnM,EA4f3G;EAAA,MAAgHkM,kBAAhH;EAAA,eAAmJhB,YAAnJ,EACQjH,iBADR,EAEQqE,0BAFR,EAGQI,uBAHR,EAIQuC,sBAJR,EAKQD,4BALR,EAMQ7H,wBANR;EAAA,UAM6CpD,YAN7C,EAM2DqB,eAN3D,EAM4E3B,kBAN5E,EAMgGG,YANhG;EAAA,UAMyHsL,YANzH,EAOQjH,iBAPR,EAQQqE,0BARR,EASQI,uBATR,EAUQuC,sBAVR,EAWQD,4BAXR,EAYQ7H,wBAZR;AAAA;AAaA+I,kBAAkB,CAACE,IAAnB,kBAzgB2GpM,EAygB3G;EAAA,UAA8ID,YAA9I,EAA4JqB,eAA5J,EAA6K3B,kBAA7K,EAAiMG,YAAjM;AAAA;;AACA;EAAA,mDA1gB2GI,EA0gB3G,mBAA2FkM,kBAA3F,EAA2H,CAAC;IAChHxI,IAAI,EAAExC,QAD0G;IAEhHyC,IAAI,EAAE,CAAC;MACC0I,OAAO,EAAE,CAACtM,YAAD,EAAeqB,eAAf,EAAgC3B,kBAAhC,EAAoDG,YAApD,CADV;MAEC0M,OAAO,EAAE,CACLpB,YADK,EAELjH,iBAFK,EAGLqE,0BAHK,EAILI,uBAJK,EAKLuC,sBALK,EAMLD,4BANK,EAOL7H,wBAPK,CAFV;MAWCoJ,YAAY,EAAE,CACVrB,YADU,EAEVjH,iBAFU,EAGVqE,0BAHU,EAIVI,uBAJU,EAKVuC,sBALU,EAMVD,4BANU,EAOV7H,wBAPU;IAXf,CAAD;EAF0G,CAAD,CAA3H;AAAA;AAyBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASR,gCAAT,EAA2CD,aAA3C,EAA0DQ,mBAA1D,EAA+Ec,mCAA/E,EAAoHkH,YAApH,EAAkIgB,kBAAlI,EAAsJjI,iBAAtJ,EAAyKqE,0BAAzK,EAAqMnF,wBAArM,EAA+N6H,4BAA/N,EAA6PtC,uBAA7P,EAAsRuC,sBAAtR,EAA8SrI,sBAA9S"}, "metadata": {}, "sourceType": "module"}