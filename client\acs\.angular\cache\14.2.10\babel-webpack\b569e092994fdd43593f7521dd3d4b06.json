{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./DBA.component.html?ngResource\";\nimport { ChangeDetectorRef, Component, ViewChild } from '@angular/core';\nimport { Validators, UntypedFormArray, FormGroup, FormControl } from '@angular/forms';\nimport { FilingApiService } from '../../Services/FilingApiService';\nimport { DBAFilling } from '../../Models/DBA';\nimport { ToastrService } from 'ngx-toastr';\nimport { ActivatedRoute } from '@angular/router';\nimport { CartService } from 'src/app/Modules/Shared/Services/cart/cart.service';\nimport { FilingServiceResponseObject } from '../../Enums/filing-service-enum';\nimport { CustomSharedValidations } from 'src/app/Modules/Shared/functions/custom-validations';\nimport { FormModeService } from 'src/app/Modules/Shared/Services/Common/FormModeService';\nimport { FilingInfoService } from '../../Services/filing-price.service';\nimport { SubCategoryCode } from 'src/app/Modules/Shared/Enums/sub-category-code.enum';\nimport { LoadFilingService } from '../../Functions/filing-service-load.func';\nimport { LoaderService } from '../../../../Modules/Core/Services/Common/loader.service';\nimport { QuestionService } from 'src/app/Modules/Shared/Services/Common/question.service';\nimport { DynamicFormComponent } from 'src/app/Modules/Shared/Components/dynamic-form/dynamic-form.component';\nimport { PageTitleService } from 'src/app/Modules/Shared/Services/Common/page-title.service';\nlet DBAFilingComponent = class DBAFilingComponent {\n  constructor(isLoader, filingInfoService, cartService, formMode, Api, toaster, activatedRoute, service, cdr, pageTitleService) {\n    this.isLoader = isLoader;\n    this.filingInfoService = filingInfoService;\n    this.cartService = cartService;\n    this.formMode = formMode;\n    this.Api = Api;\n    this.toaster = toaster;\n    this.activatedRoute = activatedRoute;\n    this.service = service;\n    this.cdr = cdr;\n    this.pageTitleService = pageTitleService;\n    this.isAdd = true;\n    this.FilingTypes = [];\n    this.AdditionalFictitiousTypes = [];\n    this.SubCatCodes = SubCategoryCode;\n    this.dynamicFormMasterData = [];\n    this.isQuestions = false;\n    this.dbaFilingModel = new DBAFilling();\n    this.fictitiousNameToFile = [''];\n    this.FicticiousNames = new UntypedFormArray([]);\n    this.Form = new FormGroup({\n      FictitiousNameToFile: this.FicticiousNames,\n      Instructions: new FormControl(null, [CustomSharedValidations.specialInstructions]),\n      SelectedFilingOptionType: new FormControl(null)\n    });\n  }\n\n  set UploadComponent(uploadComponent) {\n    if (uploadComponent) {\n      this.UploadComponentObj = uploadComponent;\n      this.UploadComponentObj.EmitUploadedData.subscribe(data => {\n        this.FileData = data;\n      }); //Mapping uploaded file data to FileUploadComponent in case of edit\n\n      if (this.dbaFilingModel.FileData) {\n        this.UploadComponentObj.uploadedFileList = this.dbaFilingModel.FileData;\n        this.UploadComponentObj.toggleUploadView = 2;\n        this.FileData = this.dbaFilingModel.FileData;\n      }\n    }\n\n    this.cdr.detectChanges();\n  }\n\n  set DynamicFormComponent(dynamicFormComponent) {\n    if (dynamicFormComponent) {\n      this.DynamicFormComponentObj = dynamicFormComponent;\n    }\n  }\n\n  ngOnInit() {\n    this.filingInfoService.getLabel(this.filingInfoService.SubCatCode.DBA, '004').subscribe(label => {\n      this.pageTitleService.setPageTitle(label);\n    }); //Get all States from store if store is empty call states API\n\n    this.isLoader.show();\n    this.StartLoad();\n    this.Api.GetFilingType().subscribe(response => {\n      this.FilingTypes = response;\n    });\n    this.Api.GetDynamicFormMasterDataUpload(\"FS\", \"004\", \"110\", \"NaN\", 'A').subscribe(data => {\n      this.dynamicFormMasterData = data;\n      var dynamicFormData = this.dbaFilingModel.DynamicFormData && this.dbaFilingModel.DynamicFormData.length > 0 ? this.dbaFilingModel.DynamicFormData : [];\n      this.questions$ = this.service.getMappedQuestions(data, dynamicFormData, \"\");\n      this.isLoader.hide();\n      this.isQuestions = true;\n    });\n  }\n\n  StartLoad() {\n    var _this = this;\n\n    this.activatedRoute.queryParams.subscribe( /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (queryString) {\n        if (queryString.on) {\n          _this.isAdd = false;\n        }\n\n        LoadFilingService(queryString, _this.Api, FilingServiceResponseObject.DBA, _this.formMode).then(serviceData => {\n          _this.dbaFilingModel = serviceData;\n\n          _this.LoadDBA();\n        }).catch(e => console.log(e));\n      });\n\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  }\n\n  RemoveFictiousFile(indexNo) {\n    if (this.FicticiousNames.length > 1) this.FicticiousNames.removeAt(indexNo);\n  }\n\n  LoadDBA() {\n    this.dbaFilingModel.FictitiousNameToFile.forEach(e => this.AddMoreFictiousName(\"YES\", e));\n    this.Form.controls.Instructions.setValue(this.dbaFilingModel.Instructions);\n\n    if (this.dbaFilingModel.FileData) {\n      this.UploadComponentObj.uploadedFileList = this.dbaFilingModel.FileData;\n      this.UploadComponentObj.toggleUploadView = 2;\n      this.FileData = this.dbaFilingModel.FileData;\n    }\n  }\n\n  AddMoreFictiousName(value, controlValue) {\n    if (value != 'YES') {\n      return;\n    }\n\n    let ficticiousForm = new FormGroup({\n      FictitiousName: new FormControl(controlValue || '', Validators.required)\n    });\n    this.FicticiousNames.push(ficticiousForm);\n  }\n\n  Save(dynamicFormData) {\n    this.dbaFilingModel.FictitiousNameToFile = this.Form.controls.FictitiousNameToFile.value.map(x => x.FictitiousName);\n    this.dbaFilingModel.Instructions = this.Form.controls.Instructions.value;\n\n    if (this.FileData) {\n      this.dbaFilingModel.FileData = this.FileData;\n    } else if (this.Form.controls.SelectedFilingOptionType.value == 1) {\n      this.toaster.error('File upload required.');\n      return;\n    }\n\n    if (dynamicFormData) {\n      this.dbaFilingModel.DynamicFormData = dynamicFormData.keyValuePair || [];\n    }\n\n    this.Api.SaveFilingService({\n      dBA: this.dbaFilingModel\n    }).subscribe(x => {\n      this.Form.reset();\n    });\n  }\n\n  Load() {\n    this.Form.controls.FictitiousNameToFile.setValue(this.dbaFilingModel.FictitiousNameToFile);\n    this.dbaFilingModel.FictitiousNameToFile.forEach(e => this.AddMoreFictiousName(\"YES\", e));\n    this.Form.controls.Instructions.setValue(this.dbaFilingModel.Instructions);\n  }\n\n  OnSave(dynamicFormData) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!dynamicFormData && _this2.DynamicFormComponentObj) {\n        var enteredDynamicFormData = yield _this2.DynamicFormComponentObj.getDynamicFormData();\n      }\n\n      try {\n        if (_this2.Form.valid) {\n          _this2.Save(dynamicFormData || enteredDynamicFormData);\n\n          return false;\n        }\n      } catch (er) {\n        console.error(er);\n      }\n    })();\n  }\n\n};\n\nDBAFilingComponent.ctorParameters = () => [{\n  type: LoaderService\n}, {\n  type: FilingInfoService\n}, {\n  type: CartService\n}, {\n  type: FormModeService\n}, {\n  type: FilingApiService\n}, {\n  type: ToastrService\n}, {\n  type: ActivatedRoute\n}, {\n  type: QuestionService\n}, {\n  type: ChangeDetectorRef\n}, {\n  type: PageTitleService\n}];\n\nDBAFilingComponent.propDecorators = {\n  UploadComponent: [{\n    type: ViewChild,\n    args: ['UploadComponent', {\n      static: false\n    }]\n  }],\n  DynamicFormComponent: [{\n    type: ViewChild,\n    args: [DynamicFormComponent, {\n      static: false\n    }]\n  }]\n};\nDBAFilingComponent = __decorate([Component({\n  selector: 'app-DBA',\n  template: __NG_CLI_RESOURCE__0\n})], DBAFilingComponent);\nexport { DBAFilingComponent };", "map": {"version": 3, "mappings": ";;;AAAA,SAASA,iBAAT,EAA4BC,SAA5B,EAA+CC,SAA/C,QAAgE,eAAhE;AACA,SAASC,UAAT,EAAqBC,gBAArB,EAAuCC,SAAvC,EAAkDC,WAAlD,QAAqE,gBAArE;AACA,SAASC,gBAAT,QAAiC,iCAAjC;AACA,SAASC,UAAT,QAA2B,kBAA3B;AACA,SAASC,aAAT,QAA8B,YAA9B;AACA,SAASC,cAAT,QAA+B,iBAA/B;AACA,SAASC,WAAT,QAA4B,mDAA5B;AACA,SAASC,2BAAT,QAA4C,iCAA5C;AACA,SAASC,uBAAT,QAAwC,qDAAxC;AACA,SAASC,eAAT,QAAgC,wDAAhC;AACA,SAASC,iBAAT,QAAkC,qCAAlC;AACA,SAASC,eAAT,QAAgC,qDAAhC;AACA,SAASC,iBAAT,QAAkC,0CAAlC;AACA,SAASC,aAAT,QAA8B,yDAA9B;AAGA,SAASC,eAAT,QAAgC,yDAAhC;AAEA,SAASC,oBAAT,QAAqC,uEAArC;AACA,SAASC,gBAAT,QAAiC,2DAAjC;IAOaC,kBAAkB,SAAlBA,kBAAkB;EAE3BC,YAAmBC,QAAnB,EAAmDC,iBAAnD,EAAiGC,WAAjG,EACWC,QADX,EAC8CC,GAD9C,EAC6EC,OAD7E,EAEYC,cAFZ,EAEoDC,OAFpD,EAEsFC,GAFtF,EAEsHC,gBAFtH,EAEwJ;IAFrI;IAAgC;IAA8C;IACtF;IAAmC;IAA+B;IACjE;IAAwC;IAAkC;IAAgC;IAGtH,aAAQ,IAAR;IAEA,mBAAqB,EAArB;IACA,iCAAmC,EAAnC;IACA,mBAAcjB,eAAd;IACA,6BAA+B,EAA/B;IACA,mBAAuB,KAAvB;IAGO,sBAA6B,IAAIR,UAAJ,EAA7B;IACA,4BAAuB,CAAC,EAAD,CAAvB;IACP,uBAAkB,IAAIJ,gBAAJ,CAAqB,EAArB,CAAlB;IAkEA,YAAO,IAAIC,SAAJ,CAAc;MACjB6B,oBAAoB,EAAE,KAAKC,eADV;MAEjBC,YAAY,EAAE,IAAI9B,WAAJ,CAA+B,IAA/B,EAAqC,CAACO,uBAAuB,CAACwB,mBAAzB,CAArC,CAFG;MAGjBC,wBAAwB,EAAE,IAAIhC,WAAJ,CAA+B,IAA/B;IAHT,CAAd,CAAP;EA/EC;;EAgBmE,IAAfiC,eAAe,CAACC,eAAD,EAAuC;IACvG,IAAIA,eAAJ,EAAqB;MAEjB,KAAKC,kBAAL,GAA0BD,eAA1B;MAEA,KAAKC,kBAAL,CAAwBC,gBAAxB,CAAyCC,SAAzC,CAAmDC,IAAI,IAAG;QACtD,KAAKC,QAAL,GAAgBD,IAAhB;MACH,CAFD,EAJiB,CAOjB;;MACA,IAAI,KAAKE,cAAL,CAAoBD,QAAxB,EAAkC;QAC9B,KAAKJ,kBAAL,CAAwBM,gBAAxB,GAA2C,KAAKD,cAAL,CAAoBD,QAA/D;QACA,KAAKJ,kBAAL,CAAwBO,gBAAxB,GAA2C,CAA3C;QACA,KAAKH,QAAL,GAAgB,KAAKC,cAAL,CAAoBD,QAApC;MACH;IACJ;;IACD,KAAKb,GAAL,CAASiB,aAAT;EACH;;EAG2E,IAApB7B,oBAAoB,CAAC8B,oBAAD,EAA2C;IACnH,IAAIA,oBAAJ,EAA0B;MACtB,KAAKC,uBAAL,GAA+BD,oBAA/B;IACH;EACJ;;EAEDE,QAAQ;IACJ,KAAK3B,iBAAL,CACK4B,QADL,CACc,KAAK5B,iBAAL,CAAuB6B,UAAvB,CAAkCC,GADhD,EACqD,KADrD,EAC4DZ,SAD5D,CACsEa,KAAK,IAAG;MACtE,KAAKvB,gBAAL,CAAsBwB,YAAtB,CAAmCD,KAAnC;IACH,CAHL,EADI,CAKJ;;IACA,KAAKhC,QAAL,CAAckC,IAAd;IACA,KAAKC,SAAL;IACA,KAAK/B,GAAL,CAASgC,aAAT,GAAyBjB,SAAzB,CAAmCkB,QAAQ,IAAG;MAC1C,KAAKC,WAAL,GAAmBD,QAAnB;IACH,CAFD;IAGA,KAAKjC,GAAL,CAASmC,8BAAT,CAAwC,IAAxC,EAA8C,KAA9C,EAAqD,KAArD,EAA4D,KAA5D,EAAmE,GAAnE,EAAwEpB,SAAxE,CAAmFC,IAAD,IAAS;MACvF,KAAKoB,qBAAL,GAA6BpB,IAA7B;MACA,IAAIqB,eAAe,GAAI,KAAKnB,cAAL,CAAoBoB,eAApB,IAAuC,KAAKpB,cAAL,CAAoBoB,eAApB,CAAoCC,MAApC,GAA6C,CAArF,GAA0F,KAAKrB,cAAL,CAAoBoB,eAA9G,GAAgI,EAAtJ;MACA,KAAKE,UAAL,GAAkB,KAAKrC,OAAL,CAAasC,kBAAb,CACdzB,IADc,EAEdqB,eAFc,EAGd,EAHc,CAAlB;MAKA,KAAKzC,QAAL,CAAc8C,IAAd;MACA,KAAKC,WAAL,GAAmB,IAAnB;IACH,CAVD;EAWH;;EAEDZ,SAAS;IAAA;;IACL,KAAK7B,cAAL,CAAoB0C,WAApB,CAAgC7B,SAAhC;MAAA,6BAA0C,WAAO8B,WAAP,EAAsB;QAC5D,IAAIA,WAAW,CAACC,EAAhB,EAAoB;UAChB,KAAI,CAACC,KAAL,GAAa,KAAb;QACH;;QACD1D,iBAAiB,CAAawD,WAAb,EAA0B,KAAI,CAAC7C,GAA/B,EAAoChB,2BAA2B,CAAC2C,GAAhE,EAAqE,KAAI,CAAC5B,QAA1E,CAAjB,CACKiD,IADL,CACWC,WAAD,IAAgB;UAClB,KAAI,CAAC/B,cAAL,GAAsB+B,WAAtB;;UACA,KAAI,CAACC,OAAL;QACH,CAJL,EAKKC,KALL,CAKYC,CAAD,IAAOC,OAAO,CAACC,GAAR,CAAYF,CAAZ,CALlB;MAMH,CAVD;;MAAA;QAAA;MAAA;IAAA;EAWH;;EASDG,kBAAkB,CAACC,OAAD,EAAQ;IACtB,IAAI,KAAKjD,eAAL,CAAqBgC,MAArB,GAA8B,CAAlC,EACI,KAAKhC,eAAL,CAAqBkD,QAArB,CAA8BD,OAA9B;EACP;;EAEDN,OAAO;IACH,KAAKhC,cAAL,CAAoBZ,oBAApB,CAAyCoD,OAAzC,CAAiDN,CAAC,IAAI,KAAKO,mBAAL,CAAyB,KAAzB,EAAgCP,CAAhC,CAAtD;IACA,KAAKQ,IAAL,CAAUC,QAAV,CAAmBrD,YAAnB,CAAgCsD,QAAhC,CAAyC,KAAK5C,cAAL,CAAoBV,YAA7D;;IACA,IAAI,KAAKU,cAAL,CAAoBD,QAAxB,EAAkC;MAC9B,KAAKJ,kBAAL,CAAwBM,gBAAxB,GAA2C,KAAKD,cAAL,CAAoBD,QAA/D;MACA,KAAKJ,kBAAL,CAAwBO,gBAAxB,GAA2C,CAA3C;MACA,KAAKH,QAAL,GAAgB,KAAKC,cAAL,CAAoBD,QAApC;IACH;EACJ;;EAED0C,mBAAmB,CAACI,KAAD,EAAQC,YAAR,EAA6B;IAC5C,IAAID,KAAK,IAAI,KAAb,EAAoB;MAAE;IAAS;;IAE/B,IAAIE,cAAc,GAAG,IAAIxF,SAAJ,CAAc;MAC/ByF,cAAc,EAAE,IAAIxF,WAAJ,CAA+BsF,YAAY,IAAI,EAA/C,EAAmDzF,UAAU,CAAC4F,QAA9D;IADe,CAAd,CAArB;IAIA,KAAK5D,eAAL,CAAqB6D,IAArB,CAA0BH,cAA1B;EACH;;EAEDI,IAAI,CAAChC,eAAD,EAAiB;IACjB,KAAKnB,cAAL,CAAoBZ,oBAApB,GAA2C,KAAKsD,IAAL,CAAUC,QAAV,CAAmBvD,oBAAnB,CAAwCyD,KAAxC,CAA8CO,GAA9C,CAAkDC,CAAC,IAAIA,CAAC,CAACL,cAAzD,CAA3C;IACA,KAAKhD,cAAL,CAAoBV,YAApB,GAAmC,KAAKoD,IAAL,CAAUC,QAAV,CAAmBrD,YAAnB,CAAgCuD,KAAnE;;IACA,IAAI,KAAK9C,QAAT,EAAmB;MACf,KAAKC,cAAL,CAAoBD,QAApB,GAA+B,KAAKA,QAApC;IACH,CAFD,MAEO,IAAI,KAAK2C,IAAL,CAAUC,QAAV,CAAmBnD,wBAAnB,CAA4CqD,KAA5C,IAAqD,CAAzD,EAA4D;MAC/D,KAAK9D,OAAL,CAAauE,KAAb,CAAmB,uBAAnB;MACA;IACH;;IACD,IAAInC,eAAJ,EAAqB;MACjB,KAAKnB,cAAL,CAAoBoB,eAApB,GAAsCD,eAAe,CAACoC,YAAhB,IAAgC,EAAtE;IACH;;IAED,KAAKzE,GAAL,CAAS0E,iBAAT,CAA2B;MAAEC,GAAG,EAAE,KAAKzD;IAAZ,CAA3B,EAAyDH,SAAzD,CAAmEwD,CAAC,IAAG;MACnE,KAAKX,IAAL,CAAUgB,KAAV;IACH,CAFD;EAGH;;EAGDC,IAAI;IACA,KAAKjB,IAAL,CAAUC,QAAV,CAAmBvD,oBAAnB,CAAwCwD,QAAxC,CAAiD,KAAK5C,cAAL,CAAoBZ,oBAArE;IACA,KAAKY,cAAL,CAAoBZ,oBAApB,CAAyCoD,OAAzC,CAAiDN,CAAC,IAAI,KAAKO,mBAAL,CAAyB,KAAzB,EAAgCP,CAAhC,CAAtD;IACA,KAAKQ,IAAL,CAAUC,QAAV,CAAmBrD,YAAnB,CAAgCsD,QAAhC,CAAyC,KAAK5C,cAAL,CAAoBV,YAA7D;EACH;;EAEKsE,MAAM,CAACzC,eAAD,EAAiB;IAAA;;IAAA;MACzB,IAAI,CAACA,eAAD,IAAoB,MAAI,CAACd,uBAA7B,EAAsD;QAClD,IAAIwD,sBAAsB,SAAS,MAAI,CAACxD,uBAAL,CAA6ByD,kBAA7B,EAAnC;MACH;;MACD,IAAI;QACA,IAAI,MAAI,CAACpB,IAAL,CAAUqB,KAAd,EAAqB;UAEjB,MAAI,CAACZ,IAAL,CAAUhC,eAAe,IAAI0C,sBAA7B;;UACA,OAAO,KAAP;QAEH;MACJ,CAPD,CAQA,OAAOG,EAAP,EAAW;QACP7B,OAAO,CAACmB,KAAR,CAAcU,EAAd;MACH;IAdwB;EAe5B;;AA5J0B;;;;;;;;;;;;;;;;;;;;;;;;;;UAqB1B5G;IAAS6G,OAAC,iBAAD,EAAoB;MAAEC,MAAM,EAAE;IAAV,CAApB;;;UAmBT9G;IAAS6G,OAAC3F,oBAAD,EAAuB;MAAE4F,MAAM,EAAE;IAAV,CAAvB;;;AAxCD1F,kBAAkB,eAJ9BrB,SAAS,CAAC;EACPgH,QAAQ,EAAE,SADH;EAEPC;AAFO,CAAD,CAIqB,GAAlB5F,kBAAkB,CAAlB;SAAAA", "names": ["ChangeDetectorRef", "Component", "ViewChild", "Validators", "UntypedFormArray", "FormGroup", "FormControl", "FilingApiService", "DBAFilling", "ToastrService", "ActivatedRoute", "CartService", "FilingServiceResponseObject", "CustomSharedValidations", "FormModeService", "FilingInfoService", "SubCategoryCode", "LoadFilingService", "LoaderService", "QuestionService", "DynamicFormComponent", "PageTitleService", "DBAFilingComponent", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "filingInfoService", "cartService", "formMode", "Api", "toaster", "activatedRoute", "service", "cdr", "pageTitleService", "FictitiousNameToFile", "FicticiousNames", "Instructions", "specialInstructions", "SelectedFilingOptionType", "UploadComponent", "uploadComponent", "UploadComponentObj", "EmitUploadedData", "subscribe", "data", "FileData", "dbaFilingModel", "uploadedFileList", "toggleUploadView", "detectChanges", "dynamicFormComponent", "DynamicFormComponentObj", "ngOnInit", "get<PERSON><PERSON><PERSON>", "SubCatCode", "DBA", "label", "setPageTitle", "show", "StartLoad", "GetFilingType", "response", "FilingTypes", "GetDynamicFormMasterDataUpload", "dynamicFormMasterData", "dynamicFormData", "DynamicFormData", "length", "questions$", "getMappedQuestions", "hide", "isQuestions", "queryParams", "queryString", "on", "isAdd", "then", "serviceData", "LoadDBA", "catch", "e", "console", "log", "RemoveFictiousFile", "indexNo", "removeAt", "for<PERSON>ach", "AddMoreFictiousName", "Form", "controls", "setValue", "value", "controlValue", "ficticiousForm", "FictitiousName", "required", "push", "Save", "map", "x", "error", "keyValuePair", "SaveFilingService", "dBA", "reset", "Load", "OnSave", "enteredDynamicFormData", "getDynamicFormData", "valid", "er", "args", "static", "selector", "template"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\viatech\\Ecom\\client\\acs\\src\\app\\Modules\\FilingService\\Pages\\dba-file\\DBA.component.ts"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';\r\nimport { Validators, UntypedFormArray, FormGroup, FormControl } from '@angular/forms';\r\nimport { FilingApiService } from '../../Services/FilingApiService';\r\nimport { DBAFilling } from '../../Models/DBA';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { CartService } from 'src/app/Modules/Shared/Services/cart/cart.service';\r\nimport { FilingServiceResponseObject } from '../../Enums/filing-service-enum';\r\nimport { CustomSharedValidations } from 'src/app/Modules/Shared/functions/custom-validations';\r\nimport { FormModeService } from 'src/app/Modules/Shared/Services/Common/FormModeService';\r\nimport { FilingInfoService } from '../../Services/filing-price.service';\r\nimport { SubCategoryCode } from 'src/app/Modules/Shared/Enums/sub-category-code.enum';\r\nimport { LoadFilingService } from '../../Functions/filing-service-load.func';\r\nimport { LoaderService } from '../../../../Modules/Core/Services/Common/loader.service';\r\nimport { QuestionBase } from 'src/app/Modules/Shared/Models/DynamicForm/question-base';\r\nimport { Observable } from 'rxjs';\r\nimport { QuestionService } from 'src/app/Modules/Shared/Services/Common/question.service';\r\nimport { FileUploaderComponent } from 'src/app/Modules/Shared/Components/file-uploader/file-uploader.component';\r\nimport { DynamicFormComponent } from 'src/app/Modules/Shared/Components/dynamic-form/dynamic-form.component';\r\nimport { PageTitleService } from 'src/app/Modules/Shared/Services/Common/page-title.service';\r\n\r\n\r\n@Component({\r\n    selector: 'app-DBA',\r\n    templateUrl: 'DBA.component.html'\r\n})\r\nexport class DBAFilingComponent implements OnInit {\r\n\r\n    constructor(public isLoader: LoaderService, public filingInfoService: FilingInfoService, private cartService: CartService,\r\n        public formMode: FormModeService, private Api: FilingApiService, private toaster: ToastrService,\r\n        private activatedRoute: ActivatedRoute, private service: QuestionService, private cdr: ChangeDetectorRef, private pageTitleService: PageTitleService) {\r\n    }\r\n\r\n    isAdd = true;\r\n    FileData: any;\r\n    FilingTypes: any[] = []\r\n    AdditionalFictitiousTypes: any[] = []\r\n    SubCatCodes = SubCategoryCode;\r\n    dynamicFormMasterData: any[] = [];\r\n    isQuestions: boolean = false;\r\n    questions$: Observable<QuestionBase<any>[]>;\r\n\r\n    public dbaFilingModel: DBAFilling = new DBAFilling();\r\n    public fictitiousNameToFile = [''];\r\n    FicticiousNames = new UntypedFormArray([]);\r\n\r\n    UploadComponentObj: FileUploaderComponent;\r\n    @ViewChild('UploadComponent', { static: false }) set UploadComponent(uploadComponent: FileUploaderComponent) {\r\n        if (uploadComponent) {\r\n\r\n            this.UploadComponentObj = uploadComponent;\r\n\r\n            this.UploadComponentObj.EmitUploadedData.subscribe(data => {\r\n                this.FileData = data\r\n            });\r\n            //Mapping uploaded file data to FileUploadComponent in case of edit\r\n            if (this.dbaFilingModel.FileData) {\r\n                this.UploadComponentObj.uploadedFileList = this.dbaFilingModel.FileData\r\n                this.UploadComponentObj.toggleUploadView = 2\r\n                this.FileData = this.dbaFilingModel.FileData;\r\n            }\r\n        }\r\n        this.cdr.detectChanges();\r\n    }\r\n\r\n    DynamicFormComponentObj: DynamicFormComponent;\r\n    @ViewChild(DynamicFormComponent, { static: false }) set DynamicFormComponent(dynamicFormComponent: DynamicFormComponent) {\r\n        if (dynamicFormComponent) {\r\n            this.DynamicFormComponentObj = dynamicFormComponent;\r\n        }\r\n    }\r\n\r\n    ngOnInit() {\r\n        this.filingInfoService\r\n            .getLabel(this.filingInfoService.SubCatCode.DBA, '004').subscribe(label => {\r\n                this.pageTitleService.setPageTitle(label);\r\n            });\r\n        //Get all States from store if store is empty call states API\r\n        this.isLoader.show();\r\n        this.StartLoad();\r\n        this.Api.GetFilingType().subscribe(response => {\r\n            this.FilingTypes = response;\r\n        })\r\n        this.Api.GetDynamicFormMasterDataUpload(\"FS\", \"004\", \"110\", \"NaN\", 'A').subscribe((data) => {\r\n            this.dynamicFormMasterData = data;\r\n            var dynamicFormData = (this.dbaFilingModel.DynamicFormData && this.dbaFilingModel.DynamicFormData.length > 0) ? this.dbaFilingModel.DynamicFormData : [];\r\n            this.questions$ = this.service.getMappedQuestions(\r\n                data,\r\n                dynamicFormData,\r\n                \"\"\r\n            );\r\n            this.isLoader.hide();\r\n            this.isQuestions = true;\r\n        });\r\n    }\r\n\r\n    StartLoad() {\r\n        this.activatedRoute.queryParams.subscribe(async (queryString) => {\r\n            if (queryString.on) {\r\n                this.isAdd = false;\r\n            }\r\n            LoadFilingService<DBAFilling>(queryString, this.Api, FilingServiceResponseObject.DBA, this.formMode)\r\n                .then((serviceData) => {\r\n                    this.dbaFilingModel = serviceData;\r\n                    this.LoadDBA();\r\n                })\r\n                .catch((e) => console.log(e));\r\n        });\r\n    }\r\n\r\n    Form = new FormGroup({\r\n        FictitiousNameToFile: this.FicticiousNames,\r\n        Instructions: new FormControl<string | null>(null, [CustomSharedValidations.specialInstructions]),\r\n        SelectedFilingOptionType: new FormControl<number | null>(null)\r\n    })\r\n\r\n\r\n    RemoveFictiousFile(indexNo) {\r\n        if (this.FicticiousNames.length > 1)\r\n            this.FicticiousNames.removeAt(indexNo)\r\n    }\r\n\r\n    LoadDBA() {\r\n        this.dbaFilingModel.FictitiousNameToFile.forEach(e => this.AddMoreFictiousName(\"YES\", e))\r\n        this.Form.controls.Instructions.setValue(this.dbaFilingModel.Instructions);\r\n        if (this.dbaFilingModel.FileData) {\r\n            this.UploadComponentObj.uploadedFileList = this.dbaFilingModel.FileData\r\n            this.UploadComponentObj.toggleUploadView = 2\r\n            this.FileData = this.dbaFilingModel.FileData;\r\n        }\r\n    }\r\n\r\n    AddMoreFictiousName(value, controlValue?: string) {\r\n        if (value != 'YES') { return; }\r\n\r\n        let ficticiousForm = new FormGroup({\r\n            FictitiousName: new FormControl<string | null>(controlValue || '', Validators.required)\r\n        })\r\n\r\n        this.FicticiousNames.push(ficticiousForm);\r\n    }\r\n\r\n    Save(dynamicFormData?) {\r\n        this.dbaFilingModel.FictitiousNameToFile = this.Form.controls.FictitiousNameToFile.value.map(x => x.FictitiousName);\r\n        this.dbaFilingModel.Instructions = this.Form.controls.Instructions.value;\r\n        if (this.FileData) {\r\n            this.dbaFilingModel.FileData = this.FileData;\r\n        } else if (this.Form.controls.SelectedFilingOptionType.value == 1) {\r\n            this.toaster.error('File upload required.');\r\n            return;\r\n        }\r\n        if (dynamicFormData) {\r\n            this.dbaFilingModel.DynamicFormData = dynamicFormData.keyValuePair || [];\r\n        }\r\n\r\n        this.Api.SaveFilingService({ dBA: this.dbaFilingModel }).subscribe(x => {\r\n            this.Form.reset();\r\n        })\r\n    }\r\n\r\n\r\n    Load() {\r\n        this.Form.controls.FictitiousNameToFile.setValue(this.dbaFilingModel.FictitiousNameToFile)\r\n        this.dbaFilingModel.FictitiousNameToFile.forEach(e => this.AddMoreFictiousName(\"YES\", e))\r\n        this.Form.controls.Instructions.setValue(this.dbaFilingModel.Instructions)\r\n    }\r\n\r\n    async OnSave(dynamicFormData?) {\r\n        if (!dynamicFormData && this.DynamicFormComponentObj) {\r\n            var enteredDynamicFormData = await this.DynamicFormComponentObj.getDynamicFormData()\r\n        }\r\n        try {\r\n            if (this.Form.valid) {\r\n\r\n                this.Save(dynamicFormData || enteredDynamicFormData);\r\n                return false;\r\n\r\n            }\r\n        }\r\n        catch (er) {\r\n            console.error(er)\r\n        }\r\n    }\r\n\r\n}"]}, "metadata": {}, "sourceType": "module"}