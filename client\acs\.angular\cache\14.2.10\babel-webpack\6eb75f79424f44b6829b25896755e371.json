{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./imprinted-product.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./imprinted-product.component.css?ngResource\";\nimport { Component, ViewChild } from \"@angular/core\";\nimport { ActivatedRoute } from \"@angular/router\";\nimport { SimpleProductService } from \"src/app/Modules/Core/Services/Common/simple-product.service\";\nimport { IMPRINTED_PRODUCTS_DATA } from \"../../data/imprinted-products-data\";\nimport { QuestionService } from \"src/app/Modules/Shared/Services/Common/question.service\";\nimport { FormControl, FormGroup } from \"@angular/forms\";\nimport { DBAFilling } from \"src/app/Modules/FilingService/Models/DBA\";\nimport { GoogleAnalyticsService } from \"src/app/Modules/Shared/Services/Common/google-analytics.service\";\nimport { GAEvents, GAEventsTypes } from \"src/app/Modules/Shared/Enums/google-analytics.enum\";\nimport { PageTitleService } from \"src/app/Modules/Shared/Services/Common/page-title.service\";\nlet ImprintedProductComponent = class ImprintedProductComponent {\n  constructor(route, service, questionService, googleAnalyticsService, pageTitleService) {\n    this.route = route;\n    this.service = service;\n    this.questionService = questionService;\n    this.googleAnalyticsService = googleAnalyticsService;\n    this.pageTitleService = pageTitleService;\n    this.bulkPrices = [];\n    this.colorList = [];\n    this.selectedColor = \"\";\n    this.imagesArray = [];\n    this.selectedImage = \"\";\n    this.submitted = false;\n    this.selectedTagType = null;\n    this.productDetails = null;\n    this.pageData = null;\n    this.dynamicFormControlsData = {};\n    this.isGrouped = false;\n    this.groupedProducts = {};\n    this.inMultiplesOf = 1;\n    this.minQuantity = 1;\n    this.dynamicFormUploadedFiles = [];\n    this.Form = new FormGroup({\n      FictitiousNameToFile: new FormControl(null),\n      Instructions: new FormControl(null),\n      SelectedFilingOptionType: new FormControl(null)\n    });\n    this.formFlag = false;\n    this.skuResponse = [];\n    this.dbaFilingModel = new DBAFilling();\n    this.envConfig = JSON.parse(localStorage.getItem(\"envConfig\"));\n  }\n\n  set ImprintedFormComponent(ImprintedFormComponent) {\n    if (ImprintedFormComponent) {\n      this.ImprintedFormComponentObj = ImprintedFormComponent;\n    }\n  }\n\n  ngOnInit() {\n    this.route.data.subscribe(v => {\n      this.product = v.product;\n      this.category = v.category;\n      this.subCategory = v.subCategory;\n      this.inMultiplesOf = v.inMultiplesOf || 1;\n      this.minQuantity = v.minQuantity || 1;\n      this.isGrouped = v.isGrouped;\n      this.productDetails = IMPRINTED_PRODUCTS_DATA[`${this.product}${this.category}${this.subCategory}`];\n      this.groupedProducts = {};\n      this.getProductDetails();\n      this.productSku();\n    });\n    this.service.GetDynamicFormMasterData(this.product, this.category, this.subCategory, \"--\", 'A').subscribe(data => {\n      this.questions$ = this.questionService.getMappedQuestions(data, [], \"\");\n    });\n  }\n\n  returnZero() {\n    return 0;\n  }\n\n  validQuantity() {\n    return this.quantity && this.quantity % this.multipleOfValue() == 0 && this.quantity >= this.minQuantityValue();\n  }\n\n  minQuantityValue() {\n    return this.selectedTagType?.minQuantity ? this.selectedTagType?.minQuantity : this.minQuantity;\n  }\n\n  multipleOfValue() {\n    return this.selectedTagType?.inMultiplesOf ? this.selectedTagType?.inMultiplesOf : this.inMultiplesOf;\n  }\n\n  OnSave(dynamicFormData) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      if (!dynamicFormData && _this.ImprintedFormComponentObj) {\n        var enteredDynamicFormData = yield _this.ImprintedFormComponentObj.getDynamicFormData();\n      }\n\n      try {\n        if (_this.Form.valid) {\n          console.log(dynamicFormData, enteredDynamicFormData); // this.Save(dynamicFormData || enteredDynamicFormData);\n\n          _this.dynamicFormControlsData = dynamicFormData || enteredDynamicFormData;\n          console.log(_this.dynamicFormControlsData.keyValuePair);\n\n          _this.addOrderToCart();\n\n          return false;\n        }\n      } catch (er) {\n        console.error(er);\n      }\n    })();\n  }\n\n  imageUrl() {\n    const subCategory = this.subCategory;\n    let url = `${this.envConfig.blobUrl}${this.product}_${this.category}_${subCategory}`;\n\n    if (this.selectedTagType?.colorOptions && this.selectedTagType?.entityCode && this.selectedColor) {\n      url += `_${this.selectedTagType?.entityCode}_${this.selectedColor}`;\n    }\n\n    if (!this.selectedTagType?.colorOptions && this.selectedTagType?.entityCode) {\n      url += `_${this.selectedTagType?.entityCode}`;\n    }\n\n    return url + \".jpg\";\n  }\n\n  getColors() {\n    const subCategoryCode = this.subCategory;\n    this._subscriptions = this.service.getColors(this.product, this.category, subCategoryCode, this.selectedTagType?.entityCode).subscribe(response => {\n      this.selectedTagType.colorList = response;\n      this.onColorChange(this.selectedTagType.colorList[0]?.code);\n    });\n  }\n\n  getProductDetails() {\n    const subCategoryCode = this.subCategory;\n    this.service.getProducts(this.product, this.category, subCategoryCode).subscribe(response => {\n      const categoryData = response.filter(t => t.categoryCode == this.category);\n\n      if (categoryData.length > 0) {\n        const category = categoryData[0];\n        this.categoryName = category.description;\n        const productsData = category.products.filter(t => t.productName == subCategoryCode);\n\n        if (productsData.length > 0) {\n          const product = productsData[0];\n          this.pageTitle = product.description;\n          this.description = product.fullDescription?.trim() || \"\";\n          this.unitPrice = product.price;\n          this.trackProductPage();\n          this.pageTitleService.setPageTitle(this.pageTitle);\n        }\n      }\n    });\n  }\n\n  productSku() {\n    const subCategoryCode = this.subCategory;\n\n    if (subCategoryCode) {\n      this._subscriptions = this.service.getProductSku(this.product, this.category, subCategoryCode).subscribe(response => {\n        this.skuResponse = response;\n        this.productDetails = this.productDetails?.filter(product => this.skuResponse.some(exclude => exclude.subCategoryCode === this.subCategory && exclude.entityCode === product.entityCode && exclude.optionCode === product.optionCode));\n\n        if (this.isGrouped) {\n          this.groupedProducts = this.productDetails.reduce((acc, item) => {\n            if (!acc[item[\"group\"]]) {\n              acc[item[\"group\"]] = [];\n            }\n\n            acc[item[\"group\"]].push(item);\n            return acc;\n          }, {});\n        }\n\n        this.imagesArray = [\"DEFAULT_IMAGE.png\"];\n        this.selectedImage = this.imagesArray[0];\n\n        if (this.productDetails.length == 1) {\n          this.selectedTagType = this.productDetails[0];\n          this.imagesArray = this.selectedTagType?.imagesArray;\n          this.entityCode = this.selectedTagType.entityCode;\n          this.selectedImage = this.selectedTagType?.imagesArray[0];\n          this.quantity = this.multipleOfValue() > 1 ? Math.max(this.multipleOfValue(), 1) : Math.max(this.minQuantityValue(), 1);\n\n          if (this.selectedTagType.colorOptions) {\n            this.getColors();\n          } else {\n            this.selectedTagType.colorList = [];\n            this.updatePrice(); // this.tieredPricing();\n          }\n\n          this.trackProductSelect(this.selectedTagType);\n        }\n      });\n    }\n  }\n\n  getSKUID(product) {\n    const matchedObject = this.skuResponse?.find(item => item.productCode === this.product && item.categoryCode === this.category && item.subCategoryCode === this.subCategory && item.entityCode === product.entityCode && item.optionCode === product.optionCode);\n    return matchedObject?.productSku || \"\";\n  }\n\n  onTypeSelect(tagType) {\n    this.selectedColor = \"\";\n    this.selectedTagType = tagType;\n    this.entityCode = tagType.entityCode;\n\n    if (this._subscriptions) {\n      this._subscriptions.unsubscribe();\n    }\n\n    this.formFlag = false;\n\n    if (this.selectedTagType.hasOwnProperty(\"imagesArray\")) {\n      this.imagesArray = this.selectedTagType.imagesArray;\n      this.selectedImage = this.imagesArray[0];\n    }\n\n    this.quantity = this.multipleOfValue() > 1 ? Math.max(this.multipleOfValue(), 1) : Math.max(this.minQuantityValue(), 1);\n\n    if (this.selectedTagType.colorOptions) {\n      this.getColors();\n    } else {\n      this.selectedTagType.colorList = [];\n      this.updatePrice(); // this.tieredPricing();\n    }\n\n    this.trackProductSelect(tagType);\n  }\n\n  setClickedImage(image) {\n    // this.selectedImage = image;\n    this.trackImageSelect(this.selectedTagType, image);\n  }\n\n  onColorChange(color) {\n    this.selectedColor = color;\n    this.updatePrice(); // this.tieredPricing();\n  }\n\n  updatePrice() {\n    const subCategoryCode = this.subCategory;\n\n    if (subCategoryCode && this.selectedTagType) {\n      const color = this.selectedTagType.colorOptions ? this.selectedColor || this.selectedTagType.colorList[0]?.code : \"\";\n      this._subscriptions = this.service.getProductPrice(this.product, this.category, subCategoryCode, this.quantity || 1, this.selectedTagType.entityCode, this.selectedTagType.optionCode, color).subscribe(response => {\n        this.unitPrice = response; //> 0 ? response / (this.quantity || 1) : response;\n      });\n    }\n  }\n\n  tieredPricing() {\n    const subCategoryCode = this.subCategory;\n    const color = this.selectedTagType.colorOptions ? this.selectedColor || this.selectedTagType.colorList[0]?.code : \"\";\n    this.service.blumbergTieredPricing(this.product, this.category, subCategoryCode, this.selectedTagType.entityCode, this.selectedTagType.optionCode, color).subscribe(response => {\n      this.bulkPrices = response;\n    });\n  }\n\n  addOrderToCart() {\n    if (this.quantity > 0 && this.selectedTagType) {\n      this.submitted = true;\n      this.service.addToCart(this.product, this.category, this.subCategory, this.quantity, this.selectedColor, this.selectedTagType.entityCode, \"\", this.selectedTagType.optionCode, \"\", \"\", this.dynamicFormControlsData.keyValuePair, this.dynamicFormUploadedFiles).subscribe(response => {\n        this.submitted = false;\n\n        if (response.responseCode === 200) {\n          this.trackAddToCart(this.selectedTagType);\n          this.resetData();\n        }\n      });\n    }\n  }\n\n  resetData() {\n    this.quantity = null;\n    this.selectedTagType = null;\n    this.unitPrice = null;\n    this.bulkPrices = null;\n  }\n\n  clearForm() {\n    this.ImprintedFormComponentObj?.dynamicform?.reset();\n  }\n\n  showHideForm() {\n    this.clearForm();\n    this.service.GetDynamicFormMasterData(this.product, this.category, this.subCategory, \"--\", \"A\", this.entityCode).subscribe(data => {\n      this.questions$ = this.questionService.getMappedQuestions(data, [], \"\");\n      this.formFlag = true;\n    });\n  }\n\n  fileUplaod(files) {\n    this.dynamicFormUploadedFiles = files;\n  }\n\n  showImprintForm() {\n    this.showHideForm();\n    this.trackAddImprint();\n  }\n\n  onQuantityChange() {\n    this.updatePrice();\n    this.trackQuantity();\n  }\n\n  getPageGAParams() {\n    return {\n      page_id: `${this.product}_${this.category}_${this.subCategory}`,\n      page_name: this.pageTitle,\n      category: this.categoryName,\n      type: \"Estate & Will Supplies\"\n    };\n  }\n\n  getProductGAParams(product) {\n    return {\n      product_name: `${product.group ? product.group + \" \" : \"\"}${product.label}`,\n      product_sku: this.getSKUID(product) // product_category: `${product.entityCode} - ${product.optionCode} - ${this.selectedColor}`,\n      // product_entity_code: product.entityCode,\n      // product_option_code: product.optionCode,\n\n    };\n  }\n\n  trackProductPage() {\n    this.googleAnalyticsService.sendEvent(GAEventsTypes.Imprinted + GAEvents.PageViewed, this.getPageGAParams());\n  }\n\n  trackProductSelect(product) {\n    this.googleAnalyticsService.sendEvent(GAEventsTypes.Imprinted + GAEvents.ProductSelected, { ...this.getPageGAParams(),\n      ...this.getProductGAParams(product)\n    });\n  }\n\n  trackImageSelect(product, imageName) {\n    this.googleAnalyticsService.sendEvent(GAEventsTypes.Imprinted + GAEvents.ImageSelected, { ...this.getPageGAParams(),\n      ...this.getProductGAParams(product),\n      product_image: imageName\n    });\n  }\n\n  trackQuantity() {\n    const product = this.selectedTagType;\n    this.googleAnalyticsService.sendEvent(GAEventsTypes.Imprinted + GAEvents.Quantity, { ...this.getPageGAParams(),\n      ...this.getProductGAParams(product),\n      product_color: this.selectedColor,\n      product_quantity: this.quantity\n    });\n  }\n\n  trackAddImprint() {\n    const product = this.selectedTagType;\n    this.googleAnalyticsService.sendEvent(GAEventsTypes.Imprinted + GAEvents.AddImprints, { ...this.getPageGAParams(),\n      ...this.getProductGAParams(product)\n    });\n  }\n\n  trackAddToCart(product) {\n    this.googleAnalyticsService.sendEvent(GAEventsTypes.Imprinted + GAEvents.AddToCart, { ...this.getPageGAParams(),\n      ...this.getProductGAParams(product),\n      product_color: this.selectedColor,\n      product_quantity: this.quantity,\n      product_unit_price: this.unitPrice > 0 ? this.unitPrice / (this.quantity || 1) : this.unitPrice,\n      product_total_price: this.unitPrice\n    });\n  }\n\n};\n\nImprintedProductComponent.ctorParameters = () => [{\n  type: ActivatedRoute\n}, {\n  type: SimpleProductService\n}, {\n  type: QuestionService\n}, {\n  type: GoogleAnalyticsService\n}, {\n  type: PageTitleService\n}];\n\nImprintedProductComponent.propDecorators = {\n  ImprintedFormComponent: [{\n    type: ViewChild,\n    args: [\"ImprintedFormComponent\", {\n      static: false\n    }]\n  }]\n};\nImprintedProductComponent = __decorate([Component({\n  selector: 'app-imprinted-product',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], ImprintedProductComponent);\nexport { ImprintedProductComponent };", "map": {"version": 3, "mappings": ";;;;AAAA,SAASA,SAAT,EAA4BC,SAA5B,QAA6C,eAA7C;AACA,SAASC,cAAT,QAA+B,iBAA/B;AACA,SAASC,oBAAT,QAAqC,6DAArC;AASA,SAASC,uBAAT,QAAwC,oCAAxC;AAGA,SAASC,eAAT,QAAgC,yDAAhC;AACA,SAASC,WAAT,EAAsBC,SAAtB,QAAuC,gBAAvC;AACA,SAASC,UAAT,QAA2B,0CAA3B;AAGA,SAASC,sBAAT,QAAuC,iEAAvC;AACA,SAASC,QAAT,EAAmBC,aAAnB,QAAwC,oDAAxC;AACA,SAASC,gBAAT,QAAiC,2DAAjC;IAOaC,yBAAyB,SAAzBA,yBAAyB;EAmDpCC,YACUC,KADV,EAEUC,OAFV,EAGUC,eAHV,EAIUC,sBAJV,EAKUC,gBALV,EAK4C;IAJlC;IACA;IACA;IACA;IACA;IAzCV,kBAAiC,EAAjC;IACA,iBAAmC,EAAnC;IACA,qBAA+B,EAA/B;IACA,mBAAc,EAAd;IACA,qBAAgB,EAAhB;IAEA,iBAAqB,KAArB;IACA,uBAAwC,IAAxC;IACA,sBAAyC,IAAzC;IAEA,gBAAiC,IAAjC;IACA,+BAA+B,EAA/B;IACA,iBAAqB,KAArB;IACA,uBAAsD,EAAtD;IACA,qBAAgB,CAAhB;IACA,mBAAc,CAAd;IACA,gCAAmC,EAAnC;IAWA,YAAO,IAAIZ,SAAJ,CAAc;MACnBa,oBAAoB,EAAE,IAAId,WAAJ,CAA+B,IAA/B,CADH;MAEnBe,YAAY,EAAE,IAAIf,WAAJ,CAA+B,IAA/B,CAFK;MAGnBgB,wBAAwB,EAAE,IAAIhB,WAAJ,CAA+B,IAA/B;IAHP,CAAd,CAAP;IAKA,gBAAoB,KAApB;IACA,mBAAqB,EAArB;IAkDO,sBAA6B,IAAIE,UAAJ,EAA7B;IAxCL,KAAKe,SAAL,GAAiBC,IAAI,CAACC,KAAL,CAAWC,YAAY,CAACC,OAAb,CAAqB,WAArB,CAAX,CAAjB;EACD;;EAvByB,IAAtBC,sBAAsB,CAACA,sBAAD,EAA+C;IACvE,IAAIA,sBAAJ,EAA4B;MAC1B,KAAKC,yBAAL,GAAiCD,sBAAjC;IACD;EACF;;EAqBDE,QAAQ;IACN,KAAKf,KAAL,CAAWgB,IAAX,CAAgBC,SAAhB,CAA2BC,CAAD,IAAM;MAC9B,KAAKC,OAAL,GAAeD,CAAC,CAACC,OAAjB;MACA,KAAKC,QAAL,GAAgBF,CAAC,CAACE,QAAlB;MACA,KAAKC,WAAL,GAAmBH,CAAC,CAACG,WAArB;MACA,KAAKC,aAAL,GAAqBJ,CAAC,CAACI,aAAF,IAAmB,CAAxC;MACA,KAAKC,WAAL,GAAmBL,CAAC,CAACK,WAAF,IAAiB,CAApC;MACA,KAAKC,SAAL,GAAiBN,CAAC,CAACM,SAAnB;MAEA,KAAKC,cAAL,GACEpC,uBAAuB,CAAC,GAAG,KAAK8B,OAAO,GAAG,KAAKC,QAAQ,GAAG,KAAKC,WAAW,EAAnD,CADzB;MAGA,KAAKK,eAAL,GAAuB,EAAvB;MAEA,KAAKC,iBAAL;MACA,KAAKC,UAAL;IACD,CAfD;IAiBA,KAAK3B,OAAL,CAAa4B,wBAAb,CAAsC,KAAKV,OAA3C,EAAoD,KAAKC,QAAzD,EAAmE,KAAKC,WAAxE,EAAqF,IAArF,EAA2F,GAA3F,EAAgGJ,SAAhG,CAA2GD,IAAD,IAAS;MACjH,KAAKc,UAAL,GAAkB,KAAK5B,eAAL,CAAqB6B,kBAArB,CAAwCf,IAAxC,EAA8C,EAA9C,EAAkD,EAAlD,CAAlB;IACD,CAFD;EAID;;EACDgB,UAAU;IACR,OAAO,CAAP;EACD;;EAEDC,aAAa;IACX,OAAO,KAAKC,QAAL,IAAiB,KAAKA,QAAL,GAAgB,KAAKC,eAAL,EAAhB,IAA0C,CAA3D,IAAgE,KAAKD,QAAL,IAAiB,KAAKE,gBAAL,EAAxF;EACD;;EACDA,gBAAgB;IACd,OAAO,KAAKC,eAAL,EAAsBd,WAAtB,GAAoC,KAAKc,eAAL,EAAsBd,WAA1D,GAAwE,KAAKA,WAApF;EACD;;EACDY,eAAe;IACb,OAAO,KAAKE,eAAL,EAAsBf,aAAtB,GAAsC,KAAKe,eAAL,EAAsBf,aAA5D,GAA4E,KAAKA,aAAxF;EACD;;EAIKgB,MAAM,CAACC,eAAD,EAAiB;IAAA;;IAAA;MAC3B,IAAI,CAACA,eAAD,IAAoB,KAAI,CAACzB,yBAA7B,EAAwD;QACtD,IAAI0B,sBAAsB,SAAS,KAAI,CAAC1B,yBAAL,CAA+B2B,kBAA/B,EAAnC;MACD;;MACD,IAAI;QACF,IAAI,KAAI,CAACC,IAAL,CAAUC,KAAd,EAAqB;UACnBC,OAAO,CAACC,GAAR,CAAYN,eAAZ,EAA6BC,sBAA7B,EADmB,CAEnB;;UACA,KAAI,CAACM,uBAAL,GAA+BP,eAAe,IAAIC,sBAAlD;UACAI,OAAO,CAACC,GAAR,CAAY,KAAI,CAACC,uBAAL,CAA6BC,YAAzC;;UACA,KAAI,CAACC,cAAL;;UACA,OAAO,KAAP;QAED;MACF,CAVD,CAWA,OAAOC,EAAP,EAAW;QACTL,OAAO,CAACM,KAAR,CAAcD,EAAd;MACD;IAjB0B;EAkB5B;;EAGDE,QAAQ;IACN,MAAM9B,WAAW,GAAG,KAAKA,WAAzB;IACA,IAAI+B,GAAG,GAAG,GAAG,KAAK5C,SAAL,CAAe6C,OAAO,GAAG,KAAKlC,OAAO,IAAI,KAAKC,QAAQ,IAAIC,WAAW,EAAlF;;IACA,IACE,KAAKgB,eAAL,EAAsBiB,YAAtB,IACA,KAAKjB,eAAL,EAAsBkB,UADtB,IAEA,KAAKC,aAHP,EAIE;MACAJ,GAAG,IAAI,IAAI,KAAKf,eAAL,EAAsBkB,UAAU,IAAI,KAAKC,aAAa,EAAjE;IACD;;IACD,IACE,CAAC,KAAKnB,eAAL,EAAsBiB,YAAvB,IACA,KAAKjB,eAAL,EAAsBkB,UAFxB,EAGE;MACAH,GAAG,IAAI,IAAI,KAAKf,eAAL,EAAsBkB,UAAU,EAA3C;IACD;;IACD,OAAOH,GAAG,GAAG,MAAb;EACD;;EAEDK,SAAS;IACP,MAAMC,eAAe,GAAG,KAAKrC,WAA7B;IAEA,KAAKsC,cAAL,GAAsB,KAAK1D,OAAL,CACnBwD,SADmB,CAElB,KAAKtC,OAFa,EAGlB,KAAKC,QAHa,EAIlBsC,eAJkB,EAKlB,KAAKrB,eAAL,EAAsBkB,UALJ,EAOnBtC,SAPmB,CAOR2C,QAAD,IAAa;MACtB,KAAKvB,eAAL,CAAqBwB,SAArB,GAAiCD,QAAjC;MACA,KAAKE,aAAL,CAAmB,KAAKzB,eAAL,CAAqBwB,SAArB,CAA+B,CAA/B,GAAmCE,IAAtD;IACD,CAVmB,CAAtB;EAWD;;EAEDpC,iBAAiB;IACf,MAAM+B,eAAe,GAAG,KAAKrC,WAA7B;IACA,KAAKpB,OAAL,CACG+D,WADH,CACe,KAAK7C,OADpB,EAC6B,KAAKC,QADlC,EAC4CsC,eAD5C,EAEGzC,SAFH,CAEc2C,QAAD,IAAa;MACtB,MAAMK,YAAY,GAAGL,QAAQ,CAACM,MAAT,CAClBC,CAAD,IAAOA,CAAC,CAACC,YAAF,IAAkB,KAAKhD,QADX,CAArB;;MAGA,IAAI6C,YAAY,CAACI,MAAb,GAAsB,CAA1B,EAA6B;QAC3B,MAAMjD,QAAQ,GAAG6C,YAAY,CAAC,CAAD,CAA7B;QACA,KAAKK,YAAL,GAAoBlD,QAAQ,CAACmD,WAA7B;QACA,MAAMC,YAAY,GAAGpD,QAAQ,CAACqD,QAAT,CAAkBP,MAAlB,CAClBC,CAAD,IAAOA,CAAC,CAACO,WAAF,IAAiBhB,eADL,CAArB;;QAIA,IAAIc,YAAY,CAACH,MAAb,GAAsB,CAA1B,EAA6B;UAC3B,MAAMlD,OAAO,GAAGqD,YAAY,CAAC,CAAD,CAA5B;UACA,KAAKG,SAAL,GAAiBxD,OAAO,CAACoD,WAAzB;UACA,KAAKA,WAAL,GAAmBpD,OAAO,CAACyD,eAAR,EAAyBC,IAAzB,MAAmC,EAAtD;UACA,KAAKC,SAAL,GAAiB3D,OAAO,CAAC4D,KAAzB;UACA,KAAKC,gBAAL;UACA,KAAK5E,gBAAL,CAAsB6E,YAAtB,CAAmC,KAAKN,SAAxC;QACD;MACF;IACF,CAtBH;EAuBD;;EACD/C,UAAU;IACR,MAAM8B,eAAe,GAAG,KAAKrC,WAA7B;;IACA,IAAIqC,eAAJ,EAAqB;MACnB,KAAKC,cAAL,GAAsB,KAAK1D,OAAL,CACnBiF,aADmB,CAElB,KAAK/D,OAFa,EAGlB,KAAKC,QAHa,EAIlBsC,eAJkB,EAMnBzC,SANmB,CAMR2C,QAAD,IAAa;QACtB,KAAKuB,WAAL,GAAmBvB,QAAnB;QACA,KAAKnC,cAAL,GAAsB,KAAKA,cAAL,EAAqByC,MAArB,CAA6B/C,OAAD,IAChD,KAAKgE,WAAL,CAAiBC,IAAjB,CAAsBC,OAAO,IAAIA,OAAO,CAAC3B,eAAR,KAA4B,KAAKrC,WAAjC,IAAgDgE,OAAO,CAAC9B,UAAR,KAAuBpC,OAAO,CAACoC,UAA/E,IAA6F8B,OAAO,CAACC,UAAR,KAAuBnE,OAAO,CAACmE,UAA7J,CADoB,CAAtB;;QAGA,IAAI,KAAK9D,SAAT,EAAoB;UAClB,KAAKE,eAAL,GAAuB,KAAKD,cAAL,CAAoB8D,MAApB,CAA2B,CAACC,GAAD,EAAMC,IAAN,KAAc;YAC9D,IAAI,CAACD,GAAG,CAACC,IAAI,CAAC,OAAD,CAAL,CAAR,EAAyB;cACvBD,GAAG,CAACC,IAAI,CAAC,OAAD,CAAL,CAAH,GAAqB,EAArB;YACD;;YAEDD,GAAG,CAACC,IAAI,CAAC,OAAD,CAAL,CAAH,CAAmBC,IAAnB,CAAwBD,IAAxB;YACA,OAAOD,GAAP;UACD,CAPsB,EAOpB,EAPoB,CAAvB;QAQD;;QAED,KAAKG,WAAL,GAAmB,CAAC,mBAAD,CAAnB;QACA,KAAKC,aAAL,GAAqB,KAAKD,WAAL,CAAiB,CAAjB,CAArB;;QAEA,IAAI,KAAKlE,cAAL,CAAoB4C,MAApB,IAA8B,CAAlC,EAAqC;UACnC,KAAKhC,eAAL,GAAuB,KAAKZ,cAAL,CAAoB,CAApB,CAAvB;UACA,KAAKkE,WAAL,GAAmB,KAAKtD,eAAL,EAAsBsD,WAAzC;UACA,KAAKpC,UAAL,GAAkB,KAAKlB,eAAL,CAAqBkB,UAAvC;UACA,KAAKqC,aAAL,GAAqB,KAAKvD,eAAL,EAAsBsD,WAAtB,CAAkC,CAAlC,CAArB;UACA,KAAKzD,QAAL,GAAgB,KAAKC,eAAL,KAAyB,CAAzB,GAA6B0D,IAAI,CAACC,GAAL,CAAS,KAAK3D,eAAL,EAAT,EAAiC,CAAjC,CAA7B,GAAmE0D,IAAI,CAACC,GAAL,CAAS,KAAK1D,gBAAL,EAAT,EAAkC,CAAlC,CAAnF;;UACA,IAAI,KAAKC,eAAL,CAAqBiB,YAAzB,EAAuC;YACrC,KAAKG,SAAL;UACD,CAFD,MAEO;YACL,KAAKpB,eAAL,CAAqBwB,SAArB,GAAiC,EAAjC;YACA,KAAKkC,WAAL,GAFK,CAGL;UACD;;UACD,KAAKC,kBAAL,CAAwB,KAAK3D,eAA7B;QACD;MACF,CAxCmB,CAAtB;IAyCD;EACF;;EAED4D,QAAQ,CAAC9E,OAAD,EAAQ;IACd,MAAM+E,aAAa,GAAG,KAAKf,WAAL,EAAkBgB,IAAlB,CAAuBV,IAAI,IAAIA,IAAI,CAACW,WAAL,KAAqB,KAAKjF,OAA1B,IAAqCsE,IAAI,CAACrB,YAAL,KAAsB,KAAKhD,QAAhE,IAA4EqE,IAAI,CAAC/B,eAAL,KAAyB,KAAKrC,WAA1G,IAAyHoE,IAAI,CAAClC,UAAL,KAAoBpC,OAAO,CAACoC,UAArJ,IAAmKkC,IAAI,CAACH,UAAL,KAAoBnE,OAAO,CAACmE,UAA9N,CAAtB;IACA,OAAOY,aAAa,EAAEtE,UAAf,IAA6B,EAApC;EACD;;EAEDyE,YAAY,CAACC,OAAD,EAAuB;IACjC,KAAK9C,aAAL,GAAqB,EAArB;IACA,KAAKnB,eAAL,GAAuBiE,OAAvB;IACA,KAAK/C,UAAL,GAAkB+C,OAAO,CAAC/C,UAA1B;;IACA,IAAI,KAAKI,cAAT,EAAyB;MACvB,KAAKA,cAAL,CAAoB4C,WAApB;IACD;;IACD,KAAKC,QAAL,GAAgB,KAAhB;;IACA,IAAI,KAAKnE,eAAL,CAAqBoE,cAArB,CAAoC,aAApC,CAAJ,EAAwD;MACtD,KAAKd,WAAL,GAAmB,KAAKtD,eAAL,CAAqBsD,WAAxC;MACA,KAAKC,aAAL,GAAqB,KAAKD,WAAL,CAAiB,CAAjB,CAArB;IACD;;IACD,KAAKzD,QAAL,GAAgB,KAAKC,eAAL,KAAyB,CAAzB,GAA6B0D,IAAI,CAACC,GAAL,CAAS,KAAK3D,eAAL,EAAT,EAAiC,CAAjC,CAA7B,GAAmE0D,IAAI,CAACC,GAAL,CAAS,KAAK1D,gBAAL,EAAT,EAAkC,CAAlC,CAAnF;;IACA,IAAI,KAAKC,eAAL,CAAqBiB,YAAzB,EAAuC;MACrC,KAAKG,SAAL;IACD,CAFD,MAEO;MACL,KAAKpB,eAAL,CAAqBwB,SAArB,GAAiC,EAAjC;MACA,KAAKkC,WAAL,GAFK,CAGL;IACD;;IACD,KAAKC,kBAAL,CAAwBM,OAAxB;EACD;;EAEDI,eAAe,CAACC,KAAD,EAAM;IACnB;IACA,KAAKC,gBAAL,CAAsB,KAAKvE,eAA3B,EAA4CsE,KAA5C;EACD;;EAED7C,aAAa,CAAC+C,KAAD,EAAqB;IAChC,KAAKrD,aAAL,GAAqBqD,KAArB;IACA,KAAKd,WAAL,GAFgC,CAGhC;EACD;;EACDA,WAAW;IACT,MAAMrC,eAAe,GAAG,KAAKrC,WAA7B;;IACA,IAAIqC,eAAe,IAAI,KAAKrB,eAA5B,EAA6C;MAC3C,MAAMwE,KAAK,GAAG,KAAKxE,eAAL,CAAqBiB,YAArB,GACV,KAAKE,aAAL,IAAsB,KAAKnB,eAAL,CAAqBwB,SAArB,CAA+B,CAA/B,GAAmCE,IAD/C,GAEV,EAFJ;MAGA,KAAKJ,cAAL,GAAsB,KAAK1D,OAAL,CACnB6G,eADmB,CAElB,KAAK3F,OAFa,EAGlB,KAAKC,QAHa,EAIlBsC,eAJkB,EAKlB,KAAKxB,QAAL,IAAiB,CALC,EAMlB,KAAKG,eAAL,CAAqBkB,UANH,EAOlB,KAAKlB,eAAL,CAAqBiD,UAPH,EAQlBuB,KARkB,EAUnB5F,SAVmB,CAUR2C,QAAD,IAAa;QACtB,KAAKkB,SAAL,GAAiBlB,QAAjB,CADsB,CACK;MAC5B,CAZmB,CAAtB;IAaD;EACF;;EACDmD,aAAa;IACX,MAAMrD,eAAe,GAAG,KAAKrC,WAA7B;IACA,MAAMwF,KAAK,GAAG,KAAKxE,eAAL,CAAqBiB,YAArB,GACV,KAAKE,aAAL,IAAsB,KAAKnB,eAAL,CAAqBwB,SAArB,CAA+B,CAA/B,GAAmCE,IAD/C,GAEV,EAFJ;IAGA,KAAK9D,OAAL,CACG+G,qBADH,CAEI,KAAK7F,OAFT,EAGI,KAAKC,QAHT,EAIIsC,eAJJ,EAKI,KAAKrB,eAAL,CAAqBkB,UALzB,EAMI,KAAKlB,eAAL,CAAqBiD,UANzB,EAOIuB,KAPJ,EASG5F,SATH,CASc2C,QAAD,IAAa;MACtB,KAAKqD,UAAL,GAAkBrD,QAAlB;IACD,CAXH;EAYD;;EACDZ,cAAc;IACZ,IAAI,KAAKd,QAAL,GAAgB,CAAhB,IAAqB,KAAKG,eAA9B,EAA+C;MAC7C,KAAK6E,SAAL,GAAiB,IAAjB;MAEA,KAAKjH,OAAL,CACGkH,SADH,CAEI,KAAKhG,OAFT,EAGI,KAAKC,QAHT,EAII,KAAKC,WAJT,EAKI,KAAKa,QALT,EAMI,KAAKsB,aANT,EAOI,KAAKnB,eAAL,CAAqBkB,UAPzB,EAQI,EARJ,EASI,KAAKlB,eAAL,CAAqBiD,UATzB,EAUI,EAVJ,EAWI,EAXJ,EAYI,KAAKxC,uBAAL,CAA6BC,YAZjC,EAaI,KAAKqE,wBAbT,EAeGnG,SAfH,CAec2C,QAAD,IAAa;QACtB,KAAKsD,SAAL,GAAiB,KAAjB;;QACA,IAAItD,QAAQ,CAACyD,YAAT,KAA0B,GAA9B,EAAmC;UACjC,KAAKC,cAAL,CAAoB,KAAKjF,eAAzB;UACA,KAAKkF,SAAL;QACD;MACF,CArBH;IAsBD;EACF;;EACDA,SAAS;IACP,KAAKrF,QAAL,GAAgB,IAAhB;IACA,KAAKG,eAAL,GAAuB,IAAvB;IACA,KAAKyC,SAAL,GAAiB,IAAjB;IACA,KAAKmC,UAAL,GAAkB,IAAlB;EACD;;EAEDO,SAAS;IACP,KAAK1G,yBAAL,EAAgC2G,WAAhC,EAA6CC,KAA7C;EACD;;EAEDC,YAAY;IACV,KAAKH,SAAL;IACA,KAAKvH,OAAL,CACG4B,wBADH,CAEI,KAAKV,OAFT,EAGI,KAAKC,QAHT,EAII,KAAKC,WAJT,EAKI,IALJ,EAMI,GANJ,EAOI,KAAKkC,UAPT,EASGtC,SATH,CAScD,IAAD,IAAS;MAClB,KAAKc,UAAL,GAAkB,KAAK5B,eAAL,CAAqB6B,kBAArB,CAChBf,IADgB,EAEhB,EAFgB,EAGhB,EAHgB,CAAlB;MAKA,KAAKwF,QAAL,GAAgB,IAAhB;IACD,CAhBH;EAiBD;;EAEDoB,UAAU,CAACC,KAAD,EAAc;IACtB,KAAKT,wBAAL,GAAgCS,KAAhC;EACD;;EACDC,eAAe;IACb,KAAKH,YAAL;IACA,KAAKI,eAAL;EACD;;EACDC,gBAAgB;IACd,KAAKjC,WAAL;IACA,KAAKkC,aAAL;EACD;;EAEOC,eAAe;IACrB,OAAO;MACLC,OAAO,EAAE,GAAG,KAAKhH,OAAO,IAAI,KAAKC,QAAQ,IAAI,KAAKC,WAAW,EADxD;MAEL+G,SAAS,EAAE,KAAKzD,SAFX;MAGLvD,QAAQ,EAAE,KAAKkD,YAHV;MAIL+D,IAAI,EAAE;IAJD,CAAP;EAMD;;EAEOC,kBAAkB,CAACnH,OAAD,EAAuB;IAC/C,OAAO;MACLoH,YAAY,EAAE,GAAGpH,OAAO,CAACqH,KAAR,GAAgBrH,OAAO,CAACqH,KAAR,GAAgB,GAAhC,GAAsC,EAAE,GAAGrH,OAAO,CAACsH,KAAK,EADpE;MAELC,WAAW,EAAE,KAAKzC,QAAL,CAAc9E,OAAd,CAFR,CAGL;MACA;MACA;;IALK,CAAP;EAOD;;EAEO6D,gBAAgB;IACtB,KAAK7E,sBAAL,CAA4BwI,SAA5B,CACE/I,aAAa,CAACgJ,SAAd,GAA0BjJ,QAAQ,CAACkJ,UADrC,EAEE,KAAKX,eAAL,EAFF;EAID;;EAEOlC,kBAAkB,CAAC7E,OAAD,EAAuB;IAC/C,KAAKhB,sBAAL,CAA4BwI,SAA5B,CACE/I,aAAa,CAACgJ,SAAd,GAA0BjJ,QAAQ,CAACmJ,eADrC,EAEE,EACE,GAAG,KAAKZ,eAAL,EADL;MAEE,GAAG,KAAKI,kBAAL,CAAwBnH,OAAxB;IAFL,CAFF;EAOD;;EAEOyF,gBAAgB,CAACzF,OAAD,EAAyB4H,SAAzB,EAA0C;IAChE,KAAK5I,sBAAL,CAA4BwI,SAA5B,CACE/I,aAAa,CAACgJ,SAAd,GAA0BjJ,QAAQ,CAACqJ,aADrC,EAEE,EACE,GAAG,KAAKd,eAAL,EADL;MAEE,GAAG,KAAKI,kBAAL,CAAwBnH,OAAxB,CAFL;MAGE8H,aAAa,EAAEF;IAHjB,CAFF;EAQD;;EAEOd,aAAa;IACnB,MAAM9G,OAAO,GAAG,KAAKkB,eAArB;IACA,KAAKlC,sBAAL,CAA4BwI,SAA5B,CACE/I,aAAa,CAACgJ,SAAd,GAA0BjJ,QAAQ,CAACuJ,QADrC,EAEE,EACE,GAAG,KAAKhB,eAAL,EADL;MAEE,GAAG,KAAKI,kBAAL,CAAwBnH,OAAxB,CAFL;MAGEgI,aAAa,EAAE,KAAK3F,aAHtB;MAIE4F,gBAAgB,EAAE,KAAKlH;IAJzB,CAFF;EASD;;EAEO6F,eAAe;IACrB,MAAM5G,OAAO,GAAG,KAAKkB,eAArB;IACA,KAAKlC,sBAAL,CAA4BwI,SAA5B,CACE/I,aAAa,CAACgJ,SAAd,GAA0BjJ,QAAQ,CAAC0J,WADrC,EAEE,EACE,GAAG,KAAKnB,eAAL,EADL;MAEE,GAAG,KAAKI,kBAAL,CAAwBnH,OAAxB;IAFL,CAFF;EAOD;;EACOmG,cAAc,CAACnG,OAAD,EAAuB;IAC3C,KAAKhB,sBAAL,CAA4BwI,SAA5B,CACE/I,aAAa,CAACgJ,SAAd,GAA0BjJ,QAAQ,CAAC2J,SADrC,EAEE,EACE,GAAG,KAAKpB,eAAL,EADL;MAEE,GAAG,KAAKI,kBAAL,CAAwBnH,OAAxB,CAFL;MAGEgI,aAAa,EAAE,KAAK3F,aAHtB;MAIE4F,gBAAgB,EAAE,KAAKlH,QAJzB;MAKEqH,kBAAkB,EAAE,KAAKzE,SAAL,GAAiB,CAAjB,GAAqB,KAAKA,SAAL,IAAkB,KAAK5C,QAAL,IAAiB,CAAnC,CAArB,GAA6D,KAAK4C,SALxF;MAME0E,mBAAmB,EAAE,KAAK1E;IAN5B,CAFF;EAWD;;AA5cmC;;;;;;;;;;;;;;;;UAmCnC5F;IAASuK,OAAC,wBAAD,EAA2B;MAAEC,MAAM,EAAE;IAAV,CAA3B;;;AAnCC5J,yBAAyB,eALrCb,SAAS,CAAC;EACT0K,QAAQ,EAAE,uBADD;EAETC,8BAFS;;AAAA,CAAD,CAK4B,GAAzB9J,yBAAyB,CAAzB;SAAAA", "names": ["Component", "ViewChild", "ActivatedRoute", "SimpleProductService", "IMPRINTED_PRODUCTS_DATA", "QuestionService", "FormControl", "FormGroup", "DBAFilling", "GoogleAnalyticsService", "GAEvents", "GAEventsTypes", "PageTitleService", "ImprintedProductComponent", "constructor", "route", "service", "questionService", "googleAnalyticsService", "pageTitleService", "FictitiousNameToFile", "Instructions", "SelectedFilingOptionType", "envConfig", "JSON", "parse", "localStorage", "getItem", "ImprintedFormComponent", "ImprintedFormComponentObj", "ngOnInit", "data", "subscribe", "v", "product", "category", "subCategory", "inMultiplesOf", "minQuantity", "isGrouped", "productDetails", "groupedProducts", "getProductDetails", "productSku", "GetDynamicFormMasterData", "questions$", "getMappedQuestions", "returnZero", "validQuantity", "quantity", "multipleOfValue", "minQuantityValue", "selectedTagType", "OnSave", "dynamicFormData", "enteredDynamicFormData", "getDynamicFormData", "Form", "valid", "console", "log", "dynamicFormControlsData", "keyValuePair", "addOrderToCart", "er", "error", "imageUrl", "url", "blobUrl", "colorOptions", "entityCode", "selectedColor", "getColors", "subCategoryCode", "_subscriptions", "response", "colorList", "onColorChange", "code", "getProducts", "categoryData", "filter", "t", "categoryCode", "length", "categoryName", "description", "productsData", "products", "productName", "pageTitle", "fullDescription", "trim", "unitPrice", "price", "trackProductPage", "setPageTitle", "getProductSku", "skuResponse", "some", "exclude", "optionCode", "reduce", "acc", "item", "push", "imagesArray", "selectedImage", "Math", "max", "updatePrice", "trackProductSelect", "getSKUID", "matchedObject", "find", "productCode", "onTypeSelect", "tagType", "unsubscribe", "formFlag", "hasOwnProperty", "setClickedImage", "image", "trackImageSelect", "color", "getProductPrice", "tieredPricing", "blumbergTieredPricing", "bulkPrices", "submitted", "addToCart", "dynamicFormUploadedFiles", "responseCode", "trackAddToCart", "resetData", "clearForm", "dynamicform", "reset", "showHideForm", "fileUplaod", "files", "showImprintForm", "trackAddImprint", "onQuantityChange", "trackQuantity", "getPageGAParams", "page_id", "page_name", "type", "getProductGAParams", "product_name", "group", "label", "product_sku", "sendEvent", "Imprinted", "<PERSON><PERSON><PERSON><PERSON>", "ProductSelected", "imageName", "ImageSelected", "product_image", "Quantity", "product_color", "product_quantity", "AddImprints", "AddToCart", "product_unit_price", "product_total_price", "args", "static", "selector", "template"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\viatech\\Ecom\\client\\acs\\src\\app\\Modules\\Estate&WillPlanning\\components\\imprinted-product\\imprinted-product.component.ts"], "sourcesContent": ["import { Component, OnInit, ViewChild } from \"@angular/core\";\r\nimport { ActivatedRoute } from \"@angular/router\";\r\nimport { SimpleProductService } from \"src/app/Modules/Core/Services/Common/simple-product.service\";\r\nimport {\r\n  PageDataModel,\r\n  PageDataObjectModel,\r\n} from \"src/app/Modules/Estate&WillPlanning/models/page-data.model\";\r\nimport { ColorsResponseModel } from \"src/app/Modules/Shared/Models/Common/colors-response.model\";\r\nimport { ProductEntity } from \"src/app/Modules/Shared/Models/Common/product-entity.model\";\r\nimport { EnvConfigModel } from \"src/app/Modules/Shared/Models/envConfig.model\";\r\nimport { BulkPricingModel } from \"src/app/Modules/Shared/Models/Pricings/bulk-pricing.model\";\r\nimport { IMPRINTED_PRODUCTS_DATA } from \"../../data/imprinted-products-data\";\r\nimport { QuestionBase } from \"src/app/Modules/Shared/Models/DynamicForm/question-base\";\r\nimport { Observable, Subscription } from \"rxjs\";\r\nimport { QuestionService } from \"src/app/Modules/Shared/Services/Common/question.service\";\r\nimport { FormControl, FormGroup } from \"@angular/forms\";\r\nimport { DBAFilling } from \"src/app/Modules/FilingService/Models/DBA\";\r\nimport { ImprintedFormComponent } from \"../imprinted-form/imprinted-form.component\";\r\nimport { BooleanInput } from \"@angular/cdk/coercion\";\r\nimport { GoogleAnalyticsService } from \"src/app/Modules/Shared/Services/Common/google-analytics.service\";\r\nimport { GAEvents, GAEventsTypes } from \"src/app/Modules/Shared/Enums/google-analytics.enum\";\r\nimport { PageTitleService } from \"src/app/Modules/Shared/Services/Common/page-title.service\";\r\n\r\n@Component({\r\n  selector: 'app-imprinted-product',\r\n  templateUrl: './imprinted-product.component.html',\r\n  styleUrls: ['./imprinted-product.component.css']\r\n})\r\nexport class ImprintedProductComponent implements OnInit {\r\n\r\n  envConfig: EnvConfigModel;\r\n\r\n  product: string;\r\n  category: string;\r\n  subCategory: string;\r\n  entityCode: string;\r\n\r\n  pageTitle: string;\r\n  categoryName: string;\r\n  description: string;\r\n\r\n  unitPrice: number;\r\n  quantity: number;\r\n  bulkPrices: BulkPricingModel[] = [];\r\n  colorList: ColorsResponseModel[] = [];\r\n  selectedColor: string | null = \"\";\r\n  imagesArray = [];\r\n  selectedImage = \"\";\r\n\r\n  submitted: boolean = false;\r\n  selectedTagType: ProductEntity | null = null;\r\n  productDetails: ProductEntity[] | null = null;\r\n\r\n  pageData: PageDataModel | null = null;\r\n  dynamicFormControlsData: any = {};\r\n  isGrouped: boolean = false;\r\n  groupedProducts: { [key: string]: ProductEntity[] } = {};\r\n  inMultiplesOf = 1;\r\n  minQuantity = 1;\r\n  dynamicFormUploadedFiles: File[] = [];\r\n\r\n  questions$: Observable<QuestionBase<any>[]>;\r\n  ImprintedFormComponentObj: ImprintedFormComponent;\r\n  @ViewChild(\"ImprintedFormComponent\", { static: false })\r\n  set ImprintedFormComponent(ImprintedFormComponent: ImprintedFormComponent) {\r\n    if (ImprintedFormComponent) {\r\n      this.ImprintedFormComponentObj = ImprintedFormComponent;\r\n    }\r\n  }\r\n\r\n  Form = new FormGroup({\r\n    FictitiousNameToFile: new FormControl<string | null>(null),\r\n    Instructions: new FormControl<string | null>(null),\r\n    SelectedFilingOptionType: new FormControl<number | null>(null)\r\n  })\r\n  formFlag: boolean = false;\r\n  skuResponse: any[] = [];\r\n  private _subscriptions: Subscription;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private service: SimpleProductService,\r\n    private questionService: QuestionService,\r\n    private googleAnalyticsService: GoogleAnalyticsService,\r\n    private pageTitleService: PageTitleService\r\n  ) {\r\n    this.envConfig = JSON.parse(localStorage.getItem(\"envConfig\"));\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.route.data.subscribe((v) => {\r\n      this.product = v.product;\r\n      this.category = v.category;\r\n      this.subCategory = v.subCategory;\r\n      this.inMultiplesOf = v.inMultiplesOf || 1;\r\n      this.minQuantity = v.minQuantity || 1;\r\n      this.isGrouped = v.isGrouped;\r\n\r\n      this.productDetails =\r\n        IMPRINTED_PRODUCTS_DATA[`${this.product}${this.category}${this.subCategory}`];\r\n\r\n      this.groupedProducts = {};\r\n\r\n      this.getProductDetails();\r\n      this.productSku();\r\n    });\r\n\r\n    this.service.GetDynamicFormMasterData(this.product, this.category, this.subCategory, \"--\", 'A').subscribe((data) => {\r\n      this.questions$ = this.questionService.getMappedQuestions(data, [], \"\");\r\n    })\r\n\r\n  }\r\n  returnZero() {\r\n    return 0;\r\n  }\r\n\r\n  validQuantity() {\r\n    return this.quantity && this.quantity % this.multipleOfValue() == 0 && this.quantity >= this.minQuantityValue();\r\n  }\r\n  minQuantityValue() {\r\n    return this.selectedTagType?.minQuantity ? this.selectedTagType?.minQuantity : this.minQuantity;\r\n  }\r\n  multipleOfValue() {\r\n    return this.selectedTagType?.inMultiplesOf ? this.selectedTagType?.inMultiplesOf : this.inMultiplesOf;\r\n  }\r\n\r\n  public dbaFilingModel: DBAFilling = new DBAFilling();\r\n\r\n  async OnSave(dynamicFormData?) {\r\n    if (!dynamicFormData && this.ImprintedFormComponentObj) {\r\n      var enteredDynamicFormData = await this.ImprintedFormComponentObj.getDynamicFormData()\r\n    }\r\n    try {\r\n      if (this.Form.valid) {\r\n        console.log(dynamicFormData, enteredDynamicFormData)\r\n        // this.Save(dynamicFormData || enteredDynamicFormData);\r\n        this.dynamicFormControlsData = dynamicFormData || enteredDynamicFormData;\r\n        console.log(this.dynamicFormControlsData.keyValuePair)\r\n        this.addOrderToCart();\r\n        return false;\r\n\r\n      }\r\n    }\r\n    catch (er) {\r\n      console.error(er)\r\n    }\r\n  }\r\n\r\n\r\n  imageUrl() {\r\n    const subCategory = this.subCategory;\r\n    let url = `${this.envConfig.blobUrl}${this.product}_${this.category}_${subCategory}`;\r\n    if (\r\n      this.selectedTagType?.colorOptions &&\r\n      this.selectedTagType?.entityCode &&\r\n      this.selectedColor\r\n    ) {\r\n      url += `_${this.selectedTagType?.entityCode}_${this.selectedColor}`;\r\n    }\r\n    if (\r\n      !this.selectedTagType?.colorOptions &&\r\n      this.selectedTagType?.entityCode\r\n    ) {\r\n      url += `_${this.selectedTagType?.entityCode}`;\r\n    }\r\n    return url + \".jpg\";\r\n  }\r\n\r\n  getColors() {\r\n    const subCategoryCode = this.subCategory;\r\n\r\n    this._subscriptions = this.service\r\n      .getColors(\r\n        this.product,\r\n        this.category,\r\n        subCategoryCode,\r\n        this.selectedTagType?.entityCode\r\n      )\r\n      .subscribe((response) => {\r\n        this.selectedTagType.colorList = response;\r\n        this.onColorChange(this.selectedTagType.colorList[0]?.code);\r\n      });\r\n  }\r\n\r\n  getProductDetails() {\r\n    const subCategoryCode = this.subCategory;\r\n    this.service\r\n      .getProducts(this.product, this.category, subCategoryCode)\r\n      .subscribe((response) => {\r\n        const categoryData = response.filter(\r\n          (t) => t.categoryCode == this.category\r\n        );\r\n        if (categoryData.length > 0) {\r\n          const category = categoryData[0];\r\n          this.categoryName = category.description;\r\n          const productsData = category.products.filter(\r\n            (t) => t.productName == subCategoryCode\r\n          );\r\n\r\n          if (productsData.length > 0) {\r\n            const product = productsData[0];\r\n            this.pageTitle = product.description;\r\n            this.description = product.fullDescription?.trim() || \"\";\r\n            this.unitPrice = product.price;\r\n            this.trackProductPage();\r\n            this.pageTitleService.setPageTitle(this.pageTitle);\r\n          }\r\n        }\r\n      });\r\n  }\r\n  productSku() {\r\n    const subCategoryCode = this.subCategory;\r\n    if (subCategoryCode) {\r\n      this._subscriptions = this.service\r\n        .getProductSku(\r\n          this.product,\r\n          this.category,\r\n          subCategoryCode\r\n        )\r\n        .subscribe((response) => {\r\n          this.skuResponse = response;\r\n          this.productDetails = this.productDetails?.filter((product: any) =>\r\n            this.skuResponse.some(exclude => exclude.subCategoryCode === this.subCategory && exclude.entityCode === product.entityCode && exclude.optionCode === product.optionCode)\r\n          );\r\n          if (this.isGrouped) {\r\n            this.groupedProducts = this.productDetails.reduce((acc, item) => {\r\n              if (!acc[item[\"group\"]]) {\r\n                acc[item[\"group\"]] = [];\r\n              }\r\n\r\n              acc[item[\"group\"]].push(item);\r\n              return acc;\r\n            }, {});\r\n          }\r\n\r\n          this.imagesArray = [\"DEFAULT_IMAGE.png\"];\r\n          this.selectedImage = this.imagesArray[0];\r\n\r\n          if (this.productDetails.length == 1) {\r\n            this.selectedTagType = this.productDetails[0];\r\n            this.imagesArray = this.selectedTagType?.imagesArray;\r\n            this.entityCode = this.selectedTagType.entityCode;\r\n            this.selectedImage = this.selectedTagType?.imagesArray[0];\r\n            this.quantity = this.multipleOfValue() > 1 ? Math.max(this.multipleOfValue(), 1) : Math.max(this.minQuantityValue(), 1);\r\n            if (this.selectedTagType.colorOptions) {\r\n              this.getColors();\r\n            } else {\r\n              this.selectedTagType.colorList = [];\r\n              this.updatePrice();\r\n              // this.tieredPricing();\r\n            }\r\n            this.trackProductSelect(this.selectedTagType);\r\n          }\r\n        });\r\n    }\r\n  }\r\n\r\n  getSKUID(product) {\r\n    const matchedObject = this.skuResponse?.find(item => item.productCode === this.product && item.categoryCode === this.category && item.subCategoryCode === this.subCategory && item.entityCode === product.entityCode && item.optionCode === product.optionCode);\r\n    return matchedObject?.productSku || \"\";\r\n  }\r\n\r\n  onTypeSelect(tagType: ProductEntity) {\r\n    this.selectedColor = \"\";\r\n    this.selectedTagType = tagType;\r\n    this.entityCode = tagType.entityCode;\r\n    if (this._subscriptions) {\r\n      this._subscriptions.unsubscribe();\r\n    }\r\n    this.formFlag = false;\r\n    if (this.selectedTagType.hasOwnProperty(\"imagesArray\")) {\r\n      this.imagesArray = this.selectedTagType.imagesArray;\r\n      this.selectedImage = this.imagesArray[0];\r\n    }\r\n    this.quantity = this.multipleOfValue() > 1 ? Math.max(this.multipleOfValue(), 1) : Math.max(this.minQuantityValue(), 1);\r\n    if (this.selectedTagType.colorOptions) {\r\n      this.getColors();\r\n    } else {\r\n      this.selectedTagType.colorList = [];\r\n      this.updatePrice();\r\n      // this.tieredPricing();\r\n    }\r\n    this.trackProductSelect(tagType);\r\n  }\r\n\r\n  setClickedImage(image) {\r\n    // this.selectedImage = image;\r\n    this.trackImageSelect(this.selectedTagType, image);\r\n  }\r\n\r\n  onColorChange(color: string | null) {\r\n    this.selectedColor = color;\r\n    this.updatePrice();\r\n    // this.tieredPricing();\r\n  }\r\n  updatePrice() {\r\n    const subCategoryCode = this.subCategory;\r\n    if (subCategoryCode && this.selectedTagType) {\r\n      const color = this.selectedTagType.colorOptions\r\n        ? this.selectedColor || this.selectedTagType.colorList[0]?.code\r\n        : \"\";\r\n      this._subscriptions = this.service\r\n        .getProductPrice(\r\n          this.product,\r\n          this.category,\r\n          subCategoryCode,\r\n          this.quantity || 1,\r\n          this.selectedTagType.entityCode,\r\n          this.selectedTagType.optionCode,\r\n          color\r\n        )\r\n        .subscribe((response) => {\r\n          this.unitPrice = response; //> 0 ? response / (this.quantity || 1) : response;\r\n        });\r\n    }\r\n  }\r\n  tieredPricing() {\r\n    const subCategoryCode = this.subCategory;\r\n    const color = this.selectedTagType.colorOptions\r\n      ? this.selectedColor || this.selectedTagType.colorList[0]?.code\r\n      : \"\";\r\n    this.service\r\n      .blumbergTieredPricing(\r\n        this.product,\r\n        this.category,\r\n        subCategoryCode,\r\n        this.selectedTagType.entityCode,\r\n        this.selectedTagType.optionCode,\r\n        color\r\n      )\r\n      .subscribe((response) => {\r\n        this.bulkPrices = response;\r\n      });\r\n  }\r\n  addOrderToCart() {\r\n    if (this.quantity > 0 && this.selectedTagType) {\r\n      this.submitted = true;\r\n\r\n      this.service\r\n        .addToCart(\r\n          this.product,\r\n          this.category,\r\n          this.subCategory,\r\n          this.quantity,\r\n          this.selectedColor,\r\n          this.selectedTagType.entityCode,\r\n          \"\",\r\n          this.selectedTagType.optionCode,\r\n          \"\",\r\n          \"\",\r\n          this.dynamicFormControlsData.keyValuePair,\r\n          this.dynamicFormUploadedFiles\r\n        )\r\n        .subscribe((response) => {\r\n          this.submitted = false;\r\n          if (response.responseCode === 200) {\r\n            this.trackAddToCart(this.selectedTagType);\r\n            this.resetData();\r\n          }\r\n        });\r\n    }\r\n  }\r\n  resetData() {\r\n    this.quantity = null;\r\n    this.selectedTagType = null;\r\n    this.unitPrice = null;\r\n    this.bulkPrices = null;\r\n  }\r\n\r\n  clearForm() {\r\n    this.ImprintedFormComponentObj?.dynamicform?.reset();\r\n  }\r\n\r\n  showHideForm() {\r\n    this.clearForm();\r\n    this.service\r\n      .GetDynamicFormMasterData(\r\n        this.product,\r\n        this.category,\r\n        this.subCategory,\r\n        \"--\",\r\n        \"A\",\r\n        this.entityCode\r\n      )\r\n      .subscribe((data) => {\r\n        this.questions$ = this.questionService.getMappedQuestions(\r\n          data,\r\n          [],\r\n          \"\"\r\n        );\r\n        this.formFlag = true;\r\n      });\r\n  }\r\n\r\n  fileUplaod(files: File[]) {\r\n    this.dynamicFormUploadedFiles = files;\r\n  }\r\n  showImprintForm() {\r\n    this.showHideForm();\r\n    this.trackAddImprint();\r\n  }\r\n  onQuantityChange() {\r\n    this.updatePrice();\r\n    this.trackQuantity();\r\n  }\r\n\r\n  private getPageGAParams() {\r\n    return {\r\n      page_id: `${this.product}_${this.category}_${this.subCategory}`,\r\n      page_name: this.pageTitle,\r\n      category: this.categoryName,\r\n      type: \"Estate & Will Supplies\",\r\n    };\r\n  }\r\n\r\n  private getProductGAParams(product: ProductEntity) {\r\n    return {\r\n      product_name: `${product.group ? product.group + \" \" : \"\"}${product.label}`,\r\n      product_sku: this.getSKUID(product),\r\n      // product_category: `${product.entityCode} - ${product.optionCode} - ${this.selectedColor}`,\r\n      // product_entity_code: product.entityCode,\r\n      // product_option_code: product.optionCode,\r\n    };\r\n  }\r\n\r\n  private trackProductPage() {\r\n    this.googleAnalyticsService.sendEvent(\r\n      GAEventsTypes.Imprinted + GAEvents.PageViewed,\r\n      this.getPageGAParams()\r\n    );\r\n  }\r\n\r\n  private trackProductSelect(product: ProductEntity) {\r\n    this.googleAnalyticsService.sendEvent(\r\n      GAEventsTypes.Imprinted + GAEvents.ProductSelected,\r\n      {\r\n        ...this.getPageGAParams(),\r\n        ...this.getProductGAParams(product),\r\n      }\r\n    );\r\n  }\r\n\r\n  private trackImageSelect(product: ProductEntity, imageName: string) {\r\n    this.googleAnalyticsService.sendEvent(\r\n      GAEventsTypes.Imprinted + GAEvents.ImageSelected,\r\n      {\r\n        ...this.getPageGAParams(),\r\n        ...this.getProductGAParams(product),\r\n        product_image: imageName,\r\n      }\r\n    );\r\n  }\r\n\r\n  private trackQuantity() {\r\n    const product = this.selectedTagType;\r\n    this.googleAnalyticsService.sendEvent(\r\n      GAEventsTypes.Imprinted + GAEvents.Quantity,\r\n      {\r\n        ...this.getPageGAParams(),\r\n        ...this.getProductGAParams(product),\r\n        product_color: this.selectedColor,\r\n        product_quantity: this.quantity,\r\n      }\r\n    );\r\n  }\r\n\r\n  private trackAddImprint() {\r\n    const product = this.selectedTagType;\r\n    this.googleAnalyticsService.sendEvent(\r\n      GAEventsTypes.Imprinted + GAEvents.AddImprints,\r\n      {\r\n        ...this.getPageGAParams(),\r\n        ...this.getProductGAParams(product),\r\n      }\r\n    );\r\n  }\r\n  private trackAddToCart(product: ProductEntity) {\r\n    this.googleAnalyticsService.sendEvent(\r\n      GAEventsTypes.Imprinted + GAEvents.AddToCart,\r\n      {\r\n        ...this.getPageGAParams(),\r\n        ...this.getProductGAParams(product),\r\n        product_color: this.selectedColor,\r\n        product_quantity: this.quantity,\r\n        product_unit_price: this.unitPrice > 0 ? this.unitPrice / (this.quantity || 1) : this.unitPrice,\r\n        product_total_price: this.unitPrice,\r\n      }\r\n    );\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}