{"ast": null, "code": "import { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function sample(notifier) {\n  return source => source.lift(new SampleOperator(notifier));\n}\n\nclass SampleOperator {\n  constructor(notifier) {\n    this.notifier = notifier;\n  }\n\n  call(subscriber, source) {\n    const sampleSubscriber = new SampleSubscriber(subscriber);\n    const subscription = source.subscribe(sampleSubscriber);\n    subscription.add(innerSubscribe(this.notifier, new SimpleInnerSubscriber(sampleSubscriber)));\n    return subscription;\n  }\n\n}\n\nclass SampleSubscriber extends SimpleOuterSubscriber {\n  constructor() {\n    super(...arguments);\n    this.hasValue = false;\n  }\n\n  _next(value) {\n    this.value = value;\n    this.hasValue = true;\n  }\n\n  notifyNext() {\n    this.emitValue();\n  }\n\n  notifyComplete() {\n    this.emitValue();\n  }\n\n  emitValue() {\n    if (this.hasValue) {\n      this.hasValue = false;\n      this.destination.next(this.value);\n    }\n  }\n\n}", "map": {"version": 3, "names": ["SimpleOuterSubscriber", "innerSubscribe", "SimpleInnerSubscriber", "sample", "notifier", "source", "lift", "SampleOperator", "constructor", "call", "subscriber", "sampleSubscriber", "SampleSubscriber", "subscription", "subscribe", "add", "arguments", "hasValue", "_next", "value", "notifyNext", "emitValue", "notifyComplete", "destination", "next"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/sample.js"], "sourcesContent": ["import { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function sample(notifier) {\n    return (source) => source.lift(new SampleOperator(notifier));\n}\nclass SampleOperator {\n    constructor(notifier) {\n        this.notifier = notifier;\n    }\n    call(subscriber, source) {\n        const sampleSubscriber = new SampleSubscriber(subscriber);\n        const subscription = source.subscribe(sampleSubscriber);\n        subscription.add(innerSubscribe(this.notifier, new SimpleInnerSubscriber(sampleSubscriber)));\n        return subscription;\n    }\n}\nclass SampleSubscriber extends SimpleOuterSubscriber {\n    constructor() {\n        super(...arguments);\n        this.hasValue = false;\n    }\n    _next(value) {\n        this.value = value;\n        this.hasValue = true;\n    }\n    notifyNext() {\n        this.emitValue();\n    }\n    notifyComplete() {\n        this.emitValue();\n    }\n    emitValue() {\n        if (this.hasValue) {\n            this.hasValue = false;\n            this.destination.next(this.value);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,qBAAT,EAAgCC,cAAhC,EAAgDC,qBAAhD,QAA6E,mBAA7E;AACA,OAAO,SAASC,MAAT,CAAgBC,QAAhB,EAA0B;EAC7B,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,cAAJ,CAAmBH,QAAnB,CAAZ,CAAnB;AACH;;AACD,MAAMG,cAAN,CAAqB;EACjBC,WAAW,CAACJ,QAAD,EAAW;IAClB,KAAKA,QAAL,GAAgBA,QAAhB;EACH;;EACDK,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,MAAMM,gBAAgB,GAAG,IAAIC,gBAAJ,CAAqBF,UAArB,CAAzB;IACA,MAAMG,YAAY,GAAGR,MAAM,CAACS,SAAP,CAAiBH,gBAAjB,CAArB;IACAE,YAAY,CAACE,GAAb,CAAiBd,cAAc,CAAC,KAAKG,QAAN,EAAgB,IAAIF,qBAAJ,CAA0BS,gBAA1B,CAAhB,CAA/B;IACA,OAAOE,YAAP;EACH;;AATgB;;AAWrB,MAAMD,gBAAN,SAA+BZ,qBAA/B,CAAqD;EACjDQ,WAAW,GAAG;IACV,MAAM,GAAGQ,SAAT;IACA,KAAKC,QAAL,GAAgB,KAAhB;EACH;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACT,KAAKA,KAAL,GAAaA,KAAb;IACA,KAAKF,QAAL,GAAgB,IAAhB;EACH;;EACDG,UAAU,GAAG;IACT,KAAKC,SAAL;EACH;;EACDC,cAAc,GAAG;IACb,KAAKD,SAAL;EACH;;EACDA,SAAS,GAAG;IACR,IAAI,KAAKJ,QAAT,EAAmB;MACf,KAAKA,QAAL,GAAgB,KAAhB;MACA,KAAKM,WAAL,CAAiBC,IAAjB,CAAsB,KAAKL,KAA3B;IACH;EACJ;;AApBgD"}, "metadata": {}, "sourceType": "module"}