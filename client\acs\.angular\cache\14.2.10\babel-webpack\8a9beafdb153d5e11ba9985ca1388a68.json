{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function skipWhile(predicate) {\n  return source => source.lift(new Skip<PERSON>hileOperator(predicate));\n}\n\nclass Skip<PERSON>hileOperator {\n  constructor(predicate) {\n    this.predicate = predicate;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new SkipWhileSubscriber(subscriber, this.predicate));\n  }\n\n}\n\nclass Skip<PERSON>hileSubscriber extends Subscriber {\n  constructor(destination, predicate) {\n    super(destination);\n    this.predicate = predicate;\n    this.skipping = true;\n    this.index = 0;\n  }\n\n  _next(value) {\n    const destination = this.destination;\n\n    if (this.skipping) {\n      this.tryCallPredicate(value);\n    }\n\n    if (!this.skipping) {\n      destination.next(value);\n    }\n  }\n\n  tryCallPredicate(value) {\n    try {\n      const result = this.predicate(value, this.index++);\n      this.skipping = Boolean(result);\n    } catch (err) {\n      this.destination.error(err);\n    }\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "<PERSON><PERSON><PERSON><PERSON>", "predicate", "source", "lift", "SkipWhileOperator", "constructor", "call", "subscriber", "subscribe", "SkipWhileSubscriber", "destination", "skipping", "index", "_next", "value", "tryCallPredicate", "next", "result", "Boolean", "err", "error"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/skipWhile.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function skipWhile(predicate) {\n    return (source) => source.lift(new Skip<PERSON>hileOperator(predicate));\n}\nclass SkipWhileOperator {\n    constructor(predicate) {\n        this.predicate = predicate;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new SkipWhileSubscriber(subscriber, this.predicate));\n    }\n}\nclass SkipWhileSubscriber extends Subscriber {\n    constructor(destination, predicate) {\n        super(destination);\n        this.predicate = predicate;\n        this.skipping = true;\n        this.index = 0;\n    }\n    _next(value) {\n        const destination = this.destination;\n        if (this.skipping) {\n            this.tryCallPredicate(value);\n        }\n        if (!this.skipping) {\n            destination.next(value);\n        }\n    }\n    tryCallPredicate(value) {\n        try {\n            const result = this.predicate(value, this.index++);\n            this.skipping = Boolean(result);\n        }\n        catch (err) {\n            this.destination.error(err);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,SAAT,CAAmBC,SAAnB,EAA8B;EACjC,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,iBAAJ,CAAsBH,SAAtB,CAAZ,CAAnB;AACH;;AACD,MAAMG,iBAAN,CAAwB;EACpBC,WAAW,CAACJ,SAAD,EAAY;IACnB,KAAKA,SAAL,GAAiBA,SAAjB;EACH;;EACDK,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,mBAAJ,CAAwBF,UAAxB,EAAoC,KAAKN,SAAzC,CAAjB,CAAP;EACH;;AANmB;;AAQxB,MAAMQ,mBAAN,SAAkCV,UAAlC,CAA6C;EACzCM,WAAW,CAACK,WAAD,EAAcT,SAAd,EAAyB;IAChC,MAAMS,WAAN;IACA,KAAKT,SAAL,GAAiBA,SAAjB;IACA,KAAKU,QAAL,GAAgB,IAAhB;IACA,KAAKC,KAAL,GAAa,CAAb;EACH;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACT,MAAMJ,WAAW,GAAG,KAAKA,WAAzB;;IACA,IAAI,KAAKC,QAAT,EAAmB;MACf,KAAKI,gBAAL,CAAsBD,KAAtB;IACH;;IACD,IAAI,CAAC,KAAKH,QAAV,EAAoB;MAChBD,WAAW,CAACM,IAAZ,CAAiBF,KAAjB;IACH;EACJ;;EACDC,gBAAgB,CAACD,KAAD,EAAQ;IACpB,IAAI;MACA,MAAMG,MAAM,GAAG,KAAKhB,SAAL,CAAea,KAAf,EAAsB,KAAKF,KAAL,EAAtB,CAAf;MACA,KAAKD,QAAL,GAAgBO,OAAO,CAACD,MAAD,CAAvB;IACH,CAHD,CAIA,OAAOE,GAAP,EAAY;MACR,KAAKT,WAAL,CAAiBU,KAAjB,CAAuBD,GAAvB;IACH;EACJ;;AAxBwC"}, "metadata": {}, "sourceType": "module"}