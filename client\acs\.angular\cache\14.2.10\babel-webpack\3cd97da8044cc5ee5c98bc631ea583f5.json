{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function refCount() {\n  return function refCountOperatorFunction(source) {\n    return source.lift(new RefCountOperator(source));\n  };\n}\n\nclass RefCountOperator {\n  constructor(connectable) {\n    this.connectable = connectable;\n  }\n\n  call(subscriber, source) {\n    const {\n      connectable\n    } = this;\n    connectable._refCount++;\n    const refCounter = new RefCountSubscriber(subscriber, connectable);\n    const subscription = source.subscribe(refCounter);\n\n    if (!refCounter.closed) {\n      refCounter.connection = connectable.connect();\n    }\n\n    return subscription;\n  }\n\n}\n\nclass RefCountSubscriber extends Subscriber {\n  constructor(destination, connectable) {\n    super(destination);\n    this.connectable = connectable;\n  }\n\n  _unsubscribe() {\n    const {\n      connectable\n    } = this;\n\n    if (!connectable) {\n      this.connection = null;\n      return;\n    }\n\n    this.connectable = null;\n    const refCount = connectable._refCount;\n\n    if (refCount <= 0) {\n      this.connection = null;\n      return;\n    }\n\n    connectable._refCount = refCount - 1;\n\n    if (refCount > 1) {\n      this.connection = null;\n      return;\n    }\n\n    const {\n      connection\n    } = this;\n    const sharedConnection = connectable._connection;\n    this.connection = null;\n\n    if (sharedConnection && (!connection || sharedConnection === connection)) {\n      sharedConnection.unsubscribe();\n    }\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "refCount", "refCountOperatorFunction", "source", "lift", "RefCountOperator", "constructor", "connectable", "call", "subscriber", "_refCount", "refCounter", "RefCountSubscriber", "subscription", "subscribe", "closed", "connection", "connect", "destination", "_unsubscribe", "sharedConnection", "_connection", "unsubscribe"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/refCount.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function refCount() {\n    return function refCountOperatorFunction(source) {\n        return source.lift(new RefCountOperator(source));\n    };\n}\nclass RefCountOperator {\n    constructor(connectable) {\n        this.connectable = connectable;\n    }\n    call(subscriber, source) {\n        const { connectable } = this;\n        connectable._refCount++;\n        const refCounter = new RefCountSubscriber(subscriber, connectable);\n        const subscription = source.subscribe(refCounter);\n        if (!refCounter.closed) {\n            refCounter.connection = connectable.connect();\n        }\n        return subscription;\n    }\n}\nclass RefCountSubscriber extends Subscriber {\n    constructor(destination, connectable) {\n        super(destination);\n        this.connectable = connectable;\n    }\n    _unsubscribe() {\n        const { connectable } = this;\n        if (!connectable) {\n            this.connection = null;\n            return;\n        }\n        this.connectable = null;\n        const refCount = connectable._refCount;\n        if (refCount <= 0) {\n            this.connection = null;\n            return;\n        }\n        connectable._refCount = refCount - 1;\n        if (refCount > 1) {\n            this.connection = null;\n            return;\n        }\n        const { connection } = this;\n        const sharedConnection = connectable._connection;\n        this.connection = null;\n        if (sharedConnection && (!connection || sharedConnection === connection)) {\n            sharedConnection.unsubscribe();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,QAAT,GAAoB;EACvB,OAAO,SAASC,wBAAT,CAAkCC,MAAlC,EAA0C;IAC7C,OAAOA,MAAM,CAACC,IAAP,CAAY,IAAIC,gBAAJ,CAAqBF,MAArB,CAAZ,CAAP;EACH,CAFD;AAGH;;AACD,MAAME,gBAAN,CAAuB;EACnBC,WAAW,CAACC,WAAD,EAAc;IACrB,KAAKA,WAAL,GAAmBA,WAAnB;EACH;;EACDC,IAAI,CAACC,UAAD,EAAaN,MAAb,EAAqB;IACrB,MAAM;MAAEI;IAAF,IAAkB,IAAxB;IACAA,WAAW,CAACG,SAAZ;IACA,MAAMC,UAAU,GAAG,IAAIC,kBAAJ,CAAuBH,UAAvB,EAAmCF,WAAnC,CAAnB;IACA,MAAMM,YAAY,GAAGV,MAAM,CAACW,SAAP,CAAiBH,UAAjB,CAArB;;IACA,IAAI,CAACA,UAAU,CAACI,MAAhB,EAAwB;MACpBJ,UAAU,CAACK,UAAX,GAAwBT,WAAW,CAACU,OAAZ,EAAxB;IACH;;IACD,OAAOJ,YAAP;EACH;;AAbkB;;AAevB,MAAMD,kBAAN,SAAiCZ,UAAjC,CAA4C;EACxCM,WAAW,CAACY,WAAD,EAAcX,WAAd,EAA2B;IAClC,MAAMW,WAAN;IACA,KAAKX,WAAL,GAAmBA,WAAnB;EACH;;EACDY,YAAY,GAAG;IACX,MAAM;MAAEZ;IAAF,IAAkB,IAAxB;;IACA,IAAI,CAACA,WAAL,EAAkB;MACd,KAAKS,UAAL,GAAkB,IAAlB;MACA;IACH;;IACD,KAAKT,WAAL,GAAmB,IAAnB;IACA,MAAMN,QAAQ,GAAGM,WAAW,CAACG,SAA7B;;IACA,IAAIT,QAAQ,IAAI,CAAhB,EAAmB;MACf,KAAKe,UAAL,GAAkB,IAAlB;MACA;IACH;;IACDT,WAAW,CAACG,SAAZ,GAAwBT,QAAQ,GAAG,CAAnC;;IACA,IAAIA,QAAQ,GAAG,CAAf,EAAkB;MACd,KAAKe,UAAL,GAAkB,IAAlB;MACA;IACH;;IACD,MAAM;MAAEA;IAAF,IAAiB,IAAvB;IACA,MAAMI,gBAAgB,GAAGb,WAAW,CAACc,WAArC;IACA,KAAKL,UAAL,GAAkB,IAAlB;;IACA,IAAII,gBAAgB,KAAK,CAACJ,UAAD,IAAeI,gBAAgB,KAAKJ,UAAzC,CAApB,EAA0E;MACtEI,gBAAgB,CAACE,WAAjB;IACH;EACJ;;AA5BuC"}, "metadata": {}, "sourceType": "module"}