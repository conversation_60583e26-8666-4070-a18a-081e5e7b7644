{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { SecurityContext, Injectable, Optional, Inject, Skip<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, InjectionToken, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, Input, NgModule } from '@angular/core';\nimport { mixinColor, MatCommonModule } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { DOCUMENT } from '@angular/common';\nimport { of, throwError, forkJoin, Subscription } from 'rxjs';\nimport { tap, map, catchError, finalize, share, take } from 'rxjs/operators';\nimport * as i1 from '@angular/common/http';\nimport { HttpClient } from '@angular/common/http';\nimport * as i2 from '@angular/platform-browser';\nimport { DomSanitizer } from '@angular/platform-browser';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * The Trusted Types policy, or null if Trusted Types are not\n * enabled/supported, or undefined if the policy has not been created yet.\n */\n\nconst _c0 = [\"*\"];\nlet policy;\n/**\n * Returns the Trusted Types policy, or null if Trusted Types are not\n * enabled/supported. The first call to this function will create the policy.\n */\n\nfunction getPolicy() {\n  if (policy === undefined) {\n    policy = null;\n\n    if (typeof window !== 'undefined') {\n      const ttWindow = window;\n\n      if (ttWindow.trustedTypes !== undefined) {\n        policy = ttWindow.trustedTypes.createPolicy('angular#components', {\n          createHTML: s => s\n        });\n      }\n    }\n  }\n\n  return policy;\n}\n/**\n * Unsafely promote a string to a TrustedHTML, falling back to strings when\n * Trusted Types are not available.\n * @security This is a security-sensitive function; any use of this function\n * must go through security review. In particular, it must be assured that the\n * provided string will never cause an XSS vulnerability if used in a context\n * that will be interpreted as HTML by a browser, e.g. when assigning to\n * element.innerHTML.\n */\n\n\nfunction trustedHTMLFromString(html) {\n  return getPolicy()?.createHTML(html) || html;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Returns an exception to be thrown in the case when attempting to\n * load an icon with a name that cannot be found.\n * @docs-private\n */\n\n\nfunction getMatIconNameNotFoundError(iconName) {\n  return Error(`Unable to find icon with the name \"${iconName}\"`);\n}\n/**\n * Returns an exception to be thrown when the consumer attempts to use\n * `<mat-icon>` without including @angular/common/http.\n * @docs-private\n */\n\n\nfunction getMatIconNoHttpProviderError() {\n  return Error('Could not find HttpClient provider for use with Angular Material icons. ' + 'Please include the HttpClientModule from @angular/common/http in your ' + 'app imports.');\n}\n/**\n * Returns an exception to be thrown when a URL couldn't be sanitized.\n * @param url URL that was attempted to be sanitized.\n * @docs-private\n */\n\n\nfunction getMatIconFailedToSanitizeUrlError(url) {\n  return Error(`The URL provided to MatIconRegistry was not trusted as a resource URL ` + `via Angular's DomSanitizer. Attempted URL was \"${url}\".`);\n}\n/**\n * Returns an exception to be thrown when a HTML string couldn't be sanitized.\n * @param literal HTML that was attempted to be sanitized.\n * @docs-private\n */\n\n\nfunction getMatIconFailedToSanitizeLiteralError(literal) {\n  return Error(`The literal provided to MatIconRegistry was not trusted as safe HTML by ` + `Angular's DomSanitizer. Attempted literal was \"${literal}\".`);\n}\n/**\n * Configuration for an icon, including the URL and possibly the cached SVG element.\n * @docs-private\n */\n\n\nclass SvgIconConfig {\n  constructor(url, svgText, options) {\n    this.url = url;\n    this.svgText = svgText;\n    this.options = options;\n  }\n\n}\n/**\n * Service to register and display icons used by the `<mat-icon>` component.\n * - Registers icon URLs by namespace and name.\n * - Registers icon set URLs by namespace.\n * - Registers aliases for CSS classes, for use with icon fonts.\n * - Loads icons from URLs and extracts individual icons from icon sets.\n */\n\n\nclass MatIconRegistry {\n  constructor(_httpClient, _sanitizer, document, _errorHandler) {\n    this._httpClient = _httpClient;\n    this._sanitizer = _sanitizer;\n    this._errorHandler = _errorHandler;\n    /**\n     * URLs and cached SVG elements for individual icons. Keys are of the format \"[namespace]:[icon]\".\n     */\n\n    this._svgIconConfigs = new Map();\n    /**\n     * SvgIconConfig objects and cached SVG elements for icon sets, keyed by namespace.\n     * Multiple icon sets can be registered under the same namespace.\n     */\n\n    this._iconSetConfigs = new Map();\n    /** Cache for icons loaded by direct URLs. */\n\n    this._cachedIconsByUrl = new Map();\n    /** In-progress icon fetches. Used to coalesce multiple requests to the same URL. */\n\n    this._inProgressUrlFetches = new Map();\n    /** Map from font identifiers to their CSS class names. Used for icon fonts. */\n\n    this._fontCssClassesByAlias = new Map();\n    /** Registered icon resolver functions. */\n\n    this._resolvers = [];\n    /**\n     * The CSS classes to apply when an `<mat-icon>` component has no icon name, url, or font\n     * specified. The default 'material-icons' value assumes that the material icon font has been\n     * loaded as described at http://google.github.io/material-design-icons/#icon-font-for-the-web\n     */\n\n    this._defaultFontSetClass = ['material-icons', 'mat-ligature-font'];\n    this._document = document;\n  }\n  /**\n   * Registers an icon by URL in the default namespace.\n   * @param iconName Name under which the icon should be registered.\n   * @param url\n   */\n\n\n  addSvgIcon(iconName, url, options) {\n    return this.addSvgIconInNamespace('', iconName, url, options);\n  }\n  /**\n   * Registers an icon using an HTML string in the default namespace.\n   * @param iconName Name under which the icon should be registered.\n   * @param literal SVG source of the icon.\n   */\n\n\n  addSvgIconLiteral(iconName, literal, options) {\n    return this.addSvgIconLiteralInNamespace('', iconName, literal, options);\n  }\n  /**\n   * Registers an icon by URL in the specified namespace.\n   * @param namespace Namespace in which the icon should be registered.\n   * @param iconName Name under which the icon should be registered.\n   * @param url\n   */\n\n\n  addSvgIconInNamespace(namespace, iconName, url, options) {\n    return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig(url, null, options));\n  }\n  /**\n   * Registers an icon resolver function with the registry. The function will be invoked with the\n   * name and namespace of an icon when the registry tries to resolve the URL from which to fetch\n   * the icon. The resolver is expected to return a `SafeResourceUrl` that points to the icon,\n   * an object with the icon URL and icon options, or `null` if the icon is not supported. Resolvers\n   * will be invoked in the order in which they have been registered.\n   * @param resolver Resolver function to be registered.\n   */\n\n\n  addSvgIconResolver(resolver) {\n    this._resolvers.push(resolver);\n\n    return this;\n  }\n  /**\n   * Registers an icon using an HTML string in the specified namespace.\n   * @param namespace Namespace in which the icon should be registered.\n   * @param iconName Name under which the icon should be registered.\n   * @param literal SVG source of the icon.\n   */\n\n\n  addSvgIconLiteralInNamespace(namespace, iconName, literal, options) {\n    const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal); // TODO: add an ngDevMode check\n\n\n    if (!cleanLiteral) {\n      throw getMatIconFailedToSanitizeLiteralError(literal);\n    } // Security: The literal is passed in as SafeHtml, and is thus trusted.\n\n\n    const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n    return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig('', trustedLiteral, options));\n  }\n  /**\n   * Registers an icon set by URL in the default namespace.\n   * @param url\n   */\n\n\n  addSvgIconSet(url, options) {\n    return this.addSvgIconSetInNamespace('', url, options);\n  }\n  /**\n   * Registers an icon set using an HTML string in the default namespace.\n   * @param literal SVG source of the icon set.\n   */\n\n\n  addSvgIconSetLiteral(literal, options) {\n    return this.addSvgIconSetLiteralInNamespace('', literal, options);\n  }\n  /**\n   * Registers an icon set by URL in the specified namespace.\n   * @param namespace Namespace in which to register the icon set.\n   * @param url\n   */\n\n\n  addSvgIconSetInNamespace(namespace, url, options) {\n    return this._addSvgIconSetConfig(namespace, new SvgIconConfig(url, null, options));\n  }\n  /**\n   * Registers an icon set using an HTML string in the specified namespace.\n   * @param namespace Namespace in which to register the icon set.\n   * @param literal SVG source of the icon set.\n   */\n\n\n  addSvgIconSetLiteralInNamespace(namespace, literal, options) {\n    const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n\n    if (!cleanLiteral) {\n      throw getMatIconFailedToSanitizeLiteralError(literal);\n    } // Security: The literal is passed in as SafeHtml, and is thus trusted.\n\n\n    const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n    return this._addSvgIconSetConfig(namespace, new SvgIconConfig('', trustedLiteral, options));\n  }\n  /**\n   * Defines an alias for CSS class names to be used for icon fonts. Creating an matIcon\n   * component with the alias as the fontSet input will cause the class name to be applied\n   * to the `<mat-icon>` element.\n   *\n   * If the registered font is a ligature font, then don't forget to also include the special\n   * class `mat-ligature-font` to allow the usage via attribute. So register like this:\n   *\n   * ```ts\n   * iconRegistry.registerFontClassAlias('f1', 'font1 mat-ligature-font');\n   * ```\n   *\n   * And use like this:\n   *\n   * ```html\n   * <mat-icon fontSet=\"f1\" fontIcon=\"home\"></mat-icon>\n   * ```\n   *\n   * @param alias Alias for the font.\n   * @param classNames Class names override to be used instead of the alias.\n   */\n\n\n  registerFontClassAlias(alias, classNames = alias) {\n    this._fontCssClassesByAlias.set(alias, classNames);\n\n    return this;\n  }\n  /**\n   * Returns the CSS class name associated with the alias by a previous call to\n   * registerFontClassAlias. If no CSS class has been associated, returns the alias unmodified.\n   */\n\n\n  classNameForFontAlias(alias) {\n    return this._fontCssClassesByAlias.get(alias) || alias;\n  }\n  /**\n   * Sets the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n   * have a fontSet input value, and is not loading an icon by name or URL.\n   */\n\n\n  setDefaultFontSetClass(...classNames) {\n    this._defaultFontSetClass = classNames;\n    return this;\n  }\n  /**\n   * Returns the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n   * have a fontSet input value, and is not loading an icon by name or URL.\n   */\n\n\n  getDefaultFontSetClass() {\n    return this._defaultFontSetClass;\n  }\n  /**\n   * Returns an Observable that produces the icon (as an `<svg>` DOM element) from the given URL.\n   * The response from the URL may be cached so this will not always cause an HTTP request, but\n   * the produced element will always be a new copy of the originally fetched icon. (That is,\n   * it will not contain any modifications made to elements previously returned).\n   *\n   * @param safeUrl URL from which to fetch the SVG icon.\n   */\n\n\n  getSvgIconFromUrl(safeUrl) {\n    const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n\n    if (!url) {\n      throw getMatIconFailedToSanitizeUrlError(safeUrl);\n    }\n\n    const cachedIcon = this._cachedIconsByUrl.get(url);\n\n    if (cachedIcon) {\n      return of(cloneSvg(cachedIcon));\n    }\n\n    return this._loadSvgIconFromConfig(new SvgIconConfig(safeUrl, null)).pipe(tap(svg => this._cachedIconsByUrl.set(url, svg)), map(svg => cloneSvg(svg)));\n  }\n  /**\n   * Returns an Observable that produces the icon (as an `<svg>` DOM element) with the given name\n   * and namespace. The icon must have been previously registered with addIcon or addIconSet;\n   * if not, the Observable will throw an error.\n   *\n   * @param name Name of the icon to be retrieved.\n   * @param namespace Namespace in which to look for the icon.\n   */\n\n\n  getNamedSvgIcon(name, namespace = '') {\n    const key = iconKey(namespace, name);\n\n    let config = this._svgIconConfigs.get(key); // Return (copy of) cached icon if possible.\n\n\n    if (config) {\n      return this._getSvgFromConfig(config);\n    } // Otherwise try to resolve the config from one of the resolver functions.\n\n\n    config = this._getIconConfigFromResolvers(namespace, name);\n\n    if (config) {\n      this._svgIconConfigs.set(key, config);\n\n      return this._getSvgFromConfig(config);\n    } // See if we have any icon sets registered for the namespace.\n\n\n    const iconSetConfigs = this._iconSetConfigs.get(namespace);\n\n    if (iconSetConfigs) {\n      return this._getSvgFromIconSetConfigs(name, iconSetConfigs);\n    }\n\n    return throwError(getMatIconNameNotFoundError(key));\n  }\n\n  ngOnDestroy() {\n    this._resolvers = [];\n\n    this._svgIconConfigs.clear();\n\n    this._iconSetConfigs.clear();\n\n    this._cachedIconsByUrl.clear();\n  }\n  /**\n   * Returns the cached icon for a SvgIconConfig if available, or fetches it from its URL if not.\n   */\n\n\n  _getSvgFromConfig(config) {\n    if (config.svgText) {\n      // We already have the SVG element for this icon, return a copy.\n      return of(cloneSvg(this._svgElementFromConfig(config)));\n    } else {\n      // Fetch the icon from the config's URL, cache it, and return a copy.\n      return this._loadSvgIconFromConfig(config).pipe(map(svg => cloneSvg(svg)));\n    }\n  }\n  /**\n   * Attempts to find an icon with the specified name in any of the SVG icon sets.\n   * First searches the available cached icons for a nested element with a matching name, and\n   * if found copies the element to a new `<svg>` element. If not found, fetches all icon sets\n   * that have not been cached, and searches again after all fetches are completed.\n   * The returned Observable produces the SVG element if possible, and throws\n   * an error if no icon with the specified name can be found.\n   */\n\n\n  _getSvgFromIconSetConfigs(name, iconSetConfigs) {\n    // For all the icon set SVG elements we've fetched, see if any contain an icon with the\n    // requested name.\n    const namedIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n\n    if (namedIcon) {\n      // We could cache namedIcon in _svgIconConfigs, but since we have to make a copy every\n      // time anyway, there's probably not much advantage compared to just always extracting\n      // it from the icon set.\n      return of(namedIcon);\n    } // Not found in any cached icon sets. If there are icon sets with URLs that we haven't\n    // fetched, fetch them now and look for iconName in the results.\n\n\n    const iconSetFetchRequests = iconSetConfigs.filter(iconSetConfig => !iconSetConfig.svgText).map(iconSetConfig => {\n      return this._loadSvgIconSetFromConfig(iconSetConfig).pipe(catchError(err => {\n        const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, iconSetConfig.url); // Swallow errors fetching individual URLs so the\n        // combined Observable won't necessarily fail.\n\n\n        const errorMessage = `Loading icon set URL: ${url} failed: ${err.message}`;\n\n        this._errorHandler.handleError(new Error(errorMessage));\n\n        return of(null);\n      }));\n    }); // Fetch all the icon set URLs. When the requests complete, every IconSet should have a\n    // cached SVG element (unless the request failed), and we can check again for the icon.\n\n    return forkJoin(iconSetFetchRequests).pipe(map(() => {\n      const foundIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs); // TODO: add an ngDevMode check\n\n\n      if (!foundIcon) {\n        throw getMatIconNameNotFoundError(name);\n      }\n\n      return foundIcon;\n    }));\n  }\n  /**\n   * Searches the cached SVG elements for the given icon sets for a nested icon element whose \"id\"\n   * tag matches the specified name. If found, copies the nested element to a new SVG element and\n   * returns it. Returns null if no matching element is found.\n   */\n\n\n  _extractIconWithNameFromAnySet(iconName, iconSetConfigs) {\n    // Iterate backwards, so icon sets added later have precedence.\n    for (let i = iconSetConfigs.length - 1; i >= 0; i--) {\n      const config = iconSetConfigs[i]; // Parsing the icon set's text into an SVG element can be expensive. We can avoid some of\n      // the parsing by doing a quick check using `indexOf` to see if there's any chance for the\n      // icon to be in the set. This won't be 100% accurate, but it should help us avoid at least\n      // some of the parsing.\n\n      if (config.svgText && config.svgText.toString().indexOf(iconName) > -1) {\n        const svg = this._svgElementFromConfig(config);\n\n        const foundIcon = this._extractSvgIconFromSet(svg, iconName, config.options);\n\n        if (foundIcon) {\n          return foundIcon;\n        }\n      }\n    }\n\n    return null;\n  }\n  /**\n   * Loads the content of the icon URL specified in the SvgIconConfig and creates an SVG element\n   * from it.\n   */\n\n\n  _loadSvgIconFromConfig(config) {\n    return this._fetchIcon(config).pipe(tap(svgText => config.svgText = svgText), map(() => this._svgElementFromConfig(config)));\n  }\n  /**\n   * Loads the content of the icon set URL specified in the\n   * SvgIconConfig and attaches it to the config.\n   */\n\n\n  _loadSvgIconSetFromConfig(config) {\n    if (config.svgText) {\n      return of(null);\n    }\n\n    return this._fetchIcon(config).pipe(tap(svgText => config.svgText = svgText));\n  }\n  /**\n   * Searches the cached element of the given SvgIconConfig for a nested icon element whose \"id\"\n   * tag matches the specified name. If found, copies the nested element to a new SVG element and\n   * returns it. Returns null if no matching element is found.\n   */\n\n\n  _extractSvgIconFromSet(iconSet, iconName, options) {\n    // Use the `id=\"iconName\"` syntax in order to escape special\n    // characters in the ID (versus using the #iconName syntax).\n    const iconSource = iconSet.querySelector(`[id=\"${iconName}\"]`);\n\n    if (!iconSource) {\n      return null;\n    } // Clone the element and remove the ID to prevent multiple elements from being added\n    // to the page with the same ID.\n\n\n    const iconElement = iconSource.cloneNode(true);\n    iconElement.removeAttribute('id'); // If the icon node is itself an <svg> node, clone and return it directly. If not, set it as\n    // the content of a new <svg> node.\n\n    if (iconElement.nodeName.toLowerCase() === 'svg') {\n      return this._setSvgAttributes(iconElement, options);\n    } // If the node is a <symbol>, it won't be rendered so we have to convert it into <svg>. Note\n    // that the same could be achieved by referring to it via <use href=\"#id\">, however the <use>\n    // tag is problematic on Firefox, because it needs to include the current page path.\n\n\n    if (iconElement.nodeName.toLowerCase() === 'symbol') {\n      return this._setSvgAttributes(this._toSvgElement(iconElement), options);\n    } // createElement('SVG') doesn't work as expected; the DOM ends up with\n    // the correct nodes, but the SVG content doesn't render. Instead we\n    // have to create an empty SVG node using innerHTML and append its content.\n    // Elements created using DOMParser.parseFromString have the same problem.\n    // http://stackoverflow.com/questions/23003278/svg-innerhtml-in-firefox-can-not-display\n\n\n    const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>')); // Clone the node so we don't remove it from the parent icon set element.\n\n\n    svg.appendChild(iconElement);\n    return this._setSvgAttributes(svg, options);\n  }\n  /**\n   * Creates a DOM element from the given SVG string.\n   */\n\n\n  _svgElementFromString(str) {\n    const div = this._document.createElement('DIV');\n\n    div.innerHTML = str;\n    const svg = div.querySelector('svg'); // TODO: add an ngDevMode check\n\n    if (!svg) {\n      throw Error('<svg> tag not found');\n    }\n\n    return svg;\n  }\n  /**\n   * Converts an element into an SVG node by cloning all of its children.\n   */\n\n\n  _toSvgElement(element) {\n    const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n\n    const attributes = element.attributes; // Copy over all the attributes from the `symbol` to the new SVG, except the id.\n\n    for (let i = 0; i < attributes.length; i++) {\n      const {\n        name,\n        value\n      } = attributes[i];\n\n      if (name !== 'id') {\n        svg.setAttribute(name, value);\n      }\n    }\n\n    for (let i = 0; i < element.childNodes.length; i++) {\n      if (element.childNodes[i].nodeType === this._document.ELEMENT_NODE) {\n        svg.appendChild(element.childNodes[i].cloneNode(true));\n      }\n    }\n\n    return svg;\n  }\n  /**\n   * Sets the default attributes for an SVG element to be used as an icon.\n   */\n\n\n  _setSvgAttributes(svg, options) {\n    svg.setAttribute('fit', '');\n    svg.setAttribute('height', '100%');\n    svg.setAttribute('width', '100%');\n    svg.setAttribute('preserveAspectRatio', 'xMidYMid meet');\n    svg.setAttribute('focusable', 'false'); // Disable IE11 default behavior to make SVGs focusable.\n\n    if (options && options.viewBox) {\n      svg.setAttribute('viewBox', options.viewBox);\n    }\n\n    return svg;\n  }\n  /**\n   * Returns an Observable which produces the string contents of the given icon. Results may be\n   * cached, so future calls with the same URL may not cause another HTTP request.\n   */\n\n\n  _fetchIcon(iconConfig) {\n    const {\n      url: safeUrl,\n      options\n    } = iconConfig;\n    const withCredentials = options?.withCredentials ?? false;\n\n    if (!this._httpClient) {\n      throw getMatIconNoHttpProviderError();\n    } // TODO: add an ngDevMode check\n\n\n    if (safeUrl == null) {\n      throw Error(`Cannot fetch icon from URL \"${safeUrl}\".`);\n    }\n\n    const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl); // TODO: add an ngDevMode check\n\n\n    if (!url) {\n      throw getMatIconFailedToSanitizeUrlError(safeUrl);\n    } // Store in-progress fetches to avoid sending a duplicate request for a URL when there is\n    // already a request in progress for that URL. It's necessary to call share() on the\n    // Observable returned by http.get() so that multiple subscribers don't cause multiple XHRs.\n\n\n    const inProgressFetch = this._inProgressUrlFetches.get(url);\n\n    if (inProgressFetch) {\n      return inProgressFetch;\n    }\n\n    const req = this._httpClient.get(url, {\n      responseType: 'text',\n      withCredentials\n    }).pipe(map(svg => {\n      // Security: This SVG is fetched from a SafeResourceUrl, and is thus\n      // trusted HTML.\n      return trustedHTMLFromString(svg);\n    }), finalize(() => this._inProgressUrlFetches.delete(url)), share());\n\n    this._inProgressUrlFetches.set(url, req);\n\n    return req;\n  }\n  /**\n   * Registers an icon config by name in the specified namespace.\n   * @param namespace Namespace in which to register the icon config.\n   * @param iconName Name under which to register the config.\n   * @param config Config to be registered.\n   */\n\n\n  _addSvgIconConfig(namespace, iconName, config) {\n    this._svgIconConfigs.set(iconKey(namespace, iconName), config);\n\n    return this;\n  }\n  /**\n   * Registers an icon set config in the specified namespace.\n   * @param namespace Namespace in which to register the icon config.\n   * @param config Config to be registered.\n   */\n\n\n  _addSvgIconSetConfig(namespace, config) {\n    const configNamespace = this._iconSetConfigs.get(namespace);\n\n    if (configNamespace) {\n      configNamespace.push(config);\n    } else {\n      this._iconSetConfigs.set(namespace, [config]);\n    }\n\n    return this;\n  }\n  /** Parses a config's text into an SVG element. */\n\n\n  _svgElementFromConfig(config) {\n    if (!config.svgElement) {\n      const svg = this._svgElementFromString(config.svgText);\n\n      this._setSvgAttributes(svg, config.options);\n\n      config.svgElement = svg;\n    }\n\n    return config.svgElement;\n  }\n  /** Tries to create an icon config through the registered resolver functions. */\n\n\n  _getIconConfigFromResolvers(namespace, name) {\n    for (let i = 0; i < this._resolvers.length; i++) {\n      const result = this._resolvers[i](name, namespace);\n\n      if (result) {\n        return isSafeUrlWithOptions(result) ? new SvgIconConfig(result.url, null, result.options) : new SvgIconConfig(result, null);\n      }\n    }\n\n    return undefined;\n  }\n\n}\n\nMatIconRegistry.ɵfac = function MatIconRegistry_Factory(t) {\n  return new (t || MatIconRegistry)(i0.ɵɵinject(i1.HttpClient, 8), i0.ɵɵinject(i2.DomSanitizer), i0.ɵɵinject(DOCUMENT, 8), i0.ɵɵinject(i0.ErrorHandler));\n};\n\nMatIconRegistry.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: MatIconRegistry,\n  factory: MatIconRegistry.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatIconRegistry, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.HttpClient,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i2.DomSanitizer\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i0.ErrorHandler\n    }];\n  }, null);\n})();\n/** @docs-private */\n\n\nfunction ICON_REGISTRY_PROVIDER_FACTORY(parentRegistry, httpClient, sanitizer, errorHandler, document) {\n  return parentRegistry || new MatIconRegistry(httpClient, sanitizer, document, errorHandler);\n}\n/** @docs-private */\n\n\nconst ICON_REGISTRY_PROVIDER = {\n  // If there is already an MatIconRegistry available, use that. Otherwise, provide a new one.\n  provide: MatIconRegistry,\n  deps: [[new Optional(), new SkipSelf(), MatIconRegistry], [new Optional(), HttpClient], DomSanitizer, ErrorHandler, [new Optional(), DOCUMENT]],\n  useFactory: ICON_REGISTRY_PROVIDER_FACTORY\n};\n/** Clones an SVGElement while preserving type information. */\n\nfunction cloneSvg(svg) {\n  return svg.cloneNode(true);\n}\n/** Returns the cache key to use for an icon namespace and name. */\n\n\nfunction iconKey(namespace, name) {\n  return namespace + ':' + name;\n}\n\nfunction isSafeUrlWithOptions(value) {\n  return !!(value.url && value.options);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatIcon.\n\n/** @docs-private */\n\n\nconst _MatIconBase = mixinColor(class {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n\n});\n/** Injection token to be used to override the default options for `mat-icon`. */\n\n\nconst MAT_ICON_DEFAULT_OPTIONS = new InjectionToken('MAT_ICON_DEFAULT_OPTIONS');\n/**\n * Injection token used to provide the current location to `MatIcon`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\n\nconst MAT_ICON_LOCATION = new InjectionToken('mat-icon-location', {\n  providedIn: 'root',\n  factory: MAT_ICON_LOCATION_FACTORY\n});\n/** @docs-private */\n\nfunction MAT_ICON_LOCATION_FACTORY() {\n  const _document = inject(DOCUMENT);\n\n  const _location = _document ? _document.location : null;\n\n  return {\n    // Note that this needs to be a function, rather than a property, because Angular\n    // will only resolve it once, but we want the current path on each call.\n    getPathname: () => _location ? _location.pathname + _location.search : ''\n  };\n}\n/** SVG attributes that accept a FuncIRI (e.g. `url(<something>)`). */\n\n\nconst funcIriAttributes = ['clip-path', 'color-profile', 'src', 'cursor', 'fill', 'filter', 'marker', 'marker-start', 'marker-mid', 'marker-end', 'mask', 'stroke'];\n/** Selector that can be used to find all elements that are using a `FuncIRI`. */\n\nconst funcIriAttributeSelector = funcIriAttributes.map(attr => `[${attr}]`).join(', ');\n/** Regex that can be used to extract the id out of a FuncIRI. */\n\nconst funcIriPattern = /^url\\(['\"]?#(.*?)['\"]?\\)$/;\n/**\n * Component to display an icon. It can be used in the following ways:\n *\n * - Specify the svgIcon input to load an SVG icon from a URL previously registered with the\n *   addSvgIcon, addSvgIconInNamespace, addSvgIconSet, or addSvgIconSetInNamespace methods of\n *   MatIconRegistry. If the svgIcon value contains a colon it is assumed to be in the format\n *   \"[namespace]:[name]\", if not the value will be the name of an icon in the default namespace.\n *   Examples:\n *     `<mat-icon svgIcon=\"left-arrow\"></mat-icon>\n *     <mat-icon svgIcon=\"animals:cat\"></mat-icon>`\n *\n * - Use a font ligature as an icon by putting the ligature text in the `fontIcon` attribute or the\n *   content of the `<mat-icon>` component. If you register a custom font class, don't forget to also\n *   include the special class `mat-ligature-font`. It is recommended to use the attribute alternative\n *   to prevent the ligature text to be selectable and to appear in search engine results.\n *   By default, the Material icons font is used as described at\n *   http://google.github.io/material-design-icons/#icon-font-for-the-web. You can specify an\n *   alternate font by setting the fontSet input to either the CSS class to apply to use the\n *   desired font, or to an alias previously registered with MatIconRegistry.registerFontClassAlias.\n *   Examples:\n *     `<mat-icon fontIcon=\"home\"></mat-icon>\n *     <mat-icon>home</mat-icon>\n *     <mat-icon fontSet=\"myfont\" fontIcon=\"sun\"></mat-icon>\n *     <mat-icon fontSet=\"myfont\">sun</mat-icon>`\n *\n * - Specify a font glyph to be included via CSS rules by setting the fontSet input to specify the\n *   font, and the fontIcon input to specify the icon. Typically the fontIcon will specify a\n *   CSS class which causes the glyph to be displayed via a :before selector, as in\n *   https://fortawesome.github.io/Font-Awesome/examples/\n *   Example:\n *     `<mat-icon fontSet=\"fa\" fontIcon=\"alarm\"></mat-icon>`\n */\n\nclass MatIcon extends _MatIconBase {\n  constructor(elementRef, _iconRegistry, ariaHidden, _location, _errorHandler, defaults) {\n    super(elementRef);\n    this._iconRegistry = _iconRegistry;\n    this._location = _location;\n    this._errorHandler = _errorHandler;\n    this._inline = false;\n    this._previousFontSetClass = [];\n    /** Subscription to the current in-progress SVG icon request. */\n\n    this._currentIconFetch = Subscription.EMPTY;\n\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this.defaultColor = defaults.color;\n      }\n\n      if (defaults.fontSet) {\n        this.fontSet = defaults.fontSet;\n      }\n    } // If the user has not explicitly set aria-hidden, mark the icon as hidden, as this is\n    // the right thing to do for the majority of icon use-cases.\n\n\n    if (!ariaHidden) {\n      elementRef.nativeElement.setAttribute('aria-hidden', 'true');\n    }\n  }\n  /**\n   * Whether the icon should be inlined, automatically sizing the icon to match the font size of\n   * the element the icon is contained in.\n   */\n\n\n  get inline() {\n    return this._inline;\n  }\n\n  set inline(inline) {\n    this._inline = coerceBooleanProperty(inline);\n  }\n  /** Name of the icon in the SVG icon set. */\n\n\n  get svgIcon() {\n    return this._svgIcon;\n  }\n\n  set svgIcon(value) {\n    if (value !== this._svgIcon) {\n      if (value) {\n        this._updateSvgIcon(value);\n      } else if (this._svgIcon) {\n        this._clearSvgElement();\n      }\n\n      this._svgIcon = value;\n    }\n  }\n  /** Font set that the icon is a part of. */\n\n\n  get fontSet() {\n    return this._fontSet;\n  }\n\n  set fontSet(value) {\n    const newValue = this._cleanupFontValue(value);\n\n    if (newValue !== this._fontSet) {\n      this._fontSet = newValue;\n\n      this._updateFontIconClasses();\n    }\n  }\n  /** Name of an icon within a font set. */\n\n\n  get fontIcon() {\n    return this._fontIcon;\n  }\n\n  set fontIcon(value) {\n    const newValue = this._cleanupFontValue(value);\n\n    if (newValue !== this._fontIcon) {\n      this._fontIcon = newValue;\n\n      this._updateFontIconClasses();\n    }\n  }\n  /**\n   * Splits an svgIcon binding value into its icon set and icon name components.\n   * Returns a 2-element array of [(icon set), (icon name)].\n   * The separator for the two fields is ':'. If there is no separator, an empty\n   * string is returned for the icon set and the entire value is returned for\n   * the icon name. If the argument is falsy, returns an array of two empty strings.\n   * Throws an error if the name contains two or more ':' separators.\n   * Examples:\n   *   `'social:cake' -> ['social', 'cake']\n   *   'penguin' -> ['', 'penguin']\n   *   null -> ['', '']\n   *   'a:b:c' -> (throws Error)`\n   */\n\n\n  _splitIconName(iconName) {\n    if (!iconName) {\n      return ['', ''];\n    }\n\n    const parts = iconName.split(':');\n\n    switch (parts.length) {\n      case 1:\n        return ['', parts[0]];\n      // Use default namespace.\n\n      case 2:\n        return parts;\n\n      default:\n        throw Error(`Invalid icon name: \"${iconName}\"`);\n      // TODO: add an ngDevMode check\n    }\n  }\n\n  ngOnInit() {\n    // Update font classes because ngOnChanges won't be called if none of the inputs are present,\n    // e.g. <mat-icon>arrow</mat-icon> In this case we need to add a CSS class for the default font.\n    this._updateFontIconClasses();\n  }\n\n  ngAfterViewChecked() {\n    const cachedElements = this._elementsWithExternalReferences;\n\n    if (cachedElements && cachedElements.size) {\n      const newPath = this._location.getPathname(); // We need to check whether the URL has changed on each change detection since\n      // the browser doesn't have an API that will let us react on link clicks and\n      // we can't depend on the Angular router. The references need to be updated,\n      // because while most browsers don't care whether the URL is correct after\n      // the first render, Safari will break if the user navigates to a different\n      // page and the SVG isn't re-rendered.\n\n\n      if (newPath !== this._previousPath) {\n        this._previousPath = newPath;\n\n        this._prependPathToReferences(newPath);\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    this._currentIconFetch.unsubscribe();\n\n    if (this._elementsWithExternalReferences) {\n      this._elementsWithExternalReferences.clear();\n    }\n  }\n\n  _usingFontIcon() {\n    return !this.svgIcon;\n  }\n\n  _setSvgElement(svg) {\n    this._clearSvgElement(); // Note: we do this fix here, rather than the icon registry, because the\n    // references have to point to the URL at the time that the icon was created.\n\n\n    const path = this._location.getPathname();\n\n    this._previousPath = path;\n\n    this._cacheChildrenWithExternalReferences(svg);\n\n    this._prependPathToReferences(path);\n\n    this._elementRef.nativeElement.appendChild(svg);\n  }\n\n  _clearSvgElement() {\n    const layoutElement = this._elementRef.nativeElement;\n    let childCount = layoutElement.childNodes.length;\n\n    if (this._elementsWithExternalReferences) {\n      this._elementsWithExternalReferences.clear();\n    } // Remove existing non-element child nodes and SVGs, and add the new SVG element. Note that\n    // we can't use innerHTML, because IE will throw if the element has a data binding.\n\n\n    while (childCount--) {\n      const child = layoutElement.childNodes[childCount]; // 1 corresponds to Node.ELEMENT_NODE. We remove all non-element nodes in order to get rid\n      // of any loose text nodes, as well as any SVG elements in order to remove any old icons.\n\n      if (child.nodeType !== 1 || child.nodeName.toLowerCase() === 'svg') {\n        child.remove();\n      }\n    }\n  }\n\n  _updateFontIconClasses() {\n    if (!this._usingFontIcon()) {\n      return;\n    }\n\n    const elem = this._elementRef.nativeElement;\n    const fontSetClasses = (this.fontSet ? this._iconRegistry.classNameForFontAlias(this.fontSet).split(/ +/) : this._iconRegistry.getDefaultFontSetClass()).filter(className => className.length > 0);\n\n    this._previousFontSetClass.forEach(className => elem.classList.remove(className));\n\n    fontSetClasses.forEach(className => elem.classList.add(className));\n    this._previousFontSetClass = fontSetClasses;\n\n    if (this.fontIcon !== this._previousFontIconClass && !fontSetClasses.includes('mat-ligature-font')) {\n      if (this._previousFontIconClass) {\n        elem.classList.remove(this._previousFontIconClass);\n      }\n\n      if (this.fontIcon) {\n        elem.classList.add(this.fontIcon);\n      }\n\n      this._previousFontIconClass = this.fontIcon;\n    }\n  }\n  /**\n   * Cleans up a value to be used as a fontIcon or fontSet.\n   * Since the value ends up being assigned as a CSS class, we\n   * have to trim the value and omit space-separated values.\n   */\n\n\n  _cleanupFontValue(value) {\n    return typeof value === 'string' ? value.trim().split(' ')[0] : value;\n  }\n  /**\n   * Prepends the current path to all elements that have an attribute pointing to a `FuncIRI`\n   * reference. This is required because WebKit browsers require references to be prefixed with\n   * the current path, if the page has a `base` tag.\n   */\n\n\n  _prependPathToReferences(path) {\n    const elements = this._elementsWithExternalReferences;\n\n    if (elements) {\n      elements.forEach((attrs, element) => {\n        attrs.forEach(attr => {\n          element.setAttribute(attr.name, `url('${path}#${attr.value}')`);\n        });\n      });\n    }\n  }\n  /**\n   * Caches the children of an SVG element that have `url()`\n   * references that we need to prefix with the current path.\n   */\n\n\n  _cacheChildrenWithExternalReferences(element) {\n    const elementsWithFuncIri = element.querySelectorAll(funcIriAttributeSelector);\n    const elements = this._elementsWithExternalReferences = this._elementsWithExternalReferences || new Map();\n\n    for (let i = 0; i < elementsWithFuncIri.length; i++) {\n      funcIriAttributes.forEach(attr => {\n        const elementWithReference = elementsWithFuncIri[i];\n        const value = elementWithReference.getAttribute(attr);\n        const match = value ? value.match(funcIriPattern) : null;\n\n        if (match) {\n          let attributes = elements.get(elementWithReference);\n\n          if (!attributes) {\n            attributes = [];\n            elements.set(elementWithReference, attributes);\n          }\n\n          attributes.push({\n            name: attr,\n            value: match[1]\n          });\n        }\n      });\n    }\n  }\n  /** Sets a new SVG icon with a particular name. */\n\n\n  _updateSvgIcon(rawName) {\n    this._svgNamespace = null;\n    this._svgName = null;\n\n    this._currentIconFetch.unsubscribe();\n\n    if (rawName) {\n      const [namespace, iconName] = this._splitIconName(rawName);\n\n      if (namespace) {\n        this._svgNamespace = namespace;\n      }\n\n      if (iconName) {\n        this._svgName = iconName;\n      }\n\n      this._currentIconFetch = this._iconRegistry.getNamedSvgIcon(iconName, namespace).pipe(take(1)).subscribe(svg => this._setSvgElement(svg), err => {\n        const errorMessage = `Error retrieving icon ${namespace}:${iconName}! ${err.message}`;\n\n        this._errorHandler.handleError(new Error(errorMessage));\n      });\n    }\n  }\n\n}\n\nMatIcon.ɵfac = function MatIcon_Factory(t) {\n  return new (t || MatIcon)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MatIconRegistry), i0.ɵɵinjectAttribute('aria-hidden'), i0.ɵɵdirectiveInject(MAT_ICON_LOCATION), i0.ɵɵdirectiveInject(i0.ErrorHandler), i0.ɵɵdirectiveInject(MAT_ICON_DEFAULT_OPTIONS, 8));\n};\n\nMatIcon.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatIcon,\n  selectors: [[\"mat-icon\"]],\n  hostAttrs: [\"role\", \"img\", 1, \"mat-icon\", \"notranslate\"],\n  hostVars: 8,\n  hostBindings: function MatIcon_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"data-mat-icon-type\", ctx._usingFontIcon() ? \"font\" : \"svg\")(\"data-mat-icon-name\", ctx._svgName || ctx.fontIcon)(\"data-mat-icon-namespace\", ctx._svgNamespace || ctx.fontSet)(\"fontIcon\", ctx._usingFontIcon() ? ctx.fontIcon : null);\n      i0.ɵɵclassProp(\"mat-icon-inline\", ctx.inline)(\"mat-icon-no-color\", ctx.color !== \"primary\" && ctx.color !== \"accent\" && ctx.color !== \"warn\");\n    }\n  },\n  inputs: {\n    color: \"color\",\n    inline: \"inline\",\n    svgIcon: \"svgIcon\",\n    fontSet: \"fontSet\",\n    fontIcon: \"fontIcon\"\n  },\n  exportAs: [\"matIcon\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function MatIcon_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  styles: [\".mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatIcon, [{\n    type: Component,\n    args: [{\n      template: '<ng-content></ng-content>',\n      selector: 'mat-icon',\n      exportAs: 'matIcon',\n      inputs: ['color'],\n      host: {\n        'role': 'img',\n        'class': 'mat-icon notranslate',\n        '[attr.data-mat-icon-type]': '_usingFontIcon() ? \"font\" : \"svg\"',\n        '[attr.data-mat-icon-name]': '_svgName || fontIcon',\n        '[attr.data-mat-icon-namespace]': '_svgNamespace || fontSet',\n        '[attr.fontIcon]': '_usingFontIcon() ? fontIcon : null',\n        '[class.mat-icon-inline]': 'inline',\n        '[class.mat-icon-no-color]': 'color !== \"primary\" && color !== \"accent\" && color !== \"warn\"'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: MatIconRegistry\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['aria-hidden']\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_ICON_LOCATION]\n      }]\n    }, {\n      type: i0.ErrorHandler\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_ICON_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, {\n    inline: [{\n      type: Input\n    }],\n    svgIcon: [{\n      type: Input\n    }],\n    fontSet: [{\n      type: Input\n    }],\n    fontIcon: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatIconModule {}\n\nMatIconModule.ɵfac = function MatIconModule_Factory(t) {\n  return new (t || MatIconModule)();\n};\n\nMatIconModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatIconModule,\n  declarations: [MatIcon],\n  imports: [MatCommonModule],\n  exports: [MatIcon, MatCommonModule]\n});\nMatIconModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule, MatCommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatIconModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule],\n      exports: [MatIcon, MatCommonModule],\n      declarations: [MatIcon]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { ICON_REGISTRY_PROVIDER, ICON_REGISTRY_PROVIDER_FACTORY, MAT_ICON_DEFAULT_OPTIONS, MAT_ICON_LOCATION, MAT_ICON_LOCATION_FACTORY, MatIcon, MatIconModule, MatIconRegistry, getMatIconFailedToSanitizeLiteralError, getMatIconFailedToSanitizeUrlError, getMatIconNameNotFoundError, getMatIconNoHttpProviderError };", "map": {"version": 3, "names": ["i0", "SecurityContext", "Injectable", "Optional", "Inject", "SkipSelf", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "InjectionToken", "inject", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Attribute", "Input", "NgModule", "mixinColor", "MatCommonModule", "coerceBooleanProperty", "DOCUMENT", "of", "throwError", "fork<PERSON><PERSON>n", "Subscription", "tap", "map", "catchError", "finalize", "share", "take", "i1", "HttpClient", "i2", "Dom<PERSON><PERSON><PERSON>zer", "policy", "getPolicy", "undefined", "window", "ttWindow", "trustedTypes", "createPolicy", "createHTML", "s", "trustedHTMLFromString", "html", "getMatIconNameNotFoundError", "iconName", "Error", "getMatIconNoHttpProviderError", "getMatIconFailedToSanitizeUrlError", "url", "getMatIconFailedToSanitizeLiteralError", "literal", "SvgIconConfig", "constructor", "svgText", "options", "MatIconRegistry", "_httpClient", "_sanitizer", "document", "_error<PERSON><PERSON><PERSON>", "_svgIconConfigs", "Map", "_iconSetConfigs", "_cachedIconsByUrl", "_inProgressUrlFetches", "_fontCssClassesByAlias", "_resolvers", "_defaultFontSetClass", "_document", "addSvgIcon", "addSvgIconInNamespace", "addSvgIconLiteral", "addSvgIconLiteralInNamespace", "namespace", "_addSvgIconConfig", "addSvgIconResolver", "resolver", "push", "cleanLiteral", "sanitize", "HTML", "trustedLiteral", "addSvgIconSet", "addSvgIconSetInNamespace", "addSvgIconSetLiteral", "addSvgIconSetLiteralInNamespace", "_addSvgIconSetConfig", "registerFontClassAlias", "alias", "classNames", "set", "classNameForFontAlias", "get", "setDefaultFontSetClass", "getDefaultFontSetClass", "getSvgIconFromUrl", "safeUrl", "RESOURCE_URL", "cachedIcon", "cloneSvg", "_loadSvgIconFromConfig", "pipe", "svg", "getNamedSvgIcon", "name", "key", "<PERSON><PERSON><PERSON>", "config", "_getSvgFromConfig", "_getIconConfigFromResolvers", "iconSetConfigs", "_getSvgFromIconSetConfigs", "ngOnDestroy", "clear", "_svgElementFromConfig", "namedIcon", "_extractIconWithNameFromAnySet", "iconSetFetchRequests", "filter", "iconSetConfig", "_loadSvgIconSetFromConfig", "err", "errorMessage", "message", "handleError", "foundIcon", "i", "length", "toString", "indexOf", "_extractSvgIconFromSet", "_fetchIcon", "iconSet", "iconSource", "querySelector", "iconElement", "cloneNode", "removeAttribute", "nodeName", "toLowerCase", "_setSvgAttributes", "_toSvgElement", "_svgElementFromString", "append<PERSON><PERSON><PERSON>", "str", "div", "createElement", "innerHTML", "element", "attributes", "value", "setAttribute", "childNodes", "nodeType", "ELEMENT_NODE", "viewBox", "iconConfig", "withCredentials", "inProgressFetch", "req", "responseType", "delete", "configNamespace", "svgElement", "result", "isSafeUrlWithOptions", "ɵfac", "ɵprov", "type", "args", "providedIn", "decorators", "ICON_REGISTRY_PROVIDER_FACTORY", "parentRegistry", "httpClient", "sanitizer", "<PERSON><PERSON><PERSON><PERSON>", "ICON_REGISTRY_PROVIDER", "provide", "deps", "useFactory", "_MatIconBase", "_elementRef", "MAT_ICON_DEFAULT_OPTIONS", "MAT_ICON_LOCATION", "factory", "MAT_ICON_LOCATION_FACTORY", "_location", "location", "getPathname", "pathname", "search", "funcIriAttributes", "funcIriAttributeSelector", "attr", "join", "funcIriPattern", "MatIcon", "elementRef", "_iconRegistry", "ariaHidden", "defaults", "_inline", "_previousFontSetClass", "_currentIconFetch", "EMPTY", "color", "defaultColor", "fontSet", "nativeElement", "inline", "svgIcon", "_svgIcon", "_updateSvgIcon", "_clearSvgElement", "_fontSet", "newValue", "_cleanupFontValue", "_updateFontIconClasses", "fontIcon", "_fontIcon", "_splitIconName", "parts", "split", "ngOnInit", "ngAfterViewChecked", "cachedElements", "_elementsWithExternalReferences", "size", "newPath", "_previousPath", "_prependPathToReferences", "unsubscribe", "_usingFontIcon", "_setSvgElement", "path", "_cacheChildrenWithExternalReferences", "layoutElement", "childCount", "child", "remove", "elem", "fontSetClasses", "className", "for<PERSON>ach", "classList", "add", "_previousFontIconClass", "includes", "trim", "elements", "attrs", "elementsWithFuncIri", "querySelectorAll", "elementWithReference", "getAttribute", "match", "rawName", "_svgNamespace", "_svgName", "subscribe", "ElementRef", "ɵcmp", "template", "selector", "exportAs", "inputs", "host", "encapsulation", "None", "changeDetection", "OnPush", "styles", "MatIconModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/material/fesm2020/icon.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { SecurityContext, Injectable, Optional, Inject, Skip<PERSON>elf, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, InjectionToken, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, Input, NgModule } from '@angular/core';\nimport { mixinColor, MatCommonModule } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { DOCUMENT } from '@angular/common';\nimport { of, throwError, forkJoin, Subscription } from 'rxjs';\nimport { tap, map, catchError, finalize, share, take } from 'rxjs/operators';\nimport * as i1 from '@angular/common/http';\nimport { HttpClient } from '@angular/common/http';\nimport * as i2 from '@angular/platform-browser';\nimport { DomSanitizer } from '@angular/platform-browser';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * The Trusted Types policy, or null if Trusted Types are not\n * enabled/supported, or undefined if the policy has not been created yet.\n */\nlet policy;\n/**\n * Returns the Trusted Types policy, or null if Trusted Types are not\n * enabled/supported. The first call to this function will create the policy.\n */\nfunction getPolicy() {\n    if (policy === undefined) {\n        policy = null;\n        if (typeof window !== 'undefined') {\n            const ttWindow = window;\n            if (ttWindow.trustedTypes !== undefined) {\n                policy = ttWindow.trustedTypes.createPolicy('angular#components', {\n                    createHTML: (s) => s,\n                });\n            }\n        }\n    }\n    return policy;\n}\n/**\n * Unsafely promote a string to a TrustedHTML, falling back to strings when\n * Trusted Types are not available.\n * @security This is a security-sensitive function; any use of this function\n * must go through security review. In particular, it must be assured that the\n * provided string will never cause an XSS vulnerability if used in a context\n * that will be interpreted as HTML by a browser, e.g. when assigning to\n * element.innerHTML.\n */\nfunction trustedHTMLFromString(html) {\n    return getPolicy()?.createHTML(html) || html;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Returns an exception to be thrown in the case when attempting to\n * load an icon with a name that cannot be found.\n * @docs-private\n */\nfunction getMatIconNameNotFoundError(iconName) {\n    return Error(`Unable to find icon with the name \"${iconName}\"`);\n}\n/**\n * Returns an exception to be thrown when the consumer attempts to use\n * `<mat-icon>` without including @angular/common/http.\n * @docs-private\n */\nfunction getMatIconNoHttpProviderError() {\n    return Error('Could not find HttpClient provider for use with Angular Material icons. ' +\n        'Please include the HttpClientModule from @angular/common/http in your ' +\n        'app imports.');\n}\n/**\n * Returns an exception to be thrown when a URL couldn't be sanitized.\n * @param url URL that was attempted to be sanitized.\n * @docs-private\n */\nfunction getMatIconFailedToSanitizeUrlError(url) {\n    return Error(`The URL provided to MatIconRegistry was not trusted as a resource URL ` +\n        `via Angular's DomSanitizer. Attempted URL was \"${url}\".`);\n}\n/**\n * Returns an exception to be thrown when a HTML string couldn't be sanitized.\n * @param literal HTML that was attempted to be sanitized.\n * @docs-private\n */\nfunction getMatIconFailedToSanitizeLiteralError(literal) {\n    return Error(`The literal provided to MatIconRegistry was not trusted as safe HTML by ` +\n        `Angular's DomSanitizer. Attempted literal was \"${literal}\".`);\n}\n/**\n * Configuration for an icon, including the URL and possibly the cached SVG element.\n * @docs-private\n */\nclass SvgIconConfig {\n    constructor(url, svgText, options) {\n        this.url = url;\n        this.svgText = svgText;\n        this.options = options;\n    }\n}\n/**\n * Service to register and display icons used by the `<mat-icon>` component.\n * - Registers icon URLs by namespace and name.\n * - Registers icon set URLs by namespace.\n * - Registers aliases for CSS classes, for use with icon fonts.\n * - Loads icons from URLs and extracts individual icons from icon sets.\n */\nclass MatIconRegistry {\n    constructor(_httpClient, _sanitizer, document, _errorHandler) {\n        this._httpClient = _httpClient;\n        this._sanitizer = _sanitizer;\n        this._errorHandler = _errorHandler;\n        /**\n         * URLs and cached SVG elements for individual icons. Keys are of the format \"[namespace]:[icon]\".\n         */\n        this._svgIconConfigs = new Map();\n        /**\n         * SvgIconConfig objects and cached SVG elements for icon sets, keyed by namespace.\n         * Multiple icon sets can be registered under the same namespace.\n         */\n        this._iconSetConfigs = new Map();\n        /** Cache for icons loaded by direct URLs. */\n        this._cachedIconsByUrl = new Map();\n        /** In-progress icon fetches. Used to coalesce multiple requests to the same URL. */\n        this._inProgressUrlFetches = new Map();\n        /** Map from font identifiers to their CSS class names. Used for icon fonts. */\n        this._fontCssClassesByAlias = new Map();\n        /** Registered icon resolver functions. */\n        this._resolvers = [];\n        /**\n         * The CSS classes to apply when an `<mat-icon>` component has no icon name, url, or font\n         * specified. The default 'material-icons' value assumes that the material icon font has been\n         * loaded as described at http://google.github.io/material-design-icons/#icon-font-for-the-web\n         */\n        this._defaultFontSetClass = ['material-icons', 'mat-ligature-font'];\n        this._document = document;\n    }\n    /**\n     * Registers an icon by URL in the default namespace.\n     * @param iconName Name under which the icon should be registered.\n     * @param url\n     */\n    addSvgIcon(iconName, url, options) {\n        return this.addSvgIconInNamespace('', iconName, url, options);\n    }\n    /**\n     * Registers an icon using an HTML string in the default namespace.\n     * @param iconName Name under which the icon should be registered.\n     * @param literal SVG source of the icon.\n     */\n    addSvgIconLiteral(iconName, literal, options) {\n        return this.addSvgIconLiteralInNamespace('', iconName, literal, options);\n    }\n    /**\n     * Registers an icon by URL in the specified namespace.\n     * @param namespace Namespace in which the icon should be registered.\n     * @param iconName Name under which the icon should be registered.\n     * @param url\n     */\n    addSvgIconInNamespace(namespace, iconName, url, options) {\n        return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig(url, null, options));\n    }\n    /**\n     * Registers an icon resolver function with the registry. The function will be invoked with the\n     * name and namespace of an icon when the registry tries to resolve the URL from which to fetch\n     * the icon. The resolver is expected to return a `SafeResourceUrl` that points to the icon,\n     * an object with the icon URL and icon options, or `null` if the icon is not supported. Resolvers\n     * will be invoked in the order in which they have been registered.\n     * @param resolver Resolver function to be registered.\n     */\n    addSvgIconResolver(resolver) {\n        this._resolvers.push(resolver);\n        return this;\n    }\n    /**\n     * Registers an icon using an HTML string in the specified namespace.\n     * @param namespace Namespace in which the icon should be registered.\n     * @param iconName Name under which the icon should be registered.\n     * @param literal SVG source of the icon.\n     */\n    addSvgIconLiteralInNamespace(namespace, iconName, literal, options) {\n        const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n        // TODO: add an ngDevMode check\n        if (!cleanLiteral) {\n            throw getMatIconFailedToSanitizeLiteralError(literal);\n        }\n        // Security: The literal is passed in as SafeHtml, and is thus trusted.\n        const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n        return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig('', trustedLiteral, options));\n    }\n    /**\n     * Registers an icon set by URL in the default namespace.\n     * @param url\n     */\n    addSvgIconSet(url, options) {\n        return this.addSvgIconSetInNamespace('', url, options);\n    }\n    /**\n     * Registers an icon set using an HTML string in the default namespace.\n     * @param literal SVG source of the icon set.\n     */\n    addSvgIconSetLiteral(literal, options) {\n        return this.addSvgIconSetLiteralInNamespace('', literal, options);\n    }\n    /**\n     * Registers an icon set by URL in the specified namespace.\n     * @param namespace Namespace in which to register the icon set.\n     * @param url\n     */\n    addSvgIconSetInNamespace(namespace, url, options) {\n        return this._addSvgIconSetConfig(namespace, new SvgIconConfig(url, null, options));\n    }\n    /**\n     * Registers an icon set using an HTML string in the specified namespace.\n     * @param namespace Namespace in which to register the icon set.\n     * @param literal SVG source of the icon set.\n     */\n    addSvgIconSetLiteralInNamespace(namespace, literal, options) {\n        const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n        if (!cleanLiteral) {\n            throw getMatIconFailedToSanitizeLiteralError(literal);\n        }\n        // Security: The literal is passed in as SafeHtml, and is thus trusted.\n        const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n        return this._addSvgIconSetConfig(namespace, new SvgIconConfig('', trustedLiteral, options));\n    }\n    /**\n     * Defines an alias for CSS class names to be used for icon fonts. Creating an matIcon\n     * component with the alias as the fontSet input will cause the class name to be applied\n     * to the `<mat-icon>` element.\n     *\n     * If the registered font is a ligature font, then don't forget to also include the special\n     * class `mat-ligature-font` to allow the usage via attribute. So register like this:\n     *\n     * ```ts\n     * iconRegistry.registerFontClassAlias('f1', 'font1 mat-ligature-font');\n     * ```\n     *\n     * And use like this:\n     *\n     * ```html\n     * <mat-icon fontSet=\"f1\" fontIcon=\"home\"></mat-icon>\n     * ```\n     *\n     * @param alias Alias for the font.\n     * @param classNames Class names override to be used instead of the alias.\n     */\n    registerFontClassAlias(alias, classNames = alias) {\n        this._fontCssClassesByAlias.set(alias, classNames);\n        return this;\n    }\n    /**\n     * Returns the CSS class name associated with the alias by a previous call to\n     * registerFontClassAlias. If no CSS class has been associated, returns the alias unmodified.\n     */\n    classNameForFontAlias(alias) {\n        return this._fontCssClassesByAlias.get(alias) || alias;\n    }\n    /**\n     * Sets the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n     * have a fontSet input value, and is not loading an icon by name or URL.\n     */\n    setDefaultFontSetClass(...classNames) {\n        this._defaultFontSetClass = classNames;\n        return this;\n    }\n    /**\n     * Returns the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n     * have a fontSet input value, and is not loading an icon by name or URL.\n     */\n    getDefaultFontSetClass() {\n        return this._defaultFontSetClass;\n    }\n    /**\n     * Returns an Observable that produces the icon (as an `<svg>` DOM element) from the given URL.\n     * The response from the URL may be cached so this will not always cause an HTTP request, but\n     * the produced element will always be a new copy of the originally fetched icon. (That is,\n     * it will not contain any modifications made to elements previously returned).\n     *\n     * @param safeUrl URL from which to fetch the SVG icon.\n     */\n    getSvgIconFromUrl(safeUrl) {\n        const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n        if (!url) {\n            throw getMatIconFailedToSanitizeUrlError(safeUrl);\n        }\n        const cachedIcon = this._cachedIconsByUrl.get(url);\n        if (cachedIcon) {\n            return of(cloneSvg(cachedIcon));\n        }\n        return this._loadSvgIconFromConfig(new SvgIconConfig(safeUrl, null)).pipe(tap(svg => this._cachedIconsByUrl.set(url, svg)), map(svg => cloneSvg(svg)));\n    }\n    /**\n     * Returns an Observable that produces the icon (as an `<svg>` DOM element) with the given name\n     * and namespace. The icon must have been previously registered with addIcon or addIconSet;\n     * if not, the Observable will throw an error.\n     *\n     * @param name Name of the icon to be retrieved.\n     * @param namespace Namespace in which to look for the icon.\n     */\n    getNamedSvgIcon(name, namespace = '') {\n        const key = iconKey(namespace, name);\n        let config = this._svgIconConfigs.get(key);\n        // Return (copy of) cached icon if possible.\n        if (config) {\n            return this._getSvgFromConfig(config);\n        }\n        // Otherwise try to resolve the config from one of the resolver functions.\n        config = this._getIconConfigFromResolvers(namespace, name);\n        if (config) {\n            this._svgIconConfigs.set(key, config);\n            return this._getSvgFromConfig(config);\n        }\n        // See if we have any icon sets registered for the namespace.\n        const iconSetConfigs = this._iconSetConfigs.get(namespace);\n        if (iconSetConfigs) {\n            return this._getSvgFromIconSetConfigs(name, iconSetConfigs);\n        }\n        return throwError(getMatIconNameNotFoundError(key));\n    }\n    ngOnDestroy() {\n        this._resolvers = [];\n        this._svgIconConfigs.clear();\n        this._iconSetConfigs.clear();\n        this._cachedIconsByUrl.clear();\n    }\n    /**\n     * Returns the cached icon for a SvgIconConfig if available, or fetches it from its URL if not.\n     */\n    _getSvgFromConfig(config) {\n        if (config.svgText) {\n            // We already have the SVG element for this icon, return a copy.\n            return of(cloneSvg(this._svgElementFromConfig(config)));\n        }\n        else {\n            // Fetch the icon from the config's URL, cache it, and return a copy.\n            return this._loadSvgIconFromConfig(config).pipe(map(svg => cloneSvg(svg)));\n        }\n    }\n    /**\n     * Attempts to find an icon with the specified name in any of the SVG icon sets.\n     * First searches the available cached icons for a nested element with a matching name, and\n     * if found copies the element to a new `<svg>` element. If not found, fetches all icon sets\n     * that have not been cached, and searches again after all fetches are completed.\n     * The returned Observable produces the SVG element if possible, and throws\n     * an error if no icon with the specified name can be found.\n     */\n    _getSvgFromIconSetConfigs(name, iconSetConfigs) {\n        // For all the icon set SVG elements we've fetched, see if any contain an icon with the\n        // requested name.\n        const namedIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n        if (namedIcon) {\n            // We could cache namedIcon in _svgIconConfigs, but since we have to make a copy every\n            // time anyway, there's probably not much advantage compared to just always extracting\n            // it from the icon set.\n            return of(namedIcon);\n        }\n        // Not found in any cached icon sets. If there are icon sets with URLs that we haven't\n        // fetched, fetch them now and look for iconName in the results.\n        const iconSetFetchRequests = iconSetConfigs\n            .filter(iconSetConfig => !iconSetConfig.svgText)\n            .map(iconSetConfig => {\n            return this._loadSvgIconSetFromConfig(iconSetConfig).pipe(catchError((err) => {\n                const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, iconSetConfig.url);\n                // Swallow errors fetching individual URLs so the\n                // combined Observable won't necessarily fail.\n                const errorMessage = `Loading icon set URL: ${url} failed: ${err.message}`;\n                this._errorHandler.handleError(new Error(errorMessage));\n                return of(null);\n            }));\n        });\n        // Fetch all the icon set URLs. When the requests complete, every IconSet should have a\n        // cached SVG element (unless the request failed), and we can check again for the icon.\n        return forkJoin(iconSetFetchRequests).pipe(map(() => {\n            const foundIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n            // TODO: add an ngDevMode check\n            if (!foundIcon) {\n                throw getMatIconNameNotFoundError(name);\n            }\n            return foundIcon;\n        }));\n    }\n    /**\n     * Searches the cached SVG elements for the given icon sets for a nested icon element whose \"id\"\n     * tag matches the specified name. If found, copies the nested element to a new SVG element and\n     * returns it. Returns null if no matching element is found.\n     */\n    _extractIconWithNameFromAnySet(iconName, iconSetConfigs) {\n        // Iterate backwards, so icon sets added later have precedence.\n        for (let i = iconSetConfigs.length - 1; i >= 0; i--) {\n            const config = iconSetConfigs[i];\n            // Parsing the icon set's text into an SVG element can be expensive. We can avoid some of\n            // the parsing by doing a quick check using `indexOf` to see if there's any chance for the\n            // icon to be in the set. This won't be 100% accurate, but it should help us avoid at least\n            // some of the parsing.\n            if (config.svgText && config.svgText.toString().indexOf(iconName) > -1) {\n                const svg = this._svgElementFromConfig(config);\n                const foundIcon = this._extractSvgIconFromSet(svg, iconName, config.options);\n                if (foundIcon) {\n                    return foundIcon;\n                }\n            }\n        }\n        return null;\n    }\n    /**\n     * Loads the content of the icon URL specified in the SvgIconConfig and creates an SVG element\n     * from it.\n     */\n    _loadSvgIconFromConfig(config) {\n        return this._fetchIcon(config).pipe(tap(svgText => (config.svgText = svgText)), map(() => this._svgElementFromConfig(config)));\n    }\n    /**\n     * Loads the content of the icon set URL specified in the\n     * SvgIconConfig and attaches it to the config.\n     */\n    _loadSvgIconSetFromConfig(config) {\n        if (config.svgText) {\n            return of(null);\n        }\n        return this._fetchIcon(config).pipe(tap(svgText => (config.svgText = svgText)));\n    }\n    /**\n     * Searches the cached element of the given SvgIconConfig for a nested icon element whose \"id\"\n     * tag matches the specified name. If found, copies the nested element to a new SVG element and\n     * returns it. Returns null if no matching element is found.\n     */\n    _extractSvgIconFromSet(iconSet, iconName, options) {\n        // Use the `id=\"iconName\"` syntax in order to escape special\n        // characters in the ID (versus using the #iconName syntax).\n        const iconSource = iconSet.querySelector(`[id=\"${iconName}\"]`);\n        if (!iconSource) {\n            return null;\n        }\n        // Clone the element and remove the ID to prevent multiple elements from being added\n        // to the page with the same ID.\n        const iconElement = iconSource.cloneNode(true);\n        iconElement.removeAttribute('id');\n        // If the icon node is itself an <svg> node, clone and return it directly. If not, set it as\n        // the content of a new <svg> node.\n        if (iconElement.nodeName.toLowerCase() === 'svg') {\n            return this._setSvgAttributes(iconElement, options);\n        }\n        // If the node is a <symbol>, it won't be rendered so we have to convert it into <svg>. Note\n        // that the same could be achieved by referring to it via <use href=\"#id\">, however the <use>\n        // tag is problematic on Firefox, because it needs to include the current page path.\n        if (iconElement.nodeName.toLowerCase() === 'symbol') {\n            return this._setSvgAttributes(this._toSvgElement(iconElement), options);\n        }\n        // createElement('SVG') doesn't work as expected; the DOM ends up with\n        // the correct nodes, but the SVG content doesn't render. Instead we\n        // have to create an empty SVG node using innerHTML and append its content.\n        // Elements created using DOMParser.parseFromString have the same problem.\n        // http://stackoverflow.com/questions/23003278/svg-innerhtml-in-firefox-can-not-display\n        const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n        // Clone the node so we don't remove it from the parent icon set element.\n        svg.appendChild(iconElement);\n        return this._setSvgAttributes(svg, options);\n    }\n    /**\n     * Creates a DOM element from the given SVG string.\n     */\n    _svgElementFromString(str) {\n        const div = this._document.createElement('DIV');\n        div.innerHTML = str;\n        const svg = div.querySelector('svg');\n        // TODO: add an ngDevMode check\n        if (!svg) {\n            throw Error('<svg> tag not found');\n        }\n        return svg;\n    }\n    /**\n     * Converts an element into an SVG node by cloning all of its children.\n     */\n    _toSvgElement(element) {\n        const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n        const attributes = element.attributes;\n        // Copy over all the attributes from the `symbol` to the new SVG, except the id.\n        for (let i = 0; i < attributes.length; i++) {\n            const { name, value } = attributes[i];\n            if (name !== 'id') {\n                svg.setAttribute(name, value);\n            }\n        }\n        for (let i = 0; i < element.childNodes.length; i++) {\n            if (element.childNodes[i].nodeType === this._document.ELEMENT_NODE) {\n                svg.appendChild(element.childNodes[i].cloneNode(true));\n            }\n        }\n        return svg;\n    }\n    /**\n     * Sets the default attributes for an SVG element to be used as an icon.\n     */\n    _setSvgAttributes(svg, options) {\n        svg.setAttribute('fit', '');\n        svg.setAttribute('height', '100%');\n        svg.setAttribute('width', '100%');\n        svg.setAttribute('preserveAspectRatio', 'xMidYMid meet');\n        svg.setAttribute('focusable', 'false'); // Disable IE11 default behavior to make SVGs focusable.\n        if (options && options.viewBox) {\n            svg.setAttribute('viewBox', options.viewBox);\n        }\n        return svg;\n    }\n    /**\n     * Returns an Observable which produces the string contents of the given icon. Results may be\n     * cached, so future calls with the same URL may not cause another HTTP request.\n     */\n    _fetchIcon(iconConfig) {\n        const { url: safeUrl, options } = iconConfig;\n        const withCredentials = options?.withCredentials ?? false;\n        if (!this._httpClient) {\n            throw getMatIconNoHttpProviderError();\n        }\n        // TODO: add an ngDevMode check\n        if (safeUrl == null) {\n            throw Error(`Cannot fetch icon from URL \"${safeUrl}\".`);\n        }\n        const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n        // TODO: add an ngDevMode check\n        if (!url) {\n            throw getMatIconFailedToSanitizeUrlError(safeUrl);\n        }\n        // Store in-progress fetches to avoid sending a duplicate request for a URL when there is\n        // already a request in progress for that URL. It's necessary to call share() on the\n        // Observable returned by http.get() so that multiple subscribers don't cause multiple XHRs.\n        const inProgressFetch = this._inProgressUrlFetches.get(url);\n        if (inProgressFetch) {\n            return inProgressFetch;\n        }\n        const req = this._httpClient.get(url, { responseType: 'text', withCredentials }).pipe(map(svg => {\n            // Security: This SVG is fetched from a SafeResourceUrl, and is thus\n            // trusted HTML.\n            return trustedHTMLFromString(svg);\n        }), finalize(() => this._inProgressUrlFetches.delete(url)), share());\n        this._inProgressUrlFetches.set(url, req);\n        return req;\n    }\n    /**\n     * Registers an icon config by name in the specified namespace.\n     * @param namespace Namespace in which to register the icon config.\n     * @param iconName Name under which to register the config.\n     * @param config Config to be registered.\n     */\n    _addSvgIconConfig(namespace, iconName, config) {\n        this._svgIconConfigs.set(iconKey(namespace, iconName), config);\n        return this;\n    }\n    /**\n     * Registers an icon set config in the specified namespace.\n     * @param namespace Namespace in which to register the icon config.\n     * @param config Config to be registered.\n     */\n    _addSvgIconSetConfig(namespace, config) {\n        const configNamespace = this._iconSetConfigs.get(namespace);\n        if (configNamespace) {\n            configNamespace.push(config);\n        }\n        else {\n            this._iconSetConfigs.set(namespace, [config]);\n        }\n        return this;\n    }\n    /** Parses a config's text into an SVG element. */\n    _svgElementFromConfig(config) {\n        if (!config.svgElement) {\n            const svg = this._svgElementFromString(config.svgText);\n            this._setSvgAttributes(svg, config.options);\n            config.svgElement = svg;\n        }\n        return config.svgElement;\n    }\n    /** Tries to create an icon config through the registered resolver functions. */\n    _getIconConfigFromResolvers(namespace, name) {\n        for (let i = 0; i < this._resolvers.length; i++) {\n            const result = this._resolvers[i](name, namespace);\n            if (result) {\n                return isSafeUrlWithOptions(result)\n                    ? new SvgIconConfig(result.url, null, result.options)\n                    : new SvgIconConfig(result, null);\n            }\n        }\n        return undefined;\n    }\n}\nMatIconRegistry.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatIconRegistry, deps: [{ token: i1.HttpClient, optional: true }, { token: i2.DomSanitizer }, { token: DOCUMENT, optional: true }, { token: i0.ErrorHandler }], target: i0.ɵɵFactoryTarget.Injectable });\nMatIconRegistry.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatIconRegistry, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatIconRegistry, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.HttpClient, decorators: [{\n                    type: Optional\n                }] }, { type: i2.DomSanitizer }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.ErrorHandler }]; } });\n/** @docs-private */\nfunction ICON_REGISTRY_PROVIDER_FACTORY(parentRegistry, httpClient, sanitizer, errorHandler, document) {\n    return parentRegistry || new MatIconRegistry(httpClient, sanitizer, document, errorHandler);\n}\n/** @docs-private */\nconst ICON_REGISTRY_PROVIDER = {\n    // If there is already an MatIconRegistry available, use that. Otherwise, provide a new one.\n    provide: MatIconRegistry,\n    deps: [\n        [new Optional(), new SkipSelf(), MatIconRegistry],\n        [new Optional(), HttpClient],\n        DomSanitizer,\n        ErrorHandler,\n        [new Optional(), DOCUMENT],\n    ],\n    useFactory: ICON_REGISTRY_PROVIDER_FACTORY,\n};\n/** Clones an SVGElement while preserving type information. */\nfunction cloneSvg(svg) {\n    return svg.cloneNode(true);\n}\n/** Returns the cache key to use for an icon namespace and name. */\nfunction iconKey(namespace, name) {\n    return namespace + ':' + name;\n}\nfunction isSafeUrlWithOptions(value) {\n    return !!(value.url && value.options);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatIcon.\n/** @docs-private */\nconst _MatIconBase = mixinColor(class {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n    }\n});\n/** Injection token to be used to override the default options for `mat-icon`. */\nconst MAT_ICON_DEFAULT_OPTIONS = new InjectionToken('MAT_ICON_DEFAULT_OPTIONS');\n/**\n * Injection token used to provide the current location to `MatIcon`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\nconst MAT_ICON_LOCATION = new InjectionToken('mat-icon-location', {\n    providedIn: 'root',\n    factory: MAT_ICON_LOCATION_FACTORY,\n});\n/** @docs-private */\nfunction MAT_ICON_LOCATION_FACTORY() {\n    const _document = inject(DOCUMENT);\n    const _location = _document ? _document.location : null;\n    return {\n        // Note that this needs to be a function, rather than a property, because Angular\n        // will only resolve it once, but we want the current path on each call.\n        getPathname: () => (_location ? _location.pathname + _location.search : ''),\n    };\n}\n/** SVG attributes that accept a FuncIRI (e.g. `url(<something>)`). */\nconst funcIriAttributes = [\n    'clip-path',\n    'color-profile',\n    'src',\n    'cursor',\n    'fill',\n    'filter',\n    'marker',\n    'marker-start',\n    'marker-mid',\n    'marker-end',\n    'mask',\n    'stroke',\n];\n/** Selector that can be used to find all elements that are using a `FuncIRI`. */\nconst funcIriAttributeSelector = funcIriAttributes.map(attr => `[${attr}]`).join(', ');\n/** Regex that can be used to extract the id out of a FuncIRI. */\nconst funcIriPattern = /^url\\(['\"]?#(.*?)['\"]?\\)$/;\n/**\n * Component to display an icon. It can be used in the following ways:\n *\n * - Specify the svgIcon input to load an SVG icon from a URL previously registered with the\n *   addSvgIcon, addSvgIconInNamespace, addSvgIconSet, or addSvgIconSetInNamespace methods of\n *   MatIconRegistry. If the svgIcon value contains a colon it is assumed to be in the format\n *   \"[namespace]:[name]\", if not the value will be the name of an icon in the default namespace.\n *   Examples:\n *     `<mat-icon svgIcon=\"left-arrow\"></mat-icon>\n *     <mat-icon svgIcon=\"animals:cat\"></mat-icon>`\n *\n * - Use a font ligature as an icon by putting the ligature text in the `fontIcon` attribute or the\n *   content of the `<mat-icon>` component. If you register a custom font class, don't forget to also\n *   include the special class `mat-ligature-font`. It is recommended to use the attribute alternative\n *   to prevent the ligature text to be selectable and to appear in search engine results.\n *   By default, the Material icons font is used as described at\n *   http://google.github.io/material-design-icons/#icon-font-for-the-web. You can specify an\n *   alternate font by setting the fontSet input to either the CSS class to apply to use the\n *   desired font, or to an alias previously registered with MatIconRegistry.registerFontClassAlias.\n *   Examples:\n *     `<mat-icon fontIcon=\"home\"></mat-icon>\n *     <mat-icon>home</mat-icon>\n *     <mat-icon fontSet=\"myfont\" fontIcon=\"sun\"></mat-icon>\n *     <mat-icon fontSet=\"myfont\">sun</mat-icon>`\n *\n * - Specify a font glyph to be included via CSS rules by setting the fontSet input to specify the\n *   font, and the fontIcon input to specify the icon. Typically the fontIcon will specify a\n *   CSS class which causes the glyph to be displayed via a :before selector, as in\n *   https://fortawesome.github.io/Font-Awesome/examples/\n *   Example:\n *     `<mat-icon fontSet=\"fa\" fontIcon=\"alarm\"></mat-icon>`\n */\nclass MatIcon extends _MatIconBase {\n    constructor(elementRef, _iconRegistry, ariaHidden, _location, _errorHandler, defaults) {\n        super(elementRef);\n        this._iconRegistry = _iconRegistry;\n        this._location = _location;\n        this._errorHandler = _errorHandler;\n        this._inline = false;\n        this._previousFontSetClass = [];\n        /** Subscription to the current in-progress SVG icon request. */\n        this._currentIconFetch = Subscription.EMPTY;\n        if (defaults) {\n            if (defaults.color) {\n                this.color = this.defaultColor = defaults.color;\n            }\n            if (defaults.fontSet) {\n                this.fontSet = defaults.fontSet;\n            }\n        }\n        // If the user has not explicitly set aria-hidden, mark the icon as hidden, as this is\n        // the right thing to do for the majority of icon use-cases.\n        if (!ariaHidden) {\n            elementRef.nativeElement.setAttribute('aria-hidden', 'true');\n        }\n    }\n    /**\n     * Whether the icon should be inlined, automatically sizing the icon to match the font size of\n     * the element the icon is contained in.\n     */\n    get inline() {\n        return this._inline;\n    }\n    set inline(inline) {\n        this._inline = coerceBooleanProperty(inline);\n    }\n    /** Name of the icon in the SVG icon set. */\n    get svgIcon() {\n        return this._svgIcon;\n    }\n    set svgIcon(value) {\n        if (value !== this._svgIcon) {\n            if (value) {\n                this._updateSvgIcon(value);\n            }\n            else if (this._svgIcon) {\n                this._clearSvgElement();\n            }\n            this._svgIcon = value;\n        }\n    }\n    /** Font set that the icon is a part of. */\n    get fontSet() {\n        return this._fontSet;\n    }\n    set fontSet(value) {\n        const newValue = this._cleanupFontValue(value);\n        if (newValue !== this._fontSet) {\n            this._fontSet = newValue;\n            this._updateFontIconClasses();\n        }\n    }\n    /** Name of an icon within a font set. */\n    get fontIcon() {\n        return this._fontIcon;\n    }\n    set fontIcon(value) {\n        const newValue = this._cleanupFontValue(value);\n        if (newValue !== this._fontIcon) {\n            this._fontIcon = newValue;\n            this._updateFontIconClasses();\n        }\n    }\n    /**\n     * Splits an svgIcon binding value into its icon set and icon name components.\n     * Returns a 2-element array of [(icon set), (icon name)].\n     * The separator for the two fields is ':'. If there is no separator, an empty\n     * string is returned for the icon set and the entire value is returned for\n     * the icon name. If the argument is falsy, returns an array of two empty strings.\n     * Throws an error if the name contains two or more ':' separators.\n     * Examples:\n     *   `'social:cake' -> ['social', 'cake']\n     *   'penguin' -> ['', 'penguin']\n     *   null -> ['', '']\n     *   'a:b:c' -> (throws Error)`\n     */\n    _splitIconName(iconName) {\n        if (!iconName) {\n            return ['', ''];\n        }\n        const parts = iconName.split(':');\n        switch (parts.length) {\n            case 1:\n                return ['', parts[0]]; // Use default namespace.\n            case 2:\n                return parts;\n            default:\n                throw Error(`Invalid icon name: \"${iconName}\"`); // TODO: add an ngDevMode check\n        }\n    }\n    ngOnInit() {\n        // Update font classes because ngOnChanges won't be called if none of the inputs are present,\n        // e.g. <mat-icon>arrow</mat-icon> In this case we need to add a CSS class for the default font.\n        this._updateFontIconClasses();\n    }\n    ngAfterViewChecked() {\n        const cachedElements = this._elementsWithExternalReferences;\n        if (cachedElements && cachedElements.size) {\n            const newPath = this._location.getPathname();\n            // We need to check whether the URL has changed on each change detection since\n            // the browser doesn't have an API that will let us react on link clicks and\n            // we can't depend on the Angular router. The references need to be updated,\n            // because while most browsers don't care whether the URL is correct after\n            // the first render, Safari will break if the user navigates to a different\n            // page and the SVG isn't re-rendered.\n            if (newPath !== this._previousPath) {\n                this._previousPath = newPath;\n                this._prependPathToReferences(newPath);\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._currentIconFetch.unsubscribe();\n        if (this._elementsWithExternalReferences) {\n            this._elementsWithExternalReferences.clear();\n        }\n    }\n    _usingFontIcon() {\n        return !this.svgIcon;\n    }\n    _setSvgElement(svg) {\n        this._clearSvgElement();\n        // Note: we do this fix here, rather than the icon registry, because the\n        // references have to point to the URL at the time that the icon was created.\n        const path = this._location.getPathname();\n        this._previousPath = path;\n        this._cacheChildrenWithExternalReferences(svg);\n        this._prependPathToReferences(path);\n        this._elementRef.nativeElement.appendChild(svg);\n    }\n    _clearSvgElement() {\n        const layoutElement = this._elementRef.nativeElement;\n        let childCount = layoutElement.childNodes.length;\n        if (this._elementsWithExternalReferences) {\n            this._elementsWithExternalReferences.clear();\n        }\n        // Remove existing non-element child nodes and SVGs, and add the new SVG element. Note that\n        // we can't use innerHTML, because IE will throw if the element has a data binding.\n        while (childCount--) {\n            const child = layoutElement.childNodes[childCount];\n            // 1 corresponds to Node.ELEMENT_NODE. We remove all non-element nodes in order to get rid\n            // of any loose text nodes, as well as any SVG elements in order to remove any old icons.\n            if (child.nodeType !== 1 || child.nodeName.toLowerCase() === 'svg') {\n                child.remove();\n            }\n        }\n    }\n    _updateFontIconClasses() {\n        if (!this._usingFontIcon()) {\n            return;\n        }\n        const elem = this._elementRef.nativeElement;\n        const fontSetClasses = (this.fontSet\n            ? this._iconRegistry.classNameForFontAlias(this.fontSet).split(/ +/)\n            : this._iconRegistry.getDefaultFontSetClass()).filter(className => className.length > 0);\n        this._previousFontSetClass.forEach(className => elem.classList.remove(className));\n        fontSetClasses.forEach(className => elem.classList.add(className));\n        this._previousFontSetClass = fontSetClasses;\n        if (this.fontIcon !== this._previousFontIconClass &&\n            !fontSetClasses.includes('mat-ligature-font')) {\n            if (this._previousFontIconClass) {\n                elem.classList.remove(this._previousFontIconClass);\n            }\n            if (this.fontIcon) {\n                elem.classList.add(this.fontIcon);\n            }\n            this._previousFontIconClass = this.fontIcon;\n        }\n    }\n    /**\n     * Cleans up a value to be used as a fontIcon or fontSet.\n     * Since the value ends up being assigned as a CSS class, we\n     * have to trim the value and omit space-separated values.\n     */\n    _cleanupFontValue(value) {\n        return typeof value === 'string' ? value.trim().split(' ')[0] : value;\n    }\n    /**\n     * Prepends the current path to all elements that have an attribute pointing to a `FuncIRI`\n     * reference. This is required because WebKit browsers require references to be prefixed with\n     * the current path, if the page has a `base` tag.\n     */\n    _prependPathToReferences(path) {\n        const elements = this._elementsWithExternalReferences;\n        if (elements) {\n            elements.forEach((attrs, element) => {\n                attrs.forEach(attr => {\n                    element.setAttribute(attr.name, `url('${path}#${attr.value}')`);\n                });\n            });\n        }\n    }\n    /**\n     * Caches the children of an SVG element that have `url()`\n     * references that we need to prefix with the current path.\n     */\n    _cacheChildrenWithExternalReferences(element) {\n        const elementsWithFuncIri = element.querySelectorAll(funcIriAttributeSelector);\n        const elements = (this._elementsWithExternalReferences =\n            this._elementsWithExternalReferences || new Map());\n        for (let i = 0; i < elementsWithFuncIri.length; i++) {\n            funcIriAttributes.forEach(attr => {\n                const elementWithReference = elementsWithFuncIri[i];\n                const value = elementWithReference.getAttribute(attr);\n                const match = value ? value.match(funcIriPattern) : null;\n                if (match) {\n                    let attributes = elements.get(elementWithReference);\n                    if (!attributes) {\n                        attributes = [];\n                        elements.set(elementWithReference, attributes);\n                    }\n                    attributes.push({ name: attr, value: match[1] });\n                }\n            });\n        }\n    }\n    /** Sets a new SVG icon with a particular name. */\n    _updateSvgIcon(rawName) {\n        this._svgNamespace = null;\n        this._svgName = null;\n        this._currentIconFetch.unsubscribe();\n        if (rawName) {\n            const [namespace, iconName] = this._splitIconName(rawName);\n            if (namespace) {\n                this._svgNamespace = namespace;\n            }\n            if (iconName) {\n                this._svgName = iconName;\n            }\n            this._currentIconFetch = this._iconRegistry\n                .getNamedSvgIcon(iconName, namespace)\n                .pipe(take(1))\n                .subscribe(svg => this._setSvgElement(svg), (err) => {\n                const errorMessage = `Error retrieving icon ${namespace}:${iconName}! ${err.message}`;\n                this._errorHandler.handleError(new Error(errorMessage));\n            });\n        }\n    }\n}\nMatIcon.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatIcon, deps: [{ token: i0.ElementRef }, { token: MatIconRegistry }, { token: 'aria-hidden', attribute: true }, { token: MAT_ICON_LOCATION }, { token: i0.ErrorHandler }, { token: MAT_ICON_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatIcon.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatIcon, selector: \"mat-icon\", inputs: { color: \"color\", inline: \"inline\", svgIcon: \"svgIcon\", fontSet: \"fontSet\", fontIcon: \"fontIcon\" }, host: { attributes: { \"role\": \"img\" }, properties: { \"attr.data-mat-icon-type\": \"_usingFontIcon() ? \\\"font\\\" : \\\"svg\\\"\", \"attr.data-mat-icon-name\": \"_svgName || fontIcon\", \"attr.data-mat-icon-namespace\": \"_svgNamespace || fontSet\", \"attr.fontIcon\": \"_usingFontIcon() ? fontIcon : null\", \"class.mat-icon-inline\": \"inline\", \"class.mat-icon-no-color\": \"color !== \\\"primary\\\" && color !== \\\"accent\\\" && color !== \\\"warn\\\"\" }, classAttribute: \"mat-icon notranslate\" }, exportAs: [\"matIcon\"], usesInheritance: true, ngImport: i0, template: '<ng-content></ng-content>', isInline: true, styles: [\".mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatIcon, decorators: [{\n            type: Component,\n            args: [{ template: '<ng-content></ng-content>', selector: 'mat-icon', exportAs: 'matIcon', inputs: ['color'], host: {\n                        'role': 'img',\n                        'class': 'mat-icon notranslate',\n                        '[attr.data-mat-icon-type]': '_usingFontIcon() ? \"font\" : \"svg\"',\n                        '[attr.data-mat-icon-name]': '_svgName || fontIcon',\n                        '[attr.data-mat-icon-namespace]': '_svgNamespace || fontSet',\n                        '[attr.fontIcon]': '_usingFontIcon() ? fontIcon : null',\n                        '[class.mat-icon-inline]': 'inline',\n                        '[class.mat-icon-no-color]': 'color !== \"primary\" && color !== \"accent\" && color !== \"warn\"',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: MatIconRegistry }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['aria-hidden']\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_ICON_LOCATION]\n                }] }, { type: i0.ErrorHandler }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_ICON_DEFAULT_OPTIONS]\n                }] }]; }, propDecorators: { inline: [{\n                type: Input\n            }], svgIcon: [{\n                type: Input\n            }], fontSet: [{\n                type: Input\n            }], fontIcon: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatIconModule {\n}\nMatIconModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatIconModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatIconModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: MatIconModule, declarations: [MatIcon], imports: [MatCommonModule], exports: [MatIcon, MatCommonModule] });\nMatIconModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatIconModule, imports: [MatCommonModule, MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatIconModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule],\n                    exports: [MatIcon, MatCommonModule],\n                    declarations: [MatIcon],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ICON_REGISTRY_PROVIDER, ICON_REGISTRY_PROVIDER_FACTORY, MAT_ICON_DEFAULT_OPTIONS, MAT_ICON_LOCATION, MAT_ICON_LOCATION_FACTORY, MatIcon, MatIconModule, MatIconRegistry, getMatIconFailedToSanitizeLiteralError, getMatIconFailedToSanitizeUrlError, getMatIconNameNotFoundError, getMatIconNoHttpProviderError };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,eAAT,EAA0BC,UAA1B,EAAsCC,QAAtC,EAAgDC,MAAhD,EAAwDC,QAAxD,EAAkEC,YAAlE,EAAgFC,cAAhF,EAAgGC,MAAhG,EAAwGC,SAAxG,EAAmHC,iBAAnH,EAAsIC,uBAAtI,EAA+JC,SAA/J,EAA0KC,KAA1K,EAAiLC,QAAjL,QAAiM,eAAjM;AACA,SAASC,UAAT,EAAqBC,eAArB,QAA4C,wBAA5C;AACA,SAASC,qBAAT,QAAsC,uBAAtC;AACA,SAASC,QAAT,QAAyB,iBAAzB;AACA,SAASC,EAAT,EAAaC,UAAb,EAAyBC,QAAzB,EAAmCC,YAAnC,QAAuD,MAAvD;AACA,SAASC,GAAT,EAAcC,GAAd,EAAmBC,UAAnB,EAA+BC,QAA/B,EAAyCC,KAAzC,EAAgDC,IAAhD,QAA4D,gBAA5D;AACA,OAAO,KAAKC,EAAZ,MAAoB,sBAApB;AACA,SAASC,UAAT,QAA2B,sBAA3B;AACA,OAAO,KAAKC,EAAZ,MAAoB,2BAApB;AACA,SAASC,YAAT,QAA6B,2BAA7B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,IAAIC,MAAJ;AACA;AACA;AACA;AACA;;AACA,SAASC,SAAT,GAAqB;EACjB,IAAID,MAAM,KAAKE,SAAf,EAA0B;IACtBF,MAAM,GAAG,IAAT;;IACA,IAAI,OAAOG,MAAP,KAAkB,WAAtB,EAAmC;MAC/B,MAAMC,QAAQ,GAAGD,MAAjB;;MACA,IAAIC,QAAQ,CAACC,YAAT,KAA0BH,SAA9B,EAAyC;QACrCF,MAAM,GAAGI,QAAQ,CAACC,YAAT,CAAsBC,YAAtB,CAAmC,oBAAnC,EAAyD;UAC9DC,UAAU,EAAGC,CAAD,IAAOA;QAD2C,CAAzD,CAAT;MAGH;IACJ;EACJ;;EACD,OAAOR,MAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASS,qBAAT,CAA+BC,IAA/B,EAAqC;EACjC,OAAOT,SAAS,IAAIM,UAAb,CAAwBG,IAAxB,KAAiCA,IAAxC;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,2BAAT,CAAqCC,QAArC,EAA+C;EAC3C,OAAOC,KAAK,CAAE,sCAAqCD,QAAS,GAAhD,CAAZ;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASE,6BAAT,GAAyC;EACrC,OAAOD,KAAK,CAAC,6EACT,wEADS,GAET,cAFQ,CAAZ;AAGH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASE,kCAAT,CAA4CC,GAA5C,EAAiD;EAC7C,OAAOH,KAAK,CAAE,wEAAD,GACR,kDAAiDG,GAAI,IAD9C,CAAZ;AAEH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASC,sCAAT,CAAgDC,OAAhD,EAAyD;EACrD,OAAOL,KAAK,CAAE,0EAAD,GACR,kDAAiDK,OAAQ,IADlD,CAAZ;AAEH;AACD;AACA;AACA;AACA;;;AACA,MAAMC,aAAN,CAAoB;EAChBC,WAAW,CAACJ,GAAD,EAAMK,OAAN,EAAeC,OAAf,EAAwB;IAC/B,KAAKN,GAAL,GAAWA,GAAX;IACA,KAAKK,OAAL,GAAeA,OAAf;IACA,KAAKC,OAAL,GAAeA,OAAf;EACH;;AALe;AAOpB;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,eAAN,CAAsB;EAClBH,WAAW,CAACI,WAAD,EAAcC,UAAd,EAA0BC,QAA1B,EAAoCC,aAApC,EAAmD;IAC1D,KAAKH,WAAL,GAAmBA,WAAnB;IACA,KAAKC,UAAL,GAAkBA,UAAlB;IACA,KAAKE,aAAL,GAAqBA,aAArB;IACA;AACR;AACA;;IACQ,KAAKC,eAAL,GAAuB,IAAIC,GAAJ,EAAvB;IACA;AACR;AACA;AACA;;IACQ,KAAKC,eAAL,GAAuB,IAAID,GAAJ,EAAvB;IACA;;IACA,KAAKE,iBAAL,GAAyB,IAAIF,GAAJ,EAAzB;IACA;;IACA,KAAKG,qBAAL,GAA6B,IAAIH,GAAJ,EAA7B;IACA;;IACA,KAAKI,sBAAL,GAA8B,IAAIJ,GAAJ,EAA9B;IACA;;IACA,KAAKK,UAAL,GAAkB,EAAlB;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKC,oBAAL,GAA4B,CAAC,gBAAD,EAAmB,mBAAnB,CAA5B;IACA,KAAKC,SAAL,GAAiBV,QAAjB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIW,UAAU,CAACzB,QAAD,EAAWI,GAAX,EAAgBM,OAAhB,EAAyB;IAC/B,OAAO,KAAKgB,qBAAL,CAA2B,EAA3B,EAA+B1B,QAA/B,EAAyCI,GAAzC,EAA8CM,OAA9C,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIiB,iBAAiB,CAAC3B,QAAD,EAAWM,OAAX,EAAoBI,OAApB,EAA6B;IAC1C,OAAO,KAAKkB,4BAAL,CAAkC,EAAlC,EAAsC5B,QAAtC,EAAgDM,OAAhD,EAAyDI,OAAzD,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIgB,qBAAqB,CAACG,SAAD,EAAY7B,QAAZ,EAAsBI,GAAtB,EAA2BM,OAA3B,EAAoC;IACrD,OAAO,KAAKoB,iBAAL,CAAuBD,SAAvB,EAAkC7B,QAAlC,EAA4C,IAAIO,aAAJ,CAAkBH,GAAlB,EAAuB,IAAvB,EAA6BM,OAA7B,CAA5C,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIqB,kBAAkB,CAACC,QAAD,EAAW;IACzB,KAAKV,UAAL,CAAgBW,IAAhB,CAAqBD,QAArB;;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIJ,4BAA4B,CAACC,SAAD,EAAY7B,QAAZ,EAAsBM,OAAtB,EAA+BI,OAA/B,EAAwC;IAChE,MAAMwB,YAAY,GAAG,KAAKrB,UAAL,CAAgBsB,QAAhB,CAAyB/E,eAAe,CAACgF,IAAzC,EAA+C9B,OAA/C,CAArB,CADgE,CAEhE;;;IACA,IAAI,CAAC4B,YAAL,EAAmB;MACf,MAAM7B,sCAAsC,CAACC,OAAD,CAA5C;IACH,CAL+D,CAMhE;;;IACA,MAAM+B,cAAc,GAAGxC,qBAAqB,CAACqC,YAAD,CAA5C;IACA,OAAO,KAAKJ,iBAAL,CAAuBD,SAAvB,EAAkC7B,QAAlC,EAA4C,IAAIO,aAAJ,CAAkB,EAAlB,EAAsB8B,cAAtB,EAAsC3B,OAAtC,CAA5C,CAAP;EACH;EACD;AACJ;AACA;AACA;;;EACI4B,aAAa,CAAClC,GAAD,EAAMM,OAAN,EAAe;IACxB,OAAO,KAAK6B,wBAAL,CAA8B,EAA9B,EAAkCnC,GAAlC,EAAuCM,OAAvC,CAAP;EACH;EACD;AACJ;AACA;AACA;;;EACI8B,oBAAoB,CAAClC,OAAD,EAAUI,OAAV,EAAmB;IACnC,OAAO,KAAK+B,+BAAL,CAAqC,EAArC,EAAyCnC,OAAzC,EAAkDI,OAAlD,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACI6B,wBAAwB,CAACV,SAAD,EAAYzB,GAAZ,EAAiBM,OAAjB,EAA0B;IAC9C,OAAO,KAAKgC,oBAAL,CAA0Bb,SAA1B,EAAqC,IAAItB,aAAJ,CAAkBH,GAAlB,EAAuB,IAAvB,EAA6BM,OAA7B,CAArC,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACI+B,+BAA+B,CAACZ,SAAD,EAAYvB,OAAZ,EAAqBI,OAArB,EAA8B;IACzD,MAAMwB,YAAY,GAAG,KAAKrB,UAAL,CAAgBsB,QAAhB,CAAyB/E,eAAe,CAACgF,IAAzC,EAA+C9B,OAA/C,CAArB;;IACA,IAAI,CAAC4B,YAAL,EAAmB;MACf,MAAM7B,sCAAsC,CAACC,OAAD,CAA5C;IACH,CAJwD,CAKzD;;;IACA,MAAM+B,cAAc,GAAGxC,qBAAqB,CAACqC,YAAD,CAA5C;IACA,OAAO,KAAKQ,oBAAL,CAA0Bb,SAA1B,EAAqC,IAAItB,aAAJ,CAAkB,EAAlB,EAAsB8B,cAAtB,EAAsC3B,OAAtC,CAArC,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIiC,sBAAsB,CAACC,KAAD,EAAQC,UAAU,GAAGD,KAArB,EAA4B;IAC9C,KAAKvB,sBAAL,CAA4ByB,GAA5B,CAAgCF,KAAhC,EAAuCC,UAAvC;;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIE,qBAAqB,CAACH,KAAD,EAAQ;IACzB,OAAO,KAAKvB,sBAAL,CAA4B2B,GAA5B,CAAgCJ,KAAhC,KAA0CA,KAAjD;EACH;EACD;AACJ;AACA;AACA;;;EACIK,sBAAsB,CAAC,GAAGJ,UAAJ,EAAgB;IAClC,KAAKtB,oBAAL,GAA4BsB,UAA5B;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIK,sBAAsB,GAAG;IACrB,OAAO,KAAK3B,oBAAZ;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACI4B,iBAAiB,CAACC,OAAD,EAAU;IACvB,MAAMhD,GAAG,GAAG,KAAKS,UAAL,CAAgBsB,QAAhB,CAAyB/E,eAAe,CAACiG,YAAzC,EAAuDD,OAAvD,CAAZ;;IACA,IAAI,CAAChD,GAAL,EAAU;MACN,MAAMD,kCAAkC,CAACiD,OAAD,CAAxC;IACH;;IACD,MAAME,UAAU,GAAG,KAAKnC,iBAAL,CAAuB6B,GAAvB,CAA2B5C,GAA3B,CAAnB;;IACA,IAAIkD,UAAJ,EAAgB;MACZ,OAAOhF,EAAE,CAACiF,QAAQ,CAACD,UAAD,CAAT,CAAT;IACH;;IACD,OAAO,KAAKE,sBAAL,CAA4B,IAAIjD,aAAJ,CAAkB6C,OAAlB,EAA2B,IAA3B,CAA5B,EAA8DK,IAA9D,CAAmE/E,GAAG,CAACgF,GAAG,IAAI,KAAKvC,iBAAL,CAAuB2B,GAAvB,CAA2B1C,GAA3B,EAAgCsD,GAAhC,CAAR,CAAtE,EAAqH/E,GAAG,CAAC+E,GAAG,IAAIH,QAAQ,CAACG,GAAD,CAAhB,CAAxH,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIC,eAAe,CAACC,IAAD,EAAO/B,SAAS,GAAG,EAAnB,EAAuB;IAClC,MAAMgC,GAAG,GAAGC,OAAO,CAACjC,SAAD,EAAY+B,IAAZ,CAAnB;;IACA,IAAIG,MAAM,GAAG,KAAK/C,eAAL,CAAqBgC,GAArB,CAAyBa,GAAzB,CAAb,CAFkC,CAGlC;;;IACA,IAAIE,MAAJ,EAAY;MACR,OAAO,KAAKC,iBAAL,CAAuBD,MAAvB,CAAP;IACH,CANiC,CAOlC;;;IACAA,MAAM,GAAG,KAAKE,2BAAL,CAAiCpC,SAAjC,EAA4C+B,IAA5C,CAAT;;IACA,IAAIG,MAAJ,EAAY;MACR,KAAK/C,eAAL,CAAqB8B,GAArB,CAAyBe,GAAzB,EAA8BE,MAA9B;;MACA,OAAO,KAAKC,iBAAL,CAAuBD,MAAvB,CAAP;IACH,CAZiC,CAalC;;;IACA,MAAMG,cAAc,GAAG,KAAKhD,eAAL,CAAqB8B,GAArB,CAAyBnB,SAAzB,CAAvB;;IACA,IAAIqC,cAAJ,EAAoB;MAChB,OAAO,KAAKC,yBAAL,CAA+BP,IAA/B,EAAqCM,cAArC,CAAP;IACH;;IACD,OAAO3F,UAAU,CAACwB,2BAA2B,CAAC8D,GAAD,CAA5B,CAAjB;EACH;;EACDO,WAAW,GAAG;IACV,KAAK9C,UAAL,GAAkB,EAAlB;;IACA,KAAKN,eAAL,CAAqBqD,KAArB;;IACA,KAAKnD,eAAL,CAAqBmD,KAArB;;IACA,KAAKlD,iBAAL,CAAuBkD,KAAvB;EACH;EACD;AACJ;AACA;;;EACIL,iBAAiB,CAACD,MAAD,EAAS;IACtB,IAAIA,MAAM,CAACtD,OAAX,EAAoB;MAChB;MACA,OAAOnC,EAAE,CAACiF,QAAQ,CAAC,KAAKe,qBAAL,CAA2BP,MAA3B,CAAD,CAAT,CAAT;IACH,CAHD,MAIK;MACD;MACA,OAAO,KAAKP,sBAAL,CAA4BO,MAA5B,EAAoCN,IAApC,CAAyC9E,GAAG,CAAC+E,GAAG,IAAIH,QAAQ,CAACG,GAAD,CAAhB,CAA5C,CAAP;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIS,yBAAyB,CAACP,IAAD,EAAOM,cAAP,EAAuB;IAC5C;IACA;IACA,MAAMK,SAAS,GAAG,KAAKC,8BAAL,CAAoCZ,IAApC,EAA0CM,cAA1C,CAAlB;;IACA,IAAIK,SAAJ,EAAe;MACX;MACA;MACA;MACA,OAAOjG,EAAE,CAACiG,SAAD,CAAT;IACH,CAT2C,CAU5C;IACA;;;IACA,MAAME,oBAAoB,GAAGP,cAAc,CACtCQ,MADwB,CACjBC,aAAa,IAAI,CAACA,aAAa,CAAClE,OADf,EAExB9B,GAFwB,CAEpBgG,aAAa,IAAI;MACtB,OAAO,KAAKC,yBAAL,CAA+BD,aAA/B,EAA8ClB,IAA9C,CAAmD7E,UAAU,CAAEiG,GAAD,IAAS;QAC1E,MAAMzE,GAAG,GAAG,KAAKS,UAAL,CAAgBsB,QAAhB,CAAyB/E,eAAe,CAACiG,YAAzC,EAAuDsB,aAAa,CAACvE,GAArE,CAAZ,CAD0E,CAE1E;QACA;;;QACA,MAAM0E,YAAY,GAAI,yBAAwB1E,GAAI,YAAWyE,GAAG,CAACE,OAAQ,EAAzE;;QACA,KAAKhE,aAAL,CAAmBiE,WAAnB,CAA+B,IAAI/E,KAAJ,CAAU6E,YAAV,CAA/B;;QACA,OAAOxG,EAAE,CAAC,IAAD,CAAT;MACH,CAPmE,CAA7D,CAAP;IAQH,CAX4B,CAA7B,CAZ4C,CAwB5C;IACA;;IACA,OAAOE,QAAQ,CAACiG,oBAAD,CAAR,CAA+BhB,IAA/B,CAAoC9E,GAAG,CAAC,MAAM;MACjD,MAAMsG,SAAS,GAAG,KAAKT,8BAAL,CAAoCZ,IAApC,EAA0CM,cAA1C,CAAlB,CADiD,CAEjD;;;MACA,IAAI,CAACe,SAAL,EAAgB;QACZ,MAAMlF,2BAA2B,CAAC6D,IAAD,CAAjC;MACH;;MACD,OAAOqB,SAAP;IACH,CAP6C,CAAvC,CAAP;EAQH;EACD;AACJ;AACA;AACA;AACA;;;EACIT,8BAA8B,CAACxE,QAAD,EAAWkE,cAAX,EAA2B;IACrD;IACA,KAAK,IAAIgB,CAAC,GAAGhB,cAAc,CAACiB,MAAf,GAAwB,CAArC,EAAwCD,CAAC,IAAI,CAA7C,EAAgDA,CAAC,EAAjD,EAAqD;MACjD,MAAMnB,MAAM,GAAGG,cAAc,CAACgB,CAAD,CAA7B,CADiD,CAEjD;MACA;MACA;MACA;;MACA,IAAInB,MAAM,CAACtD,OAAP,IAAkBsD,MAAM,CAACtD,OAAP,CAAe2E,QAAf,GAA0BC,OAA1B,CAAkCrF,QAAlC,IAA8C,CAAC,CAArE,EAAwE;QACpE,MAAM0D,GAAG,GAAG,KAAKY,qBAAL,CAA2BP,MAA3B,CAAZ;;QACA,MAAMkB,SAAS,GAAG,KAAKK,sBAAL,CAA4B5B,GAA5B,EAAiC1D,QAAjC,EAA2C+D,MAAM,CAACrD,OAAlD,CAAlB;;QACA,IAAIuE,SAAJ,EAAe;UACX,OAAOA,SAAP;QACH;MACJ;IACJ;;IACD,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIzB,sBAAsB,CAACO,MAAD,EAAS;IAC3B,OAAO,KAAKwB,UAAL,CAAgBxB,MAAhB,EAAwBN,IAAxB,CAA6B/E,GAAG,CAAC+B,OAAO,IAAKsD,MAAM,CAACtD,OAAP,GAAiBA,OAA9B,CAAhC,EAAyE9B,GAAG,CAAC,MAAM,KAAK2F,qBAAL,CAA2BP,MAA3B,CAAP,CAA5E,CAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIa,yBAAyB,CAACb,MAAD,EAAS;IAC9B,IAAIA,MAAM,CAACtD,OAAX,EAAoB;MAChB,OAAOnC,EAAE,CAAC,IAAD,CAAT;IACH;;IACD,OAAO,KAAKiH,UAAL,CAAgBxB,MAAhB,EAAwBN,IAAxB,CAA6B/E,GAAG,CAAC+B,OAAO,IAAKsD,MAAM,CAACtD,OAAP,GAAiBA,OAA9B,CAAhC,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACI6E,sBAAsB,CAACE,OAAD,EAAUxF,QAAV,EAAoBU,OAApB,EAA6B;IAC/C;IACA;IACA,MAAM+E,UAAU,GAAGD,OAAO,CAACE,aAAR,CAAuB,QAAO1F,QAAS,IAAvC,CAAnB;;IACA,IAAI,CAACyF,UAAL,EAAiB;MACb,OAAO,IAAP;IACH,CAN8C,CAO/C;IACA;;;IACA,MAAME,WAAW,GAAGF,UAAU,CAACG,SAAX,CAAqB,IAArB,CAApB;IACAD,WAAW,CAACE,eAAZ,CAA4B,IAA5B,EAV+C,CAW/C;IACA;;IACA,IAAIF,WAAW,CAACG,QAAZ,CAAqBC,WAArB,OAAuC,KAA3C,EAAkD;MAC9C,OAAO,KAAKC,iBAAL,CAAuBL,WAAvB,EAAoCjF,OAApC,CAAP;IACH,CAf8C,CAgB/C;IACA;IACA;;;IACA,IAAIiF,WAAW,CAACG,QAAZ,CAAqBC,WAArB,OAAuC,QAA3C,EAAqD;MACjD,OAAO,KAAKC,iBAAL,CAAuB,KAAKC,aAAL,CAAmBN,WAAnB,CAAvB,EAAwDjF,OAAxD,CAAP;IACH,CArB8C,CAsB/C;IACA;IACA;IACA;IACA;;;IACA,MAAMgD,GAAG,GAAG,KAAKwC,qBAAL,CAA2BrG,qBAAqB,CAAC,aAAD,CAAhD,CAAZ,CA3B+C,CA4B/C;;;IACA6D,GAAG,CAACyC,WAAJ,CAAgBR,WAAhB;IACA,OAAO,KAAKK,iBAAL,CAAuBtC,GAAvB,EAA4BhD,OAA5B,CAAP;EACH;EACD;AACJ;AACA;;;EACIwF,qBAAqB,CAACE,GAAD,EAAM;IACvB,MAAMC,GAAG,GAAG,KAAK7E,SAAL,CAAe8E,aAAf,CAA6B,KAA7B,CAAZ;;IACAD,GAAG,CAACE,SAAJ,GAAgBH,GAAhB;IACA,MAAM1C,GAAG,GAAG2C,GAAG,CAACX,aAAJ,CAAkB,KAAlB,CAAZ,CAHuB,CAIvB;;IACA,IAAI,CAAChC,GAAL,EAAU;MACN,MAAMzD,KAAK,CAAC,qBAAD,CAAX;IACH;;IACD,OAAOyD,GAAP;EACH;EACD;AACJ;AACA;;;EACIuC,aAAa,CAACO,OAAD,EAAU;IACnB,MAAM9C,GAAG,GAAG,KAAKwC,qBAAL,CAA2BrG,qBAAqB,CAAC,aAAD,CAAhD,CAAZ;;IACA,MAAM4G,UAAU,GAAGD,OAAO,CAACC,UAA3B,CAFmB,CAGnB;;IACA,KAAK,IAAIvB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuB,UAAU,CAACtB,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;MACxC,MAAM;QAAEtB,IAAF;QAAQ8C;MAAR,IAAkBD,UAAU,CAACvB,CAAD,CAAlC;;MACA,IAAItB,IAAI,KAAK,IAAb,EAAmB;QACfF,GAAG,CAACiD,YAAJ,CAAiB/C,IAAjB,EAAuB8C,KAAvB;MACH;IACJ;;IACD,KAAK,IAAIxB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsB,OAAO,CAACI,UAAR,CAAmBzB,MAAvC,EAA+CD,CAAC,EAAhD,EAAoD;MAChD,IAAIsB,OAAO,CAACI,UAAR,CAAmB1B,CAAnB,EAAsB2B,QAAtB,KAAmC,KAAKrF,SAAL,CAAesF,YAAtD,EAAoE;QAChEpD,GAAG,CAACyC,WAAJ,CAAgBK,OAAO,CAACI,UAAR,CAAmB1B,CAAnB,EAAsBU,SAAtB,CAAgC,IAAhC,CAAhB;MACH;IACJ;;IACD,OAAOlC,GAAP;EACH;EACD;AACJ;AACA;;;EACIsC,iBAAiB,CAACtC,GAAD,EAAMhD,OAAN,EAAe;IAC5BgD,GAAG,CAACiD,YAAJ,CAAiB,KAAjB,EAAwB,EAAxB;IACAjD,GAAG,CAACiD,YAAJ,CAAiB,QAAjB,EAA2B,MAA3B;IACAjD,GAAG,CAACiD,YAAJ,CAAiB,OAAjB,EAA0B,MAA1B;IACAjD,GAAG,CAACiD,YAAJ,CAAiB,qBAAjB,EAAwC,eAAxC;IACAjD,GAAG,CAACiD,YAAJ,CAAiB,WAAjB,EAA8B,OAA9B,EAL4B,CAKY;;IACxC,IAAIjG,OAAO,IAAIA,OAAO,CAACqG,OAAvB,EAAgC;MAC5BrD,GAAG,CAACiD,YAAJ,CAAiB,SAAjB,EAA4BjG,OAAO,CAACqG,OAApC;IACH;;IACD,OAAOrD,GAAP;EACH;EACD;AACJ;AACA;AACA;;;EACI6B,UAAU,CAACyB,UAAD,EAAa;IACnB,MAAM;MAAE5G,GAAG,EAAEgD,OAAP;MAAgB1C;IAAhB,IAA4BsG,UAAlC;IACA,MAAMC,eAAe,GAAGvG,OAAO,EAAEuG,eAAT,IAA4B,KAApD;;IACA,IAAI,CAAC,KAAKrG,WAAV,EAAuB;MACnB,MAAMV,6BAA6B,EAAnC;IACH,CALkB,CAMnB;;;IACA,IAAIkD,OAAO,IAAI,IAAf,EAAqB;MACjB,MAAMnD,KAAK,CAAE,+BAA8BmD,OAAQ,IAAxC,CAAX;IACH;;IACD,MAAMhD,GAAG,GAAG,KAAKS,UAAL,CAAgBsB,QAAhB,CAAyB/E,eAAe,CAACiG,YAAzC,EAAuDD,OAAvD,CAAZ,CAVmB,CAWnB;;;IACA,IAAI,CAAChD,GAAL,EAAU;MACN,MAAMD,kCAAkC,CAACiD,OAAD,CAAxC;IACH,CAdkB,CAenB;IACA;IACA;;;IACA,MAAM8D,eAAe,GAAG,KAAK9F,qBAAL,CAA2B4B,GAA3B,CAA+B5C,GAA/B,CAAxB;;IACA,IAAI8G,eAAJ,EAAqB;MACjB,OAAOA,eAAP;IACH;;IACD,MAAMC,GAAG,GAAG,KAAKvG,WAAL,CAAiBoC,GAAjB,CAAqB5C,GAArB,EAA0B;MAAEgH,YAAY,EAAE,MAAhB;MAAwBH;IAAxB,CAA1B,EAAqExD,IAArE,CAA0E9E,GAAG,CAAC+E,GAAG,IAAI;MAC7F;MACA;MACA,OAAO7D,qBAAqB,CAAC6D,GAAD,CAA5B;IACH,CAJwF,CAA7E,EAIR7E,QAAQ,CAAC,MAAM,KAAKuC,qBAAL,CAA2BiG,MAA3B,CAAkCjH,GAAlC,CAAP,CAJA,EAIgDtB,KAAK,EAJrD,CAAZ;;IAKA,KAAKsC,qBAAL,CAA2B0B,GAA3B,CAA+B1C,GAA/B,EAAoC+G,GAApC;;IACA,OAAOA,GAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIrF,iBAAiB,CAACD,SAAD,EAAY7B,QAAZ,EAAsB+D,MAAtB,EAA8B;IAC3C,KAAK/C,eAAL,CAAqB8B,GAArB,CAAyBgB,OAAO,CAACjC,SAAD,EAAY7B,QAAZ,CAAhC,EAAuD+D,MAAvD;;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIrB,oBAAoB,CAACb,SAAD,EAAYkC,MAAZ,EAAoB;IACpC,MAAMuD,eAAe,GAAG,KAAKpG,eAAL,CAAqB8B,GAArB,CAAyBnB,SAAzB,CAAxB;;IACA,IAAIyF,eAAJ,EAAqB;MACjBA,eAAe,CAACrF,IAAhB,CAAqB8B,MAArB;IACH,CAFD,MAGK;MACD,KAAK7C,eAAL,CAAqB4B,GAArB,CAAyBjB,SAAzB,EAAoC,CAACkC,MAAD,CAApC;IACH;;IACD,OAAO,IAAP;EACH;EACD;;;EACAO,qBAAqB,CAACP,MAAD,EAAS;IAC1B,IAAI,CAACA,MAAM,CAACwD,UAAZ,EAAwB;MACpB,MAAM7D,GAAG,GAAG,KAAKwC,qBAAL,CAA2BnC,MAAM,CAACtD,OAAlC,CAAZ;;MACA,KAAKuF,iBAAL,CAAuBtC,GAAvB,EAA4BK,MAAM,CAACrD,OAAnC;;MACAqD,MAAM,CAACwD,UAAP,GAAoB7D,GAApB;IACH;;IACD,OAAOK,MAAM,CAACwD,UAAd;EACH;EACD;;;EACAtD,2BAA2B,CAACpC,SAAD,EAAY+B,IAAZ,EAAkB;IACzC,KAAK,IAAIsB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK5D,UAAL,CAAgB6D,MAApC,EAA4CD,CAAC,EAA7C,EAAiD;MAC7C,MAAMsC,MAAM,GAAG,KAAKlG,UAAL,CAAgB4D,CAAhB,EAAmBtB,IAAnB,EAAyB/B,SAAzB,CAAf;;MACA,IAAI2F,MAAJ,EAAY;QACR,OAAOC,oBAAoB,CAACD,MAAD,CAApB,GACD,IAAIjH,aAAJ,CAAkBiH,MAAM,CAACpH,GAAzB,EAA8B,IAA9B,EAAoCoH,MAAM,CAAC9G,OAA3C,CADC,GAED,IAAIH,aAAJ,CAAkBiH,MAAlB,EAA0B,IAA1B,CAFN;MAGH;IACJ;;IACD,OAAOlI,SAAP;EACH;;AA9diB;;AAgetBqB,eAAe,CAAC+G,IAAhB;EAAA,iBAA4G/G,eAA5G,EAAkGxD,EAAlG,UAA6I6B,EAAE,CAACC,UAAhJ,MAAkG9B,EAAlG,UAAuL+B,EAAE,CAACC,YAA1L,GAAkGhC,EAAlG,UAAmNkB,QAAnN,MAAkGlB,EAAlG,UAAwPA,EAAE,CAACM,YAA3P;AAAA;;AACAkD,eAAe,CAACgH,KAAhB,kBADkGxK,EAClG;EAAA,OAAgHwD,eAAhH;EAAA,SAAgHA,eAAhH;EAAA,YAA6I;AAA7I;;AACA;EAAA,mDAFkGxD,EAElG,mBAA2FwD,eAA3F,EAAwH,CAAC;IAC7GiH,IAAI,EAAEvK,UADuG;IAE7GwK,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAFuG,CAAD,CAAxH,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAE5I,EAAE,CAACC,UAAX;MAAuB8I,UAAU,EAAE,CAAC;QAClEH,IAAI,EAAEtK;MAD4D,CAAD;IAAnC,CAAD,EAE3B;MAAEsK,IAAI,EAAE1I,EAAE,CAACC;IAAX,CAF2B,EAEA;MAAEyI,IAAI,EAAEtI,SAAR;MAAmByI,UAAU,EAAE,CAAC;QAC7DH,IAAI,EAAEtK;MADuD,CAAD,EAE7D;QACCsK,IAAI,EAAErK,MADP;QAECsK,IAAI,EAAE,CAACxJ,QAAD;MAFP,CAF6D;IAA/B,CAFA,EAO3B;MAAEuJ,IAAI,EAAEzK,EAAE,CAACM;IAAX,CAP2B,CAAP;EAOS,CAVnD;AAAA;AAWA;;;AACA,SAASuK,8BAAT,CAAwCC,cAAxC,EAAwDC,UAAxD,EAAoEC,SAApE,EAA+EC,YAA/E,EAA6FtH,QAA7F,EAAuG;EACnG,OAAOmH,cAAc,IAAI,IAAItH,eAAJ,CAAoBuH,UAApB,EAAgCC,SAAhC,EAA2CrH,QAA3C,EAAqDsH,YAArD,CAAzB;AACH;AACD;;;AACA,MAAMC,sBAAsB,GAAG;EAC3B;EACAC,OAAO,EAAE3H,eAFkB;EAG3B4H,IAAI,EAAE,CACF,CAAC,IAAIjL,QAAJ,EAAD,EAAiB,IAAIE,QAAJ,EAAjB,EAAiCmD,eAAjC,CADE,EAEF,CAAC,IAAIrD,QAAJ,EAAD,EAAiB2B,UAAjB,CAFE,EAGFE,YAHE,EAIF1B,YAJE,EAKF,CAAC,IAAIH,QAAJ,EAAD,EAAiBe,QAAjB,CALE,CAHqB;EAU3BmK,UAAU,EAAER;AAVe,CAA/B;AAYA;;AACA,SAASzE,QAAT,CAAkBG,GAAlB,EAAuB;EACnB,OAAOA,GAAG,CAACkC,SAAJ,CAAc,IAAd,CAAP;AACH;AACD;;;AACA,SAAS9B,OAAT,CAAiBjC,SAAjB,EAA4B+B,IAA5B,EAAkC;EAC9B,OAAO/B,SAAS,GAAG,GAAZ,GAAkB+B,IAAzB;AACH;;AACD,SAAS6D,oBAAT,CAA8Bf,KAA9B,EAAqC;EACjC,OAAO,CAAC,EAAEA,KAAK,CAACtG,GAAN,IAAasG,KAAK,CAAChG,OAArB,CAAR;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAM+H,YAAY,GAAGvK,UAAU,CAAC,MAAM;EAClCsC,WAAW,CAACkI,WAAD,EAAc;IACrB,KAAKA,WAAL,GAAmBA,WAAnB;EACH;;AAHiC,CAAP,CAA/B;AAKA;;;AACA,MAAMC,wBAAwB,GAAG,IAAIjL,cAAJ,CAAmB,0BAAnB,CAAjC;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMkL,iBAAiB,GAAG,IAAIlL,cAAJ,CAAmB,mBAAnB,EAAwC;EAC9DoK,UAAU,EAAE,MADkD;EAE9De,OAAO,EAAEC;AAFqD,CAAxC,CAA1B;AAIA;;AACA,SAASA,yBAAT,GAAqC;EACjC,MAAMtH,SAAS,GAAG7D,MAAM,CAACU,QAAD,CAAxB;;EACA,MAAM0K,SAAS,GAAGvH,SAAS,GAAGA,SAAS,CAACwH,QAAb,GAAwB,IAAnD;;EACA,OAAO;IACH;IACA;IACAC,WAAW,EAAE,MAAOF,SAAS,GAAGA,SAAS,CAACG,QAAV,GAAqBH,SAAS,CAACI,MAAlC,GAA2C;EAHrE,CAAP;AAKH;AACD;;;AACA,MAAMC,iBAAiB,GAAG,CACtB,WADsB,EAEtB,eAFsB,EAGtB,KAHsB,EAItB,QAJsB,EAKtB,MALsB,EAMtB,QANsB,EAOtB,QAPsB,EAQtB,cARsB,EAStB,YATsB,EAUtB,YAVsB,EAWtB,MAXsB,EAYtB,QAZsB,CAA1B;AAcA;;AACA,MAAMC,wBAAwB,GAAGD,iBAAiB,CAACzK,GAAlB,CAAsB2K,IAAI,IAAK,IAAGA,IAAK,GAAvC,EAA2CC,IAA3C,CAAgD,IAAhD,CAAjC;AACA;;AACA,MAAMC,cAAc,GAAG,2BAAvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,OAAN,SAAsBhB,YAAtB,CAAmC;EAC/BjI,WAAW,CAACkJ,UAAD,EAAaC,aAAb,EAA4BC,UAA5B,EAAwCb,SAAxC,EAAmDhI,aAAnD,EAAkE8I,QAAlE,EAA4E;IACnF,MAAMH,UAAN;IACA,KAAKC,aAAL,GAAqBA,aAArB;IACA,KAAKZ,SAAL,GAAiBA,SAAjB;IACA,KAAKhI,aAAL,GAAqBA,aAArB;IACA,KAAK+I,OAAL,GAAe,KAAf;IACA,KAAKC,qBAAL,GAA6B,EAA7B;IACA;;IACA,KAAKC,iBAAL,GAAyBvL,YAAY,CAACwL,KAAtC;;IACA,IAAIJ,QAAJ,EAAc;MACV,IAAIA,QAAQ,CAACK,KAAb,EAAoB;QAChB,KAAKA,KAAL,GAAa,KAAKC,YAAL,GAAoBN,QAAQ,CAACK,KAA1C;MACH;;MACD,IAAIL,QAAQ,CAACO,OAAb,EAAsB;QAClB,KAAKA,OAAL,GAAeP,QAAQ,CAACO,OAAxB;MACH;IACJ,CAhBkF,CAiBnF;IACA;;;IACA,IAAI,CAACR,UAAL,EAAiB;MACbF,UAAU,CAACW,aAAX,CAAyB1D,YAAzB,CAAsC,aAAtC,EAAqD,MAArD;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACc,IAAN2D,MAAM,GAAG;IACT,OAAO,KAAKR,OAAZ;EACH;;EACS,IAANQ,MAAM,CAACA,MAAD,EAAS;IACf,KAAKR,OAAL,GAAe1L,qBAAqB,CAACkM,MAAD,CAApC;EACH;EACD;;;EACW,IAAPC,OAAO,GAAG;IACV,OAAO,KAAKC,QAAZ;EACH;;EACU,IAAPD,OAAO,CAAC7D,KAAD,EAAQ;IACf,IAAIA,KAAK,KAAK,KAAK8D,QAAnB,EAA6B;MACzB,IAAI9D,KAAJ,EAAW;QACP,KAAK+D,cAAL,CAAoB/D,KAApB;MACH,CAFD,MAGK,IAAI,KAAK8D,QAAT,EAAmB;QACpB,KAAKE,gBAAL;MACH;;MACD,KAAKF,QAAL,GAAgB9D,KAAhB;IACH;EACJ;EACD;;;EACW,IAAP0D,OAAO,GAAG;IACV,OAAO,KAAKO,QAAZ;EACH;;EACU,IAAPP,OAAO,CAAC1D,KAAD,EAAQ;IACf,MAAMkE,QAAQ,GAAG,KAAKC,iBAAL,CAAuBnE,KAAvB,CAAjB;;IACA,IAAIkE,QAAQ,KAAK,KAAKD,QAAtB,EAAgC;MAC5B,KAAKA,QAAL,GAAgBC,QAAhB;;MACA,KAAKE,sBAAL;IACH;EACJ;EACD;;;EACY,IAARC,QAAQ,GAAG;IACX,OAAO,KAAKC,SAAZ;EACH;;EACW,IAARD,QAAQ,CAACrE,KAAD,EAAQ;IAChB,MAAMkE,QAAQ,GAAG,KAAKC,iBAAL,CAAuBnE,KAAvB,CAAjB;;IACA,IAAIkE,QAAQ,KAAK,KAAKI,SAAtB,EAAiC;MAC7B,KAAKA,SAAL,GAAiBJ,QAAjB;;MACA,KAAKE,sBAAL;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIG,cAAc,CAACjL,QAAD,EAAW;IACrB,IAAI,CAACA,QAAL,EAAe;MACX,OAAO,CAAC,EAAD,EAAK,EAAL,CAAP;IACH;;IACD,MAAMkL,KAAK,GAAGlL,QAAQ,CAACmL,KAAT,CAAe,GAAf,CAAd;;IACA,QAAQD,KAAK,CAAC/F,MAAd;MACI,KAAK,CAAL;QACI,OAAO,CAAC,EAAD,EAAK+F,KAAK,CAAC,CAAD,CAAV,CAAP;MAAuB;;MAC3B,KAAK,CAAL;QACI,OAAOA,KAAP;;MACJ;QACI,MAAMjL,KAAK,CAAE,uBAAsBD,QAAS,GAAjC,CAAX;MAAiD;IANzD;EAQH;;EACDoL,QAAQ,GAAG;IACP;IACA;IACA,KAAKN,sBAAL;EACH;;EACDO,kBAAkB,GAAG;IACjB,MAAMC,cAAc,GAAG,KAAKC,+BAA5B;;IACA,IAAID,cAAc,IAAIA,cAAc,CAACE,IAArC,EAA2C;MACvC,MAAMC,OAAO,GAAG,KAAK1C,SAAL,CAAeE,WAAf,EAAhB,CADuC,CAEvC;MACA;MACA;MACA;MACA;MACA;;;MACA,IAAIwC,OAAO,KAAK,KAAKC,aAArB,EAAoC;QAChC,KAAKA,aAAL,GAAqBD,OAArB;;QACA,KAAKE,wBAAL,CAA8BF,OAA9B;MACH;IACJ;EACJ;;EACDrH,WAAW,GAAG;IACV,KAAK4F,iBAAL,CAAuB4B,WAAvB;;IACA,IAAI,KAAKL,+BAAT,EAA0C;MACtC,KAAKA,+BAAL,CAAqClH,KAArC;IACH;EACJ;;EACDwH,cAAc,GAAG;IACb,OAAO,CAAC,KAAKtB,OAAb;EACH;;EACDuB,cAAc,CAACpI,GAAD,EAAM;IAChB,KAAKgH,gBAAL,GADgB,CAEhB;IACA;;;IACA,MAAMqB,IAAI,GAAG,KAAKhD,SAAL,CAAeE,WAAf,EAAb;;IACA,KAAKyC,aAAL,GAAqBK,IAArB;;IACA,KAAKC,oCAAL,CAA0CtI,GAA1C;;IACA,KAAKiI,wBAAL,CAA8BI,IAA9B;;IACA,KAAKrD,WAAL,CAAiB2B,aAAjB,CAA+BlE,WAA/B,CAA2CzC,GAA3C;EACH;;EACDgH,gBAAgB,GAAG;IACf,MAAMuB,aAAa,GAAG,KAAKvD,WAAL,CAAiB2B,aAAvC;IACA,IAAI6B,UAAU,GAAGD,aAAa,CAACrF,UAAd,CAAyBzB,MAA1C;;IACA,IAAI,KAAKoG,+BAAT,EAA0C;MACtC,KAAKA,+BAAL,CAAqClH,KAArC;IACH,CALc,CAMf;IACA;;;IACA,OAAO6H,UAAU,EAAjB,EAAqB;MACjB,MAAMC,KAAK,GAAGF,aAAa,CAACrF,UAAd,CAAyBsF,UAAzB,CAAd,CADiB,CAEjB;MACA;;MACA,IAAIC,KAAK,CAACtF,QAAN,KAAmB,CAAnB,IAAwBsF,KAAK,CAACrG,QAAN,CAAeC,WAAf,OAAiC,KAA7D,EAAoE;QAChEoG,KAAK,CAACC,MAAN;MACH;IACJ;EACJ;;EACDtB,sBAAsB,GAAG;IACrB,IAAI,CAAC,KAAKe,cAAL,EAAL,EAA4B;MACxB;IACH;;IACD,MAAMQ,IAAI,GAAG,KAAK3D,WAAL,CAAiB2B,aAA9B;IACA,MAAMiC,cAAc,GAAG,CAAC,KAAKlC,OAAL,GAClB,KAAKT,aAAL,CAAmB5G,qBAAnB,CAAyC,KAAKqH,OAA9C,EAAuDe,KAAvD,CAA6D,IAA7D,CADkB,GAElB,KAAKxB,aAAL,CAAmBzG,sBAAnB,EAFiB,EAE4BwB,MAF5B,CAEmC6H,SAAS,IAAIA,SAAS,CAACpH,MAAV,GAAmB,CAFnE,CAAvB;;IAGA,KAAK4E,qBAAL,CAA2ByC,OAA3B,CAAmCD,SAAS,IAAIF,IAAI,CAACI,SAAL,CAAeL,MAAf,CAAsBG,SAAtB,CAAhD;;IACAD,cAAc,CAACE,OAAf,CAAuBD,SAAS,IAAIF,IAAI,CAACI,SAAL,CAAeC,GAAf,CAAmBH,SAAnB,CAApC;IACA,KAAKxC,qBAAL,GAA6BuC,cAA7B;;IACA,IAAI,KAAKvB,QAAL,KAAkB,KAAK4B,sBAAvB,IACA,CAACL,cAAc,CAACM,QAAf,CAAwB,mBAAxB,CADL,EACmD;MAC/C,IAAI,KAAKD,sBAAT,EAAiC;QAC7BN,IAAI,CAACI,SAAL,CAAeL,MAAf,CAAsB,KAAKO,sBAA3B;MACH;;MACD,IAAI,KAAK5B,QAAT,EAAmB;QACfsB,IAAI,CAACI,SAAL,CAAeC,GAAf,CAAmB,KAAK3B,QAAxB;MACH;;MACD,KAAK4B,sBAAL,GAA8B,KAAK5B,QAAnC;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;;;EACIF,iBAAiB,CAACnE,KAAD,EAAQ;IACrB,OAAO,OAAOA,KAAP,KAAiB,QAAjB,GAA4BA,KAAK,CAACmG,IAAN,GAAa1B,KAAb,CAAmB,GAAnB,EAAwB,CAAxB,CAA5B,GAAyDzE,KAAhE;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIiF,wBAAwB,CAACI,IAAD,EAAO;IAC3B,MAAMe,QAAQ,GAAG,KAAKvB,+BAAtB;;IACA,IAAIuB,QAAJ,EAAc;MACVA,QAAQ,CAACN,OAAT,CAAiB,CAACO,KAAD,EAAQvG,OAAR,KAAoB;QACjCuG,KAAK,CAACP,OAAN,CAAclD,IAAI,IAAI;UAClB9C,OAAO,CAACG,YAAR,CAAqB2C,IAAI,CAAC1F,IAA1B,EAAiC,QAAOmI,IAAK,IAAGzC,IAAI,CAAC5C,KAAM,IAA3D;QACH,CAFD;MAGH,CAJD;IAKH;EACJ;EACD;AACJ;AACA;AACA;;;EACIsF,oCAAoC,CAACxF,OAAD,EAAU;IAC1C,MAAMwG,mBAAmB,GAAGxG,OAAO,CAACyG,gBAAR,CAAyB5D,wBAAzB,CAA5B;IACA,MAAMyD,QAAQ,GAAI,KAAKvB,+BAAL,GACd,KAAKA,+BAAL,IAAwC,IAAItK,GAAJ,EAD5C;;IAEA,KAAK,IAAIiE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8H,mBAAmB,CAAC7H,MAAxC,EAAgDD,CAAC,EAAjD,EAAqD;MACjDkE,iBAAiB,CAACoD,OAAlB,CAA0BlD,IAAI,IAAI;QAC9B,MAAM4D,oBAAoB,GAAGF,mBAAmB,CAAC9H,CAAD,CAAhD;QACA,MAAMwB,KAAK,GAAGwG,oBAAoB,CAACC,YAArB,CAAkC7D,IAAlC,CAAd;QACA,MAAM8D,KAAK,GAAG1G,KAAK,GAAGA,KAAK,CAAC0G,KAAN,CAAY5D,cAAZ,CAAH,GAAiC,IAApD;;QACA,IAAI4D,KAAJ,EAAW;UACP,IAAI3G,UAAU,GAAGqG,QAAQ,CAAC9J,GAAT,CAAakK,oBAAb,CAAjB;;UACA,IAAI,CAACzG,UAAL,EAAiB;YACbA,UAAU,GAAG,EAAb;YACAqG,QAAQ,CAAChK,GAAT,CAAaoK,oBAAb,EAAmCzG,UAAnC;UACH;;UACDA,UAAU,CAACxE,IAAX,CAAgB;YAAE2B,IAAI,EAAE0F,IAAR;YAAc5C,KAAK,EAAE0G,KAAK,CAAC,CAAD;UAA1B,CAAhB;QACH;MACJ,CAZD;IAaH;EACJ;EACD;;;EACA3C,cAAc,CAAC4C,OAAD,EAAU;IACpB,KAAKC,aAAL,GAAqB,IAArB;IACA,KAAKC,QAAL,GAAgB,IAAhB;;IACA,KAAKvD,iBAAL,CAAuB4B,WAAvB;;IACA,IAAIyB,OAAJ,EAAa;MACT,MAAM,CAACxL,SAAD,EAAY7B,QAAZ,IAAwB,KAAKiL,cAAL,CAAoBoC,OAApB,CAA9B;;MACA,IAAIxL,SAAJ,EAAe;QACX,KAAKyL,aAAL,GAAqBzL,SAArB;MACH;;MACD,IAAI7B,QAAJ,EAAc;QACV,KAAKuN,QAAL,GAAgBvN,QAAhB;MACH;;MACD,KAAKgK,iBAAL,GAAyB,KAAKL,aAAL,CACpBhG,eADoB,CACJ3D,QADI,EACM6B,SADN,EAEpB4B,IAFoB,CAEf1E,IAAI,CAAC,CAAD,CAFW,EAGpByO,SAHoB,CAGV9J,GAAG,IAAI,KAAKoI,cAAL,CAAoBpI,GAApB,CAHG,EAGwBmB,GAAD,IAAS;QACrD,MAAMC,YAAY,GAAI,yBAAwBjD,SAAU,IAAG7B,QAAS,KAAI6E,GAAG,CAACE,OAAQ,EAApF;;QACA,KAAKhE,aAAL,CAAmBiE,WAAnB,CAA+B,IAAI/E,KAAJ,CAAU6E,YAAV,CAA/B;MACH,CANwB,CAAzB;IAOH;EACJ;;AArP8B;;AAuPnC2E,OAAO,CAAC/B,IAAR;EAAA,iBAAoG+B,OAApG,EAvXkGtM,EAuXlG,mBAA6HA,EAAE,CAACsQ,UAAhI,GAvXkGtQ,EAuXlG,mBAAuJwD,eAAvJ,GAvXkGxD,EAuXlG,mBAAmL,aAAnL,GAvXkGA,EAuXlG,mBAA8NyL,iBAA9N,GAvXkGzL,EAuXlG,mBAA4PA,EAAE,CAACM,YAA/P,GAvXkGN,EAuXlG,mBAAwRwL,wBAAxR;AAAA;;AACAc,OAAO,CAACiE,IAAR,kBAxXkGvQ,EAwXlG;EAAA,MAAwFsM,OAAxF;EAAA;EAAA,oBAAiQ,KAAjQ;EAAA;EAAA;IAAA;MAxXkGtM,EAwXlG;MAxXkGA,EAwXlG;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAxXkGA,EAwXlG;EAAA;EAAA;EAAA;EAAA;IAAA;MAxXkGA,EAwXlG;MAxXkGA,EAwXwpB,gBAA1vB;IAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDAzXkGA,EAyXlG,mBAA2FsM,OAA3F,EAAgH,CAAC;IACrG7B,IAAI,EAAEhK,SAD+F;IAErGiK,IAAI,EAAE,CAAC;MAAE8F,QAAQ,EAAE,2BAAZ;MAAyCC,QAAQ,EAAE,UAAnD;MAA+DC,QAAQ,EAAE,SAAzE;MAAoFC,MAAM,EAAE,CAAC,OAAD,CAA5F;MAAuGC,IAAI,EAAE;QACxG,QAAQ,KADgG;QAExG,SAAS,sBAF+F;QAGxG,6BAA6B,mCAH2E;QAIxG,6BAA6B,sBAJ2E;QAKxG,kCAAkC,0BALsE;QAMxG,mBAAmB,oCANqF;QAOxG,2BAA2B,QAP6E;QAQxG,6BAA6B;MAR2E,CAA7G;MASIC,aAAa,EAAEnQ,iBAAiB,CAACoQ,IATrC;MAS2CC,eAAe,EAAEpQ,uBAAuB,CAACqQ,MATpF;MAS4FC,MAAM,EAAE,CAAC,oxBAAD;IATpG,CAAD;EAF+F,CAAD,CAAhH,EAY4B,YAAY;IAAE,OAAO,CAAC;MAAExG,IAAI,EAAEzK,EAAE,CAACsQ;IAAX,CAAD,EAA0B;MAAE7F,IAAI,EAAEjH;IAAR,CAA1B,EAAqD;MAAEiH,IAAI,EAAEtI,SAAR;MAAmByI,UAAU,EAAE,CAAC;QAClHH,IAAI,EAAE7J,SAD4G;QAElH8J,IAAI,EAAE,CAAC,aAAD;MAF4G,CAAD;IAA/B,CAArD,EAG3B;MAAED,IAAI,EAAEtI,SAAR;MAAmByI,UAAU,EAAE,CAAC;QAClCH,IAAI,EAAErK,MAD4B;QAElCsK,IAAI,EAAE,CAACe,iBAAD;MAF4B,CAAD;IAA/B,CAH2B,EAM3B;MAAEhB,IAAI,EAAEzK,EAAE,CAACM;IAAX,CAN2B,EAMA;MAAEmK,IAAI,EAAEtI,SAAR;MAAmByI,UAAU,EAAE,CAAC;QAC7DH,IAAI,EAAEtK;MADuD,CAAD,EAE7D;QACCsK,IAAI,EAAErK,MADP;QAECsK,IAAI,EAAE,CAACc,wBAAD;MAFP,CAF6D;IAA/B,CANA,CAAP;EAWlB,CAvBxB,EAuB0C;IAAE2B,MAAM,EAAE,CAAC;MACrC1C,IAAI,EAAE5J;IAD+B,CAAD,CAAV;IAE1BuM,OAAO,EAAE,CAAC;MACV3C,IAAI,EAAE5J;IADI,CAAD,CAFiB;IAI1BoM,OAAO,EAAE,CAAC;MACVxC,IAAI,EAAE5J;IADI,CAAD,CAJiB;IAM1B+M,QAAQ,EAAE,CAAC;MACXnD,IAAI,EAAE5J;IADK,CAAD;EANgB,CAvB1C;AAAA;AAiCA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMqQ,aAAN,CAAoB;;AAEpBA,aAAa,CAAC3G,IAAd;EAAA,iBAA0G2G,aAA1G;AAAA;;AACAA,aAAa,CAACC,IAAd,kBApakGnR,EAoalG;EAAA,MAA2GkR,aAA3G;EAAA,eAAyI5E,OAAzI;EAAA,UAA6JtL,eAA7J;EAAA,UAAyLsL,OAAzL,EAAkMtL,eAAlM;AAAA;AACAkQ,aAAa,CAACE,IAAd,kBArakGpR,EAqalG;EAAA,UAAoIgB,eAApI,EAAqJA,eAArJ;AAAA;;AACA;EAAA,mDAtakGhB,EAsalG,mBAA2FkR,aAA3F,EAAsH,CAAC;IAC3GzG,IAAI,EAAE3J,QADqG;IAE3G4J,IAAI,EAAE,CAAC;MACC2G,OAAO,EAAE,CAACrQ,eAAD,CADV;MAECsQ,OAAO,EAAE,CAAChF,OAAD,EAAUtL,eAAV,CAFV;MAGCuQ,YAAY,EAAE,CAACjF,OAAD;IAHf,CAAD;EAFqG,CAAD,CAAtH;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASpB,sBAAT,EAAiCL,8BAAjC,EAAiEW,wBAAjE,EAA2FC,iBAA3F,EAA8GE,yBAA9G,EAAyIW,OAAzI,EAAkJ4E,aAAlJ,EAAiK1N,eAAjK,EAAkLN,sCAAlL,EAA0NF,kCAA1N,EAA8PJ,2BAA9P,EAA2RG,6BAA3R"}, "metadata": {}, "sourceType": "module"}