{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { MatCommonModule } from '@angular/material/core';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nclass MatDivider {\n  constructor() {\n    this._vertical = false;\n    this._inset = false;\n  }\n  /** Whether the divider is vertically aligned. */\n\n\n  get vertical() {\n    return this._vertical;\n  }\n\n  set vertical(value) {\n    this._vertical = coerceBooleanProperty(value);\n  }\n  /** Whether the divider is an inset divider. */\n\n\n  get inset() {\n    return this._inset;\n  }\n\n  set inset(value) {\n    this._inset = coerceBooleanProperty(value);\n  }\n\n}\n\nMatDivider.ɵfac = function MatDivider_Factory(t) {\n  return new (t || MatDivider)();\n};\n\nMatDivider.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatDivider,\n  selectors: [[\"mat-divider\"]],\n  hostAttrs: [\"role\", \"separator\", 1, \"mat-divider\"],\n  hostVars: 7,\n  hostBindings: function MatDivider_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-orientation\", ctx.vertical ? \"vertical\" : \"horizontal\");\n      i0.ɵɵclassProp(\"mat-divider-vertical\", ctx.vertical)(\"mat-divider-horizontal\", !ctx.vertical)(\"mat-divider-inset\", ctx.inset);\n    }\n  },\n  inputs: {\n    vertical: \"vertical\",\n    inset: \"inset\"\n  },\n  decls: 0,\n  vars: 0,\n  template: function MatDivider_Template(rf, ctx) {},\n  styles: [\".mat-divider{display:block;margin:0;border-top-width:1px;border-top-style:solid}.mat-divider.mat-divider-vertical{border-top:0;border-right-width:1px;border-right-style:solid}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDivider, [{\n    type: Component,\n    args: [{\n      selector: 'mat-divider',\n      host: {\n        'role': 'separator',\n        '[attr.aria-orientation]': 'vertical ? \"vertical\" : \"horizontal\"',\n        '[class.mat-divider-vertical]': 'vertical',\n        '[class.mat-divider-horizontal]': '!vertical',\n        '[class.mat-divider-inset]': 'inset',\n        'class': 'mat-divider'\n      },\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-divider{display:block;margin:0;border-top-width:1px;border-top-style:solid}.mat-divider.mat-divider-vertical{border-top:0;border-right-width:1px;border-right-style:solid}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\"]\n    }]\n  }], null, {\n    vertical: [{\n      type: Input\n    }],\n    inset: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatDividerModule {}\n\nMatDividerModule.ɵfac = function MatDividerModule_Factory(t) {\n  return new (t || MatDividerModule)();\n};\n\nMatDividerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatDividerModule,\n  declarations: [MatDivider],\n  imports: [MatCommonModule],\n  exports: [MatDivider, MatCommonModule]\n});\nMatDividerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule, MatCommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDividerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule],\n      exports: [MatDivider, MatCommonModule],\n      declarations: [MatDivider]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MatDivider, MatDividerModule };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "NgModule", "coerceBooleanProperty", "MatCommonModule", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "_vertical", "_inset", "vertical", "value", "inset", "ɵfac", "ɵcmp", "type", "args", "selector", "host", "template", "encapsulation", "None", "changeDetection", "OnPush", "styles", "MatDividerModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/material/fesm2020/divider.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { MatCommonModule } from '@angular/material/core';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatDivider {\n    constructor() {\n        this._vertical = false;\n        this._inset = false;\n    }\n    /** Whether the divider is vertically aligned. */\n    get vertical() {\n        return this._vertical;\n    }\n    set vertical(value) {\n        this._vertical = coerceBooleanProperty(value);\n    }\n    /** Whether the divider is an inset divider. */\n    get inset() {\n        return this._inset;\n    }\n    set inset(value) {\n        this._inset = coerceBooleanProperty(value);\n    }\n}\nMatDivider.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatDivider, deps: [], target: i0.ɵɵFactoryTarget.Component });\nMatDivider.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatDivider, selector: \"mat-divider\", inputs: { vertical: \"vertical\", inset: \"inset\" }, host: { attributes: { \"role\": \"separator\" }, properties: { \"attr.aria-orientation\": \"vertical ? \\\"vertical\\\" : \\\"horizontal\\\"\", \"class.mat-divider-vertical\": \"vertical\", \"class.mat-divider-horizontal\": \"!vertical\", \"class.mat-divider-inset\": \"inset\" }, classAttribute: \"mat-divider\" }, ngImport: i0, template: '', isInline: true, styles: [\".mat-divider{display:block;margin:0;border-top-width:1px;border-top-style:solid}.mat-divider.mat-divider-vertical{border-top:0;border-right-width:1px;border-right-style:solid}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatDivider, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-divider', host: {\n                        'role': 'separator',\n                        '[attr.aria-orientation]': 'vertical ? \"vertical\" : \"horizontal\"',\n                        '[class.mat-divider-vertical]': 'vertical',\n                        '[class.mat-divider-horizontal]': '!vertical',\n                        '[class.mat-divider-inset]': 'inset',\n                        'class': 'mat-divider',\n                    }, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-divider{display:block;margin:0;border-top-width:1px;border-top-style:solid}.mat-divider.mat-divider-vertical{border-top:0;border-right-width:1px;border-right-style:solid}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\"] }]\n        }], propDecorators: { vertical: [{\n                type: Input\n            }], inset: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatDividerModule {\n}\nMatDividerModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatDividerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatDividerModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: MatDividerModule, declarations: [MatDivider], imports: [MatCommonModule], exports: [MatDivider, MatCommonModule] });\nMatDividerModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatDividerModule, imports: [MatCommonModule, MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatDividerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule],\n                    exports: [MatDivider, MatCommonModule],\n                    declarations: [MatDivider],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatDivider, MatDividerModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,SAAT,EAAoBC,iBAApB,EAAuCC,uBAAvC,EAAgEC,KAAhE,EAAuEC,QAAvE,QAAuF,eAAvF;AACA,SAASC,qBAAT,QAAsC,uBAAtC;AACA,SAASC,eAAT,QAAgC,wBAAhC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,UAAN,CAAiB;EACbC,WAAW,GAAG;IACV,KAAKC,SAAL,GAAiB,KAAjB;IACA,KAAKC,MAAL,GAAc,KAAd;EACH;EACD;;;EACY,IAARC,QAAQ,GAAG;IACX,OAAO,KAAKF,SAAZ;EACH;;EACW,IAARE,QAAQ,CAACC,KAAD,EAAQ;IAChB,KAAKH,SAAL,GAAiBJ,qBAAqB,CAACO,KAAD,CAAtC;EACH;EACD;;;EACS,IAALC,KAAK,GAAG;IACR,OAAO,KAAKH,MAAZ;EACH;;EACQ,IAALG,KAAK,CAACD,KAAD,EAAQ;IACb,KAAKF,MAAL,GAAcL,qBAAqB,CAACO,KAAD,CAAnC;EACH;;AAlBY;;AAoBjBL,UAAU,CAACO,IAAX;EAAA,iBAAuGP,UAAvG;AAAA;;AACAA,UAAU,CAACQ,IAAX,kBAD6FhB,EAC7F;EAAA,MAA2FQ,UAA3F;EAAA;EAAA,oBAAgN,WAAhN;EAAA;EAAA;IAAA;MAD6FR,EAC7F;MAD6FA,EAC7F;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDAF6FA,EAE7F,mBAA2FQ,UAA3F,EAAmH,CAAC;IACxGS,IAAI,EAAEhB,SADkG;IAExGiB,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,aAAZ;MAA2BC,IAAI,EAAE;QAC5B,QAAQ,WADoB;QAE5B,2BAA2B,sCAFC;QAG5B,gCAAgC,UAHJ;QAI5B,kCAAkC,WAJN;QAK5B,6BAA6B,OALD;QAM5B,SAAS;MANmB,CAAjC;MAOIC,QAAQ,EAAE,EAPd;MAOkBC,aAAa,EAAEpB,iBAAiB,CAACqB,IAPnD;MAOyDC,eAAe,EAAErB,uBAAuB,CAACsB,MAPlG;MAO0GC,MAAM,EAAE,CAAC,6SAAD;IAPlH,CAAD;EAFkG,CAAD,CAAnH,QAU4B;IAAEd,QAAQ,EAAE,CAAC;MACzBK,IAAI,EAAEb;IADmB,CAAD,CAAZ;IAEZU,KAAK,EAAE,CAAC;MACRG,IAAI,EAAEb;IADE,CAAD;EAFK,CAV5B;AAAA;AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMuB,gBAAN,CAAuB;;AAEvBA,gBAAgB,CAACZ,IAAjB;EAAA,iBAA6GY,gBAA7G;AAAA;;AACAA,gBAAgB,CAACC,IAAjB,kBA5B6F5B,EA4B7F;EAAA,MAA8G2B,gBAA9G;EAAA,eAA+InB,UAA/I;EAAA,UAAsKD,eAAtK;EAAA,UAAkMC,UAAlM,EAA8MD,eAA9M;AAAA;AACAoB,gBAAgB,CAACE,IAAjB,kBA7B6F7B,EA6B7F;EAAA,UAA0IO,eAA1I,EAA2JA,eAA3J;AAAA;;AACA;EAAA,mDA9B6FP,EA8B7F,mBAA2F2B,gBAA3F,EAAyH,CAAC;IAC9GV,IAAI,EAAEZ,QADwG;IAE9Ga,IAAI,EAAE,CAAC;MACCY,OAAO,EAAE,CAACvB,eAAD,CADV;MAECwB,OAAO,EAAE,CAACvB,UAAD,EAAaD,eAAb,CAFV;MAGCyB,YAAY,EAAE,CAACxB,UAAD;IAHf,CAAD;EAFwG,CAAD,CAAzH;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASA,UAAT,EAAqBmB,gBAArB"}, "metadata": {}, "sourceType": "module"}