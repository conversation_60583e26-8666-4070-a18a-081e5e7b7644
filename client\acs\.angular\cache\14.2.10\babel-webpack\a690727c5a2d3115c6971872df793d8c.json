{"ast": null, "code": "import { QueueAction } from './QueueAction';\nimport { QueueScheduler } from './QueueScheduler';\nexport const queueScheduler = new QueueScheduler(QueueAction);\nexport const queue = queueScheduler;", "map": {"version": 3, "names": ["QueueAction", "QueueScheduler", "queueScheduler", "queue"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/scheduler/queue.js"], "sourcesContent": ["import { QueueAction } from './QueueAction';\nimport { QueueScheduler } from './QueueScheduler';\nexport const queueScheduler = new QueueScheduler(QueueAction);\nexport const queue = queueScheduler;\n"], "mappings": "AAAA,SAASA,WAAT,QAA4B,eAA5B;AACA,SAASC,cAAT,QAA+B,kBAA/B;AACA,OAAO,MAAMC,cAAc,GAAG,IAAID,cAAJ,CAAmBD,WAAnB,CAAvB;AACP,OAAO,MAAMG,KAAK,GAAGD,cAAd"}, "metadata": {}, "sourceType": "module"}