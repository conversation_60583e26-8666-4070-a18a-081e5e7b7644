{"ast": null, "code": "const TimeoutErrorImpl = (() => {\n  function TimeoutErrorImpl() {\n    Error.call(this);\n    this.message = 'Timeout has occurred';\n    this.name = 'TimeoutError';\n    return this;\n  }\n\n  TimeoutErrorImpl.prototype = Object.create(Error.prototype);\n  return TimeoutErrorImpl;\n})();\n\nexport const TimeoutError = TimeoutErrorImpl;", "map": {"version": 3, "names": ["TimeoutErrorImpl", "Error", "call", "message", "name", "prototype", "Object", "create", "TimeoutError"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/TimeoutError.js"], "sourcesContent": ["const TimeoutErrorImpl = (() => {\n    function TimeoutErrorImpl() {\n        Error.call(this);\n        this.message = 'Timeout has occurred';\n        this.name = 'TimeoutError';\n        return this;\n    }\n    TimeoutErrorImpl.prototype = Object.create(Error.prototype);\n    return TimeoutErrorImpl;\n})();\nexport const TimeoutError = TimeoutErrorImpl;\n"], "mappings": "AAAA,MAAMA,gBAAgB,GAAG,CAAC,MAAM;EAC5B,SAASA,gBAAT,GAA4B;IACxBC,KAAK,CAACC,IAAN,CAAW,IAAX;IACA,KAAKC,OAAL,GAAe,sBAAf;IACA,KAAKC,IAAL,GAAY,cAAZ;IACA,OAAO,IAAP;EACH;;EACDJ,gBAAgB,CAACK,SAAjB,GAA6BC,MAAM,CAACC,MAAP,CAAcN,KAAK,CAACI,SAApB,CAA7B;EACA,OAAOL,gBAAP;AACH,CATwB,GAAzB;;AAUA,OAAO,MAAMQ,YAAY,GAAGR,gBAArB"}, "metadata": {}, "sourceType": "module"}