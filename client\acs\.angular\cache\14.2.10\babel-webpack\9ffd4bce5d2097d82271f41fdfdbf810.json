{"ast": null, "code": "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "map": {"version": 3, "names": ["debounce", "fn", "pending", "Promise", "resolve", "then", "undefined"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/utils/debounce.js"], "sourcesContent": ["export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}"], "mappings": "AAAA,eAAe,SAASA,QAAT,CAAkBC,EAAlB,EAAsB;EACnC,IAAIC,OAAJ;EACA,OAAO,YAAY;IACjB,IAAI,CAACA,OAAL,EAAc;MACZA,OAAO,GAAG,IAAIC,OAAJ,CAAY,UAAUC,OAAV,EAAmB;QACvCD,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,YAAY;UACjCH,OAAO,GAAGI,SAAV;UACAF,OAAO,CAACH,EAAE,EAAH,CAAP;QACD,CAHD;MAID,CALS,CAAV;IAMD;;IAED,OAAOC,OAAP;EACD,CAXD;AAYD"}, "metadata": {}, "sourceType": "module"}