{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function distinctUntilChanged(compare, keySelector) {\n  return source => source.lift(new DistinctUntilChangedOperator(compare, keySelector));\n}\n\nclass DistinctUntilChangedOperator {\n  constructor(compare, keySelector) {\n    this.compare = compare;\n    this.keySelector = keySelector;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new DistinctUntilChangedSubscriber(subscriber, this.compare, this.keySelector));\n  }\n\n}\n\nclass DistinctUntilChangedSubscriber extends Subscriber {\n  constructor(destination, compare, keySelector) {\n    super(destination);\n    this.keySelector = keySelector;\n    this.hasKey = false;\n\n    if (typeof compare === 'function') {\n      this.compare = compare;\n    }\n  }\n\n  compare(x, y) {\n    return x === y;\n  }\n\n  _next(value) {\n    let key;\n\n    try {\n      const {\n        keySelector\n      } = this;\n      key = keySelector ? keySelector(value) : value;\n    } catch (err) {\n      return this.destination.error(err);\n    }\n\n    let result = false;\n\n    if (this.hasKey) {\n      try {\n        const {\n          compare\n        } = this;\n        result = compare(this.key, key);\n      } catch (err) {\n        return this.destination.error(err);\n      }\n    } else {\n      this.hasKey = true;\n    }\n\n    if (!result) {\n      this.key = key;\n      this.destination.next(value);\n    }\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "distinctUntilChanged", "compare", "keySelector", "source", "lift", "DistinctUntilChangedOperator", "constructor", "call", "subscriber", "subscribe", "DistinctUntilChangedSubscriber", "destination", "<PERSON><PERSON><PERSON>", "x", "y", "_next", "value", "key", "err", "error", "result", "next"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/distinctUntilChanged.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function distinctUntilChanged(compare, keySelector) {\n    return (source) => source.lift(new DistinctUntilChangedOperator(compare, keySelector));\n}\nclass DistinctUntilChangedOperator {\n    constructor(compare, keySelector) {\n        this.compare = compare;\n        this.keySelector = keySelector;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new DistinctUntilChangedSubscriber(subscriber, this.compare, this.keySelector));\n    }\n}\nclass DistinctUntilChangedSubscriber extends Subscriber {\n    constructor(destination, compare, keySelector) {\n        super(destination);\n        this.keySelector = keySelector;\n        this.hasKey = false;\n        if (typeof compare === 'function') {\n            this.compare = compare;\n        }\n    }\n    compare(x, y) {\n        return x === y;\n    }\n    _next(value) {\n        let key;\n        try {\n            const { keySelector } = this;\n            key = keySelector ? keySelector(value) : value;\n        }\n        catch (err) {\n            return this.destination.error(err);\n        }\n        let result = false;\n        if (this.hasKey) {\n            try {\n                const { compare } = this;\n                result = compare(this.key, key);\n            }\n            catch (err) {\n                return this.destination.error(err);\n            }\n        }\n        else {\n            this.has<PERSON>ey = true;\n        }\n        if (!result) {\n            this.key = key;\n            this.destination.next(value);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,oBAAT,CAA8BC,OAA9B,EAAuCC,WAAvC,EAAoD;EACvD,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,4BAAJ,CAAiCJ,OAAjC,EAA0CC,WAA1C,CAAZ,CAAnB;AACH;;AACD,MAAMG,4BAAN,CAAmC;EAC/BC,WAAW,CAACL,OAAD,EAAUC,WAAV,EAAuB;IAC9B,KAAKD,OAAL,GAAeA,OAAf;IACA,KAAKC,WAAL,GAAmBA,WAAnB;EACH;;EACDK,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,8BAAJ,CAAmCF,UAAnC,EAA+C,KAAKP,OAApD,EAA6D,KAAKC,WAAlE,CAAjB,CAAP;EACH;;AAP8B;;AASnC,MAAMQ,8BAAN,SAA6CX,UAA7C,CAAwD;EACpDO,WAAW,CAACK,WAAD,EAAcV,OAAd,EAAuBC,WAAvB,EAAoC;IAC3C,MAAMS,WAAN;IACA,KAAKT,WAAL,GAAmBA,WAAnB;IACA,KAAKU,MAAL,GAAc,KAAd;;IACA,IAAI,OAAOX,OAAP,KAAmB,UAAvB,EAAmC;MAC/B,KAAKA,OAAL,GAAeA,OAAf;IACH;EACJ;;EACDA,OAAO,CAACY,CAAD,EAAIC,CAAJ,EAAO;IACV,OAAOD,CAAC,KAAKC,CAAb;EACH;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACT,IAAIC,GAAJ;;IACA,IAAI;MACA,MAAM;QAAEf;MAAF,IAAkB,IAAxB;MACAe,GAAG,GAAGf,WAAW,GAAGA,WAAW,CAACc,KAAD,CAAd,GAAwBA,KAAzC;IACH,CAHD,CAIA,OAAOE,GAAP,EAAY;MACR,OAAO,KAAKP,WAAL,CAAiBQ,KAAjB,CAAuBD,GAAvB,CAAP;IACH;;IACD,IAAIE,MAAM,GAAG,KAAb;;IACA,IAAI,KAAKR,MAAT,EAAiB;MACb,IAAI;QACA,MAAM;UAAEX;QAAF,IAAc,IAApB;QACAmB,MAAM,GAAGnB,OAAO,CAAC,KAAKgB,GAAN,EAAWA,GAAX,CAAhB;MACH,CAHD,CAIA,OAAOC,GAAP,EAAY;QACR,OAAO,KAAKP,WAAL,CAAiBQ,KAAjB,CAAuBD,GAAvB,CAAP;MACH;IACJ,CARD,MASK;MACD,KAAKN,MAAL,GAAc,IAAd;IACH;;IACD,IAAI,CAACQ,MAAL,EAAa;MACT,KAAKH,GAAL,GAAWA,GAAX;MACA,KAAKN,WAAL,CAAiBU,IAAjB,CAAsBL,KAAtB;IACH;EACJ;;AAtCmD"}, "metadata": {}, "sourceType": "module"}