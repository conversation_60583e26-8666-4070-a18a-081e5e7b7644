{"ast": null, "code": "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getViewportRect(element, strategy) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    var layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || !layoutViewport && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "map": {"version": 3, "names": ["getWindow", "getDocumentElement", "getWindowScrollBarX", "isLayoutViewport", "getViewportRect", "element", "strategy", "win", "html", "visualViewport", "width", "clientWidth", "height", "clientHeight", "x", "y", "layoutViewport", "offsetLeft", "offsetTop"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js"], "sourcesContent": ["import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getViewportRect(element, strategy) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    var layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || !layoutViewport && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,gBAAtB;AACA,OAAOC,kBAAP,MAA+B,yBAA/B;AACA,OAAOC,mBAAP,MAAgC,0BAAhC;AACA,OAAOC,gBAAP,MAA6B,uBAA7B;AACA,eAAe,SAASC,eAAT,CAAyBC,OAAzB,EAAkCC,QAAlC,EAA4C;EACzD,IAAIC,GAAG,GAAGP,SAAS,CAACK,OAAD,CAAnB;EACA,IAAIG,IAAI,GAAGP,kBAAkB,CAACI,OAAD,CAA7B;EACA,IAAII,cAAc,GAAGF,GAAG,CAACE,cAAzB;EACA,IAAIC,KAAK,GAAGF,IAAI,CAACG,WAAjB;EACA,IAAIC,MAAM,GAAGJ,IAAI,CAACK,YAAlB;EACA,IAAIC,CAAC,GAAG,CAAR;EACA,IAAIC,CAAC,GAAG,CAAR;;EAEA,IAAIN,cAAJ,EAAoB;IAClBC,KAAK,GAAGD,cAAc,CAACC,KAAvB;IACAE,MAAM,GAAGH,cAAc,CAACG,MAAxB;IACA,IAAII,cAAc,GAAGb,gBAAgB,EAArC;;IAEA,IAAIa,cAAc,IAAI,CAACA,cAAD,IAAmBV,QAAQ,KAAK,OAAtD,EAA+D;MAC7DQ,CAAC,GAAGL,cAAc,CAACQ,UAAnB;MACAF,CAAC,GAAGN,cAAc,CAACS,SAAnB;IACD;EACF;;EAED,OAAO;IACLR,KAAK,EAAEA,KADF;IAELE,MAAM,EAAEA,MAFH;IAGLE,CAAC,EAAEA,CAAC,GAAGZ,mBAAmB,CAACG,OAAD,CAHrB;IAILU,CAAC,EAAEA;EAJE,CAAP;AAMD"}, "metadata": {}, "sourceType": "module"}