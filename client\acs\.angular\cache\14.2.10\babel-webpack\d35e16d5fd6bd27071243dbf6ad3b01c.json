{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, InjectionToken, EventEmitter, Directive, Optional, Input, Output, NgModule } from '@angular/core';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A pending copy-to-clipboard operation.\n *\n * The implementation of copying text to the clipboard modifies the DOM and\n * forces a re-layout. This re-layout can take too long if the string is large,\n * causing the execCommand('copy') to happen too long after the user clicked.\n * This results in the browser refusing to copy. This object lets the\n * re-layout happen in a separate tick from copying by providing a copy function\n * that can be called later.\n *\n * Destroy must be called when no longer in use, regardless of whether `copy` is\n * called.\n */\n\nclass PendingCopy {\n  constructor(text, _document) {\n    this._document = _document;\n\n    const textarea = this._textarea = this._document.createElement('textarea');\n\n    const styles = textarea.style; // Hide the element for display and accessibility. Set a fixed position so the page layout\n    // isn't affected. We use `fixed` with `top: 0`, because focus is moved into the textarea\n    // for a split second and if it's off-screen, some browsers will attempt to scroll it into view.\n\n    styles.position = 'fixed';\n    styles.top = styles.opacity = '0';\n    styles.left = '-999em';\n    textarea.setAttribute('aria-hidden', 'true');\n    textarea.value = text; // Making the textarea `readonly` prevents the screen from jumping on iOS Safari (see #25169).\n\n    textarea.readOnly = true;\n\n    this._document.body.appendChild(textarea);\n  }\n  /** Finishes copying the text. */\n\n\n  copy() {\n    const textarea = this._textarea;\n    let successful = false;\n\n    try {\n      // Older browsers could throw if copy is not supported.\n      if (textarea) {\n        const currentFocus = this._document.activeElement;\n        textarea.select();\n        textarea.setSelectionRange(0, textarea.value.length);\n        successful = this._document.execCommand('copy');\n\n        if (currentFocus) {\n          currentFocus.focus();\n        }\n      }\n    } catch {// Discard error.\n      // Initial setting of {@code successful} will represent failure here.\n    }\n\n    return successful;\n  }\n  /** Cleans up DOM changes used to perform the copy operation. */\n\n\n  destroy() {\n    const textarea = this._textarea;\n\n    if (textarea) {\n      textarea.remove();\n      this._textarea = undefined;\n    }\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A service for copying text to the clipboard.\n */\n\n\nclass Clipboard {\n  constructor(document) {\n    this._document = document;\n  }\n  /**\n   * Copies the provided text into the user's clipboard.\n   *\n   * @param text The string to copy.\n   * @returns Whether the operation was successful.\n   */\n\n\n  copy(text) {\n    const pendingCopy = this.beginCopy(text);\n    const successful = pendingCopy.copy();\n    pendingCopy.destroy();\n    return successful;\n  }\n  /**\n   * Prepares a string to be copied later. This is useful for large strings\n   * which take too long to successfully render and be copied in the same tick.\n   *\n   * The caller must call `destroy` on the returned `PendingCopy`.\n   *\n   * @param text The string to copy.\n   * @returns the pending copy operation.\n   */\n\n\n  beginCopy(text) {\n    return new PendingCopy(text, this._document);\n  }\n\n}\n\nClipboard.ɵfac = function Clipboard_Factory(t) {\n  return new (t || Clipboard)(i0.ɵɵinject(DOCUMENT));\n};\n\nClipboard.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Clipboard,\n  factory: Clipboard.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Clipboard, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injection token that can be used to provide the default options to `CdkCopyToClipboard`. */\n\n\nconst CDK_COPY_TO_CLIPBOARD_CONFIG = new InjectionToken('CDK_COPY_TO_CLIPBOARD_CONFIG');\n/**\n * Provides behavior for a button that when clicked copies content into user's\n * clipboard.\n */\n\nclass CdkCopyToClipboard {\n  constructor(_clipboard, _ngZone, config) {\n    this._clipboard = _clipboard;\n    this._ngZone = _ngZone;\n    /** Content to be copied. */\n\n    this.text = '';\n    /**\n     * How many times to attempt to copy the text. This may be necessary for longer text, because\n     * the browser needs time to fill an intermediate textarea element and copy the content.\n     */\n\n    this.attempts = 1;\n    /**\n     * Emits when some text is copied to the clipboard. The\n     * emitted value indicates whether copying was successful.\n     */\n\n    this.copied = new EventEmitter();\n    /** Copies that are currently being attempted. */\n\n    this._pending = new Set();\n\n    if (config && config.attempts != null) {\n      this.attempts = config.attempts;\n    }\n  }\n  /** Copies the current text to the clipboard. */\n\n\n  copy(attempts = this.attempts) {\n    if (attempts > 1) {\n      let remainingAttempts = attempts;\n\n      const pending = this._clipboard.beginCopy(this.text);\n\n      this._pending.add(pending);\n\n      const attempt = () => {\n        const successful = pending.copy();\n\n        if (!successful && --remainingAttempts && !this._destroyed) {\n          // We use 1 for the timeout since it's more predictable when flushing in unit tests.\n          this._currentTimeout = this._ngZone.runOutsideAngular(() => setTimeout(attempt, 1));\n        } else {\n          this._currentTimeout = null;\n\n          this._pending.delete(pending);\n\n          pending.destroy();\n          this.copied.emit(successful);\n        }\n      };\n\n      attempt();\n    } else {\n      this.copied.emit(this._clipboard.copy(this.text));\n    }\n  }\n\n  ngOnDestroy() {\n    if (this._currentTimeout) {\n      clearTimeout(this._currentTimeout);\n    }\n\n    this._pending.forEach(copy => copy.destroy());\n\n    this._pending.clear();\n\n    this._destroyed = true;\n  }\n\n}\n\nCdkCopyToClipboard.ɵfac = function CdkCopyToClipboard_Factory(t) {\n  return new (t || CdkCopyToClipboard)(i0.ɵɵdirectiveInject(Clipboard), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(CDK_COPY_TO_CLIPBOARD_CONFIG, 8));\n};\n\nCdkCopyToClipboard.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkCopyToClipboard,\n  selectors: [[\"\", \"cdkCopyToClipboard\", \"\"]],\n  hostBindings: function CdkCopyToClipboard_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function CdkCopyToClipboard_click_HostBindingHandler() {\n        return ctx.copy();\n      });\n    }\n  },\n  inputs: {\n    text: [\"cdkCopyToClipboard\", \"text\"],\n    attempts: [\"cdkCopyToClipboardAttempts\", \"attempts\"]\n  },\n  outputs: {\n    copied: \"cdkCopyToClipboardCopied\"\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkCopyToClipboard, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkCopyToClipboard]',\n      host: {\n        '(click)': 'copy()'\n      }\n    }]\n  }], function () {\n    return [{\n      type: Clipboard\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [CDK_COPY_TO_CLIPBOARD_CONFIG]\n      }]\n    }];\n  }, {\n    text: [{\n      type: Input,\n      args: ['cdkCopyToClipboard']\n    }],\n    attempts: [{\n      type: Input,\n      args: ['cdkCopyToClipboardAttempts']\n    }],\n    copied: [{\n      type: Output,\n      args: ['cdkCopyToClipboardCopied']\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass ClipboardModule {}\n\nClipboardModule.ɵfac = function ClipboardModule_Factory(t) {\n  return new (t || ClipboardModule)();\n};\n\nClipboardModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ClipboardModule,\n  declarations: [CdkCopyToClipboard],\n  exports: [CdkCopyToClipboard]\n});\nClipboardModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ClipboardModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [CdkCopyToClipboard],\n      exports: [CdkCopyToClipboard]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { CDK_COPY_TO_CLIPBOARD_CONFIG, CdkCopyToClipboard, Clipboard, ClipboardModule, PendingCopy };", "map": {"version": 3, "names": ["DOCUMENT", "i0", "Injectable", "Inject", "InjectionToken", "EventEmitter", "Directive", "Optional", "Input", "Output", "NgModule", "PendingCopy", "constructor", "text", "_document", "textarea", "_textarea", "createElement", "styles", "style", "position", "top", "opacity", "left", "setAttribute", "value", "readOnly", "body", "append<PERSON><PERSON><PERSON>", "copy", "successful", "currentFocus", "activeElement", "select", "setSelectionRange", "length", "execCommand", "focus", "destroy", "remove", "undefined", "Clipboard", "document", "pendingCopy", "beginCopy", "ɵfac", "ɵprov", "type", "args", "providedIn", "decorators", "CDK_COPY_TO_CLIPBOARD_CONFIG", "CdkCopyToClipboard", "_clipboard", "_ngZone", "config", "attempts", "copied", "_pending", "Set", "remainingAttempts", "pending", "add", "attempt", "_destroyed", "_currentTimeout", "runOutsideAngular", "setTimeout", "delete", "emit", "ngOnDestroy", "clearTimeout", "for<PERSON>ach", "clear", "NgZone", "ɵdir", "selector", "host", "ClipboardModule", "ɵmod", "ɵinj", "declarations", "exports"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/cdk/fesm2020/clipboard.mjs"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, InjectionToken, EventEmitter, Directive, Optional, Input, Output, NgModule } from '@angular/core';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * A pending copy-to-clipboard operation.\n *\n * The implementation of copying text to the clipboard modifies the DOM and\n * forces a re-layout. This re-layout can take too long if the string is large,\n * causing the execCommand('copy') to happen too long after the user clicked.\n * This results in the browser refusing to copy. This object lets the\n * re-layout happen in a separate tick from copying by providing a copy function\n * that can be called later.\n *\n * Destroy must be called when no longer in use, regardless of whether `copy` is\n * called.\n */\nclass PendingCopy {\n    constructor(text, _document) {\n        this._document = _document;\n        const textarea = (this._textarea = this._document.createElement('textarea'));\n        const styles = textarea.style;\n        // Hide the element for display and accessibility. Set a fixed position so the page layout\n        // isn't affected. We use `fixed` with `top: 0`, because focus is moved into the textarea\n        // for a split second and if it's off-screen, some browsers will attempt to scroll it into view.\n        styles.position = 'fixed';\n        styles.top = styles.opacity = '0';\n        styles.left = '-999em';\n        textarea.setAttribute('aria-hidden', 'true');\n        textarea.value = text;\n        // Making the textarea `readonly` prevents the screen from jumping on iOS Safari (see #25169).\n        textarea.readOnly = true;\n        this._document.body.appendChild(textarea);\n    }\n    /** Finishes copying the text. */\n    copy() {\n        const textarea = this._textarea;\n        let successful = false;\n        try {\n            // Older browsers could throw if copy is not supported.\n            if (textarea) {\n                const currentFocus = this._document.activeElement;\n                textarea.select();\n                textarea.setSelectionRange(0, textarea.value.length);\n                successful = this._document.execCommand('copy');\n                if (currentFocus) {\n                    currentFocus.focus();\n                }\n            }\n        }\n        catch {\n            // Discard error.\n            // Initial setting of {@code successful} will represent failure here.\n        }\n        return successful;\n    }\n    /** Cleans up DOM changes used to perform the copy operation. */\n    destroy() {\n        const textarea = this._textarea;\n        if (textarea) {\n            textarea.remove();\n            this._textarea = undefined;\n        }\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * A service for copying text to the clipboard.\n */\nclass Clipboard {\n    constructor(document) {\n        this._document = document;\n    }\n    /**\n     * Copies the provided text into the user's clipboard.\n     *\n     * @param text The string to copy.\n     * @returns Whether the operation was successful.\n     */\n    copy(text) {\n        const pendingCopy = this.beginCopy(text);\n        const successful = pendingCopy.copy();\n        pendingCopy.destroy();\n        return successful;\n    }\n    /**\n     * Prepares a string to be copied later. This is useful for large strings\n     * which take too long to successfully render and be copied in the same tick.\n     *\n     * The caller must call `destroy` on the returned `PendingCopy`.\n     *\n     * @param text The string to copy.\n     * @returns the pending copy operation.\n     */\n    beginCopy(text) {\n        return new PendingCopy(text, this._document);\n    }\n}\nClipboard.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: Clipboard, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nClipboard.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: Clipboard, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: Clipboard, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token that can be used to provide the default options to `CdkCopyToClipboard`. */\nconst CDK_COPY_TO_CLIPBOARD_CONFIG = new InjectionToken('CDK_COPY_TO_CLIPBOARD_CONFIG');\n/**\n * Provides behavior for a button that when clicked copies content into user's\n * clipboard.\n */\nclass CdkCopyToClipboard {\n    constructor(_clipboard, _ngZone, config) {\n        this._clipboard = _clipboard;\n        this._ngZone = _ngZone;\n        /** Content to be copied. */\n        this.text = '';\n        /**\n         * How many times to attempt to copy the text. This may be necessary for longer text, because\n         * the browser needs time to fill an intermediate textarea element and copy the content.\n         */\n        this.attempts = 1;\n        /**\n         * Emits when some text is copied to the clipboard. The\n         * emitted value indicates whether copying was successful.\n         */\n        this.copied = new EventEmitter();\n        /** Copies that are currently being attempted. */\n        this._pending = new Set();\n        if (config && config.attempts != null) {\n            this.attempts = config.attempts;\n        }\n    }\n    /** Copies the current text to the clipboard. */\n    copy(attempts = this.attempts) {\n        if (attempts > 1) {\n            let remainingAttempts = attempts;\n            const pending = this._clipboard.beginCopy(this.text);\n            this._pending.add(pending);\n            const attempt = () => {\n                const successful = pending.copy();\n                if (!successful && --remainingAttempts && !this._destroyed) {\n                    // We use 1 for the timeout since it's more predictable when flushing in unit tests.\n                    this._currentTimeout = this._ngZone.runOutsideAngular(() => setTimeout(attempt, 1));\n                }\n                else {\n                    this._currentTimeout = null;\n                    this._pending.delete(pending);\n                    pending.destroy();\n                    this.copied.emit(successful);\n                }\n            };\n            attempt();\n        }\n        else {\n            this.copied.emit(this._clipboard.copy(this.text));\n        }\n    }\n    ngOnDestroy() {\n        if (this._currentTimeout) {\n            clearTimeout(this._currentTimeout);\n        }\n        this._pending.forEach(copy => copy.destroy());\n        this._pending.clear();\n        this._destroyed = true;\n    }\n}\nCdkCopyToClipboard.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkCopyToClipboard, deps: [{ token: Clipboard }, { token: i0.NgZone }, { token: CDK_COPY_TO_CLIPBOARD_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nCdkCopyToClipboard.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: CdkCopyToClipboard, selector: \"[cdkCopyToClipboard]\", inputs: { text: [\"cdkCopyToClipboard\", \"text\"], attempts: [\"cdkCopyToClipboardAttempts\", \"attempts\"] }, outputs: { copied: \"cdkCopyToClipboardCopied\" }, host: { listeners: { \"click\": \"copy()\" } }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkCopyToClipboard, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkCopyToClipboard]',\n                    host: {\n                        '(click)': 'copy()',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: Clipboard }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [CDK_COPY_TO_CLIPBOARD_CONFIG]\n                }] }]; }, propDecorators: { text: [{\n                type: Input,\n                args: ['cdkCopyToClipboard']\n            }], attempts: [{\n                type: Input,\n                args: ['cdkCopyToClipboardAttempts']\n            }], copied: [{\n                type: Output,\n                args: ['cdkCopyToClipboardCopied']\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass ClipboardModule {\n}\nClipboardModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: ClipboardModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nClipboardModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: ClipboardModule, declarations: [CdkCopyToClipboard], exports: [CdkCopyToClipboard] });\nClipboardModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: ClipboardModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: ClipboardModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [CdkCopyToClipboard],\n                    exports: [CdkCopyToClipboard],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CDK_COPY_TO_CLIPBOARD_CONFIG, CdkCopyToClipboard, Clipboard, ClipboardModule, PendingCopy };\n"], "mappings": "AAAA,SAASA,QAAT,QAAyB,iBAAzB;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,MAArB,EAA6BC,cAA7B,EAA6CC,YAA7C,EAA2DC,SAA3D,EAAsEC,QAAtE,EAAgFC,KAAhF,EAAuFC,MAAvF,EAA+FC,QAA/F,QAA+G,eAA/G;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,WAAN,CAAkB;EACdC,WAAW,CAACC,IAAD,EAAOC,SAAP,EAAkB;IACzB,KAAKA,SAAL,GAAiBA,SAAjB;;IACA,MAAMC,QAAQ,GAAI,KAAKC,SAAL,GAAiB,KAAKF,SAAL,CAAeG,aAAf,CAA6B,UAA7B,CAAnC;;IACA,MAAMC,MAAM,GAAGH,QAAQ,CAACI,KAAxB,CAHyB,CAIzB;IACA;IACA;;IACAD,MAAM,CAACE,QAAP,GAAkB,OAAlB;IACAF,MAAM,CAACG,GAAP,GAAaH,MAAM,CAACI,OAAP,GAAiB,GAA9B;IACAJ,MAAM,CAACK,IAAP,GAAc,QAAd;IACAR,QAAQ,CAACS,YAAT,CAAsB,aAAtB,EAAqC,MAArC;IACAT,QAAQ,CAACU,KAAT,GAAiBZ,IAAjB,CAXyB,CAYzB;;IACAE,QAAQ,CAACW,QAAT,GAAoB,IAApB;;IACA,KAAKZ,SAAL,CAAea,IAAf,CAAoBC,WAApB,CAAgCb,QAAhC;EACH;EACD;;;EACAc,IAAI,GAAG;IACH,MAAMd,QAAQ,GAAG,KAAKC,SAAtB;IACA,IAAIc,UAAU,GAAG,KAAjB;;IACA,IAAI;MACA;MACA,IAAIf,QAAJ,EAAc;QACV,MAAMgB,YAAY,GAAG,KAAKjB,SAAL,CAAekB,aAApC;QACAjB,QAAQ,CAACkB,MAAT;QACAlB,QAAQ,CAACmB,iBAAT,CAA2B,CAA3B,EAA8BnB,QAAQ,CAACU,KAAT,CAAeU,MAA7C;QACAL,UAAU,GAAG,KAAKhB,SAAL,CAAesB,WAAf,CAA2B,MAA3B,CAAb;;QACA,IAAIL,YAAJ,EAAkB;UACdA,YAAY,CAACM,KAAb;QACH;MACJ;IACJ,CAXD,CAYA,MAAM,CACF;MACA;IACH;;IACD,OAAOP,UAAP;EACH;EACD;;;EACAQ,OAAO,GAAG;IACN,MAAMvB,QAAQ,GAAG,KAAKC,SAAtB;;IACA,IAAID,QAAJ,EAAc;MACVA,QAAQ,CAACwB,MAAT;MACA,KAAKvB,SAAL,GAAiBwB,SAAjB;IACH;EACJ;;AA9Ca;AAiDlB;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMC,SAAN,CAAgB;EACZ7B,WAAW,CAAC8B,QAAD,EAAW;IAClB,KAAK5B,SAAL,GAAiB4B,QAAjB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIb,IAAI,CAAChB,IAAD,EAAO;IACP,MAAM8B,WAAW,GAAG,KAAKC,SAAL,CAAe/B,IAAf,CAApB;IACA,MAAMiB,UAAU,GAAGa,WAAW,CAACd,IAAZ,EAAnB;IACAc,WAAW,CAACL,OAAZ;IACA,OAAOR,UAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIc,SAAS,CAAC/B,IAAD,EAAO;IACZ,OAAO,IAAIF,WAAJ,CAAgBE,IAAhB,EAAsB,KAAKC,SAA3B,CAAP;EACH;;AA3BW;;AA6BhB2B,SAAS,CAACI,IAAV;EAAA,iBAAsGJ,SAAtG,EAA4FxC,EAA5F,UAAiID,QAAjI;AAAA;;AACAyC,SAAS,CAACK,KAAV,kBAD4F7C,EAC5F;EAAA,OAA0GwC,SAA1G;EAAA,SAA0GA,SAA1G;EAAA,YAAiI;AAAjI;;AACA;EAAA,mDAF4FxC,EAE5F,mBAA2FwC,SAA3F,EAAkH,CAAC;IACvGM,IAAI,EAAE7C,UADiG;IAEvG8C,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAFiG,CAAD,CAAlH,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAEP,SAAR;MAAmBU,UAAU,EAAE,CAAC;QAC9DH,IAAI,EAAE5C,MADwD;QAE9D6C,IAAI,EAAE,CAAChD,QAAD;MAFwD,CAAD;IAA/B,CAAD,CAAP;EAGlB,CANxB;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMmD,4BAA4B,GAAG,IAAI/C,cAAJ,CAAmB,8BAAnB,CAArC;AACA;AACA;AACA;AACA;;AACA,MAAMgD,kBAAN,CAAyB;EACrBxC,WAAW,CAACyC,UAAD,EAAaC,OAAb,EAAsBC,MAAtB,EAA8B;IACrC,KAAKF,UAAL,GAAkBA,UAAlB;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA;;IACA,KAAKzC,IAAL,GAAY,EAAZ;IACA;AACR;AACA;AACA;;IACQ,KAAK2C,QAAL,GAAgB,CAAhB;IACA;AACR;AACA;AACA;;IACQ,KAAKC,MAAL,GAAc,IAAIpD,YAAJ,EAAd;IACA;;IACA,KAAKqD,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;;IACA,IAAIJ,MAAM,IAAIA,MAAM,CAACC,QAAP,IAAmB,IAAjC,EAAuC;MACnC,KAAKA,QAAL,GAAgBD,MAAM,CAACC,QAAvB;IACH;EACJ;EACD;;;EACA3B,IAAI,CAAC2B,QAAQ,GAAG,KAAKA,QAAjB,EAA2B;IAC3B,IAAIA,QAAQ,GAAG,CAAf,EAAkB;MACd,IAAII,iBAAiB,GAAGJ,QAAxB;;MACA,MAAMK,OAAO,GAAG,KAAKR,UAAL,CAAgBT,SAAhB,CAA0B,KAAK/B,IAA/B,CAAhB;;MACA,KAAK6C,QAAL,CAAcI,GAAd,CAAkBD,OAAlB;;MACA,MAAME,OAAO,GAAG,MAAM;QAClB,MAAMjC,UAAU,GAAG+B,OAAO,CAAChC,IAAR,EAAnB;;QACA,IAAI,CAACC,UAAD,IAAe,EAAE8B,iBAAjB,IAAsC,CAAC,KAAKI,UAAhD,EAA4D;UACxD;UACA,KAAKC,eAAL,GAAuB,KAAKX,OAAL,CAAaY,iBAAb,CAA+B,MAAMC,UAAU,CAACJ,OAAD,EAAU,CAAV,CAA/C,CAAvB;QACH,CAHD,MAIK;UACD,KAAKE,eAAL,GAAuB,IAAvB;;UACA,KAAKP,QAAL,CAAcU,MAAd,CAAqBP,OAArB;;UACAA,OAAO,CAACvB,OAAR;UACA,KAAKmB,MAAL,CAAYY,IAAZ,CAAiBvC,UAAjB;QACH;MACJ,CAZD;;MAaAiC,OAAO;IACV,CAlBD,MAmBK;MACD,KAAKN,MAAL,CAAYY,IAAZ,CAAiB,KAAKhB,UAAL,CAAgBxB,IAAhB,CAAqB,KAAKhB,IAA1B,CAAjB;IACH;EACJ;;EACDyD,WAAW,GAAG;IACV,IAAI,KAAKL,eAAT,EAA0B;MACtBM,YAAY,CAAC,KAAKN,eAAN,CAAZ;IACH;;IACD,KAAKP,QAAL,CAAcc,OAAd,CAAsB3C,IAAI,IAAIA,IAAI,CAACS,OAAL,EAA9B;;IACA,KAAKoB,QAAL,CAAce,KAAd;;IACA,KAAKT,UAAL,GAAkB,IAAlB;EACH;;AAtDoB;;AAwDzBZ,kBAAkB,CAACP,IAAnB;EAAA,iBAA+GO,kBAA/G,EA/E4FnD,EA+E5F,mBAAmJwC,SAAnJ,GA/E4FxC,EA+E5F,mBAAyKA,EAAE,CAACyE,MAA5K,GA/E4FzE,EA+E5F,mBAA+LkD,4BAA/L;AAAA;;AACAC,kBAAkB,CAACuB,IAAnB,kBAhF4F1E,EAgF5F;EAAA,MAAmGmD,kBAAnG;EAAA;EAAA;IAAA;MAhF4FnD,EAgF5F;QAAA,OAAmG,UAAnG;MAAA;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;AAAA;;AACA;EAAA,mDAjF4FA,EAiF5F,mBAA2FmD,kBAA3F,EAA2H,CAAC;IAChHL,IAAI,EAAEzC,SAD0G;IAEhH0C,IAAI,EAAE,CAAC;MACC4B,QAAQ,EAAE,sBADX;MAECC,IAAI,EAAE;QACF,WAAW;MADT;IAFP,CAAD;EAF0G,CAAD,CAA3H,EAQ4B,YAAY;IAAE,OAAO,CAAC;MAAE9B,IAAI,EAAEN;IAAR,CAAD,EAAsB;MAAEM,IAAI,EAAE9C,EAAE,CAACyE;IAAX,CAAtB,EAA2C;MAAE3B,IAAI,EAAEP,SAAR;MAAmBU,UAAU,EAAE,CAAC;QACxGH,IAAI,EAAExC;MADkG,CAAD,EAExG;QACCwC,IAAI,EAAE5C,MADP;QAEC6C,IAAI,EAAE,CAACG,4BAAD;MAFP,CAFwG;IAA/B,CAA3C,CAAP;EAKlB,CAbxB,EAa0C;IAAEtC,IAAI,EAAE,CAAC;MACnCkC,IAAI,EAAEvC,KAD6B;MAEnCwC,IAAI,EAAE,CAAC,oBAAD;IAF6B,CAAD,CAAR;IAG1BQ,QAAQ,EAAE,CAAC;MACXT,IAAI,EAAEvC,KADK;MAEXwC,IAAI,EAAE,CAAC,4BAAD;IAFK,CAAD,CAHgB;IAM1BS,MAAM,EAAE,CAAC;MACTV,IAAI,EAAEtC,MADG;MAETuC,IAAI,EAAE,CAAC,0BAAD;IAFG,CAAD;EANkB,CAb1C;AAAA;AAwBA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM8B,eAAN,CAAsB;;AAEtBA,eAAe,CAACjC,IAAhB;EAAA,iBAA4GiC,eAA5G;AAAA;;AACAA,eAAe,CAACC,IAAhB,kBAnH4F9E,EAmH5F;EAAA,MAA6G6E,eAA7G;EAAA,eAA6I1B,kBAA7I;EAAA,UAA4KA,kBAA5K;AAAA;AACA0B,eAAe,CAACE,IAAhB,kBApH4F/E,EAoH5F;;AACA;EAAA,mDArH4FA,EAqH5F,mBAA2F6E,eAA3F,EAAwH,CAAC;IAC7G/B,IAAI,EAAErC,QADuG;IAE7GsC,IAAI,EAAE,CAAC;MACCiC,YAAY,EAAE,CAAC7B,kBAAD,CADf;MAEC8B,OAAO,EAAE,CAAC9B,kBAAD;IAFV,CAAD;EAFuG,CAAD,CAAxH;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASD,4BAAT,EAAuCC,kBAAvC,EAA2DX,SAA3D,EAAsEqC,eAAtE,EAAuFnE,WAAvF"}, "metadata": {}, "sourceType": "module"}