{"ast": null, "code": "import { map } from './map';\nexport function pluck(...properties) {\n  const length = properties.length;\n\n  if (length === 0) {\n    throw new Error('list of properties cannot be empty.');\n  }\n\n  return source => map(plucker(properties, length))(source);\n}\n\nfunction plucker(props, length) {\n  const mapper = x => {\n    let currentProp = x;\n\n    for (let i = 0; i < length; i++) {\n      const p = currentProp != null ? currentProp[props[i]] : undefined;\n\n      if (p !== void 0) {\n        currentProp = p;\n      } else {\n        return undefined;\n      }\n    }\n\n    return currentProp;\n  };\n\n  return mapper;\n}", "map": {"version": 3, "names": ["map", "pluck", "properties", "length", "Error", "source", "plucker", "props", "mapper", "x", "currentProp", "i", "p", "undefined"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/pluck.js"], "sourcesContent": ["import { map } from './map';\nexport function pluck(...properties) {\n    const length = properties.length;\n    if (length === 0) {\n        throw new Error('list of properties cannot be empty.');\n    }\n    return (source) => map(plucker(properties, length))(source);\n}\nfunction plucker(props, length) {\n    const mapper = (x) => {\n        let currentProp = x;\n        for (let i = 0; i < length; i++) {\n            const p = currentProp != null ? currentProp[props[i]] : undefined;\n            if (p !== void 0) {\n                currentProp = p;\n            }\n            else {\n                return undefined;\n            }\n        }\n        return currentProp;\n    };\n    return mapper;\n}\n"], "mappings": "AAAA,SAASA,GAAT,QAAoB,OAApB;AACA,OAAO,SAASC,KAAT,CAAe,GAAGC,UAAlB,EAA8B;EACjC,MAAMC,MAAM,GAAGD,UAAU,CAACC,MAA1B;;EACA,IAAIA,MAAM,KAAK,CAAf,EAAkB;IACd,MAAM,IAAIC,KAAJ,CAAU,qCAAV,CAAN;EACH;;EACD,OAAQC,MAAD,IAAYL,GAAG,CAACM,OAAO,CAACJ,UAAD,EAAaC,MAAb,CAAR,CAAH,CAAiCE,MAAjC,CAAnB;AACH;;AACD,SAASC,OAAT,CAAiBC,KAAjB,EAAwBJ,MAAxB,EAAgC;EAC5B,MAAMK,MAAM,GAAIC,CAAD,IAAO;IAClB,IAAIC,WAAW,GAAGD,CAAlB;;IACA,KAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGR,MAApB,EAA4BQ,CAAC,EAA7B,EAAiC;MAC7B,MAAMC,CAAC,GAAGF,WAAW,IAAI,IAAf,GAAsBA,WAAW,CAACH,KAAK,CAACI,CAAD,CAAN,CAAjC,GAA8CE,SAAxD;;MACA,IAAID,CAAC,KAAK,KAAK,CAAf,EAAkB;QACdF,WAAW,GAAGE,CAAd;MACH,CAFD,MAGK;QACD,OAAOC,SAAP;MACH;IACJ;;IACD,OAAOH,WAAP;EACH,CAZD;;EAaA,OAAOF,MAAP;AACH"}, "metadata": {}, "sourceType": "module"}