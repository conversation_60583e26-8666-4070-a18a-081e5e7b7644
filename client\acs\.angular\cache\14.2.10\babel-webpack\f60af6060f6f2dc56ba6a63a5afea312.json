{"ast": null, "code": ";\n\n(function (root, factory) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var WordArray = C_lib.WordArray;\n    var Hasher = C_lib.Hasher;\n    var C_algo = C.algo; // Reusable object\n\n    var W = [];\n    /**\n     * SHA-1 hash algorithm.\n     */\n\n    var SHA1 = C_algo.SHA1 = Hasher.extend({\n      _doReset: function () {\n        this._hash = new WordArray.init([0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0]);\n      },\n      _doProcessBlock: function (M, offset) {\n        // Shortcut\n        var H = this._hash.words; // Working variables\n\n        var a = H[0];\n        var b = H[1];\n        var c = H[2];\n        var d = H[3];\n        var e = H[4]; // Computation\n\n        for (var i = 0; i < 80; i++) {\n          if (i < 16) {\n            W[i] = M[offset + i] | 0;\n          } else {\n            var n = W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16];\n            W[i] = n << 1 | n >>> 31;\n          }\n\n          var t = (a << 5 | a >>> 27) + e + W[i];\n\n          if (i < 20) {\n            t += (b & c | ~b & d) + 0x5a827999;\n          } else if (i < 40) {\n            t += (b ^ c ^ d) + 0x6ed9eba1;\n          } else if (i < 60) {\n            t += (b & c | b & d | c & d) - 0x70e44324;\n          } else\n            /* if (i < 80) */\n            {\n              t += (b ^ c ^ d) - 0x359d3e2a;\n            }\n\n          e = d;\n          d = c;\n          c = b << 30 | b >>> 2;\n          b = a;\n          a = t;\n        } // Intermediate hash value\n\n\n        H[0] = H[0] + a | 0;\n        H[1] = H[1] + b | 0;\n        H[2] = H[2] + c | 0;\n        H[3] = H[3] + d | 0;\n        H[4] = H[4] + e | 0;\n      },\n      _doFinalize: function () {\n        // Shortcuts\n        var data = this._data;\n        var dataWords = data.words;\n        var nBitsTotal = this._nDataBytes * 8;\n        var nBitsLeft = data.sigBytes * 8; // Add padding\n\n        dataWords[nBitsLeft >>> 5] |= 0x80 << 24 - nBitsLeft % 32;\n        dataWords[(nBitsLeft + 64 >>> 9 << 4) + 14] = Math.floor(nBitsTotal / 0x100000000);\n        dataWords[(nBitsLeft + 64 >>> 9 << 4) + 15] = nBitsTotal;\n        data.sigBytes = dataWords.length * 4; // Hash final blocks\n\n        this._process(); // Return final computed hash\n\n\n        return this._hash;\n      },\n      clone: function () {\n        var clone = Hasher.clone.call(this);\n        clone._hash = this._hash.clone();\n        return clone;\n      }\n    });\n    /**\n     * Shortcut function to the hasher's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     *\n     * @return {WordArray} The hash.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hash = CryptoJS.SHA1('message');\n     *     var hash = CryptoJS.SHA1(wordArray);\n     */\n\n    C.SHA1 = Hasher._createHelper(SHA1);\n    /**\n     * Shortcut function to the HMAC's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     * @param {WordArray|string} key The secret key.\n     *\n     * @return {WordArray} The HMAC.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hmac = CryptoJS.HmacSHA1(message, key);\n     */\n\n    C.HmacSHA1 = Hasher._createHmacHelper(SHA1);\n  })();\n\n  return CryptoJS.SHA1;\n});", "map": {"version": 3, "names": ["root", "factory", "exports", "module", "require", "define", "amd", "CryptoJS", "C", "C_lib", "lib", "WordArray", "<PERSON><PERSON>", "C_algo", "algo", "W", "SHA1", "extend", "_doReset", "_hash", "init", "_doProcessBlock", "M", "offset", "H", "words", "a", "b", "c", "d", "e", "i", "n", "t", "_doFinalize", "data", "_data", "dataWords", "nBitsTotal", "_nDataBytes", "nBitsLeft", "sigBytes", "Math", "floor", "length", "_process", "clone", "call", "_createHelper", "HmacSHA1", "_createHmacHelper"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/crypto-js/sha1.js"], "sourcesContent": [";(function (root, factory) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t(function () {\n\t    // Shortcuts\n\t    var C = CryptoJS;\n\t    var C_lib = C.lib;\n\t    var WordArray = C_lib.WordArray;\n\t    var Hasher = C_lib.Hasher;\n\t    var C_algo = C.algo;\n\n\t    // Reusable object\n\t    var W = [];\n\n\t    /**\n\t     * SHA-1 hash algorithm.\n\t     */\n\t    var SHA1 = C_algo.SHA1 = Hasher.extend({\n\t        _doReset: function () {\n\t            this._hash = new WordArray.init([\n\t                0x67452301, 0xefcdab89,\n\t                0x98badcfe, 0x10325476,\n\t                0xc3d2e1f0\n\t            ]);\n\t        },\n\n\t        _doProcessBlock: function (M, offset) {\n\t            // Shortcut\n\t            var H = this._hash.words;\n\n\t            // Working variables\n\t            var a = H[0];\n\t            var b = H[1];\n\t            var c = H[2];\n\t            var d = H[3];\n\t            var e = H[4];\n\n\t            // Computation\n\t            for (var i = 0; i < 80; i++) {\n\t                if (i < 16) {\n\t                    W[i] = M[offset + i] | 0;\n\t                } else {\n\t                    var n = W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16];\n\t                    W[i] = (n << 1) | (n >>> 31);\n\t                }\n\n\t                var t = ((a << 5) | (a >>> 27)) + e + W[i];\n\t                if (i < 20) {\n\t                    t += ((b & c) | (~b & d)) + 0x5a827999;\n\t                } else if (i < 40) {\n\t                    t += (b ^ c ^ d) + 0x6ed9eba1;\n\t                } else if (i < 60) {\n\t                    t += ((b & c) | (b & d) | (c & d)) - 0x70e44324;\n\t                } else /* if (i < 80) */ {\n\t                    t += (b ^ c ^ d) - 0x359d3e2a;\n\t                }\n\n\t                e = d;\n\t                d = c;\n\t                c = (b << 30) | (b >>> 2);\n\t                b = a;\n\t                a = t;\n\t            }\n\n\t            // Intermediate hash value\n\t            H[0] = (H[0] + a) | 0;\n\t            H[1] = (H[1] + b) | 0;\n\t            H[2] = (H[2] + c) | 0;\n\t            H[3] = (H[3] + d) | 0;\n\t            H[4] = (H[4] + e) | 0;\n\t        },\n\n\t        _doFinalize: function () {\n\t            // Shortcuts\n\t            var data = this._data;\n\t            var dataWords = data.words;\n\n\t            var nBitsTotal = this._nDataBytes * 8;\n\t            var nBitsLeft = data.sigBytes * 8;\n\n\t            // Add padding\n\t            dataWords[nBitsLeft >>> 5] |= 0x80 << (24 - nBitsLeft % 32);\n\t            dataWords[(((nBitsLeft + 64) >>> 9) << 4) + 14] = Math.floor(nBitsTotal / 0x100000000);\n\t            dataWords[(((nBitsLeft + 64) >>> 9) << 4) + 15] = nBitsTotal;\n\t            data.sigBytes = dataWords.length * 4;\n\n\t            // Hash final blocks\n\t            this._process();\n\n\t            // Return final computed hash\n\t            return this._hash;\n\t        },\n\n\t        clone: function () {\n\t            var clone = Hasher.clone.call(this);\n\t            clone._hash = this._hash.clone();\n\n\t            return clone;\n\t        }\n\t    });\n\n\t    /**\n\t     * Shortcut function to the hasher's object interface.\n\t     *\n\t     * @param {WordArray|string} message The message to hash.\n\t     *\n\t     * @return {WordArray} The hash.\n\t     *\n\t     * @static\n\t     *\n\t     * @example\n\t     *\n\t     *     var hash = CryptoJS.SHA1('message');\n\t     *     var hash = CryptoJS.SHA1(wordArray);\n\t     */\n\t    C.SHA1 = Hasher._createHelper(SHA1);\n\n\t    /**\n\t     * Shortcut function to the HMAC's object interface.\n\t     *\n\t     * @param {WordArray|string} message The message to hash.\n\t     * @param {WordArray|string} key The secret key.\n\t     *\n\t     * @return {WordArray} The HMAC.\n\t     *\n\t     * @static\n\t     *\n\t     * @example\n\t     *\n\t     *     var hmac = CryptoJS.HmacSHA1(message, key);\n\t     */\n\t    C.HmacSHA1 = Hasher._createHmacHelper(SHA1);\n\t}());\n\n\n\treturn CryptoJS.SHA1;\n\n}));"], "mappings": "AAAA;;AAAE,WAAUA,IAAV,EAAgBC,OAAhB,EAAyB;EAC1B,IAAI,OAAOC,OAAP,KAAmB,QAAvB,EAAiC;IAChC;IACAC,MAAM,CAACD,OAAP,GAAiBA,OAAO,GAAGD,OAAO,CAACG,OAAO,CAAC,QAAD,CAAR,CAAlC;EACA,CAHD,MAIK,IAAI,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAA3C,EAAgD;IACpD;IACAD,MAAM,CAAC,CAAC,QAAD,CAAD,EAAaJ,OAAb,CAAN;EACA,CAHI,MAIA;IACJ;IACAA,OAAO,CAACD,IAAI,CAACO,QAAN,CAAP;EACA;AACD,CAbC,EAaA,IAbA,EAaM,UAAUA,QAAV,EAAoB;EAE1B,aAAY;IACT;IACA,IAAIC,CAAC,GAAGD,QAAR;IACA,IAAIE,KAAK,GAAGD,CAAC,CAACE,GAAd;IACA,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAtB;IACA,IAAIC,MAAM,GAAGH,KAAK,CAACG,MAAnB;IACA,IAAIC,MAAM,GAAGL,CAAC,CAACM,IAAf,CANS,CAQT;;IACA,IAAIC,CAAC,GAAG,EAAR;IAEA;AACL;AACA;;IACK,IAAIC,IAAI,GAAGH,MAAM,CAACG,IAAP,GAAcJ,MAAM,CAACK,MAAP,CAAc;MACnCC,QAAQ,EAAE,YAAY;QAClB,KAAKC,KAAL,GAAa,IAAIR,SAAS,CAACS,IAAd,CAAmB,CAC5B,UAD4B,EAChB,UADgB,EAE5B,UAF4B,EAEhB,UAFgB,EAG5B,UAH4B,CAAnB,CAAb;MAKH,CAPkC;MASnCC,eAAe,EAAE,UAAUC,CAAV,EAAaC,MAAb,EAAqB;QAClC;QACA,IAAIC,CAAC,GAAG,KAAKL,KAAL,CAAWM,KAAnB,CAFkC,CAIlC;;QACA,IAAIC,CAAC,GAAGF,CAAC,CAAC,CAAD,CAAT;QACA,IAAIG,CAAC,GAAGH,CAAC,CAAC,CAAD,CAAT;QACA,IAAII,CAAC,GAAGJ,CAAC,CAAC,CAAD,CAAT;QACA,IAAIK,CAAC,GAAGL,CAAC,CAAC,CAAD,CAAT;QACA,IAAIM,CAAC,GAAGN,CAAC,CAAC,CAAD,CAAT,CATkC,CAWlC;;QACA,KAAK,IAAIO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,EAApB,EAAwBA,CAAC,EAAzB,EAA6B;UACzB,IAAIA,CAAC,GAAG,EAAR,EAAY;YACRhB,CAAC,CAACgB,CAAD,CAAD,GAAOT,CAAC,CAACC,MAAM,GAAGQ,CAAV,CAAD,GAAgB,CAAvB;UACH,CAFD,MAEO;YACH,IAAIC,CAAC,GAAGjB,CAAC,CAACgB,CAAC,GAAG,CAAL,CAAD,GAAWhB,CAAC,CAACgB,CAAC,GAAG,CAAL,CAAZ,GAAsBhB,CAAC,CAACgB,CAAC,GAAG,EAAL,CAAvB,GAAkChB,CAAC,CAACgB,CAAC,GAAG,EAAL,CAA3C;YACAhB,CAAC,CAACgB,CAAD,CAAD,GAAQC,CAAC,IAAI,CAAN,GAAYA,CAAC,KAAK,EAAzB;UACH;;UAED,IAAIC,CAAC,GAAG,CAAEP,CAAC,IAAI,CAAN,GAAYA,CAAC,KAAK,EAAnB,IAA0BI,CAA1B,GAA8Bf,CAAC,CAACgB,CAAD,CAAvC;;UACA,IAAIA,CAAC,GAAG,EAAR,EAAY;YACRE,CAAC,IAAI,CAAEN,CAAC,GAAGC,CAAL,GAAW,CAACD,CAAD,GAAKE,CAAjB,IAAuB,UAA5B;UACH,CAFD,MAEO,IAAIE,CAAC,GAAG,EAAR,EAAY;YACfE,CAAC,IAAI,CAACN,CAAC,GAAGC,CAAJ,GAAQC,CAAT,IAAc,UAAnB;UACH,CAFM,MAEA,IAAIE,CAAC,GAAG,EAAR,EAAY;YACfE,CAAC,IAAI,CAAEN,CAAC,GAAGC,CAAL,GAAWD,CAAC,GAAGE,CAAf,GAAqBD,CAAC,GAAGC,CAA1B,IAAgC,UAArC;UACH,CAFM;YAEA;YAAkB;cACrBI,CAAC,IAAI,CAACN,CAAC,GAAGC,CAAJ,GAAQC,CAAT,IAAc,UAAnB;YACH;;UAEDC,CAAC,GAAGD,CAAJ;UACAA,CAAC,GAAGD,CAAJ;UACAA,CAAC,GAAID,CAAC,IAAI,EAAN,GAAaA,CAAC,KAAK,CAAvB;UACAA,CAAC,GAAGD,CAAJ;UACAA,CAAC,GAAGO,CAAJ;QACH,CApCiC,CAsClC;;;QACAT,CAAC,CAAC,CAAD,CAAD,GAAQA,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAR,GAAa,CAApB;QACAF,CAAC,CAAC,CAAD,CAAD,GAAQA,CAAC,CAAC,CAAD,CAAD,GAAOG,CAAR,GAAa,CAApB;QACAH,CAAC,CAAC,CAAD,CAAD,GAAQA,CAAC,CAAC,CAAD,CAAD,GAAOI,CAAR,GAAa,CAApB;QACAJ,CAAC,CAAC,CAAD,CAAD,GAAQA,CAAC,CAAC,CAAD,CAAD,GAAOK,CAAR,GAAa,CAApB;QACAL,CAAC,CAAC,CAAD,CAAD,GAAQA,CAAC,CAAC,CAAD,CAAD,GAAOM,CAAR,GAAa,CAApB;MACH,CArDkC;MAuDnCI,WAAW,EAAE,YAAY;QACrB;QACA,IAAIC,IAAI,GAAG,KAAKC,KAAhB;QACA,IAAIC,SAAS,GAAGF,IAAI,CAACV,KAArB;QAEA,IAAIa,UAAU,GAAG,KAAKC,WAAL,GAAmB,CAApC;QACA,IAAIC,SAAS,GAAGL,IAAI,CAACM,QAAL,GAAgB,CAAhC,CANqB,CAQrB;;QACAJ,SAAS,CAACG,SAAS,KAAK,CAAf,CAAT,IAA8B,QAAS,KAAKA,SAAS,GAAG,EAAxD;QACAH,SAAS,CAAC,CAAGG,SAAS,GAAG,EAAb,KAAqB,CAAtB,IAA4B,CAA7B,IAAkC,EAAnC,CAAT,GAAkDE,IAAI,CAACC,KAAL,CAAWL,UAAU,GAAG,WAAxB,CAAlD;QACAD,SAAS,CAAC,CAAGG,SAAS,GAAG,EAAb,KAAqB,CAAtB,IAA4B,CAA7B,IAAkC,EAAnC,CAAT,GAAkDF,UAAlD;QACAH,IAAI,CAACM,QAAL,GAAgBJ,SAAS,CAACO,MAAV,GAAmB,CAAnC,CAZqB,CAcrB;;QACA,KAAKC,QAAL,GAfqB,CAiBrB;;;QACA,OAAO,KAAK1B,KAAZ;MACH,CA1EkC;MA4EnC2B,KAAK,EAAE,YAAY;QACf,IAAIA,KAAK,GAAGlC,MAAM,CAACkC,KAAP,CAAaC,IAAb,CAAkB,IAAlB,CAAZ;QACAD,KAAK,CAAC3B,KAAN,GAAc,KAAKA,KAAL,CAAW2B,KAAX,EAAd;QAEA,OAAOA,KAAP;MACH;IAjFkC,CAAd,CAAzB;IAoFA;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IACKtC,CAAC,CAACQ,IAAF,GAASJ,MAAM,CAACoC,aAAP,CAAqBhC,IAArB,CAAT;IAEA;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IACKR,CAAC,CAACyC,QAAF,GAAarC,MAAM,CAACsC,iBAAP,CAAyBlC,IAAzB,CAAb;EACH,CAjIA,GAAD;;EAoIA,OAAOT,QAAQ,CAACS,IAAhB;AAEA,CArJC,CAAD"}, "metadata": {}, "sourceType": "script"}