{"ast": null, "code": "import { isArray } from '../util/isArray';\nimport { race as raceStatic } from '../observable/race';\nexport function race(...observables) {\n  return function raceOperatorFunction(source) {\n    if (observables.length === 1 && isArray(observables[0])) {\n      observables = observables[0];\n    }\n\n    return source.lift.call(raceStatic(source, ...observables));\n  };\n}", "map": {"version": 3, "names": ["isArray", "race", "raceStatic", "observables", "raceOperatorFunction", "source", "length", "lift", "call"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/race.js"], "sourcesContent": ["import { isArray } from '../util/isArray';\nimport { race as raceStatic } from '../observable/race';\nexport function race(...observables) {\n    return function raceOperatorFunction(source) {\n        if (observables.length === 1 && isArray(observables[0])) {\n            observables = observables[0];\n        }\n        return source.lift.call(raceStatic(source, ...observables));\n    };\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,iBAAxB;AACA,SAASC,IAAI,IAAIC,UAAjB,QAAmC,oBAAnC;AACA,OAAO,SAASD,IAAT,CAAc,GAAGE,WAAjB,EAA8B;EACjC,OAAO,SAASC,oBAAT,CAA8BC,MAA9B,EAAsC;IACzC,IAAIF,WAAW,CAACG,MAAZ,KAAuB,CAAvB,IAA4BN,OAAO,CAACG,WAAW,CAAC,CAAD,CAAZ,CAAvC,EAAyD;MACrDA,WAAW,GAAGA,WAAW,CAAC,CAAD,CAAzB;IACH;;IACD,OAAOE,MAAM,CAACE,IAAP,CAAYC,IAAZ,CAAiBN,UAAU,CAACG,MAAD,EAAS,GAAGF,WAAZ,CAA3B,CAAP;EACH,CALD;AAMH"}, "metadata": {}, "sourceType": "module"}