{"ast": null, "code": "import { SimpleOuterSubscriber, SimpleInnerSubscriber, innerSubscribe } from '../innerSubscribe';\nexport function skipUntil(notifier) {\n  return source => source.lift(new SkipUntilOperator(notifier));\n}\n\nclass SkipUntilOperator {\n  constructor(notifier) {\n    this.notifier = notifier;\n  }\n\n  call(destination, source) {\n    return source.subscribe(new SkipUntilSubscriber(destination, this.notifier));\n  }\n\n}\n\nclass SkipUntilSubscriber extends SimpleOuterSubscriber {\n  constructor(destination, notifier) {\n    super(destination);\n    this.hasValue = false;\n    const innerSubscriber = new SimpleInnerSubscriber(this);\n    this.add(innerSubscriber);\n    this.innerSubscription = innerSubscriber;\n    const innerSubscription = innerSubscribe(notifier, innerSubscriber);\n\n    if (innerSubscription !== innerSubscriber) {\n      this.add(innerSubscription);\n      this.innerSubscription = innerSubscription;\n    }\n  }\n\n  _next(value) {\n    if (this.hasValue) {\n      super._next(value);\n    }\n  }\n\n  notifyNext() {\n    this.hasValue = true;\n\n    if (this.innerSubscription) {\n      this.innerSubscription.unsubscribe();\n    }\n  }\n\n  notifyComplete() {}\n\n}", "map": {"version": 3, "names": ["SimpleOuterSubscriber", "SimpleInnerSubscriber", "innerSubscribe", "<PERSON><PERSON><PERSON><PERSON>", "notifier", "source", "lift", "SkipUntilOperator", "constructor", "call", "destination", "subscribe", "SkipUntilSubscriber", "hasValue", "innerSubscriber", "add", "innerSubscription", "_next", "value", "notifyNext", "unsubscribe", "notifyComplete"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/skipUntil.js"], "sourcesContent": ["import { SimpleOuterSubscriber, SimpleInnerSubscriber, innerSubscribe } from '../innerSubscribe';\nexport function skipUntil(notifier) {\n    return (source) => source.lift(new SkipUntilOperator(notifier));\n}\nclass SkipUntilOperator {\n    constructor(notifier) {\n        this.notifier = notifier;\n    }\n    call(destination, source) {\n        return source.subscribe(new SkipUntilSubscriber(destination, this.notifier));\n    }\n}\nclass SkipUntilSubscriber extends SimpleOuterSubscriber {\n    constructor(destination, notifier) {\n        super(destination);\n        this.hasValue = false;\n        const innerSubscriber = new SimpleInnerSubscriber(this);\n        this.add(innerSubscriber);\n        this.innerSubscription = innerSubscriber;\n        const innerSubscription = innerSubscribe(notifier, innerSubscriber);\n        if (innerSubscription !== innerSubscriber) {\n            this.add(innerSubscription);\n            this.innerSubscription = innerSubscription;\n        }\n    }\n    _next(value) {\n        if (this.hasValue) {\n            super._next(value);\n        }\n    }\n    notifyNext() {\n        this.hasValue = true;\n        if (this.innerSubscription) {\n            this.innerSubscription.unsubscribe();\n        }\n    }\n    notifyComplete() {\n    }\n}\n"], "mappings": "AAAA,SAASA,qBAAT,EAAgCC,qBAAhC,EAAuDC,cAAvD,QAA6E,mBAA7E;AACA,OAAO,SAASC,SAAT,CAAmBC,QAAnB,EAA6B;EAChC,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,iBAAJ,CAAsBH,QAAtB,CAAZ,CAAnB;AACH;;AACD,MAAMG,iBAAN,CAAwB;EACpBC,WAAW,CAACJ,QAAD,EAAW;IAClB,KAAKA,QAAL,GAAgBA,QAAhB;EACH;;EACDK,IAAI,CAACC,WAAD,EAAcL,MAAd,EAAsB;IACtB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,mBAAJ,CAAwBF,WAAxB,EAAqC,KAAKN,QAA1C,CAAjB,CAAP;EACH;;AANmB;;AAQxB,MAAMQ,mBAAN,SAAkCZ,qBAAlC,CAAwD;EACpDQ,WAAW,CAACE,WAAD,EAAcN,QAAd,EAAwB;IAC/B,MAAMM,WAAN;IACA,KAAKG,QAAL,GAAgB,KAAhB;IACA,MAAMC,eAAe,GAAG,IAAIb,qBAAJ,CAA0B,IAA1B,CAAxB;IACA,KAAKc,GAAL,CAASD,eAAT;IACA,KAAKE,iBAAL,GAAyBF,eAAzB;IACA,MAAME,iBAAiB,GAAGd,cAAc,CAACE,QAAD,EAAWU,eAAX,CAAxC;;IACA,IAAIE,iBAAiB,KAAKF,eAA1B,EAA2C;MACvC,KAAKC,GAAL,CAASC,iBAAT;MACA,KAAKA,iBAAL,GAAyBA,iBAAzB;IACH;EACJ;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACT,IAAI,KAAKL,QAAT,EAAmB;MACf,MAAMI,KAAN,CAAYC,KAAZ;IACH;EACJ;;EACDC,UAAU,GAAG;IACT,KAAKN,QAAL,GAAgB,IAAhB;;IACA,IAAI,KAAKG,iBAAT,EAA4B;MACxB,KAAKA,iBAAL,CAAuBI,WAAvB;IACH;EACJ;;EACDC,cAAc,GAAG,CAChB;;AAzBmD"}, "metadata": {}, "sourceType": "module"}