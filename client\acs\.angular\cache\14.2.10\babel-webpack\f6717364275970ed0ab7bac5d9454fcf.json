{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./dynamic-form-corporation.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./dynamic-form-corporation.component.css?ngResource\";\nimport { ChangeDetectorRef, Component, ViewChild } from '@angular/core';\nimport { FilingApiService } from '../../Services/FilingApiService';\nimport { StateSelectorComponent } from 'src/app/Modules/Shared/Components/formComponents/stateSelector/state-selector.component';\nimport { CorporationTypeSelectorComponent } from 'src/app/Modules/Shared/Components/formComponents/corporation-type-selector/corporation-type-selector.component';\nimport { FilingModeComponent } from 'src/app/Modules/Shared/Components/formComponents/filing-mode/filing-mode.component';\nimport { FormGroup, FormControl } from '@angular/forms';\nimport { forkJoin, interval } from 'rxjs';\nimport { ResetExcept } from 'src/app/Modules/Shared/functions/form-reset';\nimport { FormCorporation } from '../../Models/FormCorporation';\nimport { ToastrService } from 'ngx-toastr';\nimport { ActivatedRoute } from '@angular/router';\nimport { LoadFilingService } from '../../Functions/filing-service-load.func';\nimport { FilingServiceResponseObject } from '../../Enums/filing-service-enum';\nimport { FormModeService } from 'src/app/Modules/Shared/Services/Common/FormModeService';\nimport { CategoryCode } from 'src/app/Modules/Shared/Enums/category-code.enum';\nimport { SubCategoryCode } from 'src/app/Modules/Shared/Enums/sub-category-code.enum';\nimport { CustomSharedValidations } from 'src/app/Modules/Shared/functions/custom-validations';\nimport { FilingInfoService } from '../../Services/filing-price.service';\nimport { LoaderService } from '../../../../Modules/Core/Services/Common/loader.service';\nimport { StatePriceRequest } from 'src/app/Modules/Shared/Models/Common/state-price-request.model';\nimport { QuestionService } from 'src/app/Modules/Shared/Services/Common/question.service';\nimport { ComponentMessageService } from 'src/app/Modules/Core/Services/Common/component-message.service';\nimport { ComponentMessageModel } from 'src/app/Modules/Shared/Models/component-message.model';\nimport { SubjectEnums } from 'src/app/Modules/Shared/Enums/subject.enum';\nimport { PageTitleService } from 'src/app/Modules/Shared/Services/Common/page-title.service';\nlet DynamicFormCorporationComponent = class DynamicFormCorporationComponent {\n  constructor(isLoader, cdr, filingInfoService, Api, formMode, toaster, activatedRoute, service, ComponentMessags, pageTitleService, filingPriceService) {\n    this.isLoader = isLoader;\n    this.cdr = cdr;\n    this.filingInfoService = filingInfoService;\n    this.Api = Api;\n    this.formMode = formMode;\n    this.toaster = toaster;\n    this.activatedRoute = activatedRoute;\n    this.service = service;\n    this.ComponentMessags = ComponentMessags;\n    this.pageTitleService = pageTitleService;\n    this.filingPriceService = filingPriceService;\n    this.statesHavingDyanmicForms = ['NV', 'TX', 'DE', 'NY', 'FL', 'AZ', 'GA', 'WY', 'CO', 'OR', 'IL', 'AC', 'AL', 'AK', 'CT', 'HI', 'ID'];\n    this.selfFilingDynamicFormStates = [];\n    this.allStatesCode = [];\n    this.dynamicSOIQuestionData = [];\n    this.selfDraft = [];\n    this.acsDraft = [];\n    this.dyanmicControl2 = [];\n    this.dynamiApiControls = [];\n    this.counter = 0;\n    this.index = 0;\n    this.categoryCode = \"\";\n    this.formCorporationType = \"\";\n    this.isEdit = false;\n    this.onSaveClick = false;\n    this.isDataLoaded = false;\n    this.falseFlag = false;\n    this.upsellingCodes = {\n      SOIProductCode: 'FS',\n      SOICategoryCode: 'FOT',\n      SOISubCategoryCode: '901',\n      EINProductCode: 'FS',\n      EINCategoryCode: 'FOT',\n      EINSubCategoryCode: '907',\n      KitsProductCode: 'CK',\n      KitsCategoryCode: 'AI1',\n      KitsSubCategoryCode: 'AD'\n    };\n    this.dynamicFormUploadedFiles = [];\n    this.upSellingInputData = {\n      formationState: '',\n      kitType: 'CK',\n      isEdit: false,\n      formationType: '',\n      EntityName: '',\n      AllowedCommonShareCount: '',\n      IsEntityReserved: '',\n      ExpirationDate: ''\n    };\n    this.ClassUpSelling = \"col-md-12\";\n    this._IsEINSelected = false;\n    this._IsSOISelected = false;\n    this._IsCKSelected = false; //=====================\n    // @ViewChild(ServiceFormAComponent,{static:true}) ServiceFormAComponent :ServiceFormAComponent;\n    //\n\n    this.Yes = \"YES\";\n    this.No = \"NO\";\n    this.ProfessionalCorporation = \"PC\";\n    this.ProfitCorporation = \"FP\";\n    this.California = \"CA\";\n    this.Nevada = \"NV\";\n    this.FormCorporate = new FormCorporation();\n    this.EmployerIdentificationNo = null;\n    this.beneficialOwnershipInfo = null;\n    this.AnnualReport = null;\n    this.KitOrderVM = null;\n    this.ServiceType = [];\n    this.dependentControls = [];\n    this.UniqueFlowStates = [this.California]; //, this.Nevada]\n\n    this.dynamicFormMasterData = [];\n    this.Form = new FormGroup({\n      FormationState: new FormControl(null),\n      FormationType: new FormControl(null),\n      FilingMode: new FormControl(null),\n      ExpirationDate: new FormControl(null),\n      Remarks: new FormControl(null, [CustomSharedValidations.specialInstructions])\n    });\n  }\n\n  set UpsellingComponent(upsellingComponent) {\n    if (upsellingComponent) {\n      this.UpsellingComponentObj = upsellingComponent;\n    }\n  }\n\n  set FilingModeComponent(filingModeComponent) {\n    if (filingModeComponent) {\n      // this.cdr.detectChanges()\n      filingModeComponent.selectedState = this.selectedState;\n      this.FilingModeComponentObj = filingModeComponent;\n      this.FilingModeComponentObj.FormEditData = this.FormCorporate;\n      this.FilingModeComponentObj.$SendDynamicFormData.subscribe(data => {\n        this.onSave(data.dynamicData);\n      });\n    }\n  }\n\n  set UploadComponent(uploadComponent) {\n    if (uploadComponent) {\n      this.UploadComponentObj = uploadComponent;\n      this.UploadComponentObj.EmitUploadedData.subscribe(data => {\n        this.FileData = data;\n      }); //Mapping uploaded file data to FileUploadComponent in case of edit\n\n      if (this.FormCorporate.FileData) {\n        this.UploadComponentObj.uploadedFileList = this.FormCorporate.FileData;\n        this.UploadComponentObj.toggleUploadView = 2;\n        this.FileData = this.FormCorporate.FileData;\n      }\n    }\n  }\n\n  set DynamicFormComponent(dynamicFormComponent) {\n    if (dynamicFormComponent) {\n      this.DynamicFormComponentObj = dynamicFormComponent;\n    }\n  }\n\n  ngOnInit() {\n    this.filingPriceService.getLabel(this.filingInfoService.SubCatCode.formcorp, '001').subscribe(label => {\n      this.pageTitleService.setPageTitle(label);\n    });\n    this.Api.LoadDynamicControlsInCache().subscribe();\n    this.isLoader.show();\n    this.filingInfoService.getPrice(this.filingInfoService.SubCatCode.formcorp).subscribe(res => {\n      this.price = res;\n      this.basePrice = res;\n    });\n    this.Api.FCGetStates().subscribe(States => {\n      this.isLoader.hide();\n      this.States = States;\n      this.StateComponent.States = States;\n      this.mapStatesValue(this.States);\n      this.allStatesCode = this.StateComponent.States.map(x => x.stateCode);\n    });\n    const fillValues = forkJoin([this.Api.GetFormationType(SubCategoryCode.formpartnership, CategoryCode.formcorporation)]);\n    fillValues.subscribe(resp => {\n      this.typeOfCorporation = resp[0] || [];\n      this.StartLoad();\n    });\n    this.CorpTypeComponent.Caption = \"2. Please select the Type of Corporation. \"; // Display Corporation Type after state selection\n\n    this.StateComponent.$OnStateSelection.subscribe(SelectedState => {\n      this.isLoader.hide();\n      this.CorpTypeComponent.reset();\n      this.selectedState = SelectedState;\n      this.FilingModeComponentObj.reset();\n\n      if (SelectedState) {\n        this.upSellingInputData.formationState = SelectedState;\n        this.upSellingInputData.isEdit = this.formMode.IsEditMode;\n        this.CorpTypeComponent.IsVisible = true;\n        this.getStatePrice(SelectedState);\n      } else {\n        this.CorpTypeComponent.IsVisible = false;\n      }\n\n      if (this.typeOfCorporation) {\n        this.setCorporationTypes();\n      } else {\n        if (this.formChangeInterval) {\n          this.formChangeInterval.unsubscribe();\n        }\n\n        this.formChangeInterval = interval(100).subscribe(() => {\n          if (this.typeOfCorporation) {\n            this.setCorporationTypes();\n            this.formChangeInterval.unsubscribe();\n          } else {\n            console.log(\"Waiting for Corporation data to load...\");\n          }\n        });\n      }\n    });\n    this.CorpTypeComponent.$OnCorportationTypeSelection.subscribe(corporationType => {\n      this.formCorporationType = corporationType.trim();\n\n      if (this.formCorporationType) {\n        this.getForm();\n      }\n\n      this.upSellingInputData.formationType = this.formCorporationType;\n      this.FilingModeComponentObj.reset();\n      ResetExcept(this.Form, [\"FormationState\", \"FormationType\"]);\n      this.FilingModeComponentObj.filingServiceType = \"CORP\";\n      this.FilingModeComponentObj.downloadPDFForm(this.selectedState, CategoryCode.formcorporation, SubCategoryCode.formcorp);\n      this.FilingModeComponentObj.SelectedFilingType = null;\n      this.FilingModeComponentObj.TabLineNo = 3;\n      this.FilingModeComponentObj.Price = this.price;\n\n      if (this.UniqueFlowStates.includes(this.Form.controls.FormationState.value) || this.dynamicFormMasterData.length <= 0) {\n        if (!this.UniqueFlowStates.includes(this.Form.controls.FormationState.value) && this.formCorporationType) {\n          this.FilingModeComponentObj.SelectedFilingType = this.FilingModeComponentObj.FilingTypes.OfflineMode;\n        } else if (this.Form.controls.FormationState.value == 'NV' && this.formCorporationType != this.ProfitCorporation && this.formCorporationType) {\n          this.FilingModeComponentObj.SelectedFilingType = this.FilingModeComponentObj.FilingTypes.OfflineMode;\n        } else if (this.formCorporationType == this.ProfessionalCorporation.trim() || this.formCorporationType == this.ProfitCorporation.trim() && this.Form.controls.FormationState.value == this.California) {\n          this.FilingModeComponentObj.SelectedFilingType = this.FilingModeComponentObj.FilingTypes.OnlineMode;\n        } else if (this.Form.controls.FormationState.value == 'NV' && this.formCorporationType == this.ProfitCorporation) {\n          this.FilingModeComponentObj.SelectedFilingType = this.FilingModeComponentObj.FilingTypes.OnlineMode;\n        } else if (this.formCorporationType == \"NP\" && this.formCorporationType != this.ProfessionalCorporation.trim() && this.formCorporationType && this.Form.controls.FormationState.value == this.California) {\n          this.FilingModeComponentObj.SelectedFilingType = this.FilingModeComponentObj.FilingTypes.OfflineMode;\n        }\n      } else if (this.statesHavingDyanmicForms.includes(this.Form.controls.FormationState.value) && this.dynamicFormMasterData.length > 0) {\n        if (this.formCorporationType && this.formCorporationType == this.ProfitCorporation) {\n          this.FilingModeComponentObj.SelectedFilingType = this.FilingModeComponentObj.FilingTypes.OnlineMode;\n        } else {\n          this.FilingModeComponentObj.SelectedFilingType = this.FilingModeComponentObj.FilingTypes.OfflineMode;\n        }\n      } else {\n        this.FilingModeComponentObj.SelectedFilingType = this.FilingModeComponentObj.FilingTypes.OfflineMode;\n      }\n    });\n    this.Form.controls.FilingMode.valueChanges.subscribe(value => {\n      this.FilingModeComponentObj.SelectedFormationState = this.selectedState;\n\n      if (this.Form.controls.FilingMode.value != null && this.Form.controls.FilingMode.value.toString() != '' && this.Form.controls.FilingMode.value != undefined) {\n        const activeSectionMessage = new ComponentMessageModel();\n        activeSectionMessage.subject = SubjectEnums.filingModeCheck;\n        activeSectionMessage.payload = {\n          filingModeCheck: this.Form.controls.FilingMode.value\n        };\n        this.ComponentMessags.sendReplayMessage(activeSectionMessage);\n      }\n\n      if (this.Form.controls.FilingMode.value && this.Form.controls.FilingMode.value == 2) {\n        if (this.selfDraft.length > 1 && this.formCorporationType) {\n          this.dynamicFormMasterData = this.selfDraft;\n          this.getQuestions(this.selfDraft);\n        } else {\n          this.isLoader.show();\n          this.Api.GetDynamicFormMasterDataUpload('FS', '001', '010', this.selectedState, 'A', this.Form.controls.FormationType.value).subscribe(data => {\n            this.dynamicFormMasterData = data;\n            this.getQuestions(data);\n          });\n        }\n      } else if (this.Form.controls.FilingMode.value && this.Form.controls.FilingMode.value == 1) {\n        if (this.acsDraft.length > 1 && this.formCorporationType) {\n          this.dynamicFormMasterData = this.acsDraft;\n          this.getQuestions(this.acsDraft);\n        } else {\n          this.isLoader.show();\n          this.Api.GetDynamicFormMasterDataUpload('FS', 'EZ', '010', this.selectedState, 'C').subscribe(data => {\n            this.dynamicFormMasterData = data;\n            this.FilingModeComponentObj.isUploadVisible = true;\n            this.getQuestions(data);\n          });\n        }\n      }\n\n      this.categoryCode = this.Form.controls.FilingMode.value == 1 ? \"EZ\" : '001';\n\n      if (this.Form.controls.FilingMode.value) {\n        if (this.formCorporationType) {\n          if (this.categoryCode == 'EZ' && this.dyanmicControl2.length > 1) {\n            this.dependentControls = this.dyanmicControl2;\n          }\n\n          if (this.categoryCode == '001' && this.dynamiApiControls.length > 1) {\n            this.dependentControls = this.dynamiApiControls;\n          }\n        } else {\n          this.isLoader.show();\n          this.Api.GetAllDependentControlsData('FS', this.categoryCode, '010', this.selectedState, this.Form.controls.FormationType.value).subscribe(data => {\n            this.dependentControls = data;\n            this.isLoader.hide();\n          });\n        }\n      }\n    });\n  }\n\n  setCorporationTypes() {\n    if (this.selectedState !== \"CA\") {\n      const filteredArray = this.typeOfCorporation?.filter(corporationTypeCode => {\n        return corporationTypeCode.corporationTypeCode !== \"NP\";\n      });\n      this.CorpTypeComponent.CorporationTypes = filteredArray;\n    } else {\n      this.CorpTypeComponent.CorporationTypes = this.typeOfCorporation;\n    }\n\n    ResetExcept(this.Form, [\"FormationState\"]);\n  }\n\n  getQuestions(data) {\n    var dynamicFormData = this.selectedState == this.FormCorporate.FormationState ? this.FormCorporate.DynamicFormData : [];\n    this.questions$ = this.service.getMappedQuestions(data, dynamicFormData, this.selectedState);\n    this.isDataLoaded = true;\n    this.isLoader.hide();\n  }\n\n  getForm() {\n    this.isLoader.show();\n    const formLLC = forkJoin([this.Api.GetDynamicFormMasterDataUpload('FS', '001', '010', this.selectedState, 'A', this.formCorporationType), this.Api.GetDynamicFormMasterDataUpload('FS', 'EZ', '010', this.selectedState, 'C'), this.Api.GetAllDependentControlsData('FS', 'EZ', '010', this.selectedState, this.formCorporationType), this.Api.GetAllDependentControlsData('FS', '001', '010', this.selectedState, this.formCorporationType)]);\n    formLLC.subscribe(response => {\n      this.selfDraft = response[0];\n      this.acsDraft = response[1];\n      this.dyanmicControl2 = response[2];\n      this.dynamiApiControls = response[3];\n      this.isLoader.hide();\n    });\n  }\n\n  mapStatesValue(states) {\n    this.States = states.map(x => {\n      Value: x.stateCode;\n\n      Text: x.stateName;\n\n      return x;\n    });\n  }\n\n  StartLoad() {\n    var _this = this;\n\n    this.isLoader.show();\n    this.activatedRoute.queryParams.subscribe( /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (queryString) {\n        if (queryString.on) {//this.isAdd = false;\n        }\n\n        LoadFilingService(queryString, _this.Api, FilingServiceResponseObject.DynamicFormCorporation, _this.formMode).then(serviceData => {\n          _this.FormCorporate = serviceData;\n\n          _this.Load();\n        }).catch(e => console.log(e));\n      });\n\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  }\n\n  updateValidityState() {\n    this.Form.updateValueAndValidity();\n  }\n\n  onSave(dynamicFormData) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!dynamicFormData && _this2.DynamicFormComponentObj) {\n        var enteredDynamicFormData = yield _this2.DynamicFormComponentObj.getDynamicFormData();\n      }\n\n      try {\n        if (!_this2.selfFilingDynamicFormStates.includes(_this2.selectedState) && _this2.Form.controls.FilingMode.value == 2 || _this2.allStatesCode.includes(_this2.Form.controls.FormationState.value) && _this2.Form.controls.FilingMode.value == 1 || _this2.Form.controls.FormationState.value == \"CA\") {\n          yield _this2.UpSellingDataCreation(enteredDynamicFormData);\n        }\n\n        if (_this2.Form.valid) {\n          _this2.onSaveClick = true;\n          setTimeout(() => {\n            _this2.Save(dynamicFormData || enteredDynamicFormData);\n          }, 2500);\n        }\n\n        return false;\n      } catch (ex) {\n        console.error(ex);\n      }\n    })();\n  }\n\n  UpSellingDataCreation(DynamicFormDataValues) {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      let upSellingData = [];\n      let resolver;\n      let respo = new Promise(res => resolver = res);\n      let services = [];\n\n      if (_this3.UpsellingComponentObj.UpSelling_BOI) {\n        upSellingData.push(_this3.UpsellingComponentObj.UpSelling_BOI.getUpSellingData());\n        services.push(\"BOI\");\n      }\n\n      if (_this3.UpsellingComponentObj.UpSelling_EIN) {\n        yield _this3.getDependentControlsData(_this3.upsellingCodes.EINProductCode, _this3.upsellingCodes.EINCategoryCode, _this3.upsellingCodes.EINSubCategoryCode).then(data => {\n          DynamicFormDataValues.keyValuePair.map(mainCtrl => {\n            data.map(dependentCtrl => {\n              if (mainCtrl.ControlId == dependentCtrl.controlId) {\n                var formcontrol = dependentCtrl.dependentControlId;\n\n                if (dependentCtrl.dependentControlId == 'Expiration_Date') {\n                  let date = new Date(data.payload.value);\n\n                  _this3.UpsellingComponentObj.UpSelling_EIN.Form.controls.file_date_y.setValue(date.getDate());\n\n                  _this3.UpsellingComponentObj.UpSelling_EIN.Form.controls.file_date_m.setValue(date.getMonth() + 1);\n\n                  _this3.UpsellingComponentObj.UpSelling_EIN.Form.controls.file_date_d.setValue(date.getFullYear());\n                } else {\n                  _this3.UpsellingComponentObj.UpSelling_EIN.Form.get(formcontrol).setValue(mainCtrl.Value);\n                }\n              }\n            });\n          });\n        }).then(() => {\n          upSellingData.push(_this3.UpsellingComponentObj.UpSelling_EIN.getUpSellingData());\n          services.push(\"EIN\");\n        });\n      }\n\n      if (_this3.UpsellingComponentObj.UpSelling_SOI) {\n        yield _this3.getDependentControlsData(_this3.upsellingCodes.SOIProductCode, _this3.upsellingCodes.SOICategoryCode, _this3.upsellingCodes.SOISubCategoryCode).then(data => {\n          DynamicFormDataValues.keyValuePair.map(mainCtrl => {\n            data.map(dependentControl => {\n              if (mainCtrl.ControlId == dependentControl.controlId && mainCtrl.Value) {\n                let formcontrol = dependentControl.dependentControlId;\n\n                _this3.UpsellingComponentObj.UpSelling_SOI.Form.get(formcontrol).setValue(mainCtrl.Value);\n              }\n            });\n          });\n        }).then(() => {\n          upSellingData.push(_this3.UpsellingComponentObj.UpSelling_SOI.getUpSellingData());\n          services.push(\"SOI\");\n        });\n      }\n\n      if (_this3.UpsellingComponentObj.UpSelling_CK) {\n        yield _this3.getDependentControlsData(_this3.upsellingCodes.KitsProductCode, _this3.upsellingCodes.KitsCategoryCode, _this3.upsellingCodes.KitsSubCategoryCode).then(data => {\n          DynamicFormDataValues.keyValuePair.map(mainCtrl => {\n            data.map(depCtrl => {\n              if (mainCtrl.ControlId == depCtrl.controlId) {\n                let formcontrol = depCtrl.dependentControlId;\n\n                if (depCtrl.dependentControlId == 'EntityName') {\n                  _this3.UpsellingComponentObj.UpSelling_CK.Form1.get(formcontrol).setValue(mainCtrl.Value);\n                } else if (depCtrl.dependentControlId != 'EntityName') {\n                  if (depCtrl.dependentControlId == 'FilingStatus' && mainCtrl.Value == 'NO') {\n                    _this3.UpsellingComponentObj.UpSelling_CK.Form2.get(formcontrol).setValue(false);\n                  } else if (depCtrl.dependentControlId == 'Expiration_Date') {\n                    let date = new Date(data.payload.value);\n\n                    _this3.UpsellingComponentObj.UpSelling_CK.Form2.controls.FormationDate.setValue(date.getDate());\n\n                    _this3.UpsellingComponentObj.UpSelling_CK.Form2.controls.FormationMonth.setValue(date.getMonth() + 1);\n\n                    _this3.UpsellingComponentObj.UpSelling_CK.Form2.controls.FormationYear.setValue(date.getFullYear());\n                  } else if (depCtrl.dependentControlId != 'FilingStatus') {\n                    _this3.UpsellingComponentObj.UpSelling_CK.Form2.get(formcontrol).setValue(mainCtrl.Value);\n                  } else if (depCtrl.dependentControlId == 'FilingStatus' && mainCtrl.Value == 'YES') {\n                    _this3.UpsellingComponentObj.UpSelling_CK.Form2.get(formcontrol).setValue(true);\n                  }\n                }\n              }\n            });\n          });\n        }).then(() => {\n          upSellingData.push(_this3.UpsellingComponentObj.UpSelling_CK.getUpSellingData());\n          services.push(\"CK\");\n        });\n      }\n\n      yield _this3.mapUpselling(upSellingData, services);\n      return resolver(...upSellingData, ...services);\n    })();\n  }\n\n  mapUpselling(upSellingData, services) {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* () {\n      return services.length > 0 && Promise.all([...upSellingData, ...services]).then(response => {\n        if (upSellingData.length <= 0 && services.length <= 0) {\n          return;\n        }\n\n        let index_BOI = _this4.getIndexNo(\"BOI\", services);\n\n        let index_EIN = _this4.getIndexNo(\"EIN\", services);\n\n        let index_SOI = _this4.getIndexNo(\"SOI\", services);\n\n        let index_CK = _this4.getIndexNo(\"CK\", services);\n\n        if (index_BOI != null) {\n          ///To be sent to parent component\n          _this4.beneficialOwnershipInfo = response[index_BOI];\n        }\n\n        if (index_EIN != null) {\n          ///To be sent to parent component\n          _this4.EmployerIdentificationNo = response[index_EIN];\n        }\n\n        if (index_SOI != null) {\n          ///To be sent to parent component\n          _this4.AnnualReport = response[index_SOI];\n        }\n\n        if (index_CK != null) {\n          ///To be sent to parent component\n          _this4.KitOrderVM = response[index_CK];\n        }\n\n        return;\n      });\n    })();\n  }\n\n  upsellingSetvalueCheck(data, ControlValue) {\n    return data.payload && data.payload.value && data.payload.name == ControlValue;\n  }\n\n  getIndexNo(code, array) {\n    let val = array.findIndex(x => x == code);\n\n    if (val == -1) {\n      return null;\n    }\n\n    return val;\n  }\n\n  componentsVisibility() {\n    return this.Form.controls.FormationType.value && this.Form.controls.FilingMode.value && (this.Form.controls.FilingMode.value == 2 && !this.selfFilingDynamicFormStates.includes(this.selectedState) && this.Form.controls.FormationType.value != 'NP' || this.Form.controls.FilingMode.value == 1 && this.allStatesCode.includes(this.selectedState) || this.selectedState == 'CA' || this.selectedState == 'ID');\n  }\n\n  Save(dynamicFormData) {\n    this.FormCorporate.FormationState = this.Form.controls.FormationState.value;\n    this.FormCorporate.FormationType = this.Form.controls.FormationType.value;\n    this.FormCorporate.FilingMode = this.Form.controls.FilingMode.value;\n    this.FormCorporate.Remarks = this.Form.controls.Remarks.value != null ? this.Form.controls.Remarks.value : null;\n\n    if (this.FileData) {\n      this.FormCorporate.FileData = this.FileData;\n    } else if (this.selfFilingDynamicFormStates?.includes(this.Form.controls.FormationState.value)) {\n      this.FormCorporate.FileData = [];\n    } else if (this.Form.controls.FilingMode.value == 1) {\n      this.toaster.error('File upload required.');\n      this.onSaveClick = false;\n      return;\n    }\n\n    if (dynamicFormData) {\n      this.FormCorporate.DynamicFormData = dynamicFormData.keyValuePair || [];\n    } else {\n      this.FormCorporate.DynamicFormData = [];\n    }\n\n    let filingService = {\n      dynamicFormCorporation: this.FormCorporate,\n      upSelling: {\n        BeneficialOwnerInformation: this.beneficialOwnershipInfo,\n        annualReport: this.AnnualReport,\n        employerIdentificationNumber: this.EmployerIdentificationNo,\n        corporateKit: this.KitOrderVM\n      },\n      productCode: 'FS',\n      categoryCode: this.categoryCode,\n      subCategoryCode: '010',\n      optionCode: \"NA\",\n      dynamicFormUploadedFiles: this.dynamicFormUploadedFiles\n    };\n    this.Api.SaveDynamicFormFilingService(filingService).subscribe(x => {\n      this.Form.reset();\n    });\n  }\n\n  fileUplaod(files) {\n    this.dynamicFormUploadedFiles = files;\n  }\n\n  Load() {\n    this.getStatePrice(this.FormCorporate.FormationState);\n    this.Form.controls.FormationState.setValue(this.FormCorporate.FormationState);\n    this.Form.controls.FormationType.setValue(this.FormCorporate.FormationType.trim());\n    this.FilingModeComponentObj.filingServiceType = \"CORP\";\n\n    if (this.FormCorporate.CategoryCode == 'EZ') {\n      this.isEdit = true;\n      this.Form.controls.FilingMode.setValue(1);\n      this.FilingModeComponentObj.SelectedOnlineMethodChange(1);\n    } else {\n      this.isEdit = true;\n      this.Form.controls.FilingMode.setValue(2);\n      this.FilingModeComponentObj.SelectedOnlineMethodChange(2);\n    }\n\n    this.Form.controls.Remarks.setValue(this.FormCorporate.Remarks);\n  }\n\n  ResetForm() {\n    this.Form.reset();\n  }\n\n  getStatePrice(state) {\n    var request = new StatePriceRequest();\n    request.ProductCode = 'FS';\n    request.CategoryCode = '001';\n    request.SubCategoryCode = '030';\n    request.State = state;\n    this.Api.GetStateWisePrice(request).subscribe(res => {\n      this.price = res.price > 0 ? res.price : this.basePrice;\n    });\n  }\n\n  getDependentControlsData(depProductCode, depCategoryCode, depSubcategoryCode) {\n    var req = {\n      state: this.selectedState,\n      productCode: 'FS',\n      categoryCode: this.Form.controls.FilingMode.value == 1 ? \"EZ\" : '001',\n      subCategoryCode: '010',\n      entityType: this.Form.controls.FormationType.value,\n      dependentProductCode: depProductCode,\n      dependentCategoryCode: depCategoryCode,\n      dependentSubcategoryCode: depSubcategoryCode\n    };\n    return this.Api.GetDependentControlsData(req);\n  }\n\n  callSubmit() {\n    if (this.Form.controls.FilingMode.value == 2) {\n      document.getElementById('__Main1').click();\n    } else {\n      this.FilingModeComponentObj.uploadFormSubmit();\n    }\n  }\n\n  onFocusOutEvent(data, eve) {\n    if (data == 'Expiration_Date') {\n      var queData = new Date(eve.target.value);\n    } else {\n      queData = eve.target.value;\n    }\n\n    const activeSectionMessage = new ComponentMessageModel();\n    activeSectionMessage.payload = {\n      name: data,\n      value: queData\n    };\n    this.ComponentMessags.sendReplayMessage(activeSectionMessage);\n  }\n\n  ngOnDestroy() {\n    // when the component get's destroyed, unsubscribe all the subscriptions\n    if (this.routesSubscription) {\n      this.routesSubscription.unsubscribe();\n    }\n\n    if (this.formChangeInterval) {\n      this.formChangeInterval.unsubscribe();\n    }\n  }\n\n};\n\nDynamicFormCorporationComponent.ctorParameters = () => [{\n  type: LoaderService\n}, {\n  type: ChangeDetectorRef\n}, {\n  type: FilingInfoService\n}, {\n  type: FilingApiService\n}, {\n  type: FormModeService\n}, {\n  type: ToastrService\n}, {\n  type: ActivatedRoute\n}, {\n  type: QuestionService\n}, {\n  type: ComponentMessageService\n}, {\n  type: PageTitleService\n}, {\n  type: FilingInfoService\n}];\n\nDynamicFormCorporationComponent.propDecorators = {\n  UpsellingComponent: [{\n    type: ViewChild,\n    args: ['UpsellingComponent', {\n      static: false\n    }]\n  }],\n  StateComponent: [{\n    type: ViewChild,\n    args: [StateSelectorComponent, {\n      static: true\n    }]\n  }],\n  CorpTypeComponent: [{\n    type: ViewChild,\n    args: [CorporationTypeSelectorComponent, {\n      static: true\n    }]\n  }],\n  FilingModeComponent: [{\n    type: ViewChild,\n    args: [FilingModeComponent, {\n      static: true\n    }]\n  }],\n  UploadComponent: [{\n    type: ViewChild,\n    args: ['UploadComponent', {\n      static: false\n    }]\n  }],\n  DynamicFormComponent: [{\n    type: ViewChild,\n    args: ['DynamicFormComponent', {\n      static: false\n    }]\n  }]\n};\nDynamicFormCorporationComponent = __decorate([Component({\n  selector: 'app-dynamic-form-corporation',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [QuestionService],\n  styles: [__NG_CLI_RESOURCE__1]\n})], DynamicFormCorporationComponent);\nexport { DynamicFormCorporationComponent };", "map": {"version": 3, "mappings": ";;;;AAAA,SAASA,iBAAT,EAA4BC,SAA5B,EAA+CC,SAA/C,QAAgE,eAAhE;AACA,SAASC,gBAAT,QAAiC,iCAAjC;AACA,SAASC,sBAAT,QAAuC,yFAAvC;AACA,SAASC,gCAAT,QAAiD,gHAAjD;AACA,SAASC,mBAAT,QAAoC,oFAApC;AACA,SAASC,SAAT,EAAoBC,WAApB,QAAuC,gBAAvC;AACA,SAASC,QAAT,EAAmBC,QAAnB,QAA6D,MAA7D;AACA,SAASC,WAAT,QAA4B,6CAA5B;AACA,SAASC,eAAT,QAAgC,8BAAhC;AACA,SAASC,aAAT,QAA8B,YAA9B;AACA,SAASC,cAAT,QAA+B,iBAA/B;AACA,SAASC,iBAAT,QAAkC,0CAAlC;AACA,SAASC,2BAAT,QAA4C,iCAA5C;AACA,SAASC,eAAT,QAAgC,wDAAhC;AACA,SAASC,YAAT,QAA6B,iDAA7B;AACA,SAASC,eAAT,QAAgC,qDAAhC;AACA,SAASC,uBAAT,QAAwC,qDAAxC;AAGA,SAASC,iBAAT,QAAkC,qCAAlC;AACA,SAASC,aAAT,QAA8B,yDAA9B;AAEA,SAASC,iBAAT,QAAkC,gEAAlC;AAEA,SAASC,eAAT,QAAgC,yDAAhC;AAGA,SAASC,uBAAT,QAAwC,gEAAxC;AACA,SAASC,qBAAT,QAAsC,uDAAtC;AACA,SAASC,YAAT,QAA6B,2CAA7B;AAIA,SAASC,gBAAT,QAAiC,2DAAjC;IAQaC,+BAA+B,SAA/BA,+BAA+B;EA6B1CC,YAAoBC,QAApB,EAAqDC,GAArD,EAAoFC,iBAApF,EACUC,GADV,EACwCC,QADxC,EAC2EC,OAD3E,EAEUC,cAFV,EAEkDC,OAFlD,EAEoFC,gBAFpF,EAGUC,gBAHV,EAGqDC,kBAHrD,EAG0F;IAHtE;IAAiC;IAA+B;IAC1E;IAA8B;IAAmC;IACjE;IAAwC;IAAkC;IAC1E;IAA2C;IA7BrD,gCAA2B,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,EAAyB,IAAzB,EAA+B,IAA/B,EAAqC,IAArC,EAA2C,IAA3C,EAAiD,IAAjD,EAAuD,IAAvD,EAA6D,IAA7D,EAAmE,IAAnE,EAAyE,IAAzE,EAA+E,IAA/E,EAAqF,IAArF,EAA2F,IAA3F,EAAiG,IAAjG,CAA3B;IACA,mCAA8B,EAA9B;IACA,qBAAuB,EAAvB;IAEA,8BAAgC,EAAhC;IACA,iBAAmB,EAAnB;IACA,gBAAkB,EAAlB;IACA,uBAAyB,EAAzB;IACA,yBAA2B,EAA3B;IACA,eAAkB,CAAlB;IACA,aAAgB,CAAhB;IACA,oBAAuB,EAAvB;IACA,2BAA8B,EAA9B;IAEA,cAAkB,KAAlB;IACA,mBAAuB,KAAvB;IACA,oBAAwB,KAAxB;IACA,iBAAqB,KAArB;IACA,sBAAsB;MACpBC,cAAc,EAAE,IADI;MACEC,eAAe,EAAE,KADnB;MAC0BC,kBAAkB,EAAE,KAD9C;MAEpBC,cAAc,EAAE,IAFI;MAEEC,eAAe,EAAE,KAFnB;MAE0BC,kBAAkB,EAAE,KAF9C;MAGpBC,eAAe,EAAE,IAHG;MAGGC,gBAAgB,EAAE,KAHrB;MAG4BC,mBAAmB,EAAE;IAHjD,CAAtB;IAOA,gCAAmC,EAAnC;IAoDA,0BAA0B;MACxBC,cAAc,EAAE,EADQ;MAExBC,OAAO,EAAE,IAFe;MAGxBC,MAAM,EAAE,KAHgB;MAIxBC,aAAa,EAAE,EAJS;MAKxBC,UAAU,EAAE,EALY;MAMxBC,uBAAuB,EAAE,EAND;MAOxBC,gBAAgB,EAAE,EAPM;MAQxBC,cAAc,EAAE;IARQ,CAA1B;IAaA,sBAAyB,WAAzB;IAEA,sBAAiB,KAAjB;IACA,sBAAiB,KAAjB;IACA,qBAAgB,KAAhB,CAjE0F,CAmE1F;IACA;IACA;;IAEA,WAAc,KAAd;IACA,UAAa,IAAb;IACA,+BAA0B,IAA1B;IACA,yBAAoB,IAApB;IACA,kBAAa,IAAb;IACA,cAAS,IAAT;IAIA,qBAAiC,IAAI9C,eAAJ,EAAjC;IACA,gCAAyD,IAAzD;IACA,+BAA+B,IAA/B;IACA,oBAAuC,IAAvC;IACA,kBAA4B,IAA5B;IACA,mBAAqB,EAArB;IAEA,yBAA2B,EAA3B;IAEA,wBAAmB,CAAC,KAAK+C,UAAN,CAAnB,CAzF0F,CAyFrD;;IAIrC,6BAA+B,EAA/B;IA8OA,YAAO,IAAIpD,SAAJ,CAAc;MACnBqD,cAAc,EAAE,IAAIpD,WAAJ,CAA+B,IAA/B,CADG;MAEnBqD,aAAa,EAAE,IAAIrD,WAAJ,CAA+B,IAA/B,CAFI;MAGnBsD,UAAU,EAAE,IAAItD,WAAJ,CAA+B,IAA/B,CAHO;MAInBkD,cAAc,EAAE,IAAIlD,WAAJ,CAA+B,IAA/B,CAJG;MAKnBuD,OAAO,EAAE,IAAIvD,WAAJ,CAA+B,IAA/B,EAAqC,CAACY,uBAAuB,CAAC4C,mBAAzB,CAArC;IALU,CAAd,CAAP;EA1UK;;EAKqE,IAAlBC,kBAAkB,CAACC,kBAAD,EAAuC;IAC/G,IAAIA,kBAAJ,EAAwB;MACtB,KAAKC,qBAAL,GAA6BD,kBAA7B;IACD;EACF;;EAIwE,IAAnB5D,mBAAmB,CAAC8D,mBAAD,EAAyC;IAChH,IAAIA,mBAAJ,EAAyB;MACvB;MACAA,mBAAmB,CAACC,aAApB,GAAoC,KAAKA,aAAzC;MACA,KAAKC,sBAAL,GAA8BF,mBAA9B;MACA,KAAKE,sBAAL,CAA4BC,YAA5B,GAA2C,KAAKC,aAAhD;MACA,KAAKF,sBAAL,CAA4BG,oBAA5B,CAAiDC,SAAjD,CAA2DC,IAAI,IAAG;QAChE,KAAKC,MAAL,CAAYD,IAAI,CAACE,WAAjB;MACD,CAFD;IAGD;EACF;;EAGmE,IAAfC,eAAe,CAACC,eAAD,EAAuC;IACzG,IAAIA,eAAJ,EAAqB;MACnB,KAAKC,kBAAL,GAA0BD,eAA1B;MACA,KAAKC,kBAAL,CAAwBC,gBAAxB,CAAyCP,SAAzC,CAAmDC,IAAI,IAAG;QACxD,KAAKO,QAAL,GAAgBP,IAAhB;MACD,CAFD,EAFmB,CAKnB;;MACA,IAAI,KAAKH,aAAL,CAAmBU,QAAvB,EAAiC;QAC/B,KAAKF,kBAAL,CAAwBG,gBAAxB,GAA2C,KAAKX,aAAL,CAAmBU,QAA9D;QACA,KAAKF,kBAAL,CAAwBI,gBAAxB,GAA2C,CAA3C;QACA,KAAKF,QAAL,GAAgB,KAAKV,aAAL,CAAmBU,QAAnC;MACD;IACF;EACF;;EAE6E,IAApBG,oBAAoB,CAACC,oBAAD,EAA2C;IACvH,IAAIA,oBAAJ,EAA0B;MACxB,KAAKC,uBAAL,GAA+BD,oBAA/B;IACD;EACF;;EAmDDE,QAAQ;IACN,KAAK/C,kBAAL,CACGgD,QADH,CACY,KAAKxD,iBAAL,CAAuByD,UAAvB,CAAkCC,QAD9C,EACwD,KADxD,EAC+DjB,SAD/D,CACyEkB,KAAK,IAAG;MAC7E,KAAKpD,gBAAL,CAAsBqD,YAAtB,CAAmCD,KAAnC;IACD,CAHH;IAIA,KAAK1D,GAAL,CAAS4D,0BAAT,GAAsCpB,SAAtC;IACA,KAAK3C,QAAL,CAAcgE,IAAd;IACA,KAAK9D,iBAAL,CAAuB+D,QAAvB,CAAgC,KAAK/D,iBAAL,CAAuByD,UAAvB,CAAkCC,QAAlE,EAA4EjB,SAA5E,CAAsFuB,GAAG,IAAG;MAC1F,KAAKC,KAAL,GAAaD,GAAb;MACA,KAAKE,SAAL,GAAiBF,GAAjB;IACD,CAHD;IAKA,KAAK/D,GAAL,CAASkE,WAAT,GAAuB1B,SAAvB,CAAkC2B,MAAD,IAAW;MAC1C,KAAKtE,QAAL,CAAcuE,IAAd;MACA,KAAKD,MAAL,GAAcA,MAAd;MACA,KAAKE,cAAL,CAAoBF,MAApB,GAA6BA,MAA7B;MACA,KAAKG,cAAL,CAAoB,KAAKH,MAAzB;MACA,KAAKI,aAAL,GAAqB,KAAKF,cAAL,CAAoBF,MAApB,CAA2BK,GAA3B,CAA+BC,CAAC,IAAIA,CAAC,CAACC,SAAtC,CAArB;IACD,CAND;IAQA,MAAMC,UAAU,GAAGpG,QAAQ,CAAC,CAC1B,KAAKyB,GAAL,CAAS4E,gBAAT,CAA0B3F,eAAe,CAAC4F,eAA1C,EAA2D7F,YAAY,CAAC8F,eAAxE,CAD0B,CAAD,CAA3B;IAIAH,UAAU,CAACnC,SAAX,CAAqBuC,IAAI,IAAG;MAC1B,KAAKC,iBAAL,GAAyBD,IAAI,CAAC,CAAD,CAAJ,IAAW,EAApC;MACA,KAAKE,SAAL;IACD,CAHD;IAKA,KAAKC,iBAAL,CAAuBC,OAAvB,GAAiC,4CAAjC,CA7BM,CA+BN;;IACA,KAAKd,cAAL,CAAoBe,iBAApB,CAAsC5C,SAAtC,CAAgD6C,aAAa,IAAG;MAC9D,KAAKxF,QAAL,CAAcuE,IAAd;MACA,KAAKc,iBAAL,CAAuBI,KAAvB;MACA,KAAKnD,aAAL,GAAqBkD,aAArB;MACA,KAAKjD,sBAAL,CAA4BkD,KAA5B;;MACA,IAAID,aAAJ,EAAmB;QACjB,KAAKE,kBAAL,CAAwBtE,cAAxB,GAAyCoE,aAAzC;QACA,KAAKE,kBAAL,CAAwBpE,MAAxB,GAAiC,KAAKlB,QAAL,CAAcuF,UAA/C;QACA,KAAKN,iBAAL,CAAuBO,SAAvB,GAAmC,IAAnC;QACA,KAAKC,aAAL,CAAmBL,aAAnB;MACD,CALD,MAKO;QACL,KAAKH,iBAAL,CAAuBO,SAAvB,GAAmC,KAAnC;MACD;;MAED,IAAI,KAAKT,iBAAT,EAA4B;QAC1B,KAAKW,mBAAL;MACD,CAFD,MAEO;QACL,IAAI,KAAKC,kBAAT,EAA6B;UAC3B,KAAKA,kBAAL,CAAwBC,WAAxB;QACD;;QAED,KAAKD,kBAAL,GAA0BpH,QAAQ,CAAC,GAAD,CAAR,CAAcgE,SAAd,CAAwB,MAAK;UACrD,IAAI,KAAKwC,iBAAT,EAA4B;YAC1B,KAAKW,mBAAL;YACA,KAAKC,kBAAL,CAAwBC,WAAxB;UACD,CAHD,MAGO;YACLC,OAAO,CAACC,GAAR,CAAY,yCAAZ;UACD;QACF,CAPyB,CAA1B;MAQD;IACF,CA9BD;IAgCA,KAAKb,iBAAL,CAAuBc,4BAAvB,CAAoDxD,SAApD,CAA8DyD,eAAe,IAAG;MAC9E,KAAKC,mBAAL,GAA2BD,eAAe,CAACE,IAAhB,EAA3B;;MACA,IAAI,KAAKD,mBAAT,EAA8B;QAC5B,KAAKE,OAAL;MACD;;MACD,KAAKb,kBAAL,CAAwBnE,aAAxB,GAAwC,KAAK8E,mBAA7C;MACA,KAAK9D,sBAAL,CAA4BkD,KAA5B;MACA7G,WAAW,CAAC,KAAK4H,IAAN,EAAY,CAAC,gBAAD,EAAmB,eAAnB,CAAZ,CAAX;MACA,KAAKjE,sBAAL,CAA4BkE,iBAA5B,GAAgD,MAAhD;MACA,KAAKlE,sBAAL,CAA4BmE,eAA5B,CAA4C,KAAKpE,aAAjD,EAAgEnD,YAAY,CAAC8F,eAA7E,EAA8F7F,eAAe,CAACwE,QAA9G;MACA,KAAKrB,sBAAL,CAA4BoE,kBAA5B,GAAiD,IAAjD;MACA,KAAKpE,sBAAL,CAA4BqE,SAA5B,GAAwC,CAAxC;MACA,KAAKrE,sBAAL,CAA4BsE,KAA5B,GAAoC,KAAK1C,KAAzC;;MACA,IAAI,KAAK2C,gBAAL,CAAsBC,QAAtB,CAA+B,KAAKP,IAAL,CAAUQ,QAAV,CAAmBnF,cAAnB,CAAkCoF,KAAjE,KAA2E,KAAKC,qBAAL,CAA2BC,MAA3B,IAAqC,CAApH,EAAuH;QACrH,IAAI,CAAC,KAAKL,gBAAL,CAAsBC,QAAtB,CAA+B,KAAKP,IAAL,CAAUQ,QAAV,CAAmBnF,cAAnB,CAAkCoF,KAAjE,CAAD,IAA4E,KAAKZ,mBAArF,EAA0G;UACxG,KAAK9D,sBAAL,CAA4BoE,kBAA5B,GAAiD,KAAKpE,sBAAL,CAA4B6E,WAA5B,CAAwCC,WAAzF;QACD,CAFD,MAGK,IAAI,KAAKb,IAAL,CAAUQ,QAAV,CAAmBnF,cAAnB,CAAkCoF,KAAlC,IAA2C,IAA3C,IAAmD,KAAKZ,mBAAL,IAA4B,KAAKiB,iBAApF,IAAyG,KAAKjB,mBAAlH,EAAuI;UAC1I,KAAK9D,sBAAL,CAA4BoE,kBAA5B,GAAiD,KAAKpE,sBAAL,CAA4B6E,WAA5B,CAAwCC,WAAzF;QACD,CAFI,MAGA,IAAI,KAAKhB,mBAAL,IAA4B,KAAKkB,uBAAL,CAA6BjB,IAA7B,EAA5B,IAAoE,KAAKD,mBAAL,IAA4B,KAAKiB,iBAAL,CAAuBhB,IAAvB,EAA5B,IAA6D,KAAKE,IAAL,CAAUQ,QAAV,CAAmBnF,cAAnB,CAAkCoF,KAAlC,IAA2C,KAAKrF,UAArL,EAAkM;UACrM,KAAKW,sBAAL,CAA4BoE,kBAA5B,GAAiD,KAAKpE,sBAAL,CAA4B6E,WAA5B,CAAwCI,UAAzF;QACD,CAFI,MAGA,IAAI,KAAKhB,IAAL,CAAUQ,QAAV,CAAmBnF,cAAnB,CAAkCoF,KAAlC,IAA2C,IAA3C,IAAmD,KAAKZ,mBAAL,IAA4B,KAAKiB,iBAAxF,EAA2G;UAC9G,KAAK/E,sBAAL,CAA4BoE,kBAA5B,GAAiD,KAAKpE,sBAAL,CAA4B6E,WAA5B,CAAwCI,UAAzF;QACD,CAFI,MAGA,IAAK,KAAKnB,mBAAL,IAA4B,IAA5B,IAAoC,KAAKA,mBAAL,IAA4B,KAAKkB,uBAAL,CAA6BjB,IAA7B,EAAhE,IAAuG,KAAKD,mBAA5G,IAAmI,KAAKG,IAAL,CAAUQ,QAAV,CAAmBnF,cAAnB,CAAkCoF,KAAlC,IAA2C,KAAKrF,UAAxL,EAAqM;UACxM,KAAKW,sBAAL,CAA4BoE,kBAA5B,GAAiD,KAAKpE,sBAAL,CAA4B6E,WAA5B,CAAwCC,WAAzF;QACD;MACF,CAhBD,MAiBK,IAAI,KAAKI,wBAAL,CAA8BV,QAA9B,CAAuC,KAAKP,IAAL,CAAUQ,QAAV,CAAmBnF,cAAnB,CAAkCoF,KAAzE,KAAmF,KAAKC,qBAAL,CAA2BC,MAA3B,GAAoC,CAA3H,EAA8H;QACjI,IAAI,KAAKd,mBAAL,IAA4B,KAAKA,mBAAL,IAA4B,KAAKiB,iBAAjE,EAAoF;UAClF,KAAK/E,sBAAL,CAA4BoE,kBAA5B,GAAiD,KAAKpE,sBAAL,CAA4B6E,WAA5B,CAAwCI,UAAzF;QACD,CAFD,MAGK;UACH,KAAKjF,sBAAL,CAA4BoE,kBAA5B,GAAiD,KAAKpE,sBAAL,CAA4B6E,WAA5B,CAAwCC,WAAzF;QACD;MACF,CAPI,MAOE;QACL,KAAK9E,sBAAL,CAA4BoE,kBAA5B,GAAiD,KAAKpE,sBAAL,CAA4B6E,WAA5B,CAAwCC,WAAzF;MACD;IACF,CAxCD;IA0CA,KAAKb,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8B2F,YAA9B,CAA2C/E,SAA3C,CAAqDsE,KAAK,IAAG;MAC3D,KAAK1E,sBAAL,CAA4BoF,sBAA5B,GAAqD,KAAKrF,aAA1D;;MACA,IAAI,KAAKkE,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BkF,KAA9B,IAAuC,IAAvC,IAAgD,KAAKT,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BkF,KAA/B,CAAsCW,QAAtC,MAAoD,EAAnG,IAAyG,KAAKpB,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BkF,KAA9B,IAAuCY,SAApJ,EAA+J;QAC7J,MAAMC,oBAAoB,GAA0B,IAAInI,qBAAJ,EAApD;QACAmI,oBAAoB,CAACC,OAArB,GAA+BnI,YAAY,CAACoI,eAA5C;QACAF,oBAAoB,CAACG,OAArB,GAA+B;UAC7BD,eAAe,EAAE,KAAKxB,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BkF;QADlB,CAA/B;QAGA,KAAKzG,gBAAL,CAAsB0H,iBAAtB,CAAwCJ,oBAAxC;MACD;;MACD,IAAI,KAAKtB,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BkF,KAA9B,IAAuC,KAAKT,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BkF,KAA9B,IAAuC,CAAlF,EAAqF;QACnF,IAAI,KAAKkB,SAAL,CAAehB,MAAf,GAAwB,CAAxB,IAA6B,KAAKd,mBAAtC,EAA2D;UACzD,KAAKa,qBAAL,GAA6B,KAAKiB,SAAlC;UACA,KAAKC,YAAL,CAAkB,KAAKD,SAAvB;QACD,CAHD,MAIK;UACH,KAAKnI,QAAL,CAAcgE,IAAd;UACA,KAAK7D,GAAL,CAASkI,8BAAT,CAAwC,IAAxC,EAA8C,KAA9C,EAAqD,KAArD,EAA4D,KAAK/F,aAAjE,EAAgF,GAAhF,EAAqF,KAAKkE,IAAL,CAAUQ,QAAV,CAAmBlF,aAAnB,CAAiCmF,KAAtH,EAA6HtE,SAA7H,CAAuIC,IAAI,IAAG;YAC5I,KAAKsE,qBAAL,GAA6BtE,IAA7B;YACA,KAAKwF,YAAL,CAAkBxF,IAAlB;UACD,CAHD;QAID;MACF,CAZD,MAYO,IAAI,KAAK4D,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BkF,KAA9B,IAAuC,KAAKT,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BkF,KAA9B,IAAuC,CAAlF,EAAqF;QAC1F,IAAI,KAAKqB,QAAL,CAAcnB,MAAd,GAAuB,CAAvB,IAA4B,KAAKd,mBAArC,EAA0D;UACxD,KAAKa,qBAAL,GAA6B,KAAKoB,QAAlC;UACA,KAAKF,YAAL,CAAkB,KAAKE,QAAvB;QACD,CAHD,MAIK;UACH,KAAKtI,QAAL,CAAcgE,IAAd;UACA,KAAK7D,GAAL,CAASkI,8BAAT,CAAwC,IAAxC,EAA8C,IAA9C,EAAoD,KAApD,EAA2D,KAAK/F,aAAhE,EAA+E,GAA/E,EAAoFK,SAApF,CAA8FC,IAAI,IAAG;YACnG,KAAKsE,qBAAL,GAA6BtE,IAA7B;YACA,KAAKL,sBAAL,CAA4BgG,eAA5B,GAA8C,IAA9C;YACA,KAAKH,YAAL,CAAkBxF,IAAlB;UACD,CAJD;QAKD;MACF;;MACD,KAAK4F,YAAL,GAAoB,KAAKhC,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BkF,KAA9B,IAAuC,CAAvC,GAA2C,IAA3C,GAAkD,KAAtE;;MACA,IAAI,KAAKT,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BkF,KAAlC,EAAyC;QAEvC,IAAI,KAAKZ,mBAAT,EAA8B;UAC5B,IAAI,KAAKmC,YAAL,IAAqB,IAArB,IAA6B,KAAKC,eAAL,CAAqBtB,MAArB,GAA8B,CAA/D,EAAkE;YAChE,KAAKuB,iBAAL,GAAyB,KAAKD,eAA9B;UACD;;UACD,IAAI,KAAKD,YAAL,IAAqB,KAArB,IAA8B,KAAKG,iBAAL,CAAuBxB,MAAvB,GAAgC,CAAlE,EAAqE;YACnE,KAAKuB,iBAAL,GAAyB,KAAKC,iBAA9B;UACD;QACF,CAPD,MAQK;UACH,KAAK3I,QAAL,CAAcgE,IAAd;UACA,KAAK7D,GAAL,CAASyI,2BAAT,CAAqC,IAArC,EAA2C,KAAKJ,YAAhD,EAA8D,KAA9D,EAAqE,KAAKlG,aAA1E,EAAyF,KAAKkE,IAAL,CAAUQ,QAAV,CAAmBlF,aAAnB,CAAiCmF,KAA1H,EAAiItE,SAAjI,CAA2IC,IAAI,IAAG;YAChJ,KAAK8F,iBAAL,GAAyB9F,IAAzB;YACA,KAAK5C,QAAL,CAAcuE,IAAd;UACD,CAHD;QAID;MACF;IACF,CAvDD;EAyDD;;EAEDuB,mBAAmB;IACjB,IAAI,KAAKxD,aAAL,KAAuB,IAA3B,EAAiC;MAC/B,MAAMuG,aAAa,GAAG,KAAK1D,iBAAL,EAAwB2D,MAAxB,CACnBC,mBAAD,IAAwB;QACtB,OAAOA,mBAAmB,CAACA,mBAApB,KAA4C,IAAnD;MACD,CAHmB,CAAtB;MAKA,KAAK1D,iBAAL,CAAuB2D,gBAAvB,GAA0CH,aAA1C;IACD,CAPD,MAOO;MACL,KAAKxD,iBAAL,CAAuB2D,gBAAvB,GAA0C,KAAK7D,iBAA/C;IACD;;IACDvG,WAAW,CAAC,KAAK4H,IAAN,EAAY,CAAC,gBAAD,CAAZ,CAAX;EACD;;EAED4B,YAAY,CAACxF,IAAD,EAAK;IACf,IAAIqG,eAAe,GAAG,KAAK3G,aAAL,IAAsB,KAAKG,aAAL,CAAmBZ,cAAzC,GAA0D,KAAKY,aAAL,CAAmByG,eAA7E,GAA+F,EAArH;IACA,KAAKC,UAAL,GAAkB,KAAK5I,OAAL,CAAa6I,kBAAb,CAAgCxG,IAAhC,EAAsCqG,eAAtC,EAAuD,KAAK3G,aAA5D,CAAlB;IACA,KAAK+G,YAAL,GAAoB,IAApB;IACA,KAAKrJ,QAAL,CAAcuE,IAAd;EACD;;EACDgC,OAAO;IACL,KAAKvG,QAAL,CAAcgE,IAAd;IACA,MAAMsF,OAAO,GAAG5K,QAAQ,CAAC,CACvB,KAAKyB,GAAL,CAASkI,8BAAT,CAAwC,IAAxC,EAA8C,KAA9C,EAAqD,KAArD,EAA4D,KAAK/F,aAAjE,EAAgF,GAAhF,EAAqF,KAAK+D,mBAA1F,CADuB,EAEvB,KAAKlG,GAAL,CAASkI,8BAAT,CAAwC,IAAxC,EAA8C,IAA9C,EAAoD,KAApD,EAA2D,KAAK/F,aAAhE,EAA+E,GAA/E,CAFuB,EAGvB,KAAKnC,GAAL,CAASyI,2BAAT,CAAqC,IAArC,EAA2C,IAA3C,EAAiD,KAAjD,EAAwD,KAAKtG,aAA7D,EAA4E,KAAK+D,mBAAjF,CAHuB,EAIvB,KAAKlG,GAAL,CAASyI,2BAAT,CAAqC,IAArC,EAA2C,KAA3C,EAAkD,KAAlD,EAAyD,KAAKtG,aAA9D,EAA6E,KAAK+D,mBAAlF,CAJuB,CAAD,CAAxB;IAMAiD,OAAO,CAAC3G,SAAR,CAAmB4G,QAAD,IAAa;MAC7B,KAAKpB,SAAL,GAAiBoB,QAAQ,CAAC,CAAD,CAAzB;MACA,KAAKjB,QAAL,GAAgBiB,QAAQ,CAAC,CAAD,CAAxB;MACA,KAAKd,eAAL,GAAuBc,QAAQ,CAAC,CAAD,CAA/B;MACA,KAAKZ,iBAAL,GAAyBY,QAAQ,CAAC,CAAD,CAAjC;MACA,KAAKvJ,QAAL,CAAcuE,IAAd;IACD,CAND;EAOD;;EAEDE,cAAc,CAAC+E,MAAD,EAAc;IAC1B,KAAKlF,MAAL,GAAckF,MAAM,CAAC7E,GAAP,CAAWC,CAAC,IAAG;MAC3B6E,KAAK,EAAE7E,CAAC,CAACC,SAAF;;MACP6E,IAAI,EAAE9E,CAAC,CAAC+E,SAAF;;MACN,OAAO/E,CAAP;IACD,CAJa,CAAd;EAKD;;EAEDQ,SAAS;IAAA;;IACP,KAAKpF,QAAL,CAAcgE,IAAd;IACA,KAAK1D,cAAL,CAAoBsJ,WAApB,CAAgCjH,SAAhC;MAAA,6BAA0C,WAAMkH,WAAN,EAAoB;QAC5D,IAAIA,WAAW,CAACC,EAAhB,EAAoB,CAClB;QACD;;QAED9K,iBAAiB,CACf6K,WADe,EAEf,KAAI,CAAC1J,GAFU,EAGflB,2BAA2B,CAAC8K,sBAHb,EAIf,KAAI,CAAC3J,QAJU,CAAjB,CAMG4J,IANH,CAMQC,WAAW,IAAG;UAClB,KAAI,CAACxH,aAAL,GAAqBwH,WAArB;;UACA,KAAI,CAACC,IAAL;QACD,CATH,EASKC,KATL,CASWC,CAAC,IAAInE,OAAO,CAACC,GAAR,CAAYkE,CAAZ,CAThB;MAUD,CAfD;;MAAA;QAAA;MAAA;IAAA;EAgBD;;EAEDC,mBAAmB;IACjB,KAAK7D,IAAL,CAAU8D,sBAAV;EACD;;EAUKzH,MAAM,CAACoG,eAAD,EAAiB;IAAA;;IAAA;MAC3B,IAAI,CAACA,eAAD,IAAoB,MAAI,CAACzF,uBAA7B,EAAsD;QACpD,IAAI+G,sBAAsB,SAAS,MAAI,CAAC/G,uBAAL,CAA6BgH,kBAA7B,EAAnC;MACD;;MAED,IAAI;QACF,IAAK,CAAC,MAAI,CAACC,2BAAL,CAAiC1D,QAAjC,CAA0C,MAAI,CAACzE,aAA/C,CAAD,IAAkE,MAAI,CAACkE,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BkF,KAA9B,IAAuC,CAA1G,IACC,MAAI,CAACvC,aAAL,CAAmBqC,QAAnB,CAA4B,MAAI,CAACP,IAAL,CAAUQ,QAAV,CAAmBnF,cAAnB,CAAkCoF,KAA9D,CAAD,IAA0E,MAAI,CAACT,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BkF,KAA9B,IAAuC,CAAlH,IAAyH,MAAI,CAACT,IAAL,CAAUQ,QAAV,CAAmBnF,cAAnB,CAAkCoF,KAAlC,IAA2C,IADvK,EAC+K;UAC7K,MAAM,MAAI,CAACyD,qBAAL,CAA2BH,sBAA3B,CAAN;QACD;;QAED,IAAI,MAAI,CAAC/D,IAAL,CAAUmE,KAAd,EAAqB;UACnB,MAAI,CAACC,WAAL,GAAmB,IAAnB;UACAC,UAAU,CAAC,MAAK;YACd,MAAI,CAACC,IAAL,CAAU7B,eAAe,IAAIsB,sBAA7B;UACD,CAFS,EAEP,IAFO,CAAV;QAGD;;QAED,OAAO,KAAP;MACD,CAdD,CAeA,OAAOQ,EAAP,EAAW;QACT9E,OAAO,CAAC+E,KAAR,CAAcD,EAAd;MACD;IAtB0B;EAuB5B;;EAEKL,qBAAqB,CAACO,qBAAD,EAA2B;IAAA;;IAAA;MACpD,IAAIC,aAAa,GAAG,EAApB;MACA,IAAIC,QAAJ;MACA,IAAIC,KAAK,GAAiB,IAAIC,OAAJ,CAAanH,GAAG,IAAIiH,QAAQ,GAAGjH,GAA/B,CAA1B;MACA,IAAIoH,QAAQ,GAAU,EAAtB;;MAEA,IAAI,MAAI,CAAClJ,qBAAL,CAA2BmJ,aAA/B,EAA8C;QAC5CL,aAAa,CAACM,IAAd,CAAmB,MAAI,CAACpJ,qBAAL,CAA2BmJ,aAA3B,CAAyCE,gBAAzC,EAAnB;QACAH,QAAQ,CAACE,IAAT,CAAc,KAAd;MACD;;MAED,IAAI,MAAI,CAACpJ,qBAAL,CAA2BsJ,aAA/B,EAA8C;QAC5C,MAAM,MAAI,CAACC,wBAAL,CAA8B,MAAI,CAACC,cAAL,CAAoB9K,cAAlD,EAAkE,MAAI,CAAC8K,cAAL,CAAoB7K,eAAtF,EAAuG,MAAI,CAAC6K,cAAL,CAAoB5K,kBAA3H,EAA+IgJ,IAA/I,CAAoJpH,IAAI,IAAG;UAC/JqI,qBAAqB,CAACY,YAAtB,CAAmClH,GAAnC,CAAwCmH,QAAD,IAAkB;YACvDlJ,IAAI,CAAC+B,GAAL,CAAUoH,aAAD,IAAuB;cAC9B,IAAID,QAAQ,CAACE,SAAT,IAAsBD,aAAa,CAACE,SAAxC,EAAmD;gBACjD,IAAIC,WAAW,GAAWH,aAAa,CAACI,kBAAxC;;gBACA,IAAIJ,aAAa,CAACI,kBAAd,IAAoC,iBAAxC,EAA2D;kBACzD,IAAIC,IAAI,GAAG,IAAIC,IAAJ,CAASzJ,IAAI,CAACqF,OAAL,CAAahB,KAAtB,CAAX;;kBACA,MAAI,CAAC7E,qBAAL,CAA2BsJ,aAA3B,CAAyClF,IAAzC,CAA8CQ,QAA9C,CAAuDsF,WAAvD,CAAmEC,QAAnE,CAA4EH,IAAI,CAACI,OAAL,EAA5E;;kBACA,MAAI,CAACpK,qBAAL,CAA2BsJ,aAA3B,CAAyClF,IAAzC,CAA8CQ,QAA9C,CAAuDyF,WAAvD,CAAmEF,QAAnE,CAA6EH,IAAI,CAACM,QAAL,KAAkB,CAA/F;;kBACA,MAAI,CAACtK,qBAAL,CAA2BsJ,aAA3B,CAAyClF,IAAzC,CAA8CQ,QAA9C,CAAuD2F,WAAvD,CAAmEJ,QAAnE,CAA4EH,IAAI,CAACQ,WAAL,EAA5E;gBACD,CALD,MAKO;kBACL,MAAI,CAACxK,qBAAL,CAA2BsJ,aAA3B,CAAyClF,IAAzC,CAA8CqG,GAA9C,CAAkDX,WAAlD,EAA+DK,QAA/D,CAAwET,QAAQ,CAACrC,KAAjF;gBACD;cACF;YACF,CAZD;UAaD,CAdD;QAeD,CAhBK,EAgBHO,IAhBG,CAgBE,MAAK;UACXkB,aAAa,CAACM,IAAd,CAAmB,MAAI,CAACpJ,qBAAL,CAA2BsJ,aAA3B,CAAyCD,gBAAzC,EAAnB;UACAH,QAAQ,CAACE,IAAT,CAAc,KAAd;QACD,CAnBK,CAAN;MAoBD;;MAED,IAAI,MAAI,CAACpJ,qBAAL,CAA2B0K,aAA/B,EAA8C;QAC5C,MAAM,MAAI,CAACnB,wBAAL,CAA8B,MAAI,CAACC,cAAL,CAAoBjL,cAAlD,EAAkE,MAAI,CAACiL,cAAL,CAAoBhL,eAAtF,EAAuG,MAAI,CAACgL,cAAL,CAAoB/K,kBAA3H,EAA+ImJ,IAA/I,CAAoJpH,IAAI,IAAG;UAC/JqI,qBAAqB,CAACY,YAAtB,CAAmClH,GAAnC,CAAwCmH,QAAD,IAAkB;YACvDlJ,IAAI,CAAC+B,GAAL,CAAUoI,gBAAD,IAA0B;cACjC,IAAKjB,QAAQ,CAACE,SAAT,IAAsBe,gBAAgB,CAACd,SAAxC,IAAsDH,QAAQ,CAACrC,KAAnE,EAA0E;gBACxE,IAAIyC,WAAW,GAAWa,gBAAgB,CAACZ,kBAA3C;;gBACA,MAAI,CAAC/J,qBAAL,CAA2B0K,aAA3B,CAAyCtG,IAAzC,CAA8CqG,GAA9C,CAAkDX,WAAlD,EAA+DK,QAA/D,CAAwET,QAAQ,CAACrC,KAAjF;cACD;YACF,CALD;UAMD,CAPD;QAQD,CATK,EASHO,IATG,CASE,MAAK;UACXkB,aAAa,CAACM,IAAd,CAAmB,MAAI,CAACpJ,qBAAL,CAA2B0K,aAA3B,CAAyCrB,gBAAzC,EAAnB;UACAH,QAAQ,CAACE,IAAT,CAAc,KAAd;QACD,CAZK,CAAN;MAaD;;MAED,IAAI,MAAI,CAACpJ,qBAAL,CAA2B4K,YAA/B,EAA6C;QAC3C,MAAM,MAAI,CAACrB,wBAAL,CAA8B,MAAI,CAACC,cAAL,CAAoB3K,eAAlD,EAAmE,MAAI,CAAC2K,cAAL,CAAoB1K,gBAAvF,EAAyG,MAAI,CAAC0K,cAAL,CAAoBzK,mBAA7H,EAAkJ6I,IAAlJ,CAAuJpH,IAAI,IAAG;UAClKqI,qBAAqB,CAACY,YAAtB,CAAmClH,GAAnC,CAAwCmH,QAAD,IAAkB;YACvDlJ,IAAI,CAAC+B,GAAL,CAAUsI,OAAD,IAAiB;cACxB,IAAInB,QAAQ,CAACE,SAAT,IAAsBiB,OAAO,CAAChB,SAAlC,EAA6C;gBAC3C,IAAIC,WAAW,GAAWe,OAAO,CAACd,kBAAlC;;gBACA,IAAIc,OAAO,CAACd,kBAAR,IAA8B,YAAlC,EAAgD;kBAC9C,MAAI,CAAC/J,qBAAL,CAA2B4K,YAA3B,CAAwCE,KAAxC,CAA8CL,GAA9C,CAAkDX,WAAlD,EAA+DK,QAA/D,CAAwET,QAAQ,CAACrC,KAAjF;gBACD,CAFD,MAEO,IAAIwD,OAAO,CAACd,kBAAR,IAA8B,YAAlC,EAAgD;kBACrD,IAAIc,OAAO,CAACd,kBAAR,IAA8B,cAA9B,IAAgDL,QAAQ,CAACrC,KAAT,IAAkB,IAAtE,EAA4E;oBAC1E,MAAI,CAACrH,qBAAL,CAA2B4K,YAA3B,CAAwCG,KAAxC,CAA8CN,GAA9C,CAAkDX,WAAlD,EAA+DK,QAA/D,CAAwE,KAAxE;kBACD,CAFD,MAEO,IAAIU,OAAO,CAACd,kBAAR,IAA8B,iBAAlC,EAAqD;oBAC1D,IAAIC,IAAI,GAAG,IAAIC,IAAJ,CAASzJ,IAAI,CAACqF,OAAL,CAAahB,KAAtB,CAAX;;oBACA,MAAI,CAAC7E,qBAAL,CAA2B4K,YAA3B,CAAwCG,KAAxC,CAA8CnG,QAA9C,CAAuDoG,aAAvD,CAAqEb,QAArE,CAA8EH,IAAI,CAACI,OAAL,EAA9E;;oBACA,MAAI,CAACpK,qBAAL,CAA2B4K,YAA3B,CAAwCG,KAAxC,CAA8CnG,QAA9C,CAAuDqG,cAAvD,CAAsEd,QAAtE,CAAgFH,IAAI,CAACM,QAAL,KAAkB,CAAlG;;oBACA,MAAI,CAACtK,qBAAL,CAA2B4K,YAA3B,CAAwCG,KAAxC,CAA8CnG,QAA9C,CAAuDsG,aAAvD,CAAqEf,QAArE,CAA8EH,IAAI,CAACQ,WAAL,EAA9E;kBACD,CALM,MAKA,IAAIK,OAAO,CAACd,kBAAR,IAA8B,cAAlC,EAAkD;oBACvD,MAAI,CAAC/J,qBAAL,CAA2B4K,YAA3B,CAAwCG,KAAxC,CAA8CN,GAA9C,CAAkDX,WAAlD,EAA+DK,QAA/D,CAAwET,QAAQ,CAACrC,KAAjF;kBACD,CAFM,MAEA,IAAIwD,OAAO,CAACd,kBAAR,IAA8B,cAA9B,IAAgDL,QAAQ,CAACrC,KAAT,IAAkB,KAAtE,EAA6E;oBAClF,MAAI,CAACrH,qBAAL,CAA2B4K,YAA3B,CAAwCG,KAAxC,CAA8CN,GAA9C,CAAkDX,WAAlD,EAA+DK,QAA/D,CAAwE,IAAxE;kBACD;gBACF;cACF;YACF,CApBD;UAqBD,CAtBD;QAuBD,CAxBK,EAwBHvC,IAxBG,CAwBE,MAAK;UACXkB,aAAa,CAACM,IAAd,CAAmB,MAAI,CAACpJ,qBAAL,CAA2B4K,YAA3B,CAAwCvB,gBAAxC,EAAnB;UACAH,QAAQ,CAACE,IAAT,CAAc,IAAd;QACD,CA3BK,CAAN;MA4BD;;MAED,MAAM,MAAI,CAAC+B,YAAL,CAAkBrC,aAAlB,EAAiCI,QAAjC,CAAN;MACA,OAAOH,QAAQ,CAAC,GAAGD,aAAJ,EAAmB,GAAGI,QAAtB,CAAf;IAlFoD;EAmFrD;;EAEKiC,YAAY,CAACrC,aAAD,EAAuBI,QAAvB,EAAsC;IAAA;;IAAA;MACtD,OAAOA,QAAQ,CAACnE,MAAT,GAAkB,CAAlB,IAAuBkE,OAAO,CAACmC,GAAR,CAAY,CAAC,GAAGtC,aAAJ,EAAmB,GAAGI,QAAtB,CAAZ,EAA6CtB,IAA7C,CAAkDT,QAAQ,IAAG;QACzF,IAAI2B,aAAa,CAAC/D,MAAd,IAAwB,CAAxB,IAA6BmE,QAAQ,CAACnE,MAAT,IAAmB,CAApD,EAAuD;UACrD;QACD;;QAED,IAAIsG,SAAS,GAAG,MAAI,CAACC,UAAL,CAAgB,KAAhB,EAAuBpC,QAAvB,CAAhB;;QACA,IAAIqC,SAAS,GAAG,MAAI,CAACD,UAAL,CAAgB,KAAhB,EAAuBpC,QAAvB,CAAhB;;QACA,IAAIsC,SAAS,GAAG,MAAI,CAACF,UAAL,CAAgB,KAAhB,EAAuBpC,QAAvB,CAAhB;;QACA,IAAIuC,QAAQ,GAAG,MAAI,CAACH,UAAL,CAAgB,IAAhB,EAAsBpC,QAAtB,CAAf;;QAEA,IAAImC,SAAS,IAAI,IAAjB,EAAuB;UACrB;UACA,MAAI,CAACK,uBAAL,GAA+BvE,QAAQ,CAACkE,SAAD,CAAvC;QACD;;QAED,IAAIE,SAAS,IAAI,IAAjB,EAAuB;UACrB;UACA,MAAI,CAACI,wBAAL,GAAgCxE,QAAQ,CAACoE,SAAD,CAAxC;QACD;;QAED,IAAIC,SAAS,IAAI,IAAjB,EAAuB;UACrB;UACA,MAAI,CAACI,YAAL,GAAoBzE,QAAQ,CAACqE,SAAD,CAA5B;QACD;;QAED,IAAIC,QAAQ,IAAI,IAAhB,EAAsB;UACpB;UACA,MAAI,CAACI,UAAL,GAAkB1E,QAAQ,CAACsE,QAAD,CAA1B;QACD;;QAED;MACD,CA/B6B,CAA9B;IADsD;EAiCvD;;EAEDK,sBAAsB,CAACtL,IAAD,EAAOuL,YAAP,EAAmB;IACvC,OAAQvL,IAAI,CAACqF,OAAL,IAAgBrF,IAAI,CAACqF,OAAL,CAAahB,KAA7B,IAAsCrE,IAAI,CAACqF,OAAL,CAAamG,IAAb,IAAqBD,YAAnE;EACD;;EAEOT,UAAU,CAACW,IAAD,EAAOC,KAAP,EAAmB;IACnC,IAAIC,GAAG,GAAGD,KAAK,CAACE,SAAN,CAAgB5J,CAAC,IAAIA,CAAC,IAAIyJ,IAA1B,CAAV;;IACA,IAAIE,GAAG,IAAI,CAAC,CAAZ,EAAe;MACb,OAAO,IAAP;IACD;;IACD,OAAOA,GAAP;EACD;;EAEDE,oBAAoB;IAClB,OAAQ,KAAKjI,IAAL,CAAUQ,QAAV,CAAmBlF,aAAnB,CAAiCmF,KAAjC,IAA0C,KAAKT,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BkF,KAAxE,KACJ,KAAKT,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BkF,KAA9B,IAAuC,CAAvC,IAA4C,CAAC,KAAKwD,2BAAL,CAAiC1D,QAAjC,CAA0C,KAAKzE,aAA/C,CAA7C,IAA8G,KAAKkE,IAAL,CAAUQ,QAAV,CAAmBlF,aAAnB,CAAiCmF,KAAjC,IAA0C,IAAzJ,IACE,KAAKT,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BkF,KAA9B,IAAuC,CAAvC,IAA4C,KAAKvC,aAAL,CAAmBqC,QAAnB,CAA4B,KAAKzE,aAAjC,CAD9C,IACkG,KAAKA,aAAL,IAAsB,IADxH,IACgI,KAAKA,aAAL,IAAsB,IAFjJ,CAAR;EAGD;;EAEDwI,IAAI,CAAC7B,eAAD,EAAiB;IAEnB,KAAKxG,aAAL,CAAmBZ,cAAnB,GAAoC,KAAK2E,IAAL,CAAUQ,QAAV,CAAmBnF,cAAnB,CAAkCoF,KAAtE;IACA,KAAKxE,aAAL,CAAmBX,aAAnB,GAAmC,KAAK0E,IAAL,CAAUQ,QAAV,CAAmBlF,aAAnB,CAAiCmF,KAApE;IACA,KAAKxE,aAAL,CAAmBV,UAAnB,GAAgC,KAAKyE,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BkF,KAA9D;IACA,KAAKxE,aAAL,CAAmBT,OAAnB,GAA6B,KAAKwE,IAAL,CAAUQ,QAAV,CAAmBhF,OAAnB,CAA2BiF,KAA3B,IAAoC,IAApC,GAA2C,KAAKT,IAAL,CAAUQ,QAAV,CAAmBhF,OAAnB,CAA2BiF,KAAtE,GAA8E,IAA3G;;IAEA,IAAI,KAAK9D,QAAT,EAAmB;MACjB,KAAKV,aAAL,CAAmBU,QAAnB,GAA8B,KAAKA,QAAnC;IACD,CAFD,MAGK,IAAI,KAAKsH,2BAAL,EAAkC1D,QAAlC,CAA2C,KAAKP,IAAL,CAAUQ,QAAV,CAAmBnF,cAAnB,CAAkCoF,KAA7E,CAAJ,EAAyF;MAC5F,KAAKxE,aAAL,CAAmBU,QAAnB,GAA8B,EAA9B;IACD,CAFI,MAGA,IAAI,KAAKqD,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BkF,KAA9B,IAAuC,CAA3C,EAA8C;MACjD,KAAK5G,OAAL,CAAa2K,KAAb,CAAmB,uBAAnB;MACA,KAAKJ,WAAL,GAAmB,KAAnB;MACA;IACD;;IACD,IAAI3B,eAAJ,EAAqB;MACnB,KAAKxG,aAAL,CAAmByG,eAAnB,GAAqCD,eAAe,CAAC4C,YAAhB,IAAgC,EAArE;IACD,CAFD,MAGK;MACH,KAAKpJ,aAAL,CAAmByG,eAAnB,GAAqC,EAArC;IACD;;IAGD,IAAIwF,aAAa,GAAG;MAClBC,sBAAsB,EAAE,KAAKlM,aADX;MAElBmM,SAAS,EAAE;QACTC,0BAA0B,EAAE,KAAKf,uBADxB;QAETgB,YAAY,EAAE,KAAKd,YAFV;QAGTe,4BAA4B,EAAE,KAAKhB,wBAH1B;QAITiB,YAAY,EAAE,KAAKf;MAJV,CAFO;MAQlBgB,WAAW,EAAE,IARK;MASlBzG,YAAY,EAAE,KAAKA,YATD;MAUlB0G,eAAe,EAAE,KAVC;MAWlBC,UAAU,EAAE,IAXM;MAYlBC,wBAAwB,EAAE,KAAKA;IAZb,CAApB;IAeA,KAAKjP,GAAL,CAASkP,4BAAT,CAAsCX,aAAtC,EAAqD/L,SAArD,CAA+DiC,CAAC,IAAG;MACjE,KAAK4B,IAAL,CAAUf,KAAV;IACD,CAFD;EAGD;;EAED6J,UAAU,CAACC,KAAD,EAAc;IACtB,KAAKH,wBAAL,GAAgCG,KAAhC;EACD;;EAEDrF,IAAI;IACF,KAAKrE,aAAL,CAAmB,KAAKpD,aAAL,CAAmBZ,cAAtC;IACA,KAAK2E,IAAL,CAAUQ,QAAV,CAAmBnF,cAAnB,CAAkC0K,QAAlC,CAA2C,KAAK9J,aAAL,CAAmBZ,cAA9D;IACA,KAAK2E,IAAL,CAAUQ,QAAV,CAAmBlF,aAAnB,CAAiCyK,QAAjC,CAA0C,KAAK9J,aAAL,CAAmBX,aAAnB,CAAiCwE,IAAjC,EAA1C;IACA,KAAK/D,sBAAL,CAA4BkE,iBAA5B,GAAgD,MAAhD;;IACA,IAAI,KAAKhE,aAAL,CAAmBtD,YAAnB,IAAmC,IAAvC,EAA6C;MAC3C,KAAKmC,MAAL,GAAc,IAAd;MACA,KAAKkF,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BwK,QAA9B,CAAuC,CAAvC;MACA,KAAKhK,sBAAL,CAA4BiN,0BAA5B,CAAuD,CAAvD;IACD,CAJD,MAKK;MACH,KAAKlO,MAAL,GAAc,IAAd;MACA,KAAKkF,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BwK,QAA9B,CAAuC,CAAvC;MACA,KAAKhK,sBAAL,CAA4BiN,0BAA5B,CAAuD,CAAvD;IACD;;IACD,KAAKhJ,IAAL,CAAUQ,QAAV,CAAmBhF,OAAnB,CAA2BuK,QAA3B,CAAoC,KAAK9J,aAAL,CAAmBT,OAAvD;EACD;;EAEDyN,SAAS;IACP,KAAKjJ,IAAL,CAAUf,KAAV;EACD;;EAEDI,aAAa,CAAC6J,KAAD,EAAM;IACjB,IAAIC,OAAO,GAAG,IAAInQ,iBAAJ,EAAd;IACAmQ,OAAO,CAACC,WAAR,GAAsB,IAAtB;IACAD,OAAO,CAACxQ,YAAR,GAAuB,KAAvB;IACAwQ,OAAO,CAACvQ,eAAR,GAA0B,KAA1B;IACAuQ,OAAO,CAACE,KAAR,GAAgBH,KAAhB;IACA,KAAKvP,GAAL,CAAS2P,iBAAT,CAA2BH,OAA3B,EAAoChN,SAApC,CAA8CuB,GAAG,IAAG;MAClD,KAAKC,KAAL,GAAaD,GAAG,CAACC,KAAJ,GAAY,CAAZ,GAAgBD,GAAG,CAACC,KAApB,GAA4B,KAAKC,SAA9C;IACD,CAFD;EAGD;;EAEDuH,wBAAwB,CAACoE,cAAD,EAAiBC,eAAjB,EAAkCC,kBAAlC,EAAoD;IAC1E,IAAIC,GAAG,GAAwB;MAC7BR,KAAK,EAAE,KAAKpN,aADiB;MAE7B2M,WAAW,EAAE,IAFgB;MAG7BzG,YAAY,EAAE,KAAKhC,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BkF,KAA9B,IAAuC,CAAvC,GAA2C,IAA3C,GAAkD,KAHnC;MAI7BiI,eAAe,EAAE,KAJY;MAK7BiB,UAAU,EAAE,KAAK3J,IAAL,CAAUQ,QAAV,CAAmBlF,aAAnB,CAAiCmF,KALhB;MAM7BmJ,oBAAoB,EAAEL,cANO;MAO7BM,qBAAqB,EAAEL,eAPM;MAQ7BM,wBAAwB,EAAEL;IARG,CAA/B;IAUA,OAAO,KAAK9P,GAAL,CAASoQ,wBAAT,CAAkCL,GAAlC,CAAP;EACD;;EAEDM,UAAU;IACR,IAAI,KAAKhK,IAAL,CAAUQ,QAAV,CAAmBjF,UAAnB,CAA8BkF,KAA9B,IAAuC,CAA3C,EAA8C;MAC5CwJ,QAAQ,CAACC,cAAT,CAAwB,SAAxB,EAAmCC,KAAnC;IACD,CAFD,MAEO;MACL,KAAKpO,sBAAL,CAA4BqO,gBAA5B;IACD;EACF;;EAEDC,eAAe,CAACjO,IAAD,EAAOkO,GAAP,EAAU;IACvB,IAAIlO,IAAI,IAAI,iBAAZ,EAA+B;MAC7B,IAAImO,OAAO,GAAG,IAAI1E,IAAJ,CAASyE,GAAG,CAACE,MAAJ,CAAW/J,KAApB,CAAd;IACD,CAFD,MAEO;MACL8J,OAAO,GAAGD,GAAG,CAACE,MAAJ,CAAW/J,KAArB;IACD;;IACD,MAAMa,oBAAoB,GAA0B,IAAInI,qBAAJ,EAApD;IACAmI,oBAAoB,CAACG,OAArB,GAA+B;MAC7BmG,IAAI,EAAExL,IADuB;MAE7BqE,KAAK,EAAE8J;IAFsB,CAA/B;IAIA,KAAKvQ,gBAAL,CAAsB0H,iBAAtB,CAAwCJ,oBAAxC;EACD;;EAEDmJ,WAAW;IACT;IACA,IAAI,KAAKC,kBAAT,EAA6B;MAC3B,KAAKA,kBAAL,CAAwBlL,WAAxB;IACD;;IACD,IAAI,KAAKD,kBAAT,EAA6B;MAC3B,KAAKA,kBAAL,CAAwBC,WAAxB;IACD;EACF;;AArpByC;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAsCzC7H;IAASgT,OAAC,oBAAD,EAAuB;MAAEC,MAAM,EAAE;IAAV,CAAvB;;;UAMTjT;IAASgT,OAAC9S,sBAAD,EAAyB;MAAE+S,MAAM,EAAE;IAAV,CAAzB;;;UACTjT;IAASgT,OAAC7S,gCAAD,EAAmC;MAAE8S,MAAM,EAAE;IAAV,CAAnC;;;UACTjT;IAASgT,OAAC5S,mBAAD,EAAsB;MAAE6S,MAAM,EAAE;IAAV,CAAtB;;;UAaTjT;IAASgT,OAAC,iBAAD,EAAoB;MAAEC,MAAM,EAAE;IAAV,CAApB;;;UAeTjT;IAASgT,OAAC,sBAAD,EAAyB;MAAEC,MAAM,EAAE;IAAV,CAAzB;;;AA1ECtR,+BAA+B,eAN3C5B,SAAS,CAAC;EACTmT,QAAQ,EAAE,8BADD;EAETC,8BAFS;EAITC,SAAS,EAAE,CAAC9R,eAAD,CAJF;;AAAA,CAAD,CAMkC,GAA/BK,+BAA+B,CAA/B;SAAAA", "names": ["ChangeDetectorRef", "Component", "ViewChild", "FilingApiService", "StateSelectorComponent", "CorporationTypeSelectorComponent", "FilingModeComponent", "FormGroup", "FormControl", "fork<PERSON><PERSON>n", "interval", "ResetExcept", "FormCorporation", "ToastrService", "ActivatedRoute", "LoadFilingService", "FilingServiceResponseObject", "FormModeService", "CategoryCode", "SubCategoryCode", "CustomSharedValidations", "FilingInfoService", "LoaderService", "StatePriceRequest", "QuestionService", "ComponentMessageService", "ComponentMessageModel", "SubjectEnums", "PageTitleService", "DynamicFormCorporationComponent", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "cdr", "filingInfoService", "Api", "formMode", "toaster", "activatedRoute", "service", "ComponentMessags", "pageTitleService", "filingPriceService", "SOIProductCode", "SOICategoryCode", "SOISubCategoryCode", "EINProductCode", "EINCategoryCode", "EINSubCategoryCode", "KitsProductCode", "KitsCategoryCode", "KitsSubCategoryCode", "formationState", "kitType", "isEdit", "formationType", "EntityName", "AllowedCommonShareCount", "IsEntityReserved", "ExpirationDate", "California", "FormationState", "FormationType", "FilingMode", "Remarks", "specialInstructions", "UpsellingComponent", "upsellingComponent", "UpsellingComponentObj", "filingModeComponent", "selectedState", "FilingModeComponentObj", "FormEditData", "FormCorporate", "$SendDynamicFormData", "subscribe", "data", "onSave", "dynamicData", "UploadComponent", "uploadComponent", "UploadComponentObj", "EmitUploadedData", "FileData", "uploadedFileList", "toggleUploadView", "DynamicFormComponent", "dynamicFormComponent", "DynamicFormComponentObj", "ngOnInit", "get<PERSON><PERSON><PERSON>", "SubCatCode", "formcorp", "label", "setPageTitle", "LoadDynamicControlsInCache", "show", "getPrice", "res", "price", "basePrice", "FCGetStates", "States", "hide", "StateComponent", "mapStatesValue", "allStatesCode", "map", "x", "stateCode", "<PERSON><PERSON><PERSON><PERSON>", "GetFormationType", "formpartnership", "formcorporation", "resp", "typeOfCorporation", "StartLoad", "CorpTypeComponent", "Caption", "$OnStateSelection", "SelectedState", "reset", "upSellingInputData", "IsEditMode", "IsVisible", "getStatePrice", "setCorporationTypes", "formChangeInterval", "unsubscribe", "console", "log", "$OnCorportationTypeSelection", "corporationType", "formCorporationType", "trim", "getForm", "Form", "filingServiceType", "downloadPDFForm", "SelectedFilingType", "TabLineNo", "Price", "UniqueFlowStates", "includes", "controls", "value", "dynamicFormMasterData", "length", "FilingTypes", "OfflineMode", "ProfitCorporation", "ProfessionalCorporation", "OnlineMode", "statesHavingDyanmicForms", "valueChanges", "SelectedFormationState", "toString", "undefined", "activeSectionMessage", "subject", "filingModeCheck", "payload", "sendReplayMessage", "selfDraft", "getQuestions", "GetDynamicFormMasterDataUpload", "acsDraft", "isUploadVisible", "categoryCode", "dyanmicControl2", "dependentControls", "dynamiApiControls", "GetAllDependentControlsData", "filteredArray", "filter", "corporationTypeCode", "CorporationTypes", "dynamicFormData", "DynamicFormData", "questions$", "getMappedQuestions", "isDataLoaded", "formLLC", "response", "states", "Value", "Text", "stateName", "queryParams", "queryString", "on", "DynamicFormCorporation", "then", "serviceData", "Load", "catch", "e", "updateValidityState", "updateValueAndValidity", "enteredDynamicFormData", "getDynamicFormData", "selfFilingDynamicFormStates", "UpSellingDataCreation", "valid", "onSaveClick", "setTimeout", "Save", "ex", "error", "DynamicFormDataValues", "upSellingData", "resolver", "respo", "Promise", "services", "UpSelling_BOI", "push", "getUpSellingData", "UpSelling_EIN", "getDependentControlsData", "upsellingCodes", "keyValuePair", "mainCtrl", "dependentCtrl", "ControlId", "controlId", "formcontrol", "dependentControlId", "date", "Date", "file_date_y", "setValue", "getDate", "file_date_m", "getMonth", "file_date_d", "getFullYear", "get", "UpSelling_SOI", "dependentControl", "UpSelling_CK", "depCtrl", "Form1", "Form2", "FormationDate", "FormationMonth", "FormationYear", "mapUpselling", "all", "index_BOI", "getIndexNo", "index_EIN", "index_SOI", "index_CK", "beneficialOwnershipInfo", "EmployerIdentificationNo", "AnnualReport", "KitOrderVM", "upsellingSetvalueCheck", "ControlValue", "name", "code", "array", "val", "findIndex", "componentsVisibility", "filingService", "dynamicFormCorporation", "upSelling", "BeneficialOwnerInformation", "annualReport", "employerIdentificationNumber", "corporateKit", "productCode", "subCategoryCode", "optionCode", "dynamicFormUploadedFiles", "SaveDynamicFormFilingService", "fileUplaod", "files", "SelectedOnlineMethodChange", "ResetForm", "state", "request", "ProductCode", "State", "GetStateWisePrice", "depProductCode", "depCategoryCode", "depSubcategoryCode", "req", "entityType", "dependentProductCode", "dependentCategoryCode", "dependentSubcategoryCode", "GetDependentControlsData", "callSubmit", "document", "getElementById", "click", "uploadFormSubmit", "onFocusOutEvent", "eve", "queData", "target", "ngOnDestroy", "routesSubscription", "args", "static", "selector", "template", "providers"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\viatech\\Ecom\\client\\acs\\src\\app\\Modules\\FilingService\\Pages\\dynamic-form-corporation\\dynamic-form-corporation.component.ts"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';\r\nimport { FilingApiService } from '../../Services/FilingApiService';\r\nimport { StateSelectorComponent } from 'src/app/Modules/Shared/Components/formComponents/stateSelector/state-selector.component';\r\nimport { CorporationTypeSelectorComponent } from 'src/app/Modules/Shared/Components/formComponents/corporation-type-selector/corporation-type-selector.component';\r\nimport { FilingModeComponent } from 'src/app/Modules/Shared/Components/formComponents/filing-mode/filing-mode.component';\r\nimport { FormGroup, FormControl } from '@angular/forms';\r\nimport { forkJoin, interval, Observable, Subscription } from 'rxjs';\r\nimport { ResetExcept } from 'src/app/Modules/Shared/functions/form-reset';\r\nimport { FormCorporation } from '../../Models/FormCorporation';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { LoadFilingService } from '../../Functions/filing-service-load.func';\r\nimport { FilingServiceResponseObject } from '../../Enums/filing-service-enum';\r\nimport { FormModeService } from 'src/app/Modules/Shared/Services/Common/FormModeService';\r\nimport { CategoryCode } from 'src/app/Modules/Shared/Enums/category-code.enum';\r\nimport { SubCategoryCode } from 'src/app/Modules/Shared/Enums/sub-category-code.enum';\r\nimport { CustomSharedValidations } from 'src/app/Modules/Shared/functions/custom-validations';\r\nimport { EmployerIdnetificationNumber } from '../../Models/EmployerIdentificationNumber';\r\nimport { StatementOfInfromation } from '../../Models/StatementOfInformation';\r\nimport { FilingInfoService } from '../../Services/filing-price.service';\r\nimport { LoaderService } from '../../../../Modules/Core/Services/Common/loader.service';\r\nimport { ProductDetail } from 'src/app/Modules/Shared/Models/KitsModel/ProductDetail';\r\nimport { StatePriceRequest } from 'src/app/Modules/Shared/Models/Common/state-price-request.model';\r\nimport { QuestionBase } from 'src/app/Modules/Shared/Models/DynamicForm/question-base';\r\nimport { QuestionService } from 'src/app/Modules/Shared/Services/Common/question.service';\r\nimport { DynamicFormComponent } from 'src/app/Modules/Shared/Components/dynamic-form/dynamic-form.component';\r\nimport { UpsellingComponent } from '../upselling/upselling.component';\r\nimport { ComponentMessageService } from 'src/app/Modules/Core/Services/Common/component-message.service';\r\nimport { ComponentMessageModel } from 'src/app/Modules/Shared/Models/component-message.model';\r\nimport { SubjectEnums } from 'src/app/Modules/Shared/Enums/subject.enum';\r\n\r\nimport { DependentControlReq } from '../../Models/DependentControlReq';\r\nimport { FileUploaderComponent } from 'src/app/Modules/Shared/Components/file-uploader/file-uploader.component';\r\nimport { PageTitleService } from 'src/app/Modules/Shared/Services/Common/page-title.service';\r\n\r\n@Component({\r\n  selector: 'app-dynamic-form-corporation',\r\n  templateUrl: './dynamic-form-corporation.component.html',\r\n  styleUrls: ['./dynamic-form-corporation.component.css'],\r\n  providers: [QuestionService]\r\n})\r\nexport class DynamicFormCorporationComponent implements OnInit {\r\n\r\n  questions$: Observable<QuestionBase<any>[]>;\r\n  statesHavingDyanmicForms = ['NV', 'TX', 'DE', 'NY', 'FL', 'AZ', 'GA', 'WY', 'CO', 'OR', 'IL', 'AC', 'AL', 'AK', 'CT', 'HI', 'ID'];\r\n  selfFilingDynamicFormStates = []\r\n  allStatesCode: any[] = [];\r\n  dynamicQuestionData: any;\r\n  dynamicSOIQuestionData: any[] = [];\r\n  selfDraft: any[] = [];\r\n  acsDraft: any[] = [];\r\n  dyanmicControl2: any[] = [];\r\n  dynamiApiControls: any[] = [];\r\n  counter: number = 0;\r\n  index: number = 0;\r\n  categoryCode: string = \"\";\r\n  formCorporationType: string = \"\";\r\n  FileData: any\r\n  isEdit: boolean = false\r\n  onSaveClick: boolean = false;\r\n  isDataLoaded: boolean = false;\r\n  falseFlag: boolean = false\r\n  upsellingCodes: any = {\r\n    SOIProductCode: 'FS', SOICategoryCode: 'FOT', SOISubCategoryCode: '901',\r\n    EINProductCode: 'FS', EINCategoryCode: 'FOT', EINSubCategoryCode: '907',\r\n    KitsProductCode: 'CK', KitsCategoryCode: 'AI1', KitsSubCategoryCode: 'AD'\r\n  }\r\n\r\n  formChangeInterval: Subscription;\r\n  dynamicFormUploadedFiles: File[] = [];\r\n  constructor(private isLoader: LoaderService, private cdr: ChangeDetectorRef, public filingInfoService: FilingInfoService,\r\n    private Api: FilingApiService, public formMode: FormModeService, private toaster: ToastrService,\r\n    private activatedRoute: ActivatedRoute, private service: QuestionService, private ComponentMessags: ComponentMessageService,\r\n    private pageTitleService: PageTitleService, public filingPriceService: FilingInfoService\r\n  ) { }\r\n\r\n  DynamicFormComponentObj: DynamicFormComponent;\r\n  UpsellingComponentObj: UpsellingComponent;\r\n  FilingModeComponentObj: FilingModeComponent;\r\n  @ViewChild('UpsellingComponent', { static: false }) set UpsellingComponent(upsellingComponent: UpsellingComponent) {\r\n    if (upsellingComponent) {\r\n      this.UpsellingComponentObj = upsellingComponent;\r\n    }\r\n  }\r\n\r\n  @ViewChild(StateSelectorComponent, { static: true }) StateComponent: StateSelectorComponent;\r\n  @ViewChild(CorporationTypeSelectorComponent, { static: true }) CorpTypeComponent: CorporationTypeSelectorComponent;\r\n  @ViewChild(FilingModeComponent, { static: true }) set FilingModeComponent(filingModeComponent: FilingModeComponent) {\r\n    if (filingModeComponent) {\r\n      // this.cdr.detectChanges()\r\n      filingModeComponent.selectedState = this.selectedState\r\n      this.FilingModeComponentObj = filingModeComponent\r\n      this.FilingModeComponentObj.FormEditData = this.FormCorporate\r\n      this.FilingModeComponentObj.$SendDynamicFormData.subscribe(data => {\r\n        this.onSave(data.dynamicData)\r\n      })\r\n    }\r\n  }\r\n\r\n  UploadComponentObj: FileUploaderComponent;\r\n  @ViewChild('UploadComponent', { static: false }) set UploadComponent(uploadComponent: FileUploaderComponent) {\r\n    if (uploadComponent) {\r\n      this.UploadComponentObj = uploadComponent;\r\n      this.UploadComponentObj.EmitUploadedData.subscribe(data => {\r\n        this.FileData = data\r\n      })\r\n      //Mapping uploaded file data to FileUploadComponent in case of edit\r\n      if (this.FormCorporate.FileData) {\r\n        this.UploadComponentObj.uploadedFileList = this.FormCorporate.FileData\r\n        this.UploadComponentObj.toggleUploadView = 2\r\n        this.FileData = this.FormCorporate.FileData;\r\n      }\r\n    }\r\n  }\r\n\r\n  @ViewChild('DynamicFormComponent', { static: false }) set DynamicFormComponent(dynamicFormComponent: DynamicFormComponent) {\r\n    if (dynamicFormComponent) {\r\n      this.DynamicFormComponentObj = dynamicFormComponent;\r\n    }\r\n  }\r\n  routesSubscription: any;\r\n  upSellingInputData: any = {\r\n    formationState: '',\r\n    kitType: 'CK',\r\n    isEdit: false,\r\n    formationType: '',\r\n    EntityName: '',\r\n    AllowedCommonShareCount: '',\r\n    IsEntityReserved: '',\r\n    ExpirationDate: ''\r\n  };\r\n  question: QuestionBase<string>;\r\n\r\n\r\n  ClassUpSelling: string = \"col-md-12\"\r\n\r\n  _IsEINSelected = false;\r\n  _IsSOISelected = false;\r\n  _IsCKSelected = false;\r\n\r\n  //=====================\r\n  // @ViewChild(ServiceFormAComponent,{static:true}) ServiceFormAComponent :ServiceFormAComponent;\r\n  //\r\n\r\n  Yes: string = \"YES\";\r\n  No: string = \"NO\"\r\n  ProfessionalCorporation = \"PC\"\r\n  ProfitCorporation = \"FP\"\r\n  California = \"CA\";\r\n  Nevada = \"NV\"\r\n  selectedState: any\r\n  corporationType: any;\r\n\r\n  FormCorporate: FormCorporation = new FormCorporation();\r\n  EmployerIdentificationNo: EmployerIdnetificationNumber = null;\r\n  beneficialOwnershipInfo: any = null;\r\n  AnnualReport: StatementOfInfromation = null;\r\n  KitOrderVM: ProductDetail = null;\r\n  ServiceType: any[] = [];\r\n  States: any[];\r\n  dependentControls: any[] = [];\r\n\r\n  UniqueFlowStates = [this.California];//, this.Nevada]\r\n  price: number;\r\n  basePrice: number;\r\n\r\n  dynamicFormMasterData: any[] = [];\r\n  typeOfCorporation: any[];\r\n  Offline: boolean\r\n\r\n  ngOnInit() {\r\n    this.filingPriceService\r\n      .getLabel(this.filingInfoService.SubCatCode.formcorp, '001').subscribe(label => {\r\n        this.pageTitleService.setPageTitle(label);\r\n      });\r\n    this.Api.LoadDynamicControlsInCache().subscribe();\r\n    this.isLoader.show();\r\n    this.filingInfoService.getPrice(this.filingInfoService.SubCatCode.formcorp).subscribe(res => {\r\n      this.price = res;\r\n      this.basePrice = res;\r\n    });\r\n\r\n    this.Api.FCGetStates().subscribe((States) => {\r\n      this.isLoader.hide();\r\n      this.States = States;\r\n      this.StateComponent.States = States;\r\n      this.mapStatesValue(this.States);\r\n      this.allStatesCode = this.StateComponent.States.map(x => x.stateCode);\r\n    });\r\n\r\n    const fillValues = forkJoin([\r\n      this.Api.GetFormationType(SubCategoryCode.formpartnership, CategoryCode.formcorporation),\r\n    ])\r\n\r\n    fillValues.subscribe(resp => {\r\n      this.typeOfCorporation = resp[0] || [];\r\n      this.StartLoad();\r\n    })\r\n\r\n    this.CorpTypeComponent.Caption = \"2. Please select the Type of Corporation. \"\r\n\r\n    // Display Corporation Type after state selection\r\n    this.StateComponent.$OnStateSelection.subscribe(SelectedState => {\r\n      this.isLoader.hide();\r\n      this.CorpTypeComponent.reset();\r\n      this.selectedState = SelectedState;\r\n      this.FilingModeComponentObj.reset();\r\n      if (SelectedState) {\r\n        this.upSellingInputData.formationState = SelectedState;\r\n        this.upSellingInputData.isEdit = this.formMode.IsEditMode;\r\n        this.CorpTypeComponent.IsVisible = true;\r\n        this.getStatePrice(SelectedState);\r\n      } else {\r\n        this.CorpTypeComponent.IsVisible = false;\r\n      }\r\n\r\n      if (this.typeOfCorporation) {\r\n        this.setCorporationTypes();\r\n      } else {\r\n        if (this.formChangeInterval) {\r\n          this.formChangeInterval.unsubscribe();\r\n        }\r\n\r\n        this.formChangeInterval = interval(100).subscribe(() => {\r\n          if (this.typeOfCorporation) {\r\n            this.setCorporationTypes();\r\n            this.formChangeInterval.unsubscribe();\r\n          } else {\r\n            console.log(\"Waiting for Corporation data to load...\");\r\n          }\r\n        });\r\n      }\r\n    })\r\n\r\n    this.CorpTypeComponent.$OnCorportationTypeSelection.subscribe(corporationType => {\r\n      this.formCorporationType = corporationType.trim();\r\n      if (this.formCorporationType) {\r\n        this.getForm();\r\n      }\r\n      this.upSellingInputData.formationType = this.formCorporationType;\r\n      this.FilingModeComponentObj.reset();\r\n      ResetExcept(this.Form, [\"FormationState\", \"FormationType\"])\r\n      this.FilingModeComponentObj.filingServiceType = \"CORP\";\r\n      this.FilingModeComponentObj.downloadPDFForm(this.selectedState, CategoryCode.formcorporation, SubCategoryCode.formcorp);\r\n      this.FilingModeComponentObj.SelectedFilingType = null;\r\n      this.FilingModeComponentObj.TabLineNo = 3;\r\n      this.FilingModeComponentObj.Price = this.price;\r\n      if (this.UniqueFlowStates.includes(this.Form.controls.FormationState.value) || this.dynamicFormMasterData.length <= 0) {\r\n        if (!this.UniqueFlowStates.includes(this.Form.controls.FormationState.value) && this.formCorporationType) {\r\n          this.FilingModeComponentObj.SelectedFilingType = this.FilingModeComponentObj.FilingTypes.OfflineMode;\r\n        }\r\n        else if (this.Form.controls.FormationState.value == 'NV' && this.formCorporationType != this.ProfitCorporation && this.formCorporationType) {\r\n          this.FilingModeComponentObj.SelectedFilingType = this.FilingModeComponentObj.FilingTypes.OfflineMode;\r\n        }\r\n        else if (this.formCorporationType == this.ProfessionalCorporation.trim() || (this.formCorporationType == this.ProfitCorporation.trim() && this.Form.controls.FormationState.value == this.California)) {\r\n          this.FilingModeComponentObj.SelectedFilingType = this.FilingModeComponentObj.FilingTypes.OnlineMode\r\n        }\r\n        else if (this.Form.controls.FormationState.value == 'NV' && this.formCorporationType == this.ProfitCorporation) {\r\n          this.FilingModeComponentObj.SelectedFilingType = this.FilingModeComponentObj.FilingTypes.OnlineMode;\r\n        }\r\n        else if ((this.formCorporationType == \"NP\" && this.formCorporationType != this.ProfessionalCorporation.trim() && this.formCorporationType && this.Form.controls.FormationState.value == this.California)) {\r\n          this.FilingModeComponentObj.SelectedFilingType = this.FilingModeComponentObj.FilingTypes.OfflineMode;\r\n        }\r\n      }\r\n      else if (this.statesHavingDyanmicForms.includes(this.Form.controls.FormationState.value) && this.dynamicFormMasterData.length > 0) {\r\n        if (this.formCorporationType && this.formCorporationType == this.ProfitCorporation) {\r\n          this.FilingModeComponentObj.SelectedFilingType = this.FilingModeComponentObj.FilingTypes.OnlineMode\r\n        }\r\n        else {\r\n          this.FilingModeComponentObj.SelectedFilingType = this.FilingModeComponentObj.FilingTypes.OfflineMode\r\n        }\r\n      } else {\r\n        this.FilingModeComponentObj.SelectedFilingType = this.FilingModeComponentObj.FilingTypes.OfflineMode;\r\n      }\r\n    })\r\n\r\n    this.Form.controls.FilingMode.valueChanges.subscribe(value => {\r\n      this.FilingModeComponentObj.SelectedFormationState = this.selectedState;\r\n      if (this.Form.controls.FilingMode.value != null && (this.Form.controls.FilingMode.value).toString() != '' && this.Form.controls.FilingMode.value != undefined) {\r\n        const activeSectionMessage: ComponentMessageModel = new ComponentMessageModel();\r\n        activeSectionMessage.subject = SubjectEnums.filingModeCheck;\r\n        activeSectionMessage.payload = {\r\n          filingModeCheck: this.Form.controls.FilingMode.value\r\n        }\r\n        this.ComponentMessags.sendReplayMessage(activeSectionMessage);\r\n      }\r\n      if (this.Form.controls.FilingMode.value && this.Form.controls.FilingMode.value == 2) {\r\n        if (this.selfDraft.length > 1 && this.formCorporationType) {\r\n          this.dynamicFormMasterData = this.selfDraft;\r\n          this.getQuestions(this.selfDraft);\r\n        }\r\n        else {\r\n          this.isLoader.show();\r\n          this.Api.GetDynamicFormMasterDataUpload('FS', '001', '010', this.selectedState, 'A', this.Form.controls.FormationType.value).subscribe(data => {\r\n            this.dynamicFormMasterData = data;\r\n            this.getQuestions(data);\r\n          })\r\n        }\r\n      } else if (this.Form.controls.FilingMode.value && this.Form.controls.FilingMode.value == 1) {\r\n        if (this.acsDraft.length > 1 && this.formCorporationType) {\r\n          this.dynamicFormMasterData = this.acsDraft;\r\n          this.getQuestions(this.acsDraft);\r\n        }\r\n        else {\r\n          this.isLoader.show();\r\n          this.Api.GetDynamicFormMasterDataUpload('FS', 'EZ', '010', this.selectedState, 'C').subscribe(data => {\r\n            this.dynamicFormMasterData = data;\r\n            this.FilingModeComponentObj.isUploadVisible = true;\r\n            this.getQuestions(data);\r\n          })\r\n        }\r\n      }\r\n      this.categoryCode = this.Form.controls.FilingMode.value == 1 ? \"EZ\" : '001';\r\n      if (this.Form.controls.FilingMode.value) {\r\n\r\n        if (this.formCorporationType) {\r\n          if (this.categoryCode == 'EZ' && this.dyanmicControl2.length > 1) {\r\n            this.dependentControls = this.dyanmicControl2;\r\n          }\r\n          if (this.categoryCode == '001' && this.dynamiApiControls.length > 1) {\r\n            this.dependentControls = this.dynamiApiControls;\r\n          }\r\n        }\r\n        else {\r\n          this.isLoader.show();\r\n          this.Api.GetAllDependentControlsData('FS', this.categoryCode, '010', this.selectedState, this.Form.controls.FormationType.value).subscribe(data => {\r\n            this.dependentControls = data;\r\n            this.isLoader.hide();\r\n          });\r\n        }\r\n      }\r\n    });\r\n\r\n  }\r\n\r\n  setCorporationTypes() {\r\n    if (this.selectedState !== \"CA\") {\r\n      const filteredArray = this.typeOfCorporation?.filter(\r\n        (corporationTypeCode) => {\r\n          return corporationTypeCode.corporationTypeCode !== \"NP\";\r\n        }\r\n      );\r\n      this.CorpTypeComponent.CorporationTypes = filteredArray;\r\n    } else {\r\n      this.CorpTypeComponent.CorporationTypes = this.typeOfCorporation;\r\n    }\r\n    ResetExcept(this.Form, [\"FormationState\"]);\r\n  }\r\n\r\n  getQuestions(data) {\r\n    var dynamicFormData = this.selectedState == this.FormCorporate.FormationState ? this.FormCorporate.DynamicFormData : [];\r\n    this.questions$ = this.service.getMappedQuestions(data, dynamicFormData, this.selectedState);\r\n    this.isDataLoaded = true;\r\n    this.isLoader.hide();\r\n  }\r\n  getForm() {\r\n    this.isLoader.show();\r\n    const formLLC = forkJoin([\r\n      this.Api.GetDynamicFormMasterDataUpload('FS', '001', '010', this.selectedState, 'A', this.formCorporationType),\r\n      this.Api.GetDynamicFormMasterDataUpload('FS', 'EZ', '010', this.selectedState, 'C'),\r\n      this.Api.GetAllDependentControlsData('FS', 'EZ', '010', this.selectedState, this.formCorporationType),\r\n      this.Api.GetAllDependentControlsData('FS', '001', '010', this.selectedState, this.formCorporationType)\r\n    ]);\r\n    formLLC.subscribe((response) => {\r\n      this.selfDraft = response[0];\r\n      this.acsDraft = response[1];\r\n      this.dyanmicControl2 = response[2];\r\n      this.dynamiApiControls = response[3];\r\n      this.isLoader.hide();\r\n    })\r\n  }\r\n\r\n  mapStatesValue(states: any[]) {\r\n    this.States = states.map(x => {\r\n      Value: x.stateCode\r\n      Text: x.stateName\r\n      return x;\r\n    })\r\n  }\r\n\r\n  StartLoad() {\r\n    this.isLoader.show();\r\n    this.activatedRoute.queryParams.subscribe(async queryString => {\r\n      if (queryString.on) {\r\n        //this.isAdd = false;\r\n      }\r\n\r\n      LoadFilingService<FormCorporation>(\r\n        queryString,\r\n        this.Api,\r\n        FilingServiceResponseObject.DynamicFormCorporation,\r\n        this.formMode\r\n      )\r\n        .then(serviceData => {\r\n          this.FormCorporate = serviceData;\r\n          this.Load();\r\n        }).catch(e => console.log(e))\r\n    });\r\n  }\r\n\r\n  updateValidityState() {\r\n    this.Form.updateValueAndValidity();\r\n  }\r\n\r\n  Form = new FormGroup({\r\n    FormationState: new FormControl<string | null>(null),\r\n    FormationType: new FormControl<string | null>(null),\r\n    FilingMode: new FormControl<number | null>(null),\r\n    ExpirationDate: new FormControl<string | null>(null),\r\n    Remarks: new FormControl<string | null>(null, [CustomSharedValidations.specialInstructions]),\r\n  })\r\n\r\n  async onSave(dynamicFormData?) {\r\n    if (!dynamicFormData && this.DynamicFormComponentObj) {\r\n      var enteredDynamicFormData = await this.DynamicFormComponentObj.getDynamicFormData()\r\n    }\r\n\r\n    try {\r\n      if ((!this.selfFilingDynamicFormStates.includes(this.selectedState) && this.Form.controls.FilingMode.value == 2) ||\r\n        (((this.allStatesCode.includes(this.Form.controls.FormationState.value)) && this.Form.controls.FilingMode.value == 1) || (this.Form.controls.FormationState.value == \"CA\"))) {\r\n        await this.UpSellingDataCreation(enteredDynamicFormData);\r\n      }\r\n\r\n      if (this.Form.valid) {\r\n        this.onSaveClick = true;\r\n        setTimeout(() => {\r\n          this.Save(dynamicFormData || enteredDynamicFormData);\r\n        }, 2500);\r\n      }\r\n\r\n      return false;\r\n    }\r\n    catch (ex) {\r\n      console.error(ex)\r\n    }\r\n  }\r\n\r\n  async UpSellingDataCreation(DynamicFormDataValues: any): Promise<any> {\r\n    let upSellingData = [];\r\n    let resolver;\r\n    let respo: Promise<any> = new Promise((res => resolver = res));\r\n    let services: any[] = [];\r\n\r\n    if (this.UpsellingComponentObj.UpSelling_BOI) {\r\n      upSellingData.push(this.UpsellingComponentObj.UpSelling_BOI.getUpSellingData());\r\n      services.push(\"BOI\");\r\n    }\r\n\r\n    if (this.UpsellingComponentObj.UpSelling_EIN) {\r\n      await this.getDependentControlsData(this.upsellingCodes.EINProductCode, this.upsellingCodes.EINCategoryCode, this.upsellingCodes.EINSubCategoryCode).then(data => {\r\n        DynamicFormDataValues.keyValuePair.map((mainCtrl: any) => {\r\n          data.map((dependentCtrl: any) => {\r\n            if (mainCtrl.ControlId == dependentCtrl.controlId) {\r\n              var formcontrol: string = dependentCtrl.dependentControlId\r\n              if (dependentCtrl.dependentControlId == 'Expiration_Date') {\r\n                let date = new Date(data.payload.value)\r\n                this.UpsellingComponentObj.UpSelling_EIN.Form.controls.file_date_y.setValue(date.getDate());\r\n                this.UpsellingComponentObj.UpSelling_EIN.Form.controls.file_date_m.setValue((date.getMonth() + 1));\r\n                this.UpsellingComponentObj.UpSelling_EIN.Form.controls.file_date_d.setValue(date.getFullYear());\r\n              } else {\r\n                this.UpsellingComponentObj.UpSelling_EIN.Form.get(formcontrol).setValue(mainCtrl.Value);\r\n              }\r\n            }\r\n          })\r\n        })\r\n      }).then(() => {\r\n        upSellingData.push(this.UpsellingComponentObj.UpSelling_EIN.getUpSellingData());\r\n        services.push(\"EIN\");\r\n      });\r\n    }\r\n\r\n    if (this.UpsellingComponentObj.UpSelling_SOI) {\r\n      await this.getDependentControlsData(this.upsellingCodes.SOIProductCode, this.upsellingCodes.SOICategoryCode, this.upsellingCodes.SOISubCategoryCode).then(data => {\r\n        DynamicFormDataValues.keyValuePair.map((mainCtrl: any) => {\r\n          data.map((dependentControl: any) => {\r\n            if ((mainCtrl.ControlId == dependentControl.controlId) && mainCtrl.Value) {\r\n              let formcontrol: string = dependentControl.dependentControlId\r\n              this.UpsellingComponentObj.UpSelling_SOI.Form.get(formcontrol).setValue(mainCtrl.Value);\r\n            }\r\n          })\r\n        })\r\n      }).then(() => {\r\n        upSellingData.push(this.UpsellingComponentObj.UpSelling_SOI.getUpSellingData());\r\n        services.push(\"SOI\");\r\n      });\r\n    }\r\n\r\n    if (this.UpsellingComponentObj.UpSelling_CK) {\r\n      await this.getDependentControlsData(this.upsellingCodes.KitsProductCode, this.upsellingCodes.KitsCategoryCode, this.upsellingCodes.KitsSubCategoryCode).then(data => {\r\n        DynamicFormDataValues.keyValuePair.map((mainCtrl: any) => {\r\n          data.map((depCtrl: any) => {\r\n            if (mainCtrl.ControlId == depCtrl.controlId) {\r\n              let formcontrol: string = depCtrl.dependentControlId\r\n              if (depCtrl.dependentControlId == 'EntityName') {\r\n                this.UpsellingComponentObj.UpSelling_CK.Form1.get(formcontrol).setValue(mainCtrl.Value);\r\n              } else if (depCtrl.dependentControlId != 'EntityName') {\r\n                if (depCtrl.dependentControlId == 'FilingStatus' && mainCtrl.Value == 'NO') {\r\n                  this.UpsellingComponentObj.UpSelling_CK.Form2.get(formcontrol).setValue(false);\r\n                } else if (depCtrl.dependentControlId == 'Expiration_Date') {\r\n                  let date = new Date(data.payload.value)\r\n                  this.UpsellingComponentObj.UpSelling_CK.Form2.controls.FormationDate.setValue(date.getDate());\r\n                  this.UpsellingComponentObj.UpSelling_CK.Form2.controls.FormationMonth.setValue((date.getMonth() + 1));\r\n                  this.UpsellingComponentObj.UpSelling_CK.Form2.controls.FormationYear.setValue(date.getFullYear());\r\n                } else if (depCtrl.dependentControlId != 'FilingStatus') {\r\n                  this.UpsellingComponentObj.UpSelling_CK.Form2.get(formcontrol).setValue(mainCtrl.Value);\r\n                } else if (depCtrl.dependentControlId == 'FilingStatus' && mainCtrl.Value == 'YES') {\r\n                  this.UpsellingComponentObj.UpSelling_CK.Form2.get(formcontrol).setValue(true);\r\n                }\r\n              }\r\n            }\r\n          })\r\n        })\r\n      }).then(() => {\r\n        upSellingData.push(this.UpsellingComponentObj.UpSelling_CK.getUpSellingData());\r\n        services.push(\"CK\");\r\n      });\r\n    }\r\n\r\n    await this.mapUpselling(upSellingData, services);\r\n    return resolver(...upSellingData, ...services);\r\n  }\r\n\r\n  async mapUpselling(upSellingData: any[], services: any[]): Promise<any> {\r\n    return services.length > 0 && Promise.all([...upSellingData, ...services]).then(response => {\r\n      if (upSellingData.length <= 0 && services.length <= 0) {\r\n        return;\r\n      }\r\n\r\n      let index_BOI = this.getIndexNo(\"BOI\", services);\r\n      let index_EIN = this.getIndexNo(\"EIN\", services);\r\n      let index_SOI = this.getIndexNo(\"SOI\", services);\r\n      let index_CK = this.getIndexNo(\"CK\", services);\r\n\r\n      if (index_BOI != null) {\r\n        ///To be sent to parent component\r\n        this.beneficialOwnershipInfo = response[index_BOI];\r\n      }\r\n\r\n      if (index_EIN != null) {\r\n        ///To be sent to parent component\r\n        this.EmployerIdentificationNo = response[index_EIN];\r\n      }\r\n\r\n      if (index_SOI != null) {\r\n        ///To be sent to parent component\r\n        this.AnnualReport = response[index_SOI];\r\n      }\r\n\r\n      if (index_CK != null) {\r\n        ///To be sent to parent component\r\n        this.KitOrderVM = response[index_CK];\r\n      }\r\n\r\n      return;\r\n    })\r\n  }\r\n\r\n  upsellingSetvalueCheck(data, ControlValue) {\r\n    return (data.payload && data.payload.value && data.payload.name == ControlValue);\r\n  }\r\n\r\n  private getIndexNo(code, array: any[]) {\r\n    let val = array.findIndex(x => x == code);\r\n    if (val == -1) {\r\n      return null;\r\n    }\r\n    return val;\r\n  }\r\n\r\n  componentsVisibility() {\r\n    return (this.Form.controls.FormationType.value && this.Form.controls.FilingMode.value &&\r\n      ((this.Form.controls.FilingMode.value == 2 && !this.selfFilingDynamicFormStates.includes(this.selectedState) && this.Form.controls.FormationType.value != 'NP') ||\r\n        (this.Form.controls.FilingMode.value == 1 && this.allStatesCode.includes(this.selectedState)) || this.selectedState == 'CA' || this.selectedState == 'ID'));\r\n  }\r\n\r\n  Save(dynamicFormData?) {\r\n\r\n    this.FormCorporate.FormationState = this.Form.controls.FormationState.value;\r\n    this.FormCorporate.FormationType = this.Form.controls.FormationType.value;\r\n    this.FormCorporate.FilingMode = this.Form.controls.FilingMode.value;\r\n    this.FormCorporate.Remarks = this.Form.controls.Remarks.value != null ? this.Form.controls.Remarks.value : null;\r\n\r\n    if (this.FileData) {\r\n      this.FormCorporate.FileData = this.FileData;\r\n    }\r\n    else if (this.selfFilingDynamicFormStates?.includes(this.Form.controls.FormationState.value)) {\r\n      this.FormCorporate.FileData = [];\r\n    }\r\n    else if (this.Form.controls.FilingMode.value == 1) {\r\n      this.toaster.error('File upload required.');\r\n      this.onSaveClick = false;\r\n      return;\r\n    }\r\n    if (dynamicFormData) {\r\n      this.FormCorporate.DynamicFormData = dynamicFormData.keyValuePair || [];\r\n    }\r\n    else {\r\n      this.FormCorporate.DynamicFormData = []\r\n    }\r\n\r\n\r\n    let filingService = {\r\n      dynamicFormCorporation: this.FormCorporate,\r\n      upSelling: {\r\n        BeneficialOwnerInformation: this.beneficialOwnershipInfo,\r\n        annualReport: this.AnnualReport,\r\n        employerIdentificationNumber: this.EmployerIdentificationNo,\r\n        corporateKit: this.KitOrderVM\r\n      },\r\n      productCode: 'FS',\r\n      categoryCode: this.categoryCode,\r\n      subCategoryCode: '010',\r\n      optionCode: \"NA\",\r\n      dynamicFormUploadedFiles: this.dynamicFormUploadedFiles\r\n    }\r\n\r\n    this.Api.SaveDynamicFormFilingService(filingService).subscribe(x => {\r\n      this.Form.reset();\r\n    });\r\n  }\r\n\r\n  fileUplaod(files: File[]) {\r\n    this.dynamicFormUploadedFiles = files;\r\n  }\r\n\r\n  Load() {\r\n    this.getStatePrice(this.FormCorporate.FormationState);\r\n    this.Form.controls.FormationState.setValue(this.FormCorporate.FormationState)\r\n    this.Form.controls.FormationType.setValue(this.FormCorporate.FormationType.trim())\r\n    this.FilingModeComponentObj.filingServiceType = \"CORP\";\r\n    if (this.FormCorporate.CategoryCode == 'EZ') {\r\n      this.isEdit = true\r\n      this.Form.controls.FilingMode.setValue(1);\r\n      this.FilingModeComponentObj.SelectedOnlineMethodChange(1);\r\n    }\r\n    else {\r\n      this.isEdit = true\r\n      this.Form.controls.FilingMode.setValue(2);\r\n      this.FilingModeComponentObj.SelectedOnlineMethodChange(2);\r\n    }\r\n    this.Form.controls.Remarks.setValue(this.FormCorporate.Remarks);\r\n  }\r\n\r\n  ResetForm() {\r\n    this.Form.reset();\r\n  }\r\n\r\n  getStatePrice(state) {\r\n    var request = new StatePriceRequest();\r\n    request.ProductCode = 'FS';\r\n    request.CategoryCode = '001';\r\n    request.SubCategoryCode = '030';\r\n    request.State = state;\r\n    this.Api.GetStateWisePrice(request).subscribe(res => {\r\n      this.price = res.price > 0 ? res.price : this.basePrice;\r\n    });\r\n  }\r\n\r\n  getDependentControlsData(depProductCode, depCategoryCode, depSubcategoryCode) {\r\n    var req: DependentControlReq = {\r\n      state: this.selectedState,\r\n      productCode: 'FS',\r\n      categoryCode: this.Form.controls.FilingMode.value == 1 ? \"EZ\" : '001',\r\n      subCategoryCode: '010',\r\n      entityType: this.Form.controls.FormationType.value,\r\n      dependentProductCode: depProductCode,\r\n      dependentCategoryCode: depCategoryCode,\r\n      dependentSubcategoryCode: depSubcategoryCode\r\n    }\r\n    return this.Api.GetDependentControlsData(req);\r\n  }\r\n\r\n  callSubmit() {\r\n    if (this.Form.controls.FilingMode.value == 2) {\r\n      document.getElementById('__Main1').click();\r\n    } else {\r\n      this.FilingModeComponentObj.uploadFormSubmit()\r\n    }\r\n  }\r\n\r\n  onFocusOutEvent(data, eve) {\r\n    if (data == 'Expiration_Date') {\r\n      var queData = new Date(eve.target.value);\r\n    } else {\r\n      queData = eve.target.value;\r\n    }\r\n    const activeSectionMessage: ComponentMessageModel = new ComponentMessageModel();\r\n    activeSectionMessage.payload = {\r\n      name: data,\r\n      value: queData\r\n    }\r\n    this.ComponentMessags.sendReplayMessage(activeSectionMessage);\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    // when the component get's destroyed, unsubscribe all the subscriptions\r\n    if (this.routesSubscription) {\r\n      this.routesSubscription.unsubscribe();\r\n    }\r\n    if (this.formChangeInterval) {\r\n      this.formChangeInterval.unsubscribe();\r\n    }\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}