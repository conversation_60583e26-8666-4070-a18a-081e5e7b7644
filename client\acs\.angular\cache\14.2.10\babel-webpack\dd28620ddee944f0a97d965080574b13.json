{"ast": null, "code": "'use strict';\n\nvar bind = require('function-bind');\n\nvar GetIntrinsic = require('get-intrinsic');\n\nvar $apply = GetIntrinsic('%Function.prototype.apply%');\nvar $call = GetIntrinsic('%Function.prototype.call%');\nvar $reflectApply = GetIntrinsic('%Reflect.apply%', true) || bind.call($call, $apply);\nvar $gOPD = GetIntrinsic('%Object.getOwnPropertyDescriptor%', true);\nvar $defineProperty = GetIntrinsic('%Object.defineProperty%', true);\nvar $max = GetIntrinsic('%Math.max%');\n\nif ($defineProperty) {\n  try {\n    $defineProperty({}, 'a', {\n      value: 1\n    });\n  } catch (e) {\n    // IE 8 has a broken defineProperty\n    $defineProperty = null;\n  }\n}\n\nmodule.exports = function callBind(originalFunction) {\n  var func = $reflectApply(bind, $call, arguments);\n\n  if ($gOPD && $defineProperty) {\n    var desc = $gOPD(func, 'length');\n\n    if (desc.configurable) {\n      // original length, plus the receiver, minus any additional arguments (after the receiver)\n      $defineProperty(func, 'length', {\n        value: 1 + $max(0, originalFunction.length - (arguments.length - 1))\n      });\n    }\n  }\n\n  return func;\n};\n\nvar applyBind = function applyBind() {\n  return $reflectApply(bind, $apply, arguments);\n};\n\nif ($defineProperty) {\n  $defineProperty(module.exports, 'apply', {\n    value: applyBind\n  });\n} else {\n  module.exports.apply = applyBind;\n}", "map": {"version": 3, "names": ["bind", "require", "GetIntrinsic", "$apply", "$call", "$reflectApply", "call", "$gOPD", "$defineProperty", "$max", "value", "e", "module", "exports", "callBind", "originalFunction", "func", "arguments", "desc", "configurable", "length", "applyBind", "apply"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/call-bind/index.js"], "sourcesContent": ["'use strict';\n\nvar bind = require('function-bind');\nvar GetIntrinsic = require('get-intrinsic');\n\nvar $apply = GetIntrinsic('%Function.prototype.apply%');\nvar $call = GetIntrinsic('%Function.prototype.call%');\nvar $reflectApply = GetIntrinsic('%Reflect.apply%', true) || bind.call($call, $apply);\n\nvar $gOPD = GetIntrinsic('%Object.getOwnPropertyDescriptor%', true);\nvar $defineProperty = GetIntrinsic('%Object.defineProperty%', true);\nvar $max = GetIntrinsic('%Math.max%');\n\nif ($defineProperty) {\n\ttry {\n\t\t$defineProperty({}, 'a', { value: 1 });\n\t} catch (e) {\n\t\t// IE 8 has a broken defineProperty\n\t\t$defineProperty = null;\n\t}\n}\n\nmodule.exports = function callBind(originalFunction) {\n\tvar func = $reflectApply(bind, $call, arguments);\n\tif ($gOPD && $defineProperty) {\n\t\tvar desc = $gOPD(func, 'length');\n\t\tif (desc.configurable) {\n\t\t\t// original length, plus the receiver, minus any additional arguments (after the receiver)\n\t\t\t$defineProperty(\n\t\t\t\tfunc,\n\t\t\t\t'length',\n\t\t\t\t{ value: 1 + $max(0, originalFunction.length - (arguments.length - 1)) }\n\t\t\t);\n\t\t}\n\t}\n\treturn func;\n};\n\nvar applyBind = function applyBind() {\n\treturn $reflectApply(bind, $apply, arguments);\n};\n\nif ($defineProperty) {\n\t$defineProperty(module.exports, 'apply', { value: applyBind });\n} else {\n\tmodule.exports.apply = applyBind;\n}\n"], "mappings": "AAAA;;AAEA,IAAIA,IAAI,GAAGC,OAAO,CAAC,eAAD,CAAlB;;AACA,IAAIC,YAAY,GAAGD,OAAO,CAAC,eAAD,CAA1B;;AAEA,IAAIE,MAAM,GAAGD,YAAY,CAAC,4BAAD,CAAzB;AACA,IAAIE,KAAK,GAAGF,YAAY,CAAC,2BAAD,CAAxB;AACA,IAAIG,aAAa,GAAGH,YAAY,CAAC,iBAAD,EAAoB,IAApB,CAAZ,IAAyCF,IAAI,CAACM,IAAL,CAAUF,KAAV,EAAiBD,MAAjB,CAA7D;AAEA,IAAII,KAAK,GAAGL,YAAY,CAAC,mCAAD,EAAsC,IAAtC,CAAxB;AACA,IAAIM,eAAe,GAAGN,YAAY,CAAC,yBAAD,EAA4B,IAA5B,CAAlC;AACA,IAAIO,IAAI,GAAGP,YAAY,CAAC,YAAD,CAAvB;;AAEA,IAAIM,eAAJ,EAAqB;EACpB,IAAI;IACHA,eAAe,CAAC,EAAD,EAAK,GAAL,EAAU;MAAEE,KAAK,EAAE;IAAT,CAAV,CAAf;EACA,CAFD,CAEE,OAAOC,CAAP,EAAU;IACX;IACAH,eAAe,GAAG,IAAlB;EACA;AACD;;AAEDI,MAAM,CAACC,OAAP,GAAiB,SAASC,QAAT,CAAkBC,gBAAlB,EAAoC;EACpD,IAAIC,IAAI,GAAGX,aAAa,CAACL,IAAD,EAAOI,KAAP,EAAca,SAAd,CAAxB;;EACA,IAAIV,KAAK,IAAIC,eAAb,EAA8B;IAC7B,IAAIU,IAAI,GAAGX,KAAK,CAACS,IAAD,EAAO,QAAP,CAAhB;;IACA,IAAIE,IAAI,CAACC,YAAT,EAAuB;MACtB;MACAX,eAAe,CACdQ,IADc,EAEd,QAFc,EAGd;QAAEN,KAAK,EAAE,IAAID,IAAI,CAAC,CAAD,EAAIM,gBAAgB,CAACK,MAAjB,IAA2BH,SAAS,CAACG,MAAV,GAAmB,CAA9C,CAAJ;MAAjB,CAHc,CAAf;IAKA;EACD;;EACD,OAAOJ,IAAP;AACA,CAdD;;AAgBA,IAAIK,SAAS,GAAG,SAASA,SAAT,GAAqB;EACpC,OAAOhB,aAAa,CAACL,IAAD,EAAOG,MAAP,EAAec,SAAf,CAApB;AACA,CAFD;;AAIA,IAAIT,eAAJ,EAAqB;EACpBA,eAAe,CAACI,MAAM,CAACC,OAAR,EAAiB,OAAjB,EAA0B;IAAEH,KAAK,EAAEW;EAAT,CAA1B,CAAf;AACA,CAFD,MAEO;EACNT,MAAM,CAACC,OAAP,CAAeS,KAAf,GAAuBD,SAAvB;AACA"}, "metadata": {}, "sourceType": "script"}