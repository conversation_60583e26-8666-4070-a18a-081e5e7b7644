{"ast": null, "code": "import { Subscriber } from './Subscriber';\nimport { Observable } from './Observable';\nimport { subscribeTo } from './util/subscribeTo';\nexport class SimpleInnerSubscriber extends Subscriber {\n  constructor(parent) {\n    super();\n    this.parent = parent;\n  }\n\n  _next(value) {\n    this.parent.notifyNext(value);\n  }\n\n  _error(error) {\n    this.parent.notifyError(error);\n    this.unsubscribe();\n  }\n\n  _complete() {\n    this.parent.notifyComplete();\n    this.unsubscribe();\n  }\n\n}\nexport class ComplexInnerSubscriber extends Subscriber {\n  constructor(parent, outerValue, outerIndex) {\n    super();\n    this.parent = parent;\n    this.outerValue = outerValue;\n    this.outerIndex = outerIndex;\n  }\n\n  _next(value) {\n    this.parent.notifyNext(this.outerValue, value, this.outerIndex, this);\n  }\n\n  _error(error) {\n    this.parent.notifyError(error);\n    this.unsubscribe();\n  }\n\n  _complete() {\n    this.parent.notifyComplete(this);\n    this.unsubscribe();\n  }\n\n}\nexport class SimpleOuterSubscriber extends Subscriber {\n  notifyNext(innerValue) {\n    this.destination.next(innerValue);\n  }\n\n  notifyError(err) {\n    this.destination.error(err);\n  }\n\n  notifyComplete() {\n    this.destination.complete();\n  }\n\n}\nexport class ComplexOuterSubscriber extends Subscriber {\n  notifyNext(_outerValue, innerValue, _outerIndex, _innerSub) {\n    this.destination.next(innerValue);\n  }\n\n  notifyError(error) {\n    this.destination.error(error);\n  }\n\n  notifyComplete(_innerSub) {\n    this.destination.complete();\n  }\n\n}\nexport function innerSubscribe(result, innerSubscriber) {\n  if (innerSubscriber.closed) {\n    return undefined;\n  }\n\n  if (result instanceof Observable) {\n    return result.subscribe(innerSubscriber);\n  }\n\n  let subscription;\n\n  try {\n    subscription = subscribeTo(result)(innerSubscriber);\n  } catch (error) {\n    innerSubscriber.error(error);\n  }\n\n  return subscription;\n}", "map": {"version": 3, "names": ["Subscriber", "Observable", "subscribeTo", "SimpleInnerSubscriber", "constructor", "parent", "_next", "value", "notifyNext", "_error", "error", "notifyError", "unsubscribe", "_complete", "notifyComplete", "ComplexInnerSubscriber", "outerValue", "outerIndex", "SimpleOuterSubscriber", "innerValue", "destination", "next", "err", "complete", "ComplexOuterSubscriber", "_outerValue", "_outerIndex", "_innerSub", "innerSubscribe", "result", "innerSubscriber", "closed", "undefined", "subscribe", "subscription"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/innerSubscribe.js"], "sourcesContent": ["import { Subscriber } from './Subscriber';\nimport { Observable } from './Observable';\nimport { subscribeTo } from './util/subscribeTo';\nexport class SimpleInnerSubscriber extends Subscriber {\n    constructor(parent) {\n        super();\n        this.parent = parent;\n    }\n    _next(value) {\n        this.parent.notifyNext(value);\n    }\n    _error(error) {\n        this.parent.notifyError(error);\n        this.unsubscribe();\n    }\n    _complete() {\n        this.parent.notifyComplete();\n        this.unsubscribe();\n    }\n}\nexport class ComplexInnerSubscriber extends Subscriber {\n    constructor(parent, outerValue, outerIndex) {\n        super();\n        this.parent = parent;\n        this.outerValue = outerValue;\n        this.outerIndex = outerIndex;\n    }\n    _next(value) {\n        this.parent.notifyNext(this.outerValue, value, this.outerIndex, this);\n    }\n    _error(error) {\n        this.parent.notifyError(error);\n        this.unsubscribe();\n    }\n    _complete() {\n        this.parent.notifyComplete(this);\n        this.unsubscribe();\n    }\n}\nexport class SimpleOuterSubscriber extends Subscriber {\n    notifyNext(innerValue) {\n        this.destination.next(innerValue);\n    }\n    notifyError(err) {\n        this.destination.error(err);\n    }\n    notifyComplete() {\n        this.destination.complete();\n    }\n}\nexport class ComplexOuterSubscriber extends Subscriber {\n    notifyNext(_outerValue, innerValue, _outerIndex, _innerSub) {\n        this.destination.next(innerValue);\n    }\n    notifyError(error) {\n        this.destination.error(error);\n    }\n    notifyComplete(_innerSub) {\n        this.destination.complete();\n    }\n}\nexport function innerSubscribe(result, innerSubscriber) {\n    if (innerSubscriber.closed) {\n        return undefined;\n    }\n    if (result instanceof Observable) {\n        return result.subscribe(innerSubscriber);\n    }\n    let subscription;\n    try {\n        subscription = subscribeTo(result)(innerSubscriber);\n    }\n    catch (error) {\n        innerSubscriber.error(error);\n    }\n    return subscription;\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,cAA3B;AACA,SAASC,UAAT,QAA2B,cAA3B;AACA,SAASC,WAAT,QAA4B,oBAA5B;AACA,OAAO,MAAMC,qBAAN,SAAoCH,UAApC,CAA+C;EAClDI,WAAW,CAACC,MAAD,EAAS;IAChB;IACA,KAAKA,MAAL,GAAcA,MAAd;EACH;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACT,KAAKF,MAAL,CAAYG,UAAZ,CAAuBD,KAAvB;EACH;;EACDE,MAAM,CAACC,KAAD,EAAQ;IACV,KAAKL,MAAL,CAAYM,WAAZ,CAAwBD,KAAxB;IACA,KAAKE,WAAL;EACH;;EACDC,SAAS,GAAG;IACR,KAAKR,MAAL,CAAYS,cAAZ;IACA,KAAKF,WAAL;EACH;;AAfiD;AAiBtD,OAAO,MAAMG,sBAAN,SAAqCf,UAArC,CAAgD;EACnDI,WAAW,CAACC,MAAD,EAASW,UAAT,EAAqBC,UAArB,EAAiC;IACxC;IACA,KAAKZ,MAAL,GAAcA,MAAd;IACA,KAAKW,UAAL,GAAkBA,UAAlB;IACA,KAAKC,UAAL,GAAkBA,UAAlB;EACH;;EACDX,KAAK,CAACC,KAAD,EAAQ;IACT,KAAKF,MAAL,CAAYG,UAAZ,CAAuB,KAAKQ,UAA5B,EAAwCT,KAAxC,EAA+C,KAAKU,UAApD,EAAgE,IAAhE;EACH;;EACDR,MAAM,CAACC,KAAD,EAAQ;IACV,KAAKL,MAAL,CAAYM,WAAZ,CAAwBD,KAAxB;IACA,KAAKE,WAAL;EACH;;EACDC,SAAS,GAAG;IACR,KAAKR,MAAL,CAAYS,cAAZ,CAA2B,IAA3B;IACA,KAAKF,WAAL;EACH;;AAjBkD;AAmBvD,OAAO,MAAMM,qBAAN,SAAoClB,UAApC,CAA+C;EAClDQ,UAAU,CAACW,UAAD,EAAa;IACnB,KAAKC,WAAL,CAAiBC,IAAjB,CAAsBF,UAAtB;EACH;;EACDR,WAAW,CAACW,GAAD,EAAM;IACb,KAAKF,WAAL,CAAiBV,KAAjB,CAAuBY,GAAvB;EACH;;EACDR,cAAc,GAAG;IACb,KAAKM,WAAL,CAAiBG,QAAjB;EACH;;AATiD;AAWtD,OAAO,MAAMC,sBAAN,SAAqCxB,UAArC,CAAgD;EACnDQ,UAAU,CAACiB,WAAD,EAAcN,UAAd,EAA0BO,WAA1B,EAAuCC,SAAvC,EAAkD;IACxD,KAAKP,WAAL,CAAiBC,IAAjB,CAAsBF,UAAtB;EACH;;EACDR,WAAW,CAACD,KAAD,EAAQ;IACf,KAAKU,WAAL,CAAiBV,KAAjB,CAAuBA,KAAvB;EACH;;EACDI,cAAc,CAACa,SAAD,EAAY;IACtB,KAAKP,WAAL,CAAiBG,QAAjB;EACH;;AATkD;AAWvD,OAAO,SAASK,cAAT,CAAwBC,MAAxB,EAAgCC,eAAhC,EAAiD;EACpD,IAAIA,eAAe,CAACC,MAApB,EAA4B;IACxB,OAAOC,SAAP;EACH;;EACD,IAAIH,MAAM,YAAY5B,UAAtB,EAAkC;IAC9B,OAAO4B,MAAM,CAACI,SAAP,CAAiBH,eAAjB,CAAP;EACH;;EACD,IAAII,YAAJ;;EACA,IAAI;IACAA,YAAY,GAAGhC,WAAW,CAAC2B,MAAD,CAAX,CAAoBC,eAApB,CAAf;EACH,CAFD,CAGA,OAAOpB,KAAP,EAAc;IACVoB,eAAe,CAACpB,KAAhB,CAAsBA,KAAtB;EACH;;EACD,OAAOwB,YAAP;AACH"}, "metadata": {}, "sourceType": "module"}