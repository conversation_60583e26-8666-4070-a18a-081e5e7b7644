{"ast": null, "code": "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "map": {"version": 3, "names": ["getHTMLElementScroll", "element", "scrollLeft", "scrollTop"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js"], "sourcesContent": ["export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}"], "mappings": "AAAA,eAAe,SAASA,oBAAT,CAA8BC,OAA9B,EAAuC;EACpD,OAAO;IACLC,UAAU,EAAED,OAAO,CAACC,UADf;IAELC,SAAS,EAAEF,OAAO,CAACE;EAFd,CAAP;AAID"}, "metadata": {}, "sourceType": "module"}