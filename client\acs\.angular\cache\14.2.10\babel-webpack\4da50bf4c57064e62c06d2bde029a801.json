{"ast": null, "code": ";\n\n(function (root, factory) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var Base = C_lib.Base;\n    var C_enc = C.enc;\n    var Utf8 = C_enc.Utf8;\n    var C_algo = C.algo;\n    /**\n     * HMAC algorithm.\n     */\n\n    var HMAC = C_algo.HMAC = Base.extend({\n      /**\n       * Initializes a newly created HMAC.\n       *\n       * @param {Hasher} hasher The hash algorithm to use.\n       * @param {WordArray|string} key The secret key.\n       *\n       * @example\n       *\n       *     var hmacHasher = CryptoJS.algo.HMAC.create(CryptoJS.algo.SHA256, key);\n       */\n      init: function (hasher, key) {\n        // Init hasher\n        hasher = this._hasher = new hasher.init(); // Convert string to WordArray, else assume WordArray already\n\n        if (typeof key == 'string') {\n          key = Utf8.parse(key);\n        } // Shortcuts\n\n\n        var hasherBlockSize = hasher.blockSize;\n        var hasherBlockSizeBytes = hasherBlockSize * 4; // Allow arbitrary length keys\n\n        if (key.sigBytes > hasherBlockSizeBytes) {\n          key = hasher.finalize(key);\n        } // Clamp excess bits\n\n\n        key.clamp(); // Clone key for inner and outer pads\n\n        var oKey = this._oKey = key.clone();\n        var iKey = this._iKey = key.clone(); // Shortcuts\n\n        var oKeyWords = oKey.words;\n        var iKeyWords = iKey.words; // XOR keys with pad constants\n\n        for (var i = 0; i < hasherBlockSize; i++) {\n          oKeyWords[i] ^= 0x5c5c5c5c;\n          iKeyWords[i] ^= 0x36363636;\n        }\n\n        oKey.sigBytes = iKey.sigBytes = hasherBlockSizeBytes; // Set initial values\n\n        this.reset();\n      },\n\n      /**\n       * Resets this HMAC to its initial state.\n       *\n       * @example\n       *\n       *     hmacHasher.reset();\n       */\n      reset: function () {\n        // Shortcut\n        var hasher = this._hasher; // Reset\n\n        hasher.reset();\n        hasher.update(this._iKey);\n      },\n\n      /**\n       * Updates this HMAC with a message.\n       *\n       * @param {WordArray|string} messageUpdate The message to append.\n       *\n       * @return {HMAC} This HMAC instance.\n       *\n       * @example\n       *\n       *     hmacHasher.update('message');\n       *     hmacHasher.update(wordArray);\n       */\n      update: function (messageUpdate) {\n        this._hasher.update(messageUpdate); // Chainable\n\n\n        return this;\n      },\n\n      /**\n       * Finalizes the HMAC computation.\n       * Note that the finalize operation is effectively a destructive, read-once operation.\n       *\n       * @param {WordArray|string} messageUpdate (Optional) A final message update.\n       *\n       * @return {WordArray} The HMAC.\n       *\n       * @example\n       *\n       *     var hmac = hmacHasher.finalize();\n       *     var hmac = hmacHasher.finalize('message');\n       *     var hmac = hmacHasher.finalize(wordArray);\n       */\n      finalize: function (messageUpdate) {\n        // Shortcut\n        var hasher = this._hasher; // Compute HMAC\n\n        var innerHash = hasher.finalize(messageUpdate);\n        hasher.reset();\n        var hmac = hasher.finalize(this._oKey.clone().concat(innerHash));\n        return hmac;\n      }\n    });\n  })();\n});", "map": {"version": 3, "names": ["root", "factory", "exports", "module", "require", "define", "amd", "CryptoJS", "C", "C_lib", "lib", "Base", "C_enc", "enc", "Utf8", "C_algo", "algo", "HMAC", "extend", "init", "hasher", "key", "_hasher", "parse", "hasherBlockSize", "blockSize", "hasherBlockSizeBytes", "sigBytes", "finalize", "clamp", "o<PERSON><PERSON>", "_o<PERSON>ey", "clone", "i<PERSON>ey", "_i<PERSON><PERSON>", "oKeyWords", "words", "iKeyWords", "i", "reset", "update", "messageUpdate", "innerHash", "hmac", "concat"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/crypto-js/hmac.js"], "sourcesContent": [";(function (root, factory) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t(function () {\n\t    // Shortcuts\n\t    var C = CryptoJS;\n\t    var C_lib = C.lib;\n\t    var Base = C_lib.Base;\n\t    var C_enc = C.enc;\n\t    var Utf8 = C_enc.Utf8;\n\t    var C_algo = C.algo;\n\n\t    /**\n\t     * HMAC algorithm.\n\t     */\n\t    var HMAC = C_algo.HMAC = Base.extend({\n\t        /**\n\t         * Initializes a newly created HMAC.\n\t         *\n\t         * @param {Hasher} hasher The hash algorithm to use.\n\t         * @param {WordArray|string} key The secret key.\n\t         *\n\t         * @example\n\t         *\n\t         *     var hmacHasher = CryptoJS.algo.HMAC.create(CryptoJS.algo.SHA256, key);\n\t         */\n\t        init: function (hasher, key) {\n\t            // Init hasher\n\t            hasher = this._hasher = new hasher.init();\n\n\t            // Convert string to WordArray, else assume WordArray already\n\t            if (typeof key == 'string') {\n\t                key = Utf8.parse(key);\n\t            }\n\n\t            // Shortcuts\n\t            var hasherBlockSize = hasher.blockSize;\n\t            var hasherBlockSizeBytes = hasherBlockSize * 4;\n\n\t            // Allow arbitrary length keys\n\t            if (key.sigBytes > hasherBlockSizeBytes) {\n\t                key = hasher.finalize(key);\n\t            }\n\n\t            // Clamp excess bits\n\t            key.clamp();\n\n\t            // Clone key for inner and outer pads\n\t            var oKey = this._oKey = key.clone();\n\t            var iKey = this._iKey = key.clone();\n\n\t            // Shortcuts\n\t            var oKeyWords = oKey.words;\n\t            var iKeyWords = iKey.words;\n\n\t            // XOR keys with pad constants\n\t            for (var i = 0; i < hasherBlockSize; i++) {\n\t                oKeyWords[i] ^= 0x5c5c5c5c;\n\t                iKeyWords[i] ^= 0x36363636;\n\t            }\n\t            oKey.sigBytes = iKey.sigBytes = hasherBlockSizeBytes;\n\n\t            // Set initial values\n\t            this.reset();\n\t        },\n\n\t        /**\n\t         * Resets this HMAC to its initial state.\n\t         *\n\t         * @example\n\t         *\n\t         *     hmacHasher.reset();\n\t         */\n\t        reset: function () {\n\t            // Shortcut\n\t            var hasher = this._hasher;\n\n\t            // Reset\n\t            hasher.reset();\n\t            hasher.update(this._iKey);\n\t        },\n\n\t        /**\n\t         * Updates this HMAC with a message.\n\t         *\n\t         * @param {WordArray|string} messageUpdate The message to append.\n\t         *\n\t         * @return {HMAC} This HMAC instance.\n\t         *\n\t         * @example\n\t         *\n\t         *     hmacHasher.update('message');\n\t         *     hmacHasher.update(wordArray);\n\t         */\n\t        update: function (messageUpdate) {\n\t            this._hasher.update(messageUpdate);\n\n\t            // Chainable\n\t            return this;\n\t        },\n\n\t        /**\n\t         * Finalizes the HMAC computation.\n\t         * Note that the finalize operation is effectively a destructive, read-once operation.\n\t         *\n\t         * @param {WordArray|string} messageUpdate (Optional) A final message update.\n\t         *\n\t         * @return {WordArray} The HMAC.\n\t         *\n\t         * @example\n\t         *\n\t         *     var hmac = hmacHasher.finalize();\n\t         *     var hmac = hmacHasher.finalize('message');\n\t         *     var hmac = hmacHasher.finalize(wordArray);\n\t         */\n\t        finalize: function (messageUpdate) {\n\t            // Shortcut\n\t            var hasher = this._hasher;\n\n\t            // Compute HMAC\n\t            var innerHash = hasher.finalize(messageUpdate);\n\t            hasher.reset();\n\t            var hmac = hasher.finalize(this._oKey.clone().concat(innerHash));\n\n\t            return hmac;\n\t        }\n\t    });\n\t}());\n\n\n}));"], "mappings": "AAAA;;AAAE,WAAUA,IAAV,EAAgBC,OAAhB,EAAyB;EAC1B,IAAI,OAAOC,OAAP,KAAmB,QAAvB,EAAiC;IAChC;IACAC,MAAM,CAACD,OAAP,GAAiBA,OAAO,GAAGD,OAAO,CAACG,OAAO,CAAC,QAAD,CAAR,CAAlC;EACA,CAHD,MAIK,IAAI,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAA3C,EAAgD;IACpD;IACAD,MAAM,CAAC,CAAC,QAAD,CAAD,EAAaJ,OAAb,CAAN;EACA,CAHI,MAIA;IACJ;IACAA,OAAO,CAACD,IAAI,CAACO,QAAN,CAAP;EACA;AACD,CAbC,EAaA,IAbA,EAaM,UAAUA,QAAV,EAAoB;EAE1B,aAAY;IACT;IACA,IAAIC,CAAC,GAAGD,QAAR;IACA,IAAIE,KAAK,GAAGD,CAAC,CAACE,GAAd;IACA,IAAIC,IAAI,GAAGF,KAAK,CAACE,IAAjB;IACA,IAAIC,KAAK,GAAGJ,CAAC,CAACK,GAAd;IACA,IAAIC,IAAI,GAAGF,KAAK,CAACE,IAAjB;IACA,IAAIC,MAAM,GAAGP,CAAC,CAACQ,IAAf;IAEA;AACL;AACA;;IACK,IAAIC,IAAI,GAAGF,MAAM,CAACE,IAAP,GAAcN,IAAI,CAACO,MAAL,CAAY;MACjC;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSC,IAAI,EAAE,UAAUC,MAAV,EAAkBC,GAAlB,EAAuB;QACzB;QACAD,MAAM,GAAG,KAAKE,OAAL,GAAe,IAAIF,MAAM,CAACD,IAAX,EAAxB,CAFyB,CAIzB;;QACA,IAAI,OAAOE,GAAP,IAAc,QAAlB,EAA4B;UACxBA,GAAG,GAAGP,IAAI,CAACS,KAAL,CAAWF,GAAX,CAAN;QACH,CAPwB,CASzB;;;QACA,IAAIG,eAAe,GAAGJ,MAAM,CAACK,SAA7B;QACA,IAAIC,oBAAoB,GAAGF,eAAe,GAAG,CAA7C,CAXyB,CAazB;;QACA,IAAIH,GAAG,CAACM,QAAJ,GAAeD,oBAAnB,EAAyC;UACrCL,GAAG,GAAGD,MAAM,CAACQ,QAAP,CAAgBP,GAAhB,CAAN;QACH,CAhBwB,CAkBzB;;;QACAA,GAAG,CAACQ,KAAJ,GAnByB,CAqBzB;;QACA,IAAIC,IAAI,GAAG,KAAKC,KAAL,GAAaV,GAAG,CAACW,KAAJ,EAAxB;QACA,IAAIC,IAAI,GAAG,KAAKC,KAAL,GAAab,GAAG,CAACW,KAAJ,EAAxB,CAvByB,CAyBzB;;QACA,IAAIG,SAAS,GAAGL,IAAI,CAACM,KAArB;QACA,IAAIC,SAAS,GAAGJ,IAAI,CAACG,KAArB,CA3ByB,CA6BzB;;QACA,KAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGd,eAApB,EAAqCc,CAAC,EAAtC,EAA0C;UACtCH,SAAS,CAACG,CAAD,CAAT,IAAgB,UAAhB;UACAD,SAAS,CAACC,CAAD,CAAT,IAAgB,UAAhB;QACH;;QACDR,IAAI,CAACH,QAAL,GAAgBM,IAAI,CAACN,QAAL,GAAgBD,oBAAhC,CAlCyB,CAoCzB;;QACA,KAAKa,KAAL;MACH,CAjDgC;;MAmDjC;AACT;AACA;AACA;AACA;AACA;AACA;MACSA,KAAK,EAAE,YAAY;QACf;QACA,IAAInB,MAAM,GAAG,KAAKE,OAAlB,CAFe,CAIf;;QACAF,MAAM,CAACmB,KAAP;QACAnB,MAAM,CAACoB,MAAP,CAAc,KAAKN,KAAnB;MACH,CAjEgC;;MAmEjC;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSM,MAAM,EAAE,UAAUC,aAAV,EAAyB;QAC7B,KAAKnB,OAAL,CAAakB,MAAb,CAAoBC,aAApB,EAD6B,CAG7B;;;QACA,OAAO,IAAP;MACH,CApFgC;;MAsFjC;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSb,QAAQ,EAAE,UAAUa,aAAV,EAAyB;QAC/B;QACA,IAAIrB,MAAM,GAAG,KAAKE,OAAlB,CAF+B,CAI/B;;QACA,IAAIoB,SAAS,GAAGtB,MAAM,CAACQ,QAAP,CAAgBa,aAAhB,CAAhB;QACArB,MAAM,CAACmB,KAAP;QACA,IAAII,IAAI,GAAGvB,MAAM,CAACQ,QAAP,CAAgB,KAAKG,KAAL,CAAWC,KAAX,GAAmBY,MAAnB,CAA0BF,SAA1B,CAAhB,CAAX;QAEA,OAAOC,IAAP;MACH;IA9GgC,CAAZ,CAAzB;EAgHH,CA5HA,GAAD;AA+HA,CA9IC,CAAD"}, "metadata": {}, "sourceType": "script"}