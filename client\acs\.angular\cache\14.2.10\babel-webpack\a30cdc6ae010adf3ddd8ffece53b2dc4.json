{"ast": null, "code": "import * as i1 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, Optional, Inject, Input, NgModule } from '@angular/core';\nimport { coerceElement, coerceNumberProperty, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { EMPTY, Subject, fromEvent } from 'rxjs';\nimport { auditTime, takeUntil } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Options to pass to the animationstart listener. */\n\nconst listenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/**\n * An injectable service that can be used to monitor the autofill state of an input.\n * Based on the following blog post:\n * https://medium.com/@brunn/detecting-autofilled-fields-in-javascript-aed598d25da7\n */\n\nclass AutofillMonitor {\n  constructor(_platform, _ngZone) {\n    this._platform = _platform;\n    this._ngZone = _ngZone;\n    this._monitoredElements = new Map();\n  }\n\n  monitor(elementOrRef) {\n    if (!this._platform.isBrowser) {\n      return EMPTY;\n    }\n\n    const element = coerceElement(elementOrRef);\n\n    const info = this._monitoredElements.get(element);\n\n    if (info) {\n      return info.subject;\n    }\n\n    const result = new Subject();\n    const cssClass = 'cdk-text-field-autofilled';\n\n    const listener = event => {\n      // Animation events fire on initial element render, we check for the presence of the autofill\n      // CSS class to make sure this is a real change in state, not just the initial render before\n      // we fire off events.\n      if (event.animationName === 'cdk-text-field-autofill-start' && !element.classList.contains(cssClass)) {\n        element.classList.add(cssClass);\n\n        this._ngZone.run(() => result.next({\n          target: event.target,\n          isAutofilled: true\n        }));\n      } else if (event.animationName === 'cdk-text-field-autofill-end' && element.classList.contains(cssClass)) {\n        element.classList.remove(cssClass);\n\n        this._ngZone.run(() => result.next({\n          target: event.target,\n          isAutofilled: false\n        }));\n      }\n    };\n\n    this._ngZone.runOutsideAngular(() => {\n      element.addEventListener('animationstart', listener, listenerOptions);\n      element.classList.add('cdk-text-field-autofill-monitored');\n    });\n\n    this._monitoredElements.set(element, {\n      subject: result,\n      unlisten: () => {\n        element.removeEventListener('animationstart', listener, listenerOptions);\n      }\n    });\n\n    return result;\n  }\n\n  stopMonitoring(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n\n    const info = this._monitoredElements.get(element);\n\n    if (info) {\n      info.unlisten();\n      info.subject.complete();\n      element.classList.remove('cdk-text-field-autofill-monitored');\n      element.classList.remove('cdk-text-field-autofilled');\n\n      this._monitoredElements.delete(element);\n    }\n  }\n\n  ngOnDestroy() {\n    this._monitoredElements.forEach((_info, element) => this.stopMonitoring(element));\n  }\n\n}\n\nAutofillMonitor.ɵfac = function AutofillMonitor_Factory(t) {\n  return new (t || AutofillMonitor)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(i0.NgZone));\n};\n\nAutofillMonitor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: AutofillMonitor,\n  factory: AutofillMonitor.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutofillMonitor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.Platform\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\n/** A directive that can be used to monitor the autofill state of an input. */\n\n\nclass CdkAutofill {\n  constructor(_elementRef, _autofillMonitor) {\n    this._elementRef = _elementRef;\n    this._autofillMonitor = _autofillMonitor;\n    /** Emits when the autofill state of the element changes. */\n\n    this.cdkAutofill = new EventEmitter();\n  }\n\n  ngOnInit() {\n    this._autofillMonitor.monitor(this._elementRef).subscribe(event => this.cdkAutofill.emit(event));\n  }\n\n  ngOnDestroy() {\n    this._autofillMonitor.stopMonitoring(this._elementRef);\n  }\n\n}\n\nCdkAutofill.ɵfac = function CdkAutofill_Factory(t) {\n  return new (t || CdkAutofill)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(AutofillMonitor));\n};\n\nCdkAutofill.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkAutofill,\n  selectors: [[\"\", \"cdkAutofill\", \"\"]],\n  outputs: {\n    cdkAutofill: \"cdkAutofill\"\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAutofill, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkAutofill]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: AutofillMonitor\n    }];\n  }, {\n    cdkAutofill: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Directive to automatically resize a textarea to fit its content. */\n\n\nclass CdkTextareaAutosize {\n  constructor(_elementRef, _platform, _ngZone,\n  /** @breaking-change 11.0.0 make document required */\n  document) {\n    this._elementRef = _elementRef;\n    this._platform = _platform;\n    this._ngZone = _ngZone;\n    this._destroyed = new Subject();\n    this._enabled = true;\n    /**\n     * Value of minRows as of last resize. If the minRows has decreased, the\n     * height of the textarea needs to be recomputed to reflect the new minimum. The maxHeight\n     * does not have the same problem because it does not affect the textarea's scrollHeight.\n     */\n\n    this._previousMinRows = -1;\n    this._isViewInited = false;\n    /** Handles `focus` and `blur` events. */\n\n    this._handleFocusEvent = event => {\n      this._hasFocus = event.type === 'focus';\n    };\n\n    this._document = document;\n    this._textareaElement = this._elementRef.nativeElement;\n  }\n  /** Minimum amount of rows in the textarea. */\n\n\n  get minRows() {\n    return this._minRows;\n  }\n\n  set minRows(value) {\n    this._minRows = coerceNumberProperty(value);\n\n    this._setMinHeight();\n  }\n  /** Maximum amount of rows in the textarea. */\n\n\n  get maxRows() {\n    return this._maxRows;\n  }\n\n  set maxRows(value) {\n    this._maxRows = coerceNumberProperty(value);\n\n    this._setMaxHeight();\n  }\n  /** Whether autosizing is enabled or not */\n\n\n  get enabled() {\n    return this._enabled;\n  }\n\n  set enabled(value) {\n    value = coerceBooleanProperty(value); // Only act if the actual value changed. This specifically helps to not run\n    // resizeToFitContent too early (i.e. before ngAfterViewInit)\n\n    if (this._enabled !== value) {\n      (this._enabled = value) ? this.resizeToFitContent(true) : this.reset();\n    }\n  }\n\n  get placeholder() {\n    return this._textareaElement.placeholder;\n  }\n\n  set placeholder(value) {\n    this._cachedPlaceholderHeight = undefined;\n\n    if (value) {\n      this._textareaElement.setAttribute('placeholder', value);\n    } else {\n      this._textareaElement.removeAttribute('placeholder');\n    }\n\n    this._cacheTextareaPlaceholderHeight();\n  }\n  /** Sets the minimum height of the textarea as determined by minRows. */\n\n\n  _setMinHeight() {\n    const minHeight = this.minRows && this._cachedLineHeight ? `${this.minRows * this._cachedLineHeight}px` : null;\n\n    if (minHeight) {\n      this._textareaElement.style.minHeight = minHeight;\n    }\n  }\n  /** Sets the maximum height of the textarea as determined by maxRows. */\n\n\n  _setMaxHeight() {\n    const maxHeight = this.maxRows && this._cachedLineHeight ? `${this.maxRows * this._cachedLineHeight}px` : null;\n\n    if (maxHeight) {\n      this._textareaElement.style.maxHeight = maxHeight;\n    }\n  }\n\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      // Remember the height which we started with in case autosizing is disabled\n      this._initialHeight = this._textareaElement.style.height;\n      this.resizeToFitContent();\n\n      this._ngZone.runOutsideAngular(() => {\n        const window = this._getWindow();\n\n        fromEvent(window, 'resize').pipe(auditTime(16), takeUntil(this._destroyed)).subscribe(() => this.resizeToFitContent(true));\n\n        this._textareaElement.addEventListener('focus', this._handleFocusEvent);\n\n        this._textareaElement.addEventListener('blur', this._handleFocusEvent);\n      });\n\n      this._isViewInited = true;\n      this.resizeToFitContent(true);\n    }\n  }\n\n  ngOnDestroy() {\n    this._textareaElement.removeEventListener('focus', this._handleFocusEvent);\n\n    this._textareaElement.removeEventListener('blur', this._handleFocusEvent);\n\n    this._destroyed.next();\n\n    this._destroyed.complete();\n  }\n  /**\n   * Cache the height of a single-row textarea if it has not already been cached.\n   *\n   * We need to know how large a single \"row\" of a textarea is in order to apply minRows and\n   * maxRows. For the initial version, we will assume that the height of a single line in the\n   * textarea does not ever change.\n   */\n\n\n  _cacheTextareaLineHeight() {\n    if (this._cachedLineHeight) {\n      return;\n    } // Use a clone element because we have to override some styles.\n\n\n    let textareaClone = this._textareaElement.cloneNode(false);\n\n    textareaClone.rows = 1; // Use `position: absolute` so that this doesn't cause a browser layout and use\n    // `visibility: hidden` so that nothing is rendered. Clear any other styles that\n    // would affect the height.\n\n    textareaClone.style.position = 'absolute';\n    textareaClone.style.visibility = 'hidden';\n    textareaClone.style.border = 'none';\n    textareaClone.style.padding = '0';\n    textareaClone.style.height = '';\n    textareaClone.style.minHeight = '';\n    textareaClone.style.maxHeight = ''; // In Firefox it happens that textarea elements are always bigger than the specified amount\n    // of rows. This is because Firefox tries to add extra space for the horizontal scrollbar.\n    // As a workaround that removes the extra space for the scrollbar, we can just set overflow\n    // to hidden. This ensures that there is no invalid calculation of the line height.\n    // See Firefox bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=33654\n\n    textareaClone.style.overflow = 'hidden';\n\n    this._textareaElement.parentNode.appendChild(textareaClone);\n\n    this._cachedLineHeight = textareaClone.clientHeight;\n    textareaClone.remove(); // Min and max heights have to be re-calculated if the cached line height changes\n\n    this._setMinHeight();\n\n    this._setMaxHeight();\n  }\n\n  _measureScrollHeight() {\n    const element = this._textareaElement;\n    const previousMargin = element.style.marginBottom || '';\n    const isFirefox = this._platform.FIREFOX;\n    const needsMarginFiller = isFirefox && this._hasFocus;\n    const measuringClass = isFirefox ? 'cdk-textarea-autosize-measuring-firefox' : 'cdk-textarea-autosize-measuring'; // In some cases the page might move around while we're measuring the `textarea` on Firefox. We\n    // work around it by assigning a temporary margin with the same height as the `textarea` so that\n    // it occupies the same amount of space. See #23233.\n\n    if (needsMarginFiller) {\n      element.style.marginBottom = `${element.clientHeight}px`;\n    } // Reset the textarea height to auto in order to shrink back to its default size.\n    // Also temporarily force overflow:hidden, so scroll bars do not interfere with calculations.\n\n\n    element.classList.add(measuringClass); // The measuring class includes a 2px padding to workaround an issue with Chrome,\n    // so we account for that extra space here by subtracting 4 (2px top + 2px bottom).\n\n    const scrollHeight = element.scrollHeight - 4;\n    element.classList.remove(measuringClass);\n\n    if (needsMarginFiller) {\n      element.style.marginBottom = previousMargin;\n    }\n\n    return scrollHeight;\n  }\n\n  _cacheTextareaPlaceholderHeight() {\n    if (!this._isViewInited || this._cachedPlaceholderHeight != undefined) {\n      return;\n    }\n\n    if (!this.placeholder) {\n      this._cachedPlaceholderHeight = 0;\n      return;\n    }\n\n    const value = this._textareaElement.value;\n    this._textareaElement.value = this._textareaElement.placeholder;\n    this._cachedPlaceholderHeight = this._measureScrollHeight();\n    this._textareaElement.value = value;\n  }\n\n  ngDoCheck() {\n    if (this._platform.isBrowser) {\n      this.resizeToFitContent();\n    }\n  }\n  /**\n   * Resize the textarea to fit its content.\n   * @param force Whether to force a height recalculation. By default the height will be\n   *    recalculated only if the value changed since the last call.\n   */\n\n\n  resizeToFitContent(force = false) {\n    // If autosizing is disabled, just skip everything else\n    if (!this._enabled) {\n      return;\n    }\n\n    this._cacheTextareaLineHeight();\n\n    this._cacheTextareaPlaceholderHeight(); // If we haven't determined the line-height yet, we know we're still hidden and there's no point\n    // in checking the height of the textarea.\n\n\n    if (!this._cachedLineHeight) {\n      return;\n    }\n\n    const textarea = this._elementRef.nativeElement;\n    const value = textarea.value; // Only resize if the value or minRows have changed since these calculations can be expensive.\n\n    if (!force && this._minRows === this._previousMinRows && value === this._previousValue) {\n      return;\n    }\n\n    const scrollHeight = this._measureScrollHeight();\n\n    const height = Math.max(scrollHeight, this._cachedPlaceholderHeight || 0); // Use the scrollHeight to know how large the textarea *would* be if fit its entire value.\n\n    textarea.style.height = `${height}px`;\n\n    this._ngZone.runOutsideAngular(() => {\n      if (typeof requestAnimationFrame !== 'undefined') {\n        requestAnimationFrame(() => this._scrollToCaretPosition(textarea));\n      } else {\n        setTimeout(() => this._scrollToCaretPosition(textarea));\n      }\n    });\n\n    this._previousValue = value;\n    this._previousMinRows = this._minRows;\n  }\n  /**\n   * Resets the textarea to its original size\n   */\n\n\n  reset() {\n    // Do not try to change the textarea, if the initialHeight has not been determined yet\n    // This might potentially remove styles when reset() is called before ngAfterViewInit\n    if (this._initialHeight !== undefined) {\n      this._textareaElement.style.height = this._initialHeight;\n    }\n  }\n\n  _noopInputHandler() {// no-op handler that ensures we're running change detection on input events.\n  }\n  /** Access injected document if available or fallback to global document reference */\n\n\n  _getDocument() {\n    return this._document || document;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n\n\n  _getWindow() {\n    const doc = this._getDocument();\n\n    return doc.defaultView || window;\n  }\n  /**\n   * Scrolls a textarea to the caret position. On Firefox resizing the textarea will\n   * prevent it from scrolling to the caret position. We need to re-set the selection\n   * in order for it to scroll to the proper position.\n   */\n\n\n  _scrollToCaretPosition(textarea) {\n    const {\n      selectionStart,\n      selectionEnd\n    } = textarea; // IE will throw an \"Unspecified error\" if we try to set the selection range after the\n    // element has been removed from the DOM. Assert that the directive hasn't been destroyed\n    // between the time we requested the animation frame and when it was executed.\n    // Also note that we have to assert that the textarea is focused before we set the\n    // selection range. Setting the selection range on a non-focused textarea will cause\n    // it to receive focus on IE and Edge.\n\n    if (!this._destroyed.isStopped && this._hasFocus) {\n      textarea.setSelectionRange(selectionStart, selectionEnd);\n    }\n  }\n\n}\n\nCdkTextareaAutosize.ɵfac = function CdkTextareaAutosize_Factory(t) {\n  return new (t || CdkTextareaAutosize)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(DOCUMENT, 8));\n};\n\nCdkTextareaAutosize.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkTextareaAutosize,\n  selectors: [[\"textarea\", \"cdkTextareaAutosize\", \"\"]],\n  hostAttrs: [\"rows\", \"1\", 1, \"cdk-textarea-autosize\"],\n  hostBindings: function CdkTextareaAutosize_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"input\", function CdkTextareaAutosize_input_HostBindingHandler() {\n        return ctx._noopInputHandler();\n      });\n    }\n  },\n  inputs: {\n    minRows: [\"cdkAutosizeMinRows\", \"minRows\"],\n    maxRows: [\"cdkAutosizeMaxRows\", \"maxRows\"],\n    enabled: [\"cdkTextareaAutosize\", \"enabled\"],\n    placeholder: \"placeholder\"\n  },\n  exportAs: [\"cdkTextareaAutosize\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTextareaAutosize, [{\n    type: Directive,\n    args: [{\n      selector: 'textarea[cdkTextareaAutosize]',\n      exportAs: 'cdkTextareaAutosize',\n      host: {\n        'class': 'cdk-textarea-autosize',\n        // Textarea elements that have the directive applied should have a single row by default.\n        // Browsers normally show two rows by default and therefore this limits the minRows binding.\n        'rows': '1',\n        '(input)': '_noopInputHandler()'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.Platform\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, {\n    minRows: [{\n      type: Input,\n      args: ['cdkAutosizeMinRows']\n    }],\n    maxRows: [{\n      type: Input,\n      args: ['cdkAutosizeMaxRows']\n    }],\n    enabled: [{\n      type: Input,\n      args: ['cdkTextareaAutosize']\n    }],\n    placeholder: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass TextFieldModule {}\n\nTextFieldModule.ɵfac = function TextFieldModule_Factory(t) {\n  return new (t || TextFieldModule)();\n};\n\nTextFieldModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: TextFieldModule,\n  declarations: [CdkAutofill, CdkTextareaAutosize],\n  exports: [CdkAutofill, CdkTextareaAutosize]\n});\nTextFieldModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TextFieldModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [CdkAutofill, CdkTextareaAutosize],\n      exports: [CdkAutofill, CdkTextareaAutosize]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { AutofillMonitor, CdkAutofill, CdkTextareaAutosize, TextFieldModule };", "map": {"version": 3, "names": ["i1", "normalizePassiveListenerOptions", "i0", "Injectable", "EventEmitter", "Directive", "Output", "Optional", "Inject", "Input", "NgModule", "coerceElement", "coerceNumberProperty", "coerceBooleanProperty", "EMPTY", "Subject", "fromEvent", "auditTime", "takeUntil", "DOCUMENT", "listenerOptions", "passive", "AutofillMonitor", "constructor", "_platform", "_ngZone", "_monitoredElements", "Map", "monitor", "elementOrRef", "<PERSON><PERSON><PERSON><PERSON>", "element", "info", "get", "subject", "result", "cssClass", "listener", "event", "animationName", "classList", "contains", "add", "run", "next", "target", "isAutofilled", "remove", "runOutsideAngular", "addEventListener", "set", "unlisten", "removeEventListener", "stopMonitoring", "complete", "delete", "ngOnDestroy", "for<PERSON>ach", "_info", "ɵfac", "Platform", "NgZone", "ɵprov", "type", "args", "providedIn", "CdkAutofill", "_elementRef", "_autofillMonitor", "cdkAutofill", "ngOnInit", "subscribe", "emit", "ElementRef", "ɵdir", "selector", "CdkTextareaAutosize", "document", "_destroyed", "_enabled", "_previousMinRows", "_isViewInited", "_handleFocusEvent", "_hasFocus", "_document", "_textareaElement", "nativeElement", "minRows", "_minRows", "value", "_setMinHeight", "maxRows", "_maxRows", "_setMaxHeight", "enabled", "resizeToFitContent", "reset", "placeholder", "_cachedPlaceholderHeight", "undefined", "setAttribute", "removeAttribute", "_cacheTextareaPlaceholderHeight", "minHeight", "_cachedLineHeight", "style", "maxHeight", "ngAfterViewInit", "_initialHeight", "height", "window", "_getWindow", "pipe", "_cacheTextareaLineHeight", "textareaClone", "cloneNode", "rows", "position", "visibility", "border", "padding", "overflow", "parentNode", "append<PERSON><PERSON><PERSON>", "clientHeight", "_measureScrollHeight", "<PERSON><PERSON><PERSON><PERSON>", "marginBottom", "isFirefox", "FIREFOX", "needsMarginFiller", "measuringClass", "scrollHeight", "ngDoCheck", "force", "textarea", "_previousValue", "Math", "max", "requestAnimationFrame", "_scrollToCaretPosition", "setTimeout", "_noopInputHandler", "_getDocument", "doc", "defaultView", "selectionStart", "selectionEnd", "isStopped", "setSelectionRange", "exportAs", "host", "decorators", "TextFieldModule", "ɵmod", "ɵinj", "declarations", "exports"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/cdk/fesm2020/text-field.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, Optional, Inject, Input, NgModule } from '@angular/core';\nimport { coerceElement, coerceNumberProperty, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { EMPTY, Subject, fromEvent } from 'rxjs';\nimport { auditTime, takeUntil } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Options to pass to the animationstart listener. */\nconst listenerOptions = normalizePassiveListenerOptions({ passive: true });\n/**\n * An injectable service that can be used to monitor the autofill state of an input.\n * Based on the following blog post:\n * https://medium.com/@brunn/detecting-autofilled-fields-in-javascript-aed598d25da7\n */\nclass AutofillMonitor {\n    constructor(_platform, _ngZone) {\n        this._platform = _platform;\n        this._ngZone = _ngZone;\n        this._monitoredElements = new Map();\n    }\n    monitor(elementOrRef) {\n        if (!this._platform.isBrowser) {\n            return EMPTY;\n        }\n        const element = coerceElement(elementOrRef);\n        const info = this._monitoredElements.get(element);\n        if (info) {\n            return info.subject;\n        }\n        const result = new Subject();\n        const cssClass = 'cdk-text-field-autofilled';\n        const listener = ((event) => {\n            // Animation events fire on initial element render, we check for the presence of the autofill\n            // CSS class to make sure this is a real change in state, not just the initial render before\n            // we fire off events.\n            if (event.animationName === 'cdk-text-field-autofill-start' &&\n                !element.classList.contains(cssClass)) {\n                element.classList.add(cssClass);\n                this._ngZone.run(() => result.next({ target: event.target, isAutofilled: true }));\n            }\n            else if (event.animationName === 'cdk-text-field-autofill-end' &&\n                element.classList.contains(cssClass)) {\n                element.classList.remove(cssClass);\n                this._ngZone.run(() => result.next({ target: event.target, isAutofilled: false }));\n            }\n        });\n        this._ngZone.runOutsideAngular(() => {\n            element.addEventListener('animationstart', listener, listenerOptions);\n            element.classList.add('cdk-text-field-autofill-monitored');\n        });\n        this._monitoredElements.set(element, {\n            subject: result,\n            unlisten: () => {\n                element.removeEventListener('animationstart', listener, listenerOptions);\n            },\n        });\n        return result;\n    }\n    stopMonitoring(elementOrRef) {\n        const element = coerceElement(elementOrRef);\n        const info = this._monitoredElements.get(element);\n        if (info) {\n            info.unlisten();\n            info.subject.complete();\n            element.classList.remove('cdk-text-field-autofill-monitored');\n            element.classList.remove('cdk-text-field-autofilled');\n            this._monitoredElements.delete(element);\n        }\n    }\n    ngOnDestroy() {\n        this._monitoredElements.forEach((_info, element) => this.stopMonitoring(element));\n    }\n}\nAutofillMonitor.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: AutofillMonitor, deps: [{ token: i1.Platform }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\nAutofillMonitor.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: AutofillMonitor, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: AutofillMonitor, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.Platform }, { type: i0.NgZone }]; } });\n/** A directive that can be used to monitor the autofill state of an input. */\nclass CdkAutofill {\n    constructor(_elementRef, _autofillMonitor) {\n        this._elementRef = _elementRef;\n        this._autofillMonitor = _autofillMonitor;\n        /** Emits when the autofill state of the element changes. */\n        this.cdkAutofill = new EventEmitter();\n    }\n    ngOnInit() {\n        this._autofillMonitor\n            .monitor(this._elementRef)\n            .subscribe(event => this.cdkAutofill.emit(event));\n    }\n    ngOnDestroy() {\n        this._autofillMonitor.stopMonitoring(this._elementRef);\n    }\n}\nCdkAutofill.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkAutofill, deps: [{ token: i0.ElementRef }, { token: AutofillMonitor }], target: i0.ɵɵFactoryTarget.Directive });\nCdkAutofill.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: CdkAutofill, selector: \"[cdkAutofill]\", outputs: { cdkAutofill: \"cdkAutofill\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkAutofill, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkAutofill]',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: AutofillMonitor }]; }, propDecorators: { cdkAutofill: [{\n                type: Output\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Directive to automatically resize a textarea to fit its content. */\nclass CdkTextareaAutosize {\n    constructor(_elementRef, _platform, _ngZone, \n    /** @breaking-change 11.0.0 make document required */\n    document) {\n        this._elementRef = _elementRef;\n        this._platform = _platform;\n        this._ngZone = _ngZone;\n        this._destroyed = new Subject();\n        this._enabled = true;\n        /**\n         * Value of minRows as of last resize. If the minRows has decreased, the\n         * height of the textarea needs to be recomputed to reflect the new minimum. The maxHeight\n         * does not have the same problem because it does not affect the textarea's scrollHeight.\n         */\n        this._previousMinRows = -1;\n        this._isViewInited = false;\n        /** Handles `focus` and `blur` events. */\n        this._handleFocusEvent = (event) => {\n            this._hasFocus = event.type === 'focus';\n        };\n        this._document = document;\n        this._textareaElement = this._elementRef.nativeElement;\n    }\n    /** Minimum amount of rows in the textarea. */\n    get minRows() {\n        return this._minRows;\n    }\n    set minRows(value) {\n        this._minRows = coerceNumberProperty(value);\n        this._setMinHeight();\n    }\n    /** Maximum amount of rows in the textarea. */\n    get maxRows() {\n        return this._maxRows;\n    }\n    set maxRows(value) {\n        this._maxRows = coerceNumberProperty(value);\n        this._setMaxHeight();\n    }\n    /** Whether autosizing is enabled or not */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        value = coerceBooleanProperty(value);\n        // Only act if the actual value changed. This specifically helps to not run\n        // resizeToFitContent too early (i.e. before ngAfterViewInit)\n        if (this._enabled !== value) {\n            (this._enabled = value) ? this.resizeToFitContent(true) : this.reset();\n        }\n    }\n    get placeholder() {\n        return this._textareaElement.placeholder;\n    }\n    set placeholder(value) {\n        this._cachedPlaceholderHeight = undefined;\n        if (value) {\n            this._textareaElement.setAttribute('placeholder', value);\n        }\n        else {\n            this._textareaElement.removeAttribute('placeholder');\n        }\n        this._cacheTextareaPlaceholderHeight();\n    }\n    /** Sets the minimum height of the textarea as determined by minRows. */\n    _setMinHeight() {\n        const minHeight = this.minRows && this._cachedLineHeight ? `${this.minRows * this._cachedLineHeight}px` : null;\n        if (minHeight) {\n            this._textareaElement.style.minHeight = minHeight;\n        }\n    }\n    /** Sets the maximum height of the textarea as determined by maxRows. */\n    _setMaxHeight() {\n        const maxHeight = this.maxRows && this._cachedLineHeight ? `${this.maxRows * this._cachedLineHeight}px` : null;\n        if (maxHeight) {\n            this._textareaElement.style.maxHeight = maxHeight;\n        }\n    }\n    ngAfterViewInit() {\n        if (this._platform.isBrowser) {\n            // Remember the height which we started with in case autosizing is disabled\n            this._initialHeight = this._textareaElement.style.height;\n            this.resizeToFitContent();\n            this._ngZone.runOutsideAngular(() => {\n                const window = this._getWindow();\n                fromEvent(window, 'resize')\n                    .pipe(auditTime(16), takeUntil(this._destroyed))\n                    .subscribe(() => this.resizeToFitContent(true));\n                this._textareaElement.addEventListener('focus', this._handleFocusEvent);\n                this._textareaElement.addEventListener('blur', this._handleFocusEvent);\n            });\n            this._isViewInited = true;\n            this.resizeToFitContent(true);\n        }\n    }\n    ngOnDestroy() {\n        this._textareaElement.removeEventListener('focus', this._handleFocusEvent);\n        this._textareaElement.removeEventListener('blur', this._handleFocusEvent);\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /**\n     * Cache the height of a single-row textarea if it has not already been cached.\n     *\n     * We need to know how large a single \"row\" of a textarea is in order to apply minRows and\n     * maxRows. For the initial version, we will assume that the height of a single line in the\n     * textarea does not ever change.\n     */\n    _cacheTextareaLineHeight() {\n        if (this._cachedLineHeight) {\n            return;\n        }\n        // Use a clone element because we have to override some styles.\n        let textareaClone = this._textareaElement.cloneNode(false);\n        textareaClone.rows = 1;\n        // Use `position: absolute` so that this doesn't cause a browser layout and use\n        // `visibility: hidden` so that nothing is rendered. Clear any other styles that\n        // would affect the height.\n        textareaClone.style.position = 'absolute';\n        textareaClone.style.visibility = 'hidden';\n        textareaClone.style.border = 'none';\n        textareaClone.style.padding = '0';\n        textareaClone.style.height = '';\n        textareaClone.style.minHeight = '';\n        textareaClone.style.maxHeight = '';\n        // In Firefox it happens that textarea elements are always bigger than the specified amount\n        // of rows. This is because Firefox tries to add extra space for the horizontal scrollbar.\n        // As a workaround that removes the extra space for the scrollbar, we can just set overflow\n        // to hidden. This ensures that there is no invalid calculation of the line height.\n        // See Firefox bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=33654\n        textareaClone.style.overflow = 'hidden';\n        this._textareaElement.parentNode.appendChild(textareaClone);\n        this._cachedLineHeight = textareaClone.clientHeight;\n        textareaClone.remove();\n        // Min and max heights have to be re-calculated if the cached line height changes\n        this._setMinHeight();\n        this._setMaxHeight();\n    }\n    _measureScrollHeight() {\n        const element = this._textareaElement;\n        const previousMargin = element.style.marginBottom || '';\n        const isFirefox = this._platform.FIREFOX;\n        const needsMarginFiller = isFirefox && this._hasFocus;\n        const measuringClass = isFirefox\n            ? 'cdk-textarea-autosize-measuring-firefox'\n            : 'cdk-textarea-autosize-measuring';\n        // In some cases the page might move around while we're measuring the `textarea` on Firefox. We\n        // work around it by assigning a temporary margin with the same height as the `textarea` so that\n        // it occupies the same amount of space. See #23233.\n        if (needsMarginFiller) {\n            element.style.marginBottom = `${element.clientHeight}px`;\n        }\n        // Reset the textarea height to auto in order to shrink back to its default size.\n        // Also temporarily force overflow:hidden, so scroll bars do not interfere with calculations.\n        element.classList.add(measuringClass);\n        // The measuring class includes a 2px padding to workaround an issue with Chrome,\n        // so we account for that extra space here by subtracting 4 (2px top + 2px bottom).\n        const scrollHeight = element.scrollHeight - 4;\n        element.classList.remove(measuringClass);\n        if (needsMarginFiller) {\n            element.style.marginBottom = previousMargin;\n        }\n        return scrollHeight;\n    }\n    _cacheTextareaPlaceholderHeight() {\n        if (!this._isViewInited || this._cachedPlaceholderHeight != undefined) {\n            return;\n        }\n        if (!this.placeholder) {\n            this._cachedPlaceholderHeight = 0;\n            return;\n        }\n        const value = this._textareaElement.value;\n        this._textareaElement.value = this._textareaElement.placeholder;\n        this._cachedPlaceholderHeight = this._measureScrollHeight();\n        this._textareaElement.value = value;\n    }\n    ngDoCheck() {\n        if (this._platform.isBrowser) {\n            this.resizeToFitContent();\n        }\n    }\n    /**\n     * Resize the textarea to fit its content.\n     * @param force Whether to force a height recalculation. By default the height will be\n     *    recalculated only if the value changed since the last call.\n     */\n    resizeToFitContent(force = false) {\n        // If autosizing is disabled, just skip everything else\n        if (!this._enabled) {\n            return;\n        }\n        this._cacheTextareaLineHeight();\n        this._cacheTextareaPlaceholderHeight();\n        // If we haven't determined the line-height yet, we know we're still hidden and there's no point\n        // in checking the height of the textarea.\n        if (!this._cachedLineHeight) {\n            return;\n        }\n        const textarea = this._elementRef.nativeElement;\n        const value = textarea.value;\n        // Only resize if the value or minRows have changed since these calculations can be expensive.\n        if (!force && this._minRows === this._previousMinRows && value === this._previousValue) {\n            return;\n        }\n        const scrollHeight = this._measureScrollHeight();\n        const height = Math.max(scrollHeight, this._cachedPlaceholderHeight || 0);\n        // Use the scrollHeight to know how large the textarea *would* be if fit its entire value.\n        textarea.style.height = `${height}px`;\n        this._ngZone.runOutsideAngular(() => {\n            if (typeof requestAnimationFrame !== 'undefined') {\n                requestAnimationFrame(() => this._scrollToCaretPosition(textarea));\n            }\n            else {\n                setTimeout(() => this._scrollToCaretPosition(textarea));\n            }\n        });\n        this._previousValue = value;\n        this._previousMinRows = this._minRows;\n    }\n    /**\n     * Resets the textarea to its original size\n     */\n    reset() {\n        // Do not try to change the textarea, if the initialHeight has not been determined yet\n        // This might potentially remove styles when reset() is called before ngAfterViewInit\n        if (this._initialHeight !== undefined) {\n            this._textareaElement.style.height = this._initialHeight;\n        }\n    }\n    _noopInputHandler() {\n        // no-op handler that ensures we're running change detection on input events.\n    }\n    /** Access injected document if available or fallback to global document reference */\n    _getDocument() {\n        return this._document || document;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        const doc = this._getDocument();\n        return doc.defaultView || window;\n    }\n    /**\n     * Scrolls a textarea to the caret position. On Firefox resizing the textarea will\n     * prevent it from scrolling to the caret position. We need to re-set the selection\n     * in order for it to scroll to the proper position.\n     */\n    _scrollToCaretPosition(textarea) {\n        const { selectionStart, selectionEnd } = textarea;\n        // IE will throw an \"Unspecified error\" if we try to set the selection range after the\n        // element has been removed from the DOM. Assert that the directive hasn't been destroyed\n        // between the time we requested the animation frame and when it was executed.\n        // Also note that we have to assert that the textarea is focused before we set the\n        // selection range. Setting the selection range on a non-focused textarea will cause\n        // it to receive focus on IE and Edge.\n        if (!this._destroyed.isStopped && this._hasFocus) {\n            textarea.setSelectionRange(selectionStart, selectionEnd);\n        }\n    }\n}\nCdkTextareaAutosize.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkTextareaAutosize, deps: [{ token: i0.ElementRef }, { token: i1.Platform }, { token: i0.NgZone }, { token: DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nCdkTextareaAutosize.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: CdkTextareaAutosize, selector: \"textarea[cdkTextareaAutosize]\", inputs: { minRows: [\"cdkAutosizeMinRows\", \"minRows\"], maxRows: [\"cdkAutosizeMaxRows\", \"maxRows\"], enabled: [\"cdkTextareaAutosize\", \"enabled\"], placeholder: \"placeholder\" }, host: { attributes: { \"rows\": \"1\" }, listeners: { \"input\": \"_noopInputHandler()\" }, classAttribute: \"cdk-textarea-autosize\" }, exportAs: [\"cdkTextareaAutosize\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkTextareaAutosize, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'textarea[cdkTextareaAutosize]',\n                    exportAs: 'cdkTextareaAutosize',\n                    host: {\n                        'class': 'cdk-textarea-autosize',\n                        // Textarea elements that have the directive applied should have a single row by default.\n                        // Browsers normally show two rows by default and therefore this limits the minRows binding.\n                        'rows': '1',\n                        '(input)': '_noopInputHandler()',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.Platform }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; }, propDecorators: { minRows: [{\n                type: Input,\n                args: ['cdkAutosizeMinRows']\n            }], maxRows: [{\n                type: Input,\n                args: ['cdkAutosizeMaxRows']\n            }], enabled: [{\n                type: Input,\n                args: ['cdkTextareaAutosize']\n            }], placeholder: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass TextFieldModule {\n}\nTextFieldModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: TextFieldModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nTextFieldModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: TextFieldModule, declarations: [CdkAutofill, CdkTextareaAutosize], exports: [CdkAutofill, CdkTextareaAutosize] });\nTextFieldModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: TextFieldModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: TextFieldModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [CdkAutofill, CdkTextareaAutosize],\n                    exports: [CdkAutofill, CdkTextareaAutosize],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutofillMonitor, CdkAutofill, CdkTextareaAutosize, TextFieldModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,uBAApB;AACA,SAASC,+BAAT,QAAgD,uBAAhD;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,YAArB,EAAmCC,SAAnC,EAA8CC,MAA9C,EAAsDC,QAAtD,EAAgEC,MAAhE,EAAwEC,KAAxE,EAA+EC,QAA/E,QAA+F,eAA/F;AACA,SAASC,aAAT,EAAwBC,oBAAxB,EAA8CC,qBAA9C,QAA2E,uBAA3E;AACA,SAASC,KAAT,EAAgBC,OAAhB,EAAyBC,SAAzB,QAA0C,MAA1C;AACA,SAASC,SAAT,EAAoBC,SAApB,QAAqC,gBAArC;AACA,SAASC,QAAT,QAAyB,iBAAzB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;AACA,MAAMC,eAAe,GAAGnB,+BAA+B,CAAC;EAAEoB,OAAO,EAAE;AAAX,CAAD,CAAvD;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,eAAN,CAAsB;EAClBC,WAAW,CAACC,SAAD,EAAYC,OAAZ,EAAqB;IAC5B,KAAKD,SAAL,GAAiBA,SAAjB;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKC,kBAAL,GAA0B,IAAIC,GAAJ,EAA1B;EACH;;EACDC,OAAO,CAACC,YAAD,EAAe;IAClB,IAAI,CAAC,KAAKL,SAAL,CAAeM,SAApB,EAA+B;MAC3B,OAAOhB,KAAP;IACH;;IACD,MAAMiB,OAAO,GAAGpB,aAAa,CAACkB,YAAD,CAA7B;;IACA,MAAMG,IAAI,GAAG,KAAKN,kBAAL,CAAwBO,GAAxB,CAA4BF,OAA5B,CAAb;;IACA,IAAIC,IAAJ,EAAU;MACN,OAAOA,IAAI,CAACE,OAAZ;IACH;;IACD,MAAMC,MAAM,GAAG,IAAIpB,OAAJ,EAAf;IACA,MAAMqB,QAAQ,GAAG,2BAAjB;;IACA,MAAMC,QAAQ,GAAKC,KAAD,IAAW;MACzB;MACA;MACA;MACA,IAAIA,KAAK,CAACC,aAAN,KAAwB,+BAAxB,IACA,CAACR,OAAO,CAACS,SAAR,CAAkBC,QAAlB,CAA2BL,QAA3B,CADL,EAC2C;QACvCL,OAAO,CAACS,SAAR,CAAkBE,GAAlB,CAAsBN,QAAtB;;QACA,KAAKX,OAAL,CAAakB,GAAb,CAAiB,MAAMR,MAAM,CAACS,IAAP,CAAY;UAAEC,MAAM,EAAEP,KAAK,CAACO,MAAhB;UAAwBC,YAAY,EAAE;QAAtC,CAAZ,CAAvB;MACH,CAJD,MAKK,IAAIR,KAAK,CAACC,aAAN,KAAwB,6BAAxB,IACLR,OAAO,CAACS,SAAR,CAAkBC,QAAlB,CAA2BL,QAA3B,CADC,EACqC;QACtCL,OAAO,CAACS,SAAR,CAAkBO,MAAlB,CAAyBX,QAAzB;;QACA,KAAKX,OAAL,CAAakB,GAAb,CAAiB,MAAMR,MAAM,CAACS,IAAP,CAAY;UAAEC,MAAM,EAAEP,KAAK,CAACO,MAAhB;UAAwBC,YAAY,EAAE;QAAtC,CAAZ,CAAvB;MACH;IACJ,CAdD;;IAeA,KAAKrB,OAAL,CAAauB,iBAAb,CAA+B,MAAM;MACjCjB,OAAO,CAACkB,gBAAR,CAAyB,gBAAzB,EAA2CZ,QAA3C,EAAqDjB,eAArD;MACAW,OAAO,CAACS,SAAR,CAAkBE,GAAlB,CAAsB,mCAAtB;IACH,CAHD;;IAIA,KAAKhB,kBAAL,CAAwBwB,GAAxB,CAA4BnB,OAA5B,EAAqC;MACjCG,OAAO,EAAEC,MADwB;MAEjCgB,QAAQ,EAAE,MAAM;QACZpB,OAAO,CAACqB,mBAAR,CAA4B,gBAA5B,EAA8Cf,QAA9C,EAAwDjB,eAAxD;MACH;IAJgC,CAArC;;IAMA,OAAOe,MAAP;EACH;;EACDkB,cAAc,CAACxB,YAAD,EAAe;IACzB,MAAME,OAAO,GAAGpB,aAAa,CAACkB,YAAD,CAA7B;;IACA,MAAMG,IAAI,GAAG,KAAKN,kBAAL,CAAwBO,GAAxB,CAA4BF,OAA5B,CAAb;;IACA,IAAIC,IAAJ,EAAU;MACNA,IAAI,CAACmB,QAAL;MACAnB,IAAI,CAACE,OAAL,CAAaoB,QAAb;MACAvB,OAAO,CAACS,SAAR,CAAkBO,MAAlB,CAAyB,mCAAzB;MACAhB,OAAO,CAACS,SAAR,CAAkBO,MAAlB,CAAyB,2BAAzB;;MACA,KAAKrB,kBAAL,CAAwB6B,MAAxB,CAA+BxB,OAA/B;IACH;EACJ;;EACDyB,WAAW,GAAG;IACV,KAAK9B,kBAAL,CAAwB+B,OAAxB,CAAgC,CAACC,KAAD,EAAQ3B,OAAR,KAAoB,KAAKsB,cAAL,CAAoBtB,OAApB,CAApD;EACH;;AAzDiB;;AA2DtBT,eAAe,CAACqC,IAAhB;EAAA,iBAA4GrC,eAA5G,EAAkGpB,EAAlG,UAA6IF,EAAE,CAAC4D,QAAhJ,GAAkG1D,EAAlG,UAAqKA,EAAE,CAAC2D,MAAxK;AAAA;;AACAvC,eAAe,CAACwC,KAAhB,kBADkG5D,EAClG;EAAA,OAAgHoB,eAAhH;EAAA,SAAgHA,eAAhH;EAAA,YAA6I;AAA7I;;AACA;EAAA,mDAFkGpB,EAElG,mBAA2FoB,eAA3F,EAAwH,CAAC;IAC7GyC,IAAI,EAAE5D,UADuG;IAE7G6D,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAFuG,CAAD,CAAxH,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAE/D,EAAE,CAAC4D;IAAX,CAAD,EAAwB;MAAEG,IAAI,EAAE7D,EAAE,CAAC2D;IAAX,CAAxB,CAAP;EAAsD,CAHhG;AAAA;AAIA;;;AACA,MAAMK,WAAN,CAAkB;EACd3C,WAAW,CAAC4C,WAAD,EAAcC,gBAAd,EAAgC;IACvC,KAAKD,WAAL,GAAmBA,WAAnB;IACA,KAAKC,gBAAL,GAAwBA,gBAAxB;IACA;;IACA,KAAKC,WAAL,GAAmB,IAAIjE,YAAJ,EAAnB;EACH;;EACDkE,QAAQ,GAAG;IACP,KAAKF,gBAAL,CACKxC,OADL,CACa,KAAKuC,WADlB,EAEKI,SAFL,CAEejC,KAAK,IAAI,KAAK+B,WAAL,CAAiBG,IAAjB,CAAsBlC,KAAtB,CAFxB;EAGH;;EACDkB,WAAW,GAAG;IACV,KAAKY,gBAAL,CAAsBf,cAAtB,CAAqC,KAAKc,WAA1C;EACH;;AAda;;AAgBlBD,WAAW,CAACP,IAAZ;EAAA,iBAAwGO,WAAxG,EAvBkGhE,EAuBlG,mBAAqIA,EAAE,CAACuE,UAAxI,GAvBkGvE,EAuBlG,mBAA+JoB,eAA/J;AAAA;;AACA4C,WAAW,CAACQ,IAAZ,kBAxBkGxE,EAwBlG;EAAA,MAA4FgE,WAA5F;EAAA;EAAA;IAAA;EAAA;AAAA;;AACA;EAAA,mDAzBkGhE,EAyBlG,mBAA2FgE,WAA3F,EAAoH,CAAC;IACzGH,IAAI,EAAE1D,SADmG;IAEzG2D,IAAI,EAAE,CAAC;MACCW,QAAQ,EAAE;IADX,CAAD;EAFmG,CAAD,CAApH,EAK4B,YAAY;IAAE,OAAO,CAAC;MAAEZ,IAAI,EAAE7D,EAAE,CAACuE;IAAX,CAAD,EAA0B;MAAEV,IAAI,EAAEzC;IAAR,CAA1B,CAAP;EAA8D,CALxG,EAK0H;IAAE+C,WAAW,EAAE,CAAC;MAC1HN,IAAI,EAAEzD;IADoH,CAAD;EAAf,CAL1H;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMsE,mBAAN,CAA0B;EACtBrD,WAAW,CAAC4C,WAAD,EAAc3C,SAAd,EAAyBC,OAAzB;EACX;EACAoD,QAFW,EAED;IACN,KAAKV,WAAL,GAAmBA,WAAnB;IACA,KAAK3C,SAAL,GAAiBA,SAAjB;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKqD,UAAL,GAAkB,IAAI/D,OAAJ,EAAlB;IACA,KAAKgE,QAAL,GAAgB,IAAhB;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKC,gBAAL,GAAwB,CAAC,CAAzB;IACA,KAAKC,aAAL,GAAqB,KAArB;IACA;;IACA,KAAKC,iBAAL,GAA0B5C,KAAD,IAAW;MAChC,KAAK6C,SAAL,GAAiB7C,KAAK,CAACyB,IAAN,KAAe,OAAhC;IACH,CAFD;;IAGA,KAAKqB,SAAL,GAAiBP,QAAjB;IACA,KAAKQ,gBAAL,GAAwB,KAAKlB,WAAL,CAAiBmB,aAAzC;EACH;EACD;;;EACW,IAAPC,OAAO,GAAG;IACV,OAAO,KAAKC,QAAZ;EACH;;EACU,IAAPD,OAAO,CAACE,KAAD,EAAQ;IACf,KAAKD,QAAL,GAAgB5E,oBAAoB,CAAC6E,KAAD,CAApC;;IACA,KAAKC,aAAL;EACH;EACD;;;EACW,IAAPC,OAAO,GAAG;IACV,OAAO,KAAKC,QAAZ;EACH;;EACU,IAAPD,OAAO,CAACF,KAAD,EAAQ;IACf,KAAKG,QAAL,GAAgBhF,oBAAoB,CAAC6E,KAAD,CAApC;;IACA,KAAKI,aAAL;EACH;EACD;;;EACW,IAAPC,OAAO,GAAG;IACV,OAAO,KAAKf,QAAZ;EACH;;EACU,IAAPe,OAAO,CAACL,KAAD,EAAQ;IACfA,KAAK,GAAG5E,qBAAqB,CAAC4E,KAAD,CAA7B,CADe,CAEf;IACA;;IACA,IAAI,KAAKV,QAAL,KAAkBU,KAAtB,EAA6B;MACzB,CAAC,KAAKV,QAAL,GAAgBU,KAAjB,IAA0B,KAAKM,kBAAL,CAAwB,IAAxB,CAA1B,GAA0D,KAAKC,KAAL,EAA1D;IACH;EACJ;;EACc,IAAXC,WAAW,GAAG;IACd,OAAO,KAAKZ,gBAAL,CAAsBY,WAA7B;EACH;;EACc,IAAXA,WAAW,CAACR,KAAD,EAAQ;IACnB,KAAKS,wBAAL,GAAgCC,SAAhC;;IACA,IAAIV,KAAJ,EAAW;MACP,KAAKJ,gBAAL,CAAsBe,YAAtB,CAAmC,aAAnC,EAAkDX,KAAlD;IACH,CAFD,MAGK;MACD,KAAKJ,gBAAL,CAAsBgB,eAAtB,CAAsC,aAAtC;IACH;;IACD,KAAKC,+BAAL;EACH;EACD;;;EACAZ,aAAa,GAAG;IACZ,MAAMa,SAAS,GAAG,KAAKhB,OAAL,IAAgB,KAAKiB,iBAArB,GAA0C,GAAE,KAAKjB,OAAL,GAAe,KAAKiB,iBAAkB,IAAlF,GAAwF,IAA1G;;IACA,IAAID,SAAJ,EAAe;MACX,KAAKlB,gBAAL,CAAsBoB,KAAtB,CAA4BF,SAA5B,GAAwCA,SAAxC;IACH;EACJ;EACD;;;EACAV,aAAa,GAAG;IACZ,MAAMa,SAAS,GAAG,KAAKf,OAAL,IAAgB,KAAKa,iBAArB,GAA0C,GAAE,KAAKb,OAAL,GAAe,KAAKa,iBAAkB,IAAlF,GAAwF,IAA1G;;IACA,IAAIE,SAAJ,EAAe;MACX,KAAKrB,gBAAL,CAAsBoB,KAAtB,CAA4BC,SAA5B,GAAwCA,SAAxC;IACH;EACJ;;EACDC,eAAe,GAAG;IACd,IAAI,KAAKnF,SAAL,CAAeM,SAAnB,EAA8B;MAC1B;MACA,KAAK8E,cAAL,GAAsB,KAAKvB,gBAAL,CAAsBoB,KAAtB,CAA4BI,MAAlD;MACA,KAAKd,kBAAL;;MACA,KAAKtE,OAAL,CAAauB,iBAAb,CAA+B,MAAM;QACjC,MAAM8D,MAAM,GAAG,KAAKC,UAAL,EAAf;;QACA/F,SAAS,CAAC8F,MAAD,EAAS,QAAT,CAAT,CACKE,IADL,CACU/F,SAAS,CAAC,EAAD,CADnB,EACyBC,SAAS,CAAC,KAAK4D,UAAN,CADlC,EAEKP,SAFL,CAEe,MAAM,KAAKwB,kBAAL,CAAwB,IAAxB,CAFrB;;QAGA,KAAKV,gBAAL,CAAsBpC,gBAAtB,CAAuC,OAAvC,EAAgD,KAAKiC,iBAArD;;QACA,KAAKG,gBAAL,CAAsBpC,gBAAtB,CAAuC,MAAvC,EAA+C,KAAKiC,iBAApD;MACH,CAPD;;MAQA,KAAKD,aAAL,GAAqB,IAArB;MACA,KAAKc,kBAAL,CAAwB,IAAxB;IACH;EACJ;;EACDvC,WAAW,GAAG;IACV,KAAK6B,gBAAL,CAAsBjC,mBAAtB,CAA0C,OAA1C,EAAmD,KAAK8B,iBAAxD;;IACA,KAAKG,gBAAL,CAAsBjC,mBAAtB,CAA0C,MAA1C,EAAkD,KAAK8B,iBAAvD;;IACA,KAAKJ,UAAL,CAAgBlC,IAAhB;;IACA,KAAKkC,UAAL,CAAgBxB,QAAhB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACI2D,wBAAwB,GAAG;IACvB,IAAI,KAAKT,iBAAT,EAA4B;MACxB;IACH,CAHsB,CAIvB;;;IACA,IAAIU,aAAa,GAAG,KAAK7B,gBAAL,CAAsB8B,SAAtB,CAAgC,KAAhC,CAApB;;IACAD,aAAa,CAACE,IAAd,GAAqB,CAArB,CANuB,CAOvB;IACA;IACA;;IACAF,aAAa,CAACT,KAAd,CAAoBY,QAApB,GAA+B,UAA/B;IACAH,aAAa,CAACT,KAAd,CAAoBa,UAApB,GAAiC,QAAjC;IACAJ,aAAa,CAACT,KAAd,CAAoBc,MAApB,GAA6B,MAA7B;IACAL,aAAa,CAACT,KAAd,CAAoBe,OAApB,GAA8B,GAA9B;IACAN,aAAa,CAACT,KAAd,CAAoBI,MAApB,GAA6B,EAA7B;IACAK,aAAa,CAACT,KAAd,CAAoBF,SAApB,GAAgC,EAAhC;IACAW,aAAa,CAACT,KAAd,CAAoBC,SAApB,GAAgC,EAAhC,CAhBuB,CAiBvB;IACA;IACA;IACA;IACA;;IACAQ,aAAa,CAACT,KAAd,CAAoBgB,QAApB,GAA+B,QAA/B;;IACA,KAAKpC,gBAAL,CAAsBqC,UAAtB,CAAiCC,WAAjC,CAA6CT,aAA7C;;IACA,KAAKV,iBAAL,GAAyBU,aAAa,CAACU,YAAvC;IACAV,aAAa,CAACnE,MAAd,GAzBuB,CA0BvB;;IACA,KAAK2C,aAAL;;IACA,KAAKG,aAAL;EACH;;EACDgC,oBAAoB,GAAG;IACnB,MAAM9F,OAAO,GAAG,KAAKsD,gBAArB;IACA,MAAMyC,cAAc,GAAG/F,OAAO,CAAC0E,KAAR,CAAcsB,YAAd,IAA8B,EAArD;IACA,MAAMC,SAAS,GAAG,KAAKxG,SAAL,CAAeyG,OAAjC;IACA,MAAMC,iBAAiB,GAAGF,SAAS,IAAI,KAAK7C,SAA5C;IACA,MAAMgD,cAAc,GAAGH,SAAS,GAC1B,yCAD0B,GAE1B,iCAFN,CALmB,CAQnB;IACA;IACA;;IACA,IAAIE,iBAAJ,EAAuB;MACnBnG,OAAO,CAAC0E,KAAR,CAAcsB,YAAd,GAA8B,GAAEhG,OAAO,CAAC6F,YAAa,IAArD;IACH,CAbkB,CAcnB;IACA;;;IACA7F,OAAO,CAACS,SAAR,CAAkBE,GAAlB,CAAsByF,cAAtB,EAhBmB,CAiBnB;IACA;;IACA,MAAMC,YAAY,GAAGrG,OAAO,CAACqG,YAAR,GAAuB,CAA5C;IACArG,OAAO,CAACS,SAAR,CAAkBO,MAAlB,CAAyBoF,cAAzB;;IACA,IAAID,iBAAJ,EAAuB;MACnBnG,OAAO,CAAC0E,KAAR,CAAcsB,YAAd,GAA6BD,cAA7B;IACH;;IACD,OAAOM,YAAP;EACH;;EACD9B,+BAA+B,GAAG;IAC9B,IAAI,CAAC,KAAKrB,aAAN,IAAuB,KAAKiB,wBAAL,IAAiCC,SAA5D,EAAuE;MACnE;IACH;;IACD,IAAI,CAAC,KAAKF,WAAV,EAAuB;MACnB,KAAKC,wBAAL,GAAgC,CAAhC;MACA;IACH;;IACD,MAAMT,KAAK,GAAG,KAAKJ,gBAAL,CAAsBI,KAApC;IACA,KAAKJ,gBAAL,CAAsBI,KAAtB,GAA8B,KAAKJ,gBAAL,CAAsBY,WAApD;IACA,KAAKC,wBAAL,GAAgC,KAAK2B,oBAAL,EAAhC;IACA,KAAKxC,gBAAL,CAAsBI,KAAtB,GAA8BA,KAA9B;EACH;;EACD4C,SAAS,GAAG;IACR,IAAI,KAAK7G,SAAL,CAAeM,SAAnB,EAA8B;MAC1B,KAAKiE,kBAAL;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;;;EACIA,kBAAkB,CAACuC,KAAK,GAAG,KAAT,EAAgB;IAC9B;IACA,IAAI,CAAC,KAAKvD,QAAV,EAAoB;MAChB;IACH;;IACD,KAAKkC,wBAAL;;IACA,KAAKX,+BAAL,GAN8B,CAO9B;IACA;;;IACA,IAAI,CAAC,KAAKE,iBAAV,EAA6B;MACzB;IACH;;IACD,MAAM+B,QAAQ,GAAG,KAAKpE,WAAL,CAAiBmB,aAAlC;IACA,MAAMG,KAAK,GAAG8C,QAAQ,CAAC9C,KAAvB,CAb8B,CAc9B;;IACA,IAAI,CAAC6C,KAAD,IAAU,KAAK9C,QAAL,KAAkB,KAAKR,gBAAjC,IAAqDS,KAAK,KAAK,KAAK+C,cAAxE,EAAwF;MACpF;IACH;;IACD,MAAMJ,YAAY,GAAG,KAAKP,oBAAL,EAArB;;IACA,MAAMhB,MAAM,GAAG4B,IAAI,CAACC,GAAL,CAASN,YAAT,EAAuB,KAAKlC,wBAAL,IAAiC,CAAxD,CAAf,CAnB8B,CAoB9B;;IACAqC,QAAQ,CAAC9B,KAAT,CAAeI,MAAf,GAAyB,GAAEA,MAAO,IAAlC;;IACA,KAAKpF,OAAL,CAAauB,iBAAb,CAA+B,MAAM;MACjC,IAAI,OAAO2F,qBAAP,KAAiC,WAArC,EAAkD;QAC9CA,qBAAqB,CAAC,MAAM,KAAKC,sBAAL,CAA4BL,QAA5B,CAAP,CAArB;MACH,CAFD,MAGK;QACDM,UAAU,CAAC,MAAM,KAAKD,sBAAL,CAA4BL,QAA5B,CAAP,CAAV;MACH;IACJ,CAPD;;IAQA,KAAKC,cAAL,GAAsB/C,KAAtB;IACA,KAAKT,gBAAL,GAAwB,KAAKQ,QAA7B;EACH;EACD;AACJ;AACA;;;EACIQ,KAAK,GAAG;IACJ;IACA;IACA,IAAI,KAAKY,cAAL,KAAwBT,SAA5B,EAAuC;MACnC,KAAKd,gBAAL,CAAsBoB,KAAtB,CAA4BI,MAA5B,GAAqC,KAAKD,cAA1C;IACH;EACJ;;EACDkC,iBAAiB,GAAG,CAChB;EACH;EACD;;;EACAC,YAAY,GAAG;IACX,OAAO,KAAK3D,SAAL,IAAkBP,QAAzB;EACH;EACD;;;EACAkC,UAAU,GAAG;IACT,MAAMiC,GAAG,GAAG,KAAKD,YAAL,EAAZ;;IACA,OAAOC,GAAG,CAACC,WAAJ,IAAmBnC,MAA1B;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACI8B,sBAAsB,CAACL,QAAD,EAAW;IAC7B,MAAM;MAAEW,cAAF;MAAkBC;IAAlB,IAAmCZ,QAAzC,CAD6B,CAE7B;IACA;IACA;IACA;IACA;IACA;;IACA,IAAI,CAAC,KAAKzD,UAAL,CAAgBsE,SAAjB,IAA8B,KAAKjE,SAAvC,EAAkD;MAC9CoD,QAAQ,CAACc,iBAAT,CAA2BH,cAA3B,EAA2CC,YAA3C;IACH;EACJ;;AAlQqB;;AAoQ1BvE,mBAAmB,CAACjB,IAApB;EAAA,iBAAgHiB,mBAAhH,EA9SkG1E,EA8SlG,mBAAqJA,EAAE,CAACuE,UAAxJ,GA9SkGvE,EA8SlG,mBAA+KF,EAAE,CAAC4D,QAAlL,GA9SkG1D,EA8SlG,mBAAuMA,EAAE,CAAC2D,MAA1M,GA9SkG3D,EA8SlG,mBAA6NiB,QAA7N;AAAA;;AACAyD,mBAAmB,CAACF,IAApB,kBA/SkGxE,EA+SlG;EAAA,MAAoG0E,mBAApG;EAAA;EAAA,oBAA+W,GAA/W;EAAA;IAAA;MA/SkG1E,EA+SlG;QAAA,OAAoG,uBAApG;MAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDAhTkGA,EAgTlG,mBAA2F0E,mBAA3F,EAA4H,CAAC;IACjHb,IAAI,EAAE1D,SAD2G;IAEjH2D,IAAI,EAAE,CAAC;MACCW,QAAQ,EAAE,+BADX;MAEC2E,QAAQ,EAAE,qBAFX;MAGCC,IAAI,EAAE;QACF,SAAS,uBADP;QAEF;QACA;QACA,QAAQ,GAJN;QAKF,WAAW;MALT;IAHP,CAAD;EAF2G,CAAD,CAA5H,EAa4B,YAAY;IAAE,OAAO,CAAC;MAAExF,IAAI,EAAE7D,EAAE,CAACuE;IAAX,CAAD,EAA0B;MAAEV,IAAI,EAAE/D,EAAE,CAAC4D;IAAX,CAA1B,EAAiD;MAAEG,IAAI,EAAE7D,EAAE,CAAC2D;IAAX,CAAjD,EAAsE;MAAEE,IAAI,EAAEoC,SAAR;MAAmBqD,UAAU,EAAE,CAAC;QACnIzF,IAAI,EAAExD;MAD6H,CAAD,EAEnI;QACCwD,IAAI,EAAEvD,MADP;QAECwD,IAAI,EAAE,CAAC7C,QAAD;MAFP,CAFmI;IAA/B,CAAtE,CAAP;EAKlB,CAlBxB,EAkB0C;IAAEoE,OAAO,EAAE,CAAC;MACtCxB,IAAI,EAAEtD,KADgC;MAEtCuD,IAAI,EAAE,CAAC,oBAAD;IAFgC,CAAD,CAAX;IAG1B2B,OAAO,EAAE,CAAC;MACV5B,IAAI,EAAEtD,KADI;MAEVuD,IAAI,EAAE,CAAC,oBAAD;IAFI,CAAD,CAHiB;IAM1B8B,OAAO,EAAE,CAAC;MACV/B,IAAI,EAAEtD,KADI;MAEVuD,IAAI,EAAE,CAAC,qBAAD;IAFI,CAAD,CANiB;IAS1BiC,WAAW,EAAE,CAAC;MACdlC,IAAI,EAAEtD;IADQ,CAAD;EATa,CAlB1C;AAAA;AA+BA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMgJ,eAAN,CAAsB;;AAEtBA,eAAe,CAAC9F,IAAhB;EAAA,iBAA4G8F,eAA5G;AAAA;;AACAA,eAAe,CAACC,IAAhB,kBAzVkGxJ,EAyVlG;EAAA,MAA6GuJ,eAA7G;EAAA,eAA6IvF,WAA7I,EAA0JU,mBAA1J;EAAA,UAA0LV,WAA1L,EAAuMU,mBAAvM;AAAA;AACA6E,eAAe,CAACE,IAAhB,kBA1VkGzJ,EA0VlG;;AACA;EAAA,mDA3VkGA,EA2VlG,mBAA2FuJ,eAA3F,EAAwH,CAAC;IAC7G1F,IAAI,EAAErD,QADuG;IAE7GsD,IAAI,EAAE,CAAC;MACC4F,YAAY,EAAE,CAAC1F,WAAD,EAAcU,mBAAd,CADf;MAECiF,OAAO,EAAE,CAAC3F,WAAD,EAAcU,mBAAd;IAFV,CAAD;EAFuG,CAAD,CAAxH;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAAStD,eAAT,EAA0B4C,WAA1B,EAAuCU,mBAAvC,EAA4D6E,eAA5D"}, "metadata": {}, "sourceType": "module"}