{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./filing-mode.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./filing-mode.component.css?ngResource\";\nimport { Component, EventEmitter, Renderer2, ElementRef, forwardRef, Input, ViewChild, ChangeDetectorRef } from '@angular/core';\nimport { DefaultValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { FilingApiService } from 'src/app/Modules/FilingService/Services/FilingApiService';\nimport { ProductCode } from '../../../Enums/products-code.enum';\nimport { Router } from '@angular/router';\nimport { QuestionService } from '../../../Services/Common/question.service';\nlet FilingModeComponent = class FilingModeComponent extends DefaultValueAccessor {\n  constructor(router, cdr, filingApi, renderer, elementRef, service) {\n    super(renderer, elementRef, true);\n    this.router = router;\n    this.cdr = cdr;\n    this.filingApi = filingApi;\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n    this.service = service;\n    this.editData = [];\n    this.filingServiceType = \"\";\n    this.allStatesCodeFilingMode = [];\n    this.statesHavingDyanmicForms = [\"NV\", \"TX\", \"DE\", \"NY\", \"FL\", \"AZ\", \"CA\", \"GA\", \"WY\", \"CO\", \"AC\", \"AL\", \"AK\", \"IL\", \"OR\", \"CT\", \"HI\", \"ID\", \"NC\"];\n    this.FilingTypes = {\n      OfflineMode: 1,\n      OnlineMode: 2\n    };\n    this.$OnOnlineMethodChange = new EventEmitter();\n    this.$SendDynamicFormData = new EventEmitter();\n    this.SelectedFilingType = null;\n    this.SelectedOnlineMethod = null;\n    this.Price = 45;\n    this.LineNo = 3;\n    this.TabLineNo = 3;\n    this.DownloadStatement = `Please download the service form, complete and email (<a class='email' href=\"mailto:<EMAIL>\"><EMAIL></a>) to us along with Articles of Incorporation.`;\n    this.dynamicFormMasterData = [];\n    this.selfFilingDynamicFormStates = ['CA', 'DE', 'NV', 'TX', 'FL', 'AZ', 'GA', 'NY', 'WY', 'CO', 'OR', 'IL', 'AL', 'AK', 'OR', 'CT', 'HI', 'ID'];\n    this.dependentControls = [];\n    this.isUploadVisible = false;\n  }\n\n  set DynamicFormComponent(dynamicFormComponent) {\n    if (dynamicFormComponent) {\n      this.DynamicFormComponentObj = dynamicFormComponent;\n    }\n  }\n\n  set UploadComponent(uploadComponent) {\n    if (uploadComponent) {\n      this.UploadComponentObj = uploadComponent;\n      this.UploadComponentObj.EmitUploadedData.subscribe(data => {\n        this.uploadedData = data;\n      });\n    }\n  }\n\n  ngOnInit() {\n    console.log(this.SelectedFormationType);\n\n    if (this.SelectedFormationType == 'NP' && this.SelectedFormationState != 'CA') {\n      this.SelectedOnlineMethodChange(1);\n    }\n  }\n\n  ngOnChanges(changes) {\n    if (changes.SelectedFormationState || changes.SelectedFormationType) {\n      if (this.SelectedFormationType === 'NP' && this.SelectedFormationState !== 'CA') {\n        this.SelectedOnlineMethodChange(1);\n      }\n    }\n  }\n\n  reset() {\n    this.SelectedFilingType = null;\n    this.SelectedOnlineMethod = null;\n    this.onChange(null); //  this.fireChange();\n  }\n\n  SelectedOnlineMethodChange(onlineMethod) {\n    this.SelectedOnlineMethod = onlineMethod;\n    this.$OnOnlineMethodChange.emit(onlineMethod);\n    this.onChange(onlineMethod);\n    this.fireChange();\n  }\n\n  downloadPDFForm(selectedState, filingCategory, subCategoryCode) {\n    this.selectedState = selectedState;\n    this.selectedFilingType = filingCategory;\n    this.subCategoryCode = subCategoryCode;\n  }\n\n  clickOnDownload() {\n    this.filingApi.GetPDFUrl(ProductCode.FS, this.selectedState, this.selectedFilingType, this.subCategoryCode).subscribe(data => {\n      // var downloadURL = window.URL.createObjectURL(data.downloadUrl);\n      // var link = document.createElement('a');\n      // link.href = downloadURL;\n      // link.download = data.docName;\n      // link.click();\n      if (data.downloadUrl != \"\") {\n        window.open(data.downloadUrl);\n      } else {\n        this.router.navigate(['/filingservice/error-page']);\n      }\n    });\n  }\n\n  writeValue(obj) {\n    if (!obj) {\n      return;\n    }\n\n    if (this.SelectedOnlineMethod == 1) {\n      this.SelectedOnlineMethod = 1;\n      this.SelectedFilingType = this.FilingTypes.OfflineMode;\n    } else {\n      this.SelectedOnlineMethod = obj;\n      this.SelectedFilingType = this.FilingTypes.OnlineMode;\n    } // this.Value =(obj == undefined || obj == null)?null: obj;\n\n  }\n\n  registerOnChange(fn) {\n    // throw new Error(\"registerOnChange not implemented.\");\n    this.onChange = fn;\n  }\n\n  registerOnTouched(fn) {//  throw new Error(\"registerOnTouched not implemented.\");\n  }\n\n  setDisabledState(isDisabled) {//  throw new Error(\"setDisabledState not implemented.\");\n  }\n\n  fireChange() {\n    const eve = document.createEvent('Event');\n    eve.initEvent('change', true, true);\n    this.elementRef.nativeElement.dispatchEvent(eve);\n  } //  resolveQuestions() {\n  //   var promise = new Promise((resolve) => {\n\n\n  getFormQuestions(editData) {\n    if (!this.questions$) {\n      this.filingApi.GetDynamicFormMasterDataUpload('FS', \"EZ\", this.inputSubCategoryCode, this.SelectedFormationState, 'C').subscribe(value => {\n        if (value) {\n          this.dynamicFormMasterData = value;\n          this.isUploadVisible = true;\n\n          if (this.dynamicFormMasterData) {\n            if (editData) {\n              this.SelectedOnlineMethod = 1;\n              this.selectedState = editData.FormationState;\n              var dynamicFormData = editData.DynamicFormData || [];\n              this.questions$ = this.service.getMappedQuestions(this.dynamicFormMasterData, dynamicFormData, editData.FormationState);\n              this.editData = editData;\n              this.fireChange();\n\n              if (this.UploadComponentObj && this.editData.FileData) {\n                this.UploadComponentObj.uploadedFileList = this.editData.FileData;\n                this.UploadComponentObj.toggleUploadView = 2;\n              }\n            } else {\n              var dynamicFormDataOther = this.selectedState == this.FormEditData.FormationState ? this.FormEditData.DynamicFormData : [];\n              this.questions$ = this.service.getMappedQuestions(this.dynamicFormMasterData, dynamicFormDataOther, this.selectedState);\n            }\n          }\n        }\n      });\n    }\n  }\n\n  uploadFormSubmit() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      _this.enteredDynamicFormData = yield _this.DynamicFormComponentObj.getDynamicFormData();\n\n      if (_this.enteredDynamicFormData) {\n        _this.$SendDynamicFormData.emit({\n          dynamicData: _this.enteredDynamicFormData,\n          uploadedData: _this.uploadedData\n        });\n      }\n    })();\n  }\n\n  downloadFormVisibility() {\n    return ( //CORP condition for setting download form visibility for Non-profit\n    this.SelectedFormationType == 'NP' && this.filingServiceType == 'CORP' && this.SelectedFormationState != 'CA' && this.SelectedFormationState != 'ID' && this.SelectedOnlineMethod == 2 //CORP and LLC where ACS Will Draft for states that doesn't have active dynamic controls \n    || (this.filingServiceType == 'LLC' || this.filingServiceType == 'CORP') && this.SelectedOnlineMethod == this.FilingTypes.OnlineMode && !this.statesHavingDyanmicForms.includes(this.SelectedFormationState) && this.allStatesCodeFilingMode.includes(this.SelectedFormationState) //SOI condition for setting download form visibility for ADD and EDIT\n    || this.filingServiceType == 'SOI' && (this.SelectedOnlineMethod && this.SelectedFormationState == 'CA' || this.SelectedFormationState != 'CA') && !(this.SelectedOnlineMethod == this.FilingTypes.OnlineMode || this.SelectedOnlineMethod == this.FilingTypes.OfflineMode && this.SelectedFormationState == 'CA') //Download Form will not be visible if the filling service is a upselling service\n    ) && !this.upselling;\n  }\n\n};\n\nFilingModeComponent.ctorParameters = () => [{\n  type: Router\n}, {\n  type: ChangeDetectorRef\n}, {\n  type: FilingApiService\n}, {\n  type: Renderer2\n}, {\n  type: ElementRef\n}, {\n  type: QuestionService\n}];\n\nFilingModeComponent.propDecorators = {\n  isEdit: [{\n    type: Input\n  }],\n  allStatesCodeFilingMode: [{\n    type: Input\n  }],\n  upselling: [{\n    type: Input\n  }],\n  DynamicFormComponent: [{\n    type: ViewChild,\n    args: ['DynamicFormComponent', {\n      static: false\n    }]\n  }],\n  UploadComponent: [{\n    type: ViewChild,\n    args: ['UploadComponent', {\n      static: false\n    }]\n  }],\n  SelectedFormationState: [{\n    type: Input\n  }],\n  inputCategoryCode: [{\n    type: Input\n  }],\n  inputSubCategoryCode: [{\n    type: Input\n  }],\n  SelectedFormationType: [{\n    type: Input\n  }],\n  dependentControls: [{\n    type: Input\n  }]\n};\nFilingModeComponent = __decorate([Component({\n  selector: 'app-filing-mode',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => FilingModeComponent),\n    multi: true\n  }],\n  styles: [__NG_CLI_RESOURCE__1]\n})], FilingModeComponent);\nexport { FilingModeComponent };", "map": {"version": 3, "mappings": ";;;;AAAA,SAASA,SAAT,EAA4BC,YAA5B,EAA0CC,SAA1C,EAAqDC,UAArD,EAAiEC,UAAjE,EAA6EC,KAA7E,EAAoFC,SAApF,EAA+FC,iBAA/F,QAA4J,eAA5J;AACA,SAASC,oBAAT,EAA+BC,iBAA/B,QAAwD,gBAAxD;AAGA,SAASC,gBAAT,QAAiC,yDAAjC;AAEA,SAASC,WAAT,QAA4B,mCAA5B;AACA,SAASC,MAAT,QAAuB,iBAAvB;AAEA,SAASC,eAAT,QAAgC,2CAAhC;IAkBaC,mBAAmB,SAAnBA,mBAAmB,SAASN,oBAAT,CAA6B;EA+B3DO,YAAoBC,MAApB,EAA0CC,GAA1C,EAAwEC,SAAxE,EAA2GC,QAA3G,EAAwIC,UAAxI,EAA6KC,OAA7K,EAAqM;IACnM,MAAMF,QAAN,EAAgBC,UAAhB,EAA4B,IAA5B;IADkB;IAAsB;IAA8B;IAAmC;IAA6B;IAAqC;IAzB7K,gBAAiB,EAAjB;IACA,yBAA4B,EAA5B;IAES,+BAAgC,EAAhC;IAGT,gCAA2B,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,EAAyB,IAAzB,EAA+B,IAA/B,EAAqC,IAArC,EAA2C,IAA3C,EAAiD,IAAjD,EAAuD,IAAvD,EAA4D,IAA5D,EAAiE,IAAjE,EAAsE,IAAtE,EAA2E,IAA3E,EAAgF,IAAhF,EAAqF,IAArF,EAA0F,IAA1F,EAA+F,IAA/F,EAAoG,IAApG,CAA3B;IAuBA,mBAAc;MACZE,WAAW,EAAE,CADD;MAEZC,UAAU,EAAE;IAFA,CAAd;IAKA,6BAAwB,IAAItB,YAAJ,EAAxB;IACA,4BAAuB,IAAIA,YAAJ,EAAvB;IACA,0BAA0B,IAA1B;IACA,4BAAuB,IAAvB;IACA,aAAQ,EAAR;IACA,cAAO,CAAP;IACA,iBAAY,CAAZ;IACA,yBAAoB,yMAApB;IAEA,6BAA6B,EAA7B;IAEA,mCAA8B,CAAC,IAAD,EAAM,IAAN,EAAW,IAAX,EAAgB,IAAhB,EAAqB,IAArB,EAA0B,IAA1B,EAA+B,IAA/B,EAAoC,IAApC,EAAyC,IAAzC,EAA8C,IAA9C,EAAmD,IAAnD,EAAwD,IAAxD,EAA6D,IAA7D,EAAkE,IAAlE,EAAuE,IAAvE,EAA4E,IAA5E,EAAiF,IAAjF,EAAsF,IAAtF,CAA9B;IAMS,yBAA2B,EAA3B;IAIT,uBAA4B,KAA5B;EA5BC;;EApB6E,IAApBuB,oBAAoB,CAACC,oBAAD,EAA2C;IACvH,IAAIA,oBAAJ,EAA0B;MACxB,KAAKC,uBAAL,GAA+BD,oBAA/B;IACD;EACF;;EAEmE,IAAfE,eAAe,CAACC,eAAD,EAAuC;IACzG,IAAIA,eAAJ,EAAqB;MAEnB,KAAKC,kBAAL,GAA0BD,eAA1B;MAEA,KAAKC,kBAAL,CAAwBC,gBAAxB,CAAyCC,SAAzC,CAAmDC,IAAI,IAAE;QACvD,KAAKC,YAAL,GAAoBD,IAApB;MACD,CAFD;IAGD;EAEF;;EAiCDE,QAAQ;IACNC,OAAO,CAACC,GAAR,CAAY,KAAKC,qBAAjB;;IACA,IAAI,KAAKA,qBAAL,IAA8B,IAA9B,IAAsC,KAAKC,sBAAL,IAA+B,IAAzE,EAA+E;MAC7E,KAAKC,0BAAL,CAAgC,CAAhC;IACD;EACF;;EAGDC,WAAW,CAACC,OAAD,EAAa;IAEtB,IAAIA,OAAO,CAACH,sBAAR,IAAkCG,OAAO,CAACJ,qBAA9C,EAAqE;MACnE,IAAI,KAAKA,qBAAL,KAA+B,IAA/B,IAAuC,KAAKC,sBAAL,KAAgC,IAA3E,EAAiF;QAC/E,KAAKC,0BAAL,CAAgC,CAAhC;MACD;IACF;EACF;;EAGDG,KAAK;IACH,KAAKC,kBAAL,GAA0B,IAA1B;IACA,KAAKC,oBAAL,GAA4B,IAA5B;IACA,KAAKC,QAAL,CAAc,IAAd,EAHG,CAIL;EACC;;EAEDN,0BAA0B,CAACO,YAAD,EAAa;IACrC,KAAKF,oBAAL,GAA4BE,YAA5B;IACA,KAAKC,qBAAL,CAA2BC,IAA3B,CAAgCF,YAAhC;IACA,KAAKD,QAAL,CAAcC,YAAd;IACA,KAAKG,UAAL;EACD;;EAEDC,eAAe,CAACC,aAAD,EAAgBC,cAAhB,EAA+BC,eAA/B,EAA8C;IAC3D,KAAKF,aAAL,GAAqBA,aAArB;IACA,KAAKG,kBAAL,GAA0BF,cAA1B;IACA,KAAKC,eAAL,GAAqBA,eAArB;EACD;;EAEDE,eAAe;IACb,KAAKrC,SAAL,CAAesC,SAAf,CAAyB7C,WAAW,CAAC8C,EAArC,EAAwC,KAAKN,aAA7C,EAA2D,KAAKG,kBAAhE,EAAmF,KAAKD,eAAxF,EAAyGtB,SAAzG,CAAoHC,IAAD,IAA4B;MAC7I;MACA;MACA;MACA;MACA;MACA,IAAGA,IAAI,CAAC0B,WAAL,IAAkB,EAArB,EAAwB;QACtBC,MAAM,CAACC,IAAP,CAAY5B,IAAI,CAAC0B,WAAjB;MACD,CAFD,MAGI;QACF,KAAK1C,MAAL,CAAY6C,QAAZ,CAAqB,CAAC,2BAAD,CAArB;MACD;IACF,CAZD;EAaD;;EAMDC,UAAU,CAACC,GAAD,EAAS;IAEjB,IAAG,CAACA,GAAJ,EAAQ;MAAC;IAAQ;;IAEjB,IAAG,KAAKnB,oBAAL,IAA2B,CAA9B,EAAgC;MAC9B,KAAKA,oBAAL,GAA4B,CAA5B;MACA,KAAKD,kBAAL,GAA0B,KAAKqB,WAAL,CAAiB1C,WAA3C;IACD,CAHD,MAII;MACF,KAAKsB,oBAAL,GAA4BmB,GAA5B;MACA,KAAKpB,kBAAL,GAA0B,KAAKqB,WAAL,CAAiBzC,UAA3C;IACD,CAXgB,CAajB;;EAED;;EACD0C,gBAAgB,CAACC,EAAD,EAAQ;IACtB;IAGA,KAAKrB,QAAL,GAAgBqB,EAAhB;EACD;;EACDC,iBAAiB,CAACD,EAAD,EAAQ,CAGvB;EACD;;EACDE,gBAAgB,CAACC,UAAD,EAAoB,CAClC;EACD;;EAEDpB,UAAU;IACR,MAAMqB,GAAG,GAAGC,QAAQ,CAACC,WAAT,CAAqB,OAArB,CAAZ;IACAF,GAAG,CAACG,SAAJ,CAAc,QAAd,EAAuB,IAAvB,EAA4B,IAA5B;IACA,KAAKrD,UAAL,CAAgBsD,aAAhB,CAA8BC,aAA9B,CAA4CL,GAA5C;EACD,CA3J0D,CA4J3D;EACA;;;EAEAM,gBAAgB,CAACC,QAAD,EAAU;IACxB,IAAG,CAAC,KAAKC,UAAT,EAAoB;MAClB,KAAK5D,SAAL,CAAe6D,8BAAf,CAA8C,IAA9C,EAAoD,IAApD,EAA0D,KAAKC,oBAA/D,EAAoF,KAAK1C,sBAAzF,EAAiH,GAAjH,EAAsHP,SAAtH,CAAgIkD,KAAK,IAAG;QAC9H,IAAGA,KAAH,EAAS;UACP,KAAKC,qBAAL,GAA4BD,KAA5B;UACf,KAAKE,eAAL,GAAuB,IAAvB;;UACA,IAAG,KAAKD,qBAAR,EAA8B;YAC7B,IAAGL,QAAH,EAAY;cACV,KAAKjC,oBAAL,GAA4B,CAA5B;cACA,KAAKO,aAAL,GAAqB0B,QAAQ,CAACO,cAA9B;cACA,IAAIC,eAAe,GAAGR,QAAQ,CAACS,eAAT,IAA4B,EAAlD;cACA,KAAKR,UAAL,GAAkB,KAAKzD,OAAL,CAAakE,kBAAb,CAAiC,KAAKL,qBAAtC,EAA6DG,eAA7D,EAA8ER,QAAQ,CAACO,cAAvF,CAAlB;cACA,KAAKP,QAAL,GAAgBA,QAAhB;cACA,KAAK5B,UAAL;;cACA,IAAG,KAAKpB,kBAAL,IAA2B,KAAKgD,QAAL,CAAcW,QAA5C,EAAqD;gBACnD,KAAK3D,kBAAL,CAAwB4D,gBAAxB,GAA2C,KAAKZ,QAAL,CAAcW,QAAzD;gBACA,KAAK3D,kBAAL,CAAwB6D,gBAAxB,GAA2C,CAA3C;cACD;YACF,CAXD,MAYI;cACF,IAAIC,oBAAoB,GAAG,KAAKxC,aAAL,IAAsB,KAAKyC,YAAL,CAAkBR,cAAxC,GAAyD,KAAKQ,YAAL,CAAkBN,eAA3E,GAA6F,EAAxH;cACA,KAAKR,UAAL,GAAkB,KAAKzD,OAAL,CAAakE,kBAAb,CAAgC,KAAKL,qBAArC,EAA4DS,oBAA5D,EAAkF,KAAKxC,aAAvF,CAAlB;YACD;UACD;QAED;MAAC,CAvBE;IAuBA;EACH;;EACK0C,gBAAgB;IAAA;;IAAA;MACnB,KAAI,CAACC,sBAAL,SAAoC,KAAI,CAACpE,uBAAL,CAA6BqE,kBAA7B,EAApC;;MACA,IAAG,KAAI,CAACD,sBAAR,EACA;QACE,KAAI,CAACE,oBAAL,CAA0BhD,IAA1B,CAA+B;UAACiD,WAAW,EAAG,KAAI,CAACH,sBAApB;UAA2C7D,YAAY,EAAG,KAAI,CAACA;QAA/D,CAA/B;MACD;IALkB;EAOrB;;EAEDiE,sBAAsB;IACpB,OACA,EAAC;IACA,KAAK7D,qBAAL,IAA8B,IAA9B,IAAsC,KAAK8D,iBAAL,IAA0B,MAAhE,IAA2E,KAAK7D,sBAAL,IAA6B,IAA7B,IAAqC,KAAKA,sBAAL,IAA6B,IAAlE,IAA0E,KAAKM,oBAAL,IAA6B,CAAnL,CACA;IADA,GAEI,CAAC,KAAKuD,iBAAL,IAA0B,KAA1B,IAAmC,KAAKA,iBAAL,IAA0B,MAA9D,KAAyE,KAAKvD,oBAAL,IAA4B,KAAKoB,WAAL,CAAiBzC,UAAtH,IAAoI,CAAC,KAAK6E,wBAAL,CAA8BC,QAA9B,CAAuC,KAAK/D,sBAA5C,CAArI,IAA4M,KAAKgE,uBAAL,CAA6BD,QAA7B,CAAsC,KAAK/D,sBAA3C,CAFhN,CAGA;IAHA,GAII,KAAK6D,iBAAL,IAA0B,KAA1B,KAAqC,KAAKvD,oBAAL,IAA6B,KAAKN,sBAAL,IAA6B,IAA3D,IAAoE,KAAKA,sBAAL,IAA6B,IAArI,KACG,EAAE,KAAKM,oBAAL,IAA4B,KAAKoB,WAAL,CAAiBzC,UAA7C,IAA4D,KAAKqB,oBAAL,IAA4B,KAAKoB,WAAL,CAAiB1C,WAA7C,IAA4D,KAAKgB,sBAAL,IAA6B,IAAvJ,CANP,CAQA;IARA,KASG,CAAC,KAAKiE,SAVT;EAWD;;AA/M0D;;;;;;;;;;;;;;;;;;UAQ1DlG;;;UACAA;;;UACAA;;;UAGAC;IAASkG,OAAC,sBAAD,EAAyB;MAAEC,MAAM,EAAE;IAAV,CAAzB;;;UAMTnG;IAASkG,OAAC,iBAAD,EAAoB;MAAEC,MAAM,EAAE;IAAV,CAApB;;;UAkCTpG;;;UACAA;;;UACAA;;;UACAA;;;UACAA;;;AAzDUS,mBAAmB,eAZ/Bd,SAAS,CAAC;EACT0G,QAAQ,EAAE,iBADD;EAETC,8BAFS;EAITC,SAAS,EAAE,CACT;IACEC,OAAO,EAAEpG,iBADX;IAEEqG,WAAW,EAAE1G,UAAU,CAAC,MAAMU,mBAAP,CAFzB;IAGEiG,KAAK,EAAE;EAHT,CADS,CAJF;;AAAA,CAAD,CAYsB,GAAnBjG,mBAAmB,CAAnB;SAAAA", "names": ["Component", "EventEmitter", "Renderer2", "ElementRef", "forwardRef", "Input", "ViewChild", "ChangeDetectorRef", "DefaultValueAccessor", "NG_VALUE_ACCESSOR", "FilingApiService", "ProductCode", "Router", "QuestionService", "FilingModeComponent", "constructor", "router", "cdr", "filingApi", "renderer", "elementRef", "service", "OfflineMode", "OnlineMode", "DynamicFormComponent", "dynamicFormComponent", "DynamicFormComponentObj", "UploadComponent", "uploadComponent", "UploadComponentObj", "EmitUploadedData", "subscribe", "data", "uploadedData", "ngOnInit", "console", "log", "SelectedFormationType", "SelectedFormationState", "SelectedOnlineMethodChange", "ngOnChanges", "changes", "reset", "SelectedFilingType", "SelectedOnlineMethod", "onChange", "onlineMethod", "$OnOnlineMethodChange", "emit", "fireChange", "downloadPDFForm", "selectedState", "filingCategory", "subCategoryCode", "selectedFilingType", "clickOnDownload", "GetPDFUrl", "FS", "downloadUrl", "window", "open", "navigate", "writeValue", "obj", "FilingTypes", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "eve", "document", "createEvent", "initEvent", "nativeElement", "dispatchEvent", "getFormQuestions", "editData", "questions$", "GetDynamicFormMasterDataUpload", "inputSubCategoryCode", "value", "dynamicFormMasterData", "isUploadVisible", "FormationState", "dynamicFormData", "DynamicFormData", "getMappedQuestions", "FileData", "uploadedFileList", "toggleUploadView", "dynamicFormDataOther", "FormEditData", "uploadFormSubmit", "enteredDynamicFormData", "getDynamicFormData", "$SendDynamicFormData", "dynamicData", "downloadFormVisibility", "filingServiceType", "statesHavingDyanmicForms", "includes", "allStatesCodeFilingMode", "upselling", "args", "static", "selector", "template", "providers", "provide", "useExisting", "multi"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\viatech\\Ecom\\client\\acs\\src\\app\\Modules\\Shared\\Components\\formComponents\\filing-mode\\filing-mode.component.ts"], "sourcesContent": ["import { Component, OnInit, EventEmitter, Renderer2, ElementRef, forwardRef, Input, ViewChild, ChangeDetectorRef, AfterContentChecked, AfterViewInit } from '@angular/core';\r\nimport { DefaultValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\r\nimport { FormCorporationComponent } from 'src/app/Modules/FilingService/Pages/form-corporation/form-corporation.component';\r\nimport { Observable } from 'rxjs';\r\nimport { FilingApiService } from 'src/app/Modules/FilingService/Services/FilingApiService';\r\nimport { PDFDownloadURLModel } from '../../../Models/FilingService/PDFDownloadURLModel';\r\nimport { ProductCode } from '../../../Enums/products-code.enum';\r\nimport { Router } from '@angular/router';\r\nimport { QuestionBase } from '../../../Models/DynamicForm/question-base';\r\nimport { QuestionService } from '../../../Services/Common/question.service';\r\nimport { FormCorporation } from 'src/app/Modules/FilingService/Models/FormCorporation';\r\nimport { DynamicFormComponent } from '../../dynamic-form/dynamic-form.component';\r\nimport { FileUploaderComponent } from '../../file-uploader/file-uploader.component';\r\nimport { Console } from 'console';\r\n\r\n@Component({\r\n  selector: 'app-filing-mode',\r\n  templateUrl: './filing-mode.component.html',\r\n  styleUrls: ['./filing-mode.component.css'],\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => FilingModeComponent),\r\n      multi: true\r\n    }\r\n  ]\r\n})\r\nexport class FilingModeComponent extends DefaultValueAccessor implements OnInit {\r\n  selectedState: any;\r\n  selectedFilingType: any;\r\n  FormC:FormCorporationComponent\r\n  subCategoryCode: any;\r\n  uploadedData : any;\r\n  editData : any = [];\r\n  filingServiceType: string = \"\";\r\n  @Input() isEdit;\r\n  @Input() allStatesCodeFilingMode : any = [];\r\n  @Input() upselling : boolean;\r\n  DynamicFormComponentObj: DynamicFormComponent;\r\n  statesHavingDyanmicForms = [\"NV\", \"TX\", \"DE\", \"NY\", \"FL\", \"AZ\", \"CA\" ,\"GA\", \"WY\", \"CO\",\"AC\",\"AL\",\"AK\",\"IL\",\"OR\",\"CT\",\"HI\",\"ID\",\"NC\"];\r\n  @ViewChild('DynamicFormComponent', { static: false }) set DynamicFormComponent(dynamicFormComponent: DynamicFormComponent) { \r\n    if (dynamicFormComponent) {\r\n      this.DynamicFormComponentObj = dynamicFormComponent;     \r\n    }\r\n  }\r\n  UploadComponentObj: FileUploaderComponent;\r\n  @ViewChild('UploadComponent', { static: false }) set UploadComponent(uploadComponent: FileUploaderComponent) {\r\n    if (uploadComponent) {\r\n      \r\n      this.UploadComponentObj = uploadComponent; \r\n      \r\n      this.UploadComponentObj.EmitUploadedData.subscribe(data=>{\r\n        this.uploadedData = data\r\n      }) \r\n    }\r\n     \r\n  }\r\n\r\n  constructor(private router:Router,private cdr:ChangeDetectorRef,private filingApi:FilingApiService,private renderer: Renderer2, private elementRef: ElementRef<any>, private service: QuestionService) {\r\n    super(renderer, elementRef, true);\r\n  }\r\n\r\n  FilingTypes = {\r\n    OfflineMode: 1,\r\n    OnlineMode: 2\r\n  }\r\n\r\n  $OnOnlineMethodChange = new EventEmitter<any>();\r\n  $SendDynamicFormData = new EventEmitter<any>();\r\n  SelectedFilingType: any = null;\r\n  SelectedOnlineMethod = null;\r\n  Price = 45;\r\n  LineNo=3;\r\n  TabLineNo = 3;\r\n  DownloadStatement = `Please download the service form, complete and email (<a class='email' href=\"mailto:<EMAIL>\"><EMAIL></a>) to us along with Articles of Incorporation.`\r\n  questions$: Observable<QuestionBase<any>[]>;\r\n  dynamicFormMasterData: any = [];\r\n  FormEditData:any\r\n  selfFilingDynamicFormStates = ['CA','DE','NV','TX','FL','AZ','GA','NY','WY','CO','OR','IL','AL','AK','OR','CT','HI','ID']\r\n\r\n  @Input() SelectedFormationState\r\n  @Input() inputCategoryCode\r\n  @Input() inputSubCategoryCode\r\n  @Input() SelectedFormationType\r\n  @Input() dependentControls: any[] = [];\r\n\r\n  enteredDynamicFormData : any\r\n\r\n  isUploadVisible : boolean = false\r\n  ngOnInit(): void {\r\n    console.log(this.SelectedFormationType)\r\n    if (this.SelectedFormationType == 'NP' && this.SelectedFormationState != 'CA') {\r\n      this.SelectedOnlineMethodChange(1);\r\n    }\r\n  }\r\n\r\n\r\n  ngOnChanges(changes: any): void {\r\n\r\n    if (changes.SelectedFormationState || changes.SelectedFormationType) {\r\n      if (this.SelectedFormationType === 'NP' && this.SelectedFormationState !== 'CA') {\r\n        this.SelectedOnlineMethodChange(1);\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  reset() {\r\n    this.SelectedFilingType = null;\r\n    this.SelectedOnlineMethod = null;\r\n    this.onChange(null);\r\n  //  this.fireChange();\r\n  }\r\n\r\n  SelectedOnlineMethodChange(onlineMethod) {\r\n    this.SelectedOnlineMethod = onlineMethod\r\n    this.$OnOnlineMethodChange.emit(onlineMethod);\r\n    this.onChange(onlineMethod);\r\n    this.fireChange();\r\n  }\r\n\r\n  downloadPDFForm(selectedState, filingCategory,subCategoryCode) {\r\n    this.selectedState = selectedState\r\n    this.selectedFilingType = filingCategory\r\n    this.subCategoryCode=subCategoryCode\r\n  }\r\n\r\n  clickOnDownload() {\r\n    this.filingApi.GetPDFUrl(ProductCode.FS,this.selectedState,this.selectedFilingType,this.subCategoryCode).subscribe((data:PDFDownloadURLModel)=>{\r\n      // var downloadURL = window.URL.createObjectURL(data.downloadUrl);\r\n      // var link = document.createElement('a');\r\n      // link.href = downloadURL;\r\n      // link.download = data.docName;\r\n      // link.click();\r\n      if(data.downloadUrl!=\"\"){\r\n        window.open(data.downloadUrl)\r\n      }\r\n      else{\r\n        this.router.navigate(['/filingservice/error-page'])\r\n      }\r\n    })\r\n  }\r\n\r\n  //==========================\r\n  onChange: (_: any) => {};\r\n\r\n\r\n  writeValue(obj: any): void {\r\n   \r\n    if(!obj){return;}\r\n  \r\n    if(this.SelectedOnlineMethod==1){\r\n      this.SelectedOnlineMethod = 1\r\n      this.SelectedFilingType = this.FilingTypes.OfflineMode\r\n    }\r\n    else{\r\n      this.SelectedOnlineMethod = obj\r\n      this.SelectedFilingType = this.FilingTypes.OnlineMode\r\n    }\r\n   \r\n    // this.Value =(obj == undefined || obj == null)?null: obj;\r\n\r\n  }\r\n  registerOnChange(fn: any): void {\r\n    // throw new Error(\"registerOnChange not implemented.\");\r\n\r\n\r\n    this.onChange = fn;\r\n  }\r\n  registerOnTouched(fn: any): void {\r\n\r\n\r\n    //  throw new Error(\"registerOnTouched not implemented.\");\r\n  }\r\n  setDisabledState(isDisabled: boolean): void {\r\n    //  throw new Error(\"setDisabledState not implemented.\");\r\n  }\r\n\r\n  fireChange() {\r\n    const eve = document.createEvent('Event')\r\n    eve.initEvent('change',true,true);\r\n    this.elementRef.nativeElement.dispatchEvent(eve)\r\n  }\r\n  //  resolveQuestions() {\r\n  //   var promise = new Promise((resolve) => {\r\n    \r\n  getFormQuestions(editData?){\r\n    if(!this.questions$){\r\n      this.filingApi.GetDynamicFormMasterDataUpload('FS', \"EZ\", this.inputSubCategoryCode,this.SelectedFormationState, 'C').subscribe(value => {\r\n                if(value){\r\n                  this.dynamicFormMasterData =value\r\n   this.isUploadVisible = true\r\n   if(this.dynamicFormMasterData){\r\n    if(editData){\r\n      this.SelectedOnlineMethod = 1\r\n      this.selectedState = editData.FormationState\r\n      var dynamicFormData = editData.DynamicFormData || [];\r\n      this.questions$ = this.service.getMappedQuestions( this.dynamicFormMasterData, dynamicFormData, editData.FormationState);\r\n      this.editData = editData\r\n      this.fireChange()\r\n      if(this.UploadComponentObj && this.editData.FileData){\r\n        this.UploadComponentObj.uploadedFileList = this.editData.FileData\r\n        this.UploadComponentObj.toggleUploadView = 2\r\n      }\r\n    }\r\n    else{\r\n      var dynamicFormDataOther = this.selectedState == this.FormEditData.FormationState ? this.FormEditData.DynamicFormData : [];\r\n      this.questions$ = this.service.getMappedQuestions(this.dynamicFormMasterData, dynamicFormDataOther, this.selectedState);\r\n    }\r\n   }   \r\n\r\n  }})}\r\n  }\r\n  async uploadFormSubmit(){\r\n     this.enteredDynamicFormData = await this.DynamicFormComponentObj.getDynamicFormData()  \r\n     if(this.enteredDynamicFormData)\r\n     {\r\n       this.$SendDynamicFormData.emit({dynamicData : this.enteredDynamicFormData,uploadedData : this.uploadedData})\r\n     }\r\n    \r\n  }\r\n\r\n  downloadFormVisibility(){\r\n    return (\r\n    (//CORP condition for setting download form visibility for Non-profit\r\n    (this.SelectedFormationType == 'NP' && this.filingServiceType == 'CORP' && (this.SelectedFormationState!='CA' && this.SelectedFormationState!='ID' && this.SelectedOnlineMethod == 2))\r\n    //CORP and LLC where ACS Will Draft for states that doesn't have active dynamic controls \r\n    || ((this.filingServiceType == 'LLC' || this.filingServiceType == 'CORP') && this.SelectedOnlineMethod== this.FilingTypes.OnlineMode && !this.statesHavingDyanmicForms.includes(this.SelectedFormationState) && this.allStatesCodeFilingMode.includes(this.SelectedFormationState))\r\n    //SOI condition for setting download form visibility for ADD and EDIT\r\n    || (this.filingServiceType == 'SOI' && ((this.SelectedOnlineMethod && this.SelectedFormationState=='CA') || this.SelectedFormationState!='CA' )\r\n        && !(this.SelectedOnlineMethod== this.FilingTypes.OnlineMode || (this.SelectedOnlineMethod== this.FilingTypes.OfflineMode && this.SelectedFormationState=='CA')) )\r\n    )\r\n    //Download Form will not be visible if the filling service is a upselling service\r\n    && !this.upselling);\r\n  }\r\n\r\n}"]}, "metadata": {}, "sourceType": "module"}