{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Input, EventEmitter, Optional, Inject, SkipSelf, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/collections';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Subject, Subscription } from 'rxjs';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Used to generate unique ID for each accordion. */\n\nlet nextId$1 = 0;\n/**\n * Injection token that can be used to reference instances of `CdkAccordion`. It serves\n * as alternative token to the actual `CdkAccordion` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\nconst CDK_ACCORDION = new InjectionToken('CdkAccordion');\n/**\n * Directive whose purpose is to manage the expanded state of CdkAccordionItem children.\n */\n\nclass CdkAccordion {\n  constructor() {\n    /** Emits when the state of the accordion changes */\n    this._stateChanges = new Subject();\n    /** Stream that emits true/false when openAll/closeAll is triggered. */\n\n    this._openCloseAllActions = new Subject();\n    /** A readonly id value to use for unique selection coordination. */\n\n    this.id = `cdk-accordion-${nextId$1++}`;\n    this._multi = false;\n  }\n  /** Whether the accordion should allow multiple expanded accordion items simultaneously. */\n\n\n  get multi() {\n    return this._multi;\n  }\n\n  set multi(multi) {\n    this._multi = coerceBooleanProperty(multi);\n  }\n  /** Opens all enabled accordion items in an accordion where multi is enabled. */\n\n\n  openAll() {\n    if (this._multi) {\n      this._openCloseAllActions.next(true);\n    }\n  }\n  /** Closes all enabled accordion items in an accordion where multi is enabled. */\n\n\n  closeAll() {\n    this._openCloseAllActions.next(false);\n  }\n\n  ngOnChanges(changes) {\n    this._stateChanges.next(changes);\n  }\n\n  ngOnDestroy() {\n    this._stateChanges.complete();\n\n    this._openCloseAllActions.complete();\n  }\n\n}\n\nCdkAccordion.ɵfac = function CdkAccordion_Factory(t) {\n  return new (t || CdkAccordion)();\n};\n\nCdkAccordion.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkAccordion,\n  selectors: [[\"cdk-accordion\"], [\"\", \"cdkAccordion\", \"\"]],\n  inputs: {\n    multi: \"multi\"\n  },\n  exportAs: [\"cdkAccordion\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CDK_ACCORDION,\n    useExisting: CdkAccordion\n  }]), i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAccordion, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-accordion, [cdkAccordion]',\n      exportAs: 'cdkAccordion',\n      providers: [{\n        provide: CDK_ACCORDION,\n        useExisting: CdkAccordion\n      }]\n    }]\n  }], null, {\n    multi: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Used to generate unique ID for each accordion item. */\n\n\nlet nextId = 0;\n/**\n * An basic directive expected to be extended and decorated as a component.  Sets up all\n * events and attributes needed to be managed by a CdkAccordion parent.\n */\n\nclass CdkAccordionItem {\n  constructor(accordion, _changeDetectorRef, _expansionDispatcher) {\n    this.accordion = accordion;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._expansionDispatcher = _expansionDispatcher;\n    /** Subscription to openAll/closeAll events. */\n\n    this._openCloseAllSubscription = Subscription.EMPTY;\n    /** Event emitted every time the AccordionItem is closed. */\n\n    this.closed = new EventEmitter();\n    /** Event emitted every time the AccordionItem is opened. */\n\n    this.opened = new EventEmitter();\n    /** Event emitted when the AccordionItem is destroyed. */\n\n    this.destroyed = new EventEmitter();\n    /**\n     * Emits whenever the expanded state of the accordion changes.\n     * Primarily used to facilitate two-way binding.\n     * @docs-private\n     */\n\n    this.expandedChange = new EventEmitter();\n    /** The unique AccordionItem id. */\n\n    this.id = `cdk-accordion-child-${nextId++}`;\n    this._expanded = false;\n    this._disabled = false;\n    /** Unregister function for _expansionDispatcher. */\n\n    this._removeUniqueSelectionListener = () => {};\n\n    this._removeUniqueSelectionListener = _expansionDispatcher.listen((id, accordionId) => {\n      if (this.accordion && !this.accordion.multi && this.accordion.id === accordionId && this.id !== id) {\n        this.expanded = false;\n      }\n    }); // When an accordion item is hosted in an accordion, subscribe to open/close events.\n\n    if (this.accordion) {\n      this._openCloseAllSubscription = this._subscribeToOpenCloseAllActions();\n    }\n  }\n  /** Whether the AccordionItem is expanded. */\n\n\n  get expanded() {\n    return this._expanded;\n  }\n\n  set expanded(expanded) {\n    expanded = coerceBooleanProperty(expanded); // Only emit events and update the internal value if the value changes.\n\n    if (this._expanded !== expanded) {\n      this._expanded = expanded;\n      this.expandedChange.emit(expanded);\n\n      if (expanded) {\n        this.opened.emit();\n        /**\n         * In the unique selection dispatcher, the id parameter is the id of the CdkAccordionItem,\n         * the name value is the id of the accordion.\n         */\n\n        const accordionId = this.accordion ? this.accordion.id : this.id;\n\n        this._expansionDispatcher.notify(this.id, accordionId);\n      } else {\n        this.closed.emit();\n      } // Ensures that the animation will run when the value is set outside of an `@Input`.\n      // This includes cases like the open, close and toggle methods.\n\n\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Whether the AccordionItem is disabled. */\n\n\n  get disabled() {\n    return this._disabled;\n  }\n\n  set disabled(disabled) {\n    this._disabled = coerceBooleanProperty(disabled);\n  }\n  /** Emits an event for the accordion item being destroyed. */\n\n\n  ngOnDestroy() {\n    this.opened.complete();\n    this.closed.complete();\n    this.destroyed.emit();\n    this.destroyed.complete();\n\n    this._removeUniqueSelectionListener();\n\n    this._openCloseAllSubscription.unsubscribe();\n  }\n  /** Toggles the expanded state of the accordion item. */\n\n\n  toggle() {\n    if (!this.disabled) {\n      this.expanded = !this.expanded;\n    }\n  }\n  /** Sets the expanded state of the accordion item to false. */\n\n\n  close() {\n    if (!this.disabled) {\n      this.expanded = false;\n    }\n  }\n  /** Sets the expanded state of the accordion item to true. */\n\n\n  open() {\n    if (!this.disabled) {\n      this.expanded = true;\n    }\n  }\n\n  _subscribeToOpenCloseAllActions() {\n    return this.accordion._openCloseAllActions.subscribe(expanded => {\n      // Only change expanded state if item is enabled\n      if (!this.disabled) {\n        this.expanded = expanded;\n      }\n    });\n  }\n\n}\n\nCdkAccordionItem.ɵfac = function CdkAccordionItem_Factory(t) {\n  return new (t || CdkAccordionItem)(i0.ɵɵdirectiveInject(CDK_ACCORDION, 12), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.UniqueSelectionDispatcher));\n};\n\nCdkAccordionItem.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkAccordionItem,\n  selectors: [[\"cdk-accordion-item\"], [\"\", \"cdkAccordionItem\", \"\"]],\n  inputs: {\n    expanded: \"expanded\",\n    disabled: \"disabled\"\n  },\n  outputs: {\n    closed: \"closed\",\n    opened: \"opened\",\n    destroyed: \"destroyed\",\n    expandedChange: \"expandedChange\"\n  },\n  exportAs: [\"cdkAccordionItem\"],\n  features: [i0.ɵɵProvidersFeature([// Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n  // registering to the same accordion.\n  {\n    provide: CDK_ACCORDION,\n    useValue: undefined\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAccordionItem, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-accordion-item, [cdkAccordionItem]',\n      exportAs: 'cdkAccordionItem',\n      providers: [// Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n      // registering to the same accordion.\n      {\n        provide: CDK_ACCORDION,\n        useValue: undefined\n      }]\n    }]\n  }], function () {\n    return [{\n      type: CdkAccordion,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [CDK_ACCORDION]\n      }, {\n        type: SkipSelf\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1.UniqueSelectionDispatcher\n    }];\n  }, {\n    closed: [{\n      type: Output\n    }],\n    opened: [{\n      type: Output\n    }],\n    destroyed: [{\n      type: Output\n    }],\n    expandedChange: [{\n      type: Output\n    }],\n    expanded: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass CdkAccordionModule {}\n\nCdkAccordionModule.ɵfac = function CdkAccordionModule_Factory(t) {\n  return new (t || CdkAccordionModule)();\n};\n\nCdkAccordionModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: CdkAccordionModule,\n  declarations: [CdkAccordion, CdkAccordionItem],\n  exports: [CdkAccordion, CdkAccordionItem]\n});\nCdkAccordionModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAccordionModule, [{\n    type: NgModule,\n    args: [{\n      exports: [CdkAccordion, CdkAccordionItem],\n      declarations: [CdkAccordion, CdkAccordionItem]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { CdkAccordion, CdkAccordionItem, CdkAccordionModule };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Directive", "Input", "EventEmitter", "Optional", "Inject", "SkipSelf", "Output", "NgModule", "i1", "coerceBooleanProperty", "Subject", "Subscription", "nextId$1", "CDK_ACCORDION", "CdkAccordion", "constructor", "_stateChanges", "_openCloseAllActions", "id", "_multi", "multi", "openAll", "next", "closeAll", "ngOnChanges", "changes", "ngOnDestroy", "complete", "ɵfac", "ɵdir", "provide", "useExisting", "type", "args", "selector", "exportAs", "providers", "nextId", "CdkAccordionItem", "accordion", "_changeDetectorRef", "_expansionDispatcher", "_openCloseAllSubscription", "EMPTY", "closed", "opened", "destroyed", "expandedChange", "_expanded", "_disabled", "_removeUniqueSelectionListener", "listen", "accordionId", "expanded", "_subscribeToOpenCloseAllActions", "emit", "notify", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled", "unsubscribe", "toggle", "close", "open", "subscribe", "ChangeDetectorRef", "UniqueSelectionDispatcher", "useValue", "undefined", "decorators", "CdkAccordionModule", "ɵmod", "ɵinj", "exports", "declarations"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/cdk/fesm2020/accordion.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Input, EventEmitter, Optional, Inject, SkipSelf, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/collections';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Subject, Subscription } from 'rxjs';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Used to generate unique ID for each accordion. */\nlet nextId$1 = 0;\n/**\n * Injection token that can be used to reference instances of `CdkAccordion`. It serves\n * as alternative token to the actual `CdkAccordion` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_ACCORDION = new InjectionToken('CdkAccordion');\n/**\n * Directive whose purpose is to manage the expanded state of CdkAccordionItem children.\n */\nclass CdkAccordion {\n    constructor() {\n        /** Emits when the state of the accordion changes */\n        this._stateChanges = new Subject();\n        /** Stream that emits true/false when openAll/closeAll is triggered. */\n        this._openCloseAllActions = new Subject();\n        /** A readonly id value to use for unique selection coordination. */\n        this.id = `cdk-accordion-${nextId$1++}`;\n        this._multi = false;\n    }\n    /** Whether the accordion should allow multiple expanded accordion items simultaneously. */\n    get multi() {\n        return this._multi;\n    }\n    set multi(multi) {\n        this._multi = coerceBooleanProperty(multi);\n    }\n    /** Opens all enabled accordion items in an accordion where multi is enabled. */\n    openAll() {\n        if (this._multi) {\n            this._openCloseAllActions.next(true);\n        }\n    }\n    /** Closes all enabled accordion items in an accordion where multi is enabled. */\n    closeAll() {\n        this._openCloseAllActions.next(false);\n    }\n    ngOnChanges(changes) {\n        this._stateChanges.next(changes);\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n        this._openCloseAllActions.complete();\n    }\n}\nCdkAccordion.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkAccordion, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nCdkAccordion.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: CdkAccordion, selector: \"cdk-accordion, [cdkAccordion]\", inputs: { multi: \"multi\" }, providers: [{ provide: CDK_ACCORDION, useExisting: CdkAccordion }], exportAs: [\"cdkAccordion\"], usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkAccordion, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-accordion, [cdkAccordion]',\n                    exportAs: 'cdkAccordion',\n                    providers: [{ provide: CDK_ACCORDION, useExisting: CdkAccordion }],\n                }]\n        }], propDecorators: { multi: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Used to generate unique ID for each accordion item. */\nlet nextId = 0;\n/**\n * An basic directive expected to be extended and decorated as a component.  Sets up all\n * events and attributes needed to be managed by a CdkAccordion parent.\n */\nclass CdkAccordionItem {\n    constructor(accordion, _changeDetectorRef, _expansionDispatcher) {\n        this.accordion = accordion;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._expansionDispatcher = _expansionDispatcher;\n        /** Subscription to openAll/closeAll events. */\n        this._openCloseAllSubscription = Subscription.EMPTY;\n        /** Event emitted every time the AccordionItem is closed. */\n        this.closed = new EventEmitter();\n        /** Event emitted every time the AccordionItem is opened. */\n        this.opened = new EventEmitter();\n        /** Event emitted when the AccordionItem is destroyed. */\n        this.destroyed = new EventEmitter();\n        /**\n         * Emits whenever the expanded state of the accordion changes.\n         * Primarily used to facilitate two-way binding.\n         * @docs-private\n         */\n        this.expandedChange = new EventEmitter();\n        /** The unique AccordionItem id. */\n        this.id = `cdk-accordion-child-${nextId++}`;\n        this._expanded = false;\n        this._disabled = false;\n        /** Unregister function for _expansionDispatcher. */\n        this._removeUniqueSelectionListener = () => { };\n        this._removeUniqueSelectionListener = _expansionDispatcher.listen((id, accordionId) => {\n            if (this.accordion &&\n                !this.accordion.multi &&\n                this.accordion.id === accordionId &&\n                this.id !== id) {\n                this.expanded = false;\n            }\n        });\n        // When an accordion item is hosted in an accordion, subscribe to open/close events.\n        if (this.accordion) {\n            this._openCloseAllSubscription = this._subscribeToOpenCloseAllActions();\n        }\n    }\n    /** Whether the AccordionItem is expanded. */\n    get expanded() {\n        return this._expanded;\n    }\n    set expanded(expanded) {\n        expanded = coerceBooleanProperty(expanded);\n        // Only emit events and update the internal value if the value changes.\n        if (this._expanded !== expanded) {\n            this._expanded = expanded;\n            this.expandedChange.emit(expanded);\n            if (expanded) {\n                this.opened.emit();\n                /**\n                 * In the unique selection dispatcher, the id parameter is the id of the CdkAccordionItem,\n                 * the name value is the id of the accordion.\n                 */\n                const accordionId = this.accordion ? this.accordion.id : this.id;\n                this._expansionDispatcher.notify(this.id, accordionId);\n            }\n            else {\n                this.closed.emit();\n            }\n            // Ensures that the animation will run when the value is set outside of an `@Input`.\n            // This includes cases like the open, close and toggle methods.\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Whether the AccordionItem is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(disabled) {\n        this._disabled = coerceBooleanProperty(disabled);\n    }\n    /** Emits an event for the accordion item being destroyed. */\n    ngOnDestroy() {\n        this.opened.complete();\n        this.closed.complete();\n        this.destroyed.emit();\n        this.destroyed.complete();\n        this._removeUniqueSelectionListener();\n        this._openCloseAllSubscription.unsubscribe();\n    }\n    /** Toggles the expanded state of the accordion item. */\n    toggle() {\n        if (!this.disabled) {\n            this.expanded = !this.expanded;\n        }\n    }\n    /** Sets the expanded state of the accordion item to false. */\n    close() {\n        if (!this.disabled) {\n            this.expanded = false;\n        }\n    }\n    /** Sets the expanded state of the accordion item to true. */\n    open() {\n        if (!this.disabled) {\n            this.expanded = true;\n        }\n    }\n    _subscribeToOpenCloseAllActions() {\n        return this.accordion._openCloseAllActions.subscribe(expanded => {\n            // Only change expanded state if item is enabled\n            if (!this.disabled) {\n                this.expanded = expanded;\n            }\n        });\n    }\n}\nCdkAccordionItem.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkAccordionItem, deps: [{ token: CDK_ACCORDION, optional: true, skipSelf: true }, { token: i0.ChangeDetectorRef }, { token: i1.UniqueSelectionDispatcher }], target: i0.ɵɵFactoryTarget.Directive });\nCdkAccordionItem.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: CdkAccordionItem, selector: \"cdk-accordion-item, [cdkAccordionItem]\", inputs: { expanded: \"expanded\", disabled: \"disabled\" }, outputs: { closed: \"closed\", opened: \"opened\", destroyed: \"destroyed\", expandedChange: \"expandedChange\" }, providers: [\n        // Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n        // registering to the same accordion.\n        { provide: CDK_ACCORDION, useValue: undefined },\n    ], exportAs: [\"cdkAccordionItem\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkAccordionItem, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-accordion-item, [cdkAccordionItem]',\n                    exportAs: 'cdkAccordionItem',\n                    providers: [\n                        // Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n                        // registering to the same accordion.\n                        { provide: CDK_ACCORDION, useValue: undefined },\n                    ],\n                }]\n        }], ctorParameters: function () { return [{ type: CdkAccordion, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [CDK_ACCORDION]\n                }, {\n                    type: SkipSelf\n                }] }, { type: i0.ChangeDetectorRef }, { type: i1.UniqueSelectionDispatcher }]; }, propDecorators: { closed: [{\n                type: Output\n            }], opened: [{\n                type: Output\n            }], destroyed: [{\n                type: Output\n            }], expandedChange: [{\n                type: Output\n            }], expanded: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass CdkAccordionModule {\n}\nCdkAccordionModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkAccordionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nCdkAccordionModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkAccordionModule, declarations: [CdkAccordion, CdkAccordionItem], exports: [CdkAccordion, CdkAccordionItem] });\nCdkAccordionModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkAccordionModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkAccordionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [CdkAccordion, CdkAccordionItem],\n                    declarations: [CdkAccordion, CdkAccordionItem],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkAccordion, CdkAccordionItem, CdkAccordionModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,SAAzB,EAAoCC,KAApC,EAA2CC,YAA3C,EAAyDC,QAAzD,EAAmEC,MAAnE,EAA2EC,QAA3E,EAAqFC,MAArF,EAA6FC,QAA7F,QAA6G,eAA7G;AACA,OAAO,KAAKC,EAAZ,MAAoB,0BAApB;AACA,SAASC,qBAAT,QAAsC,uBAAtC;AACA,SAASC,OAAT,EAAkBC,YAAlB,QAAsC,MAAtC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;AACA,IAAIC,QAAQ,GAAG,CAAf;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,aAAa,GAAG,IAAId,cAAJ,CAAmB,cAAnB,CAAtB;AACA;AACA;AACA;;AACA,MAAMe,YAAN,CAAmB;EACfC,WAAW,GAAG;IACV;IACA,KAAKC,aAAL,GAAqB,IAAIN,OAAJ,EAArB;IACA;;IACA,KAAKO,oBAAL,GAA4B,IAAIP,OAAJ,EAA5B;IACA;;IACA,KAAKQ,EAAL,GAAW,iBAAgBN,QAAQ,EAAG,EAAtC;IACA,KAAKO,MAAL,GAAc,KAAd;EACH;EACD;;;EACS,IAALC,KAAK,GAAG;IACR,OAAO,KAAKD,MAAZ;EACH;;EACQ,IAALC,KAAK,CAACA,KAAD,EAAQ;IACb,KAAKD,MAAL,GAAcV,qBAAqB,CAACW,KAAD,CAAnC;EACH;EACD;;;EACAC,OAAO,GAAG;IACN,IAAI,KAAKF,MAAT,EAAiB;MACb,KAAKF,oBAAL,CAA0BK,IAA1B,CAA+B,IAA/B;IACH;EACJ;EACD;;;EACAC,QAAQ,GAAG;IACP,KAAKN,oBAAL,CAA0BK,IAA1B,CAA+B,KAA/B;EACH;;EACDE,WAAW,CAACC,OAAD,EAAU;IACjB,KAAKT,aAAL,CAAmBM,IAAnB,CAAwBG,OAAxB;EACH;;EACDC,WAAW,GAAG;IACV,KAAKV,aAAL,CAAmBW,QAAnB;;IACA,KAAKV,oBAAL,CAA0BU,QAA1B;EACH;;AAjCc;;AAmCnBb,YAAY,CAACc,IAAb;EAAA,iBAAyGd,YAAzG;AAAA;;AACAA,YAAY,CAACe,IAAb,kBAD+F/B,EAC/F;EAAA,MAA6FgB,YAA7F;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA,WAD+FhB,EAC/F,oBAA6L,CAAC;IAAEgC,OAAO,EAAEjB,aAAX;IAA0BkB,WAAW,EAAEjB;EAAvC,CAAD,CAA7L,GAD+FhB,EAC/F;AAAA;;AACA;EAAA,mDAF+FA,EAE/F,mBAA2FgB,YAA3F,EAAqH,CAAC;IAC1GkB,IAAI,EAAEhC,SADoG;IAE1GiC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,+BADX;MAECC,QAAQ,EAAE,cAFX;MAGCC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEjB,aAAX;QAA0BkB,WAAW,EAAEjB;MAAvC,CAAD;IAHZ,CAAD;EAFoG,CAAD,CAArH,QAO4B;IAAEM,KAAK,EAAE,CAAC;MACtBY,IAAI,EAAE/B;IADgB,CAAD;EAAT,CAP5B;AAAA;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,IAAIoC,MAAM,GAAG,CAAb;AACA;AACA;AACA;AACA;;AACA,MAAMC,gBAAN,CAAuB;EACnBvB,WAAW,CAACwB,SAAD,EAAYC,kBAAZ,EAAgCC,oBAAhC,EAAsD;IAC7D,KAAKF,SAAL,GAAiBA,SAAjB;IACA,KAAKC,kBAAL,GAA0BA,kBAA1B;IACA,KAAKC,oBAAL,GAA4BA,oBAA5B;IACA;;IACA,KAAKC,yBAAL,GAAiC/B,YAAY,CAACgC,KAA9C;IACA;;IACA,KAAKC,MAAL,GAAc,IAAI1C,YAAJ,EAAd;IACA;;IACA,KAAK2C,MAAL,GAAc,IAAI3C,YAAJ,EAAd;IACA;;IACA,KAAK4C,SAAL,GAAiB,IAAI5C,YAAJ,EAAjB;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAK6C,cAAL,GAAsB,IAAI7C,YAAJ,EAAtB;IACA;;IACA,KAAKgB,EAAL,GAAW,uBAAsBmB,MAAM,EAAG,EAA1C;IACA,KAAKW,SAAL,GAAiB,KAAjB;IACA,KAAKC,SAAL,GAAiB,KAAjB;IACA;;IACA,KAAKC,8BAAL,GAAsC,MAAM,CAAG,CAA/C;;IACA,KAAKA,8BAAL,GAAsCT,oBAAoB,CAACU,MAArB,CAA4B,CAACjC,EAAD,EAAKkC,WAAL,KAAqB;MACnF,IAAI,KAAKb,SAAL,IACA,CAAC,KAAKA,SAAL,CAAenB,KADhB,IAEA,KAAKmB,SAAL,CAAerB,EAAf,KAAsBkC,WAFtB,IAGA,KAAKlC,EAAL,KAAYA,EAHhB,EAGoB;QAChB,KAAKmC,QAAL,GAAgB,KAAhB;MACH;IACJ,CAPqC,CAAtC,CAxB6D,CAgC7D;;IACA,IAAI,KAAKd,SAAT,EAAoB;MAChB,KAAKG,yBAAL,GAAiC,KAAKY,+BAAL,EAAjC;IACH;EACJ;EACD;;;EACY,IAARD,QAAQ,GAAG;IACX,OAAO,KAAKL,SAAZ;EACH;;EACW,IAARK,QAAQ,CAACA,QAAD,EAAW;IACnBA,QAAQ,GAAG5C,qBAAqB,CAAC4C,QAAD,CAAhC,CADmB,CAEnB;;IACA,IAAI,KAAKL,SAAL,KAAmBK,QAAvB,EAAiC;MAC7B,KAAKL,SAAL,GAAiBK,QAAjB;MACA,KAAKN,cAAL,CAAoBQ,IAApB,CAAyBF,QAAzB;;MACA,IAAIA,QAAJ,EAAc;QACV,KAAKR,MAAL,CAAYU,IAAZ;QACA;AAChB;AACA;AACA;;QACgB,MAAMH,WAAW,GAAG,KAAKb,SAAL,GAAiB,KAAKA,SAAL,CAAerB,EAAhC,GAAqC,KAAKA,EAA9D;;QACA,KAAKuB,oBAAL,CAA0Be,MAA1B,CAAiC,KAAKtC,EAAtC,EAA0CkC,WAA1C;MACH,CARD,MASK;QACD,KAAKR,MAAL,CAAYW,IAAZ;MACH,CAd4B,CAe7B;MACA;;;MACA,KAAKf,kBAAL,CAAwBiB,YAAxB;IACH;EACJ;EACD;;;EACY,IAARC,QAAQ,GAAG;IACX,OAAO,KAAKT,SAAZ;EACH;;EACW,IAARS,QAAQ,CAACA,QAAD,EAAW;IACnB,KAAKT,SAAL,GAAiBxC,qBAAqB,CAACiD,QAAD,CAAtC;EACH;EACD;;;EACAhC,WAAW,GAAG;IACV,KAAKmB,MAAL,CAAYlB,QAAZ;IACA,KAAKiB,MAAL,CAAYjB,QAAZ;IACA,KAAKmB,SAAL,CAAeS,IAAf;IACA,KAAKT,SAAL,CAAenB,QAAf;;IACA,KAAKuB,8BAAL;;IACA,KAAKR,yBAAL,CAA+BiB,WAA/B;EACH;EACD;;;EACAC,MAAM,GAAG;IACL,IAAI,CAAC,KAAKF,QAAV,EAAoB;MAChB,KAAKL,QAAL,GAAgB,CAAC,KAAKA,QAAtB;IACH;EACJ;EACD;;;EACAQ,KAAK,GAAG;IACJ,IAAI,CAAC,KAAKH,QAAV,EAAoB;MAChB,KAAKL,QAAL,GAAgB,KAAhB;IACH;EACJ;EACD;;;EACAS,IAAI,GAAG;IACH,IAAI,CAAC,KAAKJ,QAAV,EAAoB;MAChB,KAAKL,QAAL,GAAgB,IAAhB;IACH;EACJ;;EACDC,+BAA+B,GAAG;IAC9B,OAAO,KAAKf,SAAL,CAAetB,oBAAf,CAAoC8C,SAApC,CAA8CV,QAAQ,IAAI;MAC7D;MACA,IAAI,CAAC,KAAKK,QAAV,EAAoB;QAChB,KAAKL,QAAL,GAAgBA,QAAhB;MACH;IACJ,CALM,CAAP;EAMH;;AA1GkB;;AA4GvBf,gBAAgB,CAACV,IAAjB;EAAA,iBAA6GU,gBAA7G,EAtI+FxC,EAsI/F,mBAA+Ie,aAA/I,OAtI+Ff,EAsI/F,mBAAyMA,EAAE,CAACkE,iBAA5M,GAtI+FlE,EAsI/F,mBAA0OU,EAAE,CAACyD,yBAA7O;AAAA;;AACA3B,gBAAgB,CAACT,IAAjB,kBAvI+F/B,EAuI/F;EAAA,MAAiGwC,gBAAjG;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAvI+FxC,EAuI/F,oBAAqV,CAC7U;EACA;EACA;IAAEgC,OAAO,EAAEjB,aAAX;IAA0BqD,QAAQ,EAAEC;EAApC,CAH6U,CAArV;AAAA;;AAKA;EAAA,mDA5I+FrE,EA4I/F,mBAA2FwC,gBAA3F,EAAyH,CAAC;IAC9GN,IAAI,EAAEhC,SADwG;IAE9GiC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wCADX;MAECC,QAAQ,EAAE,kBAFX;MAGCC,SAAS,EAAE,CACP;MACA;MACA;QAAEN,OAAO,EAAEjB,aAAX;QAA0BqD,QAAQ,EAAEC;MAApC,CAHO;IAHZ,CAAD;EAFwG,CAAD,CAAzH,EAW4B,YAAY;IAAE,OAAO,CAAC;MAAEnC,IAAI,EAAElB,YAAR;MAAsBsD,UAAU,EAAE,CAAC;QACjEpC,IAAI,EAAE7B;MAD2D,CAAD,EAEjE;QACC6B,IAAI,EAAE5B,MADP;QAEC6B,IAAI,EAAE,CAACpB,aAAD;MAFP,CAFiE,EAKjE;QACCmB,IAAI,EAAE3B;MADP,CALiE;IAAlC,CAAD,EAO3B;MAAE2B,IAAI,EAAElC,EAAE,CAACkE;IAAX,CAP2B,EAOK;MAAEhC,IAAI,EAAExB,EAAE,CAACyD;IAAX,CAPL,CAAP;EAOsD,CAlBhG,EAkBkH;IAAErB,MAAM,EAAE,CAAC;MAC7GZ,IAAI,EAAE1B;IADuG,CAAD,CAAV;IAElGuC,MAAM,EAAE,CAAC;MACTb,IAAI,EAAE1B;IADG,CAAD,CAF0F;IAIlGwC,SAAS,EAAE,CAAC;MACZd,IAAI,EAAE1B;IADM,CAAD,CAJuF;IAMlGyC,cAAc,EAAE,CAAC;MACjBf,IAAI,EAAE1B;IADW,CAAD,CANkF;IAQlG+C,QAAQ,EAAE,CAAC;MACXrB,IAAI,EAAE/B;IADK,CAAD,CARwF;IAUlGyD,QAAQ,EAAE,CAAC;MACX1B,IAAI,EAAE/B;IADK,CAAD;EAVwF,CAlBlH;AAAA;AAgCA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMoE,kBAAN,CAAyB;;AAEzBA,kBAAkB,CAACzC,IAAnB;EAAA,iBAA+GyC,kBAA/G;AAAA;;AACAA,kBAAkB,CAACC,IAAnB,kBAtL+FxE,EAsL/F;EAAA,MAAgHuE,kBAAhH;EAAA,eAAmJvD,YAAnJ,EAAiKwB,gBAAjK;EAAA,UAA8LxB,YAA9L,EAA4MwB,gBAA5M;AAAA;AACA+B,kBAAkB,CAACE,IAAnB,kBAvL+FzE,EAuL/F;;AACA;EAAA,mDAxL+FA,EAwL/F,mBAA2FuE,kBAA3F,EAA2H,CAAC;IAChHrC,IAAI,EAAEzB,QAD0G;IAEhH0B,IAAI,EAAE,CAAC;MACCuC,OAAO,EAAE,CAAC1D,YAAD,EAAewB,gBAAf,CADV;MAECmC,YAAY,EAAE,CAAC3D,YAAD,EAAewB,gBAAf;IAFf,CAAD;EAF0G,CAAD,CAA3H;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASxB,YAAT,EAAuBwB,gBAAvB,EAAyC+B,kBAAzC"}, "metadata": {}, "sourceType": "module"}