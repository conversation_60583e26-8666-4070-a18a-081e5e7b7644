{"ast": null, "code": "/**\n * @license Angular v14.3.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\nimport * as i1 from '@angular/common';\nimport { DOCUMENT, ɵparseCookieValue, XhrFactory as XhrFactory$1 } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, Inject, PLATFORM_ID, NgModule } from '@angular/core';\nimport { of, Observable } from 'rxjs';\nimport { concatMap, filter, map } from 'rxjs/operators';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Transforms an `HttpRequest` into a stream of `HttpEvent`s, one of which will likely be a\n * `HttpResponse`.\n *\n * `HttpHandler` is injectable. When injected, the handler instance dispatches requests to the\n * first interceptor in the chain, which dispatches to the second, etc, eventually reaching the\n * `HttpBackend`.\n *\n * In an `HttpInterceptor`, the `HttpHandler` parameter is the next interceptor in the chain.\n *\n * @publicApi\n */\n\nclass HttpHandler {}\n/**\n * A final `HttpHandler` which will dispatch the request via browser HTTP APIs to a backend.\n *\n * Interceptors sit between the `HttpClient` interface and the `HttpBackend`.\n *\n * When injected, `HttpBackend` dispatches requests directly to the backend, without going\n * through the interceptor chain.\n *\n * @publicApi\n */\n\n\nclass HttpBackend {}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Represents the header configuration options for an HTTP request.\n * Instances are immutable. Modifying methods return a cloned\n * instance with the change. The original object is never changed.\n *\n * @publicApi\n */\n\n\nclass HttpHeaders {\n  /**  Constructs a new HTTP header object with the given values.*/\n  constructor(headers) {\n    /**\n     * Internal map of lowercased header names to the normalized\n     * form of the name (the form seen first).\n     */\n    this.normalizedNames = new Map();\n    /**\n     * Queued updates to be materialized the next initialization.\n     */\n\n    this.lazyUpdate = null;\n\n    if (!headers) {\n      this.headers = new Map();\n    } else if (typeof headers === 'string') {\n      this.lazyInit = () => {\n        this.headers = new Map();\n        headers.split('\\n').forEach(line => {\n          const index = line.indexOf(':');\n\n          if (index > 0) {\n            const name = line.slice(0, index);\n            const key = name.toLowerCase();\n            const value = line.slice(index + 1).trim();\n            this.maybeSetNormalizedName(name, key);\n\n            if (this.headers.has(key)) {\n              this.headers.get(key).push(value);\n            } else {\n              this.headers.set(key, [value]);\n            }\n          }\n        });\n      };\n    } else {\n      this.lazyInit = () => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          assertValidHeaders(headers);\n        }\n\n        this.headers = new Map();\n        Object.keys(headers).forEach(name => {\n          let values = headers[name];\n          const key = name.toLowerCase();\n\n          if (typeof values === 'string') {\n            values = [values];\n          }\n\n          if (values.length > 0) {\n            this.headers.set(key, values);\n            this.maybeSetNormalizedName(name, key);\n          }\n        });\n      };\n    }\n  }\n  /**\n   * Checks for existence of a given header.\n   *\n   * @param name The header name to check for existence.\n   *\n   * @returns True if the header exists, false otherwise.\n   */\n\n\n  has(name) {\n    this.init();\n    return this.headers.has(name.toLowerCase());\n  }\n  /**\n   * Retrieves the first value of a given header.\n   *\n   * @param name The header name.\n   *\n   * @returns The value string if the header exists, null otherwise\n   */\n\n\n  get(name) {\n    this.init();\n    const values = this.headers.get(name.toLowerCase());\n    return values && values.length > 0 ? values[0] : null;\n  }\n  /**\n   * Retrieves the names of the headers.\n   *\n   * @returns A list of header names.\n   */\n\n\n  keys() {\n    this.init();\n    return Array.from(this.normalizedNames.values());\n  }\n  /**\n   * Retrieves a list of values for a given header.\n   *\n   * @param name The header name from which to retrieve values.\n   *\n   * @returns A string of values if the header exists, null otherwise.\n   */\n\n\n  getAll(name) {\n    this.init();\n    return this.headers.get(name.toLowerCase()) || null;\n  }\n  /**\n   * Appends a new value to the existing set of values for a header\n   * and returns them in a clone of the original instance.\n   *\n   * @param name The header name for which to append the values.\n   * @param value The value to append.\n   *\n   * @returns A clone of the HTTP headers object with the value appended to the given header.\n   */\n\n\n  append(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 'a'\n    });\n  }\n  /**\n   * Sets or modifies a value for a given header in a clone of the original instance.\n   * If the header already exists, its value is replaced with the given value\n   * in the returned object.\n   *\n   * @param name The header name.\n   * @param value The value or values to set or override for the given header.\n   *\n   * @returns A clone of the HTTP headers object with the newly set header value.\n   */\n\n\n  set(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 's'\n    });\n  }\n  /**\n   * Deletes values for a given header in a clone of the original instance.\n   *\n   * @param name The header name.\n   * @param value The value or values to delete for the given header.\n   *\n   * @returns A clone of the HTTP headers object with the given value deleted.\n   */\n\n\n  delete(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 'd'\n    });\n  }\n\n  maybeSetNormalizedName(name, lcName) {\n    if (!this.normalizedNames.has(lcName)) {\n      this.normalizedNames.set(lcName, name);\n    }\n  }\n\n  init() {\n    if (!!this.lazyInit) {\n      if (this.lazyInit instanceof HttpHeaders) {\n        this.copyFrom(this.lazyInit);\n      } else {\n        this.lazyInit();\n      }\n\n      this.lazyInit = null;\n\n      if (!!this.lazyUpdate) {\n        this.lazyUpdate.forEach(update => this.applyUpdate(update));\n        this.lazyUpdate = null;\n      }\n    }\n  }\n\n  copyFrom(other) {\n    other.init();\n    Array.from(other.headers.keys()).forEach(key => {\n      this.headers.set(key, other.headers.get(key));\n      this.normalizedNames.set(key, other.normalizedNames.get(key));\n    });\n  }\n\n  clone(update) {\n    const clone = new HttpHeaders();\n    clone.lazyInit = !!this.lazyInit && this.lazyInit instanceof HttpHeaders ? this.lazyInit : this;\n    clone.lazyUpdate = (this.lazyUpdate || []).concat([update]);\n    return clone;\n  }\n\n  applyUpdate(update) {\n    const key = update.name.toLowerCase();\n\n    switch (update.op) {\n      case 'a':\n      case 's':\n        let value = update.value;\n\n        if (typeof value === 'string') {\n          value = [value];\n        }\n\n        if (value.length === 0) {\n          return;\n        }\n\n        this.maybeSetNormalizedName(update.name, key);\n        const base = (update.op === 'a' ? this.headers.get(key) : undefined) || [];\n        base.push(...value);\n        this.headers.set(key, base);\n        break;\n\n      case 'd':\n        const toDelete = update.value;\n\n        if (!toDelete) {\n          this.headers.delete(key);\n          this.normalizedNames.delete(key);\n        } else {\n          let existing = this.headers.get(key);\n\n          if (!existing) {\n            return;\n          }\n\n          existing = existing.filter(value => toDelete.indexOf(value) === -1);\n\n          if (existing.length === 0) {\n            this.headers.delete(key);\n            this.normalizedNames.delete(key);\n          } else {\n            this.headers.set(key, existing);\n          }\n        }\n\n        break;\n    }\n  }\n  /**\n   * @internal\n   */\n\n\n  forEach(fn) {\n    this.init();\n    Array.from(this.normalizedNames.keys()).forEach(key => fn(this.normalizedNames.get(key), this.headers.get(key)));\n  }\n\n}\n/**\n * Verifies that the headers object has the right shape: the values\n * must be either strings or arrays. Throws an error if an invalid\n * header value is present.\n */\n\n\nfunction assertValidHeaders(headers) {\n  for (const [key, value] of Object.entries(headers)) {\n    if (typeof value !== 'string' && !Array.isArray(value)) {\n      throw new Error(`Unexpected value of the \\`${key}\\` header provided. ` + `Expecting either a string or an array, but got: \\`${value}\\`.`);\n    }\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Provides encoding and decoding of URL parameter and query-string values.\n *\n * Serializes and parses URL parameter keys and values to encode and decode them.\n * If you pass URL query parameters without encoding,\n * the query parameters can be misinterpreted at the receiving end.\n *\n *\n * @publicApi\n */\n\n\nclass HttpUrlEncodingCodec {\n  /**\n   * Encodes a key name for a URL parameter or query-string.\n   * @param key The key name.\n   * @returns The encoded key name.\n   */\n  encodeKey(key) {\n    return standardEncoding(key);\n  }\n  /**\n   * Encodes the value of a URL parameter or query-string.\n   * @param value The value.\n   * @returns The encoded value.\n   */\n\n\n  encodeValue(value) {\n    return standardEncoding(value);\n  }\n  /**\n   * Decodes an encoded URL parameter or query-string key.\n   * @param key The encoded key name.\n   * @returns The decoded key name.\n   */\n\n\n  decodeKey(key) {\n    return decodeURIComponent(key);\n  }\n  /**\n   * Decodes an encoded URL parameter or query-string value.\n   * @param value The encoded value.\n   * @returns The decoded value.\n   */\n\n\n  decodeValue(value) {\n    return decodeURIComponent(value);\n  }\n\n}\n\nfunction paramParser(rawParams, codec) {\n  const map = new Map();\n\n  if (rawParams.length > 0) {\n    // The `window.location.search` can be used while creating an instance of the `HttpParams` class\n    // (e.g. `new HttpParams({ fromString: window.location.search })`). The `window.location.search`\n    // may start with the `?` char, so we strip it if it's present.\n    const params = rawParams.replace(/^\\?/, '').split('&');\n    params.forEach(param => {\n      const eqIdx = param.indexOf('=');\n      const [key, val] = eqIdx == -1 ? [codec.decodeKey(param), ''] : [codec.decodeKey(param.slice(0, eqIdx)), codec.decodeValue(param.slice(eqIdx + 1))];\n      const list = map.get(key) || [];\n      list.push(val);\n      map.set(key, list);\n    });\n  }\n\n  return map;\n}\n/**\n * Encode input string with standard encodeURIComponent and then un-encode specific characters.\n */\n\n\nconst STANDARD_ENCODING_REGEX = /%(\\d[a-f0-9])/gi;\nconst STANDARD_ENCODING_REPLACEMENTS = {\n  '40': '@',\n  '3A': ':',\n  '24': '$',\n  '2C': ',',\n  '3B': ';',\n  '3D': '=',\n  '3F': '?',\n  '2F': '/'\n};\n\nfunction standardEncoding(v) {\n  return encodeURIComponent(v).replace(STANDARD_ENCODING_REGEX, (s, t) => STANDARD_ENCODING_REPLACEMENTS[t] ?? s);\n}\n\nfunction valueToString(value) {\n  return `${value}`;\n}\n/**\n * An HTTP request/response body that represents serialized parameters,\n * per the MIME type `application/x-www-form-urlencoded`.\n *\n * This class is immutable; all mutation operations return a new instance.\n *\n * @publicApi\n */\n\n\nclass HttpParams {\n  constructor(options = {}) {\n    this.updates = null;\n    this.cloneFrom = null;\n    this.encoder = options.encoder || new HttpUrlEncodingCodec();\n\n    if (!!options.fromString) {\n      if (!!options.fromObject) {\n        throw new Error(`Cannot specify both fromString and fromObject.`);\n      }\n\n      this.map = paramParser(options.fromString, this.encoder);\n    } else if (!!options.fromObject) {\n      this.map = new Map();\n      Object.keys(options.fromObject).forEach(key => {\n        const value = options.fromObject[key]; // convert the values to strings\n\n        const values = Array.isArray(value) ? value.map(valueToString) : [valueToString(value)];\n        this.map.set(key, values);\n      });\n    } else {\n      this.map = null;\n    }\n  }\n  /**\n   * Reports whether the body includes one or more values for a given parameter.\n   * @param param The parameter name.\n   * @returns True if the parameter has one or more values,\n   * false if it has no value or is not present.\n   */\n\n\n  has(param) {\n    this.init();\n    return this.map.has(param);\n  }\n  /**\n   * Retrieves the first value for a parameter.\n   * @param param The parameter name.\n   * @returns The first value of the given parameter,\n   * or `null` if the parameter is not present.\n   */\n\n\n  get(param) {\n    this.init();\n    const res = this.map.get(param);\n    return !!res ? res[0] : null;\n  }\n  /**\n   * Retrieves all values for a  parameter.\n   * @param param The parameter name.\n   * @returns All values in a string array,\n   * or `null` if the parameter not present.\n   */\n\n\n  getAll(param) {\n    this.init();\n    return this.map.get(param) || null;\n  }\n  /**\n   * Retrieves all the parameters for this body.\n   * @returns The parameter names in a string array.\n   */\n\n\n  keys() {\n    this.init();\n    return Array.from(this.map.keys());\n  }\n  /**\n   * Appends a new value to existing values for a parameter.\n   * @param param The parameter name.\n   * @param value The new value to add.\n   * @return A new body with the appended value.\n   */\n\n\n  append(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 'a'\n    });\n  }\n  /**\n   * Constructs a new body with appended values for the given parameter name.\n   * @param params parameters and values\n   * @return A new body with the new value.\n   */\n\n\n  appendAll(params) {\n    const updates = [];\n    Object.keys(params).forEach(param => {\n      const value = params[param];\n\n      if (Array.isArray(value)) {\n        value.forEach(_value => {\n          updates.push({\n            param,\n            value: _value,\n            op: 'a'\n          });\n        });\n      } else {\n        updates.push({\n          param,\n          value: value,\n          op: 'a'\n        });\n      }\n    });\n    return this.clone(updates);\n  }\n  /**\n   * Replaces the value for a parameter.\n   * @param param The parameter name.\n   * @param value The new value.\n   * @return A new body with the new value.\n   */\n\n\n  set(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 's'\n    });\n  }\n  /**\n   * Removes a given value or all values from a parameter.\n   * @param param The parameter name.\n   * @param value The value to remove, if provided.\n   * @return A new body with the given value removed, or with all values\n   * removed if no value is specified.\n   */\n\n\n  delete(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 'd'\n    });\n  }\n  /**\n   * Serializes the body to an encoded string, where key-value pairs (separated by `=`) are\n   * separated by `&`s.\n   */\n\n\n  toString() {\n    this.init();\n    return this.keys().map(key => {\n      const eKey = this.encoder.encodeKey(key); // `a: ['1']` produces `'a=1'`\n      // `b: []` produces `''`\n      // `c: ['1', '2']` produces `'c=1&c=2'`\n\n      return this.map.get(key).map(value => eKey + '=' + this.encoder.encodeValue(value)).join('&');\n    }) // filter out empty values because `b: []` produces `''`\n    // which results in `a=1&&c=1&c=2` instead of `a=1&c=1&c=2` if we don't\n    .filter(param => param !== '').join('&');\n  }\n\n  clone(update) {\n    const clone = new HttpParams({\n      encoder: this.encoder\n    });\n    clone.cloneFrom = this.cloneFrom || this;\n    clone.updates = (this.updates || []).concat(update);\n    return clone;\n  }\n\n  init() {\n    if (this.map === null) {\n      this.map = new Map();\n    }\n\n    if (this.cloneFrom !== null) {\n      this.cloneFrom.init();\n      this.cloneFrom.keys().forEach(key => this.map.set(key, this.cloneFrom.map.get(key)));\n      this.updates.forEach(update => {\n        switch (update.op) {\n          case 'a':\n          case 's':\n            const base = (update.op === 'a' ? this.map.get(update.param) : undefined) || [];\n            base.push(valueToString(update.value));\n            this.map.set(update.param, base);\n            break;\n\n          case 'd':\n            if (update.value !== undefined) {\n              let base = this.map.get(update.param) || [];\n              const idx = base.indexOf(valueToString(update.value));\n\n              if (idx !== -1) {\n                base.splice(idx, 1);\n              }\n\n              if (base.length > 0) {\n                this.map.set(update.param, base);\n              } else {\n                this.map.delete(update.param);\n              }\n            } else {\n              this.map.delete(update.param);\n              break;\n            }\n\n        }\n      });\n      this.cloneFrom = this.updates = null;\n    }\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A token used to manipulate and access values stored in `HttpContext`.\n *\n * @publicApi\n */\n\n\nclass HttpContextToken {\n  constructor(defaultValue) {\n    this.defaultValue = defaultValue;\n  }\n\n}\n/**\n * Http context stores arbitrary user defined values and ensures type safety without\n * actually knowing the types. It is backed by a `Map` and guarantees that keys do not clash.\n *\n * This context is mutable and is shared between cloned requests unless explicitly specified.\n *\n * @usageNotes\n *\n * ### Usage Example\n *\n * ```typescript\n * // inside cache.interceptors.ts\n * export const IS_CACHE_ENABLED = new HttpContextToken<boolean>(() => false);\n *\n * export class CacheInterceptor implements HttpInterceptor {\n *\n *   intercept(req: HttpRequest<any>, delegate: HttpHandler): Observable<HttpEvent<any>> {\n *     if (req.context.get(IS_CACHE_ENABLED) === true) {\n *       return ...;\n *     }\n *     return delegate.handle(req);\n *   }\n * }\n *\n * // inside a service\n *\n * this.httpClient.get('/api/weather', {\n *   context: new HttpContext().set(IS_CACHE_ENABLED, true)\n * }).subscribe(...);\n * ```\n *\n * @publicApi\n */\n\n\nclass HttpContext {\n  constructor() {\n    this.map = new Map();\n  }\n  /**\n   * Store a value in the context. If a value is already present it will be overwritten.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   * @param value The value to store.\n   *\n   * @returns A reference to itself for easy chaining.\n   */\n\n\n  set(token, value) {\n    this.map.set(token, value);\n    return this;\n  }\n  /**\n   * Retrieve the value associated with the given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns The stored value or default if one is defined.\n   */\n\n\n  get(token) {\n    if (!this.map.has(token)) {\n      this.map.set(token, token.defaultValue());\n    }\n\n    return this.map.get(token);\n  }\n  /**\n   * Delete the value associated with the given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns A reference to itself for easy chaining.\n   */\n\n\n  delete(token) {\n    this.map.delete(token);\n    return this;\n  }\n  /**\n   * Checks for existence of a given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns True if the token exists, false otherwise.\n   */\n\n\n  has(token) {\n    return this.map.has(token);\n  }\n  /**\n   * @returns a list of tokens currently stored in the context.\n   */\n\n\n  keys() {\n    return this.map.keys();\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Determine whether the given HTTP method may include a body.\n */\n\n\nfunction mightHaveBody(method) {\n  switch (method) {\n    case 'DELETE':\n    case 'GET':\n    case 'HEAD':\n    case 'OPTIONS':\n    case 'JSONP':\n      return false;\n\n    default:\n      return true;\n  }\n}\n/**\n * Safely assert whether the given value is an ArrayBuffer.\n *\n * In some execution environments ArrayBuffer is not defined.\n */\n\n\nfunction isArrayBuffer(value) {\n  return typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer;\n}\n/**\n * Safely assert whether the given value is a Blob.\n *\n * In some execution environments Blob is not defined.\n */\n\n\nfunction isBlob(value) {\n  return typeof Blob !== 'undefined' && value instanceof Blob;\n}\n/**\n * Safely assert whether the given value is a FormData instance.\n *\n * In some execution environments FormData is not defined.\n */\n\n\nfunction isFormData(value) {\n  return typeof FormData !== 'undefined' && value instanceof FormData;\n}\n/**\n * Safely assert whether the given value is a URLSearchParams instance.\n *\n * In some execution environments URLSearchParams is not defined.\n */\n\n\nfunction isUrlSearchParams(value) {\n  return typeof URLSearchParams !== 'undefined' && value instanceof URLSearchParams;\n}\n/**\n * An outgoing HTTP request with an optional typed body.\n *\n * `HttpRequest` represents an outgoing request, including URL, method,\n * headers, body, and other request configuration options. Instances should be\n * assumed to be immutable. To modify a `HttpRequest`, the `clone`\n * method should be used.\n *\n * @publicApi\n */\n\n\nclass HttpRequest {\n  constructor(method, url, third, fourth) {\n    this.url = url;\n    /**\n     * The request body, or `null` if one isn't set.\n     *\n     * Bodies are not enforced to be immutable, as they can include a reference to any\n     * user-defined data type. However, interceptors should take care to preserve\n     * idempotence by treating them as such.\n     */\n\n    this.body = null;\n    /**\n     * Whether this request should be made in a way that exposes progress events.\n     *\n     * Progress events are expensive (change detection runs on each event) and so\n     * they should only be requested if the consumer intends to monitor them.\n     */\n\n    this.reportProgress = false;\n    /**\n     * Whether this request should be sent with outgoing credentials (cookies).\n     */\n\n    this.withCredentials = false;\n    /**\n     * The expected response type of the server.\n     *\n     * This is used to parse the response appropriately before returning it to\n     * the requestee.\n     */\n\n    this.responseType = 'json';\n    this.method = method.toUpperCase(); // Next, need to figure out which argument holds the HttpRequestInit\n    // options, if any.\n\n    let options; // Check whether a body argument is expected. The only valid way to omit\n    // the body argument is to use a known no-body method like GET.\n\n    if (mightHaveBody(this.method) || !!fourth) {\n      // Body is the third argument, options are the fourth.\n      this.body = third !== undefined ? third : null;\n      options = fourth;\n    } else {\n      // No body required, options are the third argument. The body stays null.\n      options = third;\n    } // If options have been passed, interpret them.\n\n\n    if (options) {\n      // Normalize reportProgress and withCredentials.\n      this.reportProgress = !!options.reportProgress;\n      this.withCredentials = !!options.withCredentials; // Override default response type of 'json' if one is provided.\n\n      if (!!options.responseType) {\n        this.responseType = options.responseType;\n      } // Override headers if they're provided.\n\n\n      if (!!options.headers) {\n        this.headers = options.headers;\n      }\n\n      if (!!options.context) {\n        this.context = options.context;\n      }\n\n      if (!!options.params) {\n        this.params = options.params;\n      }\n    } // If no headers have been passed in, construct a new HttpHeaders instance.\n\n\n    if (!this.headers) {\n      this.headers = new HttpHeaders();\n    } // If no context have been passed in, construct a new HttpContext instance.\n\n\n    if (!this.context) {\n      this.context = new HttpContext();\n    } // If no parameters have been passed in, construct a new HttpUrlEncodedParams instance.\n\n\n    if (!this.params) {\n      this.params = new HttpParams();\n      this.urlWithParams = url;\n    } else {\n      // Encode the parameters to a string in preparation for inclusion in the URL.\n      const params = this.params.toString();\n\n      if (params.length === 0) {\n        // No parameters, the visible URL is just the URL given at creation time.\n        this.urlWithParams = url;\n      } else {\n        // Does the URL already have query parameters? Look for '?'.\n        const qIdx = url.indexOf('?'); // There are 3 cases to handle:\n        // 1) No existing parameters -> append '?' followed by params.\n        // 2) '?' exists and is followed by existing query string ->\n        //    append '&' followed by params.\n        // 3) '?' exists at the end of the url -> append params directly.\n        // This basically amounts to determining the character, if any, with\n        // which to join the URL and parameters.\n\n        const sep = qIdx === -1 ? '?' : qIdx < url.length - 1 ? '&' : '';\n        this.urlWithParams = url + sep + params;\n      }\n    }\n  }\n  /**\n   * Transform the free-form body into a serialized format suitable for\n   * transmission to the server.\n   */\n\n\n  serializeBody() {\n    // If no body is present, no need to serialize it.\n    if (this.body === null) {\n      return null;\n    } // Check whether the body is already in a serialized form. If so,\n    // it can just be returned directly.\n\n\n    if (isArrayBuffer(this.body) || isBlob(this.body) || isFormData(this.body) || isUrlSearchParams(this.body) || typeof this.body === 'string') {\n      return this.body;\n    } // Check whether the body is an instance of HttpUrlEncodedParams.\n\n\n    if (this.body instanceof HttpParams) {\n      return this.body.toString();\n    } // Check whether the body is an object or array, and serialize with JSON if so.\n\n\n    if (typeof this.body === 'object' || typeof this.body === 'boolean' || Array.isArray(this.body)) {\n      return JSON.stringify(this.body);\n    } // Fall back on toString() for everything else.\n\n\n    return this.body.toString();\n  }\n  /**\n   * Examine the body and attempt to infer an appropriate MIME type\n   * for it.\n   *\n   * If no such type can be inferred, this method will return `null`.\n   */\n\n\n  detectContentTypeHeader() {\n    // An empty body has no content type.\n    if (this.body === null) {\n      return null;\n    } // FormData bodies rely on the browser's content type assignment.\n\n\n    if (isFormData(this.body)) {\n      return null;\n    } // Blobs usually have their own content type. If it doesn't, then\n    // no type can be inferred.\n\n\n    if (isBlob(this.body)) {\n      return this.body.type || null;\n    } // Array buffers have unknown contents and thus no type can be inferred.\n\n\n    if (isArrayBuffer(this.body)) {\n      return null;\n    } // Technically, strings could be a form of JSON data, but it's safe enough\n    // to assume they're plain strings.\n\n\n    if (typeof this.body === 'string') {\n      return 'text/plain';\n    } // `HttpUrlEncodedParams` has its own content-type.\n\n\n    if (this.body instanceof HttpParams) {\n      return 'application/x-www-form-urlencoded;charset=UTF-8';\n    } // Arrays, objects, boolean and numbers will be encoded as JSON.\n\n\n    if (typeof this.body === 'object' || typeof this.body === 'number' || typeof this.body === 'boolean') {\n      return 'application/json';\n    } // No type could be inferred.\n\n\n    return null;\n  }\n\n  clone(update = {}) {\n    // For method, url, and responseType, take the current value unless\n    // it is overridden in the update hash.\n    const method = update.method || this.method;\n    const url = update.url || this.url;\n    const responseType = update.responseType || this.responseType; // The body is somewhat special - a `null` value in update.body means\n    // whatever current body is present is being overridden with an empty\n    // body, whereas an `undefined` value in update.body implies no\n    // override.\n\n    const body = update.body !== undefined ? update.body : this.body; // Carefully handle the boolean options to differentiate between\n    // `false` and `undefined` in the update args.\n\n    const withCredentials = update.withCredentials !== undefined ? update.withCredentials : this.withCredentials;\n    const reportProgress = update.reportProgress !== undefined ? update.reportProgress : this.reportProgress; // Headers and params may be appended to if `setHeaders` or\n    // `setParams` are used.\n\n    let headers = update.headers || this.headers;\n    let params = update.params || this.params; // Pass on context if needed\n\n    const context = update.context ?? this.context; // Check whether the caller has asked to add headers.\n\n    if (update.setHeaders !== undefined) {\n      // Set every requested header.\n      headers = Object.keys(update.setHeaders).reduce((headers, name) => headers.set(name, update.setHeaders[name]), headers);\n    } // Check whether the caller has asked to set params.\n\n\n    if (update.setParams) {\n      // Set every requested param.\n      params = Object.keys(update.setParams).reduce((params, param) => params.set(param, update.setParams[param]), params);\n    } // Finally, construct the new HttpRequest using the pieces from above.\n\n\n    return new HttpRequest(method, url, body, {\n      params,\n      headers,\n      context,\n      reportProgress,\n      responseType,\n      withCredentials\n    });\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Type enumeration for the different kinds of `HttpEvent`.\n *\n * @publicApi\n */\n\n\nvar HttpEventType;\n\n(function (HttpEventType) {\n  /**\n   * The request was sent out over the wire.\n   */\n  HttpEventType[HttpEventType[\"Sent\"] = 0] = \"Sent\";\n  /**\n   * An upload progress event was received.\n   */\n\n  HttpEventType[HttpEventType[\"UploadProgress\"] = 1] = \"UploadProgress\";\n  /**\n   * The response status code and headers were received.\n   */\n\n  HttpEventType[HttpEventType[\"ResponseHeader\"] = 2] = \"ResponseHeader\";\n  /**\n   * A download progress event was received.\n   */\n\n  HttpEventType[HttpEventType[\"DownloadProgress\"] = 3] = \"DownloadProgress\";\n  /**\n   * The full response including the body was received.\n   */\n\n  HttpEventType[HttpEventType[\"Response\"] = 4] = \"Response\";\n  /**\n   * A custom event from an interceptor or a backend.\n   */\n\n  HttpEventType[HttpEventType[\"User\"] = 5] = \"User\";\n})(HttpEventType || (HttpEventType = {}));\n/**\n * Base class for both `HttpResponse` and `HttpHeaderResponse`.\n *\n * @publicApi\n */\n\n\nclass HttpResponseBase {\n  /**\n   * Super-constructor for all responses.\n   *\n   * The single parameter accepted is an initialization hash. Any properties\n   * of the response passed there will override the default values.\n   */\n  constructor(init, defaultStatus = 200\n  /* HttpStatusCode.Ok */\n  , defaultStatusText = 'OK') {\n    // If the hash has values passed, use them to initialize the response.\n    // Otherwise use the default values.\n    this.headers = init.headers || new HttpHeaders();\n    this.status = init.status !== undefined ? init.status : defaultStatus;\n    this.statusText = init.statusText || defaultStatusText;\n    this.url = init.url || null; // Cache the ok value to avoid defining a getter.\n\n    this.ok = this.status >= 200 && this.status < 300;\n  }\n\n}\n/**\n * A partial HTTP response which only includes the status and header data,\n * but no response body.\n *\n * `HttpHeaderResponse` is a `HttpEvent` available on the response\n * event stream, only when progress events are requested.\n *\n * @publicApi\n */\n\n\nclass HttpHeaderResponse extends HttpResponseBase {\n  /**\n   * Create a new `HttpHeaderResponse` with the given parameters.\n   */\n  constructor(init = {}) {\n    super(init);\n    this.type = HttpEventType.ResponseHeader;\n  }\n  /**\n   * Copy this `HttpHeaderResponse`, overriding its contents with the\n   * given parameter hash.\n   */\n\n\n  clone(update = {}) {\n    // Perform a straightforward initialization of the new HttpHeaderResponse,\n    // overriding the current parameters with new ones if given.\n    return new HttpHeaderResponse({\n      headers: update.headers || this.headers,\n      status: update.status !== undefined ? update.status : this.status,\n      statusText: update.statusText || this.statusText,\n      url: update.url || this.url || undefined\n    });\n  }\n\n}\n/**\n * A full HTTP response, including a typed response body (which may be `null`\n * if one was not returned).\n *\n * `HttpResponse` is a `HttpEvent` available on the response event\n * stream.\n *\n * @publicApi\n */\n\n\nclass HttpResponse extends HttpResponseBase {\n  /**\n   * Construct a new `HttpResponse`.\n   */\n  constructor(init = {}) {\n    super(init);\n    this.type = HttpEventType.Response;\n    this.body = init.body !== undefined ? init.body : null;\n  }\n\n  clone(update = {}) {\n    return new HttpResponse({\n      body: update.body !== undefined ? update.body : this.body,\n      headers: update.headers || this.headers,\n      status: update.status !== undefined ? update.status : this.status,\n      statusText: update.statusText || this.statusText,\n      url: update.url || this.url || undefined\n    });\n  }\n\n}\n/**\n * A response that represents an error or failure, either from a\n * non-successful HTTP status, an error while executing the request,\n * or some other failure which occurred during the parsing of the response.\n *\n * Any error returned on the `Observable` response stream will be\n * wrapped in an `HttpErrorResponse` to provide additional context about\n * the state of the HTTP layer when the error occurred. The error property\n * will contain either a wrapped Error object or the error response returned\n * from the server.\n *\n * @publicApi\n */\n\n\nclass HttpErrorResponse extends HttpResponseBase {\n  constructor(init) {\n    // Initialize with a default status of 0 / Unknown Error.\n    super(init, 0, 'Unknown Error');\n    this.name = 'HttpErrorResponse';\n    /**\n     * Errors are never okay, even when the status code is in the 2xx success range.\n     */\n\n    this.ok = false; // If the response was successful, then this was a parse error. Otherwise, it was\n    // a protocol-level failure of some sort. Either the request failed in transit\n    // or the server returned an unsuccessful status code.\n\n    if (this.status >= 200 && this.status < 300) {\n      this.message = `Http failure during parsing for ${init.url || '(unknown url)'}`;\n    } else {\n      this.message = `Http failure response for ${init.url || '(unknown url)'}: ${init.status} ${init.statusText}`;\n    }\n\n    this.error = init.error || null;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Constructs an instance of `HttpRequestOptions<T>` from a source `HttpMethodOptions` and\n * the given `body`. This function clones the object and adds the body.\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n */\n\n\nfunction addBody(options, body) {\n  return {\n    body,\n    headers: options.headers,\n    context: options.context,\n    observe: options.observe,\n    params: options.params,\n    reportProgress: options.reportProgress,\n    responseType: options.responseType,\n    withCredentials: options.withCredentials\n  };\n}\n/**\n * Performs HTTP requests.\n * This service is available as an injectable class, with methods to perform HTTP requests.\n * Each request method has multiple signatures, and the return type varies based on\n * the signature that is called (mainly the values of `observe` and `responseType`).\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n\n *\n * @usageNotes\n * Sample HTTP requests for the [Tour of Heroes](/tutorial/toh-pt0) application.\n *\n * ### HTTP Request Example\n *\n * ```\n *  // GET heroes whose name contains search term\n * searchHeroes(term: string): observable<Hero[]>{\n *\n *  const params = new HttpParams({fromString: 'name=term'});\n *    return this.httpClient.request('GET', this.heroesUrl, {responseType:'json', params});\n * }\n * ```\n *\n * Alternatively, the parameter string can be used without invoking HttpParams\n * by directly joining to the URL.\n * ```\n * this.httpClient.request('GET', this.heroesUrl + '?' + 'name=term', {responseType:'json'});\n * ```\n *\n *\n * ### JSONP Example\n * ```\n * requestJsonp(url, callback = 'callback') {\n *  return this.httpClient.jsonp(this.heroesURL, callback);\n * }\n * ```\n *\n * ### PATCH Example\n * ```\n * // PATCH one of the heroes' name\n * patchHero (id: number, heroName: string): Observable<{}> {\n * const url = `${this.heroesUrl}/${id}`;   // PATCH api/heroes/42\n *  return this.httpClient.patch(url, {name: heroName}, httpOptions)\n *    .pipe(catchError(this.handleError('patchHero')));\n * }\n * ```\n *\n * @see [HTTP Guide](guide/http)\n * @see [HTTP Request](api/common/http/HttpRequest)\n *\n * @publicApi\n */\n\n\nclass HttpClient {\n  constructor(handler) {\n    this.handler = handler;\n  }\n  /**\n   * Constructs an observable for a generic HTTP request that, when subscribed,\n   * fires the request through the chain of registered interceptors and on to the\n   * server.\n   *\n   * You can pass an `HttpRequest` directly as the only parameter. In this case,\n   * the call returns an observable of the raw `HttpEvent` stream.\n   *\n   * Alternatively you can pass an HTTP method as the first parameter,\n   * a URL string as the second, and an options hash containing the request body as the third.\n   * See `addBody()`. In this case, the specified `responseType` and `observe` options determine the\n   * type of returned observable.\n   *   * The `responseType` value determines how a successful response body is parsed.\n   *   * If `responseType` is the default `json`, you can pass a type interface for the resulting\n   * object as a type parameter to the call.\n   *\n   * The `observe` value determines the return type, according to what you are interested in\n   * observing.\n   *   * An `observe` value of events returns an observable of the raw `HttpEvent` stream, including\n   * progress events by default.\n   *   * An `observe` value of response returns an observable of `HttpResponse<T>`,\n   * where the `T` parameter depends on the `responseType` and any optionally provided type\n   * parameter.\n   *   * An `observe` value of body returns an observable of `<T>` with the same `T` body type.\n   *\n   */\n\n\n  request(first, url, options = {}) {\n    let req; // First, check whether the primary argument is an instance of `HttpRequest`.\n\n    if (first instanceof HttpRequest) {\n      // It is. The other arguments must be undefined (per the signatures) and can be\n      // ignored.\n      req = first;\n    } else {\n      // It's a string, so it represents a URL. Construct a request based on it,\n      // and incorporate the remaining arguments (assuming `GET` unless a method is\n      // provided.\n      // Figure out the headers.\n      let headers = undefined;\n\n      if (options.headers instanceof HttpHeaders) {\n        headers = options.headers;\n      } else {\n        headers = new HttpHeaders(options.headers);\n      } // Sort out parameters.\n\n\n      let params = undefined;\n\n      if (!!options.params) {\n        if (options.params instanceof HttpParams) {\n          params = options.params;\n        } else {\n          params = new HttpParams({\n            fromObject: options.params\n          });\n        }\n      } // Construct the request.\n\n\n      req = new HttpRequest(first, url, options.body !== undefined ? options.body : null, {\n        headers,\n        context: options.context,\n        params,\n        reportProgress: options.reportProgress,\n        // By default, JSON is assumed to be returned for all calls.\n        responseType: options.responseType || 'json',\n        withCredentials: options.withCredentials\n      });\n    } // Start with an Observable.of() the initial request, and run the handler (which\n    // includes all interceptors) inside a concatMap(). This way, the handler runs\n    // inside an Observable chain, which causes interceptors to be re-run on every\n    // subscription (this also makes retries re-run the handler, including interceptors).\n\n\n    const events$ = of(req).pipe(concatMap(req => this.handler.handle(req))); // If coming via the API signature which accepts a previously constructed HttpRequest,\n    // the only option is to get the event stream. Otherwise, return the event stream if\n    // that is what was requested.\n\n    if (first instanceof HttpRequest || options.observe === 'events') {\n      return events$;\n    } // The requested stream contains either the full response or the body. In either\n    // case, the first step is to filter the event stream to extract a stream of\n    // responses(s).\n\n\n    const res$ = events$.pipe(filter(event => event instanceof HttpResponse)); // Decide which stream to return.\n\n    switch (options.observe || 'body') {\n      case 'body':\n        // The requested stream is the body. Map the response stream to the response\n        // body. This could be done more simply, but a misbehaving interceptor might\n        // transform the response body into a different format and ignore the requested\n        // responseType. Guard against this by validating that the response is of the\n        // requested type.\n        switch (req.responseType) {\n          case 'arraybuffer':\n            return res$.pipe(map(res => {\n              // Validate that the body is an ArrayBuffer.\n              if (res.body !== null && !(res.body instanceof ArrayBuffer)) {\n                throw new Error('Response is not an ArrayBuffer.');\n              }\n\n              return res.body;\n            }));\n\n          case 'blob':\n            return res$.pipe(map(res => {\n              // Validate that the body is a Blob.\n              if (res.body !== null && !(res.body instanceof Blob)) {\n                throw new Error('Response is not a Blob.');\n              }\n\n              return res.body;\n            }));\n\n          case 'text':\n            return res$.pipe(map(res => {\n              // Validate that the body is a string.\n              if (res.body !== null && typeof res.body !== 'string') {\n                throw new Error('Response is not a string.');\n              }\n\n              return res.body;\n            }));\n\n          case 'json':\n          default:\n            // No validation needed for JSON responses, as they can be of any type.\n            return res$.pipe(map(res => res.body));\n        }\n\n      case 'response':\n        // The response stream was requested directly, so return it.\n        return res$;\n\n      default:\n        // Guard against new future observe types being added.\n        throw new Error(`Unreachable: unhandled observe type ${options.observe}}`);\n    }\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `DELETE` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   */\n\n\n  delete(url, options = {}) {\n    return this.request('DELETE', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `GET` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   */\n\n\n  get(url, options = {}) {\n    return this.request('GET', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `HEAD` request to execute on the server. The `HEAD` method returns\n   * meta information about the resource without transferring the\n   * resource itself. See the individual overloads for\n   * details on the return type.\n   */\n\n\n  head(url, options = {}) {\n    return this.request('HEAD', url, options);\n  }\n  /**\n   * Constructs an `Observable` that, when subscribed, causes a request with the special method\n   * `JSONP` to be dispatched via the interceptor pipeline.\n   * The [JSONP pattern](https://en.wikipedia.org/wiki/JSONP) works around limitations of certain\n   * API endpoints that don't support newer,\n   * and preferable [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) protocol.\n   * JSONP treats the endpoint API as a JavaScript file and tricks the browser to process the\n   * requests even if the API endpoint is not located on the same domain (origin) as the client-side\n   * application making the request.\n   * The endpoint API must support JSONP callback for JSONP requests to work.\n   * The resource API returns the JSON response wrapped in a callback function.\n   * You can pass the callback function name as one of the query parameters.\n   * Note that JSONP requests can only be used with `GET` requests.\n   *\n   * @param url The resource URL.\n   * @param callbackParam The callback function name.\n   *\n   */\n\n\n  jsonp(url, callbackParam) {\n    return this.request('JSONP', url, {\n      params: new HttpParams().append(callbackParam, 'JSONP_CALLBACK'),\n      observe: 'body',\n      responseType: 'json'\n    });\n  }\n  /**\n   * Constructs an `Observable` that, when subscribed, causes the configured\n   * `OPTIONS` request to execute on the server. This method allows the client\n   * to determine the supported HTTP methods and other capabilities of an endpoint,\n   * without implying a resource action. See the individual overloads for\n   * details on the return type.\n   */\n\n\n  options(url, options = {}) {\n    return this.request('OPTIONS', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `PATCH` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   */\n\n\n  patch(url, body, options = {}) {\n    return this.request('PATCH', url, addBody(options, body));\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `POST` request to execute on the server. The server responds with the location of\n   * the replaced resource. See the individual overloads for\n   * details on the return type.\n   */\n\n\n  post(url, body, options = {}) {\n    return this.request('POST', url, addBody(options, body));\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `PUT` request to execute on the server. The `PUT` method replaces an existing resource\n   * with a new set of values.\n   * See the individual overloads for details on the return type.\n   */\n\n\n  put(url, body, options = {}) {\n    return this.request('PUT', url, addBody(options, body));\n  }\n\n}\n\nHttpClient.ɵfac = function HttpClient_Factory(t) {\n  return new (t || HttpClient)(i0.ɵɵinject(HttpHandler));\n};\n\nHttpClient.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HttpClient,\n  factory: HttpClient.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClient, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: HttpHandler\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * `HttpHandler` which applies an `HttpInterceptor` to an `HttpRequest`.\n *\n *\n */\n\n\nclass HttpInterceptorHandler {\n  constructor(next, interceptor) {\n    this.next = next;\n    this.interceptor = interceptor;\n  }\n\n  handle(req) {\n    return this.interceptor.intercept(req, this.next);\n  }\n\n}\n/**\n * A multi-provider token that represents the array of registered\n * `HttpInterceptor` objects.\n *\n * @publicApi\n */\n\n\nconst HTTP_INTERCEPTORS = new InjectionToken('HTTP_INTERCEPTORS');\n\nclass NoopInterceptor {\n  intercept(req, next) {\n    return next.handle(req);\n  }\n\n}\n\nNoopInterceptor.ɵfac = function NoopInterceptor_Factory(t) {\n  return new (t || NoopInterceptor)();\n};\n\nNoopInterceptor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NoopInterceptor,\n  factory: NoopInterceptor.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NoopInterceptor, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Every request made through JSONP needs a callback name that's unique across the\n// whole page. Each request is assigned an id and the callback name is constructed\n// from that. The next id to be assigned is tracked in a global variable here that\n// is shared among all applications on the page.\n\n\nlet nextRequestId = 0;\n/**\n * When a pending <script> is unsubscribed we'll move it to this document, so it won't be\n * executed.\n */\n\nlet foreignDocument; // Error text given when a JSONP script is injected, but doesn't invoke the callback\n// passed in its URL.\n\nconst JSONP_ERR_NO_CALLBACK = 'JSONP injected script did not invoke callback.'; // Error text given when a request is passed to the JsonpClientBackend that doesn't\n// have a request method JSONP.\n\nconst JSONP_ERR_WRONG_METHOD = 'JSONP requests must use JSONP request method.';\nconst JSONP_ERR_WRONG_RESPONSE_TYPE = 'JSONP requests must use Json response type.'; // Error text given when a request is passed to the JsonpClientBackend that has\n// headers set\n\nconst JSONP_ERR_HEADERS_NOT_SUPPORTED = 'JSONP requests do not support headers.';\n/**\n * DI token/abstract type representing a map of JSONP callbacks.\n *\n * In the browser, this should always be the `window` object.\n *\n *\n */\n\nclass JsonpCallbackContext {}\n/**\n * Processes an `HttpRequest` with the JSONP method,\n * by performing JSONP style requests.\n * @see `HttpHandler`\n * @see `HttpXhrBackend`\n *\n * @publicApi\n */\n\n\nclass JsonpClientBackend {\n  constructor(callbackMap, document) {\n    this.callbackMap = callbackMap;\n    this.document = document;\n    /**\n     * A resolved promise that can be used to schedule microtasks in the event handlers.\n     */\n\n    this.resolvedPromise = Promise.resolve();\n  }\n  /**\n   * Get the name of the next callback method, by incrementing the global `nextRequestId`.\n   */\n\n\n  nextCallback() {\n    return `ng_jsonp_callback_${nextRequestId++}`;\n  }\n  /**\n   * Processes a JSONP request and returns an event stream of the results.\n   * @param req The request object.\n   * @returns An observable of the response events.\n   *\n   */\n\n\n  handle(req) {\n    // Firstly, check both the method and response type. If either doesn't match\n    // then the request was improperly routed here and cannot be handled.\n    if (req.method !== 'JSONP') {\n      throw new Error(JSONP_ERR_WRONG_METHOD);\n    } else if (req.responseType !== 'json') {\n      throw new Error(JSONP_ERR_WRONG_RESPONSE_TYPE);\n    } // Check the request headers. JSONP doesn't support headers and\n    // cannot set any that were supplied.\n\n\n    if (req.headers.keys().length > 0) {\n      throw new Error(JSONP_ERR_HEADERS_NOT_SUPPORTED);\n    } // Everything else happens inside the Observable boundary.\n\n\n    return new Observable(observer => {\n      // The first step to make a request is to generate the callback name, and replace the\n      // callback placeholder in the URL with the name. Care has to be taken here to ensure\n      // a trailing &, if matched, gets inserted back into the URL in the correct place.\n      const callback = this.nextCallback();\n      const url = req.urlWithParams.replace(/=JSONP_CALLBACK(&|$)/, `=${callback}$1`); // Construct the <script> tag and point it at the URL.\n\n      const node = this.document.createElement('script');\n      node.src = url; // A JSONP request requires waiting for multiple callbacks. These variables\n      // are closed over and track state across those callbacks.\n      // The response object, if one has been received, or null otherwise.\n\n      let body = null; // Whether the response callback has been called.\n\n      let finished = false; // Set the response callback in this.callbackMap (which will be the window\n      // object in the browser. The script being loaded via the <script> tag will\n      // eventually call this callback.\n\n      this.callbackMap[callback] = data => {\n        // Data has been received from the JSONP script. Firstly, delete this callback.\n        delete this.callbackMap[callback]; // Set state to indicate data was received.\n\n        body = data;\n        finished = true;\n      }; // cleanup() is a utility closure that removes the <script> from the page and\n      // the response callback from the window. This logic is used in both the\n      // success, error, and cancellation paths, so it's extracted out for convenience.\n\n\n      const cleanup = () => {\n        // Remove the <script> tag if it's still on the page.\n        if (node.parentNode) {\n          node.parentNode.removeChild(node);\n        } // Remove the response callback from the callbackMap (window object in the\n        // browser).\n\n\n        delete this.callbackMap[callback];\n      }; // onLoad() is the success callback which runs after the response callback\n      // if the JSONP script loads successfully. The event itself is unimportant.\n      // If something went wrong, onLoad() may run without the response callback\n      // having been invoked.\n\n\n      const onLoad = event => {\n        // We wrap it in an extra Promise, to ensure the microtask\n        // is scheduled after the loaded endpoint has executed any potential microtask itself,\n        // which is not guaranteed in Internet Explorer and EdgeHTML. See issue #39496\n        this.resolvedPromise.then(() => {\n          // Cleanup the page.\n          cleanup(); // Check whether the response callback has run.\n\n          if (!finished) {\n            // It hasn't, something went wrong with the request. Return an error via\n            // the Observable error path. All JSONP errors have status 0.\n            observer.error(new HttpErrorResponse({\n              url,\n              status: 0,\n              statusText: 'JSONP Error',\n              error: new Error(JSONP_ERR_NO_CALLBACK)\n            }));\n            return;\n          } // Success. body either contains the response body or null if none was\n          // returned.\n\n\n          observer.next(new HttpResponse({\n            body,\n            status: 200\n            /* HttpStatusCode.Ok */\n            ,\n            statusText: 'OK',\n            url\n          })); // Complete the stream, the response is over.\n\n          observer.complete();\n        });\n      }; // onError() is the error callback, which runs if the script returned generates\n      // a Javascript error. It emits the error via the Observable error channel as\n      // a HttpErrorResponse.\n\n\n      const onError = error => {\n        cleanup(); // Wrap the error in a HttpErrorResponse.\n\n        observer.error(new HttpErrorResponse({\n          error,\n          status: 0,\n          statusText: 'JSONP Error',\n          url\n        }));\n      }; // Subscribe to both the success (load) and error events on the <script> tag,\n      // and add it to the page.\n\n\n      node.addEventListener('load', onLoad);\n      node.addEventListener('error', onError);\n      this.document.body.appendChild(node); // The request has now been successfully sent.\n\n      observer.next({\n        type: HttpEventType.Sent\n      }); // Cancellation handler.\n\n      return () => {\n        if (!finished) {\n          this.removeListeners(node);\n        } // And finally, clean up the page.\n\n\n        cleanup();\n      };\n    });\n  }\n\n  removeListeners(script) {\n    // Issue #34818\n    // Changing <script>'s ownerDocument will prevent it from execution.\n    // https://html.spec.whatwg.org/multipage/scripting.html#execute-the-script-block\n    if (!foreignDocument) {\n      foreignDocument = this.document.implementation.createHTMLDocument();\n    }\n\n    foreignDocument.adoptNode(script);\n  }\n\n}\n\nJsonpClientBackend.ɵfac = function JsonpClientBackend_Factory(t) {\n  return new (t || JsonpClientBackend)(i0.ɵɵinject(JsonpCallbackContext), i0.ɵɵinject(DOCUMENT));\n};\n\nJsonpClientBackend.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: JsonpClientBackend,\n  factory: JsonpClientBackend.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JsonpClientBackend, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: JsonpCallbackContext\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Identifies requests with the method JSONP and\n * shifts them to the `JsonpClientBackend`.\n *\n * @see `HttpInterceptor`\n *\n * @publicApi\n */\n\n\nclass JsonpInterceptor {\n  constructor(jsonp) {\n    this.jsonp = jsonp;\n  }\n  /**\n   * Identifies and handles a given JSONP request.\n   * @param req The outgoing request object to handle.\n   * @param next The next interceptor in the chain, or the backend\n   * if no interceptors remain in the chain.\n   * @returns An observable of the event stream.\n   */\n\n\n  intercept(req, next) {\n    if (req.method === 'JSONP') {\n      return this.jsonp.handle(req);\n    } // Fall through for normal HTTP requests.\n\n\n    return next.handle(req);\n  }\n\n}\n\nJsonpInterceptor.ɵfac = function JsonpInterceptor_Factory(t) {\n  return new (t || JsonpInterceptor)(i0.ɵɵinject(JsonpClientBackend));\n};\n\nJsonpInterceptor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: JsonpInterceptor,\n  factory: JsonpInterceptor.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JsonpInterceptor, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: JsonpClientBackend\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\n/**\n * Determine an appropriate URL for the response, by checking either\n * XMLHttpRequest.responseURL or the X-Request-URL header.\n */\n\nfunction getResponseUrl(xhr) {\n  if ('responseURL' in xhr && xhr.responseURL) {\n    return xhr.responseURL;\n  }\n\n  if (/^X-Request-URL:/m.test(xhr.getAllResponseHeaders())) {\n    return xhr.getResponseHeader('X-Request-URL');\n  }\n\n  return null;\n}\n/**\n * Uses `XMLHttpRequest` to send requests to a backend server.\n * @see `HttpHandler`\n * @see `JsonpClientBackend`\n *\n * @publicApi\n */\n\n\nclass HttpXhrBackend {\n  constructor(xhrFactory) {\n    this.xhrFactory = xhrFactory;\n  }\n  /**\n   * Processes a request and returns a stream of response events.\n   * @param req The request object.\n   * @returns An observable of the response events.\n   */\n\n\n  handle(req) {\n    // Quick check to give a better error message when a user attempts to use\n    // HttpClient.jsonp() without installing the HttpClientJsonpModule\n    if (req.method === 'JSONP') {\n      throw new Error(`Attempted to construct Jsonp request without HttpClientJsonpModule installed.`);\n    } // Everything happens on Observable subscription.\n\n\n    return new Observable(observer => {\n      // Start by setting up the XHR object with request method, URL, and withCredentials flag.\n      const xhr = this.xhrFactory.build();\n      xhr.open(req.method, req.urlWithParams);\n\n      if (!!req.withCredentials) {\n        xhr.withCredentials = true;\n      } // Add all the requested headers.\n\n\n      req.headers.forEach((name, values) => xhr.setRequestHeader(name, values.join(','))); // Add an Accept header if one isn't present already.\n\n      if (!req.headers.has('Accept')) {\n        xhr.setRequestHeader('Accept', 'application/json, text/plain, */*');\n      } // Auto-detect the Content-Type header if one isn't present already.\n\n\n      if (!req.headers.has('Content-Type')) {\n        const detectedType = req.detectContentTypeHeader(); // Sometimes Content-Type detection fails.\n\n        if (detectedType !== null) {\n          xhr.setRequestHeader('Content-Type', detectedType);\n        }\n      } // Set the responseType if one was requested.\n\n\n      if (req.responseType) {\n        const responseType = req.responseType.toLowerCase(); // JSON responses need to be processed as text. This is because if the server\n        // returns an XSSI-prefixed JSON response, the browser will fail to parse it,\n        // xhr.response will be null, and xhr.responseText cannot be accessed to\n        // retrieve the prefixed JSON data in order to strip the prefix. Thus, all JSON\n        // is parsed by first requesting text and then applying JSON.parse.\n\n        xhr.responseType = responseType !== 'json' ? responseType : 'text';\n      } // Serialize the request body if one is present. If not, this will be set to null.\n\n\n      const reqBody = req.serializeBody(); // If progress events are enabled, response headers will be delivered\n      // in two events - the HttpHeaderResponse event and the full HttpResponse\n      // event. However, since response headers don't change in between these\n      // two events, it doesn't make sense to parse them twice. So headerResponse\n      // caches the data extracted from the response whenever it's first parsed,\n      // to ensure parsing isn't duplicated.\n\n      let headerResponse = null; // partialFromXhr extracts the HttpHeaderResponse from the current XMLHttpRequest\n      // state, and memoizes it into headerResponse.\n\n      const partialFromXhr = () => {\n        if (headerResponse !== null) {\n          return headerResponse;\n        }\n\n        const statusText = xhr.statusText || 'OK'; // Parse headers from XMLHttpRequest - this step is lazy.\n\n        const headers = new HttpHeaders(xhr.getAllResponseHeaders()); // Read the response URL from the XMLHttpResponse instance and fall back on the\n        // request URL.\n\n        const url = getResponseUrl(xhr) || req.url; // Construct the HttpHeaderResponse and memoize it.\n\n        headerResponse = new HttpHeaderResponse({\n          headers,\n          status: xhr.status,\n          statusText,\n          url\n        });\n        return headerResponse;\n      }; // Next, a few closures are defined for the various events which XMLHttpRequest can\n      // emit. This allows them to be unregistered as event listeners later.\n      // First up is the load event, which represents a response being fully available.\n\n\n      const onLoad = () => {\n        // Read response state from the memoized partial data.\n        let {\n          headers,\n          status,\n          statusText,\n          url\n        } = partialFromXhr(); // The body will be read out if present.\n\n        let body = null;\n\n        if (status !== 204\n        /* HttpStatusCode.NoContent */\n        ) {\n          // Use XMLHttpRequest.response if set, responseText otherwise.\n          body = typeof xhr.response === 'undefined' ? xhr.responseText : xhr.response;\n        } // Normalize another potential bug (this one comes from CORS).\n\n\n        if (status === 0) {\n          status = !!body ? 200\n          /* HttpStatusCode.Ok */\n          : 0;\n        } // ok determines whether the response will be transmitted on the event or\n        // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n        // but a successful status code can still result in an error if the user\n        // asked for JSON data and the body cannot be parsed as such.\n\n\n        let ok = status >= 200 && status < 300; // Check whether the body needs to be parsed as JSON (in many cases the browser\n        // will have done that already).\n\n        if (req.responseType === 'json' && typeof body === 'string') {\n          // Save the original body, before attempting XSSI prefix stripping.\n          const originalBody = body;\n          body = body.replace(XSSI_PREFIX, '');\n\n          try {\n            // Attempt the parse. If it fails, a parse error should be delivered to the user.\n            body = body !== '' ? JSON.parse(body) : null;\n          } catch (error) {\n            // Since the JSON.parse failed, it's reasonable to assume this might not have been a\n            // JSON response. Restore the original body (including any XSSI prefix) to deliver\n            // a better error response.\n            body = originalBody; // If this was an error request to begin with, leave it as a string, it probably\n            // just isn't JSON. Otherwise, deliver the parsing error to the user.\n\n            if (ok) {\n              // Even though the response status was 2xx, this is still an error.\n              ok = false; // The parse error contains the text of the body that failed to parse.\n\n              body = {\n                error,\n                text: body\n              };\n            }\n          }\n        }\n\n        if (ok) {\n          // A successful response is delivered on the event stream.\n          observer.next(new HttpResponse({\n            body,\n            headers,\n            status,\n            statusText,\n            url: url || undefined\n          })); // The full body has been received and delivered, no further events\n          // are possible. This request is complete.\n\n          observer.complete();\n        } else {\n          // An unsuccessful request is delivered on the error channel.\n          observer.error(new HttpErrorResponse({\n            // The error in this case is the response body (error from the server).\n            error: body,\n            headers,\n            status,\n            statusText,\n            url: url || undefined\n          }));\n        }\n      }; // The onError callback is called when something goes wrong at the network level.\n      // Connection timeout, DNS error, offline, etc. These are actual errors, and are\n      // transmitted on the error channel.\n\n\n      const onError = error => {\n        const {\n          url\n        } = partialFromXhr();\n        const res = new HttpErrorResponse({\n          error,\n          status: xhr.status || 0,\n          statusText: xhr.statusText || 'Unknown Error',\n          url: url || undefined\n        });\n        observer.error(res);\n      }; // The sentHeaders flag tracks whether the HttpResponseHeaders event\n      // has been sent on the stream. This is necessary to track if progress\n      // is enabled since the event will be sent on only the first download\n      // progress event.\n\n\n      let sentHeaders = false; // The download progress event handler, which is only registered if\n      // progress events are enabled.\n\n      const onDownProgress = event => {\n        // Send the HttpResponseHeaders event if it hasn't been sent already.\n        if (!sentHeaders) {\n          observer.next(partialFromXhr());\n          sentHeaders = true;\n        } // Start building the download progress event to deliver on the response\n        // event stream.\n\n\n        let progressEvent = {\n          type: HttpEventType.DownloadProgress,\n          loaded: event.loaded\n        }; // Set the total number of bytes in the event if it's available.\n\n        if (event.lengthComputable) {\n          progressEvent.total = event.total;\n        } // If the request was for text content and a partial response is\n        // available on XMLHttpRequest, include it in the progress event\n        // to allow for streaming reads.\n\n\n        if (req.responseType === 'text' && !!xhr.responseText) {\n          progressEvent.partialText = xhr.responseText;\n        } // Finally, fire the event.\n\n\n        observer.next(progressEvent);\n      }; // The upload progress event handler, which is only registered if\n      // progress events are enabled.\n\n\n      const onUpProgress = event => {\n        // Upload progress events are simpler. Begin building the progress\n        // event.\n        let progress = {\n          type: HttpEventType.UploadProgress,\n          loaded: event.loaded\n        }; // If the total number of bytes being uploaded is available, include\n        // it.\n\n        if (event.lengthComputable) {\n          progress.total = event.total;\n        } // Send the event.\n\n\n        observer.next(progress);\n      }; // By default, register for load and error events.\n\n\n      xhr.addEventListener('load', onLoad);\n      xhr.addEventListener('error', onError);\n      xhr.addEventListener('timeout', onError);\n      xhr.addEventListener('abort', onError); // Progress events are only enabled if requested.\n\n      if (req.reportProgress) {\n        // Download progress is always enabled if requested.\n        xhr.addEventListener('progress', onDownProgress); // Upload progress depends on whether there is a body to upload.\n\n        if (reqBody !== null && xhr.upload) {\n          xhr.upload.addEventListener('progress', onUpProgress);\n        }\n      } // Fire the request, and notify the event stream that it was fired.\n\n\n      xhr.send(reqBody);\n      observer.next({\n        type: HttpEventType.Sent\n      }); // This is the return from the Observable function, which is the\n      // request cancellation handler.\n\n      return () => {\n        // On a cancellation, remove all registered event listeners.\n        xhr.removeEventListener('error', onError);\n        xhr.removeEventListener('abort', onError);\n        xhr.removeEventListener('load', onLoad);\n        xhr.removeEventListener('timeout', onError);\n\n        if (req.reportProgress) {\n          xhr.removeEventListener('progress', onDownProgress);\n\n          if (reqBody !== null && xhr.upload) {\n            xhr.upload.removeEventListener('progress', onUpProgress);\n          }\n        } // Finally, abort the in-flight request.\n\n\n        if (xhr.readyState !== xhr.DONE) {\n          xhr.abort();\n        }\n      };\n    });\n  }\n\n}\n\nHttpXhrBackend.ɵfac = function HttpXhrBackend_Factory(t) {\n  return new (t || HttpXhrBackend)(i0.ɵɵinject(i1.XhrFactory));\n};\n\nHttpXhrBackend.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HttpXhrBackend,\n  factory: HttpXhrBackend.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXhrBackend, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i1.XhrFactory\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst XSRF_COOKIE_NAME = new InjectionToken('XSRF_COOKIE_NAME');\nconst XSRF_HEADER_NAME = new InjectionToken('XSRF_HEADER_NAME');\n/**\n * Retrieves the current XSRF token to use with the next outgoing request.\n *\n * @publicApi\n */\n\nclass HttpXsrfTokenExtractor {}\n/**\n * `HttpXsrfTokenExtractor` which retrieves the token from a cookie.\n */\n\n\nclass HttpXsrfCookieExtractor {\n  constructor(doc, platform, cookieName) {\n    this.doc = doc;\n    this.platform = platform;\n    this.cookieName = cookieName;\n    this.lastCookieString = '';\n    this.lastToken = null;\n    /**\n     * @internal for testing\n     */\n\n    this.parseCount = 0;\n  }\n\n  getToken() {\n    if (this.platform === 'server') {\n      return null;\n    }\n\n    const cookieString = this.doc.cookie || '';\n\n    if (cookieString !== this.lastCookieString) {\n      this.parseCount++;\n      this.lastToken = ɵparseCookieValue(cookieString, this.cookieName);\n      this.lastCookieString = cookieString;\n    }\n\n    return this.lastToken;\n  }\n\n}\n\nHttpXsrfCookieExtractor.ɵfac = function HttpXsrfCookieExtractor_Factory(t) {\n  return new (t || HttpXsrfCookieExtractor)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(XSRF_COOKIE_NAME));\n};\n\nHttpXsrfCookieExtractor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HttpXsrfCookieExtractor,\n  factory: HttpXsrfCookieExtractor.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXsrfCookieExtractor, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [XSRF_COOKIE_NAME]\n      }]\n    }];\n  }, null);\n})();\n/**\n * `HttpInterceptor` which adds an XSRF token to eligible outgoing requests.\n */\n\n\nclass HttpXsrfInterceptor {\n  constructor(tokenService, headerName) {\n    this.tokenService = tokenService;\n    this.headerName = headerName;\n  }\n\n  intercept(req, next) {\n    const lcUrl = req.url.toLowerCase(); // Skip both non-mutating requests and absolute URLs.\n    // Non-mutating requests don't require a token, and absolute URLs require special handling\n    // anyway as the cookie set\n    // on our origin is not the same as the token expected by another origin.\n\n    if (req.method === 'GET' || req.method === 'HEAD' || lcUrl.startsWith('http://') || lcUrl.startsWith('https://')) {\n      return next.handle(req);\n    }\n\n    const token = this.tokenService.getToken(); // Be careful not to overwrite an existing header of the same name.\n\n    if (token !== null && !req.headers.has(this.headerName)) {\n      req = req.clone({\n        headers: req.headers.set(this.headerName, token)\n      });\n    }\n\n    return next.handle(req);\n  }\n\n}\n\nHttpXsrfInterceptor.ɵfac = function HttpXsrfInterceptor_Factory(t) {\n  return new (t || HttpXsrfInterceptor)(i0.ɵɵinject(HttpXsrfTokenExtractor), i0.ɵɵinject(XSRF_HEADER_NAME));\n};\n\nHttpXsrfInterceptor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HttpXsrfInterceptor,\n  factory: HttpXsrfInterceptor.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXsrfInterceptor, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: HttpXsrfTokenExtractor\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [XSRF_HEADER_NAME]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * An injectable `HttpHandler` that applies multiple interceptors\n * to a request before passing it to the given `HttpBackend`.\n *\n * The interceptors are loaded lazily from the injector, to allow\n * interceptors to themselves inject classes depending indirectly\n * on `HttpInterceptingHandler` itself.\n * @see `HttpInterceptor`\n */\n\n\nclass HttpInterceptingHandler {\n  constructor(backend, injector) {\n    this.backend = backend;\n    this.injector = injector;\n    this.chain = null;\n  }\n\n  handle(req) {\n    if (this.chain === null) {\n      const interceptors = this.injector.get(HTTP_INTERCEPTORS, []);\n      this.chain = interceptors.reduceRight((next, interceptor) => new HttpInterceptorHandler(next, interceptor), this.backend);\n    }\n\n    return this.chain.handle(req);\n  }\n\n}\n\nHttpInterceptingHandler.ɵfac = function HttpInterceptingHandler_Factory(t) {\n  return new (t || HttpInterceptingHandler)(i0.ɵɵinject(HttpBackend), i0.ɵɵinject(i0.Injector));\n};\n\nHttpInterceptingHandler.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HttpInterceptingHandler,\n  factory: HttpInterceptingHandler.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpInterceptingHandler, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: HttpBackend\n    }, {\n      type: i0.Injector\n    }];\n  }, null);\n})();\n/**\n * Constructs an `HttpHandler` that applies interceptors\n * to a request before passing it to the given `HttpBackend`.\n *\n * Use as a factory function within `HttpClientModule`.\n *\n *\n */\n\n\nfunction interceptingHandler(backend, interceptors = []) {\n  if (!interceptors) {\n    return backend;\n  }\n\n  return interceptors.reduceRight((next, interceptor) => new HttpInterceptorHandler(next, interceptor), backend);\n}\n/**\n * Factory function that determines where to store JSONP callbacks.\n *\n * Ordinarily JSONP callbacks are stored on the `window` object, but this may not exist\n * in test environments. In that case, callbacks are stored on an anonymous object instead.\n *\n *\n */\n\n\nfunction jsonpCallbackContext() {\n  if (typeof window === 'object') {\n    return window;\n  }\n\n  return {};\n}\n/**\n * Configures XSRF protection support for outgoing requests.\n *\n * For a server that supports a cookie-based XSRF protection system,\n * use directly to configure XSRF protection with the correct\n * cookie and header names.\n *\n * If no names are supplied, the default cookie name is `XSRF-TOKEN`\n * and the default header name is `X-XSRF-TOKEN`.\n *\n * @publicApi\n */\n\n\nclass HttpClientXsrfModule {\n  /**\n   * Disable the default XSRF protection.\n   */\n  static disable() {\n    return {\n      ngModule: HttpClientXsrfModule,\n      providers: [{\n        provide: HttpXsrfInterceptor,\n        useClass: NoopInterceptor\n      }]\n    };\n  }\n  /**\n   * Configure XSRF protection.\n   * @param options An object that can specify either or both\n   * cookie name or header name.\n   * - Cookie name default is `XSRF-TOKEN`.\n   * - Header name default is `X-XSRF-TOKEN`.\n   *\n   */\n\n\n  static withOptions(options = {}) {\n    return {\n      ngModule: HttpClientXsrfModule,\n      providers: [options.cookieName ? {\n        provide: XSRF_COOKIE_NAME,\n        useValue: options.cookieName\n      } : [], options.headerName ? {\n        provide: XSRF_HEADER_NAME,\n        useValue: options.headerName\n      } : []]\n    };\n  }\n\n}\n\nHttpClientXsrfModule.ɵfac = function HttpClientXsrfModule_Factory(t) {\n  return new (t || HttpClientXsrfModule)();\n};\n\nHttpClientXsrfModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: HttpClientXsrfModule\n});\nHttpClientXsrfModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [HttpXsrfInterceptor, {\n    provide: HTTP_INTERCEPTORS,\n    useExisting: HttpXsrfInterceptor,\n    multi: true\n  }, {\n    provide: HttpXsrfTokenExtractor,\n    useClass: HttpXsrfCookieExtractor\n  }, {\n    provide: XSRF_COOKIE_NAME,\n    useValue: 'XSRF-TOKEN'\n  }, {\n    provide: XSRF_HEADER_NAME,\n    useValue: 'X-XSRF-TOKEN'\n  }]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientXsrfModule, [{\n    type: NgModule,\n    args: [{\n      providers: [HttpXsrfInterceptor, {\n        provide: HTTP_INTERCEPTORS,\n        useExisting: HttpXsrfInterceptor,\n        multi: true\n      }, {\n        provide: HttpXsrfTokenExtractor,\n        useClass: HttpXsrfCookieExtractor\n      }, {\n        provide: XSRF_COOKIE_NAME,\n        useValue: 'XSRF-TOKEN'\n      }, {\n        provide: XSRF_HEADER_NAME,\n        useValue: 'X-XSRF-TOKEN'\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Configures the [dependency injector](guide/glossary#injector) for `HttpClient`\n * with supporting services for XSRF. Automatically imported by `HttpClientModule`.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in [DI token](guide/glossary#di-token) `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n */\n\n\nclass HttpClientModule {}\n\nHttpClientModule.ɵfac = function HttpClientModule_Factory(t) {\n  return new (t || HttpClientModule)();\n};\n\nHttpClientModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: HttpClientModule,\n  imports: [HttpClientXsrfModule]\n});\nHttpClientModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [HttpClient, {\n    provide: HttpHandler,\n    useClass: HttpInterceptingHandler\n  }, HttpXhrBackend, {\n    provide: HttpBackend,\n    useExisting: HttpXhrBackend\n  }],\n  imports: [HttpClientXsrfModule.withOptions({\n    cookieName: 'XSRF-TOKEN',\n    headerName: 'X-XSRF-TOKEN'\n  })]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientModule, [{\n    type: NgModule,\n    args: [{\n      /**\n       * Optional configuration for XSRF protection.\n       */\n      imports: [HttpClientXsrfModule.withOptions({\n        cookieName: 'XSRF-TOKEN',\n        headerName: 'X-XSRF-TOKEN'\n      })],\n\n      /**\n       * Configures the [dependency injector](guide/glossary#injector) where it is imported\n       * with supporting services for HTTP communications.\n       */\n      providers: [HttpClient, {\n        provide: HttpHandler,\n        useClass: HttpInterceptingHandler\n      }, HttpXhrBackend, {\n        provide: HttpBackend,\n        useExisting: HttpXhrBackend\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Configures the [dependency injector](guide/glossary#injector) for `HttpClient`\n * with supporting services for JSONP.\n * Without this module, Jsonp requests reach the backend\n * with method JSONP, where they are rejected.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in [DI token](guide/glossary#di-token) `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n */\n\n\nclass HttpClientJsonpModule {}\n\nHttpClientJsonpModule.ɵfac = function HttpClientJsonpModule_Factory(t) {\n  return new (t || HttpClientJsonpModule)();\n};\n\nHttpClientJsonpModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: HttpClientJsonpModule\n});\nHttpClientJsonpModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [JsonpClientBackend, {\n    provide: JsonpCallbackContext,\n    useFactory: jsonpCallbackContext\n  }, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: JsonpInterceptor,\n    multi: true\n  }]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientJsonpModule, [{\n    type: NgModule,\n    args: [{\n      providers: [JsonpClientBackend, {\n        provide: JsonpCallbackContext,\n        useFactory: jsonpCallbackContext\n      }, {\n        provide: HTTP_INTERCEPTORS,\n        useClass: JsonpInterceptor,\n        multi: true\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A wrapper around the `XMLHttpRequest` constructor.\n *\n * @publicApi\n * @see `XhrFactory`\n * @deprecated\n * `XhrFactory` has moved, please import `XhrFactory` from `@angular/common` instead.\n */\n\n\nconst XhrFactory = XhrFactory$1;\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { HTTP_INTERCEPTORS, HttpBackend, HttpClient, HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, HttpContext, HttpContextToken, HttpErrorResponse, HttpEventType, HttpHandler, HttpHeaderResponse, HttpHeaders, HttpParams, HttpRequest, HttpResponse, HttpResponseBase, HttpUrlEncodingCodec, HttpXhrBackend, HttpXsrfTokenExtractor, JsonpClientBackend, JsonpInterceptor, XhrFactory, HttpInterceptingHandler as ɵHttpInterceptingHandler };", "map": {"version": 3, "names": ["i1", "DOCUMENT", "ɵparseCookieValue", "XhrFactory", "XhrFactory$1", "i0", "Injectable", "InjectionToken", "Inject", "PLATFORM_ID", "NgModule", "of", "Observable", "concatMap", "filter", "map", "HttpHandler", "HttpBackend", "HttpHeaders", "constructor", "headers", "normalizedNames", "Map", "lazyUpdate", "lazyInit", "split", "for<PERSON>ach", "line", "index", "indexOf", "name", "slice", "key", "toLowerCase", "value", "trim", "maybeSetNormalizedName", "has", "get", "push", "set", "ngDevMode", "assertValidHeaders", "Object", "keys", "values", "length", "init", "Array", "from", "getAll", "append", "clone", "op", "delete", "lcName", "copyFrom", "update", "applyUpdate", "other", "concat", "base", "undefined", "toDelete", "existing", "fn", "entries", "isArray", "Error", "HttpUrlEncodingCodec", "encodeKey", "standardEncoding", "encodeValue", "decodeKey", "decodeURIComponent", "decodeValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rawParams", "codec", "params", "replace", "param", "eqIdx", "val", "list", "STANDARD_ENCODING_REGEX", "STANDARD_ENCODING_REPLACEMENTS", "v", "encodeURIComponent", "s", "t", "valueToString", "HttpParams", "options", "updates", "cloneFrom", "encoder", "fromString", "fromObject", "res", "appendAll", "_value", "toString", "<PERSON><PERSON><PERSON>", "join", "idx", "splice", "HttpContextToken", "defaultValue", "HttpContext", "token", "mightHaveBody", "method", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isBlob", "Blob", "isFormData", "FormData", "isUrlSearchParams", "URLSearchParams", "HttpRequest", "url", "third", "fourth", "body", "reportProgress", "withCredentials", "responseType", "toUpperCase", "context", "urlWithParams", "qIdx", "sep", "serializeBody", "JSON", "stringify", "detectContentTypeHeader", "type", "setHeaders", "reduce", "setParams", "HttpEventType", "HttpResponseBase", "defaultStatus", "defaultStatusText", "status", "statusText", "ok", "HttpHeaderResponse", "ResponseHeader", "HttpResponse", "Response", "HttpErrorResponse", "message", "error", "addBody", "observe", "HttpClient", "handler", "request", "first", "req", "events$", "pipe", "handle", "res$", "event", "head", "jsonp", "callback<PERSON><PERSON><PERSON>", "patch", "post", "put", "ɵfac", "ɵprov", "HttpInterceptorHandler", "next", "interceptor", "intercept", "HTTP_INTERCEPTORS", "NoopInterceptor", "nextRequestId", "foreignDocument", "JSONP_ERR_NO_CALLBACK", "JSONP_ERR_WRONG_METHOD", "JSONP_ERR_WRONG_RESPONSE_TYPE", "JSONP_ERR_HEADERS_NOT_SUPPORTED", "JsonpCallbackContext", "JsonpClientBackend", "callbackMap", "document", "resolvedPromise", "Promise", "resolve", "nextCallback", "observer", "callback", "node", "createElement", "src", "finished", "data", "cleanup", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "onLoad", "then", "complete", "onError", "addEventListener", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "removeListeners", "script", "implementation", "createHTMLDocument", "adoptNode", "decorators", "args", "JsonpInterceptor", "XSSI_PREFIX", "getResponseUrl", "xhr", "responseURL", "test", "getAllResponseHeaders", "getResponseHeader", "HttpXhrBackend", "xhrFactory", "build", "open", "setRequestHeader", "detectedType", "reqBody", "headerResponse", "partialFromXhr", "response", "responseText", "originalBody", "parse", "text", "sentHeaders", "onDownProgress", "progressEvent", "DownloadProgress", "loaded", "lengthComputable", "total", "partialText", "onUpProgress", "progress", "UploadProgress", "upload", "send", "removeEventListener", "readyState", "DONE", "abort", "XSRF_COOKIE_NAME", "XSRF_HEADER_NAME", "HttpXsrfTokenExtractor", "HttpXsrfCookieExtractor", "doc", "platform", "cookieName", "lastCookieString", "lastToken", "parseCount", "getToken", "cookieString", "cookie", "HttpXsrfInterceptor", "tokenService", "headerName", "lcUrl", "startsWith", "HttpInterceptingHandler", "backend", "injector", "chain", "interceptors", "reduceRight", "Injector", "intercepting<PERSON><PERSON><PERSON>", "jsonpCallbackContext", "window", "HttpClientXsrfModule", "disable", "ngModule", "providers", "provide", "useClass", "withOptions", "useValue", "ɵmod", "ɵinj", "useExisting", "multi", "HttpClientModule", "imports", "HttpClientJsonpModule", "useFactory", "ɵHttpInterceptingHandler"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/common/fesm2020/http.mjs"], "sourcesContent": ["/**\n * @license Angular v14.3.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i1 from '@angular/common';\nimport { DOCUMENT, ɵparseCookieValue, XhrFactory as XhrFactory$1 } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, Inject, PLATFORM_ID, NgModule } from '@angular/core';\nimport { of, Observable } from 'rxjs';\nimport { concatMap, filter, map } from 'rxjs/operators';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Transforms an `HttpRequest` into a stream of `HttpEvent`s, one of which will likely be a\n * `HttpResponse`.\n *\n * `HttpHandler` is injectable. When injected, the handler instance dispatches requests to the\n * first interceptor in the chain, which dispatches to the second, etc, eventually reaching the\n * `HttpBackend`.\n *\n * In an `HttpInterceptor`, the `HttpHandler` parameter is the next interceptor in the chain.\n *\n * @publicApi\n */\nclass HttpHandler {\n}\n/**\n * A final `HttpHandler` which will dispatch the request via browser HTTP APIs to a backend.\n *\n * Interceptors sit between the `HttpClient` interface and the `HttpBackend`.\n *\n * When injected, `HttpBackend` dispatches requests directly to the backend, without going\n * through the interceptor chain.\n *\n * @publicApi\n */\nclass HttpBackend {\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Represents the header configuration options for an HTTP request.\n * Instances are immutable. Modifying methods return a cloned\n * instance with the change. The original object is never changed.\n *\n * @publicApi\n */\nclass HttpHeaders {\n    /**  Constructs a new HTTP header object with the given values.*/\n    constructor(headers) {\n        /**\n         * Internal map of lowercased header names to the normalized\n         * form of the name (the form seen first).\n         */\n        this.normalizedNames = new Map();\n        /**\n         * Queued updates to be materialized the next initialization.\n         */\n        this.lazyUpdate = null;\n        if (!headers) {\n            this.headers = new Map();\n        }\n        else if (typeof headers === 'string') {\n            this.lazyInit = () => {\n                this.headers = new Map();\n                headers.split('\\n').forEach(line => {\n                    const index = line.indexOf(':');\n                    if (index > 0) {\n                        const name = line.slice(0, index);\n                        const key = name.toLowerCase();\n                        const value = line.slice(index + 1).trim();\n                        this.maybeSetNormalizedName(name, key);\n                        if (this.headers.has(key)) {\n                            this.headers.get(key).push(value);\n                        }\n                        else {\n                            this.headers.set(key, [value]);\n                        }\n                    }\n                });\n            };\n        }\n        else {\n            this.lazyInit = () => {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    assertValidHeaders(headers);\n                }\n                this.headers = new Map();\n                Object.keys(headers).forEach(name => {\n                    let values = headers[name];\n                    const key = name.toLowerCase();\n                    if (typeof values === 'string') {\n                        values = [values];\n                    }\n                    if (values.length > 0) {\n                        this.headers.set(key, values);\n                        this.maybeSetNormalizedName(name, key);\n                    }\n                });\n            };\n        }\n    }\n    /**\n     * Checks for existence of a given header.\n     *\n     * @param name The header name to check for existence.\n     *\n     * @returns True if the header exists, false otherwise.\n     */\n    has(name) {\n        this.init();\n        return this.headers.has(name.toLowerCase());\n    }\n    /**\n     * Retrieves the first value of a given header.\n     *\n     * @param name The header name.\n     *\n     * @returns The value string if the header exists, null otherwise\n     */\n    get(name) {\n        this.init();\n        const values = this.headers.get(name.toLowerCase());\n        return values && values.length > 0 ? values[0] : null;\n    }\n    /**\n     * Retrieves the names of the headers.\n     *\n     * @returns A list of header names.\n     */\n    keys() {\n        this.init();\n        return Array.from(this.normalizedNames.values());\n    }\n    /**\n     * Retrieves a list of values for a given header.\n     *\n     * @param name The header name from which to retrieve values.\n     *\n     * @returns A string of values if the header exists, null otherwise.\n     */\n    getAll(name) {\n        this.init();\n        return this.headers.get(name.toLowerCase()) || null;\n    }\n    /**\n     * Appends a new value to the existing set of values for a header\n     * and returns them in a clone of the original instance.\n     *\n     * @param name The header name for which to append the values.\n     * @param value The value to append.\n     *\n     * @returns A clone of the HTTP headers object with the value appended to the given header.\n     */\n    append(name, value) {\n        return this.clone({ name, value, op: 'a' });\n    }\n    /**\n     * Sets or modifies a value for a given header in a clone of the original instance.\n     * If the header already exists, its value is replaced with the given value\n     * in the returned object.\n     *\n     * @param name The header name.\n     * @param value The value or values to set or override for the given header.\n     *\n     * @returns A clone of the HTTP headers object with the newly set header value.\n     */\n    set(name, value) {\n        return this.clone({ name, value, op: 's' });\n    }\n    /**\n     * Deletes values for a given header in a clone of the original instance.\n     *\n     * @param name The header name.\n     * @param value The value or values to delete for the given header.\n     *\n     * @returns A clone of the HTTP headers object with the given value deleted.\n     */\n    delete(name, value) {\n        return this.clone({ name, value, op: 'd' });\n    }\n    maybeSetNormalizedName(name, lcName) {\n        if (!this.normalizedNames.has(lcName)) {\n            this.normalizedNames.set(lcName, name);\n        }\n    }\n    init() {\n        if (!!this.lazyInit) {\n            if (this.lazyInit instanceof HttpHeaders) {\n                this.copyFrom(this.lazyInit);\n            }\n            else {\n                this.lazyInit();\n            }\n            this.lazyInit = null;\n            if (!!this.lazyUpdate) {\n                this.lazyUpdate.forEach(update => this.applyUpdate(update));\n                this.lazyUpdate = null;\n            }\n        }\n    }\n    copyFrom(other) {\n        other.init();\n        Array.from(other.headers.keys()).forEach(key => {\n            this.headers.set(key, other.headers.get(key));\n            this.normalizedNames.set(key, other.normalizedNames.get(key));\n        });\n    }\n    clone(update) {\n        const clone = new HttpHeaders();\n        clone.lazyInit =\n            (!!this.lazyInit && this.lazyInit instanceof HttpHeaders) ? this.lazyInit : this;\n        clone.lazyUpdate = (this.lazyUpdate || []).concat([update]);\n        return clone;\n    }\n    applyUpdate(update) {\n        const key = update.name.toLowerCase();\n        switch (update.op) {\n            case 'a':\n            case 's':\n                let value = update.value;\n                if (typeof value === 'string') {\n                    value = [value];\n                }\n                if (value.length === 0) {\n                    return;\n                }\n                this.maybeSetNormalizedName(update.name, key);\n                const base = (update.op === 'a' ? this.headers.get(key) : undefined) || [];\n                base.push(...value);\n                this.headers.set(key, base);\n                break;\n            case 'd':\n                const toDelete = update.value;\n                if (!toDelete) {\n                    this.headers.delete(key);\n                    this.normalizedNames.delete(key);\n                }\n                else {\n                    let existing = this.headers.get(key);\n                    if (!existing) {\n                        return;\n                    }\n                    existing = existing.filter(value => toDelete.indexOf(value) === -1);\n                    if (existing.length === 0) {\n                        this.headers.delete(key);\n                        this.normalizedNames.delete(key);\n                    }\n                    else {\n                        this.headers.set(key, existing);\n                    }\n                }\n                break;\n        }\n    }\n    /**\n     * @internal\n     */\n    forEach(fn) {\n        this.init();\n        Array.from(this.normalizedNames.keys())\n            .forEach(key => fn(this.normalizedNames.get(key), this.headers.get(key)));\n    }\n}\n/**\n * Verifies that the headers object has the right shape: the values\n * must be either strings or arrays. Throws an error if an invalid\n * header value is present.\n */\nfunction assertValidHeaders(headers) {\n    for (const [key, value] of Object.entries(headers)) {\n        if (typeof value !== 'string' && !Array.isArray(value)) {\n            throw new Error(`Unexpected value of the \\`${key}\\` header provided. ` +\n                `Expecting either a string or an array, but got: \\`${value}\\`.`);\n        }\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Provides encoding and decoding of URL parameter and query-string values.\n *\n * Serializes and parses URL parameter keys and values to encode and decode them.\n * If you pass URL query parameters without encoding,\n * the query parameters can be misinterpreted at the receiving end.\n *\n *\n * @publicApi\n */\nclass HttpUrlEncodingCodec {\n    /**\n     * Encodes a key name for a URL parameter or query-string.\n     * @param key The key name.\n     * @returns The encoded key name.\n     */\n    encodeKey(key) {\n        return standardEncoding(key);\n    }\n    /**\n     * Encodes the value of a URL parameter or query-string.\n     * @param value The value.\n     * @returns The encoded value.\n     */\n    encodeValue(value) {\n        return standardEncoding(value);\n    }\n    /**\n     * Decodes an encoded URL parameter or query-string key.\n     * @param key The encoded key name.\n     * @returns The decoded key name.\n     */\n    decodeKey(key) {\n        return decodeURIComponent(key);\n    }\n    /**\n     * Decodes an encoded URL parameter or query-string value.\n     * @param value The encoded value.\n     * @returns The decoded value.\n     */\n    decodeValue(value) {\n        return decodeURIComponent(value);\n    }\n}\nfunction paramParser(rawParams, codec) {\n    const map = new Map();\n    if (rawParams.length > 0) {\n        // The `window.location.search` can be used while creating an instance of the `HttpParams` class\n        // (e.g. `new HttpParams({ fromString: window.location.search })`). The `window.location.search`\n        // may start with the `?` char, so we strip it if it's present.\n        const params = rawParams.replace(/^\\?/, '').split('&');\n        params.forEach((param) => {\n            const eqIdx = param.indexOf('=');\n            const [key, val] = eqIdx == -1 ?\n                [codec.decodeKey(param), ''] :\n                [codec.decodeKey(param.slice(0, eqIdx)), codec.decodeValue(param.slice(eqIdx + 1))];\n            const list = map.get(key) || [];\n            list.push(val);\n            map.set(key, list);\n        });\n    }\n    return map;\n}\n/**\n * Encode input string with standard encodeURIComponent and then un-encode specific characters.\n */\nconst STANDARD_ENCODING_REGEX = /%(\\d[a-f0-9])/gi;\nconst STANDARD_ENCODING_REPLACEMENTS = {\n    '40': '@',\n    '3A': ':',\n    '24': '$',\n    '2C': ',',\n    '3B': ';',\n    '3D': '=',\n    '3F': '?',\n    '2F': '/',\n};\nfunction standardEncoding(v) {\n    return encodeURIComponent(v).replace(STANDARD_ENCODING_REGEX, (s, t) => STANDARD_ENCODING_REPLACEMENTS[t] ?? s);\n}\nfunction valueToString(value) {\n    return `${value}`;\n}\n/**\n * An HTTP request/response body that represents serialized parameters,\n * per the MIME type `application/x-www-form-urlencoded`.\n *\n * This class is immutable; all mutation operations return a new instance.\n *\n * @publicApi\n */\nclass HttpParams {\n    constructor(options = {}) {\n        this.updates = null;\n        this.cloneFrom = null;\n        this.encoder = options.encoder || new HttpUrlEncodingCodec();\n        if (!!options.fromString) {\n            if (!!options.fromObject) {\n                throw new Error(`Cannot specify both fromString and fromObject.`);\n            }\n            this.map = paramParser(options.fromString, this.encoder);\n        }\n        else if (!!options.fromObject) {\n            this.map = new Map();\n            Object.keys(options.fromObject).forEach(key => {\n                const value = options.fromObject[key];\n                // convert the values to strings\n                const values = Array.isArray(value) ? value.map(valueToString) : [valueToString(value)];\n                this.map.set(key, values);\n            });\n        }\n        else {\n            this.map = null;\n        }\n    }\n    /**\n     * Reports whether the body includes one or more values for a given parameter.\n     * @param param The parameter name.\n     * @returns True if the parameter has one or more values,\n     * false if it has no value or is not present.\n     */\n    has(param) {\n        this.init();\n        return this.map.has(param);\n    }\n    /**\n     * Retrieves the first value for a parameter.\n     * @param param The parameter name.\n     * @returns The first value of the given parameter,\n     * or `null` if the parameter is not present.\n     */\n    get(param) {\n        this.init();\n        const res = this.map.get(param);\n        return !!res ? res[0] : null;\n    }\n    /**\n     * Retrieves all values for a  parameter.\n     * @param param The parameter name.\n     * @returns All values in a string array,\n     * or `null` if the parameter not present.\n     */\n    getAll(param) {\n        this.init();\n        return this.map.get(param) || null;\n    }\n    /**\n     * Retrieves all the parameters for this body.\n     * @returns The parameter names in a string array.\n     */\n    keys() {\n        this.init();\n        return Array.from(this.map.keys());\n    }\n    /**\n     * Appends a new value to existing values for a parameter.\n     * @param param The parameter name.\n     * @param value The new value to add.\n     * @return A new body with the appended value.\n     */\n    append(param, value) {\n        return this.clone({ param, value, op: 'a' });\n    }\n    /**\n     * Constructs a new body with appended values for the given parameter name.\n     * @param params parameters and values\n     * @return A new body with the new value.\n     */\n    appendAll(params) {\n        const updates = [];\n        Object.keys(params).forEach(param => {\n            const value = params[param];\n            if (Array.isArray(value)) {\n                value.forEach(_value => {\n                    updates.push({ param, value: _value, op: 'a' });\n                });\n            }\n            else {\n                updates.push({ param, value: value, op: 'a' });\n            }\n        });\n        return this.clone(updates);\n    }\n    /**\n     * Replaces the value for a parameter.\n     * @param param The parameter name.\n     * @param value The new value.\n     * @return A new body with the new value.\n     */\n    set(param, value) {\n        return this.clone({ param, value, op: 's' });\n    }\n    /**\n     * Removes a given value or all values from a parameter.\n     * @param param The parameter name.\n     * @param value The value to remove, if provided.\n     * @return A new body with the given value removed, or with all values\n     * removed if no value is specified.\n     */\n    delete(param, value) {\n        return this.clone({ param, value, op: 'd' });\n    }\n    /**\n     * Serializes the body to an encoded string, where key-value pairs (separated by `=`) are\n     * separated by `&`s.\n     */\n    toString() {\n        this.init();\n        return this.keys()\n            .map(key => {\n            const eKey = this.encoder.encodeKey(key);\n            // `a: ['1']` produces `'a=1'`\n            // `b: []` produces `''`\n            // `c: ['1', '2']` produces `'c=1&c=2'`\n            return this.map.get(key).map(value => eKey + '=' + this.encoder.encodeValue(value))\n                .join('&');\n        })\n            // filter out empty values because `b: []` produces `''`\n            // which results in `a=1&&c=1&c=2` instead of `a=1&c=1&c=2` if we don't\n            .filter(param => param !== '')\n            .join('&');\n    }\n    clone(update) {\n        const clone = new HttpParams({ encoder: this.encoder });\n        clone.cloneFrom = this.cloneFrom || this;\n        clone.updates = (this.updates || []).concat(update);\n        return clone;\n    }\n    init() {\n        if (this.map === null) {\n            this.map = new Map();\n        }\n        if (this.cloneFrom !== null) {\n            this.cloneFrom.init();\n            this.cloneFrom.keys().forEach(key => this.map.set(key, this.cloneFrom.map.get(key)));\n            this.updates.forEach(update => {\n                switch (update.op) {\n                    case 'a':\n                    case 's':\n                        const base = (update.op === 'a' ? this.map.get(update.param) : undefined) || [];\n                        base.push(valueToString(update.value));\n                        this.map.set(update.param, base);\n                        break;\n                    case 'd':\n                        if (update.value !== undefined) {\n                            let base = this.map.get(update.param) || [];\n                            const idx = base.indexOf(valueToString(update.value));\n                            if (idx !== -1) {\n                                base.splice(idx, 1);\n                            }\n                            if (base.length > 0) {\n                                this.map.set(update.param, base);\n                            }\n                            else {\n                                this.map.delete(update.param);\n                            }\n                        }\n                        else {\n                            this.map.delete(update.param);\n                            break;\n                        }\n                }\n            });\n            this.cloneFrom = this.updates = null;\n        }\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * A token used to manipulate and access values stored in `HttpContext`.\n *\n * @publicApi\n */\nclass HttpContextToken {\n    constructor(defaultValue) {\n        this.defaultValue = defaultValue;\n    }\n}\n/**\n * Http context stores arbitrary user defined values and ensures type safety without\n * actually knowing the types. It is backed by a `Map` and guarantees that keys do not clash.\n *\n * This context is mutable and is shared between cloned requests unless explicitly specified.\n *\n * @usageNotes\n *\n * ### Usage Example\n *\n * ```typescript\n * // inside cache.interceptors.ts\n * export const IS_CACHE_ENABLED = new HttpContextToken<boolean>(() => false);\n *\n * export class CacheInterceptor implements HttpInterceptor {\n *\n *   intercept(req: HttpRequest<any>, delegate: HttpHandler): Observable<HttpEvent<any>> {\n *     if (req.context.get(IS_CACHE_ENABLED) === true) {\n *       return ...;\n *     }\n *     return delegate.handle(req);\n *   }\n * }\n *\n * // inside a service\n *\n * this.httpClient.get('/api/weather', {\n *   context: new HttpContext().set(IS_CACHE_ENABLED, true)\n * }).subscribe(...);\n * ```\n *\n * @publicApi\n */\nclass HttpContext {\n    constructor() {\n        this.map = new Map();\n    }\n    /**\n     * Store a value in the context. If a value is already present it will be overwritten.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     * @param value The value to store.\n     *\n     * @returns A reference to itself for easy chaining.\n     */\n    set(token, value) {\n        this.map.set(token, value);\n        return this;\n    }\n    /**\n     * Retrieve the value associated with the given token.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     *\n     * @returns The stored value or default if one is defined.\n     */\n    get(token) {\n        if (!this.map.has(token)) {\n            this.map.set(token, token.defaultValue());\n        }\n        return this.map.get(token);\n    }\n    /**\n     * Delete the value associated with the given token.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     *\n     * @returns A reference to itself for easy chaining.\n     */\n    delete(token) {\n        this.map.delete(token);\n        return this;\n    }\n    /**\n     * Checks for existence of a given token.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     *\n     * @returns True if the token exists, false otherwise.\n     */\n    has(token) {\n        return this.map.has(token);\n    }\n    /**\n     * @returns a list of tokens currently stored in the context.\n     */\n    keys() {\n        return this.map.keys();\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Determine whether the given HTTP method may include a body.\n */\nfunction mightHaveBody(method) {\n    switch (method) {\n        case 'DELETE':\n        case 'GET':\n        case 'HEAD':\n        case 'OPTIONS':\n        case 'JSONP':\n            return false;\n        default:\n            return true;\n    }\n}\n/**\n * Safely assert whether the given value is an ArrayBuffer.\n *\n * In some execution environments ArrayBuffer is not defined.\n */\nfunction isArrayBuffer(value) {\n    return typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer;\n}\n/**\n * Safely assert whether the given value is a Blob.\n *\n * In some execution environments Blob is not defined.\n */\nfunction isBlob(value) {\n    return typeof Blob !== 'undefined' && value instanceof Blob;\n}\n/**\n * Safely assert whether the given value is a FormData instance.\n *\n * In some execution environments FormData is not defined.\n */\nfunction isFormData(value) {\n    return typeof FormData !== 'undefined' && value instanceof FormData;\n}\n/**\n * Safely assert whether the given value is a URLSearchParams instance.\n *\n * In some execution environments URLSearchParams is not defined.\n */\nfunction isUrlSearchParams(value) {\n    return typeof URLSearchParams !== 'undefined' && value instanceof URLSearchParams;\n}\n/**\n * An outgoing HTTP request with an optional typed body.\n *\n * `HttpRequest` represents an outgoing request, including URL, method,\n * headers, body, and other request configuration options. Instances should be\n * assumed to be immutable. To modify a `HttpRequest`, the `clone`\n * method should be used.\n *\n * @publicApi\n */\nclass HttpRequest {\n    constructor(method, url, third, fourth) {\n        this.url = url;\n        /**\n         * The request body, or `null` if one isn't set.\n         *\n         * Bodies are not enforced to be immutable, as they can include a reference to any\n         * user-defined data type. However, interceptors should take care to preserve\n         * idempotence by treating them as such.\n         */\n        this.body = null;\n        /**\n         * Whether this request should be made in a way that exposes progress events.\n         *\n         * Progress events are expensive (change detection runs on each event) and so\n         * they should only be requested if the consumer intends to monitor them.\n         */\n        this.reportProgress = false;\n        /**\n         * Whether this request should be sent with outgoing credentials (cookies).\n         */\n        this.withCredentials = false;\n        /**\n         * The expected response type of the server.\n         *\n         * This is used to parse the response appropriately before returning it to\n         * the requestee.\n         */\n        this.responseType = 'json';\n        this.method = method.toUpperCase();\n        // Next, need to figure out which argument holds the HttpRequestInit\n        // options, if any.\n        let options;\n        // Check whether a body argument is expected. The only valid way to omit\n        // the body argument is to use a known no-body method like GET.\n        if (mightHaveBody(this.method) || !!fourth) {\n            // Body is the third argument, options are the fourth.\n            this.body = (third !== undefined) ? third : null;\n            options = fourth;\n        }\n        else {\n            // No body required, options are the third argument. The body stays null.\n            options = third;\n        }\n        // If options have been passed, interpret them.\n        if (options) {\n            // Normalize reportProgress and withCredentials.\n            this.reportProgress = !!options.reportProgress;\n            this.withCredentials = !!options.withCredentials;\n            // Override default response type of 'json' if one is provided.\n            if (!!options.responseType) {\n                this.responseType = options.responseType;\n            }\n            // Override headers if they're provided.\n            if (!!options.headers) {\n                this.headers = options.headers;\n            }\n            if (!!options.context) {\n                this.context = options.context;\n            }\n            if (!!options.params) {\n                this.params = options.params;\n            }\n        }\n        // If no headers have been passed in, construct a new HttpHeaders instance.\n        if (!this.headers) {\n            this.headers = new HttpHeaders();\n        }\n        // If no context have been passed in, construct a new HttpContext instance.\n        if (!this.context) {\n            this.context = new HttpContext();\n        }\n        // If no parameters have been passed in, construct a new HttpUrlEncodedParams instance.\n        if (!this.params) {\n            this.params = new HttpParams();\n            this.urlWithParams = url;\n        }\n        else {\n            // Encode the parameters to a string in preparation for inclusion in the URL.\n            const params = this.params.toString();\n            if (params.length === 0) {\n                // No parameters, the visible URL is just the URL given at creation time.\n                this.urlWithParams = url;\n            }\n            else {\n                // Does the URL already have query parameters? Look for '?'.\n                const qIdx = url.indexOf('?');\n                // There are 3 cases to handle:\n                // 1) No existing parameters -> append '?' followed by params.\n                // 2) '?' exists and is followed by existing query string ->\n                //    append '&' followed by params.\n                // 3) '?' exists at the end of the url -> append params directly.\n                // This basically amounts to determining the character, if any, with\n                // which to join the URL and parameters.\n                const sep = qIdx === -1 ? '?' : (qIdx < url.length - 1 ? '&' : '');\n                this.urlWithParams = url + sep + params;\n            }\n        }\n    }\n    /**\n     * Transform the free-form body into a serialized format suitable for\n     * transmission to the server.\n     */\n    serializeBody() {\n        // If no body is present, no need to serialize it.\n        if (this.body === null) {\n            return null;\n        }\n        // Check whether the body is already in a serialized form. If so,\n        // it can just be returned directly.\n        if (isArrayBuffer(this.body) || isBlob(this.body) || isFormData(this.body) ||\n            isUrlSearchParams(this.body) || typeof this.body === 'string') {\n            return this.body;\n        }\n        // Check whether the body is an instance of HttpUrlEncodedParams.\n        if (this.body instanceof HttpParams) {\n            return this.body.toString();\n        }\n        // Check whether the body is an object or array, and serialize with JSON if so.\n        if (typeof this.body === 'object' || typeof this.body === 'boolean' ||\n            Array.isArray(this.body)) {\n            return JSON.stringify(this.body);\n        }\n        // Fall back on toString() for everything else.\n        return this.body.toString();\n    }\n    /**\n     * Examine the body and attempt to infer an appropriate MIME type\n     * for it.\n     *\n     * If no such type can be inferred, this method will return `null`.\n     */\n    detectContentTypeHeader() {\n        // An empty body has no content type.\n        if (this.body === null) {\n            return null;\n        }\n        // FormData bodies rely on the browser's content type assignment.\n        if (isFormData(this.body)) {\n            return null;\n        }\n        // Blobs usually have their own content type. If it doesn't, then\n        // no type can be inferred.\n        if (isBlob(this.body)) {\n            return this.body.type || null;\n        }\n        // Array buffers have unknown contents and thus no type can be inferred.\n        if (isArrayBuffer(this.body)) {\n            return null;\n        }\n        // Technically, strings could be a form of JSON data, but it's safe enough\n        // to assume they're plain strings.\n        if (typeof this.body === 'string') {\n            return 'text/plain';\n        }\n        // `HttpUrlEncodedParams` has its own content-type.\n        if (this.body instanceof HttpParams) {\n            return 'application/x-www-form-urlencoded;charset=UTF-8';\n        }\n        // Arrays, objects, boolean and numbers will be encoded as JSON.\n        if (typeof this.body === 'object' || typeof this.body === 'number' ||\n            typeof this.body === 'boolean') {\n            return 'application/json';\n        }\n        // No type could be inferred.\n        return null;\n    }\n    clone(update = {}) {\n        // For method, url, and responseType, take the current value unless\n        // it is overridden in the update hash.\n        const method = update.method || this.method;\n        const url = update.url || this.url;\n        const responseType = update.responseType || this.responseType;\n        // The body is somewhat special - a `null` value in update.body means\n        // whatever current body is present is being overridden with an empty\n        // body, whereas an `undefined` value in update.body implies no\n        // override.\n        const body = (update.body !== undefined) ? update.body : this.body;\n        // Carefully handle the boolean options to differentiate between\n        // `false` and `undefined` in the update args.\n        const withCredentials = (update.withCredentials !== undefined) ? update.withCredentials : this.withCredentials;\n        const reportProgress = (update.reportProgress !== undefined) ? update.reportProgress : this.reportProgress;\n        // Headers and params may be appended to if `setHeaders` or\n        // `setParams` are used.\n        let headers = update.headers || this.headers;\n        let params = update.params || this.params;\n        // Pass on context if needed\n        const context = update.context ?? this.context;\n        // Check whether the caller has asked to add headers.\n        if (update.setHeaders !== undefined) {\n            // Set every requested header.\n            headers =\n                Object.keys(update.setHeaders)\n                    .reduce((headers, name) => headers.set(name, update.setHeaders[name]), headers);\n        }\n        // Check whether the caller has asked to set params.\n        if (update.setParams) {\n            // Set every requested param.\n            params = Object.keys(update.setParams)\n                .reduce((params, param) => params.set(param, update.setParams[param]), params);\n        }\n        // Finally, construct the new HttpRequest using the pieces from above.\n        return new HttpRequest(method, url, body, {\n            params,\n            headers,\n            context,\n            reportProgress,\n            responseType,\n            withCredentials,\n        });\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Type enumeration for the different kinds of `HttpEvent`.\n *\n * @publicApi\n */\nvar HttpEventType;\n(function (HttpEventType) {\n    /**\n     * The request was sent out over the wire.\n     */\n    HttpEventType[HttpEventType[\"Sent\"] = 0] = \"Sent\";\n    /**\n     * An upload progress event was received.\n     */\n    HttpEventType[HttpEventType[\"UploadProgress\"] = 1] = \"UploadProgress\";\n    /**\n     * The response status code and headers were received.\n     */\n    HttpEventType[HttpEventType[\"ResponseHeader\"] = 2] = \"ResponseHeader\";\n    /**\n     * A download progress event was received.\n     */\n    HttpEventType[HttpEventType[\"DownloadProgress\"] = 3] = \"DownloadProgress\";\n    /**\n     * The full response including the body was received.\n     */\n    HttpEventType[HttpEventType[\"Response\"] = 4] = \"Response\";\n    /**\n     * A custom event from an interceptor or a backend.\n     */\n    HttpEventType[HttpEventType[\"User\"] = 5] = \"User\";\n})(HttpEventType || (HttpEventType = {}));\n/**\n * Base class for both `HttpResponse` and `HttpHeaderResponse`.\n *\n * @publicApi\n */\nclass HttpResponseBase {\n    /**\n     * Super-constructor for all responses.\n     *\n     * The single parameter accepted is an initialization hash. Any properties\n     * of the response passed there will override the default values.\n     */\n    constructor(init, defaultStatus = 200 /* HttpStatusCode.Ok */, defaultStatusText = 'OK') {\n        // If the hash has values passed, use them to initialize the response.\n        // Otherwise use the default values.\n        this.headers = init.headers || new HttpHeaders();\n        this.status = init.status !== undefined ? init.status : defaultStatus;\n        this.statusText = init.statusText || defaultStatusText;\n        this.url = init.url || null;\n        // Cache the ok value to avoid defining a getter.\n        this.ok = this.status >= 200 && this.status < 300;\n    }\n}\n/**\n * A partial HTTP response which only includes the status and header data,\n * but no response body.\n *\n * `HttpHeaderResponse` is a `HttpEvent` available on the response\n * event stream, only when progress events are requested.\n *\n * @publicApi\n */\nclass HttpHeaderResponse extends HttpResponseBase {\n    /**\n     * Create a new `HttpHeaderResponse` with the given parameters.\n     */\n    constructor(init = {}) {\n        super(init);\n        this.type = HttpEventType.ResponseHeader;\n    }\n    /**\n     * Copy this `HttpHeaderResponse`, overriding its contents with the\n     * given parameter hash.\n     */\n    clone(update = {}) {\n        // Perform a straightforward initialization of the new HttpHeaderResponse,\n        // overriding the current parameters with new ones if given.\n        return new HttpHeaderResponse({\n            headers: update.headers || this.headers,\n            status: update.status !== undefined ? update.status : this.status,\n            statusText: update.statusText || this.statusText,\n            url: update.url || this.url || undefined,\n        });\n    }\n}\n/**\n * A full HTTP response, including a typed response body (which may be `null`\n * if one was not returned).\n *\n * `HttpResponse` is a `HttpEvent` available on the response event\n * stream.\n *\n * @publicApi\n */\nclass HttpResponse extends HttpResponseBase {\n    /**\n     * Construct a new `HttpResponse`.\n     */\n    constructor(init = {}) {\n        super(init);\n        this.type = HttpEventType.Response;\n        this.body = init.body !== undefined ? init.body : null;\n    }\n    clone(update = {}) {\n        return new HttpResponse({\n            body: (update.body !== undefined) ? update.body : this.body,\n            headers: update.headers || this.headers,\n            status: (update.status !== undefined) ? update.status : this.status,\n            statusText: update.statusText || this.statusText,\n            url: update.url || this.url || undefined,\n        });\n    }\n}\n/**\n * A response that represents an error or failure, either from a\n * non-successful HTTP status, an error while executing the request,\n * or some other failure which occurred during the parsing of the response.\n *\n * Any error returned on the `Observable` response stream will be\n * wrapped in an `HttpErrorResponse` to provide additional context about\n * the state of the HTTP layer when the error occurred. The error property\n * will contain either a wrapped Error object or the error response returned\n * from the server.\n *\n * @publicApi\n */\nclass HttpErrorResponse extends HttpResponseBase {\n    constructor(init) {\n        // Initialize with a default status of 0 / Unknown Error.\n        super(init, 0, 'Unknown Error');\n        this.name = 'HttpErrorResponse';\n        /**\n         * Errors are never okay, even when the status code is in the 2xx success range.\n         */\n        this.ok = false;\n        // If the response was successful, then this was a parse error. Otherwise, it was\n        // a protocol-level failure of some sort. Either the request failed in transit\n        // or the server returned an unsuccessful status code.\n        if (this.status >= 200 && this.status < 300) {\n            this.message = `Http failure during parsing for ${init.url || '(unknown url)'}`;\n        }\n        else {\n            this.message = `Http failure response for ${init.url || '(unknown url)'}: ${init.status} ${init.statusText}`;\n        }\n        this.error = init.error || null;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Constructs an instance of `HttpRequestOptions<T>` from a source `HttpMethodOptions` and\n * the given `body`. This function clones the object and adds the body.\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n */\nfunction addBody(options, body) {\n    return {\n        body,\n        headers: options.headers,\n        context: options.context,\n        observe: options.observe,\n        params: options.params,\n        reportProgress: options.reportProgress,\n        responseType: options.responseType,\n        withCredentials: options.withCredentials,\n    };\n}\n/**\n * Performs HTTP requests.\n * This service is available as an injectable class, with methods to perform HTTP requests.\n * Each request method has multiple signatures, and the return type varies based on\n * the signature that is called (mainly the values of `observe` and `responseType`).\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n\n *\n * @usageNotes\n * Sample HTTP requests for the [Tour of Heroes](/tutorial/toh-pt0) application.\n *\n * ### HTTP Request Example\n *\n * ```\n *  // GET heroes whose name contains search term\n * searchHeroes(term: string): observable<Hero[]>{\n *\n *  const params = new HttpParams({fromString: 'name=term'});\n *    return this.httpClient.request('GET', this.heroesUrl, {responseType:'json', params});\n * }\n * ```\n *\n * Alternatively, the parameter string can be used without invoking HttpParams\n * by directly joining to the URL.\n * ```\n * this.httpClient.request('GET', this.heroesUrl + '?' + 'name=term', {responseType:'json'});\n * ```\n *\n *\n * ### JSONP Example\n * ```\n * requestJsonp(url, callback = 'callback') {\n *  return this.httpClient.jsonp(this.heroesURL, callback);\n * }\n * ```\n *\n * ### PATCH Example\n * ```\n * // PATCH one of the heroes' name\n * patchHero (id: number, heroName: string): Observable<{}> {\n * const url = `${this.heroesUrl}/${id}`;   // PATCH api/heroes/42\n *  return this.httpClient.patch(url, {name: heroName}, httpOptions)\n *    .pipe(catchError(this.handleError('patchHero')));\n * }\n * ```\n *\n * @see [HTTP Guide](guide/http)\n * @see [HTTP Request](api/common/http/HttpRequest)\n *\n * @publicApi\n */\nclass HttpClient {\n    constructor(handler) {\n        this.handler = handler;\n    }\n    /**\n     * Constructs an observable for a generic HTTP request that, when subscribed,\n     * fires the request through the chain of registered interceptors and on to the\n     * server.\n     *\n     * You can pass an `HttpRequest` directly as the only parameter. In this case,\n     * the call returns an observable of the raw `HttpEvent` stream.\n     *\n     * Alternatively you can pass an HTTP method as the first parameter,\n     * a URL string as the second, and an options hash containing the request body as the third.\n     * See `addBody()`. In this case, the specified `responseType` and `observe` options determine the\n     * type of returned observable.\n     *   * The `responseType` value determines how a successful response body is parsed.\n     *   * If `responseType` is the default `json`, you can pass a type interface for the resulting\n     * object as a type parameter to the call.\n     *\n     * The `observe` value determines the return type, according to what you are interested in\n     * observing.\n     *   * An `observe` value of events returns an observable of the raw `HttpEvent` stream, including\n     * progress events by default.\n     *   * An `observe` value of response returns an observable of `HttpResponse<T>`,\n     * where the `T` parameter depends on the `responseType` and any optionally provided type\n     * parameter.\n     *   * An `observe` value of body returns an observable of `<T>` with the same `T` body type.\n     *\n     */\n    request(first, url, options = {}) {\n        let req;\n        // First, check whether the primary argument is an instance of `HttpRequest`.\n        if (first instanceof HttpRequest) {\n            // It is. The other arguments must be undefined (per the signatures) and can be\n            // ignored.\n            req = first;\n        }\n        else {\n            // It's a string, so it represents a URL. Construct a request based on it,\n            // and incorporate the remaining arguments (assuming `GET` unless a method is\n            // provided.\n            // Figure out the headers.\n            let headers = undefined;\n            if (options.headers instanceof HttpHeaders) {\n                headers = options.headers;\n            }\n            else {\n                headers = new HttpHeaders(options.headers);\n            }\n            // Sort out parameters.\n            let params = undefined;\n            if (!!options.params) {\n                if (options.params instanceof HttpParams) {\n                    params = options.params;\n                }\n                else {\n                    params = new HttpParams({ fromObject: options.params });\n                }\n            }\n            // Construct the request.\n            req = new HttpRequest(first, url, (options.body !== undefined ? options.body : null), {\n                headers,\n                context: options.context,\n                params,\n                reportProgress: options.reportProgress,\n                // By default, JSON is assumed to be returned for all calls.\n                responseType: options.responseType || 'json',\n                withCredentials: options.withCredentials,\n            });\n        }\n        // Start with an Observable.of() the initial request, and run the handler (which\n        // includes all interceptors) inside a concatMap(). This way, the handler runs\n        // inside an Observable chain, which causes interceptors to be re-run on every\n        // subscription (this also makes retries re-run the handler, including interceptors).\n        const events$ = of(req).pipe(concatMap((req) => this.handler.handle(req)));\n        // If coming via the API signature which accepts a previously constructed HttpRequest,\n        // the only option is to get the event stream. Otherwise, return the event stream if\n        // that is what was requested.\n        if (first instanceof HttpRequest || options.observe === 'events') {\n            return events$;\n        }\n        // The requested stream contains either the full response or the body. In either\n        // case, the first step is to filter the event stream to extract a stream of\n        // responses(s).\n        const res$ = events$.pipe(filter((event) => event instanceof HttpResponse));\n        // Decide which stream to return.\n        switch (options.observe || 'body') {\n            case 'body':\n                // The requested stream is the body. Map the response stream to the response\n                // body. This could be done more simply, but a misbehaving interceptor might\n                // transform the response body into a different format and ignore the requested\n                // responseType. Guard against this by validating that the response is of the\n                // requested type.\n                switch (req.responseType) {\n                    case 'arraybuffer':\n                        return res$.pipe(map((res) => {\n                            // Validate that the body is an ArrayBuffer.\n                            if (res.body !== null && !(res.body instanceof ArrayBuffer)) {\n                                throw new Error('Response is not an ArrayBuffer.');\n                            }\n                            return res.body;\n                        }));\n                    case 'blob':\n                        return res$.pipe(map((res) => {\n                            // Validate that the body is a Blob.\n                            if (res.body !== null && !(res.body instanceof Blob)) {\n                                throw new Error('Response is not a Blob.');\n                            }\n                            return res.body;\n                        }));\n                    case 'text':\n                        return res$.pipe(map((res) => {\n                            // Validate that the body is a string.\n                            if (res.body !== null && typeof res.body !== 'string') {\n                                throw new Error('Response is not a string.');\n                            }\n                            return res.body;\n                        }));\n                    case 'json':\n                    default:\n                        // No validation needed for JSON responses, as they can be of any type.\n                        return res$.pipe(map((res) => res.body));\n                }\n            case 'response':\n                // The response stream was requested directly, so return it.\n                return res$;\n            default:\n                // Guard against new future observe types being added.\n                throw new Error(`Unreachable: unhandled observe type ${options.observe}}`);\n        }\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `DELETE` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     *\n     * @param url     The endpoint URL.\n     * @param options The HTTP options to send with the request.\n     *\n     */\n    delete(url, options = {}) {\n        return this.request('DELETE', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `GET` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     */\n    get(url, options = {}) {\n        return this.request('GET', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `HEAD` request to execute on the server. The `HEAD` method returns\n     * meta information about the resource without transferring the\n     * resource itself. See the individual overloads for\n     * details on the return type.\n     */\n    head(url, options = {}) {\n        return this.request('HEAD', url, options);\n    }\n    /**\n     * Constructs an `Observable` that, when subscribed, causes a request with the special method\n     * `JSONP` to be dispatched via the interceptor pipeline.\n     * The [JSONP pattern](https://en.wikipedia.org/wiki/JSONP) works around limitations of certain\n     * API endpoints that don't support newer,\n     * and preferable [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) protocol.\n     * JSONP treats the endpoint API as a JavaScript file and tricks the browser to process the\n     * requests even if the API endpoint is not located on the same domain (origin) as the client-side\n     * application making the request.\n     * The endpoint API must support JSONP callback for JSONP requests to work.\n     * The resource API returns the JSON response wrapped in a callback function.\n     * You can pass the callback function name as one of the query parameters.\n     * Note that JSONP requests can only be used with `GET` requests.\n     *\n     * @param url The resource URL.\n     * @param callbackParam The callback function name.\n     *\n     */\n    jsonp(url, callbackParam) {\n        return this.request('JSONP', url, {\n            params: new HttpParams().append(callbackParam, 'JSONP_CALLBACK'),\n            observe: 'body',\n            responseType: 'json',\n        });\n    }\n    /**\n     * Constructs an `Observable` that, when subscribed, causes the configured\n     * `OPTIONS` request to execute on the server. This method allows the client\n     * to determine the supported HTTP methods and other capabilities of an endpoint,\n     * without implying a resource action. See the individual overloads for\n     * details on the return type.\n     */\n    options(url, options = {}) {\n        return this.request('OPTIONS', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `PATCH` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     */\n    patch(url, body, options = {}) {\n        return this.request('PATCH', url, addBody(options, body));\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `POST` request to execute on the server. The server responds with the location of\n     * the replaced resource. See the individual overloads for\n     * details on the return type.\n     */\n    post(url, body, options = {}) {\n        return this.request('POST', url, addBody(options, body));\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `PUT` request to execute on the server. The `PUT` method replaces an existing resource\n     * with a new set of values.\n     * See the individual overloads for details on the return type.\n     */\n    put(url, body, options = {}) {\n        return this.request('PUT', url, addBody(options, body));\n    }\n}\nHttpClient.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpClient, deps: [{ token: HttpHandler }], target: i0.ɵɵFactoryTarget.Injectable });\nHttpClient.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpClient });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpClient, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: HttpHandler }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * `HttpHandler` which applies an `HttpInterceptor` to an `HttpRequest`.\n *\n *\n */\nclass HttpInterceptorHandler {\n    constructor(next, interceptor) {\n        this.next = next;\n        this.interceptor = interceptor;\n    }\n    handle(req) {\n        return this.interceptor.intercept(req, this.next);\n    }\n}\n/**\n * A multi-provider token that represents the array of registered\n * `HttpInterceptor` objects.\n *\n * @publicApi\n */\nconst HTTP_INTERCEPTORS = new InjectionToken('HTTP_INTERCEPTORS');\nclass NoopInterceptor {\n    intercept(req, next) {\n        return next.handle(req);\n    }\n}\nNoopInterceptor.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: NoopInterceptor, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nNoopInterceptor.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: NoopInterceptor });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: NoopInterceptor, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Every request made through JSONP needs a callback name that's unique across the\n// whole page. Each request is assigned an id and the callback name is constructed\n// from that. The next id to be assigned is tracked in a global variable here that\n// is shared among all applications on the page.\nlet nextRequestId = 0;\n/**\n * When a pending <script> is unsubscribed we'll move it to this document, so it won't be\n * executed.\n */\nlet foreignDocument;\n// Error text given when a JSONP script is injected, but doesn't invoke the callback\n// passed in its URL.\nconst JSONP_ERR_NO_CALLBACK = 'JSONP injected script did not invoke callback.';\n// Error text given when a request is passed to the JsonpClientBackend that doesn't\n// have a request method JSONP.\nconst JSONP_ERR_WRONG_METHOD = 'JSONP requests must use JSONP request method.';\nconst JSONP_ERR_WRONG_RESPONSE_TYPE = 'JSONP requests must use Json response type.';\n// Error text given when a request is passed to the JsonpClientBackend that has\n// headers set\nconst JSONP_ERR_HEADERS_NOT_SUPPORTED = 'JSONP requests do not support headers.';\n/**\n * DI token/abstract type representing a map of JSONP callbacks.\n *\n * In the browser, this should always be the `window` object.\n *\n *\n */\nclass JsonpCallbackContext {\n}\n/**\n * Processes an `HttpRequest` with the JSONP method,\n * by performing JSONP style requests.\n * @see `HttpHandler`\n * @see `HttpXhrBackend`\n *\n * @publicApi\n */\nclass JsonpClientBackend {\n    constructor(callbackMap, document) {\n        this.callbackMap = callbackMap;\n        this.document = document;\n        /**\n         * A resolved promise that can be used to schedule microtasks in the event handlers.\n         */\n        this.resolvedPromise = Promise.resolve();\n    }\n    /**\n     * Get the name of the next callback method, by incrementing the global `nextRequestId`.\n     */\n    nextCallback() {\n        return `ng_jsonp_callback_${nextRequestId++}`;\n    }\n    /**\n     * Processes a JSONP request and returns an event stream of the results.\n     * @param req The request object.\n     * @returns An observable of the response events.\n     *\n     */\n    handle(req) {\n        // Firstly, check both the method and response type. If either doesn't match\n        // then the request was improperly routed here and cannot be handled.\n        if (req.method !== 'JSONP') {\n            throw new Error(JSONP_ERR_WRONG_METHOD);\n        }\n        else if (req.responseType !== 'json') {\n            throw new Error(JSONP_ERR_WRONG_RESPONSE_TYPE);\n        }\n        // Check the request headers. JSONP doesn't support headers and\n        // cannot set any that were supplied.\n        if (req.headers.keys().length > 0) {\n            throw new Error(JSONP_ERR_HEADERS_NOT_SUPPORTED);\n        }\n        // Everything else happens inside the Observable boundary.\n        return new Observable((observer) => {\n            // The first step to make a request is to generate the callback name, and replace the\n            // callback placeholder in the URL with the name. Care has to be taken here to ensure\n            // a trailing &, if matched, gets inserted back into the URL in the correct place.\n            const callback = this.nextCallback();\n            const url = req.urlWithParams.replace(/=JSONP_CALLBACK(&|$)/, `=${callback}$1`);\n            // Construct the <script> tag and point it at the URL.\n            const node = this.document.createElement('script');\n            node.src = url;\n            // A JSONP request requires waiting for multiple callbacks. These variables\n            // are closed over and track state across those callbacks.\n            // The response object, if one has been received, or null otherwise.\n            let body = null;\n            // Whether the response callback has been called.\n            let finished = false;\n            // Set the response callback in this.callbackMap (which will be the window\n            // object in the browser. The script being loaded via the <script> tag will\n            // eventually call this callback.\n            this.callbackMap[callback] = (data) => {\n                // Data has been received from the JSONP script. Firstly, delete this callback.\n                delete this.callbackMap[callback];\n                // Set state to indicate data was received.\n                body = data;\n                finished = true;\n            };\n            // cleanup() is a utility closure that removes the <script> from the page and\n            // the response callback from the window. This logic is used in both the\n            // success, error, and cancellation paths, so it's extracted out for convenience.\n            const cleanup = () => {\n                // Remove the <script> tag if it's still on the page.\n                if (node.parentNode) {\n                    node.parentNode.removeChild(node);\n                }\n                // Remove the response callback from the callbackMap (window object in the\n                // browser).\n                delete this.callbackMap[callback];\n            };\n            // onLoad() is the success callback which runs after the response callback\n            // if the JSONP script loads successfully. The event itself is unimportant.\n            // If something went wrong, onLoad() may run without the response callback\n            // having been invoked.\n            const onLoad = (event) => {\n                // We wrap it in an extra Promise, to ensure the microtask\n                // is scheduled after the loaded endpoint has executed any potential microtask itself,\n                // which is not guaranteed in Internet Explorer and EdgeHTML. See issue #39496\n                this.resolvedPromise.then(() => {\n                    // Cleanup the page.\n                    cleanup();\n                    // Check whether the response callback has run.\n                    if (!finished) {\n                        // It hasn't, something went wrong with the request. Return an error via\n                        // the Observable error path. All JSONP errors have status 0.\n                        observer.error(new HttpErrorResponse({\n                            url,\n                            status: 0,\n                            statusText: 'JSONP Error',\n                            error: new Error(JSONP_ERR_NO_CALLBACK),\n                        }));\n                        return;\n                    }\n                    // Success. body either contains the response body or null if none was\n                    // returned.\n                    observer.next(new HttpResponse({\n                        body,\n                        status: 200 /* HttpStatusCode.Ok */,\n                        statusText: 'OK',\n                        url,\n                    }));\n                    // Complete the stream, the response is over.\n                    observer.complete();\n                });\n            };\n            // onError() is the error callback, which runs if the script returned generates\n            // a Javascript error. It emits the error via the Observable error channel as\n            // a HttpErrorResponse.\n            const onError = (error) => {\n                cleanup();\n                // Wrap the error in a HttpErrorResponse.\n                observer.error(new HttpErrorResponse({\n                    error,\n                    status: 0,\n                    statusText: 'JSONP Error',\n                    url,\n                }));\n            };\n            // Subscribe to both the success (load) and error events on the <script> tag,\n            // and add it to the page.\n            node.addEventListener('load', onLoad);\n            node.addEventListener('error', onError);\n            this.document.body.appendChild(node);\n            // The request has now been successfully sent.\n            observer.next({ type: HttpEventType.Sent });\n            // Cancellation handler.\n            return () => {\n                if (!finished) {\n                    this.removeListeners(node);\n                }\n                // And finally, clean up the page.\n                cleanup();\n            };\n        });\n    }\n    removeListeners(script) {\n        // Issue #34818\n        // Changing <script>'s ownerDocument will prevent it from execution.\n        // https://html.spec.whatwg.org/multipage/scripting.html#execute-the-script-block\n        if (!foreignDocument) {\n            foreignDocument = this.document.implementation.createHTMLDocument();\n        }\n        foreignDocument.adoptNode(script);\n    }\n}\nJsonpClientBackend.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: JsonpClientBackend, deps: [{ token: JsonpCallbackContext }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nJsonpClientBackend.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: JsonpClientBackend });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: JsonpClientBackend, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: JsonpCallbackContext }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n/**\n * Identifies requests with the method JSONP and\n * shifts them to the `JsonpClientBackend`.\n *\n * @see `HttpInterceptor`\n *\n * @publicApi\n */\nclass JsonpInterceptor {\n    constructor(jsonp) {\n        this.jsonp = jsonp;\n    }\n    /**\n     * Identifies and handles a given JSONP request.\n     * @param req The outgoing request object to handle.\n     * @param next The next interceptor in the chain, or the backend\n     * if no interceptors remain in the chain.\n     * @returns An observable of the event stream.\n     */\n    intercept(req, next) {\n        if (req.method === 'JSONP') {\n            return this.jsonp.handle(req);\n        }\n        // Fall through for normal HTTP requests.\n        return next.handle(req);\n    }\n}\nJsonpInterceptor.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: JsonpInterceptor, deps: [{ token: JsonpClientBackend }], target: i0.ɵɵFactoryTarget.Injectable });\nJsonpInterceptor.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: JsonpInterceptor });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: JsonpInterceptor, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: JsonpClientBackend }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\n/**\n * Determine an appropriate URL for the response, by checking either\n * XMLHttpRequest.responseURL or the X-Request-URL header.\n */\nfunction getResponseUrl(xhr) {\n    if ('responseURL' in xhr && xhr.responseURL) {\n        return xhr.responseURL;\n    }\n    if (/^X-Request-URL:/m.test(xhr.getAllResponseHeaders())) {\n        return xhr.getResponseHeader('X-Request-URL');\n    }\n    return null;\n}\n/**\n * Uses `XMLHttpRequest` to send requests to a backend server.\n * @see `HttpHandler`\n * @see `JsonpClientBackend`\n *\n * @publicApi\n */\nclass HttpXhrBackend {\n    constructor(xhrFactory) {\n        this.xhrFactory = xhrFactory;\n    }\n    /**\n     * Processes a request and returns a stream of response events.\n     * @param req The request object.\n     * @returns An observable of the response events.\n     */\n    handle(req) {\n        // Quick check to give a better error message when a user attempts to use\n        // HttpClient.jsonp() without installing the HttpClientJsonpModule\n        if (req.method === 'JSONP') {\n            throw new Error(`Attempted to construct Jsonp request without HttpClientJsonpModule installed.`);\n        }\n        // Everything happens on Observable subscription.\n        return new Observable((observer) => {\n            // Start by setting up the XHR object with request method, URL, and withCredentials flag.\n            const xhr = this.xhrFactory.build();\n            xhr.open(req.method, req.urlWithParams);\n            if (!!req.withCredentials) {\n                xhr.withCredentials = true;\n            }\n            // Add all the requested headers.\n            req.headers.forEach((name, values) => xhr.setRequestHeader(name, values.join(',')));\n            // Add an Accept header if one isn't present already.\n            if (!req.headers.has('Accept')) {\n                xhr.setRequestHeader('Accept', 'application/json, text/plain, */*');\n            }\n            // Auto-detect the Content-Type header if one isn't present already.\n            if (!req.headers.has('Content-Type')) {\n                const detectedType = req.detectContentTypeHeader();\n                // Sometimes Content-Type detection fails.\n                if (detectedType !== null) {\n                    xhr.setRequestHeader('Content-Type', detectedType);\n                }\n            }\n            // Set the responseType if one was requested.\n            if (req.responseType) {\n                const responseType = req.responseType.toLowerCase();\n                // JSON responses need to be processed as text. This is because if the server\n                // returns an XSSI-prefixed JSON response, the browser will fail to parse it,\n                // xhr.response will be null, and xhr.responseText cannot be accessed to\n                // retrieve the prefixed JSON data in order to strip the prefix. Thus, all JSON\n                // is parsed by first requesting text and then applying JSON.parse.\n                xhr.responseType = ((responseType !== 'json') ? responseType : 'text');\n            }\n            // Serialize the request body if one is present. If not, this will be set to null.\n            const reqBody = req.serializeBody();\n            // If progress events are enabled, response headers will be delivered\n            // in two events - the HttpHeaderResponse event and the full HttpResponse\n            // event. However, since response headers don't change in between these\n            // two events, it doesn't make sense to parse them twice. So headerResponse\n            // caches the data extracted from the response whenever it's first parsed,\n            // to ensure parsing isn't duplicated.\n            let headerResponse = null;\n            // partialFromXhr extracts the HttpHeaderResponse from the current XMLHttpRequest\n            // state, and memoizes it into headerResponse.\n            const partialFromXhr = () => {\n                if (headerResponse !== null) {\n                    return headerResponse;\n                }\n                const statusText = xhr.statusText || 'OK';\n                // Parse headers from XMLHttpRequest - this step is lazy.\n                const headers = new HttpHeaders(xhr.getAllResponseHeaders());\n                // Read the response URL from the XMLHttpResponse instance and fall back on the\n                // request URL.\n                const url = getResponseUrl(xhr) || req.url;\n                // Construct the HttpHeaderResponse and memoize it.\n                headerResponse = new HttpHeaderResponse({ headers, status: xhr.status, statusText, url });\n                return headerResponse;\n            };\n            // Next, a few closures are defined for the various events which XMLHttpRequest can\n            // emit. This allows them to be unregistered as event listeners later.\n            // First up is the load event, which represents a response being fully available.\n            const onLoad = () => {\n                // Read response state from the memoized partial data.\n                let { headers, status, statusText, url } = partialFromXhr();\n                // The body will be read out if present.\n                let body = null;\n                if (status !== 204 /* HttpStatusCode.NoContent */) {\n                    // Use XMLHttpRequest.response if set, responseText otherwise.\n                    body = (typeof xhr.response === 'undefined') ? xhr.responseText : xhr.response;\n                }\n                // Normalize another potential bug (this one comes from CORS).\n                if (status === 0) {\n                    status = !!body ? 200 /* HttpStatusCode.Ok */ : 0;\n                }\n                // ok determines whether the response will be transmitted on the event or\n                // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n                // but a successful status code can still result in an error if the user\n                // asked for JSON data and the body cannot be parsed as such.\n                let ok = status >= 200 && status < 300;\n                // Check whether the body needs to be parsed as JSON (in many cases the browser\n                // will have done that already).\n                if (req.responseType === 'json' && typeof body === 'string') {\n                    // Save the original body, before attempting XSSI prefix stripping.\n                    const originalBody = body;\n                    body = body.replace(XSSI_PREFIX, '');\n                    try {\n                        // Attempt the parse. If it fails, a parse error should be delivered to the user.\n                        body = body !== '' ? JSON.parse(body) : null;\n                    }\n                    catch (error) {\n                        // Since the JSON.parse failed, it's reasonable to assume this might not have been a\n                        // JSON response. Restore the original body (including any XSSI prefix) to deliver\n                        // a better error response.\n                        body = originalBody;\n                        // If this was an error request to begin with, leave it as a string, it probably\n                        // just isn't JSON. Otherwise, deliver the parsing error to the user.\n                        if (ok) {\n                            // Even though the response status was 2xx, this is still an error.\n                            ok = false;\n                            // The parse error contains the text of the body that failed to parse.\n                            body = { error, text: body };\n                        }\n                    }\n                }\n                if (ok) {\n                    // A successful response is delivered on the event stream.\n                    observer.next(new HttpResponse({\n                        body,\n                        headers,\n                        status,\n                        statusText,\n                        url: url || undefined,\n                    }));\n                    // The full body has been received and delivered, no further events\n                    // are possible. This request is complete.\n                    observer.complete();\n                }\n                else {\n                    // An unsuccessful request is delivered on the error channel.\n                    observer.error(new HttpErrorResponse({\n                        // The error in this case is the response body (error from the server).\n                        error: body,\n                        headers,\n                        status,\n                        statusText,\n                        url: url || undefined,\n                    }));\n                }\n            };\n            // The onError callback is called when something goes wrong at the network level.\n            // Connection timeout, DNS error, offline, etc. These are actual errors, and are\n            // transmitted on the error channel.\n            const onError = (error) => {\n                const { url } = partialFromXhr();\n                const res = new HttpErrorResponse({\n                    error,\n                    status: xhr.status || 0,\n                    statusText: xhr.statusText || 'Unknown Error',\n                    url: url || undefined,\n                });\n                observer.error(res);\n            };\n            // The sentHeaders flag tracks whether the HttpResponseHeaders event\n            // has been sent on the stream. This is necessary to track if progress\n            // is enabled since the event will be sent on only the first download\n            // progress event.\n            let sentHeaders = false;\n            // The download progress event handler, which is only registered if\n            // progress events are enabled.\n            const onDownProgress = (event) => {\n                // Send the HttpResponseHeaders event if it hasn't been sent already.\n                if (!sentHeaders) {\n                    observer.next(partialFromXhr());\n                    sentHeaders = true;\n                }\n                // Start building the download progress event to deliver on the response\n                // event stream.\n                let progressEvent = {\n                    type: HttpEventType.DownloadProgress,\n                    loaded: event.loaded,\n                };\n                // Set the total number of bytes in the event if it's available.\n                if (event.lengthComputable) {\n                    progressEvent.total = event.total;\n                }\n                // If the request was for text content and a partial response is\n                // available on XMLHttpRequest, include it in the progress event\n                // to allow for streaming reads.\n                if (req.responseType === 'text' && !!xhr.responseText) {\n                    progressEvent.partialText = xhr.responseText;\n                }\n                // Finally, fire the event.\n                observer.next(progressEvent);\n            };\n            // The upload progress event handler, which is only registered if\n            // progress events are enabled.\n            const onUpProgress = (event) => {\n                // Upload progress events are simpler. Begin building the progress\n                // event.\n                let progress = {\n                    type: HttpEventType.UploadProgress,\n                    loaded: event.loaded,\n                };\n                // If the total number of bytes being uploaded is available, include\n                // it.\n                if (event.lengthComputable) {\n                    progress.total = event.total;\n                }\n                // Send the event.\n                observer.next(progress);\n            };\n            // By default, register for load and error events.\n            xhr.addEventListener('load', onLoad);\n            xhr.addEventListener('error', onError);\n            xhr.addEventListener('timeout', onError);\n            xhr.addEventListener('abort', onError);\n            // Progress events are only enabled if requested.\n            if (req.reportProgress) {\n                // Download progress is always enabled if requested.\n                xhr.addEventListener('progress', onDownProgress);\n                // Upload progress depends on whether there is a body to upload.\n                if (reqBody !== null && xhr.upload) {\n                    xhr.upload.addEventListener('progress', onUpProgress);\n                }\n            }\n            // Fire the request, and notify the event stream that it was fired.\n            xhr.send(reqBody);\n            observer.next({ type: HttpEventType.Sent });\n            // This is the return from the Observable function, which is the\n            // request cancellation handler.\n            return () => {\n                // On a cancellation, remove all registered event listeners.\n                xhr.removeEventListener('error', onError);\n                xhr.removeEventListener('abort', onError);\n                xhr.removeEventListener('load', onLoad);\n                xhr.removeEventListener('timeout', onError);\n                if (req.reportProgress) {\n                    xhr.removeEventListener('progress', onDownProgress);\n                    if (reqBody !== null && xhr.upload) {\n                        xhr.upload.removeEventListener('progress', onUpProgress);\n                    }\n                }\n                // Finally, abort the in-flight request.\n                if (xhr.readyState !== xhr.DONE) {\n                    xhr.abort();\n                }\n            };\n        });\n    }\n}\nHttpXhrBackend.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpXhrBackend, deps: [{ token: i1.XhrFactory }], target: i0.ɵɵFactoryTarget.Injectable });\nHttpXhrBackend.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpXhrBackend });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpXhrBackend, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i1.XhrFactory }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst XSRF_COOKIE_NAME = new InjectionToken('XSRF_COOKIE_NAME');\nconst XSRF_HEADER_NAME = new InjectionToken('XSRF_HEADER_NAME');\n/**\n * Retrieves the current XSRF token to use with the next outgoing request.\n *\n * @publicApi\n */\nclass HttpXsrfTokenExtractor {\n}\n/**\n * `HttpXsrfTokenExtractor` which retrieves the token from a cookie.\n */\nclass HttpXsrfCookieExtractor {\n    constructor(doc, platform, cookieName) {\n        this.doc = doc;\n        this.platform = platform;\n        this.cookieName = cookieName;\n        this.lastCookieString = '';\n        this.lastToken = null;\n        /**\n         * @internal for testing\n         */\n        this.parseCount = 0;\n    }\n    getToken() {\n        if (this.platform === 'server') {\n            return null;\n        }\n        const cookieString = this.doc.cookie || '';\n        if (cookieString !== this.lastCookieString) {\n            this.parseCount++;\n            this.lastToken = ɵparseCookieValue(cookieString, this.cookieName);\n            this.lastCookieString = cookieString;\n        }\n        return this.lastToken;\n    }\n}\nHttpXsrfCookieExtractor.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpXsrfCookieExtractor, deps: [{ token: DOCUMENT }, { token: PLATFORM_ID }, { token: XSRF_COOKIE_NAME }], target: i0.ɵɵFactoryTarget.Injectable });\nHttpXsrfCookieExtractor.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpXsrfCookieExtractor });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpXsrfCookieExtractor, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [XSRF_COOKIE_NAME]\n                }] }]; } });\n/**\n * `HttpInterceptor` which adds an XSRF token to eligible outgoing requests.\n */\nclass HttpXsrfInterceptor {\n    constructor(tokenService, headerName) {\n        this.tokenService = tokenService;\n        this.headerName = headerName;\n    }\n    intercept(req, next) {\n        const lcUrl = req.url.toLowerCase();\n        // Skip both non-mutating requests and absolute URLs.\n        // Non-mutating requests don't require a token, and absolute URLs require special handling\n        // anyway as the cookie set\n        // on our origin is not the same as the token expected by another origin.\n        if (req.method === 'GET' || req.method === 'HEAD' || lcUrl.startsWith('http://') ||\n            lcUrl.startsWith('https://')) {\n            return next.handle(req);\n        }\n        const token = this.tokenService.getToken();\n        // Be careful not to overwrite an existing header of the same name.\n        if (token !== null && !req.headers.has(this.headerName)) {\n            req = req.clone({ headers: req.headers.set(this.headerName, token) });\n        }\n        return next.handle(req);\n    }\n}\nHttpXsrfInterceptor.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpXsrfInterceptor, deps: [{ token: HttpXsrfTokenExtractor }, { token: XSRF_HEADER_NAME }], target: i0.ɵɵFactoryTarget.Injectable });\nHttpXsrfInterceptor.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpXsrfInterceptor });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpXsrfInterceptor, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: HttpXsrfTokenExtractor }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [XSRF_HEADER_NAME]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * An injectable `HttpHandler` that applies multiple interceptors\n * to a request before passing it to the given `HttpBackend`.\n *\n * The interceptors are loaded lazily from the injector, to allow\n * interceptors to themselves inject classes depending indirectly\n * on `HttpInterceptingHandler` itself.\n * @see `HttpInterceptor`\n */\nclass HttpInterceptingHandler {\n    constructor(backend, injector) {\n        this.backend = backend;\n        this.injector = injector;\n        this.chain = null;\n    }\n    handle(req) {\n        if (this.chain === null) {\n            const interceptors = this.injector.get(HTTP_INTERCEPTORS, []);\n            this.chain = interceptors.reduceRight((next, interceptor) => new HttpInterceptorHandler(next, interceptor), this.backend);\n        }\n        return this.chain.handle(req);\n    }\n}\nHttpInterceptingHandler.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpInterceptingHandler, deps: [{ token: HttpBackend }, { token: i0.Injector }], target: i0.ɵɵFactoryTarget.Injectable });\nHttpInterceptingHandler.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpInterceptingHandler });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpInterceptingHandler, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: HttpBackend }, { type: i0.Injector }]; } });\n/**\n * Constructs an `HttpHandler` that applies interceptors\n * to a request before passing it to the given `HttpBackend`.\n *\n * Use as a factory function within `HttpClientModule`.\n *\n *\n */\nfunction interceptingHandler(backend, interceptors = []) {\n    if (!interceptors) {\n        return backend;\n    }\n    return interceptors.reduceRight((next, interceptor) => new HttpInterceptorHandler(next, interceptor), backend);\n}\n/**\n * Factory function that determines where to store JSONP callbacks.\n *\n * Ordinarily JSONP callbacks are stored on the `window` object, but this may not exist\n * in test environments. In that case, callbacks are stored on an anonymous object instead.\n *\n *\n */\nfunction jsonpCallbackContext() {\n    if (typeof window === 'object') {\n        return window;\n    }\n    return {};\n}\n/**\n * Configures XSRF protection support for outgoing requests.\n *\n * For a server that supports a cookie-based XSRF protection system,\n * use directly to configure XSRF protection with the correct\n * cookie and header names.\n *\n * If no names are supplied, the default cookie name is `XSRF-TOKEN`\n * and the default header name is `X-XSRF-TOKEN`.\n *\n * @publicApi\n */\nclass HttpClientXsrfModule {\n    /**\n     * Disable the default XSRF protection.\n     */\n    static disable() {\n        return {\n            ngModule: HttpClientXsrfModule,\n            providers: [\n                { provide: HttpXsrfInterceptor, useClass: NoopInterceptor },\n            ],\n        };\n    }\n    /**\n     * Configure XSRF protection.\n     * @param options An object that can specify either or both\n     * cookie name or header name.\n     * - Cookie name default is `XSRF-TOKEN`.\n     * - Header name default is `X-XSRF-TOKEN`.\n     *\n     */\n    static withOptions(options = {}) {\n        return {\n            ngModule: HttpClientXsrfModule,\n            providers: [\n                options.cookieName ? { provide: XSRF_COOKIE_NAME, useValue: options.cookieName } : [],\n                options.headerName ? { provide: XSRF_HEADER_NAME, useValue: options.headerName } : [],\n            ],\n        };\n    }\n}\nHttpClientXsrfModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpClientXsrfModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nHttpClientXsrfModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpClientXsrfModule });\nHttpClientXsrfModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpClientXsrfModule, providers: [\n        HttpXsrfInterceptor,\n        { provide: HTTP_INTERCEPTORS, useExisting: HttpXsrfInterceptor, multi: true },\n        { provide: HttpXsrfTokenExtractor, useClass: HttpXsrfCookieExtractor },\n        { provide: XSRF_COOKIE_NAME, useValue: 'XSRF-TOKEN' },\n        { provide: XSRF_HEADER_NAME, useValue: 'X-XSRF-TOKEN' },\n    ] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpClientXsrfModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        HttpXsrfInterceptor,\n                        { provide: HTTP_INTERCEPTORS, useExisting: HttpXsrfInterceptor, multi: true },\n                        { provide: HttpXsrfTokenExtractor, useClass: HttpXsrfCookieExtractor },\n                        { provide: XSRF_COOKIE_NAME, useValue: 'XSRF-TOKEN' },\n                        { provide: XSRF_HEADER_NAME, useValue: 'X-XSRF-TOKEN' },\n                    ],\n                }]\n        }] });\n/**\n * Configures the [dependency injector](guide/glossary#injector) for `HttpClient`\n * with supporting services for XSRF. Automatically imported by `HttpClientModule`.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in [DI token](guide/glossary#di-token) `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n */\nclass HttpClientModule {\n}\nHttpClientModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpClientModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nHttpClientModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpClientModule, imports: [HttpClientXsrfModule] });\nHttpClientModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpClientModule, providers: [\n        HttpClient,\n        { provide: HttpHandler, useClass: HttpInterceptingHandler },\n        HttpXhrBackend,\n        { provide: HttpBackend, useExisting: HttpXhrBackend },\n    ], imports: [HttpClientXsrfModule.withOptions({\n            cookieName: 'XSRF-TOKEN',\n            headerName: 'X-XSRF-TOKEN',\n        })] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpClientModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    /**\n                     * Optional configuration for XSRF protection.\n                     */\n                    imports: [\n                        HttpClientXsrfModule.withOptions({\n                            cookieName: 'XSRF-TOKEN',\n                            headerName: 'X-XSRF-TOKEN',\n                        }),\n                    ],\n                    /**\n                     * Configures the [dependency injector](guide/glossary#injector) where it is imported\n                     * with supporting services for HTTP communications.\n                     */\n                    providers: [\n                        HttpClient,\n                        { provide: HttpHandler, useClass: HttpInterceptingHandler },\n                        HttpXhrBackend,\n                        { provide: HttpBackend, useExisting: HttpXhrBackend },\n                    ],\n                }]\n        }] });\n/**\n * Configures the [dependency injector](guide/glossary#injector) for `HttpClient`\n * with supporting services for JSONP.\n * Without this module, Jsonp requests reach the backend\n * with method JSONP, where they are rejected.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in [DI token](guide/glossary#di-token) `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n */\nclass HttpClientJsonpModule {\n}\nHttpClientJsonpModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpClientJsonpModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nHttpClientJsonpModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpClientJsonpModule });\nHttpClientJsonpModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpClientJsonpModule, providers: [\n        JsonpClientBackend,\n        { provide: JsonpCallbackContext, useFactory: jsonpCallbackContext },\n        { provide: HTTP_INTERCEPTORS, useClass: JsonpInterceptor, multi: true },\n    ] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: HttpClientJsonpModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        JsonpClientBackend,\n                        { provide: JsonpCallbackContext, useFactory: jsonpCallbackContext },\n                        { provide: HTTP_INTERCEPTORS, useClass: JsonpInterceptor, multi: true },\n                    ],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * A wrapper around the `XMLHttpRequest` constructor.\n *\n * @publicApi\n * @see `XhrFactory`\n * @deprecated\n * `XhrFactory` has moved, please import `XhrFactory` from `@angular/common` instead.\n */\nconst XhrFactory = XhrFactory$1;\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { HTTP_INTERCEPTORS, HttpBackend, HttpClient, HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, HttpContext, HttpContextToken, HttpErrorResponse, HttpEventType, HttpHandler, HttpHeaderResponse, HttpHeaders, HttpParams, HttpRequest, HttpResponse, HttpResponseBase, HttpUrlEncodingCodec, HttpXhrBackend, HttpXsrfTokenExtractor, JsonpClientBackend, JsonpInterceptor, XhrFactory, HttpInterceptingHandler as ɵHttpInterceptingHandler };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AAEA,OAAO,KAAKA,EAAZ,MAAoB,iBAApB;AACA,SAASC,QAAT,EAAmBC,iBAAnB,EAAsCC,UAAU,IAAIC,YAApD,QAAwE,iBAAxE;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,cAArB,EAAqCC,MAArC,EAA6CC,WAA7C,EAA0DC,QAA1D,QAA0E,eAA1E;AACA,SAASC,EAAT,EAAaC,UAAb,QAA+B,MAA/B;AACA,SAASC,SAAT,EAAoBC,MAApB,EAA4BC,GAA5B,QAAuC,gBAAvC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,WAAN,CAAkB;AAElB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,WAAN,CAAkB;AAGlB;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,WAAN,CAAkB;EACd;EACAC,WAAW,CAACC,OAAD,EAAU;IACjB;AACR;AACA;AACA;IACQ,KAAKC,eAAL,GAAuB,IAAIC,GAAJ,EAAvB;IACA;AACR;AACA;;IACQ,KAAKC,UAAL,GAAkB,IAAlB;;IACA,IAAI,CAACH,OAAL,EAAc;MACV,KAAKA,OAAL,GAAe,IAAIE,GAAJ,EAAf;IACH,CAFD,MAGK,IAAI,OAAOF,OAAP,KAAmB,QAAvB,EAAiC;MAClC,KAAKI,QAAL,GAAgB,MAAM;QAClB,KAAKJ,OAAL,GAAe,IAAIE,GAAJ,EAAf;QACAF,OAAO,CAACK,KAAR,CAAc,IAAd,EAAoBC,OAApB,CAA4BC,IAAI,IAAI;UAChC,MAAMC,KAAK,GAAGD,IAAI,CAACE,OAAL,CAAa,GAAb,CAAd;;UACA,IAAID,KAAK,GAAG,CAAZ,EAAe;YACX,MAAME,IAAI,GAAGH,IAAI,CAACI,KAAL,CAAW,CAAX,EAAcH,KAAd,CAAb;YACA,MAAMI,GAAG,GAAGF,IAAI,CAACG,WAAL,EAAZ;YACA,MAAMC,KAAK,GAAGP,IAAI,CAACI,KAAL,CAAWH,KAAK,GAAG,CAAnB,EAAsBO,IAAtB,EAAd;YACA,KAAKC,sBAAL,CAA4BN,IAA5B,EAAkCE,GAAlC;;YACA,IAAI,KAAKZ,OAAL,CAAaiB,GAAb,CAAiBL,GAAjB,CAAJ,EAA2B;cACvB,KAAKZ,OAAL,CAAakB,GAAb,CAAiBN,GAAjB,EAAsBO,IAAtB,CAA2BL,KAA3B;YACH,CAFD,MAGK;cACD,KAAKd,OAAL,CAAaoB,GAAb,CAAiBR,GAAjB,EAAsB,CAACE,KAAD,CAAtB;YACH;UACJ;QACJ,CAdD;MAeH,CAjBD;IAkBH,CAnBI,MAoBA;MACD,KAAKV,QAAL,GAAgB,MAAM;QAClB,IAAI,OAAOiB,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;UAC/CC,kBAAkB,CAACtB,OAAD,CAAlB;QACH;;QACD,KAAKA,OAAL,GAAe,IAAIE,GAAJ,EAAf;QACAqB,MAAM,CAACC,IAAP,CAAYxB,OAAZ,EAAqBM,OAArB,CAA6BI,IAAI,IAAI;UACjC,IAAIe,MAAM,GAAGzB,OAAO,CAACU,IAAD,CAApB;UACA,MAAME,GAAG,GAAGF,IAAI,CAACG,WAAL,EAAZ;;UACA,IAAI,OAAOY,MAAP,KAAkB,QAAtB,EAAgC;YAC5BA,MAAM,GAAG,CAACA,MAAD,CAAT;UACH;;UACD,IAAIA,MAAM,CAACC,MAAP,GAAgB,CAApB,EAAuB;YACnB,KAAK1B,OAAL,CAAaoB,GAAb,CAAiBR,GAAjB,EAAsBa,MAAtB;YACA,KAAKT,sBAAL,CAA4BN,IAA5B,EAAkCE,GAAlC;UACH;QACJ,CAVD;MAWH,CAhBD;IAiBH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACIK,GAAG,CAACP,IAAD,EAAO;IACN,KAAKiB,IAAL;IACA,OAAO,KAAK3B,OAAL,CAAaiB,GAAb,CAAiBP,IAAI,CAACG,WAAL,EAAjB,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACIK,GAAG,CAACR,IAAD,EAAO;IACN,KAAKiB,IAAL;IACA,MAAMF,MAAM,GAAG,KAAKzB,OAAL,CAAakB,GAAb,CAAiBR,IAAI,CAACG,WAAL,EAAjB,CAAf;IACA,OAAOY,MAAM,IAAIA,MAAM,CAACC,MAAP,GAAgB,CAA1B,GAA8BD,MAAM,CAAC,CAAD,CAApC,GAA0C,IAAjD;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACID,IAAI,GAAG;IACH,KAAKG,IAAL;IACA,OAAOC,KAAK,CAACC,IAAN,CAAW,KAAK5B,eAAL,CAAqBwB,MAArB,EAAX,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACIK,MAAM,CAACpB,IAAD,EAAO;IACT,KAAKiB,IAAL;IACA,OAAO,KAAK3B,OAAL,CAAakB,GAAb,CAAiBR,IAAI,CAACG,WAAL,EAAjB,KAAwC,IAA/C;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIkB,MAAM,CAACrB,IAAD,EAAOI,KAAP,EAAc;IAChB,OAAO,KAAKkB,KAAL,CAAW;MAAEtB,IAAF;MAAQI,KAAR;MAAemB,EAAE,EAAE;IAAnB,CAAX,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIb,GAAG,CAACV,IAAD,EAAOI,KAAP,EAAc;IACb,OAAO,KAAKkB,KAAL,CAAW;MAAEtB,IAAF;MAAQI,KAAR;MAAemB,EAAE,EAAE;IAAnB,CAAX,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIC,MAAM,CAACxB,IAAD,EAAOI,KAAP,EAAc;IAChB,OAAO,KAAKkB,KAAL,CAAW;MAAEtB,IAAF;MAAQI,KAAR;MAAemB,EAAE,EAAE;IAAnB,CAAX,CAAP;EACH;;EACDjB,sBAAsB,CAACN,IAAD,EAAOyB,MAAP,EAAe;IACjC,IAAI,CAAC,KAAKlC,eAAL,CAAqBgB,GAArB,CAAyBkB,MAAzB,CAAL,EAAuC;MACnC,KAAKlC,eAAL,CAAqBmB,GAArB,CAAyBe,MAAzB,EAAiCzB,IAAjC;IACH;EACJ;;EACDiB,IAAI,GAAG;IACH,IAAI,CAAC,CAAC,KAAKvB,QAAX,EAAqB;MACjB,IAAI,KAAKA,QAAL,YAAyBN,WAA7B,EAA0C;QACtC,KAAKsC,QAAL,CAAc,KAAKhC,QAAnB;MACH,CAFD,MAGK;QACD,KAAKA,QAAL;MACH;;MACD,KAAKA,QAAL,GAAgB,IAAhB;;MACA,IAAI,CAAC,CAAC,KAAKD,UAAX,EAAuB;QACnB,KAAKA,UAAL,CAAgBG,OAAhB,CAAwB+B,MAAM,IAAI,KAAKC,WAAL,CAAiBD,MAAjB,CAAlC;QACA,KAAKlC,UAAL,GAAkB,IAAlB;MACH;IACJ;EACJ;;EACDiC,QAAQ,CAACG,KAAD,EAAQ;IACZA,KAAK,CAACZ,IAAN;IACAC,KAAK,CAACC,IAAN,CAAWU,KAAK,CAACvC,OAAN,CAAcwB,IAAd,EAAX,EAAiClB,OAAjC,CAAyCM,GAAG,IAAI;MAC5C,KAAKZ,OAAL,CAAaoB,GAAb,CAAiBR,GAAjB,EAAsB2B,KAAK,CAACvC,OAAN,CAAckB,GAAd,CAAkBN,GAAlB,CAAtB;MACA,KAAKX,eAAL,CAAqBmB,GAArB,CAAyBR,GAAzB,EAA8B2B,KAAK,CAACtC,eAAN,CAAsBiB,GAAtB,CAA0BN,GAA1B,CAA9B;IACH,CAHD;EAIH;;EACDoB,KAAK,CAACK,MAAD,EAAS;IACV,MAAML,KAAK,GAAG,IAAIlC,WAAJ,EAAd;IACAkC,KAAK,CAAC5B,QAAN,GACK,CAAC,CAAC,KAAKA,QAAP,IAAmB,KAAKA,QAAL,YAAyBN,WAA7C,GAA4D,KAAKM,QAAjE,GAA4E,IADhF;IAEA4B,KAAK,CAAC7B,UAAN,GAAmB,CAAC,KAAKA,UAAL,IAAmB,EAApB,EAAwBqC,MAAxB,CAA+B,CAACH,MAAD,CAA/B,CAAnB;IACA,OAAOL,KAAP;EACH;;EACDM,WAAW,CAACD,MAAD,EAAS;IAChB,MAAMzB,GAAG,GAAGyB,MAAM,CAAC3B,IAAP,CAAYG,WAAZ,EAAZ;;IACA,QAAQwB,MAAM,CAACJ,EAAf;MACI,KAAK,GAAL;MACA,KAAK,GAAL;QACI,IAAInB,KAAK,GAAGuB,MAAM,CAACvB,KAAnB;;QACA,IAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;UAC3BA,KAAK,GAAG,CAACA,KAAD,CAAR;QACH;;QACD,IAAIA,KAAK,CAACY,MAAN,KAAiB,CAArB,EAAwB;UACpB;QACH;;QACD,KAAKV,sBAAL,CAA4BqB,MAAM,CAAC3B,IAAnC,EAAyCE,GAAzC;QACA,MAAM6B,IAAI,GAAG,CAACJ,MAAM,CAACJ,EAAP,KAAc,GAAd,GAAoB,KAAKjC,OAAL,CAAakB,GAAb,CAAiBN,GAAjB,CAApB,GAA4C8B,SAA7C,KAA2D,EAAxE;QACAD,IAAI,CAACtB,IAAL,CAAU,GAAGL,KAAb;QACA,KAAKd,OAAL,CAAaoB,GAAb,CAAiBR,GAAjB,EAAsB6B,IAAtB;QACA;;MACJ,KAAK,GAAL;QACI,MAAME,QAAQ,GAAGN,MAAM,CAACvB,KAAxB;;QACA,IAAI,CAAC6B,QAAL,EAAe;UACX,KAAK3C,OAAL,CAAakC,MAAb,CAAoBtB,GAApB;UACA,KAAKX,eAAL,CAAqBiC,MAArB,CAA4BtB,GAA5B;QACH,CAHD,MAIK;UACD,IAAIgC,QAAQ,GAAG,KAAK5C,OAAL,CAAakB,GAAb,CAAiBN,GAAjB,CAAf;;UACA,IAAI,CAACgC,QAAL,EAAe;YACX;UACH;;UACDA,QAAQ,GAAGA,QAAQ,CAAClD,MAAT,CAAgBoB,KAAK,IAAI6B,QAAQ,CAAClC,OAAT,CAAiBK,KAAjB,MAA4B,CAAC,CAAtD,CAAX;;UACA,IAAI8B,QAAQ,CAAClB,MAAT,KAAoB,CAAxB,EAA2B;YACvB,KAAK1B,OAAL,CAAakC,MAAb,CAAoBtB,GAApB;YACA,KAAKX,eAAL,CAAqBiC,MAArB,CAA4BtB,GAA5B;UACH,CAHD,MAIK;YACD,KAAKZ,OAAL,CAAaoB,GAAb,CAAiBR,GAAjB,EAAsBgC,QAAtB;UACH;QACJ;;QACD;IAnCR;EAqCH;EACD;AACJ;AACA;;;EACItC,OAAO,CAACuC,EAAD,EAAK;IACR,KAAKlB,IAAL;IACAC,KAAK,CAACC,IAAN,CAAW,KAAK5B,eAAL,CAAqBuB,IAArB,EAAX,EACKlB,OADL,CACaM,GAAG,IAAIiC,EAAE,CAAC,KAAK5C,eAAL,CAAqBiB,GAArB,CAAyBN,GAAzB,CAAD,EAAgC,KAAKZ,OAAL,CAAakB,GAAb,CAAiBN,GAAjB,CAAhC,CADtB;EAEH;;AAvNa;AAyNlB;AACA;AACA;AACA;AACA;;;AACA,SAASU,kBAAT,CAA4BtB,OAA5B,EAAqC;EACjC,KAAK,MAAM,CAACY,GAAD,EAAME,KAAN,CAAX,IAA2BS,MAAM,CAACuB,OAAP,CAAe9C,OAAf,CAA3B,EAAoD;IAChD,IAAI,OAAOc,KAAP,KAAiB,QAAjB,IAA6B,CAACc,KAAK,CAACmB,OAAN,CAAcjC,KAAd,CAAlC,EAAwD;MACpD,MAAM,IAAIkC,KAAJ,CAAW,6BAA4BpC,GAAI,sBAAjC,GACX,qDAAoDE,KAAM,KADzD,CAAN;IAEH;EACJ;AACJ;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMmC,oBAAN,CAA2B;EACvB;AACJ;AACA;AACA;AACA;EACIC,SAAS,CAACtC,GAAD,EAAM;IACX,OAAOuC,gBAAgB,CAACvC,GAAD,CAAvB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIwC,WAAW,CAACtC,KAAD,EAAQ;IACf,OAAOqC,gBAAgB,CAACrC,KAAD,CAAvB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIuC,SAAS,CAACzC,GAAD,EAAM;IACX,OAAO0C,kBAAkB,CAAC1C,GAAD,CAAzB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACI2C,WAAW,CAACzC,KAAD,EAAQ;IACf,OAAOwC,kBAAkB,CAACxC,KAAD,CAAzB;EACH;;AAhCsB;;AAkC3B,SAAS0C,WAAT,CAAqBC,SAArB,EAAgCC,KAAhC,EAAuC;EACnC,MAAM/D,GAAG,GAAG,IAAIO,GAAJ,EAAZ;;EACA,IAAIuD,SAAS,CAAC/B,MAAV,GAAmB,CAAvB,EAA0B;IACtB;IACA;IACA;IACA,MAAMiC,MAAM,GAAGF,SAAS,CAACG,OAAV,CAAkB,KAAlB,EAAyB,EAAzB,EAA6BvD,KAA7B,CAAmC,GAAnC,CAAf;IACAsD,MAAM,CAACrD,OAAP,CAAgBuD,KAAD,IAAW;MACtB,MAAMC,KAAK,GAAGD,KAAK,CAACpD,OAAN,CAAc,GAAd,CAAd;MACA,MAAM,CAACG,GAAD,EAAMmD,GAAN,IAAaD,KAAK,IAAI,CAAC,CAAV,GACf,CAACJ,KAAK,CAACL,SAAN,CAAgBQ,KAAhB,CAAD,EAAyB,EAAzB,CADe,GAEf,CAACH,KAAK,CAACL,SAAN,CAAgBQ,KAAK,CAAClD,KAAN,CAAY,CAAZ,EAAemD,KAAf,CAAhB,CAAD,EAAyCJ,KAAK,CAACH,WAAN,CAAkBM,KAAK,CAAClD,KAAN,CAAYmD,KAAK,GAAG,CAApB,CAAlB,CAAzC,CAFJ;MAGA,MAAME,IAAI,GAAGrE,GAAG,CAACuB,GAAJ,CAAQN,GAAR,KAAgB,EAA7B;MACAoD,IAAI,CAAC7C,IAAL,CAAU4C,GAAV;MACApE,GAAG,CAACyB,GAAJ,CAAQR,GAAR,EAAaoD,IAAb;IACH,CARD;EASH;;EACD,OAAOrE,GAAP;AACH;AACD;AACA;AACA;;;AACA,MAAMsE,uBAAuB,GAAG,iBAAhC;AACA,MAAMC,8BAA8B,GAAG;EACnC,MAAM,GAD6B;EAEnC,MAAM,GAF6B;EAGnC,MAAM,GAH6B;EAInC,MAAM,GAJ6B;EAKnC,MAAM,GAL6B;EAMnC,MAAM,GAN6B;EAOnC,MAAM,GAP6B;EAQnC,MAAM;AAR6B,CAAvC;;AAUA,SAASf,gBAAT,CAA0BgB,CAA1B,EAA6B;EACzB,OAAOC,kBAAkB,CAACD,CAAD,CAAlB,CAAsBP,OAAtB,CAA8BK,uBAA9B,EAAuD,CAACI,CAAD,EAAIC,CAAJ,KAAUJ,8BAA8B,CAACI,CAAD,CAA9B,IAAqCD,CAAtG,CAAP;AACH;;AACD,SAASE,aAAT,CAAuBzD,KAAvB,EAA8B;EAC1B,OAAQ,GAAEA,KAAM,EAAhB;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM0D,UAAN,CAAiB;EACbzE,WAAW,CAAC0E,OAAO,GAAG,EAAX,EAAe;IACtB,KAAKC,OAAL,GAAe,IAAf;IACA,KAAKC,SAAL,GAAiB,IAAjB;IACA,KAAKC,OAAL,GAAeH,OAAO,CAACG,OAAR,IAAmB,IAAI3B,oBAAJ,EAAlC;;IACA,IAAI,CAAC,CAACwB,OAAO,CAACI,UAAd,EAA0B;MACtB,IAAI,CAAC,CAACJ,OAAO,CAACK,UAAd,EAA0B;QACtB,MAAM,IAAI9B,KAAJ,CAAW,gDAAX,CAAN;MACH;;MACD,KAAKrD,GAAL,GAAW6D,WAAW,CAACiB,OAAO,CAACI,UAAT,EAAqB,KAAKD,OAA1B,CAAtB;IACH,CALD,MAMK,IAAI,CAAC,CAACH,OAAO,CAACK,UAAd,EAA0B;MAC3B,KAAKnF,GAAL,GAAW,IAAIO,GAAJ,EAAX;MACAqB,MAAM,CAACC,IAAP,CAAYiD,OAAO,CAACK,UAApB,EAAgCxE,OAAhC,CAAwCM,GAAG,IAAI;QAC3C,MAAME,KAAK,GAAG2D,OAAO,CAACK,UAAR,CAAmBlE,GAAnB,CAAd,CAD2C,CAE3C;;QACA,MAAMa,MAAM,GAAGG,KAAK,CAACmB,OAAN,CAAcjC,KAAd,IAAuBA,KAAK,CAACnB,GAAN,CAAU4E,aAAV,CAAvB,GAAkD,CAACA,aAAa,CAACzD,KAAD,CAAd,CAAjE;QACA,KAAKnB,GAAL,CAASyB,GAAT,CAAaR,GAAb,EAAkBa,MAAlB;MACH,CALD;IAMH,CARI,MASA;MACD,KAAK9B,GAAL,GAAW,IAAX;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIsB,GAAG,CAAC4C,KAAD,EAAQ;IACP,KAAKlC,IAAL;IACA,OAAO,KAAKhC,GAAL,CAASsB,GAAT,CAAa4C,KAAb,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACI3C,GAAG,CAAC2C,KAAD,EAAQ;IACP,KAAKlC,IAAL;IACA,MAAMoD,GAAG,GAAG,KAAKpF,GAAL,CAASuB,GAAT,CAAa2C,KAAb,CAAZ;IACA,OAAO,CAAC,CAACkB,GAAF,GAAQA,GAAG,CAAC,CAAD,CAAX,GAAiB,IAAxB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIjD,MAAM,CAAC+B,KAAD,EAAQ;IACV,KAAKlC,IAAL;IACA,OAAO,KAAKhC,GAAL,CAASuB,GAAT,CAAa2C,KAAb,KAAuB,IAA9B;EACH;EACD;AACJ;AACA;AACA;;;EACIrC,IAAI,GAAG;IACH,KAAKG,IAAL;IACA,OAAOC,KAAK,CAACC,IAAN,CAAW,KAAKlC,GAAL,CAAS6B,IAAT,EAAX,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIO,MAAM,CAAC8B,KAAD,EAAQ/C,KAAR,EAAe;IACjB,OAAO,KAAKkB,KAAL,CAAW;MAAE6B,KAAF;MAAS/C,KAAT;MAAgBmB,EAAE,EAAE;IAApB,CAAX,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACI+C,SAAS,CAACrB,MAAD,EAAS;IACd,MAAMe,OAAO,GAAG,EAAhB;IACAnD,MAAM,CAACC,IAAP,CAAYmC,MAAZ,EAAoBrD,OAApB,CAA4BuD,KAAK,IAAI;MACjC,MAAM/C,KAAK,GAAG6C,MAAM,CAACE,KAAD,CAApB;;MACA,IAAIjC,KAAK,CAACmB,OAAN,CAAcjC,KAAd,CAAJ,EAA0B;QACtBA,KAAK,CAACR,OAAN,CAAc2E,MAAM,IAAI;UACpBP,OAAO,CAACvD,IAAR,CAAa;YAAE0C,KAAF;YAAS/C,KAAK,EAAEmE,MAAhB;YAAwBhD,EAAE,EAAE;UAA5B,CAAb;QACH,CAFD;MAGH,CAJD,MAKK;QACDyC,OAAO,CAACvD,IAAR,CAAa;UAAE0C,KAAF;UAAS/C,KAAK,EAAEA,KAAhB;UAAuBmB,EAAE,EAAE;QAA3B,CAAb;MACH;IACJ,CAVD;IAWA,OAAO,KAAKD,KAAL,CAAW0C,OAAX,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACItD,GAAG,CAACyC,KAAD,EAAQ/C,KAAR,EAAe;IACd,OAAO,KAAKkB,KAAL,CAAW;MAAE6B,KAAF;MAAS/C,KAAT;MAAgBmB,EAAE,EAAE;IAApB,CAAX,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACIC,MAAM,CAAC2B,KAAD,EAAQ/C,KAAR,EAAe;IACjB,OAAO,KAAKkB,KAAL,CAAW;MAAE6B,KAAF;MAAS/C,KAAT;MAAgBmB,EAAE,EAAE;IAApB,CAAX,CAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIiD,QAAQ,GAAG;IACP,KAAKvD,IAAL;IACA,OAAO,KAAKH,IAAL,GACF7B,GADE,CACEiB,GAAG,IAAI;MACZ,MAAMuE,IAAI,GAAG,KAAKP,OAAL,CAAa1B,SAAb,CAAuBtC,GAAvB,CAAb,CADY,CAEZ;MACA;MACA;;MACA,OAAO,KAAKjB,GAAL,CAASuB,GAAT,CAAaN,GAAb,EAAkBjB,GAAlB,CAAsBmB,KAAK,IAAIqE,IAAI,GAAG,GAAP,GAAa,KAAKP,OAAL,CAAaxB,WAAb,CAAyBtC,KAAzB,CAA5C,EACFsE,IADE,CACG,GADH,CAAP;IAEH,CARM,EASH;IACA;IAVG,CAWF1F,MAXE,CAWKmE,KAAK,IAAIA,KAAK,KAAK,EAXxB,EAYFuB,IAZE,CAYG,GAZH,CAAP;EAaH;;EACDpD,KAAK,CAACK,MAAD,EAAS;IACV,MAAML,KAAK,GAAG,IAAIwC,UAAJ,CAAe;MAAEI,OAAO,EAAE,KAAKA;IAAhB,CAAf,CAAd;IACA5C,KAAK,CAAC2C,SAAN,GAAkB,KAAKA,SAAL,IAAkB,IAApC;IACA3C,KAAK,CAAC0C,OAAN,GAAgB,CAAC,KAAKA,OAAL,IAAgB,EAAjB,EAAqBlC,MAArB,CAA4BH,MAA5B,CAAhB;IACA,OAAOL,KAAP;EACH;;EACDL,IAAI,GAAG;IACH,IAAI,KAAKhC,GAAL,KAAa,IAAjB,EAAuB;MACnB,KAAKA,GAAL,GAAW,IAAIO,GAAJ,EAAX;IACH;;IACD,IAAI,KAAKyE,SAAL,KAAmB,IAAvB,EAA6B;MACzB,KAAKA,SAAL,CAAehD,IAAf;MACA,KAAKgD,SAAL,CAAenD,IAAf,GAAsBlB,OAAtB,CAA8BM,GAAG,IAAI,KAAKjB,GAAL,CAASyB,GAAT,CAAaR,GAAb,EAAkB,KAAK+D,SAAL,CAAehF,GAAf,CAAmBuB,GAAnB,CAAuBN,GAAvB,CAAlB,CAArC;MACA,KAAK8D,OAAL,CAAapE,OAAb,CAAqB+B,MAAM,IAAI;QAC3B,QAAQA,MAAM,CAACJ,EAAf;UACI,KAAK,GAAL;UACA,KAAK,GAAL;YACI,MAAMQ,IAAI,GAAG,CAACJ,MAAM,CAACJ,EAAP,KAAc,GAAd,GAAoB,KAAKtC,GAAL,CAASuB,GAAT,CAAamB,MAAM,CAACwB,KAApB,CAApB,GAAiDnB,SAAlD,KAAgE,EAA7E;YACAD,IAAI,CAACtB,IAAL,CAAUoD,aAAa,CAAClC,MAAM,CAACvB,KAAR,CAAvB;YACA,KAAKnB,GAAL,CAASyB,GAAT,CAAaiB,MAAM,CAACwB,KAApB,EAA2BpB,IAA3B;YACA;;UACJ,KAAK,GAAL;YACI,IAAIJ,MAAM,CAACvB,KAAP,KAAiB4B,SAArB,EAAgC;cAC5B,IAAID,IAAI,GAAG,KAAK9C,GAAL,CAASuB,GAAT,CAAamB,MAAM,CAACwB,KAApB,KAA8B,EAAzC;cACA,MAAMwB,GAAG,GAAG5C,IAAI,CAAChC,OAAL,CAAa8D,aAAa,CAAClC,MAAM,CAACvB,KAAR,CAA1B,CAAZ;;cACA,IAAIuE,GAAG,KAAK,CAAC,CAAb,EAAgB;gBACZ5C,IAAI,CAAC6C,MAAL,CAAYD,GAAZ,EAAiB,CAAjB;cACH;;cACD,IAAI5C,IAAI,CAACf,MAAL,GAAc,CAAlB,EAAqB;gBACjB,KAAK/B,GAAL,CAASyB,GAAT,CAAaiB,MAAM,CAACwB,KAApB,EAA2BpB,IAA3B;cACH,CAFD,MAGK;gBACD,KAAK9C,GAAL,CAASuC,MAAT,CAAgBG,MAAM,CAACwB,KAAvB;cACH;YACJ,CAZD,MAaK;cACD,KAAKlE,GAAL,CAASuC,MAAT,CAAgBG,MAAM,CAACwB,KAAvB;cACA;YACH;;QAxBT;MA0BH,CA3BD;MA4BA,KAAKc,SAAL,GAAiB,KAAKD,OAAL,GAAe,IAAhC;IACH;EACJ;;AA9KY;AAiLjB;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMa,gBAAN,CAAuB;EACnBxF,WAAW,CAACyF,YAAD,EAAe;IACtB,KAAKA,YAAL,GAAoBA,YAApB;EACH;;AAHkB;AAKvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,WAAN,CAAkB;EACd1F,WAAW,GAAG;IACV,KAAKJ,GAAL,GAAW,IAAIO,GAAJ,EAAX;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIkB,GAAG,CAACsE,KAAD,EAAQ5E,KAAR,EAAe;IACd,KAAKnB,GAAL,CAASyB,GAAT,CAAasE,KAAb,EAAoB5E,KAApB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACII,GAAG,CAACwE,KAAD,EAAQ;IACP,IAAI,CAAC,KAAK/F,GAAL,CAASsB,GAAT,CAAayE,KAAb,CAAL,EAA0B;MACtB,KAAK/F,GAAL,CAASyB,GAAT,CAAasE,KAAb,EAAoBA,KAAK,CAACF,YAAN,EAApB;IACH;;IACD,OAAO,KAAK7F,GAAL,CAASuB,GAAT,CAAawE,KAAb,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACIxD,MAAM,CAACwD,KAAD,EAAQ;IACV,KAAK/F,GAAL,CAASuC,MAAT,CAAgBwD,KAAhB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACIzE,GAAG,CAACyE,KAAD,EAAQ;IACP,OAAO,KAAK/F,GAAL,CAASsB,GAAT,CAAayE,KAAb,CAAP;EACH;EACD;AACJ;AACA;;;EACIlE,IAAI,GAAG;IACH,OAAO,KAAK7B,GAAL,CAAS6B,IAAT,EAAP;EACH;;AAvDa;AA0DlB;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,SAASmE,aAAT,CAAuBC,MAAvB,EAA+B;EAC3B,QAAQA,MAAR;IACI,KAAK,QAAL;IACA,KAAK,KAAL;IACA,KAAK,MAAL;IACA,KAAK,SAAL;IACA,KAAK,OAAL;MACI,OAAO,KAAP;;IACJ;MACI,OAAO,IAAP;EARR;AAUH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASC,aAAT,CAAuB/E,KAAvB,EAA8B;EAC1B,OAAO,OAAOgF,WAAP,KAAuB,WAAvB,IAAsChF,KAAK,YAAYgF,WAA9D;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASC,MAAT,CAAgBjF,KAAhB,EAAuB;EACnB,OAAO,OAAOkF,IAAP,KAAgB,WAAhB,IAA+BlF,KAAK,YAAYkF,IAAvD;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASC,UAAT,CAAoBnF,KAApB,EAA2B;EACvB,OAAO,OAAOoF,QAAP,KAAoB,WAApB,IAAmCpF,KAAK,YAAYoF,QAA3D;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASC,iBAAT,CAA2BrF,KAA3B,EAAkC;EAC9B,OAAO,OAAOsF,eAAP,KAA2B,WAA3B,IAA0CtF,KAAK,YAAYsF,eAAlE;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,WAAN,CAAkB;EACdtG,WAAW,CAAC6F,MAAD,EAASU,GAAT,EAAcC,KAAd,EAAqBC,MAArB,EAA6B;IACpC,KAAKF,GAAL,GAAWA,GAAX;IACA;AACR;AACA;AACA;AACA;AACA;AACA;;IACQ,KAAKG,IAAL,GAAY,IAAZ;IACA;AACR;AACA;AACA;AACA;AACA;;IACQ,KAAKC,cAAL,GAAsB,KAAtB;IACA;AACR;AACA;;IACQ,KAAKC,eAAL,GAAuB,KAAvB;IACA;AACR;AACA;AACA;AACA;AACA;;IACQ,KAAKC,YAAL,GAAoB,MAApB;IACA,KAAKhB,MAAL,GAAcA,MAAM,CAACiB,WAAP,EAAd,CA5BoC,CA6BpC;IACA;;IACA,IAAIpC,OAAJ,CA/BoC,CAgCpC;IACA;;IACA,IAAIkB,aAAa,CAAC,KAAKC,MAAN,CAAb,IAA8B,CAAC,CAACY,MAApC,EAA4C;MACxC;MACA,KAAKC,IAAL,GAAaF,KAAK,KAAK7D,SAAX,GAAwB6D,KAAxB,GAAgC,IAA5C;MACA9B,OAAO,GAAG+B,MAAV;IACH,CAJD,MAKK;MACD;MACA/B,OAAO,GAAG8B,KAAV;IACH,CA1CmC,CA2CpC;;;IACA,IAAI9B,OAAJ,EAAa;MACT;MACA,KAAKiC,cAAL,GAAsB,CAAC,CAACjC,OAAO,CAACiC,cAAhC;MACA,KAAKC,eAAL,GAAuB,CAAC,CAAClC,OAAO,CAACkC,eAAjC,CAHS,CAIT;;MACA,IAAI,CAAC,CAAClC,OAAO,CAACmC,YAAd,EAA4B;QACxB,KAAKA,YAAL,GAAoBnC,OAAO,CAACmC,YAA5B;MACH,CAPQ,CAQT;;;MACA,IAAI,CAAC,CAACnC,OAAO,CAACzE,OAAd,EAAuB;QACnB,KAAKA,OAAL,GAAeyE,OAAO,CAACzE,OAAvB;MACH;;MACD,IAAI,CAAC,CAACyE,OAAO,CAACqC,OAAd,EAAuB;QACnB,KAAKA,OAAL,GAAerC,OAAO,CAACqC,OAAvB;MACH;;MACD,IAAI,CAAC,CAACrC,OAAO,CAACd,MAAd,EAAsB;QAClB,KAAKA,MAAL,GAAcc,OAAO,CAACd,MAAtB;MACH;IACJ,CA9DmC,CA+DpC;;;IACA,IAAI,CAAC,KAAK3D,OAAV,EAAmB;MACf,KAAKA,OAAL,GAAe,IAAIF,WAAJ,EAAf;IACH,CAlEmC,CAmEpC;;;IACA,IAAI,CAAC,KAAKgH,OAAV,EAAmB;MACf,KAAKA,OAAL,GAAe,IAAIrB,WAAJ,EAAf;IACH,CAtEmC,CAuEpC;;;IACA,IAAI,CAAC,KAAK9B,MAAV,EAAkB;MACd,KAAKA,MAAL,GAAc,IAAIa,UAAJ,EAAd;MACA,KAAKuC,aAAL,GAAqBT,GAArB;IACH,CAHD,MAIK;MACD;MACA,MAAM3C,MAAM,GAAG,KAAKA,MAAL,CAAYuB,QAAZ,EAAf;;MACA,IAAIvB,MAAM,CAACjC,MAAP,KAAkB,CAAtB,EAAyB;QACrB;QACA,KAAKqF,aAAL,GAAqBT,GAArB;MACH,CAHD,MAIK;QACD;QACA,MAAMU,IAAI,GAAGV,GAAG,CAAC7F,OAAJ,CAAY,GAAZ,CAAb,CAFC,CAGD;QACA;QACA;QACA;QACA;QACA;QACA;;QACA,MAAMwG,GAAG,GAAGD,IAAI,KAAK,CAAC,CAAV,GAAc,GAAd,GAAqBA,IAAI,GAAGV,GAAG,CAAC5E,MAAJ,GAAa,CAApB,GAAwB,GAAxB,GAA8B,EAA/D;QACA,KAAKqF,aAAL,GAAqBT,GAAG,GAAGW,GAAN,GAAYtD,MAAjC;MACH;IACJ;EACJ;EACD;AACJ;AACA;AACA;;;EACIuD,aAAa,GAAG;IACZ;IACA,IAAI,KAAKT,IAAL,KAAc,IAAlB,EAAwB;MACpB,OAAO,IAAP;IACH,CAJW,CAKZ;IACA;;;IACA,IAAIZ,aAAa,CAAC,KAAKY,IAAN,CAAb,IAA4BV,MAAM,CAAC,KAAKU,IAAN,CAAlC,IAAiDR,UAAU,CAAC,KAAKQ,IAAN,CAA3D,IACAN,iBAAiB,CAAC,KAAKM,IAAN,CADjB,IACgC,OAAO,KAAKA,IAAZ,KAAqB,QADzD,EACmE;MAC/D,OAAO,KAAKA,IAAZ;IACH,CAVW,CAWZ;;;IACA,IAAI,KAAKA,IAAL,YAAqBjC,UAAzB,EAAqC;MACjC,OAAO,KAAKiC,IAAL,CAAUvB,QAAV,EAAP;IACH,CAdW,CAeZ;;;IACA,IAAI,OAAO,KAAKuB,IAAZ,KAAqB,QAArB,IAAiC,OAAO,KAAKA,IAAZ,KAAqB,SAAtD,IACA7E,KAAK,CAACmB,OAAN,CAAc,KAAK0D,IAAnB,CADJ,EAC8B;MAC1B,OAAOU,IAAI,CAACC,SAAL,CAAe,KAAKX,IAApB,CAAP;IACH,CAnBW,CAoBZ;;;IACA,OAAO,KAAKA,IAAL,CAAUvB,QAAV,EAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACImC,uBAAuB,GAAG;IACtB;IACA,IAAI,KAAKZ,IAAL,KAAc,IAAlB,EAAwB;MACpB,OAAO,IAAP;IACH,CAJqB,CAKtB;;;IACA,IAAIR,UAAU,CAAC,KAAKQ,IAAN,CAAd,EAA2B;MACvB,OAAO,IAAP;IACH,CARqB,CAStB;IACA;;;IACA,IAAIV,MAAM,CAAC,KAAKU,IAAN,CAAV,EAAuB;MACnB,OAAO,KAAKA,IAAL,CAAUa,IAAV,IAAkB,IAAzB;IACH,CAbqB,CActB;;;IACA,IAAIzB,aAAa,CAAC,KAAKY,IAAN,CAAjB,EAA8B;MAC1B,OAAO,IAAP;IACH,CAjBqB,CAkBtB;IACA;;;IACA,IAAI,OAAO,KAAKA,IAAZ,KAAqB,QAAzB,EAAmC;MAC/B,OAAO,YAAP;IACH,CAtBqB,CAuBtB;;;IACA,IAAI,KAAKA,IAAL,YAAqBjC,UAAzB,EAAqC;MACjC,OAAO,iDAAP;IACH,CA1BqB,CA2BtB;;;IACA,IAAI,OAAO,KAAKiC,IAAZ,KAAqB,QAArB,IAAiC,OAAO,KAAKA,IAAZ,KAAqB,QAAtD,IACA,OAAO,KAAKA,IAAZ,KAAqB,SADzB,EACoC;MAChC,OAAO,kBAAP;IACH,CA/BqB,CAgCtB;;;IACA,OAAO,IAAP;EACH;;EACDzE,KAAK,CAACK,MAAM,GAAG,EAAV,EAAc;IACf;IACA;IACA,MAAMuD,MAAM,GAAGvD,MAAM,CAACuD,MAAP,IAAiB,KAAKA,MAArC;IACA,MAAMU,GAAG,GAAGjE,MAAM,CAACiE,GAAP,IAAc,KAAKA,GAA/B;IACA,MAAMM,YAAY,GAAGvE,MAAM,CAACuE,YAAP,IAAuB,KAAKA,YAAjD,CALe,CAMf;IACA;IACA;IACA;;IACA,MAAMH,IAAI,GAAIpE,MAAM,CAACoE,IAAP,KAAgB/D,SAAjB,GAA8BL,MAAM,CAACoE,IAArC,GAA4C,KAAKA,IAA9D,CAVe,CAWf;IACA;;IACA,MAAME,eAAe,GAAItE,MAAM,CAACsE,eAAP,KAA2BjE,SAA5B,GAAyCL,MAAM,CAACsE,eAAhD,GAAkE,KAAKA,eAA/F;IACA,MAAMD,cAAc,GAAIrE,MAAM,CAACqE,cAAP,KAA0BhE,SAA3B,GAAwCL,MAAM,CAACqE,cAA/C,GAAgE,KAAKA,cAA5F,CAde,CAef;IACA;;IACA,IAAI1G,OAAO,GAAGqC,MAAM,CAACrC,OAAP,IAAkB,KAAKA,OAArC;IACA,IAAI2D,MAAM,GAAGtB,MAAM,CAACsB,MAAP,IAAiB,KAAKA,MAAnC,CAlBe,CAmBf;;IACA,MAAMmD,OAAO,GAAGzE,MAAM,CAACyE,OAAP,IAAkB,KAAKA,OAAvC,CApBe,CAqBf;;IACA,IAAIzE,MAAM,CAACkF,UAAP,KAAsB7E,SAA1B,EAAqC;MACjC;MACA1C,OAAO,GACHuB,MAAM,CAACC,IAAP,CAAYa,MAAM,CAACkF,UAAnB,EACKC,MADL,CACY,CAACxH,OAAD,EAAUU,IAAV,KAAmBV,OAAO,CAACoB,GAAR,CAAYV,IAAZ,EAAkB2B,MAAM,CAACkF,UAAP,CAAkB7G,IAAlB,CAAlB,CAD/B,EAC2EV,OAD3E,CADJ;IAGH,CA3Bc,CA4Bf;;;IACA,IAAIqC,MAAM,CAACoF,SAAX,EAAsB;MAClB;MACA9D,MAAM,GAAGpC,MAAM,CAACC,IAAP,CAAYa,MAAM,CAACoF,SAAnB,EACJD,MADI,CACG,CAAC7D,MAAD,EAASE,KAAT,KAAmBF,MAAM,CAACvC,GAAP,CAAWyC,KAAX,EAAkBxB,MAAM,CAACoF,SAAP,CAAiB5D,KAAjB,CAAlB,CADtB,EACkEF,MADlE,CAAT;IAEH,CAjCc,CAkCf;;;IACA,OAAO,IAAI0C,WAAJ,CAAgBT,MAAhB,EAAwBU,GAAxB,EAA6BG,IAA7B,EAAmC;MACtC9C,MADsC;MAEtC3D,OAFsC;MAGtC8G,OAHsC;MAItCJ,cAJsC;MAKtCE,YALsC;MAMtCD;IANsC,CAAnC,CAAP;EAQH;;AAlNa;AAqNlB;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAIe,aAAJ;;AACA,CAAC,UAAUA,aAAV,EAAyB;EACtB;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,MAAD,CAAb,GAAwB,CAAzB,CAAb,GAA2C,MAA3C;EACA;AACJ;AACA;;EACIA,aAAa,CAACA,aAAa,CAAC,gBAAD,CAAb,GAAkC,CAAnC,CAAb,GAAqD,gBAArD;EACA;AACJ;AACA;;EACIA,aAAa,CAACA,aAAa,CAAC,gBAAD,CAAb,GAAkC,CAAnC,CAAb,GAAqD,gBAArD;EACA;AACJ;AACA;;EACIA,aAAa,CAACA,aAAa,CAAC,kBAAD,CAAb,GAAoC,CAArC,CAAb,GAAuD,kBAAvD;EACA;AACJ;AACA;;EACIA,aAAa,CAACA,aAAa,CAAC,UAAD,CAAb,GAA4B,CAA7B,CAAb,GAA+C,UAA/C;EACA;AACJ;AACA;;EACIA,aAAa,CAACA,aAAa,CAAC,MAAD,CAAb,GAAwB,CAAzB,CAAb,GAA2C,MAA3C;AACH,CAzBD,EAyBGA,aAAa,KAAKA,aAAa,GAAG,EAArB,CAzBhB;AA0BA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,gBAAN,CAAuB;EACnB;AACJ;AACA;AACA;AACA;AACA;EACI5H,WAAW,CAAC4B,IAAD,EAAOiG,aAAa,GAAG;EAAI;EAA3B,EAAoDC,iBAAiB,GAAG,IAAxE,EAA8E;IACrF;IACA;IACA,KAAK7H,OAAL,GAAe2B,IAAI,CAAC3B,OAAL,IAAgB,IAAIF,WAAJ,EAA/B;IACA,KAAKgI,MAAL,GAAcnG,IAAI,CAACmG,MAAL,KAAgBpF,SAAhB,GAA4Bf,IAAI,CAACmG,MAAjC,GAA0CF,aAAxD;IACA,KAAKG,UAAL,GAAkBpG,IAAI,CAACoG,UAAL,IAAmBF,iBAArC;IACA,KAAKvB,GAAL,GAAW3E,IAAI,CAAC2E,GAAL,IAAY,IAAvB,CANqF,CAOrF;;IACA,KAAK0B,EAAL,GAAU,KAAKF,MAAL,IAAe,GAAf,IAAsB,KAAKA,MAAL,GAAc,GAA9C;EACH;;AAhBkB;AAkBvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMG,kBAAN,SAAiCN,gBAAjC,CAAkD;EAC9C;AACJ;AACA;EACI5H,WAAW,CAAC4B,IAAI,GAAG,EAAR,EAAY;IACnB,MAAMA,IAAN;IACA,KAAK2F,IAAL,GAAYI,aAAa,CAACQ,cAA1B;EACH;EACD;AACJ;AACA;AACA;;;EACIlG,KAAK,CAACK,MAAM,GAAG,EAAV,EAAc;IACf;IACA;IACA,OAAO,IAAI4F,kBAAJ,CAAuB;MAC1BjI,OAAO,EAAEqC,MAAM,CAACrC,OAAP,IAAkB,KAAKA,OADN;MAE1B8H,MAAM,EAAEzF,MAAM,CAACyF,MAAP,KAAkBpF,SAAlB,GAA8BL,MAAM,CAACyF,MAArC,GAA8C,KAAKA,MAFjC;MAG1BC,UAAU,EAAE1F,MAAM,CAAC0F,UAAP,IAAqB,KAAKA,UAHZ;MAI1BzB,GAAG,EAAEjE,MAAM,CAACiE,GAAP,IAAc,KAAKA,GAAnB,IAA0B5D;IAJL,CAAvB,CAAP;EAMH;;AArB6C;AAuBlD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMyF,YAAN,SAA2BR,gBAA3B,CAA4C;EACxC;AACJ;AACA;EACI5H,WAAW,CAAC4B,IAAI,GAAG,EAAR,EAAY;IACnB,MAAMA,IAAN;IACA,KAAK2F,IAAL,GAAYI,aAAa,CAACU,QAA1B;IACA,KAAK3B,IAAL,GAAY9E,IAAI,CAAC8E,IAAL,KAAc/D,SAAd,GAA0Bf,IAAI,CAAC8E,IAA/B,GAAsC,IAAlD;EACH;;EACDzE,KAAK,CAACK,MAAM,GAAG,EAAV,EAAc;IACf,OAAO,IAAI8F,YAAJ,CAAiB;MACpB1B,IAAI,EAAGpE,MAAM,CAACoE,IAAP,KAAgB/D,SAAjB,GAA8BL,MAAM,CAACoE,IAArC,GAA4C,KAAKA,IADnC;MAEpBzG,OAAO,EAAEqC,MAAM,CAACrC,OAAP,IAAkB,KAAKA,OAFZ;MAGpB8H,MAAM,EAAGzF,MAAM,CAACyF,MAAP,KAAkBpF,SAAnB,GAAgCL,MAAM,CAACyF,MAAvC,GAAgD,KAAKA,MAHzC;MAIpBC,UAAU,EAAE1F,MAAM,CAAC0F,UAAP,IAAqB,KAAKA,UAJlB;MAKpBzB,GAAG,EAAEjE,MAAM,CAACiE,GAAP,IAAc,KAAKA,GAAnB,IAA0B5D;IALX,CAAjB,CAAP;EAOH;;AAjBuC;AAmB5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM2F,iBAAN,SAAgCV,gBAAhC,CAAiD;EAC7C5H,WAAW,CAAC4B,IAAD,EAAO;IACd;IACA,MAAMA,IAAN,EAAY,CAAZ,EAAe,eAAf;IACA,KAAKjB,IAAL,GAAY,mBAAZ;IACA;AACR;AACA;;IACQ,KAAKsH,EAAL,GAAU,KAAV,CAPc,CAQd;IACA;IACA;;IACA,IAAI,KAAKF,MAAL,IAAe,GAAf,IAAsB,KAAKA,MAAL,GAAc,GAAxC,EAA6C;MACzC,KAAKQ,OAAL,GAAgB,mCAAkC3G,IAAI,CAAC2E,GAAL,IAAY,eAAgB,EAA9E;IACH,CAFD,MAGK;MACD,KAAKgC,OAAL,GAAgB,6BAA4B3G,IAAI,CAAC2E,GAAL,IAAY,eAAgB,KAAI3E,IAAI,CAACmG,MAAO,IAAGnG,IAAI,CAACoG,UAAW,EAA3G;IACH;;IACD,KAAKQ,KAAL,GAAa5G,IAAI,CAAC4G,KAAL,IAAc,IAA3B;EACH;;AAnB4C;AAsBjD;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,OAAT,CAAiB/D,OAAjB,EAA0BgC,IAA1B,EAAgC;EAC5B,OAAO;IACHA,IADG;IAEHzG,OAAO,EAAEyE,OAAO,CAACzE,OAFd;IAGH8G,OAAO,EAAErC,OAAO,CAACqC,OAHd;IAIH2B,OAAO,EAAEhE,OAAO,CAACgE,OAJd;IAKH9E,MAAM,EAAEc,OAAO,CAACd,MALb;IAMH+C,cAAc,EAAEjC,OAAO,CAACiC,cANrB;IAOHE,YAAY,EAAEnC,OAAO,CAACmC,YAPnB;IAQHD,eAAe,EAAElC,OAAO,CAACkC;EARtB,CAAP;AAUH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM+B,UAAN,CAAiB;EACb3I,WAAW,CAAC4I,OAAD,EAAU;IACjB,KAAKA,OAAL,GAAeA,OAAf;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIC,OAAO,CAACC,KAAD,EAAQvC,GAAR,EAAa7B,OAAO,GAAG,EAAvB,EAA2B;IAC9B,IAAIqE,GAAJ,CAD8B,CAE9B;;IACA,IAAID,KAAK,YAAYxC,WAArB,EAAkC;MAC9B;MACA;MACAyC,GAAG,GAAGD,KAAN;IACH,CAJD,MAKK;MACD;MACA;MACA;MACA;MACA,IAAI7I,OAAO,GAAG0C,SAAd;;MACA,IAAI+B,OAAO,CAACzE,OAAR,YAA2BF,WAA/B,EAA4C;QACxCE,OAAO,GAAGyE,OAAO,CAACzE,OAAlB;MACH,CAFD,MAGK;QACDA,OAAO,GAAG,IAAIF,WAAJ,CAAgB2E,OAAO,CAACzE,OAAxB,CAAV;MACH,CAXA,CAYD;;;MACA,IAAI2D,MAAM,GAAGjB,SAAb;;MACA,IAAI,CAAC,CAAC+B,OAAO,CAACd,MAAd,EAAsB;QAClB,IAAIc,OAAO,CAACd,MAAR,YAA0Ba,UAA9B,EAA0C;UACtCb,MAAM,GAAGc,OAAO,CAACd,MAAjB;QACH,CAFD,MAGK;UACDA,MAAM,GAAG,IAAIa,UAAJ,CAAe;YAAEM,UAAU,EAAEL,OAAO,CAACd;UAAtB,CAAf,CAAT;QACH;MACJ,CArBA,CAsBD;;;MACAmF,GAAG,GAAG,IAAIzC,WAAJ,CAAgBwC,KAAhB,EAAuBvC,GAAvB,EAA6B7B,OAAO,CAACgC,IAAR,KAAiB/D,SAAjB,GAA6B+B,OAAO,CAACgC,IAArC,GAA4C,IAAzE,EAAgF;QAClFzG,OADkF;QAElF8G,OAAO,EAAErC,OAAO,CAACqC,OAFiE;QAGlFnD,MAHkF;QAIlF+C,cAAc,EAAEjC,OAAO,CAACiC,cAJ0D;QAKlF;QACAE,YAAY,EAAEnC,OAAO,CAACmC,YAAR,IAAwB,MAN4C;QAOlFD,eAAe,EAAElC,OAAO,CAACkC;MAPyD,CAAhF,CAAN;IASH,CAxC6B,CAyC9B;IACA;IACA;IACA;;;IACA,MAAMoC,OAAO,GAAGxJ,EAAE,CAACuJ,GAAD,CAAF,CAAQE,IAAR,CAAavJ,SAAS,CAAEqJ,GAAD,IAAS,KAAKH,OAAL,CAAaM,MAAb,CAAoBH,GAApB,CAAV,CAAtB,CAAhB,CA7C8B,CA8C9B;IACA;IACA;;IACA,IAAID,KAAK,YAAYxC,WAAjB,IAAgC5B,OAAO,CAACgE,OAAR,KAAoB,QAAxD,EAAkE;MAC9D,OAAOM,OAAP;IACH,CAnD6B,CAoD9B;IACA;IACA;;;IACA,MAAMG,IAAI,GAAGH,OAAO,CAACC,IAAR,CAAatJ,MAAM,CAAEyJ,KAAD,IAAWA,KAAK,YAAYhB,YAA7B,CAAnB,CAAb,CAvD8B,CAwD9B;;IACA,QAAQ1D,OAAO,CAACgE,OAAR,IAAmB,MAA3B;MACI,KAAK,MAAL;QACI;QACA;QACA;QACA;QACA;QACA,QAAQK,GAAG,CAAClC,YAAZ;UACI,KAAK,aAAL;YACI,OAAOsC,IAAI,CAACF,IAAL,CAAUrJ,GAAG,CAAEoF,GAAD,IAAS;cAC1B;cACA,IAAIA,GAAG,CAAC0B,IAAJ,KAAa,IAAb,IAAqB,EAAE1B,GAAG,CAAC0B,IAAJ,YAAoBX,WAAtB,CAAzB,EAA6D;gBACzD,MAAM,IAAI9C,KAAJ,CAAU,iCAAV,CAAN;cACH;;cACD,OAAO+B,GAAG,CAAC0B,IAAX;YACH,CANmB,CAAb,CAAP;;UAOJ,KAAK,MAAL;YACI,OAAOyC,IAAI,CAACF,IAAL,CAAUrJ,GAAG,CAAEoF,GAAD,IAAS;cAC1B;cACA,IAAIA,GAAG,CAAC0B,IAAJ,KAAa,IAAb,IAAqB,EAAE1B,GAAG,CAAC0B,IAAJ,YAAoBT,IAAtB,CAAzB,EAAsD;gBAClD,MAAM,IAAIhD,KAAJ,CAAU,yBAAV,CAAN;cACH;;cACD,OAAO+B,GAAG,CAAC0B,IAAX;YACH,CANmB,CAAb,CAAP;;UAOJ,KAAK,MAAL;YACI,OAAOyC,IAAI,CAACF,IAAL,CAAUrJ,GAAG,CAAEoF,GAAD,IAAS;cAC1B;cACA,IAAIA,GAAG,CAAC0B,IAAJ,KAAa,IAAb,IAAqB,OAAO1B,GAAG,CAAC0B,IAAX,KAAoB,QAA7C,EAAuD;gBACnD,MAAM,IAAIzD,KAAJ,CAAU,2BAAV,CAAN;cACH;;cACD,OAAO+B,GAAG,CAAC0B,IAAX;YACH,CANmB,CAAb,CAAP;;UAOJ,KAAK,MAAL;UACA;YACI;YACA,OAAOyC,IAAI,CAACF,IAAL,CAAUrJ,GAAG,CAAEoF,GAAD,IAASA,GAAG,CAAC0B,IAAd,CAAb,CAAP;QA5BR;;MA8BJ,KAAK,UAAL;QACI;QACA,OAAOyC,IAAP;;MACJ;QACI;QACA,MAAM,IAAIlG,KAAJ,CAAW,uCAAsCyB,OAAO,CAACgE,OAAQ,GAAjE,CAAN;IA1CR;EA4CH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIvG,MAAM,CAACoE,GAAD,EAAM7B,OAAO,GAAG,EAAhB,EAAoB;IACtB,OAAO,KAAKmE,OAAL,CAAa,QAAb,EAAuBtC,GAAvB,EAA4B7B,OAA5B,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIvD,GAAG,CAACoF,GAAD,EAAM7B,OAAO,GAAG,EAAhB,EAAoB;IACnB,OAAO,KAAKmE,OAAL,CAAa,KAAb,EAAoBtC,GAApB,EAAyB7B,OAAzB,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACI2E,IAAI,CAAC9C,GAAD,EAAM7B,OAAO,GAAG,EAAhB,EAAoB;IACpB,OAAO,KAAKmE,OAAL,CAAa,MAAb,EAAqBtC,GAArB,EAA0B7B,OAA1B,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACI4E,KAAK,CAAC/C,GAAD,EAAMgD,aAAN,EAAqB;IACtB,OAAO,KAAKV,OAAL,CAAa,OAAb,EAAsBtC,GAAtB,EAA2B;MAC9B3C,MAAM,EAAE,IAAIa,UAAJ,GAAiBzC,MAAjB,CAAwBuH,aAAxB,EAAuC,gBAAvC,CADsB;MAE9Bb,OAAO,EAAE,MAFqB;MAG9B7B,YAAY,EAAE;IAHgB,CAA3B,CAAP;EAKH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACInC,OAAO,CAAC6B,GAAD,EAAM7B,OAAO,GAAG,EAAhB,EAAoB;IACvB,OAAO,KAAKmE,OAAL,CAAa,SAAb,EAAwBtC,GAAxB,EAA6B7B,OAA7B,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACI8E,KAAK,CAACjD,GAAD,EAAMG,IAAN,EAAYhC,OAAO,GAAG,EAAtB,EAA0B;IAC3B,OAAO,KAAKmE,OAAL,CAAa,OAAb,EAAsBtC,GAAtB,EAA2BkC,OAAO,CAAC/D,OAAD,EAAUgC,IAAV,CAAlC,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACI+C,IAAI,CAAClD,GAAD,EAAMG,IAAN,EAAYhC,OAAO,GAAG,EAAtB,EAA0B;IAC1B,OAAO,KAAKmE,OAAL,CAAa,MAAb,EAAqBtC,GAArB,EAA0BkC,OAAO,CAAC/D,OAAD,EAAUgC,IAAV,CAAjC,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIgD,GAAG,CAACnD,GAAD,EAAMG,IAAN,EAAYhC,OAAO,GAAG,EAAtB,EAA0B;IACzB,OAAO,KAAKmE,OAAL,CAAa,KAAb,EAAoBtC,GAApB,EAAyBkC,OAAO,CAAC/D,OAAD,EAAUgC,IAAV,CAAhC,CAAP;EACH;;AA9NY;;AAgOjBiC,UAAU,CAACgB,IAAX;EAAA,iBAAuGhB,UAAvG,EAA6FzJ,EAA7F,UAAmIW,WAAnI;AAAA;;AACA8I,UAAU,CAACiB,KAAX,kBAD6F1K,EAC7F;EAAA,OAA2GyJ,UAA3G;EAAA,SAA2GA,UAA3G;AAAA;;AACA;EAAA,mDAF6FzJ,EAE7F,mBAA2FyJ,UAA3F,EAAmH,CAAC;IACxGpB,IAAI,EAAEpI;EADkG,CAAD,CAAnH,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAEoI,IAAI,EAAE1H;IAAR,CAAD,CAAP;EAAiC,CAF3E;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMgK,sBAAN,CAA6B;EACzB7J,WAAW,CAAC8J,IAAD,EAAOC,WAAP,EAAoB;IAC3B,KAAKD,IAAL,GAAYA,IAAZ;IACA,KAAKC,WAAL,GAAmBA,WAAnB;EACH;;EACDb,MAAM,CAACH,GAAD,EAAM;IACR,OAAO,KAAKgB,WAAL,CAAiBC,SAAjB,CAA2BjB,GAA3B,EAAgC,KAAKe,IAArC,CAAP;EACH;;AAPwB;AAS7B;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMG,iBAAiB,GAAG,IAAI7K,cAAJ,CAAmB,mBAAnB,CAA1B;;AACA,MAAM8K,eAAN,CAAsB;EAClBF,SAAS,CAACjB,GAAD,EAAMe,IAAN,EAAY;IACjB,OAAOA,IAAI,CAACZ,MAAL,CAAYH,GAAZ,CAAP;EACH;;AAHiB;;AAKtBmB,eAAe,CAACP,IAAhB;EAAA,iBAA4GO,eAA5G;AAAA;;AACAA,eAAe,CAACN,KAAhB,kBAxC6F1K,EAwC7F;EAAA,OAAgHgL,eAAhH;EAAA,SAAgHA,eAAhH;AAAA;;AACA;EAAA,mDAzC6FhL,EAyC7F,mBAA2FgL,eAA3F,EAAwH,CAAC;IAC7G3C,IAAI,EAAEpI;EADuG,CAAD,CAAxH;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAIgL,aAAa,GAAG,CAApB;AACA;AACA;AACA;AACA;;AACA,IAAIC,eAAJ,C,CACA;AACA;;AACA,MAAMC,qBAAqB,GAAG,gDAA9B,C,CACA;AACA;;AACA,MAAMC,sBAAsB,GAAG,+CAA/B;AACA,MAAMC,6BAA6B,GAAG,6CAAtC,C,CACA;AACA;;AACA,MAAMC,+BAA+B,GAAG,wCAAxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,oBAAN,CAA2B;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,kBAAN,CAAyB;EACrB1K,WAAW,CAAC2K,WAAD,EAAcC,QAAd,EAAwB;IAC/B,KAAKD,WAAL,GAAmBA,WAAnB;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA;AACR;AACA;;IACQ,KAAKC,eAAL,GAAuBC,OAAO,CAACC,OAAR,EAAvB;EACH;EACD;AACJ;AACA;;;EACIC,YAAY,GAAG;IACX,OAAQ,qBAAoBb,aAAa,EAAG,EAA5C;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIjB,MAAM,CAACH,GAAD,EAAM;IACR;IACA;IACA,IAAIA,GAAG,CAAClD,MAAJ,KAAe,OAAnB,EAA4B;MACxB,MAAM,IAAI5C,KAAJ,CAAUqH,sBAAV,CAAN;IACH,CAFD,MAGK,IAAIvB,GAAG,CAAClC,YAAJ,KAAqB,MAAzB,EAAiC;MAClC,MAAM,IAAI5D,KAAJ,CAAUsH,6BAAV,CAAN;IACH,CARO,CASR;IACA;;;IACA,IAAIxB,GAAG,CAAC9I,OAAJ,CAAYwB,IAAZ,GAAmBE,MAAnB,GAA4B,CAAhC,EAAmC;MAC/B,MAAM,IAAIsB,KAAJ,CAAUuH,+BAAV,CAAN;IACH,CAbO,CAcR;;;IACA,OAAO,IAAI/K,UAAJ,CAAgBwL,QAAD,IAAc;MAChC;MACA;MACA;MACA,MAAMC,QAAQ,GAAG,KAAKF,YAAL,EAAjB;MACA,MAAMzE,GAAG,GAAGwC,GAAG,CAAC/B,aAAJ,CAAkBnD,OAAlB,CAA0B,sBAA1B,EAAmD,IAAGqH,QAAS,IAA/D,CAAZ,CALgC,CAMhC;;MACA,MAAMC,IAAI,GAAG,KAAKP,QAAL,CAAcQ,aAAd,CAA4B,QAA5B,CAAb;MACAD,IAAI,CAACE,GAAL,GAAW9E,GAAX,CARgC,CAShC;MACA;MACA;;MACA,IAAIG,IAAI,GAAG,IAAX,CAZgC,CAahC;;MACA,IAAI4E,QAAQ,GAAG,KAAf,CAdgC,CAehC;MACA;MACA;;MACA,KAAKX,WAAL,CAAiBO,QAAjB,IAA8BK,IAAD,IAAU;QACnC;QACA,OAAO,KAAKZ,WAAL,CAAiBO,QAAjB,CAAP,CAFmC,CAGnC;;QACAxE,IAAI,GAAG6E,IAAP;QACAD,QAAQ,GAAG,IAAX;MACH,CAND,CAlBgC,CAyBhC;MACA;MACA;;;MACA,MAAME,OAAO,GAAG,MAAM;QAClB;QACA,IAAIL,IAAI,CAACM,UAAT,EAAqB;UACjBN,IAAI,CAACM,UAAL,CAAgBC,WAAhB,CAA4BP,IAA5B;QACH,CAJiB,CAKlB;QACA;;;QACA,OAAO,KAAKR,WAAL,CAAiBO,QAAjB,CAAP;MACH,CARD,CA5BgC,CAqChC;MACA;MACA;MACA;;;MACA,MAAMS,MAAM,GAAIvC,KAAD,IAAW;QACtB;QACA;QACA;QACA,KAAKyB,eAAL,CAAqBe,IAArB,CAA0B,MAAM;UAC5B;UACAJ,OAAO,GAFqB,CAG5B;;UACA,IAAI,CAACF,QAAL,EAAe;YACX;YACA;YACAL,QAAQ,CAACzC,KAAT,CAAe,IAAIF,iBAAJ,CAAsB;cACjC/B,GADiC;cAEjCwB,MAAM,EAAE,CAFyB;cAGjCC,UAAU,EAAE,aAHqB;cAIjCQ,KAAK,EAAE,IAAIvF,KAAJ,CAAUoH,qBAAV;YAJ0B,CAAtB,CAAf;YAMA;UACH,CAd2B,CAe5B;UACA;;;UACAY,QAAQ,CAACnB,IAAT,CAAc,IAAI1B,YAAJ,CAAiB;YAC3B1B,IAD2B;YAE3BqB,MAAM,EAAE;YAAI;YAFe;YAG3BC,UAAU,EAAE,IAHe;YAI3BzB;UAJ2B,CAAjB,CAAd,EAjB4B,CAuB5B;;UACA0E,QAAQ,CAACY,QAAT;QACH,CAzBD;MA0BH,CA9BD,CAzCgC,CAwEhC;MACA;MACA;;;MACA,MAAMC,OAAO,GAAItD,KAAD,IAAW;QACvBgD,OAAO,GADgB,CAEvB;;QACAP,QAAQ,CAACzC,KAAT,CAAe,IAAIF,iBAAJ,CAAsB;UACjCE,KADiC;UAEjCT,MAAM,EAAE,CAFyB;UAGjCC,UAAU,EAAE,aAHqB;UAIjCzB;QAJiC,CAAtB,CAAf;MAMH,CATD,CA3EgC,CAqFhC;MACA;;;MACA4E,IAAI,CAACY,gBAAL,CAAsB,MAAtB,EAA8BJ,MAA9B;MACAR,IAAI,CAACY,gBAAL,CAAsB,OAAtB,EAA+BD,OAA/B;MACA,KAAKlB,QAAL,CAAclE,IAAd,CAAmBsF,WAAnB,CAA+Bb,IAA/B,EAzFgC,CA0FhC;;MACAF,QAAQ,CAACnB,IAAT,CAAc;QAAEvC,IAAI,EAAEI,aAAa,CAACsE;MAAtB,CAAd,EA3FgC,CA4FhC;;MACA,OAAO,MAAM;QACT,IAAI,CAACX,QAAL,EAAe;UACX,KAAKY,eAAL,CAAqBf,IAArB;QACH,CAHQ,CAIT;;;QACAK,OAAO;MACV,CAND;IAOH,CApGM,CAAP;EAqGH;;EACDU,eAAe,CAACC,MAAD,EAAS;IACpB;IACA;IACA;IACA,IAAI,CAAC/B,eAAL,EAAsB;MAClBA,eAAe,GAAG,KAAKQ,QAAL,CAAcwB,cAAd,CAA6BC,kBAA7B,EAAlB;IACH;;IACDjC,eAAe,CAACkC,SAAhB,CAA0BH,MAA1B;EACH;;AAlJoB;;AAoJzBzB,kBAAkB,CAACf,IAAnB;EAAA,iBAA+Ge,kBAA/G,EA7O6FxL,EA6O7F,UAAmJuL,oBAAnJ,GA7O6FvL,EA6O7F,UAAoLJ,QAApL;AAAA;;AACA4L,kBAAkB,CAACd,KAAnB,kBA9O6F1K,EA8O7F;EAAA,OAAmHwL,kBAAnH;EAAA,SAAmHA,kBAAnH;AAAA;;AACA;EAAA,mDA/O6FxL,EA+O7F,mBAA2FwL,kBAA3F,EAA2H,CAAC;IAChHnD,IAAI,EAAEpI;EAD0G,CAAD,CAA3H,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAEoI,IAAI,EAAEkD;IAAR,CAAD,EAAiC;MAAElD,IAAI,EAAE5E,SAAR;MAAmB4J,UAAU,EAAE,CAAC;QAC9FhF,IAAI,EAAElI,MADwF;QAE9FmN,IAAI,EAAE,CAAC1N,QAAD;MAFwF,CAAD;IAA/B,CAAjC,CAAP;EAGlB,CALxB;AAAA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM2N,gBAAN,CAAuB;EACnBzM,WAAW,CAACsJ,KAAD,EAAQ;IACf,KAAKA,KAAL,GAAaA,KAAb;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACIU,SAAS,CAACjB,GAAD,EAAMe,IAAN,EAAY;IACjB,IAAIf,GAAG,CAAClD,MAAJ,KAAe,OAAnB,EAA4B;MACxB,OAAO,KAAKyD,KAAL,CAAWJ,MAAX,CAAkBH,GAAlB,CAAP;IACH,CAHgB,CAIjB;;;IACA,OAAOe,IAAI,CAACZ,MAAL,CAAYH,GAAZ,CAAP;EACH;;AAjBkB;;AAmBvB0D,gBAAgB,CAAC9C,IAAjB;EAAA,iBAA6G8C,gBAA7G,EAhR6FvN,EAgR7F,UAA+IwL,kBAA/I;AAAA;;AACA+B,gBAAgB,CAAC7C,KAAjB,kBAjR6F1K,EAiR7F;EAAA,OAAiHuN,gBAAjH;EAAA,SAAiHA,gBAAjH;AAAA;;AACA;EAAA,mDAlR6FvN,EAkR7F,mBAA2FuN,gBAA3F,EAAyH,CAAC;IAC9GlF,IAAI,EAAEpI;EADwG,CAAD,CAAzH,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAEoI,IAAI,EAAEmD;IAAR,CAAD,CAAP;EAAwC,CAFlF;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMgC,WAAW,GAAG,cAApB;AACA;AACA;AACA;AACA;;AACA,SAASC,cAAT,CAAwBC,GAAxB,EAA6B;EACzB,IAAI,iBAAiBA,GAAjB,IAAwBA,GAAG,CAACC,WAAhC,EAA6C;IACzC,OAAOD,GAAG,CAACC,WAAX;EACH;;EACD,IAAI,mBAAmBC,IAAnB,CAAwBF,GAAG,CAACG,qBAAJ,EAAxB,CAAJ,EAA0D;IACtD,OAAOH,GAAG,CAACI,iBAAJ,CAAsB,eAAtB,CAAP;EACH;;EACD,OAAO,IAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,cAAN,CAAqB;EACjBjN,WAAW,CAACkN,UAAD,EAAa;IACpB,KAAKA,UAAL,GAAkBA,UAAlB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIhE,MAAM,CAACH,GAAD,EAAM;IACR;IACA;IACA,IAAIA,GAAG,CAAClD,MAAJ,KAAe,OAAnB,EAA4B;MACxB,MAAM,IAAI5C,KAAJ,CAAW,+EAAX,CAAN;IACH,CALO,CAMR;;;IACA,OAAO,IAAIxD,UAAJ,CAAgBwL,QAAD,IAAc;MAChC;MACA,MAAM2B,GAAG,GAAG,KAAKM,UAAL,CAAgBC,KAAhB,EAAZ;MACAP,GAAG,CAACQ,IAAJ,CAASrE,GAAG,CAAClD,MAAb,EAAqBkD,GAAG,CAAC/B,aAAzB;;MACA,IAAI,CAAC,CAAC+B,GAAG,CAACnC,eAAV,EAA2B;QACvBgG,GAAG,CAAChG,eAAJ,GAAsB,IAAtB;MACH,CAN+B,CAOhC;;;MACAmC,GAAG,CAAC9I,OAAJ,CAAYM,OAAZ,CAAoB,CAACI,IAAD,EAAOe,MAAP,KAAkBkL,GAAG,CAACS,gBAAJ,CAAqB1M,IAArB,EAA2Be,MAAM,CAAC2D,IAAP,CAAY,GAAZ,CAA3B,CAAtC,EARgC,CAShC;;MACA,IAAI,CAAC0D,GAAG,CAAC9I,OAAJ,CAAYiB,GAAZ,CAAgB,QAAhB,CAAL,EAAgC;QAC5B0L,GAAG,CAACS,gBAAJ,CAAqB,QAArB,EAA+B,mCAA/B;MACH,CAZ+B,CAahC;;;MACA,IAAI,CAACtE,GAAG,CAAC9I,OAAJ,CAAYiB,GAAZ,CAAgB,cAAhB,CAAL,EAAsC;QAClC,MAAMoM,YAAY,GAAGvE,GAAG,CAACzB,uBAAJ,EAArB,CADkC,CAElC;;QACA,IAAIgG,YAAY,KAAK,IAArB,EAA2B;UACvBV,GAAG,CAACS,gBAAJ,CAAqB,cAArB,EAAqCC,YAArC;QACH;MACJ,CApB+B,CAqBhC;;;MACA,IAAIvE,GAAG,CAAClC,YAAR,EAAsB;QAClB,MAAMA,YAAY,GAAGkC,GAAG,CAAClC,YAAJ,CAAiB/F,WAAjB,EAArB,CADkB,CAElB;QACA;QACA;QACA;QACA;;QACA8L,GAAG,CAAC/F,YAAJ,GAAqBA,YAAY,KAAK,MAAlB,GAA4BA,YAA5B,GAA2C,MAA/D;MACH,CA9B+B,CA+BhC;;;MACA,MAAM0G,OAAO,GAAGxE,GAAG,CAAC5B,aAAJ,EAAhB,CAhCgC,CAiChC;MACA;MACA;MACA;MACA;MACA;;MACA,IAAIqG,cAAc,GAAG,IAArB,CAvCgC,CAwChC;MACA;;MACA,MAAMC,cAAc,GAAG,MAAM;QACzB,IAAID,cAAc,KAAK,IAAvB,EAA6B;UACzB,OAAOA,cAAP;QACH;;QACD,MAAMxF,UAAU,GAAG4E,GAAG,CAAC5E,UAAJ,IAAkB,IAArC,CAJyB,CAKzB;;QACA,MAAM/H,OAAO,GAAG,IAAIF,WAAJ,CAAgB6M,GAAG,CAACG,qBAAJ,EAAhB,CAAhB,CANyB,CAOzB;QACA;;QACA,MAAMxG,GAAG,GAAGoG,cAAc,CAACC,GAAD,CAAd,IAAuB7D,GAAG,CAACxC,GAAvC,CATyB,CAUzB;;QACAiH,cAAc,GAAG,IAAItF,kBAAJ,CAAuB;UAAEjI,OAAF;UAAW8H,MAAM,EAAE6E,GAAG,CAAC7E,MAAvB;UAA+BC,UAA/B;UAA2CzB;QAA3C,CAAvB,CAAjB;QACA,OAAOiH,cAAP;MACH,CAbD,CA1CgC,CAwDhC;MACA;MACA;;;MACA,MAAM7B,MAAM,GAAG,MAAM;QACjB;QACA,IAAI;UAAE1L,OAAF;UAAW8H,MAAX;UAAmBC,UAAnB;UAA+BzB;QAA/B,IAAuCkH,cAAc,EAAzD,CAFiB,CAGjB;;QACA,IAAI/G,IAAI,GAAG,IAAX;;QACA,IAAIqB,MAAM,KAAK;QAAI;QAAnB,EAAmD;UAC/C;UACArB,IAAI,GAAI,OAAOkG,GAAG,CAACc,QAAX,KAAwB,WAAzB,GAAwCd,GAAG,CAACe,YAA5C,GAA2Df,GAAG,CAACc,QAAtE;QACH,CARgB,CASjB;;;QACA,IAAI3F,MAAM,KAAK,CAAf,EAAkB;UACdA,MAAM,GAAG,CAAC,CAACrB,IAAF,GAAS;UAAI;UAAb,EAAuC,CAAhD;QACH,CAZgB,CAajB;QACA;QACA;QACA;;;QACA,IAAIuB,EAAE,GAAGF,MAAM,IAAI,GAAV,IAAiBA,MAAM,GAAG,GAAnC,CAjBiB,CAkBjB;QACA;;QACA,IAAIgB,GAAG,CAAClC,YAAJ,KAAqB,MAArB,IAA+B,OAAOH,IAAP,KAAgB,QAAnD,EAA6D;UACzD;UACA,MAAMkH,YAAY,GAAGlH,IAArB;UACAA,IAAI,GAAGA,IAAI,CAAC7C,OAAL,CAAa6I,WAAb,EAA0B,EAA1B,CAAP;;UACA,IAAI;YACA;YACAhG,IAAI,GAAGA,IAAI,KAAK,EAAT,GAAcU,IAAI,CAACyG,KAAL,CAAWnH,IAAX,CAAd,GAAiC,IAAxC;UACH,CAHD,CAIA,OAAO8B,KAAP,EAAc;YACV;YACA;YACA;YACA9B,IAAI,GAAGkH,YAAP,CAJU,CAKV;YACA;;YACA,IAAI3F,EAAJ,EAAQ;cACJ;cACAA,EAAE,GAAG,KAAL,CAFI,CAGJ;;cACAvB,IAAI,GAAG;gBAAE8B,KAAF;gBAASsF,IAAI,EAAEpH;cAAf,CAAP;YACH;UACJ;QACJ;;QACD,IAAIuB,EAAJ,EAAQ;UACJ;UACAgD,QAAQ,CAACnB,IAAT,CAAc,IAAI1B,YAAJ,CAAiB;YAC3B1B,IAD2B;YAE3BzG,OAF2B;YAG3B8H,MAH2B;YAI3BC,UAJ2B;YAK3BzB,GAAG,EAAEA,GAAG,IAAI5D;UALe,CAAjB,CAAd,EAFI,CASJ;UACA;;UACAsI,QAAQ,CAACY,QAAT;QACH,CAZD,MAaK;UACD;UACAZ,QAAQ,CAACzC,KAAT,CAAe,IAAIF,iBAAJ,CAAsB;YACjC;YACAE,KAAK,EAAE9B,IAF0B;YAGjCzG,OAHiC;YAIjC8H,MAJiC;YAKjCC,UALiC;YAMjCzB,GAAG,EAAEA,GAAG,IAAI5D;UANqB,CAAtB,CAAf;QAQH;MACJ,CAnED,CA3DgC,CA+HhC;MACA;MACA;;;MACA,MAAMmJ,OAAO,GAAItD,KAAD,IAAW;QACvB,MAAM;UAAEjC;QAAF,IAAUkH,cAAc,EAA9B;QACA,MAAMzI,GAAG,GAAG,IAAIsD,iBAAJ,CAAsB;UAC9BE,KAD8B;UAE9BT,MAAM,EAAE6E,GAAG,CAAC7E,MAAJ,IAAc,CAFQ;UAG9BC,UAAU,EAAE4E,GAAG,CAAC5E,UAAJ,IAAkB,eAHA;UAI9BzB,GAAG,EAAEA,GAAG,IAAI5D;QAJkB,CAAtB,CAAZ;QAMAsI,QAAQ,CAACzC,KAAT,CAAexD,GAAf;MACH,CATD,CAlIgC,CA4IhC;MACA;MACA;MACA;;;MACA,IAAI+I,WAAW,GAAG,KAAlB,CAhJgC,CAiJhC;MACA;;MACA,MAAMC,cAAc,GAAI5E,KAAD,IAAW;QAC9B;QACA,IAAI,CAAC2E,WAAL,EAAkB;UACd9C,QAAQ,CAACnB,IAAT,CAAc2D,cAAc,EAA5B;UACAM,WAAW,GAAG,IAAd;QACH,CAL6B,CAM9B;QACA;;;QACA,IAAIE,aAAa,GAAG;UAChB1G,IAAI,EAAEI,aAAa,CAACuG,gBADJ;UAEhBC,MAAM,EAAE/E,KAAK,CAAC+E;QAFE,CAApB,CAR8B,CAY9B;;QACA,IAAI/E,KAAK,CAACgF,gBAAV,EAA4B;UACxBH,aAAa,CAACI,KAAd,GAAsBjF,KAAK,CAACiF,KAA5B;QACH,CAf6B,CAgB9B;QACA;QACA;;;QACA,IAAItF,GAAG,CAAClC,YAAJ,KAAqB,MAArB,IAA+B,CAAC,CAAC+F,GAAG,CAACe,YAAzC,EAAuD;UACnDM,aAAa,CAACK,WAAd,GAA4B1B,GAAG,CAACe,YAAhC;QACH,CArB6B,CAsB9B;;;QACA1C,QAAQ,CAACnB,IAAT,CAAcmE,aAAd;MACH,CAxBD,CAnJgC,CA4KhC;MACA;;;MACA,MAAMM,YAAY,GAAInF,KAAD,IAAW;QAC5B;QACA;QACA,IAAIoF,QAAQ,GAAG;UACXjH,IAAI,EAAEI,aAAa,CAAC8G,cADT;UAEXN,MAAM,EAAE/E,KAAK,CAAC+E;QAFH,CAAf,CAH4B,CAO5B;QACA;;QACA,IAAI/E,KAAK,CAACgF,gBAAV,EAA4B;UACxBI,QAAQ,CAACH,KAAT,GAAiBjF,KAAK,CAACiF,KAAvB;QACH,CAX2B,CAY5B;;;QACApD,QAAQ,CAACnB,IAAT,CAAc0E,QAAd;MACH,CAdD,CA9KgC,CA6LhC;;;MACA5B,GAAG,CAACb,gBAAJ,CAAqB,MAArB,EAA6BJ,MAA7B;MACAiB,GAAG,CAACb,gBAAJ,CAAqB,OAArB,EAA8BD,OAA9B;MACAc,GAAG,CAACb,gBAAJ,CAAqB,SAArB,EAAgCD,OAAhC;MACAc,GAAG,CAACb,gBAAJ,CAAqB,OAArB,EAA8BD,OAA9B,EAjMgC,CAkMhC;;MACA,IAAI/C,GAAG,CAACpC,cAAR,EAAwB;QACpB;QACAiG,GAAG,CAACb,gBAAJ,CAAqB,UAArB,EAAiCiC,cAAjC,EAFoB,CAGpB;;QACA,IAAIT,OAAO,KAAK,IAAZ,IAAoBX,GAAG,CAAC8B,MAA5B,EAAoC;UAChC9B,GAAG,CAAC8B,MAAJ,CAAW3C,gBAAX,CAA4B,UAA5B,EAAwCwC,YAAxC;QACH;MACJ,CA1M+B,CA2MhC;;;MACA3B,GAAG,CAAC+B,IAAJ,CAASpB,OAAT;MACAtC,QAAQ,CAACnB,IAAT,CAAc;QAAEvC,IAAI,EAAEI,aAAa,CAACsE;MAAtB,CAAd,EA7MgC,CA8MhC;MACA;;MACA,OAAO,MAAM;QACT;QACAW,GAAG,CAACgC,mBAAJ,CAAwB,OAAxB,EAAiC9C,OAAjC;QACAc,GAAG,CAACgC,mBAAJ,CAAwB,OAAxB,EAAiC9C,OAAjC;QACAc,GAAG,CAACgC,mBAAJ,CAAwB,MAAxB,EAAgCjD,MAAhC;QACAiB,GAAG,CAACgC,mBAAJ,CAAwB,SAAxB,EAAmC9C,OAAnC;;QACA,IAAI/C,GAAG,CAACpC,cAAR,EAAwB;UACpBiG,GAAG,CAACgC,mBAAJ,CAAwB,UAAxB,EAAoCZ,cAApC;;UACA,IAAIT,OAAO,KAAK,IAAZ,IAAoBX,GAAG,CAAC8B,MAA5B,EAAoC;YAChC9B,GAAG,CAAC8B,MAAJ,CAAWE,mBAAX,CAA+B,UAA/B,EAA2CL,YAA3C;UACH;QACJ,CAXQ,CAYT;;;QACA,IAAI3B,GAAG,CAACiC,UAAJ,KAAmBjC,GAAG,CAACkC,IAA3B,EAAiC;UAC7BlC,GAAG,CAACmC,KAAJ;QACH;MACJ,CAhBD;IAiBH,CAjOM,CAAP;EAkOH;;AAlPgB;;AAoPrB9B,cAAc,CAACtD,IAAf;EAAA,iBAA2GsD,cAA3G,EAtiB6F/N,EAsiB7F,UAA2IL,EAAE,CAACG,UAA9I;AAAA;;AACAiO,cAAc,CAACrD,KAAf,kBAviB6F1K,EAuiB7F;EAAA,OAA+G+N,cAA/G;EAAA,SAA+GA,cAA/G;AAAA;;AACA;EAAA,mDAxiB6F/N,EAwiB7F,mBAA2F+N,cAA3F,EAAuH,CAAC;IAC5G1F,IAAI,EAAEpI;EADsG,CAAD,CAAvH,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAEoI,IAAI,EAAE1I,EAAE,CAACG;IAAX,CAAD,CAAP;EAAmC,CAF7E;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMgQ,gBAAgB,GAAG,IAAI5P,cAAJ,CAAmB,kBAAnB,CAAzB;AACA,MAAM6P,gBAAgB,GAAG,IAAI7P,cAAJ,CAAmB,kBAAnB,CAAzB;AACA;AACA;AACA;AACA;AACA;;AACA,MAAM8P,sBAAN,CAA6B;AAE7B;AACA;AACA;;;AACA,MAAMC,uBAAN,CAA8B;EAC1BnP,WAAW,CAACoP,GAAD,EAAMC,QAAN,EAAgBC,UAAhB,EAA4B;IACnC,KAAKF,GAAL,GAAWA,GAAX;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,UAAL,GAAkBA,UAAlB;IACA,KAAKC,gBAAL,GAAwB,EAAxB;IACA,KAAKC,SAAL,GAAiB,IAAjB;IACA;AACR;AACA;;IACQ,KAAKC,UAAL,GAAkB,CAAlB;EACH;;EACDC,QAAQ,GAAG;IACP,IAAI,KAAKL,QAAL,KAAkB,QAAtB,EAAgC;MAC5B,OAAO,IAAP;IACH;;IACD,MAAMM,YAAY,GAAG,KAAKP,GAAL,CAASQ,MAAT,IAAmB,EAAxC;;IACA,IAAID,YAAY,KAAK,KAAKJ,gBAA1B,EAA4C;MACxC,KAAKE,UAAL;MACA,KAAKD,SAAL,GAAiBzQ,iBAAiB,CAAC4Q,YAAD,EAAe,KAAKL,UAApB,CAAlC;MACA,KAAKC,gBAAL,GAAwBI,YAAxB;IACH;;IACD,OAAO,KAAKH,SAAZ;EACH;;AAvByB;;AAyB9BL,uBAAuB,CAACxF,IAAxB;EAAA,iBAAoHwF,uBAApH,EAxlB6FjQ,EAwlB7F,UAA6JJ,QAA7J,GAxlB6FI,EAwlB7F,UAAkLI,WAAlL,GAxlB6FJ,EAwlB7F,UAA0M8P,gBAA1M;AAAA;;AACAG,uBAAuB,CAACvF,KAAxB,kBAzlB6F1K,EAylB7F;EAAA,OAAwHiQ,uBAAxH;EAAA,SAAwHA,uBAAxH;AAAA;;AACA;EAAA,mDA1lB6FjQ,EA0lB7F,mBAA2FiQ,uBAA3F,EAAgI,CAAC;IACrH5H,IAAI,EAAEpI;EAD+G,CAAD,CAAhI,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAEoI,IAAI,EAAE5E,SAAR;MAAmB4J,UAAU,EAAE,CAAC;QAC9DhF,IAAI,EAAElI,MADwD;QAE9DmN,IAAI,EAAE,CAAC1N,QAAD;MAFwD,CAAD;IAA/B,CAAD,EAG3B;MAAEyI,IAAI,EAAE5E,SAAR;MAAmB4J,UAAU,EAAE,CAAC;QAClChF,IAAI,EAAElI,MAD4B;QAElCmN,IAAI,EAAE,CAAClN,WAAD;MAF4B,CAAD;IAA/B,CAH2B,EAM3B;MAAEiI,IAAI,EAAE5E,SAAR;MAAmB4J,UAAU,EAAE,CAAC;QAClChF,IAAI,EAAElI,MAD4B;QAElCmN,IAAI,EAAE,CAACwC,gBAAD;MAF4B,CAAD;IAA/B,CAN2B,CAAP;EASlB,CAXxB;AAAA;AAYA;AACA;AACA;;;AACA,MAAMa,mBAAN,CAA0B;EACtB7P,WAAW,CAAC8P,YAAD,EAAeC,UAAf,EAA2B;IAClC,KAAKD,YAAL,GAAoBA,YAApB;IACA,KAAKC,UAAL,GAAkBA,UAAlB;EACH;;EACD/F,SAAS,CAACjB,GAAD,EAAMe,IAAN,EAAY;IACjB,MAAMkG,KAAK,GAAGjH,GAAG,CAACxC,GAAJ,CAAQzF,WAAR,EAAd,CADiB,CAEjB;IACA;IACA;IACA;;IACA,IAAIiI,GAAG,CAAClD,MAAJ,KAAe,KAAf,IAAwBkD,GAAG,CAAClD,MAAJ,KAAe,MAAvC,IAAiDmK,KAAK,CAACC,UAAN,CAAiB,SAAjB,CAAjD,IACAD,KAAK,CAACC,UAAN,CAAiB,UAAjB,CADJ,EACkC;MAC9B,OAAOnG,IAAI,CAACZ,MAAL,CAAYH,GAAZ,CAAP;IACH;;IACD,MAAMpD,KAAK,GAAG,KAAKmK,YAAL,CAAkBJ,QAAlB,EAAd,CAViB,CAWjB;;IACA,IAAI/J,KAAK,KAAK,IAAV,IAAkB,CAACoD,GAAG,CAAC9I,OAAJ,CAAYiB,GAAZ,CAAgB,KAAK6O,UAArB,CAAvB,EAAyD;MACrDhH,GAAG,GAAGA,GAAG,CAAC9G,KAAJ,CAAU;QAAEhC,OAAO,EAAE8I,GAAG,CAAC9I,OAAJ,CAAYoB,GAAZ,CAAgB,KAAK0O,UAArB,EAAiCpK,KAAjC;MAAX,CAAV,CAAN;IACH;;IACD,OAAOmE,IAAI,CAACZ,MAAL,CAAYH,GAAZ,CAAP;EACH;;AArBqB;;AAuB1B8G,mBAAmB,CAAClG,IAApB;EAAA,iBAAgHkG,mBAAhH,EAhoB6F3Q,EAgoB7F,UAAqJgQ,sBAArJ,GAhoB6FhQ,EAgoB7F,UAAwL+P,gBAAxL;AAAA;;AACAY,mBAAmB,CAACjG,KAApB,kBAjoB6F1K,EAioB7F;EAAA,OAAoH2Q,mBAApH;EAAA,SAAoHA,mBAApH;AAAA;;AACA;EAAA,mDAloB6F3Q,EAkoB7F,mBAA2F2Q,mBAA3F,EAA4H,CAAC;IACjHtI,IAAI,EAAEpI;EAD2G,CAAD,CAA5H,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAEoI,IAAI,EAAE2H;IAAR,CAAD,EAAmC;MAAE3H,IAAI,EAAE5E,SAAR;MAAmB4J,UAAU,EAAE,CAAC;QAChGhF,IAAI,EAAElI,MAD0F;QAEhGmN,IAAI,EAAE,CAACyC,gBAAD;MAF0F,CAAD;IAA/B,CAAnC,CAAP;EAGlB,CALxB;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMiB,uBAAN,CAA8B;EAC1BlQ,WAAW,CAACmQ,OAAD,EAAUC,QAAV,EAAoB;IAC3B,KAAKD,OAAL,GAAeA,OAAf;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,KAAL,GAAa,IAAb;EACH;;EACDnH,MAAM,CAACH,GAAD,EAAM;IACR,IAAI,KAAKsH,KAAL,KAAe,IAAnB,EAAyB;MACrB,MAAMC,YAAY,GAAG,KAAKF,QAAL,CAAcjP,GAAd,CAAkB8I,iBAAlB,EAAqC,EAArC,CAArB;MACA,KAAKoG,KAAL,GAAaC,YAAY,CAACC,WAAb,CAAyB,CAACzG,IAAD,EAAOC,WAAP,KAAuB,IAAIF,sBAAJ,CAA2BC,IAA3B,EAAiCC,WAAjC,CAAhD,EAA+F,KAAKoG,OAApG,CAAb;IACH;;IACD,OAAO,KAAKE,KAAL,CAAWnH,MAAX,CAAkBH,GAAlB,CAAP;EACH;;AAZyB;;AAc9BmH,uBAAuB,CAACvG,IAAxB;EAAA,iBAAoHuG,uBAApH,EAvqB6FhR,EAuqB7F,UAA6JY,WAA7J,GAvqB6FZ,EAuqB7F,UAAqLA,EAAE,CAACsR,QAAxL;AAAA;;AACAN,uBAAuB,CAACtG,KAAxB,kBAxqB6F1K,EAwqB7F;EAAA,OAAwHgR,uBAAxH;EAAA,SAAwHA,uBAAxH;AAAA;;AACA;EAAA,mDAzqB6FhR,EAyqB7F,mBAA2FgR,uBAA3F,EAAgI,CAAC;IACrH3I,IAAI,EAAEpI;EAD+G,CAAD,CAAhI,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAEoI,IAAI,EAAEzH;IAAR,CAAD,EAAwB;MAAEyH,IAAI,EAAErI,EAAE,CAACsR;IAAX,CAAxB,CAAP;EAAwD,CAFlG;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,mBAAT,CAA6BN,OAA7B,EAAsCG,YAAY,GAAG,EAArD,EAAyD;EACrD,IAAI,CAACA,YAAL,EAAmB;IACf,OAAOH,OAAP;EACH;;EACD,OAAOG,YAAY,CAACC,WAAb,CAAyB,CAACzG,IAAD,EAAOC,WAAP,KAAuB,IAAIF,sBAAJ,CAA2BC,IAA3B,EAAiCC,WAAjC,CAAhD,EAA+FoG,OAA/F,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASO,oBAAT,GAAgC;EAC5B,IAAI,OAAOC,MAAP,KAAkB,QAAtB,EAAgC;IAC5B,OAAOA,MAAP;EACH;;EACD,OAAO,EAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,oBAAN,CAA2B;EACvB;AACJ;AACA;EACkB,OAAPC,OAAO,GAAG;IACb,OAAO;MACHC,QAAQ,EAAEF,oBADP;MAEHG,SAAS,EAAE,CACP;QAAEC,OAAO,EAAEnB,mBAAX;QAAgCoB,QAAQ,EAAE/G;MAA1C,CADO;IAFR,CAAP;EAMH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACsB,OAAXgH,WAAW,CAACxM,OAAO,GAAG,EAAX,EAAe;IAC7B,OAAO;MACHoM,QAAQ,EAAEF,oBADP;MAEHG,SAAS,EAAE,CACPrM,OAAO,CAAC4K,UAAR,GAAqB;QAAE0B,OAAO,EAAEhC,gBAAX;QAA6BmC,QAAQ,EAAEzM,OAAO,CAAC4K;MAA/C,CAArB,GAAmF,EAD5E,EAEP5K,OAAO,CAACqL,UAAR,GAAqB;QAAEiB,OAAO,EAAE/B,gBAAX;QAA6BkC,QAAQ,EAAEzM,OAAO,CAACqL;MAA/C,CAArB,GAAmF,EAF5E;IAFR,CAAP;EAOH;;AA5BsB;;AA8B3Ba,oBAAoB,CAACjH,IAArB;EAAA,iBAAiHiH,oBAAjH;AAAA;;AACAA,oBAAoB,CAACQ,IAArB,kBAnvB6FlS,EAmvB7F;EAAA,MAAkH0R;AAAlH;AACAA,oBAAoB,CAACS,IAArB,kBApvB6FnS,EAovB7F;EAAA,WAAmJ,CAC3I2Q,mBAD2I,EAE3I;IAAEmB,OAAO,EAAE/G,iBAAX;IAA8BqH,WAAW,EAAEzB,mBAA3C;IAAgE0B,KAAK,EAAE;EAAvE,CAF2I,EAG3I;IAAEP,OAAO,EAAE9B,sBAAX;IAAmC+B,QAAQ,EAAE9B;EAA7C,CAH2I,EAI3I;IAAE6B,OAAO,EAAEhC,gBAAX;IAA6BmC,QAAQ,EAAE;EAAvC,CAJ2I,EAK3I;IAAEH,OAAO,EAAE/B,gBAAX;IAA6BkC,QAAQ,EAAE;EAAvC,CAL2I;AAAnJ;;AAOA;EAAA,mDA3vB6FjS,EA2vB7F,mBAA2F0R,oBAA3F,EAA6H,CAAC;IAClHrJ,IAAI,EAAEhI,QAD4G;IAElHiN,IAAI,EAAE,CAAC;MACCuE,SAAS,EAAE,CACPlB,mBADO,EAEP;QAAEmB,OAAO,EAAE/G,iBAAX;QAA8BqH,WAAW,EAAEzB,mBAA3C;QAAgE0B,KAAK,EAAE;MAAvE,CAFO,EAGP;QAAEP,OAAO,EAAE9B,sBAAX;QAAmC+B,QAAQ,EAAE9B;MAA7C,CAHO,EAIP;QAAE6B,OAAO,EAAEhC,gBAAX;QAA6BmC,QAAQ,EAAE;MAAvC,CAJO,EAKP;QAAEH,OAAO,EAAE/B,gBAAX;QAA6BkC,QAAQ,EAAE;MAAvC,CALO;IADZ,CAAD;EAF4G,CAAD,CAA7H;AAAA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMK,gBAAN,CAAuB;;AAEvBA,gBAAgB,CAAC7H,IAAjB;EAAA,iBAA6G6H,gBAA7G;AAAA;;AACAA,gBAAgB,CAACJ,IAAjB,kBAnxB6FlS,EAmxB7F;EAAA,MAA8GsS,gBAA9G;EAAA,UAA0IZ,oBAA1I;AAAA;AACAY,gBAAgB,CAACH,IAAjB,kBApxB6FnS,EAoxB7F;EAAA,WAA2I,CACnIyJ,UADmI,EAEnI;IAAEqI,OAAO,EAAEnR,WAAX;IAAwBoR,QAAQ,EAAEf;EAAlC,CAFmI,EAGnIjD,cAHmI,EAInI;IAAE+D,OAAO,EAAElR,WAAX;IAAwBwR,WAAW,EAAErE;EAArC,CAJmI,CAA3I;EAAA,UAKiB2D,oBAAoB,CAACM,WAArB,CAAiC;IACtC5B,UAAU,EAAE,YAD0B;IAEtCS,UAAU,EAAE;EAF0B,CAAjC,CALjB;AAAA;;AASA;EAAA,mDA7xB6F7Q,EA6xB7F,mBAA2FsS,gBAA3F,EAAyH,CAAC;IAC9GjK,IAAI,EAAEhI,QADwG;IAE9GiN,IAAI,EAAE,CAAC;MACC;AACpB;AACA;MACoBiF,OAAO,EAAE,CACLb,oBAAoB,CAACM,WAArB,CAAiC;QAC7B5B,UAAU,EAAE,YADiB;QAE7BS,UAAU,EAAE;MAFiB,CAAjC,CADK,CAJV;;MAUC;AACpB;AACA;AACA;MACoBgB,SAAS,EAAE,CACPpI,UADO,EAEP;QAAEqI,OAAO,EAAEnR,WAAX;QAAwBoR,QAAQ,EAAEf;MAAlC,CAFO,EAGPjD,cAHO,EAIP;QAAE+D,OAAO,EAAElR,WAAX;QAAwBwR,WAAW,EAAErE;MAArC,CAJO;IAdZ,CAAD;EAFwG,CAAD,CAAzH;AAAA;AAwBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMyE,qBAAN,CAA4B;;AAE5BA,qBAAqB,CAAC/H,IAAtB;EAAA,iBAAkH+H,qBAAlH;AAAA;;AACAA,qBAAqB,CAACN,IAAtB,kBAn0B6FlS,EAm0B7F;EAAA,MAAmHwS;AAAnH;AACAA,qBAAqB,CAACL,IAAtB,kBAp0B6FnS,EAo0B7F;EAAA,WAAqJ,CAC7IwL,kBAD6I,EAE7I;IAAEsG,OAAO,EAAEvG,oBAAX;IAAiCkH,UAAU,EAAEjB;EAA7C,CAF6I,EAG7I;IAAEM,OAAO,EAAE/G,iBAAX;IAA8BgH,QAAQ,EAAExE,gBAAxC;IAA0D8E,KAAK,EAAE;EAAjE,CAH6I;AAArJ;;AAKA;EAAA,mDAz0B6FrS,EAy0B7F,mBAA2FwS,qBAA3F,EAA8H,CAAC;IACnHnK,IAAI,EAAEhI,QAD6G;IAEnHiN,IAAI,EAAE,CAAC;MACCuE,SAAS,EAAE,CACPrG,kBADO,EAEP;QAAEsG,OAAO,EAAEvG,oBAAX;QAAiCkH,UAAU,EAAEjB;MAA7C,CAFO,EAGP;QAAEM,OAAO,EAAE/G,iBAAX;QAA8BgH,QAAQ,EAAExE,gBAAxC;QAA0D8E,KAAK,EAAE;MAAjE,CAHO;IADZ,CAAD;EAF6G,CAAD,CAA9H;AAAA;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMvS,UAAU,GAAGC,YAAnB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASgL,iBAAT,EAA4BnK,WAA5B,EAAyC6I,UAAzC,EAAqD+I,qBAArD,EAA4EF,gBAA5E,EAA8FZ,oBAA9F,EAAoHlL,WAApH,EAAiIF,gBAAjI,EAAmJ8C,iBAAnJ,EAAsKX,aAAtK,EAAqL9H,WAArL,EAAkMqI,kBAAlM,EAAsNnI,WAAtN,EAAmO0E,UAAnO,EAA+O6B,WAA/O,EAA4P8B,YAA5P,EAA0QR,gBAA1Q,EAA4R1E,oBAA5R,EAAkT+J,cAAlT,EAAkUiC,sBAAlU,EAA0VxE,kBAA1V,EAA8W+B,gBAA9W,EAAgYzN,UAAhY,EAA4YkR,uBAAuB,IAAI0B,wBAAva"}, "metadata": {}, "sourceType": "module"}