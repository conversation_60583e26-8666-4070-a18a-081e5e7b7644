{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./imprinted-form.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./imprinted-form.component.css?ngResource\";\nimport { ChangeDetectorRef, Component, EventEmitter, Input, Output, ViewChild, ViewChildren } from '@angular/core';\nimport { FormGroup } from '@angular/forms';\nimport { ToastrService } from 'ngx-toastr';\nimport { DynamicFormKeyValue } from 'src/app/Modules/FilingService/Models/DynamicFormKeyValue';\nimport { HttpClient } from \"@angular/common/http\";\nimport { DomSanitizer } from '@angular/platform-browser';\nimport { QuestionControlService } from 'src/app/Modules/Shared/Services/Common/question-control.service';\nimport { ImprintedFormQuestionComponent } from '../imprinted-form-question/imprinted-form-question.component';\nlet ImprintedFormComponent = class ImprintedFormComponent {\n  constructor(qcs, toaster, httpClient, cdr, doms) {\n    this.qcs = qcs;\n    this.toaster = toaster;\n    this.httpClient = httpClient;\n    this.cdr = cdr;\n    this.doms = doms;\n    this.questions = [];\n    this.allQuestions = [];\n    this.corpType = '';\n    this.dependentControls = [];\n    this.stylingClass = \"\";\n    this.$AddToOrder = new EventEmitter();\n    this.keys = [];\n    this.questionsByCategory = [];\n    this.EmployerIdentificationNo = null;\n    this.AnnualReport = null;\n    this.KitOrder = null;\n    this.appQuestionKeys = [];\n    this.dynamicFormQuestionKeys = {};\n    this.fileUploadEvent = new EventEmitter();\n  }\n\n  set dynamicFormQuestion(dynamicFormQuestion) {\n    if (dynamicFormQuestion) {\n      this.dynamicFormQuestionKeys = dynamicFormQuestion.changes.subscribe(data => {\n        this.dynamicFormQuestionKeys = data._results.map(element => {\n          return element.key;\n        });\n\n        if (this.dynamicFormQuestionKeys.length && this.appQuestionKeys.length) {\n          let someArray = this.dynamicFormQuestionKeys.filter((value, index, someArray) => someArray.indexOf(value) === index);\n          this.appQuestionKeys.map(data => {\n            if (someArray.includes(data.key)) {\n              data.visibility = true;\n            } else {\n              data.visibility = false;\n            }\n          });\n        }\n      });\n    }\n  }\n\n  set _FormRef1(FormRef) {\n    this.addToOrderButton = FormRef;\n  }\n\n  getDynamicFormData() {\n    setTimeout(() => {\n      this.addToOrderButton.nativeElement.click();\n    }, 10);\n    return new Promise(res => this.DynamicFormResolver = res);\n  }\n\n  ngOnChanges() {\n    this.resolveQuestions().then(value => {\n      this.dynamicform = this.qcs.toFormGroup(this.questions);\n      this.allQuestions = this.questions; //this.keys = this.GetLabel(this.questions,'sectionLabel');\n\n      this.questionsByCategory = this.GroupBy(this.questions, 'sectionLabel');\n      this.questions = this.questionsByCategory;\n      this.keys = Object.keys(this.questions);\n\n      if (this.keys) {\n        this.appQuestionKeys = this.keys.map(x => {\n          let data = {\n            key: x,\n            visibility: false\n          };\n          return data;\n        });\n      }\n    }); //   console.log(\"Controls :\" +this.dependentControls);\n    //   this.httpClient.get<any>(\"assets/data/common/dynamicQue.json\").subscribe((res)=>\n    //   this.dynamicQuestionData = res\n    //  );\n\n    this.dynamicQuestionData = this.dependentControls;\n  }\n\n  ngAfterContentChecked() {\n    this.cdr.detectChanges();\n  }\n\n  ngOnInit() {\n    this.dynamicform = new FormGroup({});\n  }\n\n  resolveQuestions() {\n    var promise = new Promise(resolve => {\n      setTimeout(() => {\n        if (this.questions && this.questions.length > 0) {\n          resolve(this.questions);\n        }\n      }, 1);\n    });\n    return promise;\n  }\n\n  safeCss(style) {\n    if (style != \"\") {\n      return this.doms.bypassSecurityTrustStyle(style);\n    }\n  }\n\n  GroupBy(data, key) {\n    return data.reduce(function (rv, x) {\n      (rv[x[key]] = rv[x[key]] || []).push(x);\n      rv[x[key]].sort((a, b) => a.order - b.order);\n      return rv;\n    }, {});\n  }\n\n  GetLabel(data, key) {\n    let sectionLabels = data.map(x => {\n      return x[key];\n    });\n    return [...new Set(sectionLabels)];\n  }\n\n  onSubmit() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        // await this.UpSellingData();\n        if (_this.dynamicform.valid) {\n          var formValue = _this.dynamicform.value;\n          _this.keyValuePair = _this.mapToModel(formValue);\n          var response = {\n            keyValuePair: _this.keyValuePair\n          };\n\n          _this.DynamicFormResolver(Object.assign([], response));\n\n          _this.toaster.info('Order is being processed'); // this.$AddToOrder.emit(response);\n\n        } else {\n          _this.toaster.error('Please fill all required fields', 'Error');\n        }\n      } catch (ex) {\n        console.error(ex);\n      }\n    })();\n  }\n\n  mapToModel(formValue) {\n    var keyValuePair = [];\n\n    for (var item in formValue) {\n      var isArray = Array.isArray(formValue[item]);\n      var value = isArray ? formValue[item].filter(x => x.isChecked) : formValue[item];\n      var dynamicFormKeyValue = new DynamicFormKeyValue();\n      dynamicFormKeyValue.ControlId = item;\n      dynamicFormKeyValue.Value = value;\n      keyValuePair.push(dynamicFormKeyValue);\n    }\n\n    return keyValuePair;\n  }\n\n  getDisplayCondition(question) {\n    try {\n      var valuesSplit = question.dependentOnControlValue.split('|');\n      var condition = '';\n\n      if (question.conditionType && question.conditionType === 'OR' && question.dependentOnControlId && valuesSplit.length >= 2) {\n        var condition = '';\n\n        for (var i = 0; i < valuesSplit.length; i++) {\n          if (i > 0) {\n            condition += ' || ';\n          }\n\n          condition += `this.dynamicform.get('${question.dependentOnControlId}')?.value == '${valuesSplit[i]}'`;\n        }\n\n        condition = '(' + condition + ')';\n      } else if (question.conditionType && question.conditionType == 'STATIC') {\n        condition = question.dependentOnControlId ? `this.${question.dependentOnControlId} == '${question.dependentOnControlValue}'` : '';\n      } else {\n        if (question.dependentOnControlId != '') {\n          condition = question.dependentOnControlId ? `this.dynamicform.get('${question.dependentOnControlId}')?.value == '${question.dependentOnControlValue}'` : '';\n        }\n      }\n\n      return question.dependentOnControlId == \"\" ? true : eval(condition);\n    } catch (ex) {\n      console.log(ex);\n    }\n  }\n\n  getCategoryDisplayCondition(key) {\n    if (!key) return false;\n    var response = true;\n    this.questionsByCategory[key].every(question => {\n      var questionCondition = this.getDisplayCondition(question);\n\n      if (!questionCondition) {\n        response = false;\n        return;\n      }\n    });\n    return response;\n  }\n\n  fileUplaod(files) {\n    this.fileUploadEvent.emit(files);\n  }\n\n  getIndexNo(code, array) {\n    let val = array.findIndex(x => x == code);\n\n    if (val == -1) {\n      return null;\n    }\n\n    return val;\n  }\n\n  getEntityName(state, subCategoryCode, dynamicValues) {\n    var value = '';\n\n    switch (state) {\n      case \"DE\":\n        {\n          var controlId = subCategoryCode == \"SC\" ? \"The_name_of_this_Corporation_is_Corp_DE\" : subCategoryCode == \"LC\" ? \"limited_liability_company_is_LLC_DE\" : \"\";\n          value = dynamicValues.find(x => x.ControlId == controlId).Value || \"\";\n          break;\n        }\n\n      case \"FL\":\n        {\n          var controlId = subCategoryCode == \"SC\" ? \"Corporate_Name_CORP_FL\" : subCategoryCode == \"LC\" ? \"Limited_Liability_Company_Name_LLC_FL\" : \"\";\n          value = dynamicValues.find(x => x.ControlId == controlId).Value || \"\";\n          break;\n        }\n\n      case \"NV\":\n        {\n          var controlId = subCategoryCode == \"SC\" ? \"Entity_Name\" : subCategoryCode == \"LC\" ? \"Name_LLC\" : \"\";\n          value = dynamicValues.find(x => x.ControlId == controlId).Value || \"\";\n          break;\n        }\n\n      case \"NY\":\n        {\n          var controlId = subCategoryCode == \"SC\" ? \"Proposed_Entity_Name_and_Corp_ending_Corp_NY\" : subCategoryCode == \"LC\" ? \"Proposed_Entity_Name_of_the_Limited_Liability_Company_LLC_NY\" : \"\";\n          value = dynamicValues.find(x => x.ControlId == controlId).Value || \"\";\n          break;\n        }\n\n      case \"TX\":\n        {\n          var controlId = subCategoryCode == \"SC\" ? \"Entity_Name\" : subCategoryCode == \"LC\" ? \"Entity_Name_LLC\" : \"\";\n          value = dynamicValues.find(x => x.ControlId == controlId).Value || \"\";\n          break;\n        }\n    }\n\n    return value;\n  }\n\n};\n\nImprintedFormComponent.ctorParameters = () => [{\n  type: QuestionControlService\n}, {\n  type: ToastrService\n}, {\n  type: HttpClient\n}, {\n  type: ChangeDetectorRef\n}, {\n  type: DomSanitizer\n}];\n\nImprintedFormComponent.propDecorators = {\n  questions: [{\n    type: Input\n  }],\n  upSellingInputData: [{\n    type: Input\n  }],\n  state: [{\n    type: Input\n  }],\n  corpType: [{\n    type: Input\n  }],\n  dependentControls: [{\n    type: Input\n  }],\n  stylingClass: [{\n    type: Input\n  }],\n  fileUploadEvent: [{\n    type: Output\n  }],\n  dynamicFormQuestion: [{\n    type: ViewChildren,\n    args: [ImprintedFormQuestionComponent]\n  }],\n  _FormRef1: [{\n    type: ViewChild,\n    args: [\"sbmit\", {\n      static: false\n    }]\n  }]\n};\nImprintedFormComponent = __decorate([Component({\n  selector: 'app-imprinted-form',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [QuestionControlService],\n  styles: [__NG_CLI_RESOURCE__1]\n})], ImprintedFormComponent);\nexport { ImprintedFormComponent };", "map": {"version": 3, "mappings": ";;;;AAAA,SAA2BA,iBAA3B,EAA8CC,SAA9C,EAAqEC,YAArE,EAAmFC,KAAnF,EAA6GC,MAA7G,EAAgIC,SAAhI,EAA2IC,YAA3I,QAA+J,eAA/J;AACA,SAASC,SAAT,QAA0B,gBAA1B;AACA,SAASC,aAAT,QAA8B,YAA9B;AACA,SAASC,mBAAT,QAAoC,0DAApC;AAGA,SAASC,UAAT,QAA8C,sBAA9C;AACA,SAASC,YAAT,QAA6B,2BAA7B;AAGA,SAASC,sBAAT,QAAuC,iEAAvC;AACA,SAASC,8BAAT,QAA+C,8DAA/C;IAQaC,sBAAsB,SAAtBA,sBAAsB;EAuBjCC,YAAoBC,GAApB,EAAyDC,OAAzD,EAAyFC,UAAzF,EACUC,GADV,EAC0CC,IAD1C,EAC4D;IADxC;IAAqC;IAAgC;IAC/E;IAAgC;IAtBjC,iBAAoC,EAApC;IACT,oBAAuC,EAAvC;IAGS,gBAAgB,EAAhB;IACA,yBAA2B,EAA3B;IACA,oBAAuB,EAAvB;IACT,mBAAc,IAAIlB,YAAJ,EAAd;IAEA,YAAc,EAAd;IAEA,2BAA6B,EAA7B;IAEA,gCAAyD,IAAzD;IACA,oBAAuC,IAAvC;IACA,gBAA0B,IAA1B;IAGA,uBAAyB,EAAzB;IACA,+BAA+B,EAA/B;IACU,uBAAkB,IAAIA,YAAJ,EAAlB;EAEuD;;EAEI,IAAnBmB,mBAAmB,CAACA,mBAAD,EAA+D;IAClI,IAAIA,mBAAJ,EAAyB;MACvB,KAAKC,uBAAL,GAA+BD,mBAAmB,CAACE,OAApB,CAA4BC,SAA5B,CAAsCC,IAAI,IAAG;QAC1E,KAAKH,uBAAL,GAA+BG,IAAI,CAACC,QAAL,CAAcC,GAAd,CAAkBC,OAAO,IAAG;UACzD,OAAOA,OAAO,CAACC,GAAf;QACD,CAF8B,CAA/B;;QAGA,IAAI,KAAKP,uBAAL,CAA6BQ,MAA7B,IAAuC,KAAKC,eAAL,CAAqBD,MAAhE,EAAwE;UACtE,IAAIE,SAAS,GAAG,KAAKV,uBAAL,CAA6BW,MAA7B,CAAoC,CAACC,KAAD,EAAQC,KAAR,EAAeH,SAAf,KAA6BA,SAAS,CAACI,OAAV,CAAkBF,KAAlB,MAA6BC,KAA9F,CAAhB;UACA,KAAKJ,eAAL,CAAqBJ,GAArB,CAAyBF,IAAI,IAAG;YAC9B,IAAIO,SAAS,CAACK,QAAV,CAAmBZ,IAAI,CAACI,GAAxB,CAAJ,EAAkC;cAChCJ,IAAI,CAACa,UAAL,GAAkB,IAAlB;YACD,CAFD,MAEO;cACLb,IAAI,CAACa,UAAL,GAAkB,KAAlB;YACD;UACF,CAND;QAOD;MACF,CAd8B,CAA/B;IAeD;EACF;;EAImD,IAATC,SAAS,CAClDC,OADkD,EAC/B;IAEnB,KAAKC,gBAAL,GAAwBD,OAAxB;EACD;;EAGDE,kBAAkB;IAChBC,UAAU,CAAC,MAAK;MACd,KAAKF,gBAAL,CAAsBG,aAAtB,CAAoCC,KAApC;IACD,CAFS,EAEP,EAFO,CAAV;IAIA,OAAO,IAAIC,OAAJ,CAAaC,GAAD,IAAU,KAAKC,mBAAL,GAA2BD,GAAjD,CAAP;EACD;;EAEDE,WAAW;IACT,KAAKC,gBAAL,GAAwBC,IAAxB,CAA8BjB,KAAD,IAAU;MACrC,KAAKkB,WAAL,GAAmB,KAAKpC,GAAL,CAASqC,WAAT,CAAqB,KAAKC,SAA1B,CAAnB;MACA,KAAKC,YAAL,GAAoB,KAAKD,SAAzB,CAFqC,CAGrC;;MACA,KAAKE,mBAAL,GAA2B,KAAKC,OAAL,CAAa,KAAKH,SAAlB,EAA6B,cAA7B,CAA3B;MACA,KAAKA,SAAL,GAAiB,KAAKE,mBAAtB;MACA,KAAKE,IAAL,GAAYC,MAAM,CAACD,IAAP,CAAY,KAAKJ,SAAjB,CAAZ;;MACA,IAAI,KAAKI,IAAT,EAAe;QACb,KAAK3B,eAAL,GAAuB,KAAK2B,IAAL,CAAU/B,GAAV,CAAeiC,CAAD,IAAM;UACzC,IAAInC,IAAI,GAAG;YACTI,GAAG,EAAE+B,CADI;YAETtB,UAAU,EAAE;UAFH,CAAX;UAIA,OAAOb,IAAP;QACD,CANsB,CAAvB;MAOD;IACF,CAhBD,EADS,CAkBT;IACA;IACA;IACA;;IACA,KAAKoC,mBAAL,GAA2B,KAAKC,iBAAhC;EACD;;EAEDC,qBAAqB;IACnB,KAAK5C,GAAL,CAAS6C,aAAT;EACD;;EAEDC,QAAQ;IACN,KAAKb,WAAL,GAAmB,IAAI7C,SAAJ,CAAc,EAAd,CAAnB;EAGD;;EAED2C,gBAAgB;IACd,IAAIgB,OAAO,GAAG,IAAIpB,OAAJ,CAAaqB,OAAD,IAAY;MACpCxB,UAAU,CAAC,MAAK;QACd,IAAI,KAAKW,SAAL,IAAkB,KAAKA,SAAL,CAAexB,MAAf,GAAwB,CAA9C,EAAiD;UAC/CqC,OAAO,CAAC,KAAKb,SAAN,CAAP;QACD;MACF,CAJS,EAIP,CAJO,CAAV;IAKD,CANa,CAAd;IAOA,OAAOY,OAAP;EACD;;EAEDE,OAAO,CAACC,KAAD,EAAM;IACX,IAAIA,KAAK,IAAI,EAAb,EAAiB;MACf,OAAO,KAAKjD,IAAL,CAAUkD,wBAAV,CAAmCD,KAAnC,CAAP;IACD;EACF;;EAEDZ,OAAO,CAAChC,IAAD,EAAOI,GAAP,EAAU;IACf,OAAOJ,IAAI,CAAC8C,MAAL,CAAY,UAAUC,EAAV,EAAcZ,CAAd,EAAe;MAC/B,CAACY,EAAE,CAACZ,CAAC,CAAC/B,GAAD,CAAF,CAAF,GAAa2C,EAAE,CAACZ,CAAC,CAAC/B,GAAD,CAAF,CAAF,IAAc,EAA5B,EAAgC4C,IAAhC,CAAqCb,CAArC,CAAD;MACCY,EAAE,CAACZ,CAAC,CAAC/B,GAAD,CAAF,CAAF,CAAW6C,IAAX,CAAgB,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACE,KAAF,GAAUD,CAAC,CAACC,KAAtC,CAAD;MACA,OAAOL,EAAP;IACD,CAJM,EAIJ,EAJI,CAAP;EAKD;;EACDM,QAAQ,CAACrD,IAAD,EAAOI,GAAP,EAAU;IAChB,IAAIkD,aAAa,GAAGtD,IAAI,CAACE,GAAL,CAASiC,CAAC,IAAG;MAC/B,OAAOA,CAAC,CAAC/B,GAAD,CAAR;IACD,CAFmB,CAApB;IAGA,OAAO,CAAC,GAAG,IAAImD,GAAJ,CAAQD,aAAR,CAAJ,CAAP;EACD;;EAEKE,QAAQ;IAAA;;IAAA;MACZ,IAAI;QAEF;QACA,IAAI,KAAI,CAAC7B,WAAL,CAAiB8B,KAArB,EAA4B;UAE1B,IAAIC,SAAS,GAAG,KAAI,CAAC/B,WAAL,CAAiBlB,KAAjC;UACA,KAAI,CAACkD,YAAL,GAAoB,KAAI,CAACC,UAAL,CAAgBF,SAAhB,CAApB;UAGA,IAAIG,QAAQ,GAAQ;YAClBF,YAAY,EAAE,KAAI,CAACA;UADD,CAApB;;UAIA,KAAI,CAACpC,mBAAL,CAAyBW,MAAM,CAAC4B,MAAP,CAAc,EAAd,EAAkBD,QAAlB,CAAzB;;UACA,KAAI,CAACrE,OAAL,CAAauE,IAAb,CAAkB,0BAAlB,EAX0B,CAY1B;;QACD,CAbD,MAcK;UACH,KAAI,CAACvE,OAAL,CAAawE,KAAb,CAAmB,iCAAnB,EAAsD,OAAtD;QACD;MACF,CApBD,CAqBA,OAAOC,EAAP,EAAW;QACTC,OAAO,CAACF,KAAR,CAAcC,EAAd;MACD;IAxBW;EAyBb;;EAEOL,UAAU,CAACF,SAAD,EAAU;IAE1B,IAAIC,YAAY,GAA0B,EAA1C;;IAEA,KAAK,IAAIQ,IAAT,IAAiBT,SAAjB,EAA4B;MAE1B,IAAIU,OAAO,GAAGC,KAAK,CAACD,OAAN,CAAcV,SAAS,CAACS,IAAD,CAAvB,CAAd;MACA,IAAI1D,KAAK,GAAG2D,OAAO,GAAGV,SAAS,CAACS,IAAD,CAAT,CAAgB3D,MAAhB,CAAuB2B,CAAC,IAAIA,CAAC,CAACmC,SAA9B,CAAH,GAA8CZ,SAAS,CAACS,IAAD,CAA1E;MAEA,IAAII,mBAAmB,GAAG,IAAIvF,mBAAJ,EAA1B;MACAuF,mBAAmB,CAACC,SAApB,GAAgCL,IAAhC;MACAI,mBAAmB,CAACE,KAApB,GAA4BhE,KAA5B;MAEAkD,YAAY,CAACX,IAAb,CAAkBuB,mBAAlB;IACD;;IAED,OAAOZ,YAAP;EACD;;EAEDe,mBAAmB,CAACC,QAAD,EAAS;IAC1B,IAAI;MACF,IAAIC,WAAW,GAAGD,QAAQ,CAACE,uBAAT,CAAiCC,KAAjC,CAAuC,GAAvC,CAAlB;MACA,IAAIC,SAAS,GAAG,EAAhB;;MAEA,IAAIJ,QAAQ,CAACK,aAAT,IAA0BL,QAAQ,CAACK,aAAT,KAA2B,IAArD,IAA6DL,QAAQ,CAACM,oBAAtE,IAA8FL,WAAW,CAACvE,MAAZ,IAAsB,CAAxH,EAA2H;QACzH,IAAI0E,SAAS,GAAG,EAAhB;;QAEA,KAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGN,WAAW,CAACvE,MAAhC,EAAwC6E,CAAC,EAAzC,EAA6C;UAC3C,IAAIA,CAAC,GAAG,CAAR,EAAW;YACTH,SAAS,IAAI,MAAb;UACD;;UACDA,SAAS,IAAI,yBAAyBJ,QAAQ,CAACM,oBAAoB,iBAAiBL,WAAW,CAACM,CAAD,CAAG,GAAlG;QACD;;QAEDH,SAAS,GAAG,MAAMA,SAAN,GAAkB,GAA9B;MACD,CAXD,MAaK,IAAIJ,QAAQ,CAACK,aAAT,IAA0BL,QAAQ,CAACK,aAAT,IAA0B,QAAxD,EAAkE;QACrED,SAAS,GAAGJ,QAAQ,CAACM,oBAAT,GAAgC,QAAQN,QAAQ,CAACM,oBAAoB,QAAQN,QAAQ,CAACE,uBAAuB,GAA7G,GAAmH,EAA/H;MACD,CAFI,MAGA;QACH,IAAIF,QAAQ,CAACM,oBAAT,IAAiC,EAArC,EAAyC;UACvCF,SAAS,GAAGJ,QAAQ,CAACM,oBAAT,GAAgC,yBAAyBN,QAAQ,CAACM,oBAAoB,iBAAiBN,QAAQ,CAACE,uBAAuB,GAAvI,GAA6I,EAAzJ;QACD;MACF;;MACD,OAAOF,QAAQ,CAACM,oBAAT,IAAiC,EAAjC,GAAsC,IAAtC,GAA6CE,IAAI,CAACJ,SAAD,CAAxD;IACD,CA1BD,CA2BA,OAAOd,EAAP,EAAW;MACTC,OAAO,CAACkB,GAAR,CAAYnB,EAAZ;IACD;EAEF;;EAEDoB,2BAA2B,CAACjF,GAAD,EAAI;IAC7B,IAAI,CAACA,GAAL,EAAU,OAAO,KAAP;IAEV,IAAIyD,QAAQ,GAAG,IAAf;IAEA,KAAK9B,mBAAL,CAAyB3B,GAAzB,EAA8BkF,KAA9B,CAAoCX,QAAQ,IAAG;MAE7C,IAAIY,iBAAiB,GAAG,KAAKb,mBAAL,CAAyBC,QAAzB,CAAxB;;MACA,IAAI,CAACY,iBAAL,EAAwB;QACtB1B,QAAQ,GAAG,KAAX;QACA;MACD;IACF,CAPD;IAQA,OAAOA,QAAP;EACD;;EAED2B,UAAU,CAACC,KAAD,EAAc;IACtB,KAAKC,eAAL,CAAqBC,IAArB,CAA0BF,KAA1B;EACD;;EAEOG,UAAU,CAACC,IAAD,EAAOC,KAAP,EAAmB;IAEnC,IAAIC,GAAG,GAAGD,KAAK,CAACE,SAAN,CAAgB7D,CAAC,IAAIA,CAAC,IAAI0D,IAA1B,CAAV;;IACA,IAAIE,GAAG,IAAI,CAAC,CAAZ,EAAe;MACb,OAAO,IAAP;IACD;;IAED,OAAOA,GAAP;EACD;;EAEOE,aAAa,CAACC,KAAD,EAAQC,eAAR,EAAyBC,aAAzB,EAAsC;IACzD,IAAI3F,KAAK,GAAG,EAAZ;;IACA,QAAQyF,KAAR;MACE,KAAK,IAAL;QACE;UACE,IAAIG,SAAS,GAAGF,eAAe,IAAI,IAAnB,GAA0B,yCAA1B,GAAsEA,eAAe,IAAI,IAAnB,GAA0B,qCAA1B,GAAkE,EAAxJ;UACA1F,KAAK,GAAG2F,aAAa,CAACE,IAAd,CAAmBnE,CAAC,IAAIA,CAAC,CAACqC,SAAF,IAAe6B,SAAvC,EAAkD5B,KAAlD,IAA2D,EAAnE;UACA;QACD;;MACH,KAAK,IAAL;QACE;UACE,IAAI4B,SAAS,GAAGF,eAAe,IAAI,IAAnB,GAA0B,wBAA1B,GAAqDA,eAAe,IAAI,IAAnB,GAA0B,uCAA1B,GAAoE,EAAzI;UACA1F,KAAK,GAAG2F,aAAa,CAACE,IAAd,CAAmBnE,CAAC,IAAIA,CAAC,CAACqC,SAAF,IAAe6B,SAAvC,EAAkD5B,KAAlD,IAA2D,EAAnE;UACA;QACD;;MACH,KAAK,IAAL;QACE;UACE,IAAI4B,SAAS,GAAGF,eAAe,IAAI,IAAnB,GAA0B,aAA1B,GAA0CA,eAAe,IAAI,IAAnB,GAA0B,UAA1B,GAAuC,EAAjG;UACA1F,KAAK,GAAG2F,aAAa,CAACE,IAAd,CAAmBnE,CAAC,IAAIA,CAAC,CAACqC,SAAF,IAAe6B,SAAvC,EAAkD5B,KAAlD,IAA2D,EAAnE;UACA;QACD;;MACH,KAAK,IAAL;QACE;UACE,IAAI4B,SAAS,GAAGF,eAAe,IAAI,IAAnB,GAA0B,8CAA1B,GAA2EA,eAAe,IAAI,IAAnB,GAA0B,8DAA1B,GAA2F,EAAtL;UACA1F,KAAK,GAAG2F,aAAa,CAACE,IAAd,CAAmBnE,CAAC,IAAIA,CAAC,CAACqC,SAAF,IAAe6B,SAAvC,EAAkD5B,KAAlD,IAA2D,EAAnE;UACA;QACD;;MACH,KAAK,IAAL;QACE;UACE,IAAI4B,SAAS,GAAGF,eAAe,IAAI,IAAnB,GAA0B,aAA1B,GAA0CA,eAAe,IAAI,IAAnB,GAA0B,iBAA1B,GAA8C,EAAxG;UACA1F,KAAK,GAAG2F,aAAa,CAACE,IAAd,CAAmBnE,CAAC,IAAIA,CAAC,CAACqC,SAAF,IAAe6B,SAAvC,EAAkD5B,KAAlD,IAA2D,EAAnE;UACA;QACD;IA9BL;;IAiCA,OAAOhE,KAAP;EACD;;AAnRgC;;;;;;;;;;;;;;;;UAEhC/B;;;UAEAA;;;UACAA;;;UACAA;;;UACAA;;;UACAA;;;UAcAC;;;UAIAE;IAAY0H,OAACnH,8BAAD;;;UAsBZR;IAAS2H,OAAC,OAAD,EAAU;MAAEC,MAAM,EAAE;IAAV,CAAV;;;AAhDCnH,sBAAsB,eANlCb,SAAS,CAAC;EACTiI,QAAQ,EAAE,oBADD;EAETC,8BAFS;EAITC,SAAS,EAAE,CAACxH,sBAAD,CAJF;;AAAA,CAAD,CAMyB,GAAtBE,sBAAsB,CAAtB;SAAAA", "names": ["ChangeDetectorRef", "Component", "EventEmitter", "Input", "Output", "ViewChild", "ViewChildren", "FormGroup", "ToastrService", "DynamicFormKeyValue", "HttpClient", "Dom<PERSON><PERSON><PERSON>zer", "QuestionControlService", "ImprintedFormQuestionComponent", "ImprintedFormComponent", "constructor", "qcs", "toaster", "httpClient", "cdr", "doms", "dynamicFormQuestion", "dynamicFormQuestionKeys", "changes", "subscribe", "data", "_results", "map", "element", "key", "length", "appQuestionKeys", "someArray", "filter", "value", "index", "indexOf", "includes", "visibility", "_FormRef1", "FormRef", "addToOrderButton", "getDynamicFormData", "setTimeout", "nativeElement", "click", "Promise", "res", "DynamicFormResolver", "ngOnChanges", "resolveQuestions", "then", "dynamicform", "toFormGroup", "questions", "allQuestions", "questionsByCategory", "GroupBy", "keys", "Object", "x", "dynamicQuestionData", "dependentControls", "ngAfterContentChecked", "detectChanges", "ngOnInit", "promise", "resolve", "safeCss", "style", "bypassSecurityTrustStyle", "reduce", "rv", "push", "sort", "a", "b", "order", "GetLabel", "sectionLabels", "Set", "onSubmit", "valid", "formValue", "keyValuePair", "mapToModel", "response", "assign", "info", "error", "ex", "console", "item", "isArray", "Array", "isChecked", "dynamicFormKeyValue", "ControlId", "Value", "getDisplayCondition", "question", "valuesSplit", "dependentOnControlValue", "split", "condition", "conditionType", "dependentOnControlId", "i", "eval", "log", "getCategoryDisplayCondition", "every", "questionCondition", "fileUplaod", "files", "fileUploadEvent", "emit", "getIndexNo", "code", "array", "val", "findIndex", "getEntityName", "state", "subCategoryCode", "dynamicValues", "controlId", "find", "args", "static", "selector", "template", "providers"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\viatech\\Ecom\\client\\acs\\src\\app\\Modules\\Estate&WillPlanning\\components\\imprinted-form\\imprinted-form.component.ts"], "sourcesContent": ["import { AfterViewChecked, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, QueryList, ViewChild, ViewChildren } from '@angular/core';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { DynamicFormKeyValue } from 'src/app/Modules/FilingService/Models/DynamicFormKeyValue';\r\nimport { EmployerIdnetificationNumber } from 'src/app/Modules/FilingService/Models/EmployerIdentificationNumber';\r\nimport { StatementOfInfromation } from 'src/app/Modules/FilingService/Models/StatementOfInformation';\r\nimport { HttpClient, HttpErrorResponse } from \"@angular/common/http\";\r\nimport { DomSanitizer } from '@angular/platform-browser';\r\nimport { QuestionBase } from 'src/app/Modules/Shared/Models/DynamicForm/question-base';\r\nimport { ProductDetail } from 'src/app/Modules/Shared/Models/KitsModel/ProductDetail';\r\nimport { QuestionControlService } from 'src/app/Modules/Shared/Services/Common/question-control.service';\r\nimport { ImprintedFormQuestionComponent } from '../imprinted-form-question/imprinted-form-question.component';\r\n\r\n@Component({\r\n  selector: 'app-imprinted-form',\r\n  templateUrl: './imprinted-form.component.html',\r\n  styleUrls: ['./imprinted-form.component.css'],\r\n  providers: [QuestionControlService]\r\n})\r\nexport class ImprintedFormComponent implements OnInit {\r\n\r\n  @Input() questions: QuestionBase<string>[] = [];\r\n  allQuestions: QuestionBase<string>[] = [];\r\n  @Input() upSellingInputData: any;\r\n  @Input() state: any;\r\n  @Input() corpType: any = '';\r\n  @Input() dependentControls: any[] = [];\r\n  @Input() stylingClass: string = \"\";\r\n  $AddToOrder = new EventEmitter<any>();\r\n  dynamicform: FormGroup;\r\n  keys: any[] = [];\r\n  dynamicQuestionData: any;\r\n  questionsByCategory: any[] = [];\r\n\r\n  EmployerIdentificationNo: EmployerIdnetificationNumber = null;\r\n  AnnualReport: StatementOfInfromation = null;\r\n  KitOrder: ProductDetail = null;\r\n  keyValuePair: any;\r\n\r\n  appQuestionKeys: any[] = [];\r\n  dynamicFormQuestionKeys: any = {};\r\n  @Output() fileUploadEvent = new EventEmitter<File[]>();\r\n  constructor(private qcs: QuestionControlService, private toaster: ToastrService, private httpClient: HttpClient,\r\n    private cdr: ChangeDetectorRef, private doms: DomSanitizer) { }\r\n\r\n  @ViewChildren(ImprintedFormQuestionComponent) set dynamicFormQuestion(dynamicFormQuestion: QueryList<ImprintedFormQuestionComponent>) {\r\n    if (dynamicFormQuestion) {\r\n      this.dynamicFormQuestionKeys = dynamicFormQuestion.changes.subscribe(data => {\r\n        this.dynamicFormQuestionKeys = data._results.map(element => {\r\n          return element.key;\r\n        });\r\n        if (this.dynamicFormQuestionKeys.length && this.appQuestionKeys.length) {\r\n          let someArray = this.dynamicFormQuestionKeys.filter((value, index, someArray) => someArray.indexOf(value) === index);\r\n          this.appQuestionKeys.map(data => {\r\n            if (someArray.includes(data.key)) {\r\n              data.visibility = true;\r\n            } else {\r\n              data.visibility = false;\r\n            }\r\n          });\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n\r\n  addToOrderButton: ElementRef;\r\n  @ViewChild(\"sbmit\", { static: false }) set _FormRef1(\r\n    FormRef: ElementRef\r\n  ) {\r\n    this.addToOrderButton = FormRef;\r\n  }\r\n  DynamicFormResolver;\r\n\r\n  getDynamicFormData(): Promise<any> {\r\n    setTimeout(() => {\r\n      this.addToOrderButton.nativeElement.click();\r\n    }, 10);\r\n\r\n    return new Promise((res) => (this.DynamicFormResolver = res));\r\n  }\r\n\r\n  ngOnChanges() {\r\n    this.resolveQuestions().then((value) => {\r\n      this.dynamicform = this.qcs.toFormGroup(this.questions);\r\n      this.allQuestions = this.questions;\r\n      //this.keys = this.GetLabel(this.questions,'sectionLabel');\r\n      this.questionsByCategory = this.GroupBy(this.questions, 'sectionLabel');\r\n      this.questions = this.questionsByCategory;\r\n      this.keys = Object.keys(this.questions);\r\n      if (this.keys) {\r\n        this.appQuestionKeys = this.keys.map((x) => {\r\n          let data = {\r\n            key: x,\r\n            visibility: false\r\n          };\r\n          return data;\r\n        });\r\n      }\r\n    });\r\n    //   console.log(\"Controls :\" +this.dependentControls);\r\n    //   this.httpClient.get<any>(\"assets/data/common/dynamicQue.json\").subscribe((res)=>\r\n    //   this.dynamicQuestionData = res\r\n    //  );\r\n    this.dynamicQuestionData = this.dependentControls;\r\n  }\r\n\r\n  ngAfterContentChecked(): void {\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.dynamicform = new FormGroup({\r\n    })\r\n\r\n  }\r\n\r\n  resolveQuestions() {\r\n    var promise = new Promise((resolve) => {\r\n      setTimeout(() => {\r\n        if (this.questions && this.questions.length > 0) {\r\n          resolve(this.questions)\r\n        }\r\n      }, 1);\r\n    });\r\n    return promise;\r\n  }\r\n\r\n  safeCss(style) {\r\n    if (style != \"\") {\r\n      return this.doms.bypassSecurityTrustStyle(style);\r\n    }\r\n  }\r\n\r\n  GroupBy(data, key) {\r\n    return data.reduce(function (rv, x) {\r\n      ((rv[x[key]] = rv[x[key]] || []).push(x));\r\n      (rv[x[key]].sort((a, b) => a.order - b.order));\r\n      return rv;\r\n    }, {});\r\n  }\r\n  GetLabel(data, key) {\r\n    let sectionLabels = data.map(x => {\r\n      return x[key];\r\n    })\r\n    return [...new Set(sectionLabels)];\r\n  }\r\n\r\n  async onSubmit() {\r\n    try {\r\n\r\n      // await this.UpSellingData();\r\n      if (this.dynamicform.valid) {\r\n\r\n        var formValue = this.dynamicform.value;\r\n        this.keyValuePair = this.mapToModel(formValue);\r\n\r\n\r\n        var response: any = {\r\n          keyValuePair: this.keyValuePair,\r\n\r\n        }\r\n        this.DynamicFormResolver(Object.assign([], response));\r\n        this.toaster.info('Order is being processed');\r\n        // this.$AddToOrder.emit(response);\r\n      }\r\n      else {\r\n        this.toaster.error('Please fill all required fields', 'Error');\r\n      }\r\n    }\r\n    catch (ex) {\r\n      console.error(ex);\r\n    }\r\n  }\r\n\r\n  private mapToModel(formValue) {\r\n\r\n    var keyValuePair: DynamicFormKeyValue[] = [];\r\n\r\n    for (var item in formValue) {\r\n\r\n      var isArray = Array.isArray(formValue[item]);\r\n      var value = isArray ? formValue[item].filter(x => x.isChecked) : formValue[item];\r\n\r\n      var dynamicFormKeyValue = new DynamicFormKeyValue();\r\n      dynamicFormKeyValue.ControlId = item;\r\n      dynamicFormKeyValue.Value = value;\r\n\r\n      keyValuePair.push(dynamicFormKeyValue);\r\n    }\r\n\r\n    return keyValuePair;\r\n  }\r\n\r\n  getDisplayCondition(question) {\r\n    try {\r\n      var valuesSplit = question.dependentOnControlValue.split('|');\r\n      var condition = '';\r\n\r\n      if (question.conditionType && question.conditionType === 'OR' && question.dependentOnControlId && valuesSplit.length >= 2) {\r\n        var condition = '';\r\n\r\n        for (var i = 0; i < valuesSplit.length; i++) {\r\n          if (i > 0) {\r\n            condition += ' || ';\r\n          }\r\n          condition += `this.dynamicform.get('${question.dependentOnControlId}')?.value == '${valuesSplit[i]}'`;\r\n        }\r\n\r\n        condition = '(' + condition + ')';\r\n      }\r\n\r\n      else if (question.conditionType && question.conditionType == 'STATIC') {\r\n        condition = question.dependentOnControlId ? `this.${question.dependentOnControlId} == '${question.dependentOnControlValue}'` : '';\r\n      }\r\n      else {\r\n        if (question.dependentOnControlId != '') {\r\n          condition = question.dependentOnControlId ? `this.dynamicform.get('${question.dependentOnControlId}')?.value == '${question.dependentOnControlValue}'` : '';\r\n        }\r\n      }\r\n      return question.dependentOnControlId == \"\" ? true : eval(condition);\r\n    }\r\n    catch (ex) {\r\n      console.log(ex)\r\n    }\r\n\r\n  }\r\n\r\n  getCategoryDisplayCondition(key) {\r\n    if (!key) return false;\r\n\r\n    var response = true;\r\n\r\n    this.questionsByCategory[key].every(question => {\r\n\r\n      var questionCondition = this.getDisplayCondition(question);\r\n      if (!questionCondition) {\r\n        response = false;\r\n        return;\r\n      }\r\n    });\r\n    return response;\r\n  }\r\n\r\n  fileUplaod(files: File[]){\r\n    this.fileUploadEvent.emit(files);\r\n  }\r\n\r\n  private getIndexNo(code, array: any[]) {\r\n\r\n    let val = array.findIndex(x => x == code);\r\n    if (val == -1) {\r\n      return null;\r\n    }\r\n\r\n    return val;\r\n  }\r\n\r\n  private getEntityName(state, subCategoryCode, dynamicValues) {\r\n    var value = '';\r\n    switch (state) {\r\n      case \"DE\":\r\n        {\r\n          var controlId = subCategoryCode == \"SC\" ? \"The_name_of_this_Corporation_is_Corp_DE\" : subCategoryCode == \"LC\" ? \"limited_liability_company_is_LLC_DE\" : \"\";\r\n          value = dynamicValues.find(x => x.ControlId == controlId).Value || \"\";\r\n          break;\r\n        }\r\n      case \"FL\":\r\n        {\r\n          var controlId = subCategoryCode == \"SC\" ? \"Corporate_Name_CORP_FL\" : subCategoryCode == \"LC\" ? \"Limited_Liability_Company_Name_LLC_FL\" : \"\";\r\n          value = dynamicValues.find(x => x.ControlId == controlId).Value || \"\";\r\n          break;\r\n        }\r\n      case \"NV\":\r\n        {\r\n          var controlId = subCategoryCode == \"SC\" ? \"Entity_Name\" : subCategoryCode == \"LC\" ? \"Name_LLC\" : \"\";\r\n          value = dynamicValues.find(x => x.ControlId == controlId).Value || \"\";\r\n          break;\r\n        }\r\n      case \"NY\":\r\n        {\r\n          var controlId = subCategoryCode == \"SC\" ? \"Proposed_Entity_Name_and_Corp_ending_Corp_NY\" : subCategoryCode == \"LC\" ? \"Proposed_Entity_Name_of_the_Limited_Liability_Company_LLC_NY\" : \"\";\r\n          value = dynamicValues.find(x => x.ControlId == controlId).Value || \"\";\r\n          break;\r\n        }\r\n      case \"TX\":\r\n        {\r\n          var controlId = subCategoryCode == \"SC\" ? \"Entity_Name\" : subCategoryCode == \"LC\" ? \"Entity_Name_LLC\" : \"\";\r\n          value = dynamicValues.find(x => x.ControlId == controlId).Value || \"\";\r\n          break;\r\n        }\r\n\r\n    }\r\n    return value;\r\n  }\r\n\r\n\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}