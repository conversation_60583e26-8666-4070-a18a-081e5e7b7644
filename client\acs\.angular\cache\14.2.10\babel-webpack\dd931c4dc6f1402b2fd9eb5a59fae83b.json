{"ast": null, "code": "import { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nexport function pairs(obj, scheduler) {\n  if (!scheduler) {\n    return new Observable(subscriber => {\n      const keys = Object.keys(obj);\n\n      for (let i = 0; i < keys.length && !subscriber.closed; i++) {\n        const key = keys[i];\n\n        if (obj.hasOwnProperty(key)) {\n          subscriber.next([key, obj[key]]);\n        }\n      }\n\n      subscriber.complete();\n    });\n  } else {\n    return new Observable(subscriber => {\n      const keys = Object.keys(obj);\n      const subscription = new Subscription();\n      subscription.add(scheduler.schedule(dispatch, 0, {\n        keys,\n        index: 0,\n        subscriber,\n        subscription,\n        obj\n      }));\n      return subscription;\n    });\n  }\n}\nexport function dispatch(state) {\n  const {\n    keys,\n    index,\n    subscriber,\n    subscription,\n    obj\n  } = state;\n\n  if (!subscriber.closed) {\n    if (index < keys.length) {\n      const key = keys[index];\n      subscriber.next([key, obj[key]]);\n      subscription.add(this.schedule({\n        keys,\n        index: index + 1,\n        subscriber,\n        subscription,\n        obj\n      }));\n    } else {\n      subscriber.complete();\n    }\n  }\n}", "map": {"version": 3, "names": ["Observable", "Subscription", "pairs", "obj", "scheduler", "subscriber", "keys", "Object", "i", "length", "closed", "key", "hasOwnProperty", "next", "complete", "subscription", "add", "schedule", "dispatch", "index", "state"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/pairs.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nexport function pairs(obj, scheduler) {\n    if (!scheduler) {\n        return new Observable(subscriber => {\n            const keys = Object.keys(obj);\n            for (let i = 0; i < keys.length && !subscriber.closed; i++) {\n                const key = keys[i];\n                if (obj.hasOwnProperty(key)) {\n                    subscriber.next([key, obj[key]]);\n                }\n            }\n            subscriber.complete();\n        });\n    }\n    else {\n        return new Observable(subscriber => {\n            const keys = Object.keys(obj);\n            const subscription = new Subscription();\n            subscription.add(scheduler.schedule(dispatch, 0, { keys, index: 0, subscriber, subscription, obj }));\n            return subscription;\n        });\n    }\n}\nexport function dispatch(state) {\n    const { keys, index, subscriber, subscription, obj } = state;\n    if (!subscriber.closed) {\n        if (index < keys.length) {\n            const key = keys[index];\n            subscriber.next([key, obj[key]]);\n            subscription.add(this.schedule({ keys, index: index + 1, subscriber, subscription, obj }));\n        }\n        else {\n            subscriber.complete();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,SAASC,KAAT,CAAeC,GAAf,EAAoBC,SAApB,EAA+B;EAClC,IAAI,CAACA,SAAL,EAAgB;IACZ,OAAO,IAAIJ,UAAJ,CAAeK,UAAU,IAAI;MAChC,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAP,CAAYH,GAAZ,CAAb;;MACA,KAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,IAAI,CAACG,MAAT,IAAmB,CAACJ,UAAU,CAACK,MAA/C,EAAuDF,CAAC,EAAxD,EAA4D;QACxD,MAAMG,GAAG,GAAGL,IAAI,CAACE,CAAD,CAAhB;;QACA,IAAIL,GAAG,CAACS,cAAJ,CAAmBD,GAAnB,CAAJ,EAA6B;UACzBN,UAAU,CAACQ,IAAX,CAAgB,CAACF,GAAD,EAAMR,GAAG,CAACQ,GAAD,CAAT,CAAhB;QACH;MACJ;;MACDN,UAAU,CAACS,QAAX;IACH,CATM,CAAP;EAUH,CAXD,MAYK;IACD,OAAO,IAAId,UAAJ,CAAeK,UAAU,IAAI;MAChC,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAP,CAAYH,GAAZ,CAAb;MACA,MAAMY,YAAY,GAAG,IAAId,YAAJ,EAArB;MACAc,YAAY,CAACC,GAAb,CAAiBZ,SAAS,CAACa,QAAV,CAAmBC,QAAnB,EAA6B,CAA7B,EAAgC;QAAEZ,IAAF;QAAQa,KAAK,EAAE,CAAf;QAAkBd,UAAlB;QAA8BU,YAA9B;QAA4CZ;MAA5C,CAAhC,CAAjB;MACA,OAAOY,YAAP;IACH,CALM,CAAP;EAMH;AACJ;AACD,OAAO,SAASG,QAAT,CAAkBE,KAAlB,EAAyB;EAC5B,MAAM;IAAEd,IAAF;IAAQa,KAAR;IAAed,UAAf;IAA2BU,YAA3B;IAAyCZ;EAAzC,IAAiDiB,KAAvD;;EACA,IAAI,CAACf,UAAU,CAACK,MAAhB,EAAwB;IACpB,IAAIS,KAAK,GAAGb,IAAI,CAACG,MAAjB,EAAyB;MACrB,MAAME,GAAG,GAAGL,IAAI,CAACa,KAAD,CAAhB;MACAd,UAAU,CAACQ,IAAX,CAAgB,CAACF,GAAD,EAAMR,GAAG,CAACQ,GAAD,CAAT,CAAhB;MACAI,YAAY,CAACC,GAAb,CAAiB,KAAKC,QAAL,CAAc;QAAEX,IAAF;QAAQa,KAAK,EAAEA,KAAK,GAAG,CAAvB;QAA0Bd,UAA1B;QAAsCU,YAAtC;QAAoDZ;MAApD,CAAd,CAAjB;IACH,CAJD,MAKK;MACDE,UAAU,CAACS,QAAX;IACH;EACJ;AACJ"}, "metadata": {}, "sourceType": "module"}