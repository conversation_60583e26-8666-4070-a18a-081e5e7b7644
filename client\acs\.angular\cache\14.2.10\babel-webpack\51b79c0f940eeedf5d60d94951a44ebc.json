{"ast": null, "code": "/**\n * @license Angular v14.3.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\nimport { ɵ_global, ɵ$localize } from '@angular/localize';\nexport { ɵ$localize as $localize } from '@angular/localize';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Attach $localize to the global context, as a side-effect of this module.\n\nɵ_global.$localize = ɵ$localize;", "map": {"version": 3, "names": ["ɵ_global", "ɵ$localize", "$localize"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/localize/fesm2020/init.mjs"], "sourcesContent": ["/**\n * @license Angular v14.3.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵ_global, ɵ$localize } from '@angular/localize';\nexport { ɵ$localize as $localize } from '@angular/localize';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Attach $localize to the global context, as a side-effect of this module.\nɵ_global.$localize = ɵ$localize;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AAEA,SAASA,QAAT,EAAmBC,UAAnB,QAAqC,mBAArC;AACA,SAASA,UAAU,IAAIC,SAAvB,QAAwC,mBAAxC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACAF,QAAQ,CAACE,SAAT,GAAqBD,UAArB"}, "metadata": {}, "sourceType": "module"}