{"ast": null, "code": "import { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nexport function scheduleArray(input, scheduler) {\n  return new Observable(subscriber => {\n    const sub = new Subscription();\n    let i = 0;\n    sub.add(scheduler.schedule(function () {\n      if (i === input.length) {\n        subscriber.complete();\n        return;\n      }\n\n      subscriber.next(input[i++]);\n\n      if (!subscriber.closed) {\n        sub.add(this.schedule());\n      }\n    }));\n    return sub;\n  });\n}", "map": {"version": 3, "names": ["Observable", "Subscription", "scheduleArray", "input", "scheduler", "subscriber", "sub", "i", "add", "schedule", "length", "complete", "next", "closed"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/scheduled/scheduleArray.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nexport function scheduleArray(input, scheduler) {\n    return new Observable(subscriber => {\n        const sub = new Subscription();\n        let i = 0;\n        sub.add(scheduler.schedule(function () {\n            if (i === input.length) {\n                subscriber.complete();\n                return;\n            }\n            subscriber.next(input[i++]);\n            if (!subscriber.closed) {\n                sub.add(this.schedule());\n            }\n        }));\n        return sub;\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,SAASC,aAAT,CAAuBC,KAAvB,EAA8BC,SAA9B,EAAyC;EAC5C,OAAO,IAAIJ,UAAJ,CAAeK,UAAU,IAAI;IAChC,MAAMC,GAAG,GAAG,IAAIL,YAAJ,EAAZ;IACA,IAAIM,CAAC,GAAG,CAAR;IACAD,GAAG,CAACE,GAAJ,CAAQJ,SAAS,CAACK,QAAV,CAAmB,YAAY;MACnC,IAAIF,CAAC,KAAKJ,KAAK,CAACO,MAAhB,EAAwB;QACpBL,UAAU,CAACM,QAAX;QACA;MACH;;MACDN,UAAU,CAACO,IAAX,CAAgBT,KAAK,CAACI,CAAC,EAAF,CAArB;;MACA,IAAI,CAACF,UAAU,CAACQ,MAAhB,EAAwB;QACpBP,GAAG,CAACE,GAAJ,CAAQ,KAAKC,QAAL,EAAR;MACH;IACJ,CATO,CAAR;IAUA,OAAOH,GAAP;EACH,CAdM,CAAP;AAeH"}, "metadata": {}, "sourceType": "module"}