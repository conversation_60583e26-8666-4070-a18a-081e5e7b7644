{"ast": null, "code": "import { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function exhaust() {\n  return source => source.lift(new SwitchFirstOperator());\n}\n\nclass SwitchFirstOperator {\n  call(subscriber, source) {\n    return source.subscribe(new SwitchFirstSubscriber(subscriber));\n  }\n\n}\n\nclass SwitchFirstSubscriber extends SimpleOuterSubscriber {\n  constructor(destination) {\n    super(destination);\n    this.hasCompleted = false;\n    this.hasSubscription = false;\n  }\n\n  _next(value) {\n    if (!this.hasSubscription) {\n      this.hasSubscription = true;\n      this.add(innerSubscribe(value, new SimpleInnerSubscriber(this)));\n    }\n  }\n\n  _complete() {\n    this.hasCompleted = true;\n\n    if (!this.hasSubscription) {\n      this.destination.complete();\n    }\n  }\n\n  notifyComplete() {\n    this.hasSubscription = false;\n\n    if (this.hasCompleted) {\n      this.destination.complete();\n    }\n  }\n\n}", "map": {"version": 3, "names": ["SimpleOuterSubscriber", "innerSubscribe", "SimpleInnerSubscriber", "exhaust", "source", "lift", "SwitchFirstOperator", "call", "subscriber", "subscribe", "SwitchFirstSubscriber", "constructor", "destination", "hasCompleted", "hasSubscription", "_next", "value", "add", "_complete", "complete", "notifyComplete"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/exhaust.js"], "sourcesContent": ["import { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function exhaust() {\n    return (source) => source.lift(new SwitchFirstOperator());\n}\nclass SwitchFirstOperator {\n    call(subscriber, source) {\n        return source.subscribe(new SwitchFirstSubscriber(subscriber));\n    }\n}\nclass SwitchFirstSubscriber extends SimpleOuterSubscriber {\n    constructor(destination) {\n        super(destination);\n        this.hasCompleted = false;\n        this.hasSubscription = false;\n    }\n    _next(value) {\n        if (!this.hasSubscription) {\n            this.hasSubscription = true;\n            this.add(innerSubscribe(value, new SimpleInnerSubscriber(this)));\n        }\n    }\n    _complete() {\n        this.hasCompleted = true;\n        if (!this.hasSubscription) {\n            this.destination.complete();\n        }\n    }\n    notifyComplete() {\n        this.hasSubscription = false;\n        if (this.hasCompleted) {\n            this.destination.complete();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,qBAAT,EAAgCC,cAAhC,EAAgDC,qBAAhD,QAA6E,mBAA7E;AACA,OAAO,SAASC,OAAT,GAAmB;EACtB,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,mBAAJ,EAAZ,CAAnB;AACH;;AACD,MAAMA,mBAAN,CAA0B;EACtBC,IAAI,CAACC,UAAD,EAAaJ,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACK,SAAP,CAAiB,IAAIC,qBAAJ,CAA0BF,UAA1B,CAAjB,CAAP;EACH;;AAHqB;;AAK1B,MAAME,qBAAN,SAAoCV,qBAApC,CAA0D;EACtDW,WAAW,CAACC,WAAD,EAAc;IACrB,MAAMA,WAAN;IACA,KAAKC,YAAL,GAAoB,KAApB;IACA,KAAKC,eAAL,GAAuB,KAAvB;EACH;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACT,IAAI,CAAC,KAAKF,eAAV,EAA2B;MACvB,KAAKA,eAAL,GAAuB,IAAvB;MACA,KAAKG,GAAL,CAAShB,cAAc,CAACe,KAAD,EAAQ,IAAId,qBAAJ,CAA0B,IAA1B,CAAR,CAAvB;IACH;EACJ;;EACDgB,SAAS,GAAG;IACR,KAAKL,YAAL,GAAoB,IAApB;;IACA,IAAI,CAAC,KAAKC,eAAV,EAA2B;MACvB,KAAKF,WAAL,CAAiBO,QAAjB;IACH;EACJ;;EACDC,cAAc,GAAG;IACb,KAAKN,eAAL,GAAuB,KAAvB;;IACA,IAAI,KAAKD,YAAT,EAAuB;MACnB,KAAKD,WAAL,CAAiBO,QAAjB;IACH;EACJ;;AAvBqD"}, "metadata": {}, "sourceType": "module"}