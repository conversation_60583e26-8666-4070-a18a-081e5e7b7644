{"ast": null, "code": "export function identity(x) {\n  return x;\n}", "map": {"version": 3, "names": ["identity", "x"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/identity.js"], "sourcesContent": ["export function identity(x) {\n    return x;\n}\n"], "mappings": "AAAA,OAAO,SAASA,QAAT,CAAkBC,CAAlB,EAAqB;EACxB,OAAOA,CAAP;AACH"}, "metadata": {}, "sourceType": "module"}