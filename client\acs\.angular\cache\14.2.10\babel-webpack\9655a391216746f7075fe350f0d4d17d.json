{"ast": null, "code": "import { isScheduler } from '../util/isScheduler';\nimport { isArray } from '../util/isArray';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { fromArray } from './fromArray';\nconst NONE = {};\nexport function combineLatest(...observables) {\n  let resultSelector = undefined;\n  let scheduler = undefined;\n\n  if (isScheduler(observables[observables.length - 1])) {\n    scheduler = observables.pop();\n  }\n\n  if (typeof observables[observables.length - 1] === 'function') {\n    resultSelector = observables.pop();\n  }\n\n  if (observables.length === 1 && isArray(observables[0])) {\n    observables = observables[0];\n  }\n\n  return fromArray(observables, scheduler).lift(new CombineLatestOperator(resultSelector));\n}\nexport class CombineLatestOperator {\n  constructor(resultSelector) {\n    this.resultSelector = resultSelector;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new CombineLatestSubscriber(subscriber, this.resultSelector));\n  }\n\n}\nexport class CombineLatestSubscriber extends OuterSubscriber {\n  constructor(destination, resultSelector) {\n    super(destination);\n    this.resultSelector = resultSelector;\n    this.active = 0;\n    this.values = [];\n    this.observables = [];\n  }\n\n  _next(observable) {\n    this.values.push(NONE);\n    this.observables.push(observable);\n  }\n\n  _complete() {\n    const observables = this.observables;\n    const len = observables.length;\n\n    if (len === 0) {\n      this.destination.complete();\n    } else {\n      this.active = len;\n      this.toRespond = len;\n\n      for (let i = 0; i < len; i++) {\n        const observable = observables[i];\n        this.add(subscribeToResult(this, observable, undefined, i));\n      }\n    }\n  }\n\n  notifyComplete(unused) {\n    if ((this.active -= 1) === 0) {\n      this.destination.complete();\n    }\n  }\n\n  notifyNext(_outerValue, innerValue, outerIndex) {\n    const values = this.values;\n    const oldVal = values[outerIndex];\n    const toRespond = !this.toRespond ? 0 : oldVal === NONE ? --this.toRespond : this.toRespond;\n    values[outerIndex] = innerValue;\n\n    if (toRespond === 0) {\n      if (this.resultSelector) {\n        this._tryResultSelector(values);\n      } else {\n        this.destination.next(values.slice());\n      }\n    }\n  }\n\n  _tryResultSelector(values) {\n    let result;\n\n    try {\n      result = this.resultSelector.apply(this, values);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n\n    this.destination.next(result);\n  }\n\n}", "map": {"version": 3, "names": ["isScheduler", "isArray", "OuterSubscriber", "subscribeToResult", "fromArray", "NONE", "combineLatest", "observables", "resultSelector", "undefined", "scheduler", "length", "pop", "lift", "CombineLatestOperator", "constructor", "call", "subscriber", "source", "subscribe", "CombineLatestSubscriber", "destination", "active", "values", "_next", "observable", "push", "_complete", "len", "complete", "toRespond", "i", "add", "notifyComplete", "unused", "notifyNext", "_outerValue", "innerValue", "outerIndex", "oldVal", "_tryResultSelector", "next", "slice", "result", "apply", "err", "error"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/combineLatest.js"], "sourcesContent": ["import { isScheduler } from '../util/isScheduler';\nimport { isArray } from '../util/isArray';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { fromArray } from './fromArray';\nconst NONE = {};\nexport function combineLatest(...observables) {\n    let resultSelector = undefined;\n    let scheduler = undefined;\n    if (isScheduler(observables[observables.length - 1])) {\n        scheduler = observables.pop();\n    }\n    if (typeof observables[observables.length - 1] === 'function') {\n        resultSelector = observables.pop();\n    }\n    if (observables.length === 1 && isArray(observables[0])) {\n        observables = observables[0];\n    }\n    return fromArray(observables, scheduler).lift(new CombineLatestOperator(resultSelector));\n}\nexport class CombineLatestOperator {\n    constructor(resultSelector) {\n        this.resultSelector = resultSelector;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new CombineLatestSubscriber(subscriber, this.resultSelector));\n    }\n}\nexport class CombineLatestSubscriber extends OuterSubscriber {\n    constructor(destination, resultSelector) {\n        super(destination);\n        this.resultSelector = resultSelector;\n        this.active = 0;\n        this.values = [];\n        this.observables = [];\n    }\n    _next(observable) {\n        this.values.push(NONE);\n        this.observables.push(observable);\n    }\n    _complete() {\n        const observables = this.observables;\n        const len = observables.length;\n        if (len === 0) {\n            this.destination.complete();\n        }\n        else {\n            this.active = len;\n            this.toRespond = len;\n            for (let i = 0; i < len; i++) {\n                const observable = observables[i];\n                this.add(subscribeToResult(this, observable, undefined, i));\n            }\n        }\n    }\n    notifyComplete(unused) {\n        if ((this.active -= 1) === 0) {\n            this.destination.complete();\n        }\n    }\n    notifyNext(_outerValue, innerValue, outerIndex) {\n        const values = this.values;\n        const oldVal = values[outerIndex];\n        const toRespond = !this.toRespond\n            ? 0\n            : oldVal === NONE ? --this.toRespond : this.toRespond;\n        values[outerIndex] = innerValue;\n        if (toRespond === 0) {\n            if (this.resultSelector) {\n                this._tryResultSelector(values);\n            }\n            else {\n                this.destination.next(values.slice());\n            }\n        }\n    }\n    _tryResultSelector(values) {\n        let result;\n        try {\n            result = this.resultSelector.apply(this, values);\n        }\n        catch (err) {\n            this.destination.error(err);\n            return;\n        }\n        this.destination.next(result);\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAT,QAA4B,qBAA5B;AACA,SAASC,OAAT,QAAwB,iBAAxB;AACA,SAASC,eAAT,QAAgC,oBAAhC;AACA,SAASC,iBAAT,QAAkC,2BAAlC;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,MAAMC,IAAI,GAAG,EAAb;AACA,OAAO,SAASC,aAAT,CAAuB,GAAGC,WAA1B,EAAuC;EAC1C,IAAIC,cAAc,GAAGC,SAArB;EACA,IAAIC,SAAS,GAAGD,SAAhB;;EACA,IAAIT,WAAW,CAACO,WAAW,CAACA,WAAW,CAACI,MAAZ,GAAqB,CAAtB,CAAZ,CAAf,EAAsD;IAClDD,SAAS,GAAGH,WAAW,CAACK,GAAZ,EAAZ;EACH;;EACD,IAAI,OAAOL,WAAW,CAACA,WAAW,CAACI,MAAZ,GAAqB,CAAtB,CAAlB,KAA+C,UAAnD,EAA+D;IAC3DH,cAAc,GAAGD,WAAW,CAACK,GAAZ,EAAjB;EACH;;EACD,IAAIL,WAAW,CAACI,MAAZ,KAAuB,CAAvB,IAA4BV,OAAO,CAACM,WAAW,CAAC,CAAD,CAAZ,CAAvC,EAAyD;IACrDA,WAAW,GAAGA,WAAW,CAAC,CAAD,CAAzB;EACH;;EACD,OAAOH,SAAS,CAACG,WAAD,EAAcG,SAAd,CAAT,CAAkCG,IAAlC,CAAuC,IAAIC,qBAAJ,CAA0BN,cAA1B,CAAvC,CAAP;AACH;AACD,OAAO,MAAMM,qBAAN,CAA4B;EAC/BC,WAAW,CAACP,cAAD,EAAiB;IACxB,KAAKA,cAAL,GAAsBA,cAAtB;EACH;;EACDQ,IAAI,CAACC,UAAD,EAAaC,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACC,SAAP,CAAiB,IAAIC,uBAAJ,CAA4BH,UAA5B,EAAwC,KAAKT,cAA7C,CAAjB,CAAP;EACH;;AAN8B;AAQnC,OAAO,MAAMY,uBAAN,SAAsClB,eAAtC,CAAsD;EACzDa,WAAW,CAACM,WAAD,EAAcb,cAAd,EAA8B;IACrC,MAAMa,WAAN;IACA,KAAKb,cAAL,GAAsBA,cAAtB;IACA,KAAKc,MAAL,GAAc,CAAd;IACA,KAAKC,MAAL,GAAc,EAAd;IACA,KAAKhB,WAAL,GAAmB,EAAnB;EACH;;EACDiB,KAAK,CAACC,UAAD,EAAa;IACd,KAAKF,MAAL,CAAYG,IAAZ,CAAiBrB,IAAjB;IACA,KAAKE,WAAL,CAAiBmB,IAAjB,CAAsBD,UAAtB;EACH;;EACDE,SAAS,GAAG;IACR,MAAMpB,WAAW,GAAG,KAAKA,WAAzB;IACA,MAAMqB,GAAG,GAAGrB,WAAW,CAACI,MAAxB;;IACA,IAAIiB,GAAG,KAAK,CAAZ,EAAe;MACX,KAAKP,WAAL,CAAiBQ,QAAjB;IACH,CAFD,MAGK;MACD,KAAKP,MAAL,GAAcM,GAAd;MACA,KAAKE,SAAL,GAAiBF,GAAjB;;MACA,KAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,GAApB,EAAyBG,CAAC,EAA1B,EAA8B;QAC1B,MAAMN,UAAU,GAAGlB,WAAW,CAACwB,CAAD,CAA9B;QACA,KAAKC,GAAL,CAAS7B,iBAAiB,CAAC,IAAD,EAAOsB,UAAP,EAAmBhB,SAAnB,EAA8BsB,CAA9B,CAA1B;MACH;IACJ;EACJ;;EACDE,cAAc,CAACC,MAAD,EAAS;IACnB,IAAI,CAAC,KAAKZ,MAAL,IAAe,CAAhB,MAAuB,CAA3B,EAA8B;MAC1B,KAAKD,WAAL,CAAiBQ,QAAjB;IACH;EACJ;;EACDM,UAAU,CAACC,WAAD,EAAcC,UAAd,EAA0BC,UAA1B,EAAsC;IAC5C,MAAMf,MAAM,GAAG,KAAKA,MAApB;IACA,MAAMgB,MAAM,GAAGhB,MAAM,CAACe,UAAD,CAArB;IACA,MAAMR,SAAS,GAAG,CAAC,KAAKA,SAAN,GACZ,CADY,GAEZS,MAAM,KAAKlC,IAAX,GAAkB,EAAE,KAAKyB,SAAzB,GAAqC,KAAKA,SAFhD;IAGAP,MAAM,CAACe,UAAD,CAAN,GAAqBD,UAArB;;IACA,IAAIP,SAAS,KAAK,CAAlB,EAAqB;MACjB,IAAI,KAAKtB,cAAT,EAAyB;QACrB,KAAKgC,kBAAL,CAAwBjB,MAAxB;MACH,CAFD,MAGK;QACD,KAAKF,WAAL,CAAiBoB,IAAjB,CAAsBlB,MAAM,CAACmB,KAAP,EAAtB;MACH;IACJ;EACJ;;EACDF,kBAAkB,CAACjB,MAAD,EAAS;IACvB,IAAIoB,MAAJ;;IACA,IAAI;MACAA,MAAM,GAAG,KAAKnC,cAAL,CAAoBoC,KAApB,CAA0B,IAA1B,EAAgCrB,MAAhC,CAAT;IACH,CAFD,CAGA,OAAOsB,GAAP,EAAY;MACR,KAAKxB,WAAL,CAAiByB,KAAjB,CAAuBD,GAAvB;MACA;IACH;;IACD,KAAKxB,WAAL,CAAiBoB,IAAjB,CAAsBE,MAAtB;EACH;;AA1DwD"}, "metadata": {}, "sourceType": "module"}