{"ast": null, "code": "export function isDate(value) {\n  return value instanceof Date && !isNaN(+value);\n}", "map": {"version": 3, "names": ["isDate", "value", "Date", "isNaN"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/isDate.js"], "sourcesContent": ["export function isDate(value) {\n    return value instanceof Date && !isNaN(+value);\n}\n"], "mappings": "AAAA,OAAO,SAASA,MAAT,CAAgBC,KAAhB,EAAuB;EAC1B,OAAOA,KAAK,YAAYC,IAAjB,IAAyB,CAACC,KAAK,CAAC,CAACF,KAAF,CAAtC;AACH"}, "metadata": {}, "sourceType": "module"}