{"ast": null, "code": ";\n\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./sha1\"), require(\"./hmac\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./sha1\", \"./hmac\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var Base = C_lib.Base;\n    var WordArray = C_lib.WordArray;\n    var C_algo = C.algo;\n    var SHA1 = C_algo.SHA1;\n    var HMAC = C_algo.HMAC;\n    /**\n     * Password-Based Key Derivation Function 2 algorithm.\n     */\n\n    var PBKDF2 = C_algo.PBKDF2 = Base.extend({\n      /**\n       * Configuration options.\n       *\n       * @property {number} keySize The key size in words to generate. Default: 4 (128 bits)\n       * @property {Hasher} hasher The hasher to use. Default: SHA1\n       * @property {number} iterations The number of iterations to perform. Default: 1\n       */\n      cfg: Base.extend({\n        keySize: 128 / 32,\n        hasher: SHA1,\n        iterations: 1\n      }),\n\n      /**\n       * Initializes a newly created key derivation function.\n       *\n       * @param {Object} cfg (Optional) The configuration options to use for the derivation.\n       *\n       * @example\n       *\n       *     var kdf = CryptoJS.algo.PBKDF2.create();\n       *     var kdf = CryptoJS.algo.PBKDF2.create({ keySize: 8 });\n       *     var kdf = CryptoJS.algo.PBKDF2.create({ keySize: 8, iterations: 1000 });\n       */\n      init: function (cfg) {\n        this.cfg = this.cfg.extend(cfg);\n      },\n\n      /**\n       * Computes the Password-Based Key Derivation Function 2.\n       *\n       * @param {WordArray|string} password The password.\n       * @param {WordArray|string} salt A salt.\n       *\n       * @return {WordArray} The derived key.\n       *\n       * @example\n       *\n       *     var key = kdf.compute(password, salt);\n       */\n      compute: function (password, salt) {\n        // Shortcut\n        var cfg = this.cfg; // Init HMAC\n\n        var hmac = HMAC.create(cfg.hasher, password); // Initial values\n\n        var derivedKey = WordArray.create();\n        var blockIndex = WordArray.create([0x00000001]); // Shortcuts\n\n        var derivedKeyWords = derivedKey.words;\n        var blockIndexWords = blockIndex.words;\n        var keySize = cfg.keySize;\n        var iterations = cfg.iterations; // Generate key\n\n        while (derivedKeyWords.length < keySize) {\n          var block = hmac.update(salt).finalize(blockIndex);\n          hmac.reset(); // Shortcuts\n\n          var blockWords = block.words;\n          var blockWordsLength = blockWords.length; // Iterations\n\n          var intermediate = block;\n\n          for (var i = 1; i < iterations; i++) {\n            intermediate = hmac.finalize(intermediate);\n            hmac.reset(); // Shortcut\n\n            var intermediateWords = intermediate.words; // XOR intermediate with block\n\n            for (var j = 0; j < blockWordsLength; j++) {\n              blockWords[j] ^= intermediateWords[j];\n            }\n          }\n\n          derivedKey.concat(block);\n          blockIndexWords[0]++;\n        }\n\n        derivedKey.sigBytes = keySize * 4;\n        return derivedKey;\n      }\n    });\n    /**\n     * Computes the Password-Based Key Derivation Function 2.\n     *\n     * @param {WordArray|string} password The password.\n     * @param {WordArray|string} salt A salt.\n     * @param {Object} cfg (Optional) The configuration options to use for this computation.\n     *\n     * @return {WordArray} The derived key.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var key = CryptoJS.PBKDF2(password, salt);\n     *     var key = CryptoJS.PBKDF2(password, salt, { keySize: 8 });\n     *     var key = CryptoJS.PBKDF2(password, salt, { keySize: 8, iterations: 1000 });\n     */\n\n    C.PBKDF2 = function (password, salt, cfg) {\n      return PBKDF2.create(cfg).compute(password, salt);\n    };\n  })();\n\n  return CryptoJS.PBKDF2;\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "C", "C_lib", "lib", "Base", "WordArray", "C_algo", "algo", "SHA1", "HMAC", "PBKDF2", "extend", "cfg", "keySize", "hasher", "iterations", "init", "compute", "password", "salt", "hmac", "create", "<PERSON><PERSON><PERSON>", "blockIndex", "derived<PERSON>eyWords", "words", "blockIndexWords", "length", "block", "update", "finalize", "reset", "blockWords", "blockWordsLength", "intermediate", "i", "intermediateWords", "j", "concat", "sigBytes"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/crypto-js/pbkdf2.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./sha1\"), require(\"./hmac\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./sha1\", \"./hmac\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t(function () {\n\t    // Shortcuts\n\t    var C = CryptoJS;\n\t    var C_lib = C.lib;\n\t    var Base = C_lib.Base;\n\t    var WordArray = C_lib.WordArray;\n\t    var C_algo = C.algo;\n\t    var SHA1 = C_algo.SHA1;\n\t    var HMAC = C_algo.HMAC;\n\n\t    /**\n\t     * Password-Based Key Derivation Function 2 algorithm.\n\t     */\n\t    var PBKDF2 = C_algo.PBKDF2 = Base.extend({\n\t        /**\n\t         * Configuration options.\n\t         *\n\t         * @property {number} keySize The key size in words to generate. Default: 4 (128 bits)\n\t         * @property {Hasher} hasher The hasher to use. Default: SHA1\n\t         * @property {number} iterations The number of iterations to perform. Default: 1\n\t         */\n\t        cfg: Base.extend({\n\t            keySize: 128/32,\n\t            hasher: SHA1,\n\t            iterations: 1\n\t        }),\n\n\t        /**\n\t         * Initializes a newly created key derivation function.\n\t         *\n\t         * @param {Object} cfg (Optional) The configuration options to use for the derivation.\n\t         *\n\t         * @example\n\t         *\n\t         *     var kdf = CryptoJS.algo.PBKDF2.create();\n\t         *     var kdf = CryptoJS.algo.PBKDF2.create({ keySize: 8 });\n\t         *     var kdf = CryptoJS.algo.PBKDF2.create({ keySize: 8, iterations: 1000 });\n\t         */\n\t        init: function (cfg) {\n\t            this.cfg = this.cfg.extend(cfg);\n\t        },\n\n\t        /**\n\t         * Computes the Password-Based Key Derivation Function 2.\n\t         *\n\t         * @param {WordArray|string} password The password.\n\t         * @param {WordArray|string} salt A salt.\n\t         *\n\t         * @return {WordArray} The derived key.\n\t         *\n\t         * @example\n\t         *\n\t         *     var key = kdf.compute(password, salt);\n\t         */\n\t        compute: function (password, salt) {\n\t            // Shortcut\n\t            var cfg = this.cfg;\n\n\t            // Init HMAC\n\t            var hmac = HMAC.create(cfg.hasher, password);\n\n\t            // Initial values\n\t            var derivedKey = WordArray.create();\n\t            var blockIndex = WordArray.create([0x00000001]);\n\n\t            // Shortcuts\n\t            var derivedKeyWords = derivedKey.words;\n\t            var blockIndexWords = blockIndex.words;\n\t            var keySize = cfg.keySize;\n\t            var iterations = cfg.iterations;\n\n\t            // Generate key\n\t            while (derivedKeyWords.length < keySize) {\n\t                var block = hmac.update(salt).finalize(blockIndex);\n\t                hmac.reset();\n\n\t                // Shortcuts\n\t                var blockWords = block.words;\n\t                var blockWordsLength = blockWords.length;\n\n\t                // Iterations\n\t                var intermediate = block;\n\t                for (var i = 1; i < iterations; i++) {\n\t                    intermediate = hmac.finalize(intermediate);\n\t                    hmac.reset();\n\n\t                    // Shortcut\n\t                    var intermediateWords = intermediate.words;\n\n\t                    // XOR intermediate with block\n\t                    for (var j = 0; j < blockWordsLength; j++) {\n\t                        blockWords[j] ^= intermediateWords[j];\n\t                    }\n\t                }\n\n\t                derivedKey.concat(block);\n\t                blockIndexWords[0]++;\n\t            }\n\t            derivedKey.sigBytes = keySize * 4;\n\n\t            return derivedKey;\n\t        }\n\t    });\n\n\t    /**\n\t     * Computes the Password-Based Key Derivation Function 2.\n\t     *\n\t     * @param {WordArray|string} password The password.\n\t     * @param {WordArray|string} salt A salt.\n\t     * @param {Object} cfg (Optional) The configuration options to use for this computation.\n\t     *\n\t     * @return {WordArray} The derived key.\n\t     *\n\t     * @static\n\t     *\n\t     * @example\n\t     *\n\t     *     var key = CryptoJS.PBKDF2(password, salt);\n\t     *     var key = CryptoJS.PBKDF2(password, salt, { keySize: 8 });\n\t     *     var key = CryptoJS.PBKDF2(password, salt, { keySize: 8, iterations: 1000 });\n\t     */\n\t    C.PBKDF2 = function (password, salt, cfg) {\n\t        return PBKDF2.create(cfg).compute(password, salt);\n\t    };\n\t}());\n\n\n\treturn CryptoJS.PBKDF2;\n\n}));"], "mappings": "AAAA;;AAAE,WAAUA,IAAV,EAAgBC,OAAhB,EAAyBC,KAAzB,EAAgC;EACjC,IAAI,OAAOC,OAAP,KAAmB,QAAvB,EAAiC;IAChC;IACAC,MAAM,CAACD,OAAP,GAAiBA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAD,CAAR,EAAoBA,OAAO,CAAC,QAAD,CAA3B,EAAuCA,OAAO,CAAC,QAAD,CAA9C,CAAlC;EACA,CAHD,MAIK,IAAI,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAA3C,EAAgD;IACpD;IACAD,MAAM,CAAC,CAAC,QAAD,EAAW,QAAX,EAAqB,QAArB,CAAD,EAAiCL,OAAjC,CAAN;EACA,CAHI,MAIA;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAN,CAAP;EACA;AACD,CAbC,EAaA,IAbA,EAaM,UAAUA,QAAV,EAAoB;EAE1B,aAAY;IACT;IACA,IAAIC,CAAC,GAAGD,QAAR;IACA,IAAIE,KAAK,GAAGD,CAAC,CAACE,GAAd;IACA,IAAIC,IAAI,GAAGF,KAAK,CAACE,IAAjB;IACA,IAAIC,SAAS,GAAGH,KAAK,CAACG,SAAtB;IACA,IAAIC,MAAM,GAAGL,CAAC,CAACM,IAAf;IACA,IAAIC,IAAI,GAAGF,MAAM,CAACE,IAAlB;IACA,IAAIC,IAAI,GAAGH,MAAM,CAACG,IAAlB;IAEA;AACL;AACA;;IACK,IAAIC,MAAM,GAAGJ,MAAM,CAACI,MAAP,GAAgBN,IAAI,CAACO,MAAL,CAAY;MACrC;AACT;AACA;AACA;AACA;AACA;AACA;MACSC,GAAG,EAAER,IAAI,CAACO,MAAL,CAAY;QACbE,OAAO,EAAE,MAAI,EADA;QAEbC,MAAM,EAAEN,IAFK;QAGbO,UAAU,EAAE;MAHC,CAAZ,CARgC;;MAcrC;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSC,IAAI,EAAE,UAAUJ,GAAV,EAAe;QACjB,KAAKA,GAAL,GAAW,KAAKA,GAAL,CAASD,MAAT,CAAgBC,GAAhB,CAAX;MACH,CA3BoC;;MA6BrC;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSK,OAAO,EAAE,UAAUC,QAAV,EAAoBC,IAApB,EAA0B;QAC/B;QACA,IAAIP,GAAG,GAAG,KAAKA,GAAf,CAF+B,CAI/B;;QACA,IAAIQ,IAAI,GAAGX,IAAI,CAACY,MAAL,CAAYT,GAAG,CAACE,MAAhB,EAAwBI,QAAxB,CAAX,CAL+B,CAO/B;;QACA,IAAII,UAAU,GAAGjB,SAAS,CAACgB,MAAV,EAAjB;QACA,IAAIE,UAAU,GAAGlB,SAAS,CAACgB,MAAV,CAAiB,CAAC,UAAD,CAAjB,CAAjB,CAT+B,CAW/B;;QACA,IAAIG,eAAe,GAAGF,UAAU,CAACG,KAAjC;QACA,IAAIC,eAAe,GAAGH,UAAU,CAACE,KAAjC;QACA,IAAIZ,OAAO,GAAGD,GAAG,CAACC,OAAlB;QACA,IAAIE,UAAU,GAAGH,GAAG,CAACG,UAArB,CAf+B,CAiB/B;;QACA,OAAOS,eAAe,CAACG,MAAhB,GAAyBd,OAAhC,EAAyC;UACrC,IAAIe,KAAK,GAAGR,IAAI,CAACS,MAAL,CAAYV,IAAZ,EAAkBW,QAAlB,CAA2BP,UAA3B,CAAZ;UACAH,IAAI,CAACW,KAAL,GAFqC,CAIrC;;UACA,IAAIC,UAAU,GAAGJ,KAAK,CAACH,KAAvB;UACA,IAAIQ,gBAAgB,GAAGD,UAAU,CAACL,MAAlC,CANqC,CAQrC;;UACA,IAAIO,YAAY,GAAGN,KAAnB;;UACA,KAAK,IAAIO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGpB,UAApB,EAAgCoB,CAAC,EAAjC,EAAqC;YACjCD,YAAY,GAAGd,IAAI,CAACU,QAAL,CAAcI,YAAd,CAAf;YACAd,IAAI,CAACW,KAAL,GAFiC,CAIjC;;YACA,IAAIK,iBAAiB,GAAGF,YAAY,CAACT,KAArC,CALiC,CAOjC;;YACA,KAAK,IAAIY,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,gBAApB,EAAsCI,CAAC,EAAvC,EAA2C;cACvCL,UAAU,CAACK,CAAD,CAAV,IAAiBD,iBAAiB,CAACC,CAAD,CAAlC;YACH;UACJ;;UAEDf,UAAU,CAACgB,MAAX,CAAkBV,KAAlB;UACAF,eAAe,CAAC,CAAD,CAAf;QACH;;QACDJ,UAAU,CAACiB,QAAX,GAAsB1B,OAAO,GAAG,CAAhC;QAEA,OAAOS,UAAP;MACH;IAxFoC,CAAZ,CAA7B;IA2FA;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IACKrB,CAAC,CAACS,MAAF,GAAW,UAAUQ,QAAV,EAAoBC,IAApB,EAA0BP,GAA1B,EAA+B;MACtC,OAAOF,MAAM,CAACW,MAAP,CAAcT,GAAd,EAAmBK,OAAnB,CAA2BC,QAA3B,EAAqCC,IAArC,CAAP;IACH,CAFD;EAGH,CA5HA,GAAD;;EA+HA,OAAOnB,QAAQ,CAACU,MAAhB;AAEA,CAhJC,CAAD"}, "metadata": {}, "sourceType": "script"}