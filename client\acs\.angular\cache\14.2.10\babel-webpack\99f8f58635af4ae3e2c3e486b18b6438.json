{"ast": null, "code": "import { Immediate } from '../util/Immediate';\nimport { AsyncAction } from './AsyncAction';\nexport class AsapAction extends AsyncAction {\n  constructor(scheduler, work) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n  }\n\n  requestAsyncId(scheduler, id, delay = 0) {\n    if (delay !== null && delay > 0) {\n      return super.requestAsyncId(scheduler, id, delay);\n    }\n\n    scheduler.actions.push(this);\n    return scheduler.scheduled || (scheduler.scheduled = Immediate.setImmediate(scheduler.flush.bind(scheduler, null)));\n  }\n\n  recycleAsyncId(scheduler, id, delay = 0) {\n    if (delay !== null && delay > 0 || delay === null && this.delay > 0) {\n      return super.recycleAsyncId(scheduler, id, delay);\n    }\n\n    if (scheduler.actions.length === 0) {\n      Immediate.clearImmediate(id);\n      scheduler.scheduled = undefined;\n    }\n\n    return undefined;\n  }\n\n}", "map": {"version": 3, "names": ["Immediate", "AsyncAction", "AsapAction", "constructor", "scheduler", "work", "requestAsyncId", "id", "delay", "actions", "push", "scheduled", "setImmediate", "flush", "bind", "recycleAsyncId", "length", "clearImmediate", "undefined"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/scheduler/AsapAction.js"], "sourcesContent": ["import { Immediate } from '../util/Immediate';\nimport { AsyncAction } from './AsyncAction';\nexport class AsapAction extends AsyncAction {\n    constructor(scheduler, work) {\n        super(scheduler, work);\n        this.scheduler = scheduler;\n        this.work = work;\n    }\n    requestAsyncId(scheduler, id, delay = 0) {\n        if (delay !== null && delay > 0) {\n            return super.requestAsyncId(scheduler, id, delay);\n        }\n        scheduler.actions.push(this);\n        return scheduler.scheduled || (scheduler.scheduled = Immediate.setImmediate(scheduler.flush.bind(scheduler, null)));\n    }\n    recycleAsyncId(scheduler, id, delay = 0) {\n        if ((delay !== null && delay > 0) || (delay === null && this.delay > 0)) {\n            return super.recycleAsyncId(scheduler, id, delay);\n        }\n        if (scheduler.actions.length === 0) {\n            Immediate.clearImmediate(id);\n            scheduler.scheduled = undefined;\n        }\n        return undefined;\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAT,QAA0B,mBAA1B;AACA,SAASC,WAAT,QAA4B,eAA5B;AACA,OAAO,MAAMC,UAAN,SAAyBD,WAAzB,CAAqC;EACxCE,WAAW,CAACC,SAAD,EAAYC,IAAZ,EAAkB;IACzB,MAAMD,SAAN,EAAiBC,IAAjB;IACA,KAAKD,SAAL,GAAiBA,SAAjB;IACA,KAAKC,IAAL,GAAYA,IAAZ;EACH;;EACDC,cAAc,CAACF,SAAD,EAAYG,EAAZ,EAAgBC,KAAK,GAAG,CAAxB,EAA2B;IACrC,IAAIA,KAAK,KAAK,IAAV,IAAkBA,KAAK,GAAG,CAA9B,EAAiC;MAC7B,OAAO,MAAMF,cAAN,CAAqBF,SAArB,EAAgCG,EAAhC,EAAoCC,KAApC,CAAP;IACH;;IACDJ,SAAS,CAACK,OAAV,CAAkBC,IAAlB,CAAuB,IAAvB;IACA,OAAON,SAAS,CAACO,SAAV,KAAwBP,SAAS,CAACO,SAAV,GAAsBX,SAAS,CAACY,YAAV,CAAuBR,SAAS,CAACS,KAAV,CAAgBC,IAAhB,CAAqBV,SAArB,EAAgC,IAAhC,CAAvB,CAA9C,CAAP;EACH;;EACDW,cAAc,CAACX,SAAD,EAAYG,EAAZ,EAAgBC,KAAK,GAAG,CAAxB,EAA2B;IACrC,IAAKA,KAAK,KAAK,IAAV,IAAkBA,KAAK,GAAG,CAA3B,IAAkCA,KAAK,KAAK,IAAV,IAAkB,KAAKA,KAAL,GAAa,CAArE,EAAyE;MACrE,OAAO,MAAMO,cAAN,CAAqBX,SAArB,EAAgCG,EAAhC,EAAoCC,KAApC,CAAP;IACH;;IACD,IAAIJ,SAAS,CAACK,OAAV,CAAkBO,MAAlB,KAA6B,CAAjC,EAAoC;MAChChB,SAAS,CAACiB,cAAV,CAAyBV,EAAzB;MACAH,SAAS,CAACO,SAAV,GAAsBO,SAAtB;IACH;;IACD,OAAOA,SAAP;EACH;;AAtBuC"}, "metadata": {}, "sourceType": "module"}