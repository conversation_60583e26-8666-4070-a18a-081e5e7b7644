{"ast": null, "code": "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "map": {"version": 3, "names": ["getNodeName", "isHTMLElement", "applyStyles", "_ref", "state", "Object", "keys", "elements", "for<PERSON>ach", "name", "style", "styles", "attributes", "element", "assign", "value", "removeAttribute", "setAttribute", "effect", "_ref2", "initialStyles", "popper", "position", "options", "strategy", "left", "top", "margin", "arrow", "reference", "styleProperties", "hasOwnProperty", "reduce", "property", "attribute", "enabled", "phase", "fn", "requires"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/modifiers/applyStyles.js"], "sourcesContent": ["import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};"], "mappings": "AAAA,OAAOA,WAAP,MAAwB,6BAAxB;AACA,SAASC,aAAT,QAA8B,4BAA9B,C,CAA4D;AAC5D;;AAEA,SAASC,WAAT,CAAqBC,IAArB,EAA2B;EACzB,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAjB;EACAC,MAAM,CAACC,IAAP,CAAYF,KAAK,CAACG,QAAlB,EAA4BC,OAA5B,CAAoC,UAAUC,IAAV,EAAgB;IAClD,IAAIC,KAAK,GAAGN,KAAK,CAACO,MAAN,CAAaF,IAAb,KAAsB,EAAlC;IACA,IAAIG,UAAU,GAAGR,KAAK,CAACQ,UAAN,CAAiBH,IAAjB,KAA0B,EAA3C;IACA,IAAII,OAAO,GAAGT,KAAK,CAACG,QAAN,CAAeE,IAAf,CAAd,CAHkD,CAGd;;IAEpC,IAAI,CAACR,aAAa,CAACY,OAAD,CAAd,IAA2B,CAACb,WAAW,CAACa,OAAD,CAA3C,EAAsD;MACpD;IACD,CAPiD,CAOhD;IACF;IACA;;;IAGAR,MAAM,CAACS,MAAP,CAAcD,OAAO,CAACH,KAAtB,EAA6BA,KAA7B;IACAL,MAAM,CAACC,IAAP,CAAYM,UAAZ,EAAwBJ,OAAxB,CAAgC,UAAUC,IAAV,EAAgB;MAC9C,IAAIM,KAAK,GAAGH,UAAU,CAACH,IAAD,CAAtB;;MAEA,IAAIM,KAAK,KAAK,KAAd,EAAqB;QACnBF,OAAO,CAACG,eAAR,CAAwBP,IAAxB;MACD,CAFD,MAEO;QACLI,OAAO,CAACI,YAAR,CAAqBR,IAArB,EAA2BM,KAAK,KAAK,IAAV,GAAiB,EAAjB,GAAsBA,KAAjD;MACD;IACF,CARD;EASD,CAtBD;AAuBD;;AAED,SAASG,MAAT,CAAgBC,KAAhB,EAAuB;EACrB,IAAIf,KAAK,GAAGe,KAAK,CAACf,KAAlB;EACA,IAAIgB,aAAa,GAAG;IAClBC,MAAM,EAAE;MACNC,QAAQ,EAAElB,KAAK,CAACmB,OAAN,CAAcC,QADlB;MAENC,IAAI,EAAE,GAFA;MAGNC,GAAG,EAAE,GAHC;MAINC,MAAM,EAAE;IAJF,CADU;IAOlBC,KAAK,EAAE;MACLN,QAAQ,EAAE;IADL,CAPW;IAUlBO,SAAS,EAAE;EAVO,CAApB;EAYAxB,MAAM,CAACS,MAAP,CAAcV,KAAK,CAACG,QAAN,CAAec,MAAf,CAAsBX,KAApC,EAA2CU,aAAa,CAACC,MAAzD;EACAjB,KAAK,CAACO,MAAN,GAAeS,aAAf;;EAEA,IAAIhB,KAAK,CAACG,QAAN,CAAeqB,KAAnB,EAA0B;IACxBvB,MAAM,CAACS,MAAP,CAAcV,KAAK,CAACG,QAAN,CAAeqB,KAAf,CAAqBlB,KAAnC,EAA0CU,aAAa,CAACQ,KAAxD;EACD;;EAED,OAAO,YAAY;IACjBvB,MAAM,CAACC,IAAP,CAAYF,KAAK,CAACG,QAAlB,EAA4BC,OAA5B,CAAoC,UAAUC,IAAV,EAAgB;MAClD,IAAII,OAAO,GAAGT,KAAK,CAACG,QAAN,CAAeE,IAAf,CAAd;MACA,IAAIG,UAAU,GAAGR,KAAK,CAACQ,UAAN,CAAiBH,IAAjB,KAA0B,EAA3C;MACA,IAAIqB,eAAe,GAAGzB,MAAM,CAACC,IAAP,CAAYF,KAAK,CAACO,MAAN,CAAaoB,cAAb,CAA4BtB,IAA5B,IAAoCL,KAAK,CAACO,MAAN,CAAaF,IAAb,CAApC,GAAyDW,aAAa,CAACX,IAAD,CAAlF,CAAtB,CAHkD,CAG+D;;MAEjH,IAAIC,KAAK,GAAGoB,eAAe,CAACE,MAAhB,CAAuB,UAAUtB,KAAV,EAAiBuB,QAAjB,EAA2B;QAC5DvB,KAAK,CAACuB,QAAD,CAAL,GAAkB,EAAlB;QACA,OAAOvB,KAAP;MACD,CAHW,EAGT,EAHS,CAAZ,CALkD,CAQ1C;;MAER,IAAI,CAACT,aAAa,CAACY,OAAD,CAAd,IAA2B,CAACb,WAAW,CAACa,OAAD,CAA3C,EAAsD;QACpD;MACD;;MAEDR,MAAM,CAACS,MAAP,CAAcD,OAAO,CAACH,KAAtB,EAA6BA,KAA7B;MACAL,MAAM,CAACC,IAAP,CAAYM,UAAZ,EAAwBJ,OAAxB,CAAgC,UAAU0B,SAAV,EAAqB;QACnDrB,OAAO,CAACG,eAAR,CAAwBkB,SAAxB;MACD,CAFD;IAGD,CAlBD;EAmBD,CApBD;AAqBD,C,CAAC;;;AAGF,eAAe;EACbzB,IAAI,EAAE,aADO;EAEb0B,OAAO,EAAE,IAFI;EAGbC,KAAK,EAAE,OAHM;EAIbC,EAAE,EAAEnC,WAJS;EAKbgB,MAAM,EAAEA,MALK;EAMboB,QAAQ,EAAE,CAAC,eAAD;AANG,CAAf"}, "metadata": {}, "sourceType": "module"}