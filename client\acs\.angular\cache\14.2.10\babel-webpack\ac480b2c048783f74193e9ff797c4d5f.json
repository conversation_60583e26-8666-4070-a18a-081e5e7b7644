{"ast": null, "code": ";\n\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * Electronic Codebook block mode.\n   */\n  CryptoJS.mode.ECB = function () {\n    var ECB = CryptoJS.lib.BlockCipherMode.extend();\n    ECB.Encryptor = ECB.extend({\n      processBlock: function (words, offset) {\n        this._cipher.encryptBlock(words, offset);\n      }\n    });\n    ECB.Decryptor = ECB.extend({\n      processBlock: function (words, offset) {\n        this._cipher.decryptBlock(words, offset);\n      }\n    });\n    return ECB;\n  }();\n\n  return CryptoJS.mode.ECB;\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "mode", "ECB", "lib", "BlockCipherMode", "extend", "Encryptor", "processBlock", "words", "offset", "_cipher", "encryptBlock", "Decryptor", "decryptBlock"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/crypto-js/mode-ecb.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./cipher-core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t/**\n\t * Electronic Codebook block mode.\n\t */\n\tCryptoJS.mode.ECB = (function () {\n\t    var ECB = CryptoJS.lib.BlockCipherMode.extend();\n\n\t    ECB.Encryptor = ECB.extend({\n\t        processBlock: function (words, offset) {\n\t            this._cipher.encryptBlock(words, offset);\n\t        }\n\t    });\n\n\t    ECB.Decryptor = ECB.extend({\n\t        processBlock: function (words, offset) {\n\t            this._cipher.decryptBlock(words, offset);\n\t        }\n\t    });\n\n\t    return ECB;\n\t}());\n\n\n\treturn CryptoJS.mode.ECB;\n\n}));"], "mappings": "AAAA;;AAAE,WAAUA,IAAV,EAAgBC,OAAhB,EAAyBC,KAAzB,EAAgC;EACjC,IAAI,OAAOC,OAAP,KAAmB,QAAvB,EAAiC;IAChC;IACAC,MAAM,CAACD,OAAP,GAAiBA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAD,CAAR,EAAoBA,OAAO,CAAC,eAAD,CAA3B,CAAlC;EACA,CAHD,MAIK,IAAI,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAA3C,EAAgD;IACpD;IACAD,MAAM,CAAC,CAAC,QAAD,EAAW,eAAX,CAAD,EAA8BL,OAA9B,CAAN;EACA,CAHI,MAIA;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAN,CAAP;EACA;AACD,CAbC,EAaA,IAbA,EAaM,UAAUA,QAAV,EAAoB;EAE3B;AACD;AACA;EACCA,QAAQ,CAACC,IAAT,CAAcC,GAAd,GAAqB,YAAY;IAC7B,IAAIA,GAAG,GAAGF,QAAQ,CAACG,GAAT,CAAaC,eAAb,CAA6BC,MAA7B,EAAV;IAEAH,GAAG,CAACI,SAAJ,GAAgBJ,GAAG,CAACG,MAAJ,CAAW;MACvBE,YAAY,EAAE,UAAUC,KAAV,EAAiBC,MAAjB,EAAyB;QACnC,KAAKC,OAAL,CAAaC,YAAb,CAA0BH,KAA1B,EAAiCC,MAAjC;MACH;IAHsB,CAAX,CAAhB;IAMAP,GAAG,CAACU,SAAJ,GAAgBV,GAAG,CAACG,MAAJ,CAAW;MACvBE,YAAY,EAAE,UAAUC,KAAV,EAAiBC,MAAjB,EAAyB;QACnC,KAAKC,OAAL,CAAaG,YAAb,CAA0BL,KAA1B,EAAiCC,MAAjC;MACH;IAHsB,CAAX,CAAhB;IAMA,OAAOP,GAAP;EACH,CAhBoB,EAArB;;EAmBA,OAAOF,QAAQ,CAACC,IAAT,CAAcC,GAArB;AAEA,CAvCC,CAAD"}, "metadata": {}, "sourceType": "script"}