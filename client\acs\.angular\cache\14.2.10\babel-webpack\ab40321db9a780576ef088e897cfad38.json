{"ast": null, "code": ";\n\n(function (root, factory) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Check if typed arrays are supported\n    if (typeof ArrayBuffer != 'function') {\n      return;\n    } // Shortcuts\n\n\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var WordArray = C_lib.WordArray; // Reference original init\n\n    var superInit = WordArray.init; // Augment WordArray.init to handle typed arrays\n\n    var subInit = WordArray.init = function (typedArray) {\n      // Convert buffers to uint8\n      if (typedArray instanceof ArrayBuffer) {\n        typedArray = new Uint8Array(typedArray);\n      } // Convert other array views to uint8\n\n\n      if (typedArray instanceof Int8Array || typeof Uint8ClampedArray !== \"undefined\" && typedArray instanceof Uint8ClampedArray || typedArray instanceof Int16Array || typedArray instanceof Uint16Array || typedArray instanceof Int32Array || typedArray instanceof Uint32Array || typedArray instanceof Float32Array || typedArray instanceof Float64Array) {\n        typedArray = new Uint8Array(typedArray.buffer, typedArray.byteOffset, typedArray.byteLength);\n      } // Handle Uint8Array\n\n\n      if (typedArray instanceof Uint8Array) {\n        // Shortcut\n        var typedArrayByteLength = typedArray.byteLength; // Extract bytes\n\n        var words = [];\n\n        for (var i = 0; i < typedArrayByteLength; i++) {\n          words[i >>> 2] |= typedArray[i] << 24 - i % 4 * 8;\n        } // Initialize this word array\n\n\n        superInit.call(this, words, typedArrayByteLength);\n      } else {\n        // Else call normal init\n        superInit.apply(this, arguments);\n      }\n    };\n\n    subInit.prototype = WordArray;\n  })();\n\n  return CryptoJS.lib.WordArray;\n});", "map": {"version": 3, "names": ["root", "factory", "exports", "module", "require", "define", "amd", "CryptoJS", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "C", "C_lib", "lib", "WordArray", "superInit", "init", "subInit", "typedArray", "Uint8Array", "Int8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "buffer", "byteOffset", "byteLength", "typedArrayByteLength", "words", "i", "call", "apply", "arguments", "prototype"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/crypto-js/lib-typedarrays.js"], "sourcesContent": [";(function (root, factory) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t(function () {\n\t    // Check if typed arrays are supported\n\t    if (typeof ArrayBuffer != 'function') {\n\t        return;\n\t    }\n\n\t    // Shortcuts\n\t    var C = CryptoJS;\n\t    var C_lib = C.lib;\n\t    var WordArray = C_lib.WordArray;\n\n\t    // Reference original init\n\t    var superInit = WordArray.init;\n\n\t    // Augment WordArray.init to handle typed arrays\n\t    var subInit = WordArray.init = function (typedArray) {\n\t        // Convert buffers to uint8\n\t        if (typedArray instanceof ArrayBuffer) {\n\t            typedArray = new Uint8Array(typedArray);\n\t        }\n\n\t        // Convert other array views to uint8\n\t        if (\n\t            typedArray instanceof Int8Array ||\n\t            (typeof Uint8ClampedArray !== \"undefined\" && typedArray instanceof Uint8ClampedArray) ||\n\t            typedArray instanceof Int16Array ||\n\t            typedArray instanceof Uint16Array ||\n\t            typedArray instanceof Int32Array ||\n\t            typedArray instanceof Uint32Array ||\n\t            typedArray instanceof Float32Array ||\n\t            typedArray instanceof Float64Array\n\t        ) {\n\t            typedArray = new Uint8Array(typedArray.buffer, typedArray.byteOffset, typedArray.byteLength);\n\t        }\n\n\t        // Handle Uint8Array\n\t        if (typedArray instanceof Uint8Array) {\n\t            // Shortcut\n\t            var typedArrayByteLength = typedArray.byteLength;\n\n\t            // Extract bytes\n\t            var words = [];\n\t            for (var i = 0; i < typedArrayByteLength; i++) {\n\t                words[i >>> 2] |= typedArray[i] << (24 - (i % 4) * 8);\n\t            }\n\n\t            // Initialize this word array\n\t            superInit.call(this, words, typedArrayByteLength);\n\t        } else {\n\t            // Else call normal init\n\t            superInit.apply(this, arguments);\n\t        }\n\t    };\n\n\t    subInit.prototype = WordArray;\n\t}());\n\n\n\treturn CryptoJS.lib.WordArray;\n\n}));"], "mappings": "AAAA;;AAAE,WAAUA,IAAV,EAAgBC,OAAhB,EAAyB;EAC1B,IAAI,OAAOC,OAAP,KAAmB,QAAvB,EAAiC;IAChC;IACAC,MAAM,CAACD,OAAP,GAAiBA,OAAO,GAAGD,OAAO,CAACG,OAAO,CAAC,QAAD,CAAR,CAAlC;EACA,CAHD,MAIK,IAAI,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAA3C,EAAgD;IACpD;IACAD,MAAM,CAAC,CAAC,QAAD,CAAD,EAAaJ,OAAb,CAAN;EACA,CAHI,MAIA;IACJ;IACAA,OAAO,CAACD,IAAI,CAACO,QAAN,CAAP;EACA;AACD,CAbC,EAaA,IAbA,EAaM,UAAUA,QAAV,EAAoB;EAE1B,aAAY;IACT;IACA,IAAI,OAAOC,WAAP,IAAsB,UAA1B,EAAsC;MAClC;IACH,CAJQ,CAMT;;;IACA,IAAIC,CAAC,GAAGF,QAAR;IACA,IAAIG,KAAK,GAAGD,CAAC,CAACE,GAAd;IACA,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAtB,CATS,CAWT;;IACA,IAAIC,SAAS,GAAGD,SAAS,CAACE,IAA1B,CAZS,CAcT;;IACA,IAAIC,OAAO,GAAGH,SAAS,CAACE,IAAV,GAAiB,UAAUE,UAAV,EAAsB;MACjD;MACA,IAAIA,UAAU,YAAYR,WAA1B,EAAuC;QACnCQ,UAAU,GAAG,IAAIC,UAAJ,CAAeD,UAAf,CAAb;MACH,CAJgD,CAMjD;;;MACA,IACIA,UAAU,YAAYE,SAAtB,IACC,OAAOC,iBAAP,KAA6B,WAA7B,IAA4CH,UAAU,YAAYG,iBADnE,IAEAH,UAAU,YAAYI,UAFtB,IAGAJ,UAAU,YAAYK,WAHtB,IAIAL,UAAU,YAAYM,UAJtB,IAKAN,UAAU,YAAYO,WALtB,IAMAP,UAAU,YAAYQ,YANtB,IAOAR,UAAU,YAAYS,YAR1B,EASE;QACET,UAAU,GAAG,IAAIC,UAAJ,CAAeD,UAAU,CAACU,MAA1B,EAAkCV,UAAU,CAACW,UAA7C,EAAyDX,UAAU,CAACY,UAApE,CAAb;MACH,CAlBgD,CAoBjD;;;MACA,IAAIZ,UAAU,YAAYC,UAA1B,EAAsC;QAClC;QACA,IAAIY,oBAAoB,GAAGb,UAAU,CAACY,UAAtC,CAFkC,CAIlC;;QACA,IAAIE,KAAK,GAAG,EAAZ;;QACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,oBAApB,EAA0CE,CAAC,EAA3C,EAA+C;UAC3CD,KAAK,CAACC,CAAC,KAAK,CAAP,CAAL,IAAkBf,UAAU,CAACe,CAAD,CAAV,IAAkB,KAAMA,CAAC,GAAG,CAAL,GAAU,CAAnD;QACH,CARiC,CAUlC;;;QACAlB,SAAS,CAACmB,IAAV,CAAe,IAAf,EAAqBF,KAArB,EAA4BD,oBAA5B;MACH,CAZD,MAYO;QACH;QACAhB,SAAS,CAACoB,KAAV,CAAgB,IAAhB,EAAsBC,SAAtB;MACH;IACJ,CArCD;;IAuCAnB,OAAO,CAACoB,SAAR,GAAoBvB,SAApB;EACH,CAvDA,GAAD;;EA0DA,OAAOL,QAAQ,CAACI,GAAT,CAAaC,SAApB;AAEA,CA3EC,CAAD"}, "metadata": {}, "sourceType": "script"}