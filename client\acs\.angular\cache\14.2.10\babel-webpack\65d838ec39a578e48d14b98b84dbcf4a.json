{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./app.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./app.component.css?ngResource\";\nimport { Component, Renderer2, ChangeDetectorRef } from '@angular/core';\nimport { Store } from '@ngrx/store';\nimport { getHeaderMenuAction } from './Modules/LandingPage/app-state/actions/home.action';\nimport { FormControlDirective, FormControlName } from '@angular/forms';\nimport { Router, RouteConfigLoadStart, RouteConfigLoadEnd, ActivatedRoute, NavigationEnd } from '@angular/router';\nimport { LoaderInterceptorHandlerService } from './Modules/Shared/Services/handler/loader-service-interceptor-handler';\nimport { PageContentService } from './Modules/Core/Services/Common/page-content.service';\nimport { ServiceEditRequest } from './Modules/FilingService/Models/ServiceEditRequest';\nimport { Util } from './Modules/Shared/helper/Util';\nimport { FormModeService } from './Modules/Shared/Services/Common/FormModeService';\nimport { ComponentMessageService } from './Modules/Core/Services/Common/component-message.service';\nimport { SubjectEnums } from './Modules/Shared/Enums/subject.enum';\nimport { ComponentMessageModel } from './Modules/Shared/Models/component-message.model';\nimport { EnvConfigService } from './Modules/Core/Services/Common/env-config.service';\nimport { HttpService } from './Modules/Core/Services/HttpService.Service';\nimport { LoaderService } from './Modules/Core/Services/Common/loader.service';\nconst originFormControlNgOnChanges = FormControlDirective.prototype.ngOnChanges;\n\nFormControlDirective.prototype.ngOnChanges = function () {\n  this.form.nativeElement = this.valueAccessor._elementRef.nativeElement;\n  return originFormControlNgOnChanges.apply(this, arguments);\n};\n\nconst originFormControlNameNgOnChanges = FormControlName.prototype.ngOnChanges;\n\nFormControlName.prototype.ngOnChanges = function () {\n  const result = originFormControlNameNgOnChanges.apply(this, arguments);\n  this.control.nativeElement = this.valueAccessor._elementRef.nativeElement;\n  return result;\n};\n\nlet AppComponent = class AppComponent {\n  constructor(store, router, loader, interceptorHandler, pageContentService, activatedRoute, formMode, componentMessageService, renderer, envConfigSerivce, http, crd) {\n    this.store = store;\n    this.router = router;\n    this.loader = loader;\n    this.interceptorHandler = interceptorHandler;\n    this.pageContentService = pageContentService;\n    this.activatedRoute = activatedRoute;\n    this.formMode = formMode;\n    this.componentMessageService = componentMessageService;\n    this.renderer = renderer;\n    this.envConfigSerivce = envConfigSerivce;\n    this.http = http;\n    this.crd = crd;\n    this.title = 'ACS';\n    this.loaderVisibility = '1';\n    this.loginPopUp = false;\n    this.ReqData = new ServiceEditRequest();\n    this.maintenanceMode = false;\n    this.showLoaderOnLazyLoading();\n    this.removePopUpURL();\n  }\n\n  ngOnInit() {\n    this.maintenanceMode = this.envConfigSerivce.getEnvConfig().maintenanceMode;\n    this.loadScript();\n    this.store.dispatch(getHeaderMenuAction());\n    this.backButtonAttachOnUpdateOrder();\n    this.showLoginPopUp();\n  }\n\n  ngAfterViewChecked() {\n    this.crd.detectChanges();\n  }\n\n  LoaderVisibility() {\n    this.loader.loaderVisibility$.subscribe(val => {\n      this.loaderVisibility = val;\n    });\n    return this.loaderVisibility;\n  }\n\n  LoaderVisibilityOnRoute() {\n    let currentRoute = this.router.url;\n    return !(currentRoute == '/' || currentRoute == '/home');\n  }\n\n  ngAfterViewInit() {\n    //setting main content div min height\n    this.pageContentService.setPageStyle();\n  }\n\n  backButtonAttachOnUpdateOrder() {\n    this.activatedRoute.queryParams.subscribe(queryString => {\n      if (queryString.on && queryString.pn && queryString.kln) {\n        this.ReqData.OrderNumber = Util.decryptData(queryString.on);\n        this.ReqData.ProductNumber = Util.decryptData(queryString.pn);\n        this.ReqData.KitLineNumber = Util.decryptData(queryString.kln);\n        this.formMode.EnableEditMode(this.ReqData);\n      }\n    });\n  } //show login popup when user not signed in \n\n\n  showLoginPopUp() {\n    this._loginsubscription = this.componentMessageService.getMessage().subscribe(message => {\n      if (message.subject === SubjectEnums.loginpopup) {\n        this.loginPopUp = message.payload.popup;\n\n        if (this.loginPopUp) {\n          this.renderer.addClass(document.body, 'sh-modal-yes');\n        }\n\n        this.router.navigate([{\n          outlets: {\n            userlogin: ['AuthPopUp', 'login']\n          }\n        }]);\n      }\n    });\n  }\n\n  onClosePopUp() {\n    this.loginPopUp = false;\n    this.renderer.removeClass(document.body, 'sh-modal-yes');\n    this.router.navigate([{\n      outlets: {\n        userlogin: null\n      }\n    }]);\n    const activeSectionMessage = new ComponentMessageModel();\n    activeSectionMessage.subject = SubjectEnums.closesubscription;\n    activeSectionMessage.payload = {};\n    this.componentMessageService.sendMessage(activeSectionMessage);\n  }\n\n  showLoaderOnLazyLoading() {\n    this.router.events.subscribe(event => {\n      if (event.url && event.url.includes('home')) {\n        return;\n      } // show loader while Lazy modules are being loaded\n\n\n      if (event instanceof RouteConfigLoadStart) {\n        this.interceptorHandler.initiateLoaderInterceptor();\n      }\n\n      if (event instanceof RouteConfigLoadEnd) {\n        this.interceptorHandler.stopLoaderInterceptor();\n      }\n    });\n  }\n\n  loadScript() {\n    var configData = JSON.parse(localStorage.getItem('envConfig'));\n    var dynamicScripts = [\"./assets/js/google-analytics.js\", `https://www.googletagmanager.com/gtag/js?id=${this.envConfigSerivce.getEnvConfig().trackingId}`];\n\n    for (var i = 0; i < dynamicScripts.length; i++) {\n      let node = document.createElement('script');\n      node.src = dynamicScripts[i];\n      node.type = 'text/javascript';\n      node.async = false;\n      document.getElementsByTagName('head')[0].appendChild(node);\n    }\n\n    setTimeout(() => {\n      this.router.events.subscribe(event => {\n        if (event instanceof NavigationEnd) {\n          console.log(event.urlAfterRedirects); // tracking id \n\n          if (configData.trackingId != \"\") {\n            window.ga('set', 'page', event.urlAfterRedirects);\n            window.ga('send', 'pageview');\n          } //measure id \n\n\n          if (configData.measureId != \"\") {\n            gtag('config', this.envConfigSerivce.getEnvConfig().measureId, {\n              'page_path': event.urlAfterRedirects\n            });\n          }\n        }\n      });\n    }, 2000);\n  }\n\n  removePopUpURL() {\n    if (window.location.href.indexOf('(') != -1) {\n      window.location.href = window.location.href.split('(')[0];\n    }\n  }\n\n};\n\nAppComponent.ctorParameters = () => [{\n  type: Store\n}, {\n  type: Router\n}, {\n  type: LoaderService\n}, {\n  type: LoaderInterceptorHandlerService\n}, {\n  type: PageContentService\n}, {\n  type: ActivatedRoute\n}, {\n  type: FormModeService\n}, {\n  type: ComponentMessageService\n}, {\n  type: Renderer2\n}, {\n  type: EnvConfigService\n}, {\n  type: HttpService\n}, {\n  type: ChangeDetectorRef\n}];\n\nAppComponent = __decorate([Component({\n  selector: 'app-root',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AppComponent);\nexport { AppComponent };", "map": {"version": 3, "mappings": ";;;AAAA,SAASA,SAAT,EAAiDC,SAAjD,EAA4DC,iBAA5D,QAAqF,eAArF;AACA,SAASC,KAAT,QAAsB,aAAtB;AACA,SAASC,mBAAT,QAAoC,qDAApC;AACA,SAASC,oBAAT,EAA+BC,eAA/B,QAAsD,gBAAtD;AACA,SAASC,MAAT,EAAiBC,oBAAjB,EAAuCC,kBAAvC,EAA2DC,cAA3D,EAA2EC,aAA3E,QAAgG,iBAAhG;AACA,SAASC,+BAAT,QAAgD,sEAAhD;AACA,SAASC,kBAAT,QAAmC,qDAAnC;AACA,SAASC,kBAAT,QAAmC,mDAAnC;AACA,SAASC,IAAT,QAAqB,8BAArB;AACA,SAASC,eAAT,QAAgC,kDAAhC;AAEA,SAASC,uBAAT,QAAwC,0DAAxC;AACA,SAASC,YAAT,QAA6B,qCAA7B;AACA,SAASC,qBAAT,QAAsC,iDAAtC;AACA,SAASC,gBAAT,QAAiC,mDAAjC;AACA,SAASC,WAAT,QAA4B,6CAA5B;AACA,SAASC,aAAT,QAA8B,+CAA9B;AAGA,MAAMC,4BAA4B,GAAGlB,oBAAoB,CAACmB,SAArB,CAA+BC,WAApE;;AACApB,oBAAoB,CAACmB,SAArB,CAA+BC,WAA/B,GAA6C;EAC3C,KAAKC,IAAL,CAAUC,aAAV,GAA0B,KAAKC,aAAL,CAAmBC,WAAnB,CAA+BF,aAAzD;EACA,OAAOJ,4BAA4B,CAACO,KAA7B,CAAmC,IAAnC,EAAyCC,SAAzC,CAAP;AACD,CAHD;;AAKA,MAAMC,gCAAgC,GAAG1B,eAAe,CAACkB,SAAhB,CAA0BC,WAAnE;;AACAnB,eAAe,CAACkB,SAAhB,CAA0BC,WAA1B,GAAwC;EACtC,MAAMQ,MAAM,GAAGD,gCAAgC,CAACF,KAAjC,CAAuC,IAAvC,EAA6CC,SAA7C,CAAf;EACA,KAAKG,OAAL,CAAaP,aAAb,GAA6B,KAAKC,aAAL,CAAmBC,WAAnB,CAA+BF,aAA5D;EACA,OAAOM,MAAP;AACD,CAJD;;IAeaE,YAAY,SAAZA,YAAY;EAOvBC,YAAoBC,KAApB,EAA+CC,MAA/C,EAAuEC,MAAvE,EAAsGC,kBAAtG,EAAmKC,kBAAnK,EAAmNC,cAAnN,EAA2PC,QAA3P,EAA8RC,uBAA9R,EAAwVC,QAAxV,EAAqXC,gBAArX,EACWC,IADX,EACsCC,GADtC,EAC6D;IADzC;IAA2B;IAAwB;IAA+B;IAA6D;IAAgD;IAAwC;IAAmC;IAA0D;IAA6B;IAC1W;IAA2B;IAPtC,aAAQ,KAAR;IACA,wBAA0B,GAA1B;IACA,kBAAsB,KAAtB;IACA,eAA8B,IAAIlC,kBAAJ,EAA9B;IACA,uBAAwB,KAAxB;IAIE,KAAKmC,uBAAL;IACA,KAAKC,cAAL;EAED;;EAEDC,QAAQ;IACN,KAAKC,eAAL,GAAqB,KAAKN,gBAAL,CAAsBO,YAAtB,GAAqCD,eAA1D;IACA,KAAKE,UAAL;IACA,KAAKjB,KAAL,CAAWkB,QAAX,CAAoBnD,mBAAmB,EAAvC;IACA,KAAKoD,6BAAL;IACA,KAAKC,cAAL;EACD;;EAEDC,kBAAkB;IAChB,KAAKV,GAAL,CAASW,aAAT;EACD;;EAEDC,gBAAgB;IACd,KAAKrB,MAAL,CAAYsB,iBAAZ,CAA8BC,SAA9B,CAAwCC,GAAG,IAAE;MAC3C,KAAKC,gBAAL,GAAwBD,GAAxB;IACD,CAFD;IAGA,OAAO,KAAKC,gBAAZ;EACD;;EAEDC,uBAAuB;IACrB,IAAIC,YAAY,GAAG,KAAK5B,MAAL,CAAY6B,GAA/B;IACA,OAAO,EAAED,YAAY,IAAI,GAAhB,IAAuBA,YAAY,IAAI,OAAzC,CAAP;EACD;;EAEDE,eAAe;IACb;IACA,KAAK3B,kBAAL,CAAwB4B,YAAxB;EACD;;EAEDb,6BAA6B;IAC3B,KAAKd,cAAL,CAAoB4B,WAApB,CAAgCR,SAAhC,CAA0CS,WAAW,IAAG;MACtD,IAAIA,WAAW,CAACC,EAAZ,IAAkBD,WAAW,CAACE,EAA9B,IAAoCF,WAAW,CAACG,GAApD,EAAyD;QAEvD,KAAKC,OAAL,CAAaC,WAAb,GAA2B7D,IAAI,CAAC8D,WAAL,CAAiBN,WAAW,CAACC,EAA7B,CAA3B;QACA,KAAKG,OAAL,CAAaG,aAAb,GAA6B/D,IAAI,CAAC8D,WAAL,CAAiBN,WAAW,CAACE,EAA7B,CAA7B;QACA,KAAKE,OAAL,CAAaI,aAAb,GAA6BhE,IAAI,CAAC8D,WAAL,CAAiBN,WAAW,CAACG,GAA7B,CAA7B;QAEA,KAAK/B,QAAL,CAAcqC,cAAd,CAA6B,KAAKL,OAAlC;MACD;IACF,CATD;EAUD,CAtDsB,CAwDvB;;;EACAlB,cAAc;IACZ,KAAKwB,kBAAL,GAA0B,KAAKrC,uBAAL,CAA6BsC,UAA7B,GAA0CpB,SAA1C,CAAoDqB,OAAO,IAAG;MACtF,IAAIA,OAAO,CAACC,OAAR,KAAoBlE,YAAY,CAACmE,UAArC,EAAiD;QAC/C,KAAKC,UAAL,GAAkBH,OAAO,CAACI,OAAR,CAAgBC,KAAlC;;QACA,IAAI,KAAKF,UAAT,EAAqB;UAAE,KAAKzC,QAAL,CAAc4C,QAAd,CAAuBC,QAAQ,CAACC,IAAhC,EAAsC,cAAtC;QAAwD;;QAC/E,KAAKrD,MAAL,CAAYsD,QAAZ,CAAqB,CAAC;UAAEC,OAAO,EAAE;YAAEC,SAAS,EAAE,CAAC,WAAD,EAAc,OAAd;UAAb;QAAX,CAAD,CAArB;MACD;IACF,CANyB,CAA1B;EAOD;;EAEDC,YAAY;IACV,KAAKT,UAAL,GAAkB,KAAlB;IACA,KAAKzC,QAAL,CAAcmD,WAAd,CAA0BN,QAAQ,CAACC,IAAnC,EAAyC,cAAzC;IACA,KAAKrD,MAAL,CAAYsD,QAAZ,CAAqB,CAAC;MAAEC,OAAO,EAAE;QAAEC,SAAS,EAAE;MAAb;IAAX,CAAD,CAArB;IACA,MAAMG,oBAAoB,GAA0B,IAAI9E,qBAAJ,EAApD;IACA8E,oBAAoB,CAACb,OAArB,GAA+BlE,YAAY,CAACgF,iBAA5C;IACAD,oBAAoB,CAACV,OAArB,GAA+B,EAA/B;IACA,KAAK3C,uBAAL,CAA6BuD,WAA7B,CAAyCF,oBAAzC;EACD;;EAIDhD,uBAAuB;IAErB,KAAKX,MAAL,CAAY8D,MAAZ,CAAmBtC,SAAnB,CAA8BuC,KAAD,IAAc;MAGzC,IAAGA,KAAK,CAAClC,GAAN,IAAakC,KAAK,CAAClC,GAAN,CAAUmC,QAAV,CAAmB,MAAnB,CAAhB,EAA2C;QAAC;MAAQ,CAHX,CAKzC;;;MACA,IAAID,KAAK,YAAY7F,oBAArB,EAA2C;QACzC,KAAKgC,kBAAL,CAAwB+D,yBAAxB;MACD;;MACD,IAAIF,KAAK,YAAY5F,kBAArB,EAAyC;QAGvC,KAAK+B,kBAAL,CAAwBgE,qBAAxB;MACD;IACF,CAdD;EAeD;;EACMlD,UAAU;IACf,IAAImD,UAAU,GAAGC,IAAI,CAACC,KAAL,CAAWC,YAAY,CAACC,OAAb,CAAqB,WAArB,CAAX,CAAjB;IACA,IAAIC,cAAc,GAAG,CAAC,iCAAD,EAAoC,+CAA+C,KAAKhE,gBAAL,CAAsBO,YAAtB,GAAqC0D,UAAU,EAAlI,CAArB;;IAEA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,cAAc,CAACG,MAAnC,EAA2CD,CAAC,EAA5C,EAAgD;MAC9C,IAAIE,IAAI,GAAGxB,QAAQ,CAACyB,aAAT,CAAuB,QAAvB,CAAX;MACAD,IAAI,CAACE,GAAL,GAAWN,cAAc,CAACE,CAAD,CAAzB;MACAE,IAAI,CAACG,IAAL,GAAY,iBAAZ;MACAH,IAAI,CAACI,KAAL,GAAa,KAAb;MACA5B,QAAQ,CAAC6B,oBAAT,CAA8B,MAA9B,EAAsC,CAAtC,EAAyCC,WAAzC,CAAqDN,IAArD;IACD;;IAEDO,UAAU,CAAC,MAAK;MACd,KAAKnF,MAAL,CAAY8D,MAAZ,CAAmBtC,SAAnB,CAA6BuC,KAAK,IAAG;QACnC,IAAIA,KAAK,YAAY1F,aAArB,EAAoC;UAClC+G,OAAO,CAACC,GAAR,CAAYtB,KAAK,CAACuB,iBAAlB,EADkC,CAElC;;UACA,IAAInB,UAAU,CAACM,UAAX,IAAyB,EAA7B,EAAiC;YACzBc,MAAO,CAACC,EAAR,CAAW,KAAX,EAAkB,MAAlB,EAA0BzB,KAAK,CAACuB,iBAAhC;YACAC,MAAO,CAACC,EAAR,CAAW,MAAX,EAAmB,UAAnB;UACP,CANiC,CAQlC;;;UACA,IAAIrB,UAAU,CAACsB,SAAX,IAAwB,EAA5B,EAAgC;YAC9BC,IAAI,CAAC,QAAD,EAAW,KAAKlF,gBAAL,CAAsBO,YAAtB,GAAqC0E,SAAhD,EAA2D;cAC7D,aAAa1B,KAAK,CAACuB;YAD0C,CAA3D,CAAJ;UAGD;QACF;MACF,CAhBD;IAiBD,CAlBS,EAkBP,IAlBO,CAAV;EAmBD;;EAGM1E,cAAc;IAEnB,IAAG2E,MAAM,CAACI,QAAP,CAAgBC,IAAhB,CAAqBC,OAArB,CAA6B,GAA7B,KAAmC,CAAC,CAAvC,EAAyC;MACvCN,MAAM,CAACI,QAAP,CAAgBC,IAAhB,GAAuBL,MAAM,CAACI,QAAP,CAAgBC,IAAhB,CAAqBE,KAArB,CAA2B,GAA3B,EAAgC,CAAhC,CAAvB;IACD;EACF;;AAxIsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAZjG,YAAY,eANxBnC,SAAS,CAAC;EACTqI,QAAQ,EAAE,UADD;EAETC,8BAFS;;AAAA,CAAD,CAMe,GAAZnG,YAAY,CAAZ;SAAAA", "names": ["Component", "Renderer2", "ChangeDetectorRef", "Store", "getHeaderMenuAction", "FormControlDirective", "FormControlName", "Router", "RouteConfigLoadStart", "RouteConfigLoadEnd", "ActivatedRoute", "NavigationEnd", "LoaderInterceptorHandlerService", "PageContentService", "ServiceEditRequest", "<PERSON><PERSON>", "FormModeService", "ComponentMessageService", "SubjectEnums", "ComponentMessageModel", "EnvConfigService", "HttpService", "LoaderService", "originFormControlNgOnChanges", "prototype", "ngOnChanges", "form", "nativeElement", "valueAccessor", "_elementRef", "apply", "arguments", "originFormControlNameNgOnChanges", "result", "control", "AppComponent", "constructor", "store", "router", "loader", "interceptorHandler", "pageContentService", "activatedRoute", "formMode", "componentMessageService", "renderer", "envConfigSerivce", "http", "crd", "showLoaderOnLazyLoading", "removePopUpURL", "ngOnInit", "maintenanceMode", "getEnvConfig", "loadScript", "dispatch", "backButtonAttachOnUpdateOrder", "showLoginPopUp", "ngAfterViewChecked", "detectChanges", "LoaderVisibility", "loaderVisibility$", "subscribe", "val", "loaderVisibility", "LoaderVisibilityOnRoute", "currentRoute", "url", "ngAfterViewInit", "setPageStyle", "queryParams", "queryString", "on", "pn", "kln", "ReqData", "OrderNumber", "decryptData", "ProductNumber", "KitLineNumber", "EnableEditMode", "_loginsubscription", "getMessage", "message", "subject", "loginpopup", "loginPopUp", "payload", "popup", "addClass", "document", "body", "navigate", "outlets", "userlogin", "onClosePopUp", "removeClass", "activeSectionMessage", "closesubscription", "sendMessage", "events", "event", "includes", "initiateLoaderInterceptor", "stopLoaderInterceptor", "configData", "JSON", "parse", "localStorage", "getItem", "dynamicScripts", "trackingId", "i", "length", "node", "createElement", "src", "type", "async", "getElementsByTagName", "append<PERSON><PERSON><PERSON>", "setTimeout", "console", "log", "urlAfterRedirects", "window", "ga", "measureId", "gtag", "location", "href", "indexOf", "split", "selector", "template"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\viatech\\Ecom\\client\\acs\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component, OnInit, AfterContentChecked, Renderer2, ChangeDetectorRef } from '@angular/core';\r\nimport { Store } from '@ngrx/store';\r\nimport { getHeaderMenuAction } from './Modules/LandingPage/app-state/actions/home.action';\r\nimport { FormControlDirective, FormControlName } from '@angular/forms';\r\nimport { Router, RouteConfigLoadStart, RouteConfigLoadEnd, ActivatedRoute, NavigationEnd } from '@angular/router';\r\nimport { LoaderInterceptorHandlerService } from './Modules/Shared/Services/handler/loader-service-interceptor-handler';\r\nimport { PageContentService } from './Modules/Core/Services/Common/page-content.service';\r\nimport { ServiceEditRequest } from './Modules/FilingService/Models/ServiceEditRequest';\r\nimport { Util } from './Modules/Shared/helper/Util';\r\nimport { FormModeService } from './Modules/Shared/Services/Common/FormModeService';\r\nimport { Observable, Subscription } from 'rxjs';\r\nimport { ComponentMessageService } from './Modules/Core/Services/Common/component-message.service';\r\nimport { SubjectEnums } from './Modules/Shared/Enums/subject.enum';\r\nimport { ComponentMessageModel } from './Modules/Shared/Models/component-message.model';\r\nimport { EnvConfigService } from './Modules/Core/Services/Common/env-config.service';\r\nimport { HttpService } from './Modules/Core/Services/HttpService.Service';\r\nimport { LoaderService } from './Modules/Core/Services/Common/loader.service';\r\ndeclare let gtag;\r\n\r\nconst originFormControlNgOnChanges = FormControlDirective.prototype.ngOnChanges;\r\nFormControlDirective.prototype.ngOnChanges = function () {\r\n  this.form.nativeElement = this.valueAccessor._elementRef.nativeElement;\r\n  return originFormControlNgOnChanges.apply(this, arguments);\r\n};\r\n\r\nconst originFormControlNameNgOnChanges = FormControlName.prototype.ngOnChanges;\r\nFormControlName.prototype.ngOnChanges = function () {\r\n  const result = originFormControlNameNgOnChanges.apply(this, arguments);\r\n  this.control.nativeElement = this.valueAccessor._elementRef.nativeElement;\r\n  return result;\r\n};\r\n\r\n\r\n\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.css'],\r\n})\r\n\r\nexport class AppComponent implements OnInit {\r\n  title = 'ACS';\r\n  loaderVisibility: string= '1';\r\n  loginPopUp: boolean = false;\r\n  ReqData: ServiceEditRequest = new ServiceEditRequest();\r\n  maintenanceMode:boolean=false\r\n  _loginsubscription: Subscription;\r\n  constructor(private store: Store<any>, private router: Router, private loader: LoaderService, private interceptorHandler: LoaderInterceptorHandlerService, private pageContentService: PageContentService, private activatedRoute: ActivatedRoute, private formMode: FormModeService, private componentMessageService: ComponentMessageService, private renderer: Renderer2, private envConfigSerivce: EnvConfigService\r\n    ,private http: HttpService, private crd : ChangeDetectorRef) {     \r\n    this.showLoaderOnLazyLoading();\r\n    this.removePopUpURL();\r\n\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.maintenanceMode=this.envConfigSerivce.getEnvConfig().maintenanceMode;\r\n    this.loadScript();\r\n    this.store.dispatch(getHeaderMenuAction());\r\n    this.backButtonAttachOnUpdateOrder();\r\n    this.showLoginPopUp();\r\n  }\r\n\r\n  ngAfterViewChecked(){\r\n    this.crd.detectChanges();\r\n  }\r\n\r\n  LoaderVisibility(){\r\n    this.loader.loaderVisibility$.subscribe(val=>{\r\n      this.loaderVisibility = val;\r\n    }) \r\n    return this.loaderVisibility;\r\n  }\r\n\r\n  LoaderVisibilityOnRoute(){\r\n    let currentRoute = this.router.url;\r\n    return !(currentRoute == '/' || currentRoute == '/home');\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    //setting main content div min height\r\n    this.pageContentService.setPageStyle();\r\n  }\r\n\r\n  backButtonAttachOnUpdateOrder() {\r\n    this.activatedRoute.queryParams.subscribe(queryString => {\r\n      if (queryString.on && queryString.pn && queryString.kln) {\r\n\r\n        this.ReqData.OrderNumber = Util.decryptData(queryString.on);\r\n        this.ReqData.ProductNumber = Util.decryptData(queryString.pn);\r\n        this.ReqData.KitLineNumber = Util.decryptData(queryString.kln);\r\n\r\n        this.formMode.EnableEditMode(this.ReqData);\r\n      }\r\n    });\r\n  }\r\n\r\n  //show login popup when user not signed in \r\n  showLoginPopUp() {\r\n    this._loginsubscription = this.componentMessageService.getMessage().subscribe(message => {\r\n      if (message.subject === SubjectEnums.loginpopup) {\r\n        this.loginPopUp = message.payload.popup;\r\n        if (this.loginPopUp) { this.renderer.addClass(document.body, 'sh-modal-yes'); }\r\n        this.router.navigate([{ outlets: { userlogin: ['AuthPopUp', 'login'] } }])\r\n      }\r\n    });\r\n  }\r\n\r\n  onClosePopUp() {\r\n    this.loginPopUp = false;\r\n    this.renderer.removeClass(document.body, 'sh-modal-yes');\r\n    this.router.navigate([{ outlets: { userlogin: null } }])\r\n    const activeSectionMessage: ComponentMessageModel = new ComponentMessageModel();\r\n    activeSectionMessage.subject = SubjectEnums.closesubscription;\r\n    activeSectionMessage.payload = {}\r\n    this.componentMessageService.sendMessage(activeSectionMessage);\r\n  }\r\n\r\n\r\n\r\n  showLoaderOnLazyLoading() {\r\n\r\n    this.router.events.subscribe((event:any) => {\r\n\r\n     \r\n      if(event.url && event.url.includes('home')){return;}\r\n    \r\n      // show loader while Lazy modules are being loaded\r\n      if (event instanceof RouteConfigLoadStart) { \r\n        this.interceptorHandler.initiateLoaderInterceptor();\r\n      }\r\n      if (event instanceof RouteConfigLoadEnd) {\r\n\r\n\r\n        this.interceptorHandler.stopLoaderInterceptor();\r\n      }\r\n    })\r\n  }\r\n  public loadScript() {\r\n    var configData = JSON.parse(localStorage.getItem('envConfig'));\r\n    var dynamicScripts = [\"./assets/js/google-analytics.js\", `https://www.googletagmanager.com/gtag/js?id=${this.envConfigSerivce.getEnvConfig().trackingId}`];\r\n\r\n    for (var i = 0; i < dynamicScripts.length; i++) {\r\n      let node = document.createElement('script');\r\n      node.src = dynamicScripts[i];\r\n      node.type = 'text/javascript';\r\n      node.async = false;\r\n      document.getElementsByTagName('head')[0].appendChild(node);\r\n    }\r\n\r\n    setTimeout(() => {\r\n      this.router.events.subscribe(event => {\r\n        if (event instanceof NavigationEnd) {\r\n          console.log(event.urlAfterRedirects);\r\n          // tracking id \r\n          if (configData.trackingId != \"\") {\r\n            (<any>window).ga('set', 'page', event.urlAfterRedirects);\r\n            (<any>window).ga('send', 'pageview');\r\n          }\r\n\r\n          //measure id \r\n          if (configData.measureId != \"\") {\r\n            gtag('config', this.envConfigSerivce.getEnvConfig().measureId, {\r\n              'page_path': event.urlAfterRedirects\r\n            });\r\n          }\r\n        }\r\n      });\r\n    }, 2000);\r\n  }\r\n\r\n\r\n  public removePopUpURL(){\r\n\r\n    if(window.location.href.indexOf('(')!=-1){\r\n      window.location.href = window.location.href.split('(')[0];\r\n    }\r\n  }\r\n\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}