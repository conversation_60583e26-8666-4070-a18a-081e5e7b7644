{"ast": null, "code": "import { Observable } from '../Observable';\nimport { isArray } from '../util/isArray';\nimport { isFunction } from '../util/isFunction';\nimport { map } from '../operators/map';\n\nconst toString = (() => Object.prototype.toString)();\n\nexport function fromEvent(target, eventName, options, resultSelector) {\n  if (isFunction(options)) {\n    resultSelector = options;\n    options = undefined;\n  }\n\n  if (resultSelector) {\n    return fromEvent(target, eventName, options).pipe(map(args => isArray(args) ? resultSelector(...args) : resultSelector(args)));\n  }\n\n  return new Observable(subscriber => {\n    function handler(e) {\n      if (arguments.length > 1) {\n        subscriber.next(Array.prototype.slice.call(arguments));\n      } else {\n        subscriber.next(e);\n      }\n    }\n\n    setupSubscription(target, eventName, handler, subscriber, options);\n  });\n}\n\nfunction setupSubscription(sourceObj, eventName, handler, subscriber, options) {\n  let unsubscribe;\n\n  if (isEventTarget(sourceObj)) {\n    const source = sourceObj;\n    sourceObj.addEventListener(eventName, handler, options);\n\n    unsubscribe = () => source.removeEventListener(eventName, handler, options);\n  } else if (isJQueryStyleEventEmitter(sourceObj)) {\n    const source = sourceObj;\n    sourceObj.on(eventName, handler);\n\n    unsubscribe = () => source.off(eventName, handler);\n  } else if (isNodeStyleEventEmitter(sourceObj)) {\n    const source = sourceObj;\n    sourceObj.addListener(eventName, handler);\n\n    unsubscribe = () => source.removeListener(eventName, handler);\n  } else if (sourceObj && sourceObj.length) {\n    for (let i = 0, len = sourceObj.length; i < len; i++) {\n      setupSubscription(sourceObj[i], eventName, handler, subscriber, options);\n    }\n  } else {\n    throw new TypeError('Invalid event target');\n  }\n\n  subscriber.add(unsubscribe);\n}\n\nfunction isNodeStyleEventEmitter(sourceObj) {\n  return sourceObj && typeof sourceObj.addListener === 'function' && typeof sourceObj.removeListener === 'function';\n}\n\nfunction isJQueryStyleEventEmitter(sourceObj) {\n  return sourceObj && typeof sourceObj.on === 'function' && typeof sourceObj.off === 'function';\n}\n\nfunction isEventTarget(sourceObj) {\n  return sourceObj && typeof sourceObj.addEventListener === 'function' && typeof sourceObj.removeEventListener === 'function';\n}", "map": {"version": 3, "names": ["Observable", "isArray", "isFunction", "map", "toString", "Object", "prototype", "fromEvent", "target", "eventName", "options", "resultSelector", "undefined", "pipe", "args", "subscriber", "handler", "e", "arguments", "length", "next", "Array", "slice", "call", "setupSubscription", "sourceObj", "unsubscribe", "isEventTarget", "source", "addEventListener", "removeEventListener", "isJQueryStyleEventEmitter", "on", "off", "isNodeStyleEventEmitter", "addListener", "removeListener", "i", "len", "TypeError", "add"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/fromEvent.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { isArray } from '../util/isArray';\nimport { isFunction } from '../util/isFunction';\nimport { map } from '../operators/map';\nconst toString = (() => Object.prototype.toString)();\nexport function fromEvent(target, eventName, options, resultSelector) {\n    if (isFunction(options)) {\n        resultSelector = options;\n        options = undefined;\n    }\n    if (resultSelector) {\n        return fromEvent(target, eventName, options).pipe(map(args => isArray(args) ? resultSelector(...args) : resultSelector(args)));\n    }\n    return new Observable(subscriber => {\n        function handler(e) {\n            if (arguments.length > 1) {\n                subscriber.next(Array.prototype.slice.call(arguments));\n            }\n            else {\n                subscriber.next(e);\n            }\n        }\n        setupSubscription(target, eventName, handler, subscriber, options);\n    });\n}\nfunction setupSubscription(sourceObj, eventName, handler, subscriber, options) {\n    let unsubscribe;\n    if (isEventTarget(sourceObj)) {\n        const source = sourceObj;\n        sourceObj.addEventListener(eventName, handler, options);\n        unsubscribe = () => source.removeEventListener(eventName, handler, options);\n    }\n    else if (isJQueryStyleEventEmitter(sourceObj)) {\n        const source = sourceObj;\n        sourceObj.on(eventName, handler);\n        unsubscribe = () => source.off(eventName, handler);\n    }\n    else if (isNodeStyleEventEmitter(sourceObj)) {\n        const source = sourceObj;\n        sourceObj.addListener(eventName, handler);\n        unsubscribe = () => source.removeListener(eventName, handler);\n    }\n    else if (sourceObj && sourceObj.length) {\n        for (let i = 0, len = sourceObj.length; i < len; i++) {\n            setupSubscription(sourceObj[i], eventName, handler, subscriber, options);\n        }\n    }\n    else {\n        throw new TypeError('Invalid event target');\n    }\n    subscriber.add(unsubscribe);\n}\nfunction isNodeStyleEventEmitter(sourceObj) {\n    return sourceObj && typeof sourceObj.addListener === 'function' && typeof sourceObj.removeListener === 'function';\n}\nfunction isJQueryStyleEventEmitter(sourceObj) {\n    return sourceObj && typeof sourceObj.on === 'function' && typeof sourceObj.off === 'function';\n}\nfunction isEventTarget(sourceObj) {\n    return sourceObj && typeof sourceObj.addEventListener === 'function' && typeof sourceObj.removeEventListener === 'function';\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,OAAT,QAAwB,iBAAxB;AACA,SAASC,UAAT,QAA2B,oBAA3B;AACA,SAASC,GAAT,QAAoB,kBAApB;;AACA,MAAMC,QAAQ,GAAG,CAAC,MAAMC,MAAM,CAACC,SAAP,CAAiBF,QAAxB,GAAjB;;AACA,OAAO,SAASG,SAAT,CAAmBC,MAAnB,EAA2BC,SAA3B,EAAsCC,OAAtC,EAA+CC,cAA/C,EAA+D;EAClE,IAAIT,UAAU,CAACQ,OAAD,CAAd,EAAyB;IACrBC,cAAc,GAAGD,OAAjB;IACAA,OAAO,GAAGE,SAAV;EACH;;EACD,IAAID,cAAJ,EAAoB;IAChB,OAAOJ,SAAS,CAACC,MAAD,EAASC,SAAT,EAAoBC,OAApB,CAAT,CAAsCG,IAAtC,CAA2CV,GAAG,CAACW,IAAI,IAAIb,OAAO,CAACa,IAAD,CAAP,GAAgBH,cAAc,CAAC,GAAGG,IAAJ,CAA9B,GAA0CH,cAAc,CAACG,IAAD,CAAjE,CAA9C,CAAP;EACH;;EACD,OAAO,IAAId,UAAJ,CAAee,UAAU,IAAI;IAChC,SAASC,OAAT,CAAiBC,CAAjB,EAAoB;MAChB,IAAIC,SAAS,CAACC,MAAV,GAAmB,CAAvB,EAA0B;QACtBJ,UAAU,CAACK,IAAX,CAAgBC,KAAK,CAACf,SAAN,CAAgBgB,KAAhB,CAAsBC,IAAtB,CAA2BL,SAA3B,CAAhB;MACH,CAFD,MAGK;QACDH,UAAU,CAACK,IAAX,CAAgBH,CAAhB;MACH;IACJ;;IACDO,iBAAiB,CAAChB,MAAD,EAASC,SAAT,EAAoBO,OAApB,EAA6BD,UAA7B,EAAyCL,OAAzC,CAAjB;EACH,CAVM,CAAP;AAWH;;AACD,SAASc,iBAAT,CAA2BC,SAA3B,EAAsChB,SAAtC,EAAiDO,OAAjD,EAA0DD,UAA1D,EAAsEL,OAAtE,EAA+E;EAC3E,IAAIgB,WAAJ;;EACA,IAAIC,aAAa,CAACF,SAAD,CAAjB,EAA8B;IAC1B,MAAMG,MAAM,GAAGH,SAAf;IACAA,SAAS,CAACI,gBAAV,CAA2BpB,SAA3B,EAAsCO,OAAtC,EAA+CN,OAA/C;;IACAgB,WAAW,GAAG,MAAME,MAAM,CAACE,mBAAP,CAA2BrB,SAA3B,EAAsCO,OAAtC,EAA+CN,OAA/C,CAApB;EACH,CAJD,MAKK,IAAIqB,yBAAyB,CAACN,SAAD,CAA7B,EAA0C;IAC3C,MAAMG,MAAM,GAAGH,SAAf;IACAA,SAAS,CAACO,EAAV,CAAavB,SAAb,EAAwBO,OAAxB;;IACAU,WAAW,GAAG,MAAME,MAAM,CAACK,GAAP,CAAWxB,SAAX,EAAsBO,OAAtB,CAApB;EACH,CAJI,MAKA,IAAIkB,uBAAuB,CAACT,SAAD,CAA3B,EAAwC;IACzC,MAAMG,MAAM,GAAGH,SAAf;IACAA,SAAS,CAACU,WAAV,CAAsB1B,SAAtB,EAAiCO,OAAjC;;IACAU,WAAW,GAAG,MAAME,MAAM,CAACQ,cAAP,CAAsB3B,SAAtB,EAAiCO,OAAjC,CAApB;EACH,CAJI,MAKA,IAAIS,SAAS,IAAIA,SAAS,CAACN,MAA3B,EAAmC;IACpC,KAAK,IAAIkB,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGb,SAAS,CAACN,MAAhC,EAAwCkB,CAAC,GAAGC,GAA5C,EAAiDD,CAAC,EAAlD,EAAsD;MAClDb,iBAAiB,CAACC,SAAS,CAACY,CAAD,CAAV,EAAe5B,SAAf,EAA0BO,OAA1B,EAAmCD,UAAnC,EAA+CL,OAA/C,CAAjB;IACH;EACJ,CAJI,MAKA;IACD,MAAM,IAAI6B,SAAJ,CAAc,sBAAd,CAAN;EACH;;EACDxB,UAAU,CAACyB,GAAX,CAAed,WAAf;AACH;;AACD,SAASQ,uBAAT,CAAiCT,SAAjC,EAA4C;EACxC,OAAOA,SAAS,IAAI,OAAOA,SAAS,CAACU,WAAjB,KAAiC,UAA9C,IAA4D,OAAOV,SAAS,CAACW,cAAjB,KAAoC,UAAvG;AACH;;AACD,SAASL,yBAAT,CAAmCN,SAAnC,EAA8C;EAC1C,OAAOA,SAAS,IAAI,OAAOA,SAAS,CAACO,EAAjB,KAAwB,UAArC,IAAmD,OAAOP,SAAS,CAACQ,GAAjB,KAAyB,UAAnF;AACH;;AACD,SAASN,aAAT,CAAuBF,SAAvB,EAAkC;EAC9B,OAAOA,SAAS,IAAI,OAAOA,SAAS,CAACI,gBAAjB,KAAsC,UAAnD,IAAiE,OAAOJ,SAAS,CAACK,mBAAjB,KAAyC,UAAjH;AACH"}, "metadata": {}, "sourceType": "module"}