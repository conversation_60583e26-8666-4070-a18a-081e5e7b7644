{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { ArgumentOutOfRangeError } from '../util/ArgumentOutOfRangeError';\nimport { empty } from '../observable/empty';\nexport function take(count) {\n  return source => {\n    if (count === 0) {\n      return empty();\n    } else {\n      return source.lift(new TakeOperator(count));\n    }\n  };\n}\n\nclass TakeOperator {\n  constructor(total) {\n    this.total = total;\n\n    if (this.total < 0) {\n      throw new ArgumentOutOfRangeError();\n    }\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new TakeSubscriber(subscriber, this.total));\n  }\n\n}\n\nclass TakeSubscriber extends Subscriber {\n  constructor(destination, total) {\n    super(destination);\n    this.total = total;\n    this.count = 0;\n  }\n\n  _next(value) {\n    const total = this.total;\n    const count = ++this.count;\n\n    if (count <= total) {\n      this.destination.next(value);\n\n      if (count === total) {\n        this.destination.complete();\n        this.unsubscribe();\n      }\n    }\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "ArgumentOutOfRangeError", "empty", "take", "count", "source", "lift", "TakeOperator", "constructor", "total", "call", "subscriber", "subscribe", "TakeSubscriber", "destination", "_next", "value", "next", "complete", "unsubscribe"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/take.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { ArgumentOutOfRangeError } from '../util/ArgumentOutOfRangeError';\nimport { empty } from '../observable/empty';\nexport function take(count) {\n    return (source) => {\n        if (count === 0) {\n            return empty();\n        }\n        else {\n            return source.lift(new TakeOperator(count));\n        }\n    };\n}\nclass TakeOperator {\n    constructor(total) {\n        this.total = total;\n        if (this.total < 0) {\n            throw new ArgumentOutOfRangeError;\n        }\n    }\n    call(subscriber, source) {\n        return source.subscribe(new TakeSubscriber(subscriber, this.total));\n    }\n}\nclass TakeSubscriber extends Subscriber {\n    constructor(destination, total) {\n        super(destination);\n        this.total = total;\n        this.count = 0;\n    }\n    _next(value) {\n        const total = this.total;\n        const count = ++this.count;\n        if (count <= total) {\n            this.destination.next(value);\n            if (count === total) {\n                this.destination.complete();\n                this.unsubscribe();\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,uBAAT,QAAwC,iCAAxC;AACA,SAASC,KAAT,QAAsB,qBAAtB;AACA,OAAO,SAASC,IAAT,CAAcC,KAAd,EAAqB;EACxB,OAAQC,MAAD,IAAY;IACf,IAAID,KAAK,KAAK,CAAd,EAAiB;MACb,OAAOF,KAAK,EAAZ;IACH,CAFD,MAGK;MACD,OAAOG,MAAM,CAACC,IAAP,CAAY,IAAIC,YAAJ,CAAiBH,KAAjB,CAAZ,CAAP;IACH;EACJ,CAPD;AAQH;;AACD,MAAMG,YAAN,CAAmB;EACfC,WAAW,CAACC,KAAD,EAAQ;IACf,KAAKA,KAAL,GAAaA,KAAb;;IACA,IAAI,KAAKA,KAAL,GAAa,CAAjB,EAAoB;MAChB,MAAM,IAAIR,uBAAJ,EAAN;IACH;EACJ;;EACDS,IAAI,CAACC,UAAD,EAAaN,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACO,SAAP,CAAiB,IAAIC,cAAJ,CAAmBF,UAAnB,EAA+B,KAAKF,KAApC,CAAjB,CAAP;EACH;;AATc;;AAWnB,MAAMI,cAAN,SAA6Bb,UAA7B,CAAwC;EACpCQ,WAAW,CAACM,WAAD,EAAcL,KAAd,EAAqB;IAC5B,MAAMK,WAAN;IACA,KAAKL,KAAL,GAAaA,KAAb;IACA,KAAKL,KAAL,GAAa,CAAb;EACH;;EACDW,KAAK,CAACC,KAAD,EAAQ;IACT,MAAMP,KAAK,GAAG,KAAKA,KAAnB;IACA,MAAML,KAAK,GAAG,EAAE,KAAKA,KAArB;;IACA,IAAIA,KAAK,IAAIK,KAAb,EAAoB;MAChB,KAAKK,WAAL,CAAiBG,IAAjB,CAAsBD,KAAtB;;MACA,IAAIZ,KAAK,KAAKK,KAAd,EAAqB;QACjB,KAAKK,WAAL,CAAiBI,QAAjB;QACA,KAAKC,WAAL;MACH;IACJ;EACJ;;AAhBmC"}, "metadata": {}, "sourceType": "module"}