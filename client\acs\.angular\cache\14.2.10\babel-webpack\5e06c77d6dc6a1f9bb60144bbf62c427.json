{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { map } from './map';\nexport function timestamp(scheduler = async) {\n  return map(value => new Timestamp(value, scheduler.now()));\n}\nexport class Timestamp {\n  constructor(value, timestamp) {\n    this.value = value;\n    this.timestamp = timestamp;\n  }\n\n}", "map": {"version": 3, "names": ["async", "map", "timestamp", "scheduler", "value", "Timestamp", "now", "constructor"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/timestamp.js"], "sourcesContent": ["import { async } from '../scheduler/async';\nimport { map } from './map';\nexport function timestamp(scheduler = async) {\n    return map((value) => new Timestamp(value, scheduler.now()));\n}\nexport class Timestamp {\n    constructor(value, timestamp) {\n        this.value = value;\n        this.timestamp = timestamp;\n    }\n}\n"], "mappings": "AAAA,SAASA,KAAT,QAAsB,oBAAtB;AACA,SAASC,GAAT,QAAoB,OAApB;AACA,OAAO,SAASC,SAAT,CAAmBC,SAAS,GAAGH,KAA/B,EAAsC;EACzC,OAAOC,GAAG,CAAEG,KAAD,IAAW,IAAIC,SAAJ,CAAcD,KAAd,EAAqBD,SAAS,CAACG,GAAV,EAArB,CAAZ,CAAV;AACH;AACD,OAAO,MAAMD,SAAN,CAAgB;EACnBE,WAAW,CAACH,KAAD,EAAQF,SAAR,EAAmB;IAC1B,KAAKE,KAAL,GAAaA,KAAb;IACA,KAAKF,SAAL,GAAiBA,SAAjB;EACH;;AAJkB"}, "metadata": {}, "sourceType": "module"}