{"ast": null, "code": "import { reduce } from './reduce';\n\nfunction toArrayReducer(arr, item, index) {\n  if (index === 0) {\n    return [item];\n  }\n\n  arr.push(item);\n  return arr;\n}\n\nexport function toArray() {\n  return reduce(toArrayReducer, []);\n}", "map": {"version": 3, "names": ["reduce", "toArrayReducer", "arr", "item", "index", "push", "toArray"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/toArray.js"], "sourcesContent": ["import { reduce } from './reduce';\nfunction toArrayReducer(arr, item, index) {\n    if (index === 0) {\n        return [item];\n    }\n    arr.push(item);\n    return arr;\n}\nexport function toArray() {\n    return reduce(toArrayReducer, []);\n}\n"], "mappings": "AAAA,SAASA,MAAT,QAAuB,UAAvB;;AACA,SAASC,cAAT,CAAwBC,GAAxB,EAA6BC,IAA7B,EAAmCC,KAAnC,EAA0C;EACtC,IAAIA,KAAK,KAAK,CAAd,EAAiB;IACb,OAAO,CAACD,IAAD,CAAP;EACH;;EACDD,GAAG,CAACG,IAAJ,CAASF,IAAT;EACA,OAAOD,GAAP;AACH;;AACD,OAAO,SAASI,OAAT,GAAmB;EACtB,OAAON,MAAM,CAACC,cAAD,EAAiB,EAAjB,CAAb;AACH"}, "metadata": {}, "sourceType": "module"}