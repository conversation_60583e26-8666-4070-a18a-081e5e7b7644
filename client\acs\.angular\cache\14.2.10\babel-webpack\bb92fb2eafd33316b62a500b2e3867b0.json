{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, ViewChild, Input, NgModule } from '@angular/core';\nimport * as i2 from '@angular/material/core';\nimport { mixinColor, mixinDisabled, mixinDisableRipple, MatRipple, MatRippleModule, MatCommonModule } from '@angular/material/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Default color palette for round buttons (mat-fab and mat-mini-fab) */\n\nconst _c0 = [\"mat-button\", \"\"];\nconst _c1 = [\"*\"];\nconst _c2 = \".mat-button .mat-button-focus-overlay,.mat-icon-button .mat-button-focus-overlay{opacity:0}.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:.04}@media(hover: none){.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:0}}.mat-button,.mat-icon-button,.mat-stroked-button,.mat-flat-button{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-button.mat-button-disabled,.mat-icon-button.mat-button-disabled,.mat-stroked-button.mat-button-disabled,.mat-flat-button.mat-button-disabled{cursor:default}.mat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-button.cdk-program-focused .mat-button-focus-overlay,.mat-icon-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-icon-button.cdk-program-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-program-focused .mat-button-focus-overlay,.mat-flat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-flat-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-raised-button{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button.mat-button-disabled{cursor:default}.mat-raised-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-raised-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-stroked-button{border:1px solid currentColor;padding:0 15px;line-height:34px}.mat-stroked-button .mat-button-ripple.mat-ripple,.mat-stroked-button .mat-button-focus-overlay{top:-1px;left:-1px;right:-1px;bottom:-1px}.mat-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:56px;height:56px;padding:0;flex-shrink:0}.mat-fab::-moz-focus-inner{border:0}.mat-fab.mat-button-disabled{cursor:default}.mat-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-fab::-moz-focus-inner{border:0}.mat-fab._mat-animation-noopable{transition:none !important;animation:none !important}.mat-fab .mat-button-wrapper{padding:16px 0;display:inline-block;line-height:24px}.mat-mini-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:40px;height:40px;padding:0;flex-shrink:0}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab.mat-button-disabled{cursor:default}.mat-mini-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-mini-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mini-fab .mat-button-wrapper{padding:8px 0;display:inline-block;line-height:24px}.mat-icon-button{padding:0;min-width:0;width:40px;height:40px;flex-shrink:0;line-height:40px;border-radius:50%}.mat-icon-button i,.mat-icon-button .mat-icon{line-height:24px}.mat-button-ripple.mat-ripple,.mat-button-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-button-ripple.mat-ripple:not(:empty){transform:translateZ(0)}.mat-button-focus-overlay{opacity:0;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1),background-color 200ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-button-focus-overlay{transition:none}.mat-button-ripple-round{border-radius:50%;z-index:1}.mat-button .mat-button-wrapper>*,.mat-flat-button .mat-button-wrapper>*,.mat-stroked-button .mat-button-wrapper>*,.mat-raised-button .mat-button-wrapper>*,.mat-icon-button .mat-button-wrapper>*,.mat-fab .mat-button-wrapper>*,.mat-mini-fab .mat-button-wrapper>*{vertical-align:middle}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button{display:inline-flex;justify-content:center;align-items:center;font-size:inherit;width:2.5em;height:2.5em}.mat-flat-button::before,.mat-raised-button::before,.mat-fab::before,.mat-mini-fab::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-stroked-button::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px) * -1)}.cdk-high-contrast-active .mat-button,.cdk-high-contrast-active .mat-flat-button,.cdk-high-contrast-active .mat-raised-button,.cdk-high-contrast-active .mat-icon-button,.cdk-high-contrast-active .mat-fab,.cdk-high-contrast-active .mat-mini-fab{outline:solid 1px}\";\nconst DEFAULT_ROUND_BUTTON_COLOR = 'accent';\n/**\n * List of classes to add to MatButton instances based on host attributes to\n * style as different variants.\n */\n\nconst BUTTON_HOST_ATTRIBUTES = ['mat-button', 'mat-flat-button', 'mat-icon-button', 'mat-raised-button', 'mat-stroked-button', 'mat-mini-fab', 'mat-fab']; // Boilerplate for applying mixins to MatButton.\n\nconst _MatButtonBase = mixinColor(mixinDisabled(mixinDisableRipple(class {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n\n})));\n/**\n * Material design button.\n */\n\n\nclass MatButton extends _MatButtonBase {\n  constructor(elementRef, _focusMonitor, _animationMode) {\n    super(elementRef);\n    this._focusMonitor = _focusMonitor;\n    this._animationMode = _animationMode;\n    /** Whether the button is round. */\n\n    this.isRoundButton = this._hasHostAttributes('mat-fab', 'mat-mini-fab');\n    /** Whether the button is icon button. */\n\n    this.isIconButton = this._hasHostAttributes('mat-icon-button'); // For each of the variant selectors that is present in the button's host\n    // attributes, add the correct corresponding class.\n\n    for (const attr of BUTTON_HOST_ATTRIBUTES) {\n      if (this._hasHostAttributes(attr)) {\n        this._getHostElement().classList.add(attr);\n      }\n    } // Add a class that applies to all buttons. This makes it easier to target if somebody\n    // wants to target all Material buttons. We do it here rather than `host` to ensure that\n    // the class is applied to derived classes.\n\n\n    elementRef.nativeElement.classList.add('mat-button-base');\n\n    if (this.isRoundButton) {\n      this.color = DEFAULT_ROUND_BUTTON_COLOR;\n    }\n  }\n\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._elementRef, true);\n  }\n\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n  }\n  /** Focuses the button. */\n\n\n  focus(origin, options) {\n    if (origin) {\n      this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n    } else {\n      this._getHostElement().focus(options);\n    }\n  }\n\n  _getHostElement() {\n    return this._elementRef.nativeElement;\n  }\n\n  _isRippleDisabled() {\n    return this.disableRipple || this.disabled;\n  }\n  /** Gets whether the button has one of the given attributes. */\n\n\n  _hasHostAttributes(...attributes) {\n    return attributes.some(attribute => this._getHostElement().hasAttribute(attribute));\n  }\n\n}\n\nMatButton.ɵfac = function MatButton_Factory(t) {\n  return new (t || MatButton)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n\nMatButton.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatButton,\n  selectors: [[\"button\", \"mat-button\", \"\"], [\"button\", \"mat-raised-button\", \"\"], [\"button\", \"mat-icon-button\", \"\"], [\"button\", \"mat-fab\", \"\"], [\"button\", \"mat-mini-fab\", \"\"], [\"button\", \"mat-stroked-button\", \"\"], [\"button\", \"mat-flat-button\", \"\"]],\n  viewQuery: function MatButton_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(MatRipple, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ripple = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-focus-indicator\"],\n  hostVars: 5,\n  hostBindings: function MatButton_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"disabled\", ctx.disabled || null);\n      i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-button-disabled\", ctx.disabled);\n    }\n  },\n  inputs: {\n    disabled: \"disabled\",\n    disableRipple: \"disableRipple\",\n    color: \"color\"\n  },\n  exportAs: [\"matButton\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  attrs: _c0,\n  ngContentSelectors: _c1,\n  decls: 4,\n  vars: 5,\n  consts: [[1, \"mat-button-wrapper\"], [\"matRipple\", \"\", 1, \"mat-button-ripple\", 3, \"matRippleDisabled\", \"matRippleCentered\", \"matRippleTrigger\"], [1, \"mat-button-focus-overlay\"]],\n  template: function MatButton_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"span\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(2, \"span\", 1)(3, \"span\", 2);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(2);\n      i0.ɵɵclassProp(\"mat-button-ripple-round\", ctx.isRoundButton || ctx.isIconButton);\n      i0.ɵɵproperty(\"matRippleDisabled\", ctx._isRippleDisabled())(\"matRippleCentered\", ctx.isIconButton)(\"matRippleTrigger\", ctx._getHostElement());\n    }\n  },\n  dependencies: [i2.MatRipple],\n  styles: [\".mat-button .mat-button-focus-overlay,.mat-icon-button .mat-button-focus-overlay{opacity:0}.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:.04}@media(hover: none){.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:0}}.mat-button,.mat-icon-button,.mat-stroked-button,.mat-flat-button{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-button.mat-button-disabled,.mat-icon-button.mat-button-disabled,.mat-stroked-button.mat-button-disabled,.mat-flat-button.mat-button-disabled{cursor:default}.mat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-button.cdk-program-focused .mat-button-focus-overlay,.mat-icon-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-icon-button.cdk-program-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-program-focused .mat-button-focus-overlay,.mat-flat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-flat-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-raised-button{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button.mat-button-disabled{cursor:default}.mat-raised-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-raised-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-stroked-button{border:1px solid currentColor;padding:0 15px;line-height:34px}.mat-stroked-button .mat-button-ripple.mat-ripple,.mat-stroked-button .mat-button-focus-overlay{top:-1px;left:-1px;right:-1px;bottom:-1px}.mat-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:56px;height:56px;padding:0;flex-shrink:0}.mat-fab::-moz-focus-inner{border:0}.mat-fab.mat-button-disabled{cursor:default}.mat-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-fab::-moz-focus-inner{border:0}.mat-fab._mat-animation-noopable{transition:none !important;animation:none !important}.mat-fab .mat-button-wrapper{padding:16px 0;display:inline-block;line-height:24px}.mat-mini-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:40px;height:40px;padding:0;flex-shrink:0}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab.mat-button-disabled{cursor:default}.mat-mini-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-mini-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mini-fab .mat-button-wrapper{padding:8px 0;display:inline-block;line-height:24px}.mat-icon-button{padding:0;min-width:0;width:40px;height:40px;flex-shrink:0;line-height:40px;border-radius:50%}.mat-icon-button i,.mat-icon-button .mat-icon{line-height:24px}.mat-button-ripple.mat-ripple,.mat-button-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-button-ripple.mat-ripple:not(:empty){transform:translateZ(0)}.mat-button-focus-overlay{opacity:0;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1),background-color 200ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-button-focus-overlay{transition:none}.mat-button-ripple-round{border-radius:50%;z-index:1}.mat-button .mat-button-wrapper>*,.mat-flat-button .mat-button-wrapper>*,.mat-stroked-button .mat-button-wrapper>*,.mat-raised-button .mat-button-wrapper>*,.mat-icon-button .mat-button-wrapper>*,.mat-fab .mat-button-wrapper>*,.mat-mini-fab .mat-button-wrapper>*{vertical-align:middle}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button{display:inline-flex;justify-content:center;align-items:center;font-size:inherit;width:2.5em;height:2.5em}.mat-flat-button::before,.mat-raised-button::before,.mat-fab::before,.mat-mini-fab::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-stroked-button::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px) * -1)}.cdk-high-contrast-active .mat-button,.cdk-high-contrast-active .mat-flat-button,.cdk-high-contrast-active .mat-raised-button,.cdk-high-contrast-active .mat-icon-button,.cdk-high-contrast-active .mat-fab,.cdk-high-contrast-active .mat-mini-fab{outline:solid 1px}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatButton, [{\n    type: Component,\n    args: [{\n      selector: `button[mat-button], button[mat-raised-button], button[mat-icon-button],\n             button[mat-fab], button[mat-mini-fab], button[mat-stroked-button],\n             button[mat-flat-button]`,\n      exportAs: 'matButton',\n      host: {\n        '[attr.disabled]': 'disabled || null',\n        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n        // Add a class for disabled button styling instead of the using attribute\n        // selector or pseudo-selector.  This allows users to create focusable\n        // disabled buttons without recreating the styles.\n        '[class.mat-button-disabled]': 'disabled',\n        'class': 'mat-focus-indicator'\n      },\n      inputs: ['disabled', 'disableRipple', 'color'],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<span class=\\\"mat-button-wrapper\\\"><ng-content></ng-content></span>\\n<span matRipple class=\\\"mat-button-ripple\\\"\\n      [class.mat-button-ripple-round]=\\\"isRoundButton || isIconButton\\\"\\n      [matRippleDisabled]=\\\"_isRippleDisabled()\\\"\\n      [matRippleCentered]=\\\"isIconButton\\\"\\n      [matRippleTrigger]=\\\"_getHostElement()\\\"></span>\\n<span class=\\\"mat-button-focus-overlay\\\"></span>\\n\",\n      styles: [\".mat-button .mat-button-focus-overlay,.mat-icon-button .mat-button-focus-overlay{opacity:0}.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:.04}@media(hover: none){.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:0}}.mat-button,.mat-icon-button,.mat-stroked-button,.mat-flat-button{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-button.mat-button-disabled,.mat-icon-button.mat-button-disabled,.mat-stroked-button.mat-button-disabled,.mat-flat-button.mat-button-disabled{cursor:default}.mat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-button.cdk-program-focused .mat-button-focus-overlay,.mat-icon-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-icon-button.cdk-program-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-program-focused .mat-button-focus-overlay,.mat-flat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-flat-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-raised-button{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button.mat-button-disabled{cursor:default}.mat-raised-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-raised-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-stroked-button{border:1px solid currentColor;padding:0 15px;line-height:34px}.mat-stroked-button .mat-button-ripple.mat-ripple,.mat-stroked-button .mat-button-focus-overlay{top:-1px;left:-1px;right:-1px;bottom:-1px}.mat-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:56px;height:56px;padding:0;flex-shrink:0}.mat-fab::-moz-focus-inner{border:0}.mat-fab.mat-button-disabled{cursor:default}.mat-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-fab::-moz-focus-inner{border:0}.mat-fab._mat-animation-noopable{transition:none !important;animation:none !important}.mat-fab .mat-button-wrapper{padding:16px 0;display:inline-block;line-height:24px}.mat-mini-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:40px;height:40px;padding:0;flex-shrink:0}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab.mat-button-disabled{cursor:default}.mat-mini-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-mini-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mini-fab .mat-button-wrapper{padding:8px 0;display:inline-block;line-height:24px}.mat-icon-button{padding:0;min-width:0;width:40px;height:40px;flex-shrink:0;line-height:40px;border-radius:50%}.mat-icon-button i,.mat-icon-button .mat-icon{line-height:24px}.mat-button-ripple.mat-ripple,.mat-button-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-button-ripple.mat-ripple:not(:empty){transform:translateZ(0)}.mat-button-focus-overlay{opacity:0;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1),background-color 200ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-button-focus-overlay{transition:none}.mat-button-ripple-round{border-radius:50%;z-index:1}.mat-button .mat-button-wrapper>*,.mat-flat-button .mat-button-wrapper>*,.mat-stroked-button .mat-button-wrapper>*,.mat-raised-button .mat-button-wrapper>*,.mat-icon-button .mat-button-wrapper>*,.mat-fab .mat-button-wrapper>*,.mat-mini-fab .mat-button-wrapper>*{vertical-align:middle}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button{display:inline-flex;justify-content:center;align-items:center;font-size:inherit;width:2.5em;height:2.5em}.mat-flat-button::before,.mat-raised-button::before,.mat-fab::before,.mat-mini-fab::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-stroked-button::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px) * -1)}.cdk-high-contrast-active .mat-button,.cdk-high-contrast-active .mat-flat-button,.cdk-high-contrast-active .mat-raised-button,.cdk-high-contrast-active .mat-icon-button,.cdk-high-contrast-active .mat-fab,.cdk-high-contrast-active .mat-mini-fab{outline:solid 1px}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.FocusMonitor\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    ripple: [{\n      type: ViewChild,\n      args: [MatRipple]\n    }]\n  });\n})();\n/**\n * Material design anchor button.\n */\n\n\nclass MatAnchor extends MatButton {\n  constructor(focusMonitor, elementRef, animationMode,\n  /** @breaking-change 14.0.0 _ngZone will be required. */\n  _ngZone) {\n    super(elementRef, focusMonitor, animationMode);\n    this._ngZone = _ngZone;\n\n    this._haltDisabledEvents = event => {\n      // A disabled button shouldn't apply any actions\n      if (this.disabled) {\n        event.preventDefault();\n        event.stopImmediatePropagation();\n      }\n    };\n  }\n\n  ngAfterViewInit() {\n    super.ngAfterViewInit();\n    /** @breaking-change 14.0.0 _ngZone will be required. */\n\n    if (this._ngZone) {\n      this._ngZone.runOutsideAngular(() => {\n        this._elementRef.nativeElement.addEventListener('click', this._haltDisabledEvents);\n      });\n    } else {\n      this._elementRef.nativeElement.addEventListener('click', this._haltDisabledEvents);\n    }\n  }\n\n  ngOnDestroy() {\n    super.ngOnDestroy();\n\n    this._elementRef.nativeElement.removeEventListener('click', this._haltDisabledEvents);\n  }\n\n}\n\nMatAnchor.ɵfac = function MatAnchor_Factory(t) {\n  return new (t || MatAnchor)(i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(i0.NgZone, 8));\n};\n\nMatAnchor.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatAnchor,\n  selectors: [[\"a\", \"mat-button\", \"\"], [\"a\", \"mat-raised-button\", \"\"], [\"a\", \"mat-icon-button\", \"\"], [\"a\", \"mat-fab\", \"\"], [\"a\", \"mat-mini-fab\", \"\"], [\"a\", \"mat-stroked-button\", \"\"], [\"a\", \"mat-flat-button\", \"\"]],\n  hostAttrs: [1, \"mat-focus-indicator\"],\n  hostVars: 7,\n  hostBindings: function MatAnchor_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex)(\"disabled\", ctx.disabled || null)(\"aria-disabled\", ctx.disabled.toString());\n      i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mat-button-disabled\", ctx.disabled);\n    }\n  },\n  inputs: {\n    disabled: \"disabled\",\n    disableRipple: \"disableRipple\",\n    color: \"color\",\n    tabIndex: \"tabIndex\"\n  },\n  exportAs: [\"matButton\", \"matAnchor\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  attrs: _c0,\n  ngContentSelectors: _c1,\n  decls: 4,\n  vars: 5,\n  consts: [[1, \"mat-button-wrapper\"], [\"matRipple\", \"\", 1, \"mat-button-ripple\", 3, \"matRippleDisabled\", \"matRippleCentered\", \"matRippleTrigger\"], [1, \"mat-button-focus-overlay\"]],\n  template: function MatAnchor_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"span\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(2, \"span\", 1)(3, \"span\", 2);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(2);\n      i0.ɵɵclassProp(\"mat-button-ripple-round\", ctx.isRoundButton || ctx.isIconButton);\n      i0.ɵɵproperty(\"matRippleDisabled\", ctx._isRippleDisabled())(\"matRippleCentered\", ctx.isIconButton)(\"matRippleTrigger\", ctx._getHostElement());\n    }\n  },\n  dependencies: [i2.MatRipple],\n  styles: [_c2],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAnchor, [{\n    type: Component,\n    args: [{\n      selector: `a[mat-button], a[mat-raised-button], a[mat-icon-button], a[mat-fab],\n             a[mat-mini-fab], a[mat-stroked-button], a[mat-flat-button]`,\n      exportAs: 'matButton, matAnchor',\n      host: {\n        // Note that we ignore the user-specified tabindex when it's disabled for\n        // consistency with the `mat-button` applied on native buttons where even\n        // though they have an index, they're not tabbable.\n        '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n        '[attr.disabled]': 'disabled || null',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n        '[class.mat-button-disabled]': 'disabled',\n        'class': 'mat-focus-indicator'\n      },\n      inputs: ['disabled', 'disableRipple', 'color'],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<span class=\\\"mat-button-wrapper\\\"><ng-content></ng-content></span>\\n<span matRipple class=\\\"mat-button-ripple\\\"\\n      [class.mat-button-ripple-round]=\\\"isRoundButton || isIconButton\\\"\\n      [matRippleDisabled]=\\\"_isRippleDisabled()\\\"\\n      [matRippleCentered]=\\\"isIconButton\\\"\\n      [matRippleTrigger]=\\\"_getHostElement()\\\"></span>\\n<span class=\\\"mat-button-focus-overlay\\\"></span>\\n\",\n      styles: [\".mat-button .mat-button-focus-overlay,.mat-icon-button .mat-button-focus-overlay{opacity:0}.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:.04}@media(hover: none){.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:0}}.mat-button,.mat-icon-button,.mat-stroked-button,.mat-flat-button{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-button.mat-button-disabled,.mat-icon-button.mat-button-disabled,.mat-stroked-button.mat-button-disabled,.mat-flat-button.mat-button-disabled{cursor:default}.mat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-button.cdk-program-focused .mat-button-focus-overlay,.mat-icon-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-icon-button.cdk-program-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-program-focused .mat-button-focus-overlay,.mat-flat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-flat-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-raised-button{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button.mat-button-disabled{cursor:default}.mat-raised-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-raised-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-stroked-button{border:1px solid currentColor;padding:0 15px;line-height:34px}.mat-stroked-button .mat-button-ripple.mat-ripple,.mat-stroked-button .mat-button-focus-overlay{top:-1px;left:-1px;right:-1px;bottom:-1px}.mat-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:56px;height:56px;padding:0;flex-shrink:0}.mat-fab::-moz-focus-inner{border:0}.mat-fab.mat-button-disabled{cursor:default}.mat-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-fab::-moz-focus-inner{border:0}.mat-fab._mat-animation-noopable{transition:none !important;animation:none !important}.mat-fab .mat-button-wrapper{padding:16px 0;display:inline-block;line-height:24px}.mat-mini-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:40px;height:40px;padding:0;flex-shrink:0}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab.mat-button-disabled{cursor:default}.mat-mini-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-mini-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mini-fab .mat-button-wrapper{padding:8px 0;display:inline-block;line-height:24px}.mat-icon-button{padding:0;min-width:0;width:40px;height:40px;flex-shrink:0;line-height:40px;border-radius:50%}.mat-icon-button i,.mat-icon-button .mat-icon{line-height:24px}.mat-button-ripple.mat-ripple,.mat-button-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-button-ripple.mat-ripple:not(:empty){transform:translateZ(0)}.mat-button-focus-overlay{opacity:0;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1),background-color 200ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-button-focus-overlay{transition:none}.mat-button-ripple-round{border-radius:50%;z-index:1}.mat-button .mat-button-wrapper>*,.mat-flat-button .mat-button-wrapper>*,.mat-stroked-button .mat-button-wrapper>*,.mat-raised-button .mat-button-wrapper>*,.mat-icon-button .mat-button-wrapper>*,.mat-fab .mat-button-wrapper>*,.mat-mini-fab .mat-button-wrapper>*{vertical-align:middle}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button{display:inline-flex;justify-content:center;align-items:center;font-size:inherit;width:2.5em;height:2.5em}.mat-flat-button::before,.mat-raised-button::before,.mat-fab::before,.mat-mini-fab::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-stroked-button::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px) * -1)}.cdk-high-contrast-active .mat-button,.cdk-high-contrast-active .mat-flat-button,.cdk-high-contrast-active .mat-raised-button,.cdk-high-contrast-active .mat-icon-button,.cdk-high-contrast-active .mat-fab,.cdk-high-contrast-active .mat-mini-fab{outline:solid 1px}\"]\n    }]\n  }], function () {\n    return [{\n      type: i1.FocusMonitor\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }, {\n      type: i0.NgZone,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    tabIndex: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatButtonModule {}\n\nMatButtonModule.ɵfac = function MatButtonModule_Factory(t) {\n  return new (t || MatButtonModule)();\n};\n\nMatButtonModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatButtonModule,\n  declarations: [MatButton, MatAnchor],\n  imports: [MatRippleModule, MatCommonModule],\n  exports: [MatButton, MatAnchor, MatCommonModule]\n});\nMatButtonModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatRippleModule, MatCommonModule, MatCommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatButtonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatRippleModule, MatCommonModule],\n      exports: [MatButton, MatAnchor, MatCommonModule],\n      declarations: [MatButton, MatAnchor]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MatAnchor, MatButton, MatButtonModule };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Optional", "Inject", "ViewChild", "Input", "NgModule", "i2", "mixinColor", "mixinDisabled", "mixinDisableRipple", "<PERSON><PERSON><PERSON><PERSON>", "MatRippleModule", "MatCommonModule", "i1", "ANIMATION_MODULE_TYPE", "DEFAULT_ROUND_BUTTON_COLOR", "BUTTON_HOST_ATTRIBUTES", "_MatButtonBase", "constructor", "_elementRef", "MatButton", "elementRef", "_focusMonitor", "_animationMode", "isRoundButton", "_hasHostAttributes", "isIconButton", "attr", "_getHostElement", "classList", "add", "nativeElement", "color", "ngAfterViewInit", "monitor", "ngOnDestroy", "stopMonitoring", "focus", "origin", "options", "focusVia", "_isRippleDisabled", "disable<PERSON><PERSON><PERSON>", "disabled", "attributes", "some", "attribute", "hasAttribute", "ɵfac", "ElementRef", "FocusMonitor", "ɵcmp", "type", "args", "selector", "exportAs", "host", "inputs", "encapsulation", "None", "changeDetection", "OnPush", "template", "styles", "undefined", "decorators", "ripple", "<PERSON><PERSON><PERSON><PERSON>", "focusMonitor", "animationMode", "_ngZone", "_haltDisabledEvents", "event", "preventDefault", "stopImmediatePropagation", "runOutsideAngular", "addEventListener", "removeEventListener", "NgZone", "tabIndex", "MatButtonModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/material/fesm2020/button.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, ViewChild, Input, NgModule } from '@angular/core';\nimport * as i2 from '@angular/material/core';\nimport { mixinColor, mixinDisabled, mixinDisableRipple, MatRipple, MatRippleModule, MatCommonModule } from '@angular/material/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Default color palette for round buttons (mat-fab and mat-mini-fab) */\nconst DEFAULT_ROUND_BUTTON_COLOR = 'accent';\n/**\n * List of classes to add to MatButton instances based on host attributes to\n * style as different variants.\n */\nconst BUTTON_HOST_ATTRIBUTES = [\n    'mat-button',\n    'mat-flat-button',\n    'mat-icon-button',\n    'mat-raised-button',\n    'mat-stroked-button',\n    'mat-mini-fab',\n    'mat-fab',\n];\n// Boilerplate for applying mixins to MatButton.\nconst _MatButtonBase = mixinColor(mixinDisabled(mixinDisableRipple(class {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n    }\n})));\n/**\n * Material design button.\n */\nclass MatButton extends _MatButtonBase {\n    constructor(elementRef, _focusMonitor, _animationMode) {\n        super(elementRef);\n        this._focusMonitor = _focusMonitor;\n        this._animationMode = _animationMode;\n        /** Whether the button is round. */\n        this.isRoundButton = this._hasHostAttributes('mat-fab', 'mat-mini-fab');\n        /** Whether the button is icon button. */\n        this.isIconButton = this._hasHostAttributes('mat-icon-button');\n        // For each of the variant selectors that is present in the button's host\n        // attributes, add the correct corresponding class.\n        for (const attr of BUTTON_HOST_ATTRIBUTES) {\n            if (this._hasHostAttributes(attr)) {\n                this._getHostElement().classList.add(attr);\n            }\n        }\n        // Add a class that applies to all buttons. This makes it easier to target if somebody\n        // wants to target all Material buttons. We do it here rather than `host` to ensure that\n        // the class is applied to derived classes.\n        elementRef.nativeElement.classList.add('mat-button-base');\n        if (this.isRoundButton) {\n            this.color = DEFAULT_ROUND_BUTTON_COLOR;\n        }\n    }\n    ngAfterViewInit() {\n        this._focusMonitor.monitor(this._elementRef, true);\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n    }\n    /** Focuses the button. */\n    focus(origin, options) {\n        if (origin) {\n            this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n        }\n        else {\n            this._getHostElement().focus(options);\n        }\n    }\n    _getHostElement() {\n        return this._elementRef.nativeElement;\n    }\n    _isRippleDisabled() {\n        return this.disableRipple || this.disabled;\n    }\n    /** Gets whether the button has one of the given attributes. */\n    _hasHostAttributes(...attributes) {\n        return attributes.some(attribute => this._getHostElement().hasAttribute(attribute));\n    }\n}\nMatButton.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatButton, deps: [{ token: i0.ElementRef }, { token: i1.FocusMonitor }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatButton.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatButton, selector: \"button[mat-button], button[mat-raised-button], button[mat-icon-button],\\n             button[mat-fab], button[mat-mini-fab], button[mat-stroked-button],\\n             button[mat-flat-button]\", inputs: { disabled: \"disabled\", disableRipple: \"disableRipple\", color: \"color\" }, host: { properties: { \"attr.disabled\": \"disabled || null\", \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\", \"class.mat-button-disabled\": \"disabled\" }, classAttribute: \"mat-focus-indicator\" }, viewQueries: [{ propertyName: \"ripple\", first: true, predicate: MatRipple, descendants: true }], exportAs: [\"matButton\"], usesInheritance: true, ngImport: i0, template: \"<span class=\\\"mat-button-wrapper\\\"><ng-content></ng-content></span>\\n<span matRipple class=\\\"mat-button-ripple\\\"\\n      [class.mat-button-ripple-round]=\\\"isRoundButton || isIconButton\\\"\\n      [matRippleDisabled]=\\\"_isRippleDisabled()\\\"\\n      [matRippleCentered]=\\\"isIconButton\\\"\\n      [matRippleTrigger]=\\\"_getHostElement()\\\"></span>\\n<span class=\\\"mat-button-focus-overlay\\\"></span>\\n\", styles: [\".mat-button .mat-button-focus-overlay,.mat-icon-button .mat-button-focus-overlay{opacity:0}.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:.04}@media(hover: none){.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:0}}.mat-button,.mat-icon-button,.mat-stroked-button,.mat-flat-button{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-button.mat-button-disabled,.mat-icon-button.mat-button-disabled,.mat-stroked-button.mat-button-disabled,.mat-flat-button.mat-button-disabled{cursor:default}.mat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-button.cdk-program-focused .mat-button-focus-overlay,.mat-icon-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-icon-button.cdk-program-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-program-focused .mat-button-focus-overlay,.mat-flat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-flat-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-raised-button{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button.mat-button-disabled{cursor:default}.mat-raised-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-raised-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-stroked-button{border:1px solid currentColor;padding:0 15px;line-height:34px}.mat-stroked-button .mat-button-ripple.mat-ripple,.mat-stroked-button .mat-button-focus-overlay{top:-1px;left:-1px;right:-1px;bottom:-1px}.mat-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:56px;height:56px;padding:0;flex-shrink:0}.mat-fab::-moz-focus-inner{border:0}.mat-fab.mat-button-disabled{cursor:default}.mat-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-fab::-moz-focus-inner{border:0}.mat-fab._mat-animation-noopable{transition:none !important;animation:none !important}.mat-fab .mat-button-wrapper{padding:16px 0;display:inline-block;line-height:24px}.mat-mini-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:40px;height:40px;padding:0;flex-shrink:0}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab.mat-button-disabled{cursor:default}.mat-mini-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-mini-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mini-fab .mat-button-wrapper{padding:8px 0;display:inline-block;line-height:24px}.mat-icon-button{padding:0;min-width:0;width:40px;height:40px;flex-shrink:0;line-height:40px;border-radius:50%}.mat-icon-button i,.mat-icon-button .mat-icon{line-height:24px}.mat-button-ripple.mat-ripple,.mat-button-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-button-ripple.mat-ripple:not(:empty){transform:translateZ(0)}.mat-button-focus-overlay{opacity:0;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1),background-color 200ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-button-focus-overlay{transition:none}.mat-button-ripple-round{border-radius:50%;z-index:1}.mat-button .mat-button-wrapper>*,.mat-flat-button .mat-button-wrapper>*,.mat-stroked-button .mat-button-wrapper>*,.mat-raised-button .mat-button-wrapper>*,.mat-icon-button .mat-button-wrapper>*,.mat-fab .mat-button-wrapper>*,.mat-mini-fab .mat-button-wrapper>*{vertical-align:middle}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button{display:inline-flex;justify-content:center;align-items:center;font-size:inherit;width:2.5em;height:2.5em}.mat-flat-button::before,.mat-raised-button::before,.mat-fab::before,.mat-mini-fab::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-stroked-button::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px) * -1)}.cdk-high-contrast-active .mat-button,.cdk-high-contrast-active .mat-flat-button,.cdk-high-contrast-active .mat-raised-button,.cdk-high-contrast-active .mat-icon-button,.cdk-high-contrast-active .mat-fab,.cdk-high-contrast-active .mat-mini-fab{outline:solid 1px}\"], dependencies: [{ kind: \"directive\", type: i2.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatButton, decorators: [{\n            type: Component,\n            args: [{ selector: `button[mat-button], button[mat-raised-button], button[mat-icon-button],\n             button[mat-fab], button[mat-mini-fab], button[mat-stroked-button],\n             button[mat-flat-button]`, exportAs: 'matButton', host: {\n                        '[attr.disabled]': 'disabled || null',\n                        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                        // Add a class for disabled button styling instead of the using attribute\n                        // selector or pseudo-selector.  This allows users to create focusable\n                        // disabled buttons without recreating the styles.\n                        '[class.mat-button-disabled]': 'disabled',\n                        'class': 'mat-focus-indicator',\n                    }, inputs: ['disabled', 'disableRipple', 'color'], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<span class=\\\"mat-button-wrapper\\\"><ng-content></ng-content></span>\\n<span matRipple class=\\\"mat-button-ripple\\\"\\n      [class.mat-button-ripple-round]=\\\"isRoundButton || isIconButton\\\"\\n      [matRippleDisabled]=\\\"_isRippleDisabled()\\\"\\n      [matRippleCentered]=\\\"isIconButton\\\"\\n      [matRippleTrigger]=\\\"_getHostElement()\\\"></span>\\n<span class=\\\"mat-button-focus-overlay\\\"></span>\\n\", styles: [\".mat-button .mat-button-focus-overlay,.mat-icon-button .mat-button-focus-overlay{opacity:0}.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:.04}@media(hover: none){.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:0}}.mat-button,.mat-icon-button,.mat-stroked-button,.mat-flat-button{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-button.mat-button-disabled,.mat-icon-button.mat-button-disabled,.mat-stroked-button.mat-button-disabled,.mat-flat-button.mat-button-disabled{cursor:default}.mat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-button.cdk-program-focused .mat-button-focus-overlay,.mat-icon-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-icon-button.cdk-program-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-program-focused .mat-button-focus-overlay,.mat-flat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-flat-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-raised-button{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button.mat-button-disabled{cursor:default}.mat-raised-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-raised-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-stroked-button{border:1px solid currentColor;padding:0 15px;line-height:34px}.mat-stroked-button .mat-button-ripple.mat-ripple,.mat-stroked-button .mat-button-focus-overlay{top:-1px;left:-1px;right:-1px;bottom:-1px}.mat-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:56px;height:56px;padding:0;flex-shrink:0}.mat-fab::-moz-focus-inner{border:0}.mat-fab.mat-button-disabled{cursor:default}.mat-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-fab::-moz-focus-inner{border:0}.mat-fab._mat-animation-noopable{transition:none !important;animation:none !important}.mat-fab .mat-button-wrapper{padding:16px 0;display:inline-block;line-height:24px}.mat-mini-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:40px;height:40px;padding:0;flex-shrink:0}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab.mat-button-disabled{cursor:default}.mat-mini-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-mini-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mini-fab .mat-button-wrapper{padding:8px 0;display:inline-block;line-height:24px}.mat-icon-button{padding:0;min-width:0;width:40px;height:40px;flex-shrink:0;line-height:40px;border-radius:50%}.mat-icon-button i,.mat-icon-button .mat-icon{line-height:24px}.mat-button-ripple.mat-ripple,.mat-button-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-button-ripple.mat-ripple:not(:empty){transform:translateZ(0)}.mat-button-focus-overlay{opacity:0;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1),background-color 200ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-button-focus-overlay{transition:none}.mat-button-ripple-round{border-radius:50%;z-index:1}.mat-button .mat-button-wrapper>*,.mat-flat-button .mat-button-wrapper>*,.mat-stroked-button .mat-button-wrapper>*,.mat-raised-button .mat-button-wrapper>*,.mat-icon-button .mat-button-wrapper>*,.mat-fab .mat-button-wrapper>*,.mat-mini-fab .mat-button-wrapper>*{vertical-align:middle}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button{display:inline-flex;justify-content:center;align-items:center;font-size:inherit;width:2.5em;height:2.5em}.mat-flat-button::before,.mat-raised-button::before,.mat-fab::before,.mat-mini-fab::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-stroked-button::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px) * -1)}.cdk-high-contrast-active .mat-button,.cdk-high-contrast-active .mat-flat-button,.cdk-high-contrast-active .mat-raised-button,.cdk-high-contrast-active .mat-icon-button,.cdk-high-contrast-active .mat-fab,.cdk-high-contrast-active .mat-mini-fab{outline:solid 1px}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.FocusMonitor }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { ripple: [{\n                type: ViewChild,\n                args: [MatRipple]\n            }] } });\n/**\n * Material design anchor button.\n */\nclass MatAnchor extends MatButton {\n    constructor(focusMonitor, elementRef, animationMode, \n    /** @breaking-change 14.0.0 _ngZone will be required. */\n    _ngZone) {\n        super(elementRef, focusMonitor, animationMode);\n        this._ngZone = _ngZone;\n        this._haltDisabledEvents = (event) => {\n            // A disabled button shouldn't apply any actions\n            if (this.disabled) {\n                event.preventDefault();\n                event.stopImmediatePropagation();\n            }\n        };\n    }\n    ngAfterViewInit() {\n        super.ngAfterViewInit();\n        /** @breaking-change 14.0.0 _ngZone will be required. */\n        if (this._ngZone) {\n            this._ngZone.runOutsideAngular(() => {\n                this._elementRef.nativeElement.addEventListener('click', this._haltDisabledEvents);\n            });\n        }\n        else {\n            this._elementRef.nativeElement.addEventListener('click', this._haltDisabledEvents);\n        }\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._elementRef.nativeElement.removeEventListener('click', this._haltDisabledEvents);\n    }\n}\nMatAnchor.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatAnchor, deps: [{ token: i1.FocusMonitor }, { token: i0.ElementRef }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: i0.NgZone, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatAnchor.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatAnchor, selector: \"a[mat-button], a[mat-raised-button], a[mat-icon-button], a[mat-fab],\\n             a[mat-mini-fab], a[mat-stroked-button], a[mat-flat-button]\", inputs: { disabled: \"disabled\", disableRipple: \"disableRipple\", color: \"color\", tabIndex: \"tabIndex\" }, host: { properties: { \"attr.tabindex\": \"disabled ? -1 : tabIndex\", \"attr.disabled\": \"disabled || null\", \"attr.aria-disabled\": \"disabled.toString()\", \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\", \"class.mat-button-disabled\": \"disabled\" }, classAttribute: \"mat-focus-indicator\" }, exportAs: [\"matButton\", \"matAnchor\"], usesInheritance: true, ngImport: i0, template: \"<span class=\\\"mat-button-wrapper\\\"><ng-content></ng-content></span>\\n<span matRipple class=\\\"mat-button-ripple\\\"\\n      [class.mat-button-ripple-round]=\\\"isRoundButton || isIconButton\\\"\\n      [matRippleDisabled]=\\\"_isRippleDisabled()\\\"\\n      [matRippleCentered]=\\\"isIconButton\\\"\\n      [matRippleTrigger]=\\\"_getHostElement()\\\"></span>\\n<span class=\\\"mat-button-focus-overlay\\\"></span>\\n\", styles: [\".mat-button .mat-button-focus-overlay,.mat-icon-button .mat-button-focus-overlay{opacity:0}.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:.04}@media(hover: none){.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:0}}.mat-button,.mat-icon-button,.mat-stroked-button,.mat-flat-button{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-button.mat-button-disabled,.mat-icon-button.mat-button-disabled,.mat-stroked-button.mat-button-disabled,.mat-flat-button.mat-button-disabled{cursor:default}.mat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-button.cdk-program-focused .mat-button-focus-overlay,.mat-icon-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-icon-button.cdk-program-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-program-focused .mat-button-focus-overlay,.mat-flat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-flat-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-raised-button{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button.mat-button-disabled{cursor:default}.mat-raised-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-raised-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-stroked-button{border:1px solid currentColor;padding:0 15px;line-height:34px}.mat-stroked-button .mat-button-ripple.mat-ripple,.mat-stroked-button .mat-button-focus-overlay{top:-1px;left:-1px;right:-1px;bottom:-1px}.mat-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:56px;height:56px;padding:0;flex-shrink:0}.mat-fab::-moz-focus-inner{border:0}.mat-fab.mat-button-disabled{cursor:default}.mat-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-fab::-moz-focus-inner{border:0}.mat-fab._mat-animation-noopable{transition:none !important;animation:none !important}.mat-fab .mat-button-wrapper{padding:16px 0;display:inline-block;line-height:24px}.mat-mini-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:40px;height:40px;padding:0;flex-shrink:0}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab.mat-button-disabled{cursor:default}.mat-mini-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-mini-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mini-fab .mat-button-wrapper{padding:8px 0;display:inline-block;line-height:24px}.mat-icon-button{padding:0;min-width:0;width:40px;height:40px;flex-shrink:0;line-height:40px;border-radius:50%}.mat-icon-button i,.mat-icon-button .mat-icon{line-height:24px}.mat-button-ripple.mat-ripple,.mat-button-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-button-ripple.mat-ripple:not(:empty){transform:translateZ(0)}.mat-button-focus-overlay{opacity:0;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1),background-color 200ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-button-focus-overlay{transition:none}.mat-button-ripple-round{border-radius:50%;z-index:1}.mat-button .mat-button-wrapper>*,.mat-flat-button .mat-button-wrapper>*,.mat-stroked-button .mat-button-wrapper>*,.mat-raised-button .mat-button-wrapper>*,.mat-icon-button .mat-button-wrapper>*,.mat-fab .mat-button-wrapper>*,.mat-mini-fab .mat-button-wrapper>*{vertical-align:middle}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button{display:inline-flex;justify-content:center;align-items:center;font-size:inherit;width:2.5em;height:2.5em}.mat-flat-button::before,.mat-raised-button::before,.mat-fab::before,.mat-mini-fab::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-stroked-button::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px) * -1)}.cdk-high-contrast-active .mat-button,.cdk-high-contrast-active .mat-flat-button,.cdk-high-contrast-active .mat-raised-button,.cdk-high-contrast-active .mat-icon-button,.cdk-high-contrast-active .mat-fab,.cdk-high-contrast-active .mat-mini-fab{outline:solid 1px}\"], dependencies: [{ kind: \"directive\", type: i2.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatAnchor, decorators: [{\n            type: Component,\n            args: [{ selector: `a[mat-button], a[mat-raised-button], a[mat-icon-button], a[mat-fab],\n             a[mat-mini-fab], a[mat-stroked-button], a[mat-flat-button]`, exportAs: 'matButton, matAnchor', host: {\n                        // Note that we ignore the user-specified tabindex when it's disabled for\n                        // consistency with the `mat-button` applied on native buttons where even\n                        // though they have an index, they're not tabbable.\n                        '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n                        '[attr.disabled]': 'disabled || null',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                        '[class.mat-button-disabled]': 'disabled',\n                        'class': 'mat-focus-indicator',\n                    }, inputs: ['disabled', 'disableRipple', 'color'], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<span class=\\\"mat-button-wrapper\\\"><ng-content></ng-content></span>\\n<span matRipple class=\\\"mat-button-ripple\\\"\\n      [class.mat-button-ripple-round]=\\\"isRoundButton || isIconButton\\\"\\n      [matRippleDisabled]=\\\"_isRippleDisabled()\\\"\\n      [matRippleCentered]=\\\"isIconButton\\\"\\n      [matRippleTrigger]=\\\"_getHostElement()\\\"></span>\\n<span class=\\\"mat-button-focus-overlay\\\"></span>\\n\", styles: [\".mat-button .mat-button-focus-overlay,.mat-icon-button .mat-button-focus-overlay{opacity:0}.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:.04}@media(hover: none){.mat-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay,.mat-stroked-button:hover:not(.mat-button-disabled) .mat-button-focus-overlay{opacity:0}}.mat-button,.mat-icon-button,.mat-stroked-button,.mat-flat-button{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-button.mat-button-disabled,.mat-icon-button.mat-button-disabled,.mat-stroked-button.mat-button-disabled,.mat-flat-button.mat-button-disabled{cursor:default}.mat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-button.cdk-program-focused .mat-button-focus-overlay,.mat-icon-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-icon-button.cdk-program-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-stroked-button.cdk-program-focused .mat-button-focus-overlay,.mat-flat-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-flat-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-button::-moz-focus-inner,.mat-icon-button::-moz-focus-inner,.mat-stroked-button::-moz-focus-inner,.mat-flat-button::-moz-focus-inner{border:0}.mat-raised-button{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button.mat-button-disabled{cursor:default}.mat-raised-button.cdk-keyboard-focused .mat-button-focus-overlay,.mat-raised-button.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-raised-button::-moz-focus-inner{border:0}.mat-raised-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-stroked-button{border:1px solid currentColor;padding:0 15px;line-height:34px}.mat-stroked-button .mat-button-ripple.mat-ripple,.mat-stroked-button .mat-button-focus-overlay{top:-1px;left:-1px;right:-1px;bottom:-1px}.mat-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:56px;height:56px;padding:0;flex-shrink:0}.mat-fab::-moz-focus-inner{border:0}.mat-fab.mat-button-disabled{cursor:default}.mat-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-fab::-moz-focus-inner{border:0}.mat-fab._mat-animation-noopable{transition:none !important;animation:none !important}.mat-fab .mat-button-wrapper{padding:16px 0;display:inline-block;line-height:24px}.mat-mini-fab{box-sizing:border-box;position:relative;-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);display:inline-block;white-space:nowrap;text-decoration:none;vertical-align:baseline;text-align:center;margin:0;min-width:64px;line-height:36px;padding:0 16px;border-radius:4px;overflow:visible;transform:translate3d(0, 0, 0);transition:background 400ms cubic-bezier(0.25, 0.8, 0.25, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);min-width:0;border-radius:50%;width:40px;height:40px;padding:0;flex-shrink:0}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab.mat-button-disabled{cursor:default}.mat-mini-fab.cdk-keyboard-focused .mat-button-focus-overlay,.mat-mini-fab.cdk-program-focused .mat-button-focus-overlay{opacity:.12}.mat-mini-fab::-moz-focus-inner{border:0}.mat-mini-fab._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mini-fab .mat-button-wrapper{padding:8px 0;display:inline-block;line-height:24px}.mat-icon-button{padding:0;min-width:0;width:40px;height:40px;flex-shrink:0;line-height:40px;border-radius:50%}.mat-icon-button i,.mat-icon-button .mat-icon{line-height:24px}.mat-button-ripple.mat-ripple,.mat-button-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-button-ripple.mat-ripple:not(:empty){transform:translateZ(0)}.mat-button-focus-overlay{opacity:0;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1),background-color 200ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-button-focus-overlay{transition:none}.mat-button-ripple-round{border-radius:50%;z-index:1}.mat-button .mat-button-wrapper>*,.mat-flat-button .mat-button-wrapper>*,.mat-stroked-button .mat-button-wrapper>*,.mat-raised-button .mat-button-wrapper>*,.mat-icon-button .mat-button-wrapper>*,.mat-fab .mat-button-wrapper>*,.mat-mini-fab .mat-button-wrapper>*{vertical-align:middle}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button{display:inline-flex;justify-content:center;align-items:center;font-size:inherit;width:2.5em;height:2.5em}.mat-flat-button::before,.mat-raised-button::before,.mat-fab::before,.mat-mini-fab::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-stroked-button::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px) * -1)}.cdk-high-contrast-active .mat-button,.cdk-high-contrast-active .mat-flat-button,.cdk-high-contrast-active .mat-raised-button,.cdk-high-contrast-active .mat-icon-button,.cdk-high-contrast-active .mat-fab,.cdk-high-contrast-active .mat-mini-fab{outline:solid 1px}\"] }]\n        }], ctorParameters: function () { return [{ type: i1.FocusMonitor }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: i0.NgZone, decorators: [{\n                    type: Optional\n                }] }]; }, propDecorators: { tabIndex: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatButtonModule {\n}\nMatButtonModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatButtonModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatButtonModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: MatButtonModule, declarations: [MatButton, MatAnchor], imports: [MatRippleModule, MatCommonModule], exports: [MatButton, MatAnchor, MatCommonModule] });\nMatButtonModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatButtonModule, imports: [MatRippleModule, MatCommonModule, MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatButtonModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatRippleModule, MatCommonModule],\n                    exports: [MatButton, MatAnchor, MatCommonModule],\n                    declarations: [MatButton, MatAnchor],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatAnchor, MatButton, MatButtonModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,SAAT,EAAoBC,iBAApB,EAAuCC,uBAAvC,EAAgEC,QAAhE,EAA0EC,MAA1E,EAAkFC,SAAlF,EAA6FC,KAA7F,EAAoGC,QAApG,QAAoH,eAApH;AACA,OAAO,KAAKC,EAAZ,MAAoB,wBAApB;AACA,SAASC,UAAT,EAAqBC,aAArB,EAAoCC,kBAApC,EAAwDC,SAAxD,EAAmEC,eAAnE,EAAoFC,eAApF,QAA2G,wBAA3G;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,SAASC,qBAAT,QAAsC,sCAAtC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;;;AACA,MAAMC,0BAA0B,GAAG,QAAnC;AACA;AACA;AACA;AACA;;AACA,MAAMC,sBAAsB,GAAG,CAC3B,YAD2B,EAE3B,iBAF2B,EAG3B,iBAH2B,EAI3B,mBAJ2B,EAK3B,oBAL2B,EAM3B,cAN2B,EAO3B,SAP2B,CAA/B,C,CASA;;AACA,MAAMC,cAAc,GAAGV,UAAU,CAACC,aAAa,CAACC,kBAAkB,CAAC,MAAM;EACrES,WAAW,CAACC,WAAD,EAAc;IACrB,KAAKA,WAAL,GAAmBA,WAAnB;EACH;;AAHoE,CAAP,CAAnB,CAAd,CAAjC;AAKA;AACA;AACA;;;AACA,MAAMC,SAAN,SAAwBH,cAAxB,CAAuC;EACnCC,WAAW,CAACG,UAAD,EAAaC,aAAb,EAA4BC,cAA5B,EAA4C;IACnD,MAAMF,UAAN;IACA,KAAKC,aAAL,GAAqBA,aAArB;IACA,KAAKC,cAAL,GAAsBA,cAAtB;IACA;;IACA,KAAKC,aAAL,GAAqB,KAAKC,kBAAL,CAAwB,SAAxB,EAAmC,cAAnC,CAArB;IACA;;IACA,KAAKC,YAAL,GAAoB,KAAKD,kBAAL,CAAwB,iBAAxB,CAApB,CAPmD,CAQnD;IACA;;IACA,KAAK,MAAME,IAAX,IAAmBX,sBAAnB,EAA2C;MACvC,IAAI,KAAKS,kBAAL,CAAwBE,IAAxB,CAAJ,EAAmC;QAC/B,KAAKC,eAAL,GAAuBC,SAAvB,CAAiCC,GAAjC,CAAqCH,IAArC;MACH;IACJ,CAdkD,CAenD;IACA;IACA;;;IACAN,UAAU,CAACU,aAAX,CAAyBF,SAAzB,CAAmCC,GAAnC,CAAuC,iBAAvC;;IACA,IAAI,KAAKN,aAAT,EAAwB;MACpB,KAAKQ,KAAL,GAAajB,0BAAb;IACH;EACJ;;EACDkB,eAAe,GAAG;IACd,KAAKX,aAAL,CAAmBY,OAAnB,CAA2B,KAAKf,WAAhC,EAA6C,IAA7C;EACH;;EACDgB,WAAW,GAAG;IACV,KAAKb,aAAL,CAAmBc,cAAnB,CAAkC,KAAKjB,WAAvC;EACH;EACD;;;EACAkB,KAAK,CAACC,MAAD,EAASC,OAAT,EAAkB;IACnB,IAAID,MAAJ,EAAY;MACR,KAAKhB,aAAL,CAAmBkB,QAAnB,CAA4B,KAAKZ,eAAL,EAA5B,EAAoDU,MAApD,EAA4DC,OAA5D;IACH,CAFD,MAGK;MACD,KAAKX,eAAL,GAAuBS,KAAvB,CAA6BE,OAA7B;IACH;EACJ;;EACDX,eAAe,GAAG;IACd,OAAO,KAAKT,WAAL,CAAiBY,aAAxB;EACH;;EACDU,iBAAiB,GAAG;IAChB,OAAO,KAAKC,aAAL,IAAsB,KAAKC,QAAlC;EACH;EACD;;;EACAlB,kBAAkB,CAAC,GAAGmB,UAAJ,EAAgB;IAC9B,OAAOA,UAAU,CAACC,IAAX,CAAgBC,SAAS,IAAI,KAAKlB,eAAL,GAAuBmB,YAAvB,CAAoCD,SAApC,CAA7B,CAAP;EACH;;AAhDkC;;AAkDvC1B,SAAS,CAAC4B,IAAV;EAAA,iBAAsG5B,SAAtG,EAA4FvB,EAA5F,mBAAiIA,EAAE,CAACoD,UAApI,GAA4FpD,EAA5F,mBAA2JgB,EAAE,CAACqC,YAA9J,GAA4FrD,EAA5F,mBAAuLiB,qBAAvL;AAAA;;AACAM,SAAS,CAAC+B,IAAV,kBAD4FtD,EAC5F;EAAA,MAA0FuB,SAA1F;EAAA;EAAA;IAAA;MAD4FvB,EAC5F,aAA4pBa,SAA5pB;IAAA;;IAAA;MAAA;;MAD4Fb,EAC5F,qBAD4FA,EAC5F;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD4FA,EAC5F;MAD4FA,EAC5F;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAD4FA,EAC5F;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD4FA,EAC5F;MAD4FA,EAC0qB,6BAAtwB;MAD4FA,EAC6sB,gBAAzyB;MAD4FA,EACsuB,eAAl0B;MAD4FA,EAC+uB,sCAA30B;IAAA;;IAAA;MAD4FA,EACkyB,aAA93B;MAD4FA,EACkyB,8EAA93B;MAD4FA,EAC22B,2IAAv8B;IAAA;EAAA;EAAA,eAAywPS,EAAE,CAACI,SAA5wP;EAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDAF4Fb,EAE5F,mBAA2FuB,SAA3F,EAAkH,CAAC;IACvGgC,IAAI,EAAEtD,SADiG;IAEvGuD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAG;AAChC;AACA,qCAFmB;MAEoBC,QAAQ,EAAE,WAF9B;MAE2CC,IAAI,EAAE;QAC5C,mBAAmB,kBADyB;QAE5C,mCAAmC,qCAFS;QAG5C;QACA;QACA;QACA,+BAA+B,UANa;QAO5C,SAAS;MAPmC,CAFjD;MAUIC,MAAM,EAAE,CAAC,UAAD,EAAa,eAAb,EAA8B,OAA9B,CAVZ;MAUoDC,aAAa,EAAE3D,iBAAiB,CAAC4D,IAVrF;MAU2FC,eAAe,EAAE5D,uBAAuB,CAAC6D,MAVpI;MAU4IC,QAAQ,EAAE,sYAVtJ;MAU8hBC,MAAM,EAAE,CAAC,skNAAD;IAVtiB,CAAD;EAFiG,CAAD,CAAlH,EAa4B,YAAY;IAAE,OAAO,CAAC;MAAEX,IAAI,EAAEvD,EAAE,CAACoD;IAAX,CAAD,EAA0B;MAAEG,IAAI,EAAEvC,EAAE,CAACqC;IAAX,CAA1B,EAAqD;MAAEE,IAAI,EAAEY,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClHb,IAAI,EAAEnD;MAD4G,CAAD,EAElH;QACCmD,IAAI,EAAElD,MADP;QAECmD,IAAI,EAAE,CAACvC,qBAAD;MAFP,CAFkH;IAA/B,CAArD,CAAP;EAKlB,CAlBxB,EAkB0C;IAAEoD,MAAM,EAAE,CAAC;MACrCd,IAAI,EAAEjD,SAD+B;MAErCkD,IAAI,EAAE,CAAC3C,SAAD;IAF+B,CAAD;EAAV,CAlB1C;AAAA;AAsBA;AACA;AACA;;;AACA,MAAMyD,SAAN,SAAwB/C,SAAxB,CAAkC;EAC9BF,WAAW,CAACkD,YAAD,EAAe/C,UAAf,EAA2BgD,aAA3B;EACX;EACAC,OAFW,EAEF;IACL,MAAMjD,UAAN,EAAkB+C,YAAlB,EAAgCC,aAAhC;IACA,KAAKC,OAAL,GAAeA,OAAf;;IACA,KAAKC,mBAAL,GAA4BC,KAAD,IAAW;MAClC;MACA,IAAI,KAAK7B,QAAT,EAAmB;QACf6B,KAAK,CAACC,cAAN;QACAD,KAAK,CAACE,wBAAN;MACH;IACJ,CAND;EAOH;;EACDzC,eAAe,GAAG;IACd,MAAMA,eAAN;IACA;;IACA,IAAI,KAAKqC,OAAT,EAAkB;MACd,KAAKA,OAAL,CAAaK,iBAAb,CAA+B,MAAM;QACjC,KAAKxD,WAAL,CAAiBY,aAAjB,CAA+B6C,gBAA/B,CAAgD,OAAhD,EAAyD,KAAKL,mBAA9D;MACH,CAFD;IAGH,CAJD,MAKK;MACD,KAAKpD,WAAL,CAAiBY,aAAjB,CAA+B6C,gBAA/B,CAAgD,OAAhD,EAAyD,KAAKL,mBAA9D;IACH;EACJ;;EACDpC,WAAW,GAAG;IACV,MAAMA,WAAN;;IACA,KAAKhB,WAAL,CAAiBY,aAAjB,CAA+B8C,mBAA/B,CAAmD,OAAnD,EAA4D,KAAKN,mBAAjE;EACH;;AA7B6B;;AA+BlCJ,SAAS,CAACnB,IAAV;EAAA,iBAAsGmB,SAAtG,EA1D4FtE,EA0D5F,mBAAiIgB,EAAE,CAACqC,YAApI,GA1D4FrD,EA0D5F,mBAA6JA,EAAE,CAACoD,UAAhK,GA1D4FpD,EA0D5F,mBAAuLiB,qBAAvL,MA1D4FjB,EA0D5F,mBAAyOA,EAAE,CAACiF,MAA5O;AAAA;;AACAX,SAAS,CAAChB,IAAV,kBA3D4FtD,EA2D5F;EAAA,MAA0FsE,SAA1F;EAAA;EAAA;EAAA;EAAA;IAAA;MA3D4FtE,EA2D5F;MA3D4FA,EA2D5F;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WA3D4FA,EA2D5F;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA3D4FA,EA2D5F;MA3D4FA,EA2DqpB,6BAAjvB;MA3D4FA,EA2DwrB,gBAApxB;MA3D4FA,EA2DitB,eAA7yB;MA3D4FA,EA2D0tB,sCAAtzB;IAAA;;IAAA;MA3D4FA,EA2D6wB,aAAz2B;MA3D4FA,EA2D6wB,8EAAz2B;MA3D4FA,EA2Ds1B,2IAAl7B;IAAA;EAAA;EAAA,eAAovPS,EAAE,CAACI,SAAvvP;EAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDA5D4Fb,EA4D5F,mBAA2FsE,SAA3F,EAAkH,CAAC;IACvGf,IAAI,EAAEtD,SADiG;IAEvGuD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAG;AAChC,wEADmB;MACuDC,QAAQ,EAAE,sBADjE;MACyFC,IAAI,EAAE;QAC1F;QACA;QACA;QACA,mBAAmB,0BAJuE;QAK1F,mBAAmB,kBALuE;QAM1F,wBAAwB,qBANkE;QAO1F,mCAAmC,qCAPuD;QAQ1F,+BAA+B,UAR2D;QAS1F,SAAS;MATiF,CAD/F;MAWIC,MAAM,EAAE,CAAC,UAAD,EAAa,eAAb,EAA8B,OAA9B,CAXZ;MAWoDC,aAAa,EAAE3D,iBAAiB,CAAC4D,IAXrF;MAW2FC,eAAe,EAAE5D,uBAAuB,CAAC6D,MAXpI;MAW4IC,QAAQ,EAAE,sYAXtJ;MAW8hBC,MAAM,EAAE,CAAC,skNAAD;IAXtiB,CAAD;EAFiG,CAAD,CAAlH,EAc4B,YAAY;IAAE,OAAO,CAAC;MAAEX,IAAI,EAAEvC,EAAE,CAACqC;IAAX,CAAD,EAA4B;MAAEE,IAAI,EAAEvD,EAAE,CAACoD;IAAX,CAA5B,EAAqD;MAAEG,IAAI,EAAEY,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClHb,IAAI,EAAEnD;MAD4G,CAAD,EAElH;QACCmD,IAAI,EAAElD,MADP;QAECmD,IAAI,EAAE,CAACvC,qBAAD;MAFP,CAFkH;IAA/B,CAArD,EAK3B;MAAEsC,IAAI,EAAEvD,EAAE,CAACiF,MAAX;MAAmBb,UAAU,EAAE,CAAC;QAClCb,IAAI,EAAEnD;MAD4B,CAAD;IAA/B,CAL2B,CAAP;EAOlB,CArBxB,EAqB0C;IAAE8E,QAAQ,EAAE,CAAC;MACvC3B,IAAI,EAAEhD;IADiC,CAAD;EAAZ,CArB1C;AAAA;AAyBA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM4E,eAAN,CAAsB;;AAEtBA,eAAe,CAAChC,IAAhB;EAAA,iBAA4GgC,eAA5G;AAAA;;AACAA,eAAe,CAACC,IAAhB,kBA/F4FpF,EA+F5F;EAAA,MAA6GmF,eAA7G;EAAA,eAA6I5D,SAA7I,EAAwJ+C,SAAxJ;EAAA,UAA8KxD,eAA9K,EAA+LC,eAA/L;EAAA,UAA2NQ,SAA3N,EAAsO+C,SAAtO,EAAiPvD,eAAjP;AAAA;AACAoE,eAAe,CAACE,IAAhB,kBAhG4FrF,EAgG5F;EAAA,UAAwIc,eAAxI,EAAyJC,eAAzJ,EAA0KA,eAA1K;AAAA;;AACA;EAAA,mDAjG4Ff,EAiG5F,mBAA2FmF,eAA3F,EAAwH,CAAC;IAC7G5B,IAAI,EAAE/C,QADuG;IAE7GgD,IAAI,EAAE,CAAC;MACC8B,OAAO,EAAE,CAACxE,eAAD,EAAkBC,eAAlB,CADV;MAECwE,OAAO,EAAE,CAAChE,SAAD,EAAY+C,SAAZ,EAAuBvD,eAAvB,CAFV;MAGCyE,YAAY,EAAE,CAACjE,SAAD,EAAY+C,SAAZ;IAHf,CAAD;EAFuG,CAAD,CAAxH;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASA,SAAT,EAAoB/C,SAApB,EAA+B4D,eAA/B"}, "metadata": {}, "sourceType": "module"}