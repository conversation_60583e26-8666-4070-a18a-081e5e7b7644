{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function ignoreElements() {\n  return function ignoreElementsOperatorFunction(source) {\n    return source.lift(new IgnoreElementsOperator());\n  };\n}\n\nclass IgnoreElementsOperator {\n  call(subscriber, source) {\n    return source.subscribe(new IgnoreElementsSubscriber(subscriber));\n  }\n\n}\n\nclass IgnoreElementsSubscriber extends Subscriber {\n  _next(unused) {}\n\n}", "map": {"version": 3, "names": ["Subscriber", "ignoreElements", "ignoreElementsOperatorFunction", "source", "lift", "IgnoreElementsOperator", "call", "subscriber", "subscribe", "IgnoreElementsSubscriber", "_next", "unused"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/ignoreElements.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function ignoreElements() {\n    return function ignoreElementsOperatorFunction(source) {\n        return source.lift(new IgnoreElementsOperator());\n    };\n}\nclass IgnoreElementsOperator {\n    call(subscriber, source) {\n        return source.subscribe(new IgnoreElementsSubscriber(subscriber));\n    }\n}\nclass IgnoreElementsSubscriber extends Subscriber {\n    _next(unused) {\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,cAAT,GAA0B;EAC7B,OAAO,SAASC,8BAAT,CAAwCC,MAAxC,EAAgD;IACnD,OAAOA,MAAM,CAACC,IAAP,CAAY,IAAIC,sBAAJ,EAAZ,CAAP;EACH,CAFD;AAGH;;AACD,MAAMA,sBAAN,CAA6B;EACzBC,IAAI,CAACC,UAAD,EAAaJ,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACK,SAAP,CAAiB,IAAIC,wBAAJ,CAA6BF,UAA7B,CAAjB,CAAP;EACH;;AAHwB;;AAK7B,MAAME,wBAAN,SAAuCT,UAAvC,CAAkD;EAC9CU,KAAK,CAACC,MAAD,EAAS,CACb;;AAF6C"}, "metadata": {}, "sourceType": "module"}