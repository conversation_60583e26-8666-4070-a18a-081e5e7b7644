{"ast": null, "code": ";\n\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /** @preserve\n   * Counter block mode compatible with  Dr <PERSON> fileenc.c\n   * derived from CryptoJS.mode.CTR\n   * <NAME_EMAIL>\n   */\n  CryptoJS.mode.CTRGladman = function () {\n    var CTRGladman = CryptoJS.lib.BlockCipherMode.extend();\n\n    function incWord(word) {\n      if ((word >> 24 & 0xff) === 0xff) {\n        //overflow\n        var b1 = word >> 16 & 0xff;\n        var b2 = word >> 8 & 0xff;\n        var b3 = word & 0xff;\n\n        if (b1 === 0xff) // overflow b1\n          {\n            b1 = 0;\n\n            if (b2 === 0xff) {\n              b2 = 0;\n\n              if (b3 === 0xff) {\n                b3 = 0;\n              } else {\n                ++b3;\n              }\n            } else {\n              ++b2;\n            }\n          } else {\n          ++b1;\n        }\n\n        word = 0;\n        word += b1 << 16;\n        word += b2 << 8;\n        word += b3;\n      } else {\n        word += 0x01 << 24;\n      }\n\n      return word;\n    }\n\n    function incCounter(counter) {\n      if ((counter[0] = incWord(counter[0])) === 0) {\n        // encr_data in fileenc.c from  Dr Brian Gladman's counts only with DWORD j < 8\n        counter[1] = incWord(counter[1]);\n      }\n\n      return counter;\n    }\n\n    var Encryptor = CTRGladman.Encryptor = CTRGladman.extend({\n      processBlock: function (words, offset) {\n        // Shortcuts\n        var cipher = this._cipher;\n        var blockSize = cipher.blockSize;\n        var iv = this._iv;\n        var counter = this._counter; // Generate keystream\n\n        if (iv) {\n          counter = this._counter = iv.slice(0); // Remove IV for subsequent blocks\n\n          this._iv = undefined;\n        }\n\n        incCounter(counter);\n        var keystream = counter.slice(0);\n        cipher.encryptBlock(keystream, 0); // Encrypt\n\n        for (var i = 0; i < blockSize; i++) {\n          words[offset + i] ^= keystream[i];\n        }\n      }\n    });\n    CTRGladman.Decryptor = Encryptor;\n    return CTRGladman;\n  }();\n\n  return CryptoJS.mode.CTRGladman;\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "mode", "CTRGladman", "lib", "BlockCipherMode", "extend", "incWord", "word", "b1", "b2", "b3", "incCounter", "counter", "Encryptor", "processBlock", "words", "offset", "cipher", "_cipher", "blockSize", "iv", "_iv", "_counter", "slice", "undefined", "keystream", "encryptBlock", "i", "Decryptor"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/crypto-js/mode-ctr-gladman.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./cipher-core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t/** @preserve\n\t * Counter block mode compatible with  Dr <PERSON> fileenc.c\n\t * derived from CryptoJS.mode.CTR\n\t * <NAME_EMAIL>\n\t */\n\tCryptoJS.mode.CTRGladman = (function () {\n\t    var CTRGladman = CryptoJS.lib.BlockCipherMode.extend();\n\n\t\tfunction incWord(word)\n\t\t{\n\t\t\tif (((word >> 24) & 0xff) === 0xff) { //overflow\n\t\t\tvar b1 = (word >> 16)&0xff;\n\t\t\tvar b2 = (word >> 8)&0xff;\n\t\t\tvar b3 = word & 0xff;\n\n\t\t\tif (b1 === 0xff) // overflow b1\n\t\t\t{\n\t\t\tb1 = 0;\n\t\t\tif (b2 === 0xff)\n\t\t\t{\n\t\t\t\tb2 = 0;\n\t\t\t\tif (b3 === 0xff)\n\t\t\t\t{\n\t\t\t\t\tb3 = 0;\n\t\t\t\t}\n\t\t\t\telse\n\t\t\t\t{\n\t\t\t\t\t++b3;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse\n\t\t\t{\n\t\t\t\t++b2;\n\t\t\t}\n\t\t\t}\n\t\t\telse\n\t\t\t{\n\t\t\t++b1;\n\t\t\t}\n\n\t\t\tword = 0;\n\t\t\tword += (b1 << 16);\n\t\t\tword += (b2 << 8);\n\t\t\tword += b3;\n\t\t\t}\n\t\t\telse\n\t\t\t{\n\t\t\tword += (0x01 << 24);\n\t\t\t}\n\t\t\treturn word;\n\t\t}\n\n\t\tfunction incCounter(counter)\n\t\t{\n\t\t\tif ((counter[0] = incWord(counter[0])) === 0)\n\t\t\t{\n\t\t\t\t// encr_data in fileenc.c from  Dr Brian Gladman's counts only with DWORD j < 8\n\t\t\t\tcounter[1] = incWord(counter[1]);\n\t\t\t}\n\t\t\treturn counter;\n\t\t}\n\n\t    var Encryptor = CTRGladman.Encryptor = CTRGladman.extend({\n\t        processBlock: function (words, offset) {\n\t            // Shortcuts\n\t            var cipher = this._cipher\n\t            var blockSize = cipher.blockSize;\n\t            var iv = this._iv;\n\t            var counter = this._counter;\n\n\t            // Generate keystream\n\t            if (iv) {\n\t                counter = this._counter = iv.slice(0);\n\n\t                // Remove IV for subsequent blocks\n\t                this._iv = undefined;\n\t            }\n\n\t\t\t\tincCounter(counter);\n\n\t\t\t\tvar keystream = counter.slice(0);\n\t            cipher.encryptBlock(keystream, 0);\n\n\t            // Encrypt\n\t            for (var i = 0; i < blockSize; i++) {\n\t                words[offset + i] ^= keystream[i];\n\t            }\n\t        }\n\t    });\n\n\t    CTRGladman.Decryptor = Encryptor;\n\n\t    return CTRGladman;\n\t}());\n\n\n\n\n\treturn CryptoJS.mode.CTRGladman;\n\n}));"], "mappings": "AAAA;;AAAE,WAAUA,IAAV,EAAgBC,OAAhB,EAAyBC,KAAzB,EAAgC;EACjC,IAAI,OAAOC,OAAP,KAAmB,QAAvB,EAAiC;IAChC;IACAC,MAAM,CAACD,OAAP,GAAiBA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAD,CAAR,EAAoBA,OAAO,CAAC,eAAD,CAA3B,CAAlC;EACA,CAHD,MAIK,IAAI,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAA3C,EAAgD;IACpD;IACAD,MAAM,CAAC,CAAC,QAAD,EAAW,eAAX,CAAD,EAA8BL,OAA9B,CAAN;EACA,CAHI,MAIA;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAN,CAAP;EACA;AACD,CAbC,EAaA,IAbA,EAaM,UAAUA,QAAV,EAAoB;EAE3B;AACD;AACA;AACA;AACA;EACCA,QAAQ,CAACC,IAAT,CAAcC,UAAd,GAA4B,YAAY;IACpC,IAAIA,UAAU,GAAGF,QAAQ,CAACG,GAAT,CAAaC,eAAb,CAA6BC,MAA7B,EAAjB;;IAEH,SAASC,OAAT,CAAiBC,IAAjB,EACA;MACC,IAAI,CAAEA,IAAI,IAAI,EAAT,GAAe,IAAhB,MAA0B,IAA9B,EAAoC;QAAE;QACtC,IAAIC,EAAE,GAAID,IAAI,IAAI,EAAT,GAAa,IAAtB;QACA,IAAIE,EAAE,GAAIF,IAAI,IAAI,CAAT,GAAY,IAArB;QACA,IAAIG,EAAE,GAAGH,IAAI,GAAG,IAAhB;;QAEA,IAAIC,EAAE,KAAK,IAAX,EAAiB;UACjB;YACAA,EAAE,GAAG,CAAL;;YACA,IAAIC,EAAE,KAAK,IAAX,EACA;cACCA,EAAE,GAAG,CAAL;;cACA,IAAIC,EAAE,KAAK,IAAX,EACA;gBACCA,EAAE,GAAG,CAAL;cACA,CAHD,MAKA;gBACC,EAAEA,EAAF;cACA;YACD,CAXD,MAaA;cACC,EAAED,EAAF;YACA;UACA,CAnBD,MAqBA;UACA,EAAED,EAAF;QACC;;QAEDD,IAAI,GAAG,CAAP;QACAA,IAAI,IAAKC,EAAE,IAAI,EAAf;QACAD,IAAI,IAAKE,EAAE,IAAI,CAAf;QACAF,IAAI,IAAIG,EAAR;MACC,CAlCD,MAoCA;QACAH,IAAI,IAAK,QAAQ,EAAjB;MACC;;MACD,OAAOA,IAAP;IACA;;IAED,SAASI,UAAT,CAAoBC,OAApB,EACA;MACC,IAAI,CAACA,OAAO,CAAC,CAAD,CAAP,GAAaN,OAAO,CAACM,OAAO,CAAC,CAAD,CAAR,CAArB,MAAuC,CAA3C,EACA;QACC;QACAA,OAAO,CAAC,CAAD,CAAP,GAAaN,OAAO,CAACM,OAAO,CAAC,CAAD,CAAR,CAApB;MACA;;MACD,OAAOA,OAAP;IACA;;IAEE,IAAIC,SAAS,GAAGX,UAAU,CAACW,SAAX,GAAuBX,UAAU,CAACG,MAAX,CAAkB;MACrDS,YAAY,EAAE,UAAUC,KAAV,EAAiBC,MAAjB,EAAyB;QACnC;QACA,IAAIC,MAAM,GAAG,KAAKC,OAAlB;QACA,IAAIC,SAAS,GAAGF,MAAM,CAACE,SAAvB;QACA,IAAIC,EAAE,GAAG,KAAKC,GAAd;QACA,IAAIT,OAAO,GAAG,KAAKU,QAAnB,CALmC,CAOnC;;QACA,IAAIF,EAAJ,EAAQ;UACJR,OAAO,GAAG,KAAKU,QAAL,GAAgBF,EAAE,CAACG,KAAH,CAAS,CAAT,CAA1B,CADI,CAGJ;;UACA,KAAKF,GAAL,GAAWG,SAAX;QACH;;QAEVb,UAAU,CAACC,OAAD,CAAV;QAEA,IAAIa,SAAS,GAAGb,OAAO,CAACW,KAAR,CAAc,CAAd,CAAhB;QACSN,MAAM,CAACS,YAAP,CAAoBD,SAApB,EAA+B,CAA/B,EAlBmC,CAoBnC;;QACA,KAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGR,SAApB,EAA+BQ,CAAC,EAAhC,EAAoC;UAChCZ,KAAK,CAACC,MAAM,GAAGW,CAAV,CAAL,IAAqBF,SAAS,CAACE,CAAD,CAA9B;QACH;MACJ;IAzBoD,CAAlB,CAAvC;IA4BAzB,UAAU,CAAC0B,SAAX,GAAuBf,SAAvB;IAEA,OAAOX,UAAP;EACH,CAxF2B,EAA5B;;EA6FA,OAAOF,QAAQ,CAACC,IAAT,CAAcC,UAArB;AAEA,CAnHC,CAAD"}, "metadata": {}, "sourceType": "script"}