{"ast": null, "code": "import { Observable } from '../Observable';\nimport { from } from './from';\nimport { empty } from './empty';\nexport function defer(observableFactory) {\n  return new Observable(subscriber => {\n    let input;\n\n    try {\n      input = observableFactory();\n    } catch (err) {\n      subscriber.error(err);\n      return undefined;\n    }\n\n    const source = input ? from(input) : empty();\n    return source.subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["Observable", "from", "empty", "defer", "observableFactory", "subscriber", "input", "err", "error", "undefined", "source", "subscribe"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/defer.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { from } from './from';\nimport { empty } from './empty';\nexport function defer(observableFactory) {\n    return new Observable(subscriber => {\n        let input;\n        try {\n            input = observableFactory();\n        }\n        catch (err) {\n            subscriber.error(err);\n            return undefined;\n        }\n        const source = input ? from(input) : empty();\n        return source.subscribe(subscriber);\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,IAAT,QAAqB,QAArB;AACA,SAASC,KAAT,QAAsB,SAAtB;AACA,OAAO,SAASC,KAAT,CAAeC,iBAAf,EAAkC;EACrC,OAAO,IAAIJ,UAAJ,CAAeK,UAAU,IAAI;IAChC,IAAIC,KAAJ;;IACA,IAAI;MACAA,KAAK,GAAGF,iBAAiB,EAAzB;IACH,CAFD,CAGA,OAAOG,GAAP,EAAY;MACRF,UAAU,CAACG,KAAX,CAAiBD,GAAjB;MACA,OAAOE,SAAP;IACH;;IACD,MAAMC,MAAM,GAAGJ,KAAK,GAAGL,IAAI,CAACK,KAAD,CAAP,GAAiBJ,KAAK,EAA1C;IACA,OAAOQ,MAAM,CAACC,SAAP,CAAiBN,UAAjB,CAAP;EACH,CAXM,CAAP;AAYH"}, "metadata": {}, "sourceType": "module"}