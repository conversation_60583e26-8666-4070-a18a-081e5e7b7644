{"ast": null, "code": "import { AsyncAction } from './AsyncAction';\nimport { AsyncScheduler } from './AsyncScheduler';\nexport const asyncScheduler = new AsyncScheduler(AsyncAction);\nexport const async = asyncScheduler;", "map": {"version": 3, "names": ["AsyncAction", "AsyncScheduler", "asyncScheduler", "async"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/scheduler/async.js"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nimport { AsyncScheduler } from './AsyncScheduler';\nexport const asyncScheduler = new AsyncScheduler(AsyncAction);\nexport const async = asyncScheduler;\n"], "mappings": "AAAA,SAASA,WAAT,QAA4B,eAA5B;AACA,SAASC,cAAT,QAA+B,kBAA/B;AACA,OAAO,MAAMC,cAAc,GAAG,IAAID,cAAJ,CAAmBD,WAAnB,CAAvB;AACP,OAAO,MAAMG,KAAK,GAAGD,cAAd"}, "metadata": {}, "sourceType": "module"}