{"ast": null, "code": "export function isFunction(x) {\n  return typeof x === 'function';\n}", "map": {"version": 3, "names": ["isFunction", "x"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/isFunction.js"], "sourcesContent": ["export function isFunction(x) {\n    return typeof x === 'function';\n}\n"], "mappings": "AAAA,OAAO,SAASA,UAAT,CAAoBC,CAApB,EAAuB;EAC1B,OAAO,OAAOA,CAAP,KAAa,UAApB;AACH"}, "metadata": {}, "sourceType": "module"}