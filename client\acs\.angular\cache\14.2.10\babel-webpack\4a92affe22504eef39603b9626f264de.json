{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { TimeoutError } from '../util/TimeoutError';\nimport { timeoutWith } from './timeoutWith';\nimport { throwError } from '../observable/throwError';\nexport function timeout(due, scheduler = async) {\n  return timeoutWith(due, throwError(new TimeoutError()), scheduler);\n}", "map": {"version": 3, "names": ["async", "TimeoutError", "timeoutWith", "throwError", "timeout", "due", "scheduler"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/timeout.js"], "sourcesContent": ["import { async } from '../scheduler/async';\nimport { TimeoutError } from '../util/TimeoutError';\nimport { timeoutWith } from './timeoutWith';\nimport { throwError } from '../observable/throwError';\nexport function timeout(due, scheduler = async) {\n    return timeoutWith(due, throwError(new TimeoutError()), scheduler);\n}\n"], "mappings": "AAAA,SAASA,KAAT,QAAsB,oBAAtB;AACA,SAASC,YAAT,QAA6B,sBAA7B;AACA,SAASC,WAAT,QAA4B,eAA5B;AACA,SAASC,UAAT,QAA2B,0BAA3B;AACA,OAAO,SAASC,OAAT,CAAiBC,GAAjB,EAAsBC,SAAS,GAAGN,KAAlC,EAAyC;EAC5C,OAAOE,WAAW,CAACG,GAAD,EAAMF,UAAU,CAAC,IAAIF,YAAJ,EAAD,CAAhB,EAAsCK,SAAtC,CAAlB;AACH"}, "metadata": {}, "sourceType": "module"}