{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { ArgumentOutOfRangeError } from '../util/ArgumentOutOfRangeError';\nexport function skipLast(count) {\n  return source => source.lift(new SkipLastOperator(count));\n}\n\nclass SkipLastOperator {\n  constructor(_skipCount) {\n    this._skipCount = _skipCount;\n\n    if (this._skipCount < 0) {\n      throw new ArgumentOutOfRangeError();\n    }\n  }\n\n  call(subscriber, source) {\n    if (this._skipCount === 0) {\n      return source.subscribe(new Subscriber(subscriber));\n    } else {\n      return source.subscribe(new SkipLastSubscriber(subscriber, this._skipCount));\n    }\n  }\n\n}\n\nclass SkipLastSubscriber extends Subscriber {\n  constructor(destination, _skipCount) {\n    super(destination);\n    this._skipCount = _skipCount;\n    this._count = 0;\n    this._ring = new Array(_skipCount);\n  }\n\n  _next(value) {\n    const skipCount = this._skipCount;\n    const count = this._count++;\n\n    if (count < skipCount) {\n      this._ring[count] = value;\n    } else {\n      const currentIndex = count % skipCount;\n      const ring = this._ring;\n      const oldValue = ring[currentIndex];\n      ring[currentIndex] = value;\n      this.destination.next(oldValue);\n    }\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "ArgumentOutOfRangeError", "skipLast", "count", "source", "lift", "SkipLastOperator", "constructor", "_skipCount", "call", "subscriber", "subscribe", "SkipLastSubscriber", "destination", "_count", "_ring", "Array", "_next", "value", "skip<PERSON><PERSON>nt", "currentIndex", "ring", "oldValue", "next"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/skipLast.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { ArgumentOutOfRangeError } from '../util/ArgumentOutOfRangeError';\nexport function skipLast(count) {\n    return (source) => source.lift(new SkipLastOperator(count));\n}\nclass SkipLastOperator {\n    constructor(_skipCount) {\n        this._skipCount = _skipCount;\n        if (this._skipCount < 0) {\n            throw new ArgumentOutOfRangeError;\n        }\n    }\n    call(subscriber, source) {\n        if (this._skipCount === 0) {\n            return source.subscribe(new Subscriber(subscriber));\n        }\n        else {\n            return source.subscribe(new SkipLastSubscriber(subscriber, this._skipCount));\n        }\n    }\n}\nclass SkipLastSubscriber extends Subscriber {\n    constructor(destination, _skipCount) {\n        super(destination);\n        this._skipCount = _skipCount;\n        this._count = 0;\n        this._ring = new Array(_skipCount);\n    }\n    _next(value) {\n        const skipCount = this._skipCount;\n        const count = this._count++;\n        if (count < skipCount) {\n            this._ring[count] = value;\n        }\n        else {\n            const currentIndex = count % skipCount;\n            const ring = this._ring;\n            const oldValue = ring[currentIndex];\n            ring[currentIndex] = value;\n            this.destination.next(oldValue);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,uBAAT,QAAwC,iCAAxC;AACA,OAAO,SAASC,QAAT,CAAkBC,KAAlB,EAAyB;EAC5B,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,gBAAJ,CAAqBH,KAArB,CAAZ,CAAnB;AACH;;AACD,MAAMG,gBAAN,CAAuB;EACnBC,WAAW,CAACC,UAAD,EAAa;IACpB,KAAKA,UAAL,GAAkBA,UAAlB;;IACA,IAAI,KAAKA,UAAL,GAAkB,CAAtB,EAAyB;MACrB,MAAM,IAAIP,uBAAJ,EAAN;IACH;EACJ;;EACDQ,IAAI,CAACC,UAAD,EAAaN,MAAb,EAAqB;IACrB,IAAI,KAAKI,UAAL,KAAoB,CAAxB,EAA2B;MACvB,OAAOJ,MAAM,CAACO,SAAP,CAAiB,IAAIX,UAAJ,CAAeU,UAAf,CAAjB,CAAP;IACH,CAFD,MAGK;MACD,OAAON,MAAM,CAACO,SAAP,CAAiB,IAAIC,kBAAJ,CAAuBF,UAAvB,EAAmC,KAAKF,UAAxC,CAAjB,CAAP;IACH;EACJ;;AAdkB;;AAgBvB,MAAMI,kBAAN,SAAiCZ,UAAjC,CAA4C;EACxCO,WAAW,CAACM,WAAD,EAAcL,UAAd,EAA0B;IACjC,MAAMK,WAAN;IACA,KAAKL,UAAL,GAAkBA,UAAlB;IACA,KAAKM,MAAL,GAAc,CAAd;IACA,KAAKC,KAAL,GAAa,IAAIC,KAAJ,CAAUR,UAAV,CAAb;EACH;;EACDS,KAAK,CAACC,KAAD,EAAQ;IACT,MAAMC,SAAS,GAAG,KAAKX,UAAvB;IACA,MAAML,KAAK,GAAG,KAAKW,MAAL,EAAd;;IACA,IAAIX,KAAK,GAAGgB,SAAZ,EAAuB;MACnB,KAAKJ,KAAL,CAAWZ,KAAX,IAAoBe,KAApB;IACH,CAFD,MAGK;MACD,MAAME,YAAY,GAAGjB,KAAK,GAAGgB,SAA7B;MACA,MAAME,IAAI,GAAG,KAAKN,KAAlB;MACA,MAAMO,QAAQ,GAAGD,IAAI,CAACD,YAAD,CAArB;MACAC,IAAI,CAACD,YAAD,CAAJ,GAAqBF,KAArB;MACA,KAAKL,WAAL,CAAiBU,IAAjB,CAAsBD,QAAtB;IACH;EACJ;;AApBuC"}, "metadata": {}, "sourceType": "module"}