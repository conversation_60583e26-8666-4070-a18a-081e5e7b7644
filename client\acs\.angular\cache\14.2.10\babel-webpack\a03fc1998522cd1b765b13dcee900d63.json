{"ast": null, "code": "import { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function buffer(closingNotifier) {\n  return function bufferOperatorFunction(source) {\n    return source.lift(new BufferOperator(closingNotifier));\n  };\n}\n\nclass BufferOperator {\n  constructor(closingNotifier) {\n    this.closingNotifier = closingNotifier;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new BufferSubscriber(subscriber, this.closingNotifier));\n  }\n\n}\n\nclass BufferSubscriber extends SimpleOuterSubscriber {\n  constructor(destination, closingNotifier) {\n    super(destination);\n    this.buffer = [];\n    this.add(innerSubscribe(closingNotifier, new SimpleInnerSubscriber(this)));\n  }\n\n  _next(value) {\n    this.buffer.push(value);\n  }\n\n  notifyNext() {\n    const buffer = this.buffer;\n    this.buffer = [];\n    this.destination.next(buffer);\n  }\n\n}", "map": {"version": 3, "names": ["SimpleOuterSubscriber", "innerSubscribe", "SimpleInnerSubscriber", "buffer", "closingNotifier", "bufferOperatorFunction", "source", "lift", "BufferOperator", "constructor", "call", "subscriber", "subscribe", "BufferSubscriber", "destination", "add", "_next", "value", "push", "notifyNext", "next"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/buffer.js"], "sourcesContent": ["import { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function buffer(closingNotifier) {\n    return function bufferOperatorFunction(source) {\n        return source.lift(new BufferOperator(closingNotifier));\n    };\n}\nclass BufferOperator {\n    constructor(closingNotifier) {\n        this.closingNotifier = closingNotifier;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new BufferSubscriber(subscriber, this.closingNotifier));\n    }\n}\nclass BufferSubscriber extends SimpleOuterSubscriber {\n    constructor(destination, closingNotifier) {\n        super(destination);\n        this.buffer = [];\n        this.add(innerSubscribe(closingNotifier, new SimpleInnerSubscriber(this)));\n    }\n    _next(value) {\n        this.buffer.push(value);\n    }\n    notifyNext() {\n        const buffer = this.buffer;\n        this.buffer = [];\n        this.destination.next(buffer);\n    }\n}\n"], "mappings": "AAAA,SAASA,qBAAT,EAAgCC,cAAhC,EAAgDC,qBAAhD,QAA6E,mBAA7E;AACA,OAAO,SAASC,MAAT,CAAgBC,eAAhB,EAAiC;EACpC,OAAO,SAASC,sBAAT,CAAgCC,MAAhC,EAAwC;IAC3C,OAAOA,MAAM,CAACC,IAAP,CAAY,IAAIC,cAAJ,CAAmBJ,eAAnB,CAAZ,CAAP;EACH,CAFD;AAGH;;AACD,MAAMI,cAAN,CAAqB;EACjBC,WAAW,CAACL,eAAD,EAAkB;IACzB,KAAKA,eAAL,GAAuBA,eAAvB;EACH;;EACDM,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,gBAAJ,CAAqBF,UAArB,EAAiC,KAAKP,eAAtC,CAAjB,CAAP;EACH;;AANgB;;AAQrB,MAAMS,gBAAN,SAA+Bb,qBAA/B,CAAqD;EACjDS,WAAW,CAACK,WAAD,EAAcV,eAAd,EAA+B;IACtC,MAAMU,WAAN;IACA,KAAKX,MAAL,GAAc,EAAd;IACA,KAAKY,GAAL,CAASd,cAAc,CAACG,eAAD,EAAkB,IAAIF,qBAAJ,CAA0B,IAA1B,CAAlB,CAAvB;EACH;;EACDc,KAAK,CAACC,KAAD,EAAQ;IACT,KAAKd,MAAL,CAAYe,IAAZ,CAAiBD,KAAjB;EACH;;EACDE,UAAU,GAAG;IACT,MAAMhB,MAAM,GAAG,KAAKA,MAApB;IACA,KAAKA,MAAL,GAAc,EAAd;IACA,KAAKW,WAAL,CAAiBM,IAAjB,CAAsBjB,MAAtB;EACH;;AAbgD"}, "metadata": {}, "sourceType": "module"}