{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { empty } from '../observable/empty';\nexport function repeat(count = -1) {\n  return source => {\n    if (count === 0) {\n      return empty();\n    } else if (count < 0) {\n      return source.lift(new RepeatOperator(-1, source));\n    } else {\n      return source.lift(new RepeatOperator(count - 1, source));\n    }\n  };\n}\n\nclass RepeatOperator {\n  constructor(count, source) {\n    this.count = count;\n    this.source = source;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new RepeatSubscriber(subscriber, this.count, this.source));\n  }\n\n}\n\nclass RepeatSubscriber extends Subscriber {\n  constructor(destination, count, source) {\n    super(destination);\n    this.count = count;\n    this.source = source;\n  }\n\n  complete() {\n    if (!this.isStopped) {\n      const {\n        source,\n        count\n      } = this;\n\n      if (count === 0) {\n        return super.complete();\n      } else if (count > -1) {\n        this.count = count - 1;\n      }\n\n      source.subscribe(this._unsubscribeAndRecycle());\n    }\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "empty", "repeat", "count", "source", "lift", "RepeatOperator", "constructor", "call", "subscriber", "subscribe", "RepeatSubscriber", "destination", "complete", "isStopped", "_unsubscribeAndRecycle"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/repeat.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { empty } from '../observable/empty';\nexport function repeat(count = -1) {\n    return (source) => {\n        if (count === 0) {\n            return empty();\n        }\n        else if (count < 0) {\n            return source.lift(new RepeatOperator(-1, source));\n        }\n        else {\n            return source.lift(new RepeatOperator(count - 1, source));\n        }\n    };\n}\nclass RepeatOperator {\n    constructor(count, source) {\n        this.count = count;\n        this.source = source;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new RepeatSubscriber(subscriber, this.count, this.source));\n    }\n}\nclass RepeatSubscriber extends Subscriber {\n    constructor(destination, count, source) {\n        super(destination);\n        this.count = count;\n        this.source = source;\n    }\n    complete() {\n        if (!this.isStopped) {\n            const { source, count } = this;\n            if (count === 0) {\n                return super.complete();\n            }\n            else if (count > -1) {\n                this.count = count - 1;\n            }\n            source.subscribe(this._unsubscribeAndRecycle());\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,KAAT,QAAsB,qBAAtB;AACA,OAAO,SAASC,MAAT,CAAgBC,KAAK,GAAG,CAAC,CAAzB,EAA4B;EAC/B,OAAQC,MAAD,IAAY;IACf,IAAID,KAAK,KAAK,CAAd,EAAiB;MACb,OAAOF,KAAK,EAAZ;IACH,CAFD,MAGK,IAAIE,KAAK,GAAG,CAAZ,EAAe;MAChB,OAAOC,MAAM,CAACC,IAAP,CAAY,IAAIC,cAAJ,CAAmB,CAAC,CAApB,EAAuBF,MAAvB,CAAZ,CAAP;IACH,CAFI,MAGA;MACD,OAAOA,MAAM,CAACC,IAAP,CAAY,IAAIC,cAAJ,CAAmBH,KAAK,GAAG,CAA3B,EAA8BC,MAA9B,CAAZ,CAAP;IACH;EACJ,CAVD;AAWH;;AACD,MAAME,cAAN,CAAqB;EACjBC,WAAW,CAACJ,KAAD,EAAQC,MAAR,EAAgB;IACvB,KAAKD,KAAL,GAAaA,KAAb;IACA,KAAKC,MAAL,GAAcA,MAAd;EACH;;EACDI,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,gBAAJ,CAAqBF,UAArB,EAAiC,KAAKN,KAAtC,EAA6C,KAAKC,MAAlD,CAAjB,CAAP;EACH;;AAPgB;;AASrB,MAAMO,gBAAN,SAA+BX,UAA/B,CAA0C;EACtCO,WAAW,CAACK,WAAD,EAAcT,KAAd,EAAqBC,MAArB,EAA6B;IACpC,MAAMQ,WAAN;IACA,KAAKT,KAAL,GAAaA,KAAb;IACA,KAAKC,MAAL,GAAcA,MAAd;EACH;;EACDS,QAAQ,GAAG;IACP,IAAI,CAAC,KAAKC,SAAV,EAAqB;MACjB,MAAM;QAAEV,MAAF;QAAUD;MAAV,IAAoB,IAA1B;;MACA,IAAIA,KAAK,KAAK,CAAd,EAAiB;QACb,OAAO,MAAMU,QAAN,EAAP;MACH,CAFD,MAGK,IAAIV,KAAK,GAAG,CAAC,CAAb,EAAgB;QACjB,KAAKA,KAAL,GAAaA,KAAK,GAAG,CAArB;MACH;;MACDC,MAAM,CAACM,SAAP,CAAiB,KAAKK,sBAAL,EAAjB;IACH;EACJ;;AAjBqC"}, "metadata": {}, "sourceType": "module"}