{"ast": null, "code": "import * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Component, ViewEncapsulation, ChangeDetectionStrategy, Directive, Optional, Inject, ContentChildren, ContentChild, Input, forwardRef, EventEmitter, Output, ViewChild, NgModule } from '@angular/core';\nimport * as i1 from '@angular/material/core';\nimport { mixinDisabled, mixinDisableRipple, setLines, MatLine, MatLineModule, MatRippleModule, MatCommonModule, MatPseudoCheckboxModule } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Subject } from 'rxjs';\nimport { takeUntil, startWith } from 'rxjs/operators';\nimport * as i3 from '@angular/cdk/a11y';\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { hasModifierKey, A, ENTER, SPACE, UP_ARROW, DOWN_ARROW } from '@angular/cdk/keycodes';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { MatDividerModule } from '@angular/material/divider';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatList.\n\n/** @docs-private */\n\nconst _c0 = [\"*\"];\nconst _c1 = \".mat-subheader{display:flex;box-sizing:border-box;padding:16px;align-items:center}.mat-list-base .mat-subheader{margin:0}button.mat-list-item,button.mat-list-option{padding:0;width:100%;background:none;color:inherit;border:none;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:left}[dir=rtl] button.mat-list-item,[dir=rtl] button.mat-list-option{text-align:right}button.mat-list-item::-moz-focus-inner,button.mat-list-option::-moz-focus-inner{border:0}.mat-list-base{padding-top:8px;display:block;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-list-base .mat-subheader{height:48px;line-height:16px}.mat-list-base .mat-subheader:first-child{margin-top:-8px}.mat-list-base .mat-list-item,.mat-list-base .mat-list-option{display:block;height:48px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base .mat-list-item .mat-list-item-content,.mat-list-base .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base .mat-list-item .mat-list-item-content-reverse,.mat-list-base .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base .mat-list-item .mat-list-item-ripple,.mat-list-base .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar,.mat-list-base .mat-list-option.mat-list-item-with-avatar{height:56px}.mat-list-base .mat-list-item.mat-2-line,.mat-list-base .mat-list-option.mat-2-line{height:72px}.mat-list-base .mat-list-item.mat-3-line,.mat-list-base .mat-list-option.mat-3-line{height:88px}.mat-list-base .mat-list-item.mat-multi-line,.mat-list-base .mat-list-option.mat-multi-line{height:auto}.mat-list-base .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base .mat-list-item .mat-list-text,.mat-list-base .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base .mat-list-item .mat-list-text>*,.mat-list-base .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base .mat-list-item .mat-list-text:empty,.mat-list-base .mat-list-option .mat-list-text:empty{display:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base .mat-list-item .mat-list-avatar,.mat-list-base .mat-list-option .mat-list-avatar{flex-shrink:0;width:40px;height:40px;border-radius:50%;object-fit:cover}.mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:72px;width:calc(100% - 72px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:72px}.mat-list-base .mat-list-item .mat-list-icon,.mat-list-base .mat-list-option .mat-list-icon{flex-shrink:0;width:24px;height:24px;font-size:24px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:64px;width:calc(100% - 64px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:64px}.mat-list-base .mat-list-item .mat-divider,.mat-list-base .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base .mat-list-item .mat-divider,[dir=rtl] .mat-list-base .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-list-base[dense]{padding-top:4px;display:block}.mat-list-base[dense] .mat-subheader{height:40px;line-height:8px}.mat-list-base[dense] .mat-subheader:first-child{margin-top:-4px}.mat-list-base[dense] .mat-list-item,.mat-list-base[dense] .mat-list-option{display:block;height:40px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-item-content,.mat-list-base[dense] .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base[dense] .mat-list-item .mat-list-item-content-reverse,.mat-list-base[dense] .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base[dense] .mat-list-item .mat-list-item-ripple,.mat-list-base[dense] .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar{height:48px}.mat-list-base[dense] .mat-list-item.mat-2-line,.mat-list-base[dense] .mat-list-option.mat-2-line{height:60px}.mat-list-base[dense] .mat-list-item.mat-3-line,.mat-list-base[dense] .mat-list-option.mat-3-line{height:76px}.mat-list-base[dense] .mat-list-item.mat-multi-line,.mat-list-base[dense] .mat-list-option.mat-multi-line{height:auto}.mat-list-base[dense] .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base[dense] .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base[dense] .mat-list-item .mat-list-text,.mat-list-base[dense] .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-text>*,.mat-list-base[dense] .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base[dense] .mat-list-item .mat-list-text:empty,.mat-list-base[dense] .mat-list-option .mat-list-text:empty{display:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base[dense] .mat-list-item .mat-list-avatar,.mat-list-base[dense] .mat-list-option .mat-list-avatar{flex-shrink:0;width:36px;height:36px;border-radius:50%;object-fit:cover}.mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:68px;width:calc(100% - 68px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:68px}.mat-list-base[dense] .mat-list-item .mat-list-icon,.mat-list-base[dense] .mat-list-option .mat-list-icon{flex-shrink:0;width:20px;height:20px;font-size:20px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:60px;width:calc(100% - 60px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:60px}.mat-list-base[dense] .mat-list-item .mat-divider,.mat-list-base[dense] .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-divider,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base[dense] .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-nav-list a{text-decoration:none;color:inherit}.mat-nav-list .mat-list-item{cursor:pointer;outline:none}mat-action-list .mat-list-item{cursor:pointer;outline:inherit}.mat-list-option:not(.mat-list-item-disabled){cursor:pointer;outline:none}.mat-list-item-disabled{pointer-events:none}.cdk-high-contrast-active .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active :host .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active .mat-list-option:hover,.cdk-high-contrast-active .mat-nav-list .mat-list-item:hover,.cdk-high-contrast-active mat-action-list .mat-list-item:hover{outline:dotted 1px;z-index:1}.cdk-high-contrast-active .mat-list-single-selected-option::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}.cdk-high-contrast-active [dir=rtl] .mat-list-single-selected-option::after{right:auto;left:16px}@media(hover: none){.mat-list-option:not(.mat-list-single-selected-option):not(.mat-list-item-disabled):hover,.mat-nav-list .mat-list-item:not(.mat-list-item-disabled):hover,.mat-action-list .mat-list-item:not(.mat-list-item-disabled):hover{background:none}}\";\nconst _c2 = [[[\"\", \"mat-list-avatar\", \"\"], [\"\", \"mat-list-icon\", \"\"], [\"\", \"matListAvatar\", \"\"], [\"\", \"matListIcon\", \"\"]], [[\"\", \"mat-line\", \"\"], [\"\", \"matLine\", \"\"]], \"*\"];\nconst _c3 = [\"[mat-list-avatar], [mat-list-icon], [matListAvatar], [matListIcon]\", \"[mat-line], [matLine]\", \"*\"];\nconst _c4 = [\"text\"];\n\nfunction MatListOption_mat_pseudo_checkbox_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 5);\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"state\", ctx_r0.selected ? \"checked\" : \"unchecked\")(\"disabled\", ctx_r0.disabled);\n  }\n}\n\nconst _c5 = [\"*\", [[\"\", \"mat-list-avatar\", \"\"], [\"\", \"mat-list-icon\", \"\"], [\"\", \"matListAvatar\", \"\"], [\"\", \"matListIcon\", \"\"]]];\nconst _c6 = [\"*\", \"[mat-list-avatar], [mat-list-icon], [matListAvatar], [matListIcon]\"];\n\nconst _MatListBase = mixinDisabled(mixinDisableRipple(class {})); // Boilerplate for applying mixins to MatListItem.\n\n/** @docs-private */\n\n\nconst _MatListItemMixinBase = mixinDisableRipple(class {});\n/**\n * Injection token that can be used to inject instances of `MatList`. It serves as\n * alternative token to the actual `MatList` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\n\n\nconst MAT_LIST = new InjectionToken('MatList');\n/**\n * Injection token that can be used to inject instances of `MatNavList`. It serves as\n * alternative token to the actual `MatNavList` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\n\nconst MAT_NAV_LIST = new InjectionToken('MatNavList');\n\nclass MatNavList extends _MatListBase {\n  constructor() {\n    super(...arguments);\n    /** Emits when the state of the list changes. */\n\n    this._stateChanges = new Subject();\n  }\n\n  ngOnChanges() {\n    this._stateChanges.next();\n  }\n\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n\n}\n\nMatNavList.ɵfac = /* @__PURE__ */function () {\n  let ɵMatNavList_BaseFactory;\n  return function MatNavList_Factory(t) {\n    return (ɵMatNavList_BaseFactory || (ɵMatNavList_BaseFactory = i0.ɵɵgetInheritedFactory(MatNavList)))(t || MatNavList);\n  };\n}();\n\nMatNavList.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatNavList,\n  selectors: [[\"mat-nav-list\"]],\n  hostAttrs: [\"role\", \"navigation\", 1, \"mat-nav-list\", \"mat-list-base\"],\n  inputs: {\n    disableRipple: \"disableRipple\",\n    disabled: \"disabled\"\n  },\n  exportAs: [\"matNavList\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_NAV_LIST,\n    useExisting: MatNavList\n  }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function MatNavList_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  styles: [\".mat-subheader{display:flex;box-sizing:border-box;padding:16px;align-items:center}.mat-list-base .mat-subheader{margin:0}button.mat-list-item,button.mat-list-option{padding:0;width:100%;background:none;color:inherit;border:none;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:left}[dir=rtl] button.mat-list-item,[dir=rtl] button.mat-list-option{text-align:right}button.mat-list-item::-moz-focus-inner,button.mat-list-option::-moz-focus-inner{border:0}.mat-list-base{padding-top:8px;display:block;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-list-base .mat-subheader{height:48px;line-height:16px}.mat-list-base .mat-subheader:first-child{margin-top:-8px}.mat-list-base .mat-list-item,.mat-list-base .mat-list-option{display:block;height:48px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base .mat-list-item .mat-list-item-content,.mat-list-base .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base .mat-list-item .mat-list-item-content-reverse,.mat-list-base .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base .mat-list-item .mat-list-item-ripple,.mat-list-base .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar,.mat-list-base .mat-list-option.mat-list-item-with-avatar{height:56px}.mat-list-base .mat-list-item.mat-2-line,.mat-list-base .mat-list-option.mat-2-line{height:72px}.mat-list-base .mat-list-item.mat-3-line,.mat-list-base .mat-list-option.mat-3-line{height:88px}.mat-list-base .mat-list-item.mat-multi-line,.mat-list-base .mat-list-option.mat-multi-line{height:auto}.mat-list-base .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base .mat-list-item .mat-list-text,.mat-list-base .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base .mat-list-item .mat-list-text>*,.mat-list-base .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base .mat-list-item .mat-list-text:empty,.mat-list-base .mat-list-option .mat-list-text:empty{display:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base .mat-list-item .mat-list-avatar,.mat-list-base .mat-list-option .mat-list-avatar{flex-shrink:0;width:40px;height:40px;border-radius:50%;object-fit:cover}.mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:72px;width:calc(100% - 72px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:72px}.mat-list-base .mat-list-item .mat-list-icon,.mat-list-base .mat-list-option .mat-list-icon{flex-shrink:0;width:24px;height:24px;font-size:24px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:64px;width:calc(100% - 64px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:64px}.mat-list-base .mat-list-item .mat-divider,.mat-list-base .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base .mat-list-item .mat-divider,[dir=rtl] .mat-list-base .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-list-base[dense]{padding-top:4px;display:block}.mat-list-base[dense] .mat-subheader{height:40px;line-height:8px}.mat-list-base[dense] .mat-subheader:first-child{margin-top:-4px}.mat-list-base[dense] .mat-list-item,.mat-list-base[dense] .mat-list-option{display:block;height:40px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-item-content,.mat-list-base[dense] .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base[dense] .mat-list-item .mat-list-item-content-reverse,.mat-list-base[dense] .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base[dense] .mat-list-item .mat-list-item-ripple,.mat-list-base[dense] .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar{height:48px}.mat-list-base[dense] .mat-list-item.mat-2-line,.mat-list-base[dense] .mat-list-option.mat-2-line{height:60px}.mat-list-base[dense] .mat-list-item.mat-3-line,.mat-list-base[dense] .mat-list-option.mat-3-line{height:76px}.mat-list-base[dense] .mat-list-item.mat-multi-line,.mat-list-base[dense] .mat-list-option.mat-multi-line{height:auto}.mat-list-base[dense] .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base[dense] .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base[dense] .mat-list-item .mat-list-text,.mat-list-base[dense] .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-text>*,.mat-list-base[dense] .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base[dense] .mat-list-item .mat-list-text:empty,.mat-list-base[dense] .mat-list-option .mat-list-text:empty{display:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base[dense] .mat-list-item .mat-list-avatar,.mat-list-base[dense] .mat-list-option .mat-list-avatar{flex-shrink:0;width:36px;height:36px;border-radius:50%;object-fit:cover}.mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:68px;width:calc(100% - 68px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:68px}.mat-list-base[dense] .mat-list-item .mat-list-icon,.mat-list-base[dense] .mat-list-option .mat-list-icon{flex-shrink:0;width:20px;height:20px;font-size:20px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:60px;width:calc(100% - 60px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:60px}.mat-list-base[dense] .mat-list-item .mat-divider,.mat-list-base[dense] .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-divider,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base[dense] .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-nav-list a{text-decoration:none;color:inherit}.mat-nav-list .mat-list-item{cursor:pointer;outline:none}mat-action-list .mat-list-item{cursor:pointer;outline:inherit}.mat-list-option:not(.mat-list-item-disabled){cursor:pointer;outline:none}.mat-list-item-disabled{pointer-events:none}.cdk-high-contrast-active .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active :host .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active .mat-list-option:hover,.cdk-high-contrast-active .mat-nav-list .mat-list-item:hover,.cdk-high-contrast-active mat-action-list .mat-list-item:hover{outline:dotted 1px;z-index:1}.cdk-high-contrast-active .mat-list-single-selected-option::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}.cdk-high-contrast-active [dir=rtl] .mat-list-single-selected-option::after{right:auto;left:16px}@media(hover: none){.mat-list-option:not(.mat-list-single-selected-option):not(.mat-list-item-disabled):hover,.mat-nav-list .mat-list-item:not(.mat-list-item-disabled):hover,.mat-action-list .mat-list-item:not(.mat-list-item-disabled):hover{background:none}}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatNavList, [{\n    type: Component,\n    args: [{\n      selector: 'mat-nav-list',\n      exportAs: 'matNavList',\n      host: {\n        'role': 'navigation',\n        'class': 'mat-nav-list mat-list-base'\n      },\n      inputs: ['disableRipple', 'disabled'],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: MAT_NAV_LIST,\n        useExisting: MatNavList\n      }],\n      template: \"<ng-content></ng-content>\\n\\n\",\n      styles: [\".mat-subheader{display:flex;box-sizing:border-box;padding:16px;align-items:center}.mat-list-base .mat-subheader{margin:0}button.mat-list-item,button.mat-list-option{padding:0;width:100%;background:none;color:inherit;border:none;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:left}[dir=rtl] button.mat-list-item,[dir=rtl] button.mat-list-option{text-align:right}button.mat-list-item::-moz-focus-inner,button.mat-list-option::-moz-focus-inner{border:0}.mat-list-base{padding-top:8px;display:block;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-list-base .mat-subheader{height:48px;line-height:16px}.mat-list-base .mat-subheader:first-child{margin-top:-8px}.mat-list-base .mat-list-item,.mat-list-base .mat-list-option{display:block;height:48px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base .mat-list-item .mat-list-item-content,.mat-list-base .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base .mat-list-item .mat-list-item-content-reverse,.mat-list-base .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base .mat-list-item .mat-list-item-ripple,.mat-list-base .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar,.mat-list-base .mat-list-option.mat-list-item-with-avatar{height:56px}.mat-list-base .mat-list-item.mat-2-line,.mat-list-base .mat-list-option.mat-2-line{height:72px}.mat-list-base .mat-list-item.mat-3-line,.mat-list-base .mat-list-option.mat-3-line{height:88px}.mat-list-base .mat-list-item.mat-multi-line,.mat-list-base .mat-list-option.mat-multi-line{height:auto}.mat-list-base .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base .mat-list-item .mat-list-text,.mat-list-base .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base .mat-list-item .mat-list-text>*,.mat-list-base .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base .mat-list-item .mat-list-text:empty,.mat-list-base .mat-list-option .mat-list-text:empty{display:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base .mat-list-item .mat-list-avatar,.mat-list-base .mat-list-option .mat-list-avatar{flex-shrink:0;width:40px;height:40px;border-radius:50%;object-fit:cover}.mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:72px;width:calc(100% - 72px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:72px}.mat-list-base .mat-list-item .mat-list-icon,.mat-list-base .mat-list-option .mat-list-icon{flex-shrink:0;width:24px;height:24px;font-size:24px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:64px;width:calc(100% - 64px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:64px}.mat-list-base .mat-list-item .mat-divider,.mat-list-base .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base .mat-list-item .mat-divider,[dir=rtl] .mat-list-base .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-list-base[dense]{padding-top:4px;display:block}.mat-list-base[dense] .mat-subheader{height:40px;line-height:8px}.mat-list-base[dense] .mat-subheader:first-child{margin-top:-4px}.mat-list-base[dense] .mat-list-item,.mat-list-base[dense] .mat-list-option{display:block;height:40px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-item-content,.mat-list-base[dense] .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base[dense] .mat-list-item .mat-list-item-content-reverse,.mat-list-base[dense] .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base[dense] .mat-list-item .mat-list-item-ripple,.mat-list-base[dense] .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar{height:48px}.mat-list-base[dense] .mat-list-item.mat-2-line,.mat-list-base[dense] .mat-list-option.mat-2-line{height:60px}.mat-list-base[dense] .mat-list-item.mat-3-line,.mat-list-base[dense] .mat-list-option.mat-3-line{height:76px}.mat-list-base[dense] .mat-list-item.mat-multi-line,.mat-list-base[dense] .mat-list-option.mat-multi-line{height:auto}.mat-list-base[dense] .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base[dense] .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base[dense] .mat-list-item .mat-list-text,.mat-list-base[dense] .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-text>*,.mat-list-base[dense] .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base[dense] .mat-list-item .mat-list-text:empty,.mat-list-base[dense] .mat-list-option .mat-list-text:empty{display:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base[dense] .mat-list-item .mat-list-avatar,.mat-list-base[dense] .mat-list-option .mat-list-avatar{flex-shrink:0;width:36px;height:36px;border-radius:50%;object-fit:cover}.mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:68px;width:calc(100% - 68px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:68px}.mat-list-base[dense] .mat-list-item .mat-list-icon,.mat-list-base[dense] .mat-list-option .mat-list-icon{flex-shrink:0;width:20px;height:20px;font-size:20px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:60px;width:calc(100% - 60px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:60px}.mat-list-base[dense] .mat-list-item .mat-divider,.mat-list-base[dense] .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-divider,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base[dense] .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-nav-list a{text-decoration:none;color:inherit}.mat-nav-list .mat-list-item{cursor:pointer;outline:none}mat-action-list .mat-list-item{cursor:pointer;outline:inherit}.mat-list-option:not(.mat-list-item-disabled){cursor:pointer;outline:none}.mat-list-item-disabled{pointer-events:none}.cdk-high-contrast-active .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active :host .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active .mat-list-option:hover,.cdk-high-contrast-active .mat-nav-list .mat-list-item:hover,.cdk-high-contrast-active mat-action-list .mat-list-item:hover{outline:dotted 1px;z-index:1}.cdk-high-contrast-active .mat-list-single-selected-option::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}.cdk-high-contrast-active [dir=rtl] .mat-list-single-selected-option::after{right:auto;left:16px}@media(hover: none){.mat-list-option:not(.mat-list-single-selected-option):not(.mat-list-item-disabled):hover,.mat-nav-list .mat-list-item:not(.mat-list-item-disabled):hover,.mat-action-list .mat-list-item:not(.mat-list-item-disabled):hover{background:none}}\"]\n    }]\n  }], null, null);\n})();\n\nclass MatList extends _MatListBase {\n  constructor(_elementRef) {\n    super();\n    this._elementRef = _elementRef;\n    /** Emits when the state of the list changes. */\n\n    this._stateChanges = new Subject();\n\n    if (this._getListType() === 'action-list') {\n      _elementRef.nativeElement.classList.add('mat-action-list');\n\n      _elementRef.nativeElement.setAttribute('role', 'group');\n    }\n  }\n\n  _getListType() {\n    const nodeName = this._elementRef.nativeElement.nodeName.toLowerCase();\n\n    if (nodeName === 'mat-list') {\n      return 'list';\n    }\n\n    if (nodeName === 'mat-action-list') {\n      return 'action-list';\n    }\n\n    return null;\n  }\n\n  ngOnChanges() {\n    this._stateChanges.next();\n  }\n\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n\n}\n\nMatList.ɵfac = function MatList_Factory(t) {\n  return new (t || MatList)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\nMatList.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatList,\n  selectors: [[\"mat-list\"], [\"mat-action-list\"]],\n  hostAttrs: [1, \"mat-list\", \"mat-list-base\"],\n  inputs: {\n    disableRipple: \"disableRipple\",\n    disabled: \"disabled\"\n  },\n  exportAs: [\"matList\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_LIST,\n    useExisting: MatList\n  }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function MatList_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  styles: [_c1],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatList, [{\n    type: Component,\n    args: [{\n      selector: 'mat-list, mat-action-list',\n      exportAs: 'matList',\n      host: {\n        'class': 'mat-list mat-list-base'\n      },\n      inputs: ['disableRipple', 'disabled'],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: MAT_LIST,\n        useExisting: MatList\n      }],\n      template: \"<ng-content></ng-content>\\n\\n\",\n      styles: [\".mat-subheader{display:flex;box-sizing:border-box;padding:16px;align-items:center}.mat-list-base .mat-subheader{margin:0}button.mat-list-item,button.mat-list-option{padding:0;width:100%;background:none;color:inherit;border:none;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:left}[dir=rtl] button.mat-list-item,[dir=rtl] button.mat-list-option{text-align:right}button.mat-list-item::-moz-focus-inner,button.mat-list-option::-moz-focus-inner{border:0}.mat-list-base{padding-top:8px;display:block;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-list-base .mat-subheader{height:48px;line-height:16px}.mat-list-base .mat-subheader:first-child{margin-top:-8px}.mat-list-base .mat-list-item,.mat-list-base .mat-list-option{display:block;height:48px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base .mat-list-item .mat-list-item-content,.mat-list-base .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base .mat-list-item .mat-list-item-content-reverse,.mat-list-base .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base .mat-list-item .mat-list-item-ripple,.mat-list-base .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar,.mat-list-base .mat-list-option.mat-list-item-with-avatar{height:56px}.mat-list-base .mat-list-item.mat-2-line,.mat-list-base .mat-list-option.mat-2-line{height:72px}.mat-list-base .mat-list-item.mat-3-line,.mat-list-base .mat-list-option.mat-3-line{height:88px}.mat-list-base .mat-list-item.mat-multi-line,.mat-list-base .mat-list-option.mat-multi-line{height:auto}.mat-list-base .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base .mat-list-item .mat-list-text,.mat-list-base .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base .mat-list-item .mat-list-text>*,.mat-list-base .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base .mat-list-item .mat-list-text:empty,.mat-list-base .mat-list-option .mat-list-text:empty{display:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base .mat-list-item .mat-list-avatar,.mat-list-base .mat-list-option .mat-list-avatar{flex-shrink:0;width:40px;height:40px;border-radius:50%;object-fit:cover}.mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:72px;width:calc(100% - 72px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:72px}.mat-list-base .mat-list-item .mat-list-icon,.mat-list-base .mat-list-option .mat-list-icon{flex-shrink:0;width:24px;height:24px;font-size:24px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:64px;width:calc(100% - 64px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:64px}.mat-list-base .mat-list-item .mat-divider,.mat-list-base .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base .mat-list-item .mat-divider,[dir=rtl] .mat-list-base .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-list-base[dense]{padding-top:4px;display:block}.mat-list-base[dense] .mat-subheader{height:40px;line-height:8px}.mat-list-base[dense] .mat-subheader:first-child{margin-top:-4px}.mat-list-base[dense] .mat-list-item,.mat-list-base[dense] .mat-list-option{display:block;height:40px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-item-content,.mat-list-base[dense] .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base[dense] .mat-list-item .mat-list-item-content-reverse,.mat-list-base[dense] .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base[dense] .mat-list-item .mat-list-item-ripple,.mat-list-base[dense] .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar{height:48px}.mat-list-base[dense] .mat-list-item.mat-2-line,.mat-list-base[dense] .mat-list-option.mat-2-line{height:60px}.mat-list-base[dense] .mat-list-item.mat-3-line,.mat-list-base[dense] .mat-list-option.mat-3-line{height:76px}.mat-list-base[dense] .mat-list-item.mat-multi-line,.mat-list-base[dense] .mat-list-option.mat-multi-line{height:auto}.mat-list-base[dense] .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base[dense] .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base[dense] .mat-list-item .mat-list-text,.mat-list-base[dense] .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-text>*,.mat-list-base[dense] .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base[dense] .mat-list-item .mat-list-text:empty,.mat-list-base[dense] .mat-list-option .mat-list-text:empty{display:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base[dense] .mat-list-item .mat-list-avatar,.mat-list-base[dense] .mat-list-option .mat-list-avatar{flex-shrink:0;width:36px;height:36px;border-radius:50%;object-fit:cover}.mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:68px;width:calc(100% - 68px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:68px}.mat-list-base[dense] .mat-list-item .mat-list-icon,.mat-list-base[dense] .mat-list-option .mat-list-icon{flex-shrink:0;width:20px;height:20px;font-size:20px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:60px;width:calc(100% - 60px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:60px}.mat-list-base[dense] .mat-list-item .mat-divider,.mat-list-base[dense] .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-divider,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base[dense] .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-nav-list a{text-decoration:none;color:inherit}.mat-nav-list .mat-list-item{cursor:pointer;outline:none}mat-action-list .mat-list-item{cursor:pointer;outline:inherit}.mat-list-option:not(.mat-list-item-disabled){cursor:pointer;outline:none}.mat-list-item-disabled{pointer-events:none}.cdk-high-contrast-active .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active :host .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active .mat-list-option:hover,.cdk-high-contrast-active .mat-nav-list .mat-list-item:hover,.cdk-high-contrast-active mat-action-list .mat-list-item:hover{outline:dotted 1px;z-index:1}.cdk-high-contrast-active .mat-list-single-selected-option::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}.cdk-high-contrast-active [dir=rtl] .mat-list-single-selected-option::after{right:auto;left:16px}@media(hover: none){.mat-list-option:not(.mat-list-single-selected-option):not(.mat-list-item-disabled):hover,.mat-nav-list .mat-list-item:not(.mat-list-item-disabled):hover,.mat-action-list .mat-list-item:not(.mat-list-item-disabled):hover{background:none}}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\n\n\nclass MatListAvatarCssMatStyler {}\n\nMatListAvatarCssMatStyler.ɵfac = function MatListAvatarCssMatStyler_Factory(t) {\n  return new (t || MatListAvatarCssMatStyler)();\n};\n\nMatListAvatarCssMatStyler.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatListAvatarCssMatStyler,\n  selectors: [[\"\", \"mat-list-avatar\", \"\"], [\"\", \"matListAvatar\", \"\"]],\n  hostAttrs: [1, \"mat-list-avatar\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatListAvatarCssMatStyler, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-list-avatar], [matListAvatar]',\n      host: {\n        'class': 'mat-list-avatar'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\n\n\nclass MatListIconCssMatStyler {}\n\nMatListIconCssMatStyler.ɵfac = function MatListIconCssMatStyler_Factory(t) {\n  return new (t || MatListIconCssMatStyler)();\n};\n\nMatListIconCssMatStyler.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatListIconCssMatStyler,\n  selectors: [[\"\", \"mat-list-icon\", \"\"], [\"\", \"matListIcon\", \"\"]],\n  hostAttrs: [1, \"mat-list-icon\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatListIconCssMatStyler, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-list-icon], [matListIcon]',\n      host: {\n        'class': 'mat-list-icon'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\n\n\nclass MatListSubheaderCssMatStyler {}\n\nMatListSubheaderCssMatStyler.ɵfac = function MatListSubheaderCssMatStyler_Factory(t) {\n  return new (t || MatListSubheaderCssMatStyler)();\n};\n\nMatListSubheaderCssMatStyler.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatListSubheaderCssMatStyler,\n  selectors: [[\"\", \"mat-subheader\", \"\"], [\"\", \"matSubheader\", \"\"]],\n  hostAttrs: [1, \"mat-subheader\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatListSubheaderCssMatStyler, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-subheader], [matSubheader]',\n      host: {\n        'class': 'mat-subheader'\n      }\n    }]\n  }], null, null);\n})();\n/** An item within a Material Design list. */\n\n\nclass MatListItem extends _MatListItemMixinBase {\n  constructor(_element, _changeDetectorRef, navList, list) {\n    super();\n    this._element = _element;\n    this._isInteractiveList = false;\n    this._destroyed = new Subject();\n    this._disabled = false;\n    this._isInteractiveList = !!(navList || list && list._getListType() === 'action-list');\n    this._list = navList || list; // If no type attribute is specified for <button>, set it to \"button\".\n    // If a type attribute is already specified, do nothing.\n\n    const element = this._getHostElement();\n\n    if (element.nodeName.toLowerCase() === 'button' && !element.hasAttribute('type')) {\n      element.setAttribute('type', 'button');\n    }\n\n    if (this._list) {\n      // React to changes in the state of the parent list since\n      // some of the item's properties depend on it (e.g. `disableRipple`).\n      this._list._stateChanges.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        _changeDetectorRef.markForCheck();\n      });\n    }\n  }\n  /** Whether the option is disabled. */\n\n\n  get disabled() {\n    return this._disabled || !!(this._list && this._list.disabled);\n  }\n\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n  }\n\n  ngAfterContentInit() {\n    setLines(this._lines, this._element);\n  }\n\n  ngOnDestroy() {\n    this._destroyed.next();\n\n    this._destroyed.complete();\n  }\n  /** Whether this list item should show a ripple effect when clicked. */\n\n\n  _isRippleDisabled() {\n    return !this._isInteractiveList || this.disableRipple || !!(this._list && this._list.disableRipple);\n  }\n  /** Retrieves the DOM element of the component host. */\n\n\n  _getHostElement() {\n    return this._element.nativeElement;\n  }\n\n}\n\nMatListItem.ɵfac = function MatListItem_Factory(t) {\n  return new (t || MatListItem)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_NAV_LIST, 8), i0.ɵɵdirectiveInject(MAT_LIST, 8));\n};\n\nMatListItem.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatListItem,\n  selectors: [[\"mat-list-item\"], [\"a\", \"mat-list-item\", \"\"], [\"button\", \"mat-list-item\", \"\"]],\n  contentQueries: function MatListItem_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatListAvatarCssMatStyler, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatListIconCssMatStyler, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatLine, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._avatar = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._icon = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lines = _t);\n    }\n  },\n  hostAttrs: [1, \"mat-list-item\", \"mat-focus-indicator\"],\n  hostVars: 4,\n  hostBindings: function MatListItem_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-list-item-disabled\", ctx.disabled)(\"mat-list-item-with-avatar\", ctx._avatar || ctx._icon);\n    }\n  },\n  inputs: {\n    disableRipple: \"disableRipple\",\n    disabled: \"disabled\"\n  },\n  exportAs: [\"matListItem\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c3,\n  decls: 6,\n  vars: 2,\n  consts: [[1, \"mat-list-item-content\"], [\"mat-ripple\", \"\", 1, \"mat-list-item-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [1, \"mat-list-text\"]],\n  template: function MatListItem_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c2);\n      i0.ɵɵelementStart(0, \"span\", 0);\n      i0.ɵɵelement(1, \"span\", 1);\n      i0.ɵɵprojection(2);\n      i0.ɵɵelementStart(3, \"span\", 2);\n      i0.ɵɵprojection(4, 1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵprojection(5, 2);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"matRippleTrigger\", ctx._getHostElement())(\"matRippleDisabled\", ctx._isRippleDisabled());\n    }\n  },\n  dependencies: [i1.MatRipple],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatListItem, [{\n    type: Component,\n    args: [{\n      selector: 'mat-list-item, a[mat-list-item], button[mat-list-item]',\n      exportAs: 'matListItem',\n      host: {\n        'class': 'mat-list-item mat-focus-indicator',\n        '[class.mat-list-item-disabled]': 'disabled',\n        '[class.mat-list-item-with-avatar]': '_avatar || _icon'\n      },\n      inputs: ['disableRipple'],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<span class=\\\"mat-list-item-content\\\">\\n  <span class=\\\"mat-list-item-ripple\\\" mat-ripple\\n       [matRippleTrigger]=\\\"_getHostElement()\\\"\\n       [matRippleDisabled]=\\\"_isRippleDisabled()\\\">\\n  </span>\\n\\n  <ng-content select=\\\"[mat-list-avatar], [mat-list-icon], [matListAvatar], [matListIcon]\\\">\\n  </ng-content>\\n\\n  <span class=\\\"mat-list-text\\\"><ng-content select=\\\"[mat-line], [matLine]\\\"></ng-content></span>\\n\\n  <ng-content></ng-content>\\n</span>\\n\"\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: MatNavList,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_NAV_LIST]\n      }]\n    }, {\n      type: MatList,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_LIST]\n      }]\n    }];\n  }, {\n    _lines: [{\n      type: ContentChildren,\n      args: [MatLine, {\n        descendants: true\n      }]\n    }],\n    _avatar: [{\n      type: ContentChild,\n      args: [MatListAvatarCssMatStyler]\n    }],\n    _icon: [{\n      type: ContentChild,\n      args: [MatListIconCssMatStyler]\n    }],\n    disabled: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst _MatSelectionListBase = mixinDisableRipple(class {});\n\nconst _MatListOptionBase = mixinDisableRipple(class {});\n/** @docs-private */\n\n\nconst MAT_SELECTION_LIST_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatSelectionList),\n  multi: true\n};\n/** Change event that is being fired whenever the selected state of an option changes. */\n\nclass MatSelectionListChange {\n  constructor(\n  /** Reference to the selection list that emitted the event. */\n  source,\n  /** Reference to the options that have been changed. */\n  options) {\n    this.source = source;\n    this.options = options;\n  }\n\n}\n/**\n * Component for list-options of selection-list. Each list-option can automatically\n * generate a checkbox and can put current item into the selectionModel of selection-list\n * if the current item is selected.\n */\n\n\nclass MatListOption extends _MatListOptionBase {\n  constructor(_element, _changeDetector,\n  /** @docs-private */\n  selectionList) {\n    super();\n    this._element = _element;\n    this._changeDetector = _changeDetector;\n    this.selectionList = selectionList;\n    this._selected = false;\n    this._disabled = false;\n    this._hasFocus = false;\n    /**\n     * Emits when the selected state of the option has changed.\n     * Use to facilitate two-data binding to the `selected` property.\n     * @docs-private\n     */\n\n    this.selectedChange = new EventEmitter();\n    /** Whether the label should appear before or after the checkbox. Defaults to 'after' */\n\n    this.checkboxPosition = 'after';\n    /**\n     * This is set to true after the first OnChanges cycle so we don't clear the value of `selected`\n     * in the first cycle.\n     */\n\n    this._inputsInitialized = false;\n  }\n  /** Theme color of the list option. This sets the color of the checkbox. */\n\n\n  get color() {\n    return this._color || this.selectionList.color;\n  }\n\n  set color(newValue) {\n    this._color = newValue;\n  }\n  /** Value of the option */\n\n\n  get value() {\n    return this._value;\n  }\n\n  set value(newValue) {\n    if (this.selected && !this.selectionList.compareWith(newValue, this.value) && this._inputsInitialized) {\n      this.selected = false;\n    }\n\n    this._value = newValue;\n  }\n  /** Whether the option is disabled. */\n\n\n  get disabled() {\n    return this._disabled || this.selectionList && this.selectionList.disabled;\n  }\n\n  set disabled(value) {\n    const newValue = coerceBooleanProperty(value);\n\n    if (newValue !== this._disabled) {\n      this._disabled = newValue;\n\n      this._changeDetector.markForCheck();\n    }\n  }\n  /** Whether the option is selected. */\n\n\n  get selected() {\n    return this.selectionList.selectedOptions.isSelected(this);\n  }\n\n  set selected(value) {\n    const isSelected = coerceBooleanProperty(value);\n\n    if (isSelected !== this._selected) {\n      this._setSelected(isSelected);\n\n      if (isSelected || this.selectionList.multiple) {\n        this.selectionList._reportValueChange();\n      }\n    }\n  }\n\n  ngOnInit() {\n    const list = this.selectionList;\n\n    if (list._value && list._value.some(value => list.compareWith(this._value, value))) {\n      this._setSelected(true);\n    }\n\n    const wasSelected = this._selected; // List options that are selected at initialization can't be reported properly to the form\n    // control. This is because it takes some time until the selection-list knows about all\n    // available options. Also it can happen that the ControlValueAccessor has an initial value\n    // that should be used instead. Deferring the value change report to the next tick ensures\n    // that the form control value is not being overwritten.\n\n    Promise.resolve().then(() => {\n      if (this._selected || wasSelected) {\n        this.selected = true;\n\n        this._changeDetector.markForCheck();\n      }\n    });\n    this._inputsInitialized = true;\n  }\n\n  ngAfterContentInit() {\n    setLines(this._lines, this._element);\n  }\n\n  ngOnDestroy() {\n    if (this.selected) {\n      // We have to delay this until the next tick in order\n      // to avoid changed after checked errors.\n      Promise.resolve().then(() => {\n        this.selected = false;\n      });\n    }\n\n    const hadFocus = this._hasFocus;\n\n    const newActiveItem = this.selectionList._removeOptionFromList(this); // Only move focus if this option was focused at the time it was destroyed.\n\n\n    if (hadFocus && newActiveItem) {\n      newActiveItem.focus();\n    }\n  }\n  /** Toggles the selection state of the option. */\n\n\n  toggle() {\n    this.selected = !this.selected;\n  }\n  /** Allows for programmatic focusing of the option. */\n\n\n  focus() {\n    this._element.nativeElement.focus();\n  }\n  /**\n   * Returns the list item's text label. Implemented as a part of the FocusKeyManager.\n   * @docs-private\n   */\n\n\n  getLabel() {\n    return this._text ? this._text.nativeElement.textContent || '' : '';\n  }\n  /** Whether this list item should show a ripple effect when clicked. */\n\n\n  _isRippleDisabled() {\n    return this.disabled || this.disableRipple || this.selectionList.disableRipple;\n  }\n\n  _handleClick() {\n    if (!this.disabled && (this.selectionList.multiple || !this.selected)) {\n      this.toggle(); // Emit a change event if the selected state of the option changed through user interaction.\n\n      this.selectionList._emitChangeEvent([this]);\n    }\n  }\n\n  _handleFocus() {\n    this.selectionList._setFocusedOption(this);\n\n    this._hasFocus = true;\n  }\n\n  _handleBlur() {\n    this.selectionList._onTouched();\n\n    this._hasFocus = false;\n  }\n  /** Retrieves the DOM element of the component host. */\n\n\n  _getHostElement() {\n    return this._element.nativeElement;\n  }\n  /** Sets the selected state of the option. Returns whether the value has changed. */\n\n\n  _setSelected(selected) {\n    if (selected === this._selected) {\n      return false;\n    }\n\n    this._selected = selected;\n\n    if (selected) {\n      this.selectionList.selectedOptions.select(this);\n    } else {\n      this.selectionList.selectedOptions.deselect(this);\n    }\n\n    this.selectedChange.emit(selected);\n\n    this._changeDetector.markForCheck();\n\n    return true;\n  }\n  /**\n   * Notifies Angular that the option needs to be checked in the next change detection run. Mainly\n   * used to trigger an update of the list option if the disabled state of the selection list\n   * changed.\n   */\n\n\n  _markForCheck() {\n    this._changeDetector.markForCheck();\n  }\n\n}\n\nMatListOption.ɵfac = function MatListOption_Factory(t) {\n  return new (t || MatListOption)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(forwardRef(() => MatSelectionList)));\n};\n\nMatListOption.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatListOption,\n  selectors: [[\"mat-list-option\"]],\n  contentQueries: function MatListOption_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatListAvatarCssMatStyler, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatListIconCssMatStyler, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatLine, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._avatar = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._icon = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lines = _t);\n    }\n  },\n  viewQuery: function MatListOption_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c4, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._text = _t.first);\n    }\n  },\n  hostAttrs: [\"role\", \"option\", 1, \"mat-list-item\", \"mat-list-option\", \"mat-focus-indicator\"],\n  hostVars: 15,\n  hostBindings: function MatListOption_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"focus\", function MatListOption_focus_HostBindingHandler() {\n        return ctx._handleFocus();\n      })(\"blur\", function MatListOption_blur_HostBindingHandler() {\n        return ctx._handleBlur();\n      })(\"click\", function MatListOption_click_HostBindingHandler() {\n        return ctx._handleClick();\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-selected\", ctx.selected)(\"aria-disabled\", ctx.disabled)(\"tabindex\", -1);\n      i0.ɵɵclassProp(\"mat-list-item-disabled\", ctx.disabled)(\"mat-list-item-with-avatar\", ctx._avatar || ctx._icon)(\"mat-primary\", ctx.color === \"primary\")(\"mat-accent\", ctx.color !== \"primary\" && ctx.color !== \"warn\")(\"mat-warn\", ctx.color === \"warn\")(\"mat-list-single-selected-option\", ctx.selected && !ctx.selectionList.multiple);\n    }\n  },\n  inputs: {\n    disableRipple: \"disableRipple\",\n    checkboxPosition: \"checkboxPosition\",\n    color: \"color\",\n    value: \"value\",\n    disabled: \"disabled\",\n    selected: \"selected\"\n  },\n  outputs: {\n    selectedChange: \"selectedChange\"\n  },\n  exportAs: [\"matListOption\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c6,\n  decls: 7,\n  vars: 5,\n  consts: [[1, \"mat-list-item-content\"], [\"mat-ripple\", \"\", 1, \"mat-list-item-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [3, \"state\", \"disabled\", 4, \"ngIf\"], [1, \"mat-list-text\"], [\"text\", \"\"], [3, \"state\", \"disabled\"]],\n  template: function MatListOption_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c5);\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵelement(1, \"div\", 1);\n      i0.ɵɵtemplate(2, MatListOption_mat_pseudo_checkbox_2_Template, 1, 2, \"mat-pseudo-checkbox\", 2);\n      i0.ɵɵelementStart(3, \"div\", 3, 4);\n      i0.ɵɵprojection(5);\n      i0.ɵɵelementEnd();\n      i0.ɵɵprojection(6, 1);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-list-item-content-reverse\", ctx.checkboxPosition == \"after\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"matRippleTrigger\", ctx._getHostElement())(\"matRippleDisabled\", ctx._isRippleDisabled());\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.selectionList.multiple);\n    }\n  },\n  dependencies: [i1.MatRipple, i1.MatPseudoCheckbox, i2.NgIf],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatListOption, [{\n    type: Component,\n    args: [{\n      selector: 'mat-list-option',\n      exportAs: 'matListOption',\n      inputs: ['disableRipple'],\n      host: {\n        'role': 'option',\n        'class': 'mat-list-item mat-list-option mat-focus-indicator',\n        '(focus)': '_handleFocus()',\n        '(blur)': '_handleBlur()',\n        '(click)': '_handleClick()',\n        '[class.mat-list-item-disabled]': 'disabled',\n        '[class.mat-list-item-with-avatar]': '_avatar || _icon',\n        // Manually set the \"primary\" or \"warn\" class if the color has been explicitly\n        // set to \"primary\" or \"warn\". The pseudo checkbox picks up these classes for\n        // its theme.\n        '[class.mat-primary]': 'color === \"primary\"',\n        // Even though accent is the default, we need to set this class anyway, because the  list might\n        // be placed inside a parent that has one of the other colors with a higher specificity.\n        '[class.mat-accent]': 'color !== \"primary\" && color !== \"warn\"',\n        '[class.mat-warn]': 'color === \"warn\"',\n        '[class.mat-list-single-selected-option]': 'selected && !selectionList.multiple',\n        '[attr.aria-selected]': 'selected',\n        '[attr.aria-disabled]': 'disabled',\n        '[attr.tabindex]': '-1'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<div class=\\\"mat-list-item-content\\\"\\n  [class.mat-list-item-content-reverse]=\\\"checkboxPosition == 'after'\\\">\\n\\n  <div mat-ripple\\n    class=\\\"mat-list-item-ripple\\\"\\n    [matRippleTrigger]=\\\"_getHostElement()\\\"\\n    [matRippleDisabled]=\\\"_isRippleDisabled()\\\"></div>\\n\\n  <mat-pseudo-checkbox\\n    *ngIf=\\\"selectionList.multiple\\\"\\n    [state]=\\\"selected ? 'checked' : 'unchecked'\\\"\\n    [disabled]=\\\"disabled\\\"></mat-pseudo-checkbox>\\n\\n  <div class=\\\"mat-list-text\\\" #text><ng-content></ng-content></div>\\n\\n  <ng-content select=\\\"[mat-list-avatar], [mat-list-icon], [matListAvatar], [matListIcon]\\\">\\n  </ng-content>\\n\\n</div>\\n\"\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: MatSelectionList,\n      decorators: [{\n        type: Inject,\n        args: [forwardRef(() => MatSelectionList)]\n      }]\n    }];\n  }, {\n    _avatar: [{\n      type: ContentChild,\n      args: [MatListAvatarCssMatStyler]\n    }],\n    _icon: [{\n      type: ContentChild,\n      args: [MatListIconCssMatStyler]\n    }],\n    _lines: [{\n      type: ContentChildren,\n      args: [MatLine, {\n        descendants: true\n      }]\n    }],\n    selectedChange: [{\n      type: Output\n    }],\n    _text: [{\n      type: ViewChild,\n      args: ['text']\n    }],\n    checkboxPosition: [{\n      type: Input\n    }],\n    color: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    selected: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Material Design list component where each item is a selectable option. Behaves as a listbox.\n */\n\n\nclass MatSelectionList extends _MatSelectionListBase {\n  constructor(_element, _changeDetector, _focusMonitor) {\n    super();\n    this._element = _element;\n    this._changeDetector = _changeDetector;\n    this._focusMonitor = _focusMonitor;\n    this._multiple = true;\n    this._contentInitialized = false;\n    /** Emits a change event whenever the selected state of an option changes. */\n\n    this.selectionChange = new EventEmitter();\n    /** Theme color of the selection list. This sets the checkbox color for all list options. */\n\n    this.color = 'accent';\n    /**\n     * Function used for comparing an option against the selected value when determining which\n     * options should appear as selected. The first argument is the value of an options. The second\n     * one is a value from the selected value. A boolean must be returned.\n     */\n\n    this.compareWith = (a1, a2) => a1 === a2;\n\n    this._disabled = false;\n    /** The currently selected options. */\n\n    this.selectedOptions = new SelectionModel(this._multiple);\n    /** The tabindex of the selection list. */\n\n    this._tabIndex = -1;\n    /** View to model callback that should be called whenever the selected options change. */\n\n    this._onChange = _ => {};\n    /** Emits when the list has been destroyed. */\n\n\n    this._destroyed = new Subject();\n    /** View to model callback that should be called if the list or its options lost focus. */\n\n    this._onTouched = () => {};\n  }\n  /** Whether the selection list is disabled. */\n\n\n  get disabled() {\n    return this._disabled;\n  }\n\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value); // The `MatSelectionList` and `MatListOption` are using the `OnPush` change detection\n    // strategy. Therefore the options will not check for any changes if the `MatSelectionList`\n    // changed its state. Since we know that a change to `disabled` property of the list affects\n    // the state of the options, we manually mark each option for check.\n\n    this._markOptionsForCheck();\n  }\n  /** Whether selection is limited to one or multiple items (default multiple). */\n\n\n  get multiple() {\n    return this._multiple;\n  }\n\n  set multiple(value) {\n    const newValue = coerceBooleanProperty(value);\n\n    if (newValue !== this._multiple) {\n      if (this._contentInitialized && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw new Error('Cannot change `multiple` mode of mat-selection-list after initialization.');\n      }\n\n      this._multiple = newValue;\n      this.selectedOptions = new SelectionModel(this._multiple, this.selectedOptions.selected);\n    }\n  }\n\n  ngAfterContentInit() {\n    this._contentInitialized = true;\n    this._keyManager = new FocusKeyManager(this.options).withWrap().withTypeAhead().withHomeAndEnd() // Allow disabled items to be focusable. For accessibility reasons, there must be a way for\n    // screen reader users, that allows reading the different options of the list.\n    .skipPredicate(() => false).withAllowedModifierKeys(['shiftKey']);\n\n    if (this._value) {\n      this._setOptionsFromValues(this._value);\n    } // If the user attempts to tab out of the selection list, allow focus to escape.\n\n\n    this._keyManager.tabOut.pipe(takeUntil(this._destroyed)).subscribe(() => {\n      this._allowFocusEscape();\n    }); // When the number of options change, update the tabindex of the selection list.\n\n\n    this.options.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n      this._updateTabIndex();\n    }); // Sync external changes to the model back to the options.\n\n    this.selectedOptions.changed.pipe(takeUntil(this._destroyed)).subscribe(event => {\n      if (event.added) {\n        for (let item of event.added) {\n          item.selected = true;\n        }\n      }\n\n      if (event.removed) {\n        for (let item of event.removed) {\n          item.selected = false;\n        }\n      }\n    });\n\n    this._focusMonitor.monitor(this._element).pipe(takeUntil(this._destroyed)).subscribe(origin => {\n      if (origin === 'keyboard' || origin === 'program') {\n        let toFocus = 0;\n\n        for (let i = 0; i < this.options.length; i++) {\n          if (this.options.get(i)?.selected) {\n            toFocus = i;\n            break;\n          }\n        }\n\n        this._keyManager.setActiveItem(toFocus);\n      }\n    });\n  }\n\n  ngOnChanges(changes) {\n    const disableRippleChanges = changes['disableRipple'];\n    const colorChanges = changes['color'];\n\n    if (disableRippleChanges && !disableRippleChanges.firstChange || colorChanges && !colorChanges.firstChange) {\n      this._markOptionsForCheck();\n    }\n  }\n\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._element);\n\n    this._destroyed.next();\n\n    this._destroyed.complete();\n\n    this._isDestroyed = true;\n  }\n  /** Focuses the selection list. */\n\n\n  focus(options) {\n    this._element.nativeElement.focus(options);\n  }\n  /** Selects all of the options. Returns the options that changed as a result. */\n\n\n  selectAll() {\n    return this._setAllOptionsSelected(true);\n  }\n  /** Deselects all of the options. Returns the options that changed as a result. */\n\n\n  deselectAll() {\n    return this._setAllOptionsSelected(false);\n  }\n  /** Sets the focused option of the selection-list. */\n\n\n  _setFocusedOption(option) {\n    this._keyManager.updateActiveItem(option);\n  }\n  /**\n   * Removes an option from the selection list and updates the active item.\n   * @returns Currently-active item.\n   */\n\n\n  _removeOptionFromList(option) {\n    const optionIndex = this._getOptionIndex(option);\n\n    if (optionIndex > -1 && this._keyManager.activeItemIndex === optionIndex) {\n      // Check whether the option is the last item\n      if (optionIndex > 0) {\n        this._keyManager.updateActiveItem(optionIndex - 1);\n      } else if (optionIndex === 0 && this.options.length > 1) {\n        this._keyManager.updateActiveItem(Math.min(optionIndex + 1, this.options.length - 1));\n      }\n    }\n\n    return this._keyManager.activeItem;\n  }\n  /** Passes relevant key presses to our key manager. */\n\n\n  _keydown(event) {\n    const keyCode = event.keyCode;\n    const manager = this._keyManager;\n    const previousFocusIndex = manager.activeItemIndex;\n    const hasModifier = hasModifierKey(event);\n\n    switch (keyCode) {\n      case SPACE:\n      case ENTER:\n        if (!hasModifier && !manager.isTyping()) {\n          this._toggleFocusedOption(); // Always prevent space from scrolling the page since the list has focus\n\n\n          event.preventDefault();\n        }\n\n        break;\n\n      default:\n        // The \"A\" key gets special treatment, because it's used for the \"select all\" functionality.\n        if (keyCode === A && this.multiple && hasModifierKey(event, 'ctrlKey') && !manager.isTyping()) {\n          const shouldSelect = this.options.some(option => !option.disabled && !option.selected);\n\n          this._setAllOptionsSelected(shouldSelect, true, true);\n\n          event.preventDefault();\n        } else {\n          manager.onKeydown(event);\n        }\n\n    }\n\n    if (this.multiple && (keyCode === UP_ARROW || keyCode === DOWN_ARROW) && event.shiftKey && manager.activeItemIndex !== previousFocusIndex) {\n      this._toggleFocusedOption();\n    }\n  }\n  /** Reports a value change to the ControlValueAccessor */\n\n\n  _reportValueChange() {\n    // Stop reporting value changes after the list has been destroyed. This avoids\n    // cases where the list might wrongly reset its value once it is removed, but\n    // the form control is still live.\n    if (this.options && !this._isDestroyed) {\n      const value = this._getSelectedOptionValues();\n\n      this._onChange(value);\n\n      this._value = value;\n    }\n  }\n  /** Emits a change event if the selected state of an option changed. */\n\n\n  _emitChangeEvent(options) {\n    this.selectionChange.emit(new MatSelectionListChange(this, options));\n  }\n  /** Implemented as part of ControlValueAccessor. */\n\n\n  writeValue(values) {\n    this._value = values;\n\n    if (this.options) {\n      this._setOptionsFromValues(values || []);\n    }\n  }\n  /** Implemented as a part of ControlValueAccessor. */\n\n\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  /** Implemented as part of ControlValueAccessor. */\n\n\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  /** Implemented as part of ControlValueAccessor. */\n\n\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  /** Sets the selected options based on the specified values. */\n\n\n  _setOptionsFromValues(values) {\n    this.options.forEach(option => option._setSelected(false));\n    values.forEach(value => {\n      const correspondingOption = this.options.find(option => {\n        // Skip options that are already in the model. This allows us to handle cases\n        // where the same primitive value is selected multiple times.\n        return option.selected ? false : this.compareWith(option.value, value);\n      });\n\n      if (correspondingOption) {\n        correspondingOption._setSelected(true);\n      }\n    });\n  }\n  /** Returns the values of the selected options. */\n\n\n  _getSelectedOptionValues() {\n    return this.options.filter(option => option.selected).map(option => option.value);\n  }\n  /** Toggles the state of the currently focused option if enabled. */\n\n\n  _toggleFocusedOption() {\n    let focusedIndex = this._keyManager.activeItemIndex;\n\n    if (focusedIndex != null && this._isValidIndex(focusedIndex)) {\n      let focusedOption = this.options.toArray()[focusedIndex];\n\n      if (focusedOption && !focusedOption.disabled && (this._multiple || !focusedOption.selected)) {\n        focusedOption.toggle(); // Emit a change event because the focused option changed its state through user\n        // interaction.\n\n        this._emitChangeEvent([focusedOption]);\n      }\n    }\n  }\n  /**\n   * Sets the selected state on all of the options\n   * and emits an event if anything changed.\n   */\n\n\n  _setAllOptionsSelected(isSelected, skipDisabled, isUserInput) {\n    // Keep track of whether anything changed, because we only want to\n    // emit the changed event when something actually changed.\n    const changedOptions = [];\n    this.options.forEach(option => {\n      if ((!skipDisabled || !option.disabled) && option._setSelected(isSelected)) {\n        changedOptions.push(option);\n      }\n    });\n\n    if (changedOptions.length) {\n      this._reportValueChange();\n\n      if (isUserInput) {\n        this._emitChangeEvent(changedOptions);\n      }\n    }\n\n    return changedOptions;\n  }\n  /**\n   * Utility to ensure all indexes are valid.\n   * @param index The index to be checked.\n   * @returns True if the index is valid for our list of options.\n   */\n\n\n  _isValidIndex(index) {\n    return index >= 0 && index < this.options.length;\n  }\n  /** Returns the index of the specified list option. */\n\n\n  _getOptionIndex(option) {\n    return this.options.toArray().indexOf(option);\n  }\n  /** Marks all the options to be checked in the next change detection run. */\n\n\n  _markOptionsForCheck() {\n    if (this.options) {\n      this.options.forEach(option => option._markForCheck());\n    }\n  }\n  /**\n   * Removes the tabindex from the selection list and resets it back afterwards, allowing the user\n   * to tab out of it. This prevents the list from capturing focus and redirecting it back within\n   * the list, creating a focus trap if it user tries to tab away.\n   */\n\n\n  _allowFocusEscape() {\n    this._tabIndex = -1;\n    setTimeout(() => {\n      this._tabIndex = 0;\n\n      this._changeDetector.markForCheck();\n    });\n  }\n  /** Updates the tabindex based upon if the selection list is empty. */\n\n\n  _updateTabIndex() {\n    this._tabIndex = this.options.length === 0 ? -1 : 0;\n  }\n\n}\n\nMatSelectionList.ɵfac = function MatSelectionList_Factory(t) {\n  return new (t || MatSelectionList)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.FocusMonitor));\n};\n\nMatSelectionList.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatSelectionList,\n  selectors: [[\"mat-selection-list\"]],\n  contentQueries: function MatSelectionList_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatListOption, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.options = _t);\n    }\n  },\n  hostAttrs: [\"role\", \"listbox\", 1, \"mat-selection-list\", \"mat-list-base\"],\n  hostVars: 3,\n  hostBindings: function MatSelectionList_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"keydown\", function MatSelectionList_keydown_HostBindingHandler($event) {\n        return ctx._keydown($event);\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-multiselectable\", ctx.multiple)(\"aria-disabled\", ctx.disabled.toString())(\"tabindex\", ctx._tabIndex);\n    }\n  },\n  inputs: {\n    disableRipple: \"disableRipple\",\n    color: \"color\",\n    compareWith: \"compareWith\",\n    disabled: \"disabled\",\n    multiple: \"multiple\"\n  },\n  outputs: {\n    selectionChange: \"selectionChange\"\n  },\n  exportAs: [\"matSelectionList\"],\n  features: [i0.ɵɵProvidersFeature([MAT_SELECTION_LIST_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function MatSelectionList_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  styles: [_c1],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectionList, [{\n    type: Component,\n    args: [{\n      selector: 'mat-selection-list',\n      exportAs: 'matSelectionList',\n      inputs: ['disableRipple'],\n      host: {\n        'role': 'listbox',\n        'class': 'mat-selection-list mat-list-base',\n        '(keydown)': '_keydown($event)',\n        '[attr.aria-multiselectable]': 'multiple',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '[attr.tabindex]': '_tabIndex'\n      },\n      template: '<ng-content></ng-content>',\n      encapsulation: ViewEncapsulation.None,\n      providers: [MAT_SELECTION_LIST_VALUE_ACCESSOR],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-subheader{display:flex;box-sizing:border-box;padding:16px;align-items:center}.mat-list-base .mat-subheader{margin:0}button.mat-list-item,button.mat-list-option{padding:0;width:100%;background:none;color:inherit;border:none;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:left}[dir=rtl] button.mat-list-item,[dir=rtl] button.mat-list-option{text-align:right}button.mat-list-item::-moz-focus-inner,button.mat-list-option::-moz-focus-inner{border:0}.mat-list-base{padding-top:8px;display:block;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-list-base .mat-subheader{height:48px;line-height:16px}.mat-list-base .mat-subheader:first-child{margin-top:-8px}.mat-list-base .mat-list-item,.mat-list-base .mat-list-option{display:block;height:48px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base .mat-list-item .mat-list-item-content,.mat-list-base .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base .mat-list-item .mat-list-item-content-reverse,.mat-list-base .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base .mat-list-item .mat-list-item-ripple,.mat-list-base .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar,.mat-list-base .mat-list-option.mat-list-item-with-avatar{height:56px}.mat-list-base .mat-list-item.mat-2-line,.mat-list-base .mat-list-option.mat-2-line{height:72px}.mat-list-base .mat-list-item.mat-3-line,.mat-list-base .mat-list-option.mat-3-line{height:88px}.mat-list-base .mat-list-item.mat-multi-line,.mat-list-base .mat-list-option.mat-multi-line{height:auto}.mat-list-base .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base .mat-list-item .mat-list-text,.mat-list-base .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base .mat-list-item .mat-list-text>*,.mat-list-base .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base .mat-list-item .mat-list-text:empty,.mat-list-base .mat-list-option .mat-list-text:empty{display:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base .mat-list-item .mat-list-avatar,.mat-list-base .mat-list-option .mat-list-avatar{flex-shrink:0;width:40px;height:40px;border-radius:50%;object-fit:cover}.mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:72px;width:calc(100% - 72px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:72px}.mat-list-base .mat-list-item .mat-list-icon,.mat-list-base .mat-list-option .mat-list-icon{flex-shrink:0;width:24px;height:24px;font-size:24px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:64px;width:calc(100% - 64px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:64px}.mat-list-base .mat-list-item .mat-divider,.mat-list-base .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base .mat-list-item .mat-divider,[dir=rtl] .mat-list-base .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-list-base[dense]{padding-top:4px;display:block}.mat-list-base[dense] .mat-subheader{height:40px;line-height:8px}.mat-list-base[dense] .mat-subheader:first-child{margin-top:-4px}.mat-list-base[dense] .mat-list-item,.mat-list-base[dense] .mat-list-option{display:block;height:40px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-item-content,.mat-list-base[dense] .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base[dense] .mat-list-item .mat-list-item-content-reverse,.mat-list-base[dense] .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base[dense] .mat-list-item .mat-list-item-ripple,.mat-list-base[dense] .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar{height:48px}.mat-list-base[dense] .mat-list-item.mat-2-line,.mat-list-base[dense] .mat-list-option.mat-2-line{height:60px}.mat-list-base[dense] .mat-list-item.mat-3-line,.mat-list-base[dense] .mat-list-option.mat-3-line{height:76px}.mat-list-base[dense] .mat-list-item.mat-multi-line,.mat-list-base[dense] .mat-list-option.mat-multi-line{height:auto}.mat-list-base[dense] .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base[dense] .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base[dense] .mat-list-item .mat-list-text,.mat-list-base[dense] .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-text>*,.mat-list-base[dense] .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base[dense] .mat-list-item .mat-list-text:empty,.mat-list-base[dense] .mat-list-option .mat-list-text:empty{display:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base[dense] .mat-list-item .mat-list-avatar,.mat-list-base[dense] .mat-list-option .mat-list-avatar{flex-shrink:0;width:36px;height:36px;border-radius:50%;object-fit:cover}.mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:68px;width:calc(100% - 68px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:68px}.mat-list-base[dense] .mat-list-item .mat-list-icon,.mat-list-base[dense] .mat-list-option .mat-list-icon{flex-shrink:0;width:20px;height:20px;font-size:20px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:60px;width:calc(100% - 60px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:60px}.mat-list-base[dense] .mat-list-item .mat-divider,.mat-list-base[dense] .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-divider,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base[dense] .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-nav-list a{text-decoration:none;color:inherit}.mat-nav-list .mat-list-item{cursor:pointer;outline:none}mat-action-list .mat-list-item{cursor:pointer;outline:inherit}.mat-list-option:not(.mat-list-item-disabled){cursor:pointer;outline:none}.mat-list-item-disabled{pointer-events:none}.cdk-high-contrast-active .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active :host .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active .mat-list-option:hover,.cdk-high-contrast-active .mat-nav-list .mat-list-item:hover,.cdk-high-contrast-active mat-action-list .mat-list-item:hover{outline:dotted 1px;z-index:1}.cdk-high-contrast-active .mat-list-single-selected-option::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}.cdk-high-contrast-active [dir=rtl] .mat-list-single-selected-option::after{right:auto;left:16px}@media(hover: none){.mat-list-option:not(.mat-list-single-selected-option):not(.mat-list-item-disabled):hover,.mat-nav-list .mat-list-item:not(.mat-list-item-disabled):hover,.mat-action-list .mat-list-item:not(.mat-list-item-disabled):hover{background:none}}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i3.FocusMonitor\n    }];\n  }, {\n    options: [{\n      type: ContentChildren,\n      args: [MatListOption, {\n        descendants: true\n      }]\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    color: [{\n      type: Input\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    multiple: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatListModule {}\n\nMatListModule.ɵfac = function MatListModule_Factory(t) {\n  return new (t || MatListModule)();\n};\n\nMatListModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatListModule,\n  declarations: [MatList, MatNavList, MatListItem, MatListAvatarCssMatStyler, MatListIconCssMatStyler, MatListSubheaderCssMatStyler, MatSelectionList, MatListOption],\n  imports: [MatLineModule, MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, CommonModule],\n  exports: [MatList, MatNavList, MatListItem, MatListAvatarCssMatStyler, MatLineModule, MatCommonModule, MatListIconCssMatStyler, MatListSubheaderCssMatStyler, MatPseudoCheckboxModule, MatSelectionList, MatListOption, MatDividerModule]\n});\nMatListModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatLineModule, MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, CommonModule, MatLineModule, MatCommonModule, MatPseudoCheckboxModule, MatDividerModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatListModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatLineModule, MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, CommonModule],\n      exports: [MatList, MatNavList, MatListItem, MatListAvatarCssMatStyler, MatLineModule, MatCommonModule, MatListIconCssMatStyler, MatListSubheaderCssMatStyler, MatPseudoCheckboxModule, MatSelectionList, MatListOption, MatDividerModule],\n      declarations: [MatList, MatNavList, MatListItem, MatListAvatarCssMatStyler, MatListIconCssMatStyler, MatListSubheaderCssMatStyler, MatSelectionList, MatListOption]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MAT_LIST, MAT_NAV_LIST, MAT_SELECTION_LIST_VALUE_ACCESSOR, MatList, MatListAvatarCssMatStyler, MatListIconCssMatStyler, MatListItem, MatListModule, MatListOption, MatListSubheaderCssMatStyler, MatNavList, MatSelectionList, MatSelectionListChange };", "map": {"version": 3, "names": ["i2", "CommonModule", "i0", "InjectionToken", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Directive", "Optional", "Inject", "ContentChildren", "ContentChild", "Input", "forwardRef", "EventEmitter", "Output", "ViewChild", "NgModule", "i1", "mixinDisabled", "mixinDisableRipple", "setLines", "MatLine", "MatLineModule", "MatRippleModule", "MatCommonModule", "MatPseudoCheckboxModule", "coerceBooleanProperty", "Subject", "takeUntil", "startWith", "i3", "FocusKeyManager", "SelectionModel", "hasModifierKey", "A", "ENTER", "SPACE", "UP_ARROW", "DOWN_ARROW", "NG_VALUE_ACCESSOR", "MatDividerModule", "_MatListBase", "_MatListItemMixinBase", "MAT_LIST", "MAT_NAV_LIST", "MatNavList", "constructor", "arguments", "_stateChanges", "ngOnChanges", "next", "ngOnDestroy", "complete", "ɵfac", "ɵcmp", "provide", "useExisting", "type", "args", "selector", "exportAs", "host", "inputs", "encapsulation", "None", "changeDetection", "OnPush", "providers", "template", "styles", "MatList", "_elementRef", "_getListType", "nativeElement", "classList", "add", "setAttribute", "nodeName", "toLowerCase", "ElementRef", "MatListAvatarCssMatStyler", "ɵdir", "MatListIconCssMatStyler", "MatListSubheaderCssMatStyler", "MatListItem", "_element", "_changeDetectorRef", "navList", "list", "_isInteractiveList", "_destroyed", "_disabled", "_list", "element", "_getHostElement", "hasAttribute", "pipe", "subscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled", "value", "ngAfterContentInit", "_lines", "_isRippleDisabled", "disable<PERSON><PERSON><PERSON>", "ChangeDetectorRef", "<PERSON><PERSON><PERSON><PERSON>", "decorators", "descendants", "_avatar", "_icon", "_MatSelectionListBase", "_MatListOptionBase", "MAT_SELECTION_LIST_VALUE_ACCESSOR", "MatSelectionList", "multi", "MatSelectionListChange", "source", "options", "MatListOption", "_changeDetector", "selectionList", "_selected", "_hasFocus", "<PERSON><PERSON><PERSON><PERSON>", "checkboxPosition", "_inputsInitialized", "color", "_color", "newValue", "_value", "selected", "compareWith", "selectedOptions", "isSelected", "_setSelected", "multiple", "_reportValueChange", "ngOnInit", "some", "wasSelected", "Promise", "resolve", "then", "hadFocus", "newActiveItem", "_removeOptionFromList", "focus", "toggle", "get<PERSON><PERSON><PERSON>", "_text", "textContent", "_handleClick", "_emitChangeEvent", "_handleFocus", "_setFocusedOption", "_handleBlur", "_onTouched", "select", "deselect", "emit", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "MatPseudoCheckbox", "NgIf", "_focusMonitor", "_multiple", "_contentInitialized", "selectionChange", "a1", "a2", "_tabIndex", "_onChange", "_", "_markOptionsForCheck", "ngDevMode", "Error", "_keyManager", "withWrap", "withTypeAhead", "withHomeAndEnd", "skipPredicate", "withAllowedModifierKeys", "_setOptionsFromValues", "tabOut", "_allowFocusEscape", "changes", "_updateTabIndex", "changed", "event", "added", "item", "removed", "monitor", "origin", "toFocus", "i", "length", "get", "setActiveItem", "disable<PERSON><PERSON>ple<PERSON>hang<PERSON>", "colorChanges", "firstChange", "stopMonitoring", "_isDestroyed", "selectAll", "_setAllOptionsSelected", "deselectAll", "option", "updateActiveItem", "optionIndex", "_getOptionIndex", "activeItemIndex", "Math", "min", "activeItem", "_keydown", "keyCode", "manager", "previousFocusIndex", "hasModifier", "isTyping", "_toggleFocusedOption", "preventDefault", "shouldSelect", "onKeydown", "shift<PERSON>ey", "_getSelectedOptionValues", "writeValue", "values", "setDisabledState", "isDisabled", "registerOnChange", "fn", "registerOnTouched", "for<PERSON>ach", "correspondingOption", "find", "filter", "map", "focusedIndex", "_isValidIndex", "focusedOption", "toArray", "skipDisabled", "isUserInput", "changedOptions", "push", "index", "indexOf", "setTimeout", "FocusMonitor", "MatListModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/material/fesm2020/list.mjs"], "sourcesContent": ["import * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Component, ViewEncapsulation, ChangeDetectionStrategy, Directive, Optional, Inject, ContentChildren, ContentChild, Input, forwardRef, EventEmitter, Output, ViewChild, NgModule } from '@angular/core';\nimport * as i1 from '@angular/material/core';\nimport { mixinDisabled, mixinDisableRipple, setLines, MatLine, MatLineModule, MatRippleModule, MatCommonModule, MatPseudoCheckboxModule } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Subject } from 'rxjs';\nimport { takeUntil, startWith } from 'rxjs/operators';\nimport * as i3 from '@angular/cdk/a11y';\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { hasModifierKey, A, ENTER, SPACE, UP_ARROW, DOWN_ARROW } from '@angular/cdk/keycodes';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { MatDividerModule } from '@angular/material/divider';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatList.\n/** @docs-private */\nconst _MatListBase = mixinDisabled(mixinDisableRipple(class {\n}));\n// Boilerplate for applying mixins to MatListItem.\n/** @docs-private */\nconst _MatListItemMixinBase = mixinDisableRipple(class {\n});\n/**\n * Injection token that can be used to inject instances of `MatList`. It serves as\n * alternative token to the actual `MatList` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_LIST = new InjectionToken('MatList');\n/**\n * Injection token that can be used to inject instances of `MatNavList`. It serves as\n * alternative token to the actual `MatNavList` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_NAV_LIST = new InjectionToken('MatNavList');\nclass MatNavList extends _MatListBase {\n    constructor() {\n        super(...arguments);\n        /** Emits when the state of the list changes. */\n        this._stateChanges = new Subject();\n    }\n    ngOnChanges() {\n        this._stateChanges.next();\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n    }\n}\nMatNavList.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatNavList, deps: null, target: i0.ɵɵFactoryTarget.Component });\nMatNavList.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatNavList, selector: \"mat-nav-list\", inputs: { disableRipple: \"disableRipple\", disabled: \"disabled\" }, host: { attributes: { \"role\": \"navigation\" }, classAttribute: \"mat-nav-list mat-list-base\" }, providers: [{ provide: MAT_NAV_LIST, useExisting: MatNavList }], exportAs: [\"matNavList\"], usesInheritance: true, usesOnChanges: true, ngImport: i0, template: \"<ng-content></ng-content>\\n\\n\", styles: [\".mat-subheader{display:flex;box-sizing:border-box;padding:16px;align-items:center}.mat-list-base .mat-subheader{margin:0}button.mat-list-item,button.mat-list-option{padding:0;width:100%;background:none;color:inherit;border:none;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:left}[dir=rtl] button.mat-list-item,[dir=rtl] button.mat-list-option{text-align:right}button.mat-list-item::-moz-focus-inner,button.mat-list-option::-moz-focus-inner{border:0}.mat-list-base{padding-top:8px;display:block;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-list-base .mat-subheader{height:48px;line-height:16px}.mat-list-base .mat-subheader:first-child{margin-top:-8px}.mat-list-base .mat-list-item,.mat-list-base .mat-list-option{display:block;height:48px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base .mat-list-item .mat-list-item-content,.mat-list-base .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base .mat-list-item .mat-list-item-content-reverse,.mat-list-base .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base .mat-list-item .mat-list-item-ripple,.mat-list-base .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar,.mat-list-base .mat-list-option.mat-list-item-with-avatar{height:56px}.mat-list-base .mat-list-item.mat-2-line,.mat-list-base .mat-list-option.mat-2-line{height:72px}.mat-list-base .mat-list-item.mat-3-line,.mat-list-base .mat-list-option.mat-3-line{height:88px}.mat-list-base .mat-list-item.mat-multi-line,.mat-list-base .mat-list-option.mat-multi-line{height:auto}.mat-list-base .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base .mat-list-item .mat-list-text,.mat-list-base .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base .mat-list-item .mat-list-text>*,.mat-list-base .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base .mat-list-item .mat-list-text:empty,.mat-list-base .mat-list-option .mat-list-text:empty{display:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base .mat-list-item .mat-list-avatar,.mat-list-base .mat-list-option .mat-list-avatar{flex-shrink:0;width:40px;height:40px;border-radius:50%;object-fit:cover}.mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:72px;width:calc(100% - 72px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:72px}.mat-list-base .mat-list-item .mat-list-icon,.mat-list-base .mat-list-option .mat-list-icon{flex-shrink:0;width:24px;height:24px;font-size:24px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:64px;width:calc(100% - 64px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:64px}.mat-list-base .mat-list-item .mat-divider,.mat-list-base .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base .mat-list-item .mat-divider,[dir=rtl] .mat-list-base .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-list-base[dense]{padding-top:4px;display:block}.mat-list-base[dense] .mat-subheader{height:40px;line-height:8px}.mat-list-base[dense] .mat-subheader:first-child{margin-top:-4px}.mat-list-base[dense] .mat-list-item,.mat-list-base[dense] .mat-list-option{display:block;height:40px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-item-content,.mat-list-base[dense] .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base[dense] .mat-list-item .mat-list-item-content-reverse,.mat-list-base[dense] .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base[dense] .mat-list-item .mat-list-item-ripple,.mat-list-base[dense] .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar{height:48px}.mat-list-base[dense] .mat-list-item.mat-2-line,.mat-list-base[dense] .mat-list-option.mat-2-line{height:60px}.mat-list-base[dense] .mat-list-item.mat-3-line,.mat-list-base[dense] .mat-list-option.mat-3-line{height:76px}.mat-list-base[dense] .mat-list-item.mat-multi-line,.mat-list-base[dense] .mat-list-option.mat-multi-line{height:auto}.mat-list-base[dense] .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base[dense] .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base[dense] .mat-list-item .mat-list-text,.mat-list-base[dense] .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-text>*,.mat-list-base[dense] .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base[dense] .mat-list-item .mat-list-text:empty,.mat-list-base[dense] .mat-list-option .mat-list-text:empty{display:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base[dense] .mat-list-item .mat-list-avatar,.mat-list-base[dense] .mat-list-option .mat-list-avatar{flex-shrink:0;width:36px;height:36px;border-radius:50%;object-fit:cover}.mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:68px;width:calc(100% - 68px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:68px}.mat-list-base[dense] .mat-list-item .mat-list-icon,.mat-list-base[dense] .mat-list-option .mat-list-icon{flex-shrink:0;width:20px;height:20px;font-size:20px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:60px;width:calc(100% - 60px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:60px}.mat-list-base[dense] .mat-list-item .mat-divider,.mat-list-base[dense] .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-divider,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base[dense] .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-nav-list a{text-decoration:none;color:inherit}.mat-nav-list .mat-list-item{cursor:pointer;outline:none}mat-action-list .mat-list-item{cursor:pointer;outline:inherit}.mat-list-option:not(.mat-list-item-disabled){cursor:pointer;outline:none}.mat-list-item-disabled{pointer-events:none}.cdk-high-contrast-active .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active :host .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active .mat-list-option:hover,.cdk-high-contrast-active .mat-nav-list .mat-list-item:hover,.cdk-high-contrast-active mat-action-list .mat-list-item:hover{outline:dotted 1px;z-index:1}.cdk-high-contrast-active .mat-list-single-selected-option::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}.cdk-high-contrast-active [dir=rtl] .mat-list-single-selected-option::after{right:auto;left:16px}@media(hover: none){.mat-list-option:not(.mat-list-single-selected-option):not(.mat-list-item-disabled):hover,.mat-nav-list .mat-list-item:not(.mat-list-item-disabled):hover,.mat-action-list .mat-list-item:not(.mat-list-item-disabled):hover{background:none}}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatNavList, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-nav-list', exportAs: 'matNavList', host: {\n                        'role': 'navigation',\n                        'class': 'mat-nav-list mat-list-base',\n                    }, inputs: ['disableRipple', 'disabled'], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [{ provide: MAT_NAV_LIST, useExisting: MatNavList }], template: \"<ng-content></ng-content>\\n\\n\", styles: [\".mat-subheader{display:flex;box-sizing:border-box;padding:16px;align-items:center}.mat-list-base .mat-subheader{margin:0}button.mat-list-item,button.mat-list-option{padding:0;width:100%;background:none;color:inherit;border:none;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:left}[dir=rtl] button.mat-list-item,[dir=rtl] button.mat-list-option{text-align:right}button.mat-list-item::-moz-focus-inner,button.mat-list-option::-moz-focus-inner{border:0}.mat-list-base{padding-top:8px;display:block;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-list-base .mat-subheader{height:48px;line-height:16px}.mat-list-base .mat-subheader:first-child{margin-top:-8px}.mat-list-base .mat-list-item,.mat-list-base .mat-list-option{display:block;height:48px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base .mat-list-item .mat-list-item-content,.mat-list-base .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base .mat-list-item .mat-list-item-content-reverse,.mat-list-base .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base .mat-list-item .mat-list-item-ripple,.mat-list-base .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar,.mat-list-base .mat-list-option.mat-list-item-with-avatar{height:56px}.mat-list-base .mat-list-item.mat-2-line,.mat-list-base .mat-list-option.mat-2-line{height:72px}.mat-list-base .mat-list-item.mat-3-line,.mat-list-base .mat-list-option.mat-3-line{height:88px}.mat-list-base .mat-list-item.mat-multi-line,.mat-list-base .mat-list-option.mat-multi-line{height:auto}.mat-list-base .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base .mat-list-item .mat-list-text,.mat-list-base .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base .mat-list-item .mat-list-text>*,.mat-list-base .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base .mat-list-item .mat-list-text:empty,.mat-list-base .mat-list-option .mat-list-text:empty{display:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base .mat-list-item .mat-list-avatar,.mat-list-base .mat-list-option .mat-list-avatar{flex-shrink:0;width:40px;height:40px;border-radius:50%;object-fit:cover}.mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:72px;width:calc(100% - 72px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:72px}.mat-list-base .mat-list-item .mat-list-icon,.mat-list-base .mat-list-option .mat-list-icon{flex-shrink:0;width:24px;height:24px;font-size:24px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:64px;width:calc(100% - 64px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:64px}.mat-list-base .mat-list-item .mat-divider,.mat-list-base .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base .mat-list-item .mat-divider,[dir=rtl] .mat-list-base .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-list-base[dense]{padding-top:4px;display:block}.mat-list-base[dense] .mat-subheader{height:40px;line-height:8px}.mat-list-base[dense] .mat-subheader:first-child{margin-top:-4px}.mat-list-base[dense] .mat-list-item,.mat-list-base[dense] .mat-list-option{display:block;height:40px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-item-content,.mat-list-base[dense] .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base[dense] .mat-list-item .mat-list-item-content-reverse,.mat-list-base[dense] .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base[dense] .mat-list-item .mat-list-item-ripple,.mat-list-base[dense] .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar{height:48px}.mat-list-base[dense] .mat-list-item.mat-2-line,.mat-list-base[dense] .mat-list-option.mat-2-line{height:60px}.mat-list-base[dense] .mat-list-item.mat-3-line,.mat-list-base[dense] .mat-list-option.mat-3-line{height:76px}.mat-list-base[dense] .mat-list-item.mat-multi-line,.mat-list-base[dense] .mat-list-option.mat-multi-line{height:auto}.mat-list-base[dense] .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base[dense] .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base[dense] .mat-list-item .mat-list-text,.mat-list-base[dense] .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-text>*,.mat-list-base[dense] .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base[dense] .mat-list-item .mat-list-text:empty,.mat-list-base[dense] .mat-list-option .mat-list-text:empty{display:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base[dense] .mat-list-item .mat-list-avatar,.mat-list-base[dense] .mat-list-option .mat-list-avatar{flex-shrink:0;width:36px;height:36px;border-radius:50%;object-fit:cover}.mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:68px;width:calc(100% - 68px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:68px}.mat-list-base[dense] .mat-list-item .mat-list-icon,.mat-list-base[dense] .mat-list-option .mat-list-icon{flex-shrink:0;width:20px;height:20px;font-size:20px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:60px;width:calc(100% - 60px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:60px}.mat-list-base[dense] .mat-list-item .mat-divider,.mat-list-base[dense] .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-divider,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base[dense] .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-nav-list a{text-decoration:none;color:inherit}.mat-nav-list .mat-list-item{cursor:pointer;outline:none}mat-action-list .mat-list-item{cursor:pointer;outline:inherit}.mat-list-option:not(.mat-list-item-disabled){cursor:pointer;outline:none}.mat-list-item-disabled{pointer-events:none}.cdk-high-contrast-active .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active :host .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active .mat-list-option:hover,.cdk-high-contrast-active .mat-nav-list .mat-list-item:hover,.cdk-high-contrast-active mat-action-list .mat-list-item:hover{outline:dotted 1px;z-index:1}.cdk-high-contrast-active .mat-list-single-selected-option::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}.cdk-high-contrast-active [dir=rtl] .mat-list-single-selected-option::after{right:auto;left:16px}@media(hover: none){.mat-list-option:not(.mat-list-single-selected-option):not(.mat-list-item-disabled):hover,.mat-nav-list .mat-list-item:not(.mat-list-item-disabled):hover,.mat-action-list .mat-list-item:not(.mat-list-item-disabled):hover{background:none}}\"] }]\n        }] });\nclass MatList extends _MatListBase {\n    constructor(_elementRef) {\n        super();\n        this._elementRef = _elementRef;\n        /** Emits when the state of the list changes. */\n        this._stateChanges = new Subject();\n        if (this._getListType() === 'action-list') {\n            _elementRef.nativeElement.classList.add('mat-action-list');\n            _elementRef.nativeElement.setAttribute('role', 'group');\n        }\n    }\n    _getListType() {\n        const nodeName = this._elementRef.nativeElement.nodeName.toLowerCase();\n        if (nodeName === 'mat-list') {\n            return 'list';\n        }\n        if (nodeName === 'mat-action-list') {\n            return 'action-list';\n        }\n        return null;\n    }\n    ngOnChanges() {\n        this._stateChanges.next();\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n    }\n}\nMatList.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatList, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Component });\nMatList.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatList, selector: \"mat-list, mat-action-list\", inputs: { disableRipple: \"disableRipple\", disabled: \"disabled\" }, host: { classAttribute: \"mat-list mat-list-base\" }, providers: [{ provide: MAT_LIST, useExisting: MatList }], exportAs: [\"matList\"], usesInheritance: true, usesOnChanges: true, ngImport: i0, template: \"<ng-content></ng-content>\\n\\n\", styles: [\".mat-subheader{display:flex;box-sizing:border-box;padding:16px;align-items:center}.mat-list-base .mat-subheader{margin:0}button.mat-list-item,button.mat-list-option{padding:0;width:100%;background:none;color:inherit;border:none;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:left}[dir=rtl] button.mat-list-item,[dir=rtl] button.mat-list-option{text-align:right}button.mat-list-item::-moz-focus-inner,button.mat-list-option::-moz-focus-inner{border:0}.mat-list-base{padding-top:8px;display:block;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-list-base .mat-subheader{height:48px;line-height:16px}.mat-list-base .mat-subheader:first-child{margin-top:-8px}.mat-list-base .mat-list-item,.mat-list-base .mat-list-option{display:block;height:48px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base .mat-list-item .mat-list-item-content,.mat-list-base .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base .mat-list-item .mat-list-item-content-reverse,.mat-list-base .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base .mat-list-item .mat-list-item-ripple,.mat-list-base .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar,.mat-list-base .mat-list-option.mat-list-item-with-avatar{height:56px}.mat-list-base .mat-list-item.mat-2-line,.mat-list-base .mat-list-option.mat-2-line{height:72px}.mat-list-base .mat-list-item.mat-3-line,.mat-list-base .mat-list-option.mat-3-line{height:88px}.mat-list-base .mat-list-item.mat-multi-line,.mat-list-base .mat-list-option.mat-multi-line{height:auto}.mat-list-base .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base .mat-list-item .mat-list-text,.mat-list-base .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base .mat-list-item .mat-list-text>*,.mat-list-base .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base .mat-list-item .mat-list-text:empty,.mat-list-base .mat-list-option .mat-list-text:empty{display:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base .mat-list-item .mat-list-avatar,.mat-list-base .mat-list-option .mat-list-avatar{flex-shrink:0;width:40px;height:40px;border-radius:50%;object-fit:cover}.mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:72px;width:calc(100% - 72px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:72px}.mat-list-base .mat-list-item .mat-list-icon,.mat-list-base .mat-list-option .mat-list-icon{flex-shrink:0;width:24px;height:24px;font-size:24px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:64px;width:calc(100% - 64px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:64px}.mat-list-base .mat-list-item .mat-divider,.mat-list-base .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base .mat-list-item .mat-divider,[dir=rtl] .mat-list-base .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-list-base[dense]{padding-top:4px;display:block}.mat-list-base[dense] .mat-subheader{height:40px;line-height:8px}.mat-list-base[dense] .mat-subheader:first-child{margin-top:-4px}.mat-list-base[dense] .mat-list-item,.mat-list-base[dense] .mat-list-option{display:block;height:40px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-item-content,.mat-list-base[dense] .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base[dense] .mat-list-item .mat-list-item-content-reverse,.mat-list-base[dense] .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base[dense] .mat-list-item .mat-list-item-ripple,.mat-list-base[dense] .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar{height:48px}.mat-list-base[dense] .mat-list-item.mat-2-line,.mat-list-base[dense] .mat-list-option.mat-2-line{height:60px}.mat-list-base[dense] .mat-list-item.mat-3-line,.mat-list-base[dense] .mat-list-option.mat-3-line{height:76px}.mat-list-base[dense] .mat-list-item.mat-multi-line,.mat-list-base[dense] .mat-list-option.mat-multi-line{height:auto}.mat-list-base[dense] .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base[dense] .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base[dense] .mat-list-item .mat-list-text,.mat-list-base[dense] .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-text>*,.mat-list-base[dense] .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base[dense] .mat-list-item .mat-list-text:empty,.mat-list-base[dense] .mat-list-option .mat-list-text:empty{display:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base[dense] .mat-list-item .mat-list-avatar,.mat-list-base[dense] .mat-list-option .mat-list-avatar{flex-shrink:0;width:36px;height:36px;border-radius:50%;object-fit:cover}.mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:68px;width:calc(100% - 68px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:68px}.mat-list-base[dense] .mat-list-item .mat-list-icon,.mat-list-base[dense] .mat-list-option .mat-list-icon{flex-shrink:0;width:20px;height:20px;font-size:20px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:60px;width:calc(100% - 60px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:60px}.mat-list-base[dense] .mat-list-item .mat-divider,.mat-list-base[dense] .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-divider,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base[dense] .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-nav-list a{text-decoration:none;color:inherit}.mat-nav-list .mat-list-item{cursor:pointer;outline:none}mat-action-list .mat-list-item{cursor:pointer;outline:inherit}.mat-list-option:not(.mat-list-item-disabled){cursor:pointer;outline:none}.mat-list-item-disabled{pointer-events:none}.cdk-high-contrast-active .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active :host .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active .mat-list-option:hover,.cdk-high-contrast-active .mat-nav-list .mat-list-item:hover,.cdk-high-contrast-active mat-action-list .mat-list-item:hover{outline:dotted 1px;z-index:1}.cdk-high-contrast-active .mat-list-single-selected-option::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}.cdk-high-contrast-active [dir=rtl] .mat-list-single-selected-option::after{right:auto;left:16px}@media(hover: none){.mat-list-option:not(.mat-list-single-selected-option):not(.mat-list-item-disabled):hover,.mat-nav-list .mat-list-item:not(.mat-list-item-disabled):hover,.mat-action-list .mat-list-item:not(.mat-list-item-disabled):hover{background:none}}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatList, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-list, mat-action-list', exportAs: 'matList', host: {\n                        'class': 'mat-list mat-list-base',\n                    }, inputs: ['disableRipple', 'disabled'], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [{ provide: MAT_LIST, useExisting: MatList }], template: \"<ng-content></ng-content>\\n\\n\", styles: [\".mat-subheader{display:flex;box-sizing:border-box;padding:16px;align-items:center}.mat-list-base .mat-subheader{margin:0}button.mat-list-item,button.mat-list-option{padding:0;width:100%;background:none;color:inherit;border:none;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:left}[dir=rtl] button.mat-list-item,[dir=rtl] button.mat-list-option{text-align:right}button.mat-list-item::-moz-focus-inner,button.mat-list-option::-moz-focus-inner{border:0}.mat-list-base{padding-top:8px;display:block;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-list-base .mat-subheader{height:48px;line-height:16px}.mat-list-base .mat-subheader:first-child{margin-top:-8px}.mat-list-base .mat-list-item,.mat-list-base .mat-list-option{display:block;height:48px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base .mat-list-item .mat-list-item-content,.mat-list-base .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base .mat-list-item .mat-list-item-content-reverse,.mat-list-base .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base .mat-list-item .mat-list-item-ripple,.mat-list-base .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar,.mat-list-base .mat-list-option.mat-list-item-with-avatar{height:56px}.mat-list-base .mat-list-item.mat-2-line,.mat-list-base .mat-list-option.mat-2-line{height:72px}.mat-list-base .mat-list-item.mat-3-line,.mat-list-base .mat-list-option.mat-3-line{height:88px}.mat-list-base .mat-list-item.mat-multi-line,.mat-list-base .mat-list-option.mat-multi-line{height:auto}.mat-list-base .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base .mat-list-item .mat-list-text,.mat-list-base .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base .mat-list-item .mat-list-text>*,.mat-list-base .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base .mat-list-item .mat-list-text:empty,.mat-list-base .mat-list-option .mat-list-text:empty{display:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base .mat-list-item .mat-list-avatar,.mat-list-base .mat-list-option .mat-list-avatar{flex-shrink:0;width:40px;height:40px;border-radius:50%;object-fit:cover}.mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:72px;width:calc(100% - 72px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:72px}.mat-list-base .mat-list-item .mat-list-icon,.mat-list-base .mat-list-option .mat-list-icon{flex-shrink:0;width:24px;height:24px;font-size:24px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:64px;width:calc(100% - 64px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:64px}.mat-list-base .mat-list-item .mat-divider,.mat-list-base .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base .mat-list-item .mat-divider,[dir=rtl] .mat-list-base .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-list-base[dense]{padding-top:4px;display:block}.mat-list-base[dense] .mat-subheader{height:40px;line-height:8px}.mat-list-base[dense] .mat-subheader:first-child{margin-top:-4px}.mat-list-base[dense] .mat-list-item,.mat-list-base[dense] .mat-list-option{display:block;height:40px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-item-content,.mat-list-base[dense] .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base[dense] .mat-list-item .mat-list-item-content-reverse,.mat-list-base[dense] .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base[dense] .mat-list-item .mat-list-item-ripple,.mat-list-base[dense] .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar{height:48px}.mat-list-base[dense] .mat-list-item.mat-2-line,.mat-list-base[dense] .mat-list-option.mat-2-line{height:60px}.mat-list-base[dense] .mat-list-item.mat-3-line,.mat-list-base[dense] .mat-list-option.mat-3-line{height:76px}.mat-list-base[dense] .mat-list-item.mat-multi-line,.mat-list-base[dense] .mat-list-option.mat-multi-line{height:auto}.mat-list-base[dense] .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base[dense] .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base[dense] .mat-list-item .mat-list-text,.mat-list-base[dense] .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-text>*,.mat-list-base[dense] .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base[dense] .mat-list-item .mat-list-text:empty,.mat-list-base[dense] .mat-list-option .mat-list-text:empty{display:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base[dense] .mat-list-item .mat-list-avatar,.mat-list-base[dense] .mat-list-option .mat-list-avatar{flex-shrink:0;width:36px;height:36px;border-radius:50%;object-fit:cover}.mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:68px;width:calc(100% - 68px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:68px}.mat-list-base[dense] .mat-list-item .mat-list-icon,.mat-list-base[dense] .mat-list-option .mat-list-icon{flex-shrink:0;width:20px;height:20px;font-size:20px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:60px;width:calc(100% - 60px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:60px}.mat-list-base[dense] .mat-list-item .mat-divider,.mat-list-base[dense] .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-divider,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base[dense] .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-nav-list a{text-decoration:none;color:inherit}.mat-nav-list .mat-list-item{cursor:pointer;outline:none}mat-action-list .mat-list-item{cursor:pointer;outline:inherit}.mat-list-option:not(.mat-list-item-disabled){cursor:pointer;outline:none}.mat-list-item-disabled{pointer-events:none}.cdk-high-contrast-active .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active :host .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active .mat-list-option:hover,.cdk-high-contrast-active .mat-nav-list .mat-list-item:hover,.cdk-high-contrast-active mat-action-list .mat-list-item:hover{outline:dotted 1px;z-index:1}.cdk-high-contrast-active .mat-list-single-selected-option::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}.cdk-high-contrast-active [dir=rtl] .mat-list-single-selected-option::after{right:auto;left:16px}@media(hover: none){.mat-list-option:not(.mat-list-single-selected-option):not(.mat-list-item-disabled):hover,.mat-nav-list .mat-list-item:not(.mat-list-item-disabled):hover,.mat-action-list .mat-list-item:not(.mat-list-item-disabled):hover{background:none}}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; } });\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nclass MatListAvatarCssMatStyler {\n}\nMatListAvatarCssMatStyler.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatListAvatarCssMatStyler, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatListAvatarCssMatStyler.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatListAvatarCssMatStyler, selector: \"[mat-list-avatar], [matListAvatar]\", host: { classAttribute: \"mat-list-avatar\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatListAvatarCssMatStyler, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-list-avatar], [matListAvatar]',\n                    host: { 'class': 'mat-list-avatar' },\n                }]\n        }] });\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nclass MatListIconCssMatStyler {\n}\nMatListIconCssMatStyler.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatListIconCssMatStyler, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatListIconCssMatStyler.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatListIconCssMatStyler, selector: \"[mat-list-icon], [matListIcon]\", host: { classAttribute: \"mat-list-icon\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatListIconCssMatStyler, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-list-icon], [matListIcon]',\n                    host: { 'class': 'mat-list-icon' },\n                }]\n        }] });\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nclass MatListSubheaderCssMatStyler {\n}\nMatListSubheaderCssMatStyler.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatListSubheaderCssMatStyler, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatListSubheaderCssMatStyler.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatListSubheaderCssMatStyler, selector: \"[mat-subheader], [matSubheader]\", host: { classAttribute: \"mat-subheader\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatListSubheaderCssMatStyler, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-subheader], [matSubheader]',\n                    host: { 'class': 'mat-subheader' },\n                }]\n        }] });\n/** An item within a Material Design list. */\nclass MatListItem extends _MatListItemMixinBase {\n    constructor(_element, _changeDetectorRef, navList, list) {\n        super();\n        this._element = _element;\n        this._isInteractiveList = false;\n        this._destroyed = new Subject();\n        this._disabled = false;\n        this._isInteractiveList = !!(navList || (list && list._getListType() === 'action-list'));\n        this._list = navList || list;\n        // If no type attribute is specified for <button>, set it to \"button\".\n        // If a type attribute is already specified, do nothing.\n        const element = this._getHostElement();\n        if (element.nodeName.toLowerCase() === 'button' && !element.hasAttribute('type')) {\n            element.setAttribute('type', 'button');\n        }\n        if (this._list) {\n            // React to changes in the state of the parent list since\n            // some of the item's properties depend on it (e.g. `disableRipple`).\n            this._list._stateChanges.pipe(takeUntil(this._destroyed)).subscribe(() => {\n                _changeDetectorRef.markForCheck();\n            });\n        }\n    }\n    /** Whether the option is disabled. */\n    get disabled() {\n        return this._disabled || !!(this._list && this._list.disabled);\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n    }\n    ngAfterContentInit() {\n        setLines(this._lines, this._element);\n    }\n    ngOnDestroy() {\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Whether this list item should show a ripple effect when clicked. */\n    _isRippleDisabled() {\n        return (!this._isInteractiveList || this.disableRipple || !!(this._list && this._list.disableRipple));\n    }\n    /** Retrieves the DOM element of the component host. */\n    _getHostElement() {\n        return this._element.nativeElement;\n    }\n}\nMatListItem.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatListItem, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: MAT_NAV_LIST, optional: true }, { token: MAT_LIST, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatListItem.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatListItem, selector: \"mat-list-item, a[mat-list-item], button[mat-list-item]\", inputs: { disableRipple: \"disableRipple\", disabled: \"disabled\" }, host: { properties: { \"class.mat-list-item-disabled\": \"disabled\", \"class.mat-list-item-with-avatar\": \"_avatar || _icon\" }, classAttribute: \"mat-list-item mat-focus-indicator\" }, queries: [{ propertyName: \"_avatar\", first: true, predicate: MatListAvatarCssMatStyler, descendants: true }, { propertyName: \"_icon\", first: true, predicate: MatListIconCssMatStyler, descendants: true }, { propertyName: \"_lines\", predicate: MatLine, descendants: true }], exportAs: [\"matListItem\"], usesInheritance: true, ngImport: i0, template: \"<span class=\\\"mat-list-item-content\\\">\\n  <span class=\\\"mat-list-item-ripple\\\" mat-ripple\\n       [matRippleTrigger]=\\\"_getHostElement()\\\"\\n       [matRippleDisabled]=\\\"_isRippleDisabled()\\\">\\n  </span>\\n\\n  <ng-content select=\\\"[mat-list-avatar], [mat-list-icon], [matListAvatar], [matListIcon]\\\">\\n  </ng-content>\\n\\n  <span class=\\\"mat-list-text\\\"><ng-content select=\\\"[mat-line], [matLine]\\\"></ng-content></span>\\n\\n  <ng-content></ng-content>\\n</span>\\n\", dependencies: [{ kind: \"directive\", type: i1.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatListItem, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-list-item, a[mat-list-item], button[mat-list-item]', exportAs: 'matListItem', host: {\n                        'class': 'mat-list-item mat-focus-indicator',\n                        '[class.mat-list-item-disabled]': 'disabled',\n                        '[class.mat-list-item-with-avatar]': '_avatar || _icon',\n                    }, inputs: ['disableRipple'], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<span class=\\\"mat-list-item-content\\\">\\n  <span class=\\\"mat-list-item-ripple\\\" mat-ripple\\n       [matRippleTrigger]=\\\"_getHostElement()\\\"\\n       [matRippleDisabled]=\\\"_isRippleDisabled()\\\">\\n  </span>\\n\\n  <ng-content select=\\\"[mat-list-avatar], [mat-list-icon], [matListAvatar], [matListIcon]\\\">\\n  </ng-content>\\n\\n  <span class=\\\"mat-list-text\\\"><ng-content select=\\\"[mat-line], [matLine]\\\"></ng-content></span>\\n\\n  <ng-content></ng-content>\\n</span>\\n\" }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: MatNavList, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_NAV_LIST]\n                }] }, { type: MatList, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_LIST]\n                }] }]; }, propDecorators: { _lines: [{\n                type: ContentChildren,\n                args: [MatLine, { descendants: true }]\n            }], _avatar: [{\n                type: ContentChild,\n                args: [MatListAvatarCssMatStyler]\n            }], _icon: [{\n                type: ContentChild,\n                args: [MatListIconCssMatStyler]\n            }], disabled: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst _MatSelectionListBase = mixinDisableRipple(class {\n});\nconst _MatListOptionBase = mixinDisableRipple(class {\n});\n/** @docs-private */\nconst MAT_SELECTION_LIST_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatSelectionList),\n    multi: true,\n};\n/** Change event that is being fired whenever the selected state of an option changes. */\nclass MatSelectionListChange {\n    constructor(\n    /** Reference to the selection list that emitted the event. */\n    source, \n    /** Reference to the options that have been changed. */\n    options) {\n        this.source = source;\n        this.options = options;\n    }\n}\n/**\n * Component for list-options of selection-list. Each list-option can automatically\n * generate a checkbox and can put current item into the selectionModel of selection-list\n * if the current item is selected.\n */\nclass MatListOption extends _MatListOptionBase {\n    constructor(_element, _changeDetector, \n    /** @docs-private */\n    selectionList) {\n        super();\n        this._element = _element;\n        this._changeDetector = _changeDetector;\n        this.selectionList = selectionList;\n        this._selected = false;\n        this._disabled = false;\n        this._hasFocus = false;\n        /**\n         * Emits when the selected state of the option has changed.\n         * Use to facilitate two-data binding to the `selected` property.\n         * @docs-private\n         */\n        this.selectedChange = new EventEmitter();\n        /** Whether the label should appear before or after the checkbox. Defaults to 'after' */\n        this.checkboxPosition = 'after';\n        /**\n         * This is set to true after the first OnChanges cycle so we don't clear the value of `selected`\n         * in the first cycle.\n         */\n        this._inputsInitialized = false;\n    }\n    /** Theme color of the list option. This sets the color of the checkbox. */\n    get color() {\n        return this._color || this.selectionList.color;\n    }\n    set color(newValue) {\n        this._color = newValue;\n    }\n    /** Value of the option */\n    get value() {\n        return this._value;\n    }\n    set value(newValue) {\n        if (this.selected &&\n            !this.selectionList.compareWith(newValue, this.value) &&\n            this._inputsInitialized) {\n            this.selected = false;\n        }\n        this._value = newValue;\n    }\n    /** Whether the option is disabled. */\n    get disabled() {\n        return this._disabled || (this.selectionList && this.selectionList.disabled);\n    }\n    set disabled(value) {\n        const newValue = coerceBooleanProperty(value);\n        if (newValue !== this._disabled) {\n            this._disabled = newValue;\n            this._changeDetector.markForCheck();\n        }\n    }\n    /** Whether the option is selected. */\n    get selected() {\n        return this.selectionList.selectedOptions.isSelected(this);\n    }\n    set selected(value) {\n        const isSelected = coerceBooleanProperty(value);\n        if (isSelected !== this._selected) {\n            this._setSelected(isSelected);\n            if (isSelected || this.selectionList.multiple) {\n                this.selectionList._reportValueChange();\n            }\n        }\n    }\n    ngOnInit() {\n        const list = this.selectionList;\n        if (list._value && list._value.some(value => list.compareWith(this._value, value))) {\n            this._setSelected(true);\n        }\n        const wasSelected = this._selected;\n        // List options that are selected at initialization can't be reported properly to the form\n        // control. This is because it takes some time until the selection-list knows about all\n        // available options. Also it can happen that the ControlValueAccessor has an initial value\n        // that should be used instead. Deferring the value change report to the next tick ensures\n        // that the form control value is not being overwritten.\n        Promise.resolve().then(() => {\n            if (this._selected || wasSelected) {\n                this.selected = true;\n                this._changeDetector.markForCheck();\n            }\n        });\n        this._inputsInitialized = true;\n    }\n    ngAfterContentInit() {\n        setLines(this._lines, this._element);\n    }\n    ngOnDestroy() {\n        if (this.selected) {\n            // We have to delay this until the next tick in order\n            // to avoid changed after checked errors.\n            Promise.resolve().then(() => {\n                this.selected = false;\n            });\n        }\n        const hadFocus = this._hasFocus;\n        const newActiveItem = this.selectionList._removeOptionFromList(this);\n        // Only move focus if this option was focused at the time it was destroyed.\n        if (hadFocus && newActiveItem) {\n            newActiveItem.focus();\n        }\n    }\n    /** Toggles the selection state of the option. */\n    toggle() {\n        this.selected = !this.selected;\n    }\n    /** Allows for programmatic focusing of the option. */\n    focus() {\n        this._element.nativeElement.focus();\n    }\n    /**\n     * Returns the list item's text label. Implemented as a part of the FocusKeyManager.\n     * @docs-private\n     */\n    getLabel() {\n        return this._text ? this._text.nativeElement.textContent || '' : '';\n    }\n    /** Whether this list item should show a ripple effect when clicked. */\n    _isRippleDisabled() {\n        return this.disabled || this.disableRipple || this.selectionList.disableRipple;\n    }\n    _handleClick() {\n        if (!this.disabled && (this.selectionList.multiple || !this.selected)) {\n            this.toggle();\n            // Emit a change event if the selected state of the option changed through user interaction.\n            this.selectionList._emitChangeEvent([this]);\n        }\n    }\n    _handleFocus() {\n        this.selectionList._setFocusedOption(this);\n        this._hasFocus = true;\n    }\n    _handleBlur() {\n        this.selectionList._onTouched();\n        this._hasFocus = false;\n    }\n    /** Retrieves the DOM element of the component host. */\n    _getHostElement() {\n        return this._element.nativeElement;\n    }\n    /** Sets the selected state of the option. Returns whether the value has changed. */\n    _setSelected(selected) {\n        if (selected === this._selected) {\n            return false;\n        }\n        this._selected = selected;\n        if (selected) {\n            this.selectionList.selectedOptions.select(this);\n        }\n        else {\n            this.selectionList.selectedOptions.deselect(this);\n        }\n        this.selectedChange.emit(selected);\n        this._changeDetector.markForCheck();\n        return true;\n    }\n    /**\n     * Notifies Angular that the option needs to be checked in the next change detection run. Mainly\n     * used to trigger an update of the list option if the disabled state of the selection list\n     * changed.\n     */\n    _markForCheck() {\n        this._changeDetector.markForCheck();\n    }\n}\nMatListOption.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatListOption, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: forwardRef(() => MatSelectionList) }], target: i0.ɵɵFactoryTarget.Component });\nMatListOption.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatListOption, selector: \"mat-list-option\", inputs: { disableRipple: \"disableRipple\", checkboxPosition: \"checkboxPosition\", color: \"color\", value: \"value\", disabled: \"disabled\", selected: \"selected\" }, outputs: { selectedChange: \"selectedChange\" }, host: { attributes: { \"role\": \"option\" }, listeners: { \"focus\": \"_handleFocus()\", \"blur\": \"_handleBlur()\", \"click\": \"_handleClick()\" }, properties: { \"class.mat-list-item-disabled\": \"disabled\", \"class.mat-list-item-with-avatar\": \"_avatar || _icon\", \"class.mat-primary\": \"color === \\\"primary\\\"\", \"class.mat-accent\": \"color !== \\\"primary\\\" && color !== \\\"warn\\\"\", \"class.mat-warn\": \"color === \\\"warn\\\"\", \"class.mat-list-single-selected-option\": \"selected && !selectionList.multiple\", \"attr.aria-selected\": \"selected\", \"attr.aria-disabled\": \"disabled\", \"attr.tabindex\": \"-1\" }, classAttribute: \"mat-list-item mat-list-option mat-focus-indicator\" }, queries: [{ propertyName: \"_avatar\", first: true, predicate: MatListAvatarCssMatStyler, descendants: true }, { propertyName: \"_icon\", first: true, predicate: MatListIconCssMatStyler, descendants: true }, { propertyName: \"_lines\", predicate: MatLine, descendants: true }], viewQueries: [{ propertyName: \"_text\", first: true, predicate: [\"text\"], descendants: true }], exportAs: [\"matListOption\"], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mat-list-item-content\\\"\\n  [class.mat-list-item-content-reverse]=\\\"checkboxPosition == 'after'\\\">\\n\\n  <div mat-ripple\\n    class=\\\"mat-list-item-ripple\\\"\\n    [matRippleTrigger]=\\\"_getHostElement()\\\"\\n    [matRippleDisabled]=\\\"_isRippleDisabled()\\\"></div>\\n\\n  <mat-pseudo-checkbox\\n    *ngIf=\\\"selectionList.multiple\\\"\\n    [state]=\\\"selected ? 'checked' : 'unchecked'\\\"\\n    [disabled]=\\\"disabled\\\"></mat-pseudo-checkbox>\\n\\n  <div class=\\\"mat-list-text\\\" #text><ng-content></ng-content></div>\\n\\n  <ng-content select=\\\"[mat-list-avatar], [mat-list-icon], [matListAvatar], [matListIcon]\\\">\\n  </ng-content>\\n\\n</div>\\n\", dependencies: [{ kind: \"directive\", type: i1.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"component\", type: i1.MatPseudoCheckbox, selector: \"mat-pseudo-checkbox\", inputs: [\"state\", \"disabled\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatListOption, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-list-option', exportAs: 'matListOption', inputs: ['disableRipple'], host: {\n                        'role': 'option',\n                        'class': 'mat-list-item mat-list-option mat-focus-indicator',\n                        '(focus)': '_handleFocus()',\n                        '(blur)': '_handleBlur()',\n                        '(click)': '_handleClick()',\n                        '[class.mat-list-item-disabled]': 'disabled',\n                        '[class.mat-list-item-with-avatar]': '_avatar || _icon',\n                        // Manually set the \"primary\" or \"warn\" class if the color has been explicitly\n                        // set to \"primary\" or \"warn\". The pseudo checkbox picks up these classes for\n                        // its theme.\n                        '[class.mat-primary]': 'color === \"primary\"',\n                        // Even though accent is the default, we need to set this class anyway, because the  list might\n                        // be placed inside a parent that has one of the other colors with a higher specificity.\n                        '[class.mat-accent]': 'color !== \"primary\" && color !== \"warn\"',\n                        '[class.mat-warn]': 'color === \"warn\"',\n                        '[class.mat-list-single-selected-option]': 'selected && !selectionList.multiple',\n                        '[attr.aria-selected]': 'selected',\n                        '[attr.aria-disabled]': 'disabled',\n                        '[attr.tabindex]': '-1',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<div class=\\\"mat-list-item-content\\\"\\n  [class.mat-list-item-content-reverse]=\\\"checkboxPosition == 'after'\\\">\\n\\n  <div mat-ripple\\n    class=\\\"mat-list-item-ripple\\\"\\n    [matRippleTrigger]=\\\"_getHostElement()\\\"\\n    [matRippleDisabled]=\\\"_isRippleDisabled()\\\"></div>\\n\\n  <mat-pseudo-checkbox\\n    *ngIf=\\\"selectionList.multiple\\\"\\n    [state]=\\\"selected ? 'checked' : 'unchecked'\\\"\\n    [disabled]=\\\"disabled\\\"></mat-pseudo-checkbox>\\n\\n  <div class=\\\"mat-list-text\\\" #text><ng-content></ng-content></div>\\n\\n  <ng-content select=\\\"[mat-list-avatar], [mat-list-icon], [matListAvatar], [matListIcon]\\\">\\n  </ng-content>\\n\\n</div>\\n\" }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: MatSelectionList, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => MatSelectionList)]\n                }] }]; }, propDecorators: { _avatar: [{\n                type: ContentChild,\n                args: [MatListAvatarCssMatStyler]\n            }], _icon: [{\n                type: ContentChild,\n                args: [MatListIconCssMatStyler]\n            }], _lines: [{\n                type: ContentChildren,\n                args: [MatLine, { descendants: true }]\n            }], selectedChange: [{\n                type: Output\n            }], _text: [{\n                type: ViewChild,\n                args: ['text']\n            }], checkboxPosition: [{\n                type: Input\n            }], color: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], selected: [{\n                type: Input\n            }] } });\n/**\n * Material Design list component where each item is a selectable option. Behaves as a listbox.\n */\nclass MatSelectionList extends _MatSelectionListBase {\n    constructor(_element, _changeDetector, _focusMonitor) {\n        super();\n        this._element = _element;\n        this._changeDetector = _changeDetector;\n        this._focusMonitor = _focusMonitor;\n        this._multiple = true;\n        this._contentInitialized = false;\n        /** Emits a change event whenever the selected state of an option changes. */\n        this.selectionChange = new EventEmitter();\n        /** Theme color of the selection list. This sets the checkbox color for all list options. */\n        this.color = 'accent';\n        /**\n         * Function used for comparing an option against the selected value when determining which\n         * options should appear as selected. The first argument is the value of an options. The second\n         * one is a value from the selected value. A boolean must be returned.\n         */\n        this.compareWith = (a1, a2) => a1 === a2;\n        this._disabled = false;\n        /** The currently selected options. */\n        this.selectedOptions = new SelectionModel(this._multiple);\n        /** The tabindex of the selection list. */\n        this._tabIndex = -1;\n        /** View to model callback that should be called whenever the selected options change. */\n        this._onChange = (_) => { };\n        /** Emits when the list has been destroyed. */\n        this._destroyed = new Subject();\n        /** View to model callback that should be called if the list or its options lost focus. */\n        this._onTouched = () => { };\n    }\n    /** Whether the selection list is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        // The `MatSelectionList` and `MatListOption` are using the `OnPush` change detection\n        // strategy. Therefore the options will not check for any changes if the `MatSelectionList`\n        // changed its state. Since we know that a change to `disabled` property of the list affects\n        // the state of the options, we manually mark each option for check.\n        this._markOptionsForCheck();\n    }\n    /** Whether selection is limited to one or multiple items (default multiple). */\n    get multiple() {\n        return this._multiple;\n    }\n    set multiple(value) {\n        const newValue = coerceBooleanProperty(value);\n        if (newValue !== this._multiple) {\n            if (this._contentInitialized && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw new Error('Cannot change `multiple` mode of mat-selection-list after initialization.');\n            }\n            this._multiple = newValue;\n            this.selectedOptions = new SelectionModel(this._multiple, this.selectedOptions.selected);\n        }\n    }\n    ngAfterContentInit() {\n        this._contentInitialized = true;\n        this._keyManager = new FocusKeyManager(this.options)\n            .withWrap()\n            .withTypeAhead()\n            .withHomeAndEnd()\n            // Allow disabled items to be focusable. For accessibility reasons, there must be a way for\n            // screen reader users, that allows reading the different options of the list.\n            .skipPredicate(() => false)\n            .withAllowedModifierKeys(['shiftKey']);\n        if (this._value) {\n            this._setOptionsFromValues(this._value);\n        }\n        // If the user attempts to tab out of the selection list, allow focus to escape.\n        this._keyManager.tabOut.pipe(takeUntil(this._destroyed)).subscribe(() => {\n            this._allowFocusEscape();\n        });\n        // When the number of options change, update the tabindex of the selection list.\n        this.options.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n            this._updateTabIndex();\n        });\n        // Sync external changes to the model back to the options.\n        this.selectedOptions.changed.pipe(takeUntil(this._destroyed)).subscribe(event => {\n            if (event.added) {\n                for (let item of event.added) {\n                    item.selected = true;\n                }\n            }\n            if (event.removed) {\n                for (let item of event.removed) {\n                    item.selected = false;\n                }\n            }\n        });\n        this._focusMonitor\n            .monitor(this._element)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(origin => {\n            if (origin === 'keyboard' || origin === 'program') {\n                let toFocus = 0;\n                for (let i = 0; i < this.options.length; i++) {\n                    if (this.options.get(i)?.selected) {\n                        toFocus = i;\n                        break;\n                    }\n                }\n                this._keyManager.setActiveItem(toFocus);\n            }\n        });\n    }\n    ngOnChanges(changes) {\n        const disableRippleChanges = changes['disableRipple'];\n        const colorChanges = changes['color'];\n        if ((disableRippleChanges && !disableRippleChanges.firstChange) ||\n            (colorChanges && !colorChanges.firstChange)) {\n            this._markOptionsForCheck();\n        }\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._element);\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._isDestroyed = true;\n    }\n    /** Focuses the selection list. */\n    focus(options) {\n        this._element.nativeElement.focus(options);\n    }\n    /** Selects all of the options. Returns the options that changed as a result. */\n    selectAll() {\n        return this._setAllOptionsSelected(true);\n    }\n    /** Deselects all of the options. Returns the options that changed as a result. */\n    deselectAll() {\n        return this._setAllOptionsSelected(false);\n    }\n    /** Sets the focused option of the selection-list. */\n    _setFocusedOption(option) {\n        this._keyManager.updateActiveItem(option);\n    }\n    /**\n     * Removes an option from the selection list and updates the active item.\n     * @returns Currently-active item.\n     */\n    _removeOptionFromList(option) {\n        const optionIndex = this._getOptionIndex(option);\n        if (optionIndex > -1 && this._keyManager.activeItemIndex === optionIndex) {\n            // Check whether the option is the last item\n            if (optionIndex > 0) {\n                this._keyManager.updateActiveItem(optionIndex - 1);\n            }\n            else if (optionIndex === 0 && this.options.length > 1) {\n                this._keyManager.updateActiveItem(Math.min(optionIndex + 1, this.options.length - 1));\n            }\n        }\n        return this._keyManager.activeItem;\n    }\n    /** Passes relevant key presses to our key manager. */\n    _keydown(event) {\n        const keyCode = event.keyCode;\n        const manager = this._keyManager;\n        const previousFocusIndex = manager.activeItemIndex;\n        const hasModifier = hasModifierKey(event);\n        switch (keyCode) {\n            case SPACE:\n            case ENTER:\n                if (!hasModifier && !manager.isTyping()) {\n                    this._toggleFocusedOption();\n                    // Always prevent space from scrolling the page since the list has focus\n                    event.preventDefault();\n                }\n                break;\n            default:\n                // The \"A\" key gets special treatment, because it's used for the \"select all\" functionality.\n                if (keyCode === A &&\n                    this.multiple &&\n                    hasModifierKey(event, 'ctrlKey') &&\n                    !manager.isTyping()) {\n                    const shouldSelect = this.options.some(option => !option.disabled && !option.selected);\n                    this._setAllOptionsSelected(shouldSelect, true, true);\n                    event.preventDefault();\n                }\n                else {\n                    manager.onKeydown(event);\n                }\n        }\n        if (this.multiple &&\n            (keyCode === UP_ARROW || keyCode === DOWN_ARROW) &&\n            event.shiftKey &&\n            manager.activeItemIndex !== previousFocusIndex) {\n            this._toggleFocusedOption();\n        }\n    }\n    /** Reports a value change to the ControlValueAccessor */\n    _reportValueChange() {\n        // Stop reporting value changes after the list has been destroyed. This avoids\n        // cases where the list might wrongly reset its value once it is removed, but\n        // the form control is still live.\n        if (this.options && !this._isDestroyed) {\n            const value = this._getSelectedOptionValues();\n            this._onChange(value);\n            this._value = value;\n        }\n    }\n    /** Emits a change event if the selected state of an option changed. */\n    _emitChangeEvent(options) {\n        this.selectionChange.emit(new MatSelectionListChange(this, options));\n    }\n    /** Implemented as part of ControlValueAccessor. */\n    writeValue(values) {\n        this._value = values;\n        if (this.options) {\n            this._setOptionsFromValues(values || []);\n        }\n    }\n    /** Implemented as a part of ControlValueAccessor. */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n    }\n    /** Implemented as part of ControlValueAccessor. */\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    /** Implemented as part of ControlValueAccessor. */\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    /** Sets the selected options based on the specified values. */\n    _setOptionsFromValues(values) {\n        this.options.forEach(option => option._setSelected(false));\n        values.forEach(value => {\n            const correspondingOption = this.options.find(option => {\n                // Skip options that are already in the model. This allows us to handle cases\n                // where the same primitive value is selected multiple times.\n                return option.selected ? false : this.compareWith(option.value, value);\n            });\n            if (correspondingOption) {\n                correspondingOption._setSelected(true);\n            }\n        });\n    }\n    /** Returns the values of the selected options. */\n    _getSelectedOptionValues() {\n        return this.options.filter(option => option.selected).map(option => option.value);\n    }\n    /** Toggles the state of the currently focused option if enabled. */\n    _toggleFocusedOption() {\n        let focusedIndex = this._keyManager.activeItemIndex;\n        if (focusedIndex != null && this._isValidIndex(focusedIndex)) {\n            let focusedOption = this.options.toArray()[focusedIndex];\n            if (focusedOption && !focusedOption.disabled && (this._multiple || !focusedOption.selected)) {\n                focusedOption.toggle();\n                // Emit a change event because the focused option changed its state through user\n                // interaction.\n                this._emitChangeEvent([focusedOption]);\n            }\n        }\n    }\n    /**\n     * Sets the selected state on all of the options\n     * and emits an event if anything changed.\n     */\n    _setAllOptionsSelected(isSelected, skipDisabled, isUserInput) {\n        // Keep track of whether anything changed, because we only want to\n        // emit the changed event when something actually changed.\n        const changedOptions = [];\n        this.options.forEach(option => {\n            if ((!skipDisabled || !option.disabled) && option._setSelected(isSelected)) {\n                changedOptions.push(option);\n            }\n        });\n        if (changedOptions.length) {\n            this._reportValueChange();\n            if (isUserInput) {\n                this._emitChangeEvent(changedOptions);\n            }\n        }\n        return changedOptions;\n    }\n    /**\n     * Utility to ensure all indexes are valid.\n     * @param index The index to be checked.\n     * @returns True if the index is valid for our list of options.\n     */\n    _isValidIndex(index) {\n        return index >= 0 && index < this.options.length;\n    }\n    /** Returns the index of the specified list option. */\n    _getOptionIndex(option) {\n        return this.options.toArray().indexOf(option);\n    }\n    /** Marks all the options to be checked in the next change detection run. */\n    _markOptionsForCheck() {\n        if (this.options) {\n            this.options.forEach(option => option._markForCheck());\n        }\n    }\n    /**\n     * Removes the tabindex from the selection list and resets it back afterwards, allowing the user\n     * to tab out of it. This prevents the list from capturing focus and redirecting it back within\n     * the list, creating a focus trap if it user tries to tab away.\n     */\n    _allowFocusEscape() {\n        this._tabIndex = -1;\n        setTimeout(() => {\n            this._tabIndex = 0;\n            this._changeDetector.markForCheck();\n        });\n    }\n    /** Updates the tabindex based upon if the selection list is empty. */\n    _updateTabIndex() {\n        this._tabIndex = this.options.length === 0 ? -1 : 0;\n    }\n}\nMatSelectionList.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatSelectionList, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i3.FocusMonitor }], target: i0.ɵɵFactoryTarget.Component });\nMatSelectionList.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatSelectionList, selector: \"mat-selection-list\", inputs: { disableRipple: \"disableRipple\", color: \"color\", compareWith: \"compareWith\", disabled: \"disabled\", multiple: \"multiple\" }, outputs: { selectionChange: \"selectionChange\" }, host: { attributes: { \"role\": \"listbox\" }, listeners: { \"keydown\": \"_keydown($event)\" }, properties: { \"attr.aria-multiselectable\": \"multiple\", \"attr.aria-disabled\": \"disabled.toString()\", \"attr.tabindex\": \"_tabIndex\" }, classAttribute: \"mat-selection-list mat-list-base\" }, providers: [MAT_SELECTION_LIST_VALUE_ACCESSOR], queries: [{ propertyName: \"options\", predicate: MatListOption, descendants: true }], exportAs: [\"matSelectionList\"], usesInheritance: true, usesOnChanges: true, ngImport: i0, template: '<ng-content></ng-content>', isInline: true, styles: [\".mat-subheader{display:flex;box-sizing:border-box;padding:16px;align-items:center}.mat-list-base .mat-subheader{margin:0}button.mat-list-item,button.mat-list-option{padding:0;width:100%;background:none;color:inherit;border:none;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:left}[dir=rtl] button.mat-list-item,[dir=rtl] button.mat-list-option{text-align:right}button.mat-list-item::-moz-focus-inner,button.mat-list-option::-moz-focus-inner{border:0}.mat-list-base{padding-top:8px;display:block;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-list-base .mat-subheader{height:48px;line-height:16px}.mat-list-base .mat-subheader:first-child{margin-top:-8px}.mat-list-base .mat-list-item,.mat-list-base .mat-list-option{display:block;height:48px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base .mat-list-item .mat-list-item-content,.mat-list-base .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base .mat-list-item .mat-list-item-content-reverse,.mat-list-base .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base .mat-list-item .mat-list-item-ripple,.mat-list-base .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar,.mat-list-base .mat-list-option.mat-list-item-with-avatar{height:56px}.mat-list-base .mat-list-item.mat-2-line,.mat-list-base .mat-list-option.mat-2-line{height:72px}.mat-list-base .mat-list-item.mat-3-line,.mat-list-base .mat-list-option.mat-3-line{height:88px}.mat-list-base .mat-list-item.mat-multi-line,.mat-list-base .mat-list-option.mat-multi-line{height:auto}.mat-list-base .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base .mat-list-item .mat-list-text,.mat-list-base .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base .mat-list-item .mat-list-text>*,.mat-list-base .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base .mat-list-item .mat-list-text:empty,.mat-list-base .mat-list-option .mat-list-text:empty{display:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base .mat-list-item .mat-list-avatar,.mat-list-base .mat-list-option .mat-list-avatar{flex-shrink:0;width:40px;height:40px;border-radius:50%;object-fit:cover}.mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:72px;width:calc(100% - 72px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:72px}.mat-list-base .mat-list-item .mat-list-icon,.mat-list-base .mat-list-option .mat-list-icon{flex-shrink:0;width:24px;height:24px;font-size:24px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:64px;width:calc(100% - 64px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:64px}.mat-list-base .mat-list-item .mat-divider,.mat-list-base .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base .mat-list-item .mat-divider,[dir=rtl] .mat-list-base .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-list-base[dense]{padding-top:4px;display:block}.mat-list-base[dense] .mat-subheader{height:40px;line-height:8px}.mat-list-base[dense] .mat-subheader:first-child{margin-top:-4px}.mat-list-base[dense] .mat-list-item,.mat-list-base[dense] .mat-list-option{display:block;height:40px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-item-content,.mat-list-base[dense] .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base[dense] .mat-list-item .mat-list-item-content-reverse,.mat-list-base[dense] .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base[dense] .mat-list-item .mat-list-item-ripple,.mat-list-base[dense] .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar{height:48px}.mat-list-base[dense] .mat-list-item.mat-2-line,.mat-list-base[dense] .mat-list-option.mat-2-line{height:60px}.mat-list-base[dense] .mat-list-item.mat-3-line,.mat-list-base[dense] .mat-list-option.mat-3-line{height:76px}.mat-list-base[dense] .mat-list-item.mat-multi-line,.mat-list-base[dense] .mat-list-option.mat-multi-line{height:auto}.mat-list-base[dense] .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base[dense] .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base[dense] .mat-list-item .mat-list-text,.mat-list-base[dense] .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-text>*,.mat-list-base[dense] .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base[dense] .mat-list-item .mat-list-text:empty,.mat-list-base[dense] .mat-list-option .mat-list-text:empty{display:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base[dense] .mat-list-item .mat-list-avatar,.mat-list-base[dense] .mat-list-option .mat-list-avatar{flex-shrink:0;width:36px;height:36px;border-radius:50%;object-fit:cover}.mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:68px;width:calc(100% - 68px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:68px}.mat-list-base[dense] .mat-list-item .mat-list-icon,.mat-list-base[dense] .mat-list-option .mat-list-icon{flex-shrink:0;width:20px;height:20px;font-size:20px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:60px;width:calc(100% - 60px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:60px}.mat-list-base[dense] .mat-list-item .mat-divider,.mat-list-base[dense] .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-divider,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base[dense] .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-nav-list a{text-decoration:none;color:inherit}.mat-nav-list .mat-list-item{cursor:pointer;outline:none}mat-action-list .mat-list-item{cursor:pointer;outline:inherit}.mat-list-option:not(.mat-list-item-disabled){cursor:pointer;outline:none}.mat-list-item-disabled{pointer-events:none}.cdk-high-contrast-active .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active :host .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active .mat-list-option:hover,.cdk-high-contrast-active .mat-nav-list .mat-list-item:hover,.cdk-high-contrast-active mat-action-list .mat-list-item:hover{outline:dotted 1px;z-index:1}.cdk-high-contrast-active .mat-list-single-selected-option::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}.cdk-high-contrast-active [dir=rtl] .mat-list-single-selected-option::after{right:auto;left:16px}@media(hover: none){.mat-list-option:not(.mat-list-single-selected-option):not(.mat-list-item-disabled):hover,.mat-nav-list .mat-list-item:not(.mat-list-item-disabled):hover,.mat-action-list .mat-list-item:not(.mat-list-item-disabled):hover{background:none}}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatSelectionList, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-selection-list', exportAs: 'matSelectionList', inputs: ['disableRipple'], host: {\n                        'role': 'listbox',\n                        'class': 'mat-selection-list mat-list-base',\n                        '(keydown)': '_keydown($event)',\n                        '[attr.aria-multiselectable]': 'multiple',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '[attr.tabindex]': '_tabIndex',\n                    }, template: '<ng-content></ng-content>', encapsulation: ViewEncapsulation.None, providers: [MAT_SELECTION_LIST_VALUE_ACCESSOR], changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-subheader{display:flex;box-sizing:border-box;padding:16px;align-items:center}.mat-list-base .mat-subheader{margin:0}button.mat-list-item,button.mat-list-option{padding:0;width:100%;background:none;color:inherit;border:none;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:left}[dir=rtl] button.mat-list-item,[dir=rtl] button.mat-list-option{text-align:right}button.mat-list-item::-moz-focus-inner,button.mat-list-option::-moz-focus-inner{border:0}.mat-list-base{padding-top:8px;display:block;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-list-base .mat-subheader{height:48px;line-height:16px}.mat-list-base .mat-subheader:first-child{margin-top:-8px}.mat-list-base .mat-list-item,.mat-list-base .mat-list-option{display:block;height:48px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base .mat-list-item .mat-list-item-content,.mat-list-base .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base .mat-list-item .mat-list-item-content-reverse,.mat-list-base .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base .mat-list-item .mat-list-item-ripple,.mat-list-base .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar,.mat-list-base .mat-list-option.mat-list-item-with-avatar{height:56px}.mat-list-base .mat-list-item.mat-2-line,.mat-list-base .mat-list-option.mat-2-line{height:72px}.mat-list-base .mat-list-item.mat-3-line,.mat-list-base .mat-list-option.mat-3-line{height:88px}.mat-list-base .mat-list-item.mat-multi-line,.mat-list-base .mat-list-option.mat-multi-line{height:auto}.mat-list-base .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base .mat-list-item .mat-list-text,.mat-list-base .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base .mat-list-item .mat-list-text>*,.mat-list-base .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base .mat-list-item .mat-list-text:empty,.mat-list-base .mat-list-option .mat-list-text:empty{display:none}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base .mat-list-item .mat-list-avatar,.mat-list-base .mat-list-option .mat-list-avatar{flex-shrink:0;width:40px;height:40px;border-radius:50%;object-fit:cover}.mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:72px;width:calc(100% - 72px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:72px}.mat-list-base .mat-list-item .mat-list-icon,.mat-list-base .mat-list-option .mat-list-icon{flex-shrink:0;width:24px;height:24px;font-size:24px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:64px;width:calc(100% - 64px)}[dir=rtl] .mat-list-base .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:64px}.mat-list-base .mat-list-item .mat-divider,.mat-list-base .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base .mat-list-item .mat-divider,[dir=rtl] .mat-list-base .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-list-base[dense]{padding-top:4px;display:block}.mat-list-base[dense] .mat-subheader{height:40px;line-height:8px}.mat-list-base[dense] .mat-subheader:first-child{margin-top:-4px}.mat-list-base[dense] .mat-list-item,.mat-list-base[dense] .mat-list-option{display:block;height:40px;-webkit-tap-highlight-color:rgba(0,0,0,0);width:100%;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-item-content,.mat-list-base[dense] .mat-list-option .mat-list-item-content{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;padding:0 16px;position:relative;height:inherit}.mat-list-base[dense] .mat-list-item .mat-list-item-content-reverse,.mat-list-base[dense] .mat-list-option .mat-list-item-content-reverse{display:flex;align-items:center;padding:0 16px;flex-direction:row-reverse;justify-content:space-around}.mat-list-base[dense] .mat-list-item .mat-list-item-ripple,.mat-list-base[dense] .mat-list-option .mat-list-item-ripple{display:block;top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar{height:48px}.mat-list-base[dense] .mat-list-item.mat-2-line,.mat-list-base[dense] .mat-list-option.mat-2-line{height:60px}.mat-list-base[dense] .mat-list-item.mat-3-line,.mat-list-base[dense] .mat-list-option.mat-3-line{height:76px}.mat-list-base[dense] .mat-list-item.mat-multi-line,.mat-list-base[dense] .mat-list-option.mat-multi-line{height:auto}.mat-list-base[dense] .mat-list-item.mat-multi-line .mat-list-item-content,.mat-list-base[dense] .mat-list-option.mat-multi-line .mat-list-item-content{padding-top:16px;padding-bottom:16px}.mat-list-base[dense] .mat-list-item .mat-list-text,.mat-list-base[dense] .mat-list-option .mat-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden;padding:0}.mat-list-base[dense] .mat-list-item .mat-list-text>*,.mat-list-base[dense] .mat-list-option .mat-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-list-base[dense] .mat-list-item .mat-list-text:empty,.mat-list-base[dense] .mat-list-option .mat-list-text:empty{display:none}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:0;padding-left:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:0}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-left:0;padding-right:16px}[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-item.mat-list-option .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar .mat-list-item-content-reverse .mat-list-text,[dir=rtl] .mat-list-base[dense] .mat-list-option.mat-list-option .mat-list-item-content-reverse .mat-list-text{padding-right:0;padding-left:16px}.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-item.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content-reverse .mat-list-text,.mat-list-base[dense] .mat-list-option.mat-list-item-with-avatar.mat-list-option .mat-list-item-content .mat-list-text{padding-right:16px;padding-left:16px}.mat-list-base[dense] .mat-list-item .mat-list-avatar,.mat-list-base[dense] .mat-list-option .mat-list-avatar{flex-shrink:0;width:36px;height:36px;border-radius:50%;object-fit:cover}.mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:68px;width:calc(100% - 68px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-avatar~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-avatar~.mat-divider-inset{margin-left:auto;margin-right:68px}.mat-list-base[dense] .mat-list-item .mat-list-icon,.mat-list-base[dense] .mat-list-option .mat-list-icon{flex-shrink:0;width:20px;height:20px;font-size:20px;box-sizing:content-box;border-radius:50%;padding:4px}.mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:60px;width:calc(100% - 60px)}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-list-icon~.mat-divider-inset,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-list-icon~.mat-divider-inset{margin-left:auto;margin-right:60px}.mat-list-base[dense] .mat-list-item .mat-divider,.mat-list-base[dense] .mat-list-option .mat-divider{position:absolute;bottom:0;left:0;width:100%;margin:0}[dir=rtl] .mat-list-base[dense] .mat-list-item .mat-divider,[dir=rtl] .mat-list-base[dense] .mat-list-option .mat-divider{margin-left:auto;margin-right:0}.mat-list-base[dense] .mat-list-item .mat-divider.mat-divider-inset,.mat-list-base[dense] .mat-list-option .mat-divider.mat-divider-inset{position:absolute}.mat-nav-list a{text-decoration:none;color:inherit}.mat-nav-list .mat-list-item{cursor:pointer;outline:none}mat-action-list .mat-list-item{cursor:pointer;outline:inherit}.mat-list-option:not(.mat-list-item-disabled){cursor:pointer;outline:none}.mat-list-item-disabled{pointer-events:none}.cdk-high-contrast-active .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active :host .mat-list-item-disabled{opacity:.5}.cdk-high-contrast-active .mat-list-option:hover,.cdk-high-contrast-active .mat-nav-list .mat-list-item:hover,.cdk-high-contrast-active mat-action-list .mat-list-item:hover{outline:dotted 1px;z-index:1}.cdk-high-contrast-active .mat-list-single-selected-option::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}.cdk-high-contrast-active [dir=rtl] .mat-list-single-selected-option::after{right:auto;left:16px}@media(hover: none){.mat-list-option:not(.mat-list-single-selected-option):not(.mat-list-item-disabled):hover,.mat-nav-list .mat-list-item:not(.mat-list-item-disabled):hover,.mat-action-list .mat-list-item:not(.mat-list-item-disabled):hover{background:none}}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i3.FocusMonitor }]; }, propDecorators: { options: [{\n                type: ContentChildren,\n                args: [MatListOption, { descendants: true }]\n            }], selectionChange: [{\n                type: Output\n            }], color: [{\n                type: Input\n            }], compareWith: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], multiple: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatListModule {\n}\nMatListModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatListModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatListModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: MatListModule, declarations: [MatList,\n        MatNavList,\n        MatListItem,\n        MatListAvatarCssMatStyler,\n        MatListIconCssMatStyler,\n        MatListSubheaderCssMatStyler,\n        MatSelectionList,\n        MatListOption], imports: [MatLineModule, MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, CommonModule], exports: [MatList,\n        MatNavList,\n        MatListItem,\n        MatListAvatarCssMatStyler,\n        MatLineModule,\n        MatCommonModule,\n        MatListIconCssMatStyler,\n        MatListSubheaderCssMatStyler,\n        MatPseudoCheckboxModule,\n        MatSelectionList,\n        MatListOption,\n        MatDividerModule] });\nMatListModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatListModule, imports: [MatLineModule, MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, CommonModule, MatLineModule,\n        MatCommonModule,\n        MatPseudoCheckboxModule,\n        MatDividerModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatListModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatLineModule, MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, CommonModule],\n                    exports: [\n                        MatList,\n                        MatNavList,\n                        MatListItem,\n                        MatListAvatarCssMatStyler,\n                        MatLineModule,\n                        MatCommonModule,\n                        MatListIconCssMatStyler,\n                        MatListSubheaderCssMatStyler,\n                        MatPseudoCheckboxModule,\n                        MatSelectionList,\n                        MatListOption,\n                        MatDividerModule,\n                    ],\n                    declarations: [\n                        MatList,\n                        MatNavList,\n                        MatListItem,\n                        MatListAvatarCssMatStyler,\n                        MatListIconCssMatStyler,\n                        MatListSubheaderCssMatStyler,\n                        MatSelectionList,\n                        MatListOption,\n                    ],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_LIST, MAT_NAV_LIST, MAT_SELECTION_LIST_VALUE_ACCESSOR, MatList, MatListAvatarCssMatStyler, MatListIconCssMatStyler, MatListItem, MatListModule, MatListOption, MatListSubheaderCssMatStyler, MatNavList, MatSelectionList, MatSelectionListChange };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,SAAzB,EAAoCC,iBAApC,EAAuDC,uBAAvD,EAAgFC,SAAhF,EAA2FC,QAA3F,EAAqGC,MAArG,EAA6GC,eAA7G,EAA8HC,YAA9H,EAA4IC,KAA5I,EAAmJC,UAAnJ,EAA+JC,YAA/J,EAA6KC,MAA7K,EAAqLC,SAArL,EAAgMC,QAAhM,QAAgN,eAAhN;AACA,OAAO,KAAKC,EAAZ,MAAoB,wBAApB;AACA,SAASC,aAAT,EAAwBC,kBAAxB,EAA4CC,QAA5C,EAAsDC,OAAtD,EAA+DC,aAA/D,EAA8EC,eAA9E,EAA+FC,eAA/F,EAAgHC,uBAAhH,QAA+I,wBAA/I;AACA,SAASC,qBAAT,QAAsC,uBAAtC;AACA,SAASC,OAAT,QAAwB,MAAxB;AACA,SAASC,SAAT,EAAoBC,SAApB,QAAqC,gBAArC;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,SAASC,eAAT,QAAgC,mBAAhC;AACA,SAASC,cAAT,QAA+B,0BAA/B;AACA,SAASC,cAAT,EAAyBC,CAAzB,EAA4BC,KAA5B,EAAmCC,KAAnC,EAA0CC,QAA1C,EAAoDC,UAApD,QAAsE,uBAAtE;AACA,SAASC,iBAAT,QAAkC,gBAAlC;AACA,SAASC,gBAAT,QAAiC,2BAAjC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;;;;;;;;IAgC6FvC,EAmX+kD,uC;;;;mBAnX/kDA,E;IAAAA,EAmX+oD,4F;;;;;;;AAlZ5uD,MAAMwC,YAAY,GAAGvB,aAAa,CAACC,kBAAkB,CAAC,MAAM,EAAP,CAAnB,CAAlC,C,CAEA;;AACA;;;AACA,MAAMuB,qBAAqB,GAAGvB,kBAAkB,CAAC,MAAM,EAAP,CAAhD;AAEA;AACA;AACA;AACA;AACA;;;AACA,MAAMwB,QAAQ,GAAG,IAAIzC,cAAJ,CAAmB,SAAnB,CAAjB;AACA;AACA;AACA;AACA;AACA;;AACA,MAAM0C,YAAY,GAAG,IAAI1C,cAAJ,CAAmB,YAAnB,CAArB;;AACA,MAAM2C,UAAN,SAAyBJ,YAAzB,CAAsC;EAClCK,WAAW,GAAG;IACV,MAAM,GAAGC,SAAT;IACA;;IACA,KAAKC,aAAL,GAAqB,IAAIrB,OAAJ,EAArB;EACH;;EACDsB,WAAW,GAAG;IACV,KAAKD,aAAL,CAAmBE,IAAnB;EACH;;EACDC,WAAW,GAAG;IACV,KAAKH,aAAL,CAAmBI,QAAnB;EACH;;AAXiC;;AAatCP,UAAU,CAACQ,IAAX;EAAA;EAAA;IAAA,8DAA6FpD,EAA7F,uBAAuG4C,UAAvG,SAAuGA,UAAvG;EAAA;AAAA;;AACAA,UAAU,CAACS,IAAX,kBAD6FrD,EAC7F;EAAA,MAA2F4C,UAA3F;EAAA;EAAA,oBAAiO,YAAjO;EAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAD6F5C,EAC7F,oBAA4S,CAAC;IAAEsD,OAAO,EAAEX,YAAX;IAAyBY,WAAW,EAAEX;EAAtC,CAAD,CAA5S,GAD6F5C,EAC7F,6BAD6FA,EAC7F;EAAA;EAAA;EAAA;EAAA;IAAA;MAD6FA,EAC7F;MAD6FA,EACoW,gBAAjc;IAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDAF6FA,EAE7F,mBAA2F4C,UAA3F,EAAmH,CAAC;IACxGY,IAAI,EAAEtD,SADkG;IAExGuD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAZ;MAA4BC,QAAQ,EAAE,YAAtC;MAAoDC,IAAI,EAAE;QACrD,QAAQ,YAD6C;QAErD,SAAS;MAF4C,CAA1D;MAGIC,MAAM,EAAE,CAAC,eAAD,EAAkB,UAAlB,CAHZ;MAG2CC,aAAa,EAAE3D,iBAAiB,CAAC4D,IAH5E;MAGkFC,eAAe,EAAE5D,uBAAuB,CAAC6D,MAH3H;MAGmIC,SAAS,EAAE,CAAC;QAAEZ,OAAO,EAAEX,YAAX;QAAyBY,WAAW,EAAEX;MAAtC,CAAD,CAH9I;MAGoMuB,QAAQ,EAAE,+BAH9M;MAG+OC,MAAM,EAAE,CAAC,kzaAAD;IAHvP,CAAD;EAFkG,CAAD,CAAnH;AAAA;;AAOA,MAAMC,OAAN,SAAsB7B,YAAtB,CAAmC;EAC/BK,WAAW,CAACyB,WAAD,EAAc;IACrB;IACA,KAAKA,WAAL,GAAmBA,WAAnB;IACA;;IACA,KAAKvB,aAAL,GAAqB,IAAIrB,OAAJ,EAArB;;IACA,IAAI,KAAK6C,YAAL,OAAwB,aAA5B,EAA2C;MACvCD,WAAW,CAACE,aAAZ,CAA0BC,SAA1B,CAAoCC,GAApC,CAAwC,iBAAxC;;MACAJ,WAAW,CAACE,aAAZ,CAA0BG,YAA1B,CAAuC,MAAvC,EAA+C,OAA/C;IACH;EACJ;;EACDJ,YAAY,GAAG;IACX,MAAMK,QAAQ,GAAG,KAAKN,WAAL,CAAiBE,aAAjB,CAA+BI,QAA/B,CAAwCC,WAAxC,EAAjB;;IACA,IAAID,QAAQ,KAAK,UAAjB,EAA6B;MACzB,OAAO,MAAP;IACH;;IACD,IAAIA,QAAQ,KAAK,iBAAjB,EAAoC;MAChC,OAAO,aAAP;IACH;;IACD,OAAO,IAAP;EACH;;EACD5B,WAAW,GAAG;IACV,KAAKD,aAAL,CAAmBE,IAAnB;EACH;;EACDC,WAAW,GAAG;IACV,KAAKH,aAAL,CAAmBI,QAAnB;EACH;;AA1B8B;;AA4BnCkB,OAAO,CAACjB,IAAR;EAAA,iBAAoGiB,OAApG,EArC6FrE,EAqC7F,mBAA6HA,EAAE,CAAC8E,UAAhI;AAAA;;AACAT,OAAO,CAAChB,IAAR,kBAtC6FrD,EAsC7F;EAAA,MAAwFqE,OAAxF;EAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAtC6FrE,EAsC7F,oBAAyQ,CAAC;IAAEsD,OAAO,EAAEZ,QAAX;IAAqBa,WAAW,EAAEc;EAAlC,CAAD,CAAzQ,GAtC6FrE,EAsC7F,6BAtC6FA,EAsC7F;EAAA;EAAA;EAAA;EAAA;IAAA;MAtC6FA,EAsC7F;MAtC6FA,EAsCuT,gBAApZ;IAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDAvC6FA,EAuC7F,mBAA2FqE,OAA3F,EAAgH,CAAC;IACrGb,IAAI,EAAEtD,SAD+F;IAErGuD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,2BAAZ;MAAyCC,QAAQ,EAAE,SAAnD;MAA8DC,IAAI,EAAE;QAC/D,SAAS;MADsD,CAApE;MAEIC,MAAM,EAAE,CAAC,eAAD,EAAkB,UAAlB,CAFZ;MAE2CC,aAAa,EAAE3D,iBAAiB,CAAC4D,IAF5E;MAEkFC,eAAe,EAAE5D,uBAAuB,CAAC6D,MAF3H;MAEmIC,SAAS,EAAE,CAAC;QAAEZ,OAAO,EAAEZ,QAAX;QAAqBa,WAAW,EAAEc;MAAlC,CAAD,CAF9I;MAE6LF,QAAQ,EAAE,+BAFvM;MAEwOC,MAAM,EAAE,CAAC,kzaAAD;IAFhP,CAAD;EAF+F,CAAD,CAAhH,EAK4B,YAAY;IAAE,OAAO,CAAC;MAAEZ,IAAI,EAAExD,EAAE,CAAC8E;IAAX,CAAD,CAAP;EAAmC,CAL7E;AAAA;AAMA;AACA;AACA;AACA;;;AACA,MAAMC,yBAAN,CAAgC;;AAEhCA,yBAAyB,CAAC3B,IAA1B;EAAA,iBAAsH2B,yBAAtH;AAAA;;AACAA,yBAAyB,CAACC,IAA1B,kBApD6FhF,EAoD7F;EAAA,MAA0G+E,yBAA1G;EAAA;EAAA;AAAA;;AACA;EAAA,mDArD6F/E,EAqD7F,mBAA2F+E,yBAA3F,EAAkI,CAAC;IACvHvB,IAAI,EAAEnD,SADiH;IAEvHoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCADX;MAECE,IAAI,EAAE;QAAE,SAAS;MAAX;IAFP,CAAD;EAFiH,CAAD,CAAlI;AAAA;AAOA;AACA;AACA;AACA;;;AACA,MAAMqB,uBAAN,CAA8B;;AAE9BA,uBAAuB,CAAC7B,IAAxB;EAAA,iBAAoH6B,uBAApH;AAAA;;AACAA,uBAAuB,CAACD,IAAxB,kBAnE6FhF,EAmE7F;EAAA,MAAwGiF,uBAAxG;EAAA;EAAA;AAAA;;AACA;EAAA,mDApE6FjF,EAoE7F,mBAA2FiF,uBAA3F,EAAgI,CAAC;IACrHzB,IAAI,EAAEnD,SAD+G;IAErHoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gCADX;MAECE,IAAI,EAAE;QAAE,SAAS;MAAX;IAFP,CAAD;EAF+G,CAAD,CAAhI;AAAA;AAOA;AACA;AACA;AACA;;;AACA,MAAMsB,4BAAN,CAAmC;;AAEnCA,4BAA4B,CAAC9B,IAA7B;EAAA,iBAAyH8B,4BAAzH;AAAA;;AACAA,4BAA4B,CAACF,IAA7B,kBAlF6FhF,EAkF7F;EAAA,MAA6GkF,4BAA7G;EAAA;EAAA;AAAA;;AACA;EAAA,mDAnF6FlF,EAmF7F,mBAA2FkF,4BAA3F,EAAqI,CAAC;IAC1H1B,IAAI,EAAEnD,SADoH;IAE1HoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iCADX;MAECE,IAAI,EAAE;QAAE,SAAS;MAAX;IAFP,CAAD;EAFoH,CAAD,CAArI;AAAA;AAOA;;;AACA,MAAMuB,WAAN,SAA0B1C,qBAA1B,CAAgD;EAC5CI,WAAW,CAACuC,QAAD,EAAWC,kBAAX,EAA+BC,OAA/B,EAAwCC,IAAxC,EAA8C;IACrD;IACA,KAAKH,QAAL,GAAgBA,QAAhB;IACA,KAAKI,kBAAL,GAA0B,KAA1B;IACA,KAAKC,UAAL,GAAkB,IAAI/D,OAAJ,EAAlB;IACA,KAAKgE,SAAL,GAAiB,KAAjB;IACA,KAAKF,kBAAL,GAA0B,CAAC,EAAEF,OAAO,IAAKC,IAAI,IAAIA,IAAI,CAAChB,YAAL,OAAwB,aAA9C,CAA3B;IACA,KAAKoB,KAAL,GAAaL,OAAO,IAAIC,IAAxB,CAPqD,CAQrD;IACA;;IACA,MAAMK,OAAO,GAAG,KAAKC,eAAL,EAAhB;;IACA,IAAID,OAAO,CAAChB,QAAR,CAAiBC,WAAjB,OAAmC,QAAnC,IAA+C,CAACe,OAAO,CAACE,YAAR,CAAqB,MAArB,CAApD,EAAkF;MAC9EF,OAAO,CAACjB,YAAR,CAAqB,MAArB,EAA6B,QAA7B;IACH;;IACD,IAAI,KAAKgB,KAAT,EAAgB;MACZ;MACA;MACA,KAAKA,KAAL,CAAW5C,aAAX,CAAyBgD,IAAzB,CAA8BpE,SAAS,CAAC,KAAK8D,UAAN,CAAvC,EAA0DO,SAA1D,CAAoE,MAAM;QACtEX,kBAAkB,CAACY,YAAnB;MACH,CAFD;IAGH;EACJ;EACD;;;EACY,IAARC,QAAQ,GAAG;IACX,OAAO,KAAKR,SAAL,IAAkB,CAAC,EAAE,KAAKC,KAAL,IAAc,KAAKA,KAAL,CAAWO,QAA3B,CAA1B;EACH;;EACW,IAARA,QAAQ,CAACC,KAAD,EAAQ;IAChB,KAAKT,SAAL,GAAiBjE,qBAAqB,CAAC0E,KAAD,CAAtC;EACH;;EACDC,kBAAkB,GAAG;IACjBjF,QAAQ,CAAC,KAAKkF,MAAN,EAAc,KAAKjB,QAAnB,CAAR;EACH;;EACDlC,WAAW,GAAG;IACV,KAAKuC,UAAL,CAAgBxC,IAAhB;;IACA,KAAKwC,UAAL,CAAgBtC,QAAhB;EACH;EACD;;;EACAmD,iBAAiB,GAAG;IAChB,OAAQ,CAAC,KAAKd,kBAAN,IAA4B,KAAKe,aAAjC,IAAkD,CAAC,EAAE,KAAKZ,KAAL,IAAc,KAAKA,KAAL,CAAWY,aAA3B,CAA3D;EACH;EACD;;;EACAV,eAAe,GAAG;IACd,OAAO,KAAKT,QAAL,CAAcZ,aAArB;EACH;;AA5C2C;;AA8ChDW,WAAW,CAAC/B,IAAZ;EAAA,iBAAwG+B,WAAxG,EAzI6FnF,EAyI7F,mBAAqIA,EAAE,CAAC8E,UAAxI,GAzI6F9E,EAyI7F,mBAA+JA,EAAE,CAACwG,iBAAlK,GAzI6FxG,EAyI7F,mBAAgM2C,YAAhM,MAzI6F3C,EAyI7F,mBAAyO0C,QAAzO;AAAA;;AACAyC,WAAW,CAAC9B,IAAZ,kBA1I6FrD,EA0I7F;EAAA,MAA4FmF,WAA5F;EAAA;EAAA;IAAA;MA1I6FnF,EA0I7F,0BAA8d+E,yBAA9d;MA1I6F/E,EA0I7F,0BAA+jBiF,uBAA/jB;MA1I6FjF,EA0I7F,0BAAkpBoB,OAAlpB;IAAA;;IAAA;MAAA;;MA1I6FpB,EA0I7F,qBA1I6FA,EA0I7F;MA1I6FA,EA0I7F,qBA1I6FA,EA0I7F;MA1I6FA,EA0I7F,qBA1I6FA,EA0I7F;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA1I6FA,EA0I7F;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WA1I6FA,EA0I7F;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA1I6FA,EA0I7F;MA1I6FA,EA0I+pB,6BAA5vB;MA1I6FA,EA0IysB,wBAAtyB;MA1I6FA,EA0I+2B,gBAA58B;MA1I6FA,EA0Ig+B,6BAA7jC;MA1I6FA,EA0I8/B,mBAA3lC;MA1I6FA,EA0IwjC,eAArpC;MA1I6FA,EA0IqkC,mBAAlqC;MA1I6FA,EA0IgmC,eAA7rC;IAAA;;IAAA;MA1I6FA,EA0IiwB,aAA91B;MA1I6FA,EA0IiwB,oGAA91B;IAAA;EAAA;EAAA,eAAmvCgB,EAAE,CAACyF,SAAtvC;EAAA;EAAA;AAAA;;AACA;EAAA,mDA3I6FzG,EA2I7F,mBAA2FmF,WAA3F,EAAoH,CAAC;IACzG3B,IAAI,EAAEtD,SADmG;IAEzGuD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,wDAAZ;MAAsEC,QAAQ,EAAE,aAAhF;MAA+FC,IAAI,EAAE;QAChG,SAAS,mCADuF;QAEhG,kCAAkC,UAF8D;QAGhG,qCAAqC;MAH2D,CAArG;MAIIC,MAAM,EAAE,CAAC,eAAD,CAJZ;MAI+BC,aAAa,EAAE3D,iBAAiB,CAAC4D,IAJhE;MAIsEC,eAAe,EAAE5D,uBAAuB,CAAC6D,MAJ/G;MAIuHE,QAAQ,EAAE;IAJjI,CAAD;EAFmG,CAAD,CAApH,EAO4B,YAAY;IAAE,OAAO,CAAC;MAAEX,IAAI,EAAExD,EAAE,CAAC8E;IAAX,CAAD,EAA0B;MAAEtB,IAAI,EAAExD,EAAE,CAACwG;IAAX,CAA1B,EAA0D;MAAEhD,IAAI,EAAEZ,UAAR;MAAoB8D,UAAU,EAAE,CAAC;QACxHlD,IAAI,EAAElD;MADkH,CAAD,EAExH;QACCkD,IAAI,EAAEjD,MADP;QAECkD,IAAI,EAAE,CAACd,YAAD;MAFP,CAFwH;IAAhC,CAA1D,EAK3B;MAAEa,IAAI,EAAEa,OAAR;MAAiBqC,UAAU,EAAE,CAAC;QAChClD,IAAI,EAAElD;MAD0B,CAAD,EAEhC;QACCkD,IAAI,EAAEjD,MADP;QAECkD,IAAI,EAAE,CAACf,QAAD;MAFP,CAFgC;IAA7B,CAL2B,CAAP;EAUlB,CAjBxB,EAiB0C;IAAE2D,MAAM,EAAE,CAAC;MACrC7C,IAAI,EAAEhD,eAD+B;MAErCiD,IAAI,EAAE,CAACrC,OAAD,EAAU;QAAEuF,WAAW,EAAE;MAAf,CAAV;IAF+B,CAAD,CAAV;IAG1BC,OAAO,EAAE,CAAC;MACVpD,IAAI,EAAE/C,YADI;MAEVgD,IAAI,EAAE,CAACsB,yBAAD;IAFI,CAAD,CAHiB;IAM1B8B,KAAK,EAAE,CAAC;MACRrD,IAAI,EAAE/C,YADE;MAERgD,IAAI,EAAE,CAACwB,uBAAD;IAFE,CAAD,CANmB;IAS1BiB,QAAQ,EAAE,CAAC;MACX1C,IAAI,EAAE9C;IADK,CAAD;EATgB,CAjB1C;AAAA;AA8BA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMoG,qBAAqB,GAAG5F,kBAAkB,CAAC,MAAM,EAAP,CAAhD;;AAEA,MAAM6F,kBAAkB,GAAG7F,kBAAkB,CAAC,MAAM,EAAP,CAA7C;AAEA;;;AACA,MAAM8F,iCAAiC,GAAG;EACtC1D,OAAO,EAAEhB,iBAD6B;EAEtCiB,WAAW,EAAE5C,UAAU,CAAC,MAAMsG,gBAAP,CAFe;EAGtCC,KAAK,EAAE;AAH+B,CAA1C;AAKA;;AACA,MAAMC,sBAAN,CAA6B;EACzBtE,WAAW;EACX;EACAuE,MAFW;EAGX;EACAC,OAJW,EAIF;IACL,KAAKD,MAAL,GAAcA,MAAd;IACA,KAAKC,OAAL,GAAeA,OAAf;EACH;;AARwB;AAU7B;AACA;AACA;AACA;AACA;;;AACA,MAAMC,aAAN,SAA4BP,kBAA5B,CAA+C;EAC3ClE,WAAW,CAACuC,QAAD,EAAWmC,eAAX;EACX;EACAC,aAFW,EAEI;IACX;IACA,KAAKpC,QAAL,GAAgBA,QAAhB;IACA,KAAKmC,eAAL,GAAuBA,eAAvB;IACA,KAAKC,aAAL,GAAqBA,aAArB;IACA,KAAKC,SAAL,GAAiB,KAAjB;IACA,KAAK/B,SAAL,GAAiB,KAAjB;IACA,KAAKgC,SAAL,GAAiB,KAAjB;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKC,cAAL,GAAsB,IAAI/G,YAAJ,EAAtB;IACA;;IACA,KAAKgH,gBAAL,GAAwB,OAAxB;IACA;AACR;AACA;AACA;;IACQ,KAAKC,kBAAL,GAA0B,KAA1B;EACH;EACD;;;EACS,IAALC,KAAK,GAAG;IACR,OAAO,KAAKC,MAAL,IAAe,KAAKP,aAAL,CAAmBM,KAAzC;EACH;;EACQ,IAALA,KAAK,CAACE,QAAD,EAAW;IAChB,KAAKD,MAAL,GAAcC,QAAd;EACH;EACD;;;EACS,IAAL7B,KAAK,GAAG;IACR,OAAO,KAAK8B,MAAZ;EACH;;EACQ,IAAL9B,KAAK,CAAC6B,QAAD,EAAW;IAChB,IAAI,KAAKE,QAAL,IACA,CAAC,KAAKV,aAAL,CAAmBW,WAAnB,CAA+BH,QAA/B,EAAyC,KAAK7B,KAA9C,CADD,IAEA,KAAK0B,kBAFT,EAE6B;MACzB,KAAKK,QAAL,GAAgB,KAAhB;IACH;;IACD,KAAKD,MAAL,GAAcD,QAAd;EACH;EACD;;;EACY,IAAR9B,QAAQ,GAAG;IACX,OAAO,KAAKR,SAAL,IAAmB,KAAK8B,aAAL,IAAsB,KAAKA,aAAL,CAAmBtB,QAAnE;EACH;;EACW,IAARA,QAAQ,CAACC,KAAD,EAAQ;IAChB,MAAM6B,QAAQ,GAAGvG,qBAAqB,CAAC0E,KAAD,CAAtC;;IACA,IAAI6B,QAAQ,KAAK,KAAKtC,SAAtB,EAAiC;MAC7B,KAAKA,SAAL,GAAiBsC,QAAjB;;MACA,KAAKT,eAAL,CAAqBtB,YAArB;IACH;EACJ;EACD;;;EACY,IAARiC,QAAQ,GAAG;IACX,OAAO,KAAKV,aAAL,CAAmBY,eAAnB,CAAmCC,UAAnC,CAA8C,IAA9C,CAAP;EACH;;EACW,IAARH,QAAQ,CAAC/B,KAAD,EAAQ;IAChB,MAAMkC,UAAU,GAAG5G,qBAAqB,CAAC0E,KAAD,CAAxC;;IACA,IAAIkC,UAAU,KAAK,KAAKZ,SAAxB,EAAmC;MAC/B,KAAKa,YAAL,CAAkBD,UAAlB;;MACA,IAAIA,UAAU,IAAI,KAAKb,aAAL,CAAmBe,QAArC,EAA+C;QAC3C,KAAKf,aAAL,CAAmBgB,kBAAnB;MACH;IACJ;EACJ;;EACDC,QAAQ,GAAG;IACP,MAAMlD,IAAI,GAAG,KAAKiC,aAAlB;;IACA,IAAIjC,IAAI,CAAC0C,MAAL,IAAe1C,IAAI,CAAC0C,MAAL,CAAYS,IAAZ,CAAiBvC,KAAK,IAAIZ,IAAI,CAAC4C,WAAL,CAAiB,KAAKF,MAAtB,EAA8B9B,KAA9B,CAA1B,CAAnB,EAAoF;MAChF,KAAKmC,YAAL,CAAkB,IAAlB;IACH;;IACD,MAAMK,WAAW,GAAG,KAAKlB,SAAzB,CALO,CAMP;IACA;IACA;IACA;IACA;;IACAmB,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;MACzB,IAAI,KAAKrB,SAAL,IAAkBkB,WAAtB,EAAmC;QAC/B,KAAKT,QAAL,GAAgB,IAAhB;;QACA,KAAKX,eAAL,CAAqBtB,YAArB;MACH;IACJ,CALD;IAMA,KAAK4B,kBAAL,GAA0B,IAA1B;EACH;;EACDzB,kBAAkB,GAAG;IACjBjF,QAAQ,CAAC,KAAKkF,MAAN,EAAc,KAAKjB,QAAnB,CAAR;EACH;;EACDlC,WAAW,GAAG;IACV,IAAI,KAAKgF,QAAT,EAAmB;MACf;MACA;MACAU,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;QACzB,KAAKZ,QAAL,GAAgB,KAAhB;MACH,CAFD;IAGH;;IACD,MAAMa,QAAQ,GAAG,KAAKrB,SAAtB;;IACA,MAAMsB,aAAa,GAAG,KAAKxB,aAAL,CAAmByB,qBAAnB,CAAyC,IAAzC,CAAtB,CATU,CAUV;;;IACA,IAAIF,QAAQ,IAAIC,aAAhB,EAA+B;MAC3BA,aAAa,CAACE,KAAd;IACH;EACJ;EACD;;;EACAC,MAAM,GAAG;IACL,KAAKjB,QAAL,GAAgB,CAAC,KAAKA,QAAtB;EACH;EACD;;;EACAgB,KAAK,GAAG;IACJ,KAAK9D,QAAL,CAAcZ,aAAd,CAA4B0E,KAA5B;EACH;EACD;AACJ;AACA;AACA;;;EACIE,QAAQ,GAAG;IACP,OAAO,KAAKC,KAAL,GAAa,KAAKA,KAAL,CAAW7E,aAAX,CAAyB8E,WAAzB,IAAwC,EAArD,GAA0D,EAAjE;EACH;EACD;;;EACAhD,iBAAiB,GAAG;IAChB,OAAO,KAAKJ,QAAL,IAAiB,KAAKK,aAAtB,IAAuC,KAAKiB,aAAL,CAAmBjB,aAAjE;EACH;;EACDgD,YAAY,GAAG;IACX,IAAI,CAAC,KAAKrD,QAAN,KAAmB,KAAKsB,aAAL,CAAmBe,QAAnB,IAA+B,CAAC,KAAKL,QAAxD,CAAJ,EAAuE;MACnE,KAAKiB,MAAL,GADmE,CAEnE;;MACA,KAAK3B,aAAL,CAAmBgC,gBAAnB,CAAoC,CAAC,IAAD,CAApC;IACH;EACJ;;EACDC,YAAY,GAAG;IACX,KAAKjC,aAAL,CAAmBkC,iBAAnB,CAAqC,IAArC;;IACA,KAAKhC,SAAL,GAAiB,IAAjB;EACH;;EACDiC,WAAW,GAAG;IACV,KAAKnC,aAAL,CAAmBoC,UAAnB;;IACA,KAAKlC,SAAL,GAAiB,KAAjB;EACH;EACD;;;EACA7B,eAAe,GAAG;IACd,OAAO,KAAKT,QAAL,CAAcZ,aAArB;EACH;EACD;;;EACA8D,YAAY,CAACJ,QAAD,EAAW;IACnB,IAAIA,QAAQ,KAAK,KAAKT,SAAtB,EAAiC;MAC7B,OAAO,KAAP;IACH;;IACD,KAAKA,SAAL,GAAiBS,QAAjB;;IACA,IAAIA,QAAJ,EAAc;MACV,KAAKV,aAAL,CAAmBY,eAAnB,CAAmCyB,MAAnC,CAA0C,IAA1C;IACH,CAFD,MAGK;MACD,KAAKrC,aAAL,CAAmBY,eAAnB,CAAmC0B,QAAnC,CAA4C,IAA5C;IACH;;IACD,KAAKnC,cAAL,CAAoBoC,IAApB,CAAyB7B,QAAzB;;IACA,KAAKX,eAAL,CAAqBtB,YAArB;;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACI+D,aAAa,GAAG;IACZ,KAAKzC,eAAL,CAAqBtB,YAArB;EACH;;AAtK0C;;AAwK/CqB,aAAa,CAAClE,IAAd;EAAA,iBAA0GkE,aAA1G,EAlX6FtH,EAkX7F,mBAAyIA,EAAE,CAAC8E,UAA5I,GAlX6F9E,EAkX7F,mBAAmKA,EAAE,CAACwG,iBAAtK,GAlX6FxG,EAkX7F,mBAAoMW,UAAU,CAAC,MAAMsG,gBAAP,CAA9M;AAAA;;AACAK,aAAa,CAACjE,IAAd,kBAnX6FrD,EAmX7F;EAAA,MAA8FsH,aAA9F;EAAA;EAAA;IAAA;MAnX6FtH,EAmX7F,0BAA0hC+E,yBAA1hC;MAnX6F/E,EAmX7F,0BAA2nCiF,uBAA3nC;MAnX6FjF,EAmX7F,0BAA8sCoB,OAA9sC;IAAA;;IAAA;MAAA;;MAnX6FpB,EAmX7F,qBAnX6FA,EAmX7F;MAnX6FA,EAmX7F,qBAnX6FA,EAmX7F;MAnX6FA,EAmX7F,qBAnX6FA,EAmX7F;IAAA;EAAA;EAAA;IAAA;MAnX6FA,EAmX7F;IAAA;;IAAA;MAAA;;MAnX6FA,EAmX7F,qBAnX6FA,EAmX7F;IAAA;EAAA;EAAA,oBAAqX,QAArX;EAAA;EAAA;IAAA;MAnX6FA,EAmX7F;QAAA,OAA8F,kBAA9F;MAAA;QAAA,OAA8F,iBAA9F;MAAA;QAAA,OAA8F,kBAA9F;MAAA;IAAA;;IAAA;MAnX6FA,EAmX7F;MAnX6FA,EAmX7F;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA,WAnX6FA,EAmX7F;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAnX6FA,EAmX7F;MAnX6FA,EAmX4zC,4BAAz5C;MAnX6FA,EAmXg7C,uBAA7gD;MAnX6FA,EAmX+kD,4FAA5qD;MAnX6FA,EAmXuvD,+BAAp1D;MAnX6FA,EAmX0xD,gBAAv3D;MAnX6FA,EAmXmzD,eAAh5D;MAnX6FA,EAmX+zD,mBAA55D;MAnX6FA,EAmX86D,eAA3gE;IAAA;;IAAA;MAnX6FA,EAmXo2C,8EAAj8C;MAnX6FA,EAmXy+C,aAAtkD;MAnX6FA,EAmXy+C,oGAAtkD;MAnX6FA,EAmX0mD,aAAvsD;MAnX6FA,EAmX0mD,+CAAvsD;IAAA;EAAA;EAAA,eAAgkEgB,EAAE,CAACyF,SAAnkE,EAAo0EzF,EAAE,CAACiJ,iBAAv0E,EAAu7EnK,EAAE,CAACoK,IAA17E;EAAA;EAAA;AAAA;;AACA;EAAA,mDApX6FlK,EAoX7F,mBAA2FsH,aAA3F,EAAsH,CAAC;IAC3G9D,IAAI,EAAEtD,SADqG;IAE3GuD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,iBAAZ;MAA+BC,QAAQ,EAAE,eAAzC;MAA0DE,MAAM,EAAE,CAAC,eAAD,CAAlE;MAAqFD,IAAI,EAAE;QACtF,QAAQ,QAD8E;QAEtF,SAAS,mDAF6E;QAGtF,WAAW,gBAH2E;QAItF,UAAU,eAJ4E;QAKtF,WAAW,gBAL2E;QAMtF,kCAAkC,UANoD;QAOtF,qCAAqC,kBAPiD;QAQtF;QACA;QACA;QACA,uBAAuB,qBAX+D;QAYtF;QACA;QACA,sBAAsB,yCAdgE;QAetF,oBAAoB,kBAfkE;QAgBtF,2CAA2C,qCAhB2C;QAiBtF,wBAAwB,UAjB8D;QAkBtF,wBAAwB,UAlB8D;QAmBtF,mBAAmB;MAnBmE,CAA3F;MAoBIE,aAAa,EAAE3D,iBAAiB,CAAC4D,IApBrC;MAoB2CC,eAAe,EAAE5D,uBAAuB,CAAC6D,MApBpF;MAoB4FE,QAAQ,EAAE;IApBtG,CAAD;EAFqG,CAAD,CAAtH,EAuB4B,YAAY;IAAE,OAAO,CAAC;MAAEX,IAAI,EAAExD,EAAE,CAAC8E;IAAX,CAAD,EAA0B;MAAEtB,IAAI,EAAExD,EAAE,CAACwG;IAAX,CAA1B,EAA0D;MAAEhD,IAAI,EAAEyD,gBAAR;MAA0BP,UAAU,EAAE,CAAC;QAC9HlD,IAAI,EAAEjD,MADwH;QAE9HkD,IAAI,EAAE,CAAC9C,UAAU,CAAC,MAAMsG,gBAAP,CAAX;MAFwH,CAAD;IAAtC,CAA1D,CAAP;EAGlB,CA1BxB,EA0B0C;IAAEL,OAAO,EAAE,CAAC;MACtCpD,IAAI,EAAE/C,YADgC;MAEtCgD,IAAI,EAAE,CAACsB,yBAAD;IAFgC,CAAD,CAAX;IAG1B8B,KAAK,EAAE,CAAC;MACRrD,IAAI,EAAE/C,YADE;MAERgD,IAAI,EAAE,CAACwB,uBAAD;IAFE,CAAD,CAHmB;IAM1BoB,MAAM,EAAE,CAAC;MACT7C,IAAI,EAAEhD,eADG;MAETiD,IAAI,EAAE,CAACrC,OAAD,EAAU;QAAEuF,WAAW,EAAE;MAAf,CAAV;IAFG,CAAD,CANkB;IAS1BgB,cAAc,EAAE,CAAC;MACjBnE,IAAI,EAAE3C;IADW,CAAD,CATU;IAW1BwI,KAAK,EAAE,CAAC;MACR7F,IAAI,EAAE1C,SADE;MAER2C,IAAI,EAAE,CAAC,MAAD;IAFE,CAAD,CAXmB;IAc1BmE,gBAAgB,EAAE,CAAC;MACnBpE,IAAI,EAAE9C;IADa,CAAD,CAdQ;IAgB1BoH,KAAK,EAAE,CAAC;MACRtE,IAAI,EAAE9C;IADE,CAAD,CAhBmB;IAkB1ByF,KAAK,EAAE,CAAC;MACR3C,IAAI,EAAE9C;IADE,CAAD,CAlBmB;IAoB1BwF,QAAQ,EAAE,CAAC;MACX1C,IAAI,EAAE9C;IADK,CAAD,CApBgB;IAsB1BwH,QAAQ,EAAE,CAAC;MACX1E,IAAI,EAAE9C;IADK,CAAD;EAtBgB,CA1B1C;AAAA;AAmDA;AACA;AACA;;;AACA,MAAMuG,gBAAN,SAA+BH,qBAA/B,CAAqD;EACjDjE,WAAW,CAACuC,QAAD,EAAWmC,eAAX,EAA4B4C,aAA5B,EAA2C;IAClD;IACA,KAAK/E,QAAL,GAAgBA,QAAhB;IACA,KAAKmC,eAAL,GAAuBA,eAAvB;IACA,KAAK4C,aAAL,GAAqBA,aAArB;IACA,KAAKC,SAAL,GAAiB,IAAjB;IACA,KAAKC,mBAAL,GAA2B,KAA3B;IACA;;IACA,KAAKC,eAAL,GAAuB,IAAI1J,YAAJ,EAAvB;IACA;;IACA,KAAKkH,KAAL,GAAa,QAAb;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKK,WAAL,GAAmB,CAACoC,EAAD,EAAKC,EAAL,KAAYD,EAAE,KAAKC,EAAtC;;IACA,KAAK9E,SAAL,GAAiB,KAAjB;IACA;;IACA,KAAK0C,eAAL,GAAuB,IAAIrG,cAAJ,CAAmB,KAAKqI,SAAxB,CAAvB;IACA;;IACA,KAAKK,SAAL,GAAiB,CAAC,CAAlB;IACA;;IACA,KAAKC,SAAL,GAAkBC,CAAD,IAAO,CAAG,CAA3B;IACA;;;IACA,KAAKlF,UAAL,GAAkB,IAAI/D,OAAJ,EAAlB;IACA;;IACA,KAAKkI,UAAL,GAAkB,MAAM,CAAG,CAA3B;EACH;EACD;;;EACY,IAAR1D,QAAQ,GAAG;IACX,OAAO,KAAKR,SAAZ;EACH;;EACW,IAARQ,QAAQ,CAACC,KAAD,EAAQ;IAChB,KAAKT,SAAL,GAAiBjE,qBAAqB,CAAC0E,KAAD,CAAtC,CADgB,CAEhB;IACA;IACA;IACA;;IACA,KAAKyE,oBAAL;EACH;EACD;;;EACY,IAARrC,QAAQ,GAAG;IACX,OAAO,KAAK6B,SAAZ;EACH;;EACW,IAAR7B,QAAQ,CAACpC,KAAD,EAAQ;IAChB,MAAM6B,QAAQ,GAAGvG,qBAAqB,CAAC0E,KAAD,CAAtC;;IACA,IAAI6B,QAAQ,KAAK,KAAKoC,SAAtB,EAAiC;MAC7B,IAAI,KAAKC,mBAAL,KAA6B,OAAOQ,SAAP,KAAqB,WAArB,IAAoCA,SAAjE,CAAJ,EAAiF;QAC7E,MAAM,IAAIC,KAAJ,CAAU,2EAAV,CAAN;MACH;;MACD,KAAKV,SAAL,GAAiBpC,QAAjB;MACA,KAAKI,eAAL,GAAuB,IAAIrG,cAAJ,CAAmB,KAAKqI,SAAxB,EAAmC,KAAKhC,eAAL,CAAqBF,QAAxD,CAAvB;IACH;EACJ;;EACD9B,kBAAkB,GAAG;IACjB,KAAKiE,mBAAL,GAA2B,IAA3B;IACA,KAAKU,WAAL,GAAmB,IAAIjJ,eAAJ,CAAoB,KAAKuF,OAAzB,EACd2D,QADc,GAEdC,aAFc,GAGdC,cAHc,GAIf;IACA;IALe,CAMdC,aANc,CAMA,MAAM,KANN,EAOdC,uBAPc,CAOU,CAAC,UAAD,CAPV,CAAnB;;IAQA,IAAI,KAAKnD,MAAT,EAAiB;MACb,KAAKoD,qBAAL,CAA2B,KAAKpD,MAAhC;IACH,CAZgB,CAajB;;;IACA,KAAK8C,WAAL,CAAiBO,MAAjB,CAAwBvF,IAAxB,CAA6BpE,SAAS,CAAC,KAAK8D,UAAN,CAAtC,EAAyDO,SAAzD,CAAmE,MAAM;MACrE,KAAKuF,iBAAL;IACH,CAFD,EAdiB,CAiBjB;;;IACA,KAAKlE,OAAL,CAAamE,OAAb,CAAqBzF,IAArB,CAA0BnE,SAAS,CAAC,IAAD,CAAnC,EAA2CD,SAAS,CAAC,KAAK8D,UAAN,CAApD,EAAuEO,SAAvE,CAAiF,MAAM;MACnF,KAAKyF,eAAL;IACH,CAFD,EAlBiB,CAqBjB;;IACA,KAAKrD,eAAL,CAAqBsD,OAArB,CAA6B3F,IAA7B,CAAkCpE,SAAS,CAAC,KAAK8D,UAAN,CAA3C,EAA8DO,SAA9D,CAAwE2F,KAAK,IAAI;MAC7E,IAAIA,KAAK,CAACC,KAAV,EAAiB;QACb,KAAK,IAAIC,IAAT,IAAiBF,KAAK,CAACC,KAAvB,EAA8B;UAC1BC,IAAI,CAAC3D,QAAL,GAAgB,IAAhB;QACH;MACJ;;MACD,IAAIyD,KAAK,CAACG,OAAV,EAAmB;QACf,KAAK,IAAID,IAAT,IAAiBF,KAAK,CAACG,OAAvB,EAAgC;UAC5BD,IAAI,CAAC3D,QAAL,GAAgB,KAAhB;QACH;MACJ;IACJ,CAXD;;IAYA,KAAKiC,aAAL,CACK4B,OADL,CACa,KAAK3G,QADlB,EAEKW,IAFL,CAEUpE,SAAS,CAAC,KAAK8D,UAAN,CAFnB,EAGKO,SAHL,CAGegG,MAAM,IAAI;MACrB,IAAIA,MAAM,KAAK,UAAX,IAAyBA,MAAM,KAAK,SAAxC,EAAmD;QAC/C,IAAIC,OAAO,GAAG,CAAd;;QACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK7E,OAAL,CAAa8E,MAAjC,EAAyCD,CAAC,EAA1C,EAA8C;UAC1C,IAAI,KAAK7E,OAAL,CAAa+E,GAAb,CAAiBF,CAAjB,GAAqBhE,QAAzB,EAAmC;YAC/B+D,OAAO,GAAGC,CAAV;YACA;UACH;QACJ;;QACD,KAAKnB,WAAL,CAAiBsB,aAAjB,CAA+BJ,OAA/B;MACH;IACJ,CAdD;EAeH;;EACDjJ,WAAW,CAACwI,OAAD,EAAU;IACjB,MAAMc,oBAAoB,GAAGd,OAAO,CAAC,eAAD,CAApC;IACA,MAAMe,YAAY,GAAGf,OAAO,CAAC,OAAD,CAA5B;;IACA,IAAKc,oBAAoB,IAAI,CAACA,oBAAoB,CAACE,WAA/C,IACCD,YAAY,IAAI,CAACA,YAAY,CAACC,WADnC,EACiD;MAC7C,KAAK5B,oBAAL;IACH;EACJ;;EACD1H,WAAW,GAAG;IACV,KAAKiH,aAAL,CAAmBsC,cAAnB,CAAkC,KAAKrH,QAAvC;;IACA,KAAKK,UAAL,CAAgBxC,IAAhB;;IACA,KAAKwC,UAAL,CAAgBtC,QAAhB;;IACA,KAAKuJ,YAAL,GAAoB,IAApB;EACH;EACD;;;EACAxD,KAAK,CAAC7B,OAAD,EAAU;IACX,KAAKjC,QAAL,CAAcZ,aAAd,CAA4B0E,KAA5B,CAAkC7B,OAAlC;EACH;EACD;;;EACAsF,SAAS,GAAG;IACR,OAAO,KAAKC,sBAAL,CAA4B,IAA5B,CAAP;EACH;EACD;;;EACAC,WAAW,GAAG;IACV,OAAO,KAAKD,sBAAL,CAA4B,KAA5B,CAAP;EACH;EACD;;;EACAlD,iBAAiB,CAACoD,MAAD,EAAS;IACtB,KAAK/B,WAAL,CAAiBgC,gBAAjB,CAAkCD,MAAlC;EACH;EACD;AACJ;AACA;AACA;;;EACI7D,qBAAqB,CAAC6D,MAAD,EAAS;IAC1B,MAAME,WAAW,GAAG,KAAKC,eAAL,CAAqBH,MAArB,CAApB;;IACA,IAAIE,WAAW,GAAG,CAAC,CAAf,IAAoB,KAAKjC,WAAL,CAAiBmC,eAAjB,KAAqCF,WAA7D,EAA0E;MACtE;MACA,IAAIA,WAAW,GAAG,CAAlB,EAAqB;QACjB,KAAKjC,WAAL,CAAiBgC,gBAAjB,CAAkCC,WAAW,GAAG,CAAhD;MACH,CAFD,MAGK,IAAIA,WAAW,KAAK,CAAhB,IAAqB,KAAK3F,OAAL,CAAa8E,MAAb,GAAsB,CAA/C,EAAkD;QACnD,KAAKpB,WAAL,CAAiBgC,gBAAjB,CAAkCI,IAAI,CAACC,GAAL,CAASJ,WAAW,GAAG,CAAvB,EAA0B,KAAK3F,OAAL,CAAa8E,MAAb,GAAsB,CAAhD,CAAlC;MACH;IACJ;;IACD,OAAO,KAAKpB,WAAL,CAAiBsC,UAAxB;EACH;EACD;;;EACAC,QAAQ,CAAC3B,KAAD,EAAQ;IACZ,MAAM4B,OAAO,GAAG5B,KAAK,CAAC4B,OAAtB;IACA,MAAMC,OAAO,GAAG,KAAKzC,WAArB;IACA,MAAM0C,kBAAkB,GAAGD,OAAO,CAACN,eAAnC;IACA,MAAMQ,WAAW,GAAG1L,cAAc,CAAC2J,KAAD,CAAlC;;IACA,QAAQ4B,OAAR;MACI,KAAKpL,KAAL;MACA,KAAKD,KAAL;QACI,IAAI,CAACwL,WAAD,IAAgB,CAACF,OAAO,CAACG,QAAR,EAArB,EAAyC;UACrC,KAAKC,oBAAL,GADqC,CAErC;;;UACAjC,KAAK,CAACkC,cAAN;QACH;;QACD;;MACJ;QACI;QACA,IAAIN,OAAO,KAAKtL,CAAZ,IACA,KAAKsG,QADL,IAEAvG,cAAc,CAAC2J,KAAD,EAAQ,SAAR,CAFd,IAGA,CAAC6B,OAAO,CAACG,QAAR,EAHL,EAGyB;UACrB,MAAMG,YAAY,GAAG,KAAKzG,OAAL,CAAaqB,IAAb,CAAkBoE,MAAM,IAAI,CAACA,MAAM,CAAC5G,QAAR,IAAoB,CAAC4G,MAAM,CAAC5E,QAAxD,CAArB;;UACA,KAAK0E,sBAAL,CAA4BkB,YAA5B,EAA0C,IAA1C,EAAgD,IAAhD;;UACAnC,KAAK,CAACkC,cAAN;QACH,CAPD,MAQK;UACDL,OAAO,CAACO,SAAR,CAAkBpC,KAAlB;QACH;;IArBT;;IAuBA,IAAI,KAAKpD,QAAL,KACCgF,OAAO,KAAKnL,QAAZ,IAAwBmL,OAAO,KAAKlL,UADrC,KAEAsJ,KAAK,CAACqC,QAFN,IAGAR,OAAO,CAACN,eAAR,KAA4BO,kBAHhC,EAGoD;MAChD,KAAKG,oBAAL;IACH;EACJ;EACD;;;EACApF,kBAAkB,GAAG;IACjB;IACA;IACA;IACA,IAAI,KAAKnB,OAAL,IAAgB,CAAC,KAAKqF,YAA1B,EAAwC;MACpC,MAAMvG,KAAK,GAAG,KAAK8H,wBAAL,EAAd;;MACA,KAAKvD,SAAL,CAAevE,KAAf;;MACA,KAAK8B,MAAL,GAAc9B,KAAd;IACH;EACJ;EACD;;;EACAqD,gBAAgB,CAACnC,OAAD,EAAU;IACtB,KAAKiD,eAAL,CAAqBP,IAArB,CAA0B,IAAI5C,sBAAJ,CAA2B,IAA3B,EAAiCE,OAAjC,CAA1B;EACH;EACD;;;EACA6G,UAAU,CAACC,MAAD,EAAS;IACf,KAAKlG,MAAL,GAAckG,MAAd;;IACA,IAAI,KAAK9G,OAAT,EAAkB;MACd,KAAKgE,qBAAL,CAA2B8C,MAAM,IAAI,EAArC;IACH;EACJ;EACD;;;EACAC,gBAAgB,CAACC,UAAD,EAAa;IACzB,KAAKnI,QAAL,GAAgBmI,UAAhB;EACH;EACD;;;EACAC,gBAAgB,CAACC,EAAD,EAAK;IACjB,KAAK7D,SAAL,GAAiB6D,EAAjB;EACH;EACD;;;EACAC,iBAAiB,CAACD,EAAD,EAAK;IAClB,KAAK3E,UAAL,GAAkB2E,EAAlB;EACH;EACD;;;EACAlD,qBAAqB,CAAC8C,MAAD,EAAS;IAC1B,KAAK9G,OAAL,CAAaoH,OAAb,CAAqB3B,MAAM,IAAIA,MAAM,CAACxE,YAAP,CAAoB,KAApB,CAA/B;IACA6F,MAAM,CAACM,OAAP,CAAetI,KAAK,IAAI;MACpB,MAAMuI,mBAAmB,GAAG,KAAKrH,OAAL,CAAasH,IAAb,CAAkB7B,MAAM,IAAI;QACpD;QACA;QACA,OAAOA,MAAM,CAAC5E,QAAP,GAAkB,KAAlB,GAA0B,KAAKC,WAAL,CAAiB2E,MAAM,CAAC3G,KAAxB,EAA+BA,KAA/B,CAAjC;MACH,CAJ2B,CAA5B;;MAKA,IAAIuI,mBAAJ,EAAyB;QACrBA,mBAAmB,CAACpG,YAApB,CAAiC,IAAjC;MACH;IACJ,CATD;EAUH;EACD;;;EACA2F,wBAAwB,GAAG;IACvB,OAAO,KAAK5G,OAAL,CAAauH,MAAb,CAAoB9B,MAAM,IAAIA,MAAM,CAAC5E,QAArC,EAA+C2G,GAA/C,CAAmD/B,MAAM,IAAIA,MAAM,CAAC3G,KAApE,CAAP;EACH;EACD;;;EACAyH,oBAAoB,GAAG;IACnB,IAAIkB,YAAY,GAAG,KAAK/D,WAAL,CAAiBmC,eAApC;;IACA,IAAI4B,YAAY,IAAI,IAAhB,IAAwB,KAAKC,aAAL,CAAmBD,YAAnB,CAA5B,EAA8D;MAC1D,IAAIE,aAAa,GAAG,KAAK3H,OAAL,CAAa4H,OAAb,GAAuBH,YAAvB,CAApB;;MACA,IAAIE,aAAa,IAAI,CAACA,aAAa,CAAC9I,QAAhC,KAA6C,KAAKkE,SAAL,IAAkB,CAAC4E,aAAa,CAAC9G,QAA9E,CAAJ,EAA6F;QACzF8G,aAAa,CAAC7F,MAAd,GADyF,CAEzF;QACA;;QACA,KAAKK,gBAAL,CAAsB,CAACwF,aAAD,CAAtB;MACH;IACJ;EACJ;EACD;AACJ;AACA;AACA;;;EACIpC,sBAAsB,CAACvE,UAAD,EAAa6G,YAAb,EAA2BC,WAA3B,EAAwC;IAC1D;IACA;IACA,MAAMC,cAAc,GAAG,EAAvB;IACA,KAAK/H,OAAL,CAAaoH,OAAb,CAAqB3B,MAAM,IAAI;MAC3B,IAAI,CAAC,CAACoC,YAAD,IAAiB,CAACpC,MAAM,CAAC5G,QAA1B,KAAuC4G,MAAM,CAACxE,YAAP,CAAoBD,UAApB,CAA3C,EAA4E;QACxE+G,cAAc,CAACC,IAAf,CAAoBvC,MAApB;MACH;IACJ,CAJD;;IAKA,IAAIsC,cAAc,CAACjD,MAAnB,EAA2B;MACvB,KAAK3D,kBAAL;;MACA,IAAI2G,WAAJ,EAAiB;QACb,KAAK3F,gBAAL,CAAsB4F,cAAtB;MACH;IACJ;;IACD,OAAOA,cAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIL,aAAa,CAACO,KAAD,EAAQ;IACjB,OAAOA,KAAK,IAAI,CAAT,IAAcA,KAAK,GAAG,KAAKjI,OAAL,CAAa8E,MAA1C;EACH;EACD;;;EACAc,eAAe,CAACH,MAAD,EAAS;IACpB,OAAO,KAAKzF,OAAL,CAAa4H,OAAb,GAAuBM,OAAvB,CAA+BzC,MAA/B,CAAP;EACH;EACD;;;EACAlC,oBAAoB,GAAG;IACnB,IAAI,KAAKvD,OAAT,EAAkB;MACd,KAAKA,OAAL,CAAaoH,OAAb,CAAqB3B,MAAM,IAAIA,MAAM,CAAC9C,aAAP,EAA/B;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;;;EACIuB,iBAAiB,GAAG;IAChB,KAAKd,SAAL,GAAiB,CAAC,CAAlB;IACA+E,UAAU,CAAC,MAAM;MACb,KAAK/E,SAAL,GAAiB,CAAjB;;MACA,KAAKlD,eAAL,CAAqBtB,YAArB;IACH,CAHS,CAAV;EAIH;EACD;;;EACAwF,eAAe,GAAG;IACd,KAAKhB,SAAL,GAAiB,KAAKpD,OAAL,CAAa8E,MAAb,KAAwB,CAAxB,GAA4B,CAAC,CAA7B,GAAiC,CAAlD;EACH;;AApTgD;;AAsTrDlF,gBAAgB,CAAC7D,IAAjB;EAAA,iBAA6G6D,gBAA7G,EAhuB6FjH,EAguB7F,mBAA+IA,EAAE,CAAC8E,UAAlJ,GAhuB6F9E,EAguB7F,mBAAyKA,EAAE,CAACwG,iBAA5K,GAhuB6FxG,EAguB7F,mBAA0M6B,EAAE,CAAC4N,YAA7M;AAAA;;AACAxI,gBAAgB,CAAC5D,IAAjB,kBAjuB6FrD,EAiuB7F;EAAA,MAAiGiH,gBAAjG;EAAA;EAAA;IAAA;MAjuB6FjH,EAiuB7F,0BAA2rBsH,aAA3rB;IAAA;;IAAA;MAAA;;MAjuB6FtH,EAiuB7F,qBAjuB6FA,EAiuB7F;IAAA;EAAA;EAAA,oBAAsW,SAAtW;EAAA;EAAA;IAAA;MAjuB6FA,EAiuB7F;QAAA,OAAiG,oBAAjG;MAAA;IAAA;;IAAA;MAjuB6FA,EAiuB7F;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA,WAjuB6FA,EAiuB7F,oBAAsmB,CAACgH,iCAAD,CAAtmB,GAjuB6FhH,EAiuB7F,6BAjuB6FA,EAiuB7F;EAAA;EAAA;EAAA;EAAA;IAAA;MAjuB6FA,EAiuB7F;MAjuB6FA,EAiuBwuB,gBAAr0B;IAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDAluB6FA,EAkuB7F,mBAA2FiH,gBAA3F,EAAyH,CAAC;IAC9GzD,IAAI,EAAEtD,SADwG;IAE9GuD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,oBAAZ;MAAkCC,QAAQ,EAAE,kBAA5C;MAAgEE,MAAM,EAAE,CAAC,eAAD,CAAxE;MAA2FD,IAAI,EAAE;QAC5F,QAAQ,SADoF;QAE5F,SAAS,kCAFmF;QAG5F,aAAa,kBAH+E;QAI5F,+BAA+B,UAJ6D;QAK5F,wBAAwB,qBALoE;QAM5F,mBAAmB;MANyE,CAAjG;MAOIO,QAAQ,EAAE,2BAPd;MAO2CL,aAAa,EAAE3D,iBAAiB,CAAC4D,IAP5E;MAOkFG,SAAS,EAAE,CAAC8C,iCAAD,CAP7F;MAOkIhD,eAAe,EAAE5D,uBAAuB,CAAC6D,MAP3K;MAOmLG,MAAM,EAAE,CAAC,kzaAAD;IAP3L,CAAD;EAFwG,CAAD,CAAzH,EAU4B,YAAY;IAAE,OAAO,CAAC;MAAEZ,IAAI,EAAExD,EAAE,CAAC8E;IAAX,CAAD,EAA0B;MAAEtB,IAAI,EAAExD,EAAE,CAACwG;IAAX,CAA1B,EAA0D;MAAEhD,IAAI,EAAE3B,EAAE,CAAC4N;IAAX,CAA1D,CAAP;EAA8F,CAVxI,EAU0J;IAAEpI,OAAO,EAAE,CAAC;MACtJ7D,IAAI,EAAEhD,eADgJ;MAEtJiD,IAAI,EAAE,CAAC6D,aAAD,EAAgB;QAAEX,WAAW,EAAE;MAAf,CAAhB;IAFgJ,CAAD,CAAX;IAG1I2D,eAAe,EAAE,CAAC;MAClB9G,IAAI,EAAE3C;IADY,CAAD,CAHyH;IAK1IiH,KAAK,EAAE,CAAC;MACRtE,IAAI,EAAE9C;IADE,CAAD,CALmI;IAO1IyH,WAAW,EAAE,CAAC;MACd3E,IAAI,EAAE9C;IADQ,CAAD,CAP6H;IAS1IwF,QAAQ,EAAE,CAAC;MACX1C,IAAI,EAAE9C;IADK,CAAD,CATgI;IAW1I6H,QAAQ,EAAE,CAAC;MACX/E,IAAI,EAAE9C;IADK,CAAD;EAXgI,CAV1J;AAAA;AAyBA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMgP,aAAN,CAAoB;;AAEpBA,aAAa,CAACtM,IAAd;EAAA,iBAA0GsM,aAA1G;AAAA;;AACAA,aAAa,CAACC,IAAd,kBArwB6F3P,EAqwB7F;EAAA,MAA2G0P,aAA3G;EAAA,eAAyIrL,OAAzI,EACQzB,UADR,EAEQuC,WAFR,EAGQJ,yBAHR,EAIQE,uBAJR,EAKQC,4BALR,EAMQ+B,gBANR,EAOQK,aAPR;EAAA,UAOkCjG,aAPlC,EAOiDC,eAPjD,EAOkEC,eAPlE,EAOmFC,uBAPnF,EAO4GzB,YAP5G;EAAA,UAOqIsE,OAPrI,EAQQzB,UARR,EASQuC,WATR,EAUQJ,yBAVR,EAWQ1D,aAXR,EAYQE,eAZR,EAaQ0D,uBAbR,EAcQC,4BAdR,EAeQ1D,uBAfR,EAgBQyF,gBAhBR,EAiBQK,aAjBR,EAkBQ/E,gBAlBR;AAAA;AAmBAmN,aAAa,CAACE,IAAd,kBAxxB6F5P,EAwxB7F;EAAA,UAAoIqB,aAApI,EAAmJC,eAAnJ,EAAoKC,eAApK,EAAqLC,uBAArL,EAA8MzB,YAA9M,EAA4NsB,aAA5N,EACQE,eADR,EAEQC,uBAFR,EAGQe,gBAHR;AAAA;;AAIA;EAAA,mDA5xB6FvC,EA4xB7F,mBAA2F0P,aAA3F,EAAsH,CAAC;IAC3GlM,IAAI,EAAEzC,QADqG;IAE3G0C,IAAI,EAAE,CAAC;MACCoM,OAAO,EAAE,CAACxO,aAAD,EAAgBC,eAAhB,EAAiCC,eAAjC,EAAkDC,uBAAlD,EAA2EzB,YAA3E,CADV;MAEC+P,OAAO,EAAE,CACLzL,OADK,EAELzB,UAFK,EAGLuC,WAHK,EAILJ,yBAJK,EAKL1D,aALK,EAMLE,eANK,EAOL0D,uBAPK,EAQLC,4BARK,EASL1D,uBATK,EAULyF,gBAVK,EAWLK,aAXK,EAYL/E,gBAZK,CAFV;MAgBCwN,YAAY,EAAE,CACV1L,OADU,EAEVzB,UAFU,EAGVuC,WAHU,EAIVJ,yBAJU,EAKVE,uBALU,EAMVC,4BANU,EAOV+B,gBAPU,EAQVK,aARU;IAhBf,CAAD;EAFqG,CAAD,CAAtH;AAAA;AA+BA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAAS5E,QAAT,EAAmBC,YAAnB,EAAiCqE,iCAAjC,EAAoE3C,OAApE,EAA6EU,yBAA7E,EAAwGE,uBAAxG,EAAiIE,WAAjI,EAA8IuK,aAA9I,EAA6JpI,aAA7J,EAA4KpC,4BAA5K,EAA0MtC,UAA1M,EAAsNqE,gBAAtN,EAAwOE,sBAAxO"}, "metadata": {}, "sourceType": "module"}