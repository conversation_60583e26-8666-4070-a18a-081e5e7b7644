{"ast": null, "code": "import { fromArray } from './fromArray';\nimport { isArray } from '../util/isArray';\nimport { Subscriber } from '../Subscriber';\nimport { iterator as Symbol_iterator } from '../../internal/symbol/iterator';\nimport { SimpleOuterSubscriber, SimpleInnerSubscriber, innerSubscribe } from '../innerSubscribe';\nexport function zip(...observables) {\n  const resultSelector = observables[observables.length - 1];\n\n  if (typeof resultSelector === 'function') {\n    observables.pop();\n  }\n\n  return fromArray(observables, undefined).lift(new ZipOperator(resultSelector));\n}\nexport class ZipOperator {\n  constructor(resultSelector) {\n    this.resultSelector = resultSelector;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new ZipSubscriber(subscriber, this.resultSelector));\n  }\n\n}\nexport class ZipSubscriber extends Subscriber {\n  constructor(destination, resultSelector, values = Object.create(null)) {\n    super(destination);\n    this.resultSelector = resultSelector;\n    this.iterators = [];\n    this.active = 0;\n    this.resultSelector = typeof resultSelector === 'function' ? resultSelector : undefined;\n  }\n\n  _next(value) {\n    const iterators = this.iterators;\n\n    if (isArray(value)) {\n      iterators.push(new StaticArrayIterator(value));\n    } else if (typeof value[Symbol_iterator] === 'function') {\n      iterators.push(new StaticIterator(value[Symbol_iterator]()));\n    } else {\n      iterators.push(new ZipBufferIterator(this.destination, this, value));\n    }\n  }\n\n  _complete() {\n    const iterators = this.iterators;\n    const len = iterators.length;\n    this.unsubscribe();\n\n    if (len === 0) {\n      this.destination.complete();\n      return;\n    }\n\n    this.active = len;\n\n    for (let i = 0; i < len; i++) {\n      let iterator = iterators[i];\n\n      if (iterator.stillUnsubscribed) {\n        const destination = this.destination;\n        destination.add(iterator.subscribe());\n      } else {\n        this.active--;\n      }\n    }\n  }\n\n  notifyInactive() {\n    this.active--;\n\n    if (this.active === 0) {\n      this.destination.complete();\n    }\n  }\n\n  checkIterators() {\n    const iterators = this.iterators;\n    const len = iterators.length;\n    const destination = this.destination;\n\n    for (let i = 0; i < len; i++) {\n      let iterator = iterators[i];\n\n      if (typeof iterator.hasValue === 'function' && !iterator.hasValue()) {\n        return;\n      }\n    }\n\n    let shouldComplete = false;\n    const args = [];\n\n    for (let i = 0; i < len; i++) {\n      let iterator = iterators[i];\n      let result = iterator.next();\n\n      if (iterator.hasCompleted()) {\n        shouldComplete = true;\n      }\n\n      if (result.done) {\n        destination.complete();\n        return;\n      }\n\n      args.push(result.value);\n    }\n\n    if (this.resultSelector) {\n      this._tryresultSelector(args);\n    } else {\n      destination.next(args);\n    }\n\n    if (shouldComplete) {\n      destination.complete();\n    }\n  }\n\n  _tryresultSelector(args) {\n    let result;\n\n    try {\n      result = this.resultSelector.apply(this, args);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n\n    this.destination.next(result);\n  }\n\n}\n\nclass StaticIterator {\n  constructor(iterator) {\n    this.iterator = iterator;\n    this.nextResult = iterator.next();\n  }\n\n  hasValue() {\n    return true;\n  }\n\n  next() {\n    const result = this.nextResult;\n    this.nextResult = this.iterator.next();\n    return result;\n  }\n\n  hasCompleted() {\n    const nextResult = this.nextResult;\n    return Boolean(nextResult && nextResult.done);\n  }\n\n}\n\nclass StaticArrayIterator {\n  constructor(array) {\n    this.array = array;\n    this.index = 0;\n    this.length = 0;\n    this.length = array.length;\n  }\n\n  [Symbol_iterator]() {\n    return this;\n  }\n\n  next(value) {\n    const i = this.index++;\n    const array = this.array;\n    return i < this.length ? {\n      value: array[i],\n      done: false\n    } : {\n      value: null,\n      done: true\n    };\n  }\n\n  hasValue() {\n    return this.array.length > this.index;\n  }\n\n  hasCompleted() {\n    return this.array.length === this.index;\n  }\n\n}\n\nclass ZipBufferIterator extends SimpleOuterSubscriber {\n  constructor(destination, parent, observable) {\n    super(destination);\n    this.parent = parent;\n    this.observable = observable;\n    this.stillUnsubscribed = true;\n    this.buffer = [];\n    this.isComplete = false;\n  }\n\n  [Symbol_iterator]() {\n    return this;\n  }\n\n  next() {\n    const buffer = this.buffer;\n\n    if (buffer.length === 0 && this.isComplete) {\n      return {\n        value: null,\n        done: true\n      };\n    } else {\n      return {\n        value: buffer.shift(),\n        done: false\n      };\n    }\n  }\n\n  hasValue() {\n    return this.buffer.length > 0;\n  }\n\n  hasCompleted() {\n    return this.buffer.length === 0 && this.isComplete;\n  }\n\n  notifyComplete() {\n    if (this.buffer.length > 0) {\n      this.isComplete = true;\n      this.parent.notifyInactive();\n    } else {\n      this.destination.complete();\n    }\n  }\n\n  notifyNext(innerValue) {\n    this.buffer.push(innerValue);\n    this.parent.checkIterators();\n  }\n\n  subscribe() {\n    return innerSubscribe(this.observable, new SimpleInnerSubscriber(this));\n  }\n\n}", "map": {"version": 3, "names": ["fromArray", "isArray", "Subscriber", "iterator", "Symbol_iterator", "SimpleOuterSubscriber", "SimpleInnerSubscriber", "innerSubscribe", "zip", "observables", "resultSelector", "length", "pop", "undefined", "lift", "ZipOperator", "constructor", "call", "subscriber", "source", "subscribe", "ZipSubscriber", "destination", "values", "Object", "create", "iterators", "active", "_next", "value", "push", "StaticArrayIterator", "StaticIterator", "ZipBufferIterator", "_complete", "len", "unsubscribe", "complete", "i", "stillUnsubscribed", "add", "notifyInactive", "checkIterators", "hasValue", "shouldComplete", "args", "result", "next", "hasCompleted", "done", "_tryresultSelector", "apply", "err", "error", "nextResult", "Boolean", "array", "index", "parent", "observable", "buffer", "isComplete", "shift", "notifyComplete", "notifyNext", "innerValue"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/zip.js"], "sourcesContent": ["import { fromArray } from './fromArray';\nimport { isArray } from '../util/isArray';\nimport { Subscriber } from '../Subscriber';\nimport { iterator as Symbol_iterator } from '../../internal/symbol/iterator';\nimport { SimpleOuterSubscriber, SimpleInnerSubscriber, innerSubscribe } from '../innerSubscribe';\nexport function zip(...observables) {\n    const resultSelector = observables[observables.length - 1];\n    if (typeof resultSelector === 'function') {\n        observables.pop();\n    }\n    return fromArray(observables, undefined).lift(new ZipOperator(resultSelector));\n}\nexport class ZipOperator {\n    constructor(resultSelector) {\n        this.resultSelector = resultSelector;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new ZipSubscriber(subscriber, this.resultSelector));\n    }\n}\nexport class ZipSubscriber extends Subscriber {\n    constructor(destination, resultSelector, values = Object.create(null)) {\n        super(destination);\n        this.resultSelector = resultSelector;\n        this.iterators = [];\n        this.active = 0;\n        this.resultSelector = (typeof resultSelector === 'function') ? resultSelector : undefined;\n    }\n    _next(value) {\n        const iterators = this.iterators;\n        if (isArray(value)) {\n            iterators.push(new StaticArrayIterator(value));\n        }\n        else if (typeof value[Symbol_iterator] === 'function') {\n            iterators.push(new StaticIterator(value[Symbol_iterator]()));\n        }\n        else {\n            iterators.push(new ZipBufferIterator(this.destination, this, value));\n        }\n    }\n    _complete() {\n        const iterators = this.iterators;\n        const len = iterators.length;\n        this.unsubscribe();\n        if (len === 0) {\n            this.destination.complete();\n            return;\n        }\n        this.active = len;\n        for (let i = 0; i < len; i++) {\n            let iterator = iterators[i];\n            if (iterator.stillUnsubscribed) {\n                const destination = this.destination;\n                destination.add(iterator.subscribe());\n            }\n            else {\n                this.active--;\n            }\n        }\n    }\n    notifyInactive() {\n        this.active--;\n        if (this.active === 0) {\n            this.destination.complete();\n        }\n    }\n    checkIterators() {\n        const iterators = this.iterators;\n        const len = iterators.length;\n        const destination = this.destination;\n        for (let i = 0; i < len; i++) {\n            let iterator = iterators[i];\n            if (typeof iterator.hasValue === 'function' && !iterator.hasValue()) {\n                return;\n            }\n        }\n        let shouldComplete = false;\n        const args = [];\n        for (let i = 0; i < len; i++) {\n            let iterator = iterators[i];\n            let result = iterator.next();\n            if (iterator.hasCompleted()) {\n                shouldComplete = true;\n            }\n            if (result.done) {\n                destination.complete();\n                return;\n            }\n            args.push(result.value);\n        }\n        if (this.resultSelector) {\n            this._tryresultSelector(args);\n        }\n        else {\n            destination.next(args);\n        }\n        if (shouldComplete) {\n            destination.complete();\n        }\n    }\n    _tryresultSelector(args) {\n        let result;\n        try {\n            result = this.resultSelector.apply(this, args);\n        }\n        catch (err) {\n            this.destination.error(err);\n            return;\n        }\n        this.destination.next(result);\n    }\n}\nclass StaticIterator {\n    constructor(iterator) {\n        this.iterator = iterator;\n        this.nextResult = iterator.next();\n    }\n    hasValue() {\n        return true;\n    }\n    next() {\n        const result = this.nextResult;\n        this.nextResult = this.iterator.next();\n        return result;\n    }\n    hasCompleted() {\n        const nextResult = this.nextResult;\n        return Boolean(nextResult && nextResult.done);\n    }\n}\nclass StaticArrayIterator {\n    constructor(array) {\n        this.array = array;\n        this.index = 0;\n        this.length = 0;\n        this.length = array.length;\n    }\n    [Symbol_iterator]() {\n        return this;\n    }\n    next(value) {\n        const i = this.index++;\n        const array = this.array;\n        return i < this.length ? { value: array[i], done: false } : { value: null, done: true };\n    }\n    hasValue() {\n        return this.array.length > this.index;\n    }\n    hasCompleted() {\n        return this.array.length === this.index;\n    }\n}\nclass ZipBufferIterator extends SimpleOuterSubscriber {\n    constructor(destination, parent, observable) {\n        super(destination);\n        this.parent = parent;\n        this.observable = observable;\n        this.stillUnsubscribed = true;\n        this.buffer = [];\n        this.isComplete = false;\n    }\n    [Symbol_iterator]() {\n        return this;\n    }\n    next() {\n        const buffer = this.buffer;\n        if (buffer.length === 0 && this.isComplete) {\n            return { value: null, done: true };\n        }\n        else {\n            return { value: buffer.shift(), done: false };\n        }\n    }\n    hasValue() {\n        return this.buffer.length > 0;\n    }\n    hasCompleted() {\n        return this.buffer.length === 0 && this.isComplete;\n    }\n    notifyComplete() {\n        if (this.buffer.length > 0) {\n            this.isComplete = true;\n            this.parent.notifyInactive();\n        }\n        else {\n            this.destination.complete();\n        }\n    }\n    notifyNext(innerValue) {\n        this.buffer.push(innerValue);\n        this.parent.checkIterators();\n    }\n    subscribe() {\n        return innerSubscribe(this.observable, new SimpleInnerSubscriber(this));\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAT,QAA0B,aAA1B;AACA,SAASC,OAAT,QAAwB,iBAAxB;AACA,SAASC,UAAT,QAA2B,eAA3B;AACA,SAASC,QAAQ,IAAIC,eAArB,QAA4C,gCAA5C;AACA,SAASC,qBAAT,EAAgCC,qBAAhC,EAAuDC,cAAvD,QAA6E,mBAA7E;AACA,OAAO,SAASC,GAAT,CAAa,GAAGC,WAAhB,EAA6B;EAChC,MAAMC,cAAc,GAAGD,WAAW,CAACA,WAAW,CAACE,MAAZ,GAAqB,CAAtB,CAAlC;;EACA,IAAI,OAAOD,cAAP,KAA0B,UAA9B,EAA0C;IACtCD,WAAW,CAACG,GAAZ;EACH;;EACD,OAAOZ,SAAS,CAACS,WAAD,EAAcI,SAAd,CAAT,CAAkCC,IAAlC,CAAuC,IAAIC,WAAJ,CAAgBL,cAAhB,CAAvC,CAAP;AACH;AACD,OAAO,MAAMK,WAAN,CAAkB;EACrBC,WAAW,CAACN,cAAD,EAAiB;IACxB,KAAKA,cAAL,GAAsBA,cAAtB;EACH;;EACDO,IAAI,CAACC,UAAD,EAAaC,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACC,SAAP,CAAiB,IAAIC,aAAJ,CAAkBH,UAAlB,EAA8B,KAAKR,cAAnC,CAAjB,CAAP;EACH;;AANoB;AAQzB,OAAO,MAAMW,aAAN,SAA4BnB,UAA5B,CAAuC;EAC1Cc,WAAW,CAACM,WAAD,EAAcZ,cAAd,EAA8Ba,MAAM,GAAGC,MAAM,CAACC,MAAP,CAAc,IAAd,CAAvC,EAA4D;IACnE,MAAMH,WAAN;IACA,KAAKZ,cAAL,GAAsBA,cAAtB;IACA,KAAKgB,SAAL,GAAiB,EAAjB;IACA,KAAKC,MAAL,GAAc,CAAd;IACA,KAAKjB,cAAL,GAAuB,OAAOA,cAAP,KAA0B,UAA3B,GAAyCA,cAAzC,GAA0DG,SAAhF;EACH;;EACDe,KAAK,CAACC,KAAD,EAAQ;IACT,MAAMH,SAAS,GAAG,KAAKA,SAAvB;;IACA,IAAIzB,OAAO,CAAC4B,KAAD,CAAX,EAAoB;MAChBH,SAAS,CAACI,IAAV,CAAe,IAAIC,mBAAJ,CAAwBF,KAAxB,CAAf;IACH,CAFD,MAGK,IAAI,OAAOA,KAAK,CAACzB,eAAD,CAAZ,KAAkC,UAAtC,EAAkD;MACnDsB,SAAS,CAACI,IAAV,CAAe,IAAIE,cAAJ,CAAmBH,KAAK,CAACzB,eAAD,CAAL,EAAnB,CAAf;IACH,CAFI,MAGA;MACDsB,SAAS,CAACI,IAAV,CAAe,IAAIG,iBAAJ,CAAsB,KAAKX,WAA3B,EAAwC,IAAxC,EAA8CO,KAA9C,CAAf;IACH;EACJ;;EACDK,SAAS,GAAG;IACR,MAAMR,SAAS,GAAG,KAAKA,SAAvB;IACA,MAAMS,GAAG,GAAGT,SAAS,CAACf,MAAtB;IACA,KAAKyB,WAAL;;IACA,IAAID,GAAG,KAAK,CAAZ,EAAe;MACX,KAAKb,WAAL,CAAiBe,QAAjB;MACA;IACH;;IACD,KAAKV,MAAL,GAAcQ,GAAd;;IACA,KAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,GAApB,EAAyBG,CAAC,EAA1B,EAA8B;MAC1B,IAAInC,QAAQ,GAAGuB,SAAS,CAACY,CAAD,CAAxB;;MACA,IAAInC,QAAQ,CAACoC,iBAAb,EAAgC;QAC5B,MAAMjB,WAAW,GAAG,KAAKA,WAAzB;QACAA,WAAW,CAACkB,GAAZ,CAAgBrC,QAAQ,CAACiB,SAAT,EAAhB;MACH,CAHD,MAIK;QACD,KAAKO,MAAL;MACH;IACJ;EACJ;;EACDc,cAAc,GAAG;IACb,KAAKd,MAAL;;IACA,IAAI,KAAKA,MAAL,KAAgB,CAApB,EAAuB;MACnB,KAAKL,WAAL,CAAiBe,QAAjB;IACH;EACJ;;EACDK,cAAc,GAAG;IACb,MAAMhB,SAAS,GAAG,KAAKA,SAAvB;IACA,MAAMS,GAAG,GAAGT,SAAS,CAACf,MAAtB;IACA,MAAMW,WAAW,GAAG,KAAKA,WAAzB;;IACA,KAAK,IAAIgB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,GAApB,EAAyBG,CAAC,EAA1B,EAA8B;MAC1B,IAAInC,QAAQ,GAAGuB,SAAS,CAACY,CAAD,CAAxB;;MACA,IAAI,OAAOnC,QAAQ,CAACwC,QAAhB,KAA6B,UAA7B,IAA2C,CAACxC,QAAQ,CAACwC,QAAT,EAAhD,EAAqE;QACjE;MACH;IACJ;;IACD,IAAIC,cAAc,GAAG,KAArB;IACA,MAAMC,IAAI,GAAG,EAAb;;IACA,KAAK,IAAIP,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,GAApB,EAAyBG,CAAC,EAA1B,EAA8B;MAC1B,IAAInC,QAAQ,GAAGuB,SAAS,CAACY,CAAD,CAAxB;MACA,IAAIQ,MAAM,GAAG3C,QAAQ,CAAC4C,IAAT,EAAb;;MACA,IAAI5C,QAAQ,CAAC6C,YAAT,EAAJ,EAA6B;QACzBJ,cAAc,GAAG,IAAjB;MACH;;MACD,IAAIE,MAAM,CAACG,IAAX,EAAiB;QACb3B,WAAW,CAACe,QAAZ;QACA;MACH;;MACDQ,IAAI,CAACf,IAAL,CAAUgB,MAAM,CAACjB,KAAjB;IACH;;IACD,IAAI,KAAKnB,cAAT,EAAyB;MACrB,KAAKwC,kBAAL,CAAwBL,IAAxB;IACH,CAFD,MAGK;MACDvB,WAAW,CAACyB,IAAZ,CAAiBF,IAAjB;IACH;;IACD,IAAID,cAAJ,EAAoB;MAChBtB,WAAW,CAACe,QAAZ;IACH;EACJ;;EACDa,kBAAkB,CAACL,IAAD,EAAO;IACrB,IAAIC,MAAJ;;IACA,IAAI;MACAA,MAAM,GAAG,KAAKpC,cAAL,CAAoByC,KAApB,CAA0B,IAA1B,EAAgCN,IAAhC,CAAT;IACH,CAFD,CAGA,OAAOO,GAAP,EAAY;MACR,KAAK9B,WAAL,CAAiB+B,KAAjB,CAAuBD,GAAvB;MACA;IACH;;IACD,KAAK9B,WAAL,CAAiByB,IAAjB,CAAsBD,MAAtB;EACH;;AA1FyC;;AA4F9C,MAAMd,cAAN,CAAqB;EACjBhB,WAAW,CAACb,QAAD,EAAW;IAClB,KAAKA,QAAL,GAAgBA,QAAhB;IACA,KAAKmD,UAAL,GAAkBnD,QAAQ,CAAC4C,IAAT,EAAlB;EACH;;EACDJ,QAAQ,GAAG;IACP,OAAO,IAAP;EACH;;EACDI,IAAI,GAAG;IACH,MAAMD,MAAM,GAAG,KAAKQ,UAApB;IACA,KAAKA,UAAL,GAAkB,KAAKnD,QAAL,CAAc4C,IAAd,EAAlB;IACA,OAAOD,MAAP;EACH;;EACDE,YAAY,GAAG;IACX,MAAMM,UAAU,GAAG,KAAKA,UAAxB;IACA,OAAOC,OAAO,CAACD,UAAU,IAAIA,UAAU,CAACL,IAA1B,CAAd;EACH;;AAhBgB;;AAkBrB,MAAMlB,mBAAN,CAA0B;EACtBf,WAAW,CAACwC,KAAD,EAAQ;IACf,KAAKA,KAAL,GAAaA,KAAb;IACA,KAAKC,KAAL,GAAa,CAAb;IACA,KAAK9C,MAAL,GAAc,CAAd;IACA,KAAKA,MAAL,GAAc6C,KAAK,CAAC7C,MAApB;EACH;;EACe,CAAfP,eAAe,IAAI;IAChB,OAAO,IAAP;EACH;;EACD2C,IAAI,CAAClB,KAAD,EAAQ;IACR,MAAMS,CAAC,GAAG,KAAKmB,KAAL,EAAV;IACA,MAAMD,KAAK,GAAG,KAAKA,KAAnB;IACA,OAAOlB,CAAC,GAAG,KAAK3B,MAAT,GAAkB;MAAEkB,KAAK,EAAE2B,KAAK,CAAClB,CAAD,CAAd;MAAmBW,IAAI,EAAE;IAAzB,CAAlB,GAAqD;MAAEpB,KAAK,EAAE,IAAT;MAAeoB,IAAI,EAAE;IAArB,CAA5D;EACH;;EACDN,QAAQ,GAAG;IACP,OAAO,KAAKa,KAAL,CAAW7C,MAAX,GAAoB,KAAK8C,KAAhC;EACH;;EACDT,YAAY,GAAG;IACX,OAAO,KAAKQ,KAAL,CAAW7C,MAAX,KAAsB,KAAK8C,KAAlC;EACH;;AApBqB;;AAsB1B,MAAMxB,iBAAN,SAAgC5B,qBAAhC,CAAsD;EAClDW,WAAW,CAACM,WAAD,EAAcoC,MAAd,EAAsBC,UAAtB,EAAkC;IACzC,MAAMrC,WAAN;IACA,KAAKoC,MAAL,GAAcA,MAAd;IACA,KAAKC,UAAL,GAAkBA,UAAlB;IACA,KAAKpB,iBAAL,GAAyB,IAAzB;IACA,KAAKqB,MAAL,GAAc,EAAd;IACA,KAAKC,UAAL,GAAkB,KAAlB;EACH;;EACe,CAAfzD,eAAe,IAAI;IAChB,OAAO,IAAP;EACH;;EACD2C,IAAI,GAAG;IACH,MAAMa,MAAM,GAAG,KAAKA,MAApB;;IACA,IAAIA,MAAM,CAACjD,MAAP,KAAkB,CAAlB,IAAuB,KAAKkD,UAAhC,EAA4C;MACxC,OAAO;QAAEhC,KAAK,EAAE,IAAT;QAAeoB,IAAI,EAAE;MAArB,CAAP;IACH,CAFD,MAGK;MACD,OAAO;QAAEpB,KAAK,EAAE+B,MAAM,CAACE,KAAP,EAAT;QAAyBb,IAAI,EAAE;MAA/B,CAAP;IACH;EACJ;;EACDN,QAAQ,GAAG;IACP,OAAO,KAAKiB,MAAL,CAAYjD,MAAZ,GAAqB,CAA5B;EACH;;EACDqC,YAAY,GAAG;IACX,OAAO,KAAKY,MAAL,CAAYjD,MAAZ,KAAuB,CAAvB,IAA4B,KAAKkD,UAAxC;EACH;;EACDE,cAAc,GAAG;IACb,IAAI,KAAKH,MAAL,CAAYjD,MAAZ,GAAqB,CAAzB,EAA4B;MACxB,KAAKkD,UAAL,GAAkB,IAAlB;MACA,KAAKH,MAAL,CAAYjB,cAAZ;IACH,CAHD,MAIK;MACD,KAAKnB,WAAL,CAAiBe,QAAjB;IACH;EACJ;;EACD2B,UAAU,CAACC,UAAD,EAAa;IACnB,KAAKL,MAAL,CAAY9B,IAAZ,CAAiBmC,UAAjB;IACA,KAAKP,MAAL,CAAYhB,cAAZ;EACH;;EACDtB,SAAS,GAAG;IACR,OAAOb,cAAc,CAAC,KAAKoD,UAAN,EAAkB,IAAIrD,qBAAJ,CAA0B,IAA1B,CAAlB,CAArB;EACH;;AA1CiD"}, "metadata": {}, "sourceType": "module"}