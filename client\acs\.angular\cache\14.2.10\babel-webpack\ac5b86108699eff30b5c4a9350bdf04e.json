{"ast": null, "code": "import { Subscriber } from './Subscriber';\nexport class OuterSubscriber extends Subscriber {\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    this.destination.next(innerValue);\n  }\n\n  notifyError(error, innerSub) {\n    this.destination.error(error);\n  }\n\n  notifyComplete(innerSub) {\n    this.destination.complete();\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "OuterSubscriber", "notifyNext", "outerValue", "innerValue", "outerIndex", "innerIndex", "innerSub", "destination", "next", "notifyError", "error", "notifyComplete", "complete"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/OuterSubscriber.js"], "sourcesContent": ["import { Subscriber } from './Subscriber';\nexport class OuterSubscriber extends Subscriber {\n    notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n        this.destination.next(innerValue);\n    }\n    notifyError(error, innerSub) {\n        this.destination.error(error);\n    }\n    notifyComplete(innerSub) {\n        this.destination.complete();\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,cAA3B;AACA,OAAO,MAAMC,eAAN,SAA8BD,UAA9B,CAAyC;EAC5CE,UAAU,CAACC,UAAD,EAAaC,UAAb,EAAyBC,UAAzB,EAAqCC,UAArC,EAAiDC,QAAjD,EAA2D;IACjE,KAAKC,WAAL,CAAiBC,IAAjB,CAAsBL,UAAtB;EACH;;EACDM,WAAW,CAACC,KAAD,EAAQJ,QAAR,EAAkB;IACzB,KAAKC,WAAL,CAAiBG,KAAjB,CAAuBA,KAAvB;EACH;;EACDC,cAAc,CAACL,QAAD,EAAW;IACrB,KAAKC,WAAL,CAAiBK,QAAjB;EACH;;AAT2C"}, "metadata": {}, "sourceType": "module"}