{"ast": null, "code": "import { Subscription } from '../Subscription';\nexport class Action extends Subscription {\n  constructor(scheduler, work) {\n    super();\n  }\n\n  schedule(state, delay = 0) {\n    return this;\n  }\n\n}", "map": {"version": 3, "names": ["Subscription", "Action", "constructor", "scheduler", "work", "schedule", "state", "delay"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/scheduler/Action.js"], "sourcesContent": ["import { Subscription } from '../Subscription';\nexport class Action extends Subscription {\n    constructor(scheduler, work) {\n        super();\n    }\n    schedule(state, delay = 0) {\n        return this;\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAT,QAA6B,iBAA7B;AACA,OAAO,MAAMC,MAAN,SAAqBD,YAArB,CAAkC;EACrCE,WAAW,CAACC,SAAD,EAAYC,IAAZ,EAAkB;IACzB;EACH;;EACDC,QAAQ,CAACC,KAAD,EAAQC,KAAK,GAAG,CAAhB,EAAmB;IACvB,OAAO,IAAP;EACH;;AANoC"}, "metadata": {}, "sourceType": "module"}