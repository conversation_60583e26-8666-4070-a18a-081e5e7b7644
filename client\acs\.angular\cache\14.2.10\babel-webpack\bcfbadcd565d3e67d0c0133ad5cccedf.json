{"ast": null, "code": "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "map": {"version": 3, "names": ["getWindow", "getComputedStyle", "element"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js"], "sourcesContent": ["import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,gBAAtB;AACA,eAAe,SAASC,gBAAT,CAA0BC,OAA1B,EAAmC;EAChD,OAAOF,SAAS,CAACE,OAAD,CAAT,CAAmBD,gBAAnB,CAAoCC,OAApC,CAAP;AACD"}, "metadata": {}, "sourceType": "module"}