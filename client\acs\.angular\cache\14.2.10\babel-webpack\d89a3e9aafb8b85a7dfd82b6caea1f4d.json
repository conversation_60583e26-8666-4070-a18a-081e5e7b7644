{"ast": null, "code": "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "map": {"version": 3, "names": ["top", "bottom", "left", "right", "detectOverflow", "getSideOffsets", "overflow", "rect", "preventedOffsets", "x", "y", "height", "width", "isAnySideFullyClipped", "some", "side", "hide", "_ref", "state", "name", "referenceRect", "rects", "reference", "popperRect", "popper", "modifiersData", "preventOverflow", "referenceOverflow", "elementContext", "popperAltOverflow", "altBoundary", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "attributes", "Object", "assign", "enabled", "phase", "requiresIfExists", "fn"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/modifiers/hide.js"], "sourcesContent": ["import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};"], "mappings": "AAAA,SAASA,GAAT,EAAcC,MAAd,EAAsBC,IAAtB,EAA4BC,KAA5B,QAAyC,aAAzC;AACA,OAAOC,cAAP,MAA2B,4BAA3B;;AAEA,SAASC,cAAT,CAAwBC,QAAxB,EAAkCC,IAAlC,EAAwCC,gBAAxC,EAA0D;EACxD,IAAIA,gBAAgB,KAAK,KAAK,CAA9B,EAAiC;IAC/BA,gBAAgB,GAAG;MACjBC,CAAC,EAAE,CADc;MAEjBC,CAAC,EAAE;IAFc,CAAnB;EAID;;EAED,OAAO;IACLV,GAAG,EAAEM,QAAQ,CAACN,GAAT,GAAeO,IAAI,CAACI,MAApB,GAA6BH,gBAAgB,CAACE,CAD9C;IAELP,KAAK,EAAEG,QAAQ,CAACH,KAAT,GAAiBI,IAAI,CAACK,KAAtB,GAA8BJ,gBAAgB,CAACC,CAFjD;IAGLR,MAAM,EAAEK,QAAQ,CAACL,MAAT,GAAkBM,IAAI,CAACI,MAAvB,GAAgCH,gBAAgB,CAACE,CAHpD;IAILR,IAAI,EAAEI,QAAQ,CAACJ,IAAT,GAAgBK,IAAI,CAACK,KAArB,GAA6BJ,gBAAgB,CAACC;EAJ/C,CAAP;AAMD;;AAED,SAASI,qBAAT,CAA+BP,QAA/B,EAAyC;EACvC,OAAO,CAACN,GAAD,EAAMG,KAAN,EAAaF,MAAb,EAAqBC,IAArB,EAA2BY,IAA3B,CAAgC,UAAUC,IAAV,EAAgB;IACrD,OAAOT,QAAQ,CAACS,IAAD,CAAR,IAAkB,CAAzB;EACD,CAFM,CAAP;AAGD;;AAED,SAASC,IAAT,CAAcC,IAAd,EAAoB;EAClB,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAjB;EAAA,IACIC,IAAI,GAAGF,IAAI,CAACE,IADhB;EAEA,IAAIC,aAAa,GAAGF,KAAK,CAACG,KAAN,CAAYC,SAAhC;EACA,IAAIC,UAAU,GAAGL,KAAK,CAACG,KAAN,CAAYG,MAA7B;EACA,IAAIhB,gBAAgB,GAAGU,KAAK,CAACO,aAAN,CAAoBC,eAA3C;EACA,IAAIC,iBAAiB,GAAGvB,cAAc,CAACc,KAAD,EAAQ;IAC5CU,cAAc,EAAE;EAD4B,CAAR,CAAtC;EAGA,IAAIC,iBAAiB,GAAGzB,cAAc,CAACc,KAAD,EAAQ;IAC5CY,WAAW,EAAE;EAD+B,CAAR,CAAtC;EAGA,IAAIC,wBAAwB,GAAG1B,cAAc,CAACsB,iBAAD,EAAoBP,aAApB,CAA7C;EACA,IAAIY,mBAAmB,GAAG3B,cAAc,CAACwB,iBAAD,EAAoBN,UAApB,EAAgCf,gBAAhC,CAAxC;EACA,IAAIyB,iBAAiB,GAAGpB,qBAAqB,CAACkB,wBAAD,CAA7C;EACA,IAAIG,gBAAgB,GAAGrB,qBAAqB,CAACmB,mBAAD,CAA5C;EACAd,KAAK,CAACO,aAAN,CAAoBN,IAApB,IAA4B;IAC1BY,wBAAwB,EAAEA,wBADA;IAE1BC,mBAAmB,EAAEA,mBAFK;IAG1BC,iBAAiB,EAAEA,iBAHO;IAI1BC,gBAAgB,EAAEA;EAJQ,CAA5B;EAMAhB,KAAK,CAACiB,UAAN,CAAiBX,MAAjB,GAA0BY,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBnB,KAAK,CAACiB,UAAN,CAAiBX,MAAnC,EAA2C;IACnE,gCAAgCS,iBADmC;IAEnE,uBAAuBC;EAF4C,CAA3C,CAA1B;AAID,C,CAAC;;;AAGF,eAAe;EACbf,IAAI,EAAE,MADO;EAEbmB,OAAO,EAAE,IAFI;EAGbC,KAAK,EAAE,MAHM;EAIbC,gBAAgB,EAAE,CAAC,iBAAD,CAJL;EAKbC,EAAE,EAAEzB;AALS,CAAf"}, "metadata": {}, "sourceType": "module"}