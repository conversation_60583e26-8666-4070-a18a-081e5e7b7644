{"ast": null, "code": "import { Subject } from '../Subject';\nimport { multicast } from './multicast';\nexport function publish(selector) {\n  return selector ? multicast(() => new Subject(), selector) : multicast(new Subject());\n}", "map": {"version": 3, "names": ["Subject", "multicast", "publish", "selector"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/publish.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { multicast } from './multicast';\nexport function publish(selector) {\n    return selector ?\n        multicast(() => new Subject(), selector) :\n        multicast(new Subject());\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,YAAxB;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,OAAO,SAASC,OAAT,CAAiBC,QAAjB,EAA2B;EAC9B,OAAOA,QAAQ,GACXF,SAAS,CAAC,MAAM,IAAID,OAAJ,EAAP,EAAsBG,QAAtB,CADE,GAEXF,SAAS,CAAC,IAAID,OAAJ,EAAD,CAFb;AAGH"}, "metadata": {}, "sourceType": "module"}