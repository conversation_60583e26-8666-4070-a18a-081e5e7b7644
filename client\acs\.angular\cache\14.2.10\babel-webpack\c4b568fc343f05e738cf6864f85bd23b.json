{"ast": null, "code": "import { top, left, right, bottom, end } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref) {\n  var x = _ref.x,\n      y = _ref.y;\n  var win = window;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets,\n      isFixed = _ref2.isFixed;\n  var _offsets$x = offsets.x,\n      x = _offsets$x === void 0 ? 0 : _offsets$x,\n      _offsets$y = offsets.y,\n      y = _offsets$y === void 0 ? 0 : _offsets$y;\n\n  var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref3.x;\n  y = _ref3.y;\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom;\n      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : // $FlowFixMe[prop-missing]\n      offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right;\n      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : // $FlowFixMe[prop-missing]\n      offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref4.x;\n  y = _ref4.y;\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref5) {\n  var state = _ref5.state,\n      options = _ref5.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n\n  if (process.env.NODE_ENV !== \"production\") {\n    var transitionProperty = getComputedStyle(state.elements.popper).transitionProperty || '';\n\n    if (adaptive && ['transform', 'top', 'right', 'bottom', 'left'].some(function (property) {\n      return transitionProperty.indexOf(property) >= 0;\n    })) {\n      console.warn(['Popper: Detected CSS transitions on at least one of the following', 'CSS properties: \"transform\", \"top\", \"right\", \"bottom\", \"left\".', '\\n\\n', 'Disable the \"computeStyles\" modifier\\'s `adaptive` option to allow', 'for smooth transitions, or remove these properties from the CSS', 'transition declaration on the popper element if only transitioning', 'opacity or background-color for example.', '\\n\\n', 'We recommend using the popper element as a wrapper around an inner', 'element that can have any CSS property transitioned for animations.'].join(' '));\n    }\n  }\n\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed'\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "map": {"version": 3, "names": ["top", "left", "right", "bottom", "end", "getOffsetParent", "getWindow", "getDocumentElement", "getComputedStyle", "getBasePlacement", "getVariation", "round", "unsetSides", "roundOffsetsByDPR", "_ref", "x", "y", "win", "window", "dpr", "devicePixelRatio", "mapToStyles", "_ref2", "_Object$assign2", "popper", "popperRect", "placement", "variation", "offsets", "position", "gpuAcceleration", "adaptive", "roundOffsets", "isFixed", "_offsets$x", "_offsets$y", "_ref3", "hasX", "hasOwnProperty", "hasY", "sideX", "sideY", "offsetParent", "heightProp", "widthProp", "offsetY", "visualViewport", "height", "offsetX", "width", "commonStyles", "Object", "assign", "_ref4", "_Object$assign", "transform", "computeStyles", "_ref5", "state", "options", "_options$gpuAccelerat", "_options$adaptive", "_options$roundOffsets", "process", "env", "NODE_ENV", "transitionProperty", "elements", "some", "property", "indexOf", "console", "warn", "join", "rects", "strategy", "modifiersData", "popperOffsets", "styles", "arrow", "attributes", "name", "enabled", "phase", "fn", "data"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/modifiers/computeStyles.js"], "sourcesContent": ["import { top, left, right, bottom, end } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref) {\n  var x = _ref.x,\n      y = _ref.y;\n  var win = window;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets,\n      isFixed = _ref2.isFixed;\n  var _offsets$x = offsets.x,\n      x = _offsets$x === void 0 ? 0 : _offsets$x,\n      _offsets$y = offsets.y,\n      y = _offsets$y === void 0 ? 0 : _offsets$y;\n\n  var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref3.x;\n  y = _ref3.y;\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom;\n      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : // $FlowFixMe[prop-missing]\n      offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right;\n      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : // $FlowFixMe[prop-missing]\n      offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref4.x;\n  y = _ref4.y;\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref5) {\n  var state = _ref5.state,\n      options = _ref5.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n\n  if (process.env.NODE_ENV !== \"production\") {\n    var transitionProperty = getComputedStyle(state.elements.popper).transitionProperty || '';\n\n    if (adaptive && ['transform', 'top', 'right', 'bottom', 'left'].some(function (property) {\n      return transitionProperty.indexOf(property) >= 0;\n    })) {\n      console.warn(['Popper: Detected CSS transitions on at least one of the following', 'CSS properties: \"transform\", \"top\", \"right\", \"bottom\", \"left\".', '\\n\\n', 'Disable the \"computeStyles\" modifier\\'s `adaptive` option to allow', 'for smooth transitions, or remove these properties from the CSS', 'transition declaration on the popper element if only transitioning', 'opacity or background-color for example.', '\\n\\n', 'We recommend using the popper element as a wrapper around an inner', 'element that can have any CSS property transitioned for animations.'].join(' '));\n    }\n  }\n\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed'\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};"], "mappings": "AAAA,SAASA,GAAT,EAAcC,IAAd,EAAoBC,KAApB,EAA2BC,MAA3B,EAAmCC,GAAnC,QAA8C,aAA9C;AACA,OAAOC,eAAP,MAA4B,iCAA5B;AACA,OAAOC,SAAP,MAAsB,2BAAtB;AACA,OAAOC,kBAAP,MAA+B,oCAA/B;AACA,OAAOC,gBAAP,MAA6B,kCAA7B;AACA,OAAOC,gBAAP,MAA6B,8BAA7B;AACA,OAAOC,YAAP,MAAyB,0BAAzB;AACA,SAASC,KAAT,QAAsB,kBAAtB,C,CAA0C;;AAE1C,IAAIC,UAAU,GAAG;EACfZ,GAAG,EAAE,MADU;EAEfE,KAAK,EAAE,MAFQ;EAGfC,MAAM,EAAE,MAHO;EAIfF,IAAI,EAAE;AAJS,CAAjB,C,CAKG;AACH;AACA;;AAEA,SAASY,iBAAT,CAA2BC,IAA3B,EAAiC;EAC/B,IAAIC,CAAC,GAAGD,IAAI,CAACC,CAAb;EAAA,IACIC,CAAC,GAAGF,IAAI,CAACE,CADb;EAEA,IAAIC,GAAG,GAAGC,MAAV;EACA,IAAIC,GAAG,GAAGF,GAAG,CAACG,gBAAJ,IAAwB,CAAlC;EACA,OAAO;IACLL,CAAC,EAAEJ,KAAK,CAACI,CAAC,GAAGI,GAAL,CAAL,GAAiBA,GAAjB,IAAwB,CADtB;IAELH,CAAC,EAAEL,KAAK,CAACK,CAAC,GAAGG,GAAL,CAAL,GAAiBA,GAAjB,IAAwB;EAFtB,CAAP;AAID;;AAED,OAAO,SAASE,WAAT,CAAqBC,KAArB,EAA4B;EACjC,IAAIC,eAAJ;;EAEA,IAAIC,MAAM,GAAGF,KAAK,CAACE,MAAnB;EAAA,IACIC,UAAU,GAAGH,KAAK,CAACG,UADvB;EAAA,IAEIC,SAAS,GAAGJ,KAAK,CAACI,SAFtB;EAAA,IAGIC,SAAS,GAAGL,KAAK,CAACK,SAHtB;EAAA,IAIIC,OAAO,GAAGN,KAAK,CAACM,OAJpB;EAAA,IAKIC,QAAQ,GAAGP,KAAK,CAACO,QALrB;EAAA,IAMIC,eAAe,GAAGR,KAAK,CAACQ,eAN5B;EAAA,IAOIC,QAAQ,GAAGT,KAAK,CAACS,QAPrB;EAAA,IAQIC,YAAY,GAAGV,KAAK,CAACU,YARzB;EAAA,IASIC,OAAO,GAAGX,KAAK,CAACW,OATpB;EAUA,IAAIC,UAAU,GAAGN,OAAO,CAACb,CAAzB;EAAA,IACIA,CAAC,GAAGmB,UAAU,KAAK,KAAK,CAApB,GAAwB,CAAxB,GAA4BA,UADpC;EAAA,IAEIC,UAAU,GAAGP,OAAO,CAACZ,CAFzB;EAAA,IAGIA,CAAC,GAAGmB,UAAU,KAAK,KAAK,CAApB,GAAwB,CAAxB,GAA4BA,UAHpC;;EAKA,IAAIC,KAAK,GAAG,OAAOJ,YAAP,KAAwB,UAAxB,GAAqCA,YAAY,CAAC;IAC5DjB,CAAC,EAAEA,CADyD;IAE5DC,CAAC,EAAEA;EAFyD,CAAD,CAAjD,GAGP;IACHD,CAAC,EAAEA,CADA;IAEHC,CAAC,EAAEA;EAFA,CAHL;;EAQAD,CAAC,GAAGqB,KAAK,CAACrB,CAAV;EACAC,CAAC,GAAGoB,KAAK,CAACpB,CAAV;EACA,IAAIqB,IAAI,GAAGT,OAAO,CAACU,cAAR,CAAuB,GAAvB,CAAX;EACA,IAAIC,IAAI,GAAGX,OAAO,CAACU,cAAR,CAAuB,GAAvB,CAAX;EACA,IAAIE,KAAK,GAAGvC,IAAZ;EACA,IAAIwC,KAAK,GAAGzC,GAAZ;EACA,IAAIiB,GAAG,GAAGC,MAAV;;EAEA,IAAIa,QAAJ,EAAc;IACZ,IAAIW,YAAY,GAAGrC,eAAe,CAACmB,MAAD,CAAlC;IACA,IAAImB,UAAU,GAAG,cAAjB;IACA,IAAIC,SAAS,GAAG,aAAhB;;IAEA,IAAIF,YAAY,KAAKpC,SAAS,CAACkB,MAAD,CAA9B,EAAwC;MACtCkB,YAAY,GAAGnC,kBAAkB,CAACiB,MAAD,CAAjC;;MAEA,IAAIhB,gBAAgB,CAACkC,YAAD,CAAhB,CAA+Bb,QAA/B,KAA4C,QAA5C,IAAwDA,QAAQ,KAAK,UAAzE,EAAqF;QACnFc,UAAU,GAAG,cAAb;QACAC,SAAS,GAAG,aAAZ;MACD;IACF,CAZW,CAYV;;;IAGFF,YAAY,GAAGA,YAAf;;IAEA,IAAIhB,SAAS,KAAK1B,GAAd,IAAqB,CAAC0B,SAAS,KAAKzB,IAAd,IAAsByB,SAAS,KAAKxB,KAArC,KAA+CyB,SAAS,KAAKvB,GAAtF,EAA2F;MACzFqC,KAAK,GAAGtC,MAAR;MACA,IAAI0C,OAAO,GAAGZ,OAAO,IAAIS,YAAY,KAAKzB,GAA5B,IAAmCA,GAAG,CAAC6B,cAAvC,GAAwD7B,GAAG,CAAC6B,cAAJ,CAAmBC,MAA3E,GAAoF;MAClGL,YAAY,CAACC,UAAD,CADZ;MAEA3B,CAAC,IAAI6B,OAAO,GAAGpB,UAAU,CAACsB,MAA1B;MACA/B,CAAC,IAAIc,eAAe,GAAG,CAAH,GAAO,CAAC,CAA5B;IACD;;IAED,IAAIJ,SAAS,KAAKzB,IAAd,IAAsB,CAACyB,SAAS,KAAK1B,GAAd,IAAqB0B,SAAS,KAAKvB,MAApC,KAA+CwB,SAAS,KAAKvB,GAAvF,EAA4F;MAC1FoC,KAAK,GAAGtC,KAAR;MACA,IAAI8C,OAAO,GAAGf,OAAO,IAAIS,YAAY,KAAKzB,GAA5B,IAAmCA,GAAG,CAAC6B,cAAvC,GAAwD7B,GAAG,CAAC6B,cAAJ,CAAmBG,KAA3E,GAAmF;MACjGP,YAAY,CAACE,SAAD,CADZ;MAEA7B,CAAC,IAAIiC,OAAO,GAAGvB,UAAU,CAACwB,KAA1B;MACAlC,CAAC,IAAIe,eAAe,GAAG,CAAH,GAAO,CAAC,CAA5B;IACD;EACF;;EAED,IAAIoB,YAAY,GAAGC,MAAM,CAACC,MAAP,CAAc;IAC/BvB,QAAQ,EAAEA;EADqB,CAAd,EAEhBE,QAAQ,IAAInB,UAFI,CAAnB;;EAIA,IAAIyC,KAAK,GAAGrB,YAAY,KAAK,IAAjB,GAAwBnB,iBAAiB,CAAC;IACpDE,CAAC,EAAEA,CADiD;IAEpDC,CAAC,EAAEA;EAFiD,CAAD,CAAzC,GAGP;IACHD,CAAC,EAAEA,CADA;IAEHC,CAAC,EAAEA;EAFA,CAHL;;EAQAD,CAAC,GAAGsC,KAAK,CAACtC,CAAV;EACAC,CAAC,GAAGqC,KAAK,CAACrC,CAAV;;EAEA,IAAIc,eAAJ,EAAqB;IACnB,IAAIwB,cAAJ;;IAEA,OAAOH,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBF,YAAlB,GAAiCI,cAAc,GAAG,EAAjB,EAAqBA,cAAc,CAACb,KAAD,CAAd,GAAwBF,IAAI,GAAG,GAAH,GAAS,EAA1D,EAA8De,cAAc,CAACd,KAAD,CAAd,GAAwBH,IAAI,GAAG,GAAH,GAAS,EAAnG,EAAuGiB,cAAc,CAACC,SAAf,GAA2B,CAACtC,GAAG,CAACG,gBAAJ,IAAwB,CAAzB,KAA+B,CAA/B,GAAmC,eAAeL,CAAf,GAAmB,MAAnB,GAA4BC,CAA5B,GAAgC,KAAnE,GAA2E,iBAAiBD,CAAjB,GAAqB,MAArB,GAA8BC,CAA9B,GAAkC,QAA/O,EAAyPsC,cAA1R,EAAP;EACD;;EAED,OAAOH,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBF,YAAlB,GAAiC3B,eAAe,GAAG,EAAlB,EAAsBA,eAAe,CAACkB,KAAD,CAAf,GAAyBF,IAAI,GAAGvB,CAAC,GAAG,IAAP,GAAc,EAAjE,EAAqEO,eAAe,CAACiB,KAAD,CAAf,GAAyBH,IAAI,GAAGtB,CAAC,GAAG,IAAP,GAAc,EAAhH,EAAoHQ,eAAe,CAACgC,SAAhB,GAA4B,EAAhJ,EAAoJhC,eAArL,EAAP;AACD;;AAED,SAASiC,aAAT,CAAuBC,KAAvB,EAA8B;EAC5B,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAlB;EAAA,IACIC,OAAO,GAAGF,KAAK,CAACE,OADpB;EAEA,IAAIC,qBAAqB,GAAGD,OAAO,CAAC7B,eAApC;EAAA,IACIA,eAAe,GAAG8B,qBAAqB,KAAK,KAAK,CAA/B,GAAmC,IAAnC,GAA0CA,qBADhE;EAAA,IAEIC,iBAAiB,GAAGF,OAAO,CAAC5B,QAFhC;EAAA,IAGIA,QAAQ,GAAG8B,iBAAiB,KAAK,KAAK,CAA3B,GAA+B,IAA/B,GAAsCA,iBAHrD;EAAA,IAIIC,qBAAqB,GAAGH,OAAO,CAAC3B,YAJpC;EAAA,IAKIA,YAAY,GAAG8B,qBAAqB,KAAK,KAAK,CAA/B,GAAmC,IAAnC,GAA0CA,qBAL7D;;EAOA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;IACzC,IAAIC,kBAAkB,GAAG1D,gBAAgB,CAACkD,KAAK,CAACS,QAAN,CAAe3C,MAAhB,CAAhB,CAAwC0C,kBAAxC,IAA8D,EAAvF;;IAEA,IAAInC,QAAQ,IAAI,CAAC,WAAD,EAAc,KAAd,EAAqB,OAArB,EAA8B,QAA9B,EAAwC,MAAxC,EAAgDqC,IAAhD,CAAqD,UAAUC,QAAV,EAAoB;MACvF,OAAOH,kBAAkB,CAACI,OAAnB,CAA2BD,QAA3B,KAAwC,CAA/C;IACD,CAFe,CAAhB,EAEI;MACFE,OAAO,CAACC,IAAR,CAAa,CAAC,mEAAD,EAAsE,gEAAtE,EAAwI,MAAxI,EAAgJ,oEAAhJ,EAAsN,iEAAtN,EAAyR,oEAAzR,EAA+V,0CAA/V,EAA2Y,MAA3Y,EAAmZ,oEAAnZ,EAAyd,qEAAzd,EAAgiBC,IAAhiB,CAAqiB,GAAriB,CAAb;IACD;EACF;;EAED,IAAIvB,YAAY,GAAG;IACjBxB,SAAS,EAAEjB,gBAAgB,CAACiD,KAAK,CAAChC,SAAP,CADV;IAEjBC,SAAS,EAAEjB,YAAY,CAACgD,KAAK,CAAChC,SAAP,CAFN;IAGjBF,MAAM,EAAEkC,KAAK,CAACS,QAAN,CAAe3C,MAHN;IAIjBC,UAAU,EAAEiC,KAAK,CAACgB,KAAN,CAAYlD,MAJP;IAKjBM,eAAe,EAAEA,eALA;IAMjBG,OAAO,EAAEyB,KAAK,CAACC,OAAN,CAAcgB,QAAd,KAA2B;EANnB,CAAnB;;EASA,IAAIjB,KAAK,CAACkB,aAAN,CAAoBC,aAApB,IAAqC,IAAzC,EAA+C;IAC7CnB,KAAK,CAACoB,MAAN,CAAatD,MAAb,GAAsB2B,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBM,KAAK,CAACoB,MAAN,CAAatD,MAA/B,EAAuCH,WAAW,CAAC8B,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBF,YAAlB,EAAgC;MACvGtB,OAAO,EAAE8B,KAAK,CAACkB,aAAN,CAAoBC,aAD0E;MAEvGhD,QAAQ,EAAE6B,KAAK,CAACC,OAAN,CAAcgB,QAF+E;MAGvG5C,QAAQ,EAAEA,QAH6F;MAIvGC,YAAY,EAAEA;IAJyF,CAAhC,CAAD,CAAlD,CAAtB;EAMD;;EAED,IAAI0B,KAAK,CAACkB,aAAN,CAAoBG,KAApB,IAA6B,IAAjC,EAAuC;IACrCrB,KAAK,CAACoB,MAAN,CAAaC,KAAb,GAAqB5B,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBM,KAAK,CAACoB,MAAN,CAAaC,KAA/B,EAAsC1D,WAAW,CAAC8B,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBF,YAAlB,EAAgC;MACrGtB,OAAO,EAAE8B,KAAK,CAACkB,aAAN,CAAoBG,KADwE;MAErGlD,QAAQ,EAAE,UAF2F;MAGrGE,QAAQ,EAAE,KAH2F;MAIrGC,YAAY,EAAEA;IAJuF,CAAhC,CAAD,CAAjD,CAArB;EAMD;;EAED0B,KAAK,CAACsB,UAAN,CAAiBxD,MAAjB,GAA0B2B,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBM,KAAK,CAACsB,UAAN,CAAiBxD,MAAnC,EAA2C;IACnE,yBAAyBkC,KAAK,CAAChC;EADoC,CAA3C,CAA1B;AAGD,C,CAAC;;;AAGF,eAAe;EACbuD,IAAI,EAAE,eADO;EAEbC,OAAO,EAAE,IAFI;EAGbC,KAAK,EAAE,aAHM;EAIbC,EAAE,EAAE5B,aAJS;EAKb6B,IAAI,EAAE;AALO,CAAf"}, "metadata": {}, "sourceType": "module"}