{"ast": null, "code": "import { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function audit(durationSelector) {\n  return function auditOperatorFunction(source) {\n    return source.lift(new AuditOperator(durationSelector));\n  };\n}\n\nclass AuditOperator {\n  constructor(durationSelector) {\n    this.durationSelector = durationSelector;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new AuditSubscriber(subscriber, this.durationSelector));\n  }\n\n}\n\nclass AuditSubscriber extends SimpleOuterSubscriber {\n  constructor(destination, durationSelector) {\n    super(destination);\n    this.durationSelector = durationSelector;\n    this.hasValue = false;\n  }\n\n  _next(value) {\n    this.value = value;\n    this.hasValue = true;\n\n    if (!this.throttled) {\n      let duration;\n\n      try {\n        const {\n          durationSelector\n        } = this;\n        duration = durationSelector(value);\n      } catch (err) {\n        return this.destination.error(err);\n      }\n\n      const innerSubscription = innerSubscribe(duration, new SimpleInnerSubscriber(this));\n\n      if (!innerSubscription || innerSubscription.closed) {\n        this.clearThrottle();\n      } else {\n        this.add(this.throttled = innerSubscription);\n      }\n    }\n  }\n\n  clearThrottle() {\n    const {\n      value,\n      hasValue,\n      throttled\n    } = this;\n\n    if (throttled) {\n      this.remove(throttled);\n      this.throttled = undefined;\n      throttled.unsubscribe();\n    }\n\n    if (hasValue) {\n      this.value = undefined;\n      this.hasValue = false;\n      this.destination.next(value);\n    }\n  }\n\n  notifyNext() {\n    this.clearThrottle();\n  }\n\n  notifyComplete() {\n    this.clearThrottle();\n  }\n\n}", "map": {"version": 3, "names": ["SimpleOuterSubscriber", "innerSubscribe", "SimpleInnerSubscriber", "audit", "durationSelector", "auditOperatorFunction", "source", "lift", "AuditOperator", "constructor", "call", "subscriber", "subscribe", "AuditSubscriber", "destination", "hasValue", "_next", "value", "throttled", "duration", "err", "error", "innerSubscription", "closed", "clearThrottle", "add", "remove", "undefined", "unsubscribe", "next", "notifyNext", "notifyComplete"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/audit.js"], "sourcesContent": ["import { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function audit(durationSelector) {\n    return function auditOperatorFunction(source) {\n        return source.lift(new AuditOperator(durationSelector));\n    };\n}\nclass AuditOperator {\n    constructor(durationSelector) {\n        this.durationSelector = durationSelector;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new AuditSubscriber(subscriber, this.durationSelector));\n    }\n}\nclass AuditSubscriber extends SimpleOuterSubscriber {\n    constructor(destination, durationSelector) {\n        super(destination);\n        this.durationSelector = durationSelector;\n        this.hasValue = false;\n    }\n    _next(value) {\n        this.value = value;\n        this.hasValue = true;\n        if (!this.throttled) {\n            let duration;\n            try {\n                const { durationSelector } = this;\n                duration = durationSelector(value);\n            }\n            catch (err) {\n                return this.destination.error(err);\n            }\n            const innerSubscription = innerSubscribe(duration, new SimpleInnerSubscriber(this));\n            if (!innerSubscription || innerSubscription.closed) {\n                this.clearThrottle();\n            }\n            else {\n                this.add(this.throttled = innerSubscription);\n            }\n        }\n    }\n    clearThrottle() {\n        const { value, hasValue, throttled } = this;\n        if (throttled) {\n            this.remove(throttled);\n            this.throttled = undefined;\n            throttled.unsubscribe();\n        }\n        if (hasValue) {\n            this.value = undefined;\n            this.hasValue = false;\n            this.destination.next(value);\n        }\n    }\n    notifyNext() {\n        this.clearThrottle();\n    }\n    notifyComplete() {\n        this.clearThrottle();\n    }\n}\n"], "mappings": "AAAA,SAASA,qBAAT,EAAgCC,cAAhC,EAAgDC,qBAAhD,QAA6E,mBAA7E;AACA,OAAO,SAASC,KAAT,CAAeC,gBAAf,EAAiC;EACpC,OAAO,SAASC,qBAAT,CAA+BC,MAA/B,EAAuC;IAC1C,OAAOA,MAAM,CAACC,IAAP,CAAY,IAAIC,aAAJ,CAAkBJ,gBAAlB,CAAZ,CAAP;EACH,CAFD;AAGH;;AACD,MAAMI,aAAN,CAAoB;EAChBC,WAAW,CAACL,gBAAD,EAAmB;IAC1B,KAAKA,gBAAL,GAAwBA,gBAAxB;EACH;;EACDM,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,eAAJ,CAAoBF,UAApB,EAAgC,KAAKP,gBAArC,CAAjB,CAAP;EACH;;AANe;;AAQpB,MAAMS,eAAN,SAA8Bb,qBAA9B,CAAoD;EAChDS,WAAW,CAACK,WAAD,EAAcV,gBAAd,EAAgC;IACvC,MAAMU,WAAN;IACA,KAAKV,gBAAL,GAAwBA,gBAAxB;IACA,KAAKW,QAAL,GAAgB,KAAhB;EACH;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACT,KAAKA,KAAL,GAAaA,KAAb;IACA,KAAKF,QAAL,GAAgB,IAAhB;;IACA,IAAI,CAAC,KAAKG,SAAV,EAAqB;MACjB,IAAIC,QAAJ;;MACA,IAAI;QACA,MAAM;UAAEf;QAAF,IAAuB,IAA7B;QACAe,QAAQ,GAAGf,gBAAgB,CAACa,KAAD,CAA3B;MACH,CAHD,CAIA,OAAOG,GAAP,EAAY;QACR,OAAO,KAAKN,WAAL,CAAiBO,KAAjB,CAAuBD,GAAvB,CAAP;MACH;;MACD,MAAME,iBAAiB,GAAGrB,cAAc,CAACkB,QAAD,EAAW,IAAIjB,qBAAJ,CAA0B,IAA1B,CAAX,CAAxC;;MACA,IAAI,CAACoB,iBAAD,IAAsBA,iBAAiB,CAACC,MAA5C,EAAoD;QAChD,KAAKC,aAAL;MACH,CAFD,MAGK;QACD,KAAKC,GAAL,CAAS,KAAKP,SAAL,GAAiBI,iBAA1B;MACH;IACJ;EACJ;;EACDE,aAAa,GAAG;IACZ,MAAM;MAAEP,KAAF;MAASF,QAAT;MAAmBG;IAAnB,IAAiC,IAAvC;;IACA,IAAIA,SAAJ,EAAe;MACX,KAAKQ,MAAL,CAAYR,SAAZ;MACA,KAAKA,SAAL,GAAiBS,SAAjB;MACAT,SAAS,CAACU,WAAV;IACH;;IACD,IAAIb,QAAJ,EAAc;MACV,KAAKE,KAAL,GAAaU,SAAb;MACA,KAAKZ,QAAL,GAAgB,KAAhB;MACA,KAAKD,WAAL,CAAiBe,IAAjB,CAAsBZ,KAAtB;IACH;EACJ;;EACDa,UAAU,GAAG;IACT,KAAKN,aAAL;EACH;;EACDO,cAAc,GAAG;IACb,KAAKP,aAAL;EACH;;AA7C+C"}, "metadata": {}, "sourceType": "module"}