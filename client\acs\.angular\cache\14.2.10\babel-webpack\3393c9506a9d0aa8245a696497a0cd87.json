{"ast": null, "code": "import { SubjectSubscriber } from '../Subject';\nimport { Observable } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { Subscription } from '../Subscription';\nimport { refCount as higherOrderRefCount } from '../operators/refCount';\nexport class ConnectableObservable extends Observable {\n  constructor(source, subjectFactory) {\n    super();\n    this.source = source;\n    this.subjectFactory = subjectFactory;\n    this._refCount = 0;\n    this._isComplete = false;\n  }\n\n  _subscribe(subscriber) {\n    return this.getSubject().subscribe(subscriber);\n  }\n\n  getSubject() {\n    const subject = this._subject;\n\n    if (!subject || subject.isStopped) {\n      this._subject = this.subjectFactory();\n    }\n\n    return this._subject;\n  }\n\n  connect() {\n    let connection = this._connection;\n\n    if (!connection) {\n      this._isComplete = false;\n      connection = this._connection = new Subscription();\n      connection.add(this.source.subscribe(new ConnectableSubscriber(this.getSubject(), this)));\n\n      if (connection.closed) {\n        this._connection = null;\n        connection = Subscription.EMPTY;\n      }\n    }\n\n    return connection;\n  }\n\n  refCount() {\n    return higherOrderRefCount()(this);\n  }\n\n}\nexport const connectableObservableDescriptor = (() => {\n  const connectableProto = ConnectableObservable.prototype;\n  return {\n    operator: {\n      value: null\n    },\n    _refCount: {\n      value: 0,\n      writable: true\n    },\n    _subject: {\n      value: null,\n      writable: true\n    },\n    _connection: {\n      value: null,\n      writable: true\n    },\n    _subscribe: {\n      value: connectableProto._subscribe\n    },\n    _isComplete: {\n      value: connectableProto._isComplete,\n      writable: true\n    },\n    getSubject: {\n      value: connectableProto.getSubject\n    },\n    connect: {\n      value: connectableProto.connect\n    },\n    refCount: {\n      value: connectableProto.refCount\n    }\n  };\n})();\n\nclass ConnectableSubscriber extends SubjectSubscriber {\n  constructor(destination, connectable) {\n    super(destination);\n    this.connectable = connectable;\n  }\n\n  _error(err) {\n    this._unsubscribe();\n\n    super._error(err);\n  }\n\n  _complete() {\n    this.connectable._isComplete = true;\n\n    this._unsubscribe();\n\n    super._complete();\n  }\n\n  _unsubscribe() {\n    const connectable = this.connectable;\n\n    if (connectable) {\n      this.connectable = null;\n      const connection = connectable._connection;\n      connectable._refCount = 0;\n      connectable._subject = null;\n      connectable._connection = null;\n\n      if (connection) {\n        connection.unsubscribe();\n      }\n    }\n  }\n\n}\n\nclass RefCountOperator {\n  constructor(connectable) {\n    this.connectable = connectable;\n  }\n\n  call(subscriber, source) {\n    const {\n      connectable\n    } = this;\n    connectable._refCount++;\n    const refCounter = new RefCountSubscriber(subscriber, connectable);\n    const subscription = source.subscribe(refCounter);\n\n    if (!refCounter.closed) {\n      refCounter.connection = connectable.connect();\n    }\n\n    return subscription;\n  }\n\n}\n\nclass RefCountSubscriber extends Subscriber {\n  constructor(destination, connectable) {\n    super(destination);\n    this.connectable = connectable;\n  }\n\n  _unsubscribe() {\n    const {\n      connectable\n    } = this;\n\n    if (!connectable) {\n      this.connection = null;\n      return;\n    }\n\n    this.connectable = null;\n    const refCount = connectable._refCount;\n\n    if (refCount <= 0) {\n      this.connection = null;\n      return;\n    }\n\n    connectable._refCount = refCount - 1;\n\n    if (refCount > 1) {\n      this.connection = null;\n      return;\n    }\n\n    const {\n      connection\n    } = this;\n    const sharedConnection = connectable._connection;\n    this.connection = null;\n\n    if (sharedConnection && (!connection || sharedConnection === connection)) {\n      sharedConnection.unsubscribe();\n    }\n  }\n\n}", "map": {"version": 3, "names": ["SubjectSubscriber", "Observable", "Subscriber", "Subscription", "refCount", "higherOrderRefCount", "ConnectableObservable", "constructor", "source", "subjectFactory", "_refCount", "_isComplete", "_subscribe", "subscriber", "getSubject", "subscribe", "subject", "_subject", "isStopped", "connect", "connection", "_connection", "add", "ConnectableSubscriber", "closed", "EMPTY", "connectableObservableDescriptor", "connectableProto", "prototype", "operator", "value", "writable", "destination", "connectable", "_error", "err", "_unsubscribe", "_complete", "unsubscribe", "RefCountOperator", "call", "refCounter", "RefCountSubscriber", "subscription", "sharedConnection"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/ConnectableObservable.js"], "sourcesContent": ["import { SubjectSubscriber } from '../Subject';\nimport { Observable } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { Subscription } from '../Subscription';\nimport { refCount as higherOrderRefCount } from '../operators/refCount';\nexport class ConnectableObservable extends Observable {\n    constructor(source, subjectFactory) {\n        super();\n        this.source = source;\n        this.subjectFactory = subjectFactory;\n        this._refCount = 0;\n        this._isComplete = false;\n    }\n    _subscribe(subscriber) {\n        return this.getSubject().subscribe(subscriber);\n    }\n    getSubject() {\n        const subject = this._subject;\n        if (!subject || subject.isStopped) {\n            this._subject = this.subjectFactory();\n        }\n        return this._subject;\n    }\n    connect() {\n        let connection = this._connection;\n        if (!connection) {\n            this._isComplete = false;\n            connection = this._connection = new Subscription();\n            connection.add(this.source\n                .subscribe(new ConnectableSubscriber(this.getSubject(), this)));\n            if (connection.closed) {\n                this._connection = null;\n                connection = Subscription.EMPTY;\n            }\n        }\n        return connection;\n    }\n    refCount() {\n        return higherOrderRefCount()(this);\n    }\n}\nexport const connectableObservableDescriptor = (() => {\n    const connectableProto = ConnectableObservable.prototype;\n    return {\n        operator: { value: null },\n        _refCount: { value: 0, writable: true },\n        _subject: { value: null, writable: true },\n        _connection: { value: null, writable: true },\n        _subscribe: { value: connectableProto._subscribe },\n        _isComplete: { value: connectableProto._isComplete, writable: true },\n        getSubject: { value: connectableProto.getSubject },\n        connect: { value: connectableProto.connect },\n        refCount: { value: connectableProto.refCount }\n    };\n})();\nclass ConnectableSubscriber extends SubjectSubscriber {\n    constructor(destination, connectable) {\n        super(destination);\n        this.connectable = connectable;\n    }\n    _error(err) {\n        this._unsubscribe();\n        super._error(err);\n    }\n    _complete() {\n        this.connectable._isComplete = true;\n        this._unsubscribe();\n        super._complete();\n    }\n    _unsubscribe() {\n        const connectable = this.connectable;\n        if (connectable) {\n            this.connectable = null;\n            const connection = connectable._connection;\n            connectable._refCount = 0;\n            connectable._subject = null;\n            connectable._connection = null;\n            if (connection) {\n                connection.unsubscribe();\n            }\n        }\n    }\n}\nclass RefCountOperator {\n    constructor(connectable) {\n        this.connectable = connectable;\n    }\n    call(subscriber, source) {\n        const { connectable } = this;\n        connectable._refCount++;\n        const refCounter = new RefCountSubscriber(subscriber, connectable);\n        const subscription = source.subscribe(refCounter);\n        if (!refCounter.closed) {\n            refCounter.connection = connectable.connect();\n        }\n        return subscription;\n    }\n}\nclass RefCountSubscriber extends Subscriber {\n    constructor(destination, connectable) {\n        super(destination);\n        this.connectable = connectable;\n    }\n    _unsubscribe() {\n        const { connectable } = this;\n        if (!connectable) {\n            this.connection = null;\n            return;\n        }\n        this.connectable = null;\n        const refCount = connectable._refCount;\n        if (refCount <= 0) {\n            this.connection = null;\n            return;\n        }\n        connectable._refCount = refCount - 1;\n        if (refCount > 1) {\n            this.connection = null;\n            return;\n        }\n        const { connection } = this;\n        const sharedConnection = connectable._connection;\n        this.connection = null;\n        if (sharedConnection && (!connection || sharedConnection === connection)) {\n            sharedConnection.unsubscribe();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,iBAAT,QAAkC,YAAlC;AACA,SAASC,UAAT,QAA2B,eAA3B;AACA,SAASC,UAAT,QAA2B,eAA3B;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,QAAQ,IAAIC,mBAArB,QAAgD,uBAAhD;AACA,OAAO,MAAMC,qBAAN,SAAoCL,UAApC,CAA+C;EAClDM,WAAW,CAACC,MAAD,EAASC,cAAT,EAAyB;IAChC;IACA,KAAKD,MAAL,GAAcA,MAAd;IACA,KAAKC,cAAL,GAAsBA,cAAtB;IACA,KAAKC,SAAL,GAAiB,CAAjB;IACA,KAAKC,WAAL,GAAmB,KAAnB;EACH;;EACDC,UAAU,CAACC,UAAD,EAAa;IACnB,OAAO,KAAKC,UAAL,GAAkBC,SAAlB,CAA4BF,UAA5B,CAAP;EACH;;EACDC,UAAU,GAAG;IACT,MAAME,OAAO,GAAG,KAAKC,QAArB;;IACA,IAAI,CAACD,OAAD,IAAYA,OAAO,CAACE,SAAxB,EAAmC;MAC/B,KAAKD,QAAL,GAAgB,KAAKR,cAAL,EAAhB;IACH;;IACD,OAAO,KAAKQ,QAAZ;EACH;;EACDE,OAAO,GAAG;IACN,IAAIC,UAAU,GAAG,KAAKC,WAAtB;;IACA,IAAI,CAACD,UAAL,EAAiB;MACb,KAAKT,WAAL,GAAmB,KAAnB;MACAS,UAAU,GAAG,KAAKC,WAAL,GAAmB,IAAIlB,YAAJ,EAAhC;MACAiB,UAAU,CAACE,GAAX,CAAe,KAAKd,MAAL,CACVO,SADU,CACA,IAAIQ,qBAAJ,CAA0B,KAAKT,UAAL,EAA1B,EAA6C,IAA7C,CADA,CAAf;;MAEA,IAAIM,UAAU,CAACI,MAAf,EAAuB;QACnB,KAAKH,WAAL,GAAmB,IAAnB;QACAD,UAAU,GAAGjB,YAAY,CAACsB,KAA1B;MACH;IACJ;;IACD,OAAOL,UAAP;EACH;;EACDhB,QAAQ,GAAG;IACP,OAAOC,mBAAmB,GAAG,IAAH,CAA1B;EACH;;AAlCiD;AAoCtD,OAAO,MAAMqB,+BAA+B,GAAG,CAAC,MAAM;EAClD,MAAMC,gBAAgB,GAAGrB,qBAAqB,CAACsB,SAA/C;EACA,OAAO;IACHC,QAAQ,EAAE;MAAEC,KAAK,EAAE;IAAT,CADP;IAEHpB,SAAS,EAAE;MAAEoB,KAAK,EAAE,CAAT;MAAYC,QAAQ,EAAE;IAAtB,CAFR;IAGHd,QAAQ,EAAE;MAAEa,KAAK,EAAE,IAAT;MAAeC,QAAQ,EAAE;IAAzB,CAHP;IAIHV,WAAW,EAAE;MAAES,KAAK,EAAE,IAAT;MAAeC,QAAQ,EAAE;IAAzB,CAJV;IAKHnB,UAAU,EAAE;MAAEkB,KAAK,EAAEH,gBAAgB,CAACf;IAA1B,CALT;IAMHD,WAAW,EAAE;MAAEmB,KAAK,EAAEH,gBAAgB,CAAChB,WAA1B;MAAuCoB,QAAQ,EAAE;IAAjD,CANV;IAOHjB,UAAU,EAAE;MAAEgB,KAAK,EAAEH,gBAAgB,CAACb;IAA1B,CAPT;IAQHK,OAAO,EAAE;MAAEW,KAAK,EAAEH,gBAAgB,CAACR;IAA1B,CARN;IASHf,QAAQ,EAAE;MAAE0B,KAAK,EAAEH,gBAAgB,CAACvB;IAA1B;EATP,CAAP;AAWH,CAb8C,GAAxC;;AAcP,MAAMmB,qBAAN,SAAoCvB,iBAApC,CAAsD;EAClDO,WAAW,CAACyB,WAAD,EAAcC,WAAd,EAA2B;IAClC,MAAMD,WAAN;IACA,KAAKC,WAAL,GAAmBA,WAAnB;EACH;;EACDC,MAAM,CAACC,GAAD,EAAM;IACR,KAAKC,YAAL;;IACA,MAAMF,MAAN,CAAaC,GAAb;EACH;;EACDE,SAAS,GAAG;IACR,KAAKJ,WAAL,CAAiBtB,WAAjB,GAA+B,IAA/B;;IACA,KAAKyB,YAAL;;IACA,MAAMC,SAAN;EACH;;EACDD,YAAY,GAAG;IACX,MAAMH,WAAW,GAAG,KAAKA,WAAzB;;IACA,IAAIA,WAAJ,EAAiB;MACb,KAAKA,WAAL,GAAmB,IAAnB;MACA,MAAMb,UAAU,GAAGa,WAAW,CAACZ,WAA/B;MACAY,WAAW,CAACvB,SAAZ,GAAwB,CAAxB;MACAuB,WAAW,CAAChB,QAAZ,GAAuB,IAAvB;MACAgB,WAAW,CAACZ,WAAZ,GAA0B,IAA1B;;MACA,IAAID,UAAJ,EAAgB;QACZA,UAAU,CAACkB,WAAX;MACH;IACJ;EACJ;;AA1BiD;;AA4BtD,MAAMC,gBAAN,CAAuB;EACnBhC,WAAW,CAAC0B,WAAD,EAAc;IACrB,KAAKA,WAAL,GAAmBA,WAAnB;EACH;;EACDO,IAAI,CAAC3B,UAAD,EAAaL,MAAb,EAAqB;IACrB,MAAM;MAAEyB;IAAF,IAAkB,IAAxB;IACAA,WAAW,CAACvB,SAAZ;IACA,MAAM+B,UAAU,GAAG,IAAIC,kBAAJ,CAAuB7B,UAAvB,EAAmCoB,WAAnC,CAAnB;IACA,MAAMU,YAAY,GAAGnC,MAAM,CAACO,SAAP,CAAiB0B,UAAjB,CAArB;;IACA,IAAI,CAACA,UAAU,CAACjB,MAAhB,EAAwB;MACpBiB,UAAU,CAACrB,UAAX,GAAwBa,WAAW,CAACd,OAAZ,EAAxB;IACH;;IACD,OAAOwB,YAAP;EACH;;AAbkB;;AAevB,MAAMD,kBAAN,SAAiCxC,UAAjC,CAA4C;EACxCK,WAAW,CAACyB,WAAD,EAAcC,WAAd,EAA2B;IAClC,MAAMD,WAAN;IACA,KAAKC,WAAL,GAAmBA,WAAnB;EACH;;EACDG,YAAY,GAAG;IACX,MAAM;MAAEH;IAAF,IAAkB,IAAxB;;IACA,IAAI,CAACA,WAAL,EAAkB;MACd,KAAKb,UAAL,GAAkB,IAAlB;MACA;IACH;;IACD,KAAKa,WAAL,GAAmB,IAAnB;IACA,MAAM7B,QAAQ,GAAG6B,WAAW,CAACvB,SAA7B;;IACA,IAAIN,QAAQ,IAAI,CAAhB,EAAmB;MACf,KAAKgB,UAAL,GAAkB,IAAlB;MACA;IACH;;IACDa,WAAW,CAACvB,SAAZ,GAAwBN,QAAQ,GAAG,CAAnC;;IACA,IAAIA,QAAQ,GAAG,CAAf,EAAkB;MACd,KAAKgB,UAAL,GAAkB,IAAlB;MACA;IACH;;IACD,MAAM;MAAEA;IAAF,IAAiB,IAAvB;IACA,MAAMwB,gBAAgB,GAAGX,WAAW,CAACZ,WAArC;IACA,KAAKD,UAAL,GAAkB,IAAlB;;IACA,IAAIwB,gBAAgB,KAAK,CAACxB,UAAD,IAAewB,gBAAgB,KAAKxB,UAAzC,CAApB,EAA0E;MACtEwB,gBAAgB,CAACN,WAAjB;IACH;EACJ;;AA5BuC"}, "metadata": {}, "sourceType": "module"}