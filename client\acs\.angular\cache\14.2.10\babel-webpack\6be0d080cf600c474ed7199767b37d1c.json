{"ast": null, "code": "export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "map": {"version": 3, "names": ["top", "bottom", "right", "left", "auto", "basePlacements", "start", "end", "clippingParents", "viewport", "popper", "reference", "variationPlacements", "reduce", "acc", "placement", "concat", "placements", "beforeRead", "read", "afterRead", "<PERSON><PERSON><PERSON>", "main", "<PERSON><PERSON><PERSON>", "beforeWrite", "write", "afterWrite", "modifierPhases"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/enums.js"], "sourcesContent": ["export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];"], "mappings": "AAAA,OAAO,IAAIA,GAAG,GAAG,KAAV;AACP,OAAO,IAAIC,MAAM,GAAG,QAAb;AACP,OAAO,IAAIC,KAAK,GAAG,OAAZ;AACP,OAAO,IAAIC,IAAI,GAAG,MAAX;AACP,OAAO,IAAIC,IAAI,GAAG,MAAX;AACP,OAAO,IAAIC,cAAc,GAAG,CAACL,GAAD,EAAMC,MAAN,EAAcC,KAAd,EAAqBC,IAArB,CAArB;AACP,OAAO,IAAIG,KAAK,GAAG,OAAZ;AACP,OAAO,IAAIC,GAAG,GAAG,KAAV;AACP,OAAO,IAAIC,eAAe,GAAG,iBAAtB;AACP,OAAO,IAAIC,QAAQ,GAAG,UAAf;AACP,OAAO,IAAIC,MAAM,GAAG,QAAb;AACP,OAAO,IAAIC,SAAS,GAAG,WAAhB;AACP,OAAO,IAAIC,mBAAmB,GAAG,aAAaP,cAAc,CAACQ,MAAf,CAAsB,UAAUC,GAAV,EAAeC,SAAf,EAA0B;EAC5F,OAAOD,GAAG,CAACE,MAAJ,CAAW,CAACD,SAAS,GAAG,GAAZ,GAAkBT,KAAnB,EAA0BS,SAAS,GAAG,GAAZ,GAAkBR,GAA5C,CAAX,CAAP;AACD,CAF6C,EAE3C,EAF2C,CAAvC;AAGP,OAAO,IAAIU,UAAU,GAAG,aAAa,GAAGD,MAAH,CAAUX,cAAV,EAA0B,CAACD,IAAD,CAA1B,EAAkCS,MAAlC,CAAyC,UAAUC,GAAV,EAAeC,SAAf,EAA0B;EACtG,OAAOD,GAAG,CAACE,MAAJ,CAAW,CAACD,SAAD,EAAYA,SAAS,GAAG,GAAZ,GAAkBT,KAA9B,EAAqCS,SAAS,GAAG,GAAZ,GAAkBR,GAAvD,CAAX,CAAP;AACD,CAFoC,EAElC,EAFkC,CAA9B,C,CAEC;;AAER,OAAO,IAAIW,UAAU,GAAG,YAAjB;AACP,OAAO,IAAIC,IAAI,GAAG,MAAX;AACP,OAAO,IAAIC,SAAS,GAAG,WAAhB,C,CAA6B;;AAEpC,OAAO,IAAIC,UAAU,GAAG,YAAjB;AACP,OAAO,IAAIC,IAAI,GAAG,MAAX;AACP,OAAO,IAAIC,SAAS,GAAG,WAAhB,C,CAA6B;;AAEpC,OAAO,IAAIC,WAAW,GAAG,aAAlB;AACP,OAAO,IAAIC,KAAK,GAAG,OAAZ;AACP,OAAO,IAAIC,UAAU,GAAG,YAAjB;AACP,OAAO,IAAIC,cAAc,GAAG,CAACT,UAAD,EAAaC,IAAb,EAAmBC,SAAnB,EAA8BC,UAA9B,EAA0CC,IAA1C,EAAgDC,SAAhD,EAA2DC,WAA3D,EAAwEC,KAAxE,EAA+EC,UAA/E,CAArB"}, "metadata": {}, "sourceType": "module"}