{"ast": null, "code": "import { observable as Symbol_observable } from '../symbol/observable';\nexport const subscribeToObservable = obj => subscriber => {\n  const obs = obj[Symbol_observable]();\n\n  if (typeof obs.subscribe !== 'function') {\n    throw new TypeError('Provided object does not correctly implement Symbol.observable');\n  } else {\n    return obs.subscribe(subscriber);\n  }\n};", "map": {"version": 3, "names": ["observable", "Symbol_observable", "subscribeToObservable", "obj", "subscriber", "obs", "subscribe", "TypeError"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/subscribeToObservable.js"], "sourcesContent": ["import { observable as Symbol_observable } from '../symbol/observable';\nexport const subscribeToObservable = (obj) => (subscriber) => {\n    const obs = obj[Symbol_observable]();\n    if (typeof obs.subscribe !== 'function') {\n        throw new TypeError('Provided object does not correctly implement Symbol.observable');\n    }\n    else {\n        return obs.subscribe(subscriber);\n    }\n};\n"], "mappings": "AAAA,SAASA,UAAU,IAAIC,iBAAvB,QAAgD,sBAAhD;AACA,OAAO,MAAMC,qBAAqB,GAAIC,GAAD,IAAUC,UAAD,IAAgB;EAC1D,MAAMC,GAAG,GAAGF,GAAG,CAACF,iBAAD,CAAH,EAAZ;;EACA,IAAI,OAAOI,GAAG,CAACC,SAAX,KAAyB,UAA7B,EAAyC;IACrC,MAAM,IAAIC,SAAJ,CAAc,gEAAd,CAAN;EACH,CAFD,MAGK;IACD,OAAOF,GAAG,CAACC,SAAJ,CAAcF,UAAd,CAAP;EACH;AACJ,CARM"}, "metadata": {}, "sourceType": "module"}