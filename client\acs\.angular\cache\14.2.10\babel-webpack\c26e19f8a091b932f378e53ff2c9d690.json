{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./name-check-reservation.component.html?ngResource\";\nimport { Component, ViewChild } from '@angular/core';\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { StateSelectorComponent } from 'src/app/Modules/Shared/Components/formComponents/stateSelector/state-selector.component';\nimport { CorporationTypeSelectorComponent } from 'src/app/Modules/Shared/Components/formComponents/corporation-type-selector/corporation-type-selector.component';\nimport { FilingApiService } from '../../Services/FilingApiService';\nimport { NameCheckAndService } from '../../Models/NameCheckAndService';\nimport { forkJoin } from 'rxjs';\nimport { ToastrService } from 'ngx-toastr';\nimport { ActivatedRoute } from '@angular/router';\nimport { LoadFilingService } from '../../Functions/filing-service-load.func';\nimport { FilingServiceResponseObject } from '../../Enums/filing-service-enum';\nimport { FormModeService } from 'src/app/Modules/Shared/Services/Common/FormModeService';\nimport { CustomSharedValidations } from 'src/app/Modules/Shared/functions/custom-validations';\nimport { FilingInfoService } from '../../Services/filing-price.service';\nimport { StatePriceRequest } from 'src/app/Modules/Shared/Models/Common/state-price-request.model';\nimport { PageTitleService } from 'src/app/Modules/Shared/Services/Common/page-title.service';\nlet NameCheckAndReservationComponent = class NameCheckAndReservationComponent {\n  constructor(filingInfoService, Api, formMode, toaster, activatedRoute, pageTitleService) {\n    this.filingInfoService = filingInfoService;\n    this.Api = Api;\n    this.formMode = formMode;\n    this.toaster = toaster;\n    this.activatedRoute = activatedRoute;\n    this.pageTitleService = pageTitleService;\n    this.NameCheckAndReservation = new NameCheckAndService();\n    this.businessEntityTypes = [];\n    this.businessEntities = [];\n    this.reservedEntityValues = [];\n    this.PleaseSelectOne = [];\n    this.States = [];\n    this.Form = new FormGroup({\n      SelectOne: new FormControl(null, [Validators.required]),\n      FormationState: new FormControl(null, [Validators.required]),\n      FormationType: new FormControl(null, [Validators.required]),\n      ProposedName1: new FormControl(null, [Validators.required]),\n      ProposedName2: new FormControl(null, [Validators.required]),\n      ProposedName3: new FormControl(null),\n      BusinessEntityType: new FormControl(null),\n      ReservedEntityName: new FormControl(null),\n      SuspendedForfeitedEntityNo: new FormControl(null, [Validators.required]),\n      ReserveNameTo: new FormControl(null, [Validators.required]),\n      ReserveNameAddress: new FormControl(null, [Validators.required]),\n      City: new FormControl(null, [Validators.required]),\n      State: new FormControl(null, [Validators.required]),\n      Zip: new FormControl(null, [Validators.required, CustomSharedValidations.zipLength]),\n      Remarks: new FormControl(null, [CustomSharedValidations.specialInstructions])\n    });\n  }\n\n  ngOnInit() {\n    this.filingInfoService.getLabel(this.filingInfoService.SubCatCode.namecheckreservation, 'FOT').subscribe(label => {\n      this.pageTitleService.setPageTitle(label);\n    }); // Get All States\n\n    this.filingInfoService.getPrice(this.filingInfoService.SubCatCode.namecheckreservation).subscribe(res => {\n      this.price = res;\n      this.basePrice = res;\n    }); //Get all States from store if store is empty call states API\n\n    this.Api.FCGetStates().subscribe(States => {\n      this.States = States;\n      this.StateComponent.States = States;\n    });\n    let fillValues = forkJoin([this.Api.GetRegisteredAgent_SelectOne(), this.Api.GetFormationType(\"909\"), //this.Api.GetEntityType('FOT','909'),\n    this.Api.GetBusinessEntityType(), this.Api.GetAdditionalFormationType(\"909\")]);\n    fillValues.subscribe(response => {\n      this.PleaseSelectOne = response[0] || [];\n      this.CorpTypeComponent.CorporationTypes = response[1] || [];\n      this.reservedEntityValues = response[2] || [];\n      this.businessEntities = response[3] || [];\n      this.MapAllMasterData(this.reservedEntityValues);\n      this.StartLoad();\n    });\n    this.CorpTypeComponent.Caption = \"2. Please select the type of formation.\"; // Display Corporation Type after state selection\n\n    this.StateComponent.$OnStateSelection.subscribe(SelectedState => {\n      this.CorpTypeComponent.reset();\n      this.NameCheckAndReservation.FormationState = SelectedState; //this.NameCheckAndReservation.FormationType = null;\n\n      this.Form.controls.FormationType.reset();\n\n      if (SelectedState) {\n        this.CorpTypeComponent.IsVisible = true;\n        this.getStatePrice(SelectedState);\n      } else {\n        this.CorpTypeComponent.IsVisible = false;\n      }\n    });\n    this.CorpTypeComponent.$OnCorportationTypeSelection.subscribe(corporationType => {\n      this.NameCheckAndReservation.FormationType = corporationType;\n    });\n    this.Form.controls.FormationType.valueChanges.subscribe(value => {\n      this.CorpTypeComponent.CorporationTypes.map(val => {\n        if (val.corporationTypeCode == value) {\n          this.businessEntityTypes = this.businessEntities.filter(data => {\n            return data.code == val.corpCode;\n          });\n        }\n      });\n    });\n  }\n\n  getStatePrice(state) {\n    var request = new StatePriceRequest();\n    request.ProductCode = 'FS';\n    request.CategoryCode = 'FOT';\n    request.SubCategoryCode = '909';\n    request.State = state;\n    this.Api.GetStateWisePrice(request).subscribe(res => {\n      this.price = res.price > 0 ? res.price : this.basePrice;\n    });\n  }\n\n  OnSave() {\n    try {\n      if (this.Form.valid) {\n        this.Save();\n      }\n    } catch (ex) {\n      console.error(ex);\n    }\n  }\n\n  StartLoad() {\n    var _this = this;\n\n    this.activatedRoute.queryParams.subscribe( /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (queryString) {\n        LoadFilingService(queryString, _this.Api, FilingServiceResponseObject.NameCheckReservation, _this.formMode).then(serviceData => {\n          _this.NameCheckAndReservation = serviceData;\n\n          _this.Load();\n        }).catch(e => console.log(e));\n      });\n\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  }\n\n  Save() {\n    this.NameCheckAndReservation.FormationState = this.Form.controls.FormationState.value;\n    this.NameCheckAndReservation.FormationType = this.Form.controls.FormationType.value;\n    this.NameCheckAndReservation.SelectOne = this.Form.controls.SelectOne.value;\n    this.NameCheckAndReservation.ProposedName1 = this.Form.controls.ProposedName1.value;\n    this.NameCheckAndReservation.ProposedName2 = this.Form.controls.ProposedName2.value;\n    this.NameCheckAndReservation.ProposedName3 = this.Form.controls.ProposedName3.value;\n    this.NameCheckAndReservation.BusinessEntityType = this.Form.controls.BusinessEntityType.value;\n    this.NameCheckAndReservation.ReservedEntityName = this.Form.controls.ReservedEntityName.value;\n    this.NameCheckAndReservation.EntityNumber = this.Form.controls.SuspendedForfeitedEntityNo.value;\n    this.NameCheckAndReservation.ReserveNameTo = this.Form.controls.ReserveNameTo.value;\n    this.NameCheckAndReservation.ReserveNameAddress = this.Form.controls.ReserveNameAddress.value;\n    this.NameCheckAndReservation.City = this.Form.controls.City.value;\n    this.NameCheckAndReservation.State = this.Form.controls.State.value;\n    this.NameCheckAndReservation.Zip = this.Form.controls.Zip.value;\n    this.NameCheckAndReservation.Remarks = this.Form.controls.Remarks.value;\n    this.Api.SaveFilingService({\n      nameCheckRes: this.NameCheckAndReservation\n    }).subscribe(x => {\n      this.Form.reset();\n    });\n  }\n\n  Load() {\n    this.getStatePrice(this.NameCheckAndReservation.FormationState);\n    this.Form.controls.FormationState.setValue(this.NameCheckAndReservation.FormationState);\n    this.Form.controls.FormationType.setValue(this.NameCheckAndReservation.FormationType);\n    this.Form.controls.SelectOne.setValue(this.NameCheckAndReservation.SelectOne);\n    this.Form.controls.ProposedName1.setValue(this.NameCheckAndReservation.ProposedName1);\n    this.Form.controls.ProposedName2.setValue(this.NameCheckAndReservation.ProposedName2);\n    this.Form.controls.ProposedName3.setValue(this.NameCheckAndReservation.ProposedName3);\n    this.Form.controls.BusinessEntityType.setValue(this.NameCheckAndReservation.BusinessEntityType);\n    this.Form.controls.ReservedEntityName.setValue(this.NameCheckAndReservation.ReservedEntityName);\n    this.Form.controls.SuspendedForfeitedEntityNo.setValue(this.NameCheckAndReservation.EntityNumber);\n    this.Form.controls.ReserveNameTo.setValue(this.NameCheckAndReservation.ReserveNameTo);\n    this.Form.controls.ReserveNameAddress.setValue(this.NameCheckAndReservation.ReserveNameAddress);\n    this.Form.controls.City.setValue(this.NameCheckAndReservation.City);\n    this.Form.controls.State.setValue(this.NameCheckAndReservation.State);\n    this.Form.controls.Zip.setValue(this.NameCheckAndReservation.Zip);\n    this.Form.controls.Remarks.setValue(this.NameCheckAndReservation.Remarks);\n  }\n\n  MapAllMasterData(data) {\n    this.reservedEntityValues = this.MapMasterDataByControlId(data, \"Reserved_Entity_Values\");\n  }\n\n  MapMasterDataByControlId(data, controlId) {\n    let mappedData = data.filter(obj => obj.controlID === controlId).map(item => {\n      var data = {\n        id: item.controlValue,\n        value: item.controlText\n      };\n      return data;\n    });\n    return mappedData || [];\n  }\n\n};\n\nNameCheckAndReservationComponent.ctorParameters = () => [{\n  type: FilingInfoService\n}, {\n  type: FilingApiService\n}, {\n  type: FormModeService\n}, {\n  type: ToastrService\n}, {\n  type: ActivatedRoute\n}, {\n  type: PageTitleService\n}];\n\nNameCheckAndReservationComponent.propDecorators = {\n  StateComponent: [{\n    type: ViewChild,\n    args: [StateSelectorComponent, {\n      static: true\n    }]\n  }],\n  CorpTypeComponent: [{\n    type: ViewChild,\n    args: [CorporationTypeSelectorComponent, {\n      static: true\n    }]\n  }]\n};\nNameCheckAndReservationComponent = __decorate([Component({\n  selector: 'name-check-reservation',\n  template: __NG_CLI_RESOURCE__0\n})], NameCheckAndReservationComponent);\nexport { NameCheckAndReservationComponent };", "map": {"version": 3, "mappings": ";;;AAAA,SAASA,SAAT,EAA4BC,SAA5B,QAA6C,eAA7C;AACA,SAASC,WAAT,EAAsBC,SAAtB,EAAiCC,UAAjC,QAAmD,gBAAnD;AACA,SAASC,sBAAT,QAAuC,yFAAvC;AACA,SAASC,gCAAT,QAAiD,gHAAjD;AACA,SAASC,gBAAT,QAAiC,iCAAjC;AACA,SAASC,mBAAT,QAAoC,kCAApC;AACA,SAASC,QAAT,QAAyB,MAAzB;AACA,SAASC,aAAT,QAA8B,YAA9B;AACA,SAASC,cAAT,QAA+B,iBAA/B;AACA,SAASC,iBAAT,QAAkC,0CAAlC;AACA,SAASC,2BAAT,QAA4C,iCAA5C;AACA,SAASC,eAAT,QAAgC,wDAAhC;AACA,SAASC,uBAAT,QAAwC,qDAAxC;AACA,SAASC,iBAAT,QAAkC,qCAAlC;AACA,SAASC,iBAAT,QAAkC,gEAAlC;AACA,SAASC,gBAAT,QAAiC,2DAAjC;IAOaC,gCAAgC,SAAhCA,gCAAgC;EAQ3CC,YAAmBC,iBAAnB,EAAiEC,GAAjE,EAA+FC,QAA/F,EAAkIC,OAAlI,EAAkKC,cAAlK,EAA0MC,gBAA1M,EAA4O;IAAzN;IAA8C;IAA8B;IAAmC;IAAgC;IAAwC;IAL1M,+BAA+C,IAAIlB,mBAAJ,EAA/C;IAOA,2BAA6B,EAA7B;IACA,wBAA0B,EAA1B;IACA,4BAA8B,EAA9B;IACA,uBAAyB,EAAzB;IACA,cAAgB,EAAhB;IAsFA,YAAO,IAAIL,SAAJ,CAAc;MACnBwB,SAAS,EAAE,IAAIzB,WAAJ,CAA+B,IAA/B,EAAqC,CAACE,UAAU,CAACwB,QAAZ,CAArC,CADQ;MAEnBC,cAAc,EAAE,IAAI3B,WAAJ,CAA+B,IAA/B,EAAqC,CAACE,UAAU,CAACwB,QAAZ,CAArC,CAFG;MAGnBE,aAAa,EAAE,IAAI5B,WAAJ,CAA+B,IAA/B,EAAqC,CAACE,UAAU,CAACwB,QAAZ,CAArC,CAHI;MAInBG,aAAa,EAAE,IAAI7B,WAAJ,CAA+B,IAA/B,EAAqC,CAACE,UAAU,CAACwB,QAAZ,CAArC,CAJI;MAKnBI,aAAa,EAAE,IAAI9B,WAAJ,CAA+B,IAA/B,EAAqC,CAACE,UAAU,CAACwB,QAAZ,CAArC,CALI;MAMnBK,aAAa,EAAE,IAAI/B,WAAJ,CAA+B,IAA/B,CANI;MAOnBgC,kBAAkB,EAAE,IAAIhC,WAAJ,CAA+B,IAA/B,CAPD;MAQnBiC,kBAAkB,EAAE,IAAIjC,WAAJ,CAA+B,IAA/B,CARD;MASnBkC,0BAA0B,EAAE,IAAIlC,WAAJ,CAA+B,IAA/B,EAAqC,CAACE,UAAU,CAACwB,QAAZ,CAArC,CATT;MAUnBS,aAAa,EAAE,IAAInC,WAAJ,CAA+B,IAA/B,EAAqC,CAACE,UAAU,CAACwB,QAAZ,CAArC,CAVI;MAWnBU,kBAAkB,EAAE,IAAIpC,WAAJ,CAA+B,IAA/B,EAAqC,CAACE,UAAU,CAACwB,QAAZ,CAArC,CAXD;MAYnBW,IAAI,EAAE,IAAIrC,WAAJ,CAA+B,IAA/B,EAAqC,CAACE,UAAU,CAACwB,QAAZ,CAArC,CAZa;MAanBY,KAAK,EAAE,IAAItC,WAAJ,CAA+B,IAA/B,EAAqC,CAACE,UAAU,CAACwB,QAAZ,CAArC,CAbY;MAcnBa,GAAG,EAAE,IAAIvC,WAAJ,CAA+B,IAA/B,EAAqC,CAACE,UAAU,CAACwB,QAAZ,EAAsBb,uBAAuB,CAAC2B,SAA9C,CAArC,CAdc;MAenBC,OAAO,EAAE,IAAIzC,WAAJ,CAA+B,IAA/B,EAAqC,CAACa,uBAAuB,CAAC6B,mBAAzB,CAArC;IAfU,CAAd,CAAP;EA5FiP;;EAUjPC,QAAQ;IACN,KAAKxB,iBAAL,CAAuByB,QAAvB,CAAgC,KAAKzB,iBAAL,CAAuB0B,UAAvB,CAAkCC,oBAAlE,EAAwF,KAAxF,EAA+FC,SAA/F,CAAyGC,KAAK,IAAG;MAC/G,KAAKxB,gBAAL,CAAsByB,YAAtB,CAAmCD,KAAnC;IACD,CAFD,EADM,CAIN;;IACA,KAAK7B,iBAAL,CAAuB+B,QAAvB,CAAgC,KAAK/B,iBAAL,CAAuB0B,UAAvB,CAAkCC,oBAAlE,EAAwFC,SAAxF,CAAkGI,GAAG,IAAG;MACtG,KAAKC,KAAL,GAAaD,GAAb;MACA,KAAKE,SAAL,GAAiBF,GAAjB;IACD,CAHD,EALM,CAUN;;IAEA,KAAK/B,GAAL,CAASkC,WAAT,GAAuBP,SAAvB,CAAkCQ,MAAD,IAAW;MAC1C,KAAKA,MAAL,GAAcA,MAAd;MACA,KAAKC,cAAL,CAAoBD,MAApB,GAA6BA,MAA7B;IACD,CAHD;IAMA,IAAIE,UAAU,GAAGlD,QAAQ,CAAC,CACxB,KAAKa,GAAL,CAASsC,4BAAT,EADwB,EAExB,KAAKtC,GAAL,CAASuC,gBAAT,CAA0B,KAA1B,CAFwB,EAGxB;IACA,KAAKvC,GAAL,CAASwC,qBAAT,EAJwB,EAKxB,KAAKxC,GAAL,CAASyC,0BAAT,CAAoC,KAApC,CALwB,CAAD,CAAzB;IAQAJ,UAAU,CAACV,SAAX,CAAqBe,QAAQ,IAAG;MAE9B,KAAKC,eAAL,GAAuBD,QAAQ,CAAC,CAAD,CAAR,IAAe,EAAtC;MACA,KAAKE,iBAAL,CAAuBC,gBAAvB,GAA0CH,QAAQ,CAAC,CAAD,CAAR,IAAe,EAAzD;MACA,KAAKI,oBAAL,GAA4BJ,QAAQ,CAAC,CAAD,CAAR,IAAe,EAA3C;MACA,KAAKK,gBAAL,GAAwBL,QAAQ,CAAC,CAAD,CAAR,IAAe,EAAvC;MACA,KAAKM,gBAAL,CAAsB,KAAKF,oBAA3B;MACA,KAAKG,SAAL;IACD,CARD;IAUA,KAAKL,iBAAL,CAAuBM,OAAvB,GAAiC,yCAAjC,CApCM,CAqCN;;IACA,KAAKd,cAAL,CAAoBe,iBAApB,CAAsCxB,SAAtC,CAAgDyB,aAAa,IAAG;MAE9D,KAAKR,iBAAL,CAAuBS,KAAvB;MACA,KAAKC,uBAAL,CAA6B/C,cAA7B,GAA8C6C,aAA9C,CAH8D,CAI9D;;MAEA,KAAKG,IAAL,CAAUC,QAAV,CAAmBhD,aAAnB,CAAiC6C,KAAjC;;MAEA,IAAID,aAAJ,EAAmB;QACjB,KAAKR,iBAAL,CAAuBa,SAAvB,GAAmC,IAAnC;QACA,KAAKC,aAAL,CAAmBN,aAAnB;MACD,CAHD,MAIK;QACH,KAAKR,iBAAL,CAAuBa,SAAvB,GAAmC,KAAnC;MACD;IACF,CAfD;IAiBA,KAAKb,iBAAL,CAAuBe,4BAAvB,CAAoDhC,SAApD,CAA8DiC,eAAe,IAAG;MAC9E,KAAKN,uBAAL,CAA6B9C,aAA7B,GAA6CoD,eAA7C;IACD,CAFD;IAIA,KAAKL,IAAL,CAAUC,QAAV,CAAmBhD,aAAnB,CAAiCqD,YAAjC,CAA8ClC,SAA9C,CAAwDmC,KAAK,IAAG;MAC9D,KAAKlB,iBAAL,CAAuBC,gBAAvB,CAAwCkB,GAAxC,CAA4CC,GAAG,IAAG;QAChD,IAAIA,GAAG,CAACC,mBAAJ,IAA2BH,KAA/B,EAAsC;UACpC,KAAKI,mBAAL,GAA2B,KAAKnB,gBAAL,CAAsBoB,MAAtB,CAA6BC,IAAI,IAAG;YAC7D,OAAOA,IAAI,CAACC,IAAL,IAAaL,GAAG,CAACM,QAAxB;UACD,CAF0B,CAA3B;QAGD;MACF,CAND;IAOD,CARD;EAUD;;EAEDZ,aAAa,CAACa,KAAD,EAAM;IACjB,IAAIC,OAAO,GAAG,IAAI7E,iBAAJ,EAAd;IACA6E,OAAO,CAACC,WAAR,GAAsB,IAAtB;IACAD,OAAO,CAACE,YAAR,GAAuB,KAAvB;IACAF,OAAO,CAACG,eAAR,GAA0B,KAA1B;IACAH,OAAO,CAACtD,KAAR,GAAgBqD,KAAhB;IACA,KAAKvE,GAAL,CAAS4E,iBAAT,CAA2BJ,OAA3B,EAAoC7C,SAApC,CAA8CI,GAAG,IAAG;MAClD,KAAKC,KAAL,GAAaD,GAAG,CAACC,KAAJ,GAAY,CAAZ,GAAgBD,GAAG,CAACC,KAApB,GAA4B,KAAKC,SAA9C;IACD,CAFD;EAGD;;EAoBD4C,MAAM;IACJ,IAAI;MACF,IAAI,KAAKtB,IAAL,CAAUuB,KAAd,EAAqB;QACnB,KAAKC,IAAL;MACD;IACF,CAJD,CAKA,OAAOC,EAAP,EAAW;MACTC,OAAO,CAACC,KAAR,CAAcF,EAAd;IACD;EACF;;EAED/B,SAAS;IAAA;;IACP,KAAK9C,cAAL,CAAoBgF,WAApB,CAAgCxD,SAAhC;MAAA,6BAA0C,WAAMyD,WAAN,EAAoB;QAE5D9F,iBAAiB,CAAsB8F,WAAtB,EAAmC,KAAI,CAACpF,GAAxC,EAA6CT,2BAA2B,CAAC8F,oBAAzE,EAA+F,KAAI,CAACpF,QAApG,CAAjB,CACGqF,IADH,CACQC,WAAW,IAAG;UAElB,KAAI,CAACjC,uBAAL,GAA+BiC,WAA/B;;UACA,KAAI,CAACC,IAAL;QACD,CALH,EAKKC,KALL,CAKWC,CAAC,IAAIT,OAAO,CAACU,GAAR,CAAYD,CAAZ,CALhB;MAMD,CARD;;MAAA;QAAA;MAAA;IAAA;EASD;;EAEDX,IAAI;IACF,KAAKzB,uBAAL,CAA6B/C,cAA7B,GAA8C,KAAKgD,IAAL,CAAUC,QAAV,CAAmBjD,cAAnB,CAAkCuD,KAAhF;IACA,KAAKR,uBAAL,CAA6B9C,aAA7B,GAA6C,KAAK+C,IAAL,CAAUC,QAAV,CAAmBhD,aAAnB,CAAiCsD,KAA9E;IACA,KAAKR,uBAAL,CAA6BjD,SAA7B,GAAyC,KAAKkD,IAAL,CAAUC,QAAV,CAAmBnD,SAAnB,CAA6ByD,KAAtE;IACA,KAAKR,uBAAL,CAA6B7C,aAA7B,GAA6C,KAAK8C,IAAL,CAAUC,QAAV,CAAmB/C,aAAnB,CAAiCqD,KAA9E;IACA,KAAKR,uBAAL,CAA6B5C,aAA7B,GAA6C,KAAK6C,IAAL,CAAUC,QAAV,CAAmB9C,aAAnB,CAAiCoD,KAA9E;IACA,KAAKR,uBAAL,CAA6B3C,aAA7B,GAA6C,KAAK4C,IAAL,CAAUC,QAAV,CAAmB7C,aAAnB,CAAiCmD,KAA9E;IACA,KAAKR,uBAAL,CAA6B1C,kBAA7B,GAAkD,KAAK2C,IAAL,CAAUC,QAAV,CAAmB5C,kBAAnB,CAAsCkD,KAAxF;IACA,KAAKR,uBAAL,CAA6BzC,kBAA7B,GAAkD,KAAK0C,IAAL,CAAUC,QAAV,CAAmB3C,kBAAnB,CAAsCiD,KAAxF;IACA,KAAKR,uBAAL,CAA6BsC,YAA7B,GAA4C,KAAKrC,IAAL,CAAUC,QAAV,CAAmB1C,0BAAnB,CAA8CgD,KAA1F;IACA,KAAKR,uBAAL,CAA6BvC,aAA7B,GAA6C,KAAKwC,IAAL,CAAUC,QAAV,CAAmBzC,aAAnB,CAAiC+C,KAA9E;IACA,KAAKR,uBAAL,CAA6BtC,kBAA7B,GAAkD,KAAKuC,IAAL,CAAUC,QAAV,CAAmBxC,kBAAnB,CAAsC8C,KAAxF;IACA,KAAKR,uBAAL,CAA6BrC,IAA7B,GAAoC,KAAKsC,IAAL,CAAUC,QAAV,CAAmBvC,IAAnB,CAAwB6C,KAA5D;IACA,KAAKR,uBAAL,CAA6BpC,KAA7B,GAAqC,KAAKqC,IAAL,CAAUC,QAAV,CAAmBtC,KAAnB,CAAyB4C,KAA9D;IACA,KAAKR,uBAAL,CAA6BnC,GAA7B,GAAmC,KAAKoC,IAAL,CAAUC,QAAV,CAAmBrC,GAAnB,CAAuB2C,KAA1D;IACA,KAAKR,uBAAL,CAA6BjC,OAA7B,GAAuC,KAAKkC,IAAL,CAAUC,QAAV,CAAmBnC,OAAnB,CAA2ByC,KAAlE;IAEA,KAAK9D,GAAL,CAAS6F,iBAAT,CAA2B;MAAEC,YAAY,EAAE,KAAKxC;IAArB,CAA3B,EAA2E3B,SAA3E,CAAqFoE,CAAC,IAAG;MACvF,KAAKxC,IAAL,CAAUF,KAAV;IACD,CAFD;EAGD;;EAEDmC,IAAI;IACF,KAAK9B,aAAL,CAAmB,KAAKJ,uBAAL,CAA6B/C,cAAhD;IAEA,KAAKgD,IAAL,CAAUC,QAAV,CAAmBjD,cAAnB,CAAkCyF,QAAlC,CAA2C,KAAK1C,uBAAL,CAA6B/C,cAAxE;IACA,KAAKgD,IAAL,CAAUC,QAAV,CAAmBhD,aAAnB,CAAiCwF,QAAjC,CAA0C,KAAK1C,uBAAL,CAA6B9C,aAAvE;IACA,KAAK+C,IAAL,CAAUC,QAAV,CAAmBnD,SAAnB,CAA6B2F,QAA7B,CAAsC,KAAK1C,uBAAL,CAA6BjD,SAAnE;IACA,KAAKkD,IAAL,CAAUC,QAAV,CAAmB/C,aAAnB,CAAiCuF,QAAjC,CAA0C,KAAK1C,uBAAL,CAA6B7C,aAAvE;IACA,KAAK8C,IAAL,CAAUC,QAAV,CAAmB9C,aAAnB,CAAiCsF,QAAjC,CAA0C,KAAK1C,uBAAL,CAA6B5C,aAAvE;IACA,KAAK6C,IAAL,CAAUC,QAAV,CAAmB7C,aAAnB,CAAiCqF,QAAjC,CAA0C,KAAK1C,uBAAL,CAA6B3C,aAAvE;IACA,KAAK4C,IAAL,CAAUC,QAAV,CAAmB5C,kBAAnB,CAAsCoF,QAAtC,CAA+C,KAAK1C,uBAAL,CAA6B1C,kBAA5E;IACA,KAAK2C,IAAL,CAAUC,QAAV,CAAmB3C,kBAAnB,CAAsCmF,QAAtC,CAA+C,KAAK1C,uBAAL,CAA6BzC,kBAA5E;IACA,KAAK0C,IAAL,CAAUC,QAAV,CAAmB1C,0BAAnB,CAA8CkF,QAA9C,CAAuD,KAAK1C,uBAAL,CAA6BsC,YAApF;IACA,KAAKrC,IAAL,CAAUC,QAAV,CAAmBzC,aAAnB,CAAiCiF,QAAjC,CAA0C,KAAK1C,uBAAL,CAA6BvC,aAAvE;IACA,KAAKwC,IAAL,CAAUC,QAAV,CAAmBxC,kBAAnB,CAAsCgF,QAAtC,CAA+C,KAAK1C,uBAAL,CAA6BtC,kBAA5E;IACA,KAAKuC,IAAL,CAAUC,QAAV,CAAmBvC,IAAnB,CAAwB+E,QAAxB,CAAiC,KAAK1C,uBAAL,CAA6BrC,IAA9D;IACA,KAAKsC,IAAL,CAAUC,QAAV,CAAmBtC,KAAnB,CAAyB8E,QAAzB,CAAkC,KAAK1C,uBAAL,CAA6BpC,KAA/D;IACA,KAAKqC,IAAL,CAAUC,QAAV,CAAmBrC,GAAnB,CAAuB6E,QAAvB,CAAgC,KAAK1C,uBAAL,CAA6BnC,GAA7D;IACA,KAAKoC,IAAL,CAAUC,QAAV,CAAmBnC,OAAnB,CAA2B2E,QAA3B,CAAoC,KAAK1C,uBAAL,CAA6BjC,OAAjE;EACD;;EAED2B,gBAAgB,CAACoB,IAAD,EAAY;IAC1B,KAAKtB,oBAAL,GAA4B,KAAKmD,wBAAL,CAA8B7B,IAA9B,EAAoC,wBAApC,CAA5B;EACD;;EAED6B,wBAAwB,CAAC7B,IAAD,EAAc8B,SAAd,EAA4B;IAClD,IAAIC,UAAU,GAAG/B,IAAI,CAClBD,MADc,CACNiC,GAAD,IAASA,GAAG,CAACC,SAAJ,KAAkBH,SADpB,EAEdnC,GAFc,CAETuC,IAAD,IAAS;MACZ,IAAIlC,IAAI,GAAG;QAAEmC,EAAE,EAAED,IAAI,CAACE,YAAX;QAAyB1C,KAAK,EAAEwC,IAAI,CAACG;MAArC,CAAX;MACA,OAAOrC,IAAP;IACD,CALc,CAAjB;IAMA,OAAO+B,UAAU,IAAI,EAArB;EACD;;AAnM0C;;;;;;;;;;;;;;;;;;UAK1CxH;IAAS+H,OAAC3H,sBAAD,EAAyB;MAAE4H,MAAM,EAAE;IAAV,CAAzB;;;UACThI;IAAS+H,OAAC1H,gCAAD,EAAmC;MAAE2H,MAAM,EAAE;IAAV,CAAnC;;;AANC9G,gCAAgC,eAJ5CnB,SAAS,CAAC;EACTkI,QAAQ,EAAE,wBADD;EAETC;AAFS,CAAD,CAImC,GAAhChH,gCAAgC,CAAhC;SAAAA", "names": ["Component", "ViewChild", "FormControl", "FormGroup", "Validators", "StateSelectorComponent", "CorporationTypeSelectorComponent", "FilingApiService", "NameCheckAndService", "fork<PERSON><PERSON>n", "ToastrService", "ActivatedRoute", "LoadFilingService", "FilingServiceResponseObject", "FormModeService", "CustomSharedValidations", "FilingInfoService", "StatePriceRequest", "PageTitleService", "NameCheckAndReservationComponent", "constructor", "filingInfoService", "Api", "formMode", "toaster", "activatedRoute", "pageTitleService", "SelectOne", "required", "FormationState", "FormationType", "ProposedName1", "ProposedName2", "ProposedName3", "BusinessEntityType", "ReservedEntityName", "SuspendedForfeitedEntityNo", "ReserveNameTo", "ReserveNameAddress", "City", "State", "Zip", "zipLength", "Remarks", "specialInstructions", "ngOnInit", "get<PERSON><PERSON><PERSON>", "SubCatCode", "namecheckreservation", "subscribe", "label", "setPageTitle", "getPrice", "res", "price", "basePrice", "FCGetStates", "States", "StateComponent", "<PERSON><PERSON><PERSON><PERSON>", "GetRegisteredAgent_SelectOne", "GetFormationType", "GetBusinessEntityType", "GetAdditionalFormationType", "response", "PleaseSelectOne", "CorpTypeComponent", "CorporationTypes", "reservedEntityValues", "businessEntities", "MapAllMasterData", "StartLoad", "Caption", "$OnStateSelection", "SelectedState", "reset", "NameCheckAndReservation", "Form", "controls", "IsVisible", "getStatePrice", "$OnCorportationTypeSelection", "corporationType", "valueChanges", "value", "map", "val", "corporationTypeCode", "businessEntityTypes", "filter", "data", "code", "corpCode", "state", "request", "ProductCode", "CategoryCode", "SubCategoryCode", "GetStateWisePrice", "OnSave", "valid", "Save", "ex", "console", "error", "queryParams", "queryString", "NameCheckReservation", "then", "serviceData", "Load", "catch", "e", "log", "EntityNumber", "SaveFilingService", "nameCheckRes", "x", "setValue", "MapMasterDataByControlId", "controlId", "mappedData", "obj", "controlID", "item", "id", "controlValue", "controlText", "args", "static", "selector", "template"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\viatech\\Ecom\\client\\acs\\src\\app\\Modules\\FilingService\\Pages\\name-check-and-reservation\\name-check-reservation.component.ts"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport { StateSelectorComponent } from 'src/app/Modules/Shared/Components/formComponents/stateSelector/state-selector.component';\r\nimport { CorporationTypeSelectorComponent } from 'src/app/Modules/Shared/Components/formComponents/corporation-type-selector/corporation-type-selector.component';\r\nimport { FilingApiService } from '../../Services/FilingApiService';\r\nimport { NameCheckAndService } from '../../Models/NameCheckAndService';\r\nimport { forkJoin } from 'rxjs';\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { LoadFilingService } from '../../Functions/filing-service-load.func';\r\nimport { FilingServiceResponseObject } from '../../Enums/filing-service-enum';\r\nimport { FormModeService } from 'src/app/Modules/Shared/Services/Common/FormModeService';\r\nimport { CustomSharedValidations } from 'src/app/Modules/Shared/functions/custom-validations';\r\nimport { FilingInfoService } from '../../Services/filing-price.service';\r\nimport { StatePriceRequest } from 'src/app/Modules/Shared/Models/Common/state-price-request.model';\r\nimport { PageTitleService } from 'src/app/Modules/Shared/Services/Common/page-title.service';\r\n\r\n\r\n@Component({\r\n  selector: 'name-check-reservation',\r\n  templateUrl: 'name-check-reservation.component.html'\r\n})\r\nexport class NameCheckAndReservationComponent implements OnInit {\r\n\r\n\r\n  NameCheckAndReservation: NameCheckAndService = new NameCheckAndService();\r\n\r\n  @ViewChild(StateSelectorComponent, { static: true }) StateComponent: StateSelectorComponent;\r\n  @ViewChild(CorporationTypeSelectorComponent, { static: true }) CorpTypeComponent: CorporationTypeSelectorComponent;\r\n\r\n  constructor(public filingInfoService: FilingInfoService, private Api: FilingApiService, public formMode: FormModeService, private toaster: ToastrService, private activatedRoute: ActivatedRoute, private pageTitleService: PageTitleService) { }\r\n\r\n  businessEntityTypes: any[] = [];\r\n  businessEntities: any[] = [];\r\n  reservedEntityValues: any[] = [];\r\n  PleaseSelectOne: any[] = [];\r\n  States: any[] = [];\r\n  price: number;\r\n  basePrice: number;\r\n\r\n  ngOnInit() {\r\n    this.filingInfoService.getLabel(this.filingInfoService.SubCatCode.namecheckreservation, 'FOT').subscribe(label => {\r\n      this.pageTitleService.setPageTitle(label);\r\n    });\r\n    // Get All States\r\n    this.filingInfoService.getPrice(this.filingInfoService.SubCatCode.namecheckreservation).subscribe(res => {\r\n      this.price = res;\r\n      this.basePrice = res;\r\n    });\r\n\r\n    //Get all States from store if store is empty call states API\r\n\r\n    this.Api.FCGetStates().subscribe((States) => {\r\n      this.States = States\r\n      this.StateComponent.States = States;\r\n    });\r\n\r\n\r\n    let fillValues = forkJoin([\r\n      this.Api.GetRegisteredAgent_SelectOne(),\r\n      this.Api.GetFormationType(\"909\"),\r\n      //this.Api.GetEntityType('FOT','909'),\r\n      this.Api.GetBusinessEntityType(),\r\n      this.Api.GetAdditionalFormationType(\"909\")\r\n    ])\r\n\r\n    fillValues.subscribe(response => {\r\n\r\n      this.PleaseSelectOne = response[0] || [];\r\n      this.CorpTypeComponent.CorporationTypes = response[1] || [];\r\n      this.reservedEntityValues = response[2] || [];\r\n      this.businessEntities = response[3] || [];\r\n      this.MapAllMasterData(this.reservedEntityValues);\r\n      this.StartLoad();\r\n    })\r\n\r\n    this.CorpTypeComponent.Caption = \"2. Please select the type of formation.\"\r\n    // Display Corporation Type after state selection\r\n    this.StateComponent.$OnStateSelection.subscribe(SelectedState => {\r\n\r\n      this.CorpTypeComponent.reset();\r\n      this.NameCheckAndReservation.FormationState = SelectedState;\r\n      //this.NameCheckAndReservation.FormationType = null;\r\n\r\n      this.Form.controls.FormationType.reset();\r\n\r\n      if (SelectedState) {\r\n        this.CorpTypeComponent.IsVisible = true;\r\n        this.getStatePrice(SelectedState);\r\n      }\r\n      else {\r\n        this.CorpTypeComponent.IsVisible = false;\r\n      }\r\n    })\r\n\r\n    this.CorpTypeComponent.$OnCorportationTypeSelection.subscribe(corporationType => {\r\n      this.NameCheckAndReservation.FormationType = corporationType;\r\n    })\r\n\r\n    this.Form.controls.FormationType.valueChanges.subscribe(value => {\r\n      this.CorpTypeComponent.CorporationTypes.map(val => {\r\n        if (val.corporationTypeCode == value) {\r\n          this.businessEntityTypes = this.businessEntities.filter(data => {\r\n            return data.code == val.corpCode;\r\n          });\r\n        }\r\n      })\r\n    })\r\n\r\n  }\r\n\r\n  getStatePrice(state) {\r\n    var request = new StatePriceRequest();\r\n    request.ProductCode = 'FS';\r\n    request.CategoryCode = 'FOT';\r\n    request.SubCategoryCode = '909';\r\n    request.State = state;\r\n    this.Api.GetStateWisePrice(request).subscribe(res => {\r\n      this.price = res.price > 0 ? res.price : this.basePrice;\r\n    });\r\n  }\r\n\r\n  Form = new FormGroup({\r\n    SelectOne: new FormControl<string | null>(null, [Validators.required]),\r\n    FormationState: new FormControl<string | null>(null, [Validators.required]),\r\n    FormationType: new FormControl<string | null>(null, [Validators.required]),\r\n    ProposedName1: new FormControl<string | null>(null, [Validators.required]),\r\n    ProposedName2: new FormControl<string | null>(null, [Validators.required]),\r\n    ProposedName3: new FormControl<string | null>(null),\r\n    BusinessEntityType: new FormControl<string | null>(null),\r\n    ReservedEntityName: new FormControl<string | null>(null),\r\n    SuspendedForfeitedEntityNo: new FormControl<string | null>(null, [Validators.required]),\r\n    ReserveNameTo: new FormControl<string | null>(null, [Validators.required]),\r\n    ReserveNameAddress: new FormControl<string | null>(null, [Validators.required]),\r\n    City: new FormControl<string | null>(null, [Validators.required]),\r\n    State: new FormControl<string | null>(null, [Validators.required]),\r\n    Zip: new FormControl<number | null>(null, [Validators.required, CustomSharedValidations.zipLength]),\r\n    Remarks: new FormControl<string | null>(null, [CustomSharedValidations.specialInstructions]),\r\n  })\r\n\r\n  OnSave() {\r\n    try {\r\n      if (this.Form.valid) {\r\n        this.Save();\r\n      }\r\n    }\r\n    catch (ex) {\r\n      console.error(ex)\r\n    }\r\n  }\r\n\r\n  StartLoad() {\r\n    this.activatedRoute.queryParams.subscribe(async queryString => {\r\n\r\n      LoadFilingService<NameCheckAndService>(queryString, this.Api, FilingServiceResponseObject.NameCheckReservation, this.formMode)\r\n        .then(serviceData => {\r\n\r\n          this.NameCheckAndReservation = serviceData;\r\n          this.Load();\r\n        }).catch(e => console.log(e))\r\n    })\r\n  }\r\n\r\n  Save() {\r\n    this.NameCheckAndReservation.FormationState = this.Form.controls.FormationState.value;\r\n    this.NameCheckAndReservation.FormationType = this.Form.controls.FormationType.value;\r\n    this.NameCheckAndReservation.SelectOne = this.Form.controls.SelectOne.value;\r\n    this.NameCheckAndReservation.ProposedName1 = this.Form.controls.ProposedName1.value;\r\n    this.NameCheckAndReservation.ProposedName2 = this.Form.controls.ProposedName2.value;\r\n    this.NameCheckAndReservation.ProposedName3 = this.Form.controls.ProposedName3.value;\r\n    this.NameCheckAndReservation.BusinessEntityType = this.Form.controls.BusinessEntityType.value;\r\n    this.NameCheckAndReservation.ReservedEntityName = this.Form.controls.ReservedEntityName.value;\r\n    this.NameCheckAndReservation.EntityNumber = this.Form.controls.SuspendedForfeitedEntityNo.value;\r\n    this.NameCheckAndReservation.ReserveNameTo = this.Form.controls.ReserveNameTo.value;\r\n    this.NameCheckAndReservation.ReserveNameAddress = this.Form.controls.ReserveNameAddress.value;\r\n    this.NameCheckAndReservation.City = this.Form.controls.City.value;\r\n    this.NameCheckAndReservation.State = this.Form.controls.State.value;\r\n    this.NameCheckAndReservation.Zip = this.Form.controls.Zip.value;\r\n    this.NameCheckAndReservation.Remarks = this.Form.controls.Remarks.value;\r\n\r\n    this.Api.SaveFilingService({ nameCheckRes: this.NameCheckAndReservation }).subscribe(x => {\r\n      this.Form.reset();\r\n    })\r\n  }\r\n\r\n  Load() {\r\n    this.getStatePrice(this.NameCheckAndReservation.FormationState);\r\n\r\n    this.Form.controls.FormationState.setValue(this.NameCheckAndReservation.FormationState)\r\n    this.Form.controls.FormationType.setValue(this.NameCheckAndReservation.FormationType)\r\n    this.Form.controls.SelectOne.setValue(this.NameCheckAndReservation.SelectOne)\r\n    this.Form.controls.ProposedName1.setValue(this.NameCheckAndReservation.ProposedName1)\r\n    this.Form.controls.ProposedName2.setValue(this.NameCheckAndReservation.ProposedName2)\r\n    this.Form.controls.ProposedName3.setValue(this.NameCheckAndReservation.ProposedName3)\r\n    this.Form.controls.BusinessEntityType.setValue(this.NameCheckAndReservation.BusinessEntityType)\r\n    this.Form.controls.ReservedEntityName.setValue(this.NameCheckAndReservation.ReservedEntityName)\r\n    this.Form.controls.SuspendedForfeitedEntityNo.setValue(this.NameCheckAndReservation.EntityNumber)\r\n    this.Form.controls.ReserveNameTo.setValue(this.NameCheckAndReservation.ReserveNameTo)\r\n    this.Form.controls.ReserveNameAddress.setValue(this.NameCheckAndReservation.ReserveNameAddress)\r\n    this.Form.controls.City.setValue(this.NameCheckAndReservation.City)\r\n    this.Form.controls.State.setValue(this.NameCheckAndReservation.State)\r\n    this.Form.controls.Zip.setValue(this.NameCheckAndReservation.Zip)\r\n    this.Form.controls.Remarks.setValue(this.NameCheckAndReservation.Remarks)\r\n  }\r\n\r\n  MapAllMasterData(data: any[]) {\r\n    this.reservedEntityValues = this.MapMasterDataByControlId(data, \"Reserved_Entity_Values\");\r\n  }\r\n\r\n  MapMasterDataByControlId(data: any[], controlId: any) {\r\n    let mappedData = data\r\n      .filter((obj) => obj.controlID === controlId)\r\n      .map((item) => {\r\n        var data = { id: item.controlValue, value: item.controlText };\r\n        return data;\r\n      });\r\n    return mappedData || [];\r\n  }\r\n\r\n}"]}, "metadata": {}, "sourceType": "module"}