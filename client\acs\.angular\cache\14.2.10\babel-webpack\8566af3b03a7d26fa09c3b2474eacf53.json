{"ast": null, "code": "'use strict';\n\nvar implementation = require('./implementation');\n\nmodule.exports = Function.prototype.bind || implementation;", "map": {"version": 3, "names": ["implementation", "require", "module", "exports", "Function", "prototype", "bind"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/function-bind/index.js"], "sourcesContent": ["'use strict';\n\nvar implementation = require('./implementation');\n\nmodule.exports = Function.prototype.bind || implementation;\n"], "mappings": "AAAA;;AAEA,IAAIA,cAAc,GAAGC,OAAO,CAAC,kBAAD,CAA5B;;AAEAC,MAAM,CAACC,OAAP,GAAiBC,QAAQ,CAACC,SAAT,CAAmBC,IAAnB,IAA2BN,cAA5C"}, "metadata": {}, "sourceType": "script"}