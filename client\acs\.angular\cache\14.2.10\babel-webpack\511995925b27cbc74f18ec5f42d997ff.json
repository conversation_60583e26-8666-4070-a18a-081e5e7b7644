{"ast": null, "code": "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement, isShadowRoot } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getUAString from \"../utils/userAgent.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = /firefox/i.test(getUAString());\n  var isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "map": {"version": 3, "names": ["getWindow", "getNodeName", "getComputedStyle", "isHTMLElement", "isShadowRoot", "isTableElement", "getParentNode", "getUAString", "getTrueOffsetParent", "element", "position", "offsetParent", "getContainingBlock", "isFirefox", "test", "isIE", "elementCss", "currentNode", "host", "indexOf", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "filter", "parentNode", "getOffsetParent", "window"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js"], "sourcesContent": ["import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement, isShadowRoot } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getUAString from \"../utils/userAgent.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = /firefox/i.test(getUAString());\n  var isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,gBAAtB;AACA,OAAOC,WAAP,MAAwB,kBAAxB;AACA,OAAOC,gBAAP,MAA6B,uBAA7B;AACA,SAASC,aAAT,EAAwBC,YAAxB,QAA4C,iBAA5C;AACA,OAAOC,cAAP,MAA2B,qBAA3B;AACA,OAAOC,aAAP,MAA0B,oBAA1B;AACA,OAAOC,WAAP,MAAwB,uBAAxB;;AAEA,SAASC,mBAAT,CAA6BC,OAA7B,EAAsC;EACpC,IAAI,CAACN,aAAa,CAACM,OAAD,CAAd,IAA2B;EAC/BP,gBAAgB,CAACO,OAAD,CAAhB,CAA0BC,QAA1B,KAAuC,OADvC,EACgD;IAC9C,OAAO,IAAP;EACD;;EAED,OAAOD,OAAO,CAACE,YAAf;AACD,C,CAAC;AACF;;;AAGA,SAASC,kBAAT,CAA4BH,OAA5B,EAAqC;EACnC,IAAII,SAAS,GAAG,WAAWC,IAAX,CAAgBP,WAAW,EAA3B,CAAhB;EACA,IAAIQ,IAAI,GAAG,WAAWD,IAAX,CAAgBP,WAAW,EAA3B,CAAX;;EAEA,IAAIQ,IAAI,IAAIZ,aAAa,CAACM,OAAD,CAAzB,EAAoC;IAClC;IACA,IAAIO,UAAU,GAAGd,gBAAgB,CAACO,OAAD,CAAjC;;IAEA,IAAIO,UAAU,CAACN,QAAX,KAAwB,OAA5B,EAAqC;MACnC,OAAO,IAAP;IACD;EACF;;EAED,IAAIO,WAAW,GAAGX,aAAa,CAACG,OAAD,CAA/B;;EAEA,IAAIL,YAAY,CAACa,WAAD,CAAhB,EAA+B;IAC7BA,WAAW,GAAGA,WAAW,CAACC,IAA1B;EACD;;EAED,OAAOf,aAAa,CAACc,WAAD,CAAb,IAA8B,CAAC,MAAD,EAAS,MAAT,EAAiBE,OAAjB,CAAyBlB,WAAW,CAACgB,WAAD,CAApC,IAAqD,CAA1F,EAA6F;IAC3F,IAAIG,GAAG,GAAGlB,gBAAgB,CAACe,WAAD,CAA1B,CAD2F,CAClD;IACzC;IACA;;IAEA,IAAIG,GAAG,CAACC,SAAJ,KAAkB,MAAlB,IAA4BD,GAAG,CAACE,WAAJ,KAAoB,MAAhD,IAA0DF,GAAG,CAACG,OAAJ,KAAgB,OAA1E,IAAqF,CAAC,WAAD,EAAc,aAAd,EAA6BJ,OAA7B,CAAqCC,GAAG,CAACI,UAAzC,MAAyD,CAAC,CAA/I,IAAoJX,SAAS,IAAIO,GAAG,CAACI,UAAJ,KAAmB,QAApL,IAAgMX,SAAS,IAAIO,GAAG,CAACK,MAAjB,IAA2BL,GAAG,CAACK,MAAJ,KAAe,MAA9O,EAAsP;MACpP,OAAOR,WAAP;IACD,CAFD,MAEO;MACLA,WAAW,GAAGA,WAAW,CAACS,UAA1B;IACD;EACF;;EAED,OAAO,IAAP;AACD,C,CAAC;AACF;;;AAGA,eAAe,SAASC,eAAT,CAAyBlB,OAAzB,EAAkC;EAC/C,IAAImB,MAAM,GAAG5B,SAAS,CAACS,OAAD,CAAtB;EACA,IAAIE,YAAY,GAAGH,mBAAmB,CAACC,OAAD,CAAtC;;EAEA,OAAOE,YAAY,IAAIN,cAAc,CAACM,YAAD,CAA9B,IAAgDT,gBAAgB,CAACS,YAAD,CAAhB,CAA+BD,QAA/B,KAA4C,QAAnG,EAA6G;IAC3GC,YAAY,GAAGH,mBAAmB,CAACG,YAAD,CAAlC;EACD;;EAED,IAAIA,YAAY,KAAKV,WAAW,CAACU,YAAD,CAAX,KAA8B,MAA9B,IAAwCV,WAAW,CAACU,YAAD,CAAX,KAA8B,MAA9B,IAAwCT,gBAAgB,CAACS,YAAD,CAAhB,CAA+BD,QAA/B,KAA4C,QAAjI,CAAhB,EAA4J;IAC1J,OAAOkB,MAAP;EACD;;EAED,OAAOjB,YAAY,IAAIC,kBAAkB,CAACH,OAAD,CAAlC,IAA+CmB,MAAtD;AACD"}, "metadata": {}, "sourceType": "module"}