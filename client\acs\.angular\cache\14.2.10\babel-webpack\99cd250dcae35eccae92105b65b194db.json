{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./dissolution.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./dissolution.component.css?ngResource\";\nimport { Component, ViewChild } from '@angular/core';\nimport { FormControl, FormGroup } from '@angular/forms';\nimport { ActivatedRoute } from '@angular/router';\nimport { ToastrService } from 'ngx-toastr';\nimport { DynamicFormComponent } from 'src/app/Modules/Shared/Components/dynamic-form/dynamic-form.component';\nimport { StateSelectorComponent } from 'src/app/Modules/Shared/Components/formComponents/stateSelector/state-selector.component';\nimport { CustomSharedValidations } from 'src/app/Modules/Shared/functions/custom-validations';\nimport { ResetExcept } from 'src/app/Modules/Shared/functions/form-reset';\nimport { StatePriceRequest } from 'src/app/Modules/Shared/Models/Common/state-price-request.model';\nimport { FormModeService } from 'src/app/Modules/Shared/Services/Common/FormModeService';\nimport { QuestionService } from 'src/app/Modules/Shared/Services/Common/question.service';\nimport { FilingServiceResponseObject } from '../../Enums/filing-service-enum';\nimport { LoadFilingService } from '../../Functions/filing-service-load.func';\nimport { Dissolution } from '../../Models/Dissolution';\nimport { FilingInfoService } from '../../Services/filing-price.service';\nimport { FilingApiService } from '../../Services/FilingApiService';\nimport { SimpleProductService } from \"src/app/Modules/Core/Services/Common/simple-product.service\";\nimport { SERVICE_DATA } from 'src/app/Modules/FilingService/Data/Service_Data';\nimport { PageTitleService } from 'src/app/Modules/Shared/Services/Common/page-title.service';\nlet DissolutionComponent = class DissolutionComponent {\n  constructor(filingInfoService, Api, simpleProductService, formMode, activatedRoute, service, toastr, pageTitleService) {\n    this.filingInfoService = filingInfoService;\n    this.Api = Api;\n    this.simpleProductService = simpleProductService;\n    this.formMode = formMode;\n    this.activatedRoute = activatedRoute;\n    this.service = service;\n    this.toastr = toastr;\n    this.pageTitleService = pageTitleService;\n    this.title = '';\n    this.message = '';\n    this.isAdd = true;\n    this.FileData = [];\n    this.DissolutionForm = new Dissolution();\n    this.dynamicFormMasterData = [];\n    this.Form = new FormGroup({\n      FormationState: new FormControl(null),\n      SpecialInstructions: new FormControl(null, [CustomSharedValidations.specialInstructions])\n    });\n  }\n\n  set DynamicFormComponent(dynamicFormComponent) {\n    if (dynamicFormComponent) {\n      this.DynamicFormComponentObj = dynamicFormComponent;\n    }\n  }\n\n  ngOnInit() {\n    this.activatedRoute.data.subscribe(data => {\n      this.message = data['message'];\n      const serviceType = data['serviceType']; // Fetch the corresponding details from the SERVICE_DATA file\n\n      this.serviceDetails = SERVICE_DATA[serviceType];\n      this.filingInfoService.getLabel(this.serviceDetails.subCategoryCode, this.serviceDetails.categoryCode).subscribe(title => {\n        this.title = title;\n        this.pageTitleService.setPageTitle(this.title);\n      });\n    });\n    this.Api.LoadDynamicControlsInCache().subscribe();\n    this.filingInfoService.getPrice(this.filingInfoService.SubCatCode.formllc).subscribe(res => {\n      this.price = res;\n      this.basePrice = res;\n    });\n    this.Api.FCGetStates().subscribe(States => {\n      this.StateComponent.States = States;\n    });\n    this.StartLoad();\n    this.StateComponent.$OnStateSelection.subscribe(selectedState => {\n      if (selectedState) {\n        this.getStatePrice(selectedState);\n        this.selectedState = selectedState;\n\n        if (this.selectedState) {\n          this.Api.GetDynamicFormMasterDataUpload(this.serviceDetails.productCode, this.serviceDetails.categoryCode, this.serviceDetails.subCategoryCode, selectedState, this.serviceDetails.filingType).subscribe(data => {\n            this.dynamicFormMasterData = data;\n            var dynamicFormData = selectedState == this.DissolutionForm.FormationState ? this.DissolutionForm.DynamicFormData : [];\n            this.questions$ = this.service.getMappedQuestions(data, dynamicFormData, this.selectedState);\n          });\n        }\n      }\n\n      ResetExcept(this.Form, [\"FormationState\"]);\n    });\n  }\n\n  OnSave(dynamicFormData) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      if (!dynamicFormData && _this.DynamicFormComponentObj) {\n        var enteredDynamicFormData = yield _this.DynamicFormComponentObj.getDynamicFormData();\n      }\n\n      try {\n        if (_this.Form.valid) {\n          _this.Save(dynamicFormData || enteredDynamicFormData);\n        }\n      } catch (ex) {\n        console.error(ex);\n      }\n    })();\n  }\n\n  StartLoad() {\n    var _this2 = this;\n\n    this.activatedRoute.queryParams.subscribe( /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (queryString) {\n        if (queryString.on) {\n          _this2.isAdd = false;\n        }\n\n        LoadFilingService(queryString, _this2.Api, FilingServiceResponseObject.Dissolution, _this2.formMode).then(serviceData => {\n          _this2.DissolutionForm = serviceData;\n\n          _this2.Load();\n        }).catch(e => console.log(e));\n      });\n\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  }\n\n  Load() {\n    this.getStatePrice(this.DissolutionForm.FormationState);\n    this.Form.controls.FormationState.setValue(this.DissolutionForm.FormationState);\n    this.Form.controls.SpecialInstructions.setValue(this.DissolutionForm.Remarks);\n  }\n\n  Save(dynamicFormData) {\n    this.DissolutionForm.FormationState = this.Form.controls.FormationState.value;\n    this.DissolutionForm.Remarks = this.Form.controls.SpecialInstructions.value;\n\n    if (dynamicFormData) {\n      this.DissolutionForm.DynamicFormData = dynamicFormData.keyValuePair || [];\n    }\n\n    if (dynamicFormData.dynamicFormUploadedFiles?.length <= 0) {\n      this.toastr.error('File upload required.');\n      return;\n    }\n\n    this.simpleProductService.addToCart(this.serviceDetails.productCode, this.serviceDetails.categoryCode, this.serviceDetails.subCategoryCode, 1, \"\", this.serviceDetails.entityCode, \"\", this.serviceDetails.optionCode, \"\", this.DissolutionForm.Remarks, this.DissolutionForm.DynamicFormData, dynamicFormData.dynamicFormUploadedFiles || [], this.DissolutionForm.FormationState).subscribe(x => {\n      this.Form.reset();\n    });\n  }\n\n  getStatePrice(state) {\n    var request = new StatePriceRequest();\n    request.ProductCode = this.serviceDetails.productCode;\n    request.CategoryCode = this.serviceDetails.categoryCode;\n    request.SubCategoryCode = this.serviceDetails.subCategoryCode;\n    request.State = state;\n    this.Api.GetStateWisePrice(request).subscribe(res => {\n      this.price = res.price > 0 ? res.price : this.basePrice;\n    });\n  }\n\n};\n\nDissolutionComponent.ctorParameters = () => [{\n  type: FilingInfoService\n}, {\n  type: FilingApiService\n}, {\n  type: SimpleProductService\n}, {\n  type: FormModeService\n}, {\n  type: ActivatedRoute\n}, {\n  type: QuestionService\n}, {\n  type: ToastrService\n}, {\n  type: PageTitleService\n}];\n\nDissolutionComponent.propDecorators = {\n  StateComponent: [{\n    type: ViewChild,\n    args: [StateSelectorComponent, {\n      static: true\n    }]\n  }],\n  DynamicFormComponent: [{\n    type: ViewChild,\n    args: [DynamicFormComponent, {\n      static: false\n    }]\n  }]\n};\nDissolutionComponent = __decorate([Component({\n  selector: 'app-dissolution',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], DissolutionComponent);\nexport { DissolutionComponent };", "map": {"version": 3, "mappings": ";;;;AAAA,SAASA,SAAT,EAA4BC,SAA5B,QAA6C,eAA7C;AACA,SAASC,WAAT,EAAsBC,SAAtB,QAAuC,gBAAvC;AACA,SAASC,cAAT,QAA+B,iBAA/B;AAEA,SAASC,aAAT,QAA8B,YAA9B;AAEA,SAASC,oBAAT,QAAqC,uEAArC;AACA,SAASC,sBAAT,QAAuC,yFAAvC;AACA,SAASC,uBAAT,QAAwC,qDAAxC;AACA,SAASC,WAAT,QAA4B,6CAA5B;AACA,SAASC,iBAAT,QAAkC,gEAAlC;AAEA,SAASC,eAAT,QAAgC,wDAAhC;AACA,SAASC,eAAT,QAAgC,yDAAhC;AACA,SAASC,2BAAT,QAA4C,iCAA5C;AACA,SAASC,iBAAT,QAAkC,0CAAlC;AACA,SAASC,WAAT,QAA4B,0BAA5B;AACA,SAASC,iBAAT,QAAkC,qCAAlC;AACA,SAASC,gBAAT,QAAiC,iCAAjC;AACA,SAASC,oBAAT,QAAqC,6DAArC;AACA,SAASC,YAAT,QAA6B,iDAA7B;AACA,SAASC,gBAAT,QAAiC,2DAAjC;IAUaC,oBAAoB,SAApBA,oBAAoB;EAsB/BC,YAAmBC,iBAAnB,EAAmEC,GAAnE,EACUC,oBADV,EAESC,QAFT,EAE4CC,cAF5C,EAEoFC,OAFpF,EAEsHC,MAFtH,EAEqJC,gBAFrJ,EAEuL;IAFpK;IAAgD;IACzD;IACD;IAAmC;IAAwC;IAAkC;IAA+B;IAtBrJ,aAAgB,EAAhB;IACA,eAAkB,EAAlB;IAGA,aAAQ,IAAR;IACA,gBAAgB,EAAhB;IAEA,uBAA+B,IAAIf,WAAJ,EAA/B;IAGA,6BAA+B,EAA/B;IA2EA,YAAO,IAAIZ,SAAJ,CAAc;MACnB4B,cAAc,EAAE,IAAI7B,WAAJ,CAA+B,IAA/B,CADG;MAEnB8B,mBAAmB,EAAE,IAAI9B,WAAJ,CAA+B,IAA/B,EAAqC,CAACM,uBAAuB,CAACyB,mBAAzB,CAArC;IAFF,CAAd,CAAP;EA5DC;;EAX2E,IAApB3B,oBAAoB,CAAC4B,oBAAD,EAA2C;IACrH,IAAIA,oBAAJ,EAA0B;MACxB,KAAKC,uBAAL,GAA+BD,oBAA/B;IACD;EACF;;EASDE,QAAQ;IACN,KAAKT,cAAL,CAAoBU,IAApB,CAAyBC,SAAzB,CAAmCD,IAAI,IAAG;MAExC,KAAKE,OAAL,GAAeF,IAAI,CAAC,SAAD,CAAnB;MACA,MAAMG,WAAW,GAAGH,IAAI,CAAC,aAAD,CAAxB,CAHwC,CAIxC;;MACA,KAAKI,cAAL,GAAsBtB,YAAY,CAACqB,WAAD,CAAlC;MACA,KAAKjB,iBAAL,CAAuBmB,QAAvB,CAAgC,KAAKD,cAAL,CAAoBE,eAApD,EAAqE,KAAKF,cAAL,CAAoBG,YAAzF,EAAuGN,SAAvG,CAAiHO,KAAK,IAAG;QACvH,KAAKA,KAAL,GAAaA,KAAb;QACA,KAAKf,gBAAL,CAAsBgB,YAAtB,CAAmC,KAAKD,KAAxC;MACD,CAHD;IAID,CAVD;IAWA,KAAKrB,GAAL,CAASuB,0BAAT,GAAsCT,SAAtC;IAEA,KAAKf,iBAAL,CACGyB,QADH,CACY,KAAKzB,iBAAL,CAAuB0B,UAAvB,CAAkCC,OAD9C,EAEGZ,SAFH,CAEca,GAAD,IAAQ;MACjB,KAAKC,KAAL,GAAaD,GAAb;MACA,KAAKE,SAAL,GAAiBF,GAAjB;IACD,CALH;IAMA,KAAK3B,GAAL,CAAS8B,WAAT,GAAuBhB,SAAvB,CAAkCiB,MAAD,IAAW;MAC1C,KAAKC,cAAL,CAAoBD,MAApB,GAA6BA,MAA7B;IACD,CAFD;IAMA,KAAKE,SAAL;IAEA,KAAKD,cAAL,CAAoBE,iBAApB,CAAsCpB,SAAtC,CAAiDqB,aAAD,IAAkB;MAChE,IAAIA,aAAJ,EAAmB;QACjB,KAAKC,aAAL,CAAmBD,aAAnB;QACA,KAAKA,aAAL,GAAqBA,aAArB;;QACA,IAAI,KAAKA,aAAT,EAAwB;UAEtB,KAAKnC,GAAL,CAASqC,8BAAT,CACE,KAAKpB,cAAL,CAAoBqB,WADtB,EAEE,KAAKrB,cAAL,CAAoBG,YAFtB,EAGE,KAAKH,cAAL,CAAoBE,eAHtB,EAIEgB,aAJF,EAKE,KAAKlB,cAAL,CAAoBsB,UALtB,EAMEzB,SANF,CAMaD,IAAD,IAAS;YACnB,KAAK2B,qBAAL,GAA6B3B,IAA7B;YACA,IAAI4B,eAAe,GACjBN,aAAa,IAAI,KAAKO,eAAL,CAAqBnC,cAAtC,GACI,KAAKmC,eAAL,CAAqBC,eADzB,GAEI,EAHN;YAIA,KAAKC,UAAL,GAAkB,KAAKxC,OAAL,CAAayC,kBAAb,CAChBhC,IADgB,EAEhB4B,eAFgB,EAGhB,KAAKN,aAHW,CAAlB;UAKD,CAjBD;QAkBD;MACF;;MACDlD,WAAW,CAAC,KAAK6D,IAAN,EAAY,CAAC,gBAAD,CAAZ,CAAX;IACD,CA3BD;EA4BD;;EAOKC,MAAM,CAACN,eAAD,EAAiB;IAAA;;IAAA;MAC3B,IAAI,CAACA,eAAD,IAAoB,KAAI,CAAC9B,uBAA7B,EAAsD;QACpD,IAAIqC,sBAAsB,SAAS,KAAI,CAACrC,uBAAL,CAA6BsC,kBAA7B,EAAnC;MACD;;MACD,IAAI;QAEF,IAAI,KAAI,CAACH,IAAL,CAAUI,KAAd,EAAqB;UACnB,KAAI,CAACC,IAAL,CAAUV,eAAe,IAAIO,sBAA7B;QACD;MACF,CALD,CAKE,OAAOI,EAAP,EAAW;QACXC,OAAO,CAACC,KAAR,CAAcF,EAAd;MACD;IAX0B;EAY5B;;EAEDnB,SAAS;IAAA;;IACP,KAAK9B,cAAL,CAAoBoD,WAApB,CAAgCzC,SAAhC;MAAA,6BAA0C,WAAO0C,WAAP,EAAsB;QAC9D,IAAIA,WAAW,CAACC,EAAhB,EAAoB;UAClB,MAAI,CAACC,KAAL,GAAa,KAAb;QACD;;QACDpE,iBAAiB,CACfkE,WADe,EAEf,MAAI,CAACxD,GAFU,EAGfX,2BAA2B,CAACE,WAHb,EAIf,MAAI,CAACW,QAJU,CAAjB,CAMGyD,IANH,CAMSC,WAAD,IAAgB;UACpB,MAAI,CAAClB,eAAL,GAAuBkB,WAAvB;;UACA,MAAI,CAACC,IAAL;QACD,CATH,EAUGC,KAVH,CAUUC,CAAD,IAAOV,OAAO,CAACW,GAAR,CAAYD,CAAZ,CAVhB;MAWD,CAfD;;MAAA;QAAA;MAAA;IAAA;EAgBD;;EAEDF,IAAI;IACF,KAAKzB,aAAL,CAAmB,KAAKM,eAAL,CAAqBnC,cAAxC;IACA,KAAKuC,IAAL,CAAUmB,QAAV,CAAmB1D,cAAnB,CAAkC2D,QAAlC,CAA2C,KAAKxB,eAAL,CAAqBnC,cAAhE;IACA,KAAKuC,IAAL,CAAUmB,QAAV,CAAmBzD,mBAAnB,CAAuC0D,QAAvC,CAAgD,KAAKxB,eAAL,CAAqByB,OAArE;EACD;;EAEDhB,IAAI,CAACV,eAAD,EAAgB;IAClB,KAAKC,eAAL,CAAqBnC,cAArB,GAAsC,KAAKuC,IAAL,CAAUmB,QAAV,CAAmB1D,cAAnB,CAAkC6D,KAAxE;IACA,KAAK1B,eAAL,CAAqByB,OAArB,GAA+B,KAAKrB,IAAL,CAAUmB,QAAV,CAAmBzD,mBAAnB,CAAuC4D,KAAtE;;IAEA,IAAI3B,eAAJ,EAAqB;MACnB,KAAKC,eAAL,CAAqBC,eAArB,GAAuCF,eAAe,CAAC4B,YAAhB,IAAgC,EAAvE;IACD;;IACD,IAAI5B,eAAe,CAAC6B,wBAAhB,EAA0CC,MAA1C,IAAoD,CAAxD,EAA2D;MACzD,KAAKlE,MAAL,CAAYiD,KAAZ,CAAkB,uBAAlB;MACA;IACD;;IAED,KAAKrD,oBAAL,CACGuE,SADH,CAEI,KAAKvD,cAAL,CAAoBqB,WAFxB,EAGI,KAAKrB,cAAL,CAAoBG,YAHxB,EAII,KAAKH,cAAL,CAAoBE,eAJxB,EAKI,CALJ,EAMI,EANJ,EAOI,KAAKF,cAAL,CAAoBwD,UAPxB,EAQM,EARN,EASI,KAAKxD,cAAL,CAAoByD,UATxB,EAUI,EAVJ,EAWI,KAAKhC,eAAL,CAAqByB,OAXzB,EAYI,KAAKzB,eAAL,CAAqBC,eAZzB,EAaIF,eAAe,CAAC6B,wBAAhB,IAA4C,EAbhD,EAcI,KAAK5B,eAAL,CAAqBnC,cAdzB,EAeIO,SAfJ,CAee6D,CAAD,IAAM;MAChB,KAAK7B,IAAL,CAAU8B,KAAV;IACD,CAjBH;EAmBD;;EAEDxC,aAAa,CAACyC,KAAD,EAAM;IACjB,IAAIC,OAAO,GAAG,IAAI5F,iBAAJ,EAAd;IACA4F,OAAO,CAACC,WAAR,GAAsB,KAAK9D,cAAL,CAAoBqB,WAA1C;IACAwC,OAAO,CAACE,YAAR,GAAuB,KAAK/D,cAAL,CAAoBG,YAA3C;IACA0D,OAAO,CAACG,eAAR,GAA0B,KAAKhE,cAAL,CAAoBE,eAA9C;IACA2D,OAAO,CAACI,KAAR,GAAgBL,KAAhB;IACA,KAAK7E,GAAL,CAASmF,iBAAT,CAA2BL,OAA3B,EAAoChE,SAApC,CAA+Ca,GAAD,IAAQ;MACpD,KAAKC,KAAL,GAAaD,GAAG,CAACC,KAAJ,GAAY,CAAZ,GAAgBD,GAAG,CAACC,KAApB,GAA4B,KAAKC,SAA9C;IACD,CAFD;EAGD;;AA7K8B;;;;;;;;;;;;;;;;;;;;;;UAa9BpD;IAAS2G,OAACrG,sBAAD,EAAyB;MAAEsG,MAAM,EAAE;IAAV,CAAzB;;;UAGT5G;IAAS2G,OAACtG,oBAAD,EAAuB;MAAEuG,MAAM,EAAE;IAAV,CAAvB;;;AAhBCxF,oBAAoB,eANhCrB,SAAS,CAAC;EACT8G,QAAQ,EAAE,iBADD;EAETC,8BAFS;;AAAA,CAAD,CAMuB,GAApB1F,oBAAoB,CAApB;SAAAA", "names": ["Component", "ViewChild", "FormControl", "FormGroup", "ActivatedRoute", "ToastrService", "DynamicFormComponent", "StateSelectorComponent", "CustomSharedValidations", "ResetExcept", "StatePriceRequest", "FormModeService", "QuestionService", "FilingServiceResponseObject", "LoadFilingService", "Dissolution", "FilingInfoService", "FilingApiService", "SimpleProductService", "SERVICE_DATA", "PageTitleService", "DissolutionComponent", "constructor", "filingInfoService", "Api", "simpleProductService", "formMode", "activatedRoute", "service", "toastr", "pageTitleService", "FormationState", "SpecialInstructions", "specialInstructions", "dynamicFormComponent", "DynamicFormComponentObj", "ngOnInit", "data", "subscribe", "message", "serviceType", "serviceDetails", "get<PERSON><PERSON><PERSON>", "subCategoryCode", "categoryCode", "title", "setPageTitle", "LoadDynamicControlsInCache", "getPrice", "SubCatCode", "formllc", "res", "price", "basePrice", "FCGetStates", "States", "StateComponent", "StartLoad", "$OnStateSelection", "selectedState", "getStatePrice", "GetDynamicFormMasterDataUpload", "productCode", "filingType", "dynamicFormMasterData", "dynamicFormData", "DissolutionForm", "DynamicFormData", "questions$", "getMappedQuestions", "Form", "OnSave", "enteredDynamicFormData", "getDynamicFormData", "valid", "Save", "ex", "console", "error", "queryParams", "queryString", "on", "isAdd", "then", "serviceData", "Load", "catch", "e", "log", "controls", "setValue", "Remarks", "value", "keyValuePair", "dynamicFormUploadedFiles", "length", "addToCart", "entityCode", "optionCode", "x", "reset", "state", "request", "ProductCode", "CategoryCode", "SubCategoryCode", "State", "GetStateWisePrice", "args", "static", "selector", "template"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\viatech\\Ecom\\client\\acs\\src\\app\\Modules\\FilingService\\Pages\\dissolution\\dissolution.component.ts"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { FormControl, FormGroup } from '@angular/forms';\r\nimport { ActivatedRoute } from '@angular/router';\r\n\r\nimport { ToastrService } from 'ngx-toastr';\r\nimport { Observable } from 'rxjs';\r\nimport { DynamicFormComponent } from 'src/app/Modules/Shared/Components/dynamic-form/dynamic-form.component';\r\nimport { StateSelectorComponent } from 'src/app/Modules/Shared/Components/formComponents/stateSelector/state-selector.component';\r\nimport { CustomSharedValidations } from 'src/app/Modules/Shared/functions/custom-validations';\r\nimport { ResetExcept } from 'src/app/Modules/Shared/functions/form-reset';\r\nimport { StatePriceRequest } from 'src/app/Modules/Shared/Models/Common/state-price-request.model';\r\nimport { QuestionBase } from 'src/app/Modules/Shared/Models/DynamicForm/question-base';\r\nimport { FormModeService } from 'src/app/Modules/Shared/Services/Common/FormModeService';\r\nimport { QuestionService } from 'src/app/Modules/Shared/Services/Common/question.service';\r\nimport { FilingServiceResponseObject } from '../../Enums/filing-service-enum';\r\nimport { LoadFilingService } from '../../Functions/filing-service-load.func';\r\nimport { Dissolution } from '../../Models/Dissolution';\r\nimport { FilingInfoService } from '../../Services/filing-price.service';\r\nimport { FilingApiService } from '../../Services/FilingApiService';\r\nimport { SimpleProductService } from \"src/app/Modules/Core/Services/Common/simple-product.service\";\r\nimport { SERVICE_DATA } from 'src/app/Modules/FilingService/Data/Service_Data';\r\nimport { PageTitleService } from 'src/app/Modules/Shared/Services/Common/page-title.service';\r\n// Adjust the path based on file location\r\n\r\n\r\n@Component({\r\n  selector: 'app-dissolution',\r\n  templateUrl: './dissolution.component.html',\r\n  styleUrls: ['./dissolution.component.css']\r\n})\r\n\r\nexport class DissolutionComponent implements OnInit {\r\n\r\n  title: string = '';\r\n  message: string = '';\r\n  questions$: Observable<QuestionBase<any>[]>;\r\n\r\n  isAdd = true;\r\n  FileData: any = [];\r\n  selectedState: any;\r\n  DissolutionForm: Dissolution = new Dissolution();\r\n  price: number;\r\n  basePrice: number;\r\n  dynamicFormMasterData: any[] = [];\r\n  @ViewChild(StateSelectorComponent, { static: true }) StateComponent: StateSelectorComponent;\r\n  serviceDetails: any;\r\n  DynamicFormComponentObj: DynamicFormComponent;\r\n  @ViewChild(DynamicFormComponent, { static: false }) set DynamicFormComponent(dynamicFormComponent: DynamicFormComponent) {\r\n    if (dynamicFormComponent) {\r\n      this.DynamicFormComponentObj = dynamicFormComponent;\r\n    }\r\n  }\r\n\r\n  constructor(public filingInfoService: FilingInfoService, protected Api: FilingApiService,\r\n    private simpleProductService: SimpleProductService,\r\n    public formMode: FormModeService, private activatedRoute: ActivatedRoute, private service: QuestionService, private toastr: ToastrService, private pageTitleService: PageTitleService) {\r\n\r\n\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.activatedRoute.data.subscribe(data => {\r\n\r\n      this.message = data['message'];\r\n      const serviceType = data['serviceType'];\r\n      // Fetch the corresponding details from the SERVICE_DATA file\r\n      this.serviceDetails = SERVICE_DATA[serviceType];\r\n      this.filingInfoService.getLabel(this.serviceDetails.subCategoryCode, this.serviceDetails.categoryCode).subscribe(title => {\r\n        this.title = title;\r\n        this.pageTitleService.setPageTitle(this.title);\r\n      });\r\n    });\r\n    this.Api.LoadDynamicControlsInCache().subscribe();\r\n\r\n    this.filingInfoService\r\n      .getPrice(this.filingInfoService.SubCatCode.formllc)\r\n      .subscribe((res) => {\r\n        this.price = res;\r\n        this.basePrice = res;\r\n      });\r\n    this.Api.FCGetStates().subscribe((States) => {\r\n      this.StateComponent.States = States;\r\n    });\r\n\r\n\r\n\r\n    this.StartLoad();\r\n\r\n    this.StateComponent.$OnStateSelection.subscribe((selectedState) => {\r\n      if (selectedState) {\r\n        this.getStatePrice(selectedState);\r\n        this.selectedState = selectedState;\r\n        if (this.selectedState) {\r\n\r\n          this.Api.GetDynamicFormMasterDataUpload(\r\n            this.serviceDetails.productCode,\r\n            this.serviceDetails.categoryCode,\r\n            this.serviceDetails.subCategoryCode,\r\n            selectedState,\r\n            this.serviceDetails.filingType\r\n          ).subscribe((data) => {\r\n            this.dynamicFormMasterData = data;\r\n            var dynamicFormData =\r\n              selectedState == this.DissolutionForm.FormationState\r\n                ? this.DissolutionForm.DynamicFormData\r\n                : [];\r\n            this.questions$ = this.service.getMappedQuestions(\r\n              data,\r\n              dynamicFormData,\r\n              this.selectedState\r\n            );\r\n          });\r\n        }\r\n      }\r\n      ResetExcept(this.Form, [\"FormationState\"]);\r\n    });\r\n  }\r\n\r\n  Form = new FormGroup({\r\n    FormationState: new FormControl<string | null>(null),\r\n    SpecialInstructions: new FormControl<string | null>(null, [CustomSharedValidations.specialInstructions])\r\n  });\r\n\r\n  async OnSave(dynamicFormData?) {\r\n    if (!dynamicFormData && this.DynamicFormComponentObj) {\r\n      var enteredDynamicFormData = await this.DynamicFormComponentObj.getDynamicFormData();\r\n    }\r\n    try {\r\n\r\n      if (this.Form.valid) {\r\n        this.Save(dynamicFormData || enteredDynamicFormData);\r\n      }\r\n    } catch (ex) {\r\n      console.error(ex);\r\n    }\r\n  }\r\n\r\n  StartLoad() {\r\n    this.activatedRoute.queryParams.subscribe(async (queryString) => {\r\n      if (queryString.on) {\r\n        this.isAdd = false;\r\n      }\r\n      LoadFilingService<Dissolution>(\r\n        queryString,\r\n        this.Api,\r\n        FilingServiceResponseObject.Dissolution,\r\n        this.formMode\r\n      )\r\n        .then((serviceData) => {\r\n          this.DissolutionForm = serviceData;\r\n          this.Load();\r\n        })\r\n        .catch((e) => console.log(e));\r\n    });\r\n  }\r\n\r\n  Load() {\r\n    this.getStatePrice(this.DissolutionForm.FormationState);\r\n    this.Form.controls.FormationState.setValue(this.DissolutionForm.FormationState);\r\n    this.Form.controls.SpecialInstructions.setValue(this.DissolutionForm.Remarks);\r\n  }\r\n\r\n  Save(dynamicFormData) {\r\n    this.DissolutionForm.FormationState = this.Form.controls.FormationState.value;\r\n    this.DissolutionForm.Remarks = this.Form.controls.SpecialInstructions.value;\r\n\r\n    if (dynamicFormData) {\r\n      this.DissolutionForm.DynamicFormData = dynamicFormData.keyValuePair || [];\r\n    }\r\n    if (dynamicFormData.dynamicFormUploadedFiles?.length <= 0) {\r\n      this.toastr.error('File upload required.');\r\n      return;\r\n    }\r\n\r\n    this.simpleProductService\r\n      .addToCart(\r\n        this.serviceDetails.productCode,\r\n        this.serviceDetails.categoryCode,\r\n        this.serviceDetails.subCategoryCode,\r\n        1,\r\n        \"\",\r\n        this.serviceDetails.entityCode\r\n        , \"\",\r\n        this.serviceDetails.optionCode,\r\n        \"\",\r\n        this.DissolutionForm.Remarks,\r\n        this.DissolutionForm.DynamicFormData,\r\n        dynamicFormData.dynamicFormUploadedFiles || [],\r\n        this.DissolutionForm.FormationState\r\n      ).subscribe((x) => {\r\n        this.Form.reset();\r\n      });\r\n\r\n  }\r\n\r\n  getStatePrice(state) {\r\n    var request = new StatePriceRequest();\r\n    request.ProductCode = this.serviceDetails.productCode;\r\n    request.CategoryCode = this.serviceDetails.categoryCode;\r\n    request.SubCategoryCode = this.serviceDetails.subCategoryCode;\r\n    request.State = state;\r\n    this.Api.GetStateWisePrice(request).subscribe((res) => {\r\n      this.price = res.price > 0 ? res.price : this.basePrice;\r\n    });\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}