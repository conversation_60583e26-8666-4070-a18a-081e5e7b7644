{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar rxjs_1 = require(\"rxjs\");\n\nexports.Subject = rxjs_1.Subject;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "rxjs_1", "require", "Subject"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs-compat/Subject.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar rxjs_1 = require(\"rxjs\");\nexports.Subject = rxjs_1.Subject;\n"], "mappings": "AAAA;;AACAA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;EAAEC,KAAK,EAAE;AAAT,CAA7C;;AACA,IAAIC,MAAM,GAAGC,OAAO,CAAC,MAAD,CAApB;;AACAH,OAAO,CAACI,OAAR,GAAkBF,MAAM,CAACE,OAAzB"}, "metadata": {}, "sourceType": "script"}