{"ast": null, "code": "/**\n * @returns {string}\n */\nfunction getCurrentScriptSource() {\n  // `document.currentScript` is the most accurate way to find the current script,\n  // but is not supported in all browsers.\n  if (document.currentScript) {\n    return document.currentScript.getAttribute(\"src\");\n  } // Fallback to getting all scripts running in the document.\n\n\n  var scriptElements = document.scripts || [];\n  var scriptElementsWithSrc = Array.prototype.filter.call(scriptElements, function (element) {\n    return element.getAttribute(\"src\");\n  });\n\n  if (scriptElementsWithSrc.length > 0) {\n    var currentScript = scriptElementsWithSrc[scriptElementsWithSrc.length - 1];\n    return currentScript.getAttribute(\"src\");\n  } // Fail as there was no script to use.\n\n\n  throw new Error(\"[webpack-dev-server] Failed to get current script source.\");\n}\n\nexport default getCurrentScriptSource;", "map": {"version": 3, "names": ["getCurrentScriptSource", "document", "currentScript", "getAttribute", "scriptElements", "scripts", "scriptElementsWithSrc", "Array", "prototype", "filter", "call", "element", "length", "Error"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/webpack-dev-server/client/utils/getCurrentScriptSource.js"], "sourcesContent": ["/**\n * @returns {string}\n */\nfunction getCurrentScriptSource() {\n  // `document.currentScript` is the most accurate way to find the current script,\n  // but is not supported in all browsers.\n  if (document.currentScript) {\n    return document.currentScript.getAttribute(\"src\");\n  } // Fallback to getting all scripts running in the document.\n\n\n  var scriptElements = document.scripts || [];\n  var scriptElementsWithSrc = Array.prototype.filter.call(scriptElements, function (element) {\n    return element.getAttribute(\"src\");\n  });\n\n  if (scriptElementsWithSrc.length > 0) {\n    var currentScript = scriptElementsWithSrc[scriptElementsWithSrc.length - 1];\n    return currentScript.getAttribute(\"src\");\n  } // Fail as there was no script to use.\n\n\n  throw new Error(\"[webpack-dev-server] Failed to get current script source.\");\n}\n\nexport default getCurrentScriptSource;"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,sBAAT,GAAkC;EAChC;EACA;EACA,IAAIC,QAAQ,CAACC,aAAb,EAA4B;IAC1B,OAAOD,QAAQ,CAACC,aAAT,CAAuBC,YAAvB,CAAoC,KAApC,CAAP;EACD,CAL+B,CAK9B;;;EAGF,IAAIC,cAAc,GAAGH,QAAQ,CAACI,OAAT,IAAoB,EAAzC;EACA,IAAIC,qBAAqB,GAAGC,KAAK,CAACC,SAAN,CAAgBC,MAAhB,CAAuBC,IAAvB,CAA4BN,cAA5B,EAA4C,UAAUO,OAAV,EAAmB;IACzF,OAAOA,OAAO,CAACR,YAAR,CAAqB,KAArB,CAAP;EACD,CAF2B,CAA5B;;EAIA,IAAIG,qBAAqB,CAACM,MAAtB,GAA+B,CAAnC,EAAsC;IACpC,IAAIV,aAAa,GAAGI,qBAAqB,CAACA,qBAAqB,CAACM,MAAtB,GAA+B,CAAhC,CAAzC;IACA,OAAOV,aAAa,CAACC,YAAd,CAA2B,KAA3B,CAAP;EACD,CAhB+B,CAgB9B;;;EAGF,MAAM,IAAIU,KAAJ,CAAU,2DAAV,CAAN;AACD;;AAED,eAAeb,sBAAf"}, "metadata": {}, "sourceType": "module"}