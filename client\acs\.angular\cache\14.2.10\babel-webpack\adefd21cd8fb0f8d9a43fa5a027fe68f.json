{"ast": null, "code": ";\n\n(function (root, factory) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var WordArray = C_lib.WordArray;\n    var C_enc = C.enc;\n    /**\n     * UTF-16 BE encoding strategy.\n     */\n\n    var Utf16BE = C_enc.Utf16 = C_enc.Utf16BE = {\n      /**\n       * Converts a word array to a UTF-16 BE string.\n       *\n       * @param {WordArray} wordArray The word array.\n       *\n       * @return {string} The UTF-16 BE string.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var utf16String = CryptoJS.enc.Utf16.stringify(wordArray);\n       */\n      stringify: function (wordArray) {\n        // Shortcuts\n        var words = wordArray.words;\n        var sigBytes = wordArray.sigBytes; // Convert\n\n        var utf16Chars = [];\n\n        for (var i = 0; i < sigBytes; i += 2) {\n          var codePoint = words[i >>> 2] >>> 16 - i % 4 * 8 & 0xffff;\n          utf16Chars.push(String.fromCharCode(codePoint));\n        }\n\n        return utf16Chars.join('');\n      },\n\n      /**\n       * Converts a UTF-16 BE string to a word array.\n       *\n       * @param {string} utf16Str The UTF-16 BE string.\n       *\n       * @return {WordArray} The word array.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var wordArray = CryptoJS.enc.Utf16.parse(utf16String);\n       */\n      parse: function (utf16Str) {\n        // Shortcut\n        var utf16StrLength = utf16Str.length; // Convert\n\n        var words = [];\n\n        for (var i = 0; i < utf16StrLength; i++) {\n          words[i >>> 1] |= utf16Str.charCodeAt(i) << 16 - i % 2 * 16;\n        }\n\n        return WordArray.create(words, utf16StrLength * 2);\n      }\n    };\n    /**\n     * UTF-16 LE encoding strategy.\n     */\n\n    C_enc.Utf16LE = {\n      /**\n       * Converts a word array to a UTF-16 LE string.\n       *\n       * @param {WordArray} wordArray The word array.\n       *\n       * @return {string} The UTF-16 LE string.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var utf16Str = CryptoJS.enc.Utf16LE.stringify(wordArray);\n       */\n      stringify: function (wordArray) {\n        // Shortcuts\n        var words = wordArray.words;\n        var sigBytes = wordArray.sigBytes; // Convert\n\n        var utf16Chars = [];\n\n        for (var i = 0; i < sigBytes; i += 2) {\n          var codePoint = swapEndian(words[i >>> 2] >>> 16 - i % 4 * 8 & 0xffff);\n          utf16Chars.push(String.fromCharCode(codePoint));\n        }\n\n        return utf16Chars.join('');\n      },\n\n      /**\n       * Converts a UTF-16 LE string to a word array.\n       *\n       * @param {string} utf16Str The UTF-16 LE string.\n       *\n       * @return {WordArray} The word array.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var wordArray = CryptoJS.enc.Utf16LE.parse(utf16Str);\n       */\n      parse: function (utf16Str) {\n        // Shortcut\n        var utf16StrLength = utf16Str.length; // Convert\n\n        var words = [];\n\n        for (var i = 0; i < utf16StrLength; i++) {\n          words[i >>> 1] |= swapEndian(utf16Str.charCodeAt(i) << 16 - i % 2 * 16);\n        }\n\n        return WordArray.create(words, utf16StrLength * 2);\n      }\n    };\n\n    function swapEndian(word) {\n      return word << 8 & 0xff00ff00 | word >>> 8 & 0x00ff00ff;\n    }\n  })();\n\n  return CryptoJS.enc.Utf16;\n});", "map": {"version": 3, "names": ["root", "factory", "exports", "module", "require", "define", "amd", "CryptoJS", "C", "C_lib", "lib", "WordArray", "C_enc", "enc", "Utf16BE", "Utf16", "stringify", "wordArray", "words", "sigBytes", "utf16Chars", "i", "codePoint", "push", "String", "fromCharCode", "join", "parse", "utf16Str", "utf16StrLength", "length", "charCodeAt", "create", "Utf16LE", "swapEndian", "word"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/crypto-js/enc-utf16.js"], "sourcesContent": [";(function (root, factory) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t(function () {\n\t    // Shortcuts\n\t    var C = CryptoJS;\n\t    var C_lib = C.lib;\n\t    var WordArray = C_lib.WordArray;\n\t    var C_enc = C.enc;\n\n\t    /**\n\t     * UTF-16 BE encoding strategy.\n\t     */\n\t    var Utf16BE = C_enc.Utf16 = C_enc.Utf16BE = {\n\t        /**\n\t         * Converts a word array to a UTF-16 BE string.\n\t         *\n\t         * @param {WordArray} wordArray The word array.\n\t         *\n\t         * @return {string} The UTF-16 BE string.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var utf16String = CryptoJS.enc.Utf16.stringify(wordArray);\n\t         */\n\t        stringify: function (wordArray) {\n\t            // Shortcuts\n\t            var words = wordArray.words;\n\t            var sigBytes = wordArray.sigBytes;\n\n\t            // Convert\n\t            var utf16Chars = [];\n\t            for (var i = 0; i < sigBytes; i += 2) {\n\t                var codePoint = (words[i >>> 2] >>> (16 - (i % 4) * 8)) & 0xffff;\n\t                utf16Chars.push(String.fromCharCode(codePoint));\n\t            }\n\n\t            return utf16Chars.join('');\n\t        },\n\n\t        /**\n\t         * Converts a UTF-16 BE string to a word array.\n\t         *\n\t         * @param {string} utf16Str The UTF-16 BE string.\n\t         *\n\t         * @return {WordArray} The word array.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var wordArray = CryptoJS.enc.Utf16.parse(utf16String);\n\t         */\n\t        parse: function (utf16Str) {\n\t            // Shortcut\n\t            var utf16StrLength = utf16Str.length;\n\n\t            // Convert\n\t            var words = [];\n\t            for (var i = 0; i < utf16StrLength; i++) {\n\t                words[i >>> 1] |= utf16Str.charCodeAt(i) << (16 - (i % 2) * 16);\n\t            }\n\n\t            return WordArray.create(words, utf16StrLength * 2);\n\t        }\n\t    };\n\n\t    /**\n\t     * UTF-16 LE encoding strategy.\n\t     */\n\t    C_enc.Utf16LE = {\n\t        /**\n\t         * Converts a word array to a UTF-16 LE string.\n\t         *\n\t         * @param {WordArray} wordArray The word array.\n\t         *\n\t         * @return {string} The UTF-16 LE string.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var utf16Str = CryptoJS.enc.Utf16LE.stringify(wordArray);\n\t         */\n\t        stringify: function (wordArray) {\n\t            // Shortcuts\n\t            var words = wordArray.words;\n\t            var sigBytes = wordArray.sigBytes;\n\n\t            // Convert\n\t            var utf16Chars = [];\n\t            for (var i = 0; i < sigBytes; i += 2) {\n\t                var codePoint = swapEndian((words[i >>> 2] >>> (16 - (i % 4) * 8)) & 0xffff);\n\t                utf16Chars.push(String.fromCharCode(codePoint));\n\t            }\n\n\t            return utf16Chars.join('');\n\t        },\n\n\t        /**\n\t         * Converts a UTF-16 LE string to a word array.\n\t         *\n\t         * @param {string} utf16Str The UTF-16 LE string.\n\t         *\n\t         * @return {WordArray} The word array.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var wordArray = CryptoJS.enc.Utf16LE.parse(utf16Str);\n\t         */\n\t        parse: function (utf16Str) {\n\t            // Shortcut\n\t            var utf16StrLength = utf16Str.length;\n\n\t            // Convert\n\t            var words = [];\n\t            for (var i = 0; i < utf16StrLength; i++) {\n\t                words[i >>> 1] |= swapEndian(utf16Str.charCodeAt(i) << (16 - (i % 2) * 16));\n\t            }\n\n\t            return WordArray.create(words, utf16StrLength * 2);\n\t        }\n\t    };\n\n\t    function swapEndian(word) {\n\t        return ((word << 8) & 0xff00ff00) | ((word >>> 8) & 0x00ff00ff);\n\t    }\n\t}());\n\n\n\treturn CryptoJS.enc.Utf16;\n\n}));"], "mappings": "AAAA;;AAAE,WAAUA,IAAV,EAAgBC,OAAhB,EAAyB;EAC1B,IAAI,OAAOC,OAAP,KAAmB,QAAvB,EAAiC;IAChC;IACAC,MAAM,CAACD,OAAP,GAAiBA,OAAO,GAAGD,OAAO,CAACG,OAAO,CAAC,QAAD,CAAR,CAAlC;EACA,CAHD,MAIK,IAAI,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAA3C,EAAgD;IACpD;IACAD,MAAM,CAAC,CAAC,QAAD,CAAD,EAAaJ,OAAb,CAAN;EACA,CAHI,MAIA;IACJ;IACAA,OAAO,CAACD,IAAI,CAACO,QAAN,CAAP;EACA;AACD,CAbC,EAaA,IAbA,EAaM,UAAUA,QAAV,EAAoB;EAE1B,aAAY;IACT;IACA,IAAIC,CAAC,GAAGD,QAAR;IACA,IAAIE,KAAK,GAAGD,CAAC,CAACE,GAAd;IACA,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAtB;IACA,IAAIC,KAAK,GAAGJ,CAAC,CAACK,GAAd;IAEA;AACL;AACA;;IACK,IAAIC,OAAO,GAAGF,KAAK,CAACG,KAAN,GAAcH,KAAK,CAACE,OAAN,GAAgB;MACxC;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSE,SAAS,EAAE,UAAUC,SAAV,EAAqB;QAC5B;QACA,IAAIC,KAAK,GAAGD,SAAS,CAACC,KAAtB;QACA,IAAIC,QAAQ,GAAGF,SAAS,CAACE,QAAzB,CAH4B,CAK5B;;QACA,IAAIC,UAAU,GAAG,EAAjB;;QACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,QAApB,EAA8BE,CAAC,IAAI,CAAnC,EAAsC;UAClC,IAAIC,SAAS,GAAIJ,KAAK,CAACG,CAAC,KAAK,CAAP,CAAL,KAAoB,KAAMA,CAAC,GAAG,CAAL,GAAU,CAApC,GAA0C,MAA1D;UACAD,UAAU,CAACG,IAAX,CAAgBC,MAAM,CAACC,YAAP,CAAoBH,SAApB,CAAhB;QACH;;QAED,OAAOF,UAAU,CAACM,IAAX,CAAgB,EAAhB,CAAP;MACH,CA3BuC;;MA6BxC;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSC,KAAK,EAAE,UAAUC,QAAV,EAAoB;QACvB;QACA,IAAIC,cAAc,GAAGD,QAAQ,CAACE,MAA9B,CAFuB,CAIvB;;QACA,IAAIZ,KAAK,GAAG,EAAZ;;QACA,KAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGQ,cAApB,EAAoCR,CAAC,EAArC,EAAyC;UACrCH,KAAK,CAACG,CAAC,KAAK,CAAP,CAAL,IAAkBO,QAAQ,CAACG,UAAT,CAAoBV,CAApB,KAA2B,KAAMA,CAAC,GAAG,CAAL,GAAU,EAA5D;QACH;;QAED,OAAOV,SAAS,CAACqB,MAAV,CAAiBd,KAAjB,EAAwBW,cAAc,GAAG,CAAzC,CAAP;MACH;IArDuC,CAA5C;IAwDA;AACL;AACA;;IACKjB,KAAK,CAACqB,OAAN,GAAgB;MACZ;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSjB,SAAS,EAAE,UAAUC,SAAV,EAAqB;QAC5B;QACA,IAAIC,KAAK,GAAGD,SAAS,CAACC,KAAtB;QACA,IAAIC,QAAQ,GAAGF,SAAS,CAACE,QAAzB,CAH4B,CAK5B;;QACA,IAAIC,UAAU,GAAG,EAAjB;;QACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,QAApB,EAA8BE,CAAC,IAAI,CAAnC,EAAsC;UAClC,IAAIC,SAAS,GAAGY,UAAU,CAAEhB,KAAK,CAACG,CAAC,KAAK,CAAP,CAAL,KAAoB,KAAMA,CAAC,GAAG,CAAL,GAAU,CAApC,GAA0C,MAA3C,CAA1B;UACAD,UAAU,CAACG,IAAX,CAAgBC,MAAM,CAACC,YAAP,CAAoBH,SAApB,CAAhB;QACH;;QAED,OAAOF,UAAU,CAACM,IAAX,CAAgB,EAAhB,CAAP;MACH,CA3BW;;MA6BZ;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSC,KAAK,EAAE,UAAUC,QAAV,EAAoB;QACvB;QACA,IAAIC,cAAc,GAAGD,QAAQ,CAACE,MAA9B,CAFuB,CAIvB;;QACA,IAAIZ,KAAK,GAAG,EAAZ;;QACA,KAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGQ,cAApB,EAAoCR,CAAC,EAArC,EAAyC;UACrCH,KAAK,CAACG,CAAC,KAAK,CAAP,CAAL,IAAkBa,UAAU,CAACN,QAAQ,CAACG,UAAT,CAAoBV,CAApB,KAA2B,KAAMA,CAAC,GAAG,CAAL,GAAU,EAA3C,CAA5B;QACH;;QAED,OAAOV,SAAS,CAACqB,MAAV,CAAiBd,KAAjB,EAAwBW,cAAc,GAAG,CAAzC,CAAP;MACH;IArDW,CAAhB;;IAwDA,SAASK,UAAT,CAAoBC,IAApB,EAA0B;MACtB,OAASA,IAAI,IAAI,CAAT,GAAc,UAAf,GAA+BA,IAAI,KAAK,CAAV,GAAe,UAApD;IACH;EACJ,CAhIA,GAAD;;EAmIA,OAAO5B,QAAQ,CAACM,GAAT,CAAaE,KAApB;AAEA,CApJC,CAAD"}, "metadata": {}, "sourceType": "script"}