{"ast": null, "code": "import { isArray } from '../util/isArray';\nimport { CombineLatestOperator } from '../observable/combineLatest';\nimport { from } from '../observable/from';\nconst none = {};\nexport function combineLatest(...observables) {\n  let project = null;\n\n  if (typeof observables[observables.length - 1] === 'function') {\n    project = observables.pop();\n  }\n\n  if (observables.length === 1 && isArray(observables[0])) {\n    observables = observables[0].slice();\n  }\n\n  return source => source.lift.call(from([source, ...observables]), new CombineLatestOperator(project));\n}", "map": {"version": 3, "names": ["isArray", "CombineLatestOperator", "from", "none", "combineLatest", "observables", "project", "length", "pop", "slice", "source", "lift", "call"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/combineLatest.js"], "sourcesContent": ["import { isArray } from '../util/isArray';\nimport { CombineLatestOperator } from '../observable/combineLatest';\nimport { from } from '../observable/from';\nconst none = {};\nexport function combineLatest(...observables) {\n    let project = null;\n    if (typeof observables[observables.length - 1] === 'function') {\n        project = observables.pop();\n    }\n    if (observables.length === 1 && isArray(observables[0])) {\n        observables = observables[0].slice();\n    }\n    return (source) => source.lift.call(from([source, ...observables]), new CombineLatestOperator(project));\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,iBAAxB;AACA,SAASC,qBAAT,QAAsC,6BAAtC;AACA,SAASC,IAAT,QAAqB,oBAArB;AACA,MAAMC,IAAI,GAAG,EAAb;AACA,OAAO,SAASC,aAAT,CAAuB,GAAGC,WAA1B,EAAuC;EAC1C,IAAIC,OAAO,GAAG,IAAd;;EACA,IAAI,OAAOD,WAAW,CAACA,WAAW,CAACE,MAAZ,GAAqB,CAAtB,CAAlB,KAA+C,UAAnD,EAA+D;IAC3DD,OAAO,GAAGD,WAAW,CAACG,GAAZ,EAAV;EACH;;EACD,IAAIH,WAAW,CAACE,MAAZ,KAAuB,CAAvB,IAA4BP,OAAO,CAACK,WAAW,CAAC,CAAD,CAAZ,CAAvC,EAAyD;IACrDA,WAAW,GAAGA,WAAW,CAAC,CAAD,CAAX,CAAeI,KAAf,EAAd;EACH;;EACD,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAYC,IAAZ,CAAiBV,IAAI,CAAC,CAACQ,MAAD,EAAS,GAAGL,WAAZ,CAAD,CAArB,EAAiD,IAAIJ,qBAAJ,CAA0BK,OAA1B,CAAjD,CAAnB;AACH"}, "metadata": {}, "sourceType": "module"}