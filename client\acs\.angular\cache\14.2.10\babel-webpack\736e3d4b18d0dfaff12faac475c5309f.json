{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function defaultIfEmpty(defaultValue = null) {\n  return source => source.lift(new DefaultIfEmptyOperator(defaultValue));\n}\n\nclass DefaultIfEmptyOperator {\n  constructor(defaultValue) {\n    this.defaultValue = defaultValue;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new DefaultIfEmptySubscriber(subscriber, this.defaultValue));\n  }\n\n}\n\nclass DefaultIfEmptySubscriber extends Subscriber {\n  constructor(destination, defaultValue) {\n    super(destination);\n    this.defaultValue = defaultValue;\n    this.isEmpty = true;\n  }\n\n  _next(value) {\n    this.isEmpty = false;\n    this.destination.next(value);\n  }\n\n  _complete() {\n    if (this.isEmpty) {\n      this.destination.next(this.defaultValue);\n    }\n\n    this.destination.complete();\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "defaultIfEmpty", "defaultValue", "source", "lift", "DefaultIfEmptyOperator", "constructor", "call", "subscriber", "subscribe", "DefaultIfEmptySubscriber", "destination", "isEmpty", "_next", "value", "next", "_complete", "complete"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/defaultIfEmpty.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function defaultIfEmpty(defaultValue = null) {\n    return (source) => source.lift(new DefaultIfEmptyOperator(defaultValue));\n}\nclass DefaultIfEmptyOperator {\n    constructor(defaultValue) {\n        this.defaultValue = defaultValue;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new DefaultIfEmptySubscriber(subscriber, this.defaultValue));\n    }\n}\nclass DefaultIfEmptySubscriber extends Subscriber {\n    constructor(destination, defaultValue) {\n        super(destination);\n        this.defaultValue = defaultValue;\n        this.isEmpty = true;\n    }\n    _next(value) {\n        this.isEmpty = false;\n        this.destination.next(value);\n    }\n    _complete() {\n        if (this.isEmpty) {\n            this.destination.next(this.defaultValue);\n        }\n        this.destination.complete();\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,cAAT,CAAwBC,YAAY,GAAG,IAAvC,EAA6C;EAChD,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,sBAAJ,CAA2BH,YAA3B,CAAZ,CAAnB;AACH;;AACD,MAAMG,sBAAN,CAA6B;EACzBC,WAAW,CAACJ,YAAD,EAAe;IACtB,KAAKA,YAAL,GAAoBA,YAApB;EACH;;EACDK,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,wBAAJ,CAA6BF,UAA7B,EAAyC,KAAKN,YAA9C,CAAjB,CAAP;EACH;;AANwB;;AAQ7B,MAAMQ,wBAAN,SAAuCV,UAAvC,CAAkD;EAC9CM,WAAW,CAACK,WAAD,EAAcT,YAAd,EAA4B;IACnC,MAAMS,WAAN;IACA,KAAKT,YAAL,GAAoBA,YAApB;IACA,KAAKU,OAAL,GAAe,IAAf;EACH;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACT,KAAKF,OAAL,GAAe,KAAf;IACA,KAAKD,WAAL,CAAiBI,IAAjB,CAAsBD,KAAtB;EACH;;EACDE,SAAS,GAAG;IACR,IAAI,KAAKJ,OAAT,EAAkB;MACd,KAAKD,WAAL,CAAiBI,IAAjB,CAAsB,KAAKb,YAA3B;IACH;;IACD,KAAKS,WAAL,CAAiBM,QAAjB;EACH;;AAf6C"}, "metadata": {}, "sourceType": "module"}