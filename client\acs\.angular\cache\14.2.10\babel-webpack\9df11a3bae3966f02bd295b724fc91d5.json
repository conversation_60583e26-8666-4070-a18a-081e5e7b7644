{"ast": null, "code": "import { EmptyError } from '../util/EmptyError';\nimport { Subscriber } from '../Subscriber';\nexport function throwIfEmpty(errorFactory = defaultErrorFactory) {\n  return source => {\n    return source.lift(new ThrowIfEmptyOperator(errorFactory));\n  };\n}\n\nclass ThrowIfEmptyOperator {\n  constructor(errorFactory) {\n    this.errorFactory = errorFactory;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new ThrowIfEmptySubscriber(subscriber, this.errorFactory));\n  }\n\n}\n\nclass ThrowIfEmptySubscriber extends Subscriber {\n  constructor(destination, errorFactory) {\n    super(destination);\n    this.errorFactory = errorFactory;\n    this.hasValue = false;\n  }\n\n  _next(value) {\n    this.hasValue = true;\n    this.destination.next(value);\n  }\n\n  _complete() {\n    if (!this.hasValue) {\n      let err;\n\n      try {\n        err = this.errorFactory();\n      } catch (e) {\n        err = e;\n      }\n\n      this.destination.error(err);\n    } else {\n      return this.destination.complete();\n    }\n  }\n\n}\n\nfunction defaultErrorFactory() {\n  return new EmptyError();\n}", "map": {"version": 3, "names": ["EmptyError", "Subscriber", "throwIfEmpty", "errorFactory", "defaultErrorFactory", "source", "lift", "ThrowIfEmptyOperator", "constructor", "call", "subscriber", "subscribe", "ThrowIfEmptySubscriber", "destination", "hasValue", "_next", "value", "next", "_complete", "err", "e", "error", "complete"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/throwIfEmpty.js"], "sourcesContent": ["import { EmptyError } from '../util/EmptyError';\nimport { Subscriber } from '../Subscriber';\nexport function throwIfEmpty(errorFactory = defaultErrorFactory) {\n    return (source) => {\n        return source.lift(new ThrowIfEmptyOperator(errorFactory));\n    };\n}\nclass ThrowIfEmptyOperator {\n    constructor(errorFactory) {\n        this.errorFactory = errorFactory;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new ThrowIfEmptySubscriber(subscriber, this.errorFactory));\n    }\n}\nclass ThrowIfEmptySubscriber extends Subscriber {\n    constructor(destination, errorFactory) {\n        super(destination);\n        this.errorFactory = errorFactory;\n        this.hasValue = false;\n    }\n    _next(value) {\n        this.hasValue = true;\n        this.destination.next(value);\n    }\n    _complete() {\n        if (!this.hasValue) {\n            let err;\n            try {\n                err = this.errorFactory();\n            }\n            catch (e) {\n                err = e;\n            }\n            this.destination.error(err);\n        }\n        else {\n            return this.destination.complete();\n        }\n    }\n}\nfunction defaultErrorFactory() {\n    return new EmptyError();\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,oBAA3B;AACA,SAASC,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,YAAT,CAAsBC,YAAY,GAAGC,mBAArC,EAA0D;EAC7D,OAAQC,MAAD,IAAY;IACf,OAAOA,MAAM,CAACC,IAAP,CAAY,IAAIC,oBAAJ,CAAyBJ,YAAzB,CAAZ,CAAP;EACH,CAFD;AAGH;;AACD,MAAMI,oBAAN,CAA2B;EACvBC,WAAW,CAACL,YAAD,EAAe;IACtB,KAAKA,YAAL,GAAoBA,YAApB;EACH;;EACDM,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,sBAAJ,CAA2BF,UAA3B,EAAuC,KAAKP,YAA5C,CAAjB,CAAP;EACH;;AANsB;;AAQ3B,MAAMS,sBAAN,SAAqCX,UAArC,CAAgD;EAC5CO,WAAW,CAACK,WAAD,EAAcV,YAAd,EAA4B;IACnC,MAAMU,WAAN;IACA,KAAKV,YAAL,GAAoBA,YAApB;IACA,KAAKW,QAAL,GAAgB,KAAhB;EACH;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACT,KAAKF,QAAL,GAAgB,IAAhB;IACA,KAAKD,WAAL,CAAiBI,IAAjB,CAAsBD,KAAtB;EACH;;EACDE,SAAS,GAAG;IACR,IAAI,CAAC,KAAKJ,QAAV,EAAoB;MAChB,IAAIK,GAAJ;;MACA,IAAI;QACAA,GAAG,GAAG,KAAKhB,YAAL,EAAN;MACH,CAFD,CAGA,OAAOiB,CAAP,EAAU;QACND,GAAG,GAAGC,CAAN;MACH;;MACD,KAAKP,WAAL,CAAiBQ,KAAjB,CAAuBF,GAAvB;IACH,CATD,MAUK;MACD,OAAO,KAAKN,WAAL,CAAiBS,QAAjB,EAAP;IACH;EACJ;;AAxB2C;;AA0BhD,SAASlB,mBAAT,GAA+B;EAC3B,OAAO,IAAIJ,UAAJ,EAAP;AACH"}, "metadata": {}, "sourceType": "module"}