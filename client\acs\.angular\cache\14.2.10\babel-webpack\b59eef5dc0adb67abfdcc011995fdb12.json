{"ast": null, "code": "import { mergeMap } from './mergeMap';\nexport function concatMap(project, resultSelector) {\n  return mergeMap(project, resultSelector, 1);\n}", "map": {"version": 3, "names": ["mergeMap", "concatMap", "project", "resultSelector"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/concatMap.js"], "sourcesContent": ["import { mergeMap } from './mergeMap';\nexport function concatMap(project, resultSelector) {\n    return mergeMap(project, resultSelector, 1);\n}\n"], "mappings": "AAAA,SAASA,QAAT,QAAyB,YAAzB;AACA,OAAO,SAASC,SAAT,CAAmBC,OAAnB,EAA4BC,cAA5B,EAA4C;EAC/C,OAAOH,QAAQ,CAACE,OAAD,EAAUC,cAAV,EAA0B,CAA1B,CAAf;AACH"}, "metadata": {}, "sourceType": "module"}