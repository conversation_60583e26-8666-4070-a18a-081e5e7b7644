{"ast": null, "code": "import { iterator as Symbol_iterator } from '../symbol/iterator';\nexport const subscribeToIterable = iterable => subscriber => {\n  const iterator = iterable[Symbol_iterator]();\n\n  do {\n    let item;\n\n    try {\n      item = iterator.next();\n    } catch (err) {\n      subscriber.error(err);\n      return subscriber;\n    }\n\n    if (item.done) {\n      subscriber.complete();\n      break;\n    }\n\n    subscriber.next(item.value);\n\n    if (subscriber.closed) {\n      break;\n    }\n  } while (true);\n\n  if (typeof iterator.return === 'function') {\n    subscriber.add(() => {\n      if (iterator.return) {\n        iterator.return();\n      }\n    });\n  }\n\n  return subscriber;\n};", "map": {"version": 3, "names": ["iterator", "Symbol_iterator", "subscribeToIterable", "iterable", "subscriber", "item", "next", "err", "error", "done", "complete", "value", "closed", "return", "add"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/util/subscribeToIterable.js"], "sourcesContent": ["import { iterator as Symbol_iterator } from '../symbol/iterator';\nexport const subscribeToIterable = (iterable) => (subscriber) => {\n    const iterator = iterable[Symbol_iterator]();\n    do {\n        let item;\n        try {\n            item = iterator.next();\n        }\n        catch (err) {\n            subscriber.error(err);\n            return subscriber;\n        }\n        if (item.done) {\n            subscriber.complete();\n            break;\n        }\n        subscriber.next(item.value);\n        if (subscriber.closed) {\n            break;\n        }\n    } while (true);\n    if (typeof iterator.return === 'function') {\n        subscriber.add(() => {\n            if (iterator.return) {\n                iterator.return();\n            }\n        });\n    }\n    return subscriber;\n};\n"], "mappings": "AAAA,SAASA,QAAQ,IAAIC,eAArB,QAA4C,oBAA5C;AACA,OAAO,MAAMC,mBAAmB,GAAIC,QAAD,IAAeC,UAAD,IAAgB;EAC7D,MAAMJ,QAAQ,GAAGG,QAAQ,CAACF,eAAD,CAAR,EAAjB;;EACA,GAAG;IACC,IAAII,IAAJ;;IACA,IAAI;MACAA,IAAI,GAAGL,QAAQ,CAACM,IAAT,EAAP;IACH,CAFD,CAGA,OAAOC,GAAP,EAAY;MACRH,UAAU,CAACI,KAAX,CAAiBD,GAAjB;MACA,OAAOH,UAAP;IACH;;IACD,IAAIC,IAAI,CAACI,IAAT,EAAe;MACXL,UAAU,CAACM,QAAX;MACA;IACH;;IACDN,UAAU,CAACE,IAAX,CAAgBD,IAAI,CAACM,KAArB;;IACA,IAAIP,UAAU,CAACQ,MAAf,EAAuB;MACnB;IACH;EACJ,CAjBD,QAiBS,IAjBT;;EAkBA,IAAI,OAAOZ,QAAQ,CAACa,MAAhB,KAA2B,UAA/B,EAA2C;IACvCT,UAAU,CAACU,GAAX,CAAe,MAAM;MACjB,IAAId,QAAQ,CAACa,MAAb,EAAqB;QACjBb,QAAQ,CAACa,MAAT;MACH;IACJ,CAJD;EAKH;;EACD,OAAOT,UAAP;AACH,CA5BM"}, "metadata": {}, "sourceType": "module"}