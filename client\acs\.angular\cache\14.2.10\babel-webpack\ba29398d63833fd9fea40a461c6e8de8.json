{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function scan(accumulator, seed) {\n  let hasSeed = false;\n\n  if (arguments.length >= 2) {\n    hasSeed = true;\n  }\n\n  return function scanOperatorFunction(source) {\n    return source.lift(new ScanOperator(accumulator, seed, hasSeed));\n  };\n}\n\nclass ScanOperator {\n  constructor(accumulator, seed, hasSeed = false) {\n    this.accumulator = accumulator;\n    this.seed = seed;\n    this.hasSeed = hasSeed;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new ScanSubscriber(subscriber, this.accumulator, this.seed, this.hasSeed));\n  }\n\n}\n\nclass ScanSubscriber extends Subscriber {\n  constructor(destination, accumulator, _seed, hasSeed) {\n    super(destination);\n    this.accumulator = accumulator;\n    this._seed = _seed;\n    this.hasSeed = hasSeed;\n    this.index = 0;\n  }\n\n  get seed() {\n    return this._seed;\n  }\n\n  set seed(value) {\n    this.hasSeed = true;\n    this._seed = value;\n  }\n\n  _next(value) {\n    if (!this.hasSeed) {\n      this.seed = value;\n      this.destination.next(value);\n    } else {\n      return this._tryNext(value);\n    }\n  }\n\n  _tryNext(value) {\n    const index = this.index++;\n    let result;\n\n    try {\n      result = this.accumulator(this.seed, value, index);\n    } catch (err) {\n      this.destination.error(err);\n    }\n\n    this.seed = result;\n    this.destination.next(result);\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "scan", "accumulator", "seed", "hasSeed", "arguments", "length", "scanOperatorFunction", "source", "lift", "ScanOperator", "constructor", "call", "subscriber", "subscribe", "ScanSubscriber", "destination", "_seed", "index", "value", "_next", "next", "_tryNext", "result", "err", "error"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/scan.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function scan(accumulator, seed) {\n    let hasSeed = false;\n    if (arguments.length >= 2) {\n        hasSeed = true;\n    }\n    return function scanOperatorFunction(source) {\n        return source.lift(new ScanOperator(accumulator, seed, hasSeed));\n    };\n}\nclass ScanOperator {\n    constructor(accumulator, seed, hasSeed = false) {\n        this.accumulator = accumulator;\n        this.seed = seed;\n        this.hasSeed = hasSeed;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new ScanSubscriber(subscriber, this.accumulator, this.seed, this.hasSeed));\n    }\n}\nclass ScanSubscriber extends Subscriber {\n    constructor(destination, accumulator, _seed, hasSeed) {\n        super(destination);\n        this.accumulator = accumulator;\n        this._seed = _seed;\n        this.hasSeed = hasSeed;\n        this.index = 0;\n    }\n    get seed() {\n        return this._seed;\n    }\n    set seed(value) {\n        this.hasSeed = true;\n        this._seed = value;\n    }\n    _next(value) {\n        if (!this.hasSeed) {\n            this.seed = value;\n            this.destination.next(value);\n        }\n        else {\n            return this._tryNext(value);\n        }\n    }\n    _tryNext(value) {\n        const index = this.index++;\n        let result;\n        try {\n            result = this.accumulator(this.seed, value, index);\n        }\n        catch (err) {\n            this.destination.error(err);\n        }\n        this.seed = result;\n        this.destination.next(result);\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,IAAT,CAAcC,WAAd,EAA2BC,IAA3B,EAAiC;EACpC,IAAIC,OAAO,GAAG,KAAd;;EACA,IAAIC,SAAS,CAACC,MAAV,IAAoB,CAAxB,EAA2B;IACvBF,OAAO,GAAG,IAAV;EACH;;EACD,OAAO,SAASG,oBAAT,CAA8BC,MAA9B,EAAsC;IACzC,OAAOA,MAAM,CAACC,IAAP,CAAY,IAAIC,YAAJ,CAAiBR,WAAjB,EAA8BC,IAA9B,EAAoCC,OAApC,CAAZ,CAAP;EACH,CAFD;AAGH;;AACD,MAAMM,YAAN,CAAmB;EACfC,WAAW,CAACT,WAAD,EAAcC,IAAd,EAAoBC,OAAO,GAAG,KAA9B,EAAqC;IAC5C,KAAKF,WAAL,GAAmBA,WAAnB;IACA,KAAKC,IAAL,GAAYA,IAAZ;IACA,KAAKC,OAAL,GAAeA,OAAf;EACH;;EACDQ,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,cAAJ,CAAmBF,UAAnB,EAA+B,KAAKX,WAApC,EAAiD,KAAKC,IAAtD,EAA4D,KAAKC,OAAjE,CAAjB,CAAP;EACH;;AARc;;AAUnB,MAAMW,cAAN,SAA6Bf,UAA7B,CAAwC;EACpCW,WAAW,CAACK,WAAD,EAAcd,WAAd,EAA2Be,KAA3B,EAAkCb,OAAlC,EAA2C;IAClD,MAAMY,WAAN;IACA,KAAKd,WAAL,GAAmBA,WAAnB;IACA,KAAKe,KAAL,GAAaA,KAAb;IACA,KAAKb,OAAL,GAAeA,OAAf;IACA,KAAKc,KAAL,GAAa,CAAb;EACH;;EACO,IAAJf,IAAI,GAAG;IACP,OAAO,KAAKc,KAAZ;EACH;;EACO,IAAJd,IAAI,CAACgB,KAAD,EAAQ;IACZ,KAAKf,OAAL,GAAe,IAAf;IACA,KAAKa,KAAL,GAAaE,KAAb;EACH;;EACDC,KAAK,CAACD,KAAD,EAAQ;IACT,IAAI,CAAC,KAAKf,OAAV,EAAmB;MACf,KAAKD,IAAL,GAAYgB,KAAZ;MACA,KAAKH,WAAL,CAAiBK,IAAjB,CAAsBF,KAAtB;IACH,CAHD,MAIK;MACD,OAAO,KAAKG,QAAL,CAAcH,KAAd,CAAP;IACH;EACJ;;EACDG,QAAQ,CAACH,KAAD,EAAQ;IACZ,MAAMD,KAAK,GAAG,KAAKA,KAAL,EAAd;IACA,IAAIK,MAAJ;;IACA,IAAI;MACAA,MAAM,GAAG,KAAKrB,WAAL,CAAiB,KAAKC,IAAtB,EAA4BgB,KAA5B,EAAmCD,KAAnC,CAAT;IACH,CAFD,CAGA,OAAOM,GAAP,EAAY;MACR,KAAKR,WAAL,CAAiBS,KAAjB,CAAuBD,GAAvB;IACH;;IACD,KAAKrB,IAAL,GAAYoB,MAAZ;IACA,KAAKP,WAAL,CAAiBK,IAAjB,CAAsBE,MAAtB;EACH;;AAnCmC"}, "metadata": {}, "sourceType": "module"}