{"ast": null, "code": "module.exports = function isBuffer(arg) {\n  return arg && typeof arg === 'object' && typeof arg.copy === 'function' && typeof arg.fill === 'function' && typeof arg.readUInt8 === 'function';\n};", "map": {"version": 3, "names": ["module", "exports", "<PERSON><PERSON><PERSON><PERSON>", "arg", "copy", "fill", "readUInt8"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/util/support/isBufferBrowser.js"], "sourcesContent": ["module.exports = function isBuffer(arg) {\n  return arg && typeof arg === 'object'\n    && typeof arg.copy === 'function'\n    && typeof arg.fill === 'function'\n    && typeof arg.readUInt8 === 'function';\n}"], "mappings": "AAAAA,MAAM,CAACC,OAAP,GAAiB,SAASC,QAAT,CAAkBC,GAAlB,EAAuB;EACtC,OAAOA,GAAG,IAAI,OAAOA,GAAP,KAAe,QAAtB,IACF,OAAOA,GAAG,CAACC,IAAX,KAAoB,UADlB,IAEF,OAAOD,GAAG,CAACE,IAAX,KAAoB,UAFlB,IAGF,OAAOF,GAAG,CAACG,SAAX,KAAyB,UAH9B;AAID,CALD"}, "metadata": {}, "sourceType": "script"}