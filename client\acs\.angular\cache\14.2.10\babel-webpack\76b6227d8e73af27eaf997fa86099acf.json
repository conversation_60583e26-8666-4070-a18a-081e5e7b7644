{"ast": null, "code": "import { Observable } from './Observable';\nimport { Subscriber } from './Subscriber';\nimport { Subscription } from './Subscription';\nimport { ObjectUnsubscribedError } from './util/ObjectUnsubscribedError';\nimport { SubjectSubscription } from './SubjectSubscription';\nimport { rxSubscriber as rxSubscriberSymbol } from '../internal/symbol/rxSubscriber';\nexport class SubjectSubscriber extends Subscriber {\n  constructor(destination) {\n    super(destination);\n    this.destination = destination;\n  }\n\n}\nexport class Subject extends Observable {\n  constructor() {\n    super();\n    this.observers = [];\n    this.closed = false;\n    this.isStopped = false;\n    this.hasError = false;\n    this.thrownError = null;\n  }\n\n  [rxSubscriberSymbol]() {\n    return new SubjectSubscriber(this);\n  }\n\n  lift(operator) {\n    const subject = new AnonymousSubject(this, this);\n    subject.operator = operator;\n    return subject;\n  }\n\n  next(value) {\n    if (this.closed) {\n      throw new ObjectUnsubscribedError();\n    }\n\n    if (!this.isStopped) {\n      const {\n        observers\n      } = this;\n      const len = observers.length;\n      const copy = observers.slice();\n\n      for (let i = 0; i < len; i++) {\n        copy[i].next(value);\n      }\n    }\n  }\n\n  error(err) {\n    if (this.closed) {\n      throw new ObjectUnsubscribedError();\n    }\n\n    this.hasError = true;\n    this.thrownError = err;\n    this.isStopped = true;\n    const {\n      observers\n    } = this;\n    const len = observers.length;\n    const copy = observers.slice();\n\n    for (let i = 0; i < len; i++) {\n      copy[i].error(err);\n    }\n\n    this.observers.length = 0;\n  }\n\n  complete() {\n    if (this.closed) {\n      throw new ObjectUnsubscribedError();\n    }\n\n    this.isStopped = true;\n    const {\n      observers\n    } = this;\n    const len = observers.length;\n    const copy = observers.slice();\n\n    for (let i = 0; i < len; i++) {\n      copy[i].complete();\n    }\n\n    this.observers.length = 0;\n  }\n\n  unsubscribe() {\n    this.isStopped = true;\n    this.closed = true;\n    this.observers = null;\n  }\n\n  _trySubscribe(subscriber) {\n    if (this.closed) {\n      throw new ObjectUnsubscribedError();\n    } else {\n      return super._trySubscribe(subscriber);\n    }\n  }\n\n  _subscribe(subscriber) {\n    if (this.closed) {\n      throw new ObjectUnsubscribedError();\n    } else if (this.hasError) {\n      subscriber.error(this.thrownError);\n      return Subscription.EMPTY;\n    } else if (this.isStopped) {\n      subscriber.complete();\n      return Subscription.EMPTY;\n    } else {\n      this.observers.push(subscriber);\n      return new SubjectSubscription(this, subscriber);\n    }\n  }\n\n  asObservable() {\n    const observable = new Observable();\n    observable.source = this;\n    return observable;\n  }\n\n}\n\nSubject.create = (destination, source) => {\n  return new AnonymousSubject(destination, source);\n};\n\nexport class AnonymousSubject extends Subject {\n  constructor(destination, source) {\n    super();\n    this.destination = destination;\n    this.source = source;\n  }\n\n  next(value) {\n    const {\n      destination\n    } = this;\n\n    if (destination && destination.next) {\n      destination.next(value);\n    }\n  }\n\n  error(err) {\n    const {\n      destination\n    } = this;\n\n    if (destination && destination.error) {\n      this.destination.error(err);\n    }\n  }\n\n  complete() {\n    const {\n      destination\n    } = this;\n\n    if (destination && destination.complete) {\n      this.destination.complete();\n    }\n  }\n\n  _subscribe(subscriber) {\n    const {\n      source\n    } = this;\n\n    if (source) {\n      return this.source.subscribe(subscriber);\n    } else {\n      return Subscription.EMPTY;\n    }\n  }\n\n}", "map": {"version": 3, "names": ["Observable", "Subscriber", "Subscription", "ObjectUnsubscribedError", "SubjectSubscription", "rxSubscriber", "rxSubscriberSymbol", "SubjectSubscriber", "constructor", "destination", "Subject", "observers", "closed", "isStopped", "<PERSON><PERSON><PERSON><PERSON>", "thrownError", "lift", "operator", "subject", "AnonymousSubject", "next", "value", "len", "length", "copy", "slice", "i", "error", "err", "complete", "unsubscribe", "_trySubscribe", "subscriber", "_subscribe", "EMPTY", "push", "asObservable", "observable", "source", "create", "subscribe"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/Subject.js"], "sourcesContent": ["import { Observable } from './Observable';\nimport { Subscriber } from './Subscriber';\nimport { Subscription } from './Subscription';\nimport { ObjectUnsubscribedError } from './util/ObjectUnsubscribedError';\nimport { SubjectSubscription } from './SubjectSubscription';\nimport { rxSubscriber as rxSubscriberSymbol } from '../internal/symbol/rxSubscriber';\nexport class SubjectSubscriber extends Subscriber {\n    constructor(destination) {\n        super(destination);\n        this.destination = destination;\n    }\n}\nexport class Subject extends Observable {\n    constructor() {\n        super();\n        this.observers = [];\n        this.closed = false;\n        this.isStopped = false;\n        this.hasError = false;\n        this.thrownError = null;\n    }\n    [rxSubscriberSymbol]() {\n        return new SubjectSubscriber(this);\n    }\n    lift(operator) {\n        const subject = new AnonymousSubject(this, this);\n        subject.operator = operator;\n        return subject;\n    }\n    next(value) {\n        if (this.closed) {\n            throw new ObjectUnsubscribedError();\n        }\n        if (!this.isStopped) {\n            const { observers } = this;\n            const len = observers.length;\n            const copy = observers.slice();\n            for (let i = 0; i < len; i++) {\n                copy[i].next(value);\n            }\n        }\n    }\n    error(err) {\n        if (this.closed) {\n            throw new ObjectUnsubscribedError();\n        }\n        this.hasError = true;\n        this.thrownError = err;\n        this.isStopped = true;\n        const { observers } = this;\n        const len = observers.length;\n        const copy = observers.slice();\n        for (let i = 0; i < len; i++) {\n            copy[i].error(err);\n        }\n        this.observers.length = 0;\n    }\n    complete() {\n        if (this.closed) {\n            throw new ObjectUnsubscribedError();\n        }\n        this.isStopped = true;\n        const { observers } = this;\n        const len = observers.length;\n        const copy = observers.slice();\n        for (let i = 0; i < len; i++) {\n            copy[i].complete();\n        }\n        this.observers.length = 0;\n    }\n    unsubscribe() {\n        this.isStopped = true;\n        this.closed = true;\n        this.observers = null;\n    }\n    _trySubscribe(subscriber) {\n        if (this.closed) {\n            throw new ObjectUnsubscribedError();\n        }\n        else {\n            return super._trySubscribe(subscriber);\n        }\n    }\n    _subscribe(subscriber) {\n        if (this.closed) {\n            throw new ObjectUnsubscribedError();\n        }\n        else if (this.hasError) {\n            subscriber.error(this.thrownError);\n            return Subscription.EMPTY;\n        }\n        else if (this.isStopped) {\n            subscriber.complete();\n            return Subscription.EMPTY;\n        }\n        else {\n            this.observers.push(subscriber);\n            return new SubjectSubscription(this, subscriber);\n        }\n    }\n    asObservable() {\n        const observable = new Observable();\n        observable.source = this;\n        return observable;\n    }\n}\nSubject.create = (destination, source) => {\n    return new AnonymousSubject(destination, source);\n};\nexport class AnonymousSubject extends Subject {\n    constructor(destination, source) {\n        super();\n        this.destination = destination;\n        this.source = source;\n    }\n    next(value) {\n        const { destination } = this;\n        if (destination && destination.next) {\n            destination.next(value);\n        }\n    }\n    error(err) {\n        const { destination } = this;\n        if (destination && destination.error) {\n            this.destination.error(err);\n        }\n    }\n    complete() {\n        const { destination } = this;\n        if (destination && destination.complete) {\n            this.destination.complete();\n        }\n    }\n    _subscribe(subscriber) {\n        const { source } = this;\n        if (source) {\n            return this.source.subscribe(subscriber);\n        }\n        else {\n            return Subscription.EMPTY;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,cAA3B;AACA,SAASC,UAAT,QAA2B,cAA3B;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,SAASC,uBAAT,QAAwC,gCAAxC;AACA,SAASC,mBAAT,QAAoC,uBAApC;AACA,SAASC,YAAY,IAAIC,kBAAzB,QAAmD,iCAAnD;AACA,OAAO,MAAMC,iBAAN,SAAgCN,UAAhC,CAA2C;EAC9CO,WAAW,CAACC,WAAD,EAAc;IACrB,MAAMA,WAAN;IACA,KAAKA,WAAL,GAAmBA,WAAnB;EACH;;AAJ6C;AAMlD,OAAO,MAAMC,OAAN,SAAsBV,UAAtB,CAAiC;EACpCQ,WAAW,GAAG;IACV;IACA,KAAKG,SAAL,GAAiB,EAAjB;IACA,KAAKC,MAAL,GAAc,KAAd;IACA,KAAKC,SAAL,GAAiB,KAAjB;IACA,KAAKC,QAAL,GAAgB,KAAhB;IACA,KAAKC,WAAL,GAAmB,IAAnB;EACH;;EACkB,CAAlBT,kBAAkB,IAAI;IACnB,OAAO,IAAIC,iBAAJ,CAAsB,IAAtB,CAAP;EACH;;EACDS,IAAI,CAACC,QAAD,EAAW;IACX,MAAMC,OAAO,GAAG,IAAIC,gBAAJ,CAAqB,IAArB,EAA2B,IAA3B,CAAhB;IACAD,OAAO,CAACD,QAAR,GAAmBA,QAAnB;IACA,OAAOC,OAAP;EACH;;EACDE,IAAI,CAACC,KAAD,EAAQ;IACR,IAAI,KAAKT,MAAT,EAAiB;MACb,MAAM,IAAIT,uBAAJ,EAAN;IACH;;IACD,IAAI,CAAC,KAAKU,SAAV,EAAqB;MACjB,MAAM;QAAEF;MAAF,IAAgB,IAAtB;MACA,MAAMW,GAAG,GAAGX,SAAS,CAACY,MAAtB;MACA,MAAMC,IAAI,GAAGb,SAAS,CAACc,KAAV,EAAb;;MACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,GAApB,EAAyBI,CAAC,EAA1B,EAA8B;QAC1BF,IAAI,CAACE,CAAD,CAAJ,CAAQN,IAAR,CAAaC,KAAb;MACH;IACJ;EACJ;;EACDM,KAAK,CAACC,GAAD,EAAM;IACP,IAAI,KAAKhB,MAAT,EAAiB;MACb,MAAM,IAAIT,uBAAJ,EAAN;IACH;;IACD,KAAKW,QAAL,GAAgB,IAAhB;IACA,KAAKC,WAAL,GAAmBa,GAAnB;IACA,KAAKf,SAAL,GAAiB,IAAjB;IACA,MAAM;MAAEF;IAAF,IAAgB,IAAtB;IACA,MAAMW,GAAG,GAAGX,SAAS,CAACY,MAAtB;IACA,MAAMC,IAAI,GAAGb,SAAS,CAACc,KAAV,EAAb;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,GAApB,EAAyBI,CAAC,EAA1B,EAA8B;MAC1BF,IAAI,CAACE,CAAD,CAAJ,CAAQC,KAAR,CAAcC,GAAd;IACH;;IACD,KAAKjB,SAAL,CAAeY,MAAf,GAAwB,CAAxB;EACH;;EACDM,QAAQ,GAAG;IACP,IAAI,KAAKjB,MAAT,EAAiB;MACb,MAAM,IAAIT,uBAAJ,EAAN;IACH;;IACD,KAAKU,SAAL,GAAiB,IAAjB;IACA,MAAM;MAAEF;IAAF,IAAgB,IAAtB;IACA,MAAMW,GAAG,GAAGX,SAAS,CAACY,MAAtB;IACA,MAAMC,IAAI,GAAGb,SAAS,CAACc,KAAV,EAAb;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,GAApB,EAAyBI,CAAC,EAA1B,EAA8B;MAC1BF,IAAI,CAACE,CAAD,CAAJ,CAAQG,QAAR;IACH;;IACD,KAAKlB,SAAL,CAAeY,MAAf,GAAwB,CAAxB;EACH;;EACDO,WAAW,GAAG;IACV,KAAKjB,SAAL,GAAiB,IAAjB;IACA,KAAKD,MAAL,GAAc,IAAd;IACA,KAAKD,SAAL,GAAiB,IAAjB;EACH;;EACDoB,aAAa,CAACC,UAAD,EAAa;IACtB,IAAI,KAAKpB,MAAT,EAAiB;MACb,MAAM,IAAIT,uBAAJ,EAAN;IACH,CAFD,MAGK;MACD,OAAO,MAAM4B,aAAN,CAAoBC,UAApB,CAAP;IACH;EACJ;;EACDC,UAAU,CAACD,UAAD,EAAa;IACnB,IAAI,KAAKpB,MAAT,EAAiB;MACb,MAAM,IAAIT,uBAAJ,EAAN;IACH,CAFD,MAGK,IAAI,KAAKW,QAAT,EAAmB;MACpBkB,UAAU,CAACL,KAAX,CAAiB,KAAKZ,WAAtB;MACA,OAAOb,YAAY,CAACgC,KAApB;IACH,CAHI,MAIA,IAAI,KAAKrB,SAAT,EAAoB;MACrBmB,UAAU,CAACH,QAAX;MACA,OAAO3B,YAAY,CAACgC,KAApB;IACH,CAHI,MAIA;MACD,KAAKvB,SAAL,CAAewB,IAAf,CAAoBH,UAApB;MACA,OAAO,IAAI5B,mBAAJ,CAAwB,IAAxB,EAA8B4B,UAA9B,CAAP;IACH;EACJ;;EACDI,YAAY,GAAG;IACX,MAAMC,UAAU,GAAG,IAAIrC,UAAJ,EAAnB;IACAqC,UAAU,CAACC,MAAX,GAAoB,IAApB;IACA,OAAOD,UAAP;EACH;;AA5FmC;;AA8FxC3B,OAAO,CAAC6B,MAAR,GAAiB,CAAC9B,WAAD,EAAc6B,MAAd,KAAyB;EACtC,OAAO,IAAInB,gBAAJ,CAAqBV,WAArB,EAAkC6B,MAAlC,CAAP;AACH,CAFD;;AAGA,OAAO,MAAMnB,gBAAN,SAA+BT,OAA/B,CAAuC;EAC1CF,WAAW,CAACC,WAAD,EAAc6B,MAAd,EAAsB;IAC7B;IACA,KAAK7B,WAAL,GAAmBA,WAAnB;IACA,KAAK6B,MAAL,GAAcA,MAAd;EACH;;EACDlB,IAAI,CAACC,KAAD,EAAQ;IACR,MAAM;MAAEZ;IAAF,IAAkB,IAAxB;;IACA,IAAIA,WAAW,IAAIA,WAAW,CAACW,IAA/B,EAAqC;MACjCX,WAAW,CAACW,IAAZ,CAAiBC,KAAjB;IACH;EACJ;;EACDM,KAAK,CAACC,GAAD,EAAM;IACP,MAAM;MAAEnB;IAAF,IAAkB,IAAxB;;IACA,IAAIA,WAAW,IAAIA,WAAW,CAACkB,KAA/B,EAAsC;MAClC,KAAKlB,WAAL,CAAiBkB,KAAjB,CAAuBC,GAAvB;IACH;EACJ;;EACDC,QAAQ,GAAG;IACP,MAAM;MAAEpB;IAAF,IAAkB,IAAxB;;IACA,IAAIA,WAAW,IAAIA,WAAW,CAACoB,QAA/B,EAAyC;MACrC,KAAKpB,WAAL,CAAiBoB,QAAjB;IACH;EACJ;;EACDI,UAAU,CAACD,UAAD,EAAa;IACnB,MAAM;MAAEM;IAAF,IAAa,IAAnB;;IACA,IAAIA,MAAJ,EAAY;MACR,OAAO,KAAKA,MAAL,CAAYE,SAAZ,CAAsBR,UAAtB,CAAP;IACH,CAFD,MAGK;MACD,OAAO9B,YAAY,CAACgC,KAApB;IACH;EACJ;;AAhCyC"}, "metadata": {}, "sourceType": "module"}