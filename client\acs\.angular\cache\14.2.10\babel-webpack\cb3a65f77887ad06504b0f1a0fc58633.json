{"ast": null, "code": "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\n\nvar callBind = require('./');\n\nvar $indexOf = callBind(GetIntrinsic('String.prototype.indexOf'));\n\nmodule.exports = function callBoundIntrinsic(name, allowMissing) {\n  var intrinsic = GetIntrinsic(name, !!allowMissing);\n\n  if (typeof intrinsic === 'function' && $indexOf(name, '.prototype.') > -1) {\n    return callBind(intrinsic);\n  }\n\n  return intrinsic;\n};", "map": {"version": 3, "names": ["GetIntrinsic", "require", "callBind", "$indexOf", "module", "exports", "callBoundIntrinsic", "name", "allowMissing", "intrinsic"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/call-bind/callBound.js"], "sourcesContent": ["'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\n\nvar callBind = require('./');\n\nvar $indexOf = callBind(GetIntrinsic('String.prototype.indexOf'));\n\nmodule.exports = function callBoundIntrinsic(name, allowMissing) {\n\tvar intrinsic = GetIntrinsic(name, !!allowMissing);\n\tif (typeof intrinsic === 'function' && $indexOf(name, '.prototype.') > -1) {\n\t\treturn callBind(intrinsic);\n\t}\n\treturn intrinsic;\n};\n"], "mappings": "AAAA;;AAEA,IAAIA,YAAY,GAAGC,OAAO,CAAC,eAAD,CAA1B;;AAEA,IAAIC,QAAQ,GAAGD,OAAO,CAAC,IAAD,CAAtB;;AAEA,IAAIE,QAAQ,GAAGD,QAAQ,CAACF,YAAY,CAAC,0BAAD,CAAb,CAAvB;;AAEAI,MAAM,CAACC,OAAP,GAAiB,SAASC,kBAAT,CAA4BC,IAA5B,EAAkCC,YAAlC,EAAgD;EAChE,IAAIC,SAAS,GAAGT,YAAY,CAACO,IAAD,EAAO,CAAC,CAACC,YAAT,CAA5B;;EACA,IAAI,OAAOC,SAAP,KAAqB,UAArB,IAAmCN,QAAQ,CAACI,IAAD,EAAO,aAAP,CAAR,GAAgC,CAAC,CAAxE,EAA2E;IAC1E,OAAOL,QAAQ,CAACO,SAAD,CAAf;EACA;;EACD,OAAOA,SAAP;AACA,CAND"}, "metadata": {}, "sourceType": "script"}