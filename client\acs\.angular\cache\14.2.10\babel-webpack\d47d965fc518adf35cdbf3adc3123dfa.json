{"ast": null, "code": "import { map } from './map';\nimport { from } from '../observable/from';\nimport { SimpleOuterSubscriber, SimpleInnerSubscriber, innerSubscribe } from '../innerSubscribe';\nexport function exhaustMap(project, resultSelector) {\n  if (resultSelector) {\n    return source => source.pipe(exhaustMap((a, i) => from(project(a, i)).pipe(map((b, ii) => resultSelector(a, b, i, ii)))));\n  }\n\n  return source => source.lift(new ExhaustMapOperator(project));\n}\n\nclass ExhaustMapOperator {\n  constructor(project) {\n    this.project = project;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new ExhaustMapSubscriber(subscriber, this.project));\n  }\n\n}\n\nclass ExhaustMapSubscriber extends SimpleOuterSubscriber {\n  constructor(destination, project) {\n    super(destination);\n    this.project = project;\n    this.hasSubscription = false;\n    this.hasCompleted = false;\n    this.index = 0;\n  }\n\n  _next(value) {\n    if (!this.hasSubscription) {\n      this.tryNext(value);\n    }\n  }\n\n  tryNext(value) {\n    let result;\n    const index = this.index++;\n\n    try {\n      result = this.project(value, index);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n\n    this.hasSubscription = true;\n\n    this._innerSub(result);\n  }\n\n  _innerSub(result) {\n    const innerSubscriber = new SimpleInnerSubscriber(this);\n    const destination = this.destination;\n    destination.add(innerSubscriber);\n    const innerSubscription = innerSubscribe(result, innerSubscriber);\n\n    if (innerSubscription !== innerSubscriber) {\n      destination.add(innerSubscription);\n    }\n  }\n\n  _complete() {\n    this.hasCompleted = true;\n\n    if (!this.hasSubscription) {\n      this.destination.complete();\n    }\n\n    this.unsubscribe();\n  }\n\n  notifyNext(innerValue) {\n    this.destination.next(innerValue);\n  }\n\n  notifyError(err) {\n    this.destination.error(err);\n  }\n\n  notifyComplete() {\n    this.hasSubscription = false;\n\n    if (this.hasCompleted) {\n      this.destination.complete();\n    }\n  }\n\n}", "map": {"version": 3, "names": ["map", "from", "SimpleOuterSubscriber", "SimpleInnerSubscriber", "innerSubscribe", "exhaustMap", "project", "resultSelector", "source", "pipe", "a", "i", "b", "ii", "lift", "ExhaustMapOperator", "constructor", "call", "subscriber", "subscribe", "ExhaustMapSubscriber", "destination", "hasSubscription", "hasCompleted", "index", "_next", "value", "tryNext", "result", "err", "error", "_innerSub", "innerSubscriber", "add", "innerSubscription", "_complete", "complete", "unsubscribe", "notifyNext", "innerValue", "next", "notifyError", "notifyComplete"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/exhaustMap.js"], "sourcesContent": ["import { map } from './map';\nimport { from } from '../observable/from';\nimport { SimpleOuterSubscriber, SimpleInnerSubscriber, innerSubscribe } from '../innerSubscribe';\nexport function exhaustMap(project, resultSelector) {\n    if (resultSelector) {\n        return (source) => source.pipe(exhaustMap((a, i) => from(project(a, i)).pipe(map((b, ii) => resultSelector(a, b, i, ii)))));\n    }\n    return (source) => source.lift(new ExhaustMapOperator(project));\n}\nclass ExhaustMapOperator {\n    constructor(project) {\n        this.project = project;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new ExhaustMapSubscriber(subscriber, this.project));\n    }\n}\nclass ExhaustMapSubscriber extends SimpleOuterSubscriber {\n    constructor(destination, project) {\n        super(destination);\n        this.project = project;\n        this.hasSubscription = false;\n        this.hasCompleted = false;\n        this.index = 0;\n    }\n    _next(value) {\n        if (!this.hasSubscription) {\n            this.tryNext(value);\n        }\n    }\n    tryNext(value) {\n        let result;\n        const index = this.index++;\n        try {\n            result = this.project(value, index);\n        }\n        catch (err) {\n            this.destination.error(err);\n            return;\n        }\n        this.hasSubscription = true;\n        this._innerSub(result);\n    }\n    _innerSub(result) {\n        const innerSubscriber = new SimpleInnerSubscriber(this);\n        const destination = this.destination;\n        destination.add(innerSubscriber);\n        const innerSubscription = innerSubscribe(result, innerSubscriber);\n        if (innerSubscription !== innerSubscriber) {\n            destination.add(innerSubscription);\n        }\n    }\n    _complete() {\n        this.hasCompleted = true;\n        if (!this.hasSubscription) {\n            this.destination.complete();\n        }\n        this.unsubscribe();\n    }\n    notifyNext(innerValue) {\n        this.destination.next(innerValue);\n    }\n    notifyError(err) {\n        this.destination.error(err);\n    }\n    notifyComplete() {\n        this.hasSubscription = false;\n        if (this.hasCompleted) {\n            this.destination.complete();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,GAAT,QAAoB,OAApB;AACA,SAASC,IAAT,QAAqB,oBAArB;AACA,SAASC,qBAAT,EAAgCC,qBAAhC,EAAuDC,cAAvD,QAA6E,mBAA7E;AACA,OAAO,SAASC,UAAT,CAAoBC,OAApB,EAA6BC,cAA7B,EAA6C;EAChD,IAAIA,cAAJ,EAAoB;IAChB,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAYJ,UAAU,CAAC,CAACK,CAAD,EAAIC,CAAJ,KAAUV,IAAI,CAACK,OAAO,CAACI,CAAD,EAAIC,CAAJ,CAAR,CAAJ,CAAoBF,IAApB,CAAyBT,GAAG,CAAC,CAACY,CAAD,EAAIC,EAAJ,KAAWN,cAAc,CAACG,CAAD,EAAIE,CAAJ,EAAOD,CAAP,EAAUE,EAAV,CAA1B,CAA5B,CAAX,CAAtB,CAAnB;EACH;;EACD,OAAQL,MAAD,IAAYA,MAAM,CAACM,IAAP,CAAY,IAAIC,kBAAJ,CAAuBT,OAAvB,CAAZ,CAAnB;AACH;;AACD,MAAMS,kBAAN,CAAyB;EACrBC,WAAW,CAACV,OAAD,EAAU;IACjB,KAAKA,OAAL,GAAeA,OAAf;EACH;;EACDW,IAAI,CAACC,UAAD,EAAaV,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACW,SAAP,CAAiB,IAAIC,oBAAJ,CAAyBF,UAAzB,EAAqC,KAAKZ,OAA1C,CAAjB,CAAP;EACH;;AANoB;;AAQzB,MAAMc,oBAAN,SAAmClB,qBAAnC,CAAyD;EACrDc,WAAW,CAACK,WAAD,EAAcf,OAAd,EAAuB;IAC9B,MAAMe,WAAN;IACA,KAAKf,OAAL,GAAeA,OAAf;IACA,KAAKgB,eAAL,GAAuB,KAAvB;IACA,KAAKC,YAAL,GAAoB,KAApB;IACA,KAAKC,KAAL,GAAa,CAAb;EACH;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACT,IAAI,CAAC,KAAKJ,eAAV,EAA2B;MACvB,KAAKK,OAAL,CAAaD,KAAb;IACH;EACJ;;EACDC,OAAO,CAACD,KAAD,EAAQ;IACX,IAAIE,MAAJ;IACA,MAAMJ,KAAK,GAAG,KAAKA,KAAL,EAAd;;IACA,IAAI;MACAI,MAAM,GAAG,KAAKtB,OAAL,CAAaoB,KAAb,EAAoBF,KAApB,CAAT;IACH,CAFD,CAGA,OAAOK,GAAP,EAAY;MACR,KAAKR,WAAL,CAAiBS,KAAjB,CAAuBD,GAAvB;MACA;IACH;;IACD,KAAKP,eAAL,GAAuB,IAAvB;;IACA,KAAKS,SAAL,CAAeH,MAAf;EACH;;EACDG,SAAS,CAACH,MAAD,EAAS;IACd,MAAMI,eAAe,GAAG,IAAI7B,qBAAJ,CAA0B,IAA1B,CAAxB;IACA,MAAMkB,WAAW,GAAG,KAAKA,WAAzB;IACAA,WAAW,CAACY,GAAZ,CAAgBD,eAAhB;IACA,MAAME,iBAAiB,GAAG9B,cAAc,CAACwB,MAAD,EAASI,eAAT,CAAxC;;IACA,IAAIE,iBAAiB,KAAKF,eAA1B,EAA2C;MACvCX,WAAW,CAACY,GAAZ,CAAgBC,iBAAhB;IACH;EACJ;;EACDC,SAAS,GAAG;IACR,KAAKZ,YAAL,GAAoB,IAApB;;IACA,IAAI,CAAC,KAAKD,eAAV,EAA2B;MACvB,KAAKD,WAAL,CAAiBe,QAAjB;IACH;;IACD,KAAKC,WAAL;EACH;;EACDC,UAAU,CAACC,UAAD,EAAa;IACnB,KAAKlB,WAAL,CAAiBmB,IAAjB,CAAsBD,UAAtB;EACH;;EACDE,WAAW,CAACZ,GAAD,EAAM;IACb,KAAKR,WAAL,CAAiBS,KAAjB,CAAuBD,GAAvB;EACH;;EACDa,cAAc,GAAG;IACb,KAAKpB,eAAL,GAAuB,KAAvB;;IACA,IAAI,KAAKC,YAAT,EAAuB;MACnB,KAAKF,WAAL,CAAiBe,QAAjB;IACH;EACJ;;AArDoD"}, "metadata": {}, "sourceType": "module"}