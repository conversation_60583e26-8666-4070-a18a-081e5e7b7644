{"ast": null, "code": "import { Observable } from '../Observable';\nimport { isArray } from '../util/isArray';\nimport { map } from '../operators/map';\nimport { isObject } from '../util/isObject';\nimport { from } from './from';\nexport function forkJoin(...sources) {\n  if (sources.length === 1) {\n    const first = sources[0];\n\n    if (isArray(first)) {\n      return forkJoinInternal(first, null);\n    }\n\n    if (isObject(first) && Object.getPrototypeOf(first) === Object.prototype) {\n      const keys = Object.keys(first);\n      return forkJoinInternal(keys.map(key => first[key]), keys);\n    }\n  }\n\n  if (typeof sources[sources.length - 1] === 'function') {\n    const resultSelector = sources.pop();\n    sources = sources.length === 1 && isArray(sources[0]) ? sources[0] : sources;\n    return forkJoinInternal(sources, null).pipe(map(args => resultSelector(...args)));\n  }\n\n  return forkJoinInternal(sources, null);\n}\n\nfunction forkJoinInternal(sources, keys) {\n  return new Observable(subscriber => {\n    const len = sources.length;\n\n    if (len === 0) {\n      subscriber.complete();\n      return;\n    }\n\n    const values = new Array(len);\n    let completed = 0;\n    let emitted = 0;\n\n    for (let i = 0; i < len; i++) {\n      const source = from(sources[i]);\n      let hasValue = false;\n      subscriber.add(source.subscribe({\n        next: value => {\n          if (!hasValue) {\n            hasValue = true;\n            emitted++;\n          }\n\n          values[i] = value;\n        },\n        error: err => subscriber.error(err),\n        complete: () => {\n          completed++;\n\n          if (completed === len || !hasValue) {\n            if (emitted === len) {\n              subscriber.next(keys ? keys.reduce((result, key, i) => (result[key] = values[i], result), {}) : values);\n            }\n\n            subscriber.complete();\n          }\n        }\n      }));\n    }\n  });\n}", "map": {"version": 3, "names": ["Observable", "isArray", "map", "isObject", "from", "fork<PERSON><PERSON>n", "sources", "length", "first", "forkJoinInternal", "Object", "getPrototypeOf", "prototype", "keys", "key", "resultSelector", "pop", "pipe", "args", "subscriber", "len", "complete", "values", "Array", "completed", "emitted", "i", "source", "hasValue", "add", "subscribe", "next", "value", "error", "err", "reduce", "result"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/observable/forkJoin.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { isArray } from '../util/isArray';\nimport { map } from '../operators/map';\nimport { isObject } from '../util/isObject';\nimport { from } from './from';\nexport function forkJoin(...sources) {\n    if (sources.length === 1) {\n        const first = sources[0];\n        if (isArray(first)) {\n            return forkJoinInternal(first, null);\n        }\n        if (isObject(first) && Object.getPrototypeOf(first) === Object.prototype) {\n            const keys = Object.keys(first);\n            return forkJoinInternal(keys.map(key => first[key]), keys);\n        }\n    }\n    if (typeof sources[sources.length - 1] === 'function') {\n        const resultSelector = sources.pop();\n        sources = (sources.length === 1 && isArray(sources[0])) ? sources[0] : sources;\n        return forkJoinInternal(sources, null).pipe(map((args) => resultSelector(...args)));\n    }\n    return forkJoinInternal(sources, null);\n}\nfunction forkJoinInternal(sources, keys) {\n    return new Observable(subscriber => {\n        const len = sources.length;\n        if (len === 0) {\n            subscriber.complete();\n            return;\n        }\n        const values = new Array(len);\n        let completed = 0;\n        let emitted = 0;\n        for (let i = 0; i < len; i++) {\n            const source = from(sources[i]);\n            let hasValue = false;\n            subscriber.add(source.subscribe({\n                next: value => {\n                    if (!hasValue) {\n                        hasValue = true;\n                        emitted++;\n                    }\n                    values[i] = value;\n                },\n                error: err => subscriber.error(err),\n                complete: () => {\n                    completed++;\n                    if (completed === len || !hasValue) {\n                        if (emitted === len) {\n                            subscriber.next(keys ?\n                                keys.reduce((result, key, i) => (result[key] = values[i], result), {}) :\n                                values);\n                        }\n                        subscriber.complete();\n                    }\n                }\n            }));\n        }\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,OAAT,QAAwB,iBAAxB;AACA,SAASC,GAAT,QAAoB,kBAApB;AACA,SAASC,QAAT,QAAyB,kBAAzB;AACA,SAASC,IAAT,QAAqB,QAArB;AACA,OAAO,SAASC,QAAT,CAAkB,GAAGC,OAArB,EAA8B;EACjC,IAAIA,OAAO,CAACC,MAAR,KAAmB,CAAvB,EAA0B;IACtB,MAAMC,KAAK,GAAGF,OAAO,CAAC,CAAD,CAArB;;IACA,IAAIL,OAAO,CAACO,KAAD,CAAX,EAAoB;MAChB,OAAOC,gBAAgB,CAACD,KAAD,EAAQ,IAAR,CAAvB;IACH;;IACD,IAAIL,QAAQ,CAACK,KAAD,CAAR,IAAmBE,MAAM,CAACC,cAAP,CAAsBH,KAAtB,MAAiCE,MAAM,CAACE,SAA/D,EAA0E;MACtE,MAAMC,IAAI,GAAGH,MAAM,CAACG,IAAP,CAAYL,KAAZ,CAAb;MACA,OAAOC,gBAAgB,CAACI,IAAI,CAACX,GAAL,CAASY,GAAG,IAAIN,KAAK,CAACM,GAAD,CAArB,CAAD,EAA8BD,IAA9B,CAAvB;IACH;EACJ;;EACD,IAAI,OAAOP,OAAO,CAACA,OAAO,CAACC,MAAR,GAAiB,CAAlB,CAAd,KAAuC,UAA3C,EAAuD;IACnD,MAAMQ,cAAc,GAAGT,OAAO,CAACU,GAAR,EAAvB;IACAV,OAAO,GAAIA,OAAO,CAACC,MAAR,KAAmB,CAAnB,IAAwBN,OAAO,CAACK,OAAO,CAAC,CAAD,CAAR,CAAhC,GAAgDA,OAAO,CAAC,CAAD,CAAvD,GAA6DA,OAAvE;IACA,OAAOG,gBAAgB,CAACH,OAAD,EAAU,IAAV,CAAhB,CAAgCW,IAAhC,CAAqCf,GAAG,CAAEgB,IAAD,IAAUH,cAAc,CAAC,GAAGG,IAAJ,CAAzB,CAAxC,CAAP;EACH;;EACD,OAAOT,gBAAgB,CAACH,OAAD,EAAU,IAAV,CAAvB;AACH;;AACD,SAASG,gBAAT,CAA0BH,OAA1B,EAAmCO,IAAnC,EAAyC;EACrC,OAAO,IAAIb,UAAJ,CAAemB,UAAU,IAAI;IAChC,MAAMC,GAAG,GAAGd,OAAO,CAACC,MAApB;;IACA,IAAIa,GAAG,KAAK,CAAZ,EAAe;MACXD,UAAU,CAACE,QAAX;MACA;IACH;;IACD,MAAMC,MAAM,GAAG,IAAIC,KAAJ,CAAUH,GAAV,CAAf;IACA,IAAII,SAAS,GAAG,CAAhB;IACA,IAAIC,OAAO,GAAG,CAAd;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGN,GAApB,EAAyBM,CAAC,EAA1B,EAA8B;MAC1B,MAAMC,MAAM,GAAGvB,IAAI,CAACE,OAAO,CAACoB,CAAD,CAAR,CAAnB;MACA,IAAIE,QAAQ,GAAG,KAAf;MACAT,UAAU,CAACU,GAAX,CAAeF,MAAM,CAACG,SAAP,CAAiB;QAC5BC,IAAI,EAAEC,KAAK,IAAI;UACX,IAAI,CAACJ,QAAL,EAAe;YACXA,QAAQ,GAAG,IAAX;YACAH,OAAO;UACV;;UACDH,MAAM,CAACI,CAAD,CAAN,GAAYM,KAAZ;QACH,CAP2B;QAQ5BC,KAAK,EAAEC,GAAG,IAAIf,UAAU,CAACc,KAAX,CAAiBC,GAAjB,CARc;QAS5Bb,QAAQ,EAAE,MAAM;UACZG,SAAS;;UACT,IAAIA,SAAS,KAAKJ,GAAd,IAAqB,CAACQ,QAA1B,EAAoC;YAChC,IAAIH,OAAO,KAAKL,GAAhB,EAAqB;cACjBD,UAAU,CAACY,IAAX,CAAgBlB,IAAI,GAChBA,IAAI,CAACsB,MAAL,CAAY,CAACC,MAAD,EAAStB,GAAT,EAAcY,CAAd,MAAqBU,MAAM,CAACtB,GAAD,CAAN,GAAcQ,MAAM,CAACI,CAAD,CAApB,EAAyBU,MAA9C,CAAZ,EAAmE,EAAnE,CADgB,GAEhBd,MAFJ;YAGH;;YACDH,UAAU,CAACE,QAAX;UACH;QACJ;MAnB2B,CAAjB,CAAf;IAqBH;EACJ,CAlCM,CAAP;AAmCH"}, "metadata": {}, "sourceType": "module"}