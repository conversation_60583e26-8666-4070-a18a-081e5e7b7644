{"ast": null, "code": ";\n\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * Counter block mode.\n   */\n  CryptoJS.mode.CTR = function () {\n    var CTR = CryptoJS.lib.BlockCipherMode.extend();\n    var Encryptor = CTR.Encryptor = CTR.extend({\n      processBlock: function (words, offset) {\n        // Shortcuts\n        var cipher = this._cipher;\n        var blockSize = cipher.blockSize;\n        var iv = this._iv;\n        var counter = this._counter; // Generate keystream\n\n        if (iv) {\n          counter = this._counter = iv.slice(0); // Remove IV for subsequent blocks\n\n          this._iv = undefined;\n        }\n\n        var keystream = counter.slice(0);\n        cipher.encryptBlock(keystream, 0); // Increment counter\n\n        counter[blockSize - 1] = counter[blockSize - 1] + 1 | 0; // Encrypt\n\n        for (var i = 0; i < blockSize; i++) {\n          words[offset + i] ^= keystream[i];\n        }\n      }\n    });\n    CTR.Decryptor = Encryptor;\n    return CTR;\n  }();\n\n  return CryptoJS.mode.CTR;\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "mode", "CTR", "lib", "BlockCipherMode", "extend", "Encryptor", "processBlock", "words", "offset", "cipher", "_cipher", "blockSize", "iv", "_iv", "counter", "_counter", "slice", "undefined", "keystream", "encryptBlock", "i", "Decryptor"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/crypto-js/mode-ctr.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./cipher-core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t/**\n\t * Counter block mode.\n\t */\n\tCryptoJS.mode.CTR = (function () {\n\t    var CTR = CryptoJS.lib.BlockCipherMode.extend();\n\n\t    var Encryptor = CTR.Encryptor = CTR.extend({\n\t        processBlock: function (words, offset) {\n\t            // Shortcuts\n\t            var cipher = this._cipher\n\t            var blockSize = cipher.blockSize;\n\t            var iv = this._iv;\n\t            var counter = this._counter;\n\n\t            // Generate keystream\n\t            if (iv) {\n\t                counter = this._counter = iv.slice(0);\n\n\t                // Remove IV for subsequent blocks\n\t                this._iv = undefined;\n\t            }\n\t            var keystream = counter.slice(0);\n\t            cipher.encryptBlock(keystream, 0);\n\n\t            // Increment counter\n\t            counter[blockSize - 1] = (counter[blockSize - 1] + 1) | 0\n\n\t            // Encrypt\n\t            for (var i = 0; i < blockSize; i++) {\n\t                words[offset + i] ^= keystream[i];\n\t            }\n\t        }\n\t    });\n\n\t    CTR.Decryptor = Encryptor;\n\n\t    return CTR;\n\t}());\n\n\n\treturn CryptoJS.mode.CTR;\n\n}));"], "mappings": "AAAA;;AAAE,WAAUA,IAAV,EAAgBC,OAAhB,EAAyBC,KAAzB,EAAgC;EACjC,IAAI,OAAOC,OAAP,KAAmB,QAAvB,EAAiC;IAChC;IACAC,MAAM,CAACD,OAAP,GAAiBA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAD,CAAR,EAAoBA,OAAO,CAAC,eAAD,CAA3B,CAAlC;EACA,CAHD,MAIK,IAAI,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAA3C,EAAgD;IACpD;IACAD,MAAM,CAAC,CAAC,QAAD,EAAW,eAAX,CAAD,EAA8BL,OAA9B,CAAN;EACA,CAHI,MAIA;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAN,CAAP;EACA;AACD,CAbC,EAaA,IAbA,EAaM,UAAUA,QAAV,EAAoB;EAE3B;AACD;AACA;EACCA,QAAQ,CAACC,IAAT,CAAcC,GAAd,GAAqB,YAAY;IAC7B,IAAIA,GAAG,GAAGF,QAAQ,CAACG,GAAT,CAAaC,eAAb,CAA6BC,MAA7B,EAAV;IAEA,IAAIC,SAAS,GAAGJ,GAAG,CAACI,SAAJ,GAAgBJ,GAAG,CAACG,MAAJ,CAAW;MACvCE,YAAY,EAAE,UAAUC,KAAV,EAAiBC,MAAjB,EAAyB;QACnC;QACA,IAAIC,MAAM,GAAG,KAAKC,OAAlB;QACA,IAAIC,SAAS,GAAGF,MAAM,CAACE,SAAvB;QACA,IAAIC,EAAE,GAAG,KAAKC,GAAd;QACA,IAAIC,OAAO,GAAG,KAAKC,QAAnB,CALmC,CAOnC;;QACA,IAAIH,EAAJ,EAAQ;UACJE,OAAO,GAAG,KAAKC,QAAL,GAAgBH,EAAE,CAACI,KAAH,CAAS,CAAT,CAA1B,CADI,CAGJ;;UACA,KAAKH,GAAL,GAAWI,SAAX;QACH;;QACD,IAAIC,SAAS,GAAGJ,OAAO,CAACE,KAAR,CAAc,CAAd,CAAhB;QACAP,MAAM,CAACU,YAAP,CAAoBD,SAApB,EAA+B,CAA/B,EAfmC,CAiBnC;;QACAJ,OAAO,CAACH,SAAS,GAAG,CAAb,CAAP,GAA0BG,OAAO,CAACH,SAAS,GAAG,CAAb,CAAP,GAAyB,CAA1B,GAA+B,CAAxD,CAlBmC,CAoBnC;;QACA,KAAK,IAAIS,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGT,SAApB,EAA+BS,CAAC,EAAhC,EAAoC;UAChCb,KAAK,CAACC,MAAM,GAAGY,CAAV,CAAL,IAAqBF,SAAS,CAACE,CAAD,CAA9B;QACH;MACJ;IAzBsC,CAAX,CAAhC;IA4BAnB,GAAG,CAACoB,SAAJ,GAAgBhB,SAAhB;IAEA,OAAOJ,GAAP;EACH,CAlCoB,EAArB;;EAqCA,OAAOF,QAAQ,CAACC,IAAT,CAAcC,GAArB;AAEA,CAzDC,CAAD"}, "metadata": {}, "sourceType": "script"}