{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function retry(count = -1) {\n  return source => source.lift(new RetryOperator(count, source));\n}\n\nclass RetryOperator {\n  constructor(count, source) {\n    this.count = count;\n    this.source = source;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new RetrySubscriber(subscriber, this.count, this.source));\n  }\n\n}\n\nclass RetrySubscriber extends Subscriber {\n  constructor(destination, count, source) {\n    super(destination);\n    this.count = count;\n    this.source = source;\n  }\n\n  error(err) {\n    if (!this.isStopped) {\n      const {\n        source,\n        count\n      } = this;\n\n      if (count === 0) {\n        return super.error(err);\n      } else if (count > -1) {\n        this.count = count - 1;\n      }\n\n      source.subscribe(this._unsubscribeAndRecycle());\n    }\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "retry", "count", "source", "lift", "RetryOperator", "constructor", "call", "subscriber", "subscribe", "RetrySubscriber", "destination", "error", "err", "isStopped", "_unsubscribeAndRecycle"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/rxjs/_esm2015/internal/operators/retry.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function retry(count = -1) {\n    return (source) => source.lift(new RetryOperator(count, source));\n}\nclass RetryOperator {\n    constructor(count, source) {\n        this.count = count;\n        this.source = source;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new RetrySubscriber(subscriber, this.count, this.source));\n    }\n}\nclass RetrySubscriber extends Subscriber {\n    constructor(destination, count, source) {\n        super(destination);\n        this.count = count;\n        this.source = source;\n    }\n    error(err) {\n        if (!this.isStopped) {\n            const { source, count } = this;\n            if (count === 0) {\n                return super.error(err);\n            }\n            else if (count > -1) {\n                this.count = count - 1;\n            }\n            source.subscribe(this._unsubscribeAndRecycle());\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,KAAT,CAAeC,KAAK,GAAG,CAAC,CAAxB,EAA2B;EAC9B,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,aAAJ,CAAkBH,KAAlB,EAAyBC,MAAzB,CAAZ,CAAnB;AACH;;AACD,MAAME,aAAN,CAAoB;EAChBC,WAAW,CAACJ,KAAD,EAAQC,MAAR,EAAgB;IACvB,KAAKD,KAAL,GAAaA,KAAb;IACA,KAAKC,MAAL,GAAcA,MAAd;EACH;;EACDI,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,eAAJ,CAAoBF,UAApB,EAAgC,KAAKN,KAArC,EAA4C,KAAKC,MAAjD,CAAjB,CAAP;EACH;;AAPe;;AASpB,MAAMO,eAAN,SAA8BV,UAA9B,CAAyC;EACrCM,WAAW,CAACK,WAAD,EAAcT,KAAd,EAAqBC,MAArB,EAA6B;IACpC,MAAMQ,WAAN;IACA,KAAKT,KAAL,GAAaA,KAAb;IACA,KAAKC,MAAL,GAAcA,MAAd;EACH;;EACDS,KAAK,CAACC,GAAD,EAAM;IACP,IAAI,CAAC,KAAKC,SAAV,EAAqB;MACjB,MAAM;QAAEX,MAAF;QAAUD;MAAV,IAAoB,IAA1B;;MACA,IAAIA,KAAK,KAAK,CAAd,EAAiB;QACb,OAAO,MAAMU,KAAN,CAAYC,GAAZ,CAAP;MACH,CAFD,MAGK,IAAIX,KAAK,GAAG,CAAC,CAAb,EAAgB;QACjB,KAAKA,KAAL,GAAaA,KAAK,GAAG,CAArB;MACH;;MACDC,MAAM,CAACM,SAAP,CAAiB,KAAKM,sBAAL,EAAjB;IACH;EACJ;;AAjBoC"}, "metadata": {}, "sourceType": "module"}