{"ast": null, "code": "import * as i1 from '@angular/cdk/scrolling';\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nexport { CdkScrollable, ScrollDispatcher, ViewportRuler } from '@angular/cdk/scrolling';\nimport * as i6 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, Optional, ElementRef, ApplicationRef, ANIMATION_MODULE_TYPE, InjectionToken, Directive, EventEmitter, Input, Output, NgModule } from '@angular/core';\nimport { coerceCssPixelValue, coerceArray, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { supportsScrollBehavior, _getEventTarget, _isTestEnvironment } from '@angular/cdk/platform';\nimport * as i5 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { DomPortalOutlet, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { Subject, Subscription, merge } from 'rxjs';\nimport { take, takeUntil, takeWhile } from 'rxjs/operators';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nconst scrollBehaviorSupported = supportsScrollBehavior();\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\n\nclass BlockScrollStrategy {\n  constructor(_viewportRuler, document) {\n    this._viewportRuler = _viewportRuler;\n    this._previousHTMLStyles = {\n      top: '',\n      left: ''\n    };\n    this._isEnabled = false;\n    this._document = document;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n\n\n  attach() {}\n  /** Blocks page-level scroll while the attached overlay is open. */\n\n\n  enable() {\n    if (this._canBeEnabled()) {\n      const root = this._document.documentElement;\n      this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition(); // Cache the previous inline styles in case the user had set them.\n\n      this._previousHTMLStyles.left = root.style.left || '';\n      this._previousHTMLStyles.top = root.style.top || ''; // Note: we're using the `html` node, instead of the `body`, because the `body` may\n      // have the user agent margin, whereas the `html` is guaranteed not to have one.\n\n      root.style.left = coerceCssPixelValue(-this._previousScrollPosition.left);\n      root.style.top = coerceCssPixelValue(-this._previousScrollPosition.top);\n      root.classList.add('cdk-global-scrollblock');\n      this._isEnabled = true;\n    }\n  }\n  /** Unblocks page-level scroll while the attached overlay is open. */\n\n\n  disable() {\n    if (this._isEnabled) {\n      const html = this._document.documentElement;\n      const body = this._document.body;\n      const htmlStyle = html.style;\n      const bodyStyle = body.style;\n      const previousHtmlScrollBehavior = htmlStyle.scrollBehavior || '';\n      const previousBodyScrollBehavior = bodyStyle.scrollBehavior || '';\n      this._isEnabled = false;\n      htmlStyle.left = this._previousHTMLStyles.left;\n      htmlStyle.top = this._previousHTMLStyles.top;\n      html.classList.remove('cdk-global-scrollblock'); // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n      // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n      // Note that we don't mutate the property if the browser doesn't support `scroll-behavior`,\n      // because it can throw off feature detections in `supportsScrollBehavior` which\n      // checks for `'scrollBehavior' in documentElement.style`.\n\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = bodyStyle.scrollBehavior = 'auto';\n      }\n\n      window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = previousHtmlScrollBehavior;\n        bodyStyle.scrollBehavior = previousBodyScrollBehavior;\n      }\n    }\n  }\n\n  _canBeEnabled() {\n    // Since the scroll strategies can't be singletons, we have to use a global CSS class\n    // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n    // scrolling multiple times.\n    const html = this._document.documentElement;\n\n    if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n      return false;\n    }\n\n    const body = this._document.body;\n\n    const viewport = this._viewportRuler.getViewportSize();\n\n    return body.scrollHeight > viewport.height || body.scrollWidth > viewport.width;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\n\n\nfunction getMatScrollStrategyAlreadyAttachedError() {\n  return Error(`Scroll strategy has already been attached.`);\n}\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\n\n\nclass CloseScrollStrategy {\n  constructor(_scrollDispatcher, _ngZone, _viewportRuler, _config) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._config = _config;\n    this._scrollSubscription = null;\n    /** Detaches the overlay ref and disables the scroll strategy. */\n\n    this._detach = () => {\n      this.disable();\n\n      if (this._overlayRef.hasAttached()) {\n        this._ngZone.run(() => this._overlayRef.detach());\n      }\n    };\n  }\n  /** Attaches this scroll strategy to an overlay. */\n\n\n  attach(overlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n\n    this._overlayRef = overlayRef;\n  }\n  /** Enables the closing of the attached overlay on scroll. */\n\n\n  enable() {\n    if (this._scrollSubscription) {\n      return;\n    }\n\n    const stream = this._scrollDispatcher.scrolled(0);\n\n    if (this._config && this._config.threshold && this._config.threshold > 1) {\n      this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n      this._scrollSubscription = stream.subscribe(() => {\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n\n        if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config.threshold) {\n          this._detach();\n        } else {\n          this._overlayRef.updatePosition();\n        }\n      });\n    } else {\n      this._scrollSubscription = stream.subscribe(this._detach);\n    }\n  }\n  /** Disables the closing the attached overlay on scroll. */\n\n\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n\n      this._scrollSubscription = null;\n    }\n  }\n\n  detach() {\n    this.disable();\n    this._overlayRef = null;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Scroll strategy that doesn't do anything. */\n\n\nclass NoopScrollStrategy {\n  /** Does nothing, as this scroll strategy is a no-op. */\n  enable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n\n\n  disable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n\n\n  attach() {}\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\n\n\nfunction isElementScrolledOutsideView(element, scrollContainers) {\n  return scrollContainers.some(containerBounds => {\n    const outsideAbove = element.bottom < containerBounds.top;\n    const outsideBelow = element.top > containerBounds.bottom;\n    const outsideLeft = element.right < containerBounds.left;\n    const outsideRight = element.left > containerBounds.right;\n    return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n  });\n}\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\n\n\nfunction isElementClippedByScrolling(element, scrollContainers) {\n  return scrollContainers.some(scrollContainerRect => {\n    const clippedAbove = element.top < scrollContainerRect.top;\n    const clippedBelow = element.bottom > scrollContainerRect.bottom;\n    const clippedLeft = element.left < scrollContainerRect.left;\n    const clippedRight = element.right > scrollContainerRect.right;\n    return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n  });\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\n\n\nclass RepositionScrollStrategy {\n  constructor(_scrollDispatcher, _viewportRuler, _ngZone, _config) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewportRuler = _viewportRuler;\n    this._ngZone = _ngZone;\n    this._config = _config;\n    this._scrollSubscription = null;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n\n\n  attach(overlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n\n    this._overlayRef = overlayRef;\n  }\n  /** Enables repositioning of the attached overlay on scroll. */\n\n\n  enable() {\n    if (!this._scrollSubscription) {\n      const throttle = this._config ? this._config.scrollThrottle : 0;\n      this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n        this._overlayRef.updatePosition(); // TODO(crisbeto): make `close` on by default once all components can handle it.\n\n\n        if (this._config && this._config.autoClose) {\n          const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n\n          const {\n            width,\n            height\n          } = this._viewportRuler.getViewportSize(); // TODO(crisbeto): include all ancestor scroll containers here once\n          // we have a way of exposing the trigger element to the scroll strategy.\n\n\n          const parentRects = [{\n            width,\n            height,\n            bottom: height,\n            right: width,\n            top: 0,\n            left: 0\n          }];\n\n          if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n            this.disable();\n\n            this._ngZone.run(() => this._overlayRef.detach());\n          }\n        }\n      });\n    }\n  }\n  /** Disables repositioning of the attached overlay on scroll. */\n\n\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n\n      this._scrollSubscription = null;\n    }\n  }\n\n  detach() {\n    this.disable();\n    this._overlayRef = null;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\n\n\nclass ScrollStrategyOptions {\n  constructor(_scrollDispatcher, _viewportRuler, _ngZone, document) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewportRuler = _viewportRuler;\n    this._ngZone = _ngZone;\n    /** Do nothing on scroll. */\n\n    this.noop = () => new NoopScrollStrategy();\n    /**\n     * Close the overlay as soon as the user scrolls.\n     * @param config Configuration to be used inside the scroll strategy.\n     */\n\n\n    this.close = config => new CloseScrollStrategy(this._scrollDispatcher, this._ngZone, this._viewportRuler, config);\n    /** Block scrolling. */\n\n\n    this.block = () => new BlockScrollStrategy(this._viewportRuler, this._document);\n    /**\n     * Update the overlay's position on scroll.\n     * @param config Configuration to be used inside the scroll strategy.\n     * Allows debouncing the reposition calls.\n     */\n\n\n    this.reposition = config => new RepositionScrollStrategy(this._scrollDispatcher, this._viewportRuler, this._ngZone, config);\n\n    this._document = document;\n  }\n\n}\n\nScrollStrategyOptions.ɵfac = function ScrollStrategyOptions_Factory(t) {\n  return new (t || ScrollStrategyOptions)(i0.ɵɵinject(i1.ScrollDispatcher), i0.ɵɵinject(i1.ViewportRuler), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n};\n\nScrollStrategyOptions.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ScrollStrategyOptions,\n  factory: ScrollStrategyOptions.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollStrategyOptions, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.ScrollDispatcher\n    }, {\n      type: i1.ViewportRuler\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Initial configuration used when creating an overlay. */\n\n\nclass OverlayConfig {\n  constructor(config) {\n    /** Strategy to be used when handling scroll events while the overlay is open. */\n    this.scrollStrategy = new NoopScrollStrategy();\n    /** Custom class to add to the overlay pane. */\n\n    this.panelClass = '';\n    /** Whether the overlay has a backdrop. */\n\n    this.hasBackdrop = false;\n    /** Custom class to add to the backdrop */\n\n    this.backdropClass = 'cdk-overlay-dark-backdrop';\n    /**\n     * Whether the overlay should be disposed of when the user goes backwards/forwards in history.\n     * Note that this usually doesn't include clicking on links (unless the user is using\n     * the `HashLocationStrategy`).\n     */\n\n    this.disposeOnNavigation = false;\n\n    if (config) {\n      // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n      // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n      // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n      const configKeys = Object.keys(config);\n\n      for (const key of configKeys) {\n        if (config[key] !== undefined) {\n          // TypeScript, as of version 3.5, sees the left-hand-side of this expression\n          // as \"I don't know *which* key this is, so the only valid value is the intersection\n          // of all the possible values.\" In this case, that happens to be `undefined`. TypeScript\n          // is not smart enough to see that the right-hand-side is actually an access of the same\n          // exact type with the same exact key, meaning that the value type must be identical.\n          // So we use `any` to work around this.\n          this[key] = config[key];\n        }\n      }\n    }\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** The points of the origin element and the overlay element to connect. */\n\n\nclass ConnectionPositionPair {\n  constructor(origin, overlay,\n  /** Offset along the X axis. */\n  offsetX,\n  /** Offset along the Y axis. */\n  offsetY,\n  /** Class(es) to be applied to the panel while this position is active. */\n  panelClass) {\n    this.offsetX = offsetX;\n    this.offsetY = offsetY;\n    this.panelClass = panelClass;\n    this.originX = origin.originX;\n    this.originY = origin.originY;\n    this.overlayX = overlay.overlayX;\n    this.overlayY = overlay.overlayY;\n  }\n\n}\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\n\n\nclass ScrollingVisibility {}\n/** The change event emitted by the strategy when a fallback position is used. */\n\n\nclass ConnectedOverlayPositionChange {\n  constructor(\n  /** The position used as a result of this change. */\n  connectionPair,\n  /** @docs-private */\n  scrollableViewProperties) {\n    this.connectionPair = connectionPair;\n    this.scrollableViewProperties = scrollableViewProperties;\n  }\n\n}\n/**\n * Validates whether a vertical position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\n\n\nfunction validateVerticalPosition(property, value) {\n  if (value !== 'top' && value !== 'bottom' && value !== 'center') {\n    throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` + `Expected \"top\", \"bottom\" or \"center\".`);\n  }\n}\n/**\n * Validates whether a horizontal position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\n\n\nfunction validateHorizontalPosition(property, value) {\n  if (value !== 'start' && value !== 'end' && value !== 'center') {\n    throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` + `Expected \"start\", \"end\" or \"center\".`);\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Service for dispatching events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\n\n\nclass BaseOverlayDispatcher {\n  constructor(document) {\n    /** Currently attached overlays in the order they were attached. */\n    this._attachedOverlays = [];\n    this._document = document;\n  }\n\n  ngOnDestroy() {\n    this.detach();\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n\n\n  add(overlayRef) {\n    // Ensure that we don't get the same overlay multiple times.\n    this.remove(overlayRef);\n\n    this._attachedOverlays.push(overlayRef);\n  }\n  /** Remove an overlay from the list of attached overlay refs. */\n\n\n  remove(overlayRef) {\n    const index = this._attachedOverlays.indexOf(overlayRef);\n\n    if (index > -1) {\n      this._attachedOverlays.splice(index, 1);\n    } // Remove the global listener once there are no more overlays.\n\n\n    if (this._attachedOverlays.length === 0) {\n      this.detach();\n    }\n  }\n\n}\n\nBaseOverlayDispatcher.ɵfac = function BaseOverlayDispatcher_Factory(t) {\n  return new (t || BaseOverlayDispatcher)(i0.ɵɵinject(DOCUMENT));\n};\n\nBaseOverlayDispatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: BaseOverlayDispatcher,\n  factory: BaseOverlayDispatcher.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseOverlayDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\n\n\nclass OverlayKeyboardDispatcher extends BaseOverlayDispatcher {\n  constructor(document,\n  /** @breaking-change 14.0.0 _ngZone will be required. */\n  _ngZone) {\n    super(document);\n    this._ngZone = _ngZone;\n    /** Keyboard event listener that will be attached to the body. */\n\n    this._keydownListener = event => {\n      const overlays = this._attachedOverlays;\n\n      for (let i = overlays.length - 1; i > -1; i--) {\n        // Dispatch the keydown event to the top overlay which has subscribers to its keydown events.\n        // We want to target the most recent overlay, rather than trying to match where the event came\n        // from, because some components might open an overlay, but keep focus on a trigger element\n        // (e.g. for select and autocomplete). We skip overlays without keydown event subscriptions,\n        // because we don't want overlays that don't handle keyboard events to block the ones below\n        // them that do.\n        if (overlays[i]._keydownEvents.observers.length > 0) {\n          const keydownEvents = overlays[i]._keydownEvents;\n          /** @breaking-change 14.0.0 _ngZone will be required. */\n\n          if (this._ngZone) {\n            this._ngZone.run(() => keydownEvents.next(event));\n          } else {\n            keydownEvents.next(event);\n          }\n\n          break;\n        }\n      }\n    };\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n\n\n  add(overlayRef) {\n    super.add(overlayRef); // Lazily start dispatcher once first overlay is added\n\n    if (!this._isAttached) {\n      /** @breaking-change 14.0.0 _ngZone will be required. */\n      if (this._ngZone) {\n        this._ngZone.runOutsideAngular(() => this._document.body.addEventListener('keydown', this._keydownListener));\n      } else {\n        this._document.body.addEventListener('keydown', this._keydownListener);\n      }\n\n      this._isAttached = true;\n    }\n  }\n  /** Detaches the global keyboard event listener. */\n\n\n  detach() {\n    if (this._isAttached) {\n      this._document.body.removeEventListener('keydown', this._keydownListener);\n\n      this._isAttached = false;\n    }\n  }\n\n}\n\nOverlayKeyboardDispatcher.ɵfac = function OverlayKeyboardDispatcher_Factory(t) {\n  return new (t || OverlayKeyboardDispatcher)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i0.NgZone, 8));\n};\n\nOverlayKeyboardDispatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: OverlayKeyboardDispatcher,\n  factory: OverlayKeyboardDispatcher.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayKeyboardDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i0.NgZone,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Service for dispatching mouse click events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\n\n\nclass OverlayOutsideClickDispatcher extends BaseOverlayDispatcher {\n  constructor(document, _platform,\n  /** @breaking-change 14.0.0 _ngZone will be required. */\n  _ngZone) {\n    super(document);\n    this._platform = _platform;\n    this._ngZone = _ngZone;\n    this._cursorStyleIsSet = false;\n    /** Store pointerdown event target to track origin of click. */\n\n    this._pointerDownListener = event => {\n      this._pointerDownEventTarget = _getEventTarget(event);\n    };\n    /** Click event listener that will be attached to the body propagate phase. */\n\n\n    this._clickListener = event => {\n      const target = _getEventTarget(event); // In case of a click event, we want to check the origin of the click\n      // (e.g. in case where a user starts a click inside the overlay and\n      // releases the click outside of it).\n      // This is done by using the event target of the preceding pointerdown event.\n      // Every click event caused by a pointer device has a preceding pointerdown\n      // event, unless the click was programmatically triggered (e.g. in a unit test).\n\n\n      const origin = event.type === 'click' && this._pointerDownEventTarget ? this._pointerDownEventTarget : target; // Reset the stored pointerdown event target, to avoid having it interfere\n      // in subsequent events.\n\n      this._pointerDownEventTarget = null; // We copy the array because the original may be modified asynchronously if the\n      // outsidePointerEvents listener decides to detach overlays resulting in index errors inside\n      // the for loop.\n\n      const overlays = this._attachedOverlays.slice(); // Dispatch the mouse event to the top overlay which has subscribers to its mouse events.\n      // We want to target all overlays for which the click could be considered as outside click.\n      // As soon as we reach an overlay for which the click is not outside click we break off\n      // the loop.\n\n\n      for (let i = overlays.length - 1; i > -1; i--) {\n        const overlayRef = overlays[i];\n\n        if (overlayRef._outsidePointerEvents.observers.length < 1 || !overlayRef.hasAttached()) {\n          continue;\n        } // If it's a click inside the overlay, just break - we should do nothing\n        // If it's an outside click (both origin and target of the click) dispatch the mouse event,\n        // and proceed with the next overlay\n\n\n        if (overlayRef.overlayElement.contains(target) || overlayRef.overlayElement.contains(origin)) {\n          break;\n        }\n\n        const outsidePointerEvents = overlayRef._outsidePointerEvents;\n        /** @breaking-change 14.0.0 _ngZone will be required. */\n\n        if (this._ngZone) {\n          this._ngZone.run(() => outsidePointerEvents.next(event));\n        } else {\n          outsidePointerEvents.next(event);\n        }\n      }\n    };\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n\n\n  add(overlayRef) {\n    super.add(overlayRef); // Safari on iOS does not generate click events for non-interactive\n    // elements. However, we want to receive a click for any element outside\n    // the overlay. We can force a \"clickable\" state by setting\n    // `cursor: pointer` on the document body. See:\n    // https://developer.mozilla.org/en-US/docs/Web/API/Element/click_event#Safari_Mobile\n    // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n\n    if (!this._isAttached) {\n      const body = this._document.body;\n      /** @breaking-change 14.0.0 _ngZone will be required. */\n\n      if (this._ngZone) {\n        this._ngZone.runOutsideAngular(() => this._addEventListeners(body));\n      } else {\n        this._addEventListeners(body);\n      } // click event is not fired on iOS. To make element \"clickable\" we are\n      // setting the cursor to pointer\n\n\n      if (this._platform.IOS && !this._cursorStyleIsSet) {\n        this._cursorOriginalValue = body.style.cursor;\n        body.style.cursor = 'pointer';\n        this._cursorStyleIsSet = true;\n      }\n\n      this._isAttached = true;\n    }\n  }\n  /** Detaches the global keyboard event listener. */\n\n\n  detach() {\n    if (this._isAttached) {\n      const body = this._document.body;\n      body.removeEventListener('pointerdown', this._pointerDownListener, true);\n      body.removeEventListener('click', this._clickListener, true);\n      body.removeEventListener('auxclick', this._clickListener, true);\n      body.removeEventListener('contextmenu', this._clickListener, true);\n\n      if (this._platform.IOS && this._cursorStyleIsSet) {\n        body.style.cursor = this._cursorOriginalValue;\n        this._cursorStyleIsSet = false;\n      }\n\n      this._isAttached = false;\n    }\n  }\n\n  _addEventListeners(body) {\n    body.addEventListener('pointerdown', this._pointerDownListener, true);\n    body.addEventListener('click', this._clickListener, true);\n    body.addEventListener('auxclick', this._clickListener, true);\n    body.addEventListener('contextmenu', this._clickListener, true);\n  }\n\n}\n\nOverlayOutsideClickDispatcher.ɵfac = function OverlayOutsideClickDispatcher_Factory(t) {\n  return new (t || OverlayOutsideClickDispatcher)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform), i0.ɵɵinject(i0.NgZone, 8));\n};\n\nOverlayOutsideClickDispatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: OverlayOutsideClickDispatcher,\n  factory: OverlayOutsideClickDispatcher.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayOutsideClickDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1$1.Platform\n    }, {\n      type: i0.NgZone,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Container inside which all overlays will render. */\n\n\nclass OverlayContainer {\n  constructor(document, _platform) {\n    this._platform = _platform;\n    this._document = document;\n  }\n\n  ngOnDestroy() {\n    this._containerElement?.remove();\n  }\n  /**\n   * This method returns the overlay container element. It will lazily\n   * create the element the first time it is called to facilitate using\n   * the container in non-browser environments.\n   * @returns the container element\n   */\n\n\n  getContainerElement() {\n    if (!this._containerElement) {\n      this._createContainer();\n    }\n\n    return this._containerElement;\n  }\n  /**\n   * Create the overlay container element, which is simply a div\n   * with the 'cdk-overlay-container' class on the document body.\n   */\n\n\n  _createContainer() {\n    const containerClass = 'cdk-overlay-container'; // TODO(crisbeto): remove the testing check once we have an overlay testing\n    // module or Angular starts tearing down the testing `NgModule`. See:\n    // https://github.com/angular/angular/issues/18831\n\n    if (this._platform.isBrowser || _isTestEnvironment()) {\n      const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`); // Remove any old containers from the opposite platform.\n      // This can happen when transitioning from the server to the client.\n\n\n      for (let i = 0; i < oppositePlatformContainers.length; i++) {\n        oppositePlatformContainers[i].remove();\n      }\n    }\n\n    const container = this._document.createElement('div');\n\n    container.classList.add(containerClass); // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n    // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n    // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n    // To mitigate the problem we made it so that only containers from a different platform are\n    // cleared, but the side-effect was that people started depending on the overly-aggressive\n    // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n    // module which does the cleanup, we try to detect that we're in a test environment and we\n    // always clear the container. See #17006.\n    // TODO(crisbeto): remove the test environment check once we have an overlay testing module.\n\n    if (_isTestEnvironment()) {\n      container.setAttribute('platform', 'test');\n    } else if (!this._platform.isBrowser) {\n      container.setAttribute('platform', 'server');\n    }\n\n    this._document.body.appendChild(container);\n\n    this._containerElement = container;\n  }\n\n}\n\nOverlayContainer.ɵfac = function OverlayContainer_Factory(t) {\n  return new (t || OverlayContainer)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform));\n};\n\nOverlayContainer.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: OverlayContainer,\n  factory: OverlayContainer.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1$1.Platform\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\n\n\nclass OverlayRef {\n  constructor(_portalOutlet, _host, _pane, _config, _ngZone, _keyboardDispatcher, _document, _location, _outsideClickDispatcher, _animationsDisabled = false) {\n    this._portalOutlet = _portalOutlet;\n    this._host = _host;\n    this._pane = _pane;\n    this._config = _config;\n    this._ngZone = _ngZone;\n    this._keyboardDispatcher = _keyboardDispatcher;\n    this._document = _document;\n    this._location = _location;\n    this._outsideClickDispatcher = _outsideClickDispatcher;\n    this._animationsDisabled = _animationsDisabled;\n    this._backdropElement = null;\n    this._backdropClick = new Subject();\n    this._attachments = new Subject();\n    this._detachments = new Subject();\n    this._locationChanges = Subscription.EMPTY;\n\n    this._backdropClickHandler = event => this._backdropClick.next(event);\n\n    this._backdropTransitionendHandler = event => {\n      this._disposeBackdrop(event.target);\n    };\n    /** Stream of keydown events dispatched to this overlay. */\n\n\n    this._keydownEvents = new Subject();\n    /** Stream of mouse outside events dispatched to this overlay. */\n\n    this._outsidePointerEvents = new Subject();\n\n    if (_config.scrollStrategy) {\n      this._scrollStrategy = _config.scrollStrategy;\n\n      this._scrollStrategy.attach(this);\n    }\n\n    this._positionStrategy = _config.positionStrategy;\n  }\n  /** The overlay's HTML element */\n\n\n  get overlayElement() {\n    return this._pane;\n  }\n  /** The overlay's backdrop HTML element. */\n\n\n  get backdropElement() {\n    return this._backdropElement;\n  }\n  /**\n   * Wrapper around the panel element. Can be used for advanced\n   * positioning where a wrapper with specific styling is\n   * required around the overlay pane.\n   */\n\n\n  get hostElement() {\n    return this._host;\n  }\n  /**\n   * Attaches content, given via a Portal, to the overlay.\n   * If the overlay is configured to have a backdrop, it will be created.\n   *\n   * @param portal Portal instance to which to attach the overlay.\n   * @returns The portal attachment result.\n   */\n\n\n  attach(portal) {\n    // Insert the host into the DOM before attaching the portal, otherwise\n    // the animations module will skip animations on repeat attachments.\n    if (!this._host.parentElement && this._previousHostParent) {\n      this._previousHostParent.appendChild(this._host);\n    }\n\n    const attachResult = this._portalOutlet.attach(portal);\n\n    if (this._positionStrategy) {\n      this._positionStrategy.attach(this);\n    }\n\n    this._updateStackingOrder();\n\n    this._updateElementSize();\n\n    this._updateElementDirection();\n\n    if (this._scrollStrategy) {\n      this._scrollStrategy.enable();\n    } // Update the position once the zone is stable so that the overlay will be fully rendered\n    // before attempting to position it, as the position may depend on the size of the rendered\n    // content.\n\n\n    this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n      // The overlay could've been detached before the zone has stabilized.\n      if (this.hasAttached()) {\n        this.updatePosition();\n      }\n    }); // Enable pointer events for the overlay pane element.\n\n\n    this._togglePointerEvents(true);\n\n    if (this._config.hasBackdrop) {\n      this._attachBackdrop();\n    }\n\n    if (this._config.panelClass) {\n      this._toggleClasses(this._pane, this._config.panelClass, true);\n    } // Only emit the `attachments` event once all other setup is done.\n\n\n    this._attachments.next(); // Track this overlay by the keyboard dispatcher\n\n\n    this._keyboardDispatcher.add(this);\n\n    if (this._config.disposeOnNavigation) {\n      this._locationChanges = this._location.subscribe(() => this.dispose());\n    }\n\n    this._outsideClickDispatcher.add(this); // TODO(crisbeto): the null check is here, because the portal outlet returns `any`.\n    // We should be guaranteed for the result to be `ComponentRef | EmbeddedViewRef`, but\n    // `instanceof EmbeddedViewRef` doesn't appear to work at the moment.\n\n\n    if (typeof attachResult?.onDestroy === 'function') {\n      // In most cases we control the portal and we know when it is being detached so that\n      // we can finish the disposal process. The exception is if the user passes in a custom\n      // `ViewContainerRef` that isn't destroyed through the overlay API. Note that we use\n      // `detach` here instead of `dispose`, because we don't know if the user intends to\n      // reattach the overlay at a later point. It also has the advantage of waiting for animations.\n      attachResult.onDestroy(() => {\n        if (this.hasAttached()) {\n          // We have to delay the `detach` call, because detaching immediately prevents\n          // other destroy hooks from running. This is likely a framework bug similar to\n          // https://github.com/angular/angular/issues/46119\n          this._ngZone.runOutsideAngular(() => Promise.resolve().then(() => this.detach()));\n        }\n      });\n    }\n\n    return attachResult;\n  }\n  /**\n   * Detaches an overlay from a portal.\n   * @returns The portal detachment result.\n   */\n\n\n  detach() {\n    if (!this.hasAttached()) {\n      return;\n    }\n\n    this.detachBackdrop(); // When the overlay is detached, the pane element should disable pointer events.\n    // This is necessary because otherwise the pane element will cover the page and disable\n    // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n\n    this._togglePointerEvents(false);\n\n    if (this._positionStrategy && this._positionStrategy.detach) {\n      this._positionStrategy.detach();\n    }\n\n    if (this._scrollStrategy) {\n      this._scrollStrategy.disable();\n    }\n\n    const detachmentResult = this._portalOutlet.detach(); // Only emit after everything is detached.\n\n\n    this._detachments.next(); // Remove this overlay from keyboard dispatcher tracking.\n\n\n    this._keyboardDispatcher.remove(this); // Keeping the host element in the DOM can cause scroll jank, because it still gets\n    // rendered, even though it's transparent and unclickable which is why we remove it.\n\n\n    this._detachContentWhenStable();\n\n    this._locationChanges.unsubscribe();\n\n    this._outsideClickDispatcher.remove(this);\n\n    return detachmentResult;\n  }\n  /** Cleans up the overlay from the DOM. */\n\n\n  dispose() {\n    const isAttached = this.hasAttached();\n\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n\n    this._disposeScrollStrategy();\n\n    this._disposeBackdrop(this._backdropElement);\n\n    this._locationChanges.unsubscribe();\n\n    this._keyboardDispatcher.remove(this);\n\n    this._portalOutlet.dispose();\n\n    this._attachments.complete();\n\n    this._backdropClick.complete();\n\n    this._keydownEvents.complete();\n\n    this._outsidePointerEvents.complete();\n\n    this._outsideClickDispatcher.remove(this);\n\n    this._host?.remove();\n    this._previousHostParent = this._pane = this._host = null;\n\n    if (isAttached) {\n      this._detachments.next();\n    }\n\n    this._detachments.complete();\n  }\n  /** Whether the overlay has attached content. */\n\n\n  hasAttached() {\n    return this._portalOutlet.hasAttached();\n  }\n  /** Gets an observable that emits when the backdrop has been clicked. */\n\n\n  backdropClick() {\n    return this._backdropClick;\n  }\n  /** Gets an observable that emits when the overlay has been attached. */\n\n\n  attachments() {\n    return this._attachments;\n  }\n  /** Gets an observable that emits when the overlay has been detached. */\n\n\n  detachments() {\n    return this._detachments;\n  }\n  /** Gets an observable of keydown events targeted to this overlay. */\n\n\n  keydownEvents() {\n    return this._keydownEvents;\n  }\n  /** Gets an observable of pointer events targeted outside this overlay. */\n\n\n  outsidePointerEvents() {\n    return this._outsidePointerEvents;\n  }\n  /** Gets the current overlay configuration, which is immutable. */\n\n\n  getConfig() {\n    return this._config;\n  }\n  /** Updates the position of the overlay based on the position strategy. */\n\n\n  updatePosition() {\n    if (this._positionStrategy) {\n      this._positionStrategy.apply();\n    }\n  }\n  /** Switches to a new position strategy and updates the overlay position. */\n\n\n  updatePositionStrategy(strategy) {\n    if (strategy === this._positionStrategy) {\n      return;\n    }\n\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n\n    this._positionStrategy = strategy;\n\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      this.updatePosition();\n    }\n  }\n  /** Update the size properties of the overlay. */\n\n\n  updateSize(sizeConfig) {\n    this._config = { ...this._config,\n      ...sizeConfig\n    };\n\n    this._updateElementSize();\n  }\n  /** Sets the LTR/RTL direction for the overlay. */\n\n\n  setDirection(dir) {\n    this._config = { ...this._config,\n      direction: dir\n    };\n\n    this._updateElementDirection();\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n\n\n  addPanelClass(classes) {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, true);\n    }\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n\n\n  removePanelClass(classes) {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, false);\n    }\n  }\n  /**\n   * Returns the layout direction of the overlay panel.\n   */\n\n\n  getDirection() {\n    const direction = this._config.direction;\n\n    if (!direction) {\n      return 'ltr';\n    }\n\n    return typeof direction === 'string' ? direction : direction.value;\n  }\n  /** Switches to a new scroll strategy. */\n\n\n  updateScrollStrategy(strategy) {\n    if (strategy === this._scrollStrategy) {\n      return;\n    }\n\n    this._disposeScrollStrategy();\n\n    this._scrollStrategy = strategy;\n\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      strategy.enable();\n    }\n  }\n  /** Updates the text direction of the overlay panel. */\n\n\n  _updateElementDirection() {\n    this._host.setAttribute('dir', this.getDirection());\n  }\n  /** Updates the size of the overlay element based on the overlay config. */\n\n\n  _updateElementSize() {\n    if (!this._pane) {\n      return;\n    }\n\n    const style = this._pane.style;\n    style.width = coerceCssPixelValue(this._config.width);\n    style.height = coerceCssPixelValue(this._config.height);\n    style.minWidth = coerceCssPixelValue(this._config.minWidth);\n    style.minHeight = coerceCssPixelValue(this._config.minHeight);\n    style.maxWidth = coerceCssPixelValue(this._config.maxWidth);\n    style.maxHeight = coerceCssPixelValue(this._config.maxHeight);\n  }\n  /** Toggles the pointer events for the overlay pane element. */\n\n\n  _togglePointerEvents(enablePointer) {\n    this._pane.style.pointerEvents = enablePointer ? '' : 'none';\n  }\n  /** Attaches a backdrop for this overlay. */\n\n\n  _attachBackdrop() {\n    const showingClass = 'cdk-overlay-backdrop-showing';\n    this._backdropElement = this._document.createElement('div');\n\n    this._backdropElement.classList.add('cdk-overlay-backdrop');\n\n    if (this._animationsDisabled) {\n      this._backdropElement.classList.add('cdk-overlay-backdrop-noop-animation');\n    }\n\n    if (this._config.backdropClass) {\n      this._toggleClasses(this._backdropElement, this._config.backdropClass, true);\n    } // Insert the backdrop before the pane in the DOM order,\n    // in order to handle stacked overlays properly.\n\n\n    this._host.parentElement.insertBefore(this._backdropElement, this._host); // Forward backdrop clicks such that the consumer of the overlay can perform whatever\n    // action desired when such a click occurs (usually closing the overlay).\n\n\n    this._backdropElement.addEventListener('click', this._backdropClickHandler); // Add class to fade-in the backdrop after one frame.\n\n\n    if (!this._animationsDisabled && typeof requestAnimationFrame !== 'undefined') {\n      this._ngZone.runOutsideAngular(() => {\n        requestAnimationFrame(() => {\n          if (this._backdropElement) {\n            this._backdropElement.classList.add(showingClass);\n          }\n        });\n      });\n    } else {\n      this._backdropElement.classList.add(showingClass);\n    }\n  }\n  /**\n   * Updates the stacking order of the element, moving it to the top if necessary.\n   * This is required in cases where one overlay was detached, while another one,\n   * that should be behind it, was destroyed. The next time both of them are opened,\n   * the stacking will be wrong, because the detached element's pane will still be\n   * in its original DOM position.\n   */\n\n\n  _updateStackingOrder() {\n    if (this._host.nextSibling) {\n      this._host.parentNode.appendChild(this._host);\n    }\n  }\n  /** Detaches the backdrop (if any) associated with the overlay. */\n\n\n  detachBackdrop() {\n    const backdropToDetach = this._backdropElement;\n\n    if (!backdropToDetach) {\n      return;\n    }\n\n    if (this._animationsDisabled) {\n      this._disposeBackdrop(backdropToDetach);\n\n      return;\n    }\n\n    backdropToDetach.classList.remove('cdk-overlay-backdrop-showing');\n\n    this._ngZone.runOutsideAngular(() => {\n      backdropToDetach.addEventListener('transitionend', this._backdropTransitionendHandler);\n    }); // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n    // In this case we make it unclickable and we try to remove it after a delay.\n\n\n    backdropToDetach.style.pointerEvents = 'none'; // Run this outside the Angular zone because there's nothing that Angular cares about.\n    // If it were to run inside the Angular zone, every test that used Overlay would have to be\n    // either async or fakeAsync.\n\n    this._backdropTimeout = this._ngZone.runOutsideAngular(() => setTimeout(() => {\n      this._disposeBackdrop(backdropToDetach);\n    }, 500));\n  }\n  /** Toggles a single CSS class or an array of classes on an element. */\n\n\n  _toggleClasses(element, cssClasses, isAdd) {\n    const classes = coerceArray(cssClasses || []).filter(c => !!c);\n\n    if (classes.length) {\n      isAdd ? element.classList.add(...classes) : element.classList.remove(...classes);\n    }\n  }\n  /** Detaches the overlay content next time the zone stabilizes. */\n\n\n  _detachContentWhenStable() {\n    // Normally we wouldn't have to explicitly run this outside the `NgZone`, however\n    // if the consumer is using `zone-patch-rxjs`, the `Subscription.unsubscribe` call will\n    // be patched to run inside the zone, which will throw us into an infinite loop.\n    this._ngZone.runOutsideAngular(() => {\n      // We can't remove the host here immediately, because the overlay pane's content\n      // might still be animating. This stream helps us avoid interrupting the animation\n      // by waiting for the pane to become empty.\n      const subscription = this._ngZone.onStable.pipe(takeUntil(merge(this._attachments, this._detachments))).subscribe(() => {\n        // Needs a couple of checks for the pane and host, because\n        // they may have been removed by the time the zone stabilizes.\n        if (!this._pane || !this._host || this._pane.children.length === 0) {\n          if (this._pane && this._config.panelClass) {\n            this._toggleClasses(this._pane, this._config.panelClass, false);\n          }\n\n          if (this._host && this._host.parentElement) {\n            this._previousHostParent = this._host.parentElement;\n\n            this._host.remove();\n          }\n\n          subscription.unsubscribe();\n        }\n      });\n    });\n  }\n  /** Disposes of a scroll strategy. */\n\n\n  _disposeScrollStrategy() {\n    const scrollStrategy = this._scrollStrategy;\n\n    if (scrollStrategy) {\n      scrollStrategy.disable();\n\n      if (scrollStrategy.detach) {\n        scrollStrategy.detach();\n      }\n    }\n  }\n  /** Removes a backdrop element from the DOM. */\n\n\n  _disposeBackdrop(backdrop) {\n    if (backdrop) {\n      backdrop.removeEventListener('click', this._backdropClickHandler);\n      backdrop.removeEventListener('transitionend', this._backdropTransitionendHandler);\n      backdrop.remove(); // It is possible that a new portal has been attached to this overlay since we started\n      // removing the backdrop. If that is the case, only clear the backdrop reference if it\n      // is still the same instance that we started to remove.\n\n      if (this._backdropElement === backdrop) {\n        this._backdropElement = null;\n      }\n    }\n\n    if (this._backdropTimeout) {\n      clearTimeout(this._backdropTimeout);\n      this._backdropTimeout = undefined;\n    }\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// TODO: refactor clipping detection into a separate thing (part of scrolling module)\n// TODO: doesn't handle both flexible width and height when it has to scroll along both axis.\n\n/** Class to be added to the overlay bounding box. */\n\n\nconst boundingBoxClass = 'cdk-overlay-connected-position-bounding-box';\n/** Regex used to split a string on its CSS units. */\n\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\n\nclass FlexibleConnectedPositionStrategy {\n  constructor(connectedTo, _viewportRuler, _document, _platform, _overlayContainer) {\n    this._viewportRuler = _viewportRuler;\n    this._document = _document;\n    this._platform = _platform;\n    this._overlayContainer = _overlayContainer;\n    /** Last size used for the bounding box. Used to avoid resizing the overlay after open. */\n\n    this._lastBoundingBoxSize = {\n      width: 0,\n      height: 0\n    };\n    /** Whether the overlay was pushed in a previous positioning. */\n\n    this._isPushed = false;\n    /** Whether the overlay can be pushed on-screen on the initial open. */\n\n    this._canPush = true;\n    /** Whether the overlay can grow via flexible width/height after the initial open. */\n\n    this._growAfterOpen = false;\n    /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n\n    this._hasFlexibleDimensions = true;\n    /** Whether the overlay position is locked. */\n\n    this._positionLocked = false;\n    /** Amount of space that must be maintained between the overlay and the edge of the viewport. */\n\n    this._viewportMargin = 0;\n    /** The Scrollable containers used to check scrollable view properties on position change. */\n\n    this._scrollables = [];\n    /** Ordered list of preferred positions, from most to least desirable. */\n\n    this._preferredPositions = [];\n    /** Subject that emits whenever the position changes. */\n\n    this._positionChanges = new Subject();\n    /** Subscription to viewport size changes. */\n\n    this._resizeSubscription = Subscription.EMPTY;\n    /** Default offset for the overlay along the x axis. */\n\n    this._offsetX = 0;\n    /** Default offset for the overlay along the y axis. */\n\n    this._offsetY = 0;\n    /** Keeps track of the CSS classes that the position strategy has applied on the overlay panel. */\n\n    this._appliedPanelClasses = [];\n    /** Observable sequence of position changes. */\n\n    this.positionChanges = this._positionChanges;\n    this.setOrigin(connectedTo);\n  }\n  /** Ordered list of preferred positions, from most to least desirable. */\n\n\n  get positions() {\n    return this._preferredPositions;\n  }\n  /** Attaches this position strategy to an overlay. */\n\n\n  attach(overlayRef) {\n    if (this._overlayRef && overlayRef !== this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('This position strategy is already attached to an overlay');\n    }\n\n    this._validatePositions();\n\n    overlayRef.hostElement.classList.add(boundingBoxClass);\n    this._overlayRef = overlayRef;\n    this._boundingBox = overlayRef.hostElement;\n    this._pane = overlayRef.overlayElement;\n    this._isDisposed = false;\n    this._isInitialRender = true;\n    this._lastPosition = null;\n\n    this._resizeSubscription.unsubscribe();\n\n    this._resizeSubscription = this._viewportRuler.change().subscribe(() => {\n      // When the window is resized, we want to trigger the next reposition as if it\n      // was an initial render, in order for the strategy to pick a new optimal position,\n      // otherwise position locking will cause it to stay at the old one.\n      this._isInitialRender = true;\n      this.apply();\n    });\n  }\n  /**\n   * Updates the position of the overlay element, using whichever preferred position relative\n   * to the origin best fits on-screen.\n   *\n   * The selection of a position goes as follows:\n   *  - If any positions fit completely within the viewport as-is,\n   *      choose the first position that does so.\n   *  - If flexible dimensions are enabled and at least one satisfies the given minimum width/height,\n   *      choose the position with the greatest available size modified by the positions' weight.\n   *  - If pushing is enabled, take the position that went off-screen the least and push it\n   *      on-screen.\n   *  - If none of the previous criteria were met, use the position that goes off-screen the least.\n   * @docs-private\n   */\n\n\n  apply() {\n    // We shouldn't do anything if the strategy was disposed or we're on the server.\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    } // If the position has been applied already (e.g. when the overlay was opened) and the\n    // consumer opted into locking in the position, re-use the old position, in order to\n    // prevent the overlay from jumping around.\n\n\n    if (!this._isInitialRender && this._positionLocked && this._lastPosition) {\n      this.reapplyLastPosition();\n      return;\n    }\n\n    this._clearPanelClasses();\n\n    this._resetOverlayElementStyles();\n\n    this._resetBoundingBoxStyles(); // We need the bounding rects for the origin, the overlay and the container to determine how to position\n    // the overlay relative to the origin.\n    // We use the viewport rect to determine whether a position would go off-screen.\n\n\n    this._viewportRect = this._getNarrowedViewportRect();\n    this._originRect = this._getOriginRect();\n    this._overlayRect = this._pane.getBoundingClientRect();\n    this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n    const originRect = this._originRect;\n    const overlayRect = this._overlayRect;\n    const viewportRect = this._viewportRect;\n    const containerRect = this._containerRect; // Positions where the overlay will fit with flexible dimensions.\n\n    const flexibleFits = []; // Fallback if none of the preferred positions fit within the viewport.\n\n    let fallback; // Go through each of the preferred positions looking for a good fit.\n    // If a good fit is found, it will be applied immediately.\n\n    for (let pos of this._preferredPositions) {\n      // Get the exact (x, y) coordinate for the point-of-origin on the origin element.\n      let originPoint = this._getOriginPoint(originRect, containerRect, pos); // From that point-of-origin, get the exact (x, y) coordinate for the top-left corner of the\n      // overlay in this position. We use the top-left corner for calculations and later translate\n      // this into an appropriate (top, left, bottom, right) style.\n\n\n      let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, pos); // Calculate how well the overlay would fit into the viewport with this point.\n\n\n      let overlayFit = this._getOverlayFit(overlayPoint, overlayRect, viewportRect, pos); // If the overlay, without any further work, fits into the viewport, use this position.\n\n\n      if (overlayFit.isCompletelyWithinViewport) {\n        this._isPushed = false;\n\n        this._applyPosition(pos, originPoint);\n\n        return;\n      } // If the overlay has flexible dimensions, we can use this position\n      // so long as there's enough space for the minimum dimensions.\n\n\n      if (this._canFitWithFlexibleDimensions(overlayFit, overlayPoint, viewportRect)) {\n        // Save positions where the overlay will fit with flexible dimensions. We will use these\n        // if none of the positions fit *without* flexible dimensions.\n        flexibleFits.push({\n          position: pos,\n          origin: originPoint,\n          overlayRect,\n          boundingBoxRect: this._calculateBoundingBoxRect(originPoint, pos)\n        });\n        continue;\n      } // If the current preferred position does not fit on the screen, remember the position\n      // if it has more visible area on-screen than we've seen and move onto the next preferred\n      // position.\n\n\n      if (!fallback || fallback.overlayFit.visibleArea < overlayFit.visibleArea) {\n        fallback = {\n          overlayFit,\n          overlayPoint,\n          originPoint,\n          position: pos,\n          overlayRect\n        };\n      }\n    } // If there are any positions where the overlay would fit with flexible dimensions, choose the\n    // one that has the greatest area available modified by the position's weight\n\n\n    if (flexibleFits.length) {\n      let bestFit = null;\n      let bestScore = -1;\n\n      for (const fit of flexibleFits) {\n        const score = fit.boundingBoxRect.width * fit.boundingBoxRect.height * (fit.position.weight || 1);\n\n        if (score > bestScore) {\n          bestScore = score;\n          bestFit = fit;\n        }\n      }\n\n      this._isPushed = false;\n\n      this._applyPosition(bestFit.position, bestFit.origin);\n\n      return;\n    } // When none of the preferred positions fit within the viewport, take the position\n    // that went off-screen the least and attempt to push it on-screen.\n\n\n    if (this._canPush) {\n      // TODO(jelbourn): after pushing, the opening \"direction\" of the overlay might not make sense.\n      this._isPushed = true;\n\n      this._applyPosition(fallback.position, fallback.originPoint);\n\n      return;\n    } // All options for getting the overlay within the viewport have been exhausted, so go with the\n    // position that went off-screen the least.\n\n\n    this._applyPosition(fallback.position, fallback.originPoint);\n  }\n\n  detach() {\n    this._clearPanelClasses();\n\n    this._lastPosition = null;\n    this._previousPushAmount = null;\n\n    this._resizeSubscription.unsubscribe();\n  }\n  /** Cleanup after the element gets destroyed. */\n\n\n  dispose() {\n    if (this._isDisposed) {\n      return;\n    } // We can't use `_resetBoundingBoxStyles` here, because it resets\n    // some properties to zero, rather than removing them.\n\n\n    if (this._boundingBox) {\n      extendStyles(this._boundingBox.style, {\n        top: '',\n        left: '',\n        right: '',\n        bottom: '',\n        height: '',\n        width: '',\n        alignItems: '',\n        justifyContent: ''\n      });\n    }\n\n    if (this._pane) {\n      this._resetOverlayElementStyles();\n    }\n\n    if (this._overlayRef) {\n      this._overlayRef.hostElement.classList.remove(boundingBoxClass);\n    }\n\n    this.detach();\n\n    this._positionChanges.complete();\n\n    this._overlayRef = this._boundingBox = null;\n    this._isDisposed = true;\n  }\n  /**\n   * This re-aligns the overlay element with the trigger in its last calculated position,\n   * even if a position higher in the \"preferred positions\" list would now fit. This\n   * allows one to re-align the panel without changing the orientation of the panel.\n   */\n\n\n  reapplyLastPosition() {\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n\n    const lastPosition = this._lastPosition;\n\n    if (lastPosition) {\n      this._originRect = this._getOriginRect();\n      this._overlayRect = this._pane.getBoundingClientRect();\n      this._viewportRect = this._getNarrowedViewportRect();\n      this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n\n      const originPoint = this._getOriginPoint(this._originRect, this._containerRect, lastPosition);\n\n      this._applyPosition(lastPosition, originPoint);\n    } else {\n      this.apply();\n    }\n  }\n  /**\n   * Sets the list of Scrollable containers that host the origin element so that\n   * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n   * Scrollable must be an ancestor element of the strategy's origin element.\n   */\n\n\n  withScrollableContainers(scrollables) {\n    this._scrollables = scrollables;\n    return this;\n  }\n  /**\n   * Adds new preferred positions.\n   * @param positions List of positions options for this overlay.\n   */\n\n\n  withPositions(positions) {\n    this._preferredPositions = positions; // If the last calculated position object isn't part of the positions anymore, clear\n    // it in order to avoid it being picked up if the consumer tries to re-apply.\n\n    if (positions.indexOf(this._lastPosition) === -1) {\n      this._lastPosition = null;\n    }\n\n    this._validatePositions();\n\n    return this;\n  }\n  /**\n   * Sets a minimum distance the overlay may be positioned to the edge of the viewport.\n   * @param margin Required margin between the overlay and the viewport edge in pixels.\n   */\n\n\n  withViewportMargin(margin) {\n    this._viewportMargin = margin;\n    return this;\n  }\n  /** Sets whether the overlay's width and height can be constrained to fit within the viewport. */\n\n\n  withFlexibleDimensions(flexibleDimensions = true) {\n    this._hasFlexibleDimensions = flexibleDimensions;\n    return this;\n  }\n  /** Sets whether the overlay can grow after the initial open via flexible width/height. */\n\n\n  withGrowAfterOpen(growAfterOpen = true) {\n    this._growAfterOpen = growAfterOpen;\n    return this;\n  }\n  /** Sets whether the overlay can be pushed on-screen if none of the provided positions fit. */\n\n\n  withPush(canPush = true) {\n    this._canPush = canPush;\n    return this;\n  }\n  /**\n   * Sets whether the overlay's position should be locked in after it is positioned\n   * initially. When an overlay is locked in, it won't attempt to reposition itself\n   * when the position is re-applied (e.g. when the user scrolls away).\n   * @param isLocked Whether the overlay should locked in.\n   */\n\n\n  withLockedPosition(isLocked = true) {\n    this._positionLocked = isLocked;\n    return this;\n  }\n  /**\n   * Sets the origin, relative to which to position the overlay.\n   * Using an element origin is useful for building components that need to be positioned\n   * relatively to a trigger (e.g. dropdown menus or tooltips), whereas using a point can be\n   * used for cases like contextual menus which open relative to the user's pointer.\n   * @param origin Reference to the new origin.\n   */\n\n\n  setOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n  /**\n   * Sets the default offset for the overlay's connection point on the x-axis.\n   * @param offset New offset in the X axis.\n   */\n\n\n  withDefaultOffsetX(offset) {\n    this._offsetX = offset;\n    return this;\n  }\n  /**\n   * Sets the default offset for the overlay's connection point on the y-axis.\n   * @param offset New offset in the Y axis.\n   */\n\n\n  withDefaultOffsetY(offset) {\n    this._offsetY = offset;\n    return this;\n  }\n  /**\n   * Configures that the position strategy should set a `transform-origin` on some elements\n   * inside the overlay, depending on the current position that is being applied. This is\n   * useful for the cases where the origin of an animation can change depending on the\n   * alignment of the overlay.\n   * @param selector CSS selector that will be used to find the target\n   *    elements onto which to set the transform origin.\n   */\n\n\n  withTransformOriginOn(selector) {\n    this._transformOriginSelector = selector;\n    return this;\n  }\n  /**\n   * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n   */\n\n\n  _getOriginPoint(originRect, containerRect, pos) {\n    let x;\n\n    if (pos.originX == 'center') {\n      // Note: when centering we should always use the `left`\n      // offset, otherwise the position will be wrong in RTL.\n      x = originRect.left + originRect.width / 2;\n    } else {\n      const startX = this._isRtl() ? originRect.right : originRect.left;\n      const endX = this._isRtl() ? originRect.left : originRect.right;\n      x = pos.originX == 'start' ? startX : endX;\n    } // When zooming in Safari the container rectangle contains negative values for the position\n    // and we need to re-add them to the calculated coordinates.\n\n\n    if (containerRect.left < 0) {\n      x -= containerRect.left;\n    }\n\n    let y;\n\n    if (pos.originY == 'center') {\n      y = originRect.top + originRect.height / 2;\n    } else {\n      y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n    } // Normally the containerRect's top value would be zero, however when the overlay is attached to an input\n    // (e.g. in an autocomplete), mobile browsers will shift everything in order to put the input in the middle\n    // of the screen and to make space for the virtual keyboard. We need to account for this offset,\n    // otherwise our positioning will be thrown off.\n    // Additionally, when zooming in Safari this fixes the vertical position.\n\n\n    if (containerRect.top < 0) {\n      y -= containerRect.top;\n    }\n\n    return {\n      x,\n      y\n    };\n  }\n  /**\n   * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n   * origin point to which the overlay should be connected.\n   */\n\n\n  _getOverlayPoint(originPoint, overlayRect, pos) {\n    // Calculate the (overlayStartX, overlayStartY), the start of the\n    // potential overlay position relative to the origin point.\n    let overlayStartX;\n\n    if (pos.overlayX == 'center') {\n      overlayStartX = -overlayRect.width / 2;\n    } else if (pos.overlayX === 'start') {\n      overlayStartX = this._isRtl() ? -overlayRect.width : 0;\n    } else {\n      overlayStartX = this._isRtl() ? 0 : -overlayRect.width;\n    }\n\n    let overlayStartY;\n\n    if (pos.overlayY == 'center') {\n      overlayStartY = -overlayRect.height / 2;\n    } else {\n      overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n    } // The (x, y) coordinates of the overlay.\n\n\n    return {\n      x: originPoint.x + overlayStartX,\n      y: originPoint.y + overlayStartY\n    };\n  }\n  /** Gets how well an overlay at the given point will fit within the viewport. */\n\n\n  _getOverlayFit(point, rawOverlayRect, viewport, position) {\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    let {\n      x,\n      y\n    } = point;\n\n    let offsetX = this._getOffset(position, 'x');\n\n    let offsetY = this._getOffset(position, 'y'); // Account for the offsets since they could push the overlay out of the viewport.\n\n\n    if (offsetX) {\n      x += offsetX;\n    }\n\n    if (offsetY) {\n      y += offsetY;\n    } // How much the overlay would overflow at this position, on each side.\n\n\n    let leftOverflow = 0 - x;\n    let rightOverflow = x + overlay.width - viewport.width;\n    let topOverflow = 0 - y;\n    let bottomOverflow = y + overlay.height - viewport.height; // Visible parts of the element on each axis.\n\n    let visibleWidth = this._subtractOverflows(overlay.width, leftOverflow, rightOverflow);\n\n    let visibleHeight = this._subtractOverflows(overlay.height, topOverflow, bottomOverflow);\n\n    let visibleArea = visibleWidth * visibleHeight;\n    return {\n      visibleArea,\n      isCompletelyWithinViewport: overlay.width * overlay.height === visibleArea,\n      fitsInViewportVertically: visibleHeight === overlay.height,\n      fitsInViewportHorizontally: visibleWidth == overlay.width\n    };\n  }\n  /**\n   * Whether the overlay can fit within the viewport when it may resize either its width or height.\n   * @param fit How well the overlay fits in the viewport at some position.\n   * @param point The (x, y) coordinates of the overlay at some position.\n   * @param viewport The geometry of the viewport.\n   */\n\n\n  _canFitWithFlexibleDimensions(fit, point, viewport) {\n    if (this._hasFlexibleDimensions) {\n      const availableHeight = viewport.bottom - point.y;\n      const availableWidth = viewport.right - point.x;\n      const minHeight = getPixelValue(this._overlayRef.getConfig().minHeight);\n      const minWidth = getPixelValue(this._overlayRef.getConfig().minWidth);\n      const verticalFit = fit.fitsInViewportVertically || minHeight != null && minHeight <= availableHeight;\n      const horizontalFit = fit.fitsInViewportHorizontally || minWidth != null && minWidth <= availableWidth;\n      return verticalFit && horizontalFit;\n    }\n\n    return false;\n  }\n  /**\n   * Gets the point at which the overlay can be \"pushed\" on-screen. If the overlay is larger than\n   * the viewport, the top-left corner will be pushed on-screen (with overflow occurring on the\n   * right and bottom).\n   *\n   * @param start Starting point from which the overlay is pushed.\n   * @param rawOverlayRect Dimensions of the overlay.\n   * @param scrollPosition Current viewport scroll position.\n   * @returns The point at which to position the overlay after pushing. This is effectively a new\n   *     originPoint.\n   */\n\n\n  _pushOverlayOnScreen(start, rawOverlayRect, scrollPosition) {\n    // If the position is locked and we've pushed the overlay already, reuse the previous push\n    // amount, rather than pushing it again. If we were to continue pushing, the element would\n    // remain in the viewport, which goes against the expectations when position locking is enabled.\n    if (this._previousPushAmount && this._positionLocked) {\n      return {\n        x: start.x + this._previousPushAmount.x,\n        y: start.y + this._previousPushAmount.y\n      };\n    } // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n\n\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    const viewport = this._viewportRect; // Determine how much the overlay goes outside the viewport on each\n    // side, which we'll use to decide which direction to push it.\n\n    const overflowRight = Math.max(start.x + overlay.width - viewport.width, 0);\n    const overflowBottom = Math.max(start.y + overlay.height - viewport.height, 0);\n    const overflowTop = Math.max(viewport.top - scrollPosition.top - start.y, 0);\n    const overflowLeft = Math.max(viewport.left - scrollPosition.left - start.x, 0); // Amount by which to push the overlay in each axis such that it remains on-screen.\n\n    let pushX = 0;\n    let pushY = 0; // If the overlay fits completely within the bounds of the viewport, push it from whichever\n    // direction is goes off-screen. Otherwise, push the top-left corner such that its in the\n    // viewport and allow for the trailing end of the overlay to go out of bounds.\n\n    if (overlay.width <= viewport.width) {\n      pushX = overflowLeft || -overflowRight;\n    } else {\n      pushX = start.x < this._viewportMargin ? viewport.left - scrollPosition.left - start.x : 0;\n    }\n\n    if (overlay.height <= viewport.height) {\n      pushY = overflowTop || -overflowBottom;\n    } else {\n      pushY = start.y < this._viewportMargin ? viewport.top - scrollPosition.top - start.y : 0;\n    }\n\n    this._previousPushAmount = {\n      x: pushX,\n      y: pushY\n    };\n    return {\n      x: start.x + pushX,\n      y: start.y + pushY\n    };\n  }\n  /**\n   * Applies a computed position to the overlay and emits a position change.\n   * @param position The position preference\n   * @param originPoint The point on the origin element where the overlay is connected.\n   */\n\n\n  _applyPosition(position, originPoint) {\n    this._setTransformOrigin(position);\n\n    this._setOverlayElementStyles(originPoint, position);\n\n    this._setBoundingBoxStyles(originPoint, position);\n\n    if (position.panelClass) {\n      this._addPanelClasses(position.panelClass);\n    } // Save the last connected position in case the position needs to be re-calculated.\n\n\n    this._lastPosition = position; // Notify that the position has been changed along with its change properties.\n    // We only emit if we've got any subscriptions, because the scroll visibility\n    // calculations can be somewhat expensive.\n\n    if (this._positionChanges.observers.length) {\n      const scrollableViewProperties = this._getScrollVisibility();\n\n      const changeEvent = new ConnectedOverlayPositionChange(position, scrollableViewProperties);\n\n      this._positionChanges.next(changeEvent);\n    }\n\n    this._isInitialRender = false;\n  }\n  /** Sets the transform origin based on the configured selector and the passed-in position.  */\n\n\n  _setTransformOrigin(position) {\n    if (!this._transformOriginSelector) {\n      return;\n    }\n\n    const elements = this._boundingBox.querySelectorAll(this._transformOriginSelector);\n\n    let xOrigin;\n    let yOrigin = position.overlayY;\n\n    if (position.overlayX === 'center') {\n      xOrigin = 'center';\n    } else if (this._isRtl()) {\n      xOrigin = position.overlayX === 'start' ? 'right' : 'left';\n    } else {\n      xOrigin = position.overlayX === 'start' ? 'left' : 'right';\n    }\n\n    for (let i = 0; i < elements.length; i++) {\n      elements[i].style.transformOrigin = `${xOrigin} ${yOrigin}`;\n    }\n  }\n  /**\n   * Gets the position and size of the overlay's sizing container.\n   *\n   * This method does no measuring and applies no styles so that we can cheaply compute the\n   * bounds for all positions and choose the best fit based on these results.\n   */\n\n\n  _calculateBoundingBoxRect(origin, position) {\n    const viewport = this._viewportRect;\n\n    const isRtl = this._isRtl();\n\n    let height, top, bottom;\n\n    if (position.overlayY === 'top') {\n      // Overlay is opening \"downward\" and thus is bound by the bottom viewport edge.\n      top = origin.y;\n      height = viewport.height - top + this._viewportMargin;\n    } else if (position.overlayY === 'bottom') {\n      // Overlay is opening \"upward\" and thus is bound by the top viewport edge. We need to add\n      // the viewport margin back in, because the viewport rect is narrowed down to remove the\n      // margin, whereas the `origin` position is calculated based on its `ClientRect`.\n      bottom = viewport.height - origin.y + this._viewportMargin * 2;\n      height = viewport.height - bottom + this._viewportMargin;\n    } else {\n      // If neither top nor bottom, it means that the overlay is vertically centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.bottom - origin.y` and\n      // `origin.y - viewport.top`.\n      const smallestDistanceToViewportEdge = Math.min(viewport.bottom - origin.y + viewport.top, origin.y);\n      const previousHeight = this._lastBoundingBoxSize.height;\n      height = smallestDistanceToViewportEdge * 2;\n      top = origin.y - smallestDistanceToViewportEdge;\n\n      if (height > previousHeight && !this._isInitialRender && !this._growAfterOpen) {\n        top = origin.y - previousHeight / 2;\n      }\n    } // The overlay is opening 'right-ward' (the content flows to the right).\n\n\n    const isBoundedByRightViewportEdge = position.overlayX === 'start' && !isRtl || position.overlayX === 'end' && isRtl; // The overlay is opening 'left-ward' (the content flows to the left).\n\n    const isBoundedByLeftViewportEdge = position.overlayX === 'end' && !isRtl || position.overlayX === 'start' && isRtl;\n    let width, left, right;\n\n    if (isBoundedByLeftViewportEdge) {\n      right = viewport.width - origin.x + this._viewportMargin;\n      width = origin.x - this._viewportMargin;\n    } else if (isBoundedByRightViewportEdge) {\n      left = origin.x;\n      width = viewport.right - origin.x;\n    } else {\n      // If neither start nor end, it means that the overlay is horizontally centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.right - origin.x` and\n      // `origin.x - viewport.left`.\n      const smallestDistanceToViewportEdge = Math.min(viewport.right - origin.x + viewport.left, origin.x);\n      const previousWidth = this._lastBoundingBoxSize.width;\n      width = smallestDistanceToViewportEdge * 2;\n      left = origin.x - smallestDistanceToViewportEdge;\n\n      if (width > previousWidth && !this._isInitialRender && !this._growAfterOpen) {\n        left = origin.x - previousWidth / 2;\n      }\n    }\n\n    return {\n      top: top,\n      left: left,\n      bottom: bottom,\n      right: right,\n      width,\n      height\n    };\n  }\n  /**\n   * Sets the position and size of the overlay's sizing wrapper. The wrapper is positioned on the\n   * origin's connection point and stretches to the bounds of the viewport.\n   *\n   * @param origin The point on the origin element where the overlay is connected.\n   * @param position The position preference\n   */\n\n\n  _setBoundingBoxStyles(origin, position) {\n    const boundingBoxRect = this._calculateBoundingBoxRect(origin, position); // It's weird if the overlay *grows* while scrolling, so we take the last size into account\n    // when applying a new size.\n\n\n    if (!this._isInitialRender && !this._growAfterOpen) {\n      boundingBoxRect.height = Math.min(boundingBoxRect.height, this._lastBoundingBoxSize.height);\n      boundingBoxRect.width = Math.min(boundingBoxRect.width, this._lastBoundingBoxSize.width);\n    }\n\n    const styles = {};\n\n    if (this._hasExactPosition()) {\n      styles.top = styles.left = '0';\n      styles.bottom = styles.right = styles.maxHeight = styles.maxWidth = '';\n      styles.width = styles.height = '100%';\n    } else {\n      const maxHeight = this._overlayRef.getConfig().maxHeight;\n\n      const maxWidth = this._overlayRef.getConfig().maxWidth;\n\n      styles.height = coerceCssPixelValue(boundingBoxRect.height);\n      styles.top = coerceCssPixelValue(boundingBoxRect.top);\n      styles.bottom = coerceCssPixelValue(boundingBoxRect.bottom);\n      styles.width = coerceCssPixelValue(boundingBoxRect.width);\n      styles.left = coerceCssPixelValue(boundingBoxRect.left);\n      styles.right = coerceCssPixelValue(boundingBoxRect.right); // Push the pane content towards the proper direction.\n\n      if (position.overlayX === 'center') {\n        styles.alignItems = 'center';\n      } else {\n        styles.alignItems = position.overlayX === 'end' ? 'flex-end' : 'flex-start';\n      }\n\n      if (position.overlayY === 'center') {\n        styles.justifyContent = 'center';\n      } else {\n        styles.justifyContent = position.overlayY === 'bottom' ? 'flex-end' : 'flex-start';\n      }\n\n      if (maxHeight) {\n        styles.maxHeight = coerceCssPixelValue(maxHeight);\n      }\n\n      if (maxWidth) {\n        styles.maxWidth = coerceCssPixelValue(maxWidth);\n      }\n    }\n\n    this._lastBoundingBoxSize = boundingBoxRect;\n    extendStyles(this._boundingBox.style, styles);\n  }\n  /** Resets the styles for the bounding box so that a new positioning can be computed. */\n\n\n  _resetBoundingBoxStyles() {\n    extendStyles(this._boundingBox.style, {\n      top: '0',\n      left: '0',\n      right: '0',\n      bottom: '0',\n      height: '',\n      width: '',\n      alignItems: '',\n      justifyContent: ''\n    });\n  }\n  /** Resets the styles for the overlay pane so that a new positioning can be computed. */\n\n\n  _resetOverlayElementStyles() {\n    extendStyles(this._pane.style, {\n      top: '',\n      left: '',\n      bottom: '',\n      right: '',\n      position: '',\n      transform: ''\n    });\n  }\n  /** Sets positioning styles to the overlay element. */\n\n\n  _setOverlayElementStyles(originPoint, position) {\n    const styles = {};\n\n    const hasExactPosition = this._hasExactPosition();\n\n    const hasFlexibleDimensions = this._hasFlexibleDimensions;\n\n    const config = this._overlayRef.getConfig();\n\n    if (hasExactPosition) {\n      const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n\n      extendStyles(styles, this._getExactOverlayY(position, originPoint, scrollPosition));\n      extendStyles(styles, this._getExactOverlayX(position, originPoint, scrollPosition));\n    } else {\n      styles.position = 'static';\n    } // Use a transform to apply the offsets. We do this because the `center` positions rely on\n    // being in the normal flex flow and setting a `top` / `left` at all will completely throw\n    // off the position. We also can't use margins, because they won't have an effect in some\n    // cases where the element doesn't have anything to \"push off of\". Finally, this works\n    // better both with flexible and non-flexible positioning.\n\n\n    let transformString = '';\n\n    let offsetX = this._getOffset(position, 'x');\n\n    let offsetY = this._getOffset(position, 'y');\n\n    if (offsetX) {\n      transformString += `translateX(${offsetX}px) `;\n    }\n\n    if (offsetY) {\n      transformString += `translateY(${offsetY}px)`;\n    }\n\n    styles.transform = transformString.trim(); // If a maxWidth or maxHeight is specified on the overlay, we remove them. We do this because\n    // we need these values to both be set to \"100%\" for the automatic flexible sizing to work.\n    // The maxHeight and maxWidth are set on the boundingBox in order to enforce the constraint.\n    // Note that this doesn't apply when we have an exact position, in which case we do want to\n    // apply them because they'll be cleared from the bounding box.\n\n    if (config.maxHeight) {\n      if (hasExactPosition) {\n        styles.maxHeight = coerceCssPixelValue(config.maxHeight);\n      } else if (hasFlexibleDimensions) {\n        styles.maxHeight = '';\n      }\n    }\n\n    if (config.maxWidth) {\n      if (hasExactPosition) {\n        styles.maxWidth = coerceCssPixelValue(config.maxWidth);\n      } else if (hasFlexibleDimensions) {\n        styles.maxWidth = '';\n      }\n    }\n\n    extendStyles(this._pane.style, styles);\n  }\n  /** Gets the exact top/bottom for the overlay when not using flexible sizing or when pushing. */\n\n\n  _getExactOverlayY(position, originPoint, scrollPosition) {\n    // Reset any existing styles. This is necessary in case the\n    // preferred position has changed since the last `apply`.\n    let styles = {\n      top: '',\n      bottom: ''\n    };\n\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    } // We want to set either `top` or `bottom` based on whether the overlay wants to appear\n    // above or below the origin and the direction in which the element will expand.\n\n\n    if (position.overlayY === 'bottom') {\n      // When using `bottom`, we adjust the y position such that it is the distance\n      // from the bottom of the viewport rather than the top.\n      const documentHeight = this._document.documentElement.clientHeight;\n      styles.bottom = `${documentHeight - (overlayPoint.y + this._overlayRect.height)}px`;\n    } else {\n      styles.top = coerceCssPixelValue(overlayPoint.y);\n    }\n\n    return styles;\n  }\n  /** Gets the exact left/right for the overlay when not using flexible sizing or when pushing. */\n\n\n  _getExactOverlayX(position, originPoint, scrollPosition) {\n    // Reset any existing styles. This is necessary in case the preferred position has\n    // changed since the last `apply`.\n    let styles = {\n      left: '',\n      right: ''\n    };\n\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    } // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n    // or \"after\" the origin, which determines the direction in which the element will expand.\n    // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n    // page is in RTL or LTR.\n\n\n    let horizontalStyleProperty;\n\n    if (this._isRtl()) {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'left' : 'right';\n    } else {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'right' : 'left';\n    } // When we're setting `right`, we adjust the x position such that it is the distance\n    // from the right edge of the viewport rather than the left edge.\n\n\n    if (horizontalStyleProperty === 'right') {\n      const documentWidth = this._document.documentElement.clientWidth;\n      styles.right = `${documentWidth - (overlayPoint.x + this._overlayRect.width)}px`;\n    } else {\n      styles.left = coerceCssPixelValue(overlayPoint.x);\n    }\n\n    return styles;\n  }\n  /**\n   * Gets the view properties of the trigger and overlay, including whether they are clipped\n   * or completely outside the view of any of the strategy's scrollables.\n   */\n\n\n  _getScrollVisibility() {\n    // Note: needs fresh rects since the position could've changed.\n    const originBounds = this._getOriginRect();\n\n    const overlayBounds = this._pane.getBoundingClientRect(); // TODO(jelbourn): instead of needing all of the client rects for these scrolling containers\n    // every time, we should be able to use the scrollTop of the containers if the size of those\n    // containers hasn't changed.\n\n\n    const scrollContainerBounds = this._scrollables.map(scrollable => {\n      return scrollable.getElementRef().nativeElement.getBoundingClientRect();\n    });\n\n    return {\n      isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n      isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n      isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n      isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds)\n    };\n  }\n  /** Subtracts the amount that an element is overflowing on an axis from its length. */\n\n\n  _subtractOverflows(length, ...overflows) {\n    return overflows.reduce((currentValue, currentOverflow) => {\n      return currentValue - Math.max(currentOverflow, 0);\n    }, length);\n  }\n  /** Narrows the given viewport rect by the current _viewportMargin. */\n\n\n  _getNarrowedViewportRect() {\n    // We recalculate the viewport rect here ourselves, rather than using the ViewportRuler,\n    // because we want to use the `clientWidth` and `clientHeight` as the base. The difference\n    // being that the client properties don't include the scrollbar, as opposed to `innerWidth`\n    // and `innerHeight` that do. This is necessary, because the overlay container uses\n    // 100% `width` and `height` which don't include the scrollbar either.\n    const width = this._document.documentElement.clientWidth;\n    const height = this._document.documentElement.clientHeight;\n\n    const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n\n    return {\n      top: scrollPosition.top + this._viewportMargin,\n      left: scrollPosition.left + this._viewportMargin,\n      right: scrollPosition.left + width - this._viewportMargin,\n      bottom: scrollPosition.top + height - this._viewportMargin,\n      width: width - 2 * this._viewportMargin,\n      height: height - 2 * this._viewportMargin\n    };\n  }\n  /** Whether the we're dealing with an RTL context */\n\n\n  _isRtl() {\n    return this._overlayRef.getDirection() === 'rtl';\n  }\n  /** Determines whether the overlay uses exact or flexible positioning. */\n\n\n  _hasExactPosition() {\n    return !this._hasFlexibleDimensions || this._isPushed;\n  }\n  /** Retrieves the offset of a position along the x or y axis. */\n\n\n  _getOffset(position, axis) {\n    if (axis === 'x') {\n      // We don't do something like `position['offset' + axis]` in\n      // order to avoid breaking minifiers that rename properties.\n      return position.offsetX == null ? this._offsetX : position.offsetX;\n    }\n\n    return position.offsetY == null ? this._offsetY : position.offsetY;\n  }\n  /** Validates that the current position match the expected values. */\n\n\n  _validatePositions() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._preferredPositions.length) {\n        throw Error('FlexibleConnectedPositionStrategy: At least one position is required.');\n      } // TODO(crisbeto): remove these once Angular's template type\n      // checking is advanced enough to catch these cases.\n\n\n      this._preferredPositions.forEach(pair => {\n        validateHorizontalPosition('originX', pair.originX);\n        validateVerticalPosition('originY', pair.originY);\n        validateHorizontalPosition('overlayX', pair.overlayX);\n        validateVerticalPosition('overlayY', pair.overlayY);\n      });\n    }\n  }\n  /** Adds a single CSS class or an array of classes on the overlay panel. */\n\n\n  _addPanelClasses(cssClasses) {\n    if (this._pane) {\n      coerceArray(cssClasses).forEach(cssClass => {\n        if (cssClass !== '' && this._appliedPanelClasses.indexOf(cssClass) === -1) {\n          this._appliedPanelClasses.push(cssClass);\n\n          this._pane.classList.add(cssClass);\n        }\n      });\n    }\n  }\n  /** Clears the classes that the position strategy has applied from the overlay panel. */\n\n\n  _clearPanelClasses() {\n    if (this._pane) {\n      this._appliedPanelClasses.forEach(cssClass => {\n        this._pane.classList.remove(cssClass);\n      });\n\n      this._appliedPanelClasses = [];\n    }\n  }\n  /** Returns the ClientRect of the current origin. */\n\n\n  _getOriginRect() {\n    const origin = this._origin;\n\n    if (origin instanceof ElementRef) {\n      return origin.nativeElement.getBoundingClientRect();\n    } // Check for Element so SVG elements are also supported.\n\n\n    if (origin instanceof Element) {\n      return origin.getBoundingClientRect();\n    }\n\n    const width = origin.width || 0;\n    const height = origin.height || 0; // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n\n    return {\n      top: origin.y,\n      bottom: origin.y + height,\n      left: origin.x,\n      right: origin.x + width,\n      height,\n      width\n    };\n  }\n\n}\n/** Shallow-extends a stylesheet object with another stylesheet object. */\n\n\nfunction extendStyles(destination, source) {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      destination[key] = source[key];\n    }\n  }\n\n  return destination;\n}\n/**\n * Extracts the pixel value as a number from a value, if it's a number\n * or a CSS pixel string (e.g. `1337px`). Otherwise returns null.\n */\n\n\nfunction getPixelValue(input) {\n  if (typeof input !== 'number' && input != null) {\n    const [value, units] = input.split(cssUnitPattern);\n    return !units || units === 'px' ? parseFloat(value) : null;\n  }\n\n  return input || null;\n}\n/**\n * Gets a version of an element's bounding `ClientRect` where all the values are rounded down to\n * the nearest pixel. This allows us to account for the cases where there may be sub-pixel\n * deviations in the `ClientRect` returned by the browser (e.g. when zoomed in with a percentage\n * size, see #21350).\n */\n\n\nfunction getRoundedBoundingClientRect(clientRect) {\n  return {\n    top: Math.floor(clientRect.top),\n    right: Math.floor(clientRect.right),\n    bottom: Math.floor(clientRect.bottom),\n    left: Math.floor(clientRect.left),\n    width: Math.floor(clientRect.width),\n    height: Math.floor(clientRect.height)\n  };\n}\n\nconst STANDARD_DROPDOWN_BELOW_POSITIONS = [{\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'top'\n}, {\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}];\nconst STANDARD_DROPDOWN_ADJACENT_POSITIONS = [{\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}];\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Class to be added to the overlay pane wrapper. */\n\nconst wrapperClass = 'cdk-global-overlay-wrapper';\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\n\nclass GlobalPositionStrategy {\n  constructor() {\n    this._cssPosition = 'static';\n    this._topOffset = '';\n    this._bottomOffset = '';\n    this._alignItems = '';\n    this._xPosition = '';\n    this._xOffset = '';\n    this._width = '';\n    this._height = '';\n    this._isDisposed = false;\n  }\n\n  attach(overlayRef) {\n    const config = overlayRef.getConfig();\n    this._overlayRef = overlayRef;\n\n    if (this._width && !config.width) {\n      overlayRef.updateSize({\n        width: this._width\n      });\n    }\n\n    if (this._height && !config.height) {\n      overlayRef.updateSize({\n        height: this._height\n      });\n    }\n\n    overlayRef.hostElement.classList.add(wrapperClass);\n    this._isDisposed = false;\n  }\n  /**\n   * Sets the top position of the overlay. Clears any previously set vertical position.\n   * @param value New top offset.\n   */\n\n\n  top(value = '') {\n    this._bottomOffset = '';\n    this._topOffset = value;\n    this._alignItems = 'flex-start';\n    return this;\n  }\n  /**\n   * Sets the left position of the overlay. Clears any previously set horizontal position.\n   * @param value New left offset.\n   */\n\n\n  left(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'left';\n    return this;\n  }\n  /**\n   * Sets the bottom position of the overlay. Clears any previously set vertical position.\n   * @param value New bottom offset.\n   */\n\n\n  bottom(value = '') {\n    this._topOffset = '';\n    this._bottomOffset = value;\n    this._alignItems = 'flex-end';\n    return this;\n  }\n  /**\n   * Sets the right position of the overlay. Clears any previously set horizontal position.\n   * @param value New right offset.\n   */\n\n\n  right(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'right';\n    return this;\n  }\n  /**\n   * Sets the overlay to the start of the viewport, depending on the overlay direction.\n   * This will be to the left in LTR layouts and to the right in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n\n\n  start(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'start';\n    return this;\n  }\n  /**\n   * Sets the overlay to the end of the viewport, depending on the overlay direction.\n   * This will be to the right in LTR layouts and to the left in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n\n\n  end(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'end';\n    return this;\n  }\n  /**\n   * Sets the overlay width and clears any previously set width.\n   * @param value New width for the overlay\n   * @deprecated Pass the `width` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n\n\n  width(value = '') {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({\n        width: value\n      });\n    } else {\n      this._width = value;\n    }\n\n    return this;\n  }\n  /**\n   * Sets the overlay height and clears any previously set height.\n   * @param value New height for the overlay\n   * @deprecated Pass the `height` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n\n\n  height(value = '') {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({\n        height: value\n      });\n    } else {\n      this._height = value;\n    }\n\n    return this;\n  }\n  /**\n   * Centers the overlay horizontally with an optional offset.\n   * Clears any previously set horizontal position.\n   *\n   * @param offset Overlay offset from the horizontal center.\n   */\n\n\n  centerHorizontally(offset = '') {\n    this.left(offset);\n    this._xPosition = 'center';\n    return this;\n  }\n  /**\n   * Centers the overlay vertically with an optional offset.\n   * Clears any previously set vertical position.\n   *\n   * @param offset Overlay offset from the vertical center.\n   */\n\n\n  centerVertically(offset = '') {\n    this.top(offset);\n    this._alignItems = 'center';\n    return this;\n  }\n  /**\n   * Apply the position to the element.\n   * @docs-private\n   */\n\n\n  apply() {\n    // Since the overlay ref applies the strategy asynchronously, it could\n    // have been disposed before it ends up being applied. If that is the\n    // case, we shouldn't do anything.\n    if (!this._overlayRef || !this._overlayRef.hasAttached()) {\n      return;\n    }\n\n    const styles = this._overlayRef.overlayElement.style;\n    const parentStyles = this._overlayRef.hostElement.style;\n\n    const config = this._overlayRef.getConfig();\n\n    const {\n      width,\n      height,\n      maxWidth,\n      maxHeight\n    } = config;\n    const shouldBeFlushHorizontally = (width === '100%' || width === '100vw') && (!maxWidth || maxWidth === '100%' || maxWidth === '100vw');\n    const shouldBeFlushVertically = (height === '100%' || height === '100vh') && (!maxHeight || maxHeight === '100%' || maxHeight === '100vh');\n    const xPosition = this._xPosition;\n    const xOffset = this._xOffset;\n    const isRtl = this._overlayRef.getConfig().direction === 'rtl';\n    let marginLeft = '';\n    let marginRight = '';\n    let justifyContent = '';\n\n    if (shouldBeFlushHorizontally) {\n      justifyContent = 'flex-start';\n    } else if (xPosition === 'center') {\n      justifyContent = 'center';\n\n      if (isRtl) {\n        marginRight = xOffset;\n      } else {\n        marginLeft = xOffset;\n      }\n    } else if (isRtl) {\n      if (xPosition === 'left' || xPosition === 'end') {\n        justifyContent = 'flex-end';\n        marginLeft = xOffset;\n      } else if (xPosition === 'right' || xPosition === 'start') {\n        justifyContent = 'flex-start';\n        marginRight = xOffset;\n      }\n    } else if (xPosition === 'left' || xPosition === 'start') {\n      justifyContent = 'flex-start';\n      marginLeft = xOffset;\n    } else if (xPosition === 'right' || xPosition === 'end') {\n      justifyContent = 'flex-end';\n      marginRight = xOffset;\n    }\n\n    styles.position = this._cssPosition;\n    styles.marginLeft = shouldBeFlushHorizontally ? '0' : marginLeft;\n    styles.marginTop = shouldBeFlushVertically ? '0' : this._topOffset;\n    styles.marginBottom = this._bottomOffset;\n    styles.marginRight = shouldBeFlushHorizontally ? '0' : marginRight;\n    parentStyles.justifyContent = justifyContent;\n    parentStyles.alignItems = shouldBeFlushVertically ? 'flex-start' : this._alignItems;\n  }\n  /**\n   * Cleans up the DOM changes from the position strategy.\n   * @docs-private\n   */\n\n\n  dispose() {\n    if (this._isDisposed || !this._overlayRef) {\n      return;\n    }\n\n    const styles = this._overlayRef.overlayElement.style;\n    const parent = this._overlayRef.hostElement;\n    const parentStyles = parent.style;\n    parent.classList.remove(wrapperClass);\n    parentStyles.justifyContent = parentStyles.alignItems = styles.marginTop = styles.marginBottom = styles.marginLeft = styles.marginRight = styles.position = '';\n    this._overlayRef = null;\n    this._isDisposed = true;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Builder for overlay position strategy. */\n\n\nclass OverlayPositionBuilder {\n  constructor(_viewportRuler, _document, _platform, _overlayContainer) {\n    this._viewportRuler = _viewportRuler;\n    this._document = _document;\n    this._platform = _platform;\n    this._overlayContainer = _overlayContainer;\n  }\n  /**\n   * Creates a global position strategy.\n   */\n\n\n  global() {\n    return new GlobalPositionStrategy();\n  }\n  /**\n   * Creates a flexible position strategy.\n   * @param origin Origin relative to which to position the overlay.\n   */\n\n\n  flexibleConnectedTo(origin) {\n    return new FlexibleConnectedPositionStrategy(origin, this._viewportRuler, this._document, this._platform, this._overlayContainer);\n  }\n\n}\n\nOverlayPositionBuilder.ɵfac = function OverlayPositionBuilder_Factory(t) {\n  return new (t || OverlayPositionBuilder)(i0.ɵɵinject(i1.ViewportRuler), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform), i0.ɵɵinject(OverlayContainer));\n};\n\nOverlayPositionBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: OverlayPositionBuilder,\n  factory: OverlayPositionBuilder.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayPositionBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.ViewportRuler\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1$1.Platform\n    }, {\n      type: OverlayContainer\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Next overlay unique ID. */\n\n\nlet nextUniqueId = 0; // Note that Overlay is *not* scoped to the app root because of the ComponentFactoryResolver\n// which needs to be different depending on where OverlayModule is imported.\n\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\n\nclass Overlay {\n  constructor(\n  /** Scrolling strategies that can be used when creating an overlay. */\n  scrollStrategies, _overlayContainer, _componentFactoryResolver, _positionBuilder, _keyboardDispatcher, _injector, _ngZone, _document, _directionality, _location, _outsideClickDispatcher, _animationsModuleType) {\n    this.scrollStrategies = scrollStrategies;\n    this._overlayContainer = _overlayContainer;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._positionBuilder = _positionBuilder;\n    this._keyboardDispatcher = _keyboardDispatcher;\n    this._injector = _injector;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._directionality = _directionality;\n    this._location = _location;\n    this._outsideClickDispatcher = _outsideClickDispatcher;\n    this._animationsModuleType = _animationsModuleType;\n  }\n  /**\n   * Creates an overlay.\n   * @param config Configuration applied to the overlay.\n   * @returns Reference to the created overlay.\n   */\n\n\n  create(config) {\n    const host = this._createHostElement();\n\n    const pane = this._createPaneElement(host);\n\n    const portalOutlet = this._createPortalOutlet(pane);\n\n    const overlayConfig = new OverlayConfig(config);\n    overlayConfig.direction = overlayConfig.direction || this._directionality.value;\n    return new OverlayRef(portalOutlet, host, pane, overlayConfig, this._ngZone, this._keyboardDispatcher, this._document, this._location, this._outsideClickDispatcher, this._animationsModuleType === 'NoopAnimations');\n  }\n  /**\n   * Gets a position builder that can be used, via fluent API,\n   * to construct and configure a position strategy.\n   * @returns An overlay position builder.\n   */\n\n\n  position() {\n    return this._positionBuilder;\n  }\n  /**\n   * Creates the DOM element for an overlay and appends it to the overlay container.\n   * @returns Newly-created pane element\n   */\n\n\n  _createPaneElement(host) {\n    const pane = this._document.createElement('div');\n\n    pane.id = `cdk-overlay-${nextUniqueId++}`;\n    pane.classList.add('cdk-overlay-pane');\n    host.appendChild(pane);\n    return pane;\n  }\n  /**\n   * Creates the host element that wraps around an overlay\n   * and can be used for advanced positioning.\n   * @returns Newly-create host element.\n   */\n\n\n  _createHostElement() {\n    const host = this._document.createElement('div');\n\n    this._overlayContainer.getContainerElement().appendChild(host);\n\n    return host;\n  }\n  /**\n   * Create a DomPortalOutlet into which the overlay content can be loaded.\n   * @param pane The DOM element to turn into a portal outlet.\n   * @returns A portal outlet for the given DOM element.\n   */\n\n\n  _createPortalOutlet(pane) {\n    // We have to resolve the ApplicationRef later in order to allow people\n    // to use overlay-based providers during app initialization.\n    if (!this._appRef) {\n      this._appRef = this._injector.get(ApplicationRef);\n    }\n\n    return new DomPortalOutlet(pane, this._componentFactoryResolver, this._appRef, this._injector, this._document);\n  }\n\n}\n\nOverlay.ɵfac = function Overlay_Factory(t) {\n  return new (t || Overlay)(i0.ɵɵinject(ScrollStrategyOptions), i0.ɵɵinject(OverlayContainer), i0.ɵɵinject(i0.ComponentFactoryResolver), i0.ɵɵinject(OverlayPositionBuilder), i0.ɵɵinject(OverlayKeyboardDispatcher), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i5.Directionality), i0.ɵɵinject(i6.Location), i0.ɵɵinject(OverlayOutsideClickDispatcher), i0.ɵɵinject(ANIMATION_MODULE_TYPE, 8));\n};\n\nOverlay.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Overlay,\n  factory: Overlay.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Overlay, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: ScrollStrategyOptions\n    }, {\n      type: OverlayContainer\n    }, {\n      type: i0.ComponentFactoryResolver\n    }, {\n      type: OverlayPositionBuilder\n    }, {\n      type: OverlayKeyboardDispatcher\n    }, {\n      type: i0.Injector\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i5.Directionality\n    }, {\n      type: i6.Location\n    }, {\n      type: OverlayOutsideClickDispatcher\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\n\n\nconst defaultPositionList = [{\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'top'\n}];\n/** Injection token that determines the scroll handling while the connected overlay is open. */\n\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY = new InjectionToken('cdk-connected-overlay-scroll-strategy');\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\n\nclass CdkOverlayOrigin {\n  constructor(\n  /** Reference to the element on which the directive is applied. */\n  elementRef) {\n    this.elementRef = elementRef;\n  }\n\n}\n\nCdkOverlayOrigin.ɵfac = function CdkOverlayOrigin_Factory(t) {\n  return new (t || CdkOverlayOrigin)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\nCdkOverlayOrigin.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkOverlayOrigin,\n  selectors: [[\"\", \"cdk-overlay-origin\", \"\"], [\"\", \"overlay-origin\", \"\"], [\"\", \"cdkOverlayOrigin\", \"\"]],\n  exportAs: [\"cdkOverlayOrigin\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkOverlayOrigin, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n      exportAs: 'cdkOverlayOrigin'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\n/**\n * Directive to facilitate declarative creation of an\n * Overlay using a FlexibleConnectedPositionStrategy.\n */\n\n\nclass CdkConnectedOverlay {\n  // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n  constructor(_overlay, templateRef, viewContainerRef, scrollStrategyFactory, _dir) {\n    this._overlay = _overlay;\n    this._dir = _dir;\n    this._hasBackdrop = false;\n    this._lockPosition = false;\n    this._growAfterOpen = false;\n    this._flexibleDimensions = false;\n    this._push = false;\n    this._backdropSubscription = Subscription.EMPTY;\n    this._attachSubscription = Subscription.EMPTY;\n    this._detachSubscription = Subscription.EMPTY;\n    this._positionSubscription = Subscription.EMPTY;\n    /** Margin between the overlay and the viewport edges. */\n\n    this.viewportMargin = 0;\n    /** Whether the overlay is open. */\n\n    this.open = false;\n    /** Whether the overlay can be closed by user interaction. */\n\n    this.disableClose = false;\n    /** Event emitted when the backdrop is clicked. */\n\n    this.backdropClick = new EventEmitter();\n    /** Event emitted when the position has changed. */\n\n    this.positionChange = new EventEmitter();\n    /** Event emitted when the overlay has been attached. */\n\n    this.attach = new EventEmitter();\n    /** Event emitted when the overlay has been detached. */\n\n    this.detach = new EventEmitter();\n    /** Emits when there are keyboard events that are targeted at the overlay. */\n\n    this.overlayKeydown = new EventEmitter();\n    /** Emits when there are mouse outside click events that are targeted at the overlay. */\n\n    this.overlayOutsideClick = new EventEmitter();\n    this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n    this._scrollStrategyFactory = scrollStrategyFactory;\n    this.scrollStrategy = this._scrollStrategyFactory();\n  }\n  /** The offset in pixels for the overlay connection point on the x-axis */\n\n\n  get offsetX() {\n    return this._offsetX;\n  }\n\n  set offsetX(offsetX) {\n    this._offsetX = offsetX;\n\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n  /** The offset in pixels for the overlay connection point on the y-axis */\n\n\n  get offsetY() {\n    return this._offsetY;\n  }\n\n  set offsetY(offsetY) {\n    this._offsetY = offsetY;\n\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n  /** Whether or not the overlay should attach a backdrop. */\n\n\n  get hasBackdrop() {\n    return this._hasBackdrop;\n  }\n\n  set hasBackdrop(value) {\n    this._hasBackdrop = coerceBooleanProperty(value);\n  }\n  /** Whether or not the overlay should be locked when scrolling. */\n\n\n  get lockPosition() {\n    return this._lockPosition;\n  }\n\n  set lockPosition(value) {\n    this._lockPosition = coerceBooleanProperty(value);\n  }\n  /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n\n\n  get flexibleDimensions() {\n    return this._flexibleDimensions;\n  }\n\n  set flexibleDimensions(value) {\n    this._flexibleDimensions = coerceBooleanProperty(value);\n  }\n  /** Whether the overlay can grow after the initial open when flexible positioning is turned on. */\n\n\n  get growAfterOpen() {\n    return this._growAfterOpen;\n  }\n\n  set growAfterOpen(value) {\n    this._growAfterOpen = coerceBooleanProperty(value);\n  }\n  /** Whether the overlay can be pushed on-screen if none of the provided positions fit. */\n\n\n  get push() {\n    return this._push;\n  }\n\n  set push(value) {\n    this._push = coerceBooleanProperty(value);\n  }\n  /** The associated overlay reference. */\n\n\n  get overlayRef() {\n    return this._overlayRef;\n  }\n  /** The element's layout direction. */\n\n\n  get dir() {\n    return this._dir ? this._dir.value : 'ltr';\n  }\n\n  ngOnDestroy() {\n    this._attachSubscription.unsubscribe();\n\n    this._detachSubscription.unsubscribe();\n\n    this._backdropSubscription.unsubscribe();\n\n    this._positionSubscription.unsubscribe();\n\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n    }\n  }\n\n  ngOnChanges(changes) {\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n\n      this._overlayRef.updateSize({\n        width: this.width,\n        minWidth: this.minWidth,\n        height: this.height,\n        minHeight: this.minHeight\n      });\n\n      if (changes['origin'] && this.open) {\n        this._position.apply();\n      }\n    }\n\n    if (changes['open']) {\n      this.open ? this._attachOverlay() : this._detachOverlay();\n    }\n  }\n  /** Creates an overlay */\n\n\n  _createOverlay() {\n    if (!this.positions || !this.positions.length) {\n      this.positions = defaultPositionList;\n    }\n\n    const overlayRef = this._overlayRef = this._overlay.create(this._buildConfig());\n\n    this._attachSubscription = overlayRef.attachments().subscribe(() => this.attach.emit());\n    this._detachSubscription = overlayRef.detachments().subscribe(() => this.detach.emit());\n    overlayRef.keydownEvents().subscribe(event => {\n      this.overlayKeydown.next(event);\n\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n\n        this._detachOverlay();\n      }\n    });\n\n    this._overlayRef.outsidePointerEvents().subscribe(event => {\n      this.overlayOutsideClick.next(event);\n    });\n  }\n  /** Builds the overlay config based on the directive's inputs */\n\n\n  _buildConfig() {\n    const positionStrategy = this._position = this.positionStrategy || this._createPositionStrategy();\n\n    const overlayConfig = new OverlayConfig({\n      direction: this._dir,\n      positionStrategy,\n      scrollStrategy: this.scrollStrategy,\n      hasBackdrop: this.hasBackdrop\n    });\n\n    if (this.width || this.width === 0) {\n      overlayConfig.width = this.width;\n    }\n\n    if (this.height || this.height === 0) {\n      overlayConfig.height = this.height;\n    }\n\n    if (this.minWidth || this.minWidth === 0) {\n      overlayConfig.minWidth = this.minWidth;\n    }\n\n    if (this.minHeight || this.minHeight === 0) {\n      overlayConfig.minHeight = this.minHeight;\n    }\n\n    if (this.backdropClass) {\n      overlayConfig.backdropClass = this.backdropClass;\n    }\n\n    if (this.panelClass) {\n      overlayConfig.panelClass = this.panelClass;\n    }\n\n    return overlayConfig;\n  }\n  /** Updates the state of a position strategy, based on the values of the directive inputs. */\n\n\n  _updatePositionStrategy(positionStrategy) {\n    const positions = this.positions.map(currentPosition => ({\n      originX: currentPosition.originX,\n      originY: currentPosition.originY,\n      overlayX: currentPosition.overlayX,\n      overlayY: currentPosition.overlayY,\n      offsetX: currentPosition.offsetX || this.offsetX,\n      offsetY: currentPosition.offsetY || this.offsetY,\n      panelClass: currentPosition.panelClass || undefined\n    }));\n    return positionStrategy.setOrigin(this._getFlexibleConnectedPositionStrategyOrigin()).withPositions(positions).withFlexibleDimensions(this.flexibleDimensions).withPush(this.push).withGrowAfterOpen(this.growAfterOpen).withViewportMargin(this.viewportMargin).withLockedPosition(this.lockPosition).withTransformOriginOn(this.transformOriginSelector);\n  }\n  /** Returns the position strategy of the overlay to be set on the overlay config */\n\n\n  _createPositionStrategy() {\n    const strategy = this._overlay.position().flexibleConnectedTo(this._getFlexibleConnectedPositionStrategyOrigin());\n\n    this._updatePositionStrategy(strategy);\n\n    return strategy;\n  }\n\n  _getFlexibleConnectedPositionStrategyOrigin() {\n    if (this.origin instanceof CdkOverlayOrigin) {\n      return this.origin.elementRef;\n    } else {\n      return this.origin;\n    }\n  }\n  /** Attaches the overlay and subscribes to backdrop clicks if backdrop exists */\n\n\n  _attachOverlay() {\n    if (!this._overlayRef) {\n      this._createOverlay();\n    } else {\n      // Update the overlay size, in case the directive's inputs have changed\n      this._overlayRef.getConfig().hasBackdrop = this.hasBackdrop;\n    }\n\n    if (!this._overlayRef.hasAttached()) {\n      this._overlayRef.attach(this._templatePortal);\n    }\n\n    if (this.hasBackdrop) {\n      this._backdropSubscription = this._overlayRef.backdropClick().subscribe(event => {\n        this.backdropClick.emit(event);\n      });\n    } else {\n      this._backdropSubscription.unsubscribe();\n    }\n\n    this._positionSubscription.unsubscribe(); // Only subscribe to `positionChanges` if requested, because putting\n    // together all the information for it can be expensive.\n\n\n    if (this.positionChange.observers.length > 0) {\n      this._positionSubscription = this._position.positionChanges.pipe(takeWhile(() => this.positionChange.observers.length > 0)).subscribe(position => {\n        this.positionChange.emit(position);\n\n        if (this.positionChange.observers.length === 0) {\n          this._positionSubscription.unsubscribe();\n        }\n      });\n    }\n  }\n  /** Detaches the overlay and unsubscribes to backdrop clicks if backdrop exists */\n\n\n  _detachOverlay() {\n    if (this._overlayRef) {\n      this._overlayRef.detach();\n    }\n\n    this._backdropSubscription.unsubscribe();\n\n    this._positionSubscription.unsubscribe();\n  }\n\n}\n\nCdkConnectedOverlay.ɵfac = function CdkConnectedOverlay_Factory(t) {\n  return new (t || CdkConnectedOverlay)(i0.ɵɵdirectiveInject(Overlay), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i5.Directionality, 8));\n};\n\nCdkConnectedOverlay.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkConnectedOverlay,\n  selectors: [[\"\", \"cdk-connected-overlay\", \"\"], [\"\", \"connected-overlay\", \"\"], [\"\", \"cdkConnectedOverlay\", \"\"]],\n  inputs: {\n    origin: [\"cdkConnectedOverlayOrigin\", \"origin\"],\n    positions: [\"cdkConnectedOverlayPositions\", \"positions\"],\n    positionStrategy: [\"cdkConnectedOverlayPositionStrategy\", \"positionStrategy\"],\n    offsetX: [\"cdkConnectedOverlayOffsetX\", \"offsetX\"],\n    offsetY: [\"cdkConnectedOverlayOffsetY\", \"offsetY\"],\n    width: [\"cdkConnectedOverlayWidth\", \"width\"],\n    height: [\"cdkConnectedOverlayHeight\", \"height\"],\n    minWidth: [\"cdkConnectedOverlayMinWidth\", \"minWidth\"],\n    minHeight: [\"cdkConnectedOverlayMinHeight\", \"minHeight\"],\n    backdropClass: [\"cdkConnectedOverlayBackdropClass\", \"backdropClass\"],\n    panelClass: [\"cdkConnectedOverlayPanelClass\", \"panelClass\"],\n    viewportMargin: [\"cdkConnectedOverlayViewportMargin\", \"viewportMargin\"],\n    scrollStrategy: [\"cdkConnectedOverlayScrollStrategy\", \"scrollStrategy\"],\n    open: [\"cdkConnectedOverlayOpen\", \"open\"],\n    disableClose: [\"cdkConnectedOverlayDisableClose\", \"disableClose\"],\n    transformOriginSelector: [\"cdkConnectedOverlayTransformOriginOn\", \"transformOriginSelector\"],\n    hasBackdrop: [\"cdkConnectedOverlayHasBackdrop\", \"hasBackdrop\"],\n    lockPosition: [\"cdkConnectedOverlayLockPosition\", \"lockPosition\"],\n    flexibleDimensions: [\"cdkConnectedOverlayFlexibleDimensions\", \"flexibleDimensions\"],\n    growAfterOpen: [\"cdkConnectedOverlayGrowAfterOpen\", \"growAfterOpen\"],\n    push: [\"cdkConnectedOverlayPush\", \"push\"]\n  },\n  outputs: {\n    backdropClick: \"backdropClick\",\n    positionChange: \"positionChange\",\n    attach: \"attach\",\n    detach: \"detach\",\n    overlayKeydown: \"overlayKeydown\",\n    overlayOutsideClick: \"overlayOutsideClick\"\n  },\n  exportAs: [\"cdkConnectedOverlay\"],\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkConnectedOverlay, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n      exportAs: 'cdkConnectedOverlay'\n    }]\n  }], function () {\n    return [{\n      type: Overlay\n    }, {\n      type: i0.TemplateRef\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: i5.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    origin: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOrigin']\n    }],\n    positions: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPositions']\n    }],\n    positionStrategy: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPositionStrategy']\n    }],\n    offsetX: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOffsetX']\n    }],\n    offsetY: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOffsetY']\n    }],\n    width: [{\n      type: Input,\n      args: ['cdkConnectedOverlayWidth']\n    }],\n    height: [{\n      type: Input,\n      args: ['cdkConnectedOverlayHeight']\n    }],\n    minWidth: [{\n      type: Input,\n      args: ['cdkConnectedOverlayMinWidth']\n    }],\n    minHeight: [{\n      type: Input,\n      args: ['cdkConnectedOverlayMinHeight']\n    }],\n    backdropClass: [{\n      type: Input,\n      args: ['cdkConnectedOverlayBackdropClass']\n    }],\n    panelClass: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPanelClass']\n    }],\n    viewportMargin: [{\n      type: Input,\n      args: ['cdkConnectedOverlayViewportMargin']\n    }],\n    scrollStrategy: [{\n      type: Input,\n      args: ['cdkConnectedOverlayScrollStrategy']\n    }],\n    open: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOpen']\n    }],\n    disableClose: [{\n      type: Input,\n      args: ['cdkConnectedOverlayDisableClose']\n    }],\n    transformOriginSelector: [{\n      type: Input,\n      args: ['cdkConnectedOverlayTransformOriginOn']\n    }],\n    hasBackdrop: [{\n      type: Input,\n      args: ['cdkConnectedOverlayHasBackdrop']\n    }],\n    lockPosition: [{\n      type: Input,\n      args: ['cdkConnectedOverlayLockPosition']\n    }],\n    flexibleDimensions: [{\n      type: Input,\n      args: ['cdkConnectedOverlayFlexibleDimensions']\n    }],\n    growAfterOpen: [{\n      type: Input,\n      args: ['cdkConnectedOverlayGrowAfterOpen']\n    }],\n    push: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPush']\n    }],\n    backdropClick: [{\n      type: Output\n    }],\n    positionChange: [{\n      type: Output\n    }],\n    attach: [{\n      type: Output\n    }],\n    detach: [{\n      type: Output\n    }],\n    overlayKeydown: [{\n      type: Output\n    }],\n    overlayOutsideClick: [{\n      type: Output\n    }]\n  });\n})();\n/** @docs-private */\n\n\nfunction CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\n\n\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n  provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nclass OverlayModule {}\n\nOverlayModule.ɵfac = function OverlayModule_Factory(t) {\n  return new (t || OverlayModule)();\n};\n\nOverlayModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: OverlayModule,\n  declarations: [CdkConnectedOverlay, CdkOverlayOrigin],\n  imports: [BidiModule, PortalModule, ScrollingModule],\n  exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule]\n});\nOverlayModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER],\n  imports: [BidiModule, PortalModule, ScrollingModule, ScrollingModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule, PortalModule, ScrollingModule],\n      exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule],\n      declarations: [CdkConnectedOverlay, CdkOverlayOrigin],\n      providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\n\n\nclass FullscreenOverlayContainer extends OverlayContainer {\n  constructor(_document, platform) {\n    super(_document, platform);\n  }\n\n  ngOnDestroy() {\n    super.ngOnDestroy();\n\n    if (this._fullScreenEventName && this._fullScreenListener) {\n      this._document.removeEventListener(this._fullScreenEventName, this._fullScreenListener);\n    }\n  }\n\n  _createContainer() {\n    super._createContainer();\n\n    this._adjustParentForFullscreenChange();\n\n    this._addFullscreenChangeListener(() => this._adjustParentForFullscreenChange());\n  }\n\n  _adjustParentForFullscreenChange() {\n    if (!this._containerElement) {\n      return;\n    }\n\n    const fullscreenElement = this.getFullscreenElement();\n    const parent = fullscreenElement || this._document.body;\n    parent.appendChild(this._containerElement);\n  }\n\n  _addFullscreenChangeListener(fn) {\n    const eventName = this._getEventName();\n\n    if (eventName) {\n      if (this._fullScreenListener) {\n        this._document.removeEventListener(eventName, this._fullScreenListener);\n      }\n\n      this._document.addEventListener(eventName, fn);\n\n      this._fullScreenListener = fn;\n    }\n  }\n\n  _getEventName() {\n    if (!this._fullScreenEventName) {\n      const _document = this._document;\n\n      if (_document.fullscreenEnabled) {\n        this._fullScreenEventName = 'fullscreenchange';\n      } else if (_document.webkitFullscreenEnabled) {\n        this._fullScreenEventName = 'webkitfullscreenchange';\n      } else if (_document.mozFullScreenEnabled) {\n        this._fullScreenEventName = 'mozfullscreenchange';\n      } else if (_document.msFullscreenEnabled) {\n        this._fullScreenEventName = 'MSFullscreenChange';\n      }\n    }\n\n    return this._fullScreenEventName;\n  }\n  /**\n   * When the page is put into fullscreen mode, a specific element is specified.\n   * Only that element and its children are visible when in fullscreen mode.\n   */\n\n\n  getFullscreenElement() {\n    const _document = this._document;\n    return _document.fullscreenElement || _document.webkitFullscreenElement || _document.mozFullScreenElement || _document.msFullscreenElement || null;\n  }\n\n}\n\nFullscreenOverlayContainer.ɵfac = function FullscreenOverlayContainer_Factory(t) {\n  return new (t || FullscreenOverlayContainer)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform));\n};\n\nFullscreenOverlayContainer.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: FullscreenOverlayContainer,\n  factory: FullscreenOverlayContainer.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FullscreenOverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1$1.Platform\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { BlockScrollStrategy, CdkConnectedOverlay, CdkOverlayOrigin, CloseScrollStrategy, ConnectedOverlayPositionChange, ConnectionPositionPair, FlexibleConnectedPositionStrategy, FullscreenOverlayContainer, GlobalPositionStrategy, NoopScrollStrategy, Overlay, OverlayConfig, OverlayContainer, OverlayKeyboardDispatcher, OverlayModule, OverlayOutsideClickDispatcher, OverlayPositionBuilder, OverlayRef, RepositionScrollStrategy, STANDARD_DROPDOWN_ADJACENT_POSITIONS, STANDARD_DROPDOWN_BELOW_POSITIONS, ScrollStrategyOptions, ScrollingVisibility, validateHorizontalPosition, validateVerticalPosition };", "map": {"version": 3, "names": ["i1", "ScrollingModule", "CdkScrollable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewportRuler", "i6", "DOCUMENT", "i0", "Injectable", "Inject", "Optional", "ElementRef", "ApplicationRef", "ANIMATION_MODULE_TYPE", "InjectionToken", "Directive", "EventEmitter", "Input", "Output", "NgModule", "coerceCssPixelValue", "coerce<PERSON><PERSON><PERSON>", "coerceBooleanProperty", "i1$1", "supportsScrollBehavior", "_getEventTarget", "_isTestEnvironment", "i5", "BidiModule", "DomPortalOutlet", "TemplatePortal", "PortalModule", "Subject", "Subscription", "merge", "take", "takeUntil", "<PERSON><PERSON><PERSON><PERSON>", "ESCAPE", "hasModifierKey", "scrollBehaviorSupported", "BlockScrollStrategy", "constructor", "_viewportRuler", "document", "_previousHTMLStyles", "top", "left", "_isEnabled", "_document", "attach", "enable", "_canBeEnabled", "root", "documentElement", "_previousScrollPosition", "getViewportScrollPosition", "style", "classList", "add", "disable", "html", "body", "htmlStyle", "bodyStyle", "previousHtmlScrollBehavior", "scroll<PERSON>eh<PERSON>or", "previousBodyScrollBehavior", "remove", "window", "scroll", "contains", "viewport", "getViewportSize", "scrollHeight", "height", "scrollWidth", "width", "getMatScrollStrategyAlreadyAttachedError", "Error", "CloseScrollStrategy", "_scrollDispatcher", "_ngZone", "_config", "_scrollSubscription", "_detach", "_overlayRef", "has<PERSON>tta<PERSON>", "run", "detach", "overlayRef", "ngDevMode", "stream", "scrolled", "threshold", "_initialScrollPosition", "subscribe", "scrollPosition", "Math", "abs", "updatePosition", "unsubscribe", "NoopScrollStrategy", "isElementScrolledOutsideView", "element", "scrollContainers", "some", "containerBounds", "outsideAbove", "bottom", "outsideBelow", "outsideLeft", "right", "outsideRight", "isElementClippedByScrolling", "scrollContainerRect", "clippedAbove", "<PERSON><PERSON><PERSON><PERSON>", "clippedLeft", "clippedRight", "RepositionScrollStrategy", "throttle", "scrollThrottle", "autoClose", "overlayRect", "overlayElement", "getBoundingClientRect", "parentRects", "ScrollStrategyOptions", "noop", "close", "config", "block", "reposition", "ɵfac", "NgZone", "ɵprov", "type", "args", "providedIn", "undefined", "decorators", "OverlayConfig", "scrollStrategy", "panelClass", "hasBackdrop", "backdropClass", "disposeOnNavigation", "config<PERSON><PERSON><PERSON>", "Object", "keys", "key", "ConnectionPositionPair", "origin", "overlay", "offsetX", "offsetY", "originX", "originY", "overlayX", "overlayY", "ScrollingVisibility", "ConnectedOverlayPositionChange", "connectionPair", "scrollableViewProperties", "validateVerticalPosition", "property", "value", "validateHorizontalPosition", "BaseOverlayDispatcher", "_attachedOverlays", "ngOnDestroy", "push", "index", "indexOf", "splice", "length", "OverlayKeyboardDispatcher", "_keydownListener", "event", "overlays", "i", "_keydownEvents", "observers", "keydownEvents", "next", "_isAttached", "runOutsideAngular", "addEventListener", "removeEventListener", "OverlayOutsideClickDispatcher", "_platform", "_cursorStyleIsSet", "_pointerDownListener", "_pointerDownEventTarget", "_clickListener", "target", "slice", "_outsidePointerEvents", "outsidePointerEvents", "_addEventListeners", "IOS", "_cursorOriginalV<PERSON>ue", "cursor", "Platform", "OverlayContainer", "_containerElement", "getContainerElement", "_createContainer", "containerClass", "<PERSON><PERSON><PERSON><PERSON>", "oppositePlatformContainers", "querySelectorAll", "container", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "OverlayRef", "_portalOutlet", "_host", "_pane", "_keyboardDispatcher", "_location", "_outsideClickDis<PERSON>tcher", "_animationsDisabled", "_backdropElement", "_backdropClick", "_attachments", "_detachments", "_locationChanges", "EMPTY", "_backdropClickHandler", "_backdropTransitionendHandler", "_disposeBackdrop", "_scrollStrategy", "_positionStrategy", "positionStrategy", "backdropElement", "hostElement", "portal", "parentElement", "_previousHostParent", "attachResult", "_updateStackingOrder", "_updateElementSize", "_updateElementDirection", "onStable", "pipe", "_togglePointerEvents", "_attachBackdrop", "_toggleClasses", "dispose", "onDestroy", "Promise", "resolve", "then", "detachBackdrop", "detachmentResult", "_detachContentWhenStable", "isAttached", "_disposeScrollStrategy", "complete", "backdropClick", "attachments", "detachments", "getConfig", "apply", "updatePositionStrategy", "strategy", "updateSize", "sizeConfig", "setDirection", "dir", "direction", "addPanelClass", "classes", "removePanelClass", "getDirection", "updateScrollStrategy", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "enablePointer", "pointerEvents", "showingClass", "insertBefore", "requestAnimationFrame", "nextS<PERSON>ling", "parentNode", "backdropToDetach", "_backdropTimeout", "setTimeout", "cssClasses", "isAdd", "filter", "c", "subscription", "children", "backdrop", "clearTimeout", "boundingBoxClass", "cssUnitPattern", "FlexibleConnectedPositionStrategy", "connectedTo", "_overlayContainer", "_lastBoundingBoxSize", "_isPushed", "_canPush", "_growAfterOpen", "_hasFlexibleDimensions", "_positionLocked", "_viewportMargin", "_scrollables", "_preferredPositions", "_positionChanges", "_resizeSubscription", "_offsetX", "_offsetY", "_appliedPanelClasses", "position<PERSON><PERSON>es", "<PERSON><PERSON><PERSON><PERSON>", "positions", "_validatePositions", "_boundingBox", "_isDisposed", "_isInitialRender", "_lastPosition", "change", "reapplyLastPosition", "_clearPanelClasses", "_resetOverlayElementStyles", "_resetBoundingBoxStyles", "_viewportRect", "_getNarrowedViewportRect", "_originRect", "_getOriginRect", "_overlayRect", "_containerRect", "originRect", "viewportRect", "containerRect", "flexibleFits", "fallback", "pos", "originPoint", "_getOriginPoint", "overlayPoint", "_getOverlayPoint", "overlayFit", "_getOverlayFit", "isCompletelyWithinViewport", "_applyPosition", "_canFitWithFlexibleDimensions", "position", "boundingBoxRect", "_calculateBoundingBoxRect", "visibleArea", "bestFit", "bestScore", "fit", "score", "weight", "_previousPushAmount", "extendStyles", "alignItems", "justifyContent", "lastPosition", "withScrollableContainers", "scrollables", "withPositions", "withViewportMargin", "margin", "withFlexibleDimensions", "flexibleDimensions", "withGrowAfterOpen", "growAfterOpen", "with<PERSON><PERSON>", "canPush", "withLockedPosition", "isLocked", "_origin", "withDefaultOffsetX", "offset", "withDefaultOffsetY", "withTransformOriginOn", "selector", "_transformOriginSelector", "x", "startX", "_isRtl", "endX", "y", "overlayStartX", "overlayStartY", "point", "rawOverlayRect", "getRoundedBoundingClientRect", "_getOffset", "leftOverflow", "rightOverflow", "topOverflow", "bottomOverflow", "visibleWidth", "_subtractOverflows", "visibleHeight", "fitsInViewportVertically", "fitsInViewportHorizontally", "availableHeight", "availableWidth", "getPixelValue", "verticalFit", "horizontalFit", "_pushOverlayOnScreen", "start", "overflowRight", "max", "overflowBottom", "overflowTop", "overflowLeft", "pushX", "pushY", "_setTransformOrigin", "_setOverlayElementStyles", "_setBoundingBoxStyles", "_addPanelClasses", "_getScrollVisibility", "changeEvent", "elements", "xOrigin", "y<PERSON><PERSON><PERSON>", "transform<PERSON><PERSON>in", "isRtl", "smallestDistanceToViewportEdge", "min", "previousHeight", "isBoundedByRightViewportEdge", "isBoundedByLeftViewportEdge", "previousWidth", "styles", "_hasExactPosition", "transform", "hasExactPosition", "hasFlexibleDimensions", "_getExactOverlayY", "_getExactOverlayX", "transformString", "trim", "documentHeight", "clientHeight", "horizontalStyleProperty", "documentWidth", "clientWidth", "originBounds", "overlayBounds", "scrollContainerBounds", "map", "scrollable", "getElementRef", "nativeElement", "isOriginClipped", "isOriginOutsideView", "isOverlayClipped", "isOverlayOutsideView", "overflows", "reduce", "currentValue", "currentOverflow", "axis", "for<PERSON>ach", "pair", "cssClass", "Element", "destination", "source", "hasOwnProperty", "input", "units", "split", "parseFloat", "clientRect", "floor", "STANDARD_DROPDOWN_BELOW_POSITIONS", "STANDARD_DROPDOWN_ADJACENT_POSITIONS", "wrapperClass", "GlobalPositionStrategy", "_cssPosition", "_topOffset", "_bottomOffset", "_alignItems", "_xPosition", "_xOffset", "_width", "_height", "end", "centerHorizontally", "centerVertically", "parentStyles", "shouldBeFlushHorizontally", "shouldBeFlushVertically", "xPosition", "xOffset", "marginLeft", "marginRight", "marginTop", "marginBottom", "parent", "OverlayPositionBuilder", "global", "flexibleConnectedTo", "nextUniqueId", "Overlay", "scrollStrategies", "_componentFactoryResolver", "_positionBuilder", "_injector", "_directionality", "_animationsModuleType", "create", "host", "_createHostElement", "pane", "_createPaneElement", "portalOutlet", "_createPortalOutlet", "overlayConfig", "id", "_appRef", "get", "ComponentFactoryResolver", "Injector", "Directionality", "Location", "defaultPositionList", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY", "CdkOverlayOrigin", "elementRef", "ɵdir", "exportAs", "CdkConnectedOverlay", "_overlay", "templateRef", "viewContainerRef", "scrollStrategyFactory", "_dir", "_hasBackdrop", "_lockPosition", "_flexibleDimensions", "_push", "_backdropSubscription", "_attachSubscription", "_detachSubscription", "_positionSubscription", "viewportMargin", "open", "disableClose", "positionChange", "overlayKeydown", "overlayOutsideClick", "_templatePortal", "_scrollStrategyFactory", "_position", "_updatePositionStrategy", "lockPosition", "ngOnChanges", "changes", "_attachOverlay", "_detachOverlay", "_createOverlay", "_buildConfig", "emit", "keyCode", "preventDefault", "_createPositionStrategy", "currentPosition", "_getFlexibleConnectedPositionStrategyOrigin", "transformOriginSelector", "TemplateRef", "ViewContainerRef", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "OverlayModule", "ɵmod", "ɵinj", "imports", "exports", "declarations", "providers", "FullscreenOverlayContainer", "platform", "_fullScreenEventName", "_fullScreenListener", "_adjustParentForFullscreenChange", "_addFullscreenChangeListener", "fullscreenElement", "getFullscreenElement", "fn", "eventName", "_getEventName", "fullscreenEnabled", "webkitFullscreenEnabled", "mozFullScreenEnabled", "msFullscreenEnabled", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement"], "sources": ["C:/Users/<USER>/Documents/viatech/Ecom/client/acs/node_modules/@angular/cdk/fesm2020/overlay.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/scrolling';\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nexport { CdkScrollable, ScrollDispatcher, ViewportRuler } from '@angular/cdk/scrolling';\nimport * as i6 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, Optional, ElementRef, ApplicationRef, ANIMATION_MODULE_TYPE, InjectionToken, Directive, EventEmitter, Input, Output, NgModule } from '@angular/core';\nimport { coerceCssPixelValue, coerceArray, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { supportsScrollBehavior, _getEventTarget, _isTestEnvironment } from '@angular/cdk/platform';\nimport * as i5 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { DomPortalOutlet, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { Subject, Subscription, merge } from 'rxjs';\nimport { take, takeUntil, takeWhile } from 'rxjs/operators';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst scrollBehaviorSupported = supportsScrollBehavior();\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\nclass BlockScrollStrategy {\n    constructor(_viewportRuler, document) {\n        this._viewportRuler = _viewportRuler;\n        this._previousHTMLStyles = { top: '', left: '' };\n        this._isEnabled = false;\n        this._document = document;\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach() { }\n    /** Blocks page-level scroll while the attached overlay is open. */\n    enable() {\n        if (this._canBeEnabled()) {\n            const root = this._document.documentElement;\n            this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition();\n            // Cache the previous inline styles in case the user had set them.\n            this._previousHTMLStyles.left = root.style.left || '';\n            this._previousHTMLStyles.top = root.style.top || '';\n            // Note: we're using the `html` node, instead of the `body`, because the `body` may\n            // have the user agent margin, whereas the `html` is guaranteed not to have one.\n            root.style.left = coerceCssPixelValue(-this._previousScrollPosition.left);\n            root.style.top = coerceCssPixelValue(-this._previousScrollPosition.top);\n            root.classList.add('cdk-global-scrollblock');\n            this._isEnabled = true;\n        }\n    }\n    /** Unblocks page-level scroll while the attached overlay is open. */\n    disable() {\n        if (this._isEnabled) {\n            const html = this._document.documentElement;\n            const body = this._document.body;\n            const htmlStyle = html.style;\n            const bodyStyle = body.style;\n            const previousHtmlScrollBehavior = htmlStyle.scrollBehavior || '';\n            const previousBodyScrollBehavior = bodyStyle.scrollBehavior || '';\n            this._isEnabled = false;\n            htmlStyle.left = this._previousHTMLStyles.left;\n            htmlStyle.top = this._previousHTMLStyles.top;\n            html.classList.remove('cdk-global-scrollblock');\n            // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n            // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n            // Note that we don't mutate the property if the browser doesn't support `scroll-behavior`,\n            // because it can throw off feature detections in `supportsScrollBehavior` which\n            // checks for `'scrollBehavior' in documentElement.style`.\n            if (scrollBehaviorSupported) {\n                htmlStyle.scrollBehavior = bodyStyle.scrollBehavior = 'auto';\n            }\n            window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n            if (scrollBehaviorSupported) {\n                htmlStyle.scrollBehavior = previousHtmlScrollBehavior;\n                bodyStyle.scrollBehavior = previousBodyScrollBehavior;\n            }\n        }\n    }\n    _canBeEnabled() {\n        // Since the scroll strategies can't be singletons, we have to use a global CSS class\n        // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n        // scrolling multiple times.\n        const html = this._document.documentElement;\n        if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n            return false;\n        }\n        const body = this._document.body;\n        const viewport = this._viewportRuler.getViewportSize();\n        return body.scrollHeight > viewport.height || body.scrollWidth > viewport.width;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\nfunction getMatScrollStrategyAlreadyAttachedError() {\n    return Error(`Scroll strategy has already been attached.`);\n}\n\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\nclass CloseScrollStrategy {\n    constructor(_scrollDispatcher, _ngZone, _viewportRuler, _config) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._ngZone = _ngZone;\n        this._viewportRuler = _viewportRuler;\n        this._config = _config;\n        this._scrollSubscription = null;\n        /** Detaches the overlay ref and disables the scroll strategy. */\n        this._detach = () => {\n            this.disable();\n            if (this._overlayRef.hasAttached()) {\n                this._ngZone.run(() => this._overlayRef.detach());\n            }\n        };\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatScrollStrategyAlreadyAttachedError();\n        }\n        this._overlayRef = overlayRef;\n    }\n    /** Enables the closing of the attached overlay on scroll. */\n    enable() {\n        if (this._scrollSubscription) {\n            return;\n        }\n        const stream = this._scrollDispatcher.scrolled(0);\n        if (this._config && this._config.threshold && this._config.threshold > 1) {\n            this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n            this._scrollSubscription = stream.subscribe(() => {\n                const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n                if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config.threshold) {\n                    this._detach();\n                }\n                else {\n                    this._overlayRef.updatePosition();\n                }\n            });\n        }\n        else {\n            this._scrollSubscription = stream.subscribe(this._detach);\n        }\n    }\n    /** Disables the closing the attached overlay on scroll. */\n    disable() {\n        if (this._scrollSubscription) {\n            this._scrollSubscription.unsubscribe();\n            this._scrollSubscription = null;\n        }\n    }\n    detach() {\n        this.disable();\n        this._overlayRef = null;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Scroll strategy that doesn't do anything. */\nclass NoopScrollStrategy {\n    /** Does nothing, as this scroll strategy is a no-op. */\n    enable() { }\n    /** Does nothing, as this scroll strategy is a no-op. */\n    disable() { }\n    /** Does nothing, as this scroll strategy is a no-op. */\n    attach() { }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\nfunction isElementScrolledOutsideView(element, scrollContainers) {\n    return scrollContainers.some(containerBounds => {\n        const outsideAbove = element.bottom < containerBounds.top;\n        const outsideBelow = element.top > containerBounds.bottom;\n        const outsideLeft = element.right < containerBounds.left;\n        const outsideRight = element.left > containerBounds.right;\n        return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n    });\n}\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\nfunction isElementClippedByScrolling(element, scrollContainers) {\n    return scrollContainers.some(scrollContainerRect => {\n        const clippedAbove = element.top < scrollContainerRect.top;\n        const clippedBelow = element.bottom > scrollContainerRect.bottom;\n        const clippedLeft = element.left < scrollContainerRect.left;\n        const clippedRight = element.right > scrollContainerRect.right;\n        return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n    });\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\nclass RepositionScrollStrategy {\n    constructor(_scrollDispatcher, _viewportRuler, _ngZone, _config) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._viewportRuler = _viewportRuler;\n        this._ngZone = _ngZone;\n        this._config = _config;\n        this._scrollSubscription = null;\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatScrollStrategyAlreadyAttachedError();\n        }\n        this._overlayRef = overlayRef;\n    }\n    /** Enables repositioning of the attached overlay on scroll. */\n    enable() {\n        if (!this._scrollSubscription) {\n            const throttle = this._config ? this._config.scrollThrottle : 0;\n            this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n                this._overlayRef.updatePosition();\n                // TODO(crisbeto): make `close` on by default once all components can handle it.\n                if (this._config && this._config.autoClose) {\n                    const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n                    const { width, height } = this._viewportRuler.getViewportSize();\n                    // TODO(crisbeto): include all ancestor scroll containers here once\n                    // we have a way of exposing the trigger element to the scroll strategy.\n                    const parentRects = [{ width, height, bottom: height, right: width, top: 0, left: 0 }];\n                    if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n                        this.disable();\n                        this._ngZone.run(() => this._overlayRef.detach());\n                    }\n                }\n            });\n        }\n    }\n    /** Disables repositioning of the attached overlay on scroll. */\n    disable() {\n        if (this._scrollSubscription) {\n            this._scrollSubscription.unsubscribe();\n            this._scrollSubscription = null;\n        }\n    }\n    detach() {\n        this.disable();\n        this._overlayRef = null;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\nclass ScrollStrategyOptions {\n    constructor(_scrollDispatcher, _viewportRuler, _ngZone, document) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._viewportRuler = _viewportRuler;\n        this._ngZone = _ngZone;\n        /** Do nothing on scroll. */\n        this.noop = () => new NoopScrollStrategy();\n        /**\n         * Close the overlay as soon as the user scrolls.\n         * @param config Configuration to be used inside the scroll strategy.\n         */\n        this.close = (config) => new CloseScrollStrategy(this._scrollDispatcher, this._ngZone, this._viewportRuler, config);\n        /** Block scrolling. */\n        this.block = () => new BlockScrollStrategy(this._viewportRuler, this._document);\n        /**\n         * Update the overlay's position on scroll.\n         * @param config Configuration to be used inside the scroll strategy.\n         * Allows debouncing the reposition calls.\n         */\n        this.reposition = (config) => new RepositionScrollStrategy(this._scrollDispatcher, this._viewportRuler, this._ngZone, config);\n        this._document = document;\n    }\n}\nScrollStrategyOptions.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: ScrollStrategyOptions, deps: [{ token: i1.ScrollDispatcher }, { token: i1.ViewportRuler }, { token: i0.NgZone }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nScrollStrategyOptions.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: ScrollStrategyOptions, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: ScrollStrategyOptions, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.ScrollDispatcher }, { type: i1.ViewportRuler }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Initial configuration used when creating an overlay. */\nclass OverlayConfig {\n    constructor(config) {\n        /** Strategy to be used when handling scroll events while the overlay is open. */\n        this.scrollStrategy = new NoopScrollStrategy();\n        /** Custom class to add to the overlay pane. */\n        this.panelClass = '';\n        /** Whether the overlay has a backdrop. */\n        this.hasBackdrop = false;\n        /** Custom class to add to the backdrop */\n        this.backdropClass = 'cdk-overlay-dark-backdrop';\n        /**\n         * Whether the overlay should be disposed of when the user goes backwards/forwards in history.\n         * Note that this usually doesn't include clicking on links (unless the user is using\n         * the `HashLocationStrategy`).\n         */\n        this.disposeOnNavigation = false;\n        if (config) {\n            // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n            // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n            // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n            const configKeys = Object.keys(config);\n            for (const key of configKeys) {\n                if (config[key] !== undefined) {\n                    // TypeScript, as of version 3.5, sees the left-hand-side of this expression\n                    // as \"I don't know *which* key this is, so the only valid value is the intersection\n                    // of all the possible values.\" In this case, that happens to be `undefined`. TypeScript\n                    // is not smart enough to see that the right-hand-side is actually an access of the same\n                    // exact type with the same exact key, meaning that the value type must be identical.\n                    // So we use `any` to work around this.\n                    this[key] = config[key];\n                }\n            }\n        }\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** The points of the origin element and the overlay element to connect. */\nclass ConnectionPositionPair {\n    constructor(origin, overlay, \n    /** Offset along the X axis. */\n    offsetX, \n    /** Offset along the Y axis. */\n    offsetY, \n    /** Class(es) to be applied to the panel while this position is active. */\n    panelClass) {\n        this.offsetX = offsetX;\n        this.offsetY = offsetY;\n        this.panelClass = panelClass;\n        this.originX = origin.originX;\n        this.originY = origin.originY;\n        this.overlayX = overlay.overlayX;\n        this.overlayY = overlay.overlayY;\n    }\n}\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\nclass ScrollingVisibility {\n}\n/** The change event emitted by the strategy when a fallback position is used. */\nclass ConnectedOverlayPositionChange {\n    constructor(\n    /** The position used as a result of this change. */\n    connectionPair, \n    /** @docs-private */\n    scrollableViewProperties) {\n        this.connectionPair = connectionPair;\n        this.scrollableViewProperties = scrollableViewProperties;\n    }\n}\n/**\n * Validates whether a vertical position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateVerticalPosition(property, value) {\n    if (value !== 'top' && value !== 'bottom' && value !== 'center') {\n        throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` +\n            `Expected \"top\", \"bottom\" or \"center\".`);\n    }\n}\n/**\n * Validates whether a horizontal position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateHorizontalPosition(property, value) {\n    if (value !== 'start' && value !== 'end' && value !== 'center') {\n        throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` +\n            `Expected \"start\", \"end\" or \"center\".`);\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Service for dispatching events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass BaseOverlayDispatcher {\n    constructor(document) {\n        /** Currently attached overlays in the order they were attached. */\n        this._attachedOverlays = [];\n        this._document = document;\n    }\n    ngOnDestroy() {\n        this.detach();\n    }\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        // Ensure that we don't get the same overlay multiple times.\n        this.remove(overlayRef);\n        this._attachedOverlays.push(overlayRef);\n    }\n    /** Remove an overlay from the list of attached overlay refs. */\n    remove(overlayRef) {\n        const index = this._attachedOverlays.indexOf(overlayRef);\n        if (index > -1) {\n            this._attachedOverlays.splice(index, 1);\n        }\n        // Remove the global listener once there are no more overlays.\n        if (this._attachedOverlays.length === 0) {\n            this.detach();\n        }\n    }\n}\nBaseOverlayDispatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: BaseOverlayDispatcher, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nBaseOverlayDispatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: BaseOverlayDispatcher, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: BaseOverlayDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayKeyboardDispatcher extends BaseOverlayDispatcher {\n    constructor(document, \n    /** @breaking-change 14.0.0 _ngZone will be required. */\n    _ngZone) {\n        super(document);\n        this._ngZone = _ngZone;\n        /** Keyboard event listener that will be attached to the body. */\n        this._keydownListener = (event) => {\n            const overlays = this._attachedOverlays;\n            for (let i = overlays.length - 1; i > -1; i--) {\n                // Dispatch the keydown event to the top overlay which has subscribers to its keydown events.\n                // We want to target the most recent overlay, rather than trying to match where the event came\n                // from, because some components might open an overlay, but keep focus on a trigger element\n                // (e.g. for select and autocomplete). We skip overlays without keydown event subscriptions,\n                // because we don't want overlays that don't handle keyboard events to block the ones below\n                // them that do.\n                if (overlays[i]._keydownEvents.observers.length > 0) {\n                    const keydownEvents = overlays[i]._keydownEvents;\n                    /** @breaking-change 14.0.0 _ngZone will be required. */\n                    if (this._ngZone) {\n                        this._ngZone.run(() => keydownEvents.next(event));\n                    }\n                    else {\n                        keydownEvents.next(event);\n                    }\n                    break;\n                }\n            }\n        };\n    }\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        super.add(overlayRef);\n        // Lazily start dispatcher once first overlay is added\n        if (!this._isAttached) {\n            /** @breaking-change 14.0.0 _ngZone will be required. */\n            if (this._ngZone) {\n                this._ngZone.runOutsideAngular(() => this._document.body.addEventListener('keydown', this._keydownListener));\n            }\n            else {\n                this._document.body.addEventListener('keydown', this._keydownListener);\n            }\n            this._isAttached = true;\n        }\n    }\n    /** Detaches the global keyboard event listener. */\n    detach() {\n        if (this._isAttached) {\n            this._document.body.removeEventListener('keydown', this._keydownListener);\n            this._isAttached = false;\n        }\n    }\n}\nOverlayKeyboardDispatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: OverlayKeyboardDispatcher, deps: [{ token: DOCUMENT }, { token: i0.NgZone, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nOverlayKeyboardDispatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: OverlayKeyboardDispatcher, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: OverlayKeyboardDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.NgZone, decorators: [{\n                    type: Optional\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Service for dispatching mouse click events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayOutsideClickDispatcher extends BaseOverlayDispatcher {\n    constructor(document, _platform, \n    /** @breaking-change 14.0.0 _ngZone will be required. */\n    _ngZone) {\n        super(document);\n        this._platform = _platform;\n        this._ngZone = _ngZone;\n        this._cursorStyleIsSet = false;\n        /** Store pointerdown event target to track origin of click. */\n        this._pointerDownListener = (event) => {\n            this._pointerDownEventTarget = _getEventTarget(event);\n        };\n        /** Click event listener that will be attached to the body propagate phase. */\n        this._clickListener = (event) => {\n            const target = _getEventTarget(event);\n            // In case of a click event, we want to check the origin of the click\n            // (e.g. in case where a user starts a click inside the overlay and\n            // releases the click outside of it).\n            // This is done by using the event target of the preceding pointerdown event.\n            // Every click event caused by a pointer device has a preceding pointerdown\n            // event, unless the click was programmatically triggered (e.g. in a unit test).\n            const origin = event.type === 'click' && this._pointerDownEventTarget\n                ? this._pointerDownEventTarget\n                : target;\n            // Reset the stored pointerdown event target, to avoid having it interfere\n            // in subsequent events.\n            this._pointerDownEventTarget = null;\n            // We copy the array because the original may be modified asynchronously if the\n            // outsidePointerEvents listener decides to detach overlays resulting in index errors inside\n            // the for loop.\n            const overlays = this._attachedOverlays.slice();\n            // Dispatch the mouse event to the top overlay which has subscribers to its mouse events.\n            // We want to target all overlays for which the click could be considered as outside click.\n            // As soon as we reach an overlay for which the click is not outside click we break off\n            // the loop.\n            for (let i = overlays.length - 1; i > -1; i--) {\n                const overlayRef = overlays[i];\n                if (overlayRef._outsidePointerEvents.observers.length < 1 || !overlayRef.hasAttached()) {\n                    continue;\n                }\n                // If it's a click inside the overlay, just break - we should do nothing\n                // If it's an outside click (both origin and target of the click) dispatch the mouse event,\n                // and proceed with the next overlay\n                if (overlayRef.overlayElement.contains(target) ||\n                    overlayRef.overlayElement.contains(origin)) {\n                    break;\n                }\n                const outsidePointerEvents = overlayRef._outsidePointerEvents;\n                /** @breaking-change 14.0.0 _ngZone will be required. */\n                if (this._ngZone) {\n                    this._ngZone.run(() => outsidePointerEvents.next(event));\n                }\n                else {\n                    outsidePointerEvents.next(event);\n                }\n            }\n        };\n    }\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        super.add(overlayRef);\n        // Safari on iOS does not generate click events for non-interactive\n        // elements. However, we want to receive a click for any element outside\n        // the overlay. We can force a \"clickable\" state by setting\n        // `cursor: pointer` on the document body. See:\n        // https://developer.mozilla.org/en-US/docs/Web/API/Element/click_event#Safari_Mobile\n        // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n        if (!this._isAttached) {\n            const body = this._document.body;\n            /** @breaking-change 14.0.0 _ngZone will be required. */\n            if (this._ngZone) {\n                this._ngZone.runOutsideAngular(() => this._addEventListeners(body));\n            }\n            else {\n                this._addEventListeners(body);\n            }\n            // click event is not fired on iOS. To make element \"clickable\" we are\n            // setting the cursor to pointer\n            if (this._platform.IOS && !this._cursorStyleIsSet) {\n                this._cursorOriginalValue = body.style.cursor;\n                body.style.cursor = 'pointer';\n                this._cursorStyleIsSet = true;\n            }\n            this._isAttached = true;\n        }\n    }\n    /** Detaches the global keyboard event listener. */\n    detach() {\n        if (this._isAttached) {\n            const body = this._document.body;\n            body.removeEventListener('pointerdown', this._pointerDownListener, true);\n            body.removeEventListener('click', this._clickListener, true);\n            body.removeEventListener('auxclick', this._clickListener, true);\n            body.removeEventListener('contextmenu', this._clickListener, true);\n            if (this._platform.IOS && this._cursorStyleIsSet) {\n                body.style.cursor = this._cursorOriginalValue;\n                this._cursorStyleIsSet = false;\n            }\n            this._isAttached = false;\n        }\n    }\n    _addEventListeners(body) {\n        body.addEventListener('pointerdown', this._pointerDownListener, true);\n        body.addEventListener('click', this._clickListener, true);\n        body.addEventListener('auxclick', this._clickListener, true);\n        body.addEventListener('contextmenu', this._clickListener, true);\n    }\n}\nOverlayOutsideClickDispatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: OverlayOutsideClickDispatcher, deps: [{ token: DOCUMENT }, { token: i1$1.Platform }, { token: i0.NgZone, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nOverlayOutsideClickDispatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: OverlayOutsideClickDispatcher, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: OverlayOutsideClickDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1$1.Platform }, { type: i0.NgZone, decorators: [{\n                    type: Optional\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Container inside which all overlays will render. */\nclass OverlayContainer {\n    constructor(document, _platform) {\n        this._platform = _platform;\n        this._document = document;\n    }\n    ngOnDestroy() {\n        this._containerElement?.remove();\n    }\n    /**\n     * This method returns the overlay container element. It will lazily\n     * create the element the first time it is called to facilitate using\n     * the container in non-browser environments.\n     * @returns the container element\n     */\n    getContainerElement() {\n        if (!this._containerElement) {\n            this._createContainer();\n        }\n        return this._containerElement;\n    }\n    /**\n     * Create the overlay container element, which is simply a div\n     * with the 'cdk-overlay-container' class on the document body.\n     */\n    _createContainer() {\n        const containerClass = 'cdk-overlay-container';\n        // TODO(crisbeto): remove the testing check once we have an overlay testing\n        // module or Angular starts tearing down the testing `NgModule`. See:\n        // https://github.com/angular/angular/issues/18831\n        if (this._platform.isBrowser || _isTestEnvironment()) {\n            const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`);\n            // Remove any old containers from the opposite platform.\n            // This can happen when transitioning from the server to the client.\n            for (let i = 0; i < oppositePlatformContainers.length; i++) {\n                oppositePlatformContainers[i].remove();\n            }\n        }\n        const container = this._document.createElement('div');\n        container.classList.add(containerClass);\n        // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n        // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n        // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n        // To mitigate the problem we made it so that only containers from a different platform are\n        // cleared, but the side-effect was that people started depending on the overly-aggressive\n        // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n        // module which does the cleanup, we try to detect that we're in a test environment and we\n        // always clear the container. See #17006.\n        // TODO(crisbeto): remove the test environment check once we have an overlay testing module.\n        if (_isTestEnvironment()) {\n            container.setAttribute('platform', 'test');\n        }\n        else if (!this._platform.isBrowser) {\n            container.setAttribute('platform', 'server');\n        }\n        this._document.body.appendChild(container);\n        this._containerElement = container;\n    }\n}\nOverlayContainer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: OverlayContainer, deps: [{ token: DOCUMENT }, { token: i1$1.Platform }], target: i0.ɵɵFactoryTarget.Injectable });\nOverlayContainer.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: OverlayContainer, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: OverlayContainer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1$1.Platform }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n    constructor(_portalOutlet, _host, _pane, _config, _ngZone, _keyboardDispatcher, _document, _location, _outsideClickDispatcher, _animationsDisabled = false) {\n        this._portalOutlet = _portalOutlet;\n        this._host = _host;\n        this._pane = _pane;\n        this._config = _config;\n        this._ngZone = _ngZone;\n        this._keyboardDispatcher = _keyboardDispatcher;\n        this._document = _document;\n        this._location = _location;\n        this._outsideClickDispatcher = _outsideClickDispatcher;\n        this._animationsDisabled = _animationsDisabled;\n        this._backdropElement = null;\n        this._backdropClick = new Subject();\n        this._attachments = new Subject();\n        this._detachments = new Subject();\n        this._locationChanges = Subscription.EMPTY;\n        this._backdropClickHandler = (event) => this._backdropClick.next(event);\n        this._backdropTransitionendHandler = (event) => {\n            this._disposeBackdrop(event.target);\n        };\n        /** Stream of keydown events dispatched to this overlay. */\n        this._keydownEvents = new Subject();\n        /** Stream of mouse outside events dispatched to this overlay. */\n        this._outsidePointerEvents = new Subject();\n        if (_config.scrollStrategy) {\n            this._scrollStrategy = _config.scrollStrategy;\n            this._scrollStrategy.attach(this);\n        }\n        this._positionStrategy = _config.positionStrategy;\n    }\n    /** The overlay's HTML element */\n    get overlayElement() {\n        return this._pane;\n    }\n    /** The overlay's backdrop HTML element. */\n    get backdropElement() {\n        return this._backdropElement;\n    }\n    /**\n     * Wrapper around the panel element. Can be used for advanced\n     * positioning where a wrapper with specific styling is\n     * required around the overlay pane.\n     */\n    get hostElement() {\n        return this._host;\n    }\n    /**\n     * Attaches content, given via a Portal, to the overlay.\n     * If the overlay is configured to have a backdrop, it will be created.\n     *\n     * @param portal Portal instance to which to attach the overlay.\n     * @returns The portal attachment result.\n     */\n    attach(portal) {\n        // Insert the host into the DOM before attaching the portal, otherwise\n        // the animations module will skip animations on repeat attachments.\n        if (!this._host.parentElement && this._previousHostParent) {\n            this._previousHostParent.appendChild(this._host);\n        }\n        const attachResult = this._portalOutlet.attach(portal);\n        if (this._positionStrategy) {\n            this._positionStrategy.attach(this);\n        }\n        this._updateStackingOrder();\n        this._updateElementSize();\n        this._updateElementDirection();\n        if (this._scrollStrategy) {\n            this._scrollStrategy.enable();\n        }\n        // Update the position once the zone is stable so that the overlay will be fully rendered\n        // before attempting to position it, as the position may depend on the size of the rendered\n        // content.\n        this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n            // The overlay could've been detached before the zone has stabilized.\n            if (this.hasAttached()) {\n                this.updatePosition();\n            }\n        });\n        // Enable pointer events for the overlay pane element.\n        this._togglePointerEvents(true);\n        if (this._config.hasBackdrop) {\n            this._attachBackdrop();\n        }\n        if (this._config.panelClass) {\n            this._toggleClasses(this._pane, this._config.panelClass, true);\n        }\n        // Only emit the `attachments` event once all other setup is done.\n        this._attachments.next();\n        // Track this overlay by the keyboard dispatcher\n        this._keyboardDispatcher.add(this);\n        if (this._config.disposeOnNavigation) {\n            this._locationChanges = this._location.subscribe(() => this.dispose());\n        }\n        this._outsideClickDispatcher.add(this);\n        // TODO(crisbeto): the null check is here, because the portal outlet returns `any`.\n        // We should be guaranteed for the result to be `ComponentRef | EmbeddedViewRef`, but\n        // `instanceof EmbeddedViewRef` doesn't appear to work at the moment.\n        if (typeof attachResult?.onDestroy === 'function') {\n            // In most cases we control the portal and we know when it is being detached so that\n            // we can finish the disposal process. The exception is if the user passes in a custom\n            // `ViewContainerRef` that isn't destroyed through the overlay API. Note that we use\n            // `detach` here instead of `dispose`, because we don't know if the user intends to\n            // reattach the overlay at a later point. It also has the advantage of waiting for animations.\n            attachResult.onDestroy(() => {\n                if (this.hasAttached()) {\n                    // We have to delay the `detach` call, because detaching immediately prevents\n                    // other destroy hooks from running. This is likely a framework bug similar to\n                    // https://github.com/angular/angular/issues/46119\n                    this._ngZone.runOutsideAngular(() => Promise.resolve().then(() => this.detach()));\n                }\n            });\n        }\n        return attachResult;\n    }\n    /**\n     * Detaches an overlay from a portal.\n     * @returns The portal detachment result.\n     */\n    detach() {\n        if (!this.hasAttached()) {\n            return;\n        }\n        this.detachBackdrop();\n        // When the overlay is detached, the pane element should disable pointer events.\n        // This is necessary because otherwise the pane element will cover the page and disable\n        // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n        this._togglePointerEvents(false);\n        if (this._positionStrategy && this._positionStrategy.detach) {\n            this._positionStrategy.detach();\n        }\n        if (this._scrollStrategy) {\n            this._scrollStrategy.disable();\n        }\n        const detachmentResult = this._portalOutlet.detach();\n        // Only emit after everything is detached.\n        this._detachments.next();\n        // Remove this overlay from keyboard dispatcher tracking.\n        this._keyboardDispatcher.remove(this);\n        // Keeping the host element in the DOM can cause scroll jank, because it still gets\n        // rendered, even though it's transparent and unclickable which is why we remove it.\n        this._detachContentWhenStable();\n        this._locationChanges.unsubscribe();\n        this._outsideClickDispatcher.remove(this);\n        return detachmentResult;\n    }\n    /** Cleans up the overlay from the DOM. */\n    dispose() {\n        const isAttached = this.hasAttached();\n        if (this._positionStrategy) {\n            this._positionStrategy.dispose();\n        }\n        this._disposeScrollStrategy();\n        this._disposeBackdrop(this._backdropElement);\n        this._locationChanges.unsubscribe();\n        this._keyboardDispatcher.remove(this);\n        this._portalOutlet.dispose();\n        this._attachments.complete();\n        this._backdropClick.complete();\n        this._keydownEvents.complete();\n        this._outsidePointerEvents.complete();\n        this._outsideClickDispatcher.remove(this);\n        this._host?.remove();\n        this._previousHostParent = this._pane = this._host = null;\n        if (isAttached) {\n            this._detachments.next();\n        }\n        this._detachments.complete();\n    }\n    /** Whether the overlay has attached content. */\n    hasAttached() {\n        return this._portalOutlet.hasAttached();\n    }\n    /** Gets an observable that emits when the backdrop has been clicked. */\n    backdropClick() {\n        return this._backdropClick;\n    }\n    /** Gets an observable that emits when the overlay has been attached. */\n    attachments() {\n        return this._attachments;\n    }\n    /** Gets an observable that emits when the overlay has been detached. */\n    detachments() {\n        return this._detachments;\n    }\n    /** Gets an observable of keydown events targeted to this overlay. */\n    keydownEvents() {\n        return this._keydownEvents;\n    }\n    /** Gets an observable of pointer events targeted outside this overlay. */\n    outsidePointerEvents() {\n        return this._outsidePointerEvents;\n    }\n    /** Gets the current overlay configuration, which is immutable. */\n    getConfig() {\n        return this._config;\n    }\n    /** Updates the position of the overlay based on the position strategy. */\n    updatePosition() {\n        if (this._positionStrategy) {\n            this._positionStrategy.apply();\n        }\n    }\n    /** Switches to a new position strategy and updates the overlay position. */\n    updatePositionStrategy(strategy) {\n        if (strategy === this._positionStrategy) {\n            return;\n        }\n        if (this._positionStrategy) {\n            this._positionStrategy.dispose();\n        }\n        this._positionStrategy = strategy;\n        if (this.hasAttached()) {\n            strategy.attach(this);\n            this.updatePosition();\n        }\n    }\n    /** Update the size properties of the overlay. */\n    updateSize(sizeConfig) {\n        this._config = { ...this._config, ...sizeConfig };\n        this._updateElementSize();\n    }\n    /** Sets the LTR/RTL direction for the overlay. */\n    setDirection(dir) {\n        this._config = { ...this._config, direction: dir };\n        this._updateElementDirection();\n    }\n    /** Add a CSS class or an array of classes to the overlay pane. */\n    addPanelClass(classes) {\n        if (this._pane) {\n            this._toggleClasses(this._pane, classes, true);\n        }\n    }\n    /** Remove a CSS class or an array of classes from the overlay pane. */\n    removePanelClass(classes) {\n        if (this._pane) {\n            this._toggleClasses(this._pane, classes, false);\n        }\n    }\n    /**\n     * Returns the layout direction of the overlay panel.\n     */\n    getDirection() {\n        const direction = this._config.direction;\n        if (!direction) {\n            return 'ltr';\n        }\n        return typeof direction === 'string' ? direction : direction.value;\n    }\n    /** Switches to a new scroll strategy. */\n    updateScrollStrategy(strategy) {\n        if (strategy === this._scrollStrategy) {\n            return;\n        }\n        this._disposeScrollStrategy();\n        this._scrollStrategy = strategy;\n        if (this.hasAttached()) {\n            strategy.attach(this);\n            strategy.enable();\n        }\n    }\n    /** Updates the text direction of the overlay panel. */\n    _updateElementDirection() {\n        this._host.setAttribute('dir', this.getDirection());\n    }\n    /** Updates the size of the overlay element based on the overlay config. */\n    _updateElementSize() {\n        if (!this._pane) {\n            return;\n        }\n        const style = this._pane.style;\n        style.width = coerceCssPixelValue(this._config.width);\n        style.height = coerceCssPixelValue(this._config.height);\n        style.minWidth = coerceCssPixelValue(this._config.minWidth);\n        style.minHeight = coerceCssPixelValue(this._config.minHeight);\n        style.maxWidth = coerceCssPixelValue(this._config.maxWidth);\n        style.maxHeight = coerceCssPixelValue(this._config.maxHeight);\n    }\n    /** Toggles the pointer events for the overlay pane element. */\n    _togglePointerEvents(enablePointer) {\n        this._pane.style.pointerEvents = enablePointer ? '' : 'none';\n    }\n    /** Attaches a backdrop for this overlay. */\n    _attachBackdrop() {\n        const showingClass = 'cdk-overlay-backdrop-showing';\n        this._backdropElement = this._document.createElement('div');\n        this._backdropElement.classList.add('cdk-overlay-backdrop');\n        if (this._animationsDisabled) {\n            this._backdropElement.classList.add('cdk-overlay-backdrop-noop-animation');\n        }\n        if (this._config.backdropClass) {\n            this._toggleClasses(this._backdropElement, this._config.backdropClass, true);\n        }\n        // Insert the backdrop before the pane in the DOM order,\n        // in order to handle stacked overlays properly.\n        this._host.parentElement.insertBefore(this._backdropElement, this._host);\n        // Forward backdrop clicks such that the consumer of the overlay can perform whatever\n        // action desired when such a click occurs (usually closing the overlay).\n        this._backdropElement.addEventListener('click', this._backdropClickHandler);\n        // Add class to fade-in the backdrop after one frame.\n        if (!this._animationsDisabled && typeof requestAnimationFrame !== 'undefined') {\n            this._ngZone.runOutsideAngular(() => {\n                requestAnimationFrame(() => {\n                    if (this._backdropElement) {\n                        this._backdropElement.classList.add(showingClass);\n                    }\n                });\n            });\n        }\n        else {\n            this._backdropElement.classList.add(showingClass);\n        }\n    }\n    /**\n     * Updates the stacking order of the element, moving it to the top if necessary.\n     * This is required in cases where one overlay was detached, while another one,\n     * that should be behind it, was destroyed. The next time both of them are opened,\n     * the stacking will be wrong, because the detached element's pane will still be\n     * in its original DOM position.\n     */\n    _updateStackingOrder() {\n        if (this._host.nextSibling) {\n            this._host.parentNode.appendChild(this._host);\n        }\n    }\n    /** Detaches the backdrop (if any) associated with the overlay. */\n    detachBackdrop() {\n        const backdropToDetach = this._backdropElement;\n        if (!backdropToDetach) {\n            return;\n        }\n        if (this._animationsDisabled) {\n            this._disposeBackdrop(backdropToDetach);\n            return;\n        }\n        backdropToDetach.classList.remove('cdk-overlay-backdrop-showing');\n        this._ngZone.runOutsideAngular(() => {\n            backdropToDetach.addEventListener('transitionend', this._backdropTransitionendHandler);\n        });\n        // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n        // In this case we make it unclickable and we try to remove it after a delay.\n        backdropToDetach.style.pointerEvents = 'none';\n        // Run this outside the Angular zone because there's nothing that Angular cares about.\n        // If it were to run inside the Angular zone, every test that used Overlay would have to be\n        // either async or fakeAsync.\n        this._backdropTimeout = this._ngZone.runOutsideAngular(() => setTimeout(() => {\n            this._disposeBackdrop(backdropToDetach);\n        }, 500));\n    }\n    /** Toggles a single CSS class or an array of classes on an element. */\n    _toggleClasses(element, cssClasses, isAdd) {\n        const classes = coerceArray(cssClasses || []).filter(c => !!c);\n        if (classes.length) {\n            isAdd ? element.classList.add(...classes) : element.classList.remove(...classes);\n        }\n    }\n    /** Detaches the overlay content next time the zone stabilizes. */\n    _detachContentWhenStable() {\n        // Normally we wouldn't have to explicitly run this outside the `NgZone`, however\n        // if the consumer is using `zone-patch-rxjs`, the `Subscription.unsubscribe` call will\n        // be patched to run inside the zone, which will throw us into an infinite loop.\n        this._ngZone.runOutsideAngular(() => {\n            // We can't remove the host here immediately, because the overlay pane's content\n            // might still be animating. This stream helps us avoid interrupting the animation\n            // by waiting for the pane to become empty.\n            const subscription = this._ngZone.onStable\n                .pipe(takeUntil(merge(this._attachments, this._detachments)))\n                .subscribe(() => {\n                // Needs a couple of checks for the pane and host, because\n                // they may have been removed by the time the zone stabilizes.\n                if (!this._pane || !this._host || this._pane.children.length === 0) {\n                    if (this._pane && this._config.panelClass) {\n                        this._toggleClasses(this._pane, this._config.panelClass, false);\n                    }\n                    if (this._host && this._host.parentElement) {\n                        this._previousHostParent = this._host.parentElement;\n                        this._host.remove();\n                    }\n                    subscription.unsubscribe();\n                }\n            });\n        });\n    }\n    /** Disposes of a scroll strategy. */\n    _disposeScrollStrategy() {\n        const scrollStrategy = this._scrollStrategy;\n        if (scrollStrategy) {\n            scrollStrategy.disable();\n            if (scrollStrategy.detach) {\n                scrollStrategy.detach();\n            }\n        }\n    }\n    /** Removes a backdrop element from the DOM. */\n    _disposeBackdrop(backdrop) {\n        if (backdrop) {\n            backdrop.removeEventListener('click', this._backdropClickHandler);\n            backdrop.removeEventListener('transitionend', this._backdropTransitionendHandler);\n            backdrop.remove();\n            // It is possible that a new portal has been attached to this overlay since we started\n            // removing the backdrop. If that is the case, only clear the backdrop reference if it\n            // is still the same instance that we started to remove.\n            if (this._backdropElement === backdrop) {\n                this._backdropElement = null;\n            }\n        }\n        if (this._backdropTimeout) {\n            clearTimeout(this._backdropTimeout);\n            this._backdropTimeout = undefined;\n        }\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// TODO: refactor clipping detection into a separate thing (part of scrolling module)\n// TODO: doesn't handle both flexible width and height when it has to scroll along both axis.\n/** Class to be added to the overlay bounding box. */\nconst boundingBoxClass = 'cdk-overlay-connected-position-bounding-box';\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\nclass FlexibleConnectedPositionStrategy {\n    constructor(connectedTo, _viewportRuler, _document, _platform, _overlayContainer) {\n        this._viewportRuler = _viewportRuler;\n        this._document = _document;\n        this._platform = _platform;\n        this._overlayContainer = _overlayContainer;\n        /** Last size used for the bounding box. Used to avoid resizing the overlay after open. */\n        this._lastBoundingBoxSize = { width: 0, height: 0 };\n        /** Whether the overlay was pushed in a previous positioning. */\n        this._isPushed = false;\n        /** Whether the overlay can be pushed on-screen on the initial open. */\n        this._canPush = true;\n        /** Whether the overlay can grow via flexible width/height after the initial open. */\n        this._growAfterOpen = false;\n        /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n        this._hasFlexibleDimensions = true;\n        /** Whether the overlay position is locked. */\n        this._positionLocked = false;\n        /** Amount of space that must be maintained between the overlay and the edge of the viewport. */\n        this._viewportMargin = 0;\n        /** The Scrollable containers used to check scrollable view properties on position change. */\n        this._scrollables = [];\n        /** Ordered list of preferred positions, from most to least desirable. */\n        this._preferredPositions = [];\n        /** Subject that emits whenever the position changes. */\n        this._positionChanges = new Subject();\n        /** Subscription to viewport size changes. */\n        this._resizeSubscription = Subscription.EMPTY;\n        /** Default offset for the overlay along the x axis. */\n        this._offsetX = 0;\n        /** Default offset for the overlay along the y axis. */\n        this._offsetY = 0;\n        /** Keeps track of the CSS classes that the position strategy has applied on the overlay panel. */\n        this._appliedPanelClasses = [];\n        /** Observable sequence of position changes. */\n        this.positionChanges = this._positionChanges;\n        this.setOrigin(connectedTo);\n    }\n    /** Ordered list of preferred positions, from most to least desirable. */\n    get positions() {\n        return this._preferredPositions;\n    }\n    /** Attaches this position strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef &&\n            overlayRef !== this._overlayRef &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('This position strategy is already attached to an overlay');\n        }\n        this._validatePositions();\n        overlayRef.hostElement.classList.add(boundingBoxClass);\n        this._overlayRef = overlayRef;\n        this._boundingBox = overlayRef.hostElement;\n        this._pane = overlayRef.overlayElement;\n        this._isDisposed = false;\n        this._isInitialRender = true;\n        this._lastPosition = null;\n        this._resizeSubscription.unsubscribe();\n        this._resizeSubscription = this._viewportRuler.change().subscribe(() => {\n            // When the window is resized, we want to trigger the next reposition as if it\n            // was an initial render, in order for the strategy to pick a new optimal position,\n            // otherwise position locking will cause it to stay at the old one.\n            this._isInitialRender = true;\n            this.apply();\n        });\n    }\n    /**\n     * Updates the position of the overlay element, using whichever preferred position relative\n     * to the origin best fits on-screen.\n     *\n     * The selection of a position goes as follows:\n     *  - If any positions fit completely within the viewport as-is,\n     *      choose the first position that does so.\n     *  - If flexible dimensions are enabled and at least one satisfies the given minimum width/height,\n     *      choose the position with the greatest available size modified by the positions' weight.\n     *  - If pushing is enabled, take the position that went off-screen the least and push it\n     *      on-screen.\n     *  - If none of the previous criteria were met, use the position that goes off-screen the least.\n     * @docs-private\n     */\n    apply() {\n        // We shouldn't do anything if the strategy was disposed or we're on the server.\n        if (this._isDisposed || !this._platform.isBrowser) {\n            return;\n        }\n        // If the position has been applied already (e.g. when the overlay was opened) and the\n        // consumer opted into locking in the position, re-use the old position, in order to\n        // prevent the overlay from jumping around.\n        if (!this._isInitialRender && this._positionLocked && this._lastPosition) {\n            this.reapplyLastPosition();\n            return;\n        }\n        this._clearPanelClasses();\n        this._resetOverlayElementStyles();\n        this._resetBoundingBoxStyles();\n        // We need the bounding rects for the origin, the overlay and the container to determine how to position\n        // the overlay relative to the origin.\n        // We use the viewport rect to determine whether a position would go off-screen.\n        this._viewportRect = this._getNarrowedViewportRect();\n        this._originRect = this._getOriginRect();\n        this._overlayRect = this._pane.getBoundingClientRect();\n        this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n        const originRect = this._originRect;\n        const overlayRect = this._overlayRect;\n        const viewportRect = this._viewportRect;\n        const containerRect = this._containerRect;\n        // Positions where the overlay will fit with flexible dimensions.\n        const flexibleFits = [];\n        // Fallback if none of the preferred positions fit within the viewport.\n        let fallback;\n        // Go through each of the preferred positions looking for a good fit.\n        // If a good fit is found, it will be applied immediately.\n        for (let pos of this._preferredPositions) {\n            // Get the exact (x, y) coordinate for the point-of-origin on the origin element.\n            let originPoint = this._getOriginPoint(originRect, containerRect, pos);\n            // From that point-of-origin, get the exact (x, y) coordinate for the top-left corner of the\n            // overlay in this position. We use the top-left corner for calculations and later translate\n            // this into an appropriate (top, left, bottom, right) style.\n            let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, pos);\n            // Calculate how well the overlay would fit into the viewport with this point.\n            let overlayFit = this._getOverlayFit(overlayPoint, overlayRect, viewportRect, pos);\n            // If the overlay, without any further work, fits into the viewport, use this position.\n            if (overlayFit.isCompletelyWithinViewport) {\n                this._isPushed = false;\n                this._applyPosition(pos, originPoint);\n                return;\n            }\n            // If the overlay has flexible dimensions, we can use this position\n            // so long as there's enough space for the minimum dimensions.\n            if (this._canFitWithFlexibleDimensions(overlayFit, overlayPoint, viewportRect)) {\n                // Save positions where the overlay will fit with flexible dimensions. We will use these\n                // if none of the positions fit *without* flexible dimensions.\n                flexibleFits.push({\n                    position: pos,\n                    origin: originPoint,\n                    overlayRect,\n                    boundingBoxRect: this._calculateBoundingBoxRect(originPoint, pos),\n                });\n                continue;\n            }\n            // If the current preferred position does not fit on the screen, remember the position\n            // if it has more visible area on-screen than we've seen and move onto the next preferred\n            // position.\n            if (!fallback || fallback.overlayFit.visibleArea < overlayFit.visibleArea) {\n                fallback = { overlayFit, overlayPoint, originPoint, position: pos, overlayRect };\n            }\n        }\n        // If there are any positions where the overlay would fit with flexible dimensions, choose the\n        // one that has the greatest area available modified by the position's weight\n        if (flexibleFits.length) {\n            let bestFit = null;\n            let bestScore = -1;\n            for (const fit of flexibleFits) {\n                const score = fit.boundingBoxRect.width * fit.boundingBoxRect.height * (fit.position.weight || 1);\n                if (score > bestScore) {\n                    bestScore = score;\n                    bestFit = fit;\n                }\n            }\n            this._isPushed = false;\n            this._applyPosition(bestFit.position, bestFit.origin);\n            return;\n        }\n        // When none of the preferred positions fit within the viewport, take the position\n        // that went off-screen the least and attempt to push it on-screen.\n        if (this._canPush) {\n            // TODO(jelbourn): after pushing, the opening \"direction\" of the overlay might not make sense.\n            this._isPushed = true;\n            this._applyPosition(fallback.position, fallback.originPoint);\n            return;\n        }\n        // All options for getting the overlay within the viewport have been exhausted, so go with the\n        // position that went off-screen the least.\n        this._applyPosition(fallback.position, fallback.originPoint);\n    }\n    detach() {\n        this._clearPanelClasses();\n        this._lastPosition = null;\n        this._previousPushAmount = null;\n        this._resizeSubscription.unsubscribe();\n    }\n    /** Cleanup after the element gets destroyed. */\n    dispose() {\n        if (this._isDisposed) {\n            return;\n        }\n        // We can't use `_resetBoundingBoxStyles` here, because it resets\n        // some properties to zero, rather than removing them.\n        if (this._boundingBox) {\n            extendStyles(this._boundingBox.style, {\n                top: '',\n                left: '',\n                right: '',\n                bottom: '',\n                height: '',\n                width: '',\n                alignItems: '',\n                justifyContent: '',\n            });\n        }\n        if (this._pane) {\n            this._resetOverlayElementStyles();\n        }\n        if (this._overlayRef) {\n            this._overlayRef.hostElement.classList.remove(boundingBoxClass);\n        }\n        this.detach();\n        this._positionChanges.complete();\n        this._overlayRef = this._boundingBox = null;\n        this._isDisposed = true;\n    }\n    /**\n     * This re-aligns the overlay element with the trigger in its last calculated position,\n     * even if a position higher in the \"preferred positions\" list would now fit. This\n     * allows one to re-align the panel without changing the orientation of the panel.\n     */\n    reapplyLastPosition() {\n        if (this._isDisposed || !this._platform.isBrowser) {\n            return;\n        }\n        const lastPosition = this._lastPosition;\n        if (lastPosition) {\n            this._originRect = this._getOriginRect();\n            this._overlayRect = this._pane.getBoundingClientRect();\n            this._viewportRect = this._getNarrowedViewportRect();\n            this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n            const originPoint = this._getOriginPoint(this._originRect, this._containerRect, lastPosition);\n            this._applyPosition(lastPosition, originPoint);\n        }\n        else {\n            this.apply();\n        }\n    }\n    /**\n     * Sets the list of Scrollable containers that host the origin element so that\n     * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n     * Scrollable must be an ancestor element of the strategy's origin element.\n     */\n    withScrollableContainers(scrollables) {\n        this._scrollables = scrollables;\n        return this;\n    }\n    /**\n     * Adds new preferred positions.\n     * @param positions List of positions options for this overlay.\n     */\n    withPositions(positions) {\n        this._preferredPositions = positions;\n        // If the last calculated position object isn't part of the positions anymore, clear\n        // it in order to avoid it being picked up if the consumer tries to re-apply.\n        if (positions.indexOf(this._lastPosition) === -1) {\n            this._lastPosition = null;\n        }\n        this._validatePositions();\n        return this;\n    }\n    /**\n     * Sets a minimum distance the overlay may be positioned to the edge of the viewport.\n     * @param margin Required margin between the overlay and the viewport edge in pixels.\n     */\n    withViewportMargin(margin) {\n        this._viewportMargin = margin;\n        return this;\n    }\n    /** Sets whether the overlay's width and height can be constrained to fit within the viewport. */\n    withFlexibleDimensions(flexibleDimensions = true) {\n        this._hasFlexibleDimensions = flexibleDimensions;\n        return this;\n    }\n    /** Sets whether the overlay can grow after the initial open via flexible width/height. */\n    withGrowAfterOpen(growAfterOpen = true) {\n        this._growAfterOpen = growAfterOpen;\n        return this;\n    }\n    /** Sets whether the overlay can be pushed on-screen if none of the provided positions fit. */\n    withPush(canPush = true) {\n        this._canPush = canPush;\n        return this;\n    }\n    /**\n     * Sets whether the overlay's position should be locked in after it is positioned\n     * initially. When an overlay is locked in, it won't attempt to reposition itself\n     * when the position is re-applied (e.g. when the user scrolls away).\n     * @param isLocked Whether the overlay should locked in.\n     */\n    withLockedPosition(isLocked = true) {\n        this._positionLocked = isLocked;\n        return this;\n    }\n    /**\n     * Sets the origin, relative to which to position the overlay.\n     * Using an element origin is useful for building components that need to be positioned\n     * relatively to a trigger (e.g. dropdown menus or tooltips), whereas using a point can be\n     * used for cases like contextual menus which open relative to the user's pointer.\n     * @param origin Reference to the new origin.\n     */\n    setOrigin(origin) {\n        this._origin = origin;\n        return this;\n    }\n    /**\n     * Sets the default offset for the overlay's connection point on the x-axis.\n     * @param offset New offset in the X axis.\n     */\n    withDefaultOffsetX(offset) {\n        this._offsetX = offset;\n        return this;\n    }\n    /**\n     * Sets the default offset for the overlay's connection point on the y-axis.\n     * @param offset New offset in the Y axis.\n     */\n    withDefaultOffsetY(offset) {\n        this._offsetY = offset;\n        return this;\n    }\n    /**\n     * Configures that the position strategy should set a `transform-origin` on some elements\n     * inside the overlay, depending on the current position that is being applied. This is\n     * useful for the cases where the origin of an animation can change depending on the\n     * alignment of the overlay.\n     * @param selector CSS selector that will be used to find the target\n     *    elements onto which to set the transform origin.\n     */\n    withTransformOriginOn(selector) {\n        this._transformOriginSelector = selector;\n        return this;\n    }\n    /**\n     * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n     */\n    _getOriginPoint(originRect, containerRect, pos) {\n        let x;\n        if (pos.originX == 'center') {\n            // Note: when centering we should always use the `left`\n            // offset, otherwise the position will be wrong in RTL.\n            x = originRect.left + originRect.width / 2;\n        }\n        else {\n            const startX = this._isRtl() ? originRect.right : originRect.left;\n            const endX = this._isRtl() ? originRect.left : originRect.right;\n            x = pos.originX == 'start' ? startX : endX;\n        }\n        // When zooming in Safari the container rectangle contains negative values for the position\n        // and we need to re-add them to the calculated coordinates.\n        if (containerRect.left < 0) {\n            x -= containerRect.left;\n        }\n        let y;\n        if (pos.originY == 'center') {\n            y = originRect.top + originRect.height / 2;\n        }\n        else {\n            y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n        }\n        // Normally the containerRect's top value would be zero, however when the overlay is attached to an input\n        // (e.g. in an autocomplete), mobile browsers will shift everything in order to put the input in the middle\n        // of the screen and to make space for the virtual keyboard. We need to account for this offset,\n        // otherwise our positioning will be thrown off.\n        // Additionally, when zooming in Safari this fixes the vertical position.\n        if (containerRect.top < 0) {\n            y -= containerRect.top;\n        }\n        return { x, y };\n    }\n    /**\n     * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n     * origin point to which the overlay should be connected.\n     */\n    _getOverlayPoint(originPoint, overlayRect, pos) {\n        // Calculate the (overlayStartX, overlayStartY), the start of the\n        // potential overlay position relative to the origin point.\n        let overlayStartX;\n        if (pos.overlayX == 'center') {\n            overlayStartX = -overlayRect.width / 2;\n        }\n        else if (pos.overlayX === 'start') {\n            overlayStartX = this._isRtl() ? -overlayRect.width : 0;\n        }\n        else {\n            overlayStartX = this._isRtl() ? 0 : -overlayRect.width;\n        }\n        let overlayStartY;\n        if (pos.overlayY == 'center') {\n            overlayStartY = -overlayRect.height / 2;\n        }\n        else {\n            overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n        }\n        // The (x, y) coordinates of the overlay.\n        return {\n            x: originPoint.x + overlayStartX,\n            y: originPoint.y + overlayStartY,\n        };\n    }\n    /** Gets how well an overlay at the given point will fit within the viewport. */\n    _getOverlayFit(point, rawOverlayRect, viewport, position) {\n        // Round the overlay rect when comparing against the\n        // viewport, because the viewport is always rounded.\n        const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n        let { x, y } = point;\n        let offsetX = this._getOffset(position, 'x');\n        let offsetY = this._getOffset(position, 'y');\n        // Account for the offsets since they could push the overlay out of the viewport.\n        if (offsetX) {\n            x += offsetX;\n        }\n        if (offsetY) {\n            y += offsetY;\n        }\n        // How much the overlay would overflow at this position, on each side.\n        let leftOverflow = 0 - x;\n        let rightOverflow = x + overlay.width - viewport.width;\n        let topOverflow = 0 - y;\n        let bottomOverflow = y + overlay.height - viewport.height;\n        // Visible parts of the element on each axis.\n        let visibleWidth = this._subtractOverflows(overlay.width, leftOverflow, rightOverflow);\n        let visibleHeight = this._subtractOverflows(overlay.height, topOverflow, bottomOverflow);\n        let visibleArea = visibleWidth * visibleHeight;\n        return {\n            visibleArea,\n            isCompletelyWithinViewport: overlay.width * overlay.height === visibleArea,\n            fitsInViewportVertically: visibleHeight === overlay.height,\n            fitsInViewportHorizontally: visibleWidth == overlay.width,\n        };\n    }\n    /**\n     * Whether the overlay can fit within the viewport when it may resize either its width or height.\n     * @param fit How well the overlay fits in the viewport at some position.\n     * @param point The (x, y) coordinates of the overlay at some position.\n     * @param viewport The geometry of the viewport.\n     */\n    _canFitWithFlexibleDimensions(fit, point, viewport) {\n        if (this._hasFlexibleDimensions) {\n            const availableHeight = viewport.bottom - point.y;\n            const availableWidth = viewport.right - point.x;\n            const minHeight = getPixelValue(this._overlayRef.getConfig().minHeight);\n            const minWidth = getPixelValue(this._overlayRef.getConfig().minWidth);\n            const verticalFit = fit.fitsInViewportVertically || (minHeight != null && minHeight <= availableHeight);\n            const horizontalFit = fit.fitsInViewportHorizontally || (minWidth != null && minWidth <= availableWidth);\n            return verticalFit && horizontalFit;\n        }\n        return false;\n    }\n    /**\n     * Gets the point at which the overlay can be \"pushed\" on-screen. If the overlay is larger than\n     * the viewport, the top-left corner will be pushed on-screen (with overflow occurring on the\n     * right and bottom).\n     *\n     * @param start Starting point from which the overlay is pushed.\n     * @param rawOverlayRect Dimensions of the overlay.\n     * @param scrollPosition Current viewport scroll position.\n     * @returns The point at which to position the overlay after pushing. This is effectively a new\n     *     originPoint.\n     */\n    _pushOverlayOnScreen(start, rawOverlayRect, scrollPosition) {\n        // If the position is locked and we've pushed the overlay already, reuse the previous push\n        // amount, rather than pushing it again. If we were to continue pushing, the element would\n        // remain in the viewport, which goes against the expectations when position locking is enabled.\n        if (this._previousPushAmount && this._positionLocked) {\n            return {\n                x: start.x + this._previousPushAmount.x,\n                y: start.y + this._previousPushAmount.y,\n            };\n        }\n        // Round the overlay rect when comparing against the\n        // viewport, because the viewport is always rounded.\n        const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n        const viewport = this._viewportRect;\n        // Determine how much the overlay goes outside the viewport on each\n        // side, which we'll use to decide which direction to push it.\n        const overflowRight = Math.max(start.x + overlay.width - viewport.width, 0);\n        const overflowBottom = Math.max(start.y + overlay.height - viewport.height, 0);\n        const overflowTop = Math.max(viewport.top - scrollPosition.top - start.y, 0);\n        const overflowLeft = Math.max(viewport.left - scrollPosition.left - start.x, 0);\n        // Amount by which to push the overlay in each axis such that it remains on-screen.\n        let pushX = 0;\n        let pushY = 0;\n        // If the overlay fits completely within the bounds of the viewport, push it from whichever\n        // direction is goes off-screen. Otherwise, push the top-left corner such that its in the\n        // viewport and allow for the trailing end of the overlay to go out of bounds.\n        if (overlay.width <= viewport.width) {\n            pushX = overflowLeft || -overflowRight;\n        }\n        else {\n            pushX = start.x < this._viewportMargin ? viewport.left - scrollPosition.left - start.x : 0;\n        }\n        if (overlay.height <= viewport.height) {\n            pushY = overflowTop || -overflowBottom;\n        }\n        else {\n            pushY = start.y < this._viewportMargin ? viewport.top - scrollPosition.top - start.y : 0;\n        }\n        this._previousPushAmount = { x: pushX, y: pushY };\n        return {\n            x: start.x + pushX,\n            y: start.y + pushY,\n        };\n    }\n    /**\n     * Applies a computed position to the overlay and emits a position change.\n     * @param position The position preference\n     * @param originPoint The point on the origin element where the overlay is connected.\n     */\n    _applyPosition(position, originPoint) {\n        this._setTransformOrigin(position);\n        this._setOverlayElementStyles(originPoint, position);\n        this._setBoundingBoxStyles(originPoint, position);\n        if (position.panelClass) {\n            this._addPanelClasses(position.panelClass);\n        }\n        // Save the last connected position in case the position needs to be re-calculated.\n        this._lastPosition = position;\n        // Notify that the position has been changed along with its change properties.\n        // We only emit if we've got any subscriptions, because the scroll visibility\n        // calculations can be somewhat expensive.\n        if (this._positionChanges.observers.length) {\n            const scrollableViewProperties = this._getScrollVisibility();\n            const changeEvent = new ConnectedOverlayPositionChange(position, scrollableViewProperties);\n            this._positionChanges.next(changeEvent);\n        }\n        this._isInitialRender = false;\n    }\n    /** Sets the transform origin based on the configured selector and the passed-in position.  */\n    _setTransformOrigin(position) {\n        if (!this._transformOriginSelector) {\n            return;\n        }\n        const elements = this._boundingBox.querySelectorAll(this._transformOriginSelector);\n        let xOrigin;\n        let yOrigin = position.overlayY;\n        if (position.overlayX === 'center') {\n            xOrigin = 'center';\n        }\n        else if (this._isRtl()) {\n            xOrigin = position.overlayX === 'start' ? 'right' : 'left';\n        }\n        else {\n            xOrigin = position.overlayX === 'start' ? 'left' : 'right';\n        }\n        for (let i = 0; i < elements.length; i++) {\n            elements[i].style.transformOrigin = `${xOrigin} ${yOrigin}`;\n        }\n    }\n    /**\n     * Gets the position and size of the overlay's sizing container.\n     *\n     * This method does no measuring and applies no styles so that we can cheaply compute the\n     * bounds for all positions and choose the best fit based on these results.\n     */\n    _calculateBoundingBoxRect(origin, position) {\n        const viewport = this._viewportRect;\n        const isRtl = this._isRtl();\n        let height, top, bottom;\n        if (position.overlayY === 'top') {\n            // Overlay is opening \"downward\" and thus is bound by the bottom viewport edge.\n            top = origin.y;\n            height = viewport.height - top + this._viewportMargin;\n        }\n        else if (position.overlayY === 'bottom') {\n            // Overlay is opening \"upward\" and thus is bound by the top viewport edge. We need to add\n            // the viewport margin back in, because the viewport rect is narrowed down to remove the\n            // margin, whereas the `origin` position is calculated based on its `ClientRect`.\n            bottom = viewport.height - origin.y + this._viewportMargin * 2;\n            height = viewport.height - bottom + this._viewportMargin;\n        }\n        else {\n            // If neither top nor bottom, it means that the overlay is vertically centered on the\n            // origin point. Note that we want the position relative to the viewport, rather than\n            // the page, which is why we don't use something like `viewport.bottom - origin.y` and\n            // `origin.y - viewport.top`.\n            const smallestDistanceToViewportEdge = Math.min(viewport.bottom - origin.y + viewport.top, origin.y);\n            const previousHeight = this._lastBoundingBoxSize.height;\n            height = smallestDistanceToViewportEdge * 2;\n            top = origin.y - smallestDistanceToViewportEdge;\n            if (height > previousHeight && !this._isInitialRender && !this._growAfterOpen) {\n                top = origin.y - previousHeight / 2;\n            }\n        }\n        // The overlay is opening 'right-ward' (the content flows to the right).\n        const isBoundedByRightViewportEdge = (position.overlayX === 'start' && !isRtl) || (position.overlayX === 'end' && isRtl);\n        // The overlay is opening 'left-ward' (the content flows to the left).\n        const isBoundedByLeftViewportEdge = (position.overlayX === 'end' && !isRtl) || (position.overlayX === 'start' && isRtl);\n        let width, left, right;\n        if (isBoundedByLeftViewportEdge) {\n            right = viewport.width - origin.x + this._viewportMargin;\n            width = origin.x - this._viewportMargin;\n        }\n        else if (isBoundedByRightViewportEdge) {\n            left = origin.x;\n            width = viewport.right - origin.x;\n        }\n        else {\n            // If neither start nor end, it means that the overlay is horizontally centered on the\n            // origin point. Note that we want the position relative to the viewport, rather than\n            // the page, which is why we don't use something like `viewport.right - origin.x` and\n            // `origin.x - viewport.left`.\n            const smallestDistanceToViewportEdge = Math.min(viewport.right - origin.x + viewport.left, origin.x);\n            const previousWidth = this._lastBoundingBoxSize.width;\n            width = smallestDistanceToViewportEdge * 2;\n            left = origin.x - smallestDistanceToViewportEdge;\n            if (width > previousWidth && !this._isInitialRender && !this._growAfterOpen) {\n                left = origin.x - previousWidth / 2;\n            }\n        }\n        return { top: top, left: left, bottom: bottom, right: right, width, height };\n    }\n    /**\n     * Sets the position and size of the overlay's sizing wrapper. The wrapper is positioned on the\n     * origin's connection point and stretches to the bounds of the viewport.\n     *\n     * @param origin The point on the origin element where the overlay is connected.\n     * @param position The position preference\n     */\n    _setBoundingBoxStyles(origin, position) {\n        const boundingBoxRect = this._calculateBoundingBoxRect(origin, position);\n        // It's weird if the overlay *grows* while scrolling, so we take the last size into account\n        // when applying a new size.\n        if (!this._isInitialRender && !this._growAfterOpen) {\n            boundingBoxRect.height = Math.min(boundingBoxRect.height, this._lastBoundingBoxSize.height);\n            boundingBoxRect.width = Math.min(boundingBoxRect.width, this._lastBoundingBoxSize.width);\n        }\n        const styles = {};\n        if (this._hasExactPosition()) {\n            styles.top = styles.left = '0';\n            styles.bottom = styles.right = styles.maxHeight = styles.maxWidth = '';\n            styles.width = styles.height = '100%';\n        }\n        else {\n            const maxHeight = this._overlayRef.getConfig().maxHeight;\n            const maxWidth = this._overlayRef.getConfig().maxWidth;\n            styles.height = coerceCssPixelValue(boundingBoxRect.height);\n            styles.top = coerceCssPixelValue(boundingBoxRect.top);\n            styles.bottom = coerceCssPixelValue(boundingBoxRect.bottom);\n            styles.width = coerceCssPixelValue(boundingBoxRect.width);\n            styles.left = coerceCssPixelValue(boundingBoxRect.left);\n            styles.right = coerceCssPixelValue(boundingBoxRect.right);\n            // Push the pane content towards the proper direction.\n            if (position.overlayX === 'center') {\n                styles.alignItems = 'center';\n            }\n            else {\n                styles.alignItems = position.overlayX === 'end' ? 'flex-end' : 'flex-start';\n            }\n            if (position.overlayY === 'center') {\n                styles.justifyContent = 'center';\n            }\n            else {\n                styles.justifyContent = position.overlayY === 'bottom' ? 'flex-end' : 'flex-start';\n            }\n            if (maxHeight) {\n                styles.maxHeight = coerceCssPixelValue(maxHeight);\n            }\n            if (maxWidth) {\n                styles.maxWidth = coerceCssPixelValue(maxWidth);\n            }\n        }\n        this._lastBoundingBoxSize = boundingBoxRect;\n        extendStyles(this._boundingBox.style, styles);\n    }\n    /** Resets the styles for the bounding box so that a new positioning can be computed. */\n    _resetBoundingBoxStyles() {\n        extendStyles(this._boundingBox.style, {\n            top: '0',\n            left: '0',\n            right: '0',\n            bottom: '0',\n            height: '',\n            width: '',\n            alignItems: '',\n            justifyContent: '',\n        });\n    }\n    /** Resets the styles for the overlay pane so that a new positioning can be computed. */\n    _resetOverlayElementStyles() {\n        extendStyles(this._pane.style, {\n            top: '',\n            left: '',\n            bottom: '',\n            right: '',\n            position: '',\n            transform: '',\n        });\n    }\n    /** Sets positioning styles to the overlay element. */\n    _setOverlayElementStyles(originPoint, position) {\n        const styles = {};\n        const hasExactPosition = this._hasExactPosition();\n        const hasFlexibleDimensions = this._hasFlexibleDimensions;\n        const config = this._overlayRef.getConfig();\n        if (hasExactPosition) {\n            const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n            extendStyles(styles, this._getExactOverlayY(position, originPoint, scrollPosition));\n            extendStyles(styles, this._getExactOverlayX(position, originPoint, scrollPosition));\n        }\n        else {\n            styles.position = 'static';\n        }\n        // Use a transform to apply the offsets. We do this because the `center` positions rely on\n        // being in the normal flex flow and setting a `top` / `left` at all will completely throw\n        // off the position. We also can't use margins, because they won't have an effect in some\n        // cases where the element doesn't have anything to \"push off of\". Finally, this works\n        // better both with flexible and non-flexible positioning.\n        let transformString = '';\n        let offsetX = this._getOffset(position, 'x');\n        let offsetY = this._getOffset(position, 'y');\n        if (offsetX) {\n            transformString += `translateX(${offsetX}px) `;\n        }\n        if (offsetY) {\n            transformString += `translateY(${offsetY}px)`;\n        }\n        styles.transform = transformString.trim();\n        // If a maxWidth or maxHeight is specified on the overlay, we remove them. We do this because\n        // we need these values to both be set to \"100%\" for the automatic flexible sizing to work.\n        // The maxHeight and maxWidth are set on the boundingBox in order to enforce the constraint.\n        // Note that this doesn't apply when we have an exact position, in which case we do want to\n        // apply them because they'll be cleared from the bounding box.\n        if (config.maxHeight) {\n            if (hasExactPosition) {\n                styles.maxHeight = coerceCssPixelValue(config.maxHeight);\n            }\n            else if (hasFlexibleDimensions) {\n                styles.maxHeight = '';\n            }\n        }\n        if (config.maxWidth) {\n            if (hasExactPosition) {\n                styles.maxWidth = coerceCssPixelValue(config.maxWidth);\n            }\n            else if (hasFlexibleDimensions) {\n                styles.maxWidth = '';\n            }\n        }\n        extendStyles(this._pane.style, styles);\n    }\n    /** Gets the exact top/bottom for the overlay when not using flexible sizing or when pushing. */\n    _getExactOverlayY(position, originPoint, scrollPosition) {\n        // Reset any existing styles. This is necessary in case the\n        // preferred position has changed since the last `apply`.\n        let styles = { top: '', bottom: '' };\n        let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n        if (this._isPushed) {\n            overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n        }\n        // We want to set either `top` or `bottom` based on whether the overlay wants to appear\n        // above or below the origin and the direction in which the element will expand.\n        if (position.overlayY === 'bottom') {\n            // When using `bottom`, we adjust the y position such that it is the distance\n            // from the bottom of the viewport rather than the top.\n            const documentHeight = this._document.documentElement.clientHeight;\n            styles.bottom = `${documentHeight - (overlayPoint.y + this._overlayRect.height)}px`;\n        }\n        else {\n            styles.top = coerceCssPixelValue(overlayPoint.y);\n        }\n        return styles;\n    }\n    /** Gets the exact left/right for the overlay when not using flexible sizing or when pushing. */\n    _getExactOverlayX(position, originPoint, scrollPosition) {\n        // Reset any existing styles. This is necessary in case the preferred position has\n        // changed since the last `apply`.\n        let styles = { left: '', right: '' };\n        let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n        if (this._isPushed) {\n            overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n        }\n        // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n        // or \"after\" the origin, which determines the direction in which the element will expand.\n        // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n        // page is in RTL or LTR.\n        let horizontalStyleProperty;\n        if (this._isRtl()) {\n            horizontalStyleProperty = position.overlayX === 'end' ? 'left' : 'right';\n        }\n        else {\n            horizontalStyleProperty = position.overlayX === 'end' ? 'right' : 'left';\n        }\n        // When we're setting `right`, we adjust the x position such that it is the distance\n        // from the right edge of the viewport rather than the left edge.\n        if (horizontalStyleProperty === 'right') {\n            const documentWidth = this._document.documentElement.clientWidth;\n            styles.right = `${documentWidth - (overlayPoint.x + this._overlayRect.width)}px`;\n        }\n        else {\n            styles.left = coerceCssPixelValue(overlayPoint.x);\n        }\n        return styles;\n    }\n    /**\n     * Gets the view properties of the trigger and overlay, including whether they are clipped\n     * or completely outside the view of any of the strategy's scrollables.\n     */\n    _getScrollVisibility() {\n        // Note: needs fresh rects since the position could've changed.\n        const originBounds = this._getOriginRect();\n        const overlayBounds = this._pane.getBoundingClientRect();\n        // TODO(jelbourn): instead of needing all of the client rects for these scrolling containers\n        // every time, we should be able to use the scrollTop of the containers if the size of those\n        // containers hasn't changed.\n        const scrollContainerBounds = this._scrollables.map(scrollable => {\n            return scrollable.getElementRef().nativeElement.getBoundingClientRect();\n        });\n        return {\n            isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n            isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n            isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n            isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds),\n        };\n    }\n    /** Subtracts the amount that an element is overflowing on an axis from its length. */\n    _subtractOverflows(length, ...overflows) {\n        return overflows.reduce((currentValue, currentOverflow) => {\n            return currentValue - Math.max(currentOverflow, 0);\n        }, length);\n    }\n    /** Narrows the given viewport rect by the current _viewportMargin. */\n    _getNarrowedViewportRect() {\n        // We recalculate the viewport rect here ourselves, rather than using the ViewportRuler,\n        // because we want to use the `clientWidth` and `clientHeight` as the base. The difference\n        // being that the client properties don't include the scrollbar, as opposed to `innerWidth`\n        // and `innerHeight` that do. This is necessary, because the overlay container uses\n        // 100% `width` and `height` which don't include the scrollbar either.\n        const width = this._document.documentElement.clientWidth;\n        const height = this._document.documentElement.clientHeight;\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n        return {\n            top: scrollPosition.top + this._viewportMargin,\n            left: scrollPosition.left + this._viewportMargin,\n            right: scrollPosition.left + width - this._viewportMargin,\n            bottom: scrollPosition.top + height - this._viewportMargin,\n            width: width - 2 * this._viewportMargin,\n            height: height - 2 * this._viewportMargin,\n        };\n    }\n    /** Whether the we're dealing with an RTL context */\n    _isRtl() {\n        return this._overlayRef.getDirection() === 'rtl';\n    }\n    /** Determines whether the overlay uses exact or flexible positioning. */\n    _hasExactPosition() {\n        return !this._hasFlexibleDimensions || this._isPushed;\n    }\n    /** Retrieves the offset of a position along the x or y axis. */\n    _getOffset(position, axis) {\n        if (axis === 'x') {\n            // We don't do something like `position['offset' + axis]` in\n            // order to avoid breaking minifiers that rename properties.\n            return position.offsetX == null ? this._offsetX : position.offsetX;\n        }\n        return position.offsetY == null ? this._offsetY : position.offsetY;\n    }\n    /** Validates that the current position match the expected values. */\n    _validatePositions() {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (!this._preferredPositions.length) {\n                throw Error('FlexibleConnectedPositionStrategy: At least one position is required.');\n            }\n            // TODO(crisbeto): remove these once Angular's template type\n            // checking is advanced enough to catch these cases.\n            this._preferredPositions.forEach(pair => {\n                validateHorizontalPosition('originX', pair.originX);\n                validateVerticalPosition('originY', pair.originY);\n                validateHorizontalPosition('overlayX', pair.overlayX);\n                validateVerticalPosition('overlayY', pair.overlayY);\n            });\n        }\n    }\n    /** Adds a single CSS class or an array of classes on the overlay panel. */\n    _addPanelClasses(cssClasses) {\n        if (this._pane) {\n            coerceArray(cssClasses).forEach(cssClass => {\n                if (cssClass !== '' && this._appliedPanelClasses.indexOf(cssClass) === -1) {\n                    this._appliedPanelClasses.push(cssClass);\n                    this._pane.classList.add(cssClass);\n                }\n            });\n        }\n    }\n    /** Clears the classes that the position strategy has applied from the overlay panel. */\n    _clearPanelClasses() {\n        if (this._pane) {\n            this._appliedPanelClasses.forEach(cssClass => {\n                this._pane.classList.remove(cssClass);\n            });\n            this._appliedPanelClasses = [];\n        }\n    }\n    /** Returns the ClientRect of the current origin. */\n    _getOriginRect() {\n        const origin = this._origin;\n        if (origin instanceof ElementRef) {\n            return origin.nativeElement.getBoundingClientRect();\n        }\n        // Check for Element so SVG elements are also supported.\n        if (origin instanceof Element) {\n            return origin.getBoundingClientRect();\n        }\n        const width = origin.width || 0;\n        const height = origin.height || 0;\n        // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n        return {\n            top: origin.y,\n            bottom: origin.y + height,\n            left: origin.x,\n            right: origin.x + width,\n            height,\n            width,\n        };\n    }\n}\n/** Shallow-extends a stylesheet object with another stylesheet object. */\nfunction extendStyles(destination, source) {\n    for (let key in source) {\n        if (source.hasOwnProperty(key)) {\n            destination[key] = source[key];\n        }\n    }\n    return destination;\n}\n/**\n * Extracts the pixel value as a number from a value, if it's a number\n * or a CSS pixel string (e.g. `1337px`). Otherwise returns null.\n */\nfunction getPixelValue(input) {\n    if (typeof input !== 'number' && input != null) {\n        const [value, units] = input.split(cssUnitPattern);\n        return !units || units === 'px' ? parseFloat(value) : null;\n    }\n    return input || null;\n}\n/**\n * Gets a version of an element's bounding `ClientRect` where all the values are rounded down to\n * the nearest pixel. This allows us to account for the cases where there may be sub-pixel\n * deviations in the `ClientRect` returned by the browser (e.g. when zoomed in with a percentage\n * size, see #21350).\n */\nfunction getRoundedBoundingClientRect(clientRect) {\n    return {\n        top: Math.floor(clientRect.top),\n        right: Math.floor(clientRect.right),\n        bottom: Math.floor(clientRect.bottom),\n        left: Math.floor(clientRect.left),\n        width: Math.floor(clientRect.width),\n        height: Math.floor(clientRect.height),\n    };\n}\nconst STANDARD_DROPDOWN_BELOW_POSITIONS = [\n    { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top' },\n    { originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom' },\n    { originX: 'end', originY: 'bottom', overlayX: 'end', overlayY: 'top' },\n    { originX: 'end', originY: 'top', overlayX: 'end', overlayY: 'bottom' },\n];\nconst STANDARD_DROPDOWN_ADJACENT_POSITIONS = [\n    { originX: 'end', originY: 'top', overlayX: 'start', overlayY: 'top' },\n    { originX: 'end', originY: 'bottom', overlayX: 'start', overlayY: 'bottom' },\n    { originX: 'start', originY: 'top', overlayX: 'end', overlayY: 'top' },\n    { originX: 'start', originY: 'bottom', overlayX: 'end', overlayY: 'bottom' },\n];\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Class to be added to the overlay pane wrapper. */\nconst wrapperClass = 'cdk-global-overlay-wrapper';\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\nclass GlobalPositionStrategy {\n    constructor() {\n        this._cssPosition = 'static';\n        this._topOffset = '';\n        this._bottomOffset = '';\n        this._alignItems = '';\n        this._xPosition = '';\n        this._xOffset = '';\n        this._width = '';\n        this._height = '';\n        this._isDisposed = false;\n    }\n    attach(overlayRef) {\n        const config = overlayRef.getConfig();\n        this._overlayRef = overlayRef;\n        if (this._width && !config.width) {\n            overlayRef.updateSize({ width: this._width });\n        }\n        if (this._height && !config.height) {\n            overlayRef.updateSize({ height: this._height });\n        }\n        overlayRef.hostElement.classList.add(wrapperClass);\n        this._isDisposed = false;\n    }\n    /**\n     * Sets the top position of the overlay. Clears any previously set vertical position.\n     * @param value New top offset.\n     */\n    top(value = '') {\n        this._bottomOffset = '';\n        this._topOffset = value;\n        this._alignItems = 'flex-start';\n        return this;\n    }\n    /**\n     * Sets the left position of the overlay. Clears any previously set horizontal position.\n     * @param value New left offset.\n     */\n    left(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'left';\n        return this;\n    }\n    /**\n     * Sets the bottom position of the overlay. Clears any previously set vertical position.\n     * @param value New bottom offset.\n     */\n    bottom(value = '') {\n        this._topOffset = '';\n        this._bottomOffset = value;\n        this._alignItems = 'flex-end';\n        return this;\n    }\n    /**\n     * Sets the right position of the overlay. Clears any previously set horizontal position.\n     * @param value New right offset.\n     */\n    right(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'right';\n        return this;\n    }\n    /**\n     * Sets the overlay to the start of the viewport, depending on the overlay direction.\n     * This will be to the left in LTR layouts and to the right in RTL.\n     * @param offset Offset from the edge of the screen.\n     */\n    start(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'start';\n        return this;\n    }\n    /**\n     * Sets the overlay to the end of the viewport, depending on the overlay direction.\n     * This will be to the right in LTR layouts and to the left in RTL.\n     * @param offset Offset from the edge of the screen.\n     */\n    end(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'end';\n        return this;\n    }\n    /**\n     * Sets the overlay width and clears any previously set width.\n     * @param value New width for the overlay\n     * @deprecated Pass the `width` through the `OverlayConfig`.\n     * @breaking-change 8.0.0\n     */\n    width(value = '') {\n        if (this._overlayRef) {\n            this._overlayRef.updateSize({ width: value });\n        }\n        else {\n            this._width = value;\n        }\n        return this;\n    }\n    /**\n     * Sets the overlay height and clears any previously set height.\n     * @param value New height for the overlay\n     * @deprecated Pass the `height` through the `OverlayConfig`.\n     * @breaking-change 8.0.0\n     */\n    height(value = '') {\n        if (this._overlayRef) {\n            this._overlayRef.updateSize({ height: value });\n        }\n        else {\n            this._height = value;\n        }\n        return this;\n    }\n    /**\n     * Centers the overlay horizontally with an optional offset.\n     * Clears any previously set horizontal position.\n     *\n     * @param offset Overlay offset from the horizontal center.\n     */\n    centerHorizontally(offset = '') {\n        this.left(offset);\n        this._xPosition = 'center';\n        return this;\n    }\n    /**\n     * Centers the overlay vertically with an optional offset.\n     * Clears any previously set vertical position.\n     *\n     * @param offset Overlay offset from the vertical center.\n     */\n    centerVertically(offset = '') {\n        this.top(offset);\n        this._alignItems = 'center';\n        return this;\n    }\n    /**\n     * Apply the position to the element.\n     * @docs-private\n     */\n    apply() {\n        // Since the overlay ref applies the strategy asynchronously, it could\n        // have been disposed before it ends up being applied. If that is the\n        // case, we shouldn't do anything.\n        if (!this._overlayRef || !this._overlayRef.hasAttached()) {\n            return;\n        }\n        const styles = this._overlayRef.overlayElement.style;\n        const parentStyles = this._overlayRef.hostElement.style;\n        const config = this._overlayRef.getConfig();\n        const { width, height, maxWidth, maxHeight } = config;\n        const shouldBeFlushHorizontally = (width === '100%' || width === '100vw') &&\n            (!maxWidth || maxWidth === '100%' || maxWidth === '100vw');\n        const shouldBeFlushVertically = (height === '100%' || height === '100vh') &&\n            (!maxHeight || maxHeight === '100%' || maxHeight === '100vh');\n        const xPosition = this._xPosition;\n        const xOffset = this._xOffset;\n        const isRtl = this._overlayRef.getConfig().direction === 'rtl';\n        let marginLeft = '';\n        let marginRight = '';\n        let justifyContent = '';\n        if (shouldBeFlushHorizontally) {\n            justifyContent = 'flex-start';\n        }\n        else if (xPosition === 'center') {\n            justifyContent = 'center';\n            if (isRtl) {\n                marginRight = xOffset;\n            }\n            else {\n                marginLeft = xOffset;\n            }\n        }\n        else if (isRtl) {\n            if (xPosition === 'left' || xPosition === 'end') {\n                justifyContent = 'flex-end';\n                marginLeft = xOffset;\n            }\n            else if (xPosition === 'right' || xPosition === 'start') {\n                justifyContent = 'flex-start';\n                marginRight = xOffset;\n            }\n        }\n        else if (xPosition === 'left' || xPosition === 'start') {\n            justifyContent = 'flex-start';\n            marginLeft = xOffset;\n        }\n        else if (xPosition === 'right' || xPosition === 'end') {\n            justifyContent = 'flex-end';\n            marginRight = xOffset;\n        }\n        styles.position = this._cssPosition;\n        styles.marginLeft = shouldBeFlushHorizontally ? '0' : marginLeft;\n        styles.marginTop = shouldBeFlushVertically ? '0' : this._topOffset;\n        styles.marginBottom = this._bottomOffset;\n        styles.marginRight = shouldBeFlushHorizontally ? '0' : marginRight;\n        parentStyles.justifyContent = justifyContent;\n        parentStyles.alignItems = shouldBeFlushVertically ? 'flex-start' : this._alignItems;\n    }\n    /**\n     * Cleans up the DOM changes from the position strategy.\n     * @docs-private\n     */\n    dispose() {\n        if (this._isDisposed || !this._overlayRef) {\n            return;\n        }\n        const styles = this._overlayRef.overlayElement.style;\n        const parent = this._overlayRef.hostElement;\n        const parentStyles = parent.style;\n        parent.classList.remove(wrapperClass);\n        parentStyles.justifyContent =\n            parentStyles.alignItems =\n                styles.marginTop =\n                    styles.marginBottom =\n                        styles.marginLeft =\n                            styles.marginRight =\n                                styles.position =\n                                    '';\n        this._overlayRef = null;\n        this._isDisposed = true;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Builder for overlay position strategy. */\nclass OverlayPositionBuilder {\n    constructor(_viewportRuler, _document, _platform, _overlayContainer) {\n        this._viewportRuler = _viewportRuler;\n        this._document = _document;\n        this._platform = _platform;\n        this._overlayContainer = _overlayContainer;\n    }\n    /**\n     * Creates a global position strategy.\n     */\n    global() {\n        return new GlobalPositionStrategy();\n    }\n    /**\n     * Creates a flexible position strategy.\n     * @param origin Origin relative to which to position the overlay.\n     */\n    flexibleConnectedTo(origin) {\n        return new FlexibleConnectedPositionStrategy(origin, this._viewportRuler, this._document, this._platform, this._overlayContainer);\n    }\n}\nOverlayPositionBuilder.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: OverlayPositionBuilder, deps: [{ token: i1.ViewportRuler }, { token: DOCUMENT }, { token: i1$1.Platform }, { token: OverlayContainer }], target: i0.ɵɵFactoryTarget.Injectable });\nOverlayPositionBuilder.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: OverlayPositionBuilder, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: OverlayPositionBuilder, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.ViewportRuler }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1$1.Platform }, { type: OverlayContainer }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Next overlay unique ID. */\nlet nextUniqueId = 0;\n// Note that Overlay is *not* scoped to the app root because of the ComponentFactoryResolver\n// which needs to be different depending on where OverlayModule is imported.\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\nclass Overlay {\n    constructor(\n    /** Scrolling strategies that can be used when creating an overlay. */\n    scrollStrategies, _overlayContainer, _componentFactoryResolver, _positionBuilder, _keyboardDispatcher, _injector, _ngZone, _document, _directionality, _location, _outsideClickDispatcher, _animationsModuleType) {\n        this.scrollStrategies = scrollStrategies;\n        this._overlayContainer = _overlayContainer;\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._positionBuilder = _positionBuilder;\n        this._keyboardDispatcher = _keyboardDispatcher;\n        this._injector = _injector;\n        this._ngZone = _ngZone;\n        this._document = _document;\n        this._directionality = _directionality;\n        this._location = _location;\n        this._outsideClickDispatcher = _outsideClickDispatcher;\n        this._animationsModuleType = _animationsModuleType;\n    }\n    /**\n     * Creates an overlay.\n     * @param config Configuration applied to the overlay.\n     * @returns Reference to the created overlay.\n     */\n    create(config) {\n        const host = this._createHostElement();\n        const pane = this._createPaneElement(host);\n        const portalOutlet = this._createPortalOutlet(pane);\n        const overlayConfig = new OverlayConfig(config);\n        overlayConfig.direction = overlayConfig.direction || this._directionality.value;\n        return new OverlayRef(portalOutlet, host, pane, overlayConfig, this._ngZone, this._keyboardDispatcher, this._document, this._location, this._outsideClickDispatcher, this._animationsModuleType === 'NoopAnimations');\n    }\n    /**\n     * Gets a position builder that can be used, via fluent API,\n     * to construct and configure a position strategy.\n     * @returns An overlay position builder.\n     */\n    position() {\n        return this._positionBuilder;\n    }\n    /**\n     * Creates the DOM element for an overlay and appends it to the overlay container.\n     * @returns Newly-created pane element\n     */\n    _createPaneElement(host) {\n        const pane = this._document.createElement('div');\n        pane.id = `cdk-overlay-${nextUniqueId++}`;\n        pane.classList.add('cdk-overlay-pane');\n        host.appendChild(pane);\n        return pane;\n    }\n    /**\n     * Creates the host element that wraps around an overlay\n     * and can be used for advanced positioning.\n     * @returns Newly-create host element.\n     */\n    _createHostElement() {\n        const host = this._document.createElement('div');\n        this._overlayContainer.getContainerElement().appendChild(host);\n        return host;\n    }\n    /**\n     * Create a DomPortalOutlet into which the overlay content can be loaded.\n     * @param pane The DOM element to turn into a portal outlet.\n     * @returns A portal outlet for the given DOM element.\n     */\n    _createPortalOutlet(pane) {\n        // We have to resolve the ApplicationRef later in order to allow people\n        // to use overlay-based providers during app initialization.\n        if (!this._appRef) {\n            this._appRef = this._injector.get(ApplicationRef);\n        }\n        return new DomPortalOutlet(pane, this._componentFactoryResolver, this._appRef, this._injector, this._document);\n    }\n}\nOverlay.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: Overlay, deps: [{ token: ScrollStrategyOptions }, { token: OverlayContainer }, { token: i0.ComponentFactoryResolver }, { token: OverlayPositionBuilder }, { token: OverlayKeyboardDispatcher }, { token: i0.Injector }, { token: i0.NgZone }, { token: DOCUMENT }, { token: i5.Directionality }, { token: i6.Location }, { token: OverlayOutsideClickDispatcher }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nOverlay.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: Overlay });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: Overlay, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: ScrollStrategyOptions }, { type: OverlayContainer }, { type: i0.ComponentFactoryResolver }, { type: OverlayPositionBuilder }, { type: OverlayKeyboardDispatcher }, { type: i0.Injector }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i5.Directionality }, { type: i6.Location }, { type: OverlayOutsideClickDispatcher }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }, {\n                    type: Optional\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\nconst defaultPositionList = [\n    {\n        originX: 'start',\n        originY: 'bottom',\n        overlayX: 'start',\n        overlayY: 'top',\n    },\n    {\n        originX: 'start',\n        originY: 'top',\n        overlayX: 'start',\n        overlayY: 'bottom',\n    },\n    {\n        originX: 'end',\n        originY: 'top',\n        overlayX: 'end',\n        overlayY: 'bottom',\n    },\n    {\n        originX: 'end',\n        originY: 'bottom',\n        overlayX: 'end',\n        overlayY: 'top',\n    },\n];\n/** Injection token that determines the scroll handling while the connected overlay is open. */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY = new InjectionToken('cdk-connected-overlay-scroll-strategy');\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\nclass CdkOverlayOrigin {\n    constructor(\n    /** Reference to the element on which the directive is applied. */\n    elementRef) {\n        this.elementRef = elementRef;\n    }\n}\nCdkOverlayOrigin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkOverlayOrigin, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\nCdkOverlayOrigin.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: CdkOverlayOrigin, selector: \"[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]\", exportAs: [\"cdkOverlayOrigin\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkOverlayOrigin, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n                    exportAs: 'cdkOverlayOrigin',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; } });\n/**\n * Directive to facilitate declarative creation of an\n * Overlay using a FlexibleConnectedPositionStrategy.\n */\nclass CdkConnectedOverlay {\n    // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n    constructor(_overlay, templateRef, viewContainerRef, scrollStrategyFactory, _dir) {\n        this._overlay = _overlay;\n        this._dir = _dir;\n        this._hasBackdrop = false;\n        this._lockPosition = false;\n        this._growAfterOpen = false;\n        this._flexibleDimensions = false;\n        this._push = false;\n        this._backdropSubscription = Subscription.EMPTY;\n        this._attachSubscription = Subscription.EMPTY;\n        this._detachSubscription = Subscription.EMPTY;\n        this._positionSubscription = Subscription.EMPTY;\n        /** Margin between the overlay and the viewport edges. */\n        this.viewportMargin = 0;\n        /** Whether the overlay is open. */\n        this.open = false;\n        /** Whether the overlay can be closed by user interaction. */\n        this.disableClose = false;\n        /** Event emitted when the backdrop is clicked. */\n        this.backdropClick = new EventEmitter();\n        /** Event emitted when the position has changed. */\n        this.positionChange = new EventEmitter();\n        /** Event emitted when the overlay has been attached. */\n        this.attach = new EventEmitter();\n        /** Event emitted when the overlay has been detached. */\n        this.detach = new EventEmitter();\n        /** Emits when there are keyboard events that are targeted at the overlay. */\n        this.overlayKeydown = new EventEmitter();\n        /** Emits when there are mouse outside click events that are targeted at the overlay. */\n        this.overlayOutsideClick = new EventEmitter();\n        this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n        this._scrollStrategyFactory = scrollStrategyFactory;\n        this.scrollStrategy = this._scrollStrategyFactory();\n    }\n    /** The offset in pixels for the overlay connection point on the x-axis */\n    get offsetX() {\n        return this._offsetX;\n    }\n    set offsetX(offsetX) {\n        this._offsetX = offsetX;\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n        }\n    }\n    /** The offset in pixels for the overlay connection point on the y-axis */\n    get offsetY() {\n        return this._offsetY;\n    }\n    set offsetY(offsetY) {\n        this._offsetY = offsetY;\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n        }\n    }\n    /** Whether or not the overlay should attach a backdrop. */\n    get hasBackdrop() {\n        return this._hasBackdrop;\n    }\n    set hasBackdrop(value) {\n        this._hasBackdrop = coerceBooleanProperty(value);\n    }\n    /** Whether or not the overlay should be locked when scrolling. */\n    get lockPosition() {\n        return this._lockPosition;\n    }\n    set lockPosition(value) {\n        this._lockPosition = coerceBooleanProperty(value);\n    }\n    /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n    get flexibleDimensions() {\n        return this._flexibleDimensions;\n    }\n    set flexibleDimensions(value) {\n        this._flexibleDimensions = coerceBooleanProperty(value);\n    }\n    /** Whether the overlay can grow after the initial open when flexible positioning is turned on. */\n    get growAfterOpen() {\n        return this._growAfterOpen;\n    }\n    set growAfterOpen(value) {\n        this._growAfterOpen = coerceBooleanProperty(value);\n    }\n    /** Whether the overlay can be pushed on-screen if none of the provided positions fit. */\n    get push() {\n        return this._push;\n    }\n    set push(value) {\n        this._push = coerceBooleanProperty(value);\n    }\n    /** The associated overlay reference. */\n    get overlayRef() {\n        return this._overlayRef;\n    }\n    /** The element's layout direction. */\n    get dir() {\n        return this._dir ? this._dir.value : 'ltr';\n    }\n    ngOnDestroy() {\n        this._attachSubscription.unsubscribe();\n        this._detachSubscription.unsubscribe();\n        this._backdropSubscription.unsubscribe();\n        this._positionSubscription.unsubscribe();\n        if (this._overlayRef) {\n            this._overlayRef.dispose();\n        }\n    }\n    ngOnChanges(changes) {\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n            this._overlayRef.updateSize({\n                width: this.width,\n                minWidth: this.minWidth,\n                height: this.height,\n                minHeight: this.minHeight,\n            });\n            if (changes['origin'] && this.open) {\n                this._position.apply();\n            }\n        }\n        if (changes['open']) {\n            this.open ? this._attachOverlay() : this._detachOverlay();\n        }\n    }\n    /** Creates an overlay */\n    _createOverlay() {\n        if (!this.positions || !this.positions.length) {\n            this.positions = defaultPositionList;\n        }\n        const overlayRef = (this._overlayRef = this._overlay.create(this._buildConfig()));\n        this._attachSubscription = overlayRef.attachments().subscribe(() => this.attach.emit());\n        this._detachSubscription = overlayRef.detachments().subscribe(() => this.detach.emit());\n        overlayRef.keydownEvents().subscribe((event) => {\n            this.overlayKeydown.next(event);\n            if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n                event.preventDefault();\n                this._detachOverlay();\n            }\n        });\n        this._overlayRef.outsidePointerEvents().subscribe((event) => {\n            this.overlayOutsideClick.next(event);\n        });\n    }\n    /** Builds the overlay config based on the directive's inputs */\n    _buildConfig() {\n        const positionStrategy = (this._position =\n            this.positionStrategy || this._createPositionStrategy());\n        const overlayConfig = new OverlayConfig({\n            direction: this._dir,\n            positionStrategy,\n            scrollStrategy: this.scrollStrategy,\n            hasBackdrop: this.hasBackdrop,\n        });\n        if (this.width || this.width === 0) {\n            overlayConfig.width = this.width;\n        }\n        if (this.height || this.height === 0) {\n            overlayConfig.height = this.height;\n        }\n        if (this.minWidth || this.minWidth === 0) {\n            overlayConfig.minWidth = this.minWidth;\n        }\n        if (this.minHeight || this.minHeight === 0) {\n            overlayConfig.minHeight = this.minHeight;\n        }\n        if (this.backdropClass) {\n            overlayConfig.backdropClass = this.backdropClass;\n        }\n        if (this.panelClass) {\n            overlayConfig.panelClass = this.panelClass;\n        }\n        return overlayConfig;\n    }\n    /** Updates the state of a position strategy, based on the values of the directive inputs. */\n    _updatePositionStrategy(positionStrategy) {\n        const positions = this.positions.map(currentPosition => ({\n            originX: currentPosition.originX,\n            originY: currentPosition.originY,\n            overlayX: currentPosition.overlayX,\n            overlayY: currentPosition.overlayY,\n            offsetX: currentPosition.offsetX || this.offsetX,\n            offsetY: currentPosition.offsetY || this.offsetY,\n            panelClass: currentPosition.panelClass || undefined,\n        }));\n        return positionStrategy\n            .setOrigin(this._getFlexibleConnectedPositionStrategyOrigin())\n            .withPositions(positions)\n            .withFlexibleDimensions(this.flexibleDimensions)\n            .withPush(this.push)\n            .withGrowAfterOpen(this.growAfterOpen)\n            .withViewportMargin(this.viewportMargin)\n            .withLockedPosition(this.lockPosition)\n            .withTransformOriginOn(this.transformOriginSelector);\n    }\n    /** Returns the position strategy of the overlay to be set on the overlay config */\n    _createPositionStrategy() {\n        const strategy = this._overlay\n            .position()\n            .flexibleConnectedTo(this._getFlexibleConnectedPositionStrategyOrigin());\n        this._updatePositionStrategy(strategy);\n        return strategy;\n    }\n    _getFlexibleConnectedPositionStrategyOrigin() {\n        if (this.origin instanceof CdkOverlayOrigin) {\n            return this.origin.elementRef;\n        }\n        else {\n            return this.origin;\n        }\n    }\n    /** Attaches the overlay and subscribes to backdrop clicks if backdrop exists */\n    _attachOverlay() {\n        if (!this._overlayRef) {\n            this._createOverlay();\n        }\n        else {\n            // Update the overlay size, in case the directive's inputs have changed\n            this._overlayRef.getConfig().hasBackdrop = this.hasBackdrop;\n        }\n        if (!this._overlayRef.hasAttached()) {\n            this._overlayRef.attach(this._templatePortal);\n        }\n        if (this.hasBackdrop) {\n            this._backdropSubscription = this._overlayRef.backdropClick().subscribe(event => {\n                this.backdropClick.emit(event);\n            });\n        }\n        else {\n            this._backdropSubscription.unsubscribe();\n        }\n        this._positionSubscription.unsubscribe();\n        // Only subscribe to `positionChanges` if requested, because putting\n        // together all the information for it can be expensive.\n        if (this.positionChange.observers.length > 0) {\n            this._positionSubscription = this._position.positionChanges\n                .pipe(takeWhile(() => this.positionChange.observers.length > 0))\n                .subscribe(position => {\n                this.positionChange.emit(position);\n                if (this.positionChange.observers.length === 0) {\n                    this._positionSubscription.unsubscribe();\n                }\n            });\n        }\n    }\n    /** Detaches the overlay and unsubscribes to backdrop clicks if backdrop exists */\n    _detachOverlay() {\n        if (this._overlayRef) {\n            this._overlayRef.detach();\n        }\n        this._backdropSubscription.unsubscribe();\n        this._positionSubscription.unsubscribe();\n    }\n}\nCdkConnectedOverlay.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkConnectedOverlay, deps: [{ token: Overlay }, { token: i0.TemplateRef }, { token: i0.ViewContainerRef }, { token: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY }, { token: i5.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nCdkConnectedOverlay.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: CdkConnectedOverlay, selector: \"[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]\", inputs: { origin: [\"cdkConnectedOverlayOrigin\", \"origin\"], positions: [\"cdkConnectedOverlayPositions\", \"positions\"], positionStrategy: [\"cdkConnectedOverlayPositionStrategy\", \"positionStrategy\"], offsetX: [\"cdkConnectedOverlayOffsetX\", \"offsetX\"], offsetY: [\"cdkConnectedOverlayOffsetY\", \"offsetY\"], width: [\"cdkConnectedOverlayWidth\", \"width\"], height: [\"cdkConnectedOverlayHeight\", \"height\"], minWidth: [\"cdkConnectedOverlayMinWidth\", \"minWidth\"], minHeight: [\"cdkConnectedOverlayMinHeight\", \"minHeight\"], backdropClass: [\"cdkConnectedOverlayBackdropClass\", \"backdropClass\"], panelClass: [\"cdkConnectedOverlayPanelClass\", \"panelClass\"], viewportMargin: [\"cdkConnectedOverlayViewportMargin\", \"viewportMargin\"], scrollStrategy: [\"cdkConnectedOverlayScrollStrategy\", \"scrollStrategy\"], open: [\"cdkConnectedOverlayOpen\", \"open\"], disableClose: [\"cdkConnectedOverlayDisableClose\", \"disableClose\"], transformOriginSelector: [\"cdkConnectedOverlayTransformOriginOn\", \"transformOriginSelector\"], hasBackdrop: [\"cdkConnectedOverlayHasBackdrop\", \"hasBackdrop\"], lockPosition: [\"cdkConnectedOverlayLockPosition\", \"lockPosition\"], flexibleDimensions: [\"cdkConnectedOverlayFlexibleDimensions\", \"flexibleDimensions\"], growAfterOpen: [\"cdkConnectedOverlayGrowAfterOpen\", \"growAfterOpen\"], push: [\"cdkConnectedOverlayPush\", \"push\"] }, outputs: { backdropClick: \"backdropClick\", positionChange: \"positionChange\", attach: \"attach\", detach: \"detach\", overlayKeydown: \"overlayKeydown\", overlayOutsideClick: \"overlayOutsideClick\" }, exportAs: [\"cdkConnectedOverlay\"], usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkConnectedOverlay, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n                    exportAs: 'cdkConnectedOverlay',\n                }]\n        }], ctorParameters: function () { return [{ type: Overlay }, { type: i0.TemplateRef }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY]\n                }] }, { type: i5.Directionality, decorators: [{\n                    type: Optional\n                }] }]; }, propDecorators: { origin: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOrigin']\n            }], positions: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPositions']\n            }], positionStrategy: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPositionStrategy']\n            }], offsetX: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOffsetX']\n            }], offsetY: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOffsetY']\n            }], width: [{\n                type: Input,\n                args: ['cdkConnectedOverlayWidth']\n            }], height: [{\n                type: Input,\n                args: ['cdkConnectedOverlayHeight']\n            }], minWidth: [{\n                type: Input,\n                args: ['cdkConnectedOverlayMinWidth']\n            }], minHeight: [{\n                type: Input,\n                args: ['cdkConnectedOverlayMinHeight']\n            }], backdropClass: [{\n                type: Input,\n                args: ['cdkConnectedOverlayBackdropClass']\n            }], panelClass: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPanelClass']\n            }], viewportMargin: [{\n                type: Input,\n                args: ['cdkConnectedOverlayViewportMargin']\n            }], scrollStrategy: [{\n                type: Input,\n                args: ['cdkConnectedOverlayScrollStrategy']\n            }], open: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOpen']\n            }], disableClose: [{\n                type: Input,\n                args: ['cdkConnectedOverlayDisableClose']\n            }], transformOriginSelector: [{\n                type: Input,\n                args: ['cdkConnectedOverlayTransformOriginOn']\n            }], hasBackdrop: [{\n                type: Input,\n                args: ['cdkConnectedOverlayHasBackdrop']\n            }], lockPosition: [{\n                type: Input,\n                args: ['cdkConnectedOverlayLockPosition']\n            }], flexibleDimensions: [{\n                type: Input,\n                args: ['cdkConnectedOverlayFlexibleDimensions']\n            }], growAfterOpen: [{\n                type: Input,\n                args: ['cdkConnectedOverlayGrowAfterOpen']\n            }], push: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPush']\n            }], backdropClick: [{\n                type: Output\n            }], positionChange: [{\n                type: Output\n            }], attach: [{\n                type: Output\n            }], detach: [{\n                type: Output\n            }], overlayKeydown: [{\n                type: Output\n            }], overlayOutsideClick: [{\n                type: Output\n            }] } });\n/** @docs-private */\nfunction CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n    provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass OverlayModule {\n}\nOverlayModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: OverlayModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nOverlayModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: OverlayModule, declarations: [CdkConnectedOverlay, CdkOverlayOrigin], imports: [BidiModule, PortalModule, ScrollingModule], exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule] });\nOverlayModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: OverlayModule, providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER], imports: [BidiModule, PortalModule, ScrollingModule, ScrollingModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: OverlayModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [BidiModule, PortalModule, ScrollingModule],\n                    exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule],\n                    declarations: [CdkConnectedOverlay, CdkOverlayOrigin],\n                    providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\nclass FullscreenOverlayContainer extends OverlayContainer {\n    constructor(_document, platform) {\n        super(_document, platform);\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        if (this._fullScreenEventName && this._fullScreenListener) {\n            this._document.removeEventListener(this._fullScreenEventName, this._fullScreenListener);\n        }\n    }\n    _createContainer() {\n        super._createContainer();\n        this._adjustParentForFullscreenChange();\n        this._addFullscreenChangeListener(() => this._adjustParentForFullscreenChange());\n    }\n    _adjustParentForFullscreenChange() {\n        if (!this._containerElement) {\n            return;\n        }\n        const fullscreenElement = this.getFullscreenElement();\n        const parent = fullscreenElement || this._document.body;\n        parent.appendChild(this._containerElement);\n    }\n    _addFullscreenChangeListener(fn) {\n        const eventName = this._getEventName();\n        if (eventName) {\n            if (this._fullScreenListener) {\n                this._document.removeEventListener(eventName, this._fullScreenListener);\n            }\n            this._document.addEventListener(eventName, fn);\n            this._fullScreenListener = fn;\n        }\n    }\n    _getEventName() {\n        if (!this._fullScreenEventName) {\n            const _document = this._document;\n            if (_document.fullscreenEnabled) {\n                this._fullScreenEventName = 'fullscreenchange';\n            }\n            else if (_document.webkitFullscreenEnabled) {\n                this._fullScreenEventName = 'webkitfullscreenchange';\n            }\n            else if (_document.mozFullScreenEnabled) {\n                this._fullScreenEventName = 'mozfullscreenchange';\n            }\n            else if (_document.msFullscreenEnabled) {\n                this._fullScreenEventName = 'MSFullscreenChange';\n            }\n        }\n        return this._fullScreenEventName;\n    }\n    /**\n     * When the page is put into fullscreen mode, a specific element is specified.\n     * Only that element and its children are visible when in fullscreen mode.\n     */\n    getFullscreenElement() {\n        const _document = this._document;\n        return (_document.fullscreenElement ||\n            _document.webkitFullscreenElement ||\n            _document.mozFullScreenElement ||\n            _document.msFullscreenElement ||\n            null);\n    }\n}\nFullscreenOverlayContainer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: FullscreenOverlayContainer, deps: [{ token: DOCUMENT }, { token: i1$1.Platform }], target: i0.ɵɵFactoryTarget.Injectable });\nFullscreenOverlayContainer.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: FullscreenOverlayContainer, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: FullscreenOverlayContainer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1$1.Platform }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BlockScrollStrategy, CdkConnectedOverlay, CdkOverlayOrigin, CloseScrollStrategy, ConnectedOverlayPositionChange, ConnectionPositionPair, FlexibleConnectedPositionStrategy, FullscreenOverlayContainer, GlobalPositionStrategy, NoopScrollStrategy, Overlay, OverlayConfig, OverlayContainer, OverlayKeyboardDispatcher, OverlayModule, OverlayOutsideClickDispatcher, OverlayPositionBuilder, OverlayRef, RepositionScrollStrategy, STANDARD_DROPDOWN_ADJACENT_POSITIONS, STANDARD_DROPDOWN_BELOW_POSITIONS, ScrollStrategyOptions, ScrollingVisibility, validateHorizontalPosition, validateVerticalPosition };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,wBAApB;AACA,SAASC,eAAT,QAAgC,wBAAhC;AACA,SAASC,aAAT,EAAwBC,gBAAxB,EAA0CC,aAA1C,QAA+D,wBAA/D;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,QAAT,QAAyB,iBAAzB;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,MAArB,EAA6BC,QAA7B,EAAuCC,UAAvC,EAAmDC,cAAnD,EAAmEC,qBAAnE,EAA0FC,cAA1F,EAA0GC,SAA1G,EAAqHC,YAArH,EAAmIC,KAAnI,EAA0IC,MAA1I,EAAkJC,QAAlJ,QAAkK,eAAlK;AACA,SAASC,mBAAT,EAA8BC,WAA9B,EAA2CC,qBAA3C,QAAwE,uBAAxE;AACA,OAAO,KAAKC,IAAZ,MAAsB,uBAAtB;AACA,SAASC,sBAAT,EAAiCC,eAAjC,EAAkDC,kBAAlD,QAA4E,uBAA5E;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,SAASC,UAAT,QAA2B,mBAA3B;AACA,SAASC,eAAT,EAA0BC,cAA1B,EAA0CC,YAA1C,QAA8D,qBAA9D;AACA,SAASC,OAAT,EAAkBC,YAAlB,EAAgCC,KAAhC,QAA6C,MAA7C;AACA,SAASC,IAAT,EAAeC,SAAf,EAA0BC,SAA1B,QAA2C,gBAA3C;AACA,SAASC,MAAT,EAAiBC,cAAjB,QAAuC,uBAAvC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,uBAAuB,GAAGhB,sBAAsB,EAAtD;AACA;AACA;AACA;;AACA,MAAMiB,mBAAN,CAA0B;EACtBC,WAAW,CAACC,cAAD,EAAiBC,QAAjB,EAA2B;IAClC,KAAKD,cAAL,GAAsBA,cAAtB;IACA,KAAKE,mBAAL,GAA2B;MAAEC,GAAG,EAAE,EAAP;MAAWC,IAAI,EAAE;IAAjB,CAA3B;IACA,KAAKC,UAAL,GAAkB,KAAlB;IACA,KAAKC,SAAL,GAAiBL,QAAjB;EACH;EACD;;;EACAM,MAAM,GAAG,CAAG;EACZ;;;EACAC,MAAM,GAAG;IACL,IAAI,KAAKC,aAAL,EAAJ,EAA0B;MACtB,MAAMC,IAAI,GAAG,KAAKJ,SAAL,CAAeK,eAA5B;MACA,KAAKC,uBAAL,GAA+B,KAAKZ,cAAL,CAAoBa,yBAApB,EAA/B,CAFsB,CAGtB;;MACA,KAAKX,mBAAL,CAAyBE,IAAzB,GAAgCM,IAAI,CAACI,KAAL,CAAWV,IAAX,IAAmB,EAAnD;MACA,KAAKF,mBAAL,CAAyBC,GAAzB,GAA+BO,IAAI,CAACI,KAAL,CAAWX,GAAX,IAAkB,EAAjD,CALsB,CAMtB;MACA;;MACAO,IAAI,CAACI,KAAL,CAAWV,IAAX,GAAkB3B,mBAAmB,CAAC,CAAC,KAAKmC,uBAAL,CAA6BR,IAA/B,CAArC;MACAM,IAAI,CAACI,KAAL,CAAWX,GAAX,GAAiB1B,mBAAmB,CAAC,CAAC,KAAKmC,uBAAL,CAA6BT,GAA/B,CAApC;MACAO,IAAI,CAACK,SAAL,CAAeC,GAAf,CAAmB,wBAAnB;MACA,KAAKX,UAAL,GAAkB,IAAlB;IACH;EACJ;EACD;;;EACAY,OAAO,GAAG;IACN,IAAI,KAAKZ,UAAT,EAAqB;MACjB,MAAMa,IAAI,GAAG,KAAKZ,SAAL,CAAeK,eAA5B;MACA,MAAMQ,IAAI,GAAG,KAAKb,SAAL,CAAea,IAA5B;MACA,MAAMC,SAAS,GAAGF,IAAI,CAACJ,KAAvB;MACA,MAAMO,SAAS,GAAGF,IAAI,CAACL,KAAvB;MACA,MAAMQ,0BAA0B,GAAGF,SAAS,CAACG,cAAV,IAA4B,EAA/D;MACA,MAAMC,0BAA0B,GAAGH,SAAS,CAACE,cAAV,IAA4B,EAA/D;MACA,KAAKlB,UAAL,GAAkB,KAAlB;MACAe,SAAS,CAAChB,IAAV,GAAiB,KAAKF,mBAAL,CAAyBE,IAA1C;MACAgB,SAAS,CAACjB,GAAV,GAAgB,KAAKD,mBAAL,CAAyBC,GAAzC;MACAe,IAAI,CAACH,SAAL,CAAeU,MAAf,CAAsB,wBAAtB,EAViB,CAWjB;MACA;MACA;MACA;MACA;;MACA,IAAI5B,uBAAJ,EAA6B;QACzBuB,SAAS,CAACG,cAAV,GAA2BF,SAAS,CAACE,cAAV,GAA2B,MAAtD;MACH;;MACDG,MAAM,CAACC,MAAP,CAAc,KAAKf,uBAAL,CAA6BR,IAA3C,EAAiD,KAAKQ,uBAAL,CAA6BT,GAA9E;;MACA,IAAIN,uBAAJ,EAA6B;QACzBuB,SAAS,CAACG,cAAV,GAA2BD,0BAA3B;QACAD,SAAS,CAACE,cAAV,GAA2BC,0BAA3B;MACH;IACJ;EACJ;;EACDf,aAAa,GAAG;IACZ;IACA;IACA;IACA,MAAMS,IAAI,GAAG,KAAKZ,SAAL,CAAeK,eAA5B;;IACA,IAAIO,IAAI,CAACH,SAAL,CAAea,QAAf,CAAwB,wBAAxB,KAAqD,KAAKvB,UAA9D,EAA0E;MACtE,OAAO,KAAP;IACH;;IACD,MAAMc,IAAI,GAAG,KAAKb,SAAL,CAAea,IAA5B;;IACA,MAAMU,QAAQ,GAAG,KAAK7B,cAAL,CAAoB8B,eAApB,EAAjB;;IACA,OAAOX,IAAI,CAACY,YAAL,GAAoBF,QAAQ,CAACG,MAA7B,IAAuCb,IAAI,CAACc,WAAL,GAAmBJ,QAAQ,CAACK,KAA1E;EACH;;AAhEqB;AAmE1B;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,SAASC,wCAAT,GAAoD;EAChD,OAAOC,KAAK,CAAE,4CAAF,CAAZ;AACH;AAED;AACA;AACA;;;AACA,MAAMC,mBAAN,CAA0B;EACtBtC,WAAW,CAACuC,iBAAD,EAAoBC,OAApB,EAA6BvC,cAA7B,EAA6CwC,OAA7C,EAAsD;IAC7D,KAAKF,iBAAL,GAAyBA,iBAAzB;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKvC,cAAL,GAAsBA,cAAtB;IACA,KAAKwC,OAAL,GAAeA,OAAf;IACA,KAAKC,mBAAL,GAA2B,IAA3B;IACA;;IACA,KAAKC,OAAL,GAAe,MAAM;MACjB,KAAKzB,OAAL;;MACA,IAAI,KAAK0B,WAAL,CAAiBC,WAAjB,EAAJ,EAAoC;QAChC,KAAKL,OAAL,CAAaM,GAAb,CAAiB,MAAM,KAAKF,WAAL,CAAiBG,MAAjB,EAAvB;MACH;IACJ,CALD;EAMH;EACD;;;EACAvC,MAAM,CAACwC,UAAD,EAAa;IACf,IAAI,KAAKJ,WAAL,KAAqB,OAAOK,SAAP,KAAqB,WAArB,IAAoCA,SAAzD,CAAJ,EAAyE;MACrE,MAAMb,wCAAwC,EAA9C;IACH;;IACD,KAAKQ,WAAL,GAAmBI,UAAnB;EACH;EACD;;;EACAvC,MAAM,GAAG;IACL,IAAI,KAAKiC,mBAAT,EAA8B;MAC1B;IACH;;IACD,MAAMQ,MAAM,GAAG,KAAKX,iBAAL,CAAuBY,QAAvB,CAAgC,CAAhC,CAAf;;IACA,IAAI,KAAKV,OAAL,IAAgB,KAAKA,OAAL,CAAaW,SAA7B,IAA0C,KAAKX,OAAL,CAAaW,SAAb,GAAyB,CAAvE,EAA0E;MACtE,KAAKC,sBAAL,GAA8B,KAAKpD,cAAL,CAAoBa,yBAApB,GAAgDV,GAA9E;MACA,KAAKsC,mBAAL,GAA2BQ,MAAM,CAACI,SAAP,CAAiB,MAAM;QAC9C,MAAMC,cAAc,GAAG,KAAKtD,cAAL,CAAoBa,yBAApB,GAAgDV,GAAvE;;QACA,IAAIoD,IAAI,CAACC,GAAL,CAASF,cAAc,GAAG,KAAKF,sBAA/B,IAAyD,KAAKZ,OAAL,CAAaW,SAA1E,EAAqF;UACjF,KAAKT,OAAL;QACH,CAFD,MAGK;UACD,KAAKC,WAAL,CAAiBc,cAAjB;QACH;MACJ,CAR0B,CAA3B;IASH,CAXD,MAYK;MACD,KAAKhB,mBAAL,GAA2BQ,MAAM,CAACI,SAAP,CAAiB,KAAKX,OAAtB,CAA3B;IACH;EACJ;EACD;;;EACAzB,OAAO,GAAG;IACN,IAAI,KAAKwB,mBAAT,EAA8B;MAC1B,KAAKA,mBAAL,CAAyBiB,WAAzB;;MACA,KAAKjB,mBAAL,GAA2B,IAA3B;IACH;EACJ;;EACDK,MAAM,GAAG;IACL,KAAK7B,OAAL;IACA,KAAK0B,WAAL,GAAmB,IAAnB;EACH;;AAtDqB;AAyD1B;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMgB,kBAAN,CAAyB;EACrB;EACAnD,MAAM,GAAG,CAAG;EACZ;;;EACAS,OAAO,GAAG,CAAG;EACb;;;EACAV,MAAM,GAAG,CAAG;;AANS;AASzB;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASqD,4BAAT,CAAsCC,OAAtC,EAA+CC,gBAA/C,EAAiE;EAC7D,OAAOA,gBAAgB,CAACC,IAAjB,CAAsBC,eAAe,IAAI;IAC5C,MAAMC,YAAY,GAAGJ,OAAO,CAACK,MAAR,GAAiBF,eAAe,CAAC7D,GAAtD;IACA,MAAMgE,YAAY,GAAGN,OAAO,CAAC1D,GAAR,GAAc6D,eAAe,CAACE,MAAnD;IACA,MAAME,WAAW,GAAGP,OAAO,CAACQ,KAAR,GAAgBL,eAAe,CAAC5D,IAApD;IACA,MAAMkE,YAAY,GAAGT,OAAO,CAACzD,IAAR,GAAe4D,eAAe,CAACK,KAApD;IACA,OAAOJ,YAAY,IAAIE,YAAhB,IAAgCC,WAAhC,IAA+CE,YAAtD;EACH,CANM,CAAP;AAOH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,2BAAT,CAAqCV,OAArC,EAA8CC,gBAA9C,EAAgE;EAC5D,OAAOA,gBAAgB,CAACC,IAAjB,CAAsBS,mBAAmB,IAAI;IAChD,MAAMC,YAAY,GAAGZ,OAAO,CAAC1D,GAAR,GAAcqE,mBAAmB,CAACrE,GAAvD;IACA,MAAMuE,YAAY,GAAGb,OAAO,CAACK,MAAR,GAAiBM,mBAAmB,CAACN,MAA1D;IACA,MAAMS,WAAW,GAAGd,OAAO,CAACzD,IAAR,GAAeoE,mBAAmB,CAACpE,IAAvD;IACA,MAAMwE,YAAY,GAAGf,OAAO,CAACQ,KAAR,GAAgBG,mBAAmB,CAACH,KAAzD;IACA,OAAOI,YAAY,IAAIC,YAAhB,IAAgCC,WAAhC,IAA+CC,YAAtD;EACH,CANM,CAAP;AAOH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMC,wBAAN,CAA+B;EAC3B9E,WAAW,CAACuC,iBAAD,EAAoBtC,cAApB,EAAoCuC,OAApC,EAA6CC,OAA7C,EAAsD;IAC7D,KAAKF,iBAAL,GAAyBA,iBAAzB;IACA,KAAKtC,cAAL,GAAsBA,cAAtB;IACA,KAAKuC,OAAL,GAAeA,OAAf;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKC,mBAAL,GAA2B,IAA3B;EACH;EACD;;;EACAlC,MAAM,CAACwC,UAAD,EAAa;IACf,IAAI,KAAKJ,WAAL,KAAqB,OAAOK,SAAP,KAAqB,WAArB,IAAoCA,SAAzD,CAAJ,EAAyE;MACrE,MAAMb,wCAAwC,EAA9C;IACH;;IACD,KAAKQ,WAAL,GAAmBI,UAAnB;EACH;EACD;;;EACAvC,MAAM,GAAG;IACL,IAAI,CAAC,KAAKiC,mBAAV,EAA+B;MAC3B,MAAMqC,QAAQ,GAAG,KAAKtC,OAAL,GAAe,KAAKA,OAAL,CAAauC,cAA5B,GAA6C,CAA9D;MACA,KAAKtC,mBAAL,GAA2B,KAAKH,iBAAL,CAAuBY,QAAvB,CAAgC4B,QAAhC,EAA0CzB,SAA1C,CAAoD,MAAM;QACjF,KAAKV,WAAL,CAAiBc,cAAjB,GADiF,CAEjF;;;QACA,IAAI,KAAKjB,OAAL,IAAgB,KAAKA,OAAL,CAAawC,SAAjC,EAA4C;UACxC,MAAMC,WAAW,GAAG,KAAKtC,WAAL,CAAiBuC,cAAjB,CAAgCC,qBAAhC,EAApB;;UACA,MAAM;YAAEjD,KAAF;YAASF;UAAT,IAAoB,KAAKhC,cAAL,CAAoB8B,eAApB,EAA1B,CAFwC,CAGxC;UACA;;;UACA,MAAMsD,WAAW,GAAG,CAAC;YAAElD,KAAF;YAASF,MAAT;YAAiBkC,MAAM,EAAElC,MAAzB;YAAiCqC,KAAK,EAAEnC,KAAxC;YAA+C/B,GAAG,EAAE,CAApD;YAAuDC,IAAI,EAAE;UAA7D,CAAD,CAApB;;UACA,IAAIwD,4BAA4B,CAACqB,WAAD,EAAcG,WAAd,CAAhC,EAA4D;YACxD,KAAKnE,OAAL;;YACA,KAAKsB,OAAL,CAAaM,GAAb,CAAiB,MAAM,KAAKF,WAAL,CAAiBG,MAAjB,EAAvB;UACH;QACJ;MACJ,CAd0B,CAA3B;IAeH;EACJ;EACD;;;EACA7B,OAAO,GAAG;IACN,IAAI,KAAKwB,mBAAT,EAA8B;MAC1B,KAAKA,mBAAL,CAAyBiB,WAAzB;;MACA,KAAKjB,mBAAL,GAA2B,IAA3B;IACH;EACJ;;EACDK,MAAM,GAAG;IACL,KAAK7B,OAAL;IACA,KAAK0B,WAAL,GAAmB,IAAnB;EACH;;AA9C0B;AAiD/B;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM0C,qBAAN,CAA4B;EACxBtF,WAAW,CAACuC,iBAAD,EAAoBtC,cAApB,EAAoCuC,OAApC,EAA6CtC,QAA7C,EAAuD;IAC9D,KAAKqC,iBAAL,GAAyBA,iBAAzB;IACA,KAAKtC,cAAL,GAAsBA,cAAtB;IACA,KAAKuC,OAAL,GAAeA,OAAf;IACA;;IACA,KAAK+C,IAAL,GAAY,MAAM,IAAI3B,kBAAJ,EAAlB;IACA;AACR;AACA;AACA;;;IACQ,KAAK4B,KAAL,GAAcC,MAAD,IAAY,IAAInD,mBAAJ,CAAwB,KAAKC,iBAA7B,EAAgD,KAAKC,OAArD,EAA8D,KAAKvC,cAAnE,EAAmFwF,MAAnF,CAAzB;IACA;;;IACA,KAAKC,KAAL,GAAa,MAAM,IAAI3F,mBAAJ,CAAwB,KAAKE,cAA7B,EAA6C,KAAKM,SAAlD,CAAnB;IACA;AACR;AACA;AACA;AACA;;;IACQ,KAAKoF,UAAL,GAAmBF,MAAD,IAAY,IAAIX,wBAAJ,CAA6B,KAAKvC,iBAAlC,EAAqD,KAAKtC,cAA1D,EAA0E,KAAKuC,OAA/E,EAAwFiD,MAAxF,CAA9B;;IACA,KAAKlF,SAAL,GAAiBL,QAAjB;EACH;;AArBuB;;AAuB5BoF,qBAAqB,CAACM,IAAtB;EAAA,iBAAkHN,qBAAlH,EAAwGzH,EAAxG,UAAyJP,EAAE,CAACG,gBAA5J,GAAwGI,EAAxG,UAAyLP,EAAE,CAACI,aAA5L,GAAwGG,EAAxG,UAAsNA,EAAE,CAACgI,MAAzN,GAAwGhI,EAAxG,UAA4OD,QAA5O;AAAA;;AACA0H,qBAAqB,CAACQ,KAAtB,kBADwGjI,EACxG;EAAA,OAAsHyH,qBAAtH;EAAA,SAAsHA,qBAAtH;EAAA,YAAyJ;AAAzJ;;AACA;EAAA,mDAFwGzH,EAExG,mBAA2FyH,qBAA3F,EAA8H,CAAC;IACnHS,IAAI,EAAEjI,UAD6G;IAEnHkI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAF6G,CAAD,CAA9H,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAEzI,EAAE,CAACG;IAAX,CAAD,EAAgC;MAAEsI,IAAI,EAAEzI,EAAE,CAACI;IAAX,CAAhC,EAA4D;MAAEqI,IAAI,EAAElI,EAAE,CAACgI;IAAX,CAA5D,EAAiF;MAAEE,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9IJ,IAAI,EAAEhI,MADwI;QAE9IiI,IAAI,EAAE,CAACpI,QAAD;MAFwI,CAAD;IAA/B,CAAjF,CAAP;EAGlB,CANxB;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMwI,aAAN,CAAoB;EAChBpG,WAAW,CAACyF,MAAD,EAAS;IAChB;IACA,KAAKY,cAAL,GAAsB,IAAIzC,kBAAJ,EAAtB;IACA;;IACA,KAAK0C,UAAL,GAAkB,EAAlB;IACA;;IACA,KAAKC,WAAL,GAAmB,KAAnB;IACA;;IACA,KAAKC,aAAL,GAAqB,2BAArB;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKC,mBAAL,GAA2B,KAA3B;;IACA,IAAIhB,MAAJ,EAAY;MACR;MACA;MACA;MACA,MAAMiB,UAAU,GAAGC,MAAM,CAACC,IAAP,CAAYnB,MAAZ,CAAnB;;MACA,KAAK,MAAMoB,GAAX,IAAkBH,UAAlB,EAA8B;QAC1B,IAAIjB,MAAM,CAACoB,GAAD,CAAN,KAAgBX,SAApB,EAA+B;UAC3B;UACA;UACA;UACA;UACA;UACA;UACA,KAAKW,GAAL,IAAYpB,MAAM,CAACoB,GAAD,CAAlB;QACH;MACJ;IACJ;EACJ;;AAjCe;AAoCpB;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMC,sBAAN,CAA6B;EACzB9G,WAAW,CAAC+G,MAAD,EAASC,OAAT;EACX;EACAC,OAFW;EAGX;EACAC,OAJW;EAKX;EACAZ,UANW,EAMC;IACR,KAAKW,OAAL,GAAeA,OAAf;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKZ,UAAL,GAAkBA,UAAlB;IACA,KAAKa,OAAL,GAAeJ,MAAM,CAACI,OAAtB;IACA,KAAKC,OAAL,GAAeL,MAAM,CAACK,OAAtB;IACA,KAAKC,QAAL,GAAgBL,OAAO,CAACK,QAAxB;IACA,KAAKC,QAAL,GAAgBN,OAAO,CAACM,QAAxB;EACH;;AAfwB;AAiB7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,mBAAN,CAA0B;AAE1B;;;AACA,MAAMC,8BAAN,CAAqC;EACjCxH,WAAW;EACX;EACAyH,cAFW;EAGX;EACAC,wBAJW,EAIe;IACtB,KAAKD,cAAL,GAAsBA,cAAtB;IACA,KAAKC,wBAAL,GAAgCA,wBAAhC;EACH;;AARgC;AAUrC;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,wBAAT,CAAkCC,QAAlC,EAA4CC,KAA5C,EAAmD;EAC/C,IAAIA,KAAK,KAAK,KAAV,IAAmBA,KAAK,KAAK,QAA7B,IAAyCA,KAAK,KAAK,QAAvD,EAAiE;IAC7D,MAAMxF,KAAK,CAAE,8BAA6BuF,QAAS,KAAIC,KAAM,KAAjD,GACP,uCADM,CAAX;EAEH;AACJ;AACD;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,0BAAT,CAAoCF,QAApC,EAA8CC,KAA9C,EAAqD;EACjD,IAAIA,KAAK,KAAK,OAAV,IAAqBA,KAAK,KAAK,KAA/B,IAAwCA,KAAK,KAAK,QAAtD,EAAgE;IAC5D,MAAMxF,KAAK,CAAE,8BAA6BuF,QAAS,KAAIC,KAAM,KAAjD,GACP,sCADM,CAAX;EAEH;AACJ;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAME,qBAAN,CAA4B;EACxB/H,WAAW,CAACE,QAAD,EAAW;IAClB;IACA,KAAK8H,iBAAL,GAAyB,EAAzB;IACA,KAAKzH,SAAL,GAAiBL,QAAjB;EACH;;EACD+H,WAAW,GAAG;IACV,KAAKlF,MAAL;EACH;EACD;;;EACA9B,GAAG,CAAC+B,UAAD,EAAa;IACZ;IACA,KAAKtB,MAAL,CAAYsB,UAAZ;;IACA,KAAKgF,iBAAL,CAAuBE,IAAvB,CAA4BlF,UAA5B;EACH;EACD;;;EACAtB,MAAM,CAACsB,UAAD,EAAa;IACf,MAAMmF,KAAK,GAAG,KAAKH,iBAAL,CAAuBI,OAAvB,CAA+BpF,UAA/B,CAAd;;IACA,IAAImF,KAAK,GAAG,CAAC,CAAb,EAAgB;MACZ,KAAKH,iBAAL,CAAuBK,MAAvB,CAA8BF,KAA9B,EAAqC,CAArC;IACH,CAJc,CAKf;;;IACA,IAAI,KAAKH,iBAAL,CAAuBM,MAAvB,KAAkC,CAAtC,EAAyC;MACrC,KAAKvF,MAAL;IACH;EACJ;;AAzBuB;;AA2B5BgF,qBAAqB,CAACnC,IAAtB;EAAA,iBAAkHmC,qBAAlH,EA7LwGlK,EA6LxG,UAAyJD,QAAzJ;AAAA;;AACAmK,qBAAqB,CAACjC,KAAtB,kBA9LwGjI,EA8LxG;EAAA,OAAsHkK,qBAAtH;EAAA,SAAsHA,qBAAtH;EAAA,YAAyJ;AAAzJ;;AACA;EAAA,mDA/LwGlK,EA+LxG,mBAA2FkK,qBAA3F,EAA8H,CAAC;IACnHhC,IAAI,EAAEjI,UAD6G;IAEnHkI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAF6G,CAAD,CAA9H,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9DJ,IAAI,EAAEhI,MADwD;QAE9DiI,IAAI,EAAE,CAACpI,QAAD;MAFwD,CAAD;IAA/B,CAAD,CAAP;EAGlB,CANxB;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM2K,yBAAN,SAAwCR,qBAAxC,CAA8D;EAC1D/H,WAAW,CAACE,QAAD;EACX;EACAsC,OAFW,EAEF;IACL,MAAMtC,QAAN;IACA,KAAKsC,OAAL,GAAeA,OAAf;IACA;;IACA,KAAKgG,gBAAL,GAAyBC,KAAD,IAAW;MAC/B,MAAMC,QAAQ,GAAG,KAAKV,iBAAtB;;MACA,KAAK,IAAIW,CAAC,GAAGD,QAAQ,CAACJ,MAAT,GAAkB,CAA/B,EAAkCK,CAAC,GAAG,CAAC,CAAvC,EAA0CA,CAAC,EAA3C,EAA+C;QAC3C;QACA;QACA;QACA;QACA;QACA;QACA,IAAID,QAAQ,CAACC,CAAD,CAAR,CAAYC,cAAZ,CAA2BC,SAA3B,CAAqCP,MAArC,GAA8C,CAAlD,EAAqD;UACjD,MAAMQ,aAAa,GAAGJ,QAAQ,CAACC,CAAD,CAAR,CAAYC,cAAlC;UACA;;UACA,IAAI,KAAKpG,OAAT,EAAkB;YACd,KAAKA,OAAL,CAAaM,GAAb,CAAiB,MAAMgG,aAAa,CAACC,IAAd,CAAmBN,KAAnB,CAAvB;UACH,CAFD,MAGK;YACDK,aAAa,CAACC,IAAd,CAAmBN,KAAnB;UACH;;UACD;QACH;MACJ;IACJ,CArBD;EAsBH;EACD;;;EACAxH,GAAG,CAAC+B,UAAD,EAAa;IACZ,MAAM/B,GAAN,CAAU+B,UAAV,EADY,CAEZ;;IACA,IAAI,CAAC,KAAKgG,WAAV,EAAuB;MACnB;MACA,IAAI,KAAKxG,OAAT,EAAkB;QACd,KAAKA,OAAL,CAAayG,iBAAb,CAA+B,MAAM,KAAK1I,SAAL,CAAea,IAAf,CAAoB8H,gBAApB,CAAqC,SAArC,EAAgD,KAAKV,gBAArD,CAArC;MACH,CAFD,MAGK;QACD,KAAKjI,SAAL,CAAea,IAAf,CAAoB8H,gBAApB,CAAqC,SAArC,EAAgD,KAAKV,gBAArD;MACH;;MACD,KAAKQ,WAAL,GAAmB,IAAnB;IACH;EACJ;EACD;;;EACAjG,MAAM,GAAG;IACL,IAAI,KAAKiG,WAAT,EAAsB;MAClB,KAAKzI,SAAL,CAAea,IAAf,CAAoB+H,mBAApB,CAAwC,SAAxC,EAAmD,KAAKX,gBAAxD;;MACA,KAAKQ,WAAL,GAAmB,KAAnB;IACH;EACJ;;AAnDyD;;AAqD9DT,yBAAyB,CAAC3C,IAA1B;EAAA,iBAAsH2C,yBAAtH,EAxQwG1K,EAwQxG,UAAiKD,QAAjK,GAxQwGC,EAwQxG,UAAsLA,EAAE,CAACgI,MAAzL;AAAA;;AACA0C,yBAAyB,CAACzC,KAA1B,kBAzQwGjI,EAyQxG;EAAA,OAA0H0K,yBAA1H;EAAA,SAA0HA,yBAA1H;EAAA,YAAiK;AAAjK;;AACA;EAAA,mDA1QwG1K,EA0QxG,mBAA2F0K,yBAA3F,EAAkI,CAAC;IACvHxC,IAAI,EAAEjI,UADiH;IAEvHkI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAFiH,CAAD,CAAlI,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9DJ,IAAI,EAAEhI,MADwD;QAE9DiI,IAAI,EAAE,CAACpI,QAAD;MAFwD,CAAD;IAA/B,CAAD,EAG3B;MAAEmI,IAAI,EAAElI,EAAE,CAACgI,MAAX;MAAmBM,UAAU,EAAE,CAAC;QAClCJ,IAAI,EAAE/H;MAD4B,CAAD;IAA/B,CAH2B,CAAP;EAKlB,CARxB;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMoL,6BAAN,SAA4CrB,qBAA5C,CAAkE;EAC9D/H,WAAW,CAACE,QAAD,EAAWmJ,SAAX;EACX;EACA7G,OAFW,EAEF;IACL,MAAMtC,QAAN;IACA,KAAKmJ,SAAL,GAAiBA,SAAjB;IACA,KAAK7G,OAAL,GAAeA,OAAf;IACA,KAAK8G,iBAAL,GAAyB,KAAzB;IACA;;IACA,KAAKC,oBAAL,GAA6Bd,KAAD,IAAW;MACnC,KAAKe,uBAAL,GAA+BzK,eAAe,CAAC0J,KAAD,CAA9C;IACH,CAFD;IAGA;;;IACA,KAAKgB,cAAL,GAAuBhB,KAAD,IAAW;MAC7B,MAAMiB,MAAM,GAAG3K,eAAe,CAAC0J,KAAD,CAA9B,CAD6B,CAE7B;MACA;MACA;MACA;MACA;MACA;;;MACA,MAAM1B,MAAM,GAAG0B,KAAK,CAAC1C,IAAN,KAAe,OAAf,IAA0B,KAAKyD,uBAA/B,GACT,KAAKA,uBADI,GAETE,MAFN,CAR6B,CAW7B;MACA;;MACA,KAAKF,uBAAL,GAA+B,IAA/B,CAb6B,CAc7B;MACA;MACA;;MACA,MAAMd,QAAQ,GAAG,KAAKV,iBAAL,CAAuB2B,KAAvB,EAAjB,CAjB6B,CAkB7B;MACA;MACA;MACA;;;MACA,KAAK,IAAIhB,CAAC,GAAGD,QAAQ,CAACJ,MAAT,GAAkB,CAA/B,EAAkCK,CAAC,GAAG,CAAC,CAAvC,EAA0CA,CAAC,EAA3C,EAA+C;QAC3C,MAAM3F,UAAU,GAAG0F,QAAQ,CAACC,CAAD,CAA3B;;QACA,IAAI3F,UAAU,CAAC4G,qBAAX,CAAiCf,SAAjC,CAA2CP,MAA3C,GAAoD,CAApD,IAAyD,CAACtF,UAAU,CAACH,WAAX,EAA9D,EAAwF;UACpF;QACH,CAJ0C,CAK3C;QACA;QACA;;;QACA,IAAIG,UAAU,CAACmC,cAAX,CAA0BtD,QAA1B,CAAmC6H,MAAnC,KACA1G,UAAU,CAACmC,cAAX,CAA0BtD,QAA1B,CAAmCkF,MAAnC,CADJ,EACgD;UAC5C;QACH;;QACD,MAAM8C,oBAAoB,GAAG7G,UAAU,CAAC4G,qBAAxC;QACA;;QACA,IAAI,KAAKpH,OAAT,EAAkB;UACd,KAAKA,OAAL,CAAaM,GAAb,CAAiB,MAAM+G,oBAAoB,CAACd,IAArB,CAA0BN,KAA1B,CAAvB;QACH,CAFD,MAGK;UACDoB,oBAAoB,CAACd,IAArB,CAA0BN,KAA1B;QACH;MACJ;IACJ,CA3CD;EA4CH;EACD;;;EACAxH,GAAG,CAAC+B,UAAD,EAAa;IACZ,MAAM/B,GAAN,CAAU+B,UAAV,EADY,CAEZ;IACA;IACA;IACA;IACA;IACA;;IACA,IAAI,CAAC,KAAKgG,WAAV,EAAuB;MACnB,MAAM5H,IAAI,GAAG,KAAKb,SAAL,CAAea,IAA5B;MACA;;MACA,IAAI,KAAKoB,OAAT,EAAkB;QACd,KAAKA,OAAL,CAAayG,iBAAb,CAA+B,MAAM,KAAKa,kBAAL,CAAwB1I,IAAxB,CAArC;MACH,CAFD,MAGK;QACD,KAAK0I,kBAAL,CAAwB1I,IAAxB;MACH,CARkB,CASnB;MACA;;;MACA,IAAI,KAAKiI,SAAL,CAAeU,GAAf,IAAsB,CAAC,KAAKT,iBAAhC,EAAmD;QAC/C,KAAKU,oBAAL,GAA4B5I,IAAI,CAACL,KAAL,CAAWkJ,MAAvC;QACA7I,IAAI,CAACL,KAAL,CAAWkJ,MAAX,GAAoB,SAApB;QACA,KAAKX,iBAAL,GAAyB,IAAzB;MACH;;MACD,KAAKN,WAAL,GAAmB,IAAnB;IACH;EACJ;EACD;;;EACAjG,MAAM,GAAG;IACL,IAAI,KAAKiG,WAAT,EAAsB;MAClB,MAAM5H,IAAI,GAAG,KAAKb,SAAL,CAAea,IAA5B;MACAA,IAAI,CAAC+H,mBAAL,CAAyB,aAAzB,EAAwC,KAAKI,oBAA7C,EAAmE,IAAnE;MACAnI,IAAI,CAAC+H,mBAAL,CAAyB,OAAzB,EAAkC,KAAKM,cAAvC,EAAuD,IAAvD;MACArI,IAAI,CAAC+H,mBAAL,CAAyB,UAAzB,EAAqC,KAAKM,cAA1C,EAA0D,IAA1D;MACArI,IAAI,CAAC+H,mBAAL,CAAyB,aAAzB,EAAwC,KAAKM,cAA7C,EAA6D,IAA7D;;MACA,IAAI,KAAKJ,SAAL,CAAeU,GAAf,IAAsB,KAAKT,iBAA/B,EAAkD;QAC9ClI,IAAI,CAACL,KAAL,CAAWkJ,MAAX,GAAoB,KAAKD,oBAAzB;QACA,KAAKV,iBAAL,GAAyB,KAAzB;MACH;;MACD,KAAKN,WAAL,GAAmB,KAAnB;IACH;EACJ;;EACDc,kBAAkB,CAAC1I,IAAD,EAAO;IACrBA,IAAI,CAAC8H,gBAAL,CAAsB,aAAtB,EAAqC,KAAKK,oBAA1C,EAAgE,IAAhE;IACAnI,IAAI,CAAC8H,gBAAL,CAAsB,OAAtB,EAA+B,KAAKO,cAApC,EAAoD,IAApD;IACArI,IAAI,CAAC8H,gBAAL,CAAsB,UAAtB,EAAkC,KAAKO,cAAvC,EAAuD,IAAvD;IACArI,IAAI,CAAC8H,gBAAL,CAAsB,aAAtB,EAAqC,KAAKO,cAA1C,EAA0D,IAA1D;EACH;;AA1G6D;;AA4GlEL,6BAA6B,CAACxD,IAA9B;EAAA,iBAA0HwD,6BAA1H,EA5YwGvL,EA4YxG,UAAyKD,QAAzK,GA5YwGC,EA4YxG,UAA8LgB,IAAI,CAACqL,QAAnM,GA5YwGrM,EA4YxG,UAAwNA,EAAE,CAACgI,MAA3N;AAAA;;AACAuD,6BAA6B,CAACtD,KAA9B,kBA7YwGjI,EA6YxG;EAAA,OAA8HuL,6BAA9H;EAAA,SAA8HA,6BAA9H;EAAA,YAAyK;AAAzK;;AACA;EAAA,mDA9YwGvL,EA8YxG,mBAA2FuL,6BAA3F,EAAsI,CAAC;IAC3HrD,IAAI,EAAEjI,UADqH;IAE3HkI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAFqH,CAAD,CAAtI,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9DJ,IAAI,EAAEhI,MADwD;QAE9DiI,IAAI,EAAE,CAACpI,QAAD;MAFwD,CAAD;IAA/B,CAAD,EAG3B;MAAEmI,IAAI,EAAElH,IAAI,CAACqL;IAAb,CAH2B,EAGF;MAAEnE,IAAI,EAAElI,EAAE,CAACgI,MAAX;MAAmBM,UAAU,EAAE,CAAC;QAC3DJ,IAAI,EAAE/H;MADqD,CAAD;IAA/B,CAHE,CAAP;EAKlB,CARxB;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMmM,gBAAN,CAAuB;EACnBnK,WAAW,CAACE,QAAD,EAAWmJ,SAAX,EAAsB;IAC7B,KAAKA,SAAL,GAAiBA,SAAjB;IACA,KAAK9I,SAAL,GAAiBL,QAAjB;EACH;;EACD+H,WAAW,GAAG;IACV,KAAKmC,iBAAL,EAAwB1I,MAAxB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACI2I,mBAAmB,GAAG;IAClB,IAAI,CAAC,KAAKD,iBAAV,EAA6B;MACzB,KAAKE,gBAAL;IACH;;IACD,OAAO,KAAKF,iBAAZ;EACH;EACD;AACJ;AACA;AACA;;;EACIE,gBAAgB,GAAG;IACf,MAAMC,cAAc,GAAG,uBAAvB,CADe,CAEf;IACA;IACA;;IACA,IAAI,KAAKlB,SAAL,CAAemB,SAAf,IAA4BxL,kBAAkB,EAAlD,EAAsD;MAClD,MAAMyL,0BAA0B,GAAG,KAAKlK,SAAL,CAAemK,gBAAf,CAAiC,IAAGH,cAAe,uBAAnB,GAA6C,IAAGA,cAAe,mBAA/F,CAAnC,CADkD,CAElD;MACA;;;MACA,KAAK,IAAI5B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8B,0BAA0B,CAACnC,MAA/C,EAAuDK,CAAC,EAAxD,EAA4D;QACxD8B,0BAA0B,CAAC9B,CAAD,CAA1B,CAA8BjH,MAA9B;MACH;IACJ;;IACD,MAAMiJ,SAAS,GAAG,KAAKpK,SAAL,CAAeqK,aAAf,CAA6B,KAA7B,CAAlB;;IACAD,SAAS,CAAC3J,SAAV,CAAoBC,GAApB,CAAwBsJ,cAAxB,EAde,CAef;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,IAAIvL,kBAAkB,EAAtB,EAA0B;MACtB2L,SAAS,CAACE,YAAV,CAAuB,UAAvB,EAAmC,MAAnC;IACH,CAFD,MAGK,IAAI,CAAC,KAAKxB,SAAL,CAAemB,SAApB,EAA+B;MAChCG,SAAS,CAACE,YAAV,CAAuB,UAAvB,EAAmC,QAAnC;IACH;;IACD,KAAKtK,SAAL,CAAea,IAAf,CAAoB0J,WAApB,CAAgCH,SAAhC;;IACA,KAAKP,iBAAL,GAAyBO,SAAzB;EACH;;AAxDkB;;AA0DvBR,gBAAgB,CAACvE,IAAjB;EAAA,iBAA6GuE,gBAA7G,EA1dwGtM,EA0dxG,UAA+ID,QAA/I,GA1dwGC,EA0dxG,UAAoKgB,IAAI,CAACqL,QAAzK;AAAA;;AACAC,gBAAgB,CAACrE,KAAjB,kBA3dwGjI,EA2dxG;EAAA,OAAiHsM,gBAAjH;EAAA,SAAiHA,gBAAjH;EAAA,YAA+I;AAA/I;;AACA;EAAA,mDA5dwGtM,EA4dxG,mBAA2FsM,gBAA3F,EAAyH,CAAC;IAC9GpE,IAAI,EAAEjI,UADwG;IAE9GkI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAFwG,CAAD,CAAzH,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9DJ,IAAI,EAAEhI,MADwD;QAE9DiI,IAAI,EAAE,CAACpI,QAAD;MAFwD,CAAD;IAA/B,CAAD,EAG3B;MAAEmI,IAAI,EAAElH,IAAI,CAACqL;IAAb,CAH2B,CAAP;EAGO,CANjD;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMa,UAAN,CAAiB;EACb/K,WAAW,CAACgL,aAAD,EAAgBC,KAAhB,EAAuBC,KAAvB,EAA8BzI,OAA9B,EAAuCD,OAAvC,EAAgD2I,mBAAhD,EAAqE5K,SAArE,EAAgF6K,SAAhF,EAA2FC,uBAA3F,EAAoHC,mBAAmB,GAAG,KAA1I,EAAiJ;IACxJ,KAAKN,aAAL,GAAqBA,aAArB;IACA,KAAKC,KAAL,GAAaA,KAAb;IACA,KAAKC,KAAL,GAAaA,KAAb;IACA,KAAKzI,OAAL,GAAeA,OAAf;IACA,KAAKD,OAAL,GAAeA,OAAf;IACA,KAAK2I,mBAAL,GAA2BA,mBAA3B;IACA,KAAK5K,SAAL,GAAiBA,SAAjB;IACA,KAAK6K,SAAL,GAAiBA,SAAjB;IACA,KAAKC,uBAAL,GAA+BA,uBAA/B;IACA,KAAKC,mBAAL,GAA2BA,mBAA3B;IACA,KAAKC,gBAAL,GAAwB,IAAxB;IACA,KAAKC,cAAL,GAAsB,IAAIlM,OAAJ,EAAtB;IACA,KAAKmM,YAAL,GAAoB,IAAInM,OAAJ,EAApB;IACA,KAAKoM,YAAL,GAAoB,IAAIpM,OAAJ,EAApB;IACA,KAAKqM,gBAAL,GAAwBpM,YAAY,CAACqM,KAArC;;IACA,KAAKC,qBAAL,GAA8BpD,KAAD,IAAW,KAAK+C,cAAL,CAAoBzC,IAApB,CAAyBN,KAAzB,CAAxC;;IACA,KAAKqD,6BAAL,GAAsCrD,KAAD,IAAW;MAC5C,KAAKsD,gBAAL,CAAsBtD,KAAK,CAACiB,MAA5B;IACH,CAFD;IAGA;;;IACA,KAAKd,cAAL,GAAsB,IAAItJ,OAAJ,EAAtB;IACA;;IACA,KAAKsK,qBAAL,GAA6B,IAAItK,OAAJ,EAA7B;;IACA,IAAImD,OAAO,CAAC4D,cAAZ,EAA4B;MACxB,KAAK2F,eAAL,GAAuBvJ,OAAO,CAAC4D,cAA/B;;MACA,KAAK2F,eAAL,CAAqBxL,MAArB,CAA4B,IAA5B;IACH;;IACD,KAAKyL,iBAAL,GAAyBxJ,OAAO,CAACyJ,gBAAjC;EACH;EACD;;;EACkB,IAAd/G,cAAc,GAAG;IACjB,OAAO,KAAK+F,KAAZ;EACH;EACD;;;EACmB,IAAfiB,eAAe,GAAG;IAClB,OAAO,KAAKZ,gBAAZ;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACmB,IAAXa,WAAW,GAAG;IACd,OAAO,KAAKnB,KAAZ;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACIzK,MAAM,CAAC6L,MAAD,EAAS;IACX;IACA;IACA,IAAI,CAAC,KAAKpB,KAAL,CAAWqB,aAAZ,IAA6B,KAAKC,mBAAtC,EAA2D;MACvD,KAAKA,mBAAL,CAAyBzB,WAAzB,CAAqC,KAAKG,KAA1C;IACH;;IACD,MAAMuB,YAAY,GAAG,KAAKxB,aAAL,CAAmBxK,MAAnB,CAA0B6L,MAA1B,CAArB;;IACA,IAAI,KAAKJ,iBAAT,EAA4B;MACxB,KAAKA,iBAAL,CAAuBzL,MAAvB,CAA8B,IAA9B;IACH;;IACD,KAAKiM,oBAAL;;IACA,KAAKC,kBAAL;;IACA,KAAKC,uBAAL;;IACA,IAAI,KAAKX,eAAT,EAA0B;MACtB,KAAKA,eAAL,CAAqBvL,MAArB;IACH,CAfU,CAgBX;IACA;IACA;;;IACA,KAAK+B,OAAL,CAAaoK,QAAb,CAAsBC,IAAtB,CAA2BpN,IAAI,CAAC,CAAD,CAA/B,EAAoC6D,SAApC,CAA8C,MAAM;MAChD;MACA,IAAI,KAAKT,WAAL,EAAJ,EAAwB;QACpB,KAAKa,cAAL;MACH;IACJ,CALD,EAnBW,CAyBX;;;IACA,KAAKoJ,oBAAL,CAA0B,IAA1B;;IACA,IAAI,KAAKrK,OAAL,CAAa8D,WAAjB,EAA8B;MAC1B,KAAKwG,eAAL;IACH;;IACD,IAAI,KAAKtK,OAAL,CAAa6D,UAAjB,EAA6B;MACzB,KAAK0G,cAAL,CAAoB,KAAK9B,KAAzB,EAAgC,KAAKzI,OAAL,CAAa6D,UAA7C,EAAyD,IAAzD;IACH,CAhCU,CAiCX;;;IACA,KAAKmF,YAAL,CAAkB1C,IAAlB,GAlCW,CAmCX;;;IACA,KAAKoC,mBAAL,CAAyBlK,GAAzB,CAA6B,IAA7B;;IACA,IAAI,KAAKwB,OAAL,CAAagE,mBAAjB,EAAsC;MAClC,KAAKkF,gBAAL,GAAwB,KAAKP,SAAL,CAAe9H,SAAf,CAAyB,MAAM,KAAK2J,OAAL,EAA/B,CAAxB;IACH;;IACD,KAAK5B,uBAAL,CAA6BpK,GAA7B,CAAiC,IAAjC,EAxCW,CAyCX;IACA;IACA;;;IACA,IAAI,OAAOuL,YAAY,EAAEU,SAArB,KAAmC,UAAvC,EAAmD;MAC/C;MACA;MACA;MACA;MACA;MACAV,YAAY,CAACU,SAAb,CAAuB,MAAM;QACzB,IAAI,KAAKrK,WAAL,EAAJ,EAAwB;UACpB;UACA;UACA;UACA,KAAKL,OAAL,CAAayG,iBAAb,CAA+B,MAAMkE,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM,KAAKtK,MAAL,EAA7B,CAArC;QACH;MACJ,CAPD;IAQH;;IACD,OAAOyJ,YAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIzJ,MAAM,GAAG;IACL,IAAI,CAAC,KAAKF,WAAL,EAAL,EAAyB;MACrB;IACH;;IACD,KAAKyK,cAAL,GAJK,CAKL;IACA;IACA;;IACA,KAAKR,oBAAL,CAA0B,KAA1B;;IACA,IAAI,KAAKb,iBAAL,IAA0B,KAAKA,iBAAL,CAAuBlJ,MAArD,EAA6D;MACzD,KAAKkJ,iBAAL,CAAuBlJ,MAAvB;IACH;;IACD,IAAI,KAAKiJ,eAAT,EAA0B;MACtB,KAAKA,eAAL,CAAqB9K,OAArB;IACH;;IACD,MAAMqM,gBAAgB,GAAG,KAAKvC,aAAL,CAAmBjI,MAAnB,EAAzB,CAfK,CAgBL;;;IACA,KAAK2I,YAAL,CAAkB3C,IAAlB,GAjBK,CAkBL;;;IACA,KAAKoC,mBAAL,CAAyBzJ,MAAzB,CAAgC,IAAhC,EAnBK,CAoBL;IACA;;;IACA,KAAK8L,wBAAL;;IACA,KAAK7B,gBAAL,CAAsBhI,WAAtB;;IACA,KAAK0H,uBAAL,CAA6B3J,MAA7B,CAAoC,IAApC;;IACA,OAAO6L,gBAAP;EACH;EACD;;;EACAN,OAAO,GAAG;IACN,MAAMQ,UAAU,GAAG,KAAK5K,WAAL,EAAnB;;IACA,IAAI,KAAKoJ,iBAAT,EAA4B;MACxB,KAAKA,iBAAL,CAAuBgB,OAAvB;IACH;;IACD,KAAKS,sBAAL;;IACA,KAAK3B,gBAAL,CAAsB,KAAKR,gBAA3B;;IACA,KAAKI,gBAAL,CAAsBhI,WAAtB;;IACA,KAAKwH,mBAAL,CAAyBzJ,MAAzB,CAAgC,IAAhC;;IACA,KAAKsJ,aAAL,CAAmBiC,OAAnB;;IACA,KAAKxB,YAAL,CAAkBkC,QAAlB;;IACA,KAAKnC,cAAL,CAAoBmC,QAApB;;IACA,KAAK/E,cAAL,CAAoB+E,QAApB;;IACA,KAAK/D,qBAAL,CAA2B+D,QAA3B;;IACA,KAAKtC,uBAAL,CAA6B3J,MAA7B,CAAoC,IAApC;;IACA,KAAKuJ,KAAL,EAAYvJ,MAAZ;IACA,KAAK6K,mBAAL,GAA2B,KAAKrB,KAAL,GAAa,KAAKD,KAAL,GAAa,IAArD;;IACA,IAAIwC,UAAJ,EAAgB;MACZ,KAAK/B,YAAL,CAAkB3C,IAAlB;IACH;;IACD,KAAK2C,YAAL,CAAkBiC,QAAlB;EACH;EACD;;;EACA9K,WAAW,GAAG;IACV,OAAO,KAAKmI,aAAL,CAAmBnI,WAAnB,EAAP;EACH;EACD;;;EACA+K,aAAa,GAAG;IACZ,OAAO,KAAKpC,cAAZ;EACH;EACD;;;EACAqC,WAAW,GAAG;IACV,OAAO,KAAKpC,YAAZ;EACH;EACD;;;EACAqC,WAAW,GAAG;IACV,OAAO,KAAKpC,YAAZ;EACH;EACD;;;EACA5C,aAAa,GAAG;IACZ,OAAO,KAAKF,cAAZ;EACH;EACD;;;EACAiB,oBAAoB,GAAG;IACnB,OAAO,KAAKD,qBAAZ;EACH;EACD;;;EACAmE,SAAS,GAAG;IACR,OAAO,KAAKtL,OAAZ;EACH;EACD;;;EACAiB,cAAc,GAAG;IACb,IAAI,KAAKuI,iBAAT,EAA4B;MACxB,KAAKA,iBAAL,CAAuB+B,KAAvB;IACH;EACJ;EACD;;;EACAC,sBAAsB,CAACC,QAAD,EAAW;IAC7B,IAAIA,QAAQ,KAAK,KAAKjC,iBAAtB,EAAyC;MACrC;IACH;;IACD,IAAI,KAAKA,iBAAT,EAA4B;MACxB,KAAKA,iBAAL,CAAuBgB,OAAvB;IACH;;IACD,KAAKhB,iBAAL,GAAyBiC,QAAzB;;IACA,IAAI,KAAKrL,WAAL,EAAJ,EAAwB;MACpBqL,QAAQ,CAAC1N,MAAT,CAAgB,IAAhB;MACA,KAAKkD,cAAL;IACH;EACJ;EACD;;;EACAyK,UAAU,CAACC,UAAD,EAAa;IACnB,KAAK3L,OAAL,GAAe,EAAE,GAAG,KAAKA,OAAV;MAAmB,GAAG2L;IAAtB,CAAf;;IACA,KAAK1B,kBAAL;EACH;EACD;;;EACA2B,YAAY,CAACC,GAAD,EAAM;IACd,KAAK7L,OAAL,GAAe,EAAE,GAAG,KAAKA,OAAV;MAAmB8L,SAAS,EAAED;IAA9B,CAAf;;IACA,KAAK3B,uBAAL;EACH;EACD;;;EACA6B,aAAa,CAACC,OAAD,EAAU;IACnB,IAAI,KAAKvD,KAAT,EAAgB;MACZ,KAAK8B,cAAL,CAAoB,KAAK9B,KAAzB,EAAgCuD,OAAhC,EAAyC,IAAzC;IACH;EACJ;EACD;;;EACAC,gBAAgB,CAACD,OAAD,EAAU;IACtB,IAAI,KAAKvD,KAAT,EAAgB;MACZ,KAAK8B,cAAL,CAAoB,KAAK9B,KAAzB,EAAgCuD,OAAhC,EAAyC,KAAzC;IACH;EACJ;EACD;AACJ;AACA;;;EACIE,YAAY,GAAG;IACX,MAAMJ,SAAS,GAAG,KAAK9L,OAAL,CAAa8L,SAA/B;;IACA,IAAI,CAACA,SAAL,EAAgB;MACZ,OAAO,KAAP;IACH;;IACD,OAAO,OAAOA,SAAP,KAAqB,QAArB,GAAgCA,SAAhC,GAA4CA,SAAS,CAAC1G,KAA7D;EACH;EACD;;;EACA+G,oBAAoB,CAACV,QAAD,EAAW;IAC3B,IAAIA,QAAQ,KAAK,KAAKlC,eAAtB,EAAuC;MACnC;IACH;;IACD,KAAK0B,sBAAL;;IACA,KAAK1B,eAAL,GAAuBkC,QAAvB;;IACA,IAAI,KAAKrL,WAAL,EAAJ,EAAwB;MACpBqL,QAAQ,CAAC1N,MAAT,CAAgB,IAAhB;MACA0N,QAAQ,CAACzN,MAAT;IACH;EACJ;EACD;;;EACAkM,uBAAuB,GAAG;IACtB,KAAK1B,KAAL,CAAWJ,YAAX,CAAwB,KAAxB,EAA+B,KAAK8D,YAAL,EAA/B;EACH;EACD;;;EACAjC,kBAAkB,GAAG;IACjB,IAAI,CAAC,KAAKxB,KAAV,EAAiB;MACb;IACH;;IACD,MAAMnK,KAAK,GAAG,KAAKmK,KAAL,CAAWnK,KAAzB;IACAA,KAAK,CAACoB,KAAN,GAAczD,mBAAmB,CAAC,KAAK+D,OAAL,CAAaN,KAAd,CAAjC;IACApB,KAAK,CAACkB,MAAN,GAAevD,mBAAmB,CAAC,KAAK+D,OAAL,CAAaR,MAAd,CAAlC;IACAlB,KAAK,CAAC8N,QAAN,GAAiBnQ,mBAAmB,CAAC,KAAK+D,OAAL,CAAaoM,QAAd,CAApC;IACA9N,KAAK,CAAC+N,SAAN,GAAkBpQ,mBAAmB,CAAC,KAAK+D,OAAL,CAAaqM,SAAd,CAArC;IACA/N,KAAK,CAACgO,QAAN,GAAiBrQ,mBAAmB,CAAC,KAAK+D,OAAL,CAAasM,QAAd,CAApC;IACAhO,KAAK,CAACiO,SAAN,GAAkBtQ,mBAAmB,CAAC,KAAK+D,OAAL,CAAauM,SAAd,CAArC;EACH;EACD;;;EACAlC,oBAAoB,CAACmC,aAAD,EAAgB;IAChC,KAAK/D,KAAL,CAAWnK,KAAX,CAAiBmO,aAAjB,GAAiCD,aAAa,GAAG,EAAH,GAAQ,MAAtD;EACH;EACD;;;EACAlC,eAAe,GAAG;IACd,MAAMoC,YAAY,GAAG,8BAArB;IACA,KAAK5D,gBAAL,GAAwB,KAAKhL,SAAL,CAAeqK,aAAf,CAA6B,KAA7B,CAAxB;;IACA,KAAKW,gBAAL,CAAsBvK,SAAtB,CAAgCC,GAAhC,CAAoC,sBAApC;;IACA,IAAI,KAAKqK,mBAAT,EAA8B;MAC1B,KAAKC,gBAAL,CAAsBvK,SAAtB,CAAgCC,GAAhC,CAAoC,qCAApC;IACH;;IACD,IAAI,KAAKwB,OAAL,CAAa+D,aAAjB,EAAgC;MAC5B,KAAKwG,cAAL,CAAoB,KAAKzB,gBAAzB,EAA2C,KAAK9I,OAAL,CAAa+D,aAAxD,EAAuE,IAAvE;IACH,CATa,CAUd;IACA;;;IACA,KAAKyE,KAAL,CAAWqB,aAAX,CAAyB8C,YAAzB,CAAsC,KAAK7D,gBAA3C,EAA6D,KAAKN,KAAlE,EAZc,CAad;IACA;;;IACA,KAAKM,gBAAL,CAAsBrC,gBAAtB,CAAuC,OAAvC,EAAgD,KAAK2C,qBAArD,EAfc,CAgBd;;;IACA,IAAI,CAAC,KAAKP,mBAAN,IAA6B,OAAO+D,qBAAP,KAAiC,WAAlE,EAA+E;MAC3E,KAAK7M,OAAL,CAAayG,iBAAb,CAA+B,MAAM;QACjCoG,qBAAqB,CAAC,MAAM;UACxB,IAAI,KAAK9D,gBAAT,EAA2B;YACvB,KAAKA,gBAAL,CAAsBvK,SAAtB,CAAgCC,GAAhC,CAAoCkO,YAApC;UACH;QACJ,CAJoB,CAArB;MAKH,CAND;IAOH,CARD,MASK;MACD,KAAK5D,gBAAL,CAAsBvK,SAAtB,CAAgCC,GAAhC,CAAoCkO,YAApC;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACI1C,oBAAoB,GAAG;IACnB,IAAI,KAAKxB,KAAL,CAAWqE,WAAf,EAA4B;MACxB,KAAKrE,KAAL,CAAWsE,UAAX,CAAsBzE,WAAtB,CAAkC,KAAKG,KAAvC;IACH;EACJ;EACD;;;EACAqC,cAAc,GAAG;IACb,MAAMkC,gBAAgB,GAAG,KAAKjE,gBAA9B;;IACA,IAAI,CAACiE,gBAAL,EAAuB;MACnB;IACH;;IACD,IAAI,KAAKlE,mBAAT,EAA8B;MAC1B,KAAKS,gBAAL,CAAsByD,gBAAtB;;MACA;IACH;;IACDA,gBAAgB,CAACxO,SAAjB,CAA2BU,MAA3B,CAAkC,8BAAlC;;IACA,KAAKc,OAAL,CAAayG,iBAAb,CAA+B,MAAM;MACjCuG,gBAAgB,CAACtG,gBAAjB,CAAkC,eAAlC,EAAmD,KAAK4C,6BAAxD;IACH,CAFD,EAVa,CAab;IACA;;;IACA0D,gBAAgB,CAACzO,KAAjB,CAAuBmO,aAAvB,GAAuC,MAAvC,CAfa,CAgBb;IACA;IACA;;IACA,KAAKO,gBAAL,GAAwB,KAAKjN,OAAL,CAAayG,iBAAb,CAA+B,MAAMyG,UAAU,CAAC,MAAM;MAC1E,KAAK3D,gBAAL,CAAsByD,gBAAtB;IACH,CAFsE,EAEpE,GAFoE,CAA/C,CAAxB;EAGH;EACD;;;EACAxC,cAAc,CAAClJ,OAAD,EAAU6L,UAAV,EAAsBC,KAAtB,EAA6B;IACvC,MAAMnB,OAAO,GAAG9P,WAAW,CAACgR,UAAU,IAAI,EAAf,CAAX,CAA8BE,MAA9B,CAAqCC,CAAC,IAAI,CAAC,CAACA,CAA5C,CAAhB;;IACA,IAAIrB,OAAO,CAACnG,MAAZ,EAAoB;MAChBsH,KAAK,GAAG9L,OAAO,CAAC9C,SAAR,CAAkBC,GAAlB,CAAsB,GAAGwN,OAAzB,CAAH,GAAuC3K,OAAO,CAAC9C,SAAR,CAAkBU,MAAlB,CAAyB,GAAG+M,OAA5B,CAA5C;IACH;EACJ;EACD;;;EACAjB,wBAAwB,GAAG;IACvB;IACA;IACA;IACA,KAAKhL,OAAL,CAAayG,iBAAb,CAA+B,MAAM;MACjC;MACA;MACA;MACA,MAAM8G,YAAY,GAAG,KAAKvN,OAAL,CAAaoK,QAAb,CAChBC,IADgB,CACXnN,SAAS,CAACF,KAAK,CAAC,KAAKiM,YAAN,EAAoB,KAAKC,YAAzB,CAAN,CADE,EAEhBpI,SAFgB,CAEN,MAAM;QACjB;QACA;QACA,IAAI,CAAC,KAAK4H,KAAN,IAAe,CAAC,KAAKD,KAArB,IAA8B,KAAKC,KAAL,CAAW8E,QAAX,CAAoB1H,MAApB,KAA+B,CAAjE,EAAoE;UAChE,IAAI,KAAK4C,KAAL,IAAc,KAAKzI,OAAL,CAAa6D,UAA/B,EAA2C;YACvC,KAAK0G,cAAL,CAAoB,KAAK9B,KAAzB,EAAgC,KAAKzI,OAAL,CAAa6D,UAA7C,EAAyD,KAAzD;UACH;;UACD,IAAI,KAAK2E,KAAL,IAAc,KAAKA,KAAL,CAAWqB,aAA7B,EAA4C;YACxC,KAAKC,mBAAL,GAA2B,KAAKtB,KAAL,CAAWqB,aAAtC;;YACA,KAAKrB,KAAL,CAAWvJ,MAAX;UACH;;UACDqO,YAAY,CAACpM,WAAb;QACH;MACJ,CAfoB,CAArB;IAgBH,CApBD;EAqBH;EACD;;;EACA+J,sBAAsB,GAAG;IACrB,MAAMrH,cAAc,GAAG,KAAK2F,eAA5B;;IACA,IAAI3F,cAAJ,EAAoB;MAChBA,cAAc,CAACnF,OAAf;;MACA,IAAImF,cAAc,CAACtD,MAAnB,EAA2B;QACvBsD,cAAc,CAACtD,MAAf;MACH;IACJ;EACJ;EACD;;;EACAgJ,gBAAgB,CAACkE,QAAD,EAAW;IACvB,IAAIA,QAAJ,EAAc;MACVA,QAAQ,CAAC9G,mBAAT,CAA6B,OAA7B,EAAsC,KAAK0C,qBAA3C;MACAoE,QAAQ,CAAC9G,mBAAT,CAA6B,eAA7B,EAA8C,KAAK2C,6BAAnD;MACAmE,QAAQ,CAACvO,MAAT,GAHU,CAIV;MACA;MACA;;MACA,IAAI,KAAK6J,gBAAL,KAA0B0E,QAA9B,EAAwC;QACpC,KAAK1E,gBAAL,GAAwB,IAAxB;MACH;IACJ;;IACD,IAAI,KAAKkE,gBAAT,EAA2B;MACvBS,YAAY,CAAC,KAAKT,gBAAN,CAAZ;MACA,KAAKA,gBAAL,GAAwBvJ,SAAxB;IACH;EACJ;;AA1ZY;AA6ZjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMiK,gBAAgB,GAAG,6CAAzB;AACA;;AACA,MAAMC,cAAc,GAAG,eAAvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,iCAAN,CAAwC;EACpCrQ,WAAW,CAACsQ,WAAD,EAAcrQ,cAAd,EAA8BM,SAA9B,EAAyC8I,SAAzC,EAAoDkH,iBAApD,EAAuE;IAC9E,KAAKtQ,cAAL,GAAsBA,cAAtB;IACA,KAAKM,SAAL,GAAiBA,SAAjB;IACA,KAAK8I,SAAL,GAAiBA,SAAjB;IACA,KAAKkH,iBAAL,GAAyBA,iBAAzB;IACA;;IACA,KAAKC,oBAAL,GAA4B;MAAErO,KAAK,EAAE,CAAT;MAAYF,MAAM,EAAE;IAApB,CAA5B;IACA;;IACA,KAAKwO,SAAL,GAAiB,KAAjB;IACA;;IACA,KAAKC,QAAL,GAAgB,IAAhB;IACA;;IACA,KAAKC,cAAL,GAAsB,KAAtB;IACA;;IACA,KAAKC,sBAAL,GAA8B,IAA9B;IACA;;IACA,KAAKC,eAAL,GAAuB,KAAvB;IACA;;IACA,KAAKC,eAAL,GAAuB,CAAvB;IACA;;IACA,KAAKC,YAAL,GAAoB,EAApB;IACA;;IACA,KAAKC,mBAAL,GAA2B,EAA3B;IACA;;IACA,KAAKC,gBAAL,GAAwB,IAAI3R,OAAJ,EAAxB;IACA;;IACA,KAAK4R,mBAAL,GAA2B3R,YAAY,CAACqM,KAAxC;IACA;;IACA,KAAKuF,QAAL,GAAgB,CAAhB;IACA;;IACA,KAAKC,QAAL,GAAgB,CAAhB;IACA;;IACA,KAAKC,oBAAL,GAA4B,EAA5B;IACA;;IACA,KAAKC,eAAL,GAAuB,KAAKL,gBAA5B;IACA,KAAKM,SAAL,CAAejB,WAAf;EACH;EACD;;;EACa,IAATkB,SAAS,GAAG;IACZ,OAAO,KAAKR,mBAAZ;EACH;EACD;;;EACAxQ,MAAM,CAACwC,UAAD,EAAa;IACf,IAAI,KAAKJ,WAAL,IACAI,UAAU,KAAK,KAAKJ,WADpB,KAEC,OAAOK,SAAP,KAAqB,WAArB,IAAoCA,SAFrC,CAAJ,EAEqD;MACjD,MAAMZ,KAAK,CAAC,0DAAD,CAAX;IACH;;IACD,KAAKoP,kBAAL;;IACAzO,UAAU,CAACoJ,WAAX,CAAuBpL,SAAvB,CAAiCC,GAAjC,CAAqCkP,gBAArC;IACA,KAAKvN,WAAL,GAAmBI,UAAnB;IACA,KAAK0O,YAAL,GAAoB1O,UAAU,CAACoJ,WAA/B;IACA,KAAKlB,KAAL,GAAalI,UAAU,CAACmC,cAAxB;IACA,KAAKwM,WAAL,GAAmB,KAAnB;IACA,KAAKC,gBAAL,GAAwB,IAAxB;IACA,KAAKC,aAAL,GAAqB,IAArB;;IACA,KAAKX,mBAAL,CAAyBvN,WAAzB;;IACA,KAAKuN,mBAAL,GAA2B,KAAKjR,cAAL,CAAoB6R,MAApB,GAA6BxO,SAA7B,CAAuC,MAAM;MACpE;MACA;MACA;MACA,KAAKsO,gBAAL,GAAwB,IAAxB;MACA,KAAK5D,KAAL;IACH,CAN0B,CAA3B;EAOH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIA,KAAK,GAAG;IACJ;IACA,IAAI,KAAK2D,WAAL,IAAoB,CAAC,KAAKtI,SAAL,CAAemB,SAAxC,EAAmD;MAC/C;IACH,CAJG,CAKJ;IACA;IACA;;;IACA,IAAI,CAAC,KAAKoH,gBAAN,IAA0B,KAAKf,eAA/B,IAAkD,KAAKgB,aAA3D,EAA0E;MACtE,KAAKE,mBAAL;MACA;IACH;;IACD,KAAKC,kBAAL;;IACA,KAAKC,0BAAL;;IACA,KAAKC,uBAAL,GAdI,CAeJ;IACA;IACA;;;IACA,KAAKC,aAAL,GAAqB,KAAKC,wBAAL,EAArB;IACA,KAAKC,WAAL,GAAmB,KAAKC,cAAL,EAAnB;IACA,KAAKC,YAAL,GAAoB,KAAKrH,KAAL,CAAW9F,qBAAX,EAApB;IACA,KAAKoN,cAAL,GAAsB,KAAKjC,iBAAL,CAAuBlG,mBAAvB,GAA6CjF,qBAA7C,EAAtB;IACA,MAAMqN,UAAU,GAAG,KAAKJ,WAAxB;IACA,MAAMnN,WAAW,GAAG,KAAKqN,YAAzB;IACA,MAAMG,YAAY,GAAG,KAAKP,aAA1B;IACA,MAAMQ,aAAa,GAAG,KAAKH,cAA3B,CAzBI,CA0BJ;;IACA,MAAMI,YAAY,GAAG,EAArB,CA3BI,CA4BJ;;IACA,IAAIC,QAAJ,CA7BI,CA8BJ;IACA;;IACA,KAAK,IAAIC,GAAT,IAAgB,KAAK9B,mBAArB,EAA0C;MACtC;MACA,IAAI+B,WAAW,GAAG,KAAKC,eAAL,CAAqBP,UAArB,EAAiCE,aAAjC,EAAgDG,GAAhD,CAAlB,CAFsC,CAGtC;MACA;MACA;;;MACA,IAAIG,YAAY,GAAG,KAAKC,gBAAL,CAAsBH,WAAtB,EAAmC7N,WAAnC,EAAgD4N,GAAhD,CAAnB,CANsC,CAOtC;;;MACA,IAAIK,UAAU,GAAG,KAAKC,cAAL,CAAoBH,YAApB,EAAkC/N,WAAlC,EAA+CwN,YAA/C,EAA6DI,GAA7D,CAAjB,CARsC,CAStC;;;MACA,IAAIK,UAAU,CAACE,0BAAf,EAA2C;QACvC,KAAK5C,SAAL,GAAiB,KAAjB;;QACA,KAAK6C,cAAL,CAAoBR,GAApB,EAAyBC,WAAzB;;QACA;MACH,CAdqC,CAetC;MACA;;;MACA,IAAI,KAAKQ,6BAAL,CAAmCJ,UAAnC,EAA+CF,YAA/C,EAA6DP,YAA7D,CAAJ,EAAgF;QAC5E;QACA;QACAE,YAAY,CAAC1K,IAAb,CAAkB;UACdsL,QAAQ,EAAEV,GADI;UAEd/L,MAAM,EAAEgM,WAFM;UAGd7N,WAHc;UAIduO,eAAe,EAAE,KAAKC,yBAAL,CAA+BX,WAA/B,EAA4CD,GAA5C;QAJH,CAAlB;QAMA;MACH,CA3BqC,CA4BtC;MACA;MACA;;;MACA,IAAI,CAACD,QAAD,IAAaA,QAAQ,CAACM,UAAT,CAAoBQ,WAApB,GAAkCR,UAAU,CAACQ,WAA9D,EAA2E;QACvEd,QAAQ,GAAG;UAAEM,UAAF;UAAcF,YAAd;UAA4BF,WAA5B;UAAyCS,QAAQ,EAAEV,GAAnD;UAAwD5N;QAAxD,CAAX;MACH;IACJ,CAlEG,CAmEJ;IACA;;;IACA,IAAI0N,YAAY,CAACtK,MAAjB,EAAyB;MACrB,IAAIsL,OAAO,GAAG,IAAd;MACA,IAAIC,SAAS,GAAG,CAAC,CAAjB;;MACA,KAAK,MAAMC,GAAX,IAAkBlB,YAAlB,EAAgC;QAC5B,MAAMmB,KAAK,GAAGD,GAAG,CAACL,eAAJ,CAAoBtR,KAApB,GAA4B2R,GAAG,CAACL,eAAJ,CAAoBxR,MAAhD,IAA0D6R,GAAG,CAACN,QAAJ,CAAaQ,MAAb,IAAuB,CAAjF,CAAd;;QACA,IAAID,KAAK,GAAGF,SAAZ,EAAuB;UACnBA,SAAS,GAAGE,KAAZ;UACAH,OAAO,GAAGE,GAAV;QACH;MACJ;;MACD,KAAKrD,SAAL,GAAiB,KAAjB;;MACA,KAAK6C,cAAL,CAAoBM,OAAO,CAACJ,QAA5B,EAAsCI,OAAO,CAAC7M,MAA9C;;MACA;IACH,CAlFG,CAmFJ;IACA;;;IACA,IAAI,KAAK2J,QAAT,EAAmB;MACf;MACA,KAAKD,SAAL,GAAiB,IAAjB;;MACA,KAAK6C,cAAL,CAAoBT,QAAQ,CAACW,QAA7B,EAAuCX,QAAQ,CAACE,WAAhD;;MACA;IACH,CA1FG,CA2FJ;IACA;;;IACA,KAAKO,cAAL,CAAoBT,QAAQ,CAACW,QAA7B,EAAuCX,QAAQ,CAACE,WAAhD;EACH;;EACDhQ,MAAM,GAAG;IACL,KAAKiP,kBAAL;;IACA,KAAKH,aAAL,GAAqB,IAArB;IACA,KAAKoC,mBAAL,GAA2B,IAA3B;;IACA,KAAK/C,mBAAL,CAAyBvN,WAAzB;EACH;EACD;;;EACAsJ,OAAO,GAAG;IACN,IAAI,KAAK0E,WAAT,EAAsB;MAClB;IACH,CAHK,CAIN;IACA;;;IACA,IAAI,KAAKD,YAAT,EAAuB;MACnBwC,YAAY,CAAC,KAAKxC,YAAL,CAAkB3Q,KAAnB,EAA0B;QAClCX,GAAG,EAAE,EAD6B;QAElCC,IAAI,EAAE,EAF4B;QAGlCiE,KAAK,EAAE,EAH2B;QAIlCH,MAAM,EAAE,EAJ0B;QAKlClC,MAAM,EAAE,EAL0B;QAMlCE,KAAK,EAAE,EAN2B;QAOlCgS,UAAU,EAAE,EAPsB;QAQlCC,cAAc,EAAE;MARkB,CAA1B,CAAZ;IAUH;;IACD,IAAI,KAAKlJ,KAAT,EAAgB;MACZ,KAAK+G,0BAAL;IACH;;IACD,IAAI,KAAKrP,WAAT,EAAsB;MAClB,KAAKA,WAAL,CAAiBwJ,WAAjB,CAA6BpL,SAA7B,CAAuCU,MAAvC,CAA8CyO,gBAA9C;IACH;;IACD,KAAKpN,MAAL;;IACA,KAAKkO,gBAAL,CAAsBtD,QAAtB;;IACA,KAAK/K,WAAL,GAAmB,KAAK8O,YAAL,GAAoB,IAAvC;IACA,KAAKC,WAAL,GAAmB,IAAnB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACII,mBAAmB,GAAG;IAClB,IAAI,KAAKJ,WAAL,IAAoB,CAAC,KAAKtI,SAAL,CAAemB,SAAxC,EAAmD;MAC/C;IACH;;IACD,MAAM6J,YAAY,GAAG,KAAKxC,aAA1B;;IACA,IAAIwC,YAAJ,EAAkB;MACd,KAAKhC,WAAL,GAAmB,KAAKC,cAAL,EAAnB;MACA,KAAKC,YAAL,GAAoB,KAAKrH,KAAL,CAAW9F,qBAAX,EAApB;MACA,KAAK+M,aAAL,GAAqB,KAAKC,wBAAL,EAArB;MACA,KAAKI,cAAL,GAAsB,KAAKjC,iBAAL,CAAuBlG,mBAAvB,GAA6CjF,qBAA7C,EAAtB;;MACA,MAAM2N,WAAW,GAAG,KAAKC,eAAL,CAAqB,KAAKX,WAA1B,EAAuC,KAAKG,cAA5C,EAA4D6B,YAA5D,CAApB;;MACA,KAAKf,cAAL,CAAoBe,YAApB,EAAkCtB,WAAlC;IACH,CAPD,MAQK;MACD,KAAK/E,KAAL;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;;;EACIsG,wBAAwB,CAACC,WAAD,EAAc;IAClC,KAAKxD,YAAL,GAAoBwD,WAApB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIC,aAAa,CAAChD,SAAD,EAAY;IACrB,KAAKR,mBAAL,GAA2BQ,SAA3B,CADqB,CAErB;IACA;;IACA,IAAIA,SAAS,CAACpJ,OAAV,CAAkB,KAAKyJ,aAAvB,MAA0C,CAAC,CAA/C,EAAkD;MAC9C,KAAKA,aAAL,GAAqB,IAArB;IACH;;IACD,KAAKJ,kBAAL;;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIgD,kBAAkB,CAACC,MAAD,EAAS;IACvB,KAAK5D,eAAL,GAAuB4D,MAAvB;IACA,OAAO,IAAP;EACH;EACD;;;EACAC,sBAAsB,CAACC,kBAAkB,GAAG,IAAtB,EAA4B;IAC9C,KAAKhE,sBAAL,GAA8BgE,kBAA9B;IACA,OAAO,IAAP;EACH;EACD;;;EACAC,iBAAiB,CAACC,aAAa,GAAG,IAAjB,EAAuB;IACpC,KAAKnE,cAAL,GAAsBmE,aAAtB;IACA,OAAO,IAAP;EACH;EACD;;;EACAC,QAAQ,CAACC,OAAO,GAAG,IAAX,EAAiB;IACrB,KAAKtE,QAAL,GAAgBsE,OAAhB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIC,kBAAkB,CAACC,QAAQ,GAAG,IAAZ,EAAkB;IAChC,KAAKrE,eAAL,GAAuBqE,QAAvB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACI3D,SAAS,CAACxK,MAAD,EAAS;IACd,KAAKoO,OAAL,GAAepO,MAAf;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIqO,kBAAkB,CAACC,MAAD,EAAS;IACvB,KAAKlE,QAAL,GAAgBkE,MAAhB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIC,kBAAkB,CAACD,MAAD,EAAS;IACvB,KAAKjE,QAAL,GAAgBiE,MAAhB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIE,qBAAqB,CAACC,QAAD,EAAW;IAC5B,KAAKC,wBAAL,GAAgCD,QAAhC;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;;;EACIxC,eAAe,CAACP,UAAD,EAAaE,aAAb,EAA4BG,GAA5B,EAAiC;IAC5C,IAAI4C,CAAJ;;IACA,IAAI5C,GAAG,CAAC3L,OAAJ,IAAe,QAAnB,EAA6B;MACzB;MACA;MACAuO,CAAC,GAAGjD,UAAU,CAACpS,IAAX,GAAkBoS,UAAU,CAACtQ,KAAX,GAAmB,CAAzC;IACH,CAJD,MAKK;MACD,MAAMwT,MAAM,GAAG,KAAKC,MAAL,KAAgBnD,UAAU,CAACnO,KAA3B,GAAmCmO,UAAU,CAACpS,IAA7D;MACA,MAAMwV,IAAI,GAAG,KAAKD,MAAL,KAAgBnD,UAAU,CAACpS,IAA3B,GAAkCoS,UAAU,CAACnO,KAA1D;MACAoR,CAAC,GAAG5C,GAAG,CAAC3L,OAAJ,IAAe,OAAf,GAAyBwO,MAAzB,GAAkCE,IAAtC;IACH,CAX2C,CAY5C;IACA;;;IACA,IAAIlD,aAAa,CAACtS,IAAd,GAAqB,CAAzB,EAA4B;MACxBqV,CAAC,IAAI/C,aAAa,CAACtS,IAAnB;IACH;;IACD,IAAIyV,CAAJ;;IACA,IAAIhD,GAAG,CAAC1L,OAAJ,IAAe,QAAnB,EAA6B;MACzB0O,CAAC,GAAGrD,UAAU,CAACrS,GAAX,GAAiBqS,UAAU,CAACxQ,MAAX,GAAoB,CAAzC;IACH,CAFD,MAGK;MACD6T,CAAC,GAAGhD,GAAG,CAAC1L,OAAJ,IAAe,KAAf,GAAuBqL,UAAU,CAACrS,GAAlC,GAAwCqS,UAAU,CAACtO,MAAvD;IACH,CAvB2C,CAwB5C;IACA;IACA;IACA;IACA;;;IACA,IAAIwO,aAAa,CAACvS,GAAd,GAAoB,CAAxB,EAA2B;MACvB0V,CAAC,IAAInD,aAAa,CAACvS,GAAnB;IACH;;IACD,OAAO;MAAEsV,CAAF;MAAKI;IAAL,CAAP;EACH;EACD;AACJ;AACA;AACA;;;EACI5C,gBAAgB,CAACH,WAAD,EAAc7N,WAAd,EAA2B4N,GAA3B,EAAgC;IAC5C;IACA;IACA,IAAIiD,aAAJ;;IACA,IAAIjD,GAAG,CAACzL,QAAJ,IAAgB,QAApB,EAA8B;MAC1B0O,aAAa,GAAG,CAAC7Q,WAAW,CAAC/C,KAAb,GAAqB,CAArC;IACH,CAFD,MAGK,IAAI2Q,GAAG,CAACzL,QAAJ,KAAiB,OAArB,EAA8B;MAC/B0O,aAAa,GAAG,KAAKH,MAAL,KAAgB,CAAC1Q,WAAW,CAAC/C,KAA7B,GAAqC,CAArD;IACH,CAFI,MAGA;MACD4T,aAAa,GAAG,KAAKH,MAAL,KAAgB,CAAhB,GAAoB,CAAC1Q,WAAW,CAAC/C,KAAjD;IACH;;IACD,IAAI6T,aAAJ;;IACA,IAAIlD,GAAG,CAACxL,QAAJ,IAAgB,QAApB,EAA8B;MAC1B0O,aAAa,GAAG,CAAC9Q,WAAW,CAACjD,MAAb,GAAsB,CAAtC;IACH,CAFD,MAGK;MACD+T,aAAa,GAAGlD,GAAG,CAACxL,QAAJ,IAAgB,KAAhB,GAAwB,CAAxB,GAA4B,CAACpC,WAAW,CAACjD,MAAzD;IACH,CAnB2C,CAoB5C;;;IACA,OAAO;MACHyT,CAAC,EAAE3C,WAAW,CAAC2C,CAAZ,GAAgBK,aADhB;MAEHD,CAAC,EAAE/C,WAAW,CAAC+C,CAAZ,GAAgBE;IAFhB,CAAP;EAIH;EACD;;;EACA5C,cAAc,CAAC6C,KAAD,EAAQC,cAAR,EAAwBpU,QAAxB,EAAkC0R,QAAlC,EAA4C;IACtD;IACA;IACA,MAAMxM,OAAO,GAAGmP,4BAA4B,CAACD,cAAD,CAA5C;IACA,IAAI;MAAER,CAAF;MAAKI;IAAL,IAAWG,KAAf;;IACA,IAAIhP,OAAO,GAAG,KAAKmP,UAAL,CAAgB5C,QAAhB,EAA0B,GAA1B,CAAd;;IACA,IAAItM,OAAO,GAAG,KAAKkP,UAAL,CAAgB5C,QAAhB,EAA0B,GAA1B,CAAd,CANsD,CAOtD;;;IACA,IAAIvM,OAAJ,EAAa;MACTyO,CAAC,IAAIzO,OAAL;IACH;;IACD,IAAIC,OAAJ,EAAa;MACT4O,CAAC,IAAI5O,OAAL;IACH,CAbqD,CActD;;;IACA,IAAImP,YAAY,GAAG,IAAIX,CAAvB;IACA,IAAIY,aAAa,GAAGZ,CAAC,GAAG1O,OAAO,CAAC7E,KAAZ,GAAoBL,QAAQ,CAACK,KAAjD;IACA,IAAIoU,WAAW,GAAG,IAAIT,CAAtB;IACA,IAAIU,cAAc,GAAGV,CAAC,GAAG9O,OAAO,CAAC/E,MAAZ,GAAqBH,QAAQ,CAACG,MAAnD,CAlBsD,CAmBtD;;IACA,IAAIwU,YAAY,GAAG,KAAKC,kBAAL,CAAwB1P,OAAO,CAAC7E,KAAhC,EAAuCkU,YAAvC,EAAqDC,aAArD,CAAnB;;IACA,IAAIK,aAAa,GAAG,KAAKD,kBAAL,CAAwB1P,OAAO,CAAC/E,MAAhC,EAAwCsU,WAAxC,EAAqDC,cAArD,CAApB;;IACA,IAAI7C,WAAW,GAAG8C,YAAY,GAAGE,aAAjC;IACA,OAAO;MACHhD,WADG;MAEHN,0BAA0B,EAAErM,OAAO,CAAC7E,KAAR,GAAgB6E,OAAO,CAAC/E,MAAxB,KAAmC0R,WAF5D;MAGHiD,wBAAwB,EAAED,aAAa,KAAK3P,OAAO,CAAC/E,MAHjD;MAIH4U,0BAA0B,EAAEJ,YAAY,IAAIzP,OAAO,CAAC7E;IAJjD,CAAP;EAMH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIoR,6BAA6B,CAACO,GAAD,EAAMmC,KAAN,EAAanU,QAAb,EAAuB;IAChD,IAAI,KAAK8O,sBAAT,EAAiC;MAC7B,MAAMkG,eAAe,GAAGhV,QAAQ,CAACqC,MAAT,GAAkB8R,KAAK,CAACH,CAAhD;MACA,MAAMiB,cAAc,GAAGjV,QAAQ,CAACwC,KAAT,GAAiB2R,KAAK,CAACP,CAA9C;MACA,MAAM5G,SAAS,GAAGkI,aAAa,CAAC,KAAKpU,WAAL,CAAiBmL,SAAjB,GAA6Be,SAA9B,CAA/B;MACA,MAAMD,QAAQ,GAAGmI,aAAa,CAAC,KAAKpU,WAAL,CAAiBmL,SAAjB,GAA6Bc,QAA9B,CAA9B;MACA,MAAMoI,WAAW,GAAGnD,GAAG,CAAC8C,wBAAJ,IAAiC9H,SAAS,IAAI,IAAb,IAAqBA,SAAS,IAAIgI,eAAvF;MACA,MAAMI,aAAa,GAAGpD,GAAG,CAAC+C,0BAAJ,IAAmChI,QAAQ,IAAI,IAAZ,IAAoBA,QAAQ,IAAIkI,cAAzF;MACA,OAAOE,WAAW,IAAIC,aAAtB;IACH;;IACD,OAAO,KAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIC,oBAAoB,CAACC,KAAD,EAAQlB,cAAR,EAAwB3S,cAAxB,EAAwC;IACxD;IACA;IACA;IACA,IAAI,KAAK0Q,mBAAL,IAA4B,KAAKpD,eAArC,EAAsD;MAClD,OAAO;QACH6E,CAAC,EAAE0B,KAAK,CAAC1B,CAAN,GAAU,KAAKzB,mBAAL,CAAyByB,CADnC;QAEHI,CAAC,EAAEsB,KAAK,CAACtB,CAAN,GAAU,KAAK7B,mBAAL,CAAyB6B;MAFnC,CAAP;IAIH,CATuD,CAUxD;IACA;;;IACA,MAAM9O,OAAO,GAAGmP,4BAA4B,CAACD,cAAD,CAA5C;IACA,MAAMpU,QAAQ,GAAG,KAAKqQ,aAAtB,CAbwD,CAcxD;IACA;;IACA,MAAMkF,aAAa,GAAG7T,IAAI,CAAC8T,GAAL,CAASF,KAAK,CAAC1B,CAAN,GAAU1O,OAAO,CAAC7E,KAAlB,GAA0BL,QAAQ,CAACK,KAA5C,EAAmD,CAAnD,CAAtB;IACA,MAAMoV,cAAc,GAAG/T,IAAI,CAAC8T,GAAL,CAASF,KAAK,CAACtB,CAAN,GAAU9O,OAAO,CAAC/E,MAAlB,GAA2BH,QAAQ,CAACG,MAA7C,EAAqD,CAArD,CAAvB;IACA,MAAMuV,WAAW,GAAGhU,IAAI,CAAC8T,GAAL,CAASxV,QAAQ,CAAC1B,GAAT,GAAemD,cAAc,CAACnD,GAA9B,GAAoCgX,KAAK,CAACtB,CAAnD,EAAsD,CAAtD,CAApB;IACA,MAAM2B,YAAY,GAAGjU,IAAI,CAAC8T,GAAL,CAASxV,QAAQ,CAACzB,IAAT,GAAgBkD,cAAc,CAAClD,IAA/B,GAAsC+W,KAAK,CAAC1B,CAArD,EAAwD,CAAxD,CAArB,CAnBwD,CAoBxD;;IACA,IAAIgC,KAAK,GAAG,CAAZ;IACA,IAAIC,KAAK,GAAG,CAAZ,CAtBwD,CAuBxD;IACA;IACA;;IACA,IAAI3Q,OAAO,CAAC7E,KAAR,IAAiBL,QAAQ,CAACK,KAA9B,EAAqC;MACjCuV,KAAK,GAAGD,YAAY,IAAI,CAACJ,aAAzB;IACH,CAFD,MAGK;MACDK,KAAK,GAAGN,KAAK,CAAC1B,CAAN,GAAU,KAAK5E,eAAf,GAAiChP,QAAQ,CAACzB,IAAT,GAAgBkD,cAAc,CAAClD,IAA/B,GAAsC+W,KAAK,CAAC1B,CAA7E,GAAiF,CAAzF;IACH;;IACD,IAAI1O,OAAO,CAAC/E,MAAR,IAAkBH,QAAQ,CAACG,MAA/B,EAAuC;MACnC0V,KAAK,GAAGH,WAAW,IAAI,CAACD,cAAxB;IACH,CAFD,MAGK;MACDI,KAAK,GAAGP,KAAK,CAACtB,CAAN,GAAU,KAAKhF,eAAf,GAAiChP,QAAQ,CAAC1B,GAAT,GAAemD,cAAc,CAACnD,GAA9B,GAAoCgX,KAAK,CAACtB,CAA3E,GAA+E,CAAvF;IACH;;IACD,KAAK7B,mBAAL,GAA2B;MAAEyB,CAAC,EAAEgC,KAAL;MAAY5B,CAAC,EAAE6B;IAAf,CAA3B;IACA,OAAO;MACHjC,CAAC,EAAE0B,KAAK,CAAC1B,CAAN,GAAUgC,KADV;MAEH5B,CAAC,EAAEsB,KAAK,CAACtB,CAAN,GAAU6B;IAFV,CAAP;EAIH;EACD;AACJ;AACA;AACA;AACA;;;EACIrE,cAAc,CAACE,QAAD,EAAWT,WAAX,EAAwB;IAClC,KAAK6E,mBAAL,CAAyBpE,QAAzB;;IACA,KAAKqE,wBAAL,CAA8B9E,WAA9B,EAA2CS,QAA3C;;IACA,KAAKsE,qBAAL,CAA2B/E,WAA3B,EAAwCS,QAAxC;;IACA,IAAIA,QAAQ,CAAClN,UAAb,EAAyB;MACrB,KAAKyR,gBAAL,CAAsBvE,QAAQ,CAAClN,UAA/B;IACH,CANiC,CAOlC;;;IACA,KAAKuL,aAAL,GAAqB2B,QAArB,CARkC,CASlC;IACA;IACA;;IACA,IAAI,KAAKvC,gBAAL,CAAsBpI,SAAtB,CAAgCP,MAApC,EAA4C;MACxC,MAAMZ,wBAAwB,GAAG,KAAKsQ,oBAAL,EAAjC;;MACA,MAAMC,WAAW,GAAG,IAAIzQ,8BAAJ,CAAmCgM,QAAnC,EAA6C9L,wBAA7C,CAApB;;MACA,KAAKuJ,gBAAL,CAAsBlI,IAAtB,CAA2BkP,WAA3B;IACH;;IACD,KAAKrG,gBAAL,GAAwB,KAAxB;EACH;EACD;;;EACAgG,mBAAmB,CAACpE,QAAD,EAAW;IAC1B,IAAI,CAAC,KAAKiC,wBAAV,EAAoC;MAChC;IACH;;IACD,MAAMyC,QAAQ,GAAG,KAAKxG,YAAL,CAAkBhH,gBAAlB,CAAmC,KAAK+K,wBAAxC,CAAjB;;IACA,IAAI0C,OAAJ;IACA,IAAIC,OAAO,GAAG5E,QAAQ,CAAClM,QAAvB;;IACA,IAAIkM,QAAQ,CAACnM,QAAT,KAAsB,QAA1B,EAAoC;MAChC8Q,OAAO,GAAG,QAAV;IACH,CAFD,MAGK,IAAI,KAAKvC,MAAL,EAAJ,EAAmB;MACpBuC,OAAO,GAAG3E,QAAQ,CAACnM,QAAT,KAAsB,OAAtB,GAAgC,OAAhC,GAA0C,MAApD;IACH,CAFI,MAGA;MACD8Q,OAAO,GAAG3E,QAAQ,CAACnM,QAAT,KAAsB,OAAtB,GAAgC,MAAhC,GAAyC,OAAnD;IACH;;IACD,KAAK,IAAIsB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuP,QAAQ,CAAC5P,MAA7B,EAAqCK,CAAC,EAAtC,EAA0C;MACtCuP,QAAQ,CAACvP,CAAD,CAAR,CAAY5H,KAAZ,CAAkBsX,eAAlB,GAAqC,GAAEF,OAAQ,IAAGC,OAAQ,EAA1D;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACI1E,yBAAyB,CAAC3M,MAAD,EAASyM,QAAT,EAAmB;IACxC,MAAM1R,QAAQ,GAAG,KAAKqQ,aAAtB;;IACA,MAAMmG,KAAK,GAAG,KAAK1C,MAAL,EAAd;;IACA,IAAI3T,MAAJ,EAAY7B,GAAZ,EAAiB+D,MAAjB;;IACA,IAAIqP,QAAQ,CAAClM,QAAT,KAAsB,KAA1B,EAAiC;MAC7B;MACAlH,GAAG,GAAG2G,MAAM,CAAC+O,CAAb;MACA7T,MAAM,GAAGH,QAAQ,CAACG,MAAT,GAAkB7B,GAAlB,GAAwB,KAAK0Q,eAAtC;IACH,CAJD,MAKK,IAAI0C,QAAQ,CAAClM,QAAT,KAAsB,QAA1B,EAAoC;MACrC;MACA;MACA;MACAnD,MAAM,GAAGrC,QAAQ,CAACG,MAAT,GAAkB8E,MAAM,CAAC+O,CAAzB,GAA6B,KAAKhF,eAAL,GAAuB,CAA7D;MACA7O,MAAM,GAAGH,QAAQ,CAACG,MAAT,GAAkBkC,MAAlB,GAA2B,KAAK2M,eAAzC;IACH,CANI,MAOA;MACD;MACA;MACA;MACA;MACA,MAAMyH,8BAA8B,GAAG/U,IAAI,CAACgV,GAAL,CAAS1W,QAAQ,CAACqC,MAAT,GAAkB4C,MAAM,CAAC+O,CAAzB,GAA6BhU,QAAQ,CAAC1B,GAA/C,EAAoD2G,MAAM,CAAC+O,CAA3D,CAAvC;MACA,MAAM2C,cAAc,GAAG,KAAKjI,oBAAL,CAA0BvO,MAAjD;MACAA,MAAM,GAAGsW,8BAA8B,GAAG,CAA1C;MACAnY,GAAG,GAAG2G,MAAM,CAAC+O,CAAP,GAAWyC,8BAAjB;;MACA,IAAItW,MAAM,GAAGwW,cAAT,IAA2B,CAAC,KAAK7G,gBAAjC,IAAqD,CAAC,KAAKjB,cAA/D,EAA+E;QAC3EvQ,GAAG,GAAG2G,MAAM,CAAC+O,CAAP,GAAW2C,cAAc,GAAG,CAAlC;MACH;IACJ,CA5BuC,CA6BxC;;;IACA,MAAMC,4BAA4B,GAAIlF,QAAQ,CAACnM,QAAT,KAAsB,OAAtB,IAAiC,CAACiR,KAAnC,IAA8C9E,QAAQ,CAACnM,QAAT,KAAsB,KAAtB,IAA+BiR,KAAlH,CA9BwC,CA+BxC;;IACA,MAAMK,2BAA2B,GAAInF,QAAQ,CAACnM,QAAT,KAAsB,KAAtB,IAA+B,CAACiR,KAAjC,IAA4C9E,QAAQ,CAACnM,QAAT,KAAsB,OAAtB,IAAiCiR,KAAjH;IACA,IAAInW,KAAJ,EAAW9B,IAAX,EAAiBiE,KAAjB;;IACA,IAAIqU,2BAAJ,EAAiC;MAC7BrU,KAAK,GAAGxC,QAAQ,CAACK,KAAT,GAAiB4E,MAAM,CAAC2O,CAAxB,GAA4B,KAAK5E,eAAzC;MACA3O,KAAK,GAAG4E,MAAM,CAAC2O,CAAP,GAAW,KAAK5E,eAAxB;IACH,CAHD,MAIK,IAAI4H,4BAAJ,EAAkC;MACnCrY,IAAI,GAAG0G,MAAM,CAAC2O,CAAd;MACAvT,KAAK,GAAGL,QAAQ,CAACwC,KAAT,GAAiByC,MAAM,CAAC2O,CAAhC;IACH,CAHI,MAIA;MACD;MACA;MACA;MACA;MACA,MAAM6C,8BAA8B,GAAG/U,IAAI,CAACgV,GAAL,CAAS1W,QAAQ,CAACwC,KAAT,GAAiByC,MAAM,CAAC2O,CAAxB,GAA4B5T,QAAQ,CAACzB,IAA9C,EAAoD0G,MAAM,CAAC2O,CAA3D,CAAvC;MACA,MAAMkD,aAAa,GAAG,KAAKpI,oBAAL,CAA0BrO,KAAhD;MACAA,KAAK,GAAGoW,8BAA8B,GAAG,CAAzC;MACAlY,IAAI,GAAG0G,MAAM,CAAC2O,CAAP,GAAW6C,8BAAlB;;MACA,IAAIpW,KAAK,GAAGyW,aAAR,IAAyB,CAAC,KAAKhH,gBAA/B,IAAmD,CAAC,KAAKjB,cAA7D,EAA6E;QACzEtQ,IAAI,GAAG0G,MAAM,CAAC2O,CAAP,GAAWkD,aAAa,GAAG,CAAlC;MACH;IACJ;;IACD,OAAO;MAAExY,GAAG,EAAEA,GAAP;MAAYC,IAAI,EAAEA,IAAlB;MAAwB8D,MAAM,EAAEA,MAAhC;MAAwCG,KAAK,EAAEA,KAA/C;MAAsDnC,KAAtD;MAA6DF;IAA7D,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACI6V,qBAAqB,CAAC/Q,MAAD,EAASyM,QAAT,EAAmB;IACpC,MAAMC,eAAe,GAAG,KAAKC,yBAAL,CAA+B3M,MAA/B,EAAuCyM,QAAvC,CAAxB,CADoC,CAEpC;IACA;;;IACA,IAAI,CAAC,KAAK5B,gBAAN,IAA0B,CAAC,KAAKjB,cAApC,EAAoD;MAChD8C,eAAe,CAACxR,MAAhB,GAAyBuB,IAAI,CAACgV,GAAL,CAAS/E,eAAe,CAACxR,MAAzB,EAAiC,KAAKuO,oBAAL,CAA0BvO,MAA3D,CAAzB;MACAwR,eAAe,CAACtR,KAAhB,GAAwBqB,IAAI,CAACgV,GAAL,CAAS/E,eAAe,CAACtR,KAAzB,EAAgC,KAAKqO,oBAAL,CAA0BrO,KAA1D,CAAxB;IACH;;IACD,MAAM0W,MAAM,GAAG,EAAf;;IACA,IAAI,KAAKC,iBAAL,EAAJ,EAA8B;MAC1BD,MAAM,CAACzY,GAAP,GAAayY,MAAM,CAACxY,IAAP,GAAc,GAA3B;MACAwY,MAAM,CAAC1U,MAAP,GAAgB0U,MAAM,CAACvU,KAAP,GAAeuU,MAAM,CAAC7J,SAAP,GAAmB6J,MAAM,CAAC9J,QAAP,GAAkB,EAApE;MACA8J,MAAM,CAAC1W,KAAP,GAAe0W,MAAM,CAAC5W,MAAP,GAAgB,MAA/B;IACH,CAJD,MAKK;MACD,MAAM+M,SAAS,GAAG,KAAKpM,WAAL,CAAiBmL,SAAjB,GAA6BiB,SAA/C;;MACA,MAAMD,QAAQ,GAAG,KAAKnM,WAAL,CAAiBmL,SAAjB,GAA6BgB,QAA9C;;MACA8J,MAAM,CAAC5W,MAAP,GAAgBvD,mBAAmB,CAAC+U,eAAe,CAACxR,MAAjB,CAAnC;MACA4W,MAAM,CAACzY,GAAP,GAAa1B,mBAAmB,CAAC+U,eAAe,CAACrT,GAAjB,CAAhC;MACAyY,MAAM,CAAC1U,MAAP,GAAgBzF,mBAAmB,CAAC+U,eAAe,CAACtP,MAAjB,CAAnC;MACA0U,MAAM,CAAC1W,KAAP,GAAezD,mBAAmB,CAAC+U,eAAe,CAACtR,KAAjB,CAAlC;MACA0W,MAAM,CAACxY,IAAP,GAAc3B,mBAAmB,CAAC+U,eAAe,CAACpT,IAAjB,CAAjC;MACAwY,MAAM,CAACvU,KAAP,GAAe5F,mBAAmB,CAAC+U,eAAe,CAACnP,KAAjB,CAAlC,CARC,CASD;;MACA,IAAIkP,QAAQ,CAACnM,QAAT,KAAsB,QAA1B,EAAoC;QAChCwR,MAAM,CAAC1E,UAAP,GAAoB,QAApB;MACH,CAFD,MAGK;QACD0E,MAAM,CAAC1E,UAAP,GAAoBX,QAAQ,CAACnM,QAAT,KAAsB,KAAtB,GAA8B,UAA9B,GAA2C,YAA/D;MACH;;MACD,IAAImM,QAAQ,CAAClM,QAAT,KAAsB,QAA1B,EAAoC;QAChCuR,MAAM,CAACzE,cAAP,GAAwB,QAAxB;MACH,CAFD,MAGK;QACDyE,MAAM,CAACzE,cAAP,GAAwBZ,QAAQ,CAAClM,QAAT,KAAsB,QAAtB,GAAiC,UAAjC,GAA8C,YAAtE;MACH;;MACD,IAAI0H,SAAJ,EAAe;QACX6J,MAAM,CAAC7J,SAAP,GAAmBtQ,mBAAmB,CAACsQ,SAAD,CAAtC;MACH;;MACD,IAAID,QAAJ,EAAc;QACV8J,MAAM,CAAC9J,QAAP,GAAkBrQ,mBAAmB,CAACqQ,QAAD,CAArC;MACH;IACJ;;IACD,KAAKyB,oBAAL,GAA4BiD,eAA5B;IACAS,YAAY,CAAC,KAAKxC,YAAL,CAAkB3Q,KAAnB,EAA0B8X,MAA1B,CAAZ;EACH;EACD;;;EACA3G,uBAAuB,GAAG;IACtBgC,YAAY,CAAC,KAAKxC,YAAL,CAAkB3Q,KAAnB,EAA0B;MAClCX,GAAG,EAAE,GAD6B;MAElCC,IAAI,EAAE,GAF4B;MAGlCiE,KAAK,EAAE,GAH2B;MAIlCH,MAAM,EAAE,GAJ0B;MAKlClC,MAAM,EAAE,EAL0B;MAMlCE,KAAK,EAAE,EAN2B;MAOlCgS,UAAU,EAAE,EAPsB;MAQlCC,cAAc,EAAE;IARkB,CAA1B,CAAZ;EAUH;EACD;;;EACAnC,0BAA0B,GAAG;IACzBiC,YAAY,CAAC,KAAKhJ,KAAL,CAAWnK,KAAZ,EAAmB;MAC3BX,GAAG,EAAE,EADsB;MAE3BC,IAAI,EAAE,EAFqB;MAG3B8D,MAAM,EAAE,EAHmB;MAI3BG,KAAK,EAAE,EAJoB;MAK3BkP,QAAQ,EAAE,EALiB;MAM3BuF,SAAS,EAAE;IANgB,CAAnB,CAAZ;EAQH;EACD;;;EACAlB,wBAAwB,CAAC9E,WAAD,EAAcS,QAAd,EAAwB;IAC5C,MAAMqF,MAAM,GAAG,EAAf;;IACA,MAAMG,gBAAgB,GAAG,KAAKF,iBAAL,EAAzB;;IACA,MAAMG,qBAAqB,GAAG,KAAKrI,sBAAnC;;IACA,MAAMnL,MAAM,GAAG,KAAK7C,WAAL,CAAiBmL,SAAjB,EAAf;;IACA,IAAIiL,gBAAJ,EAAsB;MAClB,MAAMzV,cAAc,GAAG,KAAKtD,cAAL,CAAoBa,yBAApB,EAAvB;;MACAoT,YAAY,CAAC2E,MAAD,EAAS,KAAKK,iBAAL,CAAuB1F,QAAvB,EAAiCT,WAAjC,EAA8CxP,cAA9C,CAAT,CAAZ;MACA2Q,YAAY,CAAC2E,MAAD,EAAS,KAAKM,iBAAL,CAAuB3F,QAAvB,EAAiCT,WAAjC,EAA8CxP,cAA9C,CAAT,CAAZ;IACH,CAJD,MAKK;MACDsV,MAAM,CAACrF,QAAP,GAAkB,QAAlB;IACH,CAZ2C,CAa5C;IACA;IACA;IACA;IACA;;;IACA,IAAI4F,eAAe,GAAG,EAAtB;;IACA,IAAInS,OAAO,GAAG,KAAKmP,UAAL,CAAgB5C,QAAhB,EAA0B,GAA1B,CAAd;;IACA,IAAItM,OAAO,GAAG,KAAKkP,UAAL,CAAgB5C,QAAhB,EAA0B,GAA1B,CAAd;;IACA,IAAIvM,OAAJ,EAAa;MACTmS,eAAe,IAAK,cAAanS,OAAQ,MAAzC;IACH;;IACD,IAAIC,OAAJ,EAAa;MACTkS,eAAe,IAAK,cAAalS,OAAQ,KAAzC;IACH;;IACD2R,MAAM,CAACE,SAAP,GAAmBK,eAAe,CAACC,IAAhB,EAAnB,CA3B4C,CA4B5C;IACA;IACA;IACA;IACA;;IACA,IAAI5T,MAAM,CAACuJ,SAAX,EAAsB;MAClB,IAAIgK,gBAAJ,EAAsB;QAClBH,MAAM,CAAC7J,SAAP,GAAmBtQ,mBAAmB,CAAC+G,MAAM,CAACuJ,SAAR,CAAtC;MACH,CAFD,MAGK,IAAIiK,qBAAJ,EAA2B;QAC5BJ,MAAM,CAAC7J,SAAP,GAAmB,EAAnB;MACH;IACJ;;IACD,IAAIvJ,MAAM,CAACsJ,QAAX,EAAqB;MACjB,IAAIiK,gBAAJ,EAAsB;QAClBH,MAAM,CAAC9J,QAAP,GAAkBrQ,mBAAmB,CAAC+G,MAAM,CAACsJ,QAAR,CAArC;MACH,CAFD,MAGK,IAAIkK,qBAAJ,EAA2B;QAC5BJ,MAAM,CAAC9J,QAAP,GAAkB,EAAlB;MACH;IACJ;;IACDmF,YAAY,CAAC,KAAKhJ,KAAL,CAAWnK,KAAZ,EAAmB8X,MAAnB,CAAZ;EACH;EACD;;;EACAK,iBAAiB,CAAC1F,QAAD,EAAWT,WAAX,EAAwBxP,cAAxB,EAAwC;IACrD;IACA;IACA,IAAIsV,MAAM,GAAG;MAAEzY,GAAG,EAAE,EAAP;MAAW+D,MAAM,EAAE;IAAnB,CAAb;;IACA,IAAI8O,YAAY,GAAG,KAAKC,gBAAL,CAAsBH,WAAtB,EAAmC,KAAKR,YAAxC,EAAsDiB,QAAtD,CAAnB;;IACA,IAAI,KAAK/C,SAAT,EAAoB;MAChBwC,YAAY,GAAG,KAAKkE,oBAAL,CAA0BlE,YAA1B,EAAwC,KAAKV,YAA7C,EAA2DhP,cAA3D,CAAf;IACH,CAPoD,CAQrD;IACA;;;IACA,IAAIiQ,QAAQ,CAAClM,QAAT,KAAsB,QAA1B,EAAoC;MAChC;MACA;MACA,MAAMgS,cAAc,GAAG,KAAK/Y,SAAL,CAAeK,eAAf,CAA+B2Y,YAAtD;MACAV,MAAM,CAAC1U,MAAP,GAAiB,GAAEmV,cAAc,IAAIrG,YAAY,CAAC6C,CAAb,GAAiB,KAAKvD,YAAL,CAAkBtQ,MAAvC,CAA+C,IAAhF;IACH,CALD,MAMK;MACD4W,MAAM,CAACzY,GAAP,GAAa1B,mBAAmB,CAACuU,YAAY,CAAC6C,CAAd,CAAhC;IACH;;IACD,OAAO+C,MAAP;EACH;EACD;;;EACAM,iBAAiB,CAAC3F,QAAD,EAAWT,WAAX,EAAwBxP,cAAxB,EAAwC;IACrD;IACA;IACA,IAAIsV,MAAM,GAAG;MAAExY,IAAI,EAAE,EAAR;MAAYiE,KAAK,EAAE;IAAnB,CAAb;;IACA,IAAI2O,YAAY,GAAG,KAAKC,gBAAL,CAAsBH,WAAtB,EAAmC,KAAKR,YAAxC,EAAsDiB,QAAtD,CAAnB;;IACA,IAAI,KAAK/C,SAAT,EAAoB;MAChBwC,YAAY,GAAG,KAAKkE,oBAAL,CAA0BlE,YAA1B,EAAwC,KAAKV,YAA7C,EAA2DhP,cAA3D,CAAf;IACH,CAPoD,CAQrD;IACA;IACA;IACA;;;IACA,IAAIiW,uBAAJ;;IACA,IAAI,KAAK5D,MAAL,EAAJ,EAAmB;MACf4D,uBAAuB,GAAGhG,QAAQ,CAACnM,QAAT,KAAsB,KAAtB,GAA8B,MAA9B,GAAuC,OAAjE;IACH,CAFD,MAGK;MACDmS,uBAAuB,GAAGhG,QAAQ,CAACnM,QAAT,KAAsB,KAAtB,GAA8B,OAA9B,GAAwC,MAAlE;IACH,CAlBoD,CAmBrD;IACA;;;IACA,IAAImS,uBAAuB,KAAK,OAAhC,EAAyC;MACrC,MAAMC,aAAa,GAAG,KAAKlZ,SAAL,CAAeK,eAAf,CAA+B8Y,WAArD;MACAb,MAAM,CAACvU,KAAP,GAAgB,GAAEmV,aAAa,IAAIxG,YAAY,CAACyC,CAAb,GAAiB,KAAKnD,YAAL,CAAkBpQ,KAAvC,CAA8C,IAA7E;IACH,CAHD,MAIK;MACD0W,MAAM,CAACxY,IAAP,GAAc3B,mBAAmB,CAACuU,YAAY,CAACyC,CAAd,CAAjC;IACH;;IACD,OAAOmD,MAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIb,oBAAoB,GAAG;IACnB;IACA,MAAM2B,YAAY,GAAG,KAAKrH,cAAL,EAArB;;IACA,MAAMsH,aAAa,GAAG,KAAK1O,KAAL,CAAW9F,qBAAX,EAAtB,CAHmB,CAInB;IACA;IACA;;;IACA,MAAMyU,qBAAqB,GAAG,KAAK9I,YAAL,CAAkB+I,GAAlB,CAAsBC,UAAU,IAAI;MAC9D,OAAOA,UAAU,CAACC,aAAX,GAA2BC,aAA3B,CAAyC7U,qBAAzC,EAAP;IACH,CAF6B,CAA9B;;IAGA,OAAO;MACH8U,eAAe,EAAE1V,2BAA2B,CAACmV,YAAD,EAAeE,qBAAf,CADzC;MAEHM,mBAAmB,EAAEtW,4BAA4B,CAAC8V,YAAD,EAAeE,qBAAf,CAF9C;MAGHO,gBAAgB,EAAE5V,2BAA2B,CAACoV,aAAD,EAAgBC,qBAAhB,CAH1C;MAIHQ,oBAAoB,EAAExW,4BAA4B,CAAC+V,aAAD,EAAgBC,qBAAhB;IAJ/C,CAAP;EAMH;EACD;;;EACAnD,kBAAkB,CAACpO,MAAD,EAAS,GAAGgS,SAAZ,EAAuB;IACrC,OAAOA,SAAS,CAACC,MAAV,CAAiB,CAACC,YAAD,EAAeC,eAAf,KAAmC;MACvD,OAAOD,YAAY,GAAGhX,IAAI,CAAC8T,GAAL,CAASmD,eAAT,EAA0B,CAA1B,CAAtB;IACH,CAFM,EAEJnS,MAFI,CAAP;EAGH;EACD;;;EACA8J,wBAAwB,GAAG;IACvB;IACA;IACA;IACA;IACA;IACA,MAAMjQ,KAAK,GAAG,KAAK5B,SAAL,CAAeK,eAAf,CAA+B8Y,WAA7C;IACA,MAAMzX,MAAM,GAAG,KAAK1B,SAAL,CAAeK,eAAf,CAA+B2Y,YAA9C;;IACA,MAAMhW,cAAc,GAAG,KAAKtD,cAAL,CAAoBa,yBAApB,EAAvB;;IACA,OAAO;MACHV,GAAG,EAAEmD,cAAc,CAACnD,GAAf,GAAqB,KAAK0Q,eAD5B;MAEHzQ,IAAI,EAAEkD,cAAc,CAAClD,IAAf,GAAsB,KAAKyQ,eAF9B;MAGHxM,KAAK,EAAEf,cAAc,CAAClD,IAAf,GAAsB8B,KAAtB,GAA8B,KAAK2O,eAHvC;MAIH3M,MAAM,EAAEZ,cAAc,CAACnD,GAAf,GAAqB6B,MAArB,GAA8B,KAAK6O,eAJxC;MAKH3O,KAAK,EAAEA,KAAK,GAAG,IAAI,KAAK2O,eALrB;MAMH7O,MAAM,EAAEA,MAAM,GAAG,IAAI,KAAK6O;IANvB,CAAP;EAQH;EACD;;;EACA8E,MAAM,GAAG;IACL,OAAO,KAAKhT,WAAL,CAAiB+L,YAAjB,OAAoC,KAA3C;EACH;EACD;;;EACAmK,iBAAiB,GAAG;IAChB,OAAO,CAAC,KAAKlI,sBAAN,IAAgC,KAAKH,SAA5C;EACH;EACD;;;EACA2F,UAAU,CAAC5C,QAAD,EAAWkH,IAAX,EAAiB;IACvB,IAAIA,IAAI,KAAK,GAAb,EAAkB;MACd;MACA;MACA,OAAOlH,QAAQ,CAACvM,OAAT,IAAoB,IAApB,GAA2B,KAAKkK,QAAhC,GAA2CqC,QAAQ,CAACvM,OAA3D;IACH;;IACD,OAAOuM,QAAQ,CAACtM,OAAT,IAAoB,IAApB,GAA2B,KAAKkK,QAAhC,GAA2CoC,QAAQ,CAACtM,OAA3D;EACH;EACD;;;EACAuK,kBAAkB,GAAG;IACjB,IAAI,OAAOxO,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;MAC/C,IAAI,CAAC,KAAK+N,mBAAL,CAAyB1I,MAA9B,EAAsC;QAClC,MAAMjG,KAAK,CAAC,uEAAD,CAAX;MACH,CAH8C,CAI/C;MACA;;;MACA,KAAK2O,mBAAL,CAAyB2J,OAAzB,CAAiCC,IAAI,IAAI;QACrC9S,0BAA0B,CAAC,SAAD,EAAY8S,IAAI,CAACzT,OAAjB,CAA1B;QACAQ,wBAAwB,CAAC,SAAD,EAAYiT,IAAI,CAACxT,OAAjB,CAAxB;QACAU,0BAA0B,CAAC,UAAD,EAAa8S,IAAI,CAACvT,QAAlB,CAA1B;QACAM,wBAAwB,CAAC,UAAD,EAAaiT,IAAI,CAACtT,QAAlB,CAAxB;MACH,CALD;IAMH;EACJ;EACD;;;EACAyQ,gBAAgB,CAACpI,UAAD,EAAa;IACzB,IAAI,KAAKzE,KAAT,EAAgB;MACZvM,WAAW,CAACgR,UAAD,CAAX,CAAwBgL,OAAxB,CAAgCE,QAAQ,IAAI;QACxC,IAAIA,QAAQ,KAAK,EAAb,IAAmB,KAAKxJ,oBAAL,CAA0BjJ,OAA1B,CAAkCyS,QAAlC,MAAgD,CAAC,CAAxE,EAA2E;UACvE,KAAKxJ,oBAAL,CAA0BnJ,IAA1B,CAA+B2S,QAA/B;;UACA,KAAK3P,KAAL,CAAWlK,SAAX,CAAqBC,GAArB,CAAyB4Z,QAAzB;QACH;MACJ,CALD;IAMH;EACJ;EACD;;;EACA7I,kBAAkB,GAAG;IACjB,IAAI,KAAK9G,KAAT,EAAgB;MACZ,KAAKmG,oBAAL,CAA0BsJ,OAA1B,CAAkCE,QAAQ,IAAI;QAC1C,KAAK3P,KAAL,CAAWlK,SAAX,CAAqBU,MAArB,CAA4BmZ,QAA5B;MACH,CAFD;;MAGA,KAAKxJ,oBAAL,GAA4B,EAA5B;IACH;EACJ;EACD;;;EACAiB,cAAc,GAAG;IACb,MAAMvL,MAAM,GAAG,KAAKoO,OAApB;;IACA,IAAIpO,MAAM,YAAY9I,UAAtB,EAAkC;MAC9B,OAAO8I,MAAM,CAACkT,aAAP,CAAqB7U,qBAArB,EAAP;IACH,CAJY,CAKb;;;IACA,IAAI2B,MAAM,YAAY+T,OAAtB,EAA+B;MAC3B,OAAO/T,MAAM,CAAC3B,qBAAP,EAAP;IACH;;IACD,MAAMjD,KAAK,GAAG4E,MAAM,CAAC5E,KAAP,IAAgB,CAA9B;IACA,MAAMF,MAAM,GAAG8E,MAAM,CAAC9E,MAAP,IAAiB,CAAhC,CAVa,CAWb;;IACA,OAAO;MACH7B,GAAG,EAAE2G,MAAM,CAAC+O,CADT;MAEH3R,MAAM,EAAE4C,MAAM,CAAC+O,CAAP,GAAW7T,MAFhB;MAGH5B,IAAI,EAAE0G,MAAM,CAAC2O,CAHV;MAIHpR,KAAK,EAAEyC,MAAM,CAAC2O,CAAP,GAAWvT,KAJf;MAKHF,MALG;MAMHE;IANG,CAAP;EAQH;;AA74BmC;AA+4BxC;;;AACA,SAAS+R,YAAT,CAAsB6G,WAAtB,EAAmCC,MAAnC,EAA2C;EACvC,KAAK,IAAInU,GAAT,IAAgBmU,MAAhB,EAAwB;IACpB,IAAIA,MAAM,CAACC,cAAP,CAAsBpU,GAAtB,CAAJ,EAAgC;MAC5BkU,WAAW,CAAClU,GAAD,CAAX,GAAmBmU,MAAM,CAACnU,GAAD,CAAzB;IACH;EACJ;;EACD,OAAOkU,WAAP;AACH;AACD;AACA;AACA;AACA;;;AACA,SAAS/D,aAAT,CAAuBkE,KAAvB,EAA8B;EAC1B,IAAI,OAAOA,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,IAAI,IAA1C,EAAgD;IAC5C,MAAM,CAACrT,KAAD,EAAQsT,KAAR,IAAiBD,KAAK,CAACE,KAAN,CAAYhL,cAAZ,CAAvB;IACA,OAAO,CAAC+K,KAAD,IAAUA,KAAK,KAAK,IAApB,GAA2BE,UAAU,CAACxT,KAAD,CAArC,GAA+C,IAAtD;EACH;;EACD,OAAOqT,KAAK,IAAI,IAAhB;AACH;AACD;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAS/E,4BAAT,CAAsCmF,UAAtC,EAAkD;EAC9C,OAAO;IACHlb,GAAG,EAAEoD,IAAI,CAAC+X,KAAL,CAAWD,UAAU,CAAClb,GAAtB,CADF;IAEHkE,KAAK,EAAEd,IAAI,CAAC+X,KAAL,CAAWD,UAAU,CAAChX,KAAtB,CAFJ;IAGHH,MAAM,EAAEX,IAAI,CAAC+X,KAAL,CAAWD,UAAU,CAACnX,MAAtB,CAHL;IAIH9D,IAAI,EAAEmD,IAAI,CAAC+X,KAAL,CAAWD,UAAU,CAACjb,IAAtB,CAJH;IAKH8B,KAAK,EAAEqB,IAAI,CAAC+X,KAAL,CAAWD,UAAU,CAACnZ,KAAtB,CALJ;IAMHF,MAAM,EAAEuB,IAAI,CAAC+X,KAAL,CAAWD,UAAU,CAACrZ,MAAtB;EANL,CAAP;AAQH;;AACD,MAAMuZ,iCAAiC,GAAG,CACtC;EAAErU,OAAO,EAAE,OAAX;EAAoBC,OAAO,EAAE,QAA7B;EAAuCC,QAAQ,EAAE,OAAjD;EAA0DC,QAAQ,EAAE;AAApE,CADsC,EAEtC;EAAEH,OAAO,EAAE,OAAX;EAAoBC,OAAO,EAAE,KAA7B;EAAoCC,QAAQ,EAAE,OAA9C;EAAuDC,QAAQ,EAAE;AAAjE,CAFsC,EAGtC;EAAEH,OAAO,EAAE,KAAX;EAAkBC,OAAO,EAAE,QAA3B;EAAqCC,QAAQ,EAAE,KAA/C;EAAsDC,QAAQ,EAAE;AAAhE,CAHsC,EAItC;EAAEH,OAAO,EAAE,KAAX;EAAkBC,OAAO,EAAE,KAA3B;EAAkCC,QAAQ,EAAE,KAA5C;EAAmDC,QAAQ,EAAE;AAA7D,CAJsC,CAA1C;AAMA,MAAMmU,oCAAoC,GAAG,CACzC;EAAEtU,OAAO,EAAE,KAAX;EAAkBC,OAAO,EAAE,KAA3B;EAAkCC,QAAQ,EAAE,OAA5C;EAAqDC,QAAQ,EAAE;AAA/D,CADyC,EAEzC;EAAEH,OAAO,EAAE,KAAX;EAAkBC,OAAO,EAAE,QAA3B;EAAqCC,QAAQ,EAAE,OAA/C;EAAwDC,QAAQ,EAAE;AAAlE,CAFyC,EAGzC;EAAEH,OAAO,EAAE,OAAX;EAAoBC,OAAO,EAAE,KAA7B;EAAoCC,QAAQ,EAAE,KAA9C;EAAqDC,QAAQ,EAAE;AAA/D,CAHyC,EAIzC;EAAEH,OAAO,EAAE,OAAX;EAAoBC,OAAO,EAAE,QAA7B;EAAuCC,QAAQ,EAAE,KAAjD;EAAwDC,QAAQ,EAAE;AAAlE,CAJyC,CAA7C;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;AACA,MAAMoU,YAAY,GAAG,4BAArB;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,sBAAN,CAA6B;EACzB3b,WAAW,GAAG;IACV,KAAK4b,YAAL,GAAoB,QAApB;IACA,KAAKC,UAAL,GAAkB,EAAlB;IACA,KAAKC,aAAL,GAAqB,EAArB;IACA,KAAKC,WAAL,GAAmB,EAAnB;IACA,KAAKC,UAAL,GAAkB,EAAlB;IACA,KAAKC,QAAL,GAAgB,EAAhB;IACA,KAAKC,MAAL,GAAc,EAAd;IACA,KAAKC,OAAL,GAAe,EAAf;IACA,KAAKxK,WAAL,GAAmB,KAAnB;EACH;;EACDnR,MAAM,CAACwC,UAAD,EAAa;IACf,MAAMyC,MAAM,GAAGzC,UAAU,CAAC+K,SAAX,EAAf;IACA,KAAKnL,WAAL,GAAmBI,UAAnB;;IACA,IAAI,KAAKkZ,MAAL,IAAe,CAACzW,MAAM,CAACtD,KAA3B,EAAkC;MAC9Ba,UAAU,CAACmL,UAAX,CAAsB;QAAEhM,KAAK,EAAE,KAAK+Z;MAAd,CAAtB;IACH;;IACD,IAAI,KAAKC,OAAL,IAAgB,CAAC1W,MAAM,CAACxD,MAA5B,EAAoC;MAChCe,UAAU,CAACmL,UAAX,CAAsB;QAAElM,MAAM,EAAE,KAAKka;MAAf,CAAtB;IACH;;IACDnZ,UAAU,CAACoJ,WAAX,CAAuBpL,SAAvB,CAAiCC,GAAjC,CAAqCya,YAArC;IACA,KAAK/J,WAAL,GAAmB,KAAnB;EACH;EACD;AACJ;AACA;AACA;;;EACIvR,GAAG,CAACyH,KAAK,GAAG,EAAT,EAAa;IACZ,KAAKiU,aAAL,GAAqB,EAArB;IACA,KAAKD,UAAL,GAAkBhU,KAAlB;IACA,KAAKkU,WAAL,GAAmB,YAAnB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACI1b,IAAI,CAACwH,KAAK,GAAG,EAAT,EAAa;IACb,KAAKoU,QAAL,GAAgBpU,KAAhB;IACA,KAAKmU,UAAL,GAAkB,MAAlB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACI7X,MAAM,CAAC0D,KAAK,GAAG,EAAT,EAAa;IACf,KAAKgU,UAAL,GAAkB,EAAlB;IACA,KAAKC,aAAL,GAAqBjU,KAArB;IACA,KAAKkU,WAAL,GAAmB,UAAnB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIzX,KAAK,CAACuD,KAAK,GAAG,EAAT,EAAa;IACd,KAAKoU,QAAL,GAAgBpU,KAAhB;IACA,KAAKmU,UAAL,GAAkB,OAAlB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACI5E,KAAK,CAACvP,KAAK,GAAG,EAAT,EAAa;IACd,KAAKoU,QAAL,GAAgBpU,KAAhB;IACA,KAAKmU,UAAL,GAAkB,OAAlB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACII,GAAG,CAACvU,KAAK,GAAG,EAAT,EAAa;IACZ,KAAKoU,QAAL,GAAgBpU,KAAhB;IACA,KAAKmU,UAAL,GAAkB,KAAlB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACI7Z,KAAK,CAAC0F,KAAK,GAAG,EAAT,EAAa;IACd,IAAI,KAAKjF,WAAT,EAAsB;MAClB,KAAKA,WAAL,CAAiBuL,UAAjB,CAA4B;QAAEhM,KAAK,EAAE0F;MAAT,CAA5B;IACH,CAFD,MAGK;MACD,KAAKqU,MAAL,GAAcrU,KAAd;IACH;;IACD,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACI5F,MAAM,CAAC4F,KAAK,GAAG,EAAT,EAAa;IACf,IAAI,KAAKjF,WAAT,EAAsB;MAClB,KAAKA,WAAL,CAAiBuL,UAAjB,CAA4B;QAAElM,MAAM,EAAE4F;MAAV,CAA5B;IACH,CAFD,MAGK;MACD,KAAKsU,OAAL,GAAetU,KAAf;IACH;;IACD,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIwU,kBAAkB,CAAChH,MAAM,GAAG,EAAV,EAAc;IAC5B,KAAKhV,IAAL,CAAUgV,MAAV;IACA,KAAK2G,UAAL,GAAkB,QAAlB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIM,gBAAgB,CAACjH,MAAM,GAAG,EAAV,EAAc;IAC1B,KAAKjV,GAAL,CAASiV,MAAT;IACA,KAAK0G,WAAL,GAAmB,QAAnB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACI/N,KAAK,GAAG;IACJ;IACA;IACA;IACA,IAAI,CAAC,KAAKpL,WAAN,IAAqB,CAAC,KAAKA,WAAL,CAAiBC,WAAjB,EAA1B,EAA0D;MACtD;IACH;;IACD,MAAMgW,MAAM,GAAG,KAAKjW,WAAL,CAAiBuC,cAAjB,CAAgCpE,KAA/C;IACA,MAAMwb,YAAY,GAAG,KAAK3Z,WAAL,CAAiBwJ,WAAjB,CAA6BrL,KAAlD;;IACA,MAAM0E,MAAM,GAAG,KAAK7C,WAAL,CAAiBmL,SAAjB,EAAf;;IACA,MAAM;MAAE5L,KAAF;MAASF,MAAT;MAAiB8M,QAAjB;MAA2BC;IAA3B,IAAyCvJ,MAA/C;IACA,MAAM+W,yBAAyB,GAAG,CAACra,KAAK,KAAK,MAAV,IAAoBA,KAAK,KAAK,OAA/B,MAC7B,CAAC4M,QAAD,IAAaA,QAAQ,KAAK,MAA1B,IAAoCA,QAAQ,KAAK,OADpB,CAAlC;IAEA,MAAM0N,uBAAuB,GAAG,CAACxa,MAAM,KAAK,MAAX,IAAqBA,MAAM,KAAK,OAAjC,MAC3B,CAAC+M,SAAD,IAAcA,SAAS,KAAK,MAA5B,IAAsCA,SAAS,KAAK,OADzB,CAAhC;IAEA,MAAM0N,SAAS,GAAG,KAAKV,UAAvB;IACA,MAAMW,OAAO,GAAG,KAAKV,QAArB;IACA,MAAM3D,KAAK,GAAG,KAAK1V,WAAL,CAAiBmL,SAAjB,GAA6BQ,SAA7B,KAA2C,KAAzD;IACA,IAAIqO,UAAU,GAAG,EAAjB;IACA,IAAIC,WAAW,GAAG,EAAlB;IACA,IAAIzI,cAAc,GAAG,EAArB;;IACA,IAAIoI,yBAAJ,EAA+B;MAC3BpI,cAAc,GAAG,YAAjB;IACH,CAFD,MAGK,IAAIsI,SAAS,KAAK,QAAlB,EAA4B;MAC7BtI,cAAc,GAAG,QAAjB;;MACA,IAAIkE,KAAJ,EAAW;QACPuE,WAAW,GAAGF,OAAd;MACH,CAFD,MAGK;QACDC,UAAU,GAAGD,OAAb;MACH;IACJ,CARI,MASA,IAAIrE,KAAJ,EAAW;MACZ,IAAIoE,SAAS,KAAK,MAAd,IAAwBA,SAAS,KAAK,KAA1C,EAAiD;QAC7CtI,cAAc,GAAG,UAAjB;QACAwI,UAAU,GAAGD,OAAb;MACH,CAHD,MAIK,IAAID,SAAS,KAAK,OAAd,IAAyBA,SAAS,KAAK,OAA3C,EAAoD;QACrDtI,cAAc,GAAG,YAAjB;QACAyI,WAAW,GAAGF,OAAd;MACH;IACJ,CATI,MAUA,IAAID,SAAS,KAAK,MAAd,IAAwBA,SAAS,KAAK,OAA1C,EAAmD;MACpDtI,cAAc,GAAG,YAAjB;MACAwI,UAAU,GAAGD,OAAb;IACH,CAHI,MAIA,IAAID,SAAS,KAAK,OAAd,IAAyBA,SAAS,KAAK,KAA3C,EAAkD;MACnDtI,cAAc,GAAG,UAAjB;MACAyI,WAAW,GAAGF,OAAd;IACH;;IACD9D,MAAM,CAACrF,QAAP,GAAkB,KAAKoI,YAAvB;IACA/C,MAAM,CAAC+D,UAAP,GAAoBJ,yBAAyB,GAAG,GAAH,GAASI,UAAtD;IACA/D,MAAM,CAACiE,SAAP,GAAmBL,uBAAuB,GAAG,GAAH,GAAS,KAAKZ,UAAxD;IACAhD,MAAM,CAACkE,YAAP,GAAsB,KAAKjB,aAA3B;IACAjD,MAAM,CAACgE,WAAP,GAAqBL,yBAAyB,GAAG,GAAH,GAASK,WAAvD;IACAN,YAAY,CAACnI,cAAb,GAA8BA,cAA9B;IACAmI,YAAY,CAACpI,UAAb,GAA0BsI,uBAAuB,GAAG,YAAH,GAAkB,KAAKV,WAAxE;EACH;EACD;AACJ;AACA;AACA;;;EACI9O,OAAO,GAAG;IACN,IAAI,KAAK0E,WAAL,IAAoB,CAAC,KAAK/O,WAA9B,EAA2C;MACvC;IACH;;IACD,MAAMiW,MAAM,GAAG,KAAKjW,WAAL,CAAiBuC,cAAjB,CAAgCpE,KAA/C;IACA,MAAMic,MAAM,GAAG,KAAKpa,WAAL,CAAiBwJ,WAAhC;IACA,MAAMmQ,YAAY,GAAGS,MAAM,CAACjc,KAA5B;IACAic,MAAM,CAAChc,SAAP,CAAiBU,MAAjB,CAAwBga,YAAxB;IACAa,YAAY,CAACnI,cAAb,GACImI,YAAY,CAACpI,UAAb,GACI0E,MAAM,CAACiE,SAAP,GACIjE,MAAM,CAACkE,YAAP,GACIlE,MAAM,CAAC+D,UAAP,GACI/D,MAAM,CAACgE,WAAP,GACIhE,MAAM,CAACrF,QAAP,GACI,EAP5B;IAQA,KAAK5Q,WAAL,GAAmB,IAAnB;IACA,KAAK+O,WAAL,GAAmB,IAAnB;EACH;;AA3NwB;AA8N7B;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMsL,sBAAN,CAA6B;EACzBjd,WAAW,CAACC,cAAD,EAAiBM,SAAjB,EAA4B8I,SAA5B,EAAuCkH,iBAAvC,EAA0D;IACjE,KAAKtQ,cAAL,GAAsBA,cAAtB;IACA,KAAKM,SAAL,GAAiBA,SAAjB;IACA,KAAK8I,SAAL,GAAiBA,SAAjB;IACA,KAAKkH,iBAAL,GAAyBA,iBAAzB;EACH;EACD;AACJ;AACA;;;EACI2M,MAAM,GAAG;IACL,OAAO,IAAIvB,sBAAJ,EAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIwB,mBAAmB,CAACpW,MAAD,EAAS;IACxB,OAAO,IAAIsJ,iCAAJ,CAAsCtJ,MAAtC,EAA8C,KAAK9G,cAAnD,EAAmE,KAAKM,SAAxE,EAAmF,KAAK8I,SAAxF,EAAmG,KAAKkH,iBAAxG,CAAP;EACH;;AAnBwB;;AAqB7B0M,sBAAsB,CAACrX,IAAvB;EAAA,iBAAmHqX,sBAAnH,EA1mEwGpf,EA0mExG,UAA2JP,EAAE,CAACI,aAA9J,GA1mEwGG,EA0mExG,UAAwLD,QAAxL,GA1mEwGC,EA0mExG,UAA6MgB,IAAI,CAACqL,QAAlN,GA1mEwGrM,EA0mExG,UAAuOsM,gBAAvO;AAAA;;AACA8S,sBAAsB,CAACnX,KAAvB,kBA3mEwGjI,EA2mExG;EAAA,OAAuHof,sBAAvH;EAAA,SAAuHA,sBAAvH;EAAA,YAA2J;AAA3J;;AACA;EAAA,mDA5mEwGpf,EA4mExG,mBAA2Fof,sBAA3F,EAA+H,CAAC;IACpHlX,IAAI,EAAEjI,UAD8G;IAEpHkI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAF8G,CAAD,CAA/H,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAEzI,EAAE,CAACI;IAAX,CAAD,EAA6B;MAAEqI,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC1FJ,IAAI,EAAEhI,MADoF;QAE1FiI,IAAI,EAAE,CAACpI,QAAD;MAFoF,CAAD;IAA/B,CAA7B,EAG3B;MAAEmI,IAAI,EAAElH,IAAI,CAACqL;IAAb,CAH2B,EAGF;MAAEnE,IAAI,EAAEoE;IAAR,CAHE,CAAP;EAGmC,CAN7E;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,IAAIiT,YAAY,GAAG,CAAnB,C,CACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,OAAN,CAAc;EACVrd,WAAW;EACX;EACAsd,gBAFW,EAEO/M,iBAFP,EAE0BgN,yBAF1B,EAEqDC,gBAFrD,EAEuErS,mBAFvE,EAE4FsS,SAF5F,EAEuGjb,OAFvG,EAEgHjC,SAFhH,EAE2Hmd,eAF3H,EAE4ItS,SAF5I,EAEuJC,uBAFvJ,EAEgLsS,qBAFhL,EAEuM;IAC9M,KAAKL,gBAAL,GAAwBA,gBAAxB;IACA,KAAK/M,iBAAL,GAAyBA,iBAAzB;IACA,KAAKgN,yBAAL,GAAiCA,yBAAjC;IACA,KAAKC,gBAAL,GAAwBA,gBAAxB;IACA,KAAKrS,mBAAL,GAA2BA,mBAA3B;IACA,KAAKsS,SAAL,GAAiBA,SAAjB;IACA,KAAKjb,OAAL,GAAeA,OAAf;IACA,KAAKjC,SAAL,GAAiBA,SAAjB;IACA,KAAKmd,eAAL,GAAuBA,eAAvB;IACA,KAAKtS,SAAL,GAAiBA,SAAjB;IACA,KAAKC,uBAAL,GAA+BA,uBAA/B;IACA,KAAKsS,qBAAL,GAA6BA,qBAA7B;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIC,MAAM,CAACnY,MAAD,EAAS;IACX,MAAMoY,IAAI,GAAG,KAAKC,kBAAL,EAAb;;IACA,MAAMC,IAAI,GAAG,KAAKC,kBAAL,CAAwBH,IAAxB,CAAb;;IACA,MAAMI,YAAY,GAAG,KAAKC,mBAAL,CAAyBH,IAAzB,CAArB;;IACA,MAAMI,aAAa,GAAG,IAAI/X,aAAJ,CAAkBX,MAAlB,CAAtB;IACA0Y,aAAa,CAAC5P,SAAd,GAA0B4P,aAAa,CAAC5P,SAAd,IAA2B,KAAKmP,eAAL,CAAqB7V,KAA1E;IACA,OAAO,IAAIkD,UAAJ,CAAekT,YAAf,EAA6BJ,IAA7B,EAAmCE,IAAnC,EAAyCI,aAAzC,EAAwD,KAAK3b,OAA7D,EAAsE,KAAK2I,mBAA3E,EAAgG,KAAK5K,SAArG,EAAgH,KAAK6K,SAArH,EAAgI,KAAKC,uBAArI,EAA8J,KAAKsS,qBAAL,KAA+B,gBAA7L,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACInK,QAAQ,GAAG;IACP,OAAO,KAAKgK,gBAAZ;EACH;EACD;AACJ;AACA;AACA;;;EACIQ,kBAAkB,CAACH,IAAD,EAAO;IACrB,MAAME,IAAI,GAAG,KAAKxd,SAAL,CAAeqK,aAAf,CAA6B,KAA7B,CAAb;;IACAmT,IAAI,CAACK,EAAL,GAAW,eAAchB,YAAY,EAAG,EAAxC;IACAW,IAAI,CAAC/c,SAAL,CAAeC,GAAf,CAAmB,kBAAnB;IACA4c,IAAI,CAAC/S,WAAL,CAAiBiT,IAAjB;IACA,OAAOA,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACID,kBAAkB,GAAG;IACjB,MAAMD,IAAI,GAAG,KAAKtd,SAAL,CAAeqK,aAAf,CAA6B,KAA7B,CAAb;;IACA,KAAK2F,iBAAL,CAAuBlG,mBAAvB,GAA6CS,WAA7C,CAAyD+S,IAAzD;;IACA,OAAOA,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIK,mBAAmB,CAACH,IAAD,EAAO;IACtB;IACA;IACA,IAAI,CAAC,KAAKM,OAAV,EAAmB;MACf,KAAKA,OAAL,GAAe,KAAKZ,SAAL,CAAea,GAAf,CAAmBpgB,cAAnB,CAAf;IACH;;IACD,OAAO,IAAIiB,eAAJ,CAAoB4e,IAApB,EAA0B,KAAKR,yBAA/B,EAA0D,KAAKc,OAA/D,EAAwE,KAAKZ,SAA7E,EAAwF,KAAKld,SAA7F,CAAP;EACH;;AAvES;;AAyEd8c,OAAO,CAACzX,IAAR;EAAA,iBAAoGyX,OAApG,EAhtEwGxf,EAgtExG,UAA6HyH,qBAA7H,GAhtEwGzH,EAgtExG,UAA+JsM,gBAA/J,GAhtEwGtM,EAgtExG,UAA4LA,EAAE,CAAC0gB,wBAA/L,GAhtEwG1gB,EAgtExG,UAAoOof,sBAApO,GAhtEwGpf,EAgtExG,UAAuQ0K,yBAAvQ,GAhtEwG1K,EAgtExG,UAA6SA,EAAE,CAAC2gB,QAAhT,GAhtEwG3gB,EAgtExG,UAAqUA,EAAE,CAACgI,MAAxU,GAhtEwGhI,EAgtExG,UAA2VD,QAA3V,GAhtEwGC,EAgtExG,UAAgXoB,EAAE,CAACwf,cAAnX,GAhtEwG5gB,EAgtExG,UAA8YF,EAAE,CAAC+gB,QAAjZ,GAhtEwG7gB,EAgtExG,UAAsauL,6BAAta,GAhtEwGvL,EAgtExG,UAAgdM,qBAAhd;AAAA;;AACAkf,OAAO,CAACvX,KAAR,kBAjtEwGjI,EAitExG;EAAA,OAAwGwf,OAAxG;EAAA,SAAwGA,OAAxG;AAAA;;AACA;EAAA,mDAltEwGxf,EAktExG,mBAA2Fwf,OAA3F,EAAgH,CAAC;IACrGtX,IAAI,EAAEjI;EAD+F,CAAD,CAAhH,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAEiI,IAAI,EAAET;IAAR,CAAD,EAAkC;MAAES,IAAI,EAAEoE;IAAR,CAAlC,EAA8D;MAAEpE,IAAI,EAAElI,EAAE,CAAC0gB;IAAX,CAA9D,EAAqG;MAAExY,IAAI,EAAEkX;IAAR,CAArG,EAAuI;MAAElX,IAAI,EAAEwC;IAAR,CAAvI,EAA4K;MAAExC,IAAI,EAAElI,EAAE,CAAC2gB;IAAX,CAA5K,EAAmM;MAAEzY,IAAI,EAAElI,EAAE,CAACgI;IAAX,CAAnM,EAAwN;MAAEE,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACrRJ,IAAI,EAAEhI,MAD+Q;QAErRiI,IAAI,EAAE,CAACpI,QAAD;MAF+Q,CAAD;IAA/B,CAAxN,EAG3B;MAAEmI,IAAI,EAAE9G,EAAE,CAACwf;IAAX,CAH2B,EAGE;MAAE1Y,IAAI,EAAEpI,EAAE,CAAC+gB;IAAX,CAHF,EAGyB;MAAE3Y,IAAI,EAAEqD;IAAR,CAHzB,EAGkE;MAAErD,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC/HJ,IAAI,EAAEhI,MADyH;QAE/HiI,IAAI,EAAE,CAAC7H,qBAAD;MAFyH,CAAD,EAG/H;QACC4H,IAAI,EAAE/H;MADP,CAH+H;IAA/B,CAHlE,CAAP;EAQlB,CAVxB;AAAA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAM2gB,mBAAmB,GAAG,CACxB;EACIxX,OAAO,EAAE,OADb;EAEIC,OAAO,EAAE,QAFb;EAGIC,QAAQ,EAAE,OAHd;EAIIC,QAAQ,EAAE;AAJd,CADwB,EAOxB;EACIH,OAAO,EAAE,OADb;EAEIC,OAAO,EAAE,KAFb;EAGIC,QAAQ,EAAE,OAHd;EAIIC,QAAQ,EAAE;AAJd,CAPwB,EAaxB;EACIH,OAAO,EAAE,KADb;EAEIC,OAAO,EAAE,KAFb;EAGIC,QAAQ,EAAE,KAHd;EAIIC,QAAQ,EAAE;AAJd,CAbwB,EAmBxB;EACIH,OAAO,EAAE,KADb;EAEIC,OAAO,EAAE,QAFb;EAGIC,QAAQ,EAAE,KAHd;EAIIC,QAAQ,EAAE;AAJd,CAnBwB,CAA5B;AA0BA;;AACA,MAAMsX,qCAAqC,GAAG,IAAIxgB,cAAJ,CAAmB,uCAAnB,CAA9C;AACA;AACA;AACA;AACA;;AACA,MAAMygB,gBAAN,CAAuB;EACnB7e,WAAW;EACX;EACA8e,UAFW,EAEC;IACR,KAAKA,UAAL,GAAkBA,UAAlB;EACH;;AALkB;;AAOvBD,gBAAgB,CAACjZ,IAAjB;EAAA,iBAA6GiZ,gBAA7G,EA7wEwGhhB,EA6wExG,mBAA+IA,EAAE,CAACI,UAAlJ;AAAA;;AACA4gB,gBAAgB,CAACE,IAAjB,kBA9wEwGlhB,EA8wExG;EAAA,MAAiGghB,gBAAjG;EAAA;EAAA;AAAA;;AACA;EAAA,mDA/wEwGhhB,EA+wExG,mBAA2FghB,gBAA3F,EAAyH,CAAC;IAC9G9Y,IAAI,EAAE1H,SADwG;IAE9G2H,IAAI,EAAE,CAAC;MACCwP,QAAQ,EAAE,4DADX;MAECwJ,QAAQ,EAAE;IAFX,CAAD;EAFwG,CAAD,CAAzH,EAM4B,YAAY;IAAE,OAAO,CAAC;MAAEjZ,IAAI,EAAElI,EAAE,CAACI;IAAX,CAAD,CAAP;EAAmC,CAN7E;AAAA;AAOA;AACA;AACA;AACA;;;AACA,MAAMghB,mBAAN,CAA0B;EACtB;EACAjf,WAAW,CAACkf,QAAD,EAAWC,WAAX,EAAwBC,gBAAxB,EAA0CC,qBAA1C,EAAiEC,IAAjE,EAAuE;IAC9E,KAAKJ,QAAL,GAAgBA,QAAhB;IACA,KAAKI,IAAL,GAAYA,IAAZ;IACA,KAAKC,YAAL,GAAoB,KAApB;IACA,KAAKC,aAAL,GAAqB,KAArB;IACA,KAAK7O,cAAL,GAAsB,KAAtB;IACA,KAAK8O,mBAAL,GAA2B,KAA3B;IACA,KAAKC,KAAL,GAAa,KAAb;IACA,KAAKC,qBAAL,GAA6BpgB,YAAY,CAACqM,KAA1C;IACA,KAAKgU,mBAAL,GAA2BrgB,YAAY,CAACqM,KAAxC;IACA,KAAKiU,mBAAL,GAA2BtgB,YAAY,CAACqM,KAAxC;IACA,KAAKkU,qBAAL,GAA6BvgB,YAAY,CAACqM,KAA1C;IACA;;IACA,KAAKmU,cAAL,GAAsB,CAAtB;IACA;;IACA,KAAKC,IAAL,GAAY,KAAZ;IACA;;IACA,KAAKC,YAAL,GAAoB,KAApB;IACA;;IACA,KAAKrS,aAAL,GAAqB,IAAItP,YAAJ,EAArB;IACA;;IACA,KAAK4hB,cAAL,GAAsB,IAAI5hB,YAAJ,EAAtB;IACA;;IACA,KAAKkC,MAAL,GAAc,IAAIlC,YAAJ,EAAd;IACA;;IACA,KAAKyE,MAAL,GAAc,IAAIzE,YAAJ,EAAd;IACA;;IACA,KAAK6hB,cAAL,GAAsB,IAAI7hB,YAAJ,EAAtB;IACA;;IACA,KAAK8hB,mBAAL,GAA2B,IAAI9hB,YAAJ,EAA3B;IACA,KAAK+hB,eAAL,GAAuB,IAAIjhB,cAAJ,CAAmB+f,WAAnB,EAAgCC,gBAAhC,CAAvB;IACA,KAAKkB,sBAAL,GAA8BjB,qBAA9B;IACA,KAAKhZ,cAAL,GAAsB,KAAKia,sBAAL,EAAtB;EACH;EACD;;;EACW,IAAPrZ,OAAO,GAAG;IACV,OAAO,KAAKkK,QAAZ;EACH;;EACU,IAAPlK,OAAO,CAACA,OAAD,EAAU;IACjB,KAAKkK,QAAL,GAAgBlK,OAAhB;;IACA,IAAI,KAAKsZ,SAAT,EAAoB;MAChB,KAAKC,uBAAL,CAA6B,KAAKD,SAAlC;IACH;EACJ;EACD;;;EACW,IAAPrZ,OAAO,GAAG;IACV,OAAO,KAAKkK,QAAZ;EACH;;EACU,IAAPlK,OAAO,CAACA,OAAD,EAAU;IACjB,KAAKkK,QAAL,GAAgBlK,OAAhB;;IACA,IAAI,KAAKqZ,SAAT,EAAoB;MAChB,KAAKC,uBAAL,CAA6B,KAAKD,SAAlC;IACH;EACJ;EACD;;;EACe,IAAXha,WAAW,GAAG;IACd,OAAO,KAAKgZ,YAAZ;EACH;;EACc,IAAXhZ,WAAW,CAACsB,KAAD,EAAQ;IACnB,KAAK0X,YAAL,GAAoB3gB,qBAAqB,CAACiJ,KAAD,CAAzC;EACH;EACD;;;EACgB,IAAZ4Y,YAAY,GAAG;IACf,OAAO,KAAKjB,aAAZ;EACH;;EACe,IAAZiB,YAAY,CAAC5Y,KAAD,EAAQ;IACpB,KAAK2X,aAAL,GAAqB5gB,qBAAqB,CAACiJ,KAAD,CAA1C;EACH;EACD;;;EACsB,IAAlB+M,kBAAkB,GAAG;IACrB,OAAO,KAAK6K,mBAAZ;EACH;;EACqB,IAAlB7K,kBAAkB,CAAC/M,KAAD,EAAQ;IAC1B,KAAK4X,mBAAL,GAA2B7gB,qBAAqB,CAACiJ,KAAD,CAAhD;EACH;EACD;;;EACiB,IAAbiN,aAAa,GAAG;IAChB,OAAO,KAAKnE,cAAZ;EACH;;EACgB,IAAbmE,aAAa,CAACjN,KAAD,EAAQ;IACrB,KAAK8I,cAAL,GAAsB/R,qBAAqB,CAACiJ,KAAD,CAA3C;EACH;EACD;;;EACQ,IAAJK,IAAI,GAAG;IACP,OAAO,KAAKwX,KAAZ;EACH;;EACO,IAAJxX,IAAI,CAACL,KAAD,EAAQ;IACZ,KAAK6X,KAAL,GAAa9gB,qBAAqB,CAACiJ,KAAD,CAAlC;EACH;EACD;;;EACc,IAAV7E,UAAU,GAAG;IACb,OAAO,KAAKJ,WAAZ;EACH;EACD;;;EACO,IAAH0L,GAAG,GAAG;IACN,OAAO,KAAKgR,IAAL,GAAY,KAAKA,IAAL,CAAUzX,KAAtB,GAA8B,KAArC;EACH;;EACDI,WAAW,GAAG;IACV,KAAK2X,mBAAL,CAAyBjc,WAAzB;;IACA,KAAKkc,mBAAL,CAAyBlc,WAAzB;;IACA,KAAKgc,qBAAL,CAA2Bhc,WAA3B;;IACA,KAAKmc,qBAAL,CAA2Bnc,WAA3B;;IACA,IAAI,KAAKf,WAAT,EAAsB;MAClB,KAAKA,WAAL,CAAiBqK,OAAjB;IACH;EACJ;;EACDyT,WAAW,CAACC,OAAD,EAAU;IACjB,IAAI,KAAKJ,SAAT,EAAoB;MAChB,KAAKC,uBAAL,CAA6B,KAAKD,SAAlC;;MACA,KAAK3d,WAAL,CAAiBuL,UAAjB,CAA4B;QACxBhM,KAAK,EAAE,KAAKA,KADY;QAExB0M,QAAQ,EAAE,KAAKA,QAFS;QAGxB5M,MAAM,EAAE,KAAKA,MAHW;QAIxB6M,SAAS,EAAE,KAAKA;MAJQ,CAA5B;;MAMA,IAAI6R,OAAO,CAAC,QAAD,CAAP,IAAqB,KAAKX,IAA9B,EAAoC;QAChC,KAAKO,SAAL,CAAevS,KAAf;MACH;IACJ;;IACD,IAAI2S,OAAO,CAAC,MAAD,CAAX,EAAqB;MACjB,KAAKX,IAAL,GAAY,KAAKY,cAAL,EAAZ,GAAoC,KAAKC,cAAL,EAApC;IACH;EACJ;EACD;;;EACAC,cAAc,GAAG;IACb,IAAI,CAAC,KAAKtP,SAAN,IAAmB,CAAC,KAAKA,SAAL,CAAelJ,MAAvC,EAA+C;MAC3C,KAAKkJ,SAAL,GAAiBmN,mBAAjB;IACH;;IACD,MAAM3b,UAAU,GAAI,KAAKJ,WAAL,GAAmB,KAAKsc,QAAL,CAActB,MAAd,CAAqB,KAAKmD,YAAL,EAArB,CAAvC;;IACA,KAAKnB,mBAAL,GAA2B5c,UAAU,CAAC6K,WAAX,GAAyBvK,SAAzB,CAAmC,MAAM,KAAK9C,MAAL,CAAYwgB,IAAZ,EAAzC,CAA3B;IACA,KAAKnB,mBAAL,GAA2B7c,UAAU,CAAC8K,WAAX,GAAyBxK,SAAzB,CAAmC,MAAM,KAAKP,MAAL,CAAYie,IAAZ,EAAzC,CAA3B;IACAhe,UAAU,CAAC8F,aAAX,GAA2BxF,SAA3B,CAAsCmF,KAAD,IAAW;MAC5C,KAAK0X,cAAL,CAAoBpX,IAApB,CAAyBN,KAAzB;;MACA,IAAIA,KAAK,CAACwY,OAAN,KAAkBrhB,MAAlB,IAA4B,CAAC,KAAKqgB,YAAlC,IAAkD,CAACpgB,cAAc,CAAC4I,KAAD,CAArE,EAA8E;QAC1EA,KAAK,CAACyY,cAAN;;QACA,KAAKL,cAAL;MACH;IACJ,CAND;;IAOA,KAAKje,WAAL,CAAiBiH,oBAAjB,GAAwCvG,SAAxC,CAAmDmF,KAAD,IAAW;MACzD,KAAK2X,mBAAL,CAAyBrX,IAAzB,CAA8BN,KAA9B;IACH,CAFD;EAGH;EACD;;;EACAsY,YAAY,GAAG;IACX,MAAM7U,gBAAgB,GAAI,KAAKqU,SAAL,GACtB,KAAKrU,gBAAL,IAAyB,KAAKiV,uBAAL,EAD7B;;IAEA,MAAMhD,aAAa,GAAG,IAAI/X,aAAJ,CAAkB;MACpCmI,SAAS,EAAE,KAAK+Q,IADoB;MAEpCpT,gBAFoC;MAGpC7F,cAAc,EAAE,KAAKA,cAHe;MAIpCE,WAAW,EAAE,KAAKA;IAJkB,CAAlB,CAAtB;;IAMA,IAAI,KAAKpE,KAAL,IAAc,KAAKA,KAAL,KAAe,CAAjC,EAAoC;MAChCgc,aAAa,CAAChc,KAAd,GAAsB,KAAKA,KAA3B;IACH;;IACD,IAAI,KAAKF,MAAL,IAAe,KAAKA,MAAL,KAAgB,CAAnC,EAAsC;MAClCkc,aAAa,CAAClc,MAAd,GAAuB,KAAKA,MAA5B;IACH;;IACD,IAAI,KAAK4M,QAAL,IAAiB,KAAKA,QAAL,KAAkB,CAAvC,EAA0C;MACtCsP,aAAa,CAACtP,QAAd,GAAyB,KAAKA,QAA9B;IACH;;IACD,IAAI,KAAKC,SAAL,IAAkB,KAAKA,SAAL,KAAmB,CAAzC,EAA4C;MACxCqP,aAAa,CAACrP,SAAd,GAA0B,KAAKA,SAA/B;IACH;;IACD,IAAI,KAAKtI,aAAT,EAAwB;MACpB2X,aAAa,CAAC3X,aAAd,GAA8B,KAAKA,aAAnC;IACH;;IACD,IAAI,KAAKF,UAAT,EAAqB;MACjB6X,aAAa,CAAC7X,UAAd,GAA2B,KAAKA,UAAhC;IACH;;IACD,OAAO6X,aAAP;EACH;EACD;;;EACAqC,uBAAuB,CAACtU,gBAAD,EAAmB;IACtC,MAAMsF,SAAS,GAAG,KAAKA,SAAL,CAAesI,GAAf,CAAmBsH,eAAe,KAAK;MACrDja,OAAO,EAAEia,eAAe,CAACja,OAD4B;MAErDC,OAAO,EAAEga,eAAe,CAACha,OAF4B;MAGrDC,QAAQ,EAAE+Z,eAAe,CAAC/Z,QAH2B;MAIrDC,QAAQ,EAAE8Z,eAAe,CAAC9Z,QAJ2B;MAKrDL,OAAO,EAAEma,eAAe,CAACna,OAAhB,IAA2B,KAAKA,OALY;MAMrDC,OAAO,EAAEka,eAAe,CAACla,OAAhB,IAA2B,KAAKA,OANY;MAOrDZ,UAAU,EAAE8a,eAAe,CAAC9a,UAAhB,IAA8BJ;IAPW,CAAL,CAAlC,CAAlB;IASA,OAAOgG,gBAAgB,CAClBqF,SADE,CACQ,KAAK8P,2CAAL,EADR,EAEF7M,aAFE,CAEYhD,SAFZ,EAGFmD,sBAHE,CAGqB,KAAKC,kBAH1B,EAIFG,QAJE,CAIO,KAAK7M,IAJZ,EAKF2M,iBALE,CAKgB,KAAKC,aALrB,EAMFL,kBANE,CAMiB,KAAKsL,cANtB,EAOF9K,kBAPE,CAOiB,KAAKwL,YAPtB,EAQFlL,qBARE,CAQoB,KAAK+L,uBARzB,CAAP;EASH;EACD;;;EACAH,uBAAuB,GAAG;IACtB,MAAMjT,QAAQ,GAAG,KAAKgR,QAAL,CACZ1L,QADY,GAEZ2J,mBAFY,CAEQ,KAAKkE,2CAAL,EAFR,CAAjB;;IAGA,KAAKb,uBAAL,CAA6BtS,QAA7B;;IACA,OAAOA,QAAP;EACH;;EACDmT,2CAA2C,GAAG;IAC1C,IAAI,KAAKta,MAAL,YAAuB8X,gBAA3B,EAA6C;MACzC,OAAO,KAAK9X,MAAL,CAAY+X,UAAnB;IACH,CAFD,MAGK;MACD,OAAO,KAAK/X,MAAZ;IACH;EACJ;EACD;;;EACA6Z,cAAc,GAAG;IACb,IAAI,CAAC,KAAKhe,WAAV,EAAuB;MACnB,KAAKke,cAAL;IACH,CAFD,MAGK;MACD;MACA,KAAKle,WAAL,CAAiBmL,SAAjB,GAA6BxH,WAA7B,GAA2C,KAAKA,WAAhD;IACH;;IACD,IAAI,CAAC,KAAK3D,WAAL,CAAiBC,WAAjB,EAAL,EAAqC;MACjC,KAAKD,WAAL,CAAiBpC,MAAjB,CAAwB,KAAK6f,eAA7B;IACH;;IACD,IAAI,KAAK9Z,WAAT,EAAsB;MAClB,KAAKoZ,qBAAL,GAA6B,KAAK/c,WAAL,CAAiBgL,aAAjB,GAAiCtK,SAAjC,CAA2CmF,KAAK,IAAI;QAC7E,KAAKmF,aAAL,CAAmBoT,IAAnB,CAAwBvY,KAAxB;MACH,CAF4B,CAA7B;IAGH,CAJD,MAKK;MACD,KAAKkX,qBAAL,CAA2Bhc,WAA3B;IACH;;IACD,KAAKmc,qBAAL,CAA2Bnc,WAA3B,GAnBa,CAoBb;IACA;;;IACA,IAAI,KAAKuc,cAAL,CAAoBrX,SAApB,CAA8BP,MAA9B,GAAuC,CAA3C,EAA8C;MAC1C,KAAKwX,qBAAL,GAA6B,KAAKS,SAAL,CAAejP,eAAf,CACxBzE,IADwB,CACnBlN,SAAS,CAAC,MAAM,KAAKugB,cAAL,CAAoBrX,SAApB,CAA8BP,MAA9B,GAAuC,CAA9C,CADU,EAExBhF,SAFwB,CAEdkQ,QAAQ,IAAI;QACvB,KAAK0M,cAAL,CAAoBc,IAApB,CAAyBxN,QAAzB;;QACA,IAAI,KAAK0M,cAAL,CAAoBrX,SAApB,CAA8BP,MAA9B,KAAyC,CAA7C,EAAgD;UAC5C,KAAKwX,qBAAL,CAA2Bnc,WAA3B;QACH;MACJ,CAP4B,CAA7B;IAQH;EACJ;EACD;;;EACAkd,cAAc,GAAG;IACb,IAAI,KAAKje,WAAT,EAAsB;MAClB,KAAKA,WAAL,CAAiBG,MAAjB;IACH;;IACD,KAAK4c,qBAAL,CAA2Bhc,WAA3B;;IACA,KAAKmc,qBAAL,CAA2Bnc,WAA3B;EACH;;AA5PqB;;AA8P1Bsb,mBAAmB,CAACrZ,IAApB;EAAA,iBAAgHqZ,mBAAhH,EAxhFwGphB,EAwhFxG,mBAAqJwf,OAArJ,GAxhFwGxf,EAwhFxG,mBAAyKA,EAAE,CAAC0jB,WAA5K,GAxhFwG1jB,EAwhFxG,mBAAoMA,EAAE,CAAC2jB,gBAAvM,GAxhFwG3jB,EAwhFxG,mBAAoO+gB,qCAApO,GAxhFwG/gB,EAwhFxG,mBAAsRoB,EAAE,CAACwf,cAAzR;AAAA;;AACAQ,mBAAmB,CAACF,IAApB,kBAzhFwGlhB,EAyhFxG;EAAA,MAAoGohB,mBAApG;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAzhFwGphB,EAyhFxG;AAAA;;AACA;EAAA,mDA1hFwGA,EA0hFxG,mBAA2FohB,mBAA3F,EAA4H,CAAC;IACjHlZ,IAAI,EAAE1H,SAD2G;IAEjH2H,IAAI,EAAE,CAAC;MACCwP,QAAQ,EAAE,qEADX;MAECwJ,QAAQ,EAAE;IAFX,CAAD;EAF2G,CAAD,CAA5H,EAM4B,YAAY;IAAE,OAAO,CAAC;MAAEjZ,IAAI,EAAEsX;IAAR,CAAD,EAAoB;MAAEtX,IAAI,EAAElI,EAAE,CAAC0jB;IAAX,CAApB,EAA8C;MAAExb,IAAI,EAAElI,EAAE,CAAC2jB;IAAX,CAA9C,EAA6E;MAAEzb,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC1IJ,IAAI,EAAEhI,MADoI;QAE1IiI,IAAI,EAAE,CAAC4Y,qCAAD;MAFoI,CAAD;IAA/B,CAA7E,EAG3B;MAAE7Y,IAAI,EAAE9G,EAAE,CAACwf,cAAX;MAA2BtY,UAAU,EAAE,CAAC;QAC1CJ,IAAI,EAAE/H;MADoC,CAAD;IAAvC,CAH2B,CAAP;EAKlB,CAXxB,EAW0C;IAAE+I,MAAM,EAAE,CAAC;MACrChB,IAAI,EAAExH,KAD+B;MAErCyH,IAAI,EAAE,CAAC,2BAAD;IAF+B,CAAD,CAAV;IAG1BwL,SAAS,EAAE,CAAC;MACZzL,IAAI,EAAExH,KADM;MAEZyH,IAAI,EAAE,CAAC,8BAAD;IAFM,CAAD,CAHe;IAM1BkG,gBAAgB,EAAE,CAAC;MACnBnG,IAAI,EAAExH,KADa;MAEnByH,IAAI,EAAE,CAAC,qCAAD;IAFa,CAAD,CANQ;IAS1BiB,OAAO,EAAE,CAAC;MACVlB,IAAI,EAAExH,KADI;MAEVyH,IAAI,EAAE,CAAC,4BAAD;IAFI,CAAD,CATiB;IAY1BkB,OAAO,EAAE,CAAC;MACVnB,IAAI,EAAExH,KADI;MAEVyH,IAAI,EAAE,CAAC,4BAAD;IAFI,CAAD,CAZiB;IAe1B7D,KAAK,EAAE,CAAC;MACR4D,IAAI,EAAExH,KADE;MAERyH,IAAI,EAAE,CAAC,0BAAD;IAFE,CAAD,CAfmB;IAkB1B/D,MAAM,EAAE,CAAC;MACT8D,IAAI,EAAExH,KADG;MAETyH,IAAI,EAAE,CAAC,2BAAD;IAFG,CAAD,CAlBkB;IAqB1B6I,QAAQ,EAAE,CAAC;MACX9I,IAAI,EAAExH,KADK;MAEXyH,IAAI,EAAE,CAAC,6BAAD;IAFK,CAAD,CArBgB;IAwB1B8I,SAAS,EAAE,CAAC;MACZ/I,IAAI,EAAExH,KADM;MAEZyH,IAAI,EAAE,CAAC,8BAAD;IAFM,CAAD,CAxBe;IA2B1BQ,aAAa,EAAE,CAAC;MAChBT,IAAI,EAAExH,KADU;MAEhByH,IAAI,EAAE,CAAC,kCAAD;IAFU,CAAD,CA3BW;IA8B1BM,UAAU,EAAE,CAAC;MACbP,IAAI,EAAExH,KADO;MAEbyH,IAAI,EAAE,CAAC,+BAAD;IAFO,CAAD,CA9Bc;IAiC1B+Z,cAAc,EAAE,CAAC;MACjBha,IAAI,EAAExH,KADW;MAEjByH,IAAI,EAAE,CAAC,mCAAD;IAFW,CAAD,CAjCU;IAoC1BK,cAAc,EAAE,CAAC;MACjBN,IAAI,EAAExH,KADW;MAEjByH,IAAI,EAAE,CAAC,mCAAD;IAFW,CAAD,CApCU;IAuC1Bga,IAAI,EAAE,CAAC;MACPja,IAAI,EAAExH,KADC;MAEPyH,IAAI,EAAE,CAAC,yBAAD;IAFC,CAAD,CAvCoB;IA0C1Bia,YAAY,EAAE,CAAC;MACfla,IAAI,EAAExH,KADS;MAEfyH,IAAI,EAAE,CAAC,iCAAD;IAFS,CAAD,CA1CY;IA6C1Bsb,uBAAuB,EAAE,CAAC;MAC1Bvb,IAAI,EAAExH,KADoB;MAE1ByH,IAAI,EAAE,CAAC,sCAAD;IAFoB,CAAD,CA7CC;IAgD1BO,WAAW,EAAE,CAAC;MACdR,IAAI,EAAExH,KADQ;MAEdyH,IAAI,EAAE,CAAC,gCAAD;IAFQ,CAAD,CAhDa;IAmD1Bya,YAAY,EAAE,CAAC;MACf1a,IAAI,EAAExH,KADS;MAEfyH,IAAI,EAAE,CAAC,iCAAD;IAFS,CAAD,CAnDY;IAsD1B4O,kBAAkB,EAAE,CAAC;MACrB7O,IAAI,EAAExH,KADe;MAErByH,IAAI,EAAE,CAAC,uCAAD;IAFe,CAAD,CAtDM;IAyD1B8O,aAAa,EAAE,CAAC;MAChB/O,IAAI,EAAExH,KADU;MAEhByH,IAAI,EAAE,CAAC,kCAAD;IAFU,CAAD,CAzDW;IA4D1BkC,IAAI,EAAE,CAAC;MACPnC,IAAI,EAAExH,KADC;MAEPyH,IAAI,EAAE,CAAC,yBAAD;IAFC,CAAD,CA5DoB;IA+D1B4H,aAAa,EAAE,CAAC;MAChB7H,IAAI,EAAEvH;IADU,CAAD,CA/DW;IAiE1B0hB,cAAc,EAAE,CAAC;MACjBna,IAAI,EAAEvH;IADW,CAAD,CAjEU;IAmE1BgC,MAAM,EAAE,CAAC;MACTuF,IAAI,EAAEvH;IADG,CAAD,CAnEkB;IAqE1BuE,MAAM,EAAE,CAAC;MACTgD,IAAI,EAAEvH;IADG,CAAD,CArEkB;IAuE1B2hB,cAAc,EAAE,CAAC;MACjBpa,IAAI,EAAEvH;IADW,CAAD,CAvEU;IAyE1B4hB,mBAAmB,EAAE,CAAC;MACtBra,IAAI,EAAEvH;IADgB,CAAD;EAzEK,CAX1C;AAAA;AAuFA;;;AACA,SAASijB,sDAAT,CAAgEza,OAAhE,EAAyE;EACrE,OAAO,MAAMA,OAAO,CAACsW,gBAAR,CAAyB3X,UAAzB,EAAb;AACH;AACD;;;AACA,MAAM+b,8CAA8C,GAAG;EACnDC,OAAO,EAAE/C,qCAD0C;EAEnDgD,IAAI,EAAE,CAACvE,OAAD,CAF6C;EAGnDwE,UAAU,EAAEJ;AAHuC,CAAvD;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMK,aAAN,CAAoB;;AAEpBA,aAAa,CAAClc,IAAd;EAAA,iBAA0Gkc,aAA1G;AAAA;;AACAA,aAAa,CAACC,IAAd,kBAtoFwGlkB,EAsoFxG;EAAA,MAA2GikB,aAA3G;EAAA,eAAyI7C,mBAAzI,EAA8JJ,gBAA9J;EAAA,UAA2L3f,UAA3L,EAAuMG,YAAvM,EAAqN9B,eAArN;EAAA,UAAiP0hB,mBAAjP,EAAsQJ,gBAAtQ,EAAwRthB,eAAxR;AAAA;AACAukB,aAAa,CAACE,IAAd,kBAvoFwGnkB,EAuoFxG;EAAA,WAAqI,CAACwf,OAAD,EAAUqE,8CAAV,CAArI;EAAA,UAA0MxiB,UAA1M,EAAsNG,YAAtN,EAAoO9B,eAApO,EAAqPA,eAArP;AAAA;;AACA;EAAA,mDAxoFwGM,EAwoFxG,mBAA2FikB,aAA3F,EAAsH,CAAC;IAC3G/b,IAAI,EAAEtH,QADqG;IAE3GuH,IAAI,EAAE,CAAC;MACCic,OAAO,EAAE,CAAC/iB,UAAD,EAAaG,YAAb,EAA2B9B,eAA3B,CADV;MAEC2kB,OAAO,EAAE,CAACjD,mBAAD,EAAsBJ,gBAAtB,EAAwCthB,eAAxC,CAFV;MAGC4kB,YAAY,EAAE,CAAClD,mBAAD,EAAsBJ,gBAAtB,CAHf;MAICuD,SAAS,EAAE,CAAC/E,OAAD,EAAUqE,8CAAV;IAJZ,CAAD;EAFqG,CAAD,CAAtH;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMW,0BAAN,SAAyClY,gBAAzC,CAA0D;EACtDnK,WAAW,CAACO,SAAD,EAAY+hB,QAAZ,EAAsB;IAC7B,MAAM/hB,SAAN,EAAiB+hB,QAAjB;EACH;;EACDra,WAAW,GAAG;IACV,MAAMA,WAAN;;IACA,IAAI,KAAKsa,oBAAL,IAA6B,KAAKC,mBAAtC,EAA2D;MACvD,KAAKjiB,SAAL,CAAe4I,mBAAf,CAAmC,KAAKoZ,oBAAxC,EAA8D,KAAKC,mBAAnE;IACH;EACJ;;EACDlY,gBAAgB,GAAG;IACf,MAAMA,gBAAN;;IACA,KAAKmY,gCAAL;;IACA,KAAKC,4BAAL,CAAkC,MAAM,KAAKD,gCAAL,EAAxC;EACH;;EACDA,gCAAgC,GAAG;IAC/B,IAAI,CAAC,KAAKrY,iBAAV,EAA6B;MACzB;IACH;;IACD,MAAMuY,iBAAiB,GAAG,KAAKC,oBAAL,EAA1B;IACA,MAAM5F,MAAM,GAAG2F,iBAAiB,IAAI,KAAKpiB,SAAL,CAAea,IAAnD;IACA4b,MAAM,CAAClS,WAAP,CAAmB,KAAKV,iBAAxB;EACH;;EACDsY,4BAA4B,CAACG,EAAD,EAAK;IAC7B,MAAMC,SAAS,GAAG,KAAKC,aAAL,EAAlB;;IACA,IAAID,SAAJ,EAAe;MACX,IAAI,KAAKN,mBAAT,EAA8B;QAC1B,KAAKjiB,SAAL,CAAe4I,mBAAf,CAAmC2Z,SAAnC,EAA8C,KAAKN,mBAAnD;MACH;;MACD,KAAKjiB,SAAL,CAAe2I,gBAAf,CAAgC4Z,SAAhC,EAA2CD,EAA3C;;MACA,KAAKL,mBAAL,GAA2BK,EAA3B;IACH;EACJ;;EACDE,aAAa,GAAG;IACZ,IAAI,CAAC,KAAKR,oBAAV,EAAgC;MAC5B,MAAMhiB,SAAS,GAAG,KAAKA,SAAvB;;MACA,IAAIA,SAAS,CAACyiB,iBAAd,EAAiC;QAC7B,KAAKT,oBAAL,GAA4B,kBAA5B;MACH,CAFD,MAGK,IAAIhiB,SAAS,CAAC0iB,uBAAd,EAAuC;QACxC,KAAKV,oBAAL,GAA4B,wBAA5B;MACH,CAFI,MAGA,IAAIhiB,SAAS,CAAC2iB,oBAAd,EAAoC;QACrC,KAAKX,oBAAL,GAA4B,qBAA5B;MACH,CAFI,MAGA,IAAIhiB,SAAS,CAAC4iB,mBAAd,EAAmC;QACpC,KAAKZ,oBAAL,GAA4B,oBAA5B;MACH;IACJ;;IACD,OAAO,KAAKA,oBAAZ;EACH;EACD;AACJ;AACA;AACA;;;EACIK,oBAAoB,GAAG;IACnB,MAAMriB,SAAS,GAAG,KAAKA,SAAvB;IACA,OAAQA,SAAS,CAACoiB,iBAAV,IACJpiB,SAAS,CAAC6iB,uBADN,IAEJ7iB,SAAS,CAAC8iB,oBAFN,IAGJ9iB,SAAS,CAAC+iB,mBAHN,IAIJ,IAJJ;EAKH;;AA9DqD;;AAgE1DjB,0BAA0B,CAACzc,IAA3B;EAAA,iBAAuHyc,0BAAvH,EAxuFwGxkB,EAwuFxG,UAAmKD,QAAnK,GAxuFwGC,EAwuFxG,UAAwLgB,IAAI,CAACqL,QAA7L;AAAA;;AACAmY,0BAA0B,CAACvc,KAA3B,kBAzuFwGjI,EAyuFxG;EAAA,OAA2HwkB,0BAA3H;EAAA,SAA2HA,0BAA3H;EAAA,YAAmK;AAAnK;;AACA;EAAA,mDA1uFwGxkB,EA0uFxG,mBAA2FwkB,0BAA3F,EAAmI,CAAC;IACxHtc,IAAI,EAAEjI,UADkH;IAExHkI,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAFkH,CAAD,CAAnI,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAEG,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9DJ,IAAI,EAAEhI,MADwD;QAE9DiI,IAAI,EAAE,CAACpI,QAAD;MAFwD,CAAD;IAA/B,CAAD,EAG3B;MAAEmI,IAAI,EAAElH,IAAI,CAACqL;IAAb,CAH2B,CAAP;EAGO,CANjD;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASnK,mBAAT,EAA8Bkf,mBAA9B,EAAmDJ,gBAAnD,EAAqEvc,mBAArE,EAA0FkF,8BAA1F,EAA0HV,sBAA1H,EAAkJuJ,iCAAlJ,EAAqLgS,0BAArL,EAAiN1G,sBAAjN,EAAyO/X,kBAAzO,EAA6PyZ,OAA7P,EAAsQjX,aAAtQ,EAAqR+D,gBAArR,EAAuS5B,yBAAvS,EAAkUuZ,aAAlU,EAAiV1Y,6BAAjV,EAAgX6T,sBAAhX,EAAwYlS,UAAxY,EAAoZjG,wBAApZ,EAA8a2W,oCAA9a,EAAodD,iCAApd,EAAuflW,qBAAvf,EAA8gBiC,mBAA9gB,EAAmiBO,0BAAniB,EAA+jBH,wBAA/jB"}, "metadata": {}, "sourceType": "module"}